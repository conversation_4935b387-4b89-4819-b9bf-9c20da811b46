package database

import (
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
)

func GetCountries(ctx *gin.Context) (bson.M, error) {
	lang := ctx.Request.Header.Get("Accept-Language")

	var countries []models.Country
	cursor, err := tools.Database.Collection("country").Find(nil, bson.M{})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	cursor.All(nil, &countries)

	country := bson.M{}

	if lang == "ko" {
		for _, item := range countries {
			country[item.Code] = item.Ko
		}
		return country, nil
	}
	if lang == "zh" {
		for _, item := range countries {
			country[item.Code] = item.Cn
		}
		return country, nil
	}
	for _, item := range countries {
		country[item.Code] = item.En
	}
	return country, nil
}
