package data

type ReportFields struct {
	Key      string         `json:"key"`
	Children []ReportFields `json:"children,omitempty"`
	Required bool           `json:"required,omitempty"`
}

type Report struct {
	Name               string               `json:"name"`
	DefaultFields      []ReportFields       `json:"defaultFields"`
	MultiDefaultFields []MultiDefaultFields `json:"multiDefaultFields"`
	Type               int                  `json:"type"`
	Customizable       bool                 `json:"customizable"`           //是否可以自定义模板
	CustomFields       []ReportFields       `json:"customFields,omitempty"` //可配置字段
	LatestDownloadTime string               `json:"latestDownloadTime" bson:"latest_download_time"`
}

type MultiDefaultFields struct {
	Type          int            `json:"type"`
	DefaultFields []ReportFields `json:"defaultFields"`
}

const (
	ReportTypeRandomizationStatisticsExport           = iota + 1 // 1 随机统计导出 嘉豪
	ReportTypeSubjectStatisticsExport                            // 2 受试者统计导出 健聪
	ReportTypeSiteIPStatisticsExport                             // 3 中心单品统计导出 丽丽
	ReportTypeDepotIPStatisticsExport                            // 4 库房单品统计导出 丽丽
	ReportTypeUserLoginHistory                                   // 5 用户登录历史记录 嘉豪
	ReportTypeUserRoleAssignHistory                              // 6 用户角色分配记录 丽丽
	ReportTypeUserRoleStatus                                     // 7 用户角色状态报表
	ReportTypeAuditTrailExport                                   // 8 稽查轨迹导出 丽丽
	ReportTypeProjectPermissionConfigurationExport               // 9 项目权限配置导出
	ReportTypeConfigureReport                                    // 10 配置报告
	ReportTypeSourceIPExport                                     // 11 研究产品盲底导出
	ReportTypeSourceRandomizationListExport                      // 12 随机盲底导出 健聪
	ReportTypeRandomizationSimulationReport                      // 13 模拟随机报告 //不用做
	ReportTypeRandomizeReport                                    // 14 受试者明细报表 健聪
	ReportTypeDispenseReport                                     // 15 受试者发放报表 健聪
	ReportTypeUnblindingReport                                   // 16 揭盲报表
	ReportTypeIPUnblindingReport                                 // 17 研究产品揭盲报表
	ReportTypeRoomNumberViewHistory                              // 18 房间号查看记录 //不用做
	ReportTypeDepotItemReport                                    // 18 库房单品报表  未编号 丽丽
	ReportTypeSiteItemReport                                     // 19 中心药房单品报表  未编号 丽丽
	ReportTypeShipmentOrdersReport                               // 20 研究产品订单报表  未编号 丽丽
	ReportTypeReturnOrdersReport                                 // 21 回收订单报表  未编号 丽丽
	ReportTypeRandomizationSimulationResult                      // 22 模拟随机结果 嘉豪
	ReportTypeRandomizationSimulationReportPDF                   // 23 模拟随机报告PDF
	ReportTypeProjectNotificationsConfigurationReport            // 24 项目通知配置报表
	ReportTypeProjectSourceIpUploadHistoryReport                 // 25 研究产品盲底上传记录报表 春峰
	ReportTypeForecastingPredictionReportReport                  // 26 研究产品预测报表
	ReportTypeVisitForecastRecordReportReport                    // 26 研究产品预测报表
	ReportTypeIPUnblindingReport                                 // 26 研究产品预测报表
)

var (
	ReportAttributesProject       = "report.attributes.project"
	ReportAttributesProjectNumber = "report.attributes.project.number"
	ReportAttributesProjectName   = "report.attributes.project.name"
	//基本信息
	ReportAttributesInfo               = "report.attributes.info"
	ReportAttributesInfoCountry        = "report.attributes.info.country"
	ReportAttributesInfoRegion         = "report.attributes.info.region"
	ReportAttributesInfoSiteNumber     = "report.attributes.info.site.number"
	ReportAttributesInfoSiteName       = "report.attributes.info.site.name"
	ReportAttributesInfoSiteCountry    = "report.attributes.info.site.country"
	ReportAttributesInfoSiteRegion     = "report.attributes.info.site.region"
	ReportAttributesInfoSubjectNumber  = "report.attributes.info.subject.number"
	ReportAttributesInfoStatus         = "report.attributes.info.status"
	ReportAttributesInfoStorehouseName = "report.attributes.info.storehouse.name"
	ReportAttributesInfoUserName       = "report.attributes.info.user.name"
	ReportAttributesInfoUserEmail      = "report.attributes.info.user.email"
	ReportAttributesInfoUserRole       = "report.attributes.info.user.role"
	//随机
	ReportAttributesRandom                     = "report.attributes.random"
	ReportAttributesRandomFactor               = "report.attributes.random.factor"
	ReportAttributesRandomFactorCalc           = "report.attributes.random.factor.calc"
	ReportAttributesRandomActualFactor         = "report.attributes.random.actual.factor"
	ReportAttributesRandomTime                 = "report.attributes.random.time"
	ReportAttributesRandomGroup                = "report.attributes.random.group"
	ReportAttributesRandomSubGroup             = "report.attributes.random.sub.group"
	ReportAttributesRandomSubjectNumberReplace = "report.attributes.random.subject.number.replace"
	ReportAttributesRandomNumber               = "report.attributes.random.number"
	ReportAttributesRandomRegisterTime         = "report.attributes.random.register.time"
	ReportAttributesRandomRegisterOperator     = "report.attributes.random.register.operator"
	ReportAttributesRandomOperator             = "report.attributes.random.operator"
	ReportAttributesRandomBlock                = "report.attributes.random.block"
	ReportAttributesRandomPlanNumber           = "report.attributes.random.plan.number"
	ReportAttributesRandomForm                 = "report.attributes.random.form"
	ReportAttributesRandomSubjectReplaceStatus = "report.attributes.random.subject.replace.status"
	ReportAttributesRandomCohort               = "report.attributes.random.cohort"
	ReportAttributesRandomStage                = "report.attributes.random.stage"
	ReportAttributesRandomSubjectReplaceTime   = "report.attributes.random.subject.replace.time"
	ReportAttributesRandomSubjectReplaceNumber = "report.attributes.random.subject.replace.number"
	ReportAttributesRandomConfigCode           = "report.attributes.random.config.code"
	ReportAttributesRandomSignOutOperator      = "report.attributes.random.sign.out.operator"
	ReportAttributesRandomSignOutTime          = "report.attributes.random.sign.out.time"
	ReportAttributesRandomSignOutRealTime      = "report.attributes.random.sign.out.real.time"
	ReportAttributesRandomSignOutReason        = "report.attributes.random.sign.out.reason"
	ReportAttributesRandomScreenTime           = "report.attributes.random.screen.time"
	ReportAttributesRandomICFTime              = "report.attributes.random.icf.time"
	ReportAttributesRandomFinishRemark         = "report.attributes.random.finish.remark"
	ReportAttributesRandomPlanTime             = "report.attributes.random.plan.time"
	ReportAttributesRandomSequenceNumber       = "report.attributes.random.sequence.number"
	//发药
	ReportAttributesDispensing                         = "report.attributes.dispensing"
	ReportAttributesDispensingRoom                     = "report.attributes.dispensing.room"
	ReportAttributesDispensingCycleName                = "report.attributes.dispensing.cycle.name" // 访视周期
	ReportAttributesDispensingType                     = "report.attributes.dispensing.type"       // 发药操作类型
	ReportAttributesDispensingPlanTime                 = "report.attributes.dispensing.planTime"   // 计划访视时间
	ReportAttributesDispensingTime                     = "report.attributes.dispensing.time"       // 发药操作时间
	ReportAttributesDispensingMedicine                 = "report.attributes.dispensing.medicine"
	ReportAttributesDispensingDrugName                 = "report.attributes.dispensing.drug.name"
	ReportAttributesDispensingLabel                    = "report.attributes.dispensing.label" // 发药标签
	ReportAttributesDispensingDose                     = "report.attributes.dispensing.dose"  // 发放水平
	ReportAttributesDispensingOutSize                  = "report.attributes.dispensing.outsize"
	ReportAttributesDispensingDoseFormulas             = "report.attributes.dispensing.doseFormulas"
	ReportAttributesDispensingMedicineReplace          = "report.attributes.dispensing.medicine.replace"
	ReportAttributesDispensingMedicineReal             = "report.attributes.dispensing.medicine.real"
	ReportAttributesDispensingMedicineRealGroup        = "report.attributes.dispensing.medicine.real.group"
	ReportAttributesDispensingDrugOtherNumber          = "report.attributes.dispensing.drug.other.number"
	ReportAttributesDispensingUseFormulas              = "report.attributes.dispensing.useFormulas"
	ReportAttributesDispensingOperator                 = "report.attributes.dispensing.operator" // 操作人.
	ReportAttributesDispensingRemark                   = "report.attributes.dispensing.remark"
	ReportAttributesDispensingOutVisitDispensingReason = "report.attributes.dispensing.out-visit-dispensing.reason"
	ReportAttributesDispensingReissueReason            = "report.attributes.dispensing.reissue.reason"
	ReportAttributesDispensingMedicineRealNumber       = "report.attributes.dispensing.medicine.real.number"
	ReportAttributesDispensingMedicineReissueRemark    = "report.attributes.dispensing.reissue.remark"
	ReportAttributesDispensingOutVisitDispensingRemark = "report.attributes.dispensing.out-visit-dispensing.remark"
	ReportAttributesDispensingMedicineReplaceRemark    = "report.attributes.dispensing.replace.remark"
	ReportAttributesDispensingMedicineRetrievalRemark  = "report.attributes.dispensing.retrieval.remark"
	ReportAttributesDispensingMedicineRegisterRemark   = "report.attributes.dispensing.register.remark"
	ReportAttributesDispensingMedicineInvalidRemark    = "report.attributes.dispensing.invalid.remark"
	ReportAttributesDispensingMedicineSendType         = "report.attributes.dispensing.send.type"
	ReportAttributesDispensingMedicineLogisticsInfo    = "report.attributes.dispensing.logistics.info"
	ReportAttributesDispensingMedicineLogisticsRemark  = "report.attributes.dispensing.logistics.remark"
	//揭盲
	ReportAttributesUnblinding                     = "report.attributes.unblinding"
	ReportAttributesUnblindingSponsor              = "report.attributes.unblinding.sponsor"
	ReportAttributesUnblindingMark                 = "report.attributes.unblinding.mark"
	ReportAttributesUnblindingReason               = "report.attributes.unblinding.reason"
	ReportAttributesUnblindingReasonMark           = "report.attributes.unblinding.reason.mark"
	ReportAttributesUnblindingOperator             = "report.attributes.unblinding.operator"
	ReportAttributesUnblindingTime                 = "report.attributes.unblinding.time"
	ReportAttributesUnblindingActualMedicineNumber = "report.attributes.unblinding.actual.medicine.number"
	//研究产品
	ReportAttributesResearch                     = "report.attributes.research"
	ReportAttributesResearchMedicineNumber       = "report.attributes.research.medicine.number"
	ReportAttributesResearchMedicineSerialNumber = "report.attributes.research.medicine.serial-number"
	ReportAttributesResearchMedicineName         = "report.attributes.research.medicine.name"
	ReportAttributesResearchBatch                = "report.attributes.research.batch"
	ReportAttributesResearchExpireDate           = "report.attributes.research.expireDate"
	ReportAttributesResearchSpec                 = "report.attributes.research.spec"
	ReportAttributesResearchPackageNumber        = "report.attributes.research.packageNumber"
	ReportAttributesResearchPackageSerialNumber  = "report.attributes.research.package.serialNumber"
	ReportAttributesResearchPackageMethod        = "report.attributes.research.packageMethod"
	ReportAttributesResearchPlace                = "report.attributes.research.place"
	ReportAttributesResearchOrderNumber          = "report.attributes.research.order.number"
	ReportAttributesResearchStatus               = "report.attributes.research.status"
	ReportAttributesResearchReason               = "report.attributes.research.reason"
	ReportAttributesResearchOperator             = "report.attributes.research.operator"
	ReportAttributesResearchTime                 = "report.attributes.research.time"
	ReportAttributesDispensingIsMedicineReplace  = "report.attributes.dispensing.medicine.is.replace"
	ReportAttributesDispensingIsMedicineReal     = "report.attributes.dispensing.medicine.is.real"

	// ReportAttributesResearchFreezeReason         = "report.attributes.research.freeze.reason"
	// ReportAttributesResearchFreezeOperator       = "report.attributes.research.freeze.operator"
	// ReportAttributesResearchFreezeTime           = "report.attributes.research.freeze.time"
	// ReportAttributesResearchReleaseReason        = "report.attributes.research.release.reason"
	// ReportAttributesResearchReleaseOperator      = "report.attributes.research.release.operator"
	// ReportAttributesResearchReleaseTime          = "report.attributes.research.release.time"
	// ReportAttributesResearchLostReason           = "report.attributes.research.lost.reason"
	// ReportAttributesResearchLostOperator         = "report.attributes.research.lost.operator"
	// ReportAttributesResearchFreezeLostTime       = "report.attributes.research.lost.time"
	// ReportAttributesResearchUseReason            = "report.attributes.research.use.reason"
	// ReportAttributesResearchUseOperator          = "report.attributes.research.use.operator"
	// ReportAttributesResearchUseTime              = "report.attributes.research.use.time"
	ReportAttributesResearchOther = "report.attributes.research.other"
	//订单
	ReportAttributesOrder                    = "report.attributes.order"
	ReportAttributesOrderDetail              = "report.attributes.order.detail"
	ReportAttributesOrderNumber              = "report.attributes.order.number"
	ReportAttributesOrderStatus              = "report.attributes.order.status"
	ReportAttributesOrderSend                = "report.attributes.order.send"
	ReportAttributesOrderReceive             = "report.attributes.order.receive"
	ReportAttributesOrderMedicineQuantity    = "report.attributes.order.medicineQuantity"
	ReportAttributesOrderCreateBy            = "report.attributes.order.create.by"
	ReportAttributesOrderCreateTime          = "report.attributes.order.create.time"
	ReportAttributesOrderCancelBy            = "report.attributes.order.cancel.by"
	ReportAttributesOrderCancelTime          = "report.attributes.order.cancel.time"
	ReportAttributesOrderCancelReason        = "report.attributes.order.cancel.reason"
	ReportAttributesOrderConfirmBy           = "report.attributes.order.confirm.by"
	ReportAttributesOrderConfirmTime         = "report.attributes.order.confirm.time"
	ReportAttributesOrderCloseBy             = "report.attributes.order.close.by"
	ReportAttributesOrderCloseTime           = "report.attributes.order.close.time"
	ReportAttributesOrderCloseReason         = "report.attributes.order.close.reason"
	ReportAttributesOrderSendBy              = "report.attributes.order.send.by"
	ReportAttributesOrderSendTime            = "report.attributes.order.send.time"
	ReportAttributesOrderReceiveBy           = "report.attributes.order.receive.by"
	ReportAttributesOrderReceiveTime         = "report.attributes.order.receive.time"
	ReportAttributesOrderLostBy              = "report.attributes.order.lost.by"
	ReportAttributesOrderLostTime            = "report.attributes.order.lost.time"
	ReportAttributesOrderLostReason          = "report.attributes.order.lost.reason"
	ReportAttributesOrderEndBy               = "report.attributes.order.end.by"
	ReportAttributesOrderEndTime             = "report.attributes.order.end.time"
	ReportAttributesOrderEndReason           = "report.attributes.order.end.reason"
	ReportAttributesOrderSupplier            = "report.attributes.order.supplier"
	ReportAttributesOrderSupplierOther       = "report.attributes.order.supplier.other"
	ReportAttributesOrderSupplierNumber      = "report.attributes.order.supplier.number"
	ReportAttributesOrderExpectedArrivalTime = "report.attributes.order.expectedArrivalTime"
	ReportAttributesOrderActualReceiptTime   = "report.attributes.order.actualReceiptTime"

	//用户角色分配记录
	ReportUserRoleAssignName     = "report.user.role.assign.name"
	ReportUserRoleAssignEmail    = "report.user.role.assign.email"
	ReportUserRoleAssignOperType = "report.user.role.assign.operType"
	ReportUserRoleAssignContent  = "report.user.role.assign.content"
	ReportUserRoleAssignOper     = "report.user.role.assign.oper"
	ReportUserRoleAssignOperTime = "report.user.role.assign.operTime"

	//中心单品统计报表
	ReportIPStatisticsStatusAvailable     = "report.ip.statistics.status.available"
	ReportIPStatisticsStatusToBeConfirmed = "report.ip.statistics.status.toBeConfirmed"
	ReportIPStatisticsStatusDelivered     = "report.ip.statistics.status.delivered"
	ReportIPStatisticsStatusSending       = "report.ip.statistics.status.sending"
	ReportIPStatisticsStatusQuarantine    = "report.ip.statistics.status.quarantine"
	ReportIPStatisticsStatusUsed          = "report.ip.statistics.status.used"
	ReportIPStatisticsStatusLose          = "report.ip.statistics.status.lose"
	ReportIPStatisticsStatusExpired       = "report.ip.statistics.status.expired"
	ReportIPStatisticsStatusFrozen        = "report.ip.statistics.status.frozen"
	ReportIPStatisticsStatusLocked        = "report.ip.statistics.status.locked"
	//用户登录历史
	ReportLoginHistoryIP      = "report.user.login.ip"
	ReportLoginHistoryTime    = "report.user.login.time"
	ReportLoginHistorySuccess = "report.user.login.success"

	// 研究产品预测表
	ReportForecastDeopt  = "report.forecast.depot"
	ReportForecastPeriod = "report.forecast.period"

	// 访视统计表
	ReportVisitForecastVisitStatus   = "report.visit.forecast.visit.status"
	ReportVisitForecastNoticeContent = "report.visit.forecast.notice.content"
	ReportVisitForecastNoticeTime    = "report.visit.forecast.notice.time"
	ReportVisitForecastNoticeUser    = "report.visit.forecast.notice.user"
	ReportVisitForecastNoticeType    = "report.visit.forecast.notice.type"
	ReportVisitForecastNoticeEmail   = "report.visit.forecast.notice.email"

	RandomizationStatisticsExport = Report{
		Name: "menu.report.randomizationStatisticsExport",
		Type: ReportTypeRandomizationStatisticsExport,
		MultiDefaultFields: []MultiDefaultFields{
			{
				Type: 1,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: "report.attributes.random.factor"},
				},
			},
			{
				Type: 1,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: "report.random.statistics.month"},
					{Key: "report.random.statistics.week"},
				},
			},
			{
				Type: 2,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: "report.attributes.random.factor"},
					{Key: "report.attributes.random.cohort"},
				},
			},
			{
				Type: 2,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: "report.random.statistics.month"},
					{Key: "report.random.statistics.week"},
					{Key: "report.attributes.random.cohort"},
				},
			},
			{
				Type: 3,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: "report.attributes.random.factor"},
					{Key: "report.attributes.random.stage"},
				},
			},
			{
				Type: 3,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: "report.random.statistics.month"},
					{Key: "report.random.statistics.week"},
					{Key: "report.attributes.random.stage"},
				},
			},
		},
	}
	SubjectStatisticsExport = Report{
		Name: "menu.report.subjectStatisticsExport",
		Type: ReportTypeSubjectStatisticsExport,
		MultiDefaultFields: []MultiDefaultFields{
			{
				Type: 1,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: "subject.register"},
					{Key: "subject.status.screen.success"},
					{Key: "subject.status.screen.fail"},
					{Key: "subject.random"},
					{Key: "subject.exited"},
					{Key: "report.subject.unblinding.urgent"},
					{Key: "report.subject.unblinding.pv"},
					{Key: "subject.status.finish"},
				},
			},
			{
				Type: 1,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: "subject.register"},
					{Key: "subject.status.screen.success"},
					{Key: "subject.status.screen.fail"},
					{Key: "subject.random"},
					{Key: "subject.exited"},
					{Key: "report.subject.unblinding.urgent"},
					{Key: "report.subject.unblinding.pv"},
					{Key: "subject.status.finish"},
				},
			},
			{
				Type: 2,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: "subject.register"},
					{Key: "subject.status.screen.success"},
					{Key: "subject.status.screen.fail"},
					{Key: "export.random.toBeRandom"},
					{Key: "subject.random"},
					{Key: "subject.exited"},
					{Key: "report.subject.unblinding.urgent"},
					{Key: "report.subject.unblinding.pv"},
					{Key: "subject.status.finish"},
					{Key: ReportAttributesRandomCohort},
				},
			},
			{
				Type: 2,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: "subject.register"},
					{Key: "subject.status.screen.success"},
					{Key: "subject.status.screen.fail"},
					{Key: "export.random.toBeRandom"},
					{Key: "subject.random"},
					{Key: "subject.exited"},
					{Key: "report.subject.unblinding.urgent"},
					{Key: "report.subject.unblinding.pv"},
					{Key: "subject.status.finish"},
					{Key: ReportAttributesRandomCohort},
				},
			},
			{
				Type: 3,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: "subject.register"},
					{Key: "subject.status.screen.success"},
					{Key: "subject.status.screen.fail"},
					{Key: "export.random.toBeRandom"},
					{Key: "subject.random"},
					{Key: "subject.exited"},
					{Key: "report.subject.unblinding.urgent"},
					{Key: "report.subject.unblinding.pv"},
					{Key: "subject.status.finish"},
					{Key: ReportAttributesRandomStage},
				},
			},
			{
				Type: 3,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: "subject.register"},
					{Key: "subject.status.screen.success"},
					{Key: "subject.status.screen.fail"},
					{Key: "export.random.toBeRandom"},
					{Key: "subject.random"},
					{Key: "subject.exited"},
					{Key: "report.subject.unblinding.urgent"},
					{Key: "report.subject.unblinding.pv"},
					{Key: "subject.status.finish"},
					{Key: ReportAttributesRandomStage},
				},
			},
		},
	}

	OnlyDispensingSubjectStatisticsExport = Report{
		Name: "menu.report.subjectStatisticsExport",
		Type: ReportTypeSubjectStatisticsExport,
		MultiDefaultFields: []MultiDefaultFields{
			{
				Type: 1,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: "subject.register"},
					{Key: "subject.status.join"},
					{Key: "subject.status.screen.success"},
					{Key: "subject.status.screen.fail"},
					{Key: "subject.exited"},
				},
			},
			{
				Type: 1,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: "subject.register"},
					{Key: "subject.status.join"},
					{Key: "subject.status.screen.success"},
					{Key: "subject.status.screen.fail"},
					{Key: "subject.exited"},
				},
			},
			{
				Type: 2,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: "subject.register"},
					{Key: "subject.status.join"},
					{Key: "subject.status.screen.success"},
					{Key: "subject.status.screen.fail"},
					{Key: "subject.exited"},
					{Key: ReportAttributesRandomCohort},
				},
			},
			{
				Type: 2,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: "subject.register"},
					{Key: "subject.status.join"},
					{Key: "subject.status.screen.success"},
					{Key: "subject.status.screen.fail"},
					{Key: "subject.exited"},
					{Key: ReportAttributesRandomCohort},
				},
			},
			{
				Type: 3,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: "subject.register"},
					{Key: "subject.status.join"},
					{Key: "subject.status.screen.success"},
					{Key: "subject.status.screen.fail"},
					{Key: "subject.exited"},
					{Key: ReportAttributesRandomStage},
				},
			},
			{
				Type: 3,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: "subject.register"},
					{Key: "subject.status.join"},
					{Key: "subject.status.screen.success"},
					{Key: "subject.status.screen.fail"},
					{Key: "subject.exited"},
					{Key: ReportAttributesRandomStage},
				},
			},
		},
	}
	SiteIPStatisticsExport = Report{
		Name:         "menu.report.siteIPStatisticsExport",
		Type:         ReportTypeSiteIPStatisticsExport,
		Customizable: true,
		DefaultFields: []ReportFields{
			{Key: ReportAttributesProjectNumber},
			{Key: ReportAttributesProjectName},
			{Key: ReportAttributesInfoSiteNumber},
			{Key: ReportAttributesInfoSiteName},
			{Key: ReportAttributesResearchMedicineName},
			{Key: ReportAttributesResearchExpireDate},
			{Key: ReportAttributesResearchBatch},
			{Key: ReportAttributesResearchSpec},
			{Key: ReportIPStatisticsStatusAvailable},
			{Key: ReportIPStatisticsStatusToBeConfirmed},
			{Key: ReportIPStatisticsStatusDelivered},
			{Key: ReportIPStatisticsStatusSending},
			{Key: ReportIPStatisticsStatusQuarantine},
			{Key: ReportIPStatisticsStatusUsed},
			{Key: ReportIPStatisticsStatusLose},
			{Key: ReportIPStatisticsStatusExpired},
			{Key: ReportIPStatisticsStatusFrozen},
			{Key: ReportIPStatisticsStatusLocked},
		},
		CustomFields: []ReportFields{
			{
				Key: ReportAttributesProjectNumber,
				//Required: true,
			},
			{
				Key: ReportAttributesProjectName,
				//Required: true,
			},
			{
				Key:      ReportAttributesInfoSiteNumber,
				Required: true,
			},
			{
				Key:      ReportAttributesInfoSiteName,
				Required: true,
			},
			{
				Key:      ReportAttributesResearchMedicineName,
				Required: true,
			},
			{
				Key:      ReportIPStatisticsStatusAvailable,
				Required: true,
			},
			{
				Key:      ReportIPStatisticsStatusToBeConfirmed,
				Required: true,
			},
			{
				Key:      ReportIPStatisticsStatusDelivered,
				Required: true,
			},
			{
				Key:      ReportIPStatisticsStatusSending,
				Required: true,
			},
			{
				Key:      ReportIPStatisticsStatusQuarantine,
				Required: true,
			},
			{
				Key:      ReportIPStatisticsStatusUsed,
				Required: true,
			},
			{
				Key:      ReportIPStatisticsStatusLose,
				Required: true,
			},
			{
				Key:      ReportIPStatisticsStatusExpired,
				Required: true,
			},
			{
				Key:      ReportIPStatisticsStatusFrozen,
				Required: true,
			},
			{
				Key:      ReportIPStatisticsStatusLocked,
				Required: true,
			},
			{
				Key: ReportAttributesResearch,
				Children: []ReportFields{
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchSpec},
				},
			},
		},
	}
	DepotIPStatisticsExport = Report{
		Name:         "menu.report.depotIPStatisticsExport",
		Type:         ReportTypeDepotIPStatisticsExport,
		Customizable: true,
		MultiDefaultFields: []MultiDefaultFields{
			{
				Type: 1,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesResearchMedicineName},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchSpec},
					{Key: ReportIPStatisticsStatusAvailable},
					{Key: ReportIPStatisticsStatusToBeConfirmed},
					{Key: ReportIPStatisticsStatusDelivered},
					{Key: ReportIPStatisticsStatusSending},
					{Key: ReportIPStatisticsStatusQuarantine},
					{Key: ReportIPStatisticsStatusUsed},
					{Key: ReportIPStatisticsStatusLose},
					{Key: ReportIPStatisticsStatusExpired},
					{Key: ReportIPStatisticsStatusFrozen},
					{Key: ReportIPStatisticsStatusLocked},
				},
			},
			{
				Type: 1,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoStorehouseName},
					{Key: ReportAttributesResearchMedicineName},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchSpec},
					{Key: ReportIPStatisticsStatusAvailable},
					{Key: ReportIPStatisticsStatusToBeConfirmed},
					{Key: ReportIPStatisticsStatusDelivered},
					{Key: ReportIPStatisticsStatusSending},
					{Key: ReportIPStatisticsStatusQuarantine},
					{Key: ReportIPStatisticsStatusUsed},
					{Key: ReportIPStatisticsStatusLose},
					{Key: ReportIPStatisticsStatusExpired},
					{Key: ReportIPStatisticsStatusFrozen},
					{Key: ReportIPStatisticsStatusLocked},
				},
			},
			{
				Type: 2,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesResearchMedicineName},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchSpec},
					{Key: ReportIPStatisticsStatusAvailable},
					{Key: ReportIPStatisticsStatusToBeConfirmed},
					{Key: ReportIPStatisticsStatusDelivered},
					{Key: ReportIPStatisticsStatusSending},
					{Key: ReportIPStatisticsStatusQuarantine},
					{Key: ReportIPStatisticsStatusUsed},
					{Key: ReportIPStatisticsStatusLose},
					{Key: ReportIPStatisticsStatusExpired},
					{Key: ReportIPStatisticsStatusFrozen},
					{Key: ReportIPStatisticsStatusLocked},
				},
			},
			{
				Type: 2,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoStorehouseName},
					{Key: ReportAttributesResearchMedicineName},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchSpec},
					{Key: ReportIPStatisticsStatusAvailable},
					{Key: ReportIPStatisticsStatusToBeConfirmed},
					{Key: ReportIPStatisticsStatusDelivered},
					{Key: ReportIPStatisticsStatusSending},
					{Key: ReportIPStatisticsStatusQuarantine},
					{Key: ReportIPStatisticsStatusUsed},
					{Key: ReportIPStatisticsStatusLose},
					{Key: ReportIPStatisticsStatusExpired},
					{Key: ReportIPStatisticsStatusFrozen},
					{Key: ReportIPStatisticsStatusLocked},
				},
			},
			{
				Type: 3,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesResearchMedicineName},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchSpec},
					{Key: ReportIPStatisticsStatusAvailable},
					{Key: ReportIPStatisticsStatusToBeConfirmed},
					{Key: ReportIPStatisticsStatusDelivered},
					{Key: ReportIPStatisticsStatusSending},
					{Key: ReportIPStatisticsStatusQuarantine},
					{Key: ReportIPStatisticsStatusUsed},
					{Key: ReportIPStatisticsStatusLose},
					{Key: ReportIPStatisticsStatusExpired},
					{Key: ReportIPStatisticsStatusFrozen},
					{Key: ReportIPStatisticsStatusLocked},
				},
			},
			{
				Type: 3,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoStorehouseName},
					{Key: ReportAttributesResearchMedicineName},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchSpec},
					{Key: ReportIPStatisticsStatusAvailable},
					{Key: ReportIPStatisticsStatusToBeConfirmed},
					{Key: ReportIPStatisticsStatusDelivered},
					{Key: ReportIPStatisticsStatusSending},
					{Key: ReportIPStatisticsStatusQuarantine},
					{Key: ReportIPStatisticsStatusUsed},
					{Key: ReportIPStatisticsStatusLose},
					{Key: ReportIPStatisticsStatusExpired},
					{Key: ReportIPStatisticsStatusFrozen},
					{Key: ReportIPStatisticsStatusLocked},
				},
			},
		},
		CustomFields: []ReportFields{
			{
				Key: ReportAttributesProjectNumber,
				//Required: true,
			},
			{
				Key: ReportAttributesProjectName,
				//Required: true,
			},
			{
				Key:      ReportAttributesInfoStorehouseName,
				Required: true,
			},
			{
				Key:      ReportAttributesResearchMedicineName,
				Required: true,
			},
			{
				Key:      ReportIPStatisticsStatusAvailable,
				Required: true,
			},
			{
				Key:      ReportIPStatisticsStatusToBeConfirmed,
				Required: true,
			},
			{
				Key:      ReportIPStatisticsStatusDelivered,
				Required: true,
			},
			{
				Key:      ReportIPStatisticsStatusSending,
				Required: true,
			},
			{
				Key:      ReportIPStatisticsStatusQuarantine,
				Required: true,
			},
			{
				Key:      ReportIPStatisticsStatusUsed,
				Required: true,
			},
			{
				Key:      ReportIPStatisticsStatusLose,
				Required: true,
			},
			{
				Key:      ReportIPStatisticsStatusExpired,
				Required: true,
			},
			{
				Key:      ReportIPStatisticsStatusFrozen,
				Required: true,
			},
			{
				Key:      ReportIPStatisticsStatusLocked,
				Required: true,
			},
			{
				Key: ReportAttributesResearch,
				Children: []ReportFields{
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchSpec},
				},
			},
		},
	}
	UserLoginHistory = Report{
		Name: "menu.report.userLoginHistory",
		Type: ReportTypeUserLoginHistory,
		DefaultFields: []ReportFields{
			{Key: ReportAttributesProjectNumber},
			{Key: ReportAttributesProjectName},
			{Key: ReportAttributesInfoUserName},
			{Key: ReportAttributesInfoUserEmail},
			{Key: ReportLoginHistoryIP},
			{Key: ReportLoginHistoryTime},
			{Key: ReportLoginHistorySuccess},
		},
	}
	UserRoleAssignHistory = Report{
		Name: "menu.report.userRoleAssignHistory",
		Type: ReportTypeUserRoleAssignHistory,
		DefaultFields: []ReportFields{
			{Key: ReportAttributesProjectNumber},
			{Key: ReportAttributesProjectName},
			{Key: ReportUserRoleAssignName},
			{Key: ReportUserRoleAssignEmail},
			{Key: ReportUserRoleAssignOperType},
			{Key: ReportUserRoleAssignContent},
			{Key: ReportUserRoleAssignOper},
			{Key: ReportUserRoleAssignOperTime},
		},
	}
	UserRoleStatusReport = Report{
		Name: "menu.report.userRoleStatus",
		Type: ReportTypeUserRoleStatus,
		DefaultFields: []ReportFields{
			{Key: ReportAttributesProjectNumber},
			{Key: ReportAttributesProjectName},
			{Key: ReportAttributesInfoUserName},
			{Key: ReportAttributesInfoUserEmail},
			{Key: ReportAttributesInfoUserRole},
			{Key: ReportAttributesInfoSiteName},
			{Key: ReportAttributesInfoStorehouseName},
			{Key: ReportAttributesOrderCreateTime},
			{Key: ReportAttributesInfoStatus},
		},
	}
	AuditTrailExport = Report{
		Name: "menu.report.auditTrailExport",
		Type: ReportTypeAuditTrailExport,
	}

	ProjectPermissionConfigurationExport = Report{
		Name: "menu.report.projectPermissionConfigurationExport",
		Type: ReportTypeProjectPermissionConfigurationExport,
		DefaultFields: []ReportFields{
			{Key: ReportAttributesProjectNumber},
			{Key: ReportAttributesProjectName},
			{Key: "common.role"},
			{Key: "common.menu"},
			{Key: "common.operation"},
		},
	}
	ConfigureReport = Report{
		Name:          "menu.report.configureReport",
		Type:          ReportTypeConfigureReport,
		DefaultFields: []ReportFields{},
	}
	SourceIPExport = Report{
		Name:         "menu.report.sourceIPExport",
		Type:         ReportTypeSourceIPExport,
		Customizable: true,
		DefaultFields: []ReportFields{
			{Key: ReportAttributesProjectNumber},
			{Key: ReportAttributesProjectName},
			{Key: ReportAttributesResearchPackageNumber},
			{Key: ReportAttributesResearchPackageSerialNumber},
			{Key: ReportAttributesResearchMedicineSerialNumber},
			{Key: ReportAttributesResearchMedicineName},
			{Key: ReportAttributesResearchMedicineNumber},
			{Key: ReportAttributesResearchExpireDate},
			{Key: ReportAttributesResearchBatch},
			{Key: ReportAttributesResearchStatus},
			{Key: ReportAttributesInfoSubjectNumber},
			{Key: ReportAttributesInfoSiteNumber},
			{Key: ReportAttributesInfoSiteName},
			{Key: ReportAttributesDispensingTime},
			{Key: ReportAttributesDispensingIsMedicineReplace},
			{Key: ReportAttributesDispensingIsMedicineReal},
			{Key: ReportAttributesRandomStage},
			{Key: ReportAttributesRandomCohort},
		},
		CustomFields: []ReportFields{
			{
				Key: ReportAttributesProjectNumber,
				//Required: true,
			},
			{
				Key: ReportAttributesProjectName,
				//Required: true,
			},
			{
				Key: ReportAttributesInfo,
				Children: []ReportFields{
					{Key: ReportAttributesInfoSubjectNumber},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
				},
			},
			{
				Key: ReportAttributesDispensing,
				Children: []ReportFields{
					{Key: ReportAttributesDispensingTime},
					{Key: ReportAttributesDispensingIsMedicineReplace},
					{Key: ReportAttributesDispensingIsMedicineReal},
					{Key: ReportAttributesRandomCohort, Required: true},
					{Key: ReportAttributesRandomStage, Required: true},
				},
			},
			{
				Key: ReportAttributesResearch,
				Children: []ReportFields{
					{Key: ReportAttributesResearchMedicineSerialNumber},
					{Key: ReportAttributesResearchMedicineName},
					{Key: ReportAttributesResearchMedicineNumber},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchPackageNumber},
					{Key: ReportAttributesResearchPackageSerialNumber},
					{Key: ReportAttributesResearchStatus},
				},
			},
		},
	}
	SourceRandomizationListExport = Report{
		Name:         "menu.report.sourceRandomizationListExport",
		Type:         ReportTypeSourceRandomizationListExport,
		Customizable: false,
		DefaultFields: []ReportFields{
			{Key: ReportAttributesProjectNumber},
			{Key: ReportAttributesProjectName},
			{Key: ReportAttributesInfoSiteNumber},
			{Key: ReportAttributesInfoSiteName},
			{Key: ReportAttributesRandomCohort},
			{Key: ReportAttributesInfoSubjectNumber},
			{Key: ReportAttributesInfoStatus},
			{Key: ReportAttributesRandomTime},
			{Key: "report.simulate.random.block"},
			{Key: "report.attributes.random.number"},
			{Key: "report.simulate.random.group"},
			{Key: ReportAttributesDispensingMedicineRealGroup},
			{Key: "report.attributes.random.config.code"},
			{Key: "projects.randomization.planNumber"},
			{Key: "report.attributes.random.g"},
			{Key: "report.attributes.random.factor"},
			{Key: ReportAttributesRandomSubjectReplaceTime},
			{Key: ReportAttributesRandomSubjectNumberReplace},
			{Key: ReportAttributesRandomSubjectReplaceNumber},
			{Key: ReportAttributesRandomSubjectReplaceStatus},
		},
		CustomFields: []ReportFields{},
	}
	RandomizationSimulationReport = Report{
		Name:          "menu.report.randomizationSimulationReport",
		Type:          ReportTypeRandomizationSimulationReport,
		Customizable:  true,
		DefaultFields: []ReportFields{},
		CustomFields:  []ReportFields{},
	}
	RandomizeReport = Report{
		Name:          "menu.report.randomizeReport",
		Type:          ReportTypeRandomizeReport,
		Customizable:  true,
		DefaultFields: []ReportFields{},
		MultiDefaultFields: []MultiDefaultFields{
			{
				Type: 1,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoCountry},
					{Key: ReportAttributesInfoRegion},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesInfoSubjectNumber},
					{Key: ReportAttributesInfoStatus},
					{Key: ReportAttributesRandomFactor},
					{Key: ReportAttributesRandomNumber},
					{Key: ReportAttributesRandomTime},
					{Key: ReportAttributesRandomGroup},
					{Key: ReportAttributesDispensingMedicineRealGroup},
					{Key: ReportAttributesRandomConfigCode},
					{Key: ReportAttributesRandomSubjectNumberReplace},
					{Key: ReportAttributesRandomSubjectReplaceNumber},
					{Key: ReportAttributesRandomSubjectReplaceStatus},
					{Key: ReportAttributesRandomSubjectReplaceTime},
					{Key: ReportAttributesRandomScreenTime},
					{Key: ReportAttributesRandomSignOutRealTime},
					{Key: ReportAttributesRandomFinishRemark},
				},
			},
			{
				Type: 1,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
				},
			},
			{
				Type: 2,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoCountry},
					{Key: ReportAttributesInfoRegion},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesRandomCohort},
					{Key: ReportAttributesInfoSubjectNumber},
					{Key: ReportAttributesInfoStatus},
					{Key: ReportAttributesRandomFactor},
					{Key: ReportAttributesRandomNumber},
					{Key: ReportAttributesRandomTime},
					{Key: ReportAttributesRandomGroup},
					{Key: ReportAttributesDispensingMedicineRealGroup},
					{Key: ReportAttributesRandomConfigCode},
					{Key: ReportAttributesRandomSubjectNumberReplace},
					{Key: ReportAttributesRandomSubjectReplaceNumber},
					{Key: ReportAttributesRandomSubjectReplaceStatus},
					{Key: ReportAttributesRandomSubjectReplaceTime},
					{Key: ReportAttributesRandomScreenTime},
					{Key: ReportAttributesRandomSignOutRealTime},
					{Key: ReportAttributesRandomFinishRemark},
				},
			},
			{
				Type: 2,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesRandomCohort},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
				},
			},
			{
				Type: 3,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoCountry},
					{Key: ReportAttributesInfoRegion},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesInfoSubjectNumber},
					{Key: ReportAttributesInfoStatus},
					{Key: ReportAttributesRandomFactor},
					{Key: ReportAttributesRandomNumber},
					{Key: ReportAttributesRandomTime},
					{Key: ReportAttributesRandomGroup},
					{Key: ReportAttributesDispensingMedicineRealGroup},
					{Key: ReportAttributesRandomConfigCode},
					{Key: ReportAttributesRandomSubjectNumberReplace},
					{Key: ReportAttributesRandomSubjectReplaceNumber},
					{Key: ReportAttributesRandomSubjectReplaceStatus},
					{Key: ReportAttributesRandomSubjectReplaceTime},
					{Key: ReportAttributesRandomScreenTime},
					{Key: ReportAttributesRandomSignOutRealTime},
					{Key: ReportAttributesRandomFinishRemark},
				},
			},
			{
				Type: 3,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesRandomStage},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
				},
			},
		},
		CustomFields: []ReportFields{
			{
				Key: ReportAttributesProjectNumber,
				//Required: true,
			},
			{
				Key: ReportAttributesProjectName,
				//Required: true,
			},
			{
				Key: ReportAttributesInfo,
				Children: []ReportFields{
					{Key: ReportAttributesInfoCountry},
					{Key: ReportAttributesInfoRegion},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesInfoSubjectNumber},
					{Key: ReportAttributesInfoStatus},
				},
			},
			{
				Key: ReportAttributesRandom,
				Children: []ReportFields{
					{Key: ReportAttributesRandomFactor},
					{Key: ReportAttributesRandomTime},
					{Key: ReportAttributesRandomGroup},
					{Key: ReportAttributesDispensingMedicineRealGroup},
					{Key: ReportAttributesRandomNumber},
					{Key: ReportAttributesRandomRegisterTime},
					{Key: ReportAttributesRandomRegisterOperator},
					{Key: ReportAttributesRandomOperator},
					{Key: ReportAttributesRandomStage, Required: true},
					{Key: ReportAttributesRandomSubjectNumberReplace},
					{Key: ReportAttributesRandomForm},
					{Key: ReportAttributesRandomFactorCalc},
					{Key: ReportAttributesRandomActualFactor},
					{Key: ReportAttributesRandomCohort, Required: true},
					{Key: ReportAttributesRandomSubjectReplaceNumber},
					{Key: ReportAttributesRandomSubjectReplaceStatus},
					{Key: ReportAttributesRandomSubjectReplaceTime},
					{Key: ReportAttributesRandomConfigCode},
					{Key: ReportAttributesRandomSignOutOperator},
					{Key: ReportAttributesRandomSignOutTime},
					{Key: ReportAttributesRandomSignOutRealTime},
					{Key: ReportAttributesRandomSignOutReason},
					{Key: ReportAttributesRandomScreenTime},
					{Key: ReportAttributesRandomICFTime},
					{Key: ReportAttributesRandomFinishRemark},
					{Key: ReportAttributesRandomPlanTime},
					{Key: ReportAttributesRandomSequenceNumber},
				},
			},
		},
	}
	DispenseReport = Report{
		Name:          "menu.report.dispenseReport",
		Type:          ReportTypeDispenseReport,
		Customizable:  true,
		DefaultFields: []ReportFields{},
		MultiDefaultFields: []MultiDefaultFields{
			{
				Type: 1,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoCountry},
					{Key: ReportAttributesInfoRegion},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesInfoSubjectNumber},
					{Key: ReportAttributesDispensingCycleName},
					{Key: ReportAttributesDispensingType},
					{Key: ReportAttributesDispensingTime},
					{Key: ReportAttributesDispensingOperator},
					{Key: ReportAttributesDispensingMedicine},
					{Key: ReportAttributesDispensingDrugName},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesDispensingLabel},
					{Key: ReportAttributesDispensingDose},
					{Key: ReportAttributesDispensingMedicineReplace},
					{Key: ReportAttributesDispensingMedicineReal},
					{Key: ReportAttributesDispensingMedicineRealGroup},
					{Key: ReportAttributesDispensingDrugOtherNumber},
					{Key: ReportAttributesDispensingUseFormulas},
				},
			},
			{
				Type: 1,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoCountry},
					{Key: ReportAttributesInfoRegion},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesInfoSubjectNumber},
					{Key: ReportAttributesDispensingCycleName},
					{Key: ReportAttributesDispensingType},
					{Key: ReportAttributesDispensingTime},
					{Key: ReportAttributesDispensingOperator},
					{Key: ReportAttributesDispensingMedicine},
					{Key: ReportAttributesDispensingDrugName},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesDispensingLabel},
					{Key: ReportAttributesDispensingDose},
					{Key: ReportAttributesDispensingMedicineReplace},
					{Key: ReportAttributesDispensingMedicineReal},
					{Key: ReportAttributesDispensingMedicineRealGroup},
					{Key: ReportAttributesDispensingDrugOtherNumber},
					{Key: ReportAttributesDispensingUseFormulas},
				},
			},
			{
				Type: 2,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoCountry},
					{Key: ReportAttributesInfoRegion},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesRandomCohort},
					{Key: ReportAttributesInfoSubjectNumber},
					{Key: ReportAttributesDispensingCycleName},
					{Key: ReportAttributesDispensingType},
					{Key: ReportAttributesDispensingTime},
					{Key: ReportAttributesDispensingOperator},
					{Key: ReportAttributesDispensingMedicine},
					{Key: ReportAttributesDispensingDrugName},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesDispensingLabel},
					{Key: ReportAttributesDispensingDose},
					{Key: ReportAttributesDispensingMedicineReplace},
					{Key: ReportAttributesDispensingMedicineReal},
					{Key: ReportAttributesDispensingMedicineRealGroup},
					{Key: ReportAttributesDispensingDrugOtherNumber},
					{Key: ReportAttributesDispensingUseFormulas},
				},
			},
			{
				Type: 2,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoCountry},
					{Key: ReportAttributesInfoRegion},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesRandomCohort},
					{Key: ReportAttributesInfoSubjectNumber},
					{Key: ReportAttributesDispensingCycleName},
					{Key: ReportAttributesDispensingType},
					{Key: ReportAttributesDispensingTime},
					{Key: ReportAttributesDispensingOperator},
					{Key: ReportAttributesDispensingMedicine},
					{Key: ReportAttributesDispensingDrugName},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesDispensingLabel},
					{Key: ReportAttributesDispensingDose},
					{Key: ReportAttributesDispensingMedicineReplace},
					{Key: ReportAttributesDispensingMedicineReal},
					{Key: ReportAttributesDispensingMedicineRealGroup},
					{Key: ReportAttributesDispensingDrugOtherNumber},
					{Key: ReportAttributesDispensingUseFormulas},
				},
			},
			{
				Type: 3,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoCountry},
					{Key: ReportAttributesInfoRegion},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesInfoSubjectNumber},
					{Key: ReportAttributesRandomStage},
					{Key: ReportAttributesDispensingCycleName},
					{Key: ReportAttributesDispensingType},
					{Key: ReportAttributesDispensingTime},
					{Key: ReportAttributesDispensingOperator},
					{Key: ReportAttributesDispensingMedicine},
					{Key: ReportAttributesDispensingDrugName},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesDispensingLabel},
					{Key: ReportAttributesDispensingDose},
					{Key: ReportAttributesDispensingMedicineReplace},
					{Key: ReportAttributesDispensingMedicineReal},
					{Key: ReportAttributesDispensingMedicineRealGroup},
					{Key: ReportAttributesDispensingDrugOtherNumber},
					{Key: ReportAttributesDispensingUseFormulas},
					{Key: ReportAttributesDispensingMedicineReissueRemark},
					{Key: ReportAttributesDispensingOutVisitDispensingRemark},
					{Key: ReportAttributesDispensingMedicineReplaceRemark},
				},
			},
			{
				Type: 3,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoCountry},
					{Key: ReportAttributesInfoRegion},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesInfoSubjectNumber},
					{Key: ReportAttributesRandomStage},
					{Key: ReportAttributesDispensingCycleName},
					{Key: ReportAttributesDispensingType},
					{Key: ReportAttributesDispensingTime},
					{Key: ReportAttributesDispensingOperator},
					{Key: ReportAttributesDispensingMedicine},
					{Key: ReportAttributesDispensingDrugName},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesDispensingLabel},
					{Key: ReportAttributesDispensingDose},
					{Key: ReportAttributesDispensingMedicineReplace},
					{Key: ReportAttributesDispensingMedicineReal},
					{Key: ReportAttributesDispensingMedicineRealGroup},
					{Key: ReportAttributesDispensingDrugOtherNumber},
					{Key: ReportAttributesDispensingUseFormulas},
					{Key: ReportAttributesDispensingMedicineReissueRemark},
					{Key: ReportAttributesDispensingOutVisitDispensingRemark},
					{Key: ReportAttributesDispensingMedicineReplaceRemark},
				},
			},
		},
		CustomFields: []ReportFields{
			{
				Key: ReportAttributesProjectNumber,
				//Required: true,
			},
			{
				Key: ReportAttributesProjectName,
				//Required: true,
			},
			{
				Key: ReportAttributesInfo,
				Children: []ReportFields{
					{Key: ReportAttributesInfoCountry},
					{Key: ReportAttributesInfoRegion},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
				},
			},
			{
				Key: ReportAttributesDispensing,
				Children: []ReportFields{
					{Key: ReportAttributesRandomCohort, Required: true},
					{Key: ReportAttributesInfoSubjectNumber},
					{Key: ReportAttributesRandomStage, Required: true},
					{Key: ReportAttributesRandomNumber},
					{Key: ReportAttributesDispensingCycleName},
					{Key: ReportAttributesDispensingType},
					{Key: ReportAttributesDispensingPlanTime},
					{Key: ReportAttributesDispensingTime},
					{Key: ReportAttributesDispensingOperator},
					{Key: ReportAttributesDispensingMedicine},
					{Key: ReportAttributesDispensingDrugName},
					{Key: ReportAttributesDispensingLabel},
					{Key: ReportAttributesDispensingDose},
					{Key: ReportAttributesDispensingMedicineReplace},
					{Key: ReportAttributesDispensingMedicineReal},
					{Key: ReportAttributesDispensingMedicineRealGroup},
					{Key: ReportAttributesDispensingDrugOtherNumber},
					{Key: ReportAttributesDispensingRemark},
					{Key: ReportAttributesDispensingOutVisitDispensingReason},
					{Key: ReportAttributesDispensingReissueReason},
					{Key: ReportAttributesResearchPackageNumber},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesDispensingOutSize},
					{Key: ReportAttributesDispensingDoseFormulas},
					{Key: ReportAttributesDispensingUseFormulas},
					{Key: ReportAttributesDispensingMedicineReissueRemark},
					{Key: ReportAttributesDispensingOutVisitDispensingRemark},
					{Key: ReportAttributesDispensingMedicineReplaceRemark},
					{Key: ReportAttributesDispensingMedicineRetrievalRemark},
					{Key: ReportAttributesDispensingMedicineRegisterRemark},
					{Key: ReportAttributesDispensingMedicineInvalidRemark},
					{Key: ReportAttributesDispensingMedicineSendType},
					{Key: ReportAttributesDispensingMedicineLogisticsInfo},
					{Key: ReportAttributesDispensingMedicineLogisticsRemark},
				},
			},
		},
	}
	OnlyDispensingDispenseReport = Report{
		Name:          "menu.report.dispenseReport",
		Type:          ReportTypeDispenseReport,
		Customizable:  true,
		DefaultFields: []ReportFields{},
		MultiDefaultFields: []MultiDefaultFields{
			{
				Type: 1,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoCountry},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesInfoSubjectNumber},
					{Key: ReportAttributesDispensingCycleName},
					{Key: ReportAttributesDispensingType},
					{Key: ReportAttributesDispensingTime},
					{Key: ReportAttributesDispensingOperator},
					{Key: ReportAttributesDispensingMedicine},
					{Key: ReportAttributesDispensingDrugName},
					{Key: ReportAttributesDispensingLabel},
					{Key: ReportAttributesDispensingMedicineReplace},
					{Key: ReportAttributesDispensingMedicineReal},
					{Key: ReportAttributesDispensingDrugOtherNumber},
				},
			},
			{
				Type: 2,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoCountry},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesRandomCohort},
					{Key: ReportAttributesInfoSubjectNumber},
					{Key: ReportAttributesDispensingCycleName},
					{Key: ReportAttributesDispensingType},
					{Key: ReportAttributesDispensingTime},
					{Key: ReportAttributesDispensingOperator},
					{Key: ReportAttributesDispensingMedicine},
					{Key: ReportAttributesDispensingDrugName},
					{Key: ReportAttributesDispensingLabel},
					{Key: ReportAttributesDispensingMedicineReplace},
					{Key: ReportAttributesDispensingMedicineReal},
					{Key: ReportAttributesDispensingDrugOtherNumber},
				},
			},
			{
				Type: 3,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoCountry},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesInfoSubjectNumber},
					{Key: ReportAttributesRandomStage},
					{Key: ReportAttributesDispensingCycleName},
					{Key: ReportAttributesDispensingType},
					{Key: ReportAttributesDispensingTime},
					{Key: ReportAttributesDispensingOperator},
					{Key: ReportAttributesDispensingMedicine},
					{Key: ReportAttributesDispensingDrugName},
					{Key: ReportAttributesDispensingLabel},
					{Key: ReportAttributesDispensingMedicineReplace},
					{Key: ReportAttributesDispensingMedicineReal},
					{Key: ReportAttributesDispensingDrugOtherNumber},
				},
			},
		},
		CustomFields: []ReportFields{
			{
				Key: ReportAttributesProjectNumber,
				//Required: true,
			},
			{
				Key: ReportAttributesProjectName,
				//Required: true,
			},
			{
				Key: ReportAttributesInfo,
				Children: []ReportFields{
					{Key: ReportAttributesInfoCountry},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
				},
			},
			{
				Key: ReportAttributesDispensing,
				Children: []ReportFields{
					{Key: ReportAttributesRandomCohort, Required: true},
					{Key: ReportAttributesInfoSubjectNumber},
					{Key: ReportAttributesRandomStage, Required: true},
					{Key: ReportAttributesDispensingCycleName},
					{Key: ReportAttributesDispensingType},
					{Key: ReportAttributesDispensingPlanTime},
					{Key: ReportAttributesDispensingTime},
					{Key: ReportAttributesDispensingOperator},
					{Key: ReportAttributesDispensingMedicine},
					{Key: ReportAttributesDispensingDrugName},
					{Key: ReportAttributesDispensingLabel},
					{Key: ReportAttributesDispensingDose},
					{Key: ReportAttributesDispensingMedicineReplace},
					{Key: ReportAttributesDispensingMedicineReal},
					{Key: ReportAttributesDispensingDrugOtherNumber},
					{Key: ReportAttributesDispensingRemark},
					{Key: ReportAttributesDispensingOutVisitDispensingReason},
					{Key: ReportAttributesDispensingReissueReason},
					{Key: ReportAttributesResearchPackageNumber},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesDispensingOutSize},
					{Key: ReportAttributesDispensingDoseFormulas},
					{Key: ReportAttributesDispensingUseFormulas},
					{Key: ReportAttributesDispensingMedicineReissueRemark},
					{Key: ReportAttributesDispensingOutVisitDispensingRemark},
					{Key: ReportAttributesDispensingMedicineReplaceRemark},
					{Key: ReportAttributesDispensingMedicineRetrievalRemark},
					{Key: ReportAttributesDispensingMedicineRegisterRemark},
					{Key: ReportAttributesDispensingMedicineInvalidRemark},
					{Key: ReportAttributesDispensingMedicineSendType},
					{Key: ReportAttributesDispensingMedicineLogisticsInfo},
					{Key: ReportAttributesDispensingMedicineLogisticsRemark},
				},
			},
		},
	}
	UnblindingReport = Report{
		Name:         "menu.report.unblindingReport",
		Type:         ReportTypeUnblindingReport,
		Customizable: true,
		DefaultFields: []ReportFields{
			{Key: ReportAttributesProjectNumber},
			{Key: ReportAttributesProjectName},
			{Key: ReportAttributesInfoCountry},
			{Key: ReportAttributesInfoRegion},
			{Key: ReportAttributesInfoSiteNumber},
			{Key: ReportAttributesInfoSiteName},
			{Key: ReportAttributesRandomCohort},
			{Key: ReportAttributesRandomStage},
			{Key: ReportAttributesInfoSubjectNumber},
			{Key: ReportAttributesRandomNumber},
			{Key: ReportAttributesUnblindingSponsor},
			{Key: ReportAttributesUnblindingMark},
			{Key: ReportAttributesUnblindingReason},
			{Key: ReportAttributesUnblindingReasonMark},
			{Key: ReportAttributesUnblindingOperator},
			{Key: ReportAttributesUnblindingTime},
		},
		CustomFields: []ReportFields{
			{
				Key: ReportAttributesProjectNumber,
				//Required: true,
			},
			{
				Key: ReportAttributesProjectName,
				//Required: true,
			},
			{
				Key: ReportAttributesInfo,
				Children: []ReportFields{
					{Key: ReportAttributesInfoCountry},
					{Key: ReportAttributesInfoRegion},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesInfoSubjectNumber},
				},
			},
			{
				Key: ReportAttributesRandom,
				Children: []ReportFields{
					{Key: ReportAttributesRandomNumber},
					{Key: ReportAttributesRandomCohort, Required: true},
					{Key: ReportAttributesRandomStage, Required: true},
				},
			},
			{
				Key: ReportAttributesDispensing,
				Children: []ReportFields{
					{Key: ReportAttributesDispensingMedicineRealNumber},
				},
			},
			{
				Key: ReportAttributesUnblinding,
				Children: []ReportFields{
					{Key: ReportAttributesUnblindingSponsor},
					{Key: ReportAttributesUnblindingMark},
					{Key: ReportAttributesUnblindingReason},
					{Key: ReportAttributesUnblindingReasonMark},
					{Key: ReportAttributesUnblindingOperator},
					{Key: ReportAttributesUnblindingTime},
				},
			},
		},
	}
	RoomNumberViewHistory = Report{
		Name:          "menu.report.roomNumberViewHistory",
		Type:          ReportTypeRoomNumberViewHistory,
		Customizable:  true,
		DefaultFields: []ReportFields{},
		CustomFields:  []ReportFields{},
	}
	DepotItemReport = Report{
		Name:         "menu.report.depotItemReport",
		Type:         ReportTypeDepotItemReport,
		Customizable: true,
		MultiDefaultFields: []MultiDefaultFields{
			{
				Type: 1,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoStorehouseName},
					{Key: ReportAttributesResearchMedicineNumber},
					{Key: ReportAttributesResearchMedicineName},
					{Key: ReportAttributesResearchPackageNumber},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchSpec},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchOther},
					{Key: ReportAttributesResearchOrderNumber},
					{Key: ReportAttributesResearchStatus},
				},
			},
			{
				Type: 1,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoStorehouseName},
					{Key: ReportAttributesResearchMedicineName},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchSpec},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchOther},
					{Key: ReportAttributesResearchOrderNumber},
					{Key: ReportAttributesResearchStatus},
				},
			},
			{
				Type: 2,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesRandomCohort},
					{Key: ReportAttributesInfoStorehouseName},
					{Key: ReportAttributesResearchMedicineNumber},
					{Key: ReportAttributesResearchMedicineName},
					{Key: ReportAttributesResearchPackageNumber},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchSpec},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchOther},
					{Key: ReportAttributesResearchOrderNumber},
					{Key: ReportAttributesResearchStatus},
				},
			},
			{
				Type: 2,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesRandomCohort},
					{Key: ReportAttributesInfoStorehouseName},
					{Key: ReportAttributesResearchMedicineName},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchSpec},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchOther},
					{Key: ReportAttributesResearchOrderNumber},
					{Key: ReportAttributesResearchStatus},
				},
			},
			{
				Type: 3,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesRandomStage},
					{Key: ReportAttributesInfoStorehouseName},
					{Key: ReportAttributesResearchMedicineNumber},
					{Key: ReportAttributesResearchMedicineName},
					{Key: ReportAttributesResearchPackageNumber},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchSpec},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchOther},
					{Key: ReportAttributesResearchOrderNumber},
					{Key: ReportAttributesResearchStatus},
				},
			},
			{
				Type: 3,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesRandomStage},
					{Key: ReportAttributesInfoStorehouseName},
					{Key: ReportAttributesResearchMedicineName},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchSpec},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchOther},
					{Key: ReportAttributesResearchOrderNumber},
					{Key: ReportAttributesResearchStatus},
				},
			},
		},
		CustomFields: []ReportFields{
			{
				Key: ReportAttributesProjectNumber,
				//Required: true,
			},
			{
				Key: ReportAttributesProjectName,
				//Required: true,
			},
			{
				Key: ReportAttributesInfo,
				Children: []ReportFields{
					{Key: ReportAttributesInfoStorehouseName},
					{
						Key:      ReportAttributesRandomCohort,
						Required: true,
					},
					{
						Key:      ReportAttributesRandomStage,
						Required: true,
					},
				},
			},
			{
				Key: ReportAttributesResearch,
				Children: []ReportFields{
					{Key: ReportAttributesResearchMedicineNumber},
					{Key: ReportAttributesResearchMedicineName},
					{Key: ReportAttributesResearchPackageNumber},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchSpec},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchOrderNumber},
					{Key: ReportAttributesResearchStatus},
					{Key: ReportAttributesResearchReason},
					{Key: ReportAttributesResearchOperator},
					{Key: ReportAttributesResearchTime},
					{Key: ReportAttributesResearchOther},
				},
			},
		},
	}
	SiteItemReport = Report{
		Name:         "menu.report.siteItemReport",
		Type:         ReportTypeSiteItemReport,
		Customizable: true,
		MultiDefaultFields: []MultiDefaultFields{
			{
				Type: 1,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesResearchMedicineNumber},
					{Key: ReportAttributesResearchMedicineName},
					{Key: ReportAttributesResearchPackageNumber},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchSpec},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchOther},
					{Key: ReportAttributesResearchOrderNumber},
					{Key: ReportAttributesResearchStatus},
				},
			},
			{
				Type: 1,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesResearchMedicineName},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchSpec},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchOther},
					{Key: ReportAttributesResearchOrderNumber},
					{Key: ReportAttributesResearchStatus},
				},
			},
			{
				Type: 2,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesResearchMedicineNumber},
					{Key: ReportAttributesResearchMedicineName},
					{Key: ReportAttributesResearchPackageNumber},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchSpec},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchOther},
					{Key: ReportAttributesResearchOrderNumber},
					{Key: ReportAttributesResearchStatus},
				},
			},
			{
				Type: 2,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesResearchMedicineName},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchSpec},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchOther},
					{Key: ReportAttributesResearchOrderNumber},
					{Key: ReportAttributesResearchStatus},
				},
			},
			{
				Type: 3,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesResearchMedicineNumber},
					{Key: ReportAttributesResearchMedicineName},
					{Key: ReportAttributesResearchPackageNumber},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchSpec},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchOther},
					{Key: ReportAttributesResearchOrderNumber},
					{Key: ReportAttributesResearchStatus},
				},
			},
			{
				Type: 3,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesResearchMedicineName},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchSpec},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchOther},
					{Key: ReportAttributesResearchOrderNumber},
					{Key: ReportAttributesResearchStatus},
				},
			},
		},
		CustomFields: []ReportFields{
			{
				Key: ReportAttributesProjectNumber,
				//Required: true,
			},
			{
				Key: ReportAttributesProjectName,
				//Required: true,
			},
			{
				Key: ReportAttributesInfo,
				Children: []ReportFields{
					// {
					// 	Key:      ReportAttributesRandomCohort,
					// 	Required: true,
					// },
					// {
					// 	Key:      ReportAttributesRandomStage,
					// 	Required: true,
					// },
					{Key: ReportAttributesInfoSiteCountry},
					{Key: ReportAttributesInfoSiteRegion},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesInfoSiteNumber},
				},
			},
			{
				Key: ReportAttributesResearch,
				Children: []ReportFields{
					{Key: ReportAttributesResearchMedicineNumber},
					{Key: ReportAttributesResearchMedicineName},
					{Key: ReportAttributesResearchPackageNumber},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchSpec},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchOrderNumber},
					{Key: ReportAttributesResearchStatus},
					{Key: ReportAttributesResearchReason},
					{Key: ReportAttributesResearchOperator},
					{Key: ReportAttributesResearchTime},
					{Key: ReportAttributesResearchOther},
					// {Key: ReportAttributesResearchFreezeReason},
					// {Key: ReportAttributesResearchFreezeOperator},
					// {Key: ReportAttributesResearchFreezeTime},
					// {Key: ReportAttributesResearchReleaseReason},
					// {Key: ReportAttributesResearchReleaseOperator},
					// {Key: ReportAttributesResearchReleaseTime},
					// {Key: ReportAttributesOrderLostReason},
					// {Key: ReportAttributesResearchLostOperator},
					// {Key: ReportAttributesResearchFreezeLostTime},
					// {Key: ReportAttributesResearchUseReason},
					// {Key: ReportAttributesResearchUseOperator},
					// {Key: ReportAttributesResearchUseTime},
				},
			},
		},
	}
	ShipmentOrdersReport = Report{
		Name:         "menu.report.shipmentOrdersReport",
		Type:         ReportTypeShipmentOrdersReport,
		Customizable: true,
		DefaultFields: []ReportFields{
			{Key: ReportAttributesProjectNumber},
			{Key: ReportAttributesProjectName},
			{Key: ReportAttributesOrderNumber},
			{Key: ReportAttributesOrderStatus},
			{Key: ReportAttributesOrderSend},
			{Key: ReportAttributesOrderReceive},
			{Key: ReportAttributesOrderMedicineQuantity},
			{Key: ReportAttributesOrderCreateBy},
			{Key: ReportAttributesOrderCreateTime},
			{Key: ReportAttributesOrderReceiveBy},
			{Key: ReportAttributesOrderReceiveTime},
			{Key: ReportAttributesOrderCloseBy},
			{Key: ReportAttributesOrderCloseTime},
			{Key: ReportAttributesOrderEndBy},
			{Key: ReportAttributesOrderEndTime},
			{Key: ReportAttributesOrderLostBy},
			{Key: ReportAttributesOrderLostTime},
			//{Key: ReportAttributesOrderExpectedArrivalTime},
			{Key: ReportAttributesOrderActualReceiptTime},
			//{Key: ReportAttributesOrderCancelBy},
			//{Key: ReportAttributesOrderCancelTime},
			//{Key: ReportAttributesOrderCancelReason},
			{Key: ReportAttributesResearch},
			{Key: ReportAttributesResearchBatch},
			{Key: ReportAttributesResearchExpireDate},
			{Key: ReportAttributesResearchOther},
		},
		CustomFields: []ReportFields{
			{
				Key: ReportAttributesProjectNumber,
				//Required: true,
			},
			{
				Key: ReportAttributesProjectName,
				//Required: true,
			},
			{Key: ReportAttributesOrder,
				Children: []ReportFields{
					{Key: ReportAttributesOrderNumber},
					{Key: ReportAttributesOrderStatus},
					{Key: ReportAttributesOrderSend},
					{Key: ReportAttributesOrderReceive},
					{Key: ReportAttributesOrderMedicineQuantity},
					{Key: ReportAttributesOrderCreateBy},
					{Key: ReportAttributesOrderCreateTime},
					{Key: ReportAttributesOrderCancelBy},
					{Key: ReportAttributesOrderCancelTime},
					{Key: ReportAttributesOrderCancelReason},
					{Key: ReportAttributesOrderConfirmBy},
					{Key: ReportAttributesOrderConfirmTime},
					{Key: ReportAttributesOrderCloseBy},
					{Key: ReportAttributesOrderCloseTime},
					{Key: ReportAttributesOrderCloseReason},
					{Key: ReportAttributesOrderSendBy},
					{Key: ReportAttributesOrderSendTime},
					{Key: ReportAttributesOrderReceiveBy},
					{Key: ReportAttributesOrderReceiveTime},
					{Key: ReportAttributesOrderLostBy},
					{Key: ReportAttributesOrderLostTime},
					{Key: ReportAttributesOrderLostReason},
					{Key: ReportAttributesOrderEndBy},
					{Key: ReportAttributesOrderEndTime},
					{Key: ReportAttributesOrderEndReason},
					{Key: ReportAttributesOrderSupplier},
					{Key: ReportAttributesOrderSupplierOther},
					{Key: ReportAttributesOrderSupplierNumber},
					{Key: ReportAttributesOrderExpectedArrivalTime},
					{Key: ReportAttributesOrderActualReceiptTime},
				}},
			{Key: ReportAttributesOrderDetail,
				Children: []ReportFields{
					{Key: ReportAttributesResearch},
					{Key: ReportAttributesResearchPackageNumber},
					{Key: ReportAttributesResearchPackageMethod},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchStatus},
					{Key: ReportAttributesResearchOther},
				}},
		},
	}
	ReturnOrdersReport = Report{
		Name:         "menu.report.returnOrdersReport",
		Type:         ReportTypeReturnOrdersReport,
		Customizable: true,
		DefaultFields: []ReportFields{
			{Key: ReportAttributesProjectNumber},
			{Key: ReportAttributesProjectName},
			{Key: ReportAttributesOrderNumber},
			{Key: ReportAttributesOrderStatus},
			{Key: ReportAttributesOrderSend},
			{Key: ReportAttributesOrderReceive},
			{Key: ReportAttributesOrderMedicineQuantity},
			{Key: ReportAttributesOrderCreateBy},
			{Key: ReportAttributesOrderCreateTime},
			//{Key: ReportAttributesOrderCancelBy},
			//{Key: ReportAttributesOrderCancelTime},
			//{Key: ReportAttributesOrderCancelReason},
			{Key: ReportAttributesOrderReceiveBy},
			{Key: ReportAttributesOrderReceiveTime},
			{Key: ReportAttributesOrderCloseBy},
			{Key: ReportAttributesOrderCloseTime},
			{Key: ReportAttributesOrderEndBy},
			{Key: ReportAttributesOrderEndTime},
			{Key: ReportAttributesOrderLostBy},
			{Key: ReportAttributesOrderLostTime},
			{Key: ReportAttributesResearch},
			{Key: ReportAttributesResearchBatch},
			{Key: ReportAttributesResearchExpireDate},
			{Key: ReportAttributesResearchOther},
			{Key: ReportAttributesOrderActualReceiptTime},
		},
		CustomFields: []ReportFields{
			{
				Key: ReportAttributesProjectNumber,
				//Required: true,
			},
			{
				Key: ReportAttributesProjectName,
				//Required: true,
			},
			{Key: ReportAttributesOrder,
				Children: []ReportFields{
					{Key: ReportAttributesOrderNumber},
					{Key: ReportAttributesOrderStatus},
					{Key: ReportAttributesOrderSend},
					{Key: ReportAttributesOrderReceive},
					{Key: ReportAttributesOrderMedicineQuantity},
					{Key: ReportAttributesOrderCreateBy},
					{Key: ReportAttributesOrderCreateTime},
					{Key: ReportAttributesOrderCancelBy},
					{Key: ReportAttributesOrderCancelTime},
					{Key: ReportAttributesOrderCancelReason},
					{Key: ReportAttributesOrderConfirmBy},
					{Key: ReportAttributesOrderConfirmTime},
					{Key: ReportAttributesOrderCloseBy},
					{Key: ReportAttributesOrderCloseTime},
					{Key: ReportAttributesOrderCloseReason},
					{Key: ReportAttributesOrderSendBy},
					{Key: ReportAttributesOrderSendTime},
					{Key: ReportAttributesOrderReceiveBy},
					{Key: ReportAttributesOrderReceiveTime},
					{Key: ReportAttributesOrderLostBy},
					{Key: ReportAttributesOrderLostTime},
					{Key: ReportAttributesOrderLostReason},
					{Key: ReportAttributesOrderEndBy},
					{Key: ReportAttributesOrderEndTime},
					{Key: ReportAttributesOrderEndReason},
					{Key: ReportAttributesOrderSupplier},
					{Key: ReportAttributesOrderSupplierOther},
					{Key: ReportAttributesOrderSupplierNumber},
					{Key: ReportAttributesOrderExpectedArrivalTime},
					{Key: ReportAttributesOrderActualReceiptTime},
				}},
			{Key: ReportAttributesOrderDetail,
				Children: []ReportFields{
					{Key: ReportAttributesResearch},
					{Key: ReportAttributesResearchPackageNumber},
					{Key: ReportAttributesResearchPackageMethod},
					{Key: ReportAttributesResearchBatch},
					{Key: ReportAttributesResearchExpireDate},
					{Key: ReportAttributesResearchStatus},
					{Key: ReportAttributesResearchOther},
				}},
		},
	}

	RandomizationSimulationResult = Report{
		Name:         "menu.report.randomizationSimulationResult",
		Type:         ReportTypeRandomizationSimulationResult,
		Customizable: false,
		DefaultFields: []ReportFields{
			{Key: ReportAttributesProjectNumber},
			{Key: ReportAttributesProjectName},
			{Key: "report.simulate.random.name"},
			{Key: "simulated.random.list.runCount"},
			{Key: ReportAttributesInfoSiteName},
			{Key: ReportAttributesInfoSubjectNumber},
			{Key: "report.simulate.random.block"},
			{Key: "report.attributes.random.number"},
			{Key: "report.attributes.random.config.code"},
			{Key: "report.simulate.random.group"},
			{Key: "report.attributes.random.factor"},
			{Key: "projects.randomization.planNumber"},
			{Key: "report.attributes.random.g"},
			//{Key: "check.cohort"},
		},
	}

	SourceIPUploadHistoryExport = Report{
		Name: "menu.report.sourceIpUploadHistory",
		Type: ReportTypeProjectSourceIpUploadHistoryReport,
		MultiDefaultFields: []MultiDefaultFields{
			{
				Type: 1,
				DefaultFields: []ReportFields{
					{Key: "source.ip.upload.history.name"},
					{Key: "report.attributes.research.operator"},
					{Key: "report.attributes.research.time"},
					{Key: "source.ip.upload.history.rows"},
					{Key: "common.status"},
				},
			},
			{
				Type: 1,
				DefaultFields: []ReportFields{
					{Key: "report.attributes.research.medicine.name"},
					{Key: "report.attributes.research.medicine.serial-number"},
					{Key: "report.attributes.research.medicine.number"},
					{Key: "report.attributes.research.medicine.code"},
					{Key: "report.attributes.research.expireDate"},
					{Key: "report.attributes.research.batch"},
					{Key: "report.attributes.research.packageNumber"},
					{Key: "report.attributes.research.package.serialNumber"},
					{Key: "report.attributes.research.medicine.type"},
					{Key: "report.attributes.research.time"},
					{Key: "report.attributes.research.operator"},
					{Key: "report.user.role.assign.content"},
				},
			},
		},
	}

	RandomizationSimulationPDFExport = Report{
		Name: "menu.report.RandomizationSimulationPDFExport",
		Type: ReportTypeRandomizationSimulationReportPDF,
	}

	ProjectNotificationsConfigurationExport = Report{
		Name:         "menu.report.ProjectNotificationsConfigurationReportExport",
		Type:         ReportTypeProjectNotificationsConfigurationReport,
		Customizable: false,
		DefaultFields: []ReportFields{
			{Key: "export.notifications.configuration.report.type"},
			{Key: "export.notifications.configuration.report.role"},
			{Key: "export.notifications.configuration.report.content.configuration"},
			{Key: "export.notifications.configuration.report.scene"},
		},
	}

	Reports = []Report{
		RandomizeReport,
		DispenseReport,
		UnblindingReport,
		ShipmentOrdersReport,
		ReturnOrdersReport,
		SiteItemReport,
		DepotItemReport,
		SourceRandomizationListExport,
		SourceIPExport,
		SourceIPUploadHistoryExport,
		RandomizationSimulationResult,
		RandomizationSimulationPDFExport,
		ConfigureReport,
		AuditTrailExport,
		ProjectPermissionConfigurationExport,
		ProjectNotificationsConfigurationExport,
		UserRoleStatusReport,
		UserLoginHistory,
		UserRoleAssignHistory,
		SiteIPStatisticsExport,
		DepotIPStatisticsExport,
		RandomizationStatisticsExport,
		SubjectStatisticsExport,
		ForecastingPredictionReportReport,
		VisitForecastRecordReportReport,
		IPUnblindingReport,
		//RandomizationSimulationReport,
		//RoomNumberViewHistory,
	}

	OnlyDispensingReports = []Report{
		RandomizeReport,
		OnlyDispensingDispenseReport,
		ShipmentOrdersReport,
		ReturnOrdersReport,
		SiteItemReport,
		DepotItemReport,
		SourceIPExport,
		ConfigureReport,
		AuditTrailExport,
		ProjectPermissionConfigurationExport,
		ProjectNotificationsConfigurationExport,
		UserRoleStatusReport,
		UserLoginHistory,
		UserRoleAssignHistory,
		SiteIPStatisticsExport,
		DepotIPStatisticsExport,
		OnlyDispensingSubjectStatisticsExport,
		VisitForecastRecordReportReport,
		IPUnblindingReport,
	}

	ForecastingPredictionReportReport = Report{
		Name:         "menu.report.forecastingPrediction",
		Type:         ReportTypeForecastingPredictionReportReport,
		Customizable: false,
		DefaultFields: []ReportFields{
			{Key: ReportForecastDeopt},
			{Key: ReportAttributesInfoSiteNumber},
			{Key: ReportAttributesInfoSiteName},
			{Key: ReportAttributesInfoSubjectNumber},
			{Key: ReportAttributesDispensingCycleName},
			{Key: ReportForecastPeriod},
			{Key: ReportAttributesResearch},
			{Key: ReportAttributesOrderMedicineQuantity},
			{Key: "report.simulate.random.group"},
			{Key: ReportAttributesRandomCohort},
			{Key: ReportAttributesRandomStage},
		},
	}

	VisitForecastRecordReportReport = Report{
		Name:         "menu.report.visitForecast",
		Type:         ReportTypeVisitForecastRecordReportReport,
		Customizable: true,
		MultiDefaultFields: []MultiDefaultFields{
			{
				Type: 1,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesInfoSubjectNumber},
					{Key: ReportAttributesRandomNumber},
					{Key: ReportAttributesDispensingCycleName},
					{Key: ReportForecastPeriod},
					{Key: ReportAttributesDispensingTime},
					{Key: ReportVisitForecastVisitStatus},
				},
			},
			{
				Type: 2,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesRandomCohort},
					{Key: ReportAttributesInfoSubjectNumber},
					{Key: ReportAttributesRandomNumber},
					{Key: ReportAttributesDispensingCycleName},
					{Key: ReportForecastPeriod},
					{Key: ReportAttributesDispensingTime},
					{Key: ReportVisitForecastVisitStatus},
				},
			},
			{
				Type: 3,
				DefaultFields: []ReportFields{
					{Key: ReportAttributesProjectNumber},
					{Key: ReportAttributesProjectName},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesRandomStage},
					{Key: ReportAttributesInfoSubjectNumber},
					{Key: ReportAttributesRandomNumber},
					{Key: ReportAttributesDispensingCycleName},
					{Key: ReportForecastPeriod},
					{Key: ReportAttributesDispensingTime},
					{Key: ReportVisitForecastVisitStatus},
				},
			},
		},
		CustomFields: []ReportFields{
			{
				Key: ReportAttributesProjectNumber,
				//Required: true,
			},
			{
				Key: ReportAttributesProjectName,
				//Required: true,
			},
			{
				Key: ReportAttributesInfo,
				Children: []ReportFields{
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesInfoSubjectNumber},
					{Key: ReportAttributesRandomNumber},
					{Key: ReportAttributesRandomCohort, Required: true},
					{Key: ReportAttributesRandomStage, Required: true},
					{Key: ReportAttributesDispensingCycleName},
					{Key: ReportForecastPeriod},
					{Key: ReportAttributesDispensingTime},
					{Key: ReportVisitForecastVisitStatus},
					{Key: ReportVisitForecastNoticeContent},
					{Key: ReportVisitForecastNoticeTime},
					{Key: ReportVisitForecastNoticeUser},
					{Key: ReportVisitForecastNoticeType},
					{Key: ReportVisitForecastNoticeEmail},
				},
			},
		},
	}

	// 研究产品揭盲报表
	IPUnblindingReport = Report{
		Name:         "menu.report.IPUnblindingReport",
		Type:         ReportTypeIPUnblindingReport,
		Customizable: true,
		DefaultFields: []ReportFields{
			{Key: ReportAttributesProjectNumber},
			{Key: ReportAttributesProjectName},
			{Key: ReportAttributesInfoSiteNumber},
			{Key: ReportAttributesInfoSiteName},
			{Key: ReportAttributesInfoSubjectNumber},
			{Key: ReportAttributesDispensingCycleName},
			{Key: ReportAttributesResearchMedicineName},
			{Key: ReportAttributesResearchMedicineNumber},
			{Key: ReportAttributesUnblindingReason},
			{Key: ReportAttributesUnblindingOperator},
			{Key: ReportAttributesUnblindingTime},
		},
		CustomFields: []ReportFields{
			{
				Key: ReportAttributesProjectNumber,
				//Required: true,
			},
			{
				Key: ReportAttributesProjectName,
				//Required: true,
			},
			{
				Key: ReportAttributesInfo,
				Children: []ReportFields{
					{Key: ReportAttributesInfoCountry},
					{Key: ReportAttributesInfoRegion},
					{Key: ReportAttributesInfoSiteNumber},
					{Key: ReportAttributesInfoSiteName},
					{Key: ReportAttributesInfoSubjectNumber},
				},
			},
			{
				Key: ReportAttributesDispensing,
				Children: []ReportFields{
					{Key: ReportAttributesDispensingCycleName},
				},
			},
			{
				Key: ReportAttributesResearch,
				Children: []ReportFields{
					{Key: ReportAttributesResearchMedicineName},
					{Key: ReportAttributesResearchMedicineNumber},
				},
			},
			{
				Key: ReportAttributesUnblinding,
				Children: []ReportFields{
					{Key: ReportAttributesUnblindingReason},
					{Key: ReportAttributesUnblindingReasonMark},
					{Key: ReportAttributesUnblindingMark},
					{Key: ReportAttributesUnblindingOperator},
					{Key: ReportAttributesUnblindingTime},
					{Key: ReportAttributesUnblindingActualMedicineNumber},
				},
			},
		},
	}
)
