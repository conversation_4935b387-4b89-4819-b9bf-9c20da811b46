package test

import (
	"clinflash-irt/models"
	"github.com/duke-git/lancet/v2/slice"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"strings"
	"testing"
	"time"
)

type DispensingMedicine struct {
	models.DispensingMedicine `json:",inline" bson:",inline"`
	Remark                    string
}
type Dispensing struct {
	models.Dispensing      `json:",inline" bson:",inline"`
	CancelMedicinesHistory []DispensingMedicine `json:"cancelMedicinesHistory" bson:"cancel_medicines_history"` // 取回数据记录
	History                []models.History     `bson:"history"`
}

func updateDispensingHistory(t *testing.T) {

	//查询dispensing   type === 6
	//关联history
	//for type == 6
	//if 存在同个药物号 多条取回
	//循环轨迹
	//小于取回时间的最新的那一条发药记录
	//获取时间 update

	//OID, _ := primitive.ObjectIDFromHex("64eda074477fb66b94a3fb03")
	ProjectOID, _ := primitive.ObjectIDFromHex("64e83b08e0c5f7b23da1cb6c")

	var dispensings []Dispensing
	cursor, err := DB.Collection("dispensing").Aggregate(nil, mongo.Pipeline{
		{{"$match", bson.M{
			"env_id": ProjectOID,
			//"_id": OID,
		}}},
		{{"$match", bson.M{"cancel_medicines_history.type": bson.M{"$in": bson.A{6, 7}}}}},
		{{"$lookup", bson.M{
			"from":         "history",
			"localField":   "_id",
			"foreignField": "oid",
			"as":           "history",
		}}},
	})
	err = cursor.All(nil, &dispensings)
	if err != nil {
		panic(err)
	}
	for _, dispensing := range dispensings {
		for _, medicine := range dispensing.CancelMedicinesHistory {
			if medicine.Type == 6 || medicine.Type == 7 {
				dispensingTime, types := selectDispensingTime(dispensing.History, medicine)
				medicine.Remark = "4734"
				medicine.Time = dispensingTime
				medicine.Type = types
				//fmt.Println(dispensingTime)
				//fmt.Println(types)
				update := bson.M{"$push": bson.M{"cancel_medicines_history": medicine}}
				_, err = DB.Collection("dispensing").UpdateOne(nil, bson.M{"_id": dispensing.ID}, update)
				if err != nil {
					panic(err)
				}
			}
		}
	}
}

func selectDispensingTime(history []models.History, medicine DispensingMedicine) (time.Duration, int) {
	var dispensingTime time.Duration
	types := 0

	for _, m := range history {
		if medicine.Time <= m.Time {
			break
		}
		if strings.Contains(m.Key, "history.dispensing.register") {
			continue
		}

		if m.Data["medicine"] != nil {
			_, ok := slice.Find(m.Data["medicine"].(primitive.A), func(index int, item interface{}) bool {
				return item == medicine.Number
			})
			//types = 0
			if strings.Contains(m.Key, "history.dispensing.dispensing") || strings.Contains(m.Key, "history.dispensing.dtp-dispensing") {
				types = 1
			}
			if strings.Contains(m.Key, "history.dispensing.reissue") || strings.Contains(m.Key, "history.dispensing.dtp-reissue") {
				types = 2
			}
			if ok {
				dispensingTime = m.Time
			}
		}

	}
	return dispensingTime, types
}
