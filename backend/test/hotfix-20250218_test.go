package test

import (
	"clinflash-irt/models"
	"context"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"strconv"
	"strings"
	"testing"
)

//func HandleFactorCalcRandomDesign(t *testing.T) {
//	// 查询条件
//	filter := bson.M{"info.factors.is_calc": true}
//
//	// 查找所有符合条件的数据
//	cur, err := DB.Collection("random_design").Find(context.TODO(), filter)
//	if err != nil {
//		t.Fatalf("Failed to find documents: %v", err)
//	}
//	defer cur.Close(context.TODO())
//
//	// 遍历查询结果并更新数据
//	for cur.Next(context.TODO()) {
//		var randomDesign models.RandomDesign
//		err := cur.Decode(&randomDesign)
//		if err != nil {
//			t.Errorf("Failed to decode document: %v", err)
//			continue
//		}
//
//		for i, factor := range randomDesign.Info.Factors {
//			if factor.IsCalc {
//				fmt.Println(factor.ID.Hex())
//				var customFormulas string
//				if factor.CalcType != nil && *factor.CalcType == 0 {
//					if factor.InputLabel == nil || *factor.InputLabel == "" {
//						continue
//					}
//					customFormulas = fmt.Sprintf("({CurrentTime}-{%s})/365.25", *factor.InputLabel)
//				} else if factor.CalcType != nil && *factor.CalcType == 1 {
//					if factor.InputWeightLabel == nil || *factor.InputWeightLabel == "" || factor.InputHeightLabel == nil || *factor.InputHeightLabel == "" {
//						continue
//					}
//					customFormulas = fmt.Sprintf("{%s}/({%s}/100)^2", *factor.InputWeightLabel, *factor.InputHeightLabel)
//				}
//
//				// 更新customFormulas字段
//				randomDesign.Info.Factors[i].CustomFormulas = customFormulas
//
//				// 如果precision为0，更新round字段
//				if factor.Precision == nil || *factor.Precision == 0 {
//					randomDesign.Info.Factors[i].Round = 1
//					z := 0
//					randomDesign.Info.Factors[i].Precision = &z
//				}
//				opts := &options.UpdateOptions{
//					ArrayFilters: &options.ArrayFilters{
//						Filters: bson.A{
//							bson.M{"id.id": factor.ID},
//						},
//					},
//				}
//
//				update := bson.M{
//					"$set": bson.M{
//						"info.factors.$[id].custom_formulas": customFormulas,
//						"info.factors.$[id].round":           randomDesign.Info.Factors[i].Round,
//						"info.factors.$[id].precision":       randomDesign.Info.Factors[i].Precision,
//					},
//				}
//
//				_, err = DB.Collection("random_design").UpdateOne(context.TODO(), bson.M{"_id": randomDesign.ID}, update, opts)
//				if err != nil {
//					t.Fatalf("Failed to update document: %v", err)
//				}
//			}
//		}
//
//	}
//
//	if err := cur.Err(); err != nil {
//		t.Fatalf("Cursor error: %v", err)
//	}
//}
//func HandleFactorCalcRandomList(t *testing.T) {
//	// 查询条件
//	filter := bson.M{"design.factors.is_calc": true}
//
//	// 查找所有符合条件的数据
//	cur, err := DB.Collection("random_list").Find(context.TODO(), filter)
//	if err != nil {
//		t.Fatalf("Failed to find documents: %v", err)
//	}
//	defer cur.Close(context.TODO())
//
//	// 遍历查询结果并更新数据
//	for cur.Next(context.TODO()) {
//		var randomList models.RandomList
//		err := cur.Decode(&randomList)
//		if err != nil {
//			t.Errorf("Failed to decode document: %v", err)
//			continue
//		}
//
//		for i, factor := range randomList.Design.Factors {
//			if factor.IsCalc {
//				fmt.Println(factor.ID.Hex())
//				var customFormulas string
//				if factor.CalcType != nil && *factor.CalcType == 0 {
//					if factor.InputLabel == nil || *factor.InputLabel == "" {
//						continue
//					}
//					customFormulas = fmt.Sprintf("({CurrentTime}-{%s})/365.25", *factor.InputLabel)
//				} else if factor.CalcType != nil && *factor.CalcType == 1 {
//					if factor.InputWeightLabel == nil || *factor.InputWeightLabel == "" || factor.InputHeightLabel == nil || *factor.InputHeightLabel == "" {
//						continue
//					}
//					customFormulas = fmt.Sprintf("{%s}/({%s}/100)^2", *factor.InputWeightLabel, *factor.InputHeightLabel)
//				}
//
//				// 更新customFormulas字段
//				randomList.Design.Factors[i].CustomFormulas = customFormulas
//
//				// 如果precision为0，更新round字段
//				if factor.Precision == nil || *factor.Precision == 0 {
//					randomList.Design.Factors[i].Round = 1
//					z := 0
//					randomList.Design.Factors[i].Precision = &z
//				}
//				opts := &options.UpdateOptions{
//					ArrayFilters: &options.ArrayFilters{
//						Filters: bson.A{
//							bson.M{"id.id": factor.ID},
//						},
//					},
//				}
//
//				update := bson.M{
//					"$set": bson.M{
//						"design.factors.$[id].custom_formulas": customFormulas,
//						"design.factors.$[id].round":           randomList.Design.Factors[i].Round,
//						"design.factors.$[id].precision":       randomList.Design.Factors[i].Precision,
//					},
//				}
//
//				_, err = DB.Collection("random_list").UpdateOne(context.TODO(), bson.M{"_id": randomList.ID}, update, opts)
//				if err != nil {
//					t.Fatalf("Failed to update document: %v", err)
//				}
//			}
//		}
//
//	}
//
//	if err := cur.Err(); err != nil {
//		t.Fatalf("Cursor error: %v", err)
//	}
//}

func HandleFormRegister(t *testing.T) {
	// 查询条件

	// 查找所有符合条件的数据
	cur, err := DB.Collection("form").Find(context.TODO(), bson.M{})
	if err != nil {
		t.Fatalf("Failed to find documents: %v", err)
	}
	defer cur.Close(context.TODO())

	// 遍历查询结果并更新数据
	for cur.Next(context.TODO()) {
		var form models.Form
		err := cur.Decode(&form)
		if err != nil {
			t.Errorf("Failed to decode document: %v", err)
			continue
		}

		for _, field := range form.Fields {
			if (field.ApplicationType == nil || *field.ApplicationType == 1) && field.Variable == "" {
				fmt.Println(field.ID.Hex())

				opts := &options.UpdateOptions{
					ArrayFilters: &options.ArrayFilters{
						Filters: bson.A{
							bson.M{"id.id": field.ID},
						},
					},
				}

				update := bson.M{
					"$set": bson.M{
						"fields.$[id].variable": field.Label,
					},
				}

				_, err = DB.Collection("form").UpdateOne(context.TODO(), bson.M{"_id": form.ID}, update, opts)
				if err != nil {
					t.Fatalf("Failed to update document: %v", err)
				}
			}
		}

	}

	if err := cur.Err(); err != nil {
		t.Fatalf("Cursor error: %v", err)
	}
}

//func HandleFormSubject(t *testing.T) {
//	randomDesigns := make([]models.RandomDesign, 0)
//	filter := bson.M{"info.factors.is_calc": true}
//	cur, err := DB.Collection("random_design").Find(context.TODO(), filter)
//	if err != nil {
//		panic(err)
//	}
//	err = cur.All(nil, &randomDesigns)
//	if err != nil {
//		panic(err)
//	}
//	for _, design := range randomDesigns {
//		filterM := bson.M{"env_id": design.EnvironmentID}
//		if !design.CohortID.IsZero() {
//			filterM["cohort_id"] = design.CohortID
//		}
//
//		for _, factor := range design.Info.Factors {
//			if factor.IsCalc {
//				if factor.CalcType != nil && *factor.CalcType == 0 && factor.InputLabel != nil {
//					pushField(design, factor, filterM, *factor.InputLabel, factor.Type, "Birthday")
//				} else if factor.CalcType != nil && *factor.CalcType == 1 && factor.InputHeightLabel != nil && factor.InputWeightLabel != nil {
//					pushField(design, factor, filterM, *factor.InputWeightLabel, "inputNumber", "Weight")
//					pushField(design, factor, filterM, *factor.InputHeightLabel, "inputNumber", "Height")
//				}
//			}
//		}
//
//	}
//}

func pushField(design models.RandomDesign, factor models.RandomFactor, filterM bson.M, variable string, t string, sign string) {
	fieldNumber := 0
	var form models.Form
	err := DB.Collection("form").FindOne(context.TODO(), filterM).Decode(&form)
	if err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}
	subjects := make([]models.Subject, 0)
	cur, err := DB.Collection("subject").Find(context.TODO(), filterM)
	if err != nil {
		panic(err)
	}
	err = cur.All(nil, &subjects)
	if err != nil {
		panic(err)
	}
	applicationType := 4
	status := 1
	//判断form是否存在，如果已存在，push field
	dateFormat := ""
	if factor.DateFormat != nil {
		dateFormat = *factor.DateFormat
	}
	find, b := slice.Find(form.Fields, func(index int, item models.Field) bool {
		return item.Label == variable
	})
	fieldName := ""
	if !b {
		field := models.Field{}
		if factor.CalcType != nil && *factor.CalcType == 0 {
			field = models.Field{
				ID:              primitive.NewObjectID(),
				CohortId:        design.CohortID,
				Name:            "",
				Label:           variable,
				Type:            t,
				Status:          &status,
				ApplicationType: &applicationType,
				Variable:        variable,
				Required:        true,
				DateFormat:      dateFormat,
				Modifiable:      true,
			}
		} else if factor.CalcType != nil && *factor.CalcType == 1 {
			l := float64(3)
			field = models.Field{
				ID:              primitive.NewObjectID(),
				CohortId:        design.CohortID,
				Name:            "",
				Label:           variable,
				Type:            t,
				Status:          &status,
				ApplicationType: &applicationType,
				Variable:        variable,
				Required:        true,
				FormatType:      "numberLength",
				Length:          &l,
				Modifiable:      true,
			}
		}

		field.Name = "field0"
		if !form.ID.IsZero() {
			update := bson.M{}
			if len(form.Fields) == 0 {
				update = bson.M{
					"$set": bson.M{
						"fields": []models.Field{field},
					},
				}
			} else {
				numberStr := strings.ReplaceAll(form.Fields[len(form.Fields)-1].Name, "field", "")
				atoi, err := strconv.Atoi(numberStr)
				if err != nil {
					panic(err)
				}
				fieldNumber = atoi + 1
				field.Name = fmt.Sprintf("field%d", fieldNumber)
				update = bson.M{
					"$push": bson.M{
						"fields": field,
					},
				}
			}
			_, err = DB.Collection("form").UpdateOne(nil, bson.M{"_id": form.ID}, update)
			if err != nil {
				panic(err)
			}
		} else {
			form := models.Form{
				ID:            primitive.NewObjectID(),
				CustomerID:    design.CustomerID,
				ProjectID:     design.ProjectID,
				EnvironmentID: design.EnvironmentID,
				CohortID:      design.CohortID,
				Fields:        []models.Field{field},
			}
			if _, err := DB.Collection("form").InsertOne(nil, form); err != nil {
				panic(err)
			}
		}
		fieldName = field.Name
	} else {
		fieldName = find.Name
	}

	for _, subject := range subjects {
		names := slice.Map(subject.Info, func(index int, item models.Info) string {
			return item.Name
		})
		for _, info := range subject.Info {
			sprintf := fmt.Sprintf("%s%s", factor.Name, sign)
			if info.Name == sprintf && !slice.Contain(names, fieldName) {
				update := bson.M{
					"$push": bson.M{
						"info": models.Info{
							Name:  fieldName,
							Value: info.Value,
						},
					},
				}
				_, err = DB.Collection("subject").UpdateOne(nil, bson.M{"_id": subject.ID}, update)
				if err != nil {
					panic(err)
				}
				break
			}
		}
		actualNames := slice.Map(subject.ActualInfo, func(index int, item models.Info) string {
			return item.Name
		})
		for _, info := range subject.ActualInfo {
			sprintf := fmt.Sprintf("%s%s", factor.Name, sign)
			if info.Name == sprintf && !slice.Contain(actualNames, fieldName) {
				update := bson.M{
					"$push": bson.M{
						"actual_info": models.Info{
							Name:  fieldName,
							Value: info.Value,
						},
					},
				}
				_, err = DB.Collection("subject").UpdateOne(nil, bson.M{"_id": subject.ID}, update)
				if err != nil {
					panic(err)
				}
				break
			}
		}
	}

}
