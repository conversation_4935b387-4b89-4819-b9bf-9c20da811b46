package test

import (
	"clinflash-irt/models"
	"github.com/duke-git/lancet/v2/maputil"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"testing"
)

func initStorehouseMedicineInfo(t *testing.T) {
	storehouses := make([]models.ProjectStorehouse, 0)
	cursor, err := DB.Collection("project_storehouse").Find(nil, bson.M{})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &storehouses)
	if err != nil {
		panic(err)
	}
	for _, storehouse := range storehouses {
		setting := models.ProjectDepotSetting{}
		err := DB.Collection("project_storehouse_validity").FindOne(nil, bson.M{"env_id": storehouse.EnvironmentID}).Decode(&setting)
		if err != nil && err != mongo.ErrNoDocuments {
			panic(err)
		}
		infoMap := make(map[string]*models.MedicineInfo, 0)
		if !setting.ID.IsZero() && setting.Medicines != nil && len(setting.Medicines) > 0 {
			for _, medicine := range setting.Medicines {
				if medicine.ValidityReminder > 0 {
					_, ok := infoMap[medicine.MedicineName]
					if ok {
						infoMap[medicine.MedicineName].ValidityReminder = medicine.ValidityReminder
					} else {
						infoMap[medicine.MedicineName] = &models.MedicineInfo{
							MedicineName:     medicine.MedicineName,
							ValidityReminder: medicine.ValidityReminder,
							Alert:            0,
						}
					}
				}
			}
		}
		if storehouse.Alert != nil && len(storehouse.Alert) > 0 {
			for _, alert := range storehouse.Alert {
				if alert.Value > 0 {
					_, ok := infoMap[alert.MedicineName]
					if ok {
						infoMap[alert.MedicineName].Alert = alert.Value
					} else {
						infoMap[alert.MedicineName] = &models.MedicineInfo{
							MedicineName:     alert.MedicineName,
							ValidityReminder: 0,
							Alert:            alert.Value,
						}
					}
				}
			}
		}
		infos := maputil.Values(infoMap)
		_, err = DB.Collection("project_storehouse").UpdateOne(nil, bson.M{"_id": storehouse.ID}, bson.M{"$set": bson.M{"medicine_infos": infos}})
		if err != nil {
			panic(err)
		}
	}
}
