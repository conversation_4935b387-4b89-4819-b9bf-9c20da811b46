package test

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"testing"
)

// DeleteDrugConfigureSetting -- 删除drug_configure_setting的customer_id和project_id为空的
func DeleteDrugConfigureSetting(t *testing.T) {
	_, err := DB.Collection("drug_configure_setting").DeleteMany(nil, bson.M{"customer_id": primitive.NilObjectID, "project_id": primitive.NilObjectID})
	if err != nil {
		panic(err)
	}
}
