package test

import (
	"clinflash-irt/models"
	"fmt"
	"testing"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// 未编号数据清洗 和 未编号轨迹表数据
func InsertHistory(t *testing.T) {
	var medicineFreeze models.MedicineFreeze
	_ = DB.Collection("medicine_freeze").FindOne(nil, bson.M{"number": "16898"}).Decode(&medicineFreeze)

	haveMedicines := []map[string]interface{}{}
	match := bson.M{"customer_id": medicineFreeze.CustomerID, "project_id": medicineFreeze.ProjectID, "env_id": medicineFreeze.EnvironmentID, "_id": bson.M{"$in": medicineFreeze.History}}
	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$project", Value: bson.M{
			"_id":    0,
			"id":     "$_id",
			"name":   "$name",
			"number": "$number",
		}}},
	}
	cursor, err := DB.Collection("medicine").Aggregate(nil, pipepine)
	if err != nil {
		fmt.Sprintln(err)
	}
	err = cursor.All(nil, &haveMedicines)
	if err != nil {
		fmt.Sprintln(err)
	}

	var histories []interface{}
	UID, _ := primitive.ObjectIDFromHex("60c058791f287c54a3280545")
	for _, medicine := range haveMedicines {
		var history models.History
		history.Key = "history.medicine.freeze-package-new"
		history.OID = medicine["id"].(primitive.ObjectID)
		data := make(map[string]interface{})
		data["packNumber"] = medicine["number"].(string)
		data["packageNumber"] = "-"
		data["reason"] = "超温"
		data["freezeNumber"] = "16898"
		history.Data = data
		history.Time = time.Duration(1740619447)
		history.UID = UID
		history.User = "王恩芳"
		histories = append(histories, history)
	}
	DB.Collection("history").InsertMany(nil, histories)
}
