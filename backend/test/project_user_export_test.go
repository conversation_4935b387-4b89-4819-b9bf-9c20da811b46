package test

import (
	"clinflash-irt/data"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"strings"
	"testing"
	"time"
)

func ExportProjectUserRole(T *testing.T) {
	type ContentStruct struct {
		Number     string
		Env        string
		User       primitive.A
		CreatedAt  time.Duration `json:"createdAt" bson:"created_at"`
		Status     int
		Url        string
		CustomerID primitive.ObjectID `bson:"customer_id"`
	}

	var projects []ContentStruct
	cursor, err := DB.Collection("project").Aggregate(nil, mongo.Pipeline{
		//{{"$match", bson.M{"info.number": bson.M{"$in": findProjects}}}},
		{{"$unwind", "$envs"}},

		{{Key: "$lookup", Value: bson.M{
			"from": "project_role_permission",
			"let": bson.M{
				"project_id": "$_id",
			},
			"pipeline": bson.A{
				bson.M{
					"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$project_id", "$$project_id"}}},
				},
				bson.M{"$match": bson.M{"name": bson.M{"$in": bson.A{"IP Officer", "Biostatistician [unblinded]", "IRT Designer"}}}},
			},
			"as": "project_role_permission",
		}}},
		{{"$unwind", "$project_role_permission"}},
		{{Key: "$lookup", Value: bson.M{
			"from": "user_project_environment",
			"let": bson.M{
				"env_id":  "$envs.id",
				"role_id": "$project_role_permission._id",
			},
			"pipeline": bson.A{
				bson.M{
					"$match": bson.M{"$and": bson.A{
						bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$$env_id"}}},
						bson.M{"$expr": bson.M{"$in": bson.A{"$$role_id", "$roles"}}},
					}},
				},
			},
			"as": "user_project_environment",
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "user",
			"localField":   "user_project_environment.user_id",
			"foreignField": "_id",
			"as":           "user",
		}}},
		{{Key: "$project", Value: bson.M{
			"number":      "$info.number",
			"env":         "$envs.name",
			"user":        "$user.info.email",
			"created_at":  "$meta.created_at",
			"status":      "$status",
			"url":         "$info.edc_url",
			"customer_id": "$customer_id",
			"_id":         0,
		}}},
	})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &projects)
	if err != nil {
		return
	}
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	content := make([][]interface{}, 0)
	type Customer struct {
		Id   primitive.ObjectID `json:"id" bson:"_id"`
		Name string             `json:"name" bson:"name"`
	}
	allCustomers := make([]Customer, 0)
	cursor, err = DB2.Collection("customer").Find(nil, bson.M{})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &allCustomers)
	customerNames := make(map[primitive.ObjectID]string)
	for _, customer := range allCustomers {
		customerNames[customer.Id] = customer.Name
	}
	for _, item := range projects {
		tmp := []interface{}{}
		tmp = append(tmp, item.Number)
		tmp = append(tmp, item.Env)
		timestamp := int64(item.CreatedAt)

		// 将时间戳转换为 time.Time 类型
		t := time.Unix(timestamp, 0)

		// 将时间转换为东八区（UTC+8）
		loc, _ := time.LoadLocation("Asia/Shanghai")
		tInBeijing := t.In(loc)

		// 格式化为 YYYY-MM-DD HH:mm:ss
		formattedTime := tInBeijing.Format("2006-01-02 15:04:05")
		tmp = append(tmp, formattedTime)
		status := ""
		//0:进行中,1:已完成,2:已关闭,3:已暂停,4:已终止
		switch item.Status {
		case 0:
			status = "进行中"
		case 1:
			status = "已完成"
		case 2:
			status = "已关闭"
		case 3:
			status = "已暂停"
		case 4:
			status = "已终止"
		}
		tmp = append(tmp, status)
		tmp = append(tmp, item.User)
		tmp = append(tmp, item.Url)
		customerName := customerNames[item.CustomerID]
		tmp = append(tmp, customerName)
		content = append(content, tmp)
	}
	title := []interface{}{"项目编号", "项目环境", "创建时间", "项目状态", "负责人", "EDC Url", "节点"}
	tools.ExportSheet(f, "Sheet1", title, content)
	f.SaveAs("user.xlsx")
	f.Close()
}

func ExportProjectUserBlindRole(T *testing.T) {
	findProjects := bson.A{
		"A20-202",
		"ZGJAK020",
		"2021P0004",
		"HR19042-202",
		"KYZY-SHHS-COVID-19-2301",
		"3D-197-CN-001",
		"BN102-101-Ⅰ期",
		"BN301-101",
		"ANG601-1001",
		"QY201-Ⅰ-2",
		"SHRC-CX2101-01",
		"DEMO_002群组",
	}
	type ContentStruct struct {
		Number string
		Env    string
		User   primitive.A
		Role   []string
	}

	var projects []ContentStruct
	cursor, err := DB.Collection("project").Aggregate(nil, mongo.Pipeline{
		{{"$match", bson.M{"info.number": bson.M{"$in": findProjects}}}},
		{{"$unwind", "$envs"}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "user_project_environment",
			"localField":   "envs.id",
			"foreignField": "env_id",
			"as":           "user_project_environment",
		}}},
		{{"$unwind", "$user_project_environment"}},

		{{Key: "$lookup", Value: bson.M{
			"from":         "project_role_permission",
			"localField":   "user_project_environment.roles",
			"foreignField": "_id",
			"as":           "project_role_permission",
		}}},

		{{Key: "$lookup", Value: bson.M{
			"from":         "user",
			"localField":   "user_project_environment.user_id",
			"foreignField": "_id",
			"as":           "user",
		}}},
		{{Key: "$project", Value: bson.M{
			"number": "$info.number",
			"env":    "$envs.name",
			"user":   "$user.info.email",
			"_id":    0,
			"role":   "$project_role_permission.name",
		}}},
	})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &projects)
	if err != nil {
		return
	}
	//fmt.Println(projects)
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	content := make([][]interface{}, 0)
	for _, item := range projects {
		tmp := []interface{}{}
		tmp = append(tmp, item.Number)
		tmp = append(tmp, item.Env)
		tmp = append(tmp, item.User)
		tmp = append(tmp, strings.Join(item.Role, ","))

		_, ok := slice.Find(item.Role, func(index int, name string) bool {
			roleP, _ := slice.Find(data.RolePools, func(index int, role models.RolePool) bool {
				return role.Name == name
			})
			role := *roleP
			return role.Type == 2 || role.Type == 3

		})

		if ok {
			tmp = append(tmp, "是")

		}

		content = append(content, tmp)
		fmt.Println(item)

	}
	title := []interface{}{"项目编号", "项目环境", "用户", "角色", "存在盲态角色"}
	tools.ExportSheet(f, "Sheet1", title, content)
	f.SaveAs("user_role.xlsx")
	f.Close()
}

func ExportProjectUserRoleEmail(T *testing.T) {
	//findProjects := bson.A{
	//	"A20-202",
	//	"ZGJAK020",
	//	"2021P0004",
	//	"HR19042-202",
	//	"KYZY-SHHS-COVID-19-2301",
	//	"3D-197-CN-001",
	//	"BN102-101-Ⅰ期",
	//	"BN301-101",
	//	"ANG601-1001",
	//	"QY201-Ⅰ-2",
	//	"SHRC-CX2101-01",
	//	"DEMO_002群组",
	//}
	type ContentStruct struct {
		Number string
		Env    string
		User   primitive.A
	}

	var projects []ContentStruct
	cursor, err := DB.Collection("project").Aggregate(nil, mongo.Pipeline{
		//{{"$match", bson.M{"info.number": bson.M{}}}},
		{{"$unwind", "$envs"}},

		{{Key: "$lookup", Value: bson.M{
			"from": "project_role_permission",
			"let": bson.M{
				"project_id": "$_id",
			},
			"pipeline": bson.A{
				bson.M{
					"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$project_id", "$$project_id"}}},
				},
				bson.M{"$match": bson.M{"name": "IP Officer"}},
			},
			"as": "project_role_permission",
		}}},
		{{"$unwind", "$project_role_permission"}},
		{{Key: "$lookup", Value: bson.M{
			"from": "user_project_environment",
			"let": bson.M{
				"env_id":  "$envs.id",
				"role_id": "$project_role_permission._id",
			},
			"pipeline": bson.A{
				bson.M{
					"$match": bson.M{"$and": bson.A{
						bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$$env_id"}}},
						bson.M{"$expr": bson.M{"$in": bson.A{"$$role_id", "$roles"}}},
					}},
				},
			},
			"as": "user_project_environment",
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "user",
			"localField":   "user_project_environment.user_id",
			"foreignField": "_id",
			"as":           "user",
		}}},
		{{Key: "$project", Value: bson.M{
			"number": "$info.number",
			"env":    "$envs.name",
			"user":   "$user.info.email",
			"_id":    0,
			//"user_project_environment": "$user_project_environment",
		}}},
	})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &projects)
	if err != nil {
		return
	}
	fmt.Println(projects)
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	content := make([][]interface{}, 0)
	for _, item := range projects {
		tmp := []interface{}{}
		tmp = append(tmp, item.Number)
		tmp = append(tmp, item.Env)
		tmp = append(tmp, item.User)
		content = append(content, tmp)
	}
	title := []interface{}{"项目编号", "项目环境", "IP Officer"}
	tools.ExportSheet(f, "Sheet1", title, content)
	f.SaveAs("user.xlsx")
	f.Close()
}

func ExportProject(T *testing.T) {
	type ContentStruct struct {
		Number     string
		Name       string
		Sponsor    string             `bson:"sponsor"`
		CreatedAt  time.Duration      `json:"createdAt" bson:"created_at"`
		CustomerID primitive.ObjectID `bson:"customer_id"`
	}

	var projects []ContentStruct
	c1, _ := primitive.ObjectIDFromHex("61badeb75967cf59bbd52c9e")
	c2, _ := primitive.ObjectIDFromHex("65263b0cf2da6845f39b6223")
	customerIds := make([]primitive.ObjectID, 0)
	customerIds = append(customerIds, c1, c2)
	cursor, err := DB.Collection("project").Aggregate(nil, mongo.Pipeline{
		{{"$match", bson.M{"customer_id": bson.M{"$in": customerIds}}}},
		{{Key: "$project", Value: bson.M{
			"number":     "$info.number",
			"name":       "$info.name",
			"created_at": "$meta.created_at",
			"sponsor":    "$info.sponsor",
			"_id":        0,
		}}},
	})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &projects)
	if err != nil {
		return
	}
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	content := make([][]interface{}, 0)
	c := 1
	for _, item := range projects {
		tmp := []interface{}{}
		tmp = append(tmp, c)
		c++
		tmp = append(tmp, "")
		tmp = append(tmp, item.Number)
		tmp = append(tmp, item.Name)
		tmp = append(tmp, item.Sponsor)
		timestamp := int64(item.CreatedAt)

		// 将时间戳转换为 time.Time 类型
		t := time.Unix(timestamp, 0)

		// 将时间转换为东八区（UTC+8）
		loc, _ := time.LoadLocation("Asia/Shanghai")
		tInBeijing := t.In(loc)

		// 格式化为 YYYY-MM-DD HH:mm:ss
		formattedTime := tInBeijing.Format("2006-01-02 15:04:05")
		tmp = append(tmp, "")
		tmp = append(tmp, formattedTime)
		content = append(content, tmp)
	}
	title := []interface{}{"序号", "项目编号", "方案号", "项目名称", "申办方", "IRT是否已签", "创建时间"}
	tools.ExportSheet(f, "Sheet1", title, content)
	f.SaveAs("user.xlsx")
	f.Close()
}
