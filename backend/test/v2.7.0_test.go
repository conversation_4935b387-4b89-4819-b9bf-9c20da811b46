package test

import (
	"clinflash-irt/models"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/duke-git/lancet/v2/slice"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type History struct {
	ID      primitive.ObjectID     `json:"id" bson:"_id"`
	Key     string                 `json:"key" bson:"key"`
	OID     primitive.ObjectID     `json:"oid" bson:"oid"`
	Data    map[string]interface{} `json:"data" bson:"data"`
	Time    time.Duration          `json:"time" bson:"time"`
	UID     primitive.ObjectID     `json:"uid" bson:"uid"`
	User    string                 `json:"user" bson:"user"`
	Unicode int32                  `json:"unicode" bson:"unicode"`
}

func syncUserNumber(t *testing.T) {
	users := make([]models.User, 0)
	cursor, err := DB.Collection("user").Find(nil, bson.M{}, &options.FindOptions{Projection: bson.M{
		"_id":      1,
		"cloud_id": 1,
	}})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &users)
	if err != nil {
		panic(err)
	}
	cloudIds := slice.Map(users, func(index int, item models.User) primitive.ObjectID {
		return item.CloudId
	})
	type CloudUser struct {
		ID     primitive.ObjectID `json:"id" bson:"_id"`
		Number int32              `bson:"number"`
		Info   struct {
			Name string `bson:"name"`
		} `bson:"info"`
	}
	cloudUsers := make([]CloudUser, 0)
	cursor, err = DB2.Collection("user").Find(nil, bson.M{"_id": bson.M{"$in": cloudIds}})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &cloudUsers)
	if err != nil {
		panic(err)
	}
	cloudUserMap := make(map[primitive.ObjectID]CloudUser)
	for _, cloudUser := range cloudUsers {
		cloudUserMap[cloudUser.ID] = cloudUser
	}
	var wg sync.WaitGroup
	for _, user := range users {
		cloudUser := cloudUserMap[user.CloudId]
		if !cloudUser.ID.IsZero() {
			go func(u models.User, c CloudUser) {
				defer wg.Done()
				wg.Add(1)
				fmt.Println(c.Info.Name)
				_, err := DB.Collection("user").UpdateOne(nil, bson.M{"_id": u.ID}, bson.M{"$set": bson.M{"info.unicode": c.Number, "info.name": c.Info.Name}})
				if err != nil {
					panic(err)
				}
			}(user, cloudUser)
		}
	}
	wg.Wait()
	fmt.Println("all done")
}

func syncHistoryNumber(t *testing.T) {
	histories := make([]History, 0)
	cursor, err := DB.Collection("history").Find(nil, bson.M{}, &options.FindOptions{Projection: bson.M{
		"_id": 1,
		"uid": 1,
	}})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &histories)
	if err != nil {
		panic(err)
	}
	userIds := slice.Map(histories, func(index int, item History) primitive.ObjectID {
		return item.UID
	})
	userIds = slice.Filter(userIds, func(index int, item primitive.ObjectID) bool {
		return !item.IsZero()
	})
	userIds = slice.Unique(userIds)
	users := make([]models.User, 0)
	cursor, err = DB.Collection("user").Find(nil, bson.M{}, &options.FindOptions{Projection: bson.M{
		"_id":          1,
		"info.unicode": 1,
	}})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &users)
	if err != nil {
		panic(err)
	}
	userMap := make(map[primitive.ObjectID]models.User)
	for _, user := range users {
		userMap[user.ID] = user
	}
	var wg sync.WaitGroup
	for _, userId := range userIds {
		user := userMap[userId]
		if !user.ID.IsZero() {
			go func(u models.User) {
				defer wg.Done()
				wg.Add(1)
				fmt.Println(u.Unicode)
				_, err := DB.Collection("history").UpdateMany(nil, bson.M{"uid": u.ID}, bson.M{"$set": bson.M{"unicode": u.Unicode}})
				if err != nil {
					panic(err)
				}
			}(user)
		}
	}
	wg.Wait()
	fmt.Println("all done")
}

func fixHistoryUserName(t *testing.T) {
	histories := make([]History, 0)
	cursor, err := DB.Collection("history").Find(nil, bson.M{"user": "", "uid": bson.M{"$ne": primitive.NilObjectID}}, &options.FindOptions{Projection: bson.M{
		"_id": 1,
		"uid": 1,
	}})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &histories)
	if err != nil {
		panic(err)
	}
	userIds := slice.Map(histories, func(index int, item History) primitive.ObjectID {
		return item.UID
	})
	userIds = slice.Filter(userIds, func(index int, item primitive.ObjectID) bool {
		return !item.IsZero()
	})
	userIds = slice.Unique(userIds)
	users := make([]models.User, 0)
	cursor, err = DB.Collection("user").Find(nil, bson.M{}, &options.FindOptions{Projection: bson.M{
		"_id":       1,
		"info.name": 1,
	}})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &users)
	if err != nil {
		panic(err)
	}
	userMap := make(map[primitive.ObjectID]models.User)
	for _, user := range users {
		userMap[user.ID] = user
	}
	var wg sync.WaitGroup
	for _, history := range histories {
		user := userMap[history.UID]
		go func(u models.User, h History) {
			defer wg.Done()
			wg.Add(1)
			_, err := DB.Collection("history").UpdateOne(nil, bson.M{"_id": h.ID}, bson.M{"$set": bson.M{"user": u.Name}})
			if err != nil {
				panic(err)
			}
		}(user, history)
	}
	wg.Wait()
	fmt.Println("all done")
}

func UpdateOpenSetting(t *testing.T) {
	var drugConfigures []map[string]interface{}
	cursor, err := DB.Collection("drug_configure").Find(nil, bson.M{})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &drugConfigures)
	if err != nil {
		panic(err)
	}

	for _, configure := range drugConfigures {
		for i, info := range configure["configures"].(primitive.A) {
			infoMap := info.(map[string]interface{})
			if infoMap["open_setting"] != nil && infoMap["open_setting"].(bool) {
				configure["configures"].(primitive.A)[i].(map[string]interface{})["open_setting"] = 2
			} else {
				configure["configures"].(primitive.A)[i].(map[string]interface{})["open_setting"] = 1
			}
		}
		one, err := DB.Collection("drug_configure").UpdateOne(nil,
			bson.M{"_id": configure["_id"]},
			bson.M{"$set": bson.M{"configures": configure["configures"]}},
		)
		fmt.Println(one.UpsertedCount)
		if err != nil {
			panic(err)
		}
	}
}

func UpdateVisitOperation(t *testing.T) {
	var role []models.ProjectRolePermission
	cursor, err := DB.Collection("project_role_permission").Find(nil, bson.M{"name": "IP Officer"})
	if err != nil {
		panic(err)
	}
	cursor.All(nil, &role)
	if err != nil {
		panic(err)
	}
	for _, permission := range role {
		_, err = DB.Collection("project_role_permission").UpdateOne(nil, bson.M{"_id": permission.ID}, bson.M{"$addToSet": bson.M{"permissions": "operation.build.medicine.visit.push.record"}})
		if err != nil {
			panic(err)
		}
		_, err = DB.Collection("project_role_permission").UpdateOne(nil, bson.M{"_id": permission.ID}, bson.M{"$addToSet": bson.M{"permissions": "operation.build.medicine.batch.update"}})
		if err != nil {
			panic(err)
		}
	}
}
