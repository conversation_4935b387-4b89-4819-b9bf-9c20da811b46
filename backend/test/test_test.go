package test

import (
	"context"
	"fmt"
	"os"
	"testing"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	DB  *mongo.Database
	DB2 *mongo.Database
)

func init() {
	opts := options.Client()
	opts.ApplyURI("mongodb://app:<EMAIL>:3717,dds-bp17703931a951942228-pub.mongodb.rds.aliyuncs.com:3717/?replicaSet=mgset-59755094")
	//生产只读-一般导出数据用，正常情况不要用这个库
	//opts2 := options.Client()
	//opts2.ApplyURI("mongodb://read-any:<EMAIL>:3717,dds-bp16257159dd8a742502-pub.mongodb.rds.aliyuncs.com:3717")

	client, err := mongo.Connect(context.Background(), opts)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to connect to database: %v\n", err)
		os.Exit(1)
	}

	//client2, err := mongo.Connect(context.Background(), opts2)
	//
	//if err != nil {
	//	fmt.Fprintf(os.Stderr, "Unable to connect to database: %v\n", err)
	//	os.Exit(1)
	//}
	//DB2 = client2.Database("clinflash-dmp")

	DB = client.Database("clinflash-irt-dev")
	//DB2 = client.Database("clinflash-cloud-dev")
	//db = client.Database("clinflash-irt-uat")
	//DB = client.Database("clinflash-irt-uat")
	//DB = client.Database("clinflash-irt-pre")
}
func Test(t *testing.T) {
	//t.Run("updateDrugConfigure", UpdateDrugConfigure)
	//V2.4.0脚本 //已执行。不能重复执行
	//t.Run("insertRole", insertRole)
	//t.Run("changeKey", changeKey) // 修改key
	//t.Run("insertSysAdmin", insertSysAdmin) // 初始化项目外管理员权限
	//t.Run("cleanSite", cleanSite)                             // 中心清洗
	//t.Run("differentRole", differentRole)                     // 检查是否都为角色池的角色
	//t.Run("removeDisableOperations", removeDisableOperations) //  去除盲态角色， 置灰勾选的权限
	//t.Run("addDTPRole", addDTPRole)                           //  拷贝dtp角色
	//t.Run("changeSubjectKey", changeSubjectKey)               //  盲态角色受试者管理重洗
	//t.Run("initProjectTimeZone", initProjectTimeZone)         //项目时区字段如果为空就设置为utf-8

	//导出发药数据
	//t.Run("exportDispensing", exportDispensing)

	//V2.4.1 //已执行，不能重复执行
	//适配cloud修改邮箱功能，同步cloud的userId到user表
	//t.Run("syncCloudUserId", syncCloudUserId)

	//V2.5.0
	//系统级角色权限，初始化到现有项目角色权限表中 已执行，不能重复执行
	//t.Run("initProjectAdminPermission", initProjectAdminPermission)
	//t.Run("initProjectRolePermission", initProjectRolePermission)
	//t.Run("changePermissionKey", changePermissionKey)
	//t.Run("initSupplyInfo", initSupplyInfo)
	//t.Run("initStorehouseMedicineInfo", initStorehouseMedicineInfo)
	//t.Run("handlerCancelOrder", handlerCancelOrder)

	//hotfix-20221108
	//t.Run("initUnblindingReasonConfig", initUnblindingReasonConfig)
	//t.Run("changePermissionKey", changePermissionKey)

	//feature-20230110
	//db.project.updateMany({"info.connect_edc": 1},{$set:{"info.push_mode":NumberInt(1)}})

	//V2.5.1

	//t.Run("createDispensingIndex", createDispensingIndex)
	//t.Run("updateNoticeConfig", updateNoticeConfig)
	//t.Run("updateAttribute", updateAttribute)
	//t.Run("initOperation", initOperation)
	//t.Run("attributePrefixPrefixExpression", attributePrefixPrefixExpression)
	//t.Run("initMessageCenter", initMessageCenter)

	//中心联系人历史数据处理s
	//db.project_site.find().forEach(
	//	function(item){
	//		if (item.contacts != "" || item.phone !="" || item.email != "" || item.address != ""){
	//			db.project_site.update({_id:item._id},{$set:{contact_group:[{contacts:item.contacts,phone:item.phone,email:item.email,address:item.address,isdefault:1}]}},true)
	//		}
	//	}
	//)

	//IRT-3028（超级管理员优化）,feature-3028的分支
	//db.role_permission.update({"name": "Sys-Admin","template": 1},{"$push":{"permissions":"operation.settings.roles.export"}})

	// IRT-3300
	//t.Run("UpdateSupplyPlanMedicine", UpdateSupplyPlanMedicine)
	//t.Run("UpdateVisit", UpdateVisit)

	//IRT 2.6.0
	//t.Run("updateDrugOpenSetting", updateDrugOpenSetting)
	//t.Run("updateSysMsg", updateSysMsg)
	// db.notice_config.update({"key":{$in:["notice.subject.add","notice.subject.random","notice.subject.unblinding","notice.medicine.alarm"]}},{$set:{"fields_config":[
	//     "projectName",
	//     "projectNumber",
	//     "envName",
	//     "siteName",
	//     "siteNumber"
	// ]}},false,true);
	// db.notice_config.update({"key":{$in:["notice.subject.alarm","notice.medicine.isolation","notice.medicine.order","notice.medicine.reminder","notice.storehouse.alarm","notice.order.timeout"]}},{$set:{"fields_config":[
	// 		"projectName",
	// 		"projectNumber",
	// 		"envName"
	// 	]}},false,true);
	// db.notice_config.update({"key":"notice.subject.dispensing","fields_config":{$ne:null}},{$push:{"fields_config":{$each:[
	// 	"projectName",
	// 	"projectNumber",
	// 	"envName",
	// 	"siteName",
	// 	"siteNumber"
	// ]}}},false,true);
	// db.notice_config.update({"key":"notice.subject.dispensing","fields_config":null},{$set:{"fields_config":[
	// 	"projectName",
	// 	"projectNumber",
	// 	"envName",
	// 	"siteName",
	// 	"siteNumber"
	// ]}},false,true);
	//hotfix-20230626
	//t.Run("fixHistoryRandomGroup", fixHistoryRandomGroup)

	//hotfix-20230704
	// db.custom_template.update({"_id":ObjectId("6441056f19cf59b8d7f4aecc")},{$set:{"fields":[
	//     "report.attributes.project.number",
	//     "report.attributes.project.name",
	//     "report.attributes.order.number",
	//     "report.attributes.order.status",
	//     "report.attributes.order.send",
	//     "report.attributes.order.receive",
	//     "report.attributes.order.medicineQuantity",
	//     "report.attributes.order.create.by",
	//     "report.attributes.order.create.time",
	//     "report.attributes.order.cancel.by",
	//     "report.attributes.order.cancel.time",
	//     "report.attributes.order.cancel.reason",
	//     "report.attributes.order.confirm.by",
	//     "report.attributes.order.confirm.time",
	//     "report.attributes.order.close.by",
	//     "report.attributes.order.close.time",
	//     "report.attributes.order.close.reason",
	//     "report.attributes.order.send.by",
	//     "report.attributes.order.send.time",
	//     "report.attributes.order.receive.by",
	//     "report.attributes.order.receive.time",
	//     "report.attributes.order.lost.by",
	//     "report.attributes.order.lost.time",
	//     "report.attributes.order.lost.reason",
	//     "report.attributes.order.end.by",
	//     "report.attributes.order.end.time",
	//     "report.attributes.order.end.reason",
	//     "report.attributes.order.supplier",
	//     "report.attributes.order.supplier.other",
	//     "report.attributes.order.supplier.number",
	//     "report.attributes.research",
	//     "report.attributes.research.batch",
	//     "report.attributes.research.expireDate",
	//     "report.attributes.research.status",
	//     "report.attributes.research.other"
	// ]}},false,true);

	//feature-3062
	//t.Run("updateSiteName", updateSiteName)
	// hotfix-4734
	// 	t.Run("updateDispensingHistory", updateDispensingHistory)
	//t.Run("ChangeDTP", ChangeDTP)

	//V2.7.0
	//t.Run("syncUserNumber", syncUserNumber)
	//t.Run("fixHistoryUserName", fixHistoryUserName)
	//t.Run("UpdateOpenSetting", UpdateOpenSetting)
	//t.Run("UpdateVisitOperation", UpdateVisitOperation)
	//t.Run("ChangeDTP", ChangeDTP)
	//t.Run("updateSysMsg", updateSysMsg)

	//V2.8.0
	//t.Run("UpdateFreezeMedicineHistorySort", UpdateFreezeMedicineHistorySort)
	//t.Run("UpdateProjectUserPermission", UpdateProjectUserPermission)
	//t.Run("UpdateAttributeSetting", UpdateAttributeSetting)
	//t.Run("UpdateNotice", UpdateNotice)
	//t.Run("UpdateRandomList", UpdateRandomList)
	//t.Run("UpdateRandomListPermission", UpdateRandomListPermission)
	//t.Run("initEnvCapacity", initEnvCapacity)
	//t.Run("UpdateReportPlace", UpdateReportPlace)
	//t.Run("UpdateOperationResume", UpdateOperationResume)
	//t.Run("UpdateOperationFormula", UpdateOperationFormula)
	//t.Run("UpdateAttributeReplace", UpdateAttributeReplace)
	//t.Run("AddProjectCardProjectRolePermission", AddProjectCardProjectRolePermission)
	//t.Run("UpdateProjectUnblindingType", UpdateProjectUnblindingType)
	//t.Run("UpdateUnblindingPvProjectRolePermission", UpdateUnblindingPvProjectRolePermission)
	//t.Run("UpdateProjectEdcSupplier", UpdateProjectEdcSupplier)

	//V2.8.1
	//t.Run("AddAppVersion", AddAppVersion)

	//v2.9.0_hotfix_20240423_test
	//t.Run("SimulateRandomDataCleaning", SimulateRandomDataCleaning)
	//t.Run("UpdateOperationRole", UpdateOperationRole)

	//hotfix-20240430
	//t.Run("UpdateNoticeSubjectUpdateAndScreen", UpdateNoticeSubjectUpdateAndScreen)

	//V2.9.0(hotfix-20240423)
	//t.Run("SimulateRandomDataCleaning", SimulateRandomDataCleaning)

	//V2.9.0
	//t.Run("CreateBarcodeNumberIndex", CreateBarcodeNumberIndex)
	//t.Run("UpdateOldOpenProjectDrugConfig", UpdateOldOpenProjectDrugConfig)
	//t.Run("UpdateOperationConfigurationSetting", UpdateOperationConfigurationSetting)
	//t.Run("UpdateProjectSiteTz", UpdateProjectSiteTz)
	//t.Run("UpdateEnvCohortCapacity", Updatt.Run()eEnvCohortCapacity)
	//t.Run("UpdatePackageConfig", UpdatePackageConfig)

	//查询项目数据用
	//t.Run("Query", Query)
	//t.Run("QueryCohortDisableOperation", QueryCohortDisableOperation)

	//V2.10.0
	// t.Run("InitEnvStatus", InitEnvStatus)

	//t.Run("UpdateOperationExamineUpdateRelease", UpdateOperationExamineUpdateRelease)
	//t.Run("UpdateProjectNoticeID", UpdateProjectNoticeID)
	//t.Run("UpdateOperationExamineUpdateRelease", UpdateOperationExamineUpdateRelease)

	//订单排序
	// db.medicine_order.updateMany({"status":1},{"$set":{"sort_index":NumberInt(10)}});
	// db.medicine_order.updateMany({"status":2},{"$set":{"sort_index":NumberInt(5)}});
	// db.medicine_order.updateMany({"status":3},{"$set":{"sort_index":NumberInt(20)}});
	// db.medicine_order.updateMany({"status":4},{"$set":{"sort_index":NumberInt(25)}});
	// db.medicine_order.updateMany({"status":5},{"$set":{"sort_index":NumberInt(30)}});
	// db.medicine_order.updateMany({"status":6},{"$set":{"sort_index":NumberInt(1)}});
	// db.medicine_order.updateMany({"status":7},{"$set":{"sort_index":NumberInt(15)}});
	// db.medicine_order.updateMany({"status":8},{"$set":{"sort_index":NumberInt(35)}});
	// db.medicine_order.updateMany({"status":9},{"$set":{"sort_index":NumberInt(40)}});

	// hotfix-20241112
	//t.Run("DeleteDrugConfigureSetting", DeleteDrugConfigureSetting)

	//V2.11.0
	//irt_6889_test
	//t.Run("UpdateEdcProjectAll", UpdateEdcProjectAll)
	//先执行，执行完成之后再执行下一个
	//t.Run("HandleMedicineOtherInstitute", HandleMedicineOtherInstitute)
	//t.Run("HandleMedicineOrder", HandleMedicineOrder)
	//t.Run("HandldMedicineFreeze", HandldMedicineFreeze)
	//t.Run("HandldMedicineDispensing", HandleDispensing)
	//t.Run("UpdateOperationVisitSetting", UpdateOperationVisitSetting)
	//t.Run("UpdateOperationDtpRules", UpdateOperationDtpRules)
	//系统任务语言确认_20250109
	//t.Run("InsertCNEmailLanguageHistory", InsertCNEmailLanguageHistory)
	//t.Run("updateSysMsg", updateSysMsg)
	//IRT 7114
	//只跑us
	//t.Run("InsertUSEmailLanguageHistory", InsertUSEmailLanguageHistory)
	//导出不执行
	//t.Run("ExportProjectUserRoleEmail", ExportProjectUserRoleEmail)
	//hotfix-20250218
	//t.Run("HandleFactorCalcRandomDesign", HandleFactorCalcRandomDesign)
	//t.Run("HandleFactorCalcRandomList", HandleFactorCalcRandomList)
	////t.Run("HandleFormRegister", HandleFormRegister)
	//t.Run("HandleFormSubject", HandleFormSubject)
	//t.Run("UpdateOperationOrderDTP", UpdateOperationOrderDTP)
	////2025-02-27 补充单品隔离轨迹
	//t.Run("insertHistory", InsertHistory)

	//hotfix-20250416_test PROD复制出来的UAT，然后UAT新增一个cohort1,再把UAT复制回PROD
	//t.Run("CopyEnvironmentToProd", CopyEnvironmentToProd)

	//2025-04-17 工单处理，更新研究产品序列号
	// db.medicine.updateMany(
	// 	{"env_id":ObjectId("64e6b68784924bb7d4cb7ea0"),
	// 	"serial_number": { $exists: true, $ne: null },
	// 	   $expr: {
	// 		  $eq: [{ $strLenCP: "$serial_number" }, 4]
	// 	  }
	// 	},
	// 	  {
	// 		$set: {
	// 		  "serial_number" :
	// 			  { $concat: ["0",  "$serial_number" ] },
	// 		}
	// 	  }
	//   );
	//t.Run("UpdateOperationHistoryPushSetting", UpdateOperationHistoryPushSetting)
	//t.Run("UpdateInvalidListSetting", UpdateInvalidListSetting)
	//t.Run("updateSysMsg", updateSysMsg)

	//V2.13.0
	//导出不执行--ExportProjectTz、ExportTz
	//t.Run("ExportProjectTz", ExportProjectTz)
	//t.Run("ExportTz", ExportTz)
	t.Run("UpdateProjectTimeZoneTz", UpdateProjectTimeZoneTz)
	t.Run("UpdateVisitNoticeTimeZoneTz", UpdateVisitNoticeTimeZoneTz)
	t.Run("UpdateTimeZoneTz", UpdateTimeZoneTz)
	t.Run("UpdateProjectEnvCohortIsCopy", UpdateProjectEnvCohortIsCopy)
	t.Run("updateSysMsg", updateSysMsg)
	//hotfix-20250617_test 属性配置未更新Field字段label、labelEn问题修复
	//t.Run("UpdateAttributeField", UpdateAttributeField)

	//move_customer_test 迁移申办方更改customer_id字段
	t.Run("UpdateProjectCustomerId", UpdateProjectCustomerId)

}

func TestMain(m *testing.M) {
	m.Run()
}
