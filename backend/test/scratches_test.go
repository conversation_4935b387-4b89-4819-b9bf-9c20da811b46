package test

import (
	"clinflash-irt/data"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"context"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"strings"
	"testing"
)

func RemoveBlindedRoleHomeStatisticsPermission(t *testing.T) {
	projects := make([]models.Project, 0)
	cursor, err := DB.Collection("project").Find(nil, bson.M{"info.number": bson.M{"$in": bson.A{
		//"shaixuan",
		//"8.25",
		"ACT22-001",
		"MH004-E-201",
		"AK0901-2001",
		"HZ-H08905-301",
		"MG-K10-AD-003",
		"JY-R105-202",
		"JZB05DME301",
		"JSPL-PL-5-302",
		"YR001-A03",
		"HengLi009-Ⅱ",
		"RBHB1203",
		"GST-HG131-II-01",
		"TISA-818-23201",
		"TCR1672-II-01",
		"PB119110",
		"QY201-I-2 (II)",
		"ZGJAK025&ZGJAK026",
		"SYJC-MLSTN-R01",
		"JSPL-PL-18-103",
		"Kawin-KW053-1",
		"LWY23090C",
		"VG290131-AU-001",
		"BY-D-2110",
		"YHY-DC042-Ⅱ-202402",
		"HTD1801.PCT106",
		"HTD1801.PCT105",
		"KN-BCG-III",
		"KN035-CN-017",
		"KOR-CHINA-301",
		"BG2109-AB-301",
		"KD6005CT01",
		"0035-N-301",
		"093",
	}}})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &projects)
	if err != nil {
		panic(err)
	}

	projectIds := make([]primitive.ObjectID, 0)
	projectIds = slice.Map(projects, func(index int, item models.Project) primitive.ObjectID {
		return item.ID
	})
	blindedRole := bson.A{
		"Biostatistician [blinded]",
		"CRA [blinded]",
		"CRC [blinded, can see room number temporarily]",
		"CRC [blinded]",
		"DM [blinded]",
		"PI [blinded]",
		"Pharmacist [blinded]",
		"Project Manager [blinded]",
		"Shipper Manager [blinded]",
		"Sub-I [blinded]",
		"Supply Manager [blinded]",
		"Read-Only[blinded]",
		"Sponsor[blinded]",
		"Reportor[blinded]",
		"PI [blinded,can unblind]",
		"Safety Monitor [blinded,can unblind]",
	}
	DB.Collection("project_role_permission").UpdateMany(nil,
		bson.M{"project_id": bson.M{"$in": projectIds}, "name": bson.M{"$in": blindedRole}},
		bson.M{"$pull": bson.M{"permissions": bson.M{"$in": bson.A{"operation.project.depot.IPStatistics.view", "operation.project.site.IPStatistics.view"}}}})

}

func Query(t *testing.T) {
	projects := make([]models.Project, 0)
	cursor, err := DB.Collection("project").Find(nil, bson.M{"envs.name": "PROD", "info.number": bson.M{"$nin": bson.A{
		"Test_YT",
		"DEMO001",
		"Test_BlockRand",
		"TestDemo",
		"TEST_fmh",
		"Test_zr",
		"Clinflash Demo EDC",
		"Test_FMH",
		"test 2.0",
		"TestProject_DrBai",
		"prod001",
		"test01-1",
		"DEMO_002群组",
		"TEST20240103",
		"TEST20240201",
	},
	}})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &projects)
	if err != nil {
		panic(err)
	}
	//strings := slice.Map(projects, func(index int, item models.Project) string {
	//	return item.Number
	//})
	//mails := make([]models.Mail, 0)
	//opts := &options.FindOptions{Projection: bson.M{"_id": 1, "to": 1}}
	//cursor, err = DB.Collection("mail").Find(nil, bson.M{"status": 1, "subject_data.envName": "PROD", "subject_data.projectNumber": bson.M{"$in": strings}}, opts)
	//if err != nil {
	//	return
	//}
	//cursor.All(nil, &mails)
	//count := 0
	//for _, mail := range mails {
	//	count = count + len(mail.To)
	//}
	//fmt.Println(count)
	envIds := make([]primitive.ObjectID, 0)
	for _, project := range projects {
		for _, environment := range project.Environments {
			if environment.Name == "PROD" {
				envIds = append(envIds, environment.ID)
			}
		}
	}
	////fmt.Println(len(envIds))
	subjects := make([]models.Subject, 0)
	findOptions := &options.FindOptions{Projection: bson.M{"_id": 1}}
	cursor, err = DB.Collection("subject").Find(nil, bson.M{"env_id": bson.M{"$in": envIds}}, findOptions)
	if err != nil {
		panic(err)
	}
	cursor.All(nil, &subjects)
	//fmt.Println(len(subjects))
	subjectIds := slice.Map(subjects, func(index int, item models.Subject) primitive.ObjectID {
		return item.ID
	})
	count, err := DB.Collection("dispensing").CountDocuments(nil, bson.M{"dispensing_time": bson.M{"$gte": 1704038400}, "subject_id": bson.M{"$in": subjectIds}, "status": bson.M{"$in": [2]int{2, 3}}})
	if err != nil {
		panic(err)
	}
	fmt.Println(count)
	//documents, err := DB.Collection("medicine").CountDocuments(nil, bson.M{"env_id": bson.M{"$in": envIds}})
	//if err != nil {
	//	return
	//}
	//fmt.Println(documents)
}

func QueryCohortDisableOperation(t *testing.T) {
	menuTexts := make([]string, 0)
	getMenuChildren(data.MenuPermissions, menuTexts)
}
func getMenuChildren(menus []data.MenuPermission, menuText []string) {
	locales.Init()
	ctx := context.WithValue(context.Background(), "lang", "zh")
	for _, menu := range menus {
		if menu.Children != nil && len(menu.Children) > 0 {
			getMenuChildren(menu.Children, menuText)
		}
		if menu.FrozenOperations != nil && len(menu.FrozenOperations) > 0 {
			for _, operation := range menu.FrozenOperations {
				if operation.DisableCohortStatus != nil && len(operation.DisableCohortStatus) > 0 {
					fmt.Println(locales.Tr(ctx, menu.Text))
					builder := strings.Builder{}
					for _, status := range operation.DisableCohortStatus {
						s := ""
						if status == 1 {
							s = "草稿"
						} else if status == 3 {
							s = "完成入组"
						} else if status == 4 {
							s = "停止"
						}
						builder.WriteString(s)
						builder.WriteString(",")
					}
					fmt.Println(locales.Tr(ctx, operation.Text) + "-" + builder.String())
					fmt.Println("-----------------------------------")
				}
			}

		}
	}
}
