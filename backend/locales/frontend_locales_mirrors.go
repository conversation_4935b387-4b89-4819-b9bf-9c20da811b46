package locales

import (
	"encoding/json"
	"github.com/duke-git/lancet/v2/slice"
	"io"
)

type localeStruct struct {
	Modules     map[string]localeOption
	TypeOptions map[string]localeOption
	Data        []LocaleItem
}
type localeOption struct {
	Cn string
	En string
}

type LocaleNationItem struct {
	Label    string   // 名称
	PathName []string // 路径名
	TypeName string   // 类型名
}

type LocaleNation struct {
	Cn LocaleNationItem
	En LocaleNationItem
}

type LocaleItem struct {
	Key      string
	PathCode []string // 路径code
	TypeCode string   // 类型code
	Nation   LocaleNation
}

func LocaleItemsGet() []LocaleItem {
	f, _ := LocalesFS.Open("frontend_locales_mirrors.json")
	defer f.Close()
	data, _ := io.ReadAll(f)
	items := localeStruct{}
	_ = json.Unmarshal(data, &items)

	modules := items.Modules
	types := items.TypeOptions
	return slice.Map(items.Data, func(index int, item LocaleItem) LocaleItem {
		item.Nation.Cn.TypeName = types[item.TypeCode].Cn
		item.Nation.Cn.PathName = slice.Map(item.PathCode, func(index int, item string) string {
			return modules[item].Cn
		})
		item.Nation.En.TypeName = types[item.TypeCode].En
		item.Nation.En.PathName = slice.Map(item.PathCode, func(index int, item string) string {
			return modules[item].En
		})
		return item
	})
}
