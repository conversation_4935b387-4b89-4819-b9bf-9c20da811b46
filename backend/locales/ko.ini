menu.home = 작업대
menu.settings = 설정
menu.settings.storehouse = 창고
menu.settings.roles = 역할 권한
menu.settings.users = 사용자
menu.projects = 프로젝트
menu.projects.main = 프로젝트
menu.projects.main.env = 프로젝트 환경
menu.projects.main.setting = 프로젝트 설정
menu.projects.main.setting.base = 기본 정보
menu.projects.main.setting.function = 비즈니스 기능
menu.projects.main.setting.docking = 외부 도킹
menu.projects.main.setting.permission = 프로젝트 권한
menu.projects.main.setting.custom = 사용자 정의 프로세스
menu.projects.project = 프로젝트 세부정보
menu.projects.project.home = 첫 장
menu.projects.project.subject = 과목 관리
menu.projects.project.subject.urgent-unblinding = 눈가림 해제(긴급)
menu.projects.project.subject.urgent-unblinding.unblinding = 눈가림 해제(긴급)
menu.projects.project.subject.urgent-unblinding.approval-log = 승인 기록
menu.projects.project.subject.urgent-unblinding-pv = PV Unblinding
menu.projects.project.subject.urgent-unblinding.unblinding-pv = PV Unblinding
menu.projects.project.subject.urgent-unblinding.approval-log-pv = 승인 기록
menu.projects.project.subject.dispensing = Dispense
menu.projects.project.supply = 공급 관리
menu.projects.project.supply.storehouse = 창고 통계
menu.projects.project.supply.storehouse.summary = 개요
menu.projects.project.supply.storehouse.single = 단일 제품
menu.projects.project.supply.site = 중앙약국
menu.projects.project.supply.site.summary = 개요
menu.projects.project.supply.site.single = 단일 제품
menu.projects.project.supply.site.no_number = 번호가 없는 연구 제품 항목
menu.projects.project.supply.shipment = 제품 주문 조사
menu.projects.project.supply.shipment.approval = Approval Record
menu.projects.project.supply.shipment.logistics = Logistics
menu.projects.project.supply.drug_recovery = 연구 제품 재활용
menu.projects.project.supply.release-record = 검역기록
menu.projects.project.supply.drug = 제품을 연구하다
menu.projects.project.supply.drug.order = 제품 주문 조사
menu.projects.project.supply.drug.single = 단일 제품
menu.projects.project.supply.drug.no_number = 번호가 없는 연구 제품 항목
menu.projects.project.build = 프로젝트 빌드
menu.projects.project.build.storehouse = 물류 창고 관리, 물류 관리
menu.projects.project.build.site = 중앙 관리
menu.projects.project.build.site.supply-plan = 供应计划
menu.projects.project.build.attributes = 속성 구성
menu.projects.project.build.code_rule = 인코딩 구성
menu.projects.project.build.simulate_random = 무작위 시뮬레이션
menu.projects.project.build.randomization = 임의 구성
menu.projects.project.build.randomization.design = 무작위 디자인
menu.projects.project.build.randomization.design.type = 랜덤 타입
menu.projects.project.build.randomization.design.group = 치료군
menu.projects.project.build.randomization.design.factor = 계층화 요인
menu.projects.project.build.randomization.design.list = 랜덤 리스트
menu.projects.project.build.randomization.design.attribute = 랜덤 리스트 속성
menu.projects.project.build.randomization.design.block = 난수 블록
menu.projects.project.build.randomization.form = 양식 구성
menu.projects.project.build.randomization.design.factor-in = 계층화 요인
menu.projects.project.build.drug = 제품 구성 연구
menu.projects.project.build.drug.visit = 방문주기
menu.projects.project.build.drug.config = 제품 구성 연구
menu.projects.project.build.drug.list = 제품 목록 검토
menu.projects.project.build.drug.no_number = 번호가 없는 연구 제품
menu.projects.project.build.drug.batch = 일괄 관리
menu.projects.project.build.drug.barcode = 바코드 목록
menu.projects.project.build.plan = 공급 계획
menu.projects.project.build.plan.config = 프로그램 관리
menu.projects.project.build.history = 프로젝트 로그
menu.projects.project.build.push = Statistics
menu.projects.project.settings = 프로젝트 설정
menu.projects.project.settings.notice = 알림 설정
menu.projects.project.settings.user = 인사관리
menu.projects.project.settings.export = 내보내기 구성
menu.projects.project.settings.role = 역할 권한
menu.projects.project.settings.config = 프로젝트 구성
menu.projects.project.monitor = 동적 모니터링

menu.projects.project.info = Project Information
operation.projects.project.info.view = View
menu.projects.project.basic.information = Basic Information
operation.projects.project.basic.information.view = View
menu.projects.project.basic.environment = Project Environment
operation.projects.project.basic.environment.view = View
menu.projects.project.business.functions = Business Functions
operation.projects.project.business.functions.view = View
menu.projects.project.external.docking = External Docking
operation.projects.project.external.docking.view = View
menu.projects.project.custom.process = Custom Process
operation.projects.project.custom.process.view = View
menu.projects.project.permissions = Project Permissions
operation.projects.project.permissions.view = View
menu.projects.notice.permissions = Project Notification
operation.projects.notice.permissions.view = View

operation.settings.roles.view = 확인하다
operation.settings.roles.add = 에 추가
operation.settings.roles.edit = 편집하다
operation.settings.roles.config = 권한 프로필
operation.settings.roles.export = 내보내기
operation.settings.users.view = 확인하다
operation.settings.users.add = 새로운
operation.settings.users.setting = 설정
operation.settings.users.edit = 편집하다
operation.settings.users.cancel = 취소
operation.settings.storehouse.view = 확인하다
operation.settings.storehouse.add = 새로운
operation.settings.storehouse.edit = 편집하다
operation.settings.storehouse.delete = 삭제
operation.projects.main.view ="인하다
operation.projects.main.create ="로젝트 생성
operation.projects.main.config.view = View
operation.projects.main.config.create= Add Environment
operation.projects.main.config.unlock= Unlock
operation.projects.main.config.lock= Lock
operation.projects.main.config.add= Add
operation.projects.main.config.edit= Edit
operation.projects.main.config.delete= Delete
operation.projects.main.config.copy= Copy
operation.projects.main.setting.view ="인하다
operation.projects.main.setting.base.view ="인하다
operation.projects.main.setting.base.edit ="집하다
operation.projects.main.setting.function.view ="인하다
operation.projects.main.setting.function.edit ="집하다
operation.projects.main.setting.function.admin ="리자 사용/사용 안 함
operation.projects.main.setting.docking.view ="인하다
operation.projects.main.setting.docking.edit ="집하다
operation.projects.main.setting.custom.view ="인하다
operation.projects.main.setting.custom.edit ="집하다
operation.projects.main.setting.permission.view= View
operation.projects.main.setting.permission.add= Add
operation.projects.main.setting.permission.edit= Edit
operation.projects.main.setting.permission.setting= Permission Setting
operation.projects.main.setting.permission.export= Export
operation.projects.home.view ="인하다
operation.subject.view-list = 확인하다
operation.subject.random = 무작위의
operation.subject.replace = 주제 교체
operation.subject.trail = 주제 궤적
operation.subject.unblinding-pv = 揭盲(눈가림 해제(pv))
operation.subject.medicine.view-dispensing = 피험자의 약물 정보 보기
operation.subject.medicine.trail = View
operation.subject.medicine.trail.print =  Print
operation.subject.medicine.dispensing = 조제약
operation.subject.medicine.reissue = 재발행
operation.subject.medicine.replace = 연구 제품 교체
operation.subject.unblinding = 보기
operation.subject.unblinding-application = 눈가림 해제(긴급) 신청
operation.subject.unblinding-approval = 눈가림 해제(긴급) 승인
operation.subject.unblinding-log = 보기
operation.subject.unblinding-sms = SMS 보내기
operation.subject.unblinding-print = Print
operation.subject.unblinding-pv-view = View
operation.subject.unblinding-pv-application = Unblinding（PV）Application
operation.subject.unblinding-pv-approval = Unblinding（PV）Approval
operation.subject.unblinding-pv-log = 보기
operation.subject.unblinding-pv-sms = SMS 보내기
operation.subject.unblinding-pv-print = Print
operation.subject.download = 블라인드 다운로드
operation.subject.medicine.export = 다운로드
operation.subject.download-random = 랜덤 다운로드
operation.subject.registered = 등록하다
operation.subject.update = 주제 수정
operation.subject.delete = 주제 삭제
operation.subject.medicine.room = 방 번호 보기
operation.subject.medicine.retrieval = 마약 회수
operation.subject.medicine.out-visit-dispensing = Unscheduled Dispense
operation.subject.medicine.invalid = 이번 방문에 참여하지 않음
operation.subject.medicine.room-download = 호실번호 조회기록 다운로드
operation.subject.medicine.register = 약물의 실제 사용 등록
operation.subject.secede = 피험자 철회
operation.subject.print = 트랙 인쇄
operation.subject-dtp.view-list = 보기
operation.subject-dtp.random = 무작위
operation.subject-dtp.replace = 수험자 교체
operation.subject-dtp.trail = 피험자 경로
operation.subject-dtp.unblinding-pv = 맹인 제거(pv)
operation.subject-dtp.medicine.view-dispensing = 발약 신청 상세 정보
operation.subject-dtp.medicine.trail = 발약 궤적
operation.subject-dtp.medicine.dispensing = 신청하다.
operation.subject-dtp.medicine.reissue = 신청서를 재발급 하다
operation.subject-dtp.medicine.replace = 제품 교체 검토
operation.subject-dtp.unblinding = 보기
operation.subject-dtp.unblinding-application = 눈가림 해제(긴급) 신청
operation.subject-dtp.unblinding-approval = 눈가림 해제(긴급) 승인
operation.subject-dtp.unblinding-log = 보기
operation.subject-dtp.unblinding-sms = SMS 보내기
operation.subject-dtp.download = 블로킹 다운로드
operation.subject-dtp.medicine.export = 발약
operation.subject-dtp.download-random = 무작위 다운로드
operation.subject-dtp.registered = 등기하다
operation.subject-dtp.update = 수험자 수정
operation.subject-dtp.delete = 피험자 삭제
operation.subject-dtp.medicine.room = 방 번호 보기
operation.subject-dtp.medicine.out-visit-dispensing = Unscheduled Dispense Apply
operation.subject-dtp.medicine.invalid = 이번 방문에 불참하다
operation.subject-dtp.medicine.export-room = 발약 다운로드(방 번호 포함)
operation.subject-dtp.medicine.room-download = 룸 번호 보기 레코드 다운로드
operation.subject-dtp.medicine.register = 실제 사용 약물 을 등록 하다
operation.subject-dtp.secede = 피험자 종료
operation.subject-dtp.print = 플롯 경로
operation.supply.storehouse.medicine.summary = 확인하다
operation.supply.storehouse.medicine.singe = 확인하다
operation.supply.storehouse.medicine.use = 사용할 수 있도록
operation.supply.storehouse.medicine.freeze = 격리
operation.supply.storehouse.medicine.lost = Lost/Void
operation.supply.storehouse.medicine.history = 과정
operation.supply.storehouse.medicine.download = 데이터 다운로드
operation.supply.site.medicine.summary = 확인하다
operation.supply.site.medicine.singe = 확인하다
operation.supply.site.medicine.use = 사용할 수 있도록
operation.supply.site.medicine.freeze = 격리
operation.supply.site.medicine.lost = Lost/Void
operation.supply.site.medicine.history = 과정
operation.supply.site.medicine.download = 데이터 다운로드
operation.supply.site.no_number.view = 확인하다
operation.supply.site.no_number.freeze = 격리
operation.supply.site.no_number.lost = Lost/Void
operation.supply.site.no_number.history = 과정
operation.supply.drug.order.list = 보기
operation.supply.drug.order.send = 주문 배송
operation.supply.drug.order.receive = 주문 접수
operation.supply.drug.order.end = 배송 취소
operation.supply.drug.order.cancel = 주문 취소
operation.supply.drug.order.reason = 이유
operation.supply.drug.order.download = 다운로드
operation.supply.drug.order.history = 궤적
operation.supply.drug.single.sku = 보기
operation.supply.drug.single.history = 궤적
operation.supply.drug.single.download = 다운로드
operation.supply.drug.single.delete = 분실/폐기
operation.supply.drug.single.use = 사용 가능으로 설정
operation.supply.drug.no_number.view = 보기
operation.supply.shipment.create = 주문 추가
operation.supply.shipment.cancel = 배송 주문
operation.supply.shipment.send = 주문 취소
operation.supply.shipment.lose = 분실 주문
operation.supply.shipment.list = 확인하다
operation.supply.shipment.receive = 주문을 받다
operation.supply.shipment.download = 주문 보고서 다운로드
operation.supply.shipment.alarm = 창고 확인
operation.supply.shipment.history = 과정
operation.supply.shipment.confirm = 주문 확인
operation.supply.shipment.reason = 이유
operation.supply.shipment.approval = Site Order Application Approval
menu.projects.project.supply.shipment.detail = IP Detail
operation.supply.shipment.detail.change = Replace
operation.supply.shipment.detail.changeRecord = Replacement Records
operation.supply.shipment.detail.edit = Edit
menu.projects.project.supply.recovery.detail = IP Detail
operation.supply.recovery.detail.change = Replace
operation.supply.recovery.detail.changeRecord = Replacement Records
operation.supply.shipment.close = Close
operation.supply.shipment.terminated = Terminated
operation.supply.shipment.approval.view = 확인하다
operation.supply.shipment.detail.view = Detail
operation.supply.shipment.approval.print = Print
operation.supply.shipment.logistics.view = View
operation.supply.shipment.logistics.edit = Edit
operation.supply.recovery.list = 확인하다
operation.supply.recovery.add = 새로운
operation.supply.recovery.receive = 연구제품 재활용 주문 접수
operation.supply.recovery.confirm = 배송 연구 제품 재활용 주문
operation.supply.recovery.cancel = 연구제품 재활용 주문 취소
operation.supply.recovery.lose = 플래그 연구 제품 재활용 주문 누락
operation.supply.recovery.download = 다운로드
operation.supply.recovery.reason = 까닭
operation.supply.recovery.history = 과정
operation.supply.recovery.detail.view = Detail
operation.supply.freeze.list = 확인하다
operation.supply.freeze.release = 일괄 분리 분리
operation.supply.freeze.delete = 분실/무효화됨
operation.supply.freeze.history = 과정
operation.build.storehouse.add = 새로운
operation.build.storehouse.delete = 창고 삭제
operation.build.storehouse.edit = 창고편집
operation.build.storehouse.notice = 설정
operation.build.storehouse.view = 확인하다
operation.build.storehouse.alarm = 연구 제품 경고
operation.build.site.view = 확인하다
operation.build.site.edit = 편집하다
operation.build.site.add = 센터 추가
operation.build.site.dispensing = 약을 보내다
operation.build.site.supply-plan.view = 查看
operation.build.site.supply-plan.edit = 编辑
operation.build.attribute.view = 확인하다
operation.build.attribute.edit = 편집하다
operation.build.attribute.history = 트랙 보기
operation.build.code-rule.view = 확인하다
operation.build.code-rule.edit = 편집하다
operation.build.simulate-random.view = 확인하다
operation.build.simulate-random.edit = 편집하다
operation.build.simulate-random.add = 신설
operation.build.simulate-random.run = 실행
operation.build.simulate-random.site = 센터 개요
operation.build.simulate-random.factor = 계층화 개요
operation.build.simulate-random.download = 데이터 다운로드
operation.build.randomization.type.view = 확인하다
operation.build.randomization.type.edit = 편집하다
operation.build.randomization.group.add = 새로운
operation.build.randomization.group.delete = 삭제
operation.build.randomization.group.edit = 편집하다
operation.build.randomization.group.view = 확인하다
operation.build.randomization.factor.add = 새로운
operation.build.randomization.factor.view = 확인하다
operation.build.randomization.factor.delete = 레이어 삭제
operation.build.randomization.factor.edit = 레이어 설정
operation.build.randomization.factor.set-toplimit = 계층화 설정
operation.build.randomization.list.view-summary = 확인하다
operation.build.randomization.list.upload = 업로드
operation.build.randomization.list.generate = 생성하다
operation.build.randomization.list.active = 임의 목록 사용/사용 안 함
operation.build.randomization.list.export = 내보내다
operation.build.randomization.list.invalid = 무효의
operation.build.randomization.list.edit = 编辑
operation.build.randomization.list.segmentation.view = 확인하다
operation.build.randomization.list.segmentation.clear = 다른 레이어 지우기
operation.build.randomization.list.segmentation.site = 중심에 블록 할당
operation.build.randomization.list.segmentation.factor = 계층에 블록 할당
operation.build.randomization.list.history = 과정
operation.build.randomization.list.attribute ="인하다
operation.build.randomization.factor-in.view = 확인하다
operation.build.randomization.factor-in.set-people = 인원수를 설정하다
operation.build.randomization.form.add = 새로운
operation.build.randomization.form.delete = 삭제
operation.build.randomization.form.edit = 편집하다
operation.build.randomization.form.list = 확인하다
operation.build.medicine.visit.add = 새로운
operation.build.medicine.visit.delete = 삭제
operation.build.medicine.visit.edit = 편집하다
operation.build.medicine.visit.list = 확인하다
operation.build.medicine.configuration.add = 새로운
operation.build.medicine.configuration.delete = 삭제
operation.build.medicine.configuration.edit = 편집하다
operation.build.medicine.configuration.list = 확인하다
operation.build.medicine.upload.list = 확인하다
operation.build.medicine.upload.upload = 업로드
operation.build.medicine.upload.downdata = 데이터 다운로드
operation.build.medicine.packlist.upload = 포장 목록 업로드
operation.build.medicine.package.setting = Setting
operation.build.medicine.examine = 审核
operation.build.medicine.update = 修改
operation.build.medicine.release = 放行
operation.build.medicine.upload.delete = 일괄 삭제
operation.build.medicine.upload.uploadHistory = 과정
operation.build.medicine.otherm.add = 새로운
operation.build.medicine.otherm.delete = 삭제
operation.build.medicine.otherm.edit = 편집하다
operation.build.medicine.otherm.list = 확인하다
operation.build.medicine.batch.list = 확인하다
operation.build.medicine.batch.edit = 편집하다
operation.build.medicine.barcode.view = 확인하다
operation.build.medicine.barcode.add = 바코드 생성
operation.build.medicine.barcode.scan = 코드를 스캔하여 창고에 입장하세요
operation.build.medicine.barcode.scanPackage = Package Scanning
operation.build.medicine.barcode.export = Export
operation.build.supply-plan.add = 새로운
operation.build.supply-plan.delete = 삭제
operation.build.supply-plan.edit = 편집하다
operation.build.supply-plan.view = 확인하다
operation.build.supply-plan.history = 과정
operation.build.supply-plan.medicine.add = 새로운
operation.build.supply-plan.medicine.delete = 삭제
operation.build.supply-plan.medicine.edit = 편집하다
operation.build.supply-plan.medicine.view = 확인하다
operation.build.supply-plan.medicine.history = 과정
operation.build.history.view = 확인하다
operation.build.history.print = 인쇄
operation.build.push.view = 확인하다
operation.build.push.all.send = All
operation.build.push.batch.send = Batch Send
operation.build.push.send = Resend
operation.build.push.details = Details
operation.build.settings.notice.view = 확인하다
operation.build.settings.notice.edit = 편집하다
operation.build.settings.user.view = 확인하다
operation.build.settings.user.edit = 운영 관리
operation.build.settings.user.download = 데이터 다운로드
operation.monitor.view = 확인하다
operation.monitor.edit = 관리하다
operation.build.randomization.info.view ="기
operation.build.randomization.info.export ="보내기