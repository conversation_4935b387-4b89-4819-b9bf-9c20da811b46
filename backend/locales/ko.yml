alarm:
  storehouse:
    content: <p>프로젝트 넘버:{{.projectNumber}}</p> <p>프로젝트 명칭:{{.projectName}}</p> <p>프로젝트 환경:{{.envName}}</p> <p>약 재고량이 경계치보다 낮습니다, 가능한 빨리 보충해주세요. 제품 을 연구 하 다정보(창고 명칭/제품 명 연구/경계 수량/남은 수량)：{{.drugInfo}}</p>
    title: ClinflashIRT창고 제품 을 연구 하 다 경계 알림
app:
  unauthorized: App Unauthorized
auth:
  fail: 인증 실패
cohort:
  status:
    complete: Complete
    draft: Draft
    enrollment: Enrollment
    stop: Stop
shipmentMode:
    status:
      set: Quantity
      reSupply: Reshipment
      max: Max Buffer
      supplyRatio: Supply Ratio
common:
  error:
    default: An error occurred processing the request.
    request: Bad request
    not-logged-in: Not logged in
    unauthorized: Unauthorized
    not-found: Unknown request
  checkbox: checkbox
  date: date
  dateTime: dateTime
  delete:
    fail: 삭제 실패
    success: 삭제 성공
  duplicated:
    factors: 요소가 중복됨
    names: 이름이 중복됨
  no: No
  input: input
  inputNumber: inputNumber
  load:
    fail: 로딩 실패
    success: 로딩 성공
  nil: 없음
  operation:
    edc:
      dsp:
        fail: 현재 방문 발약됨, 정보는 두 번 반려됨.
      fail: 무작위 피험자 선택됨
    fail: 작동 실패
    success: 작동 성공
  radio: radio
  remark: Remark
  save:
    fail: 저장 실패
    success: 저장 성공
  select: select
  switch: switch
  textArea: textArea
  yes: Yes
  update:
    fail: 업데이트 실패
    success: 업데이트 성공
  wrong:
    parameters: 데이터 오류 발생
  country : 국가
cross:
  check:
    error: Clinflash IRT 异常通知
customers:
  delete:
    admin: 현재 사용자는 클라이언트의 admin 권한이 없어 조작이 불가능합니다
    message: 클라이언트 아이디가 존재하여 삭제할 수 없습니다
  duplicated:
    names: 클라이언트명 중복됨
dispense_list_download_name: dispense_list
dispensing:
  plan: <p>프로젝트 넘버:{{.projectNumber}}</p> <p>프로젝트 명칭:{{.projectName}}</p> <p>프로젝트 환경:{{.envName}}</p> <p>센터 번호:{{.siteNumber}}</p> <p>센터 명칭:{{.siteName}}</p> <p>{{.subject}}:{{.subjectNumber}}</p>  <p>Random Number:{{.random_number}}</p>  <p>제품 번호 연구:{{.drugNumber}}</p> <p>방문 주기:{{.visitName}}</p> <p>피험 발약 시간:{{.dispensingDate}}</p>
  plan-logistics: "<p>Project Number:{{.projectNumber}}</p> <p>Project Name:{{.projectName}}</p>
      <p>Project Environment:{{.envName}}</p> <p>Site Number:{{.siteNumber}}</p> <p>Site
      Name:{{.siteName}}</p> <p>Subject Number:{{.subjectNumber}}</p><p>Random Number:{{.random_number}}</p>  <p>IP Number:{{.drugNumber}}</p> <p>Visit cycle:{{.visitName}}</p> <p>Order Number:{{.orderNumber}}</p> <p>Dispensation Time:{{.dispensingDate}}</p>  <p>Remark:{{.remark}}</p> "

  plan-title: Clinflash IRT {{.projectNumber}} {{.envName}}  발약 알림 {{.siteNumber}} {{.siteName}}
  unscheduled-plan-title: Clinflash IRT  {{.projectNumber}} {{.envName}} 지적재산권 비계획 방문 면제 {{.siteNumber}} {{.siteName}}
  reissue: <p>프로젝트 넘버:{{.projectNumber}}</p> <p>프로젝트 명칭:{{.projectName}}</p> <p>프로젝트 환경:{{.envName}}</p> <p>센터 번호:{{.siteNumber}}</p> <p>센터 명칭:{{.siteName}}</p> <p>{{.subject}}:{{.subjectNumber}}</p><p>Random Number:{{.random_number}}</p>  <p>제품 번호 연구:{{.drugNumber}}</p> <p>방문 주기:{{.visitName}}</p> <p>피험 발약 시간:{{.dispensingDate}}</p>
  reissue-logistics: <p>Project Number:{{.projectNumber}}</p> <p>Project Name:{{.projectName}}</p>
    <p>Project Environment:{{.envName}}</p> <p>Site number:{{.siteNumber}}</p> <p>Site Name:{{.siteName}}</p> <p>Subject Number:{{.subjectNumber}}</p> <p>Random Number:{{.random_number}}</p> <p>IP Number:{{.drugNumber}}</p> <p>Visit cycle:{{.visitName}}</p> <p>Remark:{{.remark}}</p> <p>Dispensation Time：
    {{.dispensingDate}}</p> <p>Remark:{{.remark}}</p>

  reissue-title: Clinflash IRT {{.projectNumber}} {{.envName}}  발약 보충 알림 {{.siteNumber}} {{.siteName}}
  replace: <p>프로젝트 넘버:{{.projectNumber}}</p> <p>프로젝트 명칭:{{.projectName}}</p> <p>프로젝트 환경:{{.envName}}</p> <p>센터 번호:{{.siteNumber}}</p> <p>센터 명칭:{{.siteName}}</p> <p>{{.subject}}:{{.subjectNumber}}</p> <p>Random Number:{{.random_number}}</p>  <p>제품 을 연구 하 다 번호를 교체:{{.replaceNumber}}</p> <p>교체된 제품 번호 연구:{{.drugNumber}}</p> <p>교체 시간:{{.dispensingDate}}</p>
  replace-title: Clinflash IRT {{.projectNumber}} {{.envName}}   발약 보충 알림 {{.siteNumber}} {{.siteName}}
  apply:  <p>Project Number:{{.projectNumber}}</p> <p>Project Name:{{.projectName}}</p> <p>Project Environment:{{.envName}}</p> <p>Site Number:{{.siteNumber}}</p> <p>Site Name:{{.siteName}}</p> <p>Subject Number:{{.subjectNumber}}</p>  <p>Random Number:{{.randomNumber}}</p>  <p>IP Number:{{.drugNumber}}</p> <p>Visit Cycle:{{.visitName}}</p> <p>Order Number:{{.orderNumber}}</p> <p>Remark:{{.remark}}</p>  <p>Dispense application Time:{{.dispensingDate}}</p>
  apply-title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Dispense application {{.siteNumber}} {{.siteName}}
  reissue-dtp: <p>Project Number:{{.projectNumber}}</p> <p>Project Name:{{.projectName}}</p> <p>Project Environment:{{.envName}}</p> <p>Site Number:{{.siteNumber}}</p> <p>Site Name:{{.siteName}}</p> <p>Subject Number:{{.subjectNumber}}</p>  <p>Random Number:{{.randomNumber}}</p>  <p>IP Number:{{.drugNumber}}</p> <p>Visit Cycle:{{.visitName}}</p> <p>Order Number:{{.orderNumber}}</p> <p>Remark:{{.remark}}</p>  <p>Re-Dispense application Time:{{.dispensingDate}}</p>
  reissue-dtp-title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Re- Dispense application {{.siteNumber}} {{.siteName}}
  unscheduled-apply: <p>Project Number:{{.projectNumber}}</p> <p>Project Name:{{.projectName}}</p> <p>Project Environment:{{.envName}}</p> <p>Site Number:{{.siteNumber}}</p> <p>Site Name:{{.siteName}}</p> <p>Subject Number:{{.subjectNumber}}</p>  <p>Random Number:{{.randomNumber}}</p>  <p>IP Number:{{.drugNumber}}</p> <p>Visit Cycle:{{.visitName}}</p> <p>Order Number:{{.orderNumber}}</p> <p>Remark:{{.remark}}</p>  <p>Re-Dispense application Time:{{.dispensingDate}}</p>
  unscheduled-apply-title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Re- Dispense application {{.siteNumber}} {{.siteName}}
  replace-dtp: "<p>Project Name:{{.projectNumber}}</p> <p>Project Name:{{.projectName}}</p>
    <p>Project Environment:{{.envName}}</p> <p>Site Number:{{.siteNumber}}</p> <p>Site Name:{{.siteName}}</p> <p>Subject Number:{{.subjectNumber}}</p> <p>Random Number:{{.randomNumber}}</p>  <p>IP Number:{{.replaceNumber}}</p> <p>IP Number(Replaced):{{.drugNumber}}</p> <p>Order Number: {{.orderNumber}}</p>
    <p>Replace Time:{{.dispensingDate}}</p> <p>Replace Reason:{{.reason}}</p>"
  replace-dtp-title: Clinflash IRT {{.projectNumber}}({{.envName}}) IP Replacement {{.siteNumber}} {{.siteName}}
edc:
  error: Request failed, the date push mode is active push from IRT.
  version: EDC interface version error, contact EDC related personnel
  add:
    relation:
      site:
        error: Site association failed, please associate site again.
    site:
      error: Add center error
  block:
    is:
      not:
        nil:
          error: blockRepeatNo parameter is empty, dispensation failed, please confirm configuration again.
  check:
    matching:
      value:
        error: Value matching failed, please re confirm the value of the check box
  configure:
    drug:
      error: Treatment design is empty, please confirm treatment design in EDC again.
    visit:
      error: Visit cycle is empty, please confirm configuration of visit cycle in EDC again.
  drug:
    number:
      error: IP number search error, please search again.
      mismatch: IP number search result does not match, please confirm replacing IP information.
    reissue:
      error: Re-dispensation failed, please contact unblined personnel to complete operation.
    replace:
      error: 교체에 실패하여 지난번에 완료한 방문진료를 약물 교체할 수 없습니다. 다시 선택하세요.
      nil:
        nil: IP number is empty, please operate after confirm again.
        error: Replacing IP search failed, please contact IRT engineer to dear with.
    type:
      error: type parameter is empty, dispensation failed, please confirm configuration again.
  env:
    error: Project environment information does not exist, please contact IRT engineer to check.
  factor:
    error: Cohort factor is empty, please confirm again.
  instance:
    is:
      not:
        nil:
          error: instanceRepeatNo parameter is empty, dispensation failed, please confirm configuration again.
  matching:
    value:
      error: Value matching failed, please confirm the value of radio button or drop-down box again.
  multiple:
    subject: Subject number search duplicated, please confirm subject number information.
  no:
    subject: Edit failed, please contact IRT engineer to dear with.
  parameter:
    error: Project number/project environment/site number/site name/subject number is empty, please confirm EDC configuration.
  project:
    env:
      number:
        error: Project number/project environment is empty, synchronization failed, please confirm configuration again.
  query:
    factor:
      error: Cohort search failed, please confirm cohort factor configuration again.
    project:
      error: Project search failed, please contact IRT engineer to check.
      number:
        error: Project search failed, please contact IRT engineer to check.
      dtp:
        error: DTP project does not support docking with EDC
    site:
      number:
        error: Site search duplicated, please confirm if multiple sites in IRT system.
        relation:
          site:
            error: Site search duplicated, please confirm if multiple sites in IRT system.
  register:
    synchronization:
      error: Current interface does not support one-time full synchronization, interface call failed. Please confirm EDC synchronization function configuration.
  relation:
    site:
      error: Site association failed, please contact IRT engineer to check.
  site:
    error: Site search failed, please contact IRT engineer to check.
  standard:
    lost:
      site:
        error: Site information incomplete, please contact IRT engineer to edit.
    site:
      error: Site information lost, please contact IRT engineer to add.
  start:
    site:
      error: Site enable/activate failed, please operate again.
  subject:
    after:
      dispensing:
        error: The subjects were not randomized and failed to dispense the medicine. Please reconfirm
    dispensing:
      error: The subjects were not randomized and failed to dispense the medicine. Please reconfirm
    existence: Subject already exists, please do not add repeatedly.
    random:
      number:
        error: Subject number is empty, please confirm EDC configuration.
    register:
      error: Subject is not registered, please confirm again.
    status:
      dispensing:
        error: Subject has been randomized, dispensation before randomization failed, please confirm again.
    site:
      error: The site information returned by IRT does not match the site where the subject is located. Please perform the subject transfer in IRT before dispensation.
  unable:
    at:
      random: Current fuction is not applicable in randomized project, please confirm randomization design.
  visit:
    error: Visit cycle is empty, please confirm configuration between visit number and visit cycle in EDC again.
    no:
      dispensing:
        error: Dispensation failed at current visit, please confirm property settings before/after randomization.
    number:
      drug:
        configure:
          error: Treatment design search duplicated, please confirm configuration between visit number and treatment design in EDC again.
      error: Visit number is empty, synchronization failed, please confirm configuration again.
environment:
  duplicated:
    names: 환경 명칭 중복
export:
  project: Project
  projects:
    number: Project Number
    name: Project Name
    cohort: Cohort
    stage: Stage
  barcode: Barcode
  dispensing:
    auto: 자동
    first: 처음
    medicine: 제품 번호 연구
    realMedicine: Actual use of research products
    medicineName: 제품 명 연구
    otherMedicineCount: Other research products Count
    outVisit: 외부 방문
    reissue: 재발급
    replace: 교체
    retrieve: 검색하다
    cancel: Withdraw
    invalid: Do Not Attend the Visit
    recover: Recover Dispensation
    replaceMedicine: 교체된 제품 번호 연구
    room: room
    siteName: 센터 명칭
    siteNumber: 센터 번호
    subject: 피험자
    time: 작업 시간
    type: 작업 종류
    visit: 방문 명칭
    visit_number: 방문 번호
    visitSign: 외부 방문 발약
    visit_apply: 방문 신청
    out_visit_apply: 방문 신청
    reissue_apply: 신청서를 재발급 하다
    replace_apply: 교체 신청
    is_replace: Is Replaced IP number
    is_real: Is Actual use of IP
    operate_time: Dispensing Operate Time
  random_config:
    SubjectReplaceText: 라벨(본문 바꾸기)：
    accuracy: 인원수 정확도：
    attribute: 프로젝트 속성
    blind: 블라인드 여부：
    createBy: 생성자：
    createDate: 생성 시간：
    digit: 인원수 제한：
    dispensing: 발약 여부：
    export: 보고서 설정
    factor: 분층 요소：
    no: N
    group: 그룹：
    instituteLayered: Set country as stratification factor：
    isFreeze: 운행 운송 계산법은 각 상품을 격리하여 단품으로 계산하며 연구기관중 재고로 결정할 수 있음：
    isRandom: 센터에서 랜덤으로 번호를 받지 못하면 그룹에 입장 할 수 없습니다：
    list: 랜덤 목록：
    prefix: 접두어 사용 여부：
    random: 무작위 여부：
    random_design: 랜덤 설정
    ratio: 그룹 간 비율：
    report: Clinflash IRT는 랜덤 및 실험 제품 을 연구 하 다 공급 관리 시스템이며, 무작위, 발약 및 실험 제품
      을 연구 하 다을 공급 관리를 합니다. 이 보고서의 설정들은 프로젝트 속성과 무작위 설계, 치료 방안을 각 분야의 사람들이 신속하게 확인할
      수 있도록 하여, 프로젝트를 설정,승인하여 프로젝트 문서를 생성합니다.
    summary: 요약 서술
    table:
      code: 그룹 코드
      count: 랜덤 발약 수량
      label: 랜덤 발약 라벨
      medicine: 제품 을 연구 하 다명칭
      spec: 제품 을 연구 하 다 규격
    total: 전체 예시 수：
    treatment_design: 치료 방안
    yes: Y
    type: 랜덤 분류：
  room:
    history:
      room: history room
      time: time
      user: user
    project: project
  unblinding:
    remark: Unblinding Remark
    reason: Unblinding Reason
    reason_mark: Unblinding Remark Mark
    operator: Unblinding Operator
    operate_time: Unblinding Operate Time
  user:
    name: Name
    email: Email
    role: Role
    create_time: Created Time
file_emtpy: 파일 목록이 비어 있음
history:
  dispensing:
    cancel: 【발약철회】 철회 사유：{{.reason}}
    dispensing: 【발약】 제품 을 연구 하 다번호：{{.medicine}}
    dispensing-other: 【발약】 미번호 제품 을 연구 하 다명칭 수량：{{.medicine}}
    dispensing-with-other: 【dispensing】 {{.label}}：{{.subject}}    research products number：{{.medicine}}, other research products：{{.other_medicine}}
    dispensing-with-other-reason: 【Outside visit Dispensing】 {{.label}}：{{.subject}}     research products number：{{.medicine}}, other research products：{{.other_medicine}},reason：{{.reason}}
    dispensingVisit: 【외부 방문 발약】 제품 을 연구 하 다번호：{{.medicine}}，이유:{{.reason}}
    dispensingVisit-other: 【[외부 방문 발약】 미번호 제품 을 연구 하 다명칭 수량：{{.medicine}}，이유:{{.reason}}

    dispensing-new: 【발약】 제품 을 연구 하 다번호：{{.medicine}} remark:{{.remark}}
    dispensing-other-new: 【발약】 미번호 제품 을 연구 하 다명칭 수량：{{.medicine}}  remark:{{.remark}}
    dispensing-with-other-new: 【dispensing】 {{.label}}：{{.subject}}    research products number：{{.medicine}}, other research products：{{.other_medicine}}  remark:{{.remark}}
    dispensing-with-other-reason-new: 【Outside visit Dispensing】 {{.label}}：{{.subject}}     research products number：{{.medicine}}, other research products：{{.other_medicine}},reason：{{.reason}}  remark:{{.remark}}
    dispensingVisit-new: 【외부 방문 발약】 제품 을 연구 하 다번호：{{.medicine}}，이유:{{.reason}}  remark:{{.remark}}
    dispensingVisit-other-new: 【[외부 방문 발약】 미번호 제품 을 연구 하 다명칭 수량：{{.medicine}}，이유:{{.reason}}  remark:{{.remark}}

    dtp-dispensing: 【발약】 제품 을 연구 하 다번호：{{.medicine}}
    dtp-dispensing-other: 【발약】 미번호 제품 을 연구 하 다명칭 수량：{{.medicine}}
    dtp-dispensing-with-other: 【dispensing】 {{.label}}：{{.subject}}    research products number：{{.medicine}}, other research products：{{.other_medicine}}
    dtp-dispensing-with-other-reason: 【Outside visit Dispensing】 {{.label}}：{{.subject}}     research products number：{{.medicine}}, other research products：{{.other_medicine}},reason：{{.reason}}
    dtp-dispensingVisit: 【외부 방문 발약】 제품 을 연구 하 다번호：{{.medicine}}，이유:{{.reason}}
    dtp-dispensingVisit-other: 【[외부 방문 발약】 미번호 제품 을 연구 하 다명칭 수량：{{.medicine}}，이유:{{.reason}}
    dtp-reissue: 【재발급】 제품 을 연구 하 다번호：{{.medicine}}
    dtp-reissue-other: 【reissue dispensing】{{.label}}：{{.subject}}    other medicine (count)：{{.medicine}}
    dtp-reissue-with-other: 【reissue dispensing】{{.label}}：{{.subject}}    research products：{{.medicine}}, other research products (count)：{{.other_medicine}}

    replace-logistics: 【替换】{{.label}}：{{.subject}}，随机号：{{.randomNumber}}， 访视：{{.visit}}，研究产品：{{.medicine}}，订单编号：{{.order}}，发药方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}， 备注：{{.remark}}。
    dispensing-logistics: 【发药】{{.label}}：{{.subject}}，随机号：{{.randomNumber}}， 访视：{{.visit}}，研究产品：{{.medicine}}，订单编号：{{.order}}，发药方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}， 备注：{{.remark}}。
    dispensingVisit-logistics: 【计划外发药】{{.label}}：{{.subject}}，随机号：{{.randomNumber}}， 访视：{{.visit}}，研究产品：{{.medicine}}，订单编号：{{.order}}，发药方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}， 备注：{{.remark}}, 原因，{{.reason}}。
    reissue-with-logistics: 【补发】{{.label}}：{{.subject}}，随机号：{{.randomNumber}}， 访视：{{.visit}}，研究产品：{{.medicine}}，订单编号：{{.order}}，发药方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}， 备注：{{.remark}}。

    invalid: 【non-participation】{{.label}}：{{.subject}} reason:{{.remark}}
    register: 【Registration of research products actually used】 Research products delivery system：{{.medicine}}, Actual use of research products：{{.real_medicine}}
    reissue: 【재발급】 제품 을 연구 하 다번호：{{.medicine}}, remark：{{.remark}}
    reissue-other: 【reissue dispensing】{{.label}}：{{.subject}}    other medicine (count)：{{.medicine}}, remark：{{.remark}}
    reissue-with-other: 【reissue dispensing】{{.label}}：{{.subject}}    research products：{{.medicine}}, other research products (count)：{{.other_medicine}}, remark：{{.remark}}
    replace: 【발약교체】 교체 사유：{{.reason}} ，제품 을 연구 하 다 번호를 교체：{{.medicine}}, 교체된 제품 번호 연구{{.beReplaceMedicine}}
    retrieval: 【retrieval】{{.label}}：{{.subject}}    Retrieved medicine：{{.medicine}}
    retrieval-order: 【retrieval】{{.label}}:{{.subject}}    Retrieved IP:{{.medicine}} reason:order close or cancel
    send-type-0: 中心（中心库存）
    send-type-1: 中心（直接寄送受试者）
    send-type-2: 库房（直接寄送受试者）
  medicine:
    expired: 【제품 을 연구 하 다 기한 만료】 시스템 자동 만료됨 , 현재 기한이 만료된 제품 을 연구 하 다 번호：{{.packNumber}}
    freeze: 【격리 제품 을 연구 하 다】 격리 원인：{{.reason}}，현재 격리 제품 을 연구 하 다 번호：{{.packNumber}}
    freeze-new: Research product 【isolated】，isolation number：{{.freezeNumber}}，research product number：{{.packNumber}}，reason：{{.reason}}
    otherFreeze: Research product [isolated], isolation No.：{{.freezeNumber}}, reason：{{.freezeReason}}, name of current isolated research product：{{.name}}, batch No.：{{.batch}}, expiry date：{{.expireDate}}, isolation：{{.freezeCount}}.
    lost: 【분실/폐기 제품 을 연구 하 다】 분실/ 폐기 사유：{{.reason}}，현재 분실/폐기 제품 을 연구 하 다 번호：{{.packNumber}}
    release: 【격리 해제 제품 을 연구 하 다】 격리 해제 원인：{{.reason}}，현재 격리 해제 제품 을 연구 하 다 번호：{{.packNumber}}
    releaseLost: 【격리 해제 분실/폐기 제품 을 연구 하 다】 분실/ 폐기 사유：{{.reason}}，현재 분실/폐기 제품 을 연구 하 다 번호：{{.packNumber}}
    otherRelease: The reason for isolation is:{{.reason}}，name of current isolated research product：{{.name}}, batch No.：{{.batch}}, expiry date：{{.expireDate}}, isolation：{{.count}}.
    otherReleaseLost: Decontamination of lost / invalid research products is:{{.reason}},name of current lost / invalid research product：{{.name}}, batch No.：{{.batch}}, expiry date：{{.expireDate}}, isolation：{{.count}}.
    replace: 【발약 교체 제품 을 연구 하 다】 폐기 설정 원인：{{.reason}}，현재 폐기 제품 을 연구 하 다 번호：{{.packNumber}}
    use: 【사용 가능 제품 을 연구 하 다로 설정】 사용 가능 설정 원인：{{.reason}}，현재 사용 가능한 제품 을 연구 하 다 번호：{{.packNumber}}
    replace-dtp: "연구 제품 교체, 교체 전: {{.beReplace}}, 교체 후: {{.replace}}"
    apply: 연구 제품이 신청되었습니다
    shipped: 연구 제품 출하
    receive: 연구 제품 수령
    used: 사용 연구 제품
    in-order: IP to be confirmed, Subject No.：{{.subject}}，IP：{{.medicine}}.
    confrim: IP be confirmed
    dispensing-used: IP used，Subject No.：{{.subject}},products number：{{.medicine}}
    use-dtp: 연구 제품을 사용할 수 있습니다.
    cancel-dtp: 연구용 제품 중단
    drugFreeze:
      drugFreeze: IP 【isolated】,quarantine number:{{.freezeNumber}}, research product number:{{.medicines}}, reason:{{.freezeReason}}
      otherDrugFreeze: IP 【isolated】，quarantine number：{{.freezeNumber}}，name of current isolated research product/batch No/expiry date/count：{{.otherMedicines}}，reason：{{.freezeReason}}
      allDrugFreeze: IP 【isolated】，quarantine number：{{.freezeNumber}}，reason：{{.freezeReason}}，name of current isolated research product/batch No/expiry date/count：{{.otherMedicines}}，research product number：{{.medicines}}
      lost: IP 【lost/voided】,quarantine number:{{.freezeNumber}}, research product number:{{.medicines}} , reason:{{.freezeReason}}
      release: IP 【Batch quarantined】,quarantine number:{{.freezeNumber}}, research product number:{{.medicines}} , reason:{{.freezeReason}}
      otherLost:  IP 【lost/voided】，quarantine number：{{.freezeNumber}}，name of current lost/voided research product/batch No/expiry date/count：{{.otherMedicines}}，reason：{{.freezeReason}}
      otherRelease: IP 【Batch quarantined】，quarantine number：{{.freezeNumber}}，name of current batch quarantined research product/batch No/expiry date/count：{{.otherMedicines}}，reason：{{.freezeReason}}
      allLost:  IP 【lost/voided】，quarantine number：{{.freezeNumber}}，reason：{{.freezeReason}}，name of current lost/voided research product/batch No/expiry date/count：{{.otherMedicines}}，research product number：{{.medicines}}
      allRelease: IP 【Batch quarantined】，quarantine number：{{.freezeNumber}}，reason：{{.freezeReason}}，name of current batch quarantined research product/batch No/expiry date/count：{{.otherMedicines}}，research product number：{{.medicines}}
  order:
    cancel-new: Order No.:{{.orderNumber}},order【Canceled】, reason for cancellation:{{.reason}}
    confrim-new: Order No.:{{.orderNumber}},order【Confirmed】
    create-new: Order No.:{{.orderNumber}},order【To be confirmed】
    lost-new: Order No.:{{.orderNumber}},order【Lost】, reason for loss:{{.reason}}
    receive-new: Order No.:{{.orderNumber}},order【Received】
    send-new: Order No.:{{.orderNumber}},order【In Delivery】
    logistics-other: 【物流信息更新】物流供应商：{{.logistics}}，其他物流：{{.other}}，物流单号：{{.number}}。
    logistics: 【物流信息更新】物流供应商：{{.logistics}}，物流单号：{{.number}}。
    send-with-logistics: 订单编号：{{.orderNumber}}，订单【已运送】，物流供应商：{{.logistics}}，其他物流：{{.other}}，物流单号：{{.number}}。
    cancel-dtp: 주문 번호:{{.orderNumber}}, 주문 [종료됨], 해지 사유 {{.reason}}
    cancel: Cancel the order, the order has been cancelled, reason for cancellation:{{.reason}}
    confirm-task: Send order confirmation task, order requested
    confirm-task-finish: The order confirmation task has been completed and the order is being shipped
    confrim: Confirm order, order requested
    create: Create order, order to be confirmed
    close-with-dtp: Order No.:{{.orderNumber}}, order 【closed】,Reason:All IP are replaced.
    lost: Order lost, order lost, reason for loss:{{.reason}}
    receive: Order received
    receive-task-confrim: Send research product receive task, order requested
    receive-task-finish: The task of receiving research products has been completed, and the order has been received
    receive-task-send: Send research product receiving task, Order Shipping
    send: Shipping order
    apply: 주문 작성, 주문 요청, 주목:{{.remark}}
    approval: Order No.:{{.orderNumber}}, order【Approved】
  project:
    cohort:
      add: 【Add Cohort/Rerandom】 name：{{.cohortName}}，capacity：{{.capacity}}，status：{{.state}}
      delete: 【Delete Cohort/Rerandom】 name：{{.cohortName}}s
      edcAdd: 【Add Cohort/Rerandom】 name：{{.cohortName}}，capacity：{{.capacity}}，status：{{.state}},factor：{{.factor}}
      edcEdit: 【Edit Cohort/Rerandom】 name：{{.cohortName}}，capacity：{{.capacity}}，status：{{.state}},factor：{{.factor}}
      edcRAdd: 【Add Cohort/Rerandom】 name：{{.cohortName}}，capacity：{{.capacity}}，status：{{.state}},factor：{{.factor}},upper stage ：{{.lastCohort}}
      edcREdit: 【Edit Cohort/Rerandom】 name：{{.cohortName}}，capacity：{{.capacity}}，status：{{.state}},factor：{{.factor}},upper stage ：{{.lastCohort}}
      edit: 【Edit Cohort/Rerandom】 name：{{.cohortName}}，capacity：{{.capacity}}，status：{{.state}}
      rAdd: 【Add Cohort/Rerandom】 name：{{.cohortName}}，capacity：{{.capacity}}，status：{{.state}},upper stage ：{{.lastCohort}}
      rEdit: 【Edit Cohort/Rerandom】 name：{{.cohortName}}，capacity：{{.capacity}}，status：{{.state}},upper stage ：{{.lastCohort}}
    drugConfigure:
      add: 【신규 제품 을 연구 하 다 설정】 그룹：{{.group}}，제품 을 연구 하 다 설정：{{.drugConfigures}}
      delete: 【제품 을 연구 하 다 설정 삭제】 그룹：{{.group}}，제품 을 연구 하 다 설정：{{.drugConfigures}}
      edit: 【제품 을 연구 하 다 설정 편집】 그룹：{{.group}}，제품 을 연구 하 다 설정：{{.drugConfigures}}
    env:
      add: 【환경생성】 신규{{.env}}환경
    info:
      add: 【프로젝트 추가】 프로젝트 넘버：{{.projectNum}}，프로젝트 명칭：{{.projectName}}，신청 기관：{{.biddingUnit}}，프로젝트 설명：{{.projectDesc}}
      edit: 【수정항목】 프로젝트 넘버：{{.projectNum}}，프로젝트 명칭：{{.projectName}}，신청 기관：{{.biddingUnit}}，프로젝트 설명：{{.projectDesc}}
    medicine:
      batch: 【차수관리】 창고 업데이트 차수 번호：{{.batch}}，유효기간：{{.expireDate}}，상태：{{.status}}，업데이트 차수 번호：{{.batchUpdate}}，갱신 유효기간：{{.expireDateUpdate}}
      upload: 【제품 을 연구 하 다 업로드】 제품 명 연구：{{.drugName}}，제품 을 연구 하 다 업로드 수량：{{.count}}
  randomization:
    attribute: 속성 설정： 무작위 여부：{{.random}}, Show random number：{{.isRandomNumber}}, 발약 여부：{{.dispensing}}, 블라인드 여부：{{.blind}}, 접두사 사용 여부：{{.prefix}}, 접두사로 센터를 설정하시겠습니까：{{.sitePrefix}}, 접두사 연결 부호：{{.prefixConnector}}, 피험자 번호의 다른 접두사：{{.otherPrefix}}, 접두사 본문：{{.otherPrefixText}}, 라벨(본문 바꾸기)：{{.subjectReplaceText}}, 인원수 정확도 (1:같거나 작음/ 2:같음)：{{.accuracy}}, 인원수 제한：{{.digit}}, 운행 운송 계산법은 각 상품을 격리하여 단품으로 계산하며 연구기관중 재고로 결정할 수 있음：{{.isFreeze}}, EDC 연결 제품 을 연구 하 다 설정 태그：{{.edcDrugConfigLabel}}, Is the segment random：{{.segment}}{{.segmentLength}}
    block:
      distributionFactor: 【随机分段】 【{{.name}}】 区组：{{.block}} 分配分层：{{.valueSite}}
      distributionSite: 【随机分段】 【{{.name}}】 区组：{{.block}} 分配中心：{{.valueSite}}
      generate: 【그룹 무작위】【랜덤 번호 생성】명칭：{{.name}}，초기 번호：{{.initialValue}},그룹별 설정 그룹별(가중치)：{{.groupRatio}},구간 그룹 설정 그룹별 길이(그룹 수)：{{.blockNumber}},분층 요소：{{.factors}}，번호 길이：{{.numberLength}}，무작위 수：{{.seed}}，번호 접두사：{{.numberPrefix}}，생성 수량：{{.numberText}}
      upload: 【그룹 무작위】【무작위 번호 업로드】명칭：{{.name}}，분층 요소：{{.factors}}，수용 가능한 그룹 크기：{{.blockSize}},업로드 수량：{{.numberText}}
    config:
      block:
        distributionFactor: 【Random segmentation】 【{{.name}}】 Block：{{.block}} Distribution factor：{{.valueSite}}
        distributionSite: 【Random segmentation】 【{{.name}}】 Block：{{.block}}  Distribution site：{{.valueSite}}
        generate: 【그룹 무작위】【랜덤 번호 생성】명칭：{{.name}}，초기 번호：{{.initialValue}},그룹별 설정 그룹별(가중치)：{{.groupRatio}},구간 그룹 설정 그룹별 길이(그룹 수)：{{.blockNumber}},분층 요소：{{.factors}}，번호 길이：{{.numberLength}}，무작위 수：{{.seed}}，번호 접두사：{{.numberPrefix}}，생성 수량：{{.numberText}}
        upload: 【그룹 무작위】【무작위 번호 업로드】명칭：{{.name}}，분층 요소：{{.factors}}，수용 가능한 그룹 크기：{{.blockSize}},업로드 수량：{{.numberText}}
      factor:
        add: 【Factor Add】 Field Code：{{.number}}, Name：{{.label}} , Control type：{{ .type}}, Options： {{.options}}
        addEDC: 【Factor Add】 Field Code：{{.number}}, Name：{{.label}} ,Variable name：{{ .name}}, Control type：{{ .type}}, Options： {{.options}}
        clean: 【Empty other layers】 【{{ .name}}】 Block[{{ .block}}] Empty other layers
        countryEnable: 【Set country as stratification factor】 Enable
        delete: 【Factor Delete】 Field Code：{{.oldNumber}}, Name：{{.oldLabel}} , Control type：{{ .oldType}}, Options {{.oldOptions}}
        deleteEDC: 【Factor Delete】 Field Code：{{.oldNumber}}, Name：{{.oldLabel}} ,Variable name：{{ .oldName}}, Control type：{{ .oldType}}, Options {{.oldOptions}}
        disableCountryLayer: 【Set country as stratification factor】 Disable
        disableLayer: 【Set stratification factor】 Disable
        disableSiteLayer: 【Set site as stratification factor】 Disable
        edit: 【Factor Edit】 Replace Field Code：{{.oldNumber}}, Name：{{.oldLabel}} , Control type：{{ .oldType}}, Options {{.oldOptions}} ）with Field Code：{{.number}}, Name：{{.label}} ,Variable name：{{ .name}}, Control type：{{ .type}}, Options： {{.options}}
        editEDC: 【Factor Edit】 Replace Field Code：{{.oldNumber}}, Name：{{.oldLabel}} ,Variable name：{{ .oldName}}, Control type：{{ .oldType}}, Options {{.oldOptions}} ）with Field Code：{{.number}}, Name：{{.label}} ,Variable name：{{ .name}}, Control type：{{ .type}}, Options： {{.options}}
        number: 【Set layers number】 【{{ .name}}】 layers：{{.factor}}, Expected Number:{{.estimateNumber}}, Alert number:{{.warnNumber}}
        siteEnable: 【Set site as stratification factor】 Enable
      form:
        add: 【Form Add】 Field name：{{.name}} Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}}
        addEDC: 【Form Add】 Field name：{{.name}} Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}}
        addOption: 【Form Add】 Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}}  Options：{{.options}}
        addOptionEDC: 【Form Add】 Field name：{{.name}} Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}}  Options：{{.options}}
        clean: 【Clean other factor】 【{{ .name}}】 Block {{ .block}} clean other factor
        delete: 【Form Delete】 Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}}
        deleteEDC: 【Form Delete】 Field name：{{.name}} Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}}
        deleteOption: 【Form Delete】Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}} Options：{{.options}}
        deleteOptionEDC: 【Form Delete】Field name：{{.name}} Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}} Options：{{.options}}
        edit: 【Form Edit】 (Variable name：{{.oldLabel}}, Enable modification：{{ .oldModifiable}}, Control type：{{.oldType}} ) replace (Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}} )
        editEDC: 【Form Edit】 (Field name：{{.oldName}} Variable name：{{.oldLabel}}, Enable modification：{{ .oldModifiable}}, Control type：{{.oldType}} ) replace  (Field name：{{.name}} Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}} )
        editOption: 【Form Edit】 (Variable name：{{.oldLabel}}, Enable modification：{{ .oldModifiable}}, Control type：{{.oldType}} )    replace (Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}})
        editOptionEDC: 【Form Edit】 (Field name：{{.oldName}} Variable name：{{.oldLabel}}, Enable modification：{{ .oldModifiable}}, Control type：{{.oldType}})    replace (Field name：{{.name}} Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}})
        editOptionend: 【Form Edit】 (Variable name：{{.oldLabel}}, Enable modification：{{ .oldModifiable}}, Control type：{{.oldType}} )    replace (Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}} Options：{{.options}})
        editOptionendEDC: 【Form Edit】 (Field name：{{.oldName}} Variable name：{{.oldLabel}}, Enable modification：{{ .oldModifiable}}, Control type：{{.oldType}} )    replace (Field name：{{.name}} Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}} Options：{{.oldOptions}})
        editOptionstart: 【Form Edit】 (Variable name：{{.oldLabel}}, Enable modification：{{ .oldModifiable}}, Control type：{{.oldType}} Options：{{.oldOptions}})    replace (Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}})
        editOptionstartEDC: 【Form Edit】 (Field name：{{.oldName}} Variable name：{{.oldLabel}}, Enable modification：{{ .oldModifiable}}, Control type：{{.oldType}} Options：{{.oldOptions}})    replace (Field name：{{.name}} Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}})
        editOptionstartend: 【Form Edit】 (Variable name：{{.oldLabel}}, Enable modification：{{ .oldModifiable}}, Control type：{{.oldType}} Options：{{.oldOptions}})    replace ( Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}} Options：{{.options}})
        editOptionstartendEDC: 【Form Edit】 (Field name：{{.oldName}} Variable name：{{.oldLabel}}, Enable modification：{{ .oldModifiable}}, Control type：{{.oldType}} Options：{{.oldOptions}})    replace (Field name：{{.name}} Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}} Options：{{.options}})
        editend: 【Form Edit】 (Variable name：{{.oldLabel}}, Enable modification：{{ .oldModifiable}}, Control type：{{.oldType}} ) replace  (Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}} Options：{{.options}})
        editendEDC: 【Form Edit】 (Field name：{{.oldName}} Variable name：{{.oldLabel}}, Enable modification：{{ .oldModifiable}}, Control type：{{.oldType}} )  (Field name：{{.name}} Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}} Options：{{.options}})
        editstart: 【Form Edit】 (Variable name：{{.oldLabel}}, Enable modification：{{ .oldModifiable}}, Control type：{{.oldType}} Options：{{.oldOptions}}) replace  (Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}} )
        editstartEDC: 【Form Edit】 (Field name：{{.oldName}} Variable name：{{.oldLabel}}, Enable modification：{{ .oldModifiable}}, Control type：{{.oldType}} Options：{{.oldOptions}}) replace  (Field name：{{.name}} Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}} )
        editstartend: 【Form Edit】 (Variable name：{{.oldLabel}}, Enable modification：{{ .oldModifiable}}, Control type：{{.oldType}} Options：{{.oldOptions}}) replace  ( Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}} Options：{{.options}})
        editstartendEDC: 【Form Edit】 (Field name：{{.oldName}} Variable name：{{.oldLabel}}, Enable modification：{{ .oldModifiable}}, Control type：{{.oldType}} Options：{{.oldOptions}}) replace  (Field name：{{.name}} Variable name：{{.label}}, Enable modification：{{ .modifiable}}, Control type：{{.type}} Options：{{.options}})
      group:
        add: 【Treatment Group Add】 Group Code：{{.number}}, Name：{{.name}}
        delete: 【Treatment Group Delete】  Group Code：{{.number}}, Name：{{.name}}
        edit: 【Treatment Group Edit】 Replace   Group Code：{{.oldNumber}}, Name：{{.oldName}} with Group Code：{{.number}}, Name：{{.name}}
      list:
        disableStatus: 【상태조절】 【{{.name}}】 무작위 비활성화상태
        enableStatus: 【상태조절】 【{{.name}}】 무작위 사용상태
        invalid: 【폐기】 【{{.name}}】 랜덤 차트를 이미 폐기함
      minimize: 【랜덤 최소화】 명칭：{{.name}}，초기 번호：{{.initialValue}},그룹별 설정 그룹별(가중치)：{{.groupRatio}}, 분층 요소 분층(가중치)：{{.factors}}，편향 확률：{{.probability}}，전체 예시 수：{{.total}}，번호 길이：{{.numberLength}}，무작위 수：{{.seed}}，번호 접두사：{{.numberPrefix}}
      typeBlock: 【Random type adjustment】 Enable Block randomization
      typeMin: 【Random type adjustment】 Enable Minimization random
    factor:
      clean: 【Empty other layers】 【{{ .name}}】 Block[{{ .block}}] Empty other layers
      number: 【Set layers number】 【{{ .name}}】 layers：{{.factor}}, Expected Number:{{.estimateNumber}}, Alert number:{{.warnNumber}}
    list:
      disableStatus: 【상태조절】 무작위 비활성화상태
      enableStatus: 【상태조절】 무작위 사용상태
      invalid: 【폐기】 랜덤 차트를 이미 폐기함
      site: 【Edit】 Binding Center:{{.siteName}}
    minimize: 【랜덤 최소화】 명칭：{{.name}}，초기 번호：{{.initialValue}},그룹별 설정 그룹별(가중치)：{{.groupRatio}}, 분층 요소 분층(가중치)：{{.factors}}，편향 확률：{{.probability}}，전체 예시 수：{{.total}}，번호 길이：{{.numberLength}}，무작위 수：{{.seed}}，번호 접두사：{{.numberPrefix}}
  subject:
    add: 【추가】 {{.content}}
    at-random-add: 【추가】Stage：{{.stage}}，{{.content}}
    beReplaced: 【교체됨】 이미 실험 완료된 피험자：{{.name}} 교체
    replaced-new: 【Replacement of subjects】original subjects:{{.name}},original random number:{{.beReplaceRandomNumber}}, replacement subjects:{{.replaceName}}, replacement random number:{{.replaceRandomNumber}}
    pvUnblinding: 【PV블라인드 테스트】 피험자：{{.name}} 이미 PV블라인드 테스트 됨, 테스트 원인:{{.reason}}
    remark-pvUnblinding: PvUnblinding subject：{{.name}} PvUnblinding,Reasons for uncovering blindness:{{.reason}},Remark：{{.remark}}
    random: 【랜덤】 피험자：{{.name}} 랜덤, 랜덤 번호 선택됨：{{.randomNumber}}
    randomNoNumber: Random subject：{{.name}} Its already random
    replaced: 【교체】 현재 피험자는 이미 피험된 피험자로 교체됨:{{.name}} 무작위 번호：{{.randomNumber}}
    signOut: 【종료】 피험자：{{.name}} 종료됨 종료 원인:{{.reason}}
    unblinding: 【긴급 블라인드 테스트】 피험자：{{.name}} 긴급 블라인드 테스트 됨 테스트 원인:{{.reasonStr}} {{.reason}},Notify the sponsor:{{.isSponsor}}{{.remark}}
    unblinding-success: 에 눈가림 해제(긴급) 성공
    unblinding-application: "블라인드 해제 신청(긴급), 승인 번호: {{.approvalNumber}}, 상태 승인 보류"
    unblinding-application-pv: "Applied for unblinding (pv), approval number: {{.approvalNumber}}, status pending approval"
    unblinding-approval-gree: "눈가림 해제(긴급) 승인됨, 승인 번호: {{.approvalNumber}}, 상태 통과"
    unblinding-approval-reject: "눈가림 해제(긴급) 승인됨, 승인 번호: {{.approvalNumber}}, 상태 거부됨, 이유: {{.reason}}"
    unblinding-approval-reject-pv: "Unblinding (pv) Approved, Approval Number: {{.approvalNumber}}, Status Rejected, Reason: {{.reason}}"
    update: 【수정】 {{.content}}
    updateSubjectNo: Update Subject：{{.oldSubjectNo}},Change to：{{.shortname}}
  supply-plan:
    add: 【신규 공급 계획]】 계획 명칭：{{.name}}，계획 설명：{{.description}}
    update: 【공급계획 수정】 계획 명칭：{{.name}}，계획 설명：{{.description}}
  supply-plan-medicine:
    add: 【신규 제품 을 연구 하 다 공급계획 설정】 제품 을 연구 하 다：{{.medicineName}}，초기 발약량：{{.initSupply}} ，제품 을 연구 하 다 경계치：{{.warning}},최대 버퍼：{{.buffer}},재공급량：{{.secondSupply}},배송하지 않는 일수：{{.unDistributionDate}},방출하지 않는 일수：{{.unProvideDate}},유효기간 알림：{{.validityReminder}},자동 조제：{{.autoSupply}},자동 조제량：{{.autoSupplySize}},  보충 방식：{{.supplyMode}}。
    update: 【제품 을 연구 하 다 공급계획 설정 수정】 제품 을 연구 하 다：{{.medicineName}}，초기 발약량：{{.initSupply}} ，제품 을 연구 하 다 경계치：{{.warning}},최대 버퍼：{{.buffer}},재공급량：{{.secondSupply}},배송하지 않는 일수：{{.unDistributionDate}},방출하지 않는 일수：{{.unProvideDate}},유효기간 알림：{{.validityReminder}},자동 조제：{{.autoSupply}},자동 조제량：{{.autoSupplySize}},  보충 방식:：{{.supplyMode}}。
medicine:
  autoSupplySize1: 최대 완충량
  autoSupplySize2: 재공급량
  errorSupplyMode: 오류! 보충 방식이 일치하지 않습니다。
  expire_title: Clinflash IRT  제품 을 연구 하 다 유효기간 알림
  freeze:
    title: Clinflash IRT {{.projectNumber}} {{.envName}} 의 제품 을 연구 하 다 격리 알림 {{.instituteInfo}} {{.freezeNumber}}
  status:
    available: 사용 가능
    delivered: 확인 
    destroy: 소각됨
    expired: 기한이 지났다
    inStorage: 입고 중
    lose: 분실/ 폐기
    quarantine: 격리됨
    receive: 이미 약을 수령함
    return: 반품됨
    sending: 배송됨
    stockPending: 입고 대기 중
    toBeConfirmed: 확인을 기다리다
    toBeWarehoused: To be warehoused
    transit: 배송됨
    used: 사용됨
    apply: 신청됨
    locked: Locked
  supplyMode1: 모든 제품 을 연구 하 다 보충
  supplyMode2: 단품 제품 을 연구 하 다 보충
  supplyMode3: 모든 제품 을 연구 하 다 보충 + 1개 랜덤제품 을 연구 하 다 번호
  supplyMode4: 단품 제품 을 연구 하 다 보충 + 1개 랜덤제품 을 연구 하 다 번호
medicineOrder_download_batchNumber: 화물 넘버
medicineOrder_download_cancelDate: 취소일자
medicineOrder_download_cancelUser: 취소자
medicineOrder_download_count: 제품 을 연구 하 다 수량
medicineOrder_download_createDate: 만든 날짜
medicineOrder_download_createUser: 만든이
medicineOrder_download_expiredDate: 유효기간
medicineOrder_download_fileName: 주문 차트
medicineOrder_download_medicineNumber: 제품 번호 연구
medicineOrder_download_number: 주문 번호
medicineOrder_download_orderInfo: 주문 정보 상세보기
medicineOrder_download_other: 기타
medicineOrder_download_receiveDate: 접수일자
medicineOrder_download_expectedArrivalTime: Expected Arrival Time
medicineOrder_download_actualReceiptTime: Actual Receipt Time
medicineOrder_download_receiveInstitute: 접수 단위
medicineOrder_download_receiveUser: 수신인
medicineOrder_download_sendInstitute: 발송 단위
medicineOrder_download_status: 주문 상태
medicineOrder_download_cancelReason: 취소 이유
medicineOrder_download_confirmUser: 확인인
medicineOrder_download_confirmDate: 확인 날짜
medicineOrder_download_closeUser: 종료 방법
medicineOrder_download_closeDate: 종료 날짜
medicineOrder_download_closeReason: 종료 이유
medicineOrder_download_sendUser: 전송 방법
medicineOrder_download_sendDate: 보낸 날짜
medicineOrder_download_lostUser: 길 잃은 사람
medicineOrder_download_lostDate: 잃어버린 날짜
medicineOrder_download_lostReason: 분실의 원인
medicineOrder_download_endUser: 종료 시간
medicineOrder_download_endDate: 종료 날짜
medicineOrder_download_endReason: 종료 원인
medicineOrder_download_supplier: 공급자
medicineOrder_download_supplierOther: 공급자 기타
medicineOrder_download_supplierNumber: 공급업체 번호
medicine_batch_number: 차수 번호
medicine_download_batch: 차수 번호
medicine_download_depot_name: 창고 통계 차트
medicine_download_expiredDate: 유효기간
medicine_download_location: 위치
medicine_download_name: 제품 명 연구
medicine_download_number: 제품 번호 연구
medicine_download_orderNumber: 오더 번호
medicine_download_site: 센터 명칭
medicine_download_site_name: 센터 약국 차트
medicine_download_dtp_sku: 연구 제품 단품 보고서
medicine_download_status: 상태
medicine_download_storehouse: 창고 명칭
medicine_download_freeze_reason: 동결 원인
medicine_download_freeze_operator: 동결 연산자
medicine_download_freeze_time: 동결 시간
medicine_download_release_reason: 석방 원인
medicine_download_release_operator: 운영자 석방
medicine_download_release_time: 석방 시간
medicine_download_lost_reason: 분실의 원인
medicine_download_lost_operator: 잃어버린 운영자
medicine_download_lost_time: 손실된 시간
medicine_download_use_reason: 사용 이유
medicine_download_use_operator: 연산자 사용
medicine_download_use_time: 사용 시간
medicine_download_site_number: 사이트 번호
medicine_duplicated: 제품 을 연구 하 다이 이미 존재합니다
medicine_duplicated_number: 동일한 제품 을 연구 하 다 번호를 중복하여 업로드 하지 마십시오
medicine_duplicated_package_number: The package number already exists
medicine_duplicated_serial_package_number: The package sequence number already exists
medicine_expiration_date: 유효기간
medicine_list_download_name: 제품 을 연구 하 다 목록
medicine_name: 제품 명 연구
medicine_not_exist: The research products does not exist
medicine_number: 제품 번호 연구
medicine_code: Short Code

medicine_examine_uccess: 审核通过
medicine_examine_fail: 审核失败
medicine_examine_update: 修改
medicine_examine_release: 放行

medicine_barcode_code: IP-Barcode
medicine_barcode_code_short:  Barcode & Short Code
medicine_packlist_upload_check: 제품 을 연구 하 다 번호가 존재하지 않습니다, 먼저 제품 을 연구 하 다 번호를 업로드
  해주세요
medicine_packlist_upload_firstPack: 1층 포장은 비워둘 수 없습니다
medicine_status: 상태
minimize_bias_probability_tips: 편차는 0이 될 수 없습니다
minimize_layered_tips: 그룹별 분층 편차를 가져오지 못했습니다
project_edc_irt_return: Subject number prefix rules is inconsistent with EDC configuration, please reconfirm.
operator:
  people: 작업자
  reason: 원인
  time: 시간
order:
  automatic_error: <p>프로젝트 번호:{{.projectNumber}}</p> <p>프로젝트 명칭:{{.projectName}}</p> <p>프로젝트 환경:{{.envName}}</p> <p>운송시작지:{{.start}}</p> <p>목적지:{{.destination}}</p> <p>약품 자동 주문 생성 실패, 조제량과 창고에 있는 약품 수량이 일치하는지 확인하세요.</p>
  automatic_error_title: Clinflash IRT  {{.projectNumber}}({{.envName}}) 주문 자동 생성 실패 알림 {{.destination}}
  automatic_success_title: Clinflash IRT {{.projectNumber}}({{.envName}}) 주문서 자동 알림 만들기 {{.destination}} {{.orderNumber}}
  cancel: <p>프로젝트 넘버:{{.projectNumber}}</p>
    <p>프로젝트 명칭:{{.projectName}}</p>
    <p>프로젝트 환경:{{.envName}}</p>
    <p>운송시작지:{{.start}}</p>
    <p>목적지:{{.destination}}</p>
    <p>주주문번호:{{.orderNumber}}.</p>
    <p>취소 사유:{{.reason}}.</p>
    <p>문 취소되었습니다</p>
  cancel_dtp: "<p>프로젝트 넘버:{{.projectNumber}}</p>
    <p>프로젝트 명칭:{{.projectName}}</p>
    <p>프로젝트 환경:{{.envName}}</p>
    <p>주제 번호:{{.subject}}</p>
    <p>그룹:{{.group}}</p>
    <p>난수:{{.randomNumber}}</p>
     <p>방문 이름:{{.visit}}</p>
    <p>주주문번호:{{.orderNumber}}.</p>
    <p>취소 사유:{{.reason}}.</p>
    <p>문 취소되었습니다</p>"
  cancel-logistics: "<p>Project Number:{{.projectNumber}}</p>
            <p>Project Name:{{.projectName}}</p>
            <p>Project Environment:{{.envName}}</p>
            <p>Origin:{{.start}}</p>
            <p>Subject Number:{{.subject}}</p>
            <p>Visit Cycle:{{.visit}}</p>
            <p>Order Number:{{.orderNumber}}.</p>
            <p>Express logistics:{{.logistics}}</p>
            <p>Cancel Reason:{{.reason}}.</p>
            <p>This order has been cancelled.</p>"
  close: "<p>Project Number:{{.projectNumber}}</p>
            <p>Project Name:{{.projectName}}</p>
            <p>Project Environment:{{.envName}}</p>
            <p>Origin:{{.start}}</p>
            <p>Destination:{{.destination}}</p>
            <p>Order Number:{{.orderNumber}}.</p>
            <p>Close Reason:{{.reason}}.</p>
            <p>This order has been closed.</p>"
  close-logistics: "<p>Project Number:{{.projectNumber}}</p>
            <p>Project Name:{{.projectName}}</p>
            <p>Project Environment:{{.envName}}</p>
            <p>Origin:{{.start}}</p>
            <p>Subject Number:{{.subject}}</p>
            <p>Visit Cycle:{{.visit}}</p>
            <p>Order Number:{{.orderNumber}}.</p>
            <p>Express logistics:{{.logistics}}</p>
            <p>Close Reason:{{.reason}}.</p>
            <p>This order has been closed.</p>"
  close_dtp: "<p>Project Number:{{.projectNumber}}</p>
            <p>Project Name:{{.projectName}}</p>
            <p>Project Environment:{{.envName}}</p>
            <p>Subject Number:{{.subject}}</p>
            <p>Visit Name:{{.visit}}</p>
            <p>Order Number:{{.orderNumber}}.</p>
            <p>Close Reason:{{.reason}}.</p>
            <p>This order has been closed.</p>"
  end: "<p>Project Number:{{.projectNumber}}</p>
            <p>Project Name:{{.projectName}}</p>
            <p>Project Environment:{{.envName}}</p>
            <p>Origin:{{.start}}</p>
            <p>Destination:{{.destination}}</p>
            <p>Order Number:{{.orderNumber}}.</p>
            <p>Terminate Reason:{{.reason}}</p>
            <p>This order has been terminated.</p>"
  end-logistics: "<p>Project Number:{{.projectNumber}}</p>
            <p>Project Name:{{.projectName}}</p>
            <p>Project Environment:{{.envName}}</p>
            <p>Origin:{{.start}}</p>
            <p>Subject Number:{{.subject}}</p>
            <p>Visit Cycle:{{.visit}}</p>
            <p>Order Number:{{.orderNumber}}.</p>
            <p>Terminate Reason:{{.reason}}</p>
            <p>Express logistics:{{.logistics}}</p>
            <p>This order has been terminated.</p>"
  cancel_title: Clinflash IRT {{.projectNumber}} {{.envName}} 주문 취소 알림 {{.destination}} {{.orderNumber}}
  cancel_title_dtp: Clinflash IRT {{.projectNumber}} {{.envName}} 주문 취소 알림 {{.subject}} {{.visit}} {{.orderNumber}}
  lost: "<p>프로젝트 넘버:{{.projectNumber}}</p>
  <p>프로젝트 명칭:{{.projectName}}</p>
  <p>프로젝트 환경:{{.envName}}</p>
  <p>운송시작지:{{.start}}</p>
  <p>목적지:{{.destination}}</p>
  <p>주문번호:{{.orderNumber}}</p>
  <p>{{.userName}}이 주문이 분실되었음을 확인했습니다.</p>"
  lost_title: Clinflash IRT {{.projectNumber}} {{.envName}} 주문 분실 알림 {{.destination}} {{.orderNumber}}
  no_automatic: <p>프로젝트 넘버:{{.projectNumber}}</p> <p>프로젝트 명칭:{{.projectName}}</p> <p>프로젝트 환경:{{.envName}}</p> <p>센터 번호:{{.siteNumber}}</p> <p>센터 명칭:{{.siteName}}</p> <p>약 재고량이 경계치보다 낮습니다, 가능한 빨리 보충해주세요.</p>
  no_automatic_success_title: Clinflash IRT {{.projectNumber}} {{.envName}} 주문 생성 알림 {{.destination}} {{.orderNumber}}
  medicine_order_title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Creation Notification {{.destination}} {{.orderNumber}}
  no_automatic_title: Clinflash IRT {{.projectNumber}}({{.envName}}) 제품 을 연구 하 다 경계 알림 {{.siteNumber}} {{.siteName}}
  over_title_depot: Clinflash IRT {{.projectNumber}}({{.envName}}) {{.siteName}} 주문 시간 초과 알림 {{.orderNumber}}
  over_title_site: Clinflash IRT {{.projectNumber}}({{.envName}}) {{.siteNumber}} {{.siteName}} 주문 시간 초과 알림 {{.orderNumber}}
  overtime_depot: <p>프로젝트 넘버:{{.projectNumber}}</p> <p>프로젝트 명칭:{{.projectName}}</p> <p>프로젝트 환경:{{.envName}}</p> <p>접수기관 명칭:{{.siteName}}</p> <p>주문 번호:{{.orderNumber}}</p> <p>수령을 확인하지 않은 주문이 있습니다, 현재 상태는{{.statusItem}},주문 생성 시간{{.generateDate}},확인해주세요.</p>
  overtime_site: <p>프로젝트 넘버:{{.projectNumber}}</p> <p>프로젝트 명칭:{{.projectName}}</p> <p>프로젝트 환경:{{.envName}}</p> <p>수신기관넘버:{{.siteNumber}}</p> <p>접수기관 명칭:{{.siteName}}</p> <p>주문 번호:{{.orderNumber}}</p> <p>수령을 확인하지 않은 주문이 있습니다, 현재 상태는{{.statusItem}},주문 생성 시간{{.generateDate}},확인해주세요.</p>
  receive: "<p>프로젝트 넘버:{{.projectNumber}}</p>
  <p>프로젝트 명칭:{{.projectName}}</p>
  <p>프로젝트 환경:{{.envName}}</p>
  <p>운송시작지:{{.start}}</p>
  <p>목적지:{{.destination}}</p>
  <p>주문번호:{{.orderNumber}}.</p>
  <p>{{.userName}}이 주문이 목적지에서 접수되었음을 확인했습니다.</p>"
  receive-logistics: "<p>Project number:{{.projectNumber}}</p>
            <p>Project Name:{{.projectName}}</p>
            <p>Project Environment:{{.envName}}</p>
            <p>Origin:{{.start}}</p>
            <p>Subject Number:{{.subject}}</p>
            <p>Visit Cycle:{{.visit}}</p>
            <p>Order Number:{{.orderNumber}}</p>
            <p>Express logistics:{{.logistics}}</p>
            <p>{{.userName}} confirmed that this order had been received at the destination.</p>"
  receive_dtp: "<p>프로젝트 넘버:{{.projectNumber}}</p>
  <p>프로젝트 명칭:{{.projectName}}</p>
  <p>프로젝트 환경:{{.envName}}</p>
  <p>Subject Number:{{.subject}}</p>
  <p>그룹:{{.group}}</p>
  <p>난수:{{.randomNumber}}</p>
  <p>Visit Name:{{.visit}}</p>
  <p>주문번호:{{.orderNumber}}.</p>
  <p>{{.userName}}이 주문이 목적지에서 접수되었음을 확인했습니다.</p>"
  receive_title: Clinflash IRT {{.projectNumber}} {{.envName}} 주문 접수 알림 {{.destination}} {{.orderNumber}}
  receive_title_dtp: Clinflash IRT {{.projectNumber}} {{.envName}} 주문 접수 알림 {{.subject}} {{.visit}} {{.orderNumber}}
  recovery_title: Clinflash IRT {{.projectNumber}} {{.envName}} IP 반환 알림 {{.destination}} {{.orderNumber}}
  recovery_confirm_title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Return Confirmation Notification {{.destination}} {{.orderNumber}}
  send: <p>프로젝트 넘버:{{.projectNumber}}</p>
    <p>프로젝트 명칭:{{.projectName}}</p>
    <p>프로젝트 환경:{{.envName}}</p>
    <p>운송시작지:{{.start}}</p>
    <p>목적지:{{.destination}}</p>
    <p>주문번호:{{.orderNumber}}.</p>
    <p>제품 을 연구 하 다 배송중.</p>
  send-logistics: "<p>Project number:{{.projectNumber}}</p>
          <p>Project Name:{{.projectName}}</p>
          <p>Project Environment:{{.envName}}</p>
            <p>Origin:{{.start}}</p>
            <p>Subject Number:{{.subject}}</p>
            <p>Visit Cycle:{{.visit}}</p>
            <p>Order Number:{{.orderNumber}}</p>
            <p>Express logistics:{{.logistics}}</p>
          <p>IP shipment is in delivery.</p>"
  send_title: Clinflash IRT {{.projectNumber}} {{.envName}} 주문 배송 알림 {{.destination}} {{.orderNumber}}

  send_dtp: <p>프로젝트 넘버:{{.projectNumber}}</p>
    <p>프로젝트 명칭:{{.projectName}}</p>
    <p>프로젝트 환경:{{.envName}}</p>
    <p>Subject Number:{{.subject}}</p>
    <p>그룹:{{.group}}</p>
    <p>난수:{{.randomNumber}}</p>
    <p>Visit Name:{{.visit}}</p>
    <p>주문번호:{{.orderNumber}}.</p>
    <p>제품 을 연구 하 다 배송중.</p>
  send_title_dtp: Clinflash IRT {{.projectNumber}} {{.envName}} 주문 배송 알림 {{.subject}} {{.visit}}{{.orderNumber}}
  create_title_dtp: Clinflash IRT {{.projectNumber}} {{.envName}} 订单创建通知 {{.subject}} {{.visit}} {{.orderNumber}}
  create_title_logistics: Clinflash IRT {{.projectNumber}} {{.envName}} 订单确认通知 {{.subject}} {{.visit}} {{.orderNumber}}
  create_dtp: "<p>项目编号:{{.projectNumber}}</p>
          <p>项目名称:{{.projectName}}</p>
          <p>项目环境:{{.envName}}</p>
          <p>起运地:{{.start}}</p>
          <p>受试者号:{{.subject}}</p>
          <p>访视名称:{{.visit}}</p>
          <p>订单号:{{.orderNumber}}</p>
          <p>Express logistics:{{.logistics}}</p>
          <p>研究产品订单已创建.</p>"
  end_title_dtp: Clinflash IRT {{.projectNumber}} {{.envName}} 주문 종료 통지 {{.subject}} {{.visit}} {{.orderNumber}}
  end_dtp: "<p>프로젝트 번호:{{.projectNumber}}</p>
           <p>프로젝트 이름:{{.projectName}}</p>
           <p>프로젝트 환경:{{.envName}}</p>
           <p>제목 번호: {{.subject}}</p>
           <p>그룹:{{.group}}</p>
           <p>난수:{{.randomNumber}}</p>
           <p>방문 이름: {{.visit}}</p>
           <p>주문 번호:{{.orderNumber}}</p>
           <p>종료 사유: :{{.reason}}</p>
           <p>이 주문은 종료되었습니다</p>"
  approval:
    add-title: Clinflash IRT {{.projectNumber}} {{.envName}} Site Order Application 
    failed-title: Clinflash IRT {{.projectNumber}} {{.envName}} Site Order Application Approval Failed
order_status_cancelled: 취소됨
order_status_lose: 분실됨
order_status_received: 수신됨
order_status_requested: 확인
order_status_toBeConfirmed: 확인 대기중
order_status_transit: 배송됨
order_status_apply: 신청됨
order_status_terminated: Terminated
order_status_close: Closed
order_logistics_1 : "顺丰"
order_logistics_2 : "EMS"
order_logistics_3 : "京东"
order_logistics_4 : "圆通"
order_logistics_5 : "韵达"
order_logistics_6 : "中通"
order_logistics_7 : "申通"
order_logistics_8 : "极兔"
order_logistics_9 : "其他"
page_notice:
  mail:
    send_fail: 보내기 실패
    send_success: 발송 성공
  system_update:
    email_error: 잘못된 이메일 주소
    others: 다른
    quota_exceeded: 기본 계정의 일일 전송 할당량 초과
    timeout: 메일 서버 시간 초과
project:
  site:
    delete: 이 센터는 이미 사용중이라 삭제할 수 없습니다
  storehouse:
    delete: 이 창고는 이미 사용중이라 삭제할 수 없습니다
    had:
      add: 창고가 이미 존재함
    unConnected: 이 창고는 물류창고와 연결 되어있지 않았습니다, 입고 데이터를 발송 하지 마십시오
  user:
    join: <p>{{.customerName}}랜덤 실험 연구에 당신을 초대합니다</p> <p>프로젝트 넘버:{{.projectNumber}}</p> <p>프로젝트 명칭:{{.projectName}}</p>
    title: Clinflash IRT 프로젝트 초대
projects:
  duplicated:
    names: 프로젝트 명칭 중복
    numbers: 프로젝트 넘버 중복
random:
  number: 무작위 번호
randomList:
  export:
    available: 사용 가능
    block: 그룹
    center: 중심
    delete: 무효화
    group: 그룹별
    number: 무작위 번호
    status: 상태
    subject: 피험자
    used: 사용됨
randomNumber:
  exist:
    used: 이 그룹이 랜덤 번호를 사용한 적이 있습니다, 다른 분층을 재배정 할 수 없습니다.
    used.clean: 이 그룹이 랜덤 번호를 사용한 적이 있습니다, 다른 분층을 삭제 할 수 없습니다.
random_filed_error: 오류!변수 필드 중복됨
random_attribute_error: Minimized randomization has been configured as "Not Applicable", please modify and re-create.
random_length_error: 오류! 생성된 랜덤 번호가 이미 설정된 번호 길이를 초과했습니다
random_list_download_name: random_number_list
random_list_name_duplicated: Name already exists
random_number_block_error: 오류!중복된 그룹이 있습니다
random_number_error: 오류!중복된 랜덤 번호
random_number_format_error: 오류!목록 형식 업로드 오류
random_number_format_error_trim: 업로드 데이터에 공백이 있습니다. 다시 업로드하십시오.
random_total_error: 오류! 모든 예시의 수는 각 실험 그룹의 비율의 합과 반드시 맞아야 합니다.
randomization:
  accuracy:
    ones: 같거나 작음
    twos: 같음
  type:
    ones: 그룹 무작위
    twos: 무작위 최소화
  upload:
    blockSize: 오류, 랜덤 차트 중 그룹의 길이와 시스템 중 설정의 그룹의 크기가 일치하지 않습니다
    group: 오류, 그룹 및 설정된 치료 그룹이 일치하지 않습니다
roles:
  delete:
    message: 역할이 사용중이라 삭제할 수 없습니다
  duplicated:
    names: 중복 추가, 다시 수정
    sys_check: 시스템 관리자 역할 메뉴 권한을 수정할 수 없습니다.
    project_admin_check: 프로젝트 관리자 항목 보기 권한을 수정할 수 없습니다.
shipment_order_add_info: 블라인드 프로젝트이니, 2개 그룹의 제품 명 연구을 선택해주세요
shipment_order_buffer_info: 현재 센터의 재고량이 완충량을 초과했습니다
shipment_order_cancel_not_exist: 물류센터의 출고 송장번호가 존재하지 않습니다
shipment_order_cancel_not_status: 물류가 이미 발송되어 취소할 수 없습니다
shipment_order_initial_info: 현재 센터의 초기 다시 시작 할 수 없습니다약
shipment_order_mode_info: 출발지가 센터일 경우, 보충 방식은 제품 을 연구 하 다의 수량 보충 방식만 가능합니다
shipment_order_over_warning_info: 현재 센터의 재고량이 경계치보다 높습니다
shipment_order_sendAndReceive_info: 출발지와 목적지는 중복될 수 없습니다
shipment_order_dtp_info: DTP订单不允许部分确认、接收
shipment_order_supply_info: 선택한 제품 을 연구 하 다들의 존재 여부와 기관별 공급 계획을 확인해주세요
shipment_order_no_supply: Please configure the supply plan first.
shipment_out_of_stock: 이 제품 을 연구 하 다은 재고가 부족합니다
simulated:
  random:
    list:
      factor: Factor
      group: Group
      name: SimulationResult
      number: Random number
      only: 무 작위 목록 만 시작 할 수 있 습 니 다.
      runCount: Number of runs
      site: Site
      subject: Subject
    number:
      not:
        enough: 피 실험자 수 는 반드시 랜 덤 번호 개수 보다 작 아야 한다.
site:
  disabled: 센터가 비활성화됨
  had:
    bind: 이 센터는 삭제할 수 없도록 설정 되어있습니다
  name: 센터 명칭
  number: 센터 번호
sites:
  duplicated:
    number: Duplicated project numbers, please re-enter
storehouse:
  had:
    bind: 이 창고는 삭제할 수 없도록 설정 되었습니다
storehouses:
  duplicated:
    number: 仓창고 넘버 중복
subject:
  alarm:
    content: <p>프로젝트 넘버:{{.projectNumber}}</p> <p>프로젝트 명칭:{{.projectName}}</p> <p>프로젝트 환경:{{.envName}}</p> <p>다른 계층에 등록된 피험자의 수가 경고 값에 도달했습니다.</p> <p>분층/경계 인원/실제 인원：{{.info}}</p>
    title: Clinflash IRT {{.projectNumber}} {{.envName}} 피험자 경계
  group: 그룹별
  number: 피험자 번호
  replace_subject: 제목 번호 바꾸기
  replace_number: 난수 바꾸기
  pvUnblinding:
    content: <p>프로젝트 넘버:{{.projectNumber}}</p> <p>프로젝트 명칭:{{.projectName}}</p> <p>프로젝트 환경:{{.envName}}</p> <p>센터 번호:{{.siteNumber}}</p> <p>센터 명칭:{{.siteName}}</p> <p>피험자{{.subjectNumber}}이미 pv 블라인드 테스트 됨, 시간:{{.time}} 원인:{{.reason}}</p>
    title: Clinflash IRT 피험자 pv 공개
  random:
    content: <p>프로젝트 넘버:{{.projectNumber}}</p> <p>프로젝트 명칭:{{.projectName}}</p> <p>프로젝트 환경:{{.envName}}</p> <p>센터 번호:{{.siteNumber}}</p> <p>센터 명칭:{{.siteName}}</p> <p>피험자:{{.subjectNumber}} 무작위로 그룹에 가입되었습니다.</p> <p>무작위 번호:{{.randomNumber}}.</p> <p>시간:{{.time}}.</p>
    content_no_random_number: <p>프로젝트 번호:{{.projectNumber}}</p> <p>프로젝트 이름:{{.projectName}}</p> <p>프로젝트 환경:{{.envName}}</p> <p >사이트 번호:{{.siteNumber}}</p> <p>사이트 이름:{{.siteName}}</p> <p>주제 번호:{{.subjectNumber}}</p> <p>무작위화 시간:{{.time}}</p>
    fileName: 랜덤 결과표
    title: Clinflash IRT {{.projectNumber}} {{.envName}} 무작위 피험자 {{.siteNumber}} {{.siteName}}
  status: 상태
  status.random: 무작위
  status.sign.out: 종료됨
  status.unblinding: 블라인드 테스트 완료됨
  unblinding-approval:
    title: Clinflash IRT {{.projectNumber}} {{.envName}} 긴급 눈가림 해제 {{.siteNumber}} {{.siteName}}
    content: "<p>프로젝트 번호:{{.projectNumber}}</p>
       <p>프로젝트 이름:{{.projectName}}</p>
       <p>프로젝트 환경:{{.envName}}</p>
       <p>사이트 번호:{{.siteNumber}}</p>
       <p>사이트 이름:{{.siteName}}</p>
       <p>주제 번호:{{.subjectNumber}}</p>
       <p>난수:{{.randomNumber}}</p>
       <p>긴급 눈가림 해제 시간:{{.time}}</p>
       <p>비맹점 해제 이유:{{.reason}}</p>
       <p>비고:{{.remark}}</p>
       <p>승인 코드:{{.approvalNumber}}</p>
       <p>승인 결과:{{.approvalResult}}</p>
       <p>이유:{{.rejectReason}}</p>"
  pv-unblinding-approval:
      title: Clinflash IRT {{.projectNumber}} {{.envName}} PV Unblinding Approval Result {{.siteNumber}} {{.siteName}}
      content: "<p>{{.label}}:{{.subjectNumber}}</p>
         <p>Random Number:{{.randomNumber}}</p>
         <p>Pv Unblinding Time:{{.time}} </p>
         <p>Pv Unblinding Reason:{{.reason}}</p>
         <p>Remark:{{.remark}}</p>
         <p>Approval Code:{{.approvalNumber}}</p>
         <p>Approval Result:{{.approvalResult}}</p>
         <p>Reason:{{.rejectReason}}</p>"
  unblinding:
    content: <p>프로젝트 번호:{{.projectNumber}}</p>
      <p>프로젝트 이름:{{.projectName}}</p>
      <p>프로젝트 환경:{{.envName}}</p>
      <p>사이트 번호:{{.siteNumber}}</p>
      <p>사이트 이름:{{.siteName}}</p>
      <p>주제 번호:{{.subjectNumber}}</p>
      <p>난수:{{.randomNumber}}</p>
      <p>긴급 눈가림 해제 시간:{{.time}}</p>
      <p>비맹점 해제 이유:{{.reason}}</p>
      <p>스폰서에게 알림:{{.isSponsor}}</p>
      <p>비고:{{.remark}}</p>
    fileName: 블라인드 테스트 결과표
    sponsor: Notify the sponsor
    title: Clinflash IRT {{.projectNumber}} {{.envName}} 비상 눈가림 해제 성공 {{.siteNumber}} {{.siteName}}
subject_cohort_check: 현재 단계는 비그룹 상태이므로 무작위 실험을 진행 할 수 없습니다
subject_cohort_last_group: 이전 단계 해당 피험자는 랜덤 그룹의 그룹원이 아닙니다
subject_factor_check_error: 현재 분층별 입력자 수가 설정 인원에 도달하였습니다
subject_factor_no_null: 분층 요소는 비워둘 수 없습니다
subject_medicine_count: 제품 을 연구 하 다의 양이 부족합니다
subject_medicine_count_real: There is no research product in stock, please re-enter
subject_medicine_other: N/A넘버 제품 을 연구 하 다
subject_no_random: 일치하는 랜덤 번호가 없습니다
subject_site_no_depot: Unable to randomize/dispense, site is unbound to the depot, please contact the administrator to configure.
subject_no_register: 피험자 미등록
subject_no_replace: 입력하신 피험자 번호는 이미 무작위 설정이 완료되어 교체 입력할 수 없습니다
subject_no_visit: 방문주기 설정 후 등록하시길 바랍니다
subject_number_repeat: 피험자가 이미 존재합니다
subject_random_error: 랜덤 설정을 하려면 발약을 완료해야 합니다
subject_random_number_existence: 시스템에 무작위 번호가 이미 존재하여 사용이 불가능합니다
subject_status_no_cancel: Current subject status cannot be cancel
subject_status_no_delete: 현재 피험자 상태는 삭제할 수 없습니다
subject_status_no_dispensing: The current subject status cannot be dispensed
subject_status_no_join: The current subject status cannot be operated and does not
  participate
subject_status_no_random: 현재 피험자 상태는 무작위일 수 없습니다
subject_status_no_reissue: Current subject status cannot be reissued
subject_status_no_replace: 현재 피험자 상태는 바꿀 수 없습니다
subject_status_no_replace_dispensing: The current subject status cannot study product
  replacement
subject_status_no_retrieval: Current subject status cannot be retrieval
subject_status_no_sign_out: 현재 피험자 상태는 종료할 수 없습니다
subject_status_no_unblinding: 현재 피험자의 상태는 블라인드 테스트를 할 수 없습니다
subject_status_no_update: 현재 피험자 상태는 수정할 수 없습니다
subject_visit_dispensing: Research product issued during the current visit
subject_visit_dispensing_no_join: The current visit does not participate in drug dispensing
subject_visit_dispensing_no_order: Please distribute the medicine in the order of visit
subject_visit_dispensing_no_order_confrim: 上一个访视订单待确认
subject_visit_dispensing_no_order_dtp: 当前访视配置无法使用中心（直接寄送受试者）/库房（直接寄送受试者），请检查配置
subject_visit_dispensing_store: 当前中心未绑定库房
subject_visit_dispensing_set_no_join: The current visit is not allowed to configure not to participate in the visit. Please refresh the page
subject_visit_dispensing_no_reissue: "재발급 실패, 현재 방문한 외국 방문을 재발급할 수 없습니다. 다시 선택하세요."
subject_visit_dispensing_no_site: "이 센터는 창고를 잠시 분배하지 않았다"
subject_visit_dispensing_order_status: "현재 주문 상태에서는 실제 사용 검토 제품을 등록할 수 없습니다."
subject_visit_dispensing_order_status_last: "이전 방문의 주문 상태가 배송되지 않아, 현재 방문은 신청할 수 없습니다."
subject_visit_cannot_cancel: "현재 항목 속성은 되찾기 취소 작업을 허용하지 않습니다"
subject_visit_cannot_replace: "현재 약물 상태에서는 교체 조작을 허용하지 않습니다"

upload_medicines_cell: Excel의 마지막 줄 데이터는 비워주세요
upload_medicines_drugName: 제품 을 연구 하 다 이름이 일치하지 않음
upload_medicines_info: 빈 양식 데이터를 업로드 하지 마십시오
user:
  createDate: 생성 시간
  customer:
    authorization:
      success: 권한 부여 성공
    bind:
      error: 고객 그룹에 사용자가 없습니다. 관리자에게 연락하여 사용자를 추가하십시오.
      success: 바인딩 성공
  depot: 창고
  email: 우편함
  exist:
    env: 환경에는 이 사용자가 이미 존재합니다
  name: 이름
  no:
    exist: 사용자가 존재하지 않습니다
    exist.customer: 사용자가 현재 클라이언트에 속하지 않음
  notice:
    customer:
      bind:
        title: 「고객 승인 고지」
    project:
      bind:
        title: 「프로젝트 승인 알림」
    return:
      login: 다시 로그인
    title: 프로젝트 승인 알림
  notice_customer: 귀하의 계정({{.email}})이 [{{.customer}}]명의 고객에게 성공적으로 승인되었습니다. 제 시간에 시스템에 로그인하여 확인하십시오.
  notice_project: 귀하의 계정({{.email}})이(가) 새로운 임상 시험 프로젝트 [{{.project}}]에 할당되었습니다. 제 시간에 시스템에 로그인하여 확인하십시오.
  password:
    error: Password error
  phone:
    exist: The mobile phone number is bound to the user
  resend:
    email:
      info: 사용자의 상태가 이미 활성화되어, 활성화 이메일을 전송 할 필요가 없습니다
  roles: 역할
  site: 센터
  status: 상태
  status.activited: 활성화됨
  status.not.active: 비활성
  status.enable: "사용 가능"
  status.disable: "사용 안 함"
users:
  authentication:
    failed: Authentication failed, please login again
  duplicated:
    customer: 사용자가 이미 존재합니다
    emails: 사용자 이메일 중복
  identify:
    code:
      incorrect: Incorrect identify code
  missing:
    phone: 핸드폰 번호가 완전하지 않으니 클라우드에 로그인한 후 개인 정보에서 수정하도록 안내해 주십시오
  phone:
    not:
      exist: Account does not exist or does not have permission
visit_cycle_duplicated_number: 방문 넘버 중복
visit_cycle_duplicated_random1: 이미 랜덤 방문이 존재합니다, 중복 설정하지 마십시오
visit_cycle_duplicated_random2: 랜덤 유형의 프로젝트를 다시 골라, 최대 두 가지 랜덤 방문 설정을 할 수 있습니다
work:
  task:
    error: Task has been completed and cannot be repeated
    deleted: The task no longer exists. Please refresh and try again
    scan:
        error: This task does not require scanning code confirmation operation
    packageScan:
        error: This task does not require package scanning operation
    package_number:
        error: Scanning failed, the scanned IP is not a system-generated IP in the current project/environment.
    exist: A single operation has not been completed and cannot be repeated
    medicine:
        error: Scan list has been updated. Please refresh and try again
medicine_other_repeat: 반복 추가, 다시 추가
site_not_delete: 이미 등록, 랜덤/발약 센터, 무효로 할 수 없습니다
order_status_error: 주문 상태 이상, 목록으로 돌아가서 다시 조작하세요
wms:
  cancel_order_fail: Bioquick order cancellation failed
order_status_not_cancelled: 현재 주문 상태를 종료할 수 없습니다. 페이지를 새로고침하세요.
unblinding_code_error: 언블라인드 코드 오류
unblinding_password_error: 잘못된 비밀번호
common_configuration_error: 구성 예외
subject_urgentUnblindingApproval_reason_other: 기타
subject_urgentUnblindingApproval_reason_sae: SAE
subject_urgentUnblindingApproval_reason_pregnancy: 임신
subject_urgentUnblindingApproval_reason_policy: 정책 요구 사항
subject_urgentUnblindingApproval_Agree : 통과
subject_urgentUnblindingApproval_Reject : 거부
subject_urgentUnblindingApproval_applicationed: 대상자 눈가림 해제(긴급) 신청서가 제출되었습니다. 승인자의 승인을 기다리십시오.
subject_urgentUnblindingApproval_pv_applicationed: The subject unblinding (pv) application has been submitted, please wait for the approval of the approver.
supply_plan_duplicated_name: Duplicated name, please enter again.
edc_push_subject_number: 受试者号
edc_push_randomization_number: 随机号
edc_push_group: 组别
edc_push_randomization_time: 随机时间
edc_push_visit_number: 访视号
edc_push_dispense: 发药
edc_push_dispense_time: 发药时间
edc_push_drug: 药物
edc_push_cohort: 群组/阶段
edc_push_edc_return: EDC返回
operation_log:
  label:
    medicine: 研究产品名称
    site: 中心编号
    dept: 库房
    supply: 供应计划名称
    group: 组别代码
    factor: 分层因素
    list: 随机列表名称
    invalidList: 作废随机表
    simulate_random_name: 名称
    name: 字段名称
  module:
    barcode: 项目构建-编码配置
    supply: 项目构建-供应计划
    supply_detail: 项目构建-供应计划-供应计划详情
    random_design: 项目构建-随机配置-随机设计
    form: 项目构建-随机配置-表单配置
    simulate_random: 项目构建-模拟随机
    push: 项目构建-推送统计-发送
  add: 新增
  edit: 编辑
  delete: 删除
  barcode:
    random: 随机化
    manual: 手动编码上传
    auto: 系统自动编码
  supply:
    name: 计划名称
    status: 计划状态
    status_effective: 有效
    status_invalid: 无效
    site: 计划适用中心
    all_site: 全部中心
    desc: 计划描述
  supply_detail:
    name: 	研究产品
    init_supply  : 初始发药量
    warning  : 研究产品警戒值
    buffer  : 最大缓冲量
    second_supply  : 再供应量
    un_distribution_date  : 不配送天数
    un_provide_date  : 不发放天数
    validity_reminder  : 有效期提醒
    auto_supply_size  : 自动配药量
    supply_mode_key: 补充方式
    supply_mode:
      all_supply: 全研究产品补充
      single: 单研究产品补充
      all_one: 全研究产品补充+1一个随机号研究产品
      single_one: 单研究产品补充+1一个随机号研究产品
  random_design:
    type: 随机类型
    block: 区组随机
    min: 最小化随机
    group_name: 组别名称
    group_code: 组别代码
    factor:
      layer: 地区分层
      number: 字段编号
      name: 系统字段
      label: 名称
      type: 控件类型
      options: 选项
      options_label_value: 选项标签/标签值
      folder_oid: 文件夹OID
      form_oid: 表单OID
      field_oid: 字段OID
      disable: 禁用地区分层
      country: 开启国家分层
      site: 开启中心分层
    mapping:
      random:
        folder_oid: 随机号-文件夹OID
        form_oid: 随机号-表单OID
        field_oid: 随机号-字段OID
      group:
        folder_oid: 组别-文件夹OID
        form_oid: 组别-表单OID
        field_oid: 组别-字段OID
      time:
        folder_oid: 随机时间-文件夹OID
        form_oid: 随机时间-表单OID
        field_oid: 随机时间-字段OID
  random_list:
    name: 名称
    site: 中心
    initial_number: 初始编号
    weight_ratio: 组别配置(权重比)
    block_configuration: 区组配置
    number_length: 号码长度
    seed: 随机种子
    prefix: 号码前缀
    size: 可接受的区组大小（多个区组用","隔开）
    status: 状态
    disable: 作废
    enable: 启用
    isRandom: 中心没有分配随机号不能入组
    set_site: 区组分配给中心
    set_factor: 区组分配给分层
    clean_factor: 清空分层
    set_count: 设置人数
    file_name: 上传
  form:
    name: 字段名称
    editable: 可修改
    required: 必填
    type: 控件类型
    format: 格式类型
    options: 选项
    input: 输入框
    inputNumber: 数字输入框
    textArea: 多行文本框
    select: 下拉框
    checkbox: 复选框
    radio: 单选框
    switch: 开关
    datePicker: 日期选择框
    timePicker: 时间选择框
    length: 长度
    max: 最大值
    min: 最小值
    variableFormat: 变量格式
  simulateRandom:
    name: 名称
    randomList: 随机列表
    siteQuantity: 中心数
    RunQuantity: 运行次数
    SubjectQuantity: 受试者数
    run: 运行了模拟随机。
report:
  template:
    name_exist: Template Name Already Exists
    name_invalid: Template Name Invalid