[menu_and_permission]
menu.home = 工作台
menu.settings = 设置
menu.settings.storehouse = 仓库
menu.settings.roles = 角色权限
menu.settings.users = 用户
menu.projects = 项目
menu.projects.main = 项目
menu.projects.main.env = 项目环境
menu.projects.main.setting = 项目设置
menu.projects.main.setting.base = 基本信息
menu.projects.main.setting.function = 业务功能
menu.projects.main.setting.docking = 外部对接
menu.projects.main.setting.permission = 项目权限
menu.projects.main.setting.notice = 项目通知
menu.projects.main.setting.custom = 自定义流程
menu.projects.project = 项目详情
menu.projects.project.home = 首页
menu.projects.project.overview = 概览
menu.projects.project.status = 状态
menu.projects.project.task = 项目任务
menu.projects.project.random.statistics = 随机统计
menu.projects.project.subject.statistics = 受试者统计
menu.projects.project.depot.ip.statistics = 库房单品统计
menu.projects.project.site.ip.statistics = 中心单品统计
menu.projects.project.analysis = 异常分析
menu.projects.project.dynamics = 项目动态
menu.projects.project.sub = 受试者
menu.projects.project.subject = 受试者列表
menu.projects.project.subject.urgent-unblinding = 揭盲（紧急）
menu.projects.project.subject.urgent-unblinding.unblinding = 揭盲（紧急）
menu.projects.project.subject.urgent-unblinding.approval-log = 审批记录
menu.projects.project.subject.urgent-unblinding-pv = 揭盲（pv）
menu.projects.project.subject.urgent-unblinding.unblinding-pv = 揭盲（pv）
menu.projects.project.subject.urgent-unblinding.approval-log-pv = 审批记录
menu.projects.project.subject.urgent-unblinding-ip = 揭盲（pv）
menu.projects.project.subject.urgent-unblinding.unblinding-ip = 揭盲（研究产品）
menu.projects.project.subject.urgent-unblinding.approval-log-ip = 审批记录

menu.projects.project.subject.dispensing = 发放
menu.projects.project.subject.medicine.trail = 轨迹
menu.projects.project.subject.trail = 轨迹
menu.projects.project.subject.visit.cycle = 访视管理
menu.projects.project.supply = 供应管理
menu.projects.project.supply.storehouse = 库房统计
menu.projects.project.supply.storehouse.summary = 概览
menu.projects.project.supply.storehouse.single = 单品管理
menu.projects.project.supply.site = 中心药房
menu.projects.project.supply.site.summary = 概览
menu.projects.project.supply.site.single = 单品管理
menu.projects.project.supply.site.no_number = 未编号单品管理
menu.projects.project.supply.storehouse.no_number = 未编号单品管理
menu.projects.project.supply.shipment = 研究产品订单
menu.projects.project.supply.shipment.approval = 审批记录
menu.projects.project.supply.shipment.logistics = 物流
menu.projects.project.supply.drug_recovery = 回收订单管理
menu.projects.project.supply.drug_recovery.logistics = 物流
menu.projects.project.supply.release-record = 隔离管理
menu.projects.project.supply.drug = 研究产品
menu.projects.project.supply.drug.order = 研究产品订单
menu.projects.project.supply.drug.single = 单品管理
menu.projects.project.supply.drug.no_number = 未编号单品管理
menu.projects.project.build = 项目构建
menu.projects.project.build.storehouse = 库房管理
menu.projects.project.build.site = 中心管理
menu.projects.project.build.site.supply-plan = 供应计划
menu.projects.project.build.attributes = 属性配置
menu.projects.project.build.code_rule = 编码配置
menu.projects.project.build.simulate_random = 模拟随机
menu.projects.project.build.randomization = 随机配置
menu.projects.project.build.randomization.design = 随机设计
menu.projects.project.build.randomization.design.type = 随机类型
menu.projects.project.build.randomization.design.group = 治疗组别
menu.projects.project.build.randomization.design.factor = 分层因素
menu.projects.project.build.randomization.design.list = 随机列表
menu.projects.project.build.randomization.design.attribute = 随机列表属性
menu.projects.project.build.randomization.design.block = 随机号区组
menu.projects.project.build.randomization.design.factor-in = 分层因素
menu.projects.project.build.randomization.form = 表单配置
menu.projects.project.build.drug = 研究产品管理
menu.projects.project.build.drug.visit = 访视管理
menu.projects.project.build.drug.visit.setting = 设置
menu.projects.project.build.drug.config = 研究产品配置
menu.projects.project.build.drug.config.setting = 设置
menu.projects.project.build.drug.list = 研究产品列表
menu.projects.project.build.drug.no_number = 未编号研究产品
menu.projects.project.build.drug.batch = 批次管理
menu.projects.project.build.drug.barcode = 条形码列表
menu.projects.project.build.drug.barcode_label = 标签管理
menu.projects.project.build.plan = 供应计划
menu.projects.project.build.plan.config = 计划管理
menu.projects.project.build.history = 项目日志
menu.projects.project.build.push = 推送统计
menu.projects.project.settings = 其它设置
menu.projects.project.settings.notice = 通知设置
menu.projects.project.settings.user = 人员管理
menu.projects.project.settings.export = 配置导出
menu.projects.project.settings.role = 角色权限
menu.projects.project.settings.config = 项目配置
menu.projects.project.monitor = 动态监控
menu.projects.project.info = 项目信息
operation.projects.project.info.view = 查看
menu.projects.project.basic.information = 基本信息
operation.projects.project.basic.information.view = 查看
menu.projects.project.basic.environment = 项目环境
operation.projects.project.basic.environment.view = 查看
menu.projects.project.business.functions = 业务功能
operation.projects.project.business.functions.view = 查看
menu.projects.project.external.docking = 外部对接
operation.projects.project.external.docking.view = 查看
menu.projects.project.custom.process = 自定义流程
operation.projects.project.custom.process.view = 查看
menu.projects.project.permissions = 项目权限
operation.projects.project.permissions.view = 查看
menu.projects.notice.permissions = 项目通知
operation.projects.notice.permissions.view = 查看
menu.projects.project.multiLanguage = 多语言
operation.projects.project.multiLanguage.view = 查看
operation.projects.project.multiLanguage.add = 添加
operation.projects.project.multiLanguage.edit = 编辑
operation.projects.project.multiLanguage.delete = 删除
operation.projects.project.multiLanguage.trail = 轨迹
menu.projects.project.multiLanguage.details = 语言详情
operation.projects.project.multiLanguage.details.view = 查看
operation.projects.project.multiLanguage.details.edit = 编辑
operation.projects.project.multiLanguage.details.preview = 预览
operation.projects.project.multiLanguage.details.downloadTemplate = 下载模板
operation.projects.project.multiLanguage.details.batchExport = 导入
menu.projects.project.storehouse.no_number.trail = 轨迹
menu.projects.project.storehouse.sku.trail = 轨迹
menu.projects.project.drug.sku.trail = 轨迹
menu.projects.project.site.sku.trail = 轨迹
menu.projects.project.site.no_number.trail = 轨迹
menu.projects.project.supply.shipment.trail = 轨迹
menu.projects.project.recovery.trail = 轨迹
menu.projects.project.freeze.trail = 轨迹
menu.projects.project.randomization.list.trail = 轨迹
menu.projects.project.medicine.upload.trail = 轨迹
menu.projects.project.supply.order.trail = 轨迹
menu.report = 报表
menu.report.userRoleStatus = 用户角色状态报表
menu.report.userRoleAssignHistory = 用户角色分配记录
menu.report.configureReport = 配置报告
menu.report.auditTrailExport = 稽查轨迹
menu.report.auditTrailExport.build = 项目构建
menu.report.auditTrailExport.settings = 项目设置
menu.report.auditTrailExport.release-record = 隔离管理
menu.report.auditTrailExport.order = 研究产品订单
menu.report.auditTrailExport.drug_recovery = 研究产品回收
menu.report.auditTrailExport.subject = 受试者
menu.report.auditTrailExport.dispensing = 受试者发放
menu.report.auditTrailExport.ip = 研究产品
menu.report.auditTrailExport.userLoginHistory = 用户登录历史记录
menu.report.auditTrailExport.userRoleAssignHistory = 用户角色分配记录
menu.report.projectPermissionConfigurationExport = 项目权限配置报表
menu.report.randomizeReport = 受试者明细报表
menu.report.dispenseReport = 受试者发放报表
menu.report.unblindingReport = 揭盲报表
menu.report.IPUnblindingReport = 研究产品揭盲报表
menu.report.depotItemReport = 库房单品报表
menu.report.sourceRandomizationListExport = 随机盲底
menu.report.sourceIPExport = 研究产品盲底
menu.report.siteItemReport = 中心药房单品报表
menu.report.sourceIpUploadHistory = 研究产品盲底上传记录
menu.report.randomizationSimulationResult = 模拟随机结果
menu.report.returnOrdersReport = 回收订单报表
menu.report.shipmentOrdersReport = 研究产品订单报表
menu.report.siteIPStatisticsExport = 中心单品统计报表
menu.report.randomizationStatisticsExport = 随机统计报表
menu.report.subjectStatisticsExport = 受试者统计报表
menu.report.depotIPStatisticsExport = 库房单品统计报表
menu.report.userLoginHistory = 用户登录历史记录
menu.report.RandomizationSimulationPDFExport = 模拟随机报告
menu.report.ProjectNotificationsConfigurationReportExport = 项目通知配置报表
menu.report.forecastingPrediction = 研究产品预测报表
menu.report.visitForecast = 访视统计报表
operation.settings.roles.view = 查看
operation.settings.roles.add = 添加
operation.settings.roles.edit = 编辑
operation.settings.roles.config = 权限配置
operation.settings.roles.export = 导出
operation.settings.users.view = 查看
operation.settings.users.add = 新增
operation.settings.users.setting = 设置
operation.settings.users.edit = 编辑
operation.settings.users.close = 关闭
operation.settings.users.invite-again = 再次邀请
operation.settings.users.cancel = 取消
operation.settings.users.export = 导出
operation.settings.storehouse.view = 查看
operation.settings.storehouse.add = 新增
operation.settings.storehouse.edit = 编辑
operation.settings.storehouse.delete = 删除
operation.projects.main.view = 查看
operation.projects.main.create = 创建项目
operation.projects.main.config.view = 查看
operation.projects.main.config.create = 添加环境
operation.projects.main.config.copy = 复制环境
operation.projects.main.config.edit_env = 编辑环境
operation.projects.main.config.unlock = 解锁
operation.projects.main.config.lock = 锁定
operation.projects.main.config.add = 添加
operation.projects.main.config.edit = 编辑
operation.projects.main.config.copy_cohort = 复制
operation.projects.main.config.delete = 删除
operation.projects.main.setting.view = 查看
operation.projects.main.setting.base.view = 查看
operation.projects.main.setting.base.edit = 编辑
operation.projects.main.setting.base.modify = 修改
operation.projects.main.setting.function.view = 查看
operation.projects.main.setting.function.edit = 编辑
operation.projects.main.setting.function.admin = 管理员启用/禁用
operation.projects.main.setting.docking.view = 查看
operation.projects.main.setting.docking.edit = 编辑
operation.projects.main.setting.custom.view = 查看
operation.projects.main.setting.custom.edit = 编辑
operation.projects.main.setting.permission.view = 查看
operation.projects.main.setting.permission.add = 添加
operation.projects.main.setting.permission.edit = 编辑
operation.projects.main.setting.permission.setting = 权限设置
operation.projects.main.setting.notice.view = 查看
operation.projects.main.setting.notice.add = 添加
operation.projects.main.setting.notice.delete = 删除
operation.projects.main.setting.notice.edit = 编辑
operation.projects.home.view = 查看
operation.project.status.view = 查看
operation.project.task.view = 查看
operation.project.analysis.view = 查看
operation.project.dynamics.view = 查看
operation.project.site.IPStatistics.view = 查看
operation.project.site.IPStatistics.download = 导出
operation.project.depot.IPStatistics.view = 查看
operation.project.depot.IPStatistics.download = 导出
operation.project.subject.view = 查看
operation.project.subject.download = 导出
operation.project.random.view = 查看
operation.project.random.download = 导出
operation.subject.view-list = 查看
operation.subject.random = 随机
operation.subject.replace = 受试者替换
operation.subject.trail = 查看
operation.subject.unblinding-pv = 揭盲(pv)
operation.subject.medicine.trail = 查看
operation.subject.medicine.trail.print = 打印
operation.subject.cohort.status = 状态修改
operation.subject.medicine.view-dispensing = 受试者信息详情查看
operation.subject.medicine.transport = 转运
operation.subject.medicine.dispensing = 发放
operation.subject.medicine.reissue = 补发
operation.subject.medicine.replace = 研究产品替换
operation.subject.medicine.resume = 恢复发放
operation.subject.unblinding = 查看
operation.subject.unblinding-application = 揭盲(紧急)申请
operation.subject.unblinding-approval = 揭盲(紧急)审批
operation.subject.unblinding-log = 查看
operation.subject.unblinding-sms = 发送短信
operation.subject.unblinding-print = 打印
operation.subject.unblinding-pv-view = 查看
operation.subject.unblinding-pv-application = 揭盲(pv)申请
operation.subject.unblinding-pv-approval = 揭盲(pv)审批
operation.subject.unblinding-pv-log = 查看
operation.subject.unblinding-pv-sms = 发送短信
operation.subject.unblinding-pv-print = 打印
operation.subject.unblinding-ip-view = 查看
operation.subject.unblinding-ip-application = 揭盲(研究产品)申请
operation.subject.unblinding-ip-approval = 揭盲(研究产品)审批
operation.subject.unblinding-ip-log = 查看
operation.subject.unblinding-ip-sms = 发送短信
operation.subject.unblinding-ip-print = 打印
operation.subject.registered = 登记
operation.subject.switch.cohort = 切换群组
operation.subject.update = 受试者修改
operation.subject.delete = 受试者删除
operation.subject.medicine.room = 房间号查看
operation.subject.medicine.retrieval = 取回
operation.subject.medicine.out-visit-dispensing = 计划外发放
operation.subject.medicine.invalid = 不参加本次访视
operation.subject.medicine.room-download = 房间号查看记录下载
operation.subject.medicine.register = 登记实际使用的研究产品
operation.subject.medicine.formula.update = 修改
operation.subject.medicine.planTime = 入组时间
operation.subject.medicine.setUp = 设置
operation.subject.secede = 停用（随机）
operation.subject.secede-registered = 停用（登记/筛选成功）
operation.subject.print = 打印
operation.subject-dtp.view-list = 查看
operation.subject-dtp.random = 随机
operation.subject-dtp.replace = 受试者替换
operation.subject-dtp.trail = 受试者轨迹
operation.subject-dtp.unblinding-pv = 揭盲(pv)
operation.subject-dtp.medicine.view-dispensing = 发放申请详情
operation.subject-dtp.medicine.trail = 发放轨迹
operation.subject-dtp.medicine.dispensing = 申请
operation.subject-dtp.medicine.transport = 转运
operation.subject-dtp.medicine.reissue = 补发申请
operation.subject-dtp.medicine.replace = 研究产品替换
operation.subject-dtp.unblinding = 查看
operation.subject-dtp.unblinding-application = 揭盲(紧急)申请
operation.subject-dtp.unblinding-approval = 揭盲(紧急)审批
operation.subject-dtp.unblinding-log = 查看
operation.subject-dtp.unblinding-sms = 发送短信
operation.subject-dtp.registered = 登记
operation.subject-dtp.update = 受试者修改
operation.subject-dtp.delete = 受试者删除
operation.subject-dtp.medicine.room = 房间号查看
operation.subject-dtp.medicine.out-visit-dispensing = 计划外发放申请
operation.subject-dtp.medicine.invalid = 不参加本次访视
operation.subject-dtp.medicine.export-room = 发放下载(包含房间号)
operation.subject-dtp.medicine.room-download = 房间号查看记录下载
operation.subject-dtp.medicine.register = 登记实际使用的研究产品
operation.subject-dtp.secede = 停用（随机）
operation.subject-dtp.secede-registered = 停用（登记/筛选成功）
operation.subject-dtp.print = 打印受试者轨迹
operation.subject-dtp.medicine.print = 打印发放轨迹
operation.project.subject.visit.cycle.view = 查看
operation.project.subject.visit.cycle.notice.view = 通知-查看
operation.project.subject.visit.cycle.send.notice = 发送通知
operation.supply.storehouse.medicine.summary = 查看
operation.supply.storehouse.medicine.singe = 查看
operation.supply.storehouse.medicine.use = 设为可用
operation.supply.storehouse.medicine.freeze = 隔离
operation.supply.storehouse.medicine.lost = 丢失/作废
operation.supply.storehouse.medicine.history = 查看
operation.supply.storehouse.medicine.print = 打印
operation.supply.storehouse.no_number.view = 查看
operation.supply.storehouse.no_number.freeze = 隔离
operation.supply.storehouse.no_number.lost = 丢失/作废
operation.supply.storehouse.no_number.history = 查看
operation.supply.storehouse.no_number.print = 打印
operation.supply.site.medicine.summary = 查看
operation.supply.site.medicine.summary.formula = 库存使用时间预测
operation.supply.site.medicine.singe = 查看
operation.supply.site.medicine.use = 设为可用
operation.supply.site.medicine.freeze = 隔离
operation.supply.site.medicine.lost = 丢失/作废
operation.supply.site.medicine.history = 查看
operation.supply.site.medicine.print = 打印
operation.supply.site.no_number.view = 查看
operation.supply.site.no_number.freeze = 隔离
operation.supply.site.no_number.lost = 丢失/作废
operation.supply.site.no_number.history = 查看
operation.supply.site.no_number.print = 打印
operation.supply.drug.order.list = 查看
operation.supply.drug.order.send = 运送
operation.supply.drug.order.receive = 接收
operation.supply.drug.order.end = 终止
operation.supply.drug.order.cancel = 取消
operation.supply.drug.order.reason = 原因
operation.supply.drug.order.history = 查看
operation.supply.drug.order.print = 打印
operation.supply.drug.order.confirm = 确认
operation.supply.drug.order.close = 关闭
operation.supply.drug.single.sku = 查看
operation.supply.drug.single.history = 查看
operation.supply.drug.single.print = 打印
operation.supply.drug.single.download = 数据下载
operation.supply.drug.single.delete = 丢失/作废
operation.supply.drug.single.use = 设为可用
operation.supply.drug.no_number.view = 查看
operation.supply.shipment.create = 新增
operation.supply.shipment.cancel = 通用-取消
operation.supply.shipment.send = 运送
operation.supply.shipment.lose = 丢失
operation.supply.shipment.list = 查看
operation.supply.shipment.receive = 接收
operation.supply.shipment.alarm = 库房核查
operation.supply.shipment.history = 查看
operation.supply.shipment.print = 打印
operation.supply.shipment.confirm = 通用-确认
operation.supply.shipment.reason = 原因
operation.supply.shipment.approval = 研究中心订单申请审批
operation.supply.shipment.close = 通用-关闭
operation.supply.shipment.terminated = 通用-终止
operation.supply.shipment.approval.view = 查看
operation.supply.shipment.detail.view = 详情
operation.supply.shipment.confirm-dtp = DTP-确认
operation.supply.shipment.cancel-dtp = DTP-取消
operation.supply.shipment.close-dtp = DTP-关闭
operation.supply.shipment.terminated-dtp = DTP-终止
menu.projects.project.supply.shipment.detail = 研究产品详情
operation.supply.shipment.detail.change = 更换
operation.supply.shipment.detail.changeRecord = 更换记录
operation.supply.shipment.detail.edit = 编辑
menu.projects.project.supply.recovery.detail = 研究产品详情
operation.supply.recovery.detail.change = 更换
operation.supply.recovery.detail.changeRecord = 更换记录
operation.supply.drug_recovery.detail.view = 详情
operation.supply.shipment.contacts = 联系人
operation.supply.shipment.approval.print = 打印
operation.supply.shipment.logistics.view = 查看
operation.supply.shipment.logistics.edit = 编辑
operation.supply.drug_recovery.logistics.view = 查看
operation.supply.drug_recovery.logistics.edit = 编辑
operation.supply.recovery.list = 查看
operation.supply.recovery.add = 新增
operation.supply.recovery.receive = 接收
operation.supply.recovery.confirm = 运送
operation.supply.recovery.cancel = 取消
operation.supply.recovery.lose = 丢失
operation.supply.recovery.determine = 确认
operation.supply.recovery.close = 关闭
operation.supply.recovery.end = 终止
operation.supply.recovery.reason = 原因
operation.supply.recovery.history = 查看
operation.supply.recovery.print = 打印
operation.supply.recovery.detail.view = 详情
operation.supply.freeze.list = 查看
operation.supply.freeze.release = 解隔离
operation.supply.freeze.delete = 丢失/作废
operation.supply.freeze.history = 查看
operation.supply.freeze.print = 打印
operation.supply.freeze.approval = 解隔离审批
operation.build.storehouse.add = 新增
operation.build.storehouse.delete = 删除
operation.build.storehouse.edit = 编辑
operation.build.storehouse.notice = 设置
operation.build.storehouse.view = 查看
operation.build.storehouse.alarm = 研究产品警戒
operation.build.site.view = 查看
operation.build.site.edit = 编辑
operation.build.site.add = 添加
operation.build.site.dispensing = 发放
operation.build.site.supply-plan.view = 查看
operation.build.site.supply-plan.edit = 编辑
operation.build.attribute.view = 查看
operation.build.attribute.edit = 编辑
operation.build.attribute.history = 查看轨迹
operation.build.code-rule.view = 查看
operation.build.code-rule.edit = 编辑
operation.build.simulate-random.view = 查看
operation.build.simulate-random.edit = 编辑
operation.build.simulate-random.add = 新增
operation.build.simulate-random.run = 运行
operation.build.simulate-random.site = 总览
operation.build.simulate-random.factor = 详情
operation.build.randomization.type.view = 查看
operation.build.randomization.type.edit = 编辑
operation.build.randomization.group.add = 新增
operation.build.randomization.group.inactivating = 同步
operation.build.randomization.group.delete = 删除
operation.build.randomization.group.edit = 编辑
operation.build.randomization.group.view = 查看
operation.build.randomization.factor.add = 添加
operation.build.randomization.factor.view = 查看
operation.build.randomization.factor.delete = 删除
operation.build.randomization.factor.edit = 编辑
operation.build.randomization.factor.set-toplimit = 设置分层
operation.build.randomization.list.view-summary = 查看
operation.build.randomization.list.upload = 上传
operation.build.randomization.list.generate = 生成
operation.build.randomization.list.sync = 同步
operation.build.randomization.list.active = 激活/禁用随机列表
operation.build.randomization.list.invalid = 作废
operation.build.randomization.list.edit = 编辑
operation.build.randomization.list.history = 查看
operation.build.randomization.list.print = 打印
operation.build.randomization.list.attribute = 查看
operation.build.randomization.list.segmentation.view = 查看
operation.build.randomization.list.segmentation.clear = 清空其他分层
operation.build.randomization.list.segmentation.site = 区组分配给中心
operation.build.randomization.list.segmentation.region = 区组分配给区域
operation.build.randomization.list.segmentation.country = 区组分配给国家
operation.build.randomization.list.segmentation.factor = 区组分配给分层
operation.build.randomization.list.segmentation.activate = 激活
operation.build.randomization.list.segmentation.deactivate = 失活
operation.build.randomization.factor-in.view = 查看
operation.build.randomization.factor-in.add = 添加
operation.build.randomization.factor-in.set-people = 编辑
operation.build.randomization.factor-in.delete = 删除
operation.build.randomization.form.add = 新增
operation.build.randomization.form.delete = 删除
operation.build.randomization.form.edit = 编辑
operation.build.randomization.form.list = 查看
operation.build.randomization.form.preview = 预览
operation.build.medicine.visit.update = 修改
operation.build.medicine.visit.drag = 排序
operation.build.medicine.visit.copy = 复制新增
operation.build.medicine.visit.push = 发布
operation.build.medicine.visit.push.record = 发布记录
operation.build.medicine.visit.add = 新增
operation.build.medicine.visit.delete = 删除
operation.build.medicine.visit.edit = 编辑
operation.build.medicine.visit.list = 查看
operation.build.medicine.configuration.add = 新增
operation.build.medicine.configuration.delete = 删除
operation.build.medicine.configuration.edit = 编辑
operation.build.medicine.configuration.list = 查看
operation.build.medicine.configuration.setting.add = 新增
operation.build.medicine.configuration.setting.delete = 删除
operation.build.medicine.configuration.setting.edit = 编辑
operation.build.medicine.configuration.setting.list = 查看
operation.build.medicine.upload.list = 查看
operation.build.medicine.upload.upload = 上传研究产品
operation.build.medicine.packlist.upload = 上传包装清单
operation.build.medicine.package.setting = 设置
operation.build.medicine.batch.setting = 设置
operation.build.medicine.examine = 审核
operation.build.medicine.update = 修改
operation.build.medicine.release = 放行
operation.build.medicine.upload.delete = 批量删除
operation.build.medicine.upload.uploadHistory = 查看
operation.build.medicine.upload.print = 打印
operation.build.medicine.otherm.add = 新增
operation.build.medicine.otherm.delete = 删除
operation.build.medicine.otherm.edit = 编辑
operation.build.medicine.otherm.list = 查看
operation.build.medicine.batch.update = 更新
operation.build.medicine.batch.list = 查看
operation.build.medicine.batch.edit = 编辑
operation.build.medicine.barcode.view = 查看
operation.build.medicine.barcode.add = 生成条形码
operation.build.medicine.barcode.scan = 扫码入仓
operation.build.medicine.barcode.scanPackage = 包装扫码
operation.build.medicine.barcode.export = 导出
operation.build.medicine.barcode_label.view = 查看
operation.build.medicine.barcode_label.add = 新增
operation.build.medicine.barcode_label.preview = 预览
operation.build.medicine.barcode_label.send = 发送
operation.build.supply-plan.add = 新增
operation.build.supply-plan.delete = 删除
operation.build.supply-plan.edit = 编辑
operation.build.supply-plan.view = 查看
operation.build.supply-plan.history = 轨迹
operation.build.supply-plan.medicine.add = 新增
operation.build.supply-plan.medicine.delete = 删除
operation.build.supply-plan.medicine.edit = 编辑
operation.build.supply-plan.medicine.view = 查看
operation.build.supply-plan.medicine.history = 轨迹
operation.build.history.view = 查看
operation.build.history.print = 打印
operation.build.push.view = 查看
operation.build.push.all.send = 全部推送
operation.build.push.batch.send = 批量发送
operation.build.push.history = 历史数据推送
operation.build.push.send = 重新发送
operation.build.push.details = 详情
operation.build.settings.notice.view = 查看
operation.build.settings.notice.edit = 编辑
operation.build.settings.user.add = 新增
operation.build.settings.user.role = 分配角色
operation.build.settings.user.site = 分配中心
operation.build.settings.user.depot = 分配仓库
operation.build.settings.user.app = APP帐号启用/禁用
operation.build.settings.user.view = 查看
; operation.build.settings.user.edit = 操作管理
operation.build.settings.user.unbind = 解绑
operation.build.settings.user.history = 轨迹
operation.build.settings.user.print = 打印
operation.build.settings.users.invite-again = 再次邀请
operation.build.settings.user.reauthorization = 再次授权
operation.build.randomization.info.view = 查看
operation.monitor.view = 查看
operation.monitor.edit = 管理
operation.report.userRoleStatus.download = 下载
operation.build.settings.user.download = 下载
operation.report.userRoleAssignHistory.download = 下载
operation.build.randomization.info.export = 下载
operation.report.auditTrailExport.download = 下载
operation.report.auditTrailExport.build = 项目构建
operation.report.auditTrailExport.settings = 项目设置
operation.report.auditTrailExport.release-record = 隔离管理
operation.report.auditTrailExport.order = 研究产品订单
operation.report.auditTrailExport.drug_recovery = 研究产品回收
operation.report.auditTrailExport.subject = 受试者
operation.report.auditTrailExport.dispensing = 受试者发放
operation.report.auditTrailExport.ip = 研究产品
operation.report.auditTrailExport.userLoginHistory = 下载
operation.report.auditTrailExport.userRoleAssignHistory = 下载
operation.report.siteIPStatisticsExport.download = 下载
operation.report.siteIPStatisticsExport.download.template = 自定义模板
operation.report.randomizationStatisticsExport.download = 下载
operation.report.subjectStatisticsExport.download = 下载
operation.report.forecastingPrediction.download = 下载
operation.report.visitForecast.download = 下载
operation.report.visitForecast.download.template = 自定义模板
operation.build.projectNotificationsConfigurationReport.download = 下载
operation.report.depotIPStatisticsExport.download = 下载
operation.report.depotIPStatisticsExport.download.template = 自定义模板
operation.report.userLoginHistory.download = 下载
operation.projects.main.setting.permission.export = 下载
operation.subject.download-random = 下载
operation.subject.download-random.template = 自定义模板
operation.subject.medicine.export = 下载
operation.subject.medicine.export.template = 自定义模板
operation.subject.download = 下载
operation.subject.download.template = 自定义模板
operation.report.ip.unblinding.download = 研究产品揭盲报表下载
operation.report.ip.unblinding.download.template = 研究产品揭盲报表自定义模板
operation.subject-dtp.download = 揭盲下载
operation.subject-dtp.medicine.export = 发放下载
operation.subject-dtp.download-random = 随机下载
operation.supply.storehouse.medicine.download = 下载
operation.supply.storehouse.medicine.download.template = 自定义模板
operation.build.randomization.list.export = 下载
operation.build.medicine.upload.downdata = 下载
operation.build.medicine.upload.downdata.template = 自定义模板
operation.source.ip.upload.history.downdata = 下载
operation.supply.site.medicine.download = 下载
operation.supply.site.medicine.download.template = 自定义模板
operation.build.simulate-random.download = 下载
operation.supply.recovery.download = 下载
operation.supply.recovery.download.template = 自定义模板
operation.supply.shipment.download = 下载
operation.supply.shipment.download.template = 自定义模板
operation.build.simulate-random.pdf.download = 下载

[export]
report.attributes.project = 项目
report.attributes.project.number = 项目编号
report.attributes.project.name = 项目名称
report.attributes.info = 基本信息
report.attributes.info.user.name = 姓名
report.attributes.info.user.email = 邮箱
report.attributes.info.user.role = 角色
report.attributes.info.project.name = 项目
report.attributes.info.country = 国家（分层属性）
report.attributes.info.region = 区域（分层属性）
report.attributes.info.site.country = 国家
report.attributes.info.site.region = 区域
report.attributes.info.site.number = 中心编号
report.attributes.info.site.name = 中心名称
report.attributes.info.subject.number = 受试者号
report.attributes.info.status = 状态
report.attributes.info.storehouse.name = 库房
report.attributes.random = 受试者随机
report.attributes.random.factor = 分层因素
report.attributes.random.factor.calc = 录入分层字段（页面）
report.attributes.random.actual.factor = 实际分层
report.attributes.random.time = 随机/入组时间
report.attributes.random.group = 组别
report.attributes.random.sub.group = 子组别
report.attributes.random.subject.number.replace = 替换受试者号
report.attributes.random.subject.number.replace.substitute = 替换
report.attributes.random.number = 随机号
report.attributes.random.register.time = 登记时间
report.attributes.random.register.operator = 登记操作人
report.attributes.random.operator = 随机操作人
report.attributes.random.form = 表单配置
report.attributes.random.plan.number = 计划随机数
report.attributes.random.block = 区组
report.attributes.random.subject.replace.status = 替换受试者状态
report.attributes.random.cohort = 群组
report.attributes.random.stage = 阶段
report.attributes.random.subject.replace.time = 替换时间
report.attributes.random.subject.replace.number = 替换随机号
report.attributes.random.config.code = 组别代码
report.attributes.random.sign.out.operator = 停用操作人
report.attributes.random.sign.out.time = 停用操作时间
report.attributes.random.sign.out.real.time = 实际停用日期
report.attributes.random.sign.out.reason = 停用原因
report.attributes.random.screen.time = 筛选日期
report.attributes.random.icf.time = ICF签署日期
report.attributes.random.finish.remark = 完成研究-备注
report.attributes.random.plan.time = 计划随机/入组时间
report.attributes.random.sequence.number = 随机顺序号
report.attributes.dispensing = 受试者发放
report.attributes.dispensing.room = 房间号
report.attributes.dispensing.cycle.name = 访视名称
report.attributes.dispensing.type = 发放操作类型
report.attributes.dispensing.planTime = 计划访视
report.attributes.dispensing.dose = 发放水平
report.attributes.dispensing.outsize = 是否超窗
report.attributes.dispensing.doseFormulas = 剂量调整表单
report.attributes.dispensing.time = 操作时间
report.attributes.dispensing.medicine = 研究产品编号
report.attributes.dispensing.drug.name = 研究产品名称
report.attributes.dispensing.label = 发放标签
report.attributes.dispensing.medicine.replace = 被替换研究产品
report.attributes.dispensing.medicine.real = 系统发放研究产品
report.attributes.dispensing.medicine.real.group = 实际使用研究产品组别
report.attributes.dispensing.medicine.is.replace = 是否是替换研究产品编号
report.attributes.dispensing.medicine.is.real = 是否是实际使用研究产品
report.attributes.dispensing.drug.other.number = 未编号研究产品数量
report.attributes.dispensing.useFormulas = 公式计算表单
report.attributes.dispensing.operator = 发放操作人
report.attributes.dispensing.remark = 发放-备注
report.attributes.dispensing.out-visit-dispensing.reason = 计划外发放原因
report.attributes.dispensing.reissue.reason = 补发原因
report.attributes.dispensing.reissue.remark = 补发备注
report.attributes.dispensing.out-visit-dispensing.remark = 计划外发放-备注
report.attributes.dispensing.replace.remark = 研究产品替换备注
report.attributes.dispensing.retrieval.remark = 取回-备注
report.attributes.dispensing.register.remark = 登记实际使用研究产品-备注
report.attributes.dispensing.invalid.remark = 不参加访视-备注
report.attributes.dispensing.send.type = 发放方式
report.attributes.dispensing.logistics.info = 物流
report.attributes.dispensing.logistics.remark = 物流备注
report.attributes.dispensing.sheet.actual = 发放与实际使用差异表
report.attributes.unblinding = 受试者揭盲
report.attributes.unblinding.sponsor = 是否已经通知申办方
report.attributes.unblinding.mark = 揭盲备注
report.attributes.unblinding.reason = 揭盲原因
report.attributes.unblinding.reason.mark = 揭盲原因备注
report.attributes.unblinding.operator = 揭盲操作人
report.attributes.unblinding.time = 揭盲操作时间
report.attributes.research = 研究产品
report.attributes.research.medicine.serial-number = 序列号
report.attributes.research.medicine.number = 研究产品编号
report.attributes.research.medicine.name = 研究产品名称
report.attributes.research.batch = 批次号
report.attributes.research.expireDate = 有效期
report.attributes.research.spec = 规格
report.attributes.research.packageNumber = 包装号
report.attributes.research.package.serialNumber = 包装序列号
report.attributes.research.packageMethod = 运送方式
report.attributes.research.place = 位置
report.attributes.research.order.number = 订单号
report.attributes.research.status = 状态
report.attributes.research.freeze.reason = 隔离原因
report.attributes.research.freeze.operator = 隔离操作人
report.attributes.research.freeze.time = 隔离操作时间
report.attributes.research.reason = 操作原因
report.attributes.research.operator = 操作人
report.attributes.research.time = 操作时间
report.attributes.research.release.reason = 解隔离原因
report.attributes.research.release.operator = 解隔离操作人
report.attributes.research.release.time = 解隔离操作时间
report.attributes.research.lost.reason = 丢失/作废原因
report.attributes.research.lost.operator = 丢失/作废操作人
report.attributes.research.lost.time = 丢失/作废操作时间
report.attributes.research.use.reason = 设为可用原因
report.attributes.research.use.operator = 设为可用操作人
report.attributes.research.use.time = 设为可用操作时间
report.attributes.research.other = 数量
report.attributes.order = 研究产品订单
report.attributes.order.detail = 研究产品订单详情
report.attributes.order.number = 订单号
report.attributes.order.status = 状态
report.attributes.order.send = 起运地
report.attributes.order.receive = 目的地
report.attributes.order.medicineQuantity = 研究产品数量
report.attributes.order.create.by = 创建人
report.attributes.order.create.time = 创建时间
report.attributes.order.cancel.by = 取消人
report.attributes.order.cancel.time = 取消时间
report.attributes.order.cancel.reason = 取消原因
report.attributes.order.confirm.by = 确认人
report.attributes.order.confirm.time = 确认时间
report.attributes.order.close.by = 关闭人
report.attributes.order.close.time = 关闭时间
report.attributes.order.close.reason = 关闭原因
report.attributes.order.send.by = 运送人
report.attributes.order.send.time = 运送时间
report.attributes.order.receive.by = 接收人
report.attributes.order.receive.time = 接收时间
report.attributes.order.lost.by = 丢失人
report.attributes.order.lost.time = 丢失时间
report.attributes.order.lost.reason = 丢失原因
report.attributes.order.end.by = 终止人
report.attributes.order.end.time = 终止时间
report.attributes.order.end.reason = 终止原因
report.attributes.order.supplier = 物流供应商
report.attributes.order.supplier.other = 其他物流
report.attributes.order.supplier.number = 物流单号
report.attributes.order.expectedArrivalTime = 期望送达时间
report.attributes.order.actualReceiptTime = 实际接收时间
report.ip.statistics.status.available = 可用
report.ip.statistics.status.toBeConfirmed = 待确认
report.ip.statistics.status.delivered = 已确认
report.ip.statistics.status.sending = 已运送
report.ip.statistics.status.quarantine = 已隔离
report.ip.statistics.status.used = 已使用
report.ip.statistics.status.lose = 丢失/作废
report.ip.statistics.status.expired = 已过期
report.ip.statistics.status.frozen = 冻结
report.ip.statistics.status.locked = 锁定
report.user.login.time = 操作时间
report.user.login.success = 登录是否成功
report.forecast.depot = 库房名称
report.forecast.period = 计划发放窗口期
report.forecast.date = 预测时间
report.simulate.random.name = 名称
report.simulate.random.block = 区组
report.simulate.random.group = 组别
report.simulate.random.subGroup = 子组别
report.user.role.assign.name=姓名
report.user.role.assign.email=邮箱
report.user.role.assign.operType=操作
report.user.role.assign.content=操作内容
report.user.role.assign.oper=操作人
report.user.role.assign.operTime=操作时间
report.audit.trail.operation.name = 操作名称
report.audit.trail.operation.way = 操作方式
report.project.env = 项目环境
report.random.list = 原始随机盲底
report.random.list.attribute = 随机表属性
report.random.list.name = 随机表名称
report.random.list.all.site = 全部中心
report.random.list.applicable.site = 适用中心
report.random.list.status = 随机表状态
report.random.list.status.open = 开启
report.random.list.status.close = 关闭
report.random.list.status.invalidate = 作废
report.random.list.create.time = 生成时间
report.random.list.create.by = 生成者
report.random.list.block.rule = 区组规则
report.random.list.random.number.rule = 随机号规则
report.random.list.block.rule.order = 顺序
report.random.list.block.rule.reverse = 乱序
report.random.list.random.number.count = 随机号总数量
report.random.list.initial.number = 初始编号
report.random.list.end.number = 终止编号
report.random.list.number.length = 号码长度
report.random.list.prefix = 号码前缀
report.random.list.seed = 随机种子
report.random.list.block.configuration = 区组配置
report.random.list.block.length = 区组长度
report.random.list.block.number = 区组数
report.random.list.group.ratio = 组别比例
report.random.list.group.config = 组别配置
report.random.list.factor.code = 分层因素编号
report.random.list.factor.name = 分层因素名称
report.random.list.factor.option = 选项值
report.random.list.factor.country = 国家是否作为分层因素
report.random.list.factor.site = 中心是否作为分层因素
report.random.list.factor.region = 区域是否作为分层因素
report.random.list.factor.no = 否
report.random.list.factor.yes = 是
report.random.list.probability = 偏倚概率
report.random.list.factor.ratio = 分层因素权重比
report.visit.forecast.visit.status = 访视状态
report.visit.forecast.notice.content = 通知内容
report.visit.forecast.notice.time = 推送时间
report.visit.forecast.notice.user = 推送人
report.visit.forecast.notice.type = 推送方式
report.visit.forecast.notice.email = 通知对象
report.subject.visit.OutSizeNotCompleted = 超窗未完成
report.subject.visit.InProgress = 进行中
report.subject.visit.OutSizeCompleted = 超窗已完成
report.subject.visit.Prepare = 未开始
report.subject.visit.CompletedOnSchedule = 按期已完成
report.subject.visit.app.notice = APP通知
report.subject.visit.message.notice = 短信通知
simulate_random.name = 模拟随机
simulate_random.parameter = 模拟随机参数
simulate_random.parameter.setting.name = 设置名称:
simulate_random.parameter.site.quantity = 研究机构数量:
simulate_random.parameter.number.runs = 运行次数:
simulate_random.parameter.subject.quantity = 受试者数量:
simulate_random.detail.meanStandard = 均数±标准差
simulate_random.layered.overview = 分层总览
simulate_random.layered.overview.no = 序号
simulate_random.layered.overview.layer = 分层
simulate_random.site.overview = 中心总览
simulate_random.site.overview.no = 序号
simulate_random.site.overview.site = 中心
simulate_random.site.details = 中心详情
simulate_random.site.details.no = 序号
simulate_random.site.details.site = 中心
simulate_random.site.details.number.people = 人数
simulate_random.site.details.unbalanced = 不平衡
simulate_random.site.details.total = 总计
setting.user.list.No = 序号
setting.user.list.email = 邮箱
setting.user.list.fullName = 姓名
setting.user.list.telephone = 手机号
setting.user.list.company = 公司
setting.user.list.description = 说明
setting.user.list.role = 角色
setting.user.list.role.common = 通用
setting.user.list.role.dtp = DTP
setting.roles.list.role = 角色
setting.roles.list.menu = 菜单
setting.roles.list.operation = 操作
export.notifications.configuration.report.type = 邮件类型
export.notifications.configuration.report.role = 角色勾选
export.notifications.configuration.report.content.configuration = 内容配置勾选
export.notifications.configuration.report.scene = 场景勾选
export.notifications.configuration.report.excludeRecipientList = 排除收件人
projectSettings.basicInformation.projectProperties.projectType.basicStudy = 基本研究
projectSettings.basicInformation.projectProperties.projectType.cohortStudy = 群组研究
projectSettings.basicInformation.projectProperties.projectType.reRandomizationStudy = 再随机研究
form.control.type.format.numberLength = 数字长度
form.control.type.format.decimalLength = 小数（整数+小数点+小数）
calendar.button.site.visit.matter.notice.history.people.system = 系统
configuration.export.treatment.design.shipment.other.drug = *为未编号研究产品
form.control.type.options.one = 初始剂量
form.control.type.options.two = 维持上一次的剂量
form.control.type.options.three = 剂量减少，选择将受试者当前剂量降低一个剂量级别
configureReport.basicInformation = 基本信息
configureReport.basicInformation.projectTimeZone = 项目时区
configureReport.basicInformation.projectType = 项目类型
configureReport.basicInformation.projectType.basicStudy = 基本研究
configureReport.basicInformation.projectType.cohortStudy = 群组研究
configureReport.basicInformation.projectType.reRandomizationStudy = 再随机研究
configureReport.basicInformation.projectOrderCheck = 中心研究产品库存核查
configureReport.basicInformation.projectOrderCheck.timing = 定时（包含手动核查）
configureReport.basicInformation.projectOrderCheck.realTime = 实时
configureReport.basicInformation.projectOrderCheck.notApplicable = 不适用
configureReport.basicInformation.projectOrderConfirmation = 中心回收订单确认
configureReport.basicInformation.projectOrderConfirmation.open = 是
configureReport.basicInformation.projectOrderConfirmation.close = 否
configureReport.basicInformation.projectDeIsolationApproval = 解隔离审批
configureReport.basicInformation.projectDeIsolationApproval.open = 是
configureReport.basicInformation.projectDeIsolationApproval.close = 否
configureReport.basicInformation.projectUnblindingControl = 揭盲控制
configureReport.basicInformation.projectUnblindingControl.false = 否
configureReport.basicInformation.projectUnblindingControl.unblinding = 紧急揭盲
configureReport.basicInformation.projectUnblindingControl.unblinding.sms = 审批确认-短信
configureReport.basicInformation.projectUnblindingControl.unblinding.process = 审批确认-流程操作
configureReport.basicInformation.projectUnblindingControl.unblinding.code = 揭盲码
configureReport.basicInformation.projectUnblindingControl.pvUnblinding = PV揭盲
configureReport.basicInformation.projectUnblindingControl.pvUnblinding.sms = 审批确认-短信
configureReport.basicInformation.projectUnblindingControl.pvUnblinding.process = 审批确认-流程操作
configureReport.basicInformation.projectUnblindingControl.IpUnblinding = 研究产品揭盲
configureReport.basicInformation.projectUnblindingControl.IpUnblinding.sms = 审批确认-短信
configureReport.basicInformation.projectUnblindingControl.IpUnblinding.process = 审批确认-流程操作
configureReport.basicInformation.projectOrderApprovalControl = 研究中心订单申请
configureReport.basicInformation.projectOrderApprovalControl.sms = 短信
configureReport.basicInformation.projectOrderApprovalControl.process = 流程操作
configureReport.basicInformation.projectOrderApprovalControl.false = 否
configureReport.basicInformation.projectNotice = 访视通知
configureReport.basicInformation.projectNotice.open = 是
configureReport.basicInformation.projectNotice.close = 否
configureReport.basicInformation.projectConnectEdc = 对接EDC
configureReport.basicInformation.projectPushMode = 数据推送方式
configureReport.basicInformation.projectPushMode.real = EDC实时请求
configureReport.basicInformation.projectPushMode.active = IRT全量推送
configureReport.basicInformation.projectSynchronizationMode = 同步方式
configureReport.basicInformation.projectSynchronizationMode.screen = 受试者筛选时同步
configureReport.basicInformation.projectSynchronizationMode.random = 受试者随机时同步
configureReport.cohort = 群组
configureReport.stage = 阶段
configureReport.generalSituation = 概况
configureReport.allOfThem = 的所有
configureReport.name = 名称
configureReport.projectDetails = 项目详情
configureReport.projectDetails.attributeConfigure = 属性配置
configureReport.projectDetails.attributeConfigure.systemConfiguration = 系统配置
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomize = 随机化
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomize.random = 随机
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomize.non-randomized = 非随机化
configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomizationID = 随机号展示
configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomizationID.show = 展示
configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomizationID.notShow = 不展示
configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomSequenceNumber = 随机顺序号展示
configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomSequenceNumber.show = 展示
configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomSequenceNumber.notShow = 不展示
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomSequenceNumberPrefix = 随机顺序号前缀
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomSequenceNumberDigit = 随机顺序号位数
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomSequenceNumberStart = 随机顺序号起始数
configureReport.projectDetails.attributeConfigure.systemConfiguration.treatmentDesign = 发药设计
configureReport.projectDetails.attributeConfigure.systemConfiguration.treatmentDesign.dispensing.yes = 发药
configureReport.projectDetails.attributeConfigure.systemConfiguration.treatmentDesign.dispensing.no = 不发药
configureReport.projectDetails.attributeConfigure.systemConfiguration.dtpRule = DTP规则
configureReport.projectDetails.attributeConfigure.systemConfiguration.dtpRule.ip = 研究产品
configureReport.projectDetails.attributeConfigure.systemConfiguration.dtpRule.visitFlow = 访视流程
configureReport.projectDetails.attributeConfigure.systemConfiguration.dtpRule.notApplicable = 不适用
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck = 随机研究产品供应核查
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule1 = 所有的分组，有供应后允许随机
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule2 = 已分配的分组，有供应后允许随机
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule3 = 强制随机到有供应的分组
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule3.least = 至少
configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule3.groups = 个分组需有供应
configureReport.projectDetails.attributeConfigure.systemConfiguration.blindDesign = 盲法
configureReport.projectDetails.attributeConfigure.systemConfiguration.blindDesign.blind = 盲态
configureReport.projectDetails.attributeConfigure.systemConfiguration.blindDesign.open = 开放
configureReport.projectDetails.attributeConfigure.systemConfiguration.subjectScreeningProcess = 受试者筛选流程
configureReport.projectDetails.attributeConfigure.systemConfiguration.subjectScreeningProcess.open = 是
configureReport.projectDetails.attributeConfigure.systemConfiguration.subjectScreeningProcess.close = 否
configureReport.projectDetails.attributeConfigure.subjectIDRules = 受试者号规则
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectNumberInputRule = 受试者号录入规则
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectNumberInputRule.rule1 = 自定义
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectNumberInputRule.rule2 = 自动递增且在项目中唯一
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectNumberInputRule.rule3 = 自动递增且在中心中唯一
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectPrefix = 受试者前缀
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectPrefix.have = 有
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectPrefix.nothing = 无
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectIDPrefix = 受试者号前缀
configureReport.projectDetails.attributeConfigure.subjectIDRules.replacementTextForSubjectID = 受试者号替换文本
configureReport.projectDetails.attributeConfigure.subjectIDRules.replacementTextEnForSubjectID = 受试者号替换文本(英文)
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectIDDigit = 受试者号位数
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectReplacement = 受试者替换
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectReplacement.open = 是
configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectReplacement.close = 否
configureReport.projectDetails.attributeConfigure.subjectIDRules.takeCare = 注：若受试者号前缀设置为{siteNO}，则实际受试者编号为09001，09为中心编号，001为受试者顺序号。
configureReport.projectDetails.attributeConfigure.otherRules = 其他规则
configureReport.projectDetails.attributeConfigure.otherRules.stopUnblindedSubjects = 停用非盲受试者
configureReport.projectDetails.attributeConfigure.otherRules.stopUnblindedSubjects.true = 开启
configureReport.projectDetails.attributeConfigure.otherRules.stopUnblindedSubjects.false = 关闭
configureReport.projectDetails.attributeConfigure.otherRules.quarantinedIPCountingRule = 使用隔离单品计算规则
configureReport.projectDetails.attributeConfigure.otherRules.quarantinedIPCountingRule.true = 开启
configureReport.projectDetails.attributeConfigure.otherRules.quarantinedIPCountingRule.false = 关闭
configureReport.projectDetails.attributeConfigure.otherRules.transportAccordingPackaging = 按包装运输
configureReport.projectDetails.attributeConfigure.otherRules.transportAccordingPackaging.true = 开启
configureReport.projectDetails.attributeConfigure.otherRules.transportAccordingPackaging.false = 关闭
configureReport.projectDetails.attributeConfigure.otherRules.deactivate = 【停用非盲受试者】：开启后，紧急揭盲的受试者，不再允许继续发放。
configureReport.projectDetails.attributeConfigure.otherRules.quarantine = 【隔离单品计算规则】：开启后，运行运送算法时，将隔离单品默认计算为研究中心的可用库存。
configureReport.projectDetails.attributeConfigure.otherRules.packing = 【按包装运输】：开启后，订单新增等会按照包装运输。
configureReport.projectDetails.randomConfigure = 随机配置
configureReport.projectDetails.randomConfigure.randomDesign = 随机设计
configureReport.projectDetails.randomConfigure.randomDesign.randomType = 随机类型
configureReport.projectDetails.randomConfigure.randomDesign.randomType.region = 区组随机
configureReport.projectDetails.randomConfigure.randomDesign.randomType.min = 最小化随机
configureReport.projectDetails.randomConfigure.randomDesign.group = 组别名称
configureReport.projectDetails.randomConfigure.randomDesign.regionFactor = 地区分层
configureReport.projectDetails.randomConfigure.randomDesign.regionFactor.country = 国家
configureReport.projectDetails.randomConfigure.randomDesign.regionFactor.site = 中心
configureReport.projectDetails.randomConfigure.randomDesign.regionFactor.region = 区域
configureReport.projectDetails.randomConfigure.randomDesign.factorOption = 分层因素(选项值)
configureReport.projectDetails.randomConfigure.randomDesign.factor = 分层因素
configureReport.projectDetails.randomConfigure.randomDesign.factor.fieldNumber = 字段编号
configureReport.projectDetails.randomConfigure.randomDesign.factor.fieldName = 字段名称
configureReport.projectDetails.randomConfigure.randomDesign.factor.variable = 变量名称
configureReport.projectDetails.randomConfigure.randomDesign.factor.controlType = 控件类型
configureReport.projectDetails.randomConfigure.randomDesign.factor.option = 选项值
configureReport.projectDetails.randomConfigure.randomDesign.factor.status = 状态
configureReport.projectDetails.randomConfigure.randomDesign.factor.status.valid = 有效
configureReport.projectDetails.randomConfigure.randomDesign.factor.status.invalid = 无效
configureReport.projectDetails.randomConfigure.randomDesign.form.page = 页面
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation = 分层计算
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.fieldNumber = 字段编号
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.formulaType = 公式类型
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.formulaType.bmi = BMI
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.formulaType.age = 年龄
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.customFormula = 自定义公式
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.retainDecimals = 保留小数位
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.layeredName = 分层名称
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.layeredOption = 分层选项映射
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.status = 状态
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.status.valid = 有效
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.status.invalid = 无效
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi = 分层计算—BMI
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.takeCare = 注：（1）BMI计算公式：(体重[kg]÷(身高[m])²。
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.fieldNumber = 字段编号
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.formula = 计算公式
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.weightName = 录入体重字段名称
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.heightName = 录入身高字段名称
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.controlType = 控件类型
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.layeredName = 分层名称
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.layeredOption = 分层选项映射
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.status = 状态
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.status.valid = 有效
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.status.invalid = 无效
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age = 分层计算—年龄
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.takeCare = 注：(1)	年龄：按365.25天/年进行计算。
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.fieldNumber = 字段编号
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.formula = 计算公式
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.formula.age = 年龄
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.fieldName = 录入字段名称
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.retainDecimals = 保留小数位
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.controlType = 控件类型
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.formatType = 格式类型
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.layeredName = 分层名称
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.layeredOption = 分层选项映射
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.status = 状态
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.status.valid = 有效
configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.status.invalid = 无效
configureReport.projectDetails.randomConfigure.randomDesign.minimize.calc.factor = 随机分层
configureReport.projectDetails.randomConfigure.randomDesign.minimize.calc.actual = 实际分层
configureReport.projectDetails.formConfigure = 表单配置
configureReport.projectDetails.formConfigure.subjectRegistration = 受试者登记
configureReport.projectDetails.formConfigure.subjectRegistration.fieldName = 字段名称
configureReport.projectDetails.formConfigure.subjectRegistration.isEditable = 是否可修改
configureReport.projectDetails.formConfigure.subjectRegistration.isEditable.true = 是
configureReport.projectDetails.formConfigure.subjectRegistration.isEditable.false = 否
configureReport.projectDetails.formConfigure.subjectRegistration.required = 必填
configureReport.projectDetails.formConfigure.subjectRegistration.required.true = 是
configureReport.projectDetails.formConfigure.subjectRegistration.required.false = 否
configureReport.projectDetails.formConfigure.subjectRegistration.variableId = 变量ID
configureReport.projectDetails.formConfigure.subjectRegistration.controlType = 控件类型
configureReport.projectDetails.formConfigure.subjectRegistration.option = 选项值
configureReport.projectDetails.formConfigure.subjectRegistration.formatType = 格式类型
configureReport.projectDetails.formConfigure.subjectRegistration.variableFormat = 变量格式
configureReport.projectDetails.formConfigure.subjectRegistration.variableRange = 变量范围
configureReport.projectDetails.formConfigure.subjectRegistration.status = 状态
configureReport.projectDetails.formConfigure.subjectRegistration.status.valid = 有效
configureReport.projectDetails.formConfigure.subjectRegistration.status.invalid = 无效
configureReport.projectDetails.formConfigure.subjectRegistration.currentTime = 当前时间
configureReport.projectDetails.formConfigure.customFormula = 公式计算
configureReport.projectDetails.formConfigure.customFormula.fieldName = 字段名称
configureReport.projectDetails.formConfigure.customFormula.required = 必填
configureReport.projectDetails.formConfigure.customFormula.required.true = 是
configureReport.projectDetails.formConfigure.customFormula.required.false = 否
configureReport.projectDetails.formConfigure.customFormula.variableId = 变量ID
configureReport.projectDetails.formConfigure.customFormula.controlType = 控件类型
configureReport.projectDetails.formConfigure.customFormula.formatType = 格式类型
configureReport.projectDetails.formConfigure.customFormula.variableFormat = 变量格式
configureReport.projectDetails.formConfigure.customFormula.variableRange = 变量范围
configureReport.projectDetails.formConfigure.customFormula.status = 状态
configureReport.projectDetails.formConfigure.customFormula.status.valid = 有效
configureReport.projectDetails.formConfigure.customFormula.status.invalid = 无效
configureReport.projectDetails.formConfigure.doseAdjustment = 剂量调整
configureReport.projectDetails.formConfigure.doseAdjustment.fieldName = 字段名称
configureReport.projectDetails.formConfigure.doseAdjustment.required = 必填
configureReport.projectDetails.formConfigure.doseAdjustment.required.true = 是
configureReport.projectDetails.formConfigure.doseAdjustment.required.false = 否
configureReport.projectDetails.formConfigure.doseAdjustment.variableId = 变量ID
configureReport.projectDetails.formConfigure.doseAdjustment.controlType = 控件类型
configureReport.projectDetails.formConfigure.doseAdjustment.option = 选项值
configureReport.projectDetails.formConfigure.doseAdjustment.status = 状态
configureReport.projectDetails.formConfigure.doseAdjustment.status.valid = 有效
configureReport.projectDetails.formConfigure.doseAdjustment.status.invalid = 无效
configureReport.projectDetails.formConfigure.layeredCalculation = 分层计算
configureReport.projectDetails.formConfigure.layeredCalculation.fieldName = 字段名称
configureReport.projectDetails.formConfigure.layeredCalculation.isEditable = 是否可修改
configureReport.projectDetails.formConfigure.layeredCalculation.isEditable.true = 是
configureReport.projectDetails.formConfigure.layeredCalculation.isEditable.false = 否
configureReport.projectDetails.formConfigure.layeredCalculation.required = 必填
configureReport.projectDetails.formConfigure.layeredCalculation.required.true = 是
configureReport.projectDetails.formConfigure.layeredCalculation.required.false = 否
configureReport.projectDetails.formConfigure.layeredCalculation.variableId = 变量ID
configureReport.projectDetails.formConfigure.layeredCalculation.controlType = 控件类型
configureReport.projectDetails.formConfigure.layeredCalculation.option = 选项值
configureReport.projectDetails.formConfigure.layeredCalculation.formatType = 格式类型
configureReport.projectDetails.formConfigure.layeredCalculation.variableFormat = 变量格式
configureReport.projectDetails.formConfigure.layeredCalculation.variableRange = 变量范围
configureReport.projectDetails.formConfigure.layeredCalculation.status = 状态
configureReport.projectDetails.formConfigure.layeredCalculation.status.valid = 有效
configureReport.projectDetails.formConfigure.layeredCalculation.status.invalid = 无效
configureReport.projectDetails.formConfigure.layeredCalculation.currentTime = 当前时间
configureReport.projectDetails.ipManagement = 研究产品管理
configureReport.projectDetails.ipManagement.visitManagement = 访视管理
configureReport.projectDetails.ipManagement.visitManagement.cycleVersion = 访视版本
configureReport.projectDetails.ipManagement.visitManagement.visitOffsetType = 访视偏倚类型
configureReport.projectDetails.ipManagement.visitManagement.visitNumber = 访视编号
configureReport.projectDetails.ipManagement.visitManagement.visitName = 访视名称
configureReport.projectDetails.ipManagement.visitManagement.group = 组别
configureReport.projectDetails.ipManagement.visitManagement.intervalDuration = 间隔时长
configureReport.projectDetails.ipManagement.visitManagement.window = 窗口期
configureReport.projectDetails.ipManagement.visitManagement.isDispense = 允许发放
configureReport.projectDetails.ipManagement.visitManagement.isRandomize = 允许随机
configureReport.projectDetails.ipManagement.visitManagement.isDTP = 允许DTP
configureReport.projectDetails.ipManagement.visitManagement.isSubjectReplace = 允许受试者替换
configureReport.projectDetails.ipManagement.visitManagement.isDoseAdjustment = 剂量调整
configureReport.projectDetails.ipManagement.treatmentDesign = 研究产品配置
configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp = DTP研究产品
configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.takeCare = 注：黄色字体为未编号研究产品。
configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.ip = 研究产品
configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.dtpMode = DTP方式
configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.send.site = 中心（中心库存）
configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.send.siteSubject = 中心（直接寄送受试者）
configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.send.depotSubject = 库房（直接寄送受试者）
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen = 按标签/开放配置
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.takeCare = 注：黄色字体为未编号研究产品。
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.group = 组别
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.visitName = 访视名称
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.IpName = 研究产品名称
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.dispensationQuantity = 发放数量
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.customFormula = 自定义公式
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.combinedDispensation = （组合）发放标签
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.ipSpecification = 研究产品规格
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.specification = 规格
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.automaticAssignment = 是否自动赋值
configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.calculationUnit = 自动赋值计算单位
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig = 按公式计算
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge = 年龄
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.takeCare = 注： （1）年龄：按365.25天/年进行计算； （2）黄色字体为未编号研究产品。
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.group = 组别
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.visitName = 访视名称
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.IpName = 研究产品名称
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.ipSpecification = 研究产品规格
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.ageRange = 年龄范围
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.dispensationQuantity = 发放数量
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.isOpenIp = 是否为开放研究产品
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.keepDecimalPlaces = 录入数据计算时保留小数位
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.calculation = 上次访视计算体重
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.actual = 上次访视实际体重
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.random = 随机访视体重
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight = 体重
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.takeCare = 注：黄色字体为未编号研究产品。
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.group = 组别
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.visitName = 访视名称
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.IpName = 研究产品名称
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.ipSpecification = 研究产品规格
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.weightRange = 体重范围
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.dispensationQuantity = 发放数量
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.isOpenIp = 是否为开放研究产品
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.keepDecimalPlaces = 录入数据计算时保留小数位
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.weightComparisonCalculation = 体重比较计算
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.comparedWith = 相较于
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.change = 变化的百分比（%）
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.calculation = 本次计算体重为
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA = 简易体表面积BSA
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.takeCare = 注： （1）简易体表面积BSA公式: [体重 (kg) x 身高 (cm)/3600]1/2；（2）黄色字体为未编号研究产品。
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.group = 组别
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.visitName = 访视名称
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.IpName = 研究产品名称
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.ipSpecification = 研究产品规格
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.unitCapacity = 单位容量
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.unitCalculationStandard = 单位计算标准
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.isOpenIp = 是否为开放研究产品
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.weightComparisonCalculation = 体重比较计算
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.comparedWith = 相较于
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.change = 变化的百分比（%）
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.calculation = 本次计算体重为
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA = 其他体表面积（按自定义公式）
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.takeCare1 = 注:（1）自定义公式：
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.takeCare2 = （2）黄色字体为未编号研究产品。
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.group = 组别
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.visitName = 访视名称
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.IpName = 研究产品名称
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.ipSpecification = 研究产品规格
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.unitCapacity = 单位容量
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.unitCalculationStandard = 单位计算标准
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.isOpenIp = 是否为开放研究产品
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.weightComparisonCalculation = 体重比较计算
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.comparedWith = 相较于
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.change = 变化的百分比（%）
configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.calculation = 本次计算体重为
form.control.type.input = 输入框
form.control.type.inputNumber = 数字输入框
form.control.type.textArea = 多行文本框
form.control.type.checkbox = 复选框
form.control.type.radio = 单选框
form.control.type.switch = 开关
form.control.type.date = 日期选择框
form.control.type.dateTime = 时间选择框
form.control.type.select = 下拉框
form.control.type.format.characterLength = 字符长度
form.control.type.format.inputNumber.numberLength = 数字长度
form.control.type.format.inputNumber.decimalLength = 小数（整数+小数点+小数）
form.control.type.format.checkbox = 多选框
subjectReport.title = 受试者轨迹
subjectReport.projectName = 项目名称
subjectReport.sponsor = 申办方
subjectReport.type.cohort = 群组
subjectReport.type.stage = 阶段
subjectReport.autograph = 研究者签名
subjectReport.signingDate = 签署日期
subjectReport.history.serialNumber = 序号
subjectReport.history.operator = 操作人
subjectReport.history.operationTime = 操作时间
subjectReport.history.operationContent = 操作内容
simulateRandomReport.configureParameterDetail = 随机配置参数
simulateRandomReport.configureParameterDetail.randomType = 随机类型
simulateRandomReport.configureParameterDetail.randomType.blockRandomization = 区组随机
simulateRandomReport.configureParameterDetail.randomType.minimizedRandomization = 最小化随机
simulateRandomReport.configureParameterDetail.randomizationListName = 随机列表名称
simulateRandomReport.configureParameterDetail.biasProbability = 偏倚概率
simulateRandomReport.configureParameterDetail.randomCount = 随机号数量
simulateRandomReport.configureParameterDetail.groupConfiguration = 组别配置
simulateRandomReport.configureParameterDetail.groupConfiguration.groupName = 组别名称
simulateRandomReport.configureParameterDetail.groupConfiguration.subGroupName = 子组别
simulateRandomReport.configureParameterDetail.groupConfiguration.groupProportion = 组别比例
simulateRandomReport.configureParameterDetail.stratificationFactor = 分层因素
simulateRandomReport.configureParameterDetail.stratificationFactor.factorName = 分层因素名称
simulateRandomReport.configureParameterDetail.stratificationFactor.option = 选项值
simulateRandomReport.configureParameterDetail.stratificationFactor.weightRatio = 权重比
simulateRandomReport.configureParameterDetail.isCountry = 国家是否作为分层因素
simulateRandomReport.configureParameterDetail.isCountry.true = 是
simulateRandomReport.configureParameterDetail.isCountry.false = 否
simulateRandomReport.configureParameterDetail.isSite = 中心是否作为分层因素
simulateRandomReport.configureParameterDetail.isSite.true = 是
simulateRandomReport.configureParameterDetail.isSite.false = 否
simulateRandomReport.configureParameterDetail.isRegion = 区域是否作为分层因素
simulateRandomReport.configureParameterDetail.isRegion.true = 是
simulateRandomReport.configureParameterDetail.isRegion.false = 否
simulateRandomReport.simulateRandomDetail = 模拟随机
simulateRandomReport.simulateRandomDetail.parameter = 模拟随机参数
simulateRandomReport.simulateRandomDetail.parameter.simulateRandomName = 模拟随机的名称
simulateRandomReport.simulateRandomDetail.parameter.randomListName = 启用的随机列表名称
simulateRandomReport.simulateRandomDetail.parameter.siteNumber = 中心数
simulateRandomReport.simulateRandomDetail.parameter.regionNumber = 区域数
simulateRandomReport.simulateRandomDetail.parameter.countryNumber = 国家数
simulateRandomReport.simulateRandomDetail.parameter.runNumber = 运行次数
simulateRandomReport.simulateRandomDetail.parameter.subjectQuantity = 受试者数
simulateRandomReport.simulateRandomDetail.parameter.factorRatio = 分层例数
simulateRandomReport.simulateRandomDetail.label.project = 项目
simulateRandomReport.simulateRandomDetail.label.serial = 序号
simulateRandomReport.simulateRandomDetail.label.site = 中心
simulateRandomReport.simulateRandomDetail.label.region = 区域
simulateRandomReport.simulateRandomDetail.label.country = 国家
simulateRandomReport.simulateRandomDetail.label.factor = 分层
simulateRandomReport.simulateRandomDetail.label.combinationFactor = 组合分层
simulateRandomReport.simulateRandomDetail.label.subjectCountMin = 受试者例数最小值
simulateRandomReport.simulateRandomDetail.label.total = 总计
simulateRandomReport.simulateRandomDetail.label.runCount = 运行次数
simulateRandomReport.simulateRandomDetail.label.peopleCount = 人数
simulateRandomReport.simulateRandomDetail.label.unbalanced = 不平衡
simulateRandomReport.simulateRandomDetail.overviewDetail = 总览
simulateRandomReport.simulateRandomDetail.overviewDetail.averageStandardDeviation = 均数±标准差
simulateRandomReport.simulateRandomDetail.overviewDetail.min = 最小值
simulateRandomReport.simulateRandomDetail.overviewDetail.numberOfRunningImbalances = 不均衡运行次数
simulateRandomReport.simulateRandomDetail.detailedResults = 详细结果
medicine.status.toBeWarehoused = 待入仓
medicine.status.available = 可用
medicine.status.delivered = 已确认
medicine.status.transit = 已运送
medicine.status.quarantine = 已隔离
medicine.status.used = 已使用
medicine.status.lose = 丢失/作废
medicine.status.expired = 已过期
medicine.status.receive = 已领药
medicine.status.return = 已退货
medicine.status.destroy = 已销毁
medicine.status.InOrder = 待确认
medicine.status.stockPending = 待入库
medicine.status.apply = 已申请
medicine.status.fzn = 冻结
medicine.status.toBeApproved = 待审批
medicine.status.locked = 锁定
medicine.status.dsm = 待扫码
medicine.status.dsh = 待审核
medicine.status.shsb = 审核失败
notice.exclude_recipient_list.email.account = 邮箱账户

[operation_log]
notifications.notice.env_name = 环境
notifications.notice.basic.settings = 基本设置
notifications.notice.basic.settings.emailLanguage_automatedTasks = 邮件语言-自动任务
notifications.notice.basic.settings.emailLanguage_manualTasks = 邮件语言-手动任务
notifications.notice.basic.settings.email.automatedTasks = 自动任务
notifications.notice.basic.settings.email.manualTasks = 手动任务
notifications.notice.basic.settings.emailLanguage_zh = 中文
notifications.notice.basic.settings.emailLanguage_en = 英文
notifications.notice.basic.settings.emailLanguage_zh_en = 中英文
notifications.notice.subject.add = 受试者登记
notifications.notice.subject.random = 受试者随机
notifications.notice.subject.signOut = 受试者停用
notifications.notice.subject.replace = 受试者替换
notifications.notice.subject.update = 受试者修改
notifications.notice.subject.screen = 受试者筛选
notifications.notice.subject.dispensing = 受试者发放
notifications.notice.subject.alarm = 受试者警戒
notifications.notice.subject.unblinding = 紧急揭盲
notifications.notice.medicine.isolation = 研究产品隔离
notifications.notice.medicine.order = 研究产品订单
notifications.notice.medicine.reminder = 研究产品有效期
notifications.notice.medicine.alarm = 中心库存警戒
notifications.notice.medicine.alarm.scene.no_automatic_title = 研究产品库存
notifications.notice.medicine.alarm.scene.forecast_title = 库存使用时间预测   预测提醒提前天数：
notifications.notice.storehouse.alarm = 库房库存警戒
notifications.notice.order.timeout = 订单超时
notifications.notice.subject.alert.threshold = 受试者上限设置提醒
notifications.notice.email.content = 邮件内容
notifications.roles = 角色
notifications.exclusiveReceivers = 排除收件人
notifications.dispensing.contentConfiguration = 内容配置
notifications.dispensing.contentConfiguration.group = 组别
notifications.dispensing.contentConfiguration.randomizationNumber = 随机号
notifications.dispensing.contentConfiguration.projectNumber = 项目编号
notifications.dispensing.contentConfiguration.projectName = 项目名称
notifications.dispensing.contentConfiguration.siteNumber = 中心编号
notifications.dispensing.contentConfiguration.siteName = 中心名称
notifications.dispensing.scene = 场景
notifications.dispensing.scene.Dispense = 发放
notifications.dispensing.scene.unscheduledDispense = 计划外发放
notifications.dispensing.scene.reDispense = 补发
notifications.dispensing.scene.replaceIP = 研究产品替换
notifications.dispensing.scene.retrieveIP = 研究产品取回
notifications.dispensing.scene.actuallyUsedIP = 登记实际使用研究产品
notifications.dispensing.scene.notAttend = 不参加访视
notifications.subject.screen.success = 筛选成功
notifications.subject.screen.fail = 筛选失败
notifications.subject.update.form.factor = 表单/分层信息
notifications.subject.update.screen = 筛选信息
notifications.subject.update.stop = 停用信息
notifications.subject.update.finish = 完成研究信息
notifications.subject.update.shortname = 受试者号
notifications.isolation.scene = 场景
notifications.isolation.scene.quarantine = 隔离
notifications.isolation.scene.release = 解隔离
notifications.order.scene = 场景
notifications.order.scene.apply = 申请
notifications.order.scene.applicationApprovalFailed = 申请审批失败
notifications.order.scene.createManualOrder = 创建(手动订单)
notifications.order.scene.cancel = 取消
notifications.order.scene.confirm = 确认
notifications.order.scene.close = 关闭
notifications.order.scene.deliver = 运送
notifications.order.scene.receive = 接收
notifications.order.scene.termination = 终止
notifications.order.scene.lost = 丢失
notifications.order.scene.createAutomaticOrder = 创建(自动订单)
notifications.order.scene.creationFailedAutomaticOrder = 创建失败(自动订单)
notifications.order.scene.automaticOrderAlarm = 自动订单警戒
notifications.notice.order.timeout.lateShipmentAlertSetting = 订单确认起天数
notifications.notice.order.timeout.lateShipmentSendAlertSetting = 订单运送起天数
notifications.notice.email.content.body.configuration = 正文配置
notifications.notice.email.content.body.configuration.project.name = 项目名称
notifications.notice.email.content.body.configuration.project.no = 项目编号
notifications.notice.email.content.body.configuration.project.environment = 项目环境
notifications.notice.email.content.body.configuration.site.name = 中心名称
notifications.notice.email.content.body.configuration.site.number = 中心编号
notifications.notice.email.content.scene = 场景
notifications.notice.email.content.scene.subject.register = 受试者登记
notifications.notice.email.content.scene.subject.randomization = 受试者随机
notifications.notice.email.content.scene.subject.signOut = 受试者停用
notifications.notice.email.content.scene.subject.replace = 受试者替換
notifications.notice.email.content.scene.subject.update = 受试者修改
notifications.notice.email.content.scene.subject.dispensation = 受试者发放
notifications.notice.email.content.scene.subject.alert = 受试者警戒
notifications.notice.email.content.scene.emergency.unblinding = 紧急揭盲
notifications.notice.email.content.scene.ip.quarantine = 研究产品隔离
notifications.notice.email.content.scene.shipment.order = 研究产品订单
notifications.notice.email.content.scene.ip.expiration.reminder = 研究产品有效期
notifications.notice.email.content.scene.inventory.alert = 研究产品警戒
notifications.notice.email.content.scene.depot.inventory.alert = 库房警戒
notifications.notice.email.content.scene.late.shipment.alert = 订单超时
notifications.notice.email.content.scene.subject.alert.threshold = 受试者上限设置提醒
rest.receiveRest.assured.ip.receive = 码上放心 研究产品领药。
rest.receiveRest.assured.ip.return = 码上放心 研究产品退货。
rest.receiveRest.assured.ip.destroy = 码上放心 研究产品销毁。
operation.projects.main.setting.function.admin.cloud = Cloud管理员
operation.projects.main.setting.function.admin.cloud.delete = 用户删除
operation.projects.main.setting.function.admin.cloud.disable = 用户禁用
drug.configure.setting.dose.level = 剂量水平
drug.configure.setting.dose.visit.judgment = 访视判断
drug.configure.setting.doseAdjustment.open = 开启
drug.configure.setting.doseAdjustment.close = 关闭
drug.configure.setting.dose.visitInheritance.true = 是
drug.configure.setting.dose.visitInheritance.false = 否

[error]
env.copy.prod.isCopy = 复制到已有的PROD环境，只允许从PROD复制出来的环境，能复制到PROD中
env.copy.prod.isCopy.cohort.fail = 复制失败。群组不一致，请修改后重新复制迁移。
env.copy.prod.isCopy.stage.fail = 复制失败。阶段不一致，请修改后重新复制迁移。
env.copy.prod.isCopy.core = 复制失败。核心配置不一致，请修改后重新复制迁移。