alarm:
    storehouse:
        content: <p>库存可用研究产品低于警戒值，请及时补充.研究产品信息(库房名称/研究产品名称/警戒数量/剩余数量)：{{.drugInfo}}</p>
        title: Clinflash IRT {{.projectNumber}} {{.envName}} 库房研究产品警戒通知 {{.deportName}}
app:
    unauthorized: 应用未授权
auth:
    fail: 认证失败
cohort:
    status:
        complete: 完成
        draft: 草稿
        enrollment: 入组
        stop: 停止
shipmentMode:
    status:
        set: 发放数量
        reSupply: 再供应量
        max: 最大缓冲量
        supplyRatio: 供应比例
common:
    operator: 操作
    required: 请输入
    error:
        default: 请求遇到了错误
        request: 错误的请求
        not-logged-in: 未登录
        unauthorized: 未授权
        not-found: 未知请求
    checkbox: 复选框
    date: 日期选择框
    dateTime: 时间选择框
    delete:
        fail: 删除失败
        success: 删除成功
    duplicated:
        factors: 因素重复
        names: 名称重复
    no: 否
    input: 输入框
    inputNumber: 数字输入框
    load:
        fail: 加载失败
        success: 加载成功
    nil: 无
    operation:
        edc:
            dsp:
                fail: 当前访视已发放，信息二次返回.
            fail: 受试者已随机
        fail: 操作失败
        success: 操作成功
    radio: 单选框
    remark: 备注
    save:
        fail: 保存失败
        success: 保存成功
    select: 下拉框
    switch: 开关
    textArea: 多行文本框
    yes: 是
    update:
        fail: 更新失败
        success: 更新成功
    wrong:
        parameters: 参数错误
    country: 国家
cross:
    check:
        error: Clinflash IRT 异常通知
customer:
    not:
        exist: 客户不存在
customers:
    delete:
        admin: 当前操作人在该客户下没有admin权限，无权操作
        message: 客户下存在用户，不能删除
    duplicated:
        names: 客户名称重复
dispense_list_download_name: 发放报表
dispensing:
    plan: <p>{{.label}}:{{.subjectNumber}}</p><p>研究产品编号:{{.drugNumber}}</p> <p>访视周期:{{.visitName}}</p> <p>发放时间:{{.dispensingDate}}</p> <p>备注:{{.remark}}</p>
    unscheduled-plan: <p>{{.label}}:{{.subjectNumber}}</p> <p>研究产品编号:{{.drugNumber}}</p> <p>访视周期:{{.visitName}}</p> <p>发放时间:{{.dispensingDate}}</p> <p>计划外发放原因:{{.reason}}</p>
    plan-logistics: <p>{{.label}}:{{.subjectNumber}}</p>  <p>研究产品编号:{{.drugNumber}}</p> <p>访视周期:{{.visitName}}</p> <p>订单号:{{.orderNumber}}</p> <p>发放时间:{{.dispensingDate}}</p> <p>备注:{{.remark}}</p>
    unscheduled-plan-logistics: <p>{{.label}}:{{.subjectNumber}}</p> <p>研究产品编号:{{.drugNumber}}</p> <p>访视周期:{{.visitName}}</p> <p>订单号:{{.orderNumber}}</p> <p>发放时间:{{.dispensingDate}}</p> <p>计划外发放原因:{{.reason}}</p>
    plan-title: Clinflash IRT {{.projectNumber}} {{.envName}}  研究产品发放 {{.siteNumber}} {{.siteName}}
    unscheduled-plan-title: Clinflash IRT  {{.projectNumber}} {{.envName}} 研究产品{{.unscheduled}}发药 {{.siteNumber}} {{.siteName}}
    reissue: <p>{{.label}}:{{.subjectNumber}}</p> <p>研究产品编号:{{.drugNumber}}</p> <p>访视周期:{{.visitName}}</p> <p>发放时间:{{.dispensingDate}}</p> <p>补发原因:{{.remark}}</p>
    reissue-logistics: <p>{{.label}}:{{.subjectNumber}}</p>  <p>研究产品编号:{{.drugNumber}}</p> <p>访视周期:{{.visitName}}</p> <p>订单号:{{.orderNumber}}</p> <p>发放时间:{{.dispensingDate}}</p> <p>补发原因:{{.remark}}</p>
    reissue-title: Clinflash IRT {{.projectNumber}} {{.envName}}  研究产品补发 {{.siteNumber}} {{.siteName}}
    replace: <p>{{.label}}:{{.subjectNumber}}</p><p>替换研究产品编号:{{.replaceNumber}}</p> <p>被替换研究产品编号:{{.drugNumber}}</p> <p>替换时间:{{.dispensingDate}}</p> <p>替换原因:{{.reason}}</p>
    replace-title: Clinflash IRT {{.projectNumber}} {{.envName}}   研究产品替换提醒 {{.siteNumber}} {{.siteName}}
    apply: <p>{{.label}}:{{.subjectNumber}}</p><p>研究产品编号:{{.drugNumber}}</p> <p>访视周期:{{.visitName}}</p> <p>订单号:{{.orderNumber}}</p> <p>备注:{{.remark}}</p>  <p>研究产品申请时间:{{.dispensingDate}}</p>
    apply-title: Clinflash IRT {{.projectNumber}} {{.envName}} 发放申请 {{.siteNumber}} {{.siteName}}
    reissue-dtp: <p>{{.label}}:{{.subjectNumber}}</p><p>研究产品编号:{{.drugNumber}}</p> <p>访视周期:{{.visitName}}</p> <p>订单号:{{.orderNumber}}</p> <p>补发原因:{{.remark}}</p>  <p>研究产品补发申请时间:{{.dispensingDate}}</p>
    reissue-dtp-title: Clinflash IRT {{.projectNumber}} {{.envName}} 补发申请 {{.siteNumber}} {{.siteName}}
    unscheduled-apply: <p>{{.label}}:{{.subjectNumber}}</p> <p>研究产品编号:{{.drugNumber}}</p> <p>访视周期:{{.visitName}}</p> <p>订单号:{{.orderNumber}}</p> <p>计划外发放原因:{{.reason}}</p>  <p>研究产品申请时间:{{.dispensingDate}}</p>
    unscheduled-apply-title: Clinflash IRT {{.projectNumber}} {{.envName}} 计划外申请 {{.siteNumber}} {{.siteName}}
    replace-dtp: "<p>{{.label}}:{{.subjectNumber}}</p>
        <p>研究产品编号:{{.replaceNumber}}</p>
        <p>研究产品编号(被替换)：{{.drugNumber}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>替换时间:{{.dispensingDate}}</p>
        <p>替换原因:{{.reason}}</p>"
    replace-dtp-title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Replacement {{.siteNumber}} {{.siteName}}
    register-title: Clinflash IRT {{.projectNumber}} {{.envName}} 登记实际研究产品提醒 {{.siteNumber}} {{.siteName}}
    register: "<p>{{.label}}:{{.subjectNumber}}</p>
        <p>研究产品编号:{{.drugNumber}}</p>
        <p>访视周期:{{.visitName}}</p>
        <p>发放时间:{{.dispensingTime}}</p>
        <p>备注:{{.remark}}</p>
        <p>实际登记研究产品:{{.registerNumber}}</p>
        <p>操作时间:{{.dispensingDate}}</p>"
    retrieval-title: Clinflash IRT {{.projectNumber}} {{.envName}} 研究产品取回提醒 {{.siteNumber}} {{.siteName}}
    retrieval: "<p>{{.label}}:{{.subjectNumber}}</p>
        <p>研究产品编号:{{.drugNumber}}</p>
        <p>访视周期:{{.visitName}}</p>
        <p>发放时间:{{.dispensingTime}}</p>
        <p>备注:{{.remark}}</p>
        <p>取回研究产品编号:{{.retrievalNumber}}</p>
        <p>操作时间:{{.dispensingDate}}</p>"
    #  发药邮件字段拼接
    group: "<p>组别:{{.group}}</p>"
    subGroup: "<p>子组别:{{.subGroup}}</p>"
    number: "<p>随机号:{{.random_number}}</p>"
    not-attend-title: Clinflash IRT {{.projectNumber}} {{.envName}} 不参加访视 {{.siteNumber}} {{.siteName}}
    single:
        subject: "<p>{{.label}}:{{.subjectNumber}}</p>"
        drugNumber: "<p>研究产品编号:{{.drugNumber}}</p>"
        dispensingDate: "<p>发放时间:{{.dispensingDate}}</p>"
        replaceTime: "<p>替换时间:{{.replaceTime}}</p>"
        orderNumber: "<p>订单号:{{.orderNumber}}</p>"
        visitCycle: "<p>访视周期:{{.visitCycle}}</p>"
        remark: "<p>备注:{{.remark}}</p>"
        registerNumber: "<p>实际登记研究产品:{{.registerNumber}}</p>"
        retrievalNumber: "<p>取回研究产品编号:{{.retrievalNumber}}</p>"
        dispensingTime: "<p>操作时间:{{.dispensingTime}}</p>"
        replaceNumber: "<p>替换研究产品编号:{{.replaceNumber}}</p>"
        beReplaceNumber: "<p>被替换研究产品编号:{{.beReplaceNumber}}</p>"
        unscheduled-dispensing-reason: "<p>计划外发放原因:{{.reason}}</p>"
        unscheduled-dispensing-reason-customer: "<p>{{.dispensingTypeZh}}原因:{{.reason}}</p>"
        re-dispensing-reason: "<p>补发原因:{{.reason}}</p>"
        replace-reason: "<p>研究产品替换原因:{{.reason}}</p>"
        unblindingTime: "<p>揭盲时间:{{.unblindingTime}}</p>"
        unblindingReason: "<p>揭盲原因:{{.unblindingReason}}</p>"
        commonReason: "<p>原因:{{.reason}}</p>"
        approvalCode: "<p>审批编号:{{.approvalCode}}</p>"
        approvalResult: "<p>审批结果:{{.approvalResult}}</p>"
        randomNumber: "<p>随机号:{{.randomNumber}}</p>"
batch-group:
    alert-site-title: Clinflash IRT  {{.projectNumber}} {{.envName}} 受试者中心警戒提醒 {{.siteNumber}} {{.siteName}}
    alert-depot-title: Clinflash IRT  {{.projectNumber}} {{.envName}} 受试者库房警戒提醒 {{.depotName}}
    limit-site-title: Clinflash IRT {{.projectNumber}} {{.envName}} 受试者中心上限提醒  {{.siteNumber}} {{.siteName}}
    limit-depot-title: Clinflash IRT {{.projectNumber}} {{.envName}} 受试者库房上限提醒 {{.depotName}}
    depotName: 库房：{{.depotName}}
    siteName: "中心名称:{{.siteName}}"
    alarm: "<p>批次入组人数达到警戒值。</p> <p>批次号/警戒值/实际人数:  {{.alarm}}</p>"
    limit: "<p>批次入组人数达到警戒值。</p> <p>批次号/警戒值/实际人数: {{.limit}}</p>"
edc:
    error: 请求失败，项目数据推送方式为IRT全量推送。
    version: EDC接口版本错误，请联系EDC相关人员。
    add:
        relation:
            site:
                error: 关联中心失败，请重新关联中心
    block:
        is:
            not:
                nil:
                    error: blockRepeatNo参数为空，发放失败，请重新确认配置
    check:
        matching:
            value:
                error: 复选框有未匹配到的value
    configure:
        drug:
            error: 研究产品查询为空，请重新确认EDC研究产品配置
        visit:
            error: 访视周期查询为空，请重新确认EDC访视周期配置
    drug:
        number:
            error: 查询研究产品编号出错，请重新查询
            mismatch: 查询研究产品编号结果不匹配，请确认替换研究产品编号信息
        reissue:
            error: 补发失败，请联系非盲人员完成操作
        replace:
            error: "替换失败，无法对上一次已完成的访视进行研究产品替换，请重新选择。"
            nil:
                nil: 研究产品编号为空，请重新确认后操作
                error: 替换研究产品编号查询失败，请联系IRT工程师处理
        type:
            error: type参数为空，发放失败，请重新确认配置
    env:
        error: 项目环境信息不存在，请联系IRT系统工程师查看
    factor:
        error: Cohort因素为空，请重新确认
    instance:
        is:
            not:
                nil:
                    error: instanceRepeatNo参数为空，发放失败，请重新确认配置
    matching:
        value:
            error: Value匹配失败，请重新确认单选按钮组或者下拉框的值
    multiple:
        subject: 受试者号查询重复，请确认受试者号信息。
    no:
        subject: 修改失败，请联系IRT工程师处理
    parameter:
        error: 项目编号/环境/中心编号（名称）/受试者号为空，请重新确认EDC配置
    project:
        env:
            number:
                error: 项目编号/环境为空，对接失败，请重新确认配置
    query:
        factor:
            error: Cohort查询失败，请重新确认Cohort因素配置
        project:
            error: 项目查询失败，请联系IRT系统工程师查看
            number:
                error: 项目查询失败，请联系IRT系统工程师查看
            dtp:
                error: DTP项目不支持和EDC对接
        site:
            number:
                error: 中心查询重复，请在IRT系统中确认是否存在多个中心
                relation:
                    site:
                        error: 中心查询重复，请在IRT系统中确认是否存在多个中心
    register:
        synchronization:
            error: 不支持全量同步，接口调用失败，请重新确认EDC对接功能配置
    relation:
        site:
            error: 关联中心失败，请联系IRT系统工程师查看
    site:
        error: 中心查询失败，请联系IRT系统工程师查看
    standard:
        lost:
            site:
                error: 中心信息不全，请联系IRT系统工程师修改
        site:
            error: 中心信息缺失，请联系IRT系统工程师添加
    start:
        site:
            error: 中心启用或激活失败，请重新操作
    subject:
        after:
            dispensing:
                error: 受试者未随机，发放失败，请重新确认
        dispensing:
            error: 受试者未随机，发放失败，请重新确认
        existence: 受试者已存在，请勿重新添加
        random:
            number:
                error: 受试者号为空，请重新确认EDC配置
        register:
            error: 受试者未登记，请重新确认
        status:
            dispensing:
                error: 受试者已随机，随机前发放失败，请重新确认
        site:
            error: IRT返回受试者所在中心不一致，请在IRT上操作受试者转中心后再操作发药行为
    unable:
        at:
            random: 非随机项目功能支持，请确认项目随机配置
    visit:
        error: 访视周期查询为空，请重新确认EDC访视编号与访视周期配置关系
        no:
            dispensing:
                error: 当前访视发放失败，请确认随机前/随机后的属性配置
        number:
            drug:
                configure:
                    error: 研究产品查询重复，请重新确认访视编号与EDC研究产品配置关系
            error: 访视编号为空，对接失败，请重新确认配置
environment:
    duplicated:
        names: 环境名称重复
    alertThresholds:
        limitError: 仅发放项目，阈值上限条件仅允许配置登记/筛选或入组，请重新确认。登记/筛选，根据实际配置控制显示。
        attributeError: 项目属性为“仅发放”，当前阈值上限条件配置已失效，请重新配置。
export:
    project: 项目
    projects:
        number: 项目编号
        name: 项目名称
        cohort: 群组
        stage: 阶段
    barcode: 条形码
    dispensing:
        auto: 自动
        first: 首次
        medicine: 研究产品编号
        medicineName: 研究产品名称
        realMedicine: 实际使用研究产品
        otherMedicineCount: 未编号研究产品数量
        outVisit: 计划外
        reissue: 补发
        replace: 替换
        retrieve: 取回
        register: 登记
        cancel: 撤销
        invalid: 不参加访视
        recover: 恢复发放
        replaceMedicine: 已替换研究产品编号
        room: 房间号
        siteName: 中心名称
        siteNumber: 中心编号
        subject: 受试者
        time: 操作时间
        type: 操作类型
        visit: 访视名称
        visit_number: 访视号
        visitSign: 计划外发放
        visit_apply: 访视申请
        out_visit_apply: 计划外申请
        reissue_apply: 补发申请
        replace_apply: 替换申请
        is_replace: 是否是替换研究产品编号
        is_real: 是否是实际使用研究产品
        operate_time: 发放操作时间
        realMedicineNumber: 实际使用研究产品编号
        weight: 体重
        height: 身高
        age: 年龄
    random_config:
        SubjectReplaceText: 标签(即替换文本)：
        accuracy: 位数精确值：
        attribute: 项目属性
        blind: 是否盲法：
        countryLayered: 是否国家分层：
        regionLayered: 是否区域分层：
        createBy: 生成者：
        createDate: 生成时间：
        runningTime: 模拟运行开始时间：
        generationTime: 模拟报告生成时间：
        digit: 位数限制：
        dispensing: 是否发放：
        export: 配置报告
        simulation_pdf: 模拟随机报告
        factor: 分层因素：
        no: No
        group: 组别：
        instituteLayered: 是否中心分层：
        isFreeze: 运行运送算法时将隔离的单品计算为研究机构中可用存货的一部分：
        isRandom: 中心没有分配随机号不能入组：
        list: 随机列表：
        prefix: 是否使用前缀：
        random: 是否随机：
        random_design: 随机配置
        ratio: 组间比例：
        report: Clinflash IRT是一个随机和试验研究产品供应管理系统，可用此系统进行随机、发放及试验研究产品供应管理。此配置报告包括项目属性、随机设计、治疗设计，用于各方人员可以快速的审核、批准项目的设置，以及项目文件的存档。
        configure_report: Clinflash IRT是一个随机和试验研究产品供应管理系统，可用此系统进行随机、发放及试验研|究产品供应管理。此配置报告包括项目属性、随机设计、治疗设计，用于各方人员可以快速的审|核、批准项目的设置，以及项目文件的存档。
        report_pdf: Clinflash IRT是一个随机和试验研究产品供应管理系统，可用此系统进行随机、发放及试验研究产品供应管理。此模拟随机报告包含项目属性、随机设计、随机模拟设置参数、分层总览、中心总览和详情，用于各方人员可以快速的审核、批准项目的设置，以及项目文件的存档。
        directory: 目录
        summary: 概述
        unitCapacity: 单位容量：
        unitStandard: 单位计算标准：
        ageType: 按年龄
        weightType: 按体重
        bsaType: 简易体表面积
        customerBsaType: 其他体表面积
        table:
            code: 组别代码
            count: 随机发放数量
            countMax: 发放数量
            formulas: 随机发放范围
            label: (组合)发放标签
            formula: 自定义公式
            formulasType: 公式
            medicine: 研究产品名称
            spec: 研究产品规格
        total: 总例数：
        treatment_design: 治疗设计
        yes: Yes
        type: 随机类型：
    room:
        history:
            room: 历史查看房间号
            time: 查看时间
            user: 用户
        project: 项目
    unblinding:
        remark: 揭盲备注
        reason: 揭盲原因
        reason_mark: 揭盲原因备注
        operator: 揭盲操作人
        operate_time: 揭盲操作时间
    random:
        register: 登记
        random: 随机
        exit: 停用
        unBlind: 已揭盲(紧急)
        pv: 已揭盲(PV)
        screenSuccess: 筛选成功
        screenFail: 筛选失败
        finish: 完成研究
        toBeRandom: 待随机
        number: 随机号
    user:
        name: 姓名
        email: 邮箱
        role: 角色
        create_time: 创建时间
        status_effective: 有效
        status_invalid: 无效
    medicine:
        serial_number: 序列号
        ip_name: 研究产品名称
        ip_number: 研究产品编号
        expiration_date: 有效期
        batch_number: 批次号
        package_number: 包装号
        package_number_serialNumber: 包装序列号
    subject:
        number: 受试者号
file_emtpy: 文件列表为空
col_empty_error: 上传随机表存在空白列，请修改后再进行上传
footer: 此邮件为系统自动发送的邮件。对此邮件的回复将不受监控或无响应.<br>
history:
    dispensing:
        updateBatch: 研究产品【更新】，有效期：{{.expirationDate}}，批次号：{{.batchNumber}}，状态：{{.status}}，数量：{{.count}}
        cancel: 【发放撤销】{{.label}}：{{.subject}}，撤销原因：{{.reason}}。
        dispensing: 【发放】 {{.label}}：{{.subject}}，研究产品编号：{{.medicine}}。
        dispensing-other: 【发放】{{.label}}：{{.subject}}， 未编号研究产品名称/数量/批次/有效期：{{.medicine}}。
        dispensing-with-other: 【发放】 {{.label}}：{{.subject}}，研究产品编号：{{.medicine}}， 未编号研究产品名称/数量/批次/有效期：{{.other_medicine}}。
        dispensing-with-other-reason: 【发放】 {{.label}}：{{.subject}}，研究产品编号：{{.medicine}}， 未编号研究产品名称/数量/批次/有效期：{{.other_medicine}}，原因：{{.reason}}
        dispensingVisit: 【计划外发放】 {{.label}}：{{.subject}}，研究产品编号：{{.medicine}}，计划外发放原因：{{.reason}}
        dispensingVisit-other: 【计划外发放】{{.label}}：{{.subject}}，未编号研究产品名称/数量/批次/有效期：{{.medicine}}，计划外发放原因：{{.reason}}

        dispensing-new: 【发放】 {{.label}}：{{.subject}}，{{.form}} {{.formula}} 研究产品编号：{{.medicine}}， 备注：{{.remark}}
        dispensing-other-new: 【发放】{{.label}}：{{.subject}}，{{.form}} {{.formula}}未编号研究产品名称/数量/批次/有效期：{{.medicine}}， 备注：{{.remark}}
        dispensing-with-other-new: 【发放】 {{.label}}：{{.subject}}，{{.form}} {{.formula}} 研究产品编号：{{.medicine}}， 未编号研究产品名称/数量/批次/有效期：{{.other_medicine}} ，备注：{{.remark}}
        dispensing-with-other-reason-new: 【计划外发放】 {{.label}}：{{.subject}}，{{.form}} {{.formula}} 研究产品编号：{{.medicine}}， 未编号研究产品名称/数量/批次/有效期：{{.other_medicine}}， 计划外发放原因：{{.reason}}
        dispensingVisit-new: 【计划外发放】 {{.label}}：{{.subject}}，{{.form}} {{.formula}} 研究产品编号：{{.medicine}}，计划外发放原因：{{.reason}}
        dispensingVisit-other-new: 【计划外发放】{{.label}}：{{.subject}}，{{.form}} {{.formula}} 未编号研究产品名称/数量/批次/有效期：{{.medicine}}， 计划外发放原因：{{.reason}}

        dtp-dispensing: 【申请】 {{.label}}：{{.subject}}，研究产品编号：{{.medicine}}，备注：{{.remark}}
        dtp-dispensing-other: 【申请】{{.label}}：{{.subject}}，未编号研究产品名称、数量：{{.medicine}}，备注：{{.remark}}
        dtp-dispensing-with-other: "【申请】 {{.label}}：{{.subject}}，研究产品编号：{{.medicine}}， 未编号研究产品名称、数量：{{.other_medicine}}，备注：{{.remark}}"
        dtp-dispensing-with-other-reason: 【计划外申请】 {{.label}}：{{.subject}}， 研究产品编号：{{.medicine}}， 未编号研究产品名称/数量/批次/有效期：{{.other_medicine}}，原因：{{.reason}}。
        dtp-dispensingVisit: 【计划外申请】 {{.label}}：{{.subject}}，研究产品编号：{{.medicine}}，计划外发放原因：{{.reason}}
        dtp-dispensingVisit-other: 【计划外申请】{{.label}}：{{.subject}}，未编号研究产品名称/数量/批次/有效期：{{.medicine}}，计划外发放原因：{{.reason}}
        dtp-reissue: 【补发申请】{{.label}}：{{.subject}}，研究产品编号：{{.medicine}}，补发原因：{{.remark}}
        dtp-reissue-other: 【补发申请】{{.label}}：{{.subject}}，未编号研究产品名称 数量：{{.medicine}}，补发原因：{{.remark}}
        dtp-reissue-with-other: 【补发申请】{{.label}}：{{.subject}}，研究产品编号：{{.medicine}}， 未编号研究产品名称/数量/批次/有效期：{{.other_medicine}}，补发原因：{{.remark}}
        invalid: 【不参加访视】{{.label}}：{{.subject}}，备注：{{.remark}}。
        register: 【登记实际使用研究产品】{{.label}}：{{.subject}}， 系统发放研究产品：{{.medicine}}， 实际使用研究产品：{{.real_medicine}}。
        reissue: 【补发】{{.label}}：{{.subject}}，研究产品编号：{{.medicine}}， 补发原因：{{.remark}}。
        reissue-other: 【补发】{{.label}}：{{.subject}}，未编号研究产品名称 数量：{{.medicine}}， 补发原因：{{.remark}}。
        reissue-with-other: 【补发】{{.label}}：{{.subject}}，研究产品编号：{{.medicine}}， 未编号研究产品名称(数量)：{{.other_medicine}}， 补发原因：{{.remark}}。
        replace-logistics: 【替换】{{.label}}：{{.subject}}，随机号：{{.randomNumber}}， 访视：{{.visit}}，研究产品：{{.medicine}}，订单编号：{{.order}}，发放方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}， 备注：{{.remark}}。
        dispensing-logistics: 【发放】{{.label}}：{{.subject}}，随机号：{{.randomNumber}}，{{.form}} 访视：{{.visit}}，研究产品：{{.medicine}}，订单编号：{{.order}}，发放方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}， 备注：{{.remark}}。
        dispensing-logistics-noRandomNumber: 【发放】{{.label}}：{{.subject}}，访视：{{.visit}}，{{.form}}研究产品：{{.medicine}}，订单编号：{{.order}}，发放方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}， 备注：{{.remark}}。
        dispensingVisit-logistics: 【计划外发放】{{.label}}：{{.subject}}，随机号：{{.randomNumber}}， 访视：{{.visit}}，{{.form}}研究产品：{{.medicine}}，订单编号：{{.order}}，发放方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}， 原因：{{.reason}}。
        dispensingVisit-logistics-noRandomNumber: 【计划外发放】{{.label}}：{{.subject}}，访视：{{.visit}}，{{.form}}研究产品：{{.medicine}}，订单编号：{{.order}}，发放方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}， 原因：{{.reason}}。
        reissue-with-logistics: 【补发】{{.label}}：{{.subject}}，随机号：{{.randomNumber}}， 访视：{{.visit}}，研究产品：{{.medicine}}，订单编号：{{.order}}，发放方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}， 补发原因：{{.remark}}。
        reissue-with-logistics-noRandomNumber: 【补发】{{.label}}：{{.subject}}，访视：{{.visit}}，研究产品：{{.medicine}}，订单编号：{{.order}}，发放方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}， 补发原因：{{.remark}}。
        replace: 【研究产品替换】 {{.label}}：{{.subject}}，替换原因：{{.reason}} ，替换研究产品编号：{{.medicine}}，被替换研究产品{{.beReplaceMedicine}}。
        retrieval: 【取回研究产品】{{.label}}：{{.subject}}，取回研究产品编号：{{.medicine}}。
        retrieval-order: 【取回研究产品】{{.label}}：{{.subject}}，取回研究产品编号：{{.medicine}}。订单取消/关闭/终止
        send-type-0: 中心（中心库存）
        send-type-1: 中心（直接寄送受试者）
        send-type-2: 库房（直接寄送受试者）
        scanConfrim: 【扫码确认】{{.label}}：{{.subject}}，研究产品编号：{{.medicine}}，短码：{{.shortCode}}

        dispensing-new-formula: 【发放】 {{.label}}：{{.subject}}，{{.form}} {{.formula}}  研究产品编号：{{.medicine}}，备注：{{.remark}}
        dispensing-other-new-formula: 【发放】{{.label}}：{{.subject}}，{{.form}} {{.formula}}未编号研究产品名称/数量/批次/有效期：{{.medicine}}，备注：{{.remark}}
        dispensing-with-other-new-formula: 【发放】 {{.label}}：{{.subject}}，{{.form}} {{.formula}}研究产品编号：{{.medicine}}， 未编号研究产品名称/数量/批次/有效期：{{.other_medicine}}，备注：{{.remark}}
        dispensing-with-other-reason-new-formula: 【计划外发放】 {{.label}}：{{.subject}}，{{.form}} {{.formula}}研究产品编号：{{.medicine}}， 未编号研究产品名称/数量/批次/有效期：{{.other_medicine}}，计划外发放原因：{{.reason}}
        dispensingVisit-new-formula: 【计划外发放】 {{.label}}：{{.subject}}，{{.form}} {{.formula}}研究产品编号：{{.medicine}}，{{.formula}}，计划外发放原因：{{.reason}}
        dispensingVisit-other-new-formula: 【计划外发放】{{.label}}：{{.subject}}，{{.form}} {{.formula}}未编号研究产品名称/数量/批次/有效期：{{.medicine}}，{{.formula}}，计划外发放原因：{{.reason}}
        dispensing-logistics-formula: 【发放】{{.label}}：{{.subject}}，随机号：{{.randomNumber}}， 访视：{{.visit}}，{{.form}} {{.formula}}研究产品：{{.medicine}}，订单编号：{{.order}}，发放方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}，备注：{{.remark}}。
        dispensing-logistics-noRandomNumber-formula: 【发放】{{.label}}：{{.subject}}，访视：{{.visit}}，{{.form}} {{.formula}}研究产品：{{.medicine}}，订单编号：{{.order}}，发放方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}，备注：{{.remark}}。
        dispensingVisit-logistics-formula: 【计划外发放】{{.label}}：{{.subject}}，随机号：{{.randomNumber}}， 访视：{{.visit}}，{{.form}} {{.formula}}研究产品：{{.medicine}}，订单编号：{{.order}}，发放方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}，原因：{{.reason}}。
        dispensingVisit-logistics-noRandomNumber-formula: 【计划外发放】{{.label}}：{{.subject}}，访视：{{.visit}}，{{.form}} {{.formula}}研究产品：{{.medicine}}，订单编号：{{.order}}，发放方式:{{.sendType}}，物流：{{.vendor}}/{{.number}}，原因：{{.reason}}。
        resume: 【恢复发放】{{.label}}：{{.subject}}
        dispensingCustomer-dispensing: 【发放】 {{.label}}：{{.subject}}， {{.data}}
        dispensingCustomer-dispensingVisit: 【计划外发放】 {{.label}}：{{.subject}}， {{.data}}
        dispensingCustomer-dispensingVisitCustomer: "【{{.dispensingType}}】 {{.label}}：{{.subject}}， {{.data}}"
        dispensingCustomer-reissue: 【补发】 {{.label}}：{{.subject}}， {{.data}}
        dispensingCustomer-replace: 【研究产品替换】 {{.label}}：{{.subject}}， {{.data}}
        dispensingCustomer-retrieval: 【取回研究产品】{{.label}}：{{.subject}}， {{.data}}
        dispensingCustomer-register: 【登记实际使用研究产品】 {{.label}}：{{.subject}}， {{.data}}
        dispensingCustomer-not-attend: 【不参加】 {{.label}}：{{.subject}}， {{.data}}
        dispensingCustomer-resume: 【恢复发放】 {{.label}}：{{.subject}}， {{.data}}
        dispensingCustomer-unblinding-application: 【研究产品揭盲】 {{.data}}
        single:
            comma: "，"
            period: "。"
            colon: "："
            dispensingVisit: "计划外发放"
            dispensingApply: "申请"
            randomNumber: 随机号：{{.randomNumber}}
            visit: 访视：{{.visit}}
            form: "{{.form}}"
            date: 出生日期：{{.date}}
            weight: 体重：{{.weight}}
            height: 身高：{{.height}}
            dose: "{{.dose}}"
            level: 发放水平：{{.level}}
            medicine: 研究产品编号：{{.medicine}}
            otherMedicine: 未编号研究产品名称/数量/批次/有效期：{{.medicine}}
            order: 订单编号：{{.order}}
            sendType: 发放方式
            vendor: "物流：{{.vendor}}/{{.number}}"
            outSize: "是否超窗：是"
            remark: 备注：{{.remark}}
            reason: 原因：{{.reasonDispensingVisit}}
            reasonDispensingVisit: 计划外发放原因：{{.reasonDispensingVisit}}
            reasonDispensingVisitCustomer: "{{.dispensingType}}原因：{{.reasonDispensingVisit}}"
            reasonReissue: 补发原因：{{.reasonDispensingVisit}}
            reasonReplace: 替换原因：{{.reasonDispensingVisit}}
            replaceNumber: 替换研究产品编号：{{.replaceNumber}}
            beReplaceMedicine: 被替换研究产品：{{.beReplaceMedicine}}
            retrievalMedicine: 研究产品：{{.retrievalMedicine}}
            systemMedicine: 系统发放研究产品：{{.systemMedicine}}
            realMedicine: 实际使用研究产品：{{.realMedicine}}
            notAttendRemark: "备注：开启后续阶段访视，当前阶段：{{.random}}，后续阶段:{{.atRandom}}"
            resumeRemark: "备注：关闭后续阶段访视，当前阶段：{{.currentStage}}，后续阶段：{{.nextStage}}"
            noKey: "{{ .data }}"
            unBlindMedicine: "揭盲产品编号:{{ .number }}"
            unBlindStatus: "状态"
            unBlindApplication: "待审批"
            unBlindReject: "已拒绝"
            unBlindSuccess: "已通过"
            unBlindApprovalNumber: " 审批编号:{{ .approvalNumber }}"
    medicine:
        update-batch-expireDate: 【编辑】 关联订单：{{.orderNumber}}，状态：{{.status}}，研究产品编号：{{.ipNumber}}，有效期：{{.expirationDate}}，批次号：{{.batchNumber}}。
        other-update-batch-expireDate: 【编辑】 关联订单：{{.orderNumber}}，状态：{{.status}}，研究产品：{{.ip}}，数量：{{.count}}，有效期：{{.expirationDate}}，批次号：{{.batchNumber}}。
        updateBatch: 研究产品【更新】，有效期：{{.expirationDate}}，批次号：{{.batchNumber}}，状态：{{.status}}。
        updateOtherBatch: 研究产品【更新】，有效期：{{.expirationDate}}，批次号：{{.batchNumber}}，状态：{{.status}}，数量：{{.count}}。
        expired: 【已过期研究产品】 已过期原因：系统自动已过期，当前已过期研究产品编号：{{.packNumber}}。
        freeze: 【隔离研究产品】 隔离原因：{{.reason}}，当前隔离研究产品编号：{{.packNumber}}。
        freeze-new: 研究产品【已隔离】， 隔离编号：{{.freezeNumber}}，研究产品编号：{{.packNumber}}，原因：{{.reason}}。
        freeze-package-new: 研究产品【已隔离】， 隔离编号：{{.freezeNumber}}，研究产品编号：{{.packNumber}}，包装号：{{.packageNumber}}，原因：{{.reason}}。
        otherFreeze: 研究产品【已隔离】， 隔离编号：{{.freezeNumber}}， 原因：{{.freezeReason}}， 当前隔离研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 隔离：{{.freezeCount}}。
        new-freeze: 研究产品【已隔离】，{{.data}}
        new-freeze-label:
            freezeNumber: 隔离编号：{{.freezeNumber}}
            position: 隔离位置：{{.position}}
            ipNumber: 研究产品编号：{{.packNumber}}
            reason: 原因：{{.reason}}
            packageNumber: 包装号：{{.packageNumber}}
            ipName: 当前隔离研究产品名称：{{.name}}
            batch: 批次号：{{.batch}}
            expireDate: 有效期：{{.expireDate}}
            freezeCount: 隔离：{{.freezeCount}}
            orderNumber: 关联订单：{{.orderNumber}}
            changeReason: 更换原因：{{.changeReason}}
            changeOtherMedicine: 研究产品名称/批次号/有效期/更换:{{.changeOtherMedicine}}
        otherMedicineLost: 研究产品【已丢失/作废】， 原因：{{.freezeReason}}， 当前隔离研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 丢失/作废：{{.freezeCount}}。
        otherMedicineLost-new: 研究产品【丢失/作废】，研究产品：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.freezeCount}}，原因：{{.freezeReason}}。
        lost: 【丢失/作废研究产品】，丢失/作废原因：{{.reason}}，当前丢失/作废研究产品编号：{{.packNumber}}。
        lost-new: 研究产品【丢失/作废】，研究产品编号：{{.packNumber}}，原因：{{.reason}}。
        lost-package: 研究产品【丢失/作废】，研究产品编号：{{.packNumber}}，包装号：{{.packageNumber}}，原因：{{.reason}}。
        new-lost:
            lost: 研究产品【丢失/作废】，{{.data}}
            position: 位置：{{.position}}
        release-new:
            release: 研究产品【解隔离】，{{.data}}
            packNumber: 当前解隔离研究产品编号：{{.packNumber}}
        release: 研究产品【解隔离】， 当前解隔离研究产品编号：{{.packNumber}}，原因：{{.reason}}。
        release-package: 研究产品【解隔离】， 当前解隔离研究产品编号：{{.packNumber}}，包装号：{{.packageNumber}}，原因：{{.reason}}。
        quarantine-no: 研究产品【解隔离审批】，研究产品编号：{{.packNumber}}，解隔离确认：拒绝，拒绝原因：{{.reason}}。
        approval: 研究产品【解隔离申请】，原因：{{.reason}}，研究产品编号：{{.packNumber}}。
        locked: 研究产品【锁定】
        otherLocked: 研究产品【锁定】，当前研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.count}}。
        otherUse: 研究产品【已使用】，当前研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.count}}。
        otherCanUse: 研究产品【可用】，当前研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.count}}。
        otherLost: 研究产品【作废】，当前研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.count}}。
        otherToBeConfirm: 研究产品【待确认】，当前研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.count}}。
        otherExpired: 研究产品【已过期】，当前研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.count}}。
        toBeConfirm: 研究产品【待确认】
        toBeConfirmNew: 关联订单：{{.order}}，状态：待确认。
        toFrozenNew: 关联订单：{{.orderNumber}}，状态：已冻结。
        toBeConfirmNewUpdate: 关联订单：{{.orderNumber}}，状态：待确认。
        confirmed: 研究产品【已确认】
        confirmedNew: 关联订单：{{.orderNumber}}，状态：已确认。
        otherConfirmed: 研究产品【已确认】，当前研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.count}}。
        releaseLost-new:
            release: 【解隔离丢失/作废研究产品】，{{.data}}
            reason: 丢失/作废原因：{{.reason}}
            packNumber: 当前丢失/作废研究产品编号：{{.packNumber}}
        releaseLost: 【解隔离丢失/作废研究产品】，丢失/作废原因：{{.reason}}，当前丢失/作废研究产品编号：{{.packNumber}}。
        releaseLost-package: 【解隔离丢失/作废研究产品】 丢失/作废原因：{{.reason}}，当前丢失/作废研究产品编号：{{.packNumber}}，包装号：{{.packageNumber}}。
        otherRelease: 【解隔离研究产品】， 解隔离原因：{{.reason}}，当前解隔离研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 解隔离：{{.count}}。
        otherRelease-new:
            release: 【解隔离研究产品】，{{.data}}
            reason: 解隔离原因：{{.reason}}
            name: 当前解隔离研究产品名称：{{.name}}
            count: 解隔离：{{.count}}
        otherReleaseLost: 【丢失/作废研究产品】 ，丢失/作废原因：{{.reason}}，当前丢失/作废研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 丢失/作废：{{.count}}。
        otherReleaseLost-new:
            release: 【丢失/作废研究产品】，{{.data}}
            reason: 丢失/作废原因：{{.reason}}
            name: 当前丢失/作废研究产品名称：{{.name}}
            count: 丢失/作废{{.count}}
        other-quarantine-no: 研究产品【解隔离审批】，解隔离确认：拒绝， 拒绝原因：{{.reason}}，当前研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.count}}。
        other-approval: 研究产品【解隔离申请】 原因：{{.reason}}，当前申请解隔离研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.count}}。
        replace: 【发放替换研究产品】 设为作废原因：{{.reason}}，当前作废研究产品编号：{{.packNumber}}。
        replace-with-dtp: 【替换研究产品】，研究产品【丢失/作废】，原因：研究产品被替换。
        cancel: 研究产品【已撤销】，关联受试者：{{.subject}}，访视：{{.visit}}，操作：研究产品撤销。
        use-new:
            use: 【设为可用研究产品】，{{.data}}
            packNumber: 当前可用研究产品编号：{{.packNumber}}
            position: 位置：{{.position}}
            reason: 设为可用原因：{{.reason}}
        use: 【设为可用研究产品】 设为可用原因：{{.reason}}，当前可用研究产品编号：{{.packNumber}}。
        use-package: 【设为可用研究产品】 设为可用原因：{{.reason}}，当前可用研究产品编号：{{.packNumber}}，包装号：{{.packageNumber}}。
        replace-dtp: 研究产品已替换，替换前：{{.beReplace}}，替换后：{{.replace}}。
        register: 研究产品【已使用】，原因：登记实际使用研究产品，系统发放研究产品：{{.medicine}}，实际使用研究产品：{{.real_medicine}}。
        apply: 研究产品已申请
        shipped: 关联订单：{{.orderNumber}}，状态：已运送。
        otherShipped: 研究产品【已运送】，当前研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.count}}。
        receive: 研究产品【已接收】
        receiveNew: 关联订单：{{.orderNumber}}，状态：已接收。
        otherReceive: 研究产品【已接收】，当前研究产品名称：{{.name}}， 批次号：{{.batch}}， 有效期：{{.expireDate}}， 数量：{{.count}}。
        used: 研究产品已使用
        canUse: 研究产品【可用】
        uploadCanUse: 【研究产品上传】状态：待入仓
        scanCanUse: 【扫码入仓】状态：待审核
        examinePassThrough: 【审核通过】状态：待入仓
        examineRefuse: 【审核拒绝】状态：审核失败
        updateName: 【修改】状态：待审核
        updateNameScanCan: 【修改】状态：待扫码
        release-usable: 【放行】状态：可用
        register-use: 研究产品【可用】，原因：研究产品登记为“可用”。
        register-frozen: 研究产品【冻结】，原因：研究产品登记为“冻结”。
        register-lost: 研究产品【丢失/作废】，原因：研究产品登记为“丢失/作废”。
        in-order: 研究产品待确认，受试者号：{{.subject}}，研究产品编号：{{.medicine}}。
        in-order-new: 研究产品待确认
        confrim: 研究产品已确认
        dispensing-used: 研究产品已使用，受试者号：{{.subject}}，研究产品编号：{{.medicine}}。
        use-dtp: 关联订单：{{.orderNumber}}，状态：可用。
        use-order-cancel: 研究产品 状态恢复，原因：订单取消。
        use-order-close: 研究产品 状态恢复，原因：订单关闭。
        use-order-termination: 研究产品 状态恢复，原因：订单终止。
        use-available-order-cancel: 研究产品【可用】，原因：订单取消，状态恢复。
        use-available-order-close: 研究产品【可用】，原因：订单关闭，状态恢复。
        use-available-order-termination: 研究产品【可用】，原因：订单终止，状态恢复。
        use-frozen-order-cancel: 研究产品【冻结】，原因：订单取消，状态恢复。
        use-frozen-order-close: 研究产品【冻结】，原因：订单关闭，状态恢复。
        use-frozen-order-termination: 研究产品【冻结】，原因：订单终止，状态恢复。
        use-available-order-confrim: 关联订单:{{.orderNumber}}，状态：可用，原因：订单确认时未确认该研究产品，状态恢复。
        use-available-order-frozen: 关联订单:{{.orderNumber}}，状态：冻结，原因：订单确认时未确认该研究产品，状态恢复。
        use-lose-order-cancel: 研究产品【丢失/作废】，原因：订单取消，状态恢复。
        use-lose-order-close: 研究产品【丢失/作废】，原因：订单关闭，状态恢复。
        use-lose-order-termination: 研究产品【丢失/作废】，原因：订单终止，状态恢复。
        use-expired-order-cancel: 研究产品【已过期】，原因：订单取消，状态恢复。
        use-expired-order-close: 研究产品【已过期】，原因：订单关闭，状态恢复。
        use-expired-order-termination: 研究产品【已过期】，原因：订单终止，状态恢复。
        sku-freeze: 研究产品【已冻结】
        sku-freeze-reason: 【设为冻结研究产品】 设为冻结原因：{{.reason}}，当前冻结研究产品编号：{{.packNumber}}。
        #    操作：1.发药、2.取回、3.研究产品替换、4计划外发药、5.补发、6.登记实际使用研究产品
        sku-freeze-subject: 研究产品【已冻结】， 关联受试者：{{.subject}}， 访视：{{.visit}}，操作：{{.operation}}。
        sku-used-subject: 研究产品【已使用】， 关联受试者：{{.subject}}， 访视：{{.visit}}，操作：{{.operation}}。
        sku-use-subject: 研究产品【可用】， 关联受试者：{{.subject}}， 访视：{{.visit}}，操作：{{.operation}}。
        sku-in-order-subject: 研究产品【待确认】， 关联受试者：{{.subject}}， 访视：{{.visit}}，操作：{{.operation}}。
        sku-lost-subject: 研究产品【丢失/作废】， 关联受试者：{{.subject}}， 访视：{{.visit}}，操作：{{.operation}}。
        rest-receive: 研究产品【已领药】，关联受试者：{{.subject}}，访视：{{.visit}}，操作：{{.operation}}。
        rest-return: 研究产品【已退货】，关联受试者：{{.subject}}，访视：{{.visit}}，操作：{{.operation}}。
        rest-destroy: 研究产品【已销毁】，关联受试者：{{.subject}}，访视：{{.visit}}，操作：{{.operation}}。
        rest-return-retrieve: 研究产品【已退货】，操作：{{.operation}}。
        rest-destroy-retrieve: 研究产品【已销毁】，操作：{{.operation}}。
        operation:
            dispensing: 发放
            retrieval: 取回
            replace: 研究产品替换
            unscheduled: 计划外发放
            reissue: 补发
            register: 登记实际使用研究产品
        sku-lose: 关联订单：{{.orderNumber}}，状态：已丢失。
        cancel-dtp: 研究产品已终止，终止原因：{{.reason}}。
        drugFreeze-new:
            freeze: 研究产品【已隔离】，{{.data}}
            lost: 研究产品【丢失/作废】，{{.data}}
            approval: 研究产品【解隔离申请】，{{.data}}
            release: 研究产品【解隔离】，{{.data}}
            freezeNumber: 隔离编号：{{.freezeNumber}}
            position: 隔离位置：{{.position}}
            medicines: 研究产品编号：{{.medicines}}
            medicinesPackage: 研究产品编号/包装号：{{.medicines}}
            freezeOtherMedicine: 当前隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}
            lostOtherMedicine: 当前丢失/作废研究产品名称/批次号/有效期/数量：{{.otherMedicines}}
            releaseOtherMedicine: 当前解隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}
            reason: 原因：{{.reason}}
            quarantine-no: 研究产品【解隔离审批】，{{.data}} 解隔离确认：拒绝，拒绝原因：{{.freezeReason}}。
            quarantine-yes: 研究产品【解隔离审批】，{{.data}} 解隔离确认：通过。
        drugFreeze:
            drugFreeze: 研究产品【已隔离】，隔离编号：{{.freezeNumber}}，研究产品编号：{{.medicines}}，原因：{{.freezeReason}}。
            drugFreeze-package: 研究产品【已隔离】，隔离编号：{{.freezeNumber}}，研究产品编号/包装号：{{.medicines}}，原因：{{.freezeReason}}。
            otherDrugFreeze: 研究产品【已隔离】，隔离编号：{{.freezeNumber}}，当前隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，原因：{{.freezeReason}}。
            allDrugFreeze: 研究产品【已隔离】，隔离编号：{{.freezeNumber}}，原因：{{.freezeReason}}，当前隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号：{{.medicines}}。
            allDrugFreeze-package: 研究产品【已隔离】，隔离编号：{{.freezeNumber}}，原因：{{.freezeReason}}，当前隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号/包装号：{{.medicines}}。
            lost: 研究产品【丢失/作废】，隔离编号：{{.freezeNumber}}，研究产品编号：{{.medicines}}，原因：{{.freezeReason}}。
            lost-package: 研究产品【丢失/作废】，隔离编号：{{.freezeNumber}}，研究产品编号/包装号：{{.medicines}}，原因：{{.freezeReason}}。
            approval: 研究产品【解隔离申请】，隔离编号：{{.freezeNumber}}，原因：{{.freezeReason}}，研究产品编号：{{.medicines}}。
            approval-package: 研究产品【解隔离申请】，隔离编号：{{.freezeNumber}}，原因：{{.freezeReason}}，研究产品编号/包装号：{{.medicines}}。
            release: 研究产品【解隔离】，隔离编号：{{.freezeNumber}}，研究产品编号：{{.medicines}}，原因：{{.freezeReason}}。
            release-package: 研究产品【解隔离】，隔离编号：{{.freezeNumber}}，研究产品编号/包装号：{{.medicines}}，原因：{{.freezeReason}}。
            otherLost: 研究产品【丢失/作废】，隔离编号：{{.freezeNumber}}，当前丢失/作废研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，原因：{{.freezeReason}}。
            other-approval: 研究产品【解隔离申请】，隔离编号：{{.freezeNumber}}，原因：{{.freezeReason}}，当前隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}。
            otherRelease: 研究产品【解隔离】，隔离编号：{{.freezeNumber}}，当前解隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，原因：{{.freezeReason}}。
            other-quarantine-no: 研究产品【解隔离审批】，隔离编号：{{.freezeNumber}}，当前解隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，原因：{{.untieReason}}，解隔离确认：拒绝，拒绝原因：{{.freezeReason}}。
            other-quarantine-yes: 研究产品【解隔离审批】，隔离编号：{{.freezeNumber}}，当前解隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，原因：{{.untieReason}}，解隔离确认：通过。
            allLost: 研究产品【丢失/作废】，隔离编号：{{.freezeNumber}}，原因：{{.freezeReason}}，当前丢失/作废研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号：{{.medicines}}。
            all-approval: 研究产品【解隔离申请】，隔离编号：{{.freezeNumber}}，原因：{{.freezeReason}}，当前隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号：{{.medicines}}。
            allRelease: 研究产品【解隔离】，隔离编号：{{.freezeNumber}}，原因：{{.freezeReason}}，当前解隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号：{{.medicines}}。
            all-quarantine-no: 研究产品【解隔离审批】，隔离编号：{{.freezeNumber}}，当前解隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号：{{.medicines}}， 原因：{{.untieReason}}，解隔离确认：拒绝，拒绝原因：{{.freezeReason}}。
            all-quarantine-yes: 研究产品【解隔离审批】，隔离编号：{{.freezeNumber}}，当前解隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号：{{.medicines}}， 原因：{{.untieReason}}，解隔离确认：通过。
            quarantine-no: 研究产品【解隔离审批】，隔离编号：{{.freezeNumber}}，研究产品编号：{{.medicines}}，原因：{{.untieReason}}，解隔离确认：拒绝，拒绝原因：{{.freezeReason}}。
            quarantine-yes: 研究产品【解隔离审批】，隔离编号：{{.freezeNumber}}，研究产品编号：{{.medicines}}，原因：{{.untieReason}}，解隔离确认：通过。
            allLost-package: 研究产品【丢失/作废】，隔离编号：{{.freezeNumber}}，原因：{{.freezeReason}}，当前丢失/作废研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号/包装号：{{.medicines}}。
            all-approval-package: 研究产品【解隔离申请】，隔离编号：{{.freezeNumber}}，原因：{{.freezeReason}}，当前隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号/包装号：{{.medicines}}。
            allRelease-package: 研究产品【解隔离】，隔离编号：{{.freezeNumber}}，原因：{{.freezeReason}}，当前解隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号/包装号：{{.medicines}}。
            all-quarantine-no-package: 研究产品【解隔离审批】，隔离编号：{{.freezeNumber}}，当前解隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号/包装号：{{.medicines}}， 原因：{{.untieReason}}，解隔离确认：拒绝，拒绝原因：{{.freezeReason}}。
            all-quarantine-yes-package: 研究产品【解隔离审批】，隔离编号：{{.freezeNumber}}，当前解隔离研究产品名称/批次号/有效期/数量：{{.otherMedicines}}，研究产品编号/包装号：{{.medicines}}， 原因：{{.untieReason}}，解隔离确认：通过。
            quarantine-no-package: 研究产品【解隔离审批】，隔离编号：{{.freezeNumber}}，研究产品编号/包装号：{{.medicines}}，原因：{{.untieReason}}，解隔离确认：拒绝，拒绝原因：{{.freezeReason}}。
            quarantine-yes-package: 研究产品【解隔离审批】，隔离编号：{{.freezeNumber}}，研究产品编号/包装号：{{.medicines}}，原因：{{.untieReason}}，解隔离确认：通过。
        change:
            newToConfirm: 关联订单:{{.orderNumber}}，状态：待确认，原研究产品：{{.oldMedicine}}，更换后研究产品：{{.newMedicine}}，更换原因：{{.reason}}。
            newConfirmed: 关联订单:{{.orderNumber}}，状态：已确认，原研究产品：{{.oldMedicine}}，更换后研究产品：{{.newMedicine}}，更换原因：{{.reason}}。
            old: 研究产品【已隔离】， 隔离编号：{{.freezeNumber}}，研究产品编号：{{.oldMedicine}}，关联订单：{{.orderNumber}}，更换原因：{{.reason}}。
            otherNewToConfirm: 关联订单:{{.orderNumber}}，状态：待确认，原研究产品名称/批次号/有效期/更换：{{.oldMedicine}}，更换后研究产品：{{.newMedicine}}，更换原因：{{.reason}}。
            otherNewConfirmed: 关联订单:{{.orderNumber}}，状态：已确认，原研究产品名称/批次号/有效期/更换：{{.oldMedicine}}，更换后研究产品：{{.newMedicine}}，更换原因：{{.reason}}。
            otherOld: 研究产品【已隔离】， 隔离编号：{{.freezeNumber}}，研究产品名称/批次号/有效期/更换：{{.oldMedicine}}，关联订单：{{.orderNumber}}，更换原因：{{.reason}}。
    order:
        cancel: 取消订单，订单已取消，取消原因：{{.reason}}。
        close: 关闭订单，订单已关闭，关闭原因：{{.reason}}。
        confirm-task: 发送订单确认任务，订单已请求。
        confirm-task-finish: 完成了订单确认任务，订单运送中。
        confrim: 确认订单，订单已请求。
        create: 创建订单，订单待确认。
        close-with-dtp: 订单编号：{{.orderNumber}}，订单【已关闭】，关闭原因：全部研究产品被替换。
        close-with-register: 订单编号：{{.orderNumber}}，订单【已关闭】，关闭原因：研究产品已登记为“可用/冻结”。
        close-with-register-lost: 订单编号：{{.orderNumber}}，订单【已关闭】，关闭原因：研究产品已登记为“丢失/作废”。
        lost: 订单丢失，订单已丢失，丢失原因：{{.reason}}。
        receive: 接收订单，订单已收到。
        receive-task-confrim: 发送研究产品接收任务，订单已请求。
        receive-task-finish: 完成了研究产品接收任务，订单已收到。
        receive-task-send: 发送研究产品接收任务，订单运送中。
        send: 运送订单，订单运送中。
        apply: 创建订单，订单已申请， 备注：{{.remark}}。
        cancel-new: 订单编号：{{.orderNumber}}，订单【已取消】，取消原因：{{.reason}}。
        close-new: 订单编号：{{.orderNumber}}，订单【已关闭】，关闭原因：{{.reason}}。
        confrim-new: 订单编号：{{.orderNumber}}，订单【已确认】。
        confrim-expectedArrivalTime-new: 订单编号：{{.orderNumber}}，订单【已确认】，期望送达时间:{{.expectedArrivalTime}}。
        create-new: 订单编号：{{.orderNumber}}，订单【待确认】。
        create-expectedArrivalTime-new: 订单编号：{{.orderNumber}}，订单【待确认】，期望送达时间:{{.expectedArrivalTime}}。
        lost-new: 订单编号：{{.orderNumber}}，订单【已丢失】，丢失原因：{{.reason}}。
        receive-new: 订单编号：{{.orderNumber}}，订单【已接收】。
        receive-actualTime-new: 订单编号：{{.orderNumber}}，订单【已接收】，实际接收时间:{{.actualReceiptTime}}。
        send-new: 订单编号：{{.orderNumber}}，订单【已运送】。
        send-expectedArrivalTime-new: 订单编号：{{.orderNumber}}，订单【已运送】，期望送达时间:{{.expectedArrivalTime}}。
        logistics—other: 【物流信息更新】订单编号：{{.orderNumber}}，物流供应商：{{.logistics}}，其他物流：{{.other}}，物流单号：{{.number}}。
        logistics: 【物流信息更新】订单编号：{{.orderNumber}}，物流供应商：{{.logistics}}，物流单号：{{.number}}。
        logistics—other-actualTime: 【物流信息更新】订单编号：{{.orderNumber}}，实际接收时间:{{.actualReceiptTime}}。
        logistics-actualTime: 【物流信息更新】订单编号：{{.orderNumber}}，实际接收时间:{{.actualReceiptTime}}。
        logistics—other-actualTime-all: 【物流信息更新】订单编号：{{.orderNumber}}，物流供应商：{{.logistics}}，其他物流：{{.other}}，物流单号：{{.number}}，实际接收时间:{{.actualReceiptTime}}。
        logistics-actualTime-all: 【物流信息更新】订单编号：{{.orderNumber}}，物流供应商：{{.logistics}}，物流单号：{{.number}}，实际接收时间:{{.actualReceiptTime}}。
        send-with-logistics: 订单编号：{{.orderNumber}}，订单【已运送】，物流供应商：{{.logistics}}，物流单号：{{.number}}。
        send-with-logistics-expectedTime: 订单编号：{{.orderNumber}}，订单【已运送】，物流供应商：{{.logistics}}，物流单号：{{.number}}，期望送达时间:{{.expectedArrivalTime}}。
        send-with-other-logistics: 订单编号：{{.orderNumber}}，订单【已运送】，物流供应商：{{.logistics}}，其他物流：{{.other}}，物流单号：{{.number}}。
        send-with-other-logistics-expectedTime: 订单编号：{{.orderNumber}}，订单【已运送】，物流供应商：{{.logistics}}，其他物流：{{.other}}，物流单号：{{.number}}，期望送达时间:{{.expectedArrivalTime}}。
        cancel-dtp: 订单编号：{{.orderNumber}}，订单【已终止】，终止原因：{{.reason}}。
        approval: 研究中心订单申请【审批已通过】，订单【待确认】，订单编号：{{.orderNumber}}。
        change: 【更换】，订单编号：{{.orderNumber}}，原研究产品详情：{{.oldMedicines}}，更换后研究产品详情：{{.newMedicines}}，更换原因：{{.reason}}。
        batch: 【编辑】，订单编号：{{.orderNumber}}，研究产品-有效期-批次号：{{.ipExpireDateBatch}}。
        expireDate: 【编辑】， {{.data}}
        expireDateBatch:
            orderNumber: 订单编号：{{.orderNumber}}
            ipExpireDateBatch: 研究产品-有效期-批次号：{{.ipExpireDateBatch}}
    project:
        cohort:
            add: 【添加 Cohort/再随机】 名称：{{.cohortName}}，入组上限：{{.capacity}}，状态：{{.state}}，提醒阈值：{{.reminderThresholds}}%
            delete: 【删除 Cohort/再随机】 名称：{{.cohortName}}
            edcAdd: 【添加 Cohort/再随机】 名称：{{.cohortName}}，入组上限：{{.capacity}}，状态：{{.state}}，因素：{{.factor}}，提醒阈值：{{.reminderThresholds}}%
            edcEdit: 【修改 Cohort/再随机】 名称：{{.cohortName}}，入组上限：{{.capacity}}，状态：{{.state}}，因素：{{.factor}}，提醒阈值：{{.reminderThresholds}}%
            edcRAdd: 【添加 Cohort/再随机】 名称：{{.cohortName}}，入组上限：{{.capacity}}，状态：{{.state}}，因素：{{.factor}}，上一阶段：{{.lastCohort}}，提醒阈值：{{.reminderThresholds}}%
            edcREdit: 【修改 Cohort/再随机】 名称：{{.cohortName}}，入组上限：{{.capacity}}，状态：{{.state}}，因素：{{.factor}}，上一阶段：{{.lastCohort}}，提醒阈值：{{.reminderThresholds}}%
            edit: 【修改 Cohort/再随机】 名称：{{.cohortName}}，入组上限：{{.capacity}}，状态：{{.state}}，提醒阈值：{{.reminderThresholds}}%
            rAdd: 【添加 Cohort/再随机】 名称：{{.cohortName}}，入组上限：{{.capacity}}，状态：{{.state}}，上一阶段：{{.lastCohort}}，提醒阈值：{{.reminderThresholds}}%
            rEdit: 【修改 Cohort/再随机】 名称：{{.cohortName}}，入组上限：{{.capacity}}，状态：{{.state}}，上一阶段：{{.lastCohort}}，提醒阈值：{{.reminderThresholds}}%
        drugConfigure:
            add: 【新增研究产品配置】 组别：{{.group}}，研究产品配置：{{.drugConfigures}}
            delete: 【删除研究产品配置】 组别：{{.group}}，研究产品配置：{{.drugConfigures}}
            edit: 【编辑研究产品配置】 组别：{{.group}}，研究产品配置：{{.drugConfigures}}
        env:
            add: 【添加环境】 新增{{.env}}环境
        info:
            add: 【添加项目】 项目编号：{{.projectNum}}，项目名称：{{.projectName}}，申办单位：{{.biddingUnit}}，项目描述：{{.projectDesc}}
            edit: 【修改项目】 项目编号：{{.projectNum}}，项目名称：{{.projectName}}，申办单位：{{.biddingUnit}}，项目描述：{{.projectDesc}}
        medicine:
            batch: 【批次管理】 本次更新库存批次号：{{.batch}}，有效期：{{.expireDate}}，状态：{{.status}}，更新批次号：{{.batchUpdate}}，更新有效期：{{.expireDateUpdate}}
            upload: 【上传研究产品】 研究产品名称：{{.drugName}}，上传研究产品数量：{{.count}}
    randomization:
        attribute: 属性配置： 是否随机：{{.random}}， 是否展示随机号：{{.isRandomNumber}}， 是否发放：{{.dispensing}}， 是否盲法：{{.blind}}， 是否使用前缀：{{.prefix}}， 受试者号前缀：{{.prefixExpression}}， 标签(即替换文本)：{{.subjectReplaceText}}， 位数精确值(1:小于等于/2:等于)：{{.accuracy}}， 位数限制：{{.digit}}， 运行运送算法时将隔离的单品计算为研究机构重可用存货的一部分：{{.isFreeze}}， EDC对接研究产品配置标签：{{.edcDrugConfigLabel}}， 是否号段随机：{{.segment}}{{.segmentLength}}
        block:
            distributionFactor: 【随机分段】 【{{.name}}】 区组：{{.block}} 分配分层：{{.valueSite}}
            distributionSite: 【随机分段】 【{{.name}}】 区组：{{.block}} 分配中心：{{.valueSite}}
            generate: 【区组随机】【随机号生成】名称：{{.name}}，初始编号：{{.initialValue}}，组别配置 组别(权重比)：{{.groupRatio}}，区组配置 区组长度(区组数)：{{.blockNumber}}，分层因素：{{.factors}}，号码长度：{{.numberLength}}，随机种子：{{.seed}}，号码前缀：{{.numberPrefix}}，生成数量：{{.numberText}}
            upload: 【区组随机】【随机号上传】名称：{{.name}}，分层因素：{{.factors}}，可接受区组大小：{{.blockSize}}，上传数量：{{.numberText}}
        config:
            block:
                distributionFactor: 【随机分段】 【{{.name}}】 区组：{{.block}} 分配分层：{{.valueSite}}
                distributionSite: 【随机分段】 【{{.name}}】 区组：{{.block}}  分配中心：{{.valueSite}}
                generate: 【区组随机】【随机号生成】名称：{{.name}}，初始编号：{{.initialValue}}，组别配置 组别(权重比)：{{.groupRatio}}，区组配置 区组长度(区组数)：{{.blockNumber}}，分层因素：{{.factors}}，号码长度：{{.numberLength}}，随机种子：{{.seed}}，号码前缀：{{.numberPrefix}}，生成数量：{{.numberText}}
                upload: 【区组随机】【随机号上传】名称：{{.name}}，分层因素：{{.factors}}，可接受区组大小：{{.blockSize}}，上传数量：{{.numberText}}
            factor:
                add: 【分层新增】 字段编号：{{.number}}， 名称：{{.label}} ， 控件类型：{{ .type}}， 选项： {{.options}}
                addEDC: 【分层新增】 字段编号：{{.number}}， 名称：{{.label}} ，变量名称：{{ .name}}， 控件类型：{{ .type}}， 选项： {{.options}}
                clean: 【清空其他分层】 【{{ .name}}】 区组[{{ .block}}] 清空其他分层
                countryEnable: 【分层调整】 启用国家分层
                delete: 【分层删除】 字段编号：{{.oldNumber}}， 名称：{{.oldLabel}}， 控件类型：{{ .oldType}}， 选项 {{.oldOptions}}
                deleteEDC: 【分层删除】 字段编号：{{.oldNumber}}， 名称：{{.oldLabel}} ，变量名称：{{ .oldName}}， 控件类型：{{ .oldType}}， 选项 {{.oldOptions}}
                disableCountryLayer: 【分层调整】 禁用国家分层
                disableLayer: 【分层调整】 禁用地区分层
                disableSiteLayer: 【分层调整】 禁用中心分层
                edit: 【分层修改】 （字段编号：{{.oldNumber}}， 名称：{{.oldLabel}} ， 控件类型：{{ .oldType}}， 选项 {{.oldOptions}} ）     修改为     字段编号：{{.number}}， 名称：{{.label}} ， 控件类型：{{ .type}}， 选项 {{.options}}
                editEDC: 【分层修改】 （字段编号：{{.oldNumber}}， 名称：{{.oldLabel}} ，变量名称：{{ .oldName}}， 控件类型：{{ .oldType}}， 选项 {{.oldOptions}} ）     修改为     字段编号：{{.number}}， 名称：{{.label}} ，变量名称：{{ .name}}， 控件类型：{{ .type}}， 选项 {{.options}}
                number: 【设置分层人数】 【{{ .name}}】 分层：{{.factor}}， 预计人数：{{.estimateNumber}}， 警戒人数：{{.warnNumber}}
                siteEnable: 【分层调整】 启用中心分层
            form:
                add: 【表单新增】 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}}
                addEDC: 【表单新增】 字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}}
                addOption: 【表单新增】 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}}  选项：{{.options}}
                addOptionEDC: 【表单新增】 字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}}  选项：{{.options}}
                delete: 【表单删除】 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}}
                deleteEDC: 【表单删除】 字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}}
                deleteOption: 【表单删除】变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} 选项：{{.options}}
                deleteOptionEDC: 【表单删除】字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} 选项：{{.options}}
                edit: 【表单修改】 (变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} ) 修改为 (变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} )
                editEDC: 【表单修改】 (字段名称：{{.oldName}} 变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} ) 修改为  (字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} )
                editOption: 【表单修改】 (变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} )    修改为 (变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}})
                editOptionEDC: 【表单修改】 (字段名称：{{.oldName}} 变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}})    修改为 (字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}})
                editOptionend: 【表单修改】 (变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} )    修改为 (变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} 选项：{{.options}})
                editOptionendEDC: 【表单修改】 (字段名称：{{.oldName}} 变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} )    修改为 (字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} 选项：{{.oldOptions}})
                editOptionstart: 【表单修改】 (变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} 选项：{{.oldOptions}})    修改为 (变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}})
                editOptionstartEDC: 【表单修改】 (字段名称：{{.oldName}} 变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} 选项：{{.oldOptions}})    修改为 (字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}})
                editOptionstartend: 【表单修改】 (变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} 选项：{{.oldOptions}})    修改为 ( 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} 选项：{{.options}})
                editOptionstartendEDC: 【表单修改】 (字段名称：{{.oldName}} 变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} 选项：{{.oldOptions}})    修改为 (字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} 选项：{{.options}})
                editend: 【表单修改】 (变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} ) 修改为  (变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} 选项：{{.options}})
                editendEDC: 【表单修改】 (字段名称：{{.oldName}} 变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} )  (字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} 选项：{{.options}})
                editstart: 【表单修改】 (变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} 选项：{{.oldOptions}}) 修改为  (变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} )
                editstartEDC: 【表单修改】 (字段名称：{{.oldName}} 变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} 选项：{{.oldOptions}}) 修改为  (字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} )
                editstartend: 【表单修改】 (变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} 选项：{{.oldOptions}}) 修改为  ( 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} 选项：{{.options}})
                editstartendEDC: 【表单修改】 (字段名称：{{.oldName}} 变量名称：{{.oldLabel}}， 可否修改：{{ .oldModifiable}}， 控件类型：{{.oldType}} 选项：{{.oldOptions}}) 修改为  (字段名称：{{.name}} 变量名称：{{.label}}， 可否修改：{{ .modifiable}}， 控件类型：{{.type}} 选项：{{.options}})
            group:
                add: 【治疗组别新增】 组别代码：{{.number}}， 组别名称：{{.name}}
                delete: 【治疗组别删除】  组别代码：{{.number}}， 组别名称：{{.name}}
                edit: 【治疗组别修改】  组别代码：{{.oldNumber}}， 组别名称：{{.oldName}}      修改为    组别代码：{{.number}}， 组别名称：{{.name}}
            list:
                disableStatus: 【状态调整】【{{.name}}】 禁用随机状态
                enableStatus: 【状态调整】 【{{.name}}】 启用随机状态
                invalid: 【作废】 已将随机表【{{.name}}】作废
            minimize: 【最小化随机】 名称：{{.name}}，初始编号：{{.initialValue}}，组别配置 组别(权重比)：{{.groupRatio}}， 分层因素 分层(权重比)：{{.factors}}，偏倚概率：{{.probability}}，总例数：{{.total}}，号码长度：{{.numberLength}}，随机种子：{{.seed}}，号码前缀：{{.numberPrefix}}
            typeBlock: 【随机类型调整】 启用区组随机
            typeMin: 【随机类型调整】 启用最小化随机
        factor:
            clean: 【清空其他分层】 【{{ .name}}】 区组[{{ .block}}] 清空其他分层
            number: 【设置分层人数】 【{{ .name}}】 分层：{{.factor}}， 预计人数：{{.estimateNumber}}， 警戒人数：{{.warnNumber}}
            addNumber: 【添加分层人数】 【{{ .name}}】 分层：{{.factor}}， 预计人数：{{.estimateNumber}}， 警戒人数：{{.warnNumber}}
            editNumber: 【编辑分层人数】 【{{ .name}}】 分层：{{.factor}}， 预计人数：{{.estimateNumber}}， 警戒人数：{{.warnNumber}}
            delNumber: 【删除分层人数】 【{{ .name}}】 分层：{{.factor}}， 预计人数：{{.estimateNumber}}， 警戒人数：{{.warnNumber}}
        list:
            disableStatus: 【状态调整】 禁用随机状态
            enableStatus: 【状态调整】 启用随机状态
            invalid: 【作废】 已将随机表作废
            site: 【编辑】 绑定中心:{{.siteName}}
        minimize: 【最小化随机】 名称：{{.name}}，初始编号：{{.initialValue}}，组别配置 组别(权重比)：{{.groupRatio}}， 分层因素 分层(权重比)：{{.factors}}，偏倚概率：{{.probability}}，总例数：{{.total}}，号码长度：{{.numberLength}}，随机种子：{{.seed}}，号码前缀：{{.numberPrefix}}
    subject:
        add: 【登记】 {{.content}}
        at-random-add: 【登记】 阶段：{{.stage}}，{{.content}}
        delete: 【删除】 {{.label}}：{{.name}}
        beReplaced: 【被替换】 当前受试者已经被受试者：{{.name}} 替换
        replaced-new: 【受试者替换】 原受试者：{{.name}}，原随机号：{{.beReplaceRandomNumber}}，替换受试者：{{.replaceName}}，替换受试者随机号：{{.replaceRandomNumber}}
        pvUnblinding: 【PV揭盲】 受试者：{{.name}} 已PV揭盲，揭盲原因：{{.reason}}
        remark-pvUnblinding: 【PV揭盲】 受试者：{{.name}} 已PV揭盲，揭盲原因:{{.reason}}，备注：{{.remark}}
        random: 【随机】 受试者：{{.name}} 已随机，随机号：{{.randomNumber}}，组别：{{.group}}
        randomSub: 【随机】 受试者：{{.name}} 已随机，随机号：{{.randomNumber}}，组别：{{.group}}，子组别：{{.subGroup}}
        randomNoNumber: 【随机】 受试者：{{.name}} 已随机，组别：{{.group}}
        randomNoNumberSub: 【随机】 受试者：{{.name}} 已随机，组别：{{.group}}，子组别：{{.subGroup}}
        replaced: 【替换】 当前受试者来源于受试者替换，被替换的受试者:{{.name}} 随机号：{{.randomNumber}}
        signOut: 【停用】 受试者：{{.name}} 已停用，停用原因：{{.reason}}
        unblinding: 【紧急揭盲】 受试者：{{.name}} 已紧急揭盲，揭盲原因：{{.reasonStr}}，备注：{{.reason}}，是否已经通知申办方：{{.isSponsor}}，备注：{{.remark}}
        unblinding-success: 揭盲（紧急）成功
        at-random-unblinding-success: 阶段：{{.stage}}，揭盲（紧急）成功
        unblinding-application: 【揭盲(紧急)申请】审批编号：{{.approvalNumber}}，状态:待审批
        at-random-unblinding-application: 【揭盲(紧急)申请】 阶段：{{.stage}}，审批编号：{{.approvalNumber}}，状态:待审批
        unblinding-application-pv: 【揭盲(pv)申请】审批编号：{{.approvalNumber}}，状态:待审批
        at-random-unblinding-application-pv: 【揭盲(pv)申请】 阶段：{{.stage}}，审批编号：{{.approvalNumber}}，状态:待审批
        unblinding-approval-agree: 【揭盲(紧急)审批】审批编号：{{.approvalNumber}}，状态:已通过
        unblinding-approval-agree-pv: 【揭盲(pv)审批】审批编号：{{.approvalNumber}}，状态:已通过
        unblinding-approval-reject: 【揭盲(紧急)审批】审批编号：{{.approvalNumber}}，状态:已拒绝，原因：{{.reason}}
        unblinding-approval-reject-pv: 【揭盲(pv)审批】审批编号：{{.approvalNumber}}，状态:已拒绝，原因：{{.reason}}
        update: 【修改】 {{.content}}
        at-random-update: 【修改】 阶段：{{.stage}}，{{.content}}
        at-random-random: 【随机】 阶段：{{.stage}}，{{.content}}
        updateSubjectNo: 【修改】受试者：{{.oldSubjectNo}}，改为：{{.shortname}}
        transfer: 【转运】 {{.label}}：{{.subjectNo}}，原中心：{{.oldSite}}，新中心：{{.newSite}}。
        switch-cohort: 【切换群组】 {{.label}}：{{.subjectNo}}，原群组：{{.oldCohort}}，新群组：{{.newCohort}}。
        at-random-transfer: 【转运】 阶段：{{.stage}}，{{.label}}：{{.subjectNo}}，原中心：{{.oldSite}}，新中心：{{.newSite}}。
        joinTime: 【编辑】 {{.label}}：{{.subjectNo}}，入组时间：{{.joinTime}}。
        at-random-joinTime: 【编辑】 阶段：{{.stage}}，{{.label}}：{{.subjectNo}}，入组时间：{{.joinTime}}。
        start-follow-up-visits: 【开启后续阶段访视】 阶段：{{.currentStage}}，{{.label}}：{{.subjectNo}}，当前阶段：{{.currentStage}}，后续阶段：{{.nextStage}}。
        close-follow-up-visits: 【关闭后续阶段访视】 阶段：{{.currentStage}}，{{.label}}：{{.subjectNo}}，当前阶段：{{.currentStage}}，后续阶段：{{.nextStage}}。
        label:
            common-key-value: "{{.name}}：{{.value}}"
            common-key-value1: "{{.name}}：{{.value}}"
            replaced-new: 【受试者替换】 原{{.label}}：{{.name}}，原随机号：{{.beReplaceRandomNumber}}，替换{{.label}}：{{.replaceName}}，替换受试者随机号：{{.replaceRandomNumber}}
            at-random-replaced-new-a: 【受试者替换】 原{{.label}}：{{.name}}，替换{{.label}}：{{.replaceName}}，阶段：{{.stage}}，原随机号：{{.beReplaceRandomNumber}}，替换受试者随机号：{{.replaceRandomNumber}}
            at-random-replaced-new-b: 【受试者替换】 原{{.label}}：{{.name}}，替换{{.label}}：{{.replaceName}}，阶段：{{.stage}}，原随机号：{{.beReplaceRandomNumber}}，替换受试者随机号：{{.replaceRandomNumber}}；阶段：{{.stage2}}，原随机号：{{.beReplaceRandomNumber2}}，替换受试者随机号：{{.replaceRandomNumber2}}
            pvUnblinding: 【PV揭盲】 {{.label}}：{{.name}} 已PV揭盲，揭盲原因：{{.reason}}
            at-random-pvUnblinding: 【PV揭盲】 阶段：{{.stage}}，{{.label}}：{{.name}} 已PV揭盲，揭盲原因：{{.reason}}
            remark-pvUnblinding: 【PV揭盲】 {{.label}}：{{.name}} 已PV揭盲，揭盲原因:{{.reason}}，备注：{{.remark}}
            at-random-remark-pvUnblinding: 【PV揭盲】 阶段：{{.stage}}，{{.label}}：{{.name}} 已PV揭盲，揭盲原因:{{.reason}}，备注：{{.remark}}
            random: 【随机】 {{.label}}：{{.name}} 已随机，随机号：{{.randomNumber}}，组别：{{.group}}
            at-random-random: 【随机】 阶段：{{.stage}}，{{.label}}：{{.name}} 已随机，随机号：{{.randomNumber}}，组别：{{.group}}
            randomSub: 【随机】 {{.label}}：{{.name}} 已随机，随机号：{{.randomNumber}}，组别：{{.group}}，子组别：{{.subGroup}}
            at-random-randomSub: 【随机】 阶段：{{.stage}}，{{.label}}：{{.name}} 已随机，随机号：{{.randomNumber}}，组别：{{.group}}，子组别：{{.subGroup}}
            randomNoNumber: 【随机】 {{.label}}：{{.name}} 已随机，组别：{{.group}}
            at-random-randomNoNumber: 【随机】 阶段：{{.stage}}，{{.label}}：{{.name}} 已随机，组别：{{.group}}
            randomNoNumberSub: 【随机】 {{.label}}：{{.name}} 已随机，组别：{{.group}}，子组别：{{.subGroup}}
            at-random-randomNoNumberSub: 【随机】 阶段：{{.stage}}，{{.label}}：{{.name}} 已随机，组别：{{.group}}，子组别：{{.subGroup}}
            signOut: 【停用】 {{.label}}：{{.name}} 已停用，停用原因：{{.reason}}
            at-random-signOut: 【停用】 阶段：{{.stage}}，{{.label}}：{{.name}} 已停用，停用原因：{{.reason}}
            signOutReal: 【停用】 {{.label}}：{{.name}} 已停用，实际停用日期：{{.signOutRealTime}}，停用原因：{{.reason}}
            at-random-signOutReal: 【停用】 阶段：{{.stage}}，{{.label}}：{{.name}} 已停用，实际停用日期：{{.signOutRealTime}}，停用原因：{{.reason}}
            unblinding: 【紧急揭盲】 {{.label}}：{{.name}} 已紧急揭盲，揭盲原因：{{.reasonStr}}，备注：{{.reason}}，是否已经通知申办方：{{.isSponsor}}，备注：{{.remark}}
            at-random-unblinding: 【紧急揭盲】 阶段：{{.stage}}，{{.label}}：{{.name}} 已紧急揭盲，揭盲原因：{{.reasonStr}}，备注：{{.reason}}，是否已经通知申办方：{{.isSponsor}}，备注：{{.remark}}
            updateSubjectNo: 【修改】{{.label}}：{{.oldSubjectNo}}，改为：{{.shortname}}
            screen: 【筛选】{{.label}}：{{.name}}，是否筛选成功：{{.isScreen}}，筛选日期：{{.screenTime}}，ICF签署日期：{{.icfTime}}
            at-random-screen: 【筛选】 阶段：{{.stage}}，{{.label}}：{{.name}} 已筛选，是否筛选成功：{{.isScreen}}，筛选日期：{{.screenTime}}，ICF签署日期：{{.icfTime}}
            screenScreenFail: 【筛选】{{.label}}：{{.name}}，是否筛选成功：{{.isScreen}}，筛选日期：{{.screenTime}}
            update: 【修改】{{.label}}：{{.name}}，{{.content}}
            at-random-update: 【修改】 阶段：{{.stage}}，{{.label}}：{{.name}}，{{.content}}
            updateCustomize: 【修改】{{.label}}：{{.name}}，{{.content}}，{{.updateFields}}
            update2Customize: 【编辑】 {{.updateFields}}
            updateCustomizeConnectingSymbol: "，"
            updateCustomizeLastSymbolKey: "。"
            updateCustomizeJoinTime: 入组时间：{{.joinTime}}
            updateCustomizeStage: 阶段：{{.stage}}
            updateCustomizeSignOutRealTime: 实际停用日期：{{.signOutRealTime}}
            updateCustomizeIsScreen: 是否筛选成功：{{.isScreen}}
            updateCustomizeScreenTime: 筛选日期：{{.screenTime}}
            updateCustomizeIcfTime: ICF签署日期：{{.icfTime}}
            updateCustomizeReason: 停用原因：{{.reason}}
            updateCustomizeRemark: 完成研究-备注：{{.remark}}
            updateSignOutTime: 【修改】{{.label}}：{{.name}}，{{.content}}，实际停用日期：{{.signOutRealTime}}
            updateScreen: 【修改】{{.label}}：{{.name}}，{{.content}}，是否筛选成功：{{.isScreen}}，筛选日期：{{.screenTime}}，ICF签署日期：{{.icfTime}}
            updateScreenSignOutTime: 【修改】{{.label}}：{{.name}}，{{.content}}，是否筛选成功：{{.isScreen}}，筛选日期：{{.screenTime}}，ICF签署日期：{{.icfTime}}，实际停用日期：{{.signOutRealTime}}
            updateScreenFail: 【修改】{{.label}}：{{.name}}，{{.content}}，是否筛选成功：{{.isScreen}}，筛选日期：{{.screenTime}}
            updateScreenFailSignOutTime: 【修改】{{.label}}：{{.name}}，{{.content}}，是否筛选成功：{{.isScreen}}，筛选日期：{{.screenTime}}，实际停用日期：{{.signOutRealTime}}
            finish: 【完成研究】{{.label}}：{{.name}}，随机号：{{.randomNumber}}，已完成研究，备注：{{.remark}}
            at-random-finish: 【完成研究】 阶段：{{.stage}}，{{.label}}：{{.name}}，随机号：{{.randomNumber}}，已完成研究，备注：{{.remark}}
    supply-plan:
        add: 【新增供应计划】 计划名称：{{.name}}，计划说明：{{.description}}
        update: 【修改供应计划】 计划名称：{{.name}}，计划说明：{{.description}}
    supply-plan-medicine:
        add: 【新增供应计划研究产品配置】 研究产品：{{.medicineName}}，初始发放量：{{.initSupply}} ，研究产品警戒值：{{.warning}}，最大缓冲量：{{.buffer}}，再供应量：{{.secondSupply}}，不运送天数：{{.unDistributionDate}}，不发放天数：{{.unProvideDate}}，有效期提醒：{{.validityReminder}}，自动配药：{{.autoSupply}}，自动配药量：{{.autoSupplySize}}，  补充方式：{{.supplyMode}}。
        update: 【修改供应计划研究产品配置】 研究产品：{{.medicineName}}，初始发放量：{{.initSupply}} ，研究产品警戒值：{{.warning}}，最大缓冲量：{{.buffer}}，再供应量：{{.secondSupply}}，不运送天数：{{.unDistributionDate}}，不发放天数：{{.unProvideDate}}，有效期提醒：{{.validityReminder}}，自动配药：{{.autoSupply}}，自动配药量：{{.autoSupplySize}}，  补充方式：{{.supplyMode}}。
mail:
    test: 测试
    text: 测试xx {{.Name}}
medicine:
    autoSupplySize1: 最大缓冲量
    autoSupplySize2: 再供应量
    errorSupplyMode: 补充方式不一致，提交失败，请重新修改。
    errorAutoSupplyMode: 自动配给量不一致，提交失败，请重新修改。
    errorSupplyModeHint: 补充方式不一致。
    errorAutoSupplyModeHint: 自动配给量不一致。
    expire_title: Clinflash IRT  {{.projectNumber}} {{.envName}} 研究产品有效期提醒 {{.instituteNumber}} {{.instituteName}}
    freeze:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} 研究产品隔离通知 {{.instituteInfo}} {{.freezeNumber}}
    release:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} 研究产品解隔离通知 {{.instituteInfo}} {{.freezeNumber}}
    un_provide_date:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} 研究产品达到不发放天数提醒 {{.siteNumber}} {{.siteNameZh}}
    status:
        available: 可用
        delivered: 已确认
        destroy: 已销毁
        expired: 已过期
        inStorage: 入库中
        lose: 丢失/作废
        quarantine: 已隔离
        receive: 已领药
        return: 已退货
        sending: 已运送
        stockPending: 待入库
        toBeConfirmed: 待确认
        toBeWarehoused: 待入仓
        transit: 已运送
        used: 已使用
        apply: 已申请
        frozen: 冻结
        locked: 锁定
    supplyMode1: 全研究产品补充
    supplyMode2: 单研究产品补充
    supplyMode3: 全研究产品补充 + 1个随机研究产品编号
    supplyMode4: 单研究产品补充 + 1个随机研究产品编号
medicineOrder_download_packageNumber: 包装号
medicineOrder_download_packageMethod: 运送方式
medicineOrder_download_packageMethodSingle: 单品
medicineOrder_download_packageMethodPackage: 包装
medicineOrder_download_batchNumber: 批次号
medicineOrder_download_cancelDate: 取消时间
medicineOrder_download_cancelUser: 取消人
medicineOrder_download_count: 研究产品数量
medicineOrder_download_createDate: 创建时间
medicineOrder_download_createUser: 创建人
medicineOrder_download_expiredDate: 有效期
medicineOrder_download_fileName: 订单报表
medicineOrder_download_medicineNumber: 研究产品编号
medicineOrder_download_medicine: 研究产品
medicineOrder_download_number: 订单号
medicineOrder_download_orderInfo: 订单详情
medicineOrder_download_other: 数量
medicineOrder_download_receiveDate: 接收时间
medicineOrder_download_expectedArrivalTime: 期望送达时间
medicineOrder_download_actualReceiptTime: 实际接收时间
medicineOrder_download_receiveInstitute: 目的地
medicineOrder_download_receiveUser: 接收人
medicineOrder_download_sendInstitute: 起运地
medicineOrder_download_status: 订单状态
medicineOrder_download_cancelReason: 取消原因
medicineOrder_download_confirmUser: 确认人
medicineOrder_download_confirmDate: 确认时间
medicineOrder_download_closeUser: 关闭人
medicineOrder_download_closeDate: 关闭时间
medicineOrder_download_closeReason: 关闭原因
medicineOrder_download_sendUser: 运送人
medicineOrder_download_sendDate: 运送时间
medicineOrder_download_lostUser: 丢失人
medicineOrder_download_lostDate: 丢失时间
medicineOrder_download_lostReason: 丢失原因
medicineOrder_download_endUser: 终止人
medicineOrder_download_endDate: 终止时间
medicineOrder_download_endReason: 终止原因
medicineOrder_download_supplier: 物流供应商
medicineOrder_download_supplierOther: 其他物流
medicineOrder_download_supplierNumber: 物流单号
medicine_batch_number: 批次号
medicine_download_batch: 批次号
medicine_download_spec: 规格
medicine_download_packageNumber: 包装号
medicine_download_depot_name: 库房统计报表
medicine_download_expiredDate: 有效期
medicine_download_location: 位置
medicine_download_name: 研究产品名称
medicine_download_number: 研究产品编号
medicine_download_orderNumber: 订单号
medicine_download_country: 国家(分层属性)
medicine_download_region: 区域(分层属性)
medicine_download_site_country: 国家
medicine_download_site_region: 区域
medicine_download_site: 中心名称
medicine_download_site_name: 中心药房报表
medicine_download_dtp_sku: 研究产品单品报表
medicine_download_status: 状态
medicine_download_storehouse: 库房
medicine_download_reason: 原因
medicine_download_operFree: 隔离
medicine_download_operRelease: 解隔离
medicine_download_operLost: 丢失/作废
medicine_download_operUse: 设为可用
medicine_download_operator: 操作人
medicine_download_time: 操作时间
medicine_download_freeze_reason: 隔离原因
medicine_download_freeze_operator: 隔离操作人
medicine_download_freeze_time: 隔离操作时间
medicine_download_release_reason: 解隔离原因
medicine_download_release_operator: 解隔离操作人
medicine_download_release_time: 解隔离操作时间
medicine_download_lost_reason: 丢失/作废原因
medicine_download_lost_operator: 丢失/作废操作人
medicine_download_lost_time: 丢失/作废操作时间
medicine_download_use_reason: 设为可用原因
medicine_download_use_operator: 设为可用操作人
medicine_download_use_time: 设为可用操作时间
medicine_download_site_number: 中心编号
medicine_duplicated: 研究产品已存在
medicine_duplicated_number: 请勿重复上传相同研究产品编号
medicine_duplicated_package_number: 请勿重复上传相同包装号
medicine_duplicated_serial_package_number: 请勿重复上传相同包装序列号
medicine_upload_package_number: 包装号未上传，请重新上传。
medicine_upload_package_serial_number: 包装序列号未上传，请重新上传。
medicine_upload_package_count: 包装号数量错误，请重新上传。
medicine_upload_drug_name: 请选择正确的研究产品。
medicine_upload_package_serial_count: 包装序列号数量错误，请重新上传。
medicine_duplicated_serial_number: 请勿重复上传相同序列号
medicine_expiration_date: 有效期
medicine_list_download_name: 研究产品列表
medicine_name: 研究产品名称
medicine_not_exist: 研究产品不存在
medicine_number: 研究产品编号
medicine_package: 包装号
medicine_package_barcode: 包装号-条形码
medicine_package_barcode_short: 包装号&条形码组合
medicine_code: 可识别短码
medicine_examine_uccess: 审核通过
medicine_examine_fail: 审核失败
medicine_examine_update: 修改
medicine_examine_release: 放行
medicine_barcode_code: 研究产品-条形码
medicine_barcode_code_short: 条形码&短码组合
medicine_serial_number: 序列号
medicine_packlist_upload_check: 研究产品编号不存在，请先上传研究产品编号
medicine_packlist_upload_firstPack: 包装号不能为空，请重新确认。
medicine_status: 状态
minimize_bias_probability_tips: 偏倚概率不可为空
minimize_layered_tips: 未获取到组别分层偏差，请重新确认。
project_edc_irt_return: 受试者编号前缀规则与EDC设置不一致，请重新确认
variable_duplicated: 变量ID重复，请重新确认。
form_name_duplicated: 表单名称重复，请重新确认。
option_label_duplicated: 添加重复，请重新确认。
combined_dispensation_label_duplicated: 组合标签内研究产品发放方式需一致，请重新确认。
form_invalid_error: 无效失败，字段已在研究产品配置中应用。
update_medicine_status_error: 部分研究产品状态已变更，请重新选择。
is_screen: 是否筛选成功
screen_time: 筛选日期
icf_time: ICF签署日期
operator:
    people: 操作人
    content: 操作内容
    reason: 原因
    time: 操作时间
order:
    automatic_error: <p>项目编号:{{.projectNumber}}</p> <p>项目名称:{{.projectName}}</p> <p>项目环境:{{.envName}}</p> <p>起运地:{{.start}}</p> <p>目的地:{{.destination}}</p> <p>创建自动研究产品订单失败，请检查自动配药量和库存研究产品数量是否匹配.</p>
    automatic_error_dual: <p>项目编号:{{.projectNumber}}</p> <p>项目名称:{{.projectName}}</p> <p>项目环境:{{.envName}}</p> <p>起运地:{{.start}}</p> <p>目的地:{{.destination}}</p> <p>创建自动研究产品订单失败,请检查自动配药量和库存研究产品数量是否匹配.</p> <p></p>
    automatic_error_title: Clinflash IRT  {{.projectNumber}}({{.envName}}) 自动创建订单失败提醒 {{.destination}}
    automatic_alarm_title: Clinflash IRT {{.projectNumber}}({{.envName}}) 自动订单警戒
    automatic_success_title: Clinflash IRT {{.projectNumber}}({{.envName}}) 订单自动创建提醒 {{.destination}} {{.orderNumber}}
    cancel: "<p>起运地:{{.start}}</p>
        <p>目的地:{{.destination}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>取消原因：{{.reason}}</p>
        <p>订单已取消.</p>"
    cancel_dtp: "<p>起运地:{{.start}}</p>
        <p>{{.label}}:{{.subject}}</p>
        <p>随机号:{{.randomNumber}}</p>
        <p>组别:{{.group}}</p>
        <p>访视名称:{{.visit}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>取消原因:{{.reason}}</p>
        <p>订单已取消.</p>"
    cancel_dtp_sub: "<p>起运地:{{.start}}</p>
        <p>{{.label}}:{{.subject}}</p>
        <p>随机号:{{.randomNumber}}</p>
        <p>组别:{{.group}}</p>
        <p>子组别:{{.subGroup}}</p>
        <p>访视名称:{{.visit}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>取消原因:{{.reason}}</p>
        <p>订单已取消.</p>"
    cancel-logistics: "<p>起运地:{{.start}}</p>
        <p>{{.label}}:{{.subject}}</p>
        <p>访视周期:{{.visit}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>取消原因：{{.reason}}</p>
        <p>订单已取消.</p>"
    close: "<p>起运地:{{.start}}</p>
        <p>目的地:{{.destination}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>关闭原因：{{.reason}}</p>
        <p>订单已关闭.</p>"
    close_dtp: "<p>{{.label}}:{{.subject}}</p>
        <p>访视名称:{{.visit}}</p>
        <p>随机号:{{.randomNumber}}</p>
        <p>组别:{{.group}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>关闭原因:{{.reason}}</p>
        <p>订单已关闭.</p>"
    close_dtp_sub: "<p>{{.label}}:{{.subject}}</p>
        <p>访视名称:{{.visit}}</p>
        <p>随机号:{{.randomNumber}}</p>
        <p>组别:{{.group}}</p>
        <p>子组别:{{.subGroup}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>关闭原因:{{.reason}}</p>
        <p>订单已关闭.</p>"
    close-logistics: "<p>起运地:{{.start}}</p>
        <p>{{.label}}:{{.subject}}</p>
        <p>访视周期:{{.visit}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>关闭原因：{{.reason}}</p>
        <p>订单已关闭.</p>"
    end: "<p>起运地:{{.start}}</p>
        <p>目的地:{{.destination}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>终止原因：{{.reason}}</p>
        <p>订单已终止.</p>"
    end-logistics: "<p>起运地:{{.start}}</p>
        <p>{{.label}}:{{.subject}}</p>
        <p>访视周期:{{.visit}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>物流信息:{{.logistics}}</p>
        <p>终止原因：{{.reason}}</p>
        <p>订单已终止.</p>"
    lost_dtp: "<p>起运地:{{.start}}</p>
        <p>{{.label}}:{{.subject}}</p>
        <p>随机号:{{.randomNumber}}</p>
        <p>组别:{{.group}}</p>
        <p>访视周期:{{.visit}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>丢失原因:{{.reason}}</p>
        <p>{{.userName}} 确认该订单已丢失.</p>"
    lost_dtp_sub: "<p>起运地:{{.start}}</p>
        <p>{{.label}}:{{.subject}}</p>
        <p>访视周期:{{.visit}}</p>
        <p>随机号:{{.randomNumber}}</p>
        <p>组别:{{.group}}</p>
        <p>子组别:{{.subGroup}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>丢失原因:{{.reason}}</p>
        <p>{{.userName}} 确认该订单已丢失.</p>"
    cancel_title: Clinflash IRT {{.projectNumber}} {{.envName}} 订单取消通知 {{.destination}} {{.orderNumber}}
    close_title: Clinflash IRT {{.projectNumber}} {{.envName}} 订单关闭通知 {{.destination}} {{.orderNumber}}
    end_title: Clinflash IRT {{.projectNumber}} {{.envName}} 订单终止通知 {{.destination}} {{.orderNumber}}
    cancel_title_dtp: Clinflash IRT {{.projectNumber}} {{.envName}} 订单取消通知 {{.subject}} {{.visit}} {{.orderNumber}}
    close_title_dtp: Clinflash IRT {{.projectNumber}} {{.envName}} 订单关闭通知 {{.subject}} {{.visit}} {{.orderNumber}}
    change_title: Clinflash IRT {{.projectNumber}} {{.envName}} 研究产品更换  {{.destination}} {{.orderNumber}}
    change_title_dtp: Clinflash IRT {{.projectNumber}} {{.envName}} 研究产品更换 {{.subject}} {{.visit}} {{.orderNumber}}
    batch_expiration_title_dtp: Clinflash IRT {{.projectNumber}} {{.envName}} 研究产品有效期和批次更新 {{.orderNumber}}
    lost: "<p>起运地:{{.start}}</p>
        <p>目的地:{{.destination}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>丢失原因:{{.reason}}</p>
        <p>{{.userName}} 确认该订单已丢失.</p>"
    lost_title: Clinflash IRT {{.projectNumber}} {{.envName}} 订单丢失通知 {{.destination}} {{.orderNumber}}
    lost_title_dtp: Clinflash IRT {{.projectNumber}} {{.envName}} 订单丢失通知 {{.subject}} {{.visit}} {{.orderNumber}}
    no_automatic: <p>项目编号:{{.projectNumber}}</p> <p>项目名称:{{.projectName}}</p> <p>项目环境:{{.envName}}</p> <p>中心编号:{{.siteNumber}}</p> <p>中心名称:{{.siteName}}</p> <p>库存研究产品低于警戒值，请及时补充.</p>
    no_automatic_dual: "<p>项目编号:{{.projectNumber}}</p>
        <p>项目名称:{{.projectName}}</p>
        <p>项目环境:{{.envName}}</p>
        <p>中心编号:{{.siteNumber}}</p>
        <p>中心名称:{{.siteName}}</p>
        <p>库存研究产品低于警戒值，请及时补充.</p>
        <p></p>
        <p>Project Number:{{.projectNumber}}</p>
        <p>Project Name:{{.projectName}}</p>
        <p>Project Environment:{{.envName}}</p>
        <p>Site Number:{{.siteNumber}}</p>
        <p>Site Name:{{.siteName}}</p>
        <p>The IP in stock are lower than the warning value， please replenish them in time</p>"
    no_automatic_success_title: Clinflash IRT {{.projectNumber}} {{.envName}} 订单确认通知 {{.destination}} {{.orderNumber}}
    medicine_order_title: Clinflash IRT {{.projectNumber}} {{.envName}} 订单创建通知 {{.destination}} {{.orderNumber}}
    medicine_order_dtp_title: Clinflash IRT {{.projectNumber}} {{.envName}} 订单创建通知 {{.subject}} {{.visit}} {{.orderNumber}}
    forecast_title: Clinflash IRT {{.projectNumber}} {{.envName}} 库存使用时间预测
    no_automatic_title: Clinflash IRT {{.projectNumber}} {{.envName}} 中心研究产品警戒通知 {{.siteNumber}} {{.siteName}}
    over_title_depot: Clinflash IRT {{.projectNumber}}({{.envName}}) {{.siteName}} 订单超时提醒 {{.destination}} {{.orderNumber}}
    over_title_site: Clinflash IRT {{.projectNumber}}({{.envName}}) 订单超时提醒 {{.destination}} {{.orderNumber}}
    overtime_depot: <p>项目编号:{{.projectNumber}}</p> <p>项目名称:{{.projectName}}</p> <p>项目环境:{{.envName}}</p> <p>接收机构名称:{{.siteName}}</p> <p>订单编号:{{.orderNumber}}</p> <p>有订单尚未确认收到，状态为{{.statusItem}}，订单生成时间{{.generateDate}}，请确认.</p>
    overtime_site: <p>项目编号:{{.projectNumber}}</p> <p>项目名称:{{.projectName}}</p> <p>项目环境:{{.envName}}</p> <p>接收机构编号:{{.siteNumber}}</p> <p>接收机构名称:{{.siteName}}</p> <p>订单编号:{{.orderNumber}}</p> <p>有订单尚未确认收到，状态为{{.statusItem}}，订单生成时间{{.generateDate}}，请确认.</p>
    receive: "<p>起运地:{{.start}}</p>
        <p>目的地:{{.destination}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>{{.userName}} 确认该订单已在目的地收到。</p>"
    receive-logistics: "<p>起运地:{{.start}}</p>
        <p>{{.label}}:{{.subject}}</p>
        <p>访视名称:{{.visit}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>物流信息:{{.logistics}}</p>
        <p>{{.userName}} 确认该订单已在目的地收到。</p>"
    receive_dtp: "<p>{{.label}}:{{.subject}}</p>
        <p>随机号:{{.randomNumber}}</p>
        <p>组别:{{.group}}</p>
        <p>访视名称:{{.visit}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>物流信息:{{.logistics}}</p>
        <p>{{.userName}} 确认该订单已在目的地收到。</p>"
    receive_dtp_sub: "<p>{{.label}}:{{.subject}}</p>
        <p>随机号:{{.randomNumber}}</p>
        <p>组别:{{.group}}</p>
        <p>子组别:{{.subGroup}}</p>
        <p>访视名称:{{.visit}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>{{.userName}} 确认该订单已在目的地收到。</p>"
    receive_title: Clinflash IRT {{.projectNumber}} {{.envName}} 订单接收通知 {{.destination}} {{.orderNumber}}
    receive_title_dtp: Clinflash IRT {{.projectNumber}} {{.envName}} 订单接收通知 {{.subject}} {{.visit}} {{.orderNumber}}
    recovery_title: Clinflash IRT {{.projectNumber}} {{.envName}} 回收订单创建通知 {{.destination}} {{.orderNumber}}
    recovery_confirm_title: Clinflash IRT {{.projectNumber}} {{.envName}} 回收订单确认通知 {{.destination}} {{.orderNumber}}
    send: "<p>起运地:{{.start}}</p>
        <p>目的地:{{.destination}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>研究产品订单运送中.</p>"
    send-logistics: "<p>起运地:{{.start}}</p>
        <p>{{.label}}:{{.subject}}</p>
        <p>访视名称:{{.visit}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>物流信息:{{.logistics}}</p>
        <p>研究产品订单运送中.</p>"
    send_dtp: "<p>{{.label}}:{{.subject}}</p>
        <p>随机号:{{.randomNumber}}</p>
        <p>组别:{{.group}}</p>
        <p>访视名称:{{.visit}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>物流信息:{{.logistics}}</p>
        <p>研究产品订单运送中.</p>"
    send_dtp_sub: "<p>{{.label}}:{{.subject}}</p>
        <p>随机号:{{.randomNumber}}</p>
        <p>组别:{{.group}}</p>
        <p>子组别:{{.subGroup}}</p>
        <p>访视名称:{{.visit}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>物流信息:{{.logistics}}</p>
        <p>研究产品订单运送中.</p>"
    send_title: Clinflash IRT {{.projectNumber}} {{.envName}} 订单运送通知 {{.destination}} {{.orderNumber}}
    send_title_dtp: Clinflash IRT {{.projectNumber}} {{.envName}} 订单运送通知 {{.subject}} {{.visit}} {{.orderNumber}}
    create_title_logistics: Clinflash IRT {{.projectNumber}} {{.envName}} 订单确认通知 {{.subject}} {{.visit}} {{.orderNumber}}
    create_logistics: "<p>起运地:{{.start}}</p>
        <p>{{.label}}:{{.subject}}</p>
        <p>访视名称:{{.visit}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>研究产品订单已创建.</p>"
    create_logistics_dtp: "<p>起运地:{{.start}}</p>
        <p>{{.label}}:{{.subject}}</p>
        <p>访视名称:{{.visit}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>随机号:{{.randomNumber}}</p>
        <p>组别:{{.group}}</p>
        <p>研究产品订单已确认.</p>"
    confrim_logistics: "<p>起运地:{{.start}}</p>
        <p>{{.label}}:{{.subject}}</p>
        <p>访视名称:{{.visit}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>研究产品订单已确认.</p>"
    end_title_dtp: Clinflash IRT {{.projectNumber}} {{.envName}} 订单终止通知 {{.subject}} {{.visit}} {{.orderNumber}}
    end_dtp: "<p>{{.label}}:{{.subject}}</p>
        <p>访视名称:{{.visit}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>物流信息:{{.logistics}}</p>
        <p>随机号:{{.randomNumber}}</p>
        <p>组别:{{.group}}</p>
        <p>终止原因:{{.reason}}</p>
        <p>该订单已终止</p>"
    end_dtp_sub: "<p>{{.label}}:{{.subject}}</p>
        <p>访视名称:{{.visit}}</p>
        <p>订单号:{{.orderNumber}}</p>
        <p>物流信息:{{.logistics}}</p>
        <p>随机号:{{.randomNumber}}</p>
        <p>组别:{{.group}}</p>
        <p>子组别:{{.subGroup}}</p>
        <p>终止原因:{{.reason}}</p>
        <p>该订单已终止</p>"
    approval:
        add-title: Clinflash IRT {{.projectNumber}} {{.envName}} 研究中心订单申请
        failed-title: Clinflash IRT {{.projectNumber}} {{.envName}} 研究中心订单申请审批失败
    single:
        start: "<p>起运地:{{.start}}</p>"
        subject: "<p>{{.label}}:{{.subject}}</p>"
        visit: "<p>访视周期:{{.visit}}</p>"
        orderNumber: "<p>订单号:{{.orderNumber}}</p>"
        expectedArrivalTime: "<p>期望送达时间:{{.expectedArrivalTime}}</p>"
        logistics: "<p>物流信息:{{.logistics}}</p>"
        lostReason: "<p>丢失原因:{{.reason}}</p>"
        userName: "<p>{{.userName}} 确认该订单已丢失.</p>"
order_status_cancelled: 已取消
order_status_not_cancelled: 当前订单状态不可终止，请刷新页面
order_status_wms_not_cancelled: 不可以取消订单
order_status_lose: 已丢失
order_status_received: 已接收
order_status_requested: 已确认
order_status_toBeConfirmed: 待确认
order_status_transit: 已运送
order_status_apply: 已申请
order_status_terminated: 已终止
order_status_close: 已关闭
order_logistics_1: "顺丰"
order_logistics_2: "EMS"
order_logistics_3: "京东"
order_logistics_4: "圆通"
order_logistics_5: "韵达"
order_logistics_6: "中通"
order_logistics_7: "申通"
order_logistics_8: "极兔"
order_logistics_9: "其他"
page_notice:
    mail:
        send_fail: 发送失败
        send_success: 发送成功
    system_update:
        email_error: 邮箱地址错误
        others: 其他
        quota_exceeded: 超过主帐户每日发送配额
        timeout: 邮件服务器超时
project:
    env: "<p>项目环境:{{.envName}}</p>"
    name: "<p>项目名称:{{.projectName}}</p>"
    number: "<p>项目编号:{{.projectNumber}}</p>"
    cohort: "<p>名称:{{.cohortName}}</p>"
    order:
        expectedArrivalTime: "<p>期望送达时间:{{.expectedArrivalTime}}</p>"
    site:
        number: "<p>中心编号:{{.siteNumber}}</p>"
        name: "<p>中心名称:{{.siteName}}</p>"
        delete: 该中心已被使用不能禁用
    storehouse:
        delete: 该库房已被使用不能删除
        had:
            add: 库房已存在
        unConnected: 该库房未对接物流仓库，请勿推送入库数据
    user:
        join: <p>{{.customerName}}邀请你加入随机实验研究</p> <p>项目编号:{{.projectNumber}}</p> <p>项目名称:{{.projectName}}</p>
        title: Clinflash IRT 项目邀请
projects:
    duplicated:
        names: 项目名称重复
        numbers: 编号重复，请重新输入
random:
    number: 随机号
randomList:
    export:
        available: 可用
        block: 区组
        center: 中心
        delete: 已作废
        inactivate: 无效
        group: 组别
        number: 随机号
        status: 状态
        subject: 受试者
        used: 已使用
randomNumber:
    export:
        status:
            invalid: 无效
            used: 已使用
            unused: 可用
            unavailable: 不可用
    exist:
        used: 错误，该区组存在使用过的随机号，无法重新分配其他分层，请重新确认。
        used.clean: 错误，该区组存在使用过的随机号，无法清空其他分层，请重新确认。
random_filed_error: 有重复的变量字段，请重新确认。
random_attribute_error: 最小化随机计算已配置”不适用“，请修改后再创建。
random_length_error: 错误!生成的随机号长度已经超过了设置的号码长度
random_list_download_name: 随机号报表
random_number_block_error: 区组需唯一，请重新确认。
random_number_error: 错误，重复的随机号，请重新确认。
random_number_format_error: 上传列表格式错误，请重新确认。
random_number_format_error_trim: 上传数据有空格，请重新上传
random_list_name_duplicated: 名称已存在
random_total_error: 错误，总例数须要被各分组比例之和整除，请重新确认。
random_duplicated_factor: 已存在相同分层因素，请勿重复配置
randomNumber_status_error: 随机号状态已变更，请重新确认。
randomization:
    accuracy:
        ones: 小于等于
        twos: 等于
    type:
        ones: 区组随机
        twos: 最小化随机
    upload:
        blockSize: 随机表中区组长度与系统中配置的可接受的区组大小不一致，请重新确认。
        group: 错误，组别与配置的治疗组别不一致。
        group_error: 上传失败，上传组别与系统配置不一致，请重新确认。
        group_tip_error: 上传盲底组别数量和系统配置不一致，请确认。
roles:
    delete:
        message: 角色已使用，不能删除
    duplicated:
        names: 重复添加，请重新修改
        sys_check: 不允许修改系统管理员角色菜单权限
        project_admin_check: 不允许修改项目管理员项目查看权限
shipment_order_add_info: 因为是盲法项目，所以请选择2组研究产品名称
shipment_order_buffer_info: 当前中心的库存量大于最大缓冲量
shipment_order_cancel_not_exist: 物流中心该出库单号不存在
shipment_order_cancel_not_status: 物流已发货，无法取消该运单
shipment_order_initial_info: 当前中心的无法重复初始发放
shipment_order_create_error: 没有需要创建的研究产品，请重新选择
shipment_order_mode_info: 起运地为中心时，补充方式只能为发放数量补充方式
shipment_order_over_warning_info: 当前中心的可用库存量高于警戒值
shipment_order_sendAndReceive_info: 起运地与目的地不能重复
shipment_order_dtp_info: DTP订单不允许部分确认、接收
shipment_order_check_packageNumber: 整盒包装数量和配置中不一致
shipment_order_supply_info: 供应计划未包含全部研究产品供应配置，请联系项目管理员配置。
shipment_order_supply_page_error: 无法创建订单，“{{.currentName}}”为包装运输，同包装研究产品“{{.otherName}}”所属群组状态为”已完成/草稿/已终止“，请重新确认。
shipment_order_create_modeMax_info: 目的地可用库存已大于最大缓冲量，研究产品订单不生成
shipment_order_no_supply: 请先配置供应计划。
shipment_order_ratio_err: 目的地中心未开启供应比例配置。
shipment_out_of_stock: 库存不足，无法创建研究产品订单，请联系管理员补充库存。
shipment_change_out_of_stock: 库存不足，无法更换，请联系管理员补充库存。
shipment_order_supplyRatio_create_fail: 订单生成失败，研究产品数量与比例不匹配，请重新确认。
simulated:
    random:
        list:
            factor: 分层因素
            group: 组别
            name: SimulationResult
            number: 随机号
            only: 只允许启动一个随机列表
            runCount: 运行次数
            site: 研究机构
            subject: 受试者号
        number:
            not:
                enough: 受试者数必须小于随机号个数
site:
    disabled: 该中心未被启用
    had:
        bind: 该中心曾被启用不能删除
    name: 中心名称
    number: 中心编号
sites:
    duplicated:
        number: 编号重复，请重新输入
storehouse:
    had:
        bind: 该库房曾被启用不能删除
storehouses:
    duplicated:
        number: 库房编号重复
subject:
    alarm:
        content: <p>分层录入人数达到警戒值</p> <p>分层/警戒人数/实际人数：{{.info}}</p>
        title: Clinflash IRT {{.projectNumber}} {{.envName}} 受试者警戒
    alert_threshold:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} 受试者上限设置提醒
        cohort_title: Clinflash IRT {{.projectNumber}} {{.envName}} 受试者上限设置提醒
    group: 组别
    number: 受试者号
    replace: 替换
    replace_subject: 替换受试者号
    replace_number: 替换受试者随机号
    pvUnblinding:
        content: <p>项目编号:{{.projectNumber}}</p> <p>项目名称:{{.projectName}}</p> <p>项目环境:{{.envName}}</p> <p>中心编号:{{.siteNumber}}</p> <p>中心名称:{{.siteName}}</p> <p>受试者{{.subjectNumber}}已pv揭盲， 时间:{{.time}} 原因:{{.reason}}</p>
        title: Clinflash IRT 受试者pv揭盲
    random:
        content_no_group: <p>{{.label}}:{{.subjectNumber}}</p> <p>随机号:{{.randomNumber}}</p> <p>随机时间:{{.time}}</p>
        content: <p>{{.label}}:{{.subjectNumber}}</p> <p>组别:{{.group}}</p> <p>随机号:{{.randomNumber}}</p> <p>随机时间:{{.time}}</p>
        content_sub: <p>{{.label}}:{{.subjectNumber}}</p>  <p>组别:{{.group}}</p> <p>子组别:{{.subGroup}}</p> <p>随机号:{{.randomNumber}}</p> <p>随机时间:{{.time}}</p>
        content_no_random_number_no_group: <p>{{.label}}:{{.subjectNumber}}</p> <p>随机时间:{{.time}}</p>
        content_no_random_number: <p>{{.label}}:{{.subjectNumber}}</p> <p>组别:{{.group}}</p> <p>随机时间:{{.time}}</p>
        content_no_random_number_sub: <p>{{.label}}:{{.subjectNumber}}</p> <p>组别:{{.group}}</p> <p>子组别:{{.subGroup}}</p> <p>随机时间:{{.time}}</p>
        fileName: 随机报表
        title: Clinflash IRT {{.projectNumber}} {{.envName}} 受试者随机 {{.siteNumber}} {{.siteName}}
    add:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} 受试者登记 {{.siteNumber}} {{.siteName}}
    status: 状态
    status.registered: 已登记
    status.random: 已随机
    status.sign.out: 已停用
    signOut:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} 受试者停用 {{.siteNumber}} {{.siteName}}
        content: <p>{{.label}}:{{.subjectNumber}}</p>
            <p>随机号:{{.randomNumber}}</p>
            <p>组别:{{.group}}</p>
            <p>停用时间:{{.stopTime}}</p>
            <p>原因:{{.reason}}</p>
        content_no_group: <p>{{.label}}:{{.subjectNumber}}</p>
            <p>随机号:{{.randomNumber}}</p>
            <p>停用时间:{{.stopTime}}</p>
            <p>原因:{{.reason}}</p>
        content_sub: <p>{{.label}}:{{.subjectNumber}}</p>
            <p>随机号:{{.randomNumber}}</p>
            <p>组别:{{.group}}</p>
            <p>子组别:{{.subGroup}}</p>
            <p>停用时间:{{.stopTime}}</p>
            <p>原因:{{.reason}}</p>
        content_no_random_number_no_group: <p>{{.label}}:{{.subjectNumber}}</p>
            <p>停用时间:{{.stopTime}}</p>
            <p>原因:{{.reason}}</p>
        content_no_random_number: <p>{{.label}}:{{.subjectNumber}}</p>
            <p>组别:{{.group}}</p>
            <p>停用时间:{{.stopTime}}</p>
            <p>原因:{{.reason}}</p>
        content_no_random_number_sub: <p>{{.label}}:{{.subjectNumber}}</p>
            <p>组别:{{.group}}</p>
            <p>子组别:{{.subGroup}}</p>
            <p>停用时间:{{.stopTime}}</p>
            <p>原因:{{.reason}}</p>
    replacement:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} 受试者替换 {{.siteNumber}} {{.siteName}}
    modify:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} 受试者修改 {{.siteNumber}} {{.siteName}}
    screen:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} 受试者筛选 {{.siteNumber}} {{.siteName}}
    status.unblinding: 已揭盲(紧急)
    status.screen.success: 筛选成功
    status.screen.fail: 筛选失败
    status.finish: 完成研究
    status.to.be.random: 待随机
    status.join: 已入组

    unblinding-approval:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} 受试者紧急揭盲审批结果 {{.siteNumber}} {{.siteName}}
        content: <p>{{.label}}:{{.subjectNumber}}</p>
            <p>随机号:{{.randomNumber}}</p>
            <p>紧急揭盲时间:{{.time}} </p>
            <p>紧急揭盲原因:{{.reason}}</p>
            <p>备注:{{.remark}}</p>
            <p>审批编号:{{.approvalNumber}}</p>
            <p>审批结果:{{.approvalResult}}</p>
            <p>原因:{{.rejectReason}}</p>
    ordinary-unblinding-approval:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} 紧急揭盲成功 {{.siteNumber}} {{.siteName}}
        content: <p>项目编号:{{.projectNumber}}</p>
            <p>项目名称:{{.projectName}}</p>
            <p>项目环境:{{.envName}}</p>
            <p>中心编号:{{.siteNumber}}</p>
            <p>中心名称:{{.siteName}}</p>
            <p>{{.label}}:{{.subjectNumber}}</p>
            <p>随机号:{{.randomNumber}}</p>
            <p>紧急揭盲时间:{{.time}} </p>
            <p>紧急揭盲原因:{{.reason}}</p>
    pv-unblinding-approval:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} 受试者PV揭盲审批结果 {{.siteNumber}} {{.siteName}}
        content: <p>{{.label}}:{{.subjectNumber}}</p>
            <p>随机号:{{.randomNumber}}</p>
            <p>pv揭盲时间:{{.time}} </p>
            <p>pv揭盲原因:{{.reason}}</p>
            <p>备注:{{.remark}}</p>
            <p>审批编号:{{.approvalNumber}}</p>
            <p>审批结果:{{.approvalResult}}</p>
            <p>原因:{{.rejectReason}}</p>
    ip-unblinding-approval:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} 研究产品揭盲审批结果 {{.siteNumber}} {{.siteName}}
        content: <p>{{.label}}:{{.subjectNumber}}</p>
            <p>随机号:{{.randomNumber}}</p>
            <p>研究产品揭盲时间:{{.time}} </p>
            <p>研究产品揭盲原因:{{.reason}}</p>
            <p>备注:{{.remark}}</p>
            <p>审批编号:{{.approvalNumber}}</p>
            <p>审批结果:{{.approvalResult}}</p>
            <p>原因:{{.rejectReason}}</p>
    unblinding:
        controlContent: <p>{{.label}}:{{.subjectNumber}}</p>
            <p>随机号:{{.randomNumber}}</p>
            <p>紧急揭盲时间:{{.time}} </p>
            <p>紧急揭盲原因:{{.reason}}</p>
        content: <p>{{.label}}:{{.subjectNumber}}</p>
            <p>随机号:{{.randomNumber}}</p>
            <p>紧急揭盲时间:{{.time}} </p>
            <p>紧急揭盲原因:{{.reason}}</p>
            <p>是否已经通知申办方:{{.isSponsor}}</p>
            <p>备注:{{.remark}}</p>
        fileName: 揭盲报表
        sponsor: 是否已经通知申办方
        title: Clinflash IRT {{.projectNumber}} {{.envName}} 紧急揭盲成功 {{.siteNumber}} {{.siteName}}
subject_cohort_check: 当前阶段非入组状态不能进行随机操作
subject_cohort_check_register: 登记失败，草稿态不允许登记。
subject_register_number_fail: 登记失败，受试者数量已达最大位长数。
subject_cohort_last_group: 上阶段该受试者未随机入组
subject_factor_check_error: 随机数已超过项目上限或项目非入组状态，请及时联系系统配置人员调整后再操作随机。
simulate_random_site_cont_error: 保存失败，区域/国家数必须小于等于中心数。
subject_factor_no_null: 分层因素不能为空
subject_medicine_batch_count: 发放失败，无可用有效期内发放的研究产品，请确认。
subject_medicine_count: 发放失败，中心库存不足。
subject_medicine_count_store: 发放失败，库房库存不足。
subject_medicine_alarm_count: 发放失败，中心库存不足。
subject_medicine_dose_error: 发放失败，无法匹配访视对应的发放剂量。
subject_medicine_dtp_error: 组合标签内研究产品发放方式需一致，请重新确认。
subject_medicine_label_select: 请选择研究产品标签或研究产品
subject_medicine_label_select_outsize: 发放失败，未配置独立计划外访视发放规则，请确认。
subject_medicine_count_real: 库存内无该研究产品，请重新输入
subject_medicine_count_real_same: 登记研究产品不允许相同批次，请重新输入
subject_medicine_other: 未编号研究产品
subject_no_random: 没有匹配的随机号，请重新确认。
subject_no_drug_configure: 随机失败，研究产品未配置，请及时联系系统配置人员调整后再操作随机。
subject_no_enough_drug: 随机失败，研究产品库存不足，请确认。
subject_site_no_depot: 无法随机/发放，研究中心未绑定库房，请联系管理员配置。
subject_no_register: 受试者未登记
subject_replace_no_register_screenFail: 替换失败，替换受试者未登记/筛选失败，请重新输入。
subject_replace_no_site_fail: 替换失败，替换受试者号不在当前中心内，请重新确认。
subject_replace_no_site_fail_cohort: 替换受试者号不在当前群组内，请重新确认。
subject_replace_no_site_fail_stage: 替换受试者号不在当前阶段内，请重新确认。
subject_replace_no_cohort_site_fail: 替换失败，替换受试者号不在当前群组中心内，请重新确认。
subject_replace_no_stage_site_fail: 替换失败，替换受试者号不在当前阶段中心内，请重新确认。
subject_replace_random: 替换失败，替换受试者已随机，请重新输入。
subject_replace_sign_out: 替换失败，替换受试者已停用，请重新输入。
subject_replace_register: 替换失败，替换受试者已登记，请重新输入。
subject_no_replace: 你输入的受试者号已完成随机不能替换
subject_no_visit: 请设置完访视周期后在进行登记
subject_number_repeat: 受试者已存在，请重新确认
subject_transfer_fail: 转运失败，新中心受试者号{{.subjectNo}}校验重复，请重新确认。
subject_switch_cohort_fail1: 受试者状态：当前仅支持状态处于随机前的受试者进行群组切换
subject_switch_cohort_fail2: edc对接不能勾选随机前修改推送：当前项目与EDC进行了随机前修改的数据对接，不支持进行群组切换
subject_switch_cohort_fail3: 发药：受试者已产生发药，不支持进行群组切换
subject_switch_cohort_fail4: 表单分层不一致：切换群组与当前群组表单分层配置不同，请确认
subject_random_error: 随机前的发放未完成，不能进行随机
subject_random_number_existence: 该随机号在系统中已存在不可使用
subject_status_no_cancel: 当前受试者状态不能撤销
subject_status_no_delete: 当前受试者状态不能删除
subject_status_no_dispensing: 当前受试者状态不能发放
subject_status_no_join: 当前受试者状态不能操作不参加访视，请重新确认。
subject_status_no_random: 当前受试者状态不能随机
subject_status_no_reissue: 当前受试者状态不能补发
subject_status_no_replace: 当前受试者状态不能替换
subject_status_no_replace_dispensing: 当前受试者状态不能进行研究产品替换
subject_status_no_retrieval: 当前受试者状态不能取回研究产品
subject_status_no_sign_out: 当前受试者状态不能停用
subject_status_no_unblinding: 当前受试者状态不能揭盲
subject_status_no_update: 当前受试者状态不能修改
subject_visit_dispensing: 当前访视已发放
subject_visit_dispensing_no_join: 当前访视已设置不参加发放
subject_visit_dispensing_no_order: 请按访视顺序发放
subject_visit_dispensing_no_order_confrim: 上一个访视订单待确认
subject_visit_dispensing_no_order_dtp: 当前访视配置无法使用中心（直接寄送受试者）/库房（直接寄送受试者），请检查配置
subject_visit_dispensing_store: 当前中心未绑定库房
subject_visit_dispensing_set_no_join: 当前访视不允许设置不参加，请刷新页面
subject_visit_dispensing_no_reissue: "补发失败，无法对当前计划外访视进行补发，请重新选择。"
subject_visit_dispensing_no_reissue_dose: "补发失败，访视判断/剂量水平对应选项已删除，请重新确认。"
subject_visit_dispensing_no_site: "该中心暂未分配库房"
subject_visit_dispensing_order_status: "当前订单状态不允许登记实际使用研究产品"
subject_visit_dispensing_order_status_last: "上一个访视的订单状态未配送，当前访视无法发放"
subject_visit_cannot_cancel: "当前项目属性不允许进行取回撤销操作"
subject_visit_cannot_replace: "当前研究产品状态不允许进行替换操作"
medicine_approval_err: 当前有未完成解隔离审批的任务，请先联系审批人员完成审批后再申请解隔离。
medicine_release_err: 至少选择一项或输入数量
upload_medicines_cell: 请删除Excel最后的空行数据
upload_medicines_drugName: 研究产品名称不匹配
upload_medicines_info: 请勿上传空模板数据
upload_translate_info: 请勿上传空模板数据
random_type_error: 随机表随机类型与系统配置不一致
random_group_error: 随机表组别与系统配置不一致
random_factor_error: 随机表分层因素/选项与系统配置不一致
medicine_delete_error: 研究产品已被订单使用，不能删除
factor_calc_not_match: 计算结果无法匹配到对应的分层，请重新确认。
factor_not_match: 无法匹配到对应分层，请重新确认
subject_visit_err: 模板不适配，当前日期已超过最小窗口期日期，请重新确认。
subject.register.enrollment.full: 登记失败，当前状态为入组已满。
subject.dispensing.enrollment.full: 发放失败，入组已满，请联系管理员确认。
subject.register.screen.fail.enrollment.full: 筛选失败，当前状态为入组已满。
user:
    accept: 您邀请的用户{{.userName}}，邮箱:{{.email}}已接受邀请
    createDate: 创建时间
    customer:
        authorization:
            success: 授权成功
        bind:
            error: 客户组无该用户，请联系管理员添加用户
            error1: 客户组无该用户
            success: 绑定成功
    depot: 库房
    email: 邮箱
    exist:
        env: 环境下已存在此用户
    invite: <p> 您已获邀加入Clinflash参与随机试验研究，<a href={{.url}}>系统链接</a>，您的初始密码为:{{.password}}，</p> <p>{{.url}}</p> <p>若点击链接后未正常打开页面，请手动复制链接粘贴到浏览器地址栏后访问.</p>
    join: <p>{{.customerName}}的{{.userName}}邀请你加入随机实验研究。 <a href={{.url}}>接受邀请</a></p> <p>{{.url}}</p> <p>此链接两天内有效，请及时完成操作。若点击链接后未正常打开页面，请手动复制链接粘贴到浏览器地址栏后访问.</p>
    locking: 您的账号{{.email}}连续5次密码输入错误，账号已锁定请联系管理员或使用忘记密码操作.
    name: 姓名
    no:
        exist: 用户不存在
        exist.customer: 用户不属于当前客户
    notice:
        customer:
            bind:
                title: 「客户授权通知」
        project:
            bind:
                title: 「项目授权通知」
        return:
            login: 返回登录
        title: 项目授权通知
        customer-title: 客户授权通知
    notice_customer: 您的帐户 {{.email}} 已成功授权[{{.customer}}]客户上，请及时登录系统查看。
    notice_project: 您的帐户 {{.email}} 分配到新的临床试验项目[{{.project}}]，请及时登录系统查看。
    password:
        error: 密码错误
    phone:
        exist: 该手机号已绑定用户
    resend:
        email:
            info: 用户状态已激活，无需发送激活邮件
    resend_invite: Clinflash Cloud已为您创建账号，请在7天内访问此链接 {{.link}} 激活您的账号。如非您本人操作，请忽略！
    resend_invite_title: Clinflash Cloud - 激活账号
    reset: <p>您的密码已经被管理员重置，新密码为:{{.password}}.<a href={{.url}}>系统链接</a></p> <p>{{.url}}</p> <p>若点击链接后未正常打开页面，请手动复制链接粘贴到浏览器地址栏后访问.</p>
    retrieve: <p>您申请通过邮箱重置密码，如非本人操作请忽略.<a href={{.url}}>重置密码</a></p> <p>{{.url}}</p> <p>此链接7天内有效，请及时完成操作。若点击链接后未正常打开页面，请手动复制链接粘贴到浏览器地址栏后访问.</p>
    roles: 角色
    site: 中心
    status: 状态
    status.activited: 已激活
    status.not.active: 未激活
    status.enable: 已启用
    status.unauthorized: 未授权
    status.open: 已开启
    status.disable: 已禁用
users:
    authentication:
        failed: 认证失败，请重新登录
    duplicated:
        customer: 用户已存在
        emails: 用户邮箱重复
        cloud-customer: Cloud中用户已禁用，无法添加。
        cloud-disable: Cloud已禁用
        cloud-delete: 用户已删除，请联系Cloud Admin确认。
        cloud-exist: 邮箱已存在
    identify:
        code:
            incorrect: 验证码不正确
    missing:
        phone: 手机号不完整，请引导用户登录cloud后在个人信息中修改
    phone:
        not:
            exist: 账号不存在或无权限，请联系项目管理员添加
user_no_permission: 账号无操作权限，请联系项目管理员添加
visit_cycle_duplicated_number: 访视编号重复
visit_cycle_dtp_required: 请选择DTP方式
visit_cycle_duplicated_random1: 已存在随机的访视，请勿重复配置
visit_cycle_duplicated_random3: 已存在随机、发放的访视，请勿修改配置
visit_cycle_duplicated_version: 访视版本号重复
visit_cycle_formula_error: 公式无法识别，请重新确认。
visit_cycle_formula_visit_error: 已开启随机访视体重比较条件，请配置随机访视允许发放。
visit_cycle_visit_formula_error: 已开启随机访视体重为本次计算体重，请配置随机访视允许发放。
visit_cycle_formula_customer_error: 公式无法识别，请重新输入
visit_cycle_duplicated_random2: 再随机类型的项目，最多只能配置2条随机的访视
system_suggest_value: 系统建议值
work:
    task:
        error: 任务已完成，不可重复操作
        deleted: 该任务已不存在，请刷新重试
        scan:
            error: 该任务不需要进行扫码确认操作
        packageScan:
            error: 该任务不需要进行包装扫码操作
        package_number:
            error: 扫码失败，扫码产品非当前项目/环境中系统生成的研究产品
        exist: 单次操作未完成，不可重复操作
        medicine:
            error: 系统检测扫描列表已更新，请刷新重试
medicine_other_repeat: 添加重复，请重新添加
medicine_drug_configure_check: 删除失败，研究产品已使用或已产生库存。
medicine_drug_configure_other_check2: 修改失败，“
medicine_drug_configure_other_check: ”已有库存数
medicine_drug_configure_other_check1: ，无法重新自动准确计算库存，请重新确认数据后再操作。
medicine_other_repeat_formula: 公式不唯一，添加重复，请重新添加
medicine_open_setting_repeat1: 已配置
medicine_open_setting_openTrue: 开放属性
medicine_open_setting_openFlase: 不开放属性
medicine_open_setting_repeat2: ，需保持一致，请重新配置。
medicine_open_setting_repeat3: 发放方式与已有配置不一致，请重新确认。
medicine_open_setting_approval: 保存失败，不允许仅添加单一盲态研究产品控制。
site_not_delete: 已登记、随机/发放的中心，不可设为无效
order_status_error: 订单状态异常，请返回列表重新操作
subject_status_error: 受试者状态异常，请返回列表重新操作
approval_task_error: 订单审批任务状态异常，请返回列表重新操作
urgentUnblinding_approval_task_error: 紧急揭盲审批任务状态异常，请返回列表重新操作
wms:
    cancel_order_fail: 佰诚库房订单取消失败
unblinding_code_error: 揭盲码错误
unblinding_password_error: 密码错误
common_configuration_error: 配置异常
subject_urgentUnblindingApproval_reason_other: 其他
subject_urgentUnblindingApproval_reason_sae: SAE
subject_urgentUnblindingApproval_reason_pregnancy: 妊娠
subject_urgentUnblindingApproval_reason_policy: 政策要求
subject_urgentUnblindingApproval_agree: 已通过
subject_urgentUnblindingApproval_reject: 已拒绝
subject_urgentUnblindingApproval_applicationed: 该受试者揭盲(紧急)申请已提交，请等待审批人审批确认。
subject_urgentUnblindingApproval_pv_applicationed: 该受试者揭盲(pv)申请已提交，请等待审批人审批确认。
subject_urgentUnblindingApproval_ip_applicationed: 该受试者揭盲(ip)申请已提交，请等待审批人审批确认。
supply_plan_duplicated_name: 名称重复，请重新输入
edc_push_subject_number: 受试者号
edc_push_randomization_number: 随机号
edc_push_group: 组别
edc_push_randomization_time: 随机时间
edc_push_visit_number: 访视号
edc_push_dispense: 发放
edc_push_dispense_time: 发放时间
edc_push_drug: 药物
edc_push_drug_level: 剂量水平
edc_push_drug_label: 标签
edc_push_cohort: 群组/阶段
edc_push_edc_return: EDC返回

edc_push_error_code_200: 成功
edc_push_error_code_201: 时间戳、签名和基础的参数错误，请联系IRT工程师确认。
edc_push_error_code_202: 项目编号错误，请联系IRT配置管理员确认。
edc_push_error_code_203: 环境编号错误，请联系IRT配置管理员确认。
edc_push_error_code_204: 中心编号错误，请联系IRT配置管理员确认。
edc_push_error_code_205: 受试者重复，请联系EDC配置管理员人工处理。
edc_push_error_code_206: 受试者号前缀规则不一致，请联系EDC和IRT配置管理员确认。
edc_push_error_code_207: 修改失败，目标受试者号已存在，请重新确认。
edc_push_error_code_208: 受试者创建中，请稍后操作。
edc_push_error_code_209: 创建失败，EDC中心版本未推送，请联系EDC配置管理员确认。
edc_push_error_code_210: 创建失败，姓名缩写缺失，请重新确认。
edc_push_error_code_211: 创建失败，请联系EDC工程师确认。
edc_push_error_code_212: EDC数据保存时解析出错，请联系EDC工程师确认。
edc_push_error_code_213: EDC数据请求解析错误，请联系EDC工程师确认。
edc_push_error_code_299: 其他未定义错误，请联系EDC工程师确认。

project_dynamics_scene_personnel: 人员
project_dynamics_scene_order: 订单
project_dynamics_scene_unblinding: 揭盲
project_dynamics_scene_forecast: 库存
project_dynamics_scene_visit: 访视
project_dynamics_type_enter_site: 进入中心
project_dynamics_type_bind_storehouse: 绑定库房
project_dynamics_type_role_assignment: 角色分配
project_dynamics_type_overtime: 超时
project_dynamics_type_emergency_unblinding: 紧急揭盲
project_dynamics_type_emergency_unblinding_pv: pv揭盲
project_dynamics_type_alert_storehouse: 仓库警戒
project_dynamics_type_forecast: 库存预测时间
project_dynamics_type_visit: 访视超窗
project_dynamics_content_enter_site: 【{{.siteName}}】<a>{{.email}}</a>已进入中心
project_dynamics_content_bind_storehouse: 【{{.storehouseName}}】<a>{{.email}}</a>已绑定库房
project_dynamics_content_role_assignment: 【角色分配】<a>{{.email}}</a>已分配{{.roles}}角色
project_dynamics_content_overtime: 【订单超时】<a>{{.orderNumber}}</a>已超时
project_dynamics_content_emergency_unblinding: 【{{.siteName}}】<a>{{.subjectName}}</a>已揭盲
project_dynamics_content_emergency_unblinding_emergency: 【{{.siteName}}】<a>{{.subjectName}}</a>已揭盲(紧急)
project_dynamics_content_emergency_unblinding_pv: 【{{.siteName}}】<a>{{.subjectName}}</a>已揭盲(PV)
project_dynamics_content_alert_storehouse: 【<a>{{.storehouseName}}</a>】仓库研究产品已达警戒值
project_dynamics_content_forecast: 【<a>{{.siteName}}</a>】等库存使用时间预测提醒
project_dynamics_content_visit: 【{{.siteName}}】【{{.subject}}】{{.visit}}已超窗。
barcode_error: 保存失败，请先关闭包装号功能
barcode_rule: 研究产品条形码生成规则需保持一致，请确认
barcode_package_rule: 包装条形码生成规则需保持一致，请确认
form_field_used: 字段已使用，无法删除
planned_case_error: 当前随机数已超过项目入组上限，请及时联系系统配置人员调整后再操作随机。
planned_case_random_error: 随机失败，项目已达入组上限。
planned_case_register_error: 登记失败，项目已达登记上限。
planned_case_screen_error: 筛选失败，项目已达筛选成功上限。
plan_number: 计划随机数
subject_replace_auto_error: 替换失败，替换受试者随机号与当前激活随机表冲突，请修改规则后操作。
random_list_upload_error: 分层因素不一致，请同步后再操作。
subject_random_factor_error: 随机失败，请更新最新分层结果后再操作。
simulate_random_factor_error: 系统检测到不同随机表因素不同，无法同时模拟随机，请修改一致后操作。
simulate_random_factor_ratio_error: 模拟随机分层配置与实际分层配置不同，请编辑后再运行。
simulate_random_factor_ratio_list_error: 系统检测到随机配置与随机表不一致，请修改一致后操作。
simulate_random_factor_ratio_total_error: 分层例数和与随机人数不符，请调整。
simulate_random_factor_ratio_lack_error: 分层例数缺失，请补充。
subject_random_sequence_number_start_max_error: 顺序号起始值超过位数上限，请检查随机号配置。
edc_register_error: 登记失败，草稿态不允许登记，请联系IRT项目管理员，修改群组状态至“入组”后操作。
factor_calc_mapping_converge: 映射关系重合，请重新确认。
randomization_config_factor_not_calc_form: 自定义公式变量ID对应的表单字段缺失，请检查配置。
randomization_config_factor_not_calc: 变量ID配置计算冲突，请重新确认。
randomization_config_factor_not_calc_type: 变量ID配置控件冲突，请重新确认。
mail_security: 此电子邮件包含安全链接，请不要与他人分享此电子邮件、链接或访问代码。
mail_noreply: 本电子邮件由系统自动发送，请勿直接回复。
mail_copyright: Copyright ©2020 易迪希医药科技（嘉兴）有限公司 版权所有
mail_upper_half: '<!DOCTYPE html>
    <html lang="en">
    <head>
    <meta charset="utf-8"/>
    <title>Clinflash IRT</title>
    <style>
    .text-body {
    font-size: 14px;
    color: dimgrey;
    }
    </style>
    </head>
    <body>
    <table align="center" border="0" cellspacing="0" cellpadding="0" width="600"
    style="font-size: 14px; background-color: #fff; table-layout: fixed; border-top: #0A47ED solid 6px;">
    <tbody>
    <tr>
    <td colspan="12" style="padding: 40px 0 40px 50px;">
    <img style="width: 238px; height: 40px;"
    src="{{.irt_url}}//api/img/mail"/>
    </td>
    </tr>
    <tr>
    <td colspan="12" style="padding: 5px 50px;">
    <div class="text-body">'
mail_lower_half: ' </div>
    </td>
    </tr>
    </tbody>
    <tfoot>
    <tr>
    <td colspan="12"
    style="background-color: #F8F8F9; padding: 16px 50px; font-size: 10px; line-height: 20px; color: #A4A9B3">
    <div>此电子邮件包含安全链接，请不要与他人分享此电子邮件、链接或访问代码。</div>
    <div>本电子邮件由系统自动发送，请勿直接回复。</div>
    <div>Copyright ©2020 易迪希医药科技（嘉兴）有限公司 版权所有</div>
    </td>
    </tr>
    </tfoot>
    </table>
    </body>'
mail_lower_half_zh_en: ' </div>
    </td>
    </tr>
    </tbody>
    <tfoot>
    <tr>
    <td colspan="12"
    style="background-color: #F8F8F9; padding: 16px 50px; font-size: 10px; line-height: 20px; color: #A4A9B3">
    <div>此电子邮件包含安全链接，请不要与他人分享此电子邮件、链接或访问代码。</div>
    <div>本电子邮件由系统自动发送，请勿直接回复。</div>
    <div>Copyright ©2020 易迪希医药科技（嘉兴）有限公司 版权所有</div>
    </td>
    </tr>
    <tr>
    <td colspan="12"
    style="background-color: #F8F8F9; padding: 16px 50px; font-size: 10px; line-height: 20px; color: #A4A9B3">
    <div>This email contains a secure link. Please do not share this email, link, or access code with others.</div>
    <div>This email is sent automatically by the system, please do not reply directly.</div>
    <div>Copyright ©2020 Clinflash Healthcare Technology (Jiaxing) Co.,Ltd. All Rights Reserved</div>
    </td>
    </tr>
    </tfoot>
    </table>
    </body>'
operation_log:
    label:
        medicine: 研究产品名称
        site: 中心编号
        dept: 库房名称
        supply: 供应计划名称
        group: 组别代码
        factor: 分层因素
        list: 随机列表名称
        invalidList: 作废随机表
        visitCycle: 访视名称
        drugConfigure: 研究产品配置
        packageConfigure: 包装配置
        medicinesList: 研究产品列表
        updateBatch: 批次管理
        otherMedicines: 未编码研究产品
        simulate_random_name: 名称
        name: 字段名称
        attribute: 属性配置
        uploadMedicine: 上传研究产品
        barcode_add: 生成条形码
        uploadPacklist: 上传包装清单
        deleteMedicine: 批量删除研究产品编号
        projectUser: 人员
        project: 项目设置
        barcode_label: 标签编号
    module:
        barcode: 项目构建-编码配置
        supply: 项目构建-供应计划
        supply_detail: 项目构建-供应计划-供应计划详情
        random_design: 项目构建-随机配置-随机设计
        project_site: 项目构建-中心管理
        project_storehouse: 项目构建-库房管理
        project_storehouse_medicine_alert: 项目构建-库房管理-研究产品警戒
        visitCycle: 项目构建-研究产品管理-访视管理
        visitSetting: 项目构建-研究产品管理-访视管理-设置
        attribute: 项目构建-属性配置
        drug_configure: 项目构建-研究产品管理-研究产品配置
        drug_configure_setting: 项目构建-研究产品管理-研究产品配置-设置
        package_configure: 项目构建-研究产品列表-设置
        examine: 项目构建-研究产品列表-审核
        update: 项目构建-研究产品列表-修改
        release: 项目构建-研究产品列表-放行
        barcode_add: 项目构建-研究产品管理-条形码列表-生成条形码
        barcode_label: 项目构建-研究产品管理-标签管理
        medicinesList_uploadMedicine: 项目构建-研究产品管理-研究产品列表-上传
        medicinesList_uploadPacklist: 项目构建-研究产品管理-研究产品列表-上传包装清单
        medicinesList_delete: 项目构建-研究产品管理-研究产品列表-批量删除
        updateBatch: 项目构建-研究产品管理-批次管理
        otherMedicines: 项目构建-研究产品管理-未编码研究产品
        form: 项目构建-随机配置-表单配置
        region: 项目构建-随机配置-区域配置
        simulate_random: 项目构建-模拟随机
        push: 项目构建-推送统计-发送
        projectUser: 其它设置-人员管理
        user: 设置-用户
        projectUser_role: 其它设置-人员管理-角色
        projectUser_site: 其它设置-人员管理-中心
        projectUser_depot: 其它设置-人员管理-仓库
        project_information: 项目设置-基本信息
        project_env: 项目设置-项目环境
        project_function: 项目设置-业务功能
        project_docking: 项目设置-外部对接
        project_custom: 项目设置-自定义流程
        project_permission: 项目设置-项目权限
        notifications: 其他设置-通知设置
        configure_export: 其他设置-配置导出
        project_basic_information: 项目设置-基本信息
        subjects: 受试者管理
        project_notice: 项目通知
        project_multi_language: 多语言
        project_multi_language_translate: 多语言-
        project_multi_language_batch_upload: 多语言-
    add: 新增
    edit: 编辑
    delete: 删除
    copy: 复制
    run: 运行
    unbind: 解绑
    close: 关闭
    setting: 设置
    cancel: 取消
    reauthorization: 再次授权
    invite_again: 再次邀请
    export: 导出
    project_copy: 项目复制
    cohort_copy: 复制
    activate: 激活
    inactivate: 失活
    barcode:
        env_name: 环境
        random: 编码规则
        manual: 手动编码上传
        auto: 系统自动编码
    supply:
        env_name: 环境
        name: 计划名称
        status: 计划状态
        status_effective: 有效
        status_invalid: 无效
        site: 计划适用中心
        all_site: 全部中心
        desc: 计划描述
        control: 供应计划控制
        alarm: 中心库存警戒
        supply: 自动供应
        blindMedicine: 盲态研究产品
        openMedicine: 开放研究产品
        forecastStop: 盲态研究产品最低预测自动供应停用
    supply_detail:
        env_name: 环境
        name: 研究产品
        init_supply: 初始发放量
        warning: 中心库存警戒值
        dispensing_alert: 受试者发放警戒值
        buffer: 最大缓冲量
        second_supply: 再供应量
        forecast: 最低预测
        un_distribution_date: 不运送天数
        un_provide_date: 不发放天数
        validity_reminder: 有效期提醒
        auto_supply_size: 自动配药量
        supply_mode_key: 补充方式
        forecastPeriod: 预测窗口期
        supply_mode:
            all_supply: 全研究产品补充
            single: 单研究产品补充
            all_one: 全研究产品补充+1个随机研究产品编号
            single_one: 单研究产品补充+1个随机研究产品编号
    addBarcode:
        env_name: 环境
        medicineName: 研究产品名称
        medicineSpec: 研究产品规格
        storehouse: 库房
        expireDate: 有效期
        batchNumber: 批次号
        count: 研究产品码个数
        prefix: 短码前缀
        barcodeRule: 研究产品条形码规则
        isPackageBarcode: 包装条形码
        packageRule: 包装条形码规则
        rule_order: 顺序
        rule_reverse: 乱序
        openPackageBarcode: 打开
        closePackageBarcode: 关闭
    uploadMedicines:
        env_name: 环境
        onlyID: 表单ID
        medicineName: 研究产品名称
        medicineSpec: 研究产品规格
        storehouse: 库房
        expireDate: 有效期
        batchNumber: 批次号
        count: 数量
        singleCount: 单品数量
        packageCount: 包装数量
        deleteMedicines: 批量删除的研究产品编号
        fileName: 上传文件名
    updateBatch:
        batch: 库存批次号
        expirationDate: 有效期
        status: 选择的研究产品状态
        updateBatch: 更新批次号
        name: 研究产品名称
        updateCount: 更新数量
        updateExpirationDate: 更新有效期
        toBeWarehoused: 待入仓
        available: 可用
        delivered: 已确认
        transit: 已运送
        quarantine: 已隔离
        used: 已使用
        lose: 丢失/作废
        expired: 已过期
        InOrder: 待确认
        stockPending: 待入库
        apply: 已申请
        frozen: 已冻结
        approval: 待审批
        lock: 锁定
        depot: 库房
        group: 组别
        warn: 受试者警戒人数
        capacity: 受试者上限人数
        batchNo: 批次号
    simulateRandom:
        env_name: 环境
        onlyID: 表单ID
        name: 名称
        randomList: 随机列表
        siteQuantity: 中心数
        countryQuantity: 国家数
        regionQuantity: 区域数
        RunQuantity: 运行次数
        SubjectQuantity: 受试者数
        FactorRatio: 分层例数
        run: 模拟随机：运行
    project_storehouse:
        env_name: 环境
        onlyID: 表单ID
        name: 库房
        country: 所属国家地区
        contacts: 联系人
        phone: 手机号
        email: 邮箱
        address: 地址
        connected: 是否对接
        supplier: 物流供应商
        notIncluded: 订单中不包含隔离研究产品
        research_product_name_validity_reminder: 研究产品名称(有效期提醒)
        medicine_name: 研究产品名称
        medicine_alert: 研究产品警戒值
        medicine_info: 研究产品名称/警戒值/有效期提醒
    supplierType:
        shengsheng: 生生物流
        catalent: catalent
        baicheng: 佰诚物流
        eDRUG: eDRUG平台
    projectSiteStatus:
        valid: 有效
        invalid: 无效
    projectSiteActive:
        open: 开启
        close: 关闭
    project_site:
        env_name: 环境
        number: 中心编号
        name: 中心标准名称
        shortName: 中心简称
        country: 国家/地区
        region: 区域
        status: 状态
        active: 自动订单供应
        supplyPlan: 供应计划
        storehouse: 库房名称
        contacts: 联系人
        phone: 联系方式
        email: 邮箱
        address: 地址
        order_trail: 首次研究产品自动订单：开启，订单编号
    attribute:
        env_name: 环境
        random: 随机化
        random_true: 随机
        random_false: 非随机
        isRandomNumber: 随机号展示
        isRandomNumber_true: 展示
        isRandomNumber_false: 不展示
        isRandomSequenceNumber: 随机顺序号展示
        isRandomSequenceNumber_true: 展示
        isRandomSequenceNumber_false: 不展示
        randomSequenceNumberPrefix: 随机顺序号前缀
        randomSequenceNumberDigit: 随机顺序号位数
        randomSequenceNumberStart: 随机顺序号起始数
        dispensing: 发放设计
        dispensing_true: 发放
        dispensing_false: 不发放
        dtpRule: DTP规则
        dtpRule_ip: 研究产品
        dtpRule_visitFlow: 访视流程
        dtpRule_notApplicable: 不适用
        randomControl: 随机控制
        randomControlRule: 随机控制规则
        randomControl1: 所有的分组，有供应后允许随机
        randomControl2: 已分配的分组，有供应后允许随机
        randomControl3: 强制随机到有供应的分组
        randomControl3_info: 至少多少个分组需要有供应
        allowRegisterGroup: 实际使用研究产品组别
        blind: 盲法
        blind_true: 盲态
        blind_false: 开放
        minimize_calc: 最小化随机计算
        minimize_calc_factor: 随机分层
        minimize_calc_actual: 实际分层
        notApplicable: 不适用
        prefix: 受试者前缀
        subject_number_rule: 受试者号录入规则
        subject_number_rule1: 自定义
        subject_number_rule2: 自动递增且在项目中唯一
        subject_number_rule3: 自动递增且在中心中唯一
        screen: 受试者筛选流程
        screen_true: 开启
        screen_false: 关闭
        prefix_number: 受试者号前缀
        overdueVisitApproval: 超窗访视发放审批
        overdueVisitProcess: 超窗访视发放审批流程操作
        overdueVisitSms: 超窗访视发放审批短信
        overdueVisitMainVisit: 主访视
        overdueVisitReDispensation: 补发
        overdueVisitUnscheduled: 计划外发放/独立计划外
        prefix_true: 有
        prefix_false: 无
        sitePrefix: 是否将中心作为前缀
        prefixConnector: 前缀连接符
        otherPrefix: 受试者号的其他前缀
        otherPrefixText: 前缀文本
        subjectReplaceText: 受试者号替换文本
        subjectReplaceTextEn: 受试者号替换文本(英文)
        accuracy: 受试者号位数精确值
        accuracy_le: 小于等于
        accuracy_eq: 等于
        digit: 受试者号位数
        isFreeze: 隔离单品计算规则
        edcDrugConfigLabel: EDC对接研究产品配置标签
        segment: 号段随机
        segmentType: 计算规则
        serialNumber: 序列号
        medicineNumber: 研究产品编号
        segmentLength: 号段随机长度
        unblindingReason: 揭盲原因
        freezeReason: 隔离原因
        unblindingAllowTrue: 允许备注
        unblindingAllowFalse: 不允许备注
        allowReplace: 受试者替换
        allowReplaceOpen: 开启
        allowReplaceUnOpen: 关闭
        replaceRule: 替换受试者随机号
        ReplaceRuleNumber: 替换受试者随机号规则
        blindingRestrictions: 停用非盲受试者
        pvBlindingRestrictions: 包含PV揭盲的受试者
        IPInheritance: 研究产品继承
        RemainingVisit: 剩余访视周期
    push:
        registered: 登记
        update: 修改
        random: 随机
        dispense: 发放
        out_visit_dispensing: 访视外发放
        replace: 研究产品替换
        reissue: 补发
        cancel: 研究产品撤销
        retrieval: 研究产品取回
        realDispensing: 实际用药
        subjectReplace: 受试者替换
        unknown: 未知
    projectUser:
        emailLanguage: 邮件语言
        email: 邮箱
        roles: 角色
        addRoles: 分配角色
        cancelRoles: 取消角色
        unblindingCode: 生成揭盲码
        sites: 中心
        addSites: 分配中心
        cancelSites: 取消中心
        depots: 仓库
        addDepots: 分配仓库
        cancelDepots: 取消仓库
        App: APP帐号
        status: 状态
        status_effective: 有效
        status_invalid: 无效
    project_notice:
        envName: 环境
        notice_rule: 通知规则
        notice_targets: 通知对象
    project:
        sponsor: 申办方
        name: 项目名称
        startTime: 项目周期(开始日期)
        endTime: 项目周期(结束日期)
        plannedCases: 计划病例数
        phone: 联系方式
        descriptions: 备注
        timeZone: 时区
        status: 状态
        progress: 进行中
        finish: 已完成
        close: 已关闭
        pause: 已暂停
        terminate: 已终止
        orderCheck: 订单核查
        orderCheckDay: 自定义周期
        customTime: 自定义时间
        timing: 定时(包含手动核查)
        realTime: 实时
        notApplicable: 不适用
        orderConfirmation: 中心回收订单确认
        deIsolationApproval: 解隔离审批
        administrators: 管理员
        connectEdc: 同步EDC
        pushMode: 数据推送方式
        real: EDC实时请求
        active: IRT全量推送
        pushRules: 推送规则
        subjectNumber: 受试者号
        subjectUID: 受试者UID
        pushScenario: 推送场景
        registerPush: 登记
        updateRandomFrontPush: 受试者修改(随机前)
        updateRandomAfterPush: 受试者修改(随机后)
        randomPush: 随机
        randomBlockPush: 分层校验不一致，进行随机阻断
        formRandomBlockPush: 表单校验不一致，进行随机阻断
        cohortRandomBlockPush: 群组名称校验不一致，进行随机阻断
        stageRandomBlockPush: 阶段名称校验不一致，进行随机阻断
        dispensingPush: 发药
        screenPush: 筛选
        synchronizationMode: 同步方式
        edcUrl: URL
        edcSupplier: EDC供应商
        edcMappingRules: EDC映射
        folderOid: 文件夹OID
        formOid: 表单OID
        fieldOid: 字段OID
        ipNumberOid: 研究产品编号OID
        dispenseTimeOid: 发放时间OID
        randomizationCode: 随机号
        randomizationTime: 随机时间
        group: 组别
        factor: 分层因素值
        cohor: 群组/再随机
        stepBy: 受试者筛选时同步
        timeFull: 受试者随机时同步
        connectLearning: 对接eLearning
        needLearning: 必须完成课程
        needLearningEnv: 环境
        unblindingControl: 揭盲控制
        unblindingSms: 短信
        unblindingProcess: 流程操作
        unblindingCode: 揭盲码

        pvUnblinding: 揭盲(pv)
        pvUnblindingSms: 短信(pv)
        pvUnblindingProcess: 流程操作(pv)

        ipUnblinding: 揭盲(IP)
        ipUnblindingSms: 短信(IP)
        ipUnblindingProcess: 流程操作(IP)

        orderApprovalControl: 研究中心订单审批审批控制
        envName: 环境名称
        envCapacity: 入组上限
        alertThresholds: 状态/上限值/提醒阈值
        envReminderThresholds: 提醒阈值
        newEnvName: 新环境
        lockStatus: 状态
        unlock: 解锁
        locked: 锁定
        roleName: 角色名称
        scope: 分类
        roleStatus: 状态
        roleDescription: 说明
        cancelRolePermissionSelect: 取消勾选
        addRolePermissionSelect: 勾选
        cohort: 群组名称
        stage: 阶段名称
        complete: 完成
        draft: 草稿
        enrollment: 入组
        stop: 停止
        enrollmentFull: 入组已满
        capacity: 入组上限
        reminderThresholds: 提醒阈值
        lastStage: 上一阶段
        cohortStatus: 状态
        week:
            monday: 周一
            tuesday: 周二
            wednesday: 周三
            thursday: 周四
            friday: 周五
            saturday: 周六
            sunday: 周日
    region:
        name: 名称
        env_name: 环境
    random_design:
        env_name: 环境
        factorLabel: 分层因素
        sync: 同步
        inactivate: 同步
        list: 随机列表名称
        type: 随机类型
        block: 区组随机
        min: 最小化随机
        group_name: 组别名称
        group_code: 组别代码
        status: 状态
        status_effective: 有效
        status_invalid: 无效
        factor:
            layer: 地区分层
            list: 入组限制
            number: 字段编号
            calcType: 计算公式
            formula: 自定义公式
            keepDecimal: 保留小数位
            roundingMethod: 保留小数位-取整方式
            roundingMethod_up: 向上取整
            roundingMethod_down: 向下取整
            calcType_age: 年龄
            calcType_bmi: BMI
            inputLabel: 录入字段名称
            inputWeightLabel: 录入体重字段名称
            inputHeightLabel: 录入身高字段名称
            name: 系统字段
            label: 名称
            type: 控件类型
            options: 选项
            options_label_value: 选项标签
            folder_oid: 文件夹OID
            form_oid: 表单OID
            field_oid: 字段OID
            disable: 禁用地区分层
            country: 开启国家分层
            site: 开启中心分层
            region: 开启区域分层
            status: 状态
            precision: 保留小数位
            status_effective: 有效
            status_invalid: 无效
        mapping:
            random:
                folder_oid: 随机号-文件夹OID
                form_oid: 随机号-表单OID
                field_oid: 随机号-字段OID
            group:
                folder_oid: 组别-文件夹OID
                form_oid: 组别-表单OID
                field_oid: 组别-字段OID
            time:
                folder_oid: 随机时间-文件夹OID
                form_oid: 随机时间-表单OID
                field_oid: 随机时间-字段OID
    random_list:
        env_name: 环境
        onlyID: 表单ID
        name: 名称
        site: 中心
        initial_number: 初始编号
        end_number: 终止编号
        block_rule: 区组规则
        random_number_rule: 随机号规则
        block_rule_order: 顺序
        block_rule_reverse: 乱序
        random_number_rule_order: 顺序
        random_number_rule_reverse: 乱序
        weight_ratio: 组别配置(权重比)
        block_configuration: 区组配置
        factor_ratio: 分层因素(权重比)
        number_length: 号码长度
        seed: 随机种子
        prefix: 号码前缀
        size: 可接受的区组大小（多个区组用"，"隔开）
        status: 状态
        disable: 禁用
        enable: 启用
        invalid: 作废
        isRandom: 中心没有分配随机号不能入组
        isCountryRandom: 国家没有分配随机号不能入组
        isRegionRandom: 区域没有分配随机号不能入组
        set_site: 区组分配给中心
        set_region: 区组分配给区域
        set_country: 区组分配给国家
        set_factor: 区组分配给分层
        clean_factor: 清空分层
        set_count: 设置人数
        file_name: 上传
        factor: 分层因素
        estimateNumber: 预计人数
        warnNumber: 警戒人数
        block: 区组
        randomNumber: 随机号
        randomNumberStatus: 状态
    visitCycle:
        env_name: 环境
        type: 访视日期类型
        baseCohort: baseline基准
        sort: 排序
        number: 访视编号
        name: 访视名称
        random: 是否随机
        dispensing: 是否发放
        replace: 允许受试者替换
        doseAdjustment: 剂量调整
        startDays: 起始天数
        endDays: 结束天数
        folder_oid: 文件夹OID
        form_oid: 表单OID
        dispensing_ip_oid: 发放研究产品OID
        dispensing_time_oid: 发放时间OID
        interval: 间隔时长
        period: 窗口期
        group: 组别
        baseline: baseline
        lastdate: Lastdate
        sop: 访视流程
        version: 访视版本号
        push: 预览并发布
        label: 按标签发放
        open_setting: 开放配置
        formula: 公式计算
        dtp: 是否DTP
        DTPMode: DTP方式
        send-type-0: 中心（中心库存）
        send-type-1: 中心（直接寄送受试者）
        send-type-2: 库房（直接寄送受试者）
    visitSetting:
        env_name: 环境
        unscheduled_visit: 计划外访视
        name_zh: 中文名称
        name_en: 英文名称
    drug_configure:
        env_name: 环境
        onlyID: 表单ID
        group: 组别
        preGroup: 主组别
        subGroup: 子组别
        open: 发放方式
        formula: 按公式计算
        spec: 规格
        calculationType: 公式
        age: 年龄
        weight: 体重
        bsa: 简易体表面积BSA
        otherBsa: 其他体表面积BSA
        drugValue: 研究产品名称/发放数量/规格
        dispensingNumber: 发放数量
        visitCycles: 访视名称
        label: (组合)发放标签
        drugLabel: 发放标签
        room: 房间号
        isDispense: 不发放天数
        notDispenseConfig: 研究产品名称-不发放天数
        isOpenPackage: 按包装运输
        isOpenApplication: 研究中心订单申请
        supplyRatio: 供应比例
        orderApplictionConfig: 研究产品名称-比例
        packageConfig: 研究产品名称-比例，混合包装
        packageConfigNew: 研究产品名称-比例，混合包装
        drugNameSpec: 研究产品名称/规格
        visitDrugNameDispeningNumber: 访视名称/研究产品名称/发放数量
        weightDispeningNumber: 体重范围/发放数量
        ageDispeningNumber: 年龄范围/发放数量
        specifications: 单位容量
        standard: 单位计算标准
        comparisonSwitch: 体重比较计算
        comparisonType: 比较条件
        comparisonRatio: 变化
        currentComparisonType: 本次计算体重
        otherCheck: 未编号研究产品
        openCheck: 开放研究产品
        keepDecimal: 保留小数位
        precision: 保留位数
        automatic_recode: 自动赋值
        automatic_recode_spec: 计算单位
        check: 勾选
        uncheck: 未勾选
        calculationOpen: 开启
        calculationUnOpen: 未开启
        weightCalculation: 上次访视计算体重
        weightActual: 上次访视实际体重
        weightRandom: 随机访视体重
        customerCalculation: 自定义公式
    drug_configure_setting:
        env_name: 环境
        dtp_ipType: 研究产品/发放方式
        dtp_ipType_site: 中心（中心库存）
        dtp_ipType_siteSubject: 中心（直接寄送受试者）
        dtp_ipType_depotSubject: 库房（直接寄送受试者）
        doseAdjustment: 剂量管理
        selectType: 剂量选择
        doseForm: 剂量表单
        isFirstInitial: 首次访视启用初始剂量
        isDoseReduction: 允许受试者剂量下调
        frequency: 次数
        doseLevel: 剂量水平
        doseLevelID: ID
        doseLevelName: 名称
        doseLevelGroup: 组别
        doseLevelDoseDistribution: 发放剂量
        doseLevelInitialDose: 初始剂量
        visitJudgment: 访视判断
        visitJudgmentID: ID
        visitJudgmentName: 名称/值
        visitJudgmentGroup: 组别
        visitJudgmentDoseDistribution: 发放剂量
        visitJudgmentVisitInheritance: 后续访视继承
        visitJudgmentVisitInheritanceCount: 后续访视继承次数
        InheritanceGroup: 后续访视继承组别
        InheritanceRule: 后续访视继承规则
        groupRuleMain: 主访视
        groupRuleStop: 停止发放
    drug_examine:
        nameDateNumberCount: 研究产品名称/有效期/批次号/数量
        examineConfig: 审核确认
        examineNotes: 备注
    drug_update:
        nameDateNumberCount: 研究产品名称/有效期/批次号/数量

    form:
        env_name: 环境
        name: 字段名称
        editable: 可修改
        required: 必填
        type: 控件类型
        format: 格式类型
        options: 选项
        input: 输入框
        inputNumber: 数字输入框
        textArea: 多行文本框
        select: 下拉框
        checkbox: 复选框
        radio: 单选框
        switch: 开关
        datePicker: 日期选择框
        timePicker: 时间选择框
        length: 长度
        max: 最大值
        min: 最小值
        variableFormat: 变量格式
        status: 状态
        status_effective: 有效
        status_invalid: 无效
        applicationType: 应用类型
        applicationTypeRegister: 受试者登记
        applicationTypeFormula: 公式计算
        applicationTypeDoseAdjustment: 剂量调整
        applicationTypeFactorCalc: 分层计算
        variable: 变量ID
        currentTime: 当前时间
    project_multi_language:
        projectName: 项目
        language: 语言
        status: 启用状态
        sharedSystemLibrary: 共享系统库
    project_multi_language_translate:
        projectName: 项目
        languageLibrary: 语言库
        pagePath: 页面路径
        envName: 环境
        cohortName: 群组
        stageName: 阶段
        type: 类型
        key: Key
        name: 名称
        label: label
    project_multi_language_batch_upload:
        projectName: 项目
        filename: 批量导入文件名
    barcode_label:
        correlation_name: 关联条形码任务
        send_success: 标签管理：发送
        code_method: 编码方式
        product_count: 打印数量
        template_size: 打印纸张大小
        label_size: 标签大小
        base:
            Component: 基础组件
            text: 文本
            position: 位置
            font: 字体
            background: 背景
            height: 高度
            marginTop: 上边距
            marginLeft: 左边距
report:
    template:
        name_exist: 模版名称已存在
        name_invalid: 模板名称有误

source_ip_upload_history_name: 盲底名称
source_ip_upload_history_rows: 行数
source_ip_upload_history_rows_succeeded: 成功
source_ip_upload_history_rows_failed: 失败

app_scan_package_notification_title: 包装扫码通知
app_scan_package_notification_content: 包装扫码
app_scan_notification_title: 扫码入仓通知
app_scan_notification_content: 扫码入仓
app_shipment_confirmed_notification_title: 订单待确认通知
app_shipment_confirmed_notification_content: 订单待确认
app_shipment_received_notification_title: 订单待接收通知
app_shipment_received_notification_content: 订单待接收
app_recovery_shipment_confirmed_notification_title: 回收订单待确认通知
app_recovery_shipment_confirmed_notification_content: 回收订单待确认
app_recovery_shipment_received_notification_title: 回收订单待接收通知
app_recovery_shipment_received_notification_content: 回收订单待接收
app_recovery_shipment_delivered_notification_title: 回收订单待运送通知
app_recovery_shipment_delivered_notification_content: 回收订单待运送
app_dispensation_confirmed_notification_title: 发放确认通知
app_dispensation_confirmed_notification_content: 发放确认
app_re_dispensation_confirmed_notification_title: 补发确认通知
app_re_dispensation_confirmed_notification_content: 补发确认
app_shipment_delivered_notification_title: 订单待运送通知
app_shipment_delivered_notification_content: 订单待运送
app_shipment_delivered_application_title: 研究中心订单申请通知
app_shipment_delivered_application_content: 研究中心订单申请
app_unblinding_urgent_notification_title: 揭盲(紧急)审批通知
app_unblinding_urgent_notification_content: 揭盲(紧急)审批
app_unschedualed_dispensation_confirmation_notification_title: 计划外发放确认通知
app_unschedualed_dispensation_confirmation_notification_content: 计划外发放确认
app_unblinding_pv_notification_title: 揭盲(PV)审批通知
app_unblinding_pv_notification_content: 揭盲(PV)审批
app_ip_dispensation_notification_title: 研究产品发放通知
app_ip_dispensation_notification_content: 研究产品发放
app_site_alert_notification_title: 中心警戒通知
app_site_alert_notification_content: 研究产品已达到警戒值
app_depot_alert_notification_title: 库房警戒通知
app_depot_alert_notification_content: 研究产品已达到警戒值
app_shipment_timeout_notification_title: 订单超时通知
app_shipment_timeout_notification_content_1: 订单
app_shipment_timeout_notification_content_2: 已超时
app_shipment_timeout_notification_content_day: 天
app_shipment_timeout_notification_content_days: 天
app_visit_reminder_title: 访视提醒
app_visit_reminder_content_a: "{{.days}}天后您有访视任务，请记得提前安排好！"
app_visit_reminder_content_b: "明天您有访视任务，请记得安排好！"
app_visit_reminder_content_c: "今天您有访视任务，请记得去检查！"
app_visit_reminder_content_d: "今天是不是忘记访视安排？进来看一下呢！"
project_at_random_error: 再随机项目仅支持添加两个阶段

medicine_package_err: 修改失败，包装研究产品名称与设置不匹配，请重新确认
