<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clinflash</title>
    <style>
        /* 字体 */
        /* 微软雅黑 */
        @font-face {
            font-family: MicrosoftYaHei;
            font-weight: normal;
            src: url('./assets/fonts/Microsoft-YaHei.ttf') format('truetype');
        }
        /* 微软雅黑粗体 */
        @font-face {
            font-family: MicrosoftYaHei;
            font-weight: bold;
            src: url('./assets/fonts/Microsoft-YaHei-Bold.ttf') format('truetype');
        }

        /* 清除默认的 HTML 和 body 的 margin，避免影响 WeasyPrint 的渲染 */
        html, body {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: MicrosoftYaHei, sans-serif; /* 设置默认字体 */
            font-size: 12pt; /* 设置默认字号 */
            hyphens: auto; /* 长单词中自动添加连字符，使折断更自然。 */
        }

        /* 设置统一的页面内边距 */
        @page {
            size: A4;
            margin-top:1.3cm;
            margin-bottom: 2.75cm;
            margin-left: 1.41cm;
            margin-right: 1.8cm;
        }

        /* 设置标题样式 */
        h1 {
            font-family: MicrosoftYaHei, sans-serif;
            font-weight: bold; /* 设置标题加粗 */
            font-size: 15pt;
        }

        /* 设置表格样式 */
        table {
            width: 100%;
            border-collapse: collapse; /* 去除单元格之间的间隙 */
            break-inside: avoid;
        }

        th, td {
            border: 1px solid black; /* 设置边框 */
            padding: 5px; /* 设置单元格内边距 */
            text-align: left; /* 内容居左对齐 */
            font-weight: normal; /* 设置表头和单元格字体不加粗 */
        }

        .signature-date {
            display: flex;
            margin-bottom: 20px;
        }
        .signing-date {
            margin-left: 350px; /* 指定签署日期距离页面左侧的距离 */
        }
    </style>
</head>
<body>
<h1>{{.Title}}</h1>
<p>{{.ProjectNameLabel}}: {{.ProjectName}}</p>
<p>{{.SponsorLabel}}: {{.Sponsor}}</p>
<!-- 动态显示群组/阶段信息 -->
{{if .CohortLabel}}
<p>
    {{.CohortLabel}}: {{.Cohort}}
</p>
{{end}}
<div class="signature-date">
    <div>{{.AutographLabel}}: {{.Autograph}}</div>
    <div class="signing-date">{{.SigningDateLabel}}: {{.SigningDate}}</div>
</div>
<table>
    <colgroup>
        <col style="width: 10%;"> <!-- 第一列宽度 -->
        <col style="width: 20%;"> <!-- 第二列宽度 -->
        <col style="width: 20%;"> <!-- 第三列宽度 -->
        <col style="width: 50%;"> <!-- 第四列宽度 -->
    </colgroup>
    <thead>
    <tr>
        <th>{{.SerialNumberLabel}}</th>
        <th>{{.OperatorLabel}}</th>
        <th>{{.OperationTimeLabel}}</th>
        <th>{{.OperationContentLabel}}</th>
    </tr>
    </thead>
    <tbody>
    {{range .TrackList}}
    <tr>
        <td>{{.SerialNumber}}</td>
        <td>{{.Operator}}</td>
        <td>{{.OperationTime}}</td>
        <td>{{.OperationContent}}</td>
    </tr>
    {{end}}
    </tbody>
</table>
</body>
</html>