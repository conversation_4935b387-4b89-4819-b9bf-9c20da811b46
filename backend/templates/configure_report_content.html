<!doctype html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, sbrink-to-fit=no">
    <link rel="stylesheet" bref="https://cdn.bootcss.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
    <title>Clinflash</title>
    <style>
        .text-body {
            font-size: 20px;
            line-height: 40px;
        }
        table {
            border-collapse: collapse;
            border: 1px solid #000; /* 这里假设你想把边框颜色设置为黑色 */
        }

        table, th, td {
            border: 1px solid #000; /* 同样设置单元格的边框颜色为黑色 */
            text-align: left;
        }
    </style>
    <style>
        .flex-column {
            display: flex;
            flex-direction: column;
            gap: 2px; /* 列表项之间的间距 */
        }
        .list-item {
            border-bottom: 1px solid; /* 模仿分割线 */
            padding: 5px;
        }
        .list-item:last-child {
            border-bottom: none; /* 移除最后一个列表项的下边框 */
        }

        .list-it {
            border-right: 1px solid;
            padding: 5px;
        }

        .list-it:last-child {
            border-right: none; /* 移除最后一个列表项的下边框 */
        }
    </style>
</head>
<body>

<div class="container-fluid text-body">
    <div style="text-align: center;font-size: 32px;font-weight: bold">
        <br/><br/><br/>
        <span>{{.Sponsor}}</span>
        <br/><br/><br/>
        <span>{{.ProjectNumber}}</span>
        <!-- 注意下面这些字段在您的原始问题中未在数据结构中初始化 -->
        <!-- 确保在数据结构中添加它们并赋予相应的值 -->
        <br/><br/><br/>
        <span>{{.ConfigureReport}}</span>
        <br/><br/><br/>
        <span>{{.Env}}</span>
        <br/><br/><br/>
        <span>{{.GenerationTime}}</span><span>{{.CreateDate}}</span>
        <br/><br/><br/>
        <span>{{.Generator}}</span><span>{{.CreateBy}}</span>
    </div>
    <div style="page-break-after:always"></div>
    <div>
        <h2>1 {{.Summary}}</h2>
        <span style="width: 300px; word-wrap: break-word;">
            {{.SummaryDetails}}
        </span>
    </div>
    <div style="page-break-after:always"></div>
    <div>
        <h2>2 {{.BasicInformation}}</h2>
        <table border="1" cellspacing="0" cellpadding="0" style="width: 100%; min-height: 25px; line-height: 25px; text-align: center; border-collapse: collapse;">
            <tr>
                <td >{{.ProjectTimeZoneLabel}}</td>
                <td >
                    <span>{{.ProjectTimeZone}}</span>
                </td>
            </tr>
            <tr>
                <td >{{.ProjectTypeLabel}}</td>
                <td >
                    <span>{{.ProjectType}}</span>
                </td>
            </tr>
            <tr>
                <td >{{.ProjectOrderCheckLabel}}</td>
                <td >
                    <span>{{.ProjectOrderCheck}}</span>
                </td>
            </tr>
            <tr>
                <td >{{.ProjectOrderConfirmationLabel}}</td>
                <td >
                    <span>{{.ProjectOrderConfirmation}}</span>
                </td>
            </tr>
            <tr>
                <td >{{.ProjectDeIsolationApprovalLabel}}</td>
                <td >
                    <span>{{.ProjectDeIsolationApproval}}</span>
                </td>
            </tr>
            <tr>
                <td >{{.ProjectUnblindingControlLabel}}</td>
                <td >
                    <span>{{.ProjectUnblindingControl}}</span>
                </td>
            </tr>
            <tr>
                <td >{{.ProjectOrderApprovalControlLabel}}</td>
                <td >
                    <span>{{.ProjectOrderApprovalControl}}</span>
                </td>
            </tr>
            <tr>
                <td >{{.ProjectNoticeLabel}}</td>
                <td >
                    <span>{{.ProjectNotice}}</span>
                </td>
            </tr>
            <tr>
                <td >{{.ProjectConnectEdcLabel}}</td>
                <td >
                    <span>{{.ProjectConnectEdc}}</span>
                </td>
            </tr>
            <tr>
                <td >{{.ProjectPushModeLabel}}</td>
                <td >
                    <span>{{.ProjectPushMode}}</span>
                </td>
            </tr>
            <tr>
                <td >{{.ProjectSynchronizationModeLabel}}</td>
                <td >
                    <span>{{.ProjectSynchronizationMode}}</span>
                </td>
            </tr>
        </table>
    </div>
    <div>
        {{if .IsGroupStage}}
        <div style="page-break-after:always"></div>
        <div>
            <h2>3 {{.CohortOrStage}}{{.GeneralSituation}}</h2>
            <span style="font-weight: bold">{{.ProjectNumber}} （{{.Env}}）{{.AllOfThem}}{{.CohortOrStage}}：</span>
            <br>
            <table border="1" cellspacing="0" cellpadding="0" style="width: 100%; min-height: 25px; line-height: 25px; text-align: center; border-collapse: collapse;">
                <tr>
                    <th>{{.Name}}</th>
                </tr>
                {{range .CohortNameList}}
                <tr>
                    <td><span>{{.}}</span></td>
                </tr>
                {{end}}
            </table>
        </div>
        {{end}}
    </div>
    <div style="display:block">
        {{range .ConfigureDetailList}}
        <div style="page-break-after:always"></div>
        <h2>{{.Index}} {{.Title}}</h2>
        <h3>{{.Index}}.1 {{.AttributeConfigure.Title}}</h3>
        <h4>{{.Index}}.1.1 {{.AttributeConfigure.SystemConfigure.Title}}</h4>
        <table border="1" cellspacing="0" cellpadding="0" style="width: 100%; min-height: 25px; line-height: 25px; text-align: center; border-collapse: collapse;">
            <tr>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >{{.AttributeConfigure.SystemConfigure.RandomizeLabel}}</td>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >
                    <span>{{.AttributeConfigure.SystemConfigure.Randomize}}</span>
                </td>
            </tr>
            <tr>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >{{.AttributeConfigure.SystemConfigure.DisplayRandomizationIDLabel}}</td>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >
                    <span>{{.AttributeConfigure.SystemConfigure.DisplayRandomizationID}}</span>
                </td>
            </tr>
            <tr>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >{{.AttributeConfigure.SystemConfigure.BlindDesignLabel}}</td>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >
                    <span>{{.AttributeConfigure.SystemConfigure.BlindDesign}}</span>
                </td>
            </tr>
            <tr>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >{{.AttributeConfigure.SystemConfigure.TreatmentDesignLabel}}</td>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >
                    <span>{{.AttributeConfigure.SystemConfigure.TreatmentDesign}}</span>
                </td>
            </tr>
            <tr>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >{{.AttributeConfigure.SystemConfigure.RandomizationSupplyCheckLabel}}</td>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >
                    <span>{{.AttributeConfigure.SystemConfigure.RandomizationSupplyCheck}}</span>
                </td>
            </tr>
            <tr>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >{{.AttributeConfigure.SystemConfigure.SubjectScreeningProcessLabel}}</td>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >
                    <span>{{.AttributeConfigure.SystemConfigure.SubjectScreeningProcess}}</span>
                </td>
            </tr>
        </table>
        <h4>{{.Index}}.1.2 {{.AttributeConfigure.SubjectIDRules.Title}}</h4>
        <table border="1" cellspacing="0" cellpadding="0" style="width: 100%; min-height: 25px; line-height: 25px; text-align: center; border-collapse: collapse;">
            <tr>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >{{.AttributeConfigure.SubjectIDRules.SubjectNumberInputRuleLabel}}</td>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >
                    <span>{{.AttributeConfigure.SubjectIDRules.SubjectNumberInputRule}}</span>
                </td>
            </tr>
            <tr>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >{{.AttributeConfigure.SubjectIDRules.SubjectPrefixLabel}}</td>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >
                    <span>{{.AttributeConfigure.SubjectIDRules.SubjectPrefix}}</span>
                </td>
            </tr>
            <tr>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >{{.AttributeConfigure.SubjectIDRules.SubjectIDPrefixLabel}}</td>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >
                    <span>{{.AttributeConfigure.SubjectIDRules.SubjectIDPrefix}}</span>
                </td>
            </tr>
            <tr>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >{{.AttributeConfigure.SubjectIDRules.ReplacementTextForSubjectIDLabel}}</td>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >
                    <span>{{.AttributeConfigure.SubjectIDRules.ReplacementTextForSubjectID}}</span>
                </td>
            </tr>
            <tr>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >{{.AttributeConfigure.SubjectIDRules.ReplacementTextEnForSubjectIDLabel}}</td>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >
                    <span>{{.AttributeConfigure.SubjectIDRules.ReplacementTextEnForSubjectID}}</span>
                </td>
            </tr>
            <tr>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >{{.AttributeConfigure.SubjectIDRules.SubjectIDDigitLabel}}</td>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >
                    <span>{{.AttributeConfigure.SubjectIDRules.SubjectIDDigit}}</span>
                </td>
            </tr>
            <tr>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >{{.AttributeConfigure.SubjectIDRules.SubjectReplacementLabel}}</td>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >
                    <span>{{.AttributeConfigure.SubjectIDRules.SubjectReplacement}}</span>
                </td>
            </tr>
        </table>
        <span style="width: 300px; word-wrap: break-word;">
                        {{.AttributeConfigure.SubjectIDRules.TakeCare}}
                    </span>
        <h4>{{.Index}}.1.3 {{.AttributeConfigure.OtherRules.Title}}</h4>
        <table border="1" cellspacing="0" cellpadding="0" style="width: 100%; min-height: 25px; line-height: 25px; text-align: center; border-collapse: collapse;">
            <tr>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >{{.AttributeConfigure.OtherRules.StopUnblindedSubjectsLabel}}</td>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >
                    <span>{{.AttributeConfigure.OtherRules.StopUnblindedSubjects}}</span>
                </td>
            </tr>
            <tr>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >{{.AttributeConfigure.OtherRules.QuarantinedIPCountingRuleLabel}}</td>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >
                    <span>{{.AttributeConfigure.OtherRules.QuarantinedIPCountingRule}}</span>
                </td>
            </tr>
            <tr>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >{{.AttributeConfigure.OtherRules.TransportAccordingPackagingLabel}}</td>
                <td style="width: 50%; word-break: break-all; white-space: normal;" >
                    <span>{{.AttributeConfigure.OtherRules.TransportAccordingPackaging}}</span>
                </td>
            </tr>
        </table>
        <div style="width: 100%; word-wrap: break-word;">
            {{.AttributeConfigure.OtherRules.Deactivate}}
        </div>
        <div style="width: 100%; word-wrap: break-word;">
            {{.AttributeConfigure.OtherRules.Quarantine}}
        </div>
        <div style="width: 100%; word-wrap: break-word;">
            {{.AttributeConfigure.OtherRules.Packing}}
        </div>

        {{if ne .RandomConfigure.Level ""}}
        <div style="page-break-after:always"></div>
        <h3>{{.Index}}{{.RandomConfigure.Level}} {{.RandomConfigure.Title}}</h3>
        {{if ne .RandomConfigure.RandomDesign.Level ""}}
        <h4>{{.Index}}{{.RandomConfigure.Level}}{{.RandomConfigure.RandomDesign.Level}} {{.RandomConfigure.RandomDesign.Title}}</h4>
        <table border="1" cellspacing="0" cellpadding="0" style="width: 100%; min-height: 25px; line-height: 25px; text-align: center; border-collapse: collapse;">
            <tr>
                <td style="width: 20%; word-break: break-all; white-space: normal;">{{.RandomConfigure.RandomDesign.RandomTypeLabel}}</td>
                <td colspan="5" style="width: 80%; word-break: break-all; white-space: normal;">
                    <span>{{.RandomConfigure.RandomDesign.RandomType}}</span>
                </td>
            </tr>
            <tr>
                <td style="width: 20%; word-break: break-all; white-space: normal;">{{.RandomConfigure.RandomDesign.GroupLabel}}</td>
                <td colspan="5" style="width: 80%; word-break: break-all; white-space: normal;">
                    <div class="flex-column">
                        <!-- 动态生成或JavaScript填充此处的列表项 -->
                        {{if gt (len .RandomConfigure.RandomDesign.Group) 0}}
                        {{range .RandomConfigure.RandomDesign.Group}}
                        <div class="list-item">
                            {{.}}
                        </div>
                        {{end}}
                        {{else }}
                        <div class="list-item">/</div>
                        {{end}}
                    </div>
                </td>
            </tr>
            <tr>
                <td style="width: 20%; word-break: break-all; white-space: normal;">{{.RandomConfigure.RandomDesign.RegionFactorLabel}}</td>
                <td colspan="5" style="width: 80%; word-break: break-all; white-space: normal;">
                    <span>{{.RandomConfigure.RandomDesign.RegionFactor}}</span>
                </td>
            </tr>
            <tr>
                <td style="width: 20%; word-break: break-all; white-space: normal;" rowspan="{{.RandomConfigure.RandomDesign.Length}}">{{.RandomConfigure.RandomDesign.FactorOptionLabel}}</td>
                <td style="width: 16%; word-break: break-all; white-space: normal;"><span>{{.RandomConfigure.RandomDesign.FieldNumberLabel}}</span></td>
                <td style="width: 16%; word-break: break-all; white-space: normal;"><span>{{.RandomConfigure.RandomDesign.FieldNameLabel}}</span></td>
                <td style="width: 16%; word-break: break-all; white-space: normal;"><span>{{.RandomConfigure.RandomDesign.ControlTypeLabel}}</span></td>
                <td style="width: 16%; word-break: break-all; white-space: normal;"><span>{{.RandomConfigure.RandomDesign.OptionLabel}}</span></td>
                <td style="width: 16%; word-break: break-all; white-space: normal;"><span>{{.RandomConfigure.RandomDesign.StatusLabel}}</span></td>
            </tr>
            {{if gt (len .RandomConfigure.RandomDesign.FactorList) 0}}
            {{range .RandomConfigure.RandomDesign.FactorList}}
            <tr>
                <td style="width: 16%; word-break: break-all; white-space: normal;"><span>{{.FieldNumber}}</span></td>
                <td style="width: 16%; word-break: break-all; white-space: normal;"><span>{{.FieldName}}</span></td>
                <td style="width: 16%; word-break: break-all; white-space: normal;"><span>{{.ControlType}}</span></td>
                <!--                                    <td><span>{{.Option}}</span></td>-->
                <td style="width: 16%; word-break: break-all; white-space: normal;">
                    <div class="flex-column">
                        <!-- 动态生成或JavaScript填充此处的列表项 -->
                        {{if gt (len .Option) 0}}
                        {{range .Option}}
                        <div class="list-item">
                            {{.}}
                        </div>
                        {{end}}
                        {{else }}
                        <div class="list-item"></div>
                        {{end}}
                    </div>
                </td>
                <td style="width: 16%; word-break: break-all; white-space: normal;"><span>{{.Status}}</span></td>
            </tr>
            {{end}}
            {{else }}
            <tr>
                <td style="width: 16%; word-break: break-all; white-space: normal;"><span>/</span></td>
                <td style="width: 16%; word-break: break-all; white-space: normal;"><span>/</span></td>
                <td style="width: 16%; word-break: break-all; white-space: normal;"><span>/</span></td>
                <td style="width: 16%; word-break: break-all; white-space: normal;"><span>/</span></td>
                <td style="width: 16%; word-break: break-all; white-space: normal;"><span>/</span></td>
            </tr>
            {{end}}
        </table>
        {{end}}

        {{if ne .RandomConfigure.StratificationCalculation.Level ""}}
        <h4>{{.Index}}{{.RandomConfigure.Level}}{{.RandomConfigure.StratificationCalculation.Level}} {{.RandomConfigure.StratificationCalculation.Title}}</h4>
        {{if ne .RandomConfigure.StratificationCalculation.Bmi.Level ""}}
        <div style="width: 100%; word-wrap: break-word;">
            <span>{{.RandomConfigure.StratificationCalculation.Bmi.Level}} {{.RandomConfigure.StratificationCalculation.Bmi.ParagraphNameLabel}}</span>
        </div>
        <table border="1" cellspacing="0" cellpadding="0" style="width: 100%; min-height: 25px; line-height: 25px; text-align: center; border-collapse: collapse;">
            <tr>
                <td style="width: 20%; word-break: break-all; white-space: normal;">{{.RandomConfigure.StratificationCalculation.Bmi.FieldNumberLabel}}</td>
                <td style="width: 20%; word-break: break-all; white-space: normal;">{{.RandomConfigure.StratificationCalculation.Bmi.FormulaLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.RandomConfigure.StratificationCalculation.Bmi.WeightNameLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.RandomConfigure.StratificationCalculation.Bmi.HeightNameLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.RandomConfigure.StratificationCalculation.Bmi.ControlTypeLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.RandomConfigure.StratificationCalculation.Bmi.LayeredNameLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.RandomConfigure.StratificationCalculation.Bmi.LayeredOptionLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.RandomConfigure.StratificationCalculation.Bmi.StatusLabel}}</td>
            </tr>
            {{if gt (len .RandomConfigure.StratificationCalculation.Bmi.FieldList) 0}}
            {{range .RandomConfigure.StratificationCalculation.Bmi.FieldList}}
            <tr>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>{{.FieldNumber}}</span>
                </td>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>{{.Formula}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.WeightName}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.HeightName}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.ControlType}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.LayeredName}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.LayeredOption}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.Status}}</span>
                </td>
            </tr>
            {{end}}
            {{else}}
            <tr>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
            </tr>
            {{end}}
        </table>
        <div style="width: 100%; word-wrap: break-word;">
            {{.RandomConfigure.StratificationCalculation.Bmi.TakeCare}}
        </div>
        {{end}}
        {{if ne .RandomConfigure.StratificationCalculation.Age.Level ""}}
        <div style="width: 100%; word-wrap: break-word;">
            <span>{{.RandomConfigure.StratificationCalculation.Age.Level}} {{.RandomConfigure.StratificationCalculation.Age.ParagraphNameLabel}}</span>
        </div>
        <table border="1" cellspacing="0" cellpadding="0" style="width: 100%; min-height: 25px; line-height: 25px; text-align: center; border-collapse: collapse;">
            <tr>
                <td style="width: 20%; word-break: break-all; white-space: normal;">{{.RandomConfigure.StratificationCalculation.Age.FieldNumberLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.RandomConfigure.StratificationCalculation.Age.FormulaLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.RandomConfigure.StratificationCalculation.Age.FieldNameLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.RandomConfigure.StratificationCalculation.Age.RetainDecimalsLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.RandomConfigure.StratificationCalculation.Age.ControlTypeLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.RandomConfigure.StratificationCalculation.Age.FormatTypeLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.RandomConfigure.StratificationCalculation.Age.LayeredNameLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.RandomConfigure.StratificationCalculation.Age.LayeredOptionLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.RandomConfigure.StratificationCalculation.Age.StatusLabel}}</td>
            </tr>
            {{if gt (len .RandomConfigure.StratificationCalculation.Age.FieldList) 0}}
            {{range .RandomConfigure.StratificationCalculation.Age.FieldList}}
            <tr>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>{{.FieldNumber}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.Formula}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.FieldName}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.RetainDecimals}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.ControlType}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.FormatType}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.LayeredName}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.LayeredOption}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.Status}}</span>
                </td>
            </tr>
            {{end}}
            {{else}}
            <tr>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
            </tr>
            {{end}}
        </table>
        <div style="width: 100%; word-wrap: break-word;">
            {{.RandomConfigure.StratificationCalculation.Age.TakeCare}}
        </div>
        {{end}}
        {{end}}
        {{end}}

        {{if ne .FormConfigure.Level ""}}
        <div style="page-break-after:always"></div>
        <h3>{{.Index}}{{.FormConfigure.Level}} {{.FormConfigure.Title}}</h3>
        {{if ne .FormConfigure.SubjectRegistration.Level ""}}
        <div style="width: 100%; word-wrap: break-word;">
            <span>{{.FormConfigure.SubjectRegistration.Level}} {{.FormConfigure.SubjectRegistration.Title}}</span>
        </div>
        <table border="1" cellspacing="0" cellpadding="0" style="width: 100%; min-height: 25px; line-height: 25px; text-align: center; border-collapse: collapse;">
            <tr>
                <td style="width: 20%; word-break: break-all; white-space: normal;">{{.FormConfigure.SubjectRegistration.FieldNameLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.FormConfigure.SubjectRegistration.IsEditableLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.FormConfigure.SubjectRegistration.RequiredLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.FormConfigure.SubjectRegistration.ControlTypeLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.FormConfigure.SubjectRegistration.OptionLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.FormConfigure.SubjectRegistration.FormatTypeLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.FormConfigure.SubjectRegistration.VariableFormatLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.FormConfigure.SubjectRegistration.VariableRangeLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.FormConfigure.SubjectRegistration.StatusLabel}}</td>
            </tr>
            {{if gt (len .FormConfigure.SubjectRegistration.FieldList) 0}}
            {{range .FormConfigure.SubjectRegistration.FieldList}}
            <tr>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>{{.FieldName}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.IsEditable}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.Required}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.ControlType}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.Option}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.FormatType}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.VariableFormat}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.VariableRange}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.Status}}</span>
                </td>
            </tr>
            {{end}}
            {{else}}
            <tr>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
            </tr>
            {{end}}
        </table>
        {{end}}
        {{if ne .FormConfigure.CustomFormula.Level ""}}
        <div style="width: 100%; word-wrap: break-word;">
            <span>{{.FormConfigure.CustomFormula.Level}} {{.FormConfigure.CustomFormula.Title}}</span>
        </div>
        <table border="1" cellspacing="0" cellpadding="0" style="width: 100%; min-height: 25px; line-height: 25px; text-align: center; border-collapse: collapse;">
            <tr>
                <td style="width: 20%; word-break: break-all; white-space: normal;">{{.FormConfigure.CustomFormula.FieldNameLabel}}</td>
                <td style="width: 20%; word-break: break-all; white-space: normal;">{{.FormConfigure.CustomFormula.RequiredLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.FormConfigure.CustomFormula.VariableIdLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.FormConfigure.CustomFormula.ControlTypeLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.FormConfigure.CustomFormula.FormatTypeLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.FormConfigure.CustomFormula.VariableFormatLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.FormConfigure.CustomFormula.VariableRangeLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.FormConfigure.CustomFormula.StatusLabel}}</td>
            </tr>
            {{if gt (len .FormConfigure.CustomFormula.FieldList) 0}}
            {{range .FormConfigure.CustomFormula.FieldList}}
            <tr>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>{{.FieldName}}</span>
                </td>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>{{.Required}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.VariableId}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.ControlType}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.FormatType}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.VariableFormat}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.VariableRange}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.Status}}</span>
                </td>
            </tr>
            {{end}}
            {{else}}
            <tr>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
            </tr>
            {{end}}
        </table>
        {{end}}
        {{if ne .FormConfigure.DoseAdjustment.Level ""}}
        <div style="width: 100%; word-wrap: break-word;">
            <span>{{.FormConfigure.DoseAdjustment.Level}} {{.FormConfigure.DoseAdjustment.Title}}</span>
        </div>
        <table border="1" cellspacing="0" cellpadding="0" style="width: 100%; min-height: 25px; line-height: 25px; text-align: center; border-collapse: collapse;">
            <tr>
                <td style="width: 20%; word-break: break-all; white-space: normal;">{{.FormConfigure.DoseAdjustment.FieldNameLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.FormConfigure.DoseAdjustment.RequiredLabel}}</td>
                <td style="width: 20%; word-break: break-all; white-space: normal;">{{.FormConfigure.DoseAdjustment.VariableIdLabel}}</td>
                <td style="width: 20%; word-break: break-all; white-space: normal;">{{.FormConfigure.DoseAdjustment.ControlTypeLabel}}</td>
                <td style="width: 20%; word-break: break-all; white-space: normal;">{{.FormConfigure.DoseAdjustment.OptionLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.FormConfigure.DoseAdjustment.StatusLabel}}</td>
            </tr>
            {{if gt (len .FormConfigure.DoseAdjustment.FieldList) 0}}
            {{range .FormConfigure.DoseAdjustment.FieldList}}
            <tr>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>{{.FieldName}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.Required}}</span>
                </td>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>{{.VariableId}}</span>
                </td>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>{{.ControlType}}</span>
                </td>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>{{.Option}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.Status}}</span>
                </td>
            </tr>
            {{end}}
            {{else}}
            <tr>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
            </tr>
            {{end}}
        </table>
        {{end}}
        {{end}}

        {{if ne .IpManagement.Level ""}}
        <div style="page-break-after:always"></div>
        <h3>{{.Index}}{{.IpManagement.Level}} {{.IpManagement.Title}}</h3>
        {{if ne .IpManagement.VisitManagement.Level ""}}
        <h4>{{.Index}}{{.IpManagement.Level}}{{.IpManagement.VisitManagement.Level}} {{.IpManagement.VisitManagement.Title}}</h4>
        <table border="1" cellspacing="0" cellpadding="0" style="width: 100%; min-height: 25px; line-height: 25px; text-align: center; border-collapse: collapse;">
            <tr>
                <td colspan="3" style="width: 30%; word-break: break-all; white-space: normal;">{{.IpManagement.VisitManagement.CycleVersionLabel}}</td>
                <td colspan="7" style="width: 70%; word-break: break-all; white-space: normal;">
                    <span>{{.IpManagement.VisitManagement.CycleVersion}}</span>
                </td>
            </tr>
            <tr>
                <td colspan="3" style="width: 30%; word-break: break-all; white-space: normal;">{{.IpManagement.VisitManagement.VisitOffsetTypeLabel}}</td>
                <td colspan="7" style="width: 70%; word-break: break-all; white-space: normal;">
                    <span>{{.IpManagement.VisitManagement.VisitOffsetType}}</span>
                </td>
            </tr>
            <tr>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.VisitManagement.VisitNumberLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.VisitManagement.VisitNameLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.VisitManagement.GroupLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.VisitManagement.IntervalDurationLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.VisitManagement.WindowLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.VisitManagement.IsDispenseLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.VisitManagement.IsRandomizeLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.VisitManagement.IsDTPLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.VisitManagement.IsSubjectReplaceLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.VisitManagement.IsDoseAdjustmentLabel}}</td>
            </tr>
            {{if gt (len .IpManagement.VisitManagement.VisitList) 0}}
            {{range .IpManagement.VisitManagement.VisitList}}
            <tr>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.VisitNumber}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.VisitName}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.Group}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.IntervalDuration}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.Window}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.IsDispense}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.IsRandomize}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.IsDTP}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.IsSubjectReplace}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.IsDoseAdjustment}}</span>
                </td>
            </tr>
            {{end}}
            {{else}}
            <tr>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
            </tr>
            {{end}}
        </table>
        {{end}}

        {{if ne .IpManagement.TreatmentDesign.Level ""}}
        <div style="page-break-after:always"></div>
        <h4>{{.Index}}{{.IpManagement.Level}}{{.IpManagement.TreatmentDesign.Level}} {{.IpManagement.TreatmentDesign.Title}}</h4>
        {{if ne .IpManagement.TreatmentDesign.LabelOpen.Level ""}}
        <div style="width: 100%; word-wrap: break-word;">
            <span>{{.IpManagement.TreatmentDesign.LabelOpen.Level}} {{.IpManagement.TreatmentDesign.LabelOpen.Title}}</span>
        </div>
        <table border="1" cellspacing="0" cellpadding="0" style="width: 100%; min-height: 25px; line-height: 25px; text-align: center; border-collapse: collapse;">
            <tr>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.TreatmentDesign.LabelOpen.GroupLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.TreatmentDesign.LabelOpen.VisitNameLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.TreatmentDesign.LabelOpen.IpNameLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.TreatmentDesign.LabelOpen.DispensationQuantityLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.TreatmentDesign.LabelOpen.CustomFormulaLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.TreatmentDesign.LabelOpen.CombinedDispensationLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.TreatmentDesign.LabelOpen.IpSpecificationLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.TreatmentDesign.LabelOpen.SpecificationLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.TreatmentDesign.LabelOpen.AutomaticAssignmentLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.TreatmentDesign.LabelOpen.CalculationUnitLabel}}</td>
            </tr>
            {{if gt (len .IpManagement.TreatmentDesign.LabelOpen.LabelOpenFieldList) 0}}
            {{range .IpManagement.TreatmentDesign.LabelOpen.LabelOpenFieldList}}
            <tr>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.Group}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.VisitName}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.IpName}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.DispensationQuantity}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.CustomFormula}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.CombinedDispensation}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.IpSpecification}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.Specification}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.AutomaticAssignment}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.CalculationUnit}}</span>
                </td>
            </tr>
            {{end}}
            {{else}}
            <tr>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
            </tr>
            {{end}}
        </table>
        <div style="width: 100%; word-wrap: break-word;">
            <span>{{.IpManagement.TreatmentDesign.LabelOpen.TakeCare}}</span>
        </div>
        {{end}}

        {{if ne .IpManagement.TreatmentDesign.FormulaConfig.Level ""}}
        <!--                <div style="page-break-after:always"></div>-->
        <div style="width: 100%; word-wrap: break-word;">
            <span>{{.IpManagement.TreatmentDesign.FormulaConfig.Level}} {{.IpManagement.TreatmentDesign.FormulaConfig.Title}}</span>
        </div>
        {{if gt (len .IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.FormulaAgeFieldList) 0}}
        <div style="width: 100%; word-wrap: break-word;">
            <span>{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.Title}}</span>
        </div>
        <table border="1" cellspacing="0" cellpadding="0" style="width: 100%; min-height: 25px; line-height: 25px; text-align: center; border-collapse: collapse;">
            <tr>
                <td style="width: 20%; word-break: break-all; white-space: normal;">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.GroupLabel}}</td>
                <td style="width: 20%; word-break: break-all; white-space: normal;">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.VisitNameLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.IpNameLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.IpSpecificationLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.AgeRangeLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.DispensationQuantityLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.IsOpenIpLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.KeepDecimalPlacesLabel}}</td>
            </tr>
            {{if gt (len .IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.FormulaAgeFieldList) 0}}
            {{range .IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.FormulaAgeFieldList}}
            <tr>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>{{.Group}}</span>
                </td>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>{{.VisitName}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.IpName}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.IpSpecification}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <div class="flex-column">
                        <!-- 动态生成或JavaScript填充此处的列表项 -->
                        {{if gt (len .AgeRange) 0}}
                        {{range .AgeRange}}
                        <div class="list-item">
                            {{.}}
                        </div>
                        {{end}}
                        {{else }}
                        <div class="list-item"></div>
                        {{end}}
                    </div>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <div class="flex-column">
                        <!-- 动态生成或JavaScript填充此处的列表项 -->
                        {{if gt (len .DispensationQuantity) 0}}
                        {{range .DispensationQuantity}}
                        <div class="list-item">
                            {{.}}
                        </div>
                        {{end}}
                        {{else }}
                        <div class="list-item"></div>
                        {{end}}
                    </div>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.IsOpenIp}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>{{.KeepDecimalPlaces}}</span>
                </td>
            </tr>
            {{end}}
            {{else}}
            <tr>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 20%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;">
                    <span>/</span>
                </td>
            </tr>
            {{end}}
        </table>
        <div style="width: 100%; word-wrap: break-word;">
            <span>{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.TakeCare}}</span>
        </div>
        {{end}}

        {{if gt (len .IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.FormulaWeightFieldList) 0}}
        <!--                <div style="page-break-after:always"></div>-->
        <div style="width: 100%; word-wrap: break-word;">
            <span>{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.Title}}</span>
        </div>
        <table border="1" cellspacing="0" cellpadding="0" style="width: 100%; min-height: 25px; line-height: 25px; text-align: center; border-collapse: collapse;">
            <tr>
                <td style="width: 10%; word-break: break-all; white-space: normal;" rowspan="2">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.GroupLabel}}</td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" rowspan="2">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.VisitNameLabel}}</td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" rowspan="2">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.IpNameLabel}}</td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" rowspan="2">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.IpSpecificationLabel}}</td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" rowspan="2">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.WeightRangeLabel}}</td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" rowspan="2">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.DispensationQuantityLabel}}</td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" rowspan="2">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.IsOpenIpLabel}}</td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" rowspan="2">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.KeepDecimalPlacesLabel}}</td>
                <td style="width: 27%; word-break: break-all; white-space: normal;" rowspan="1" colspan="3">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.WeightComparisonCalculationLabel}}</td>
            </tr>
            <tr>
                <td style="width: 9%; word-break: break-all; white-space: normal;" rowspan="1">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.ComparedWithLabel}}</td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" rowspan="1">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.ChangeLabel}}</td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" rowspan="1">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.CalculationLabel}}</td>
            </tr>
            {{if gt (len .IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.FormulaWeightFieldList) 0}}
            {{range .IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.FormulaWeightFieldList}}
            <tr>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>{{.Group}}</span>
                </td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" >
                    <span>{{.VisitName}}</span>
                </td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" >
                    <span>{{.IpName}}</span>
                </td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" >
                    <span>{{.IpSpecification}}</span>
                </td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" >
                    <div class="flex-column">
                        <!-- 动态生成或JavaScript填充此处的列表项 -->
                        {{if gt (len .WeightRange) 0}}
                        {{range .WeightRange}}
                        <div class="list-item">
                            {{.}}
                        </div>
                        {{end}}
                        {{else }}
                        <div class="list-item"></div>
                        {{end}}
                    </div>
                </td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" >
                    <div class="flex-column">
                        <!-- 动态生成或JavaScript填充此处的列表项 -->
                        {{if gt (len .DispensationQuantity) 0}}
                        {{range .DispensationQuantity}}
                        <div class="list-item">
                            {{.}}
                        </div>
                        {{end}}
                        {{else }}
                        <div class="list-item"></div>
                        {{end}}
                    </div>
                </td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" >
                    <span>{{.IsOpenIp}}</span>
                </td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" >
                    <span>{{.KeepDecimalPlaces}}</span>
                </td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" >
                    <span>{{.ComparedWith}}</span>
                </td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" >
                    <span>{{.Change}}</span>
                </td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" >
                    <span>{{.Calculation}}</span>
                </td>
            </tr>
            {{end}}
            {{else}}
            <tr>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 9%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
            </tr>
            {{end}}
        </table>
        <div style="width: 100%; word-wrap: break-word;">
            <span>{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.TakeCare}}</span>
        </div>
        {{end}}

        {{if gt (len .IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.FormulaSimpleBSAFieldList) 0}}
        <!--                <div style="page-break-after:always"></div>-->
        <div style="width: 100%; word-wrap: break-word;">
            <span>{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.Title}}</span>
        </div>
        <table border="1" cellspacing="0" cellpadding="0" style="width: 100%; min-height: 25px; line-height: 25px; text-align: center; border-collapse: collapse;">
            <tr>
                <td style="width: 10%; word-break: break-all; white-space: normal;" rowspan="2">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.GroupLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" rowspan="2">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.VisitNameLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" rowspan="2">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.IpNameLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" rowspan="2">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.IpSpecificationLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" rowspan="2">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.UnitCapacityLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" rowspan="2">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.UnitCalculationStandardLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" rowspan="2">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.IsOpenIpLabel}}</td>
                <td style="width: 30%; word-break: break-all; white-space: normal;" rowspan="1" colspan="3">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.WeightComparisonCalculationLabel}}</td>
            </tr>
            <tr>
                <td style="width: 10%; word-break: break-all; white-space: normal;" rowspan="1">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.ComparedWithLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" rowspan="1">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.ChangeLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" rowspan="1">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.CalculationLabel}}</td>
            </tr>
            {{if gt (len .IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.FormulaSimpleBSAFieldList) 0}}
            {{range .IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.FormulaSimpleBSAFieldList}}
            <tr>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>{{.Group}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>{{.VisitName}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>{{.IpName}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>{{.IpSpecification}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>{{.UnitCapacity}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>{{.UnitCalculationStandard}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>{{.IsOpenIp}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>{{.ComparedWith}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>{{.Change}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>{{.Calculation}}</span>
                </td>
            </tr>
            {{end}}
            {{else}}
            <tr>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
            </tr>
            {{end}}
        </table>
        <div style="width: 100%; word-wrap: break-word;">
            <span>{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.TakeCare}}</span>
        </div>
        {{end}}

        {{if gt (len .IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.FormulaOtherBSAFieldList) 0}}
        <!--                <div style="page-break-after:always"></div>-->
        <div style="width: 100%; word-wrap: break-word;">
            <span>{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.Title}}</span>
        </div>
        <table border="1" cellspacing="0" cellpadding="0" style="width: 100%; min-height: 25px; line-height: 25px; text-align: center; border-collapse: collapse;">
            <tr>
                <td style="width: 10%; word-break: break-all; white-space: normal;" rowspan="2">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.GroupLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" rowspan="2">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.VisitNameLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" rowspan="2">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.IpNameLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" rowspan="2">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.IpSpecificationLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" rowspan="2">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.UnitCapacityLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" rowspan="2">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.UnitCalculationStandardLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" rowspan="2">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.IsOpenIpLabel}}</td>
                <td style="width: 30%; word-break: break-all; white-space: normal;" rowspan="1" colspan="3">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.WeightComparisonCalculationLabel}}</td>
            </tr>
            <tr>
                <td style="width: 10%; word-break: break-all; white-space: normal;" rowspan="1">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.ComparedWithLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" rowspan="1">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.ChangeLabel}}</td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" rowspan="1">{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.CalculationLabel}}</td>
            </tr>
            {{if gt (len .IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.FormulaOtherBSAFieldList) 0}}
            {{range .IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.FormulaOtherBSAFieldList}}
            <tr>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>{{.Group}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>{{.VisitName}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>{{.IpName}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>{{.IpSpecification}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>{{.UnitCapacity}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>{{.UnitCalculationStandard}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>{{.IsOpenIp}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>{{.ComparedWith}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>{{.Change}}</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>{{.Calculation}}</span>
                </td>
            </tr>
            {{end}}
            {{else}}
            <tr>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
                <td style="width: 10%; word-break: break-all; white-space: normal;" >
                    <span>/</span>
                </td>
            </tr>
            {{end}}
        </table>
        <div style="width: 100%; word-wrap: break-word;">
            <span>{{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.TakeCare}}</span>
        </div>
        {{end}}
        {{end}}
        {{end}}

        {{end}}

        {{end}}

    </div>
</div>
<script src="https://cdn.bootcss.com/jquery/3.2.1/jquery.slim.min.js" integrity="sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN" crossorigin="anonymous"></script>
<script src="https://cdn.bootcss.com/popper.js/1.12.9/umd/popper.min.js" integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q" crossorigin="anonymous"></script>
<script src="https://cdn.bootcss.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>

</body>
</html>