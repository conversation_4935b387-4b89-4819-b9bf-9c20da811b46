package web

import (
	"clinflash-irt/models"
	"clinflash-irt/service"
	"clinflash-irt/tools"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"strings"
	"time"
)

// RemoteDispensingController struct
type RemoteDispensingController struct {
	s service.DispensingService
}

// Dispensing ..
// 发药（EDC对接）
// Method:	POST
func (c *RemoteDispensingController) Dispensing(ctx *gin.Context) {
	// 返回数据
	var resultSubjectDispensing models.RemoteSubjectDispensing

	// 参数
	var parameter map[string]interface{}
	if err := ctx.ShouldBindBodyWith(&parameter, binding.JSON); err != nil {
		tools.EdcResponse(ctx, err)
		return
	}

	// 参数转换（基本信息 projectID envID cohortID）
	acceptParameter, err := CommonParameter(ctx, parameter)
	if err != nil {
		tools.EdcResponse(ctx, err)
		return
	}

	// 判断当前访视是否为发药访视不是返回错误信息
	visitInfo, err := inquiryVisit(ctx, parameter, acceptParameter)
	if err != nil {
		tools.EdcResponse(ctx, err)
		return
	}

	// 项目ID
	projectOID, _ := primitive.ObjectIDFromHex(acceptParameter["project_id"].(string))

	// 是发药访视 看是随机前发药还是随机后发药
	if !visitInfo.Dispensing {
		tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.visit.no.dispensing.error"))
		return
	}
	acceptParameter["visit_id"] = visitInfo.ID.Hex() // 参数访视周期ID

	// 查询受试者信息
	subject, err := findSubject(ctx, parameter, acceptParameter)
	if err != nil {
		tools.EdcResponse(ctx, err)
		return
	}

	// 查询中心信息
	var projectSite models.ProjectSite
	if subject.Info != nil && len(subject.Info) > 0 { // 受试者存在
		err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
		if err != nil {
			tools.EdcResponse(ctx, err)
			return
		}
	}

	if parameter["instanceRepeatNo"] != nil {
		instanceRepeatNo, _ := parameter["instanceRepeatNo"].(string)
		if instanceRepeatNo != "" {
			acceptParameter["instanceRepeatNo"] = instanceRepeatNo
		} else {
			tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.instance.is.not.nil.error"))
			return
		}
	} else {
		tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.instance.is.not.nil.error"))
		return
	}

	if parameter["blockRepeatNo"] != nil {
		blockRepeatNo, _ := parameter["blockRepeatNo"].(string)
		if blockRepeatNo != "" {
			acceptParameter["blockRepeatNo"] = blockRepeatNo
		} else {
			tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.block.is.not.nil.error"))
			return
		}
	} else {
		tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.block.is.not.nil.error"))
		return
	}

	// type dispense(发药) replace(替换) reissue(补发)
	if parameter["type"] == nil {
		tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.drug.type.error"))
		return
	}
	var typeSign = parameter["type"].(string)

	edcUserName := "EDC"
	if parameter["username"] != nil {
		edcUserName = parameter["username"].(string)
	}

	if typeSign == "dispense" { // 发药
		// 判断是否是随机前发药
		if !visitInfo.Random { // 随机前发药
			if subject.Info == nil || len(subject.Info) == 0 { // 如果没有录入受试者则要进行登记操作 登记完成后进行发药操作
				subjectForm, project, err := parameterConversion(ctx, parameter) // 转换基本参数
				if err != nil {
					tools.EdcResponse(ctx, err)
					return
				}
				if project.SynchronizationMode == 1 { // 分步同步发药前必须走单独的登记接口
					tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.subject.register.error"))
					return
				}
				factorSign, err := getLayeredBl(subjectForm) // factorSign为false时表示需要登记分层因素
				if err != nil {
					tools.EdcResponse(ctx, err)
					return
				}
				subjectForm, err = conversionForm(ctx, subjectForm, parameter, factorSign) // 转换表单参数
				if err != nil {
					tools.EdcResponse(ctx, err)
					return
				}
				subjectID, _, err := service.SubjectRegister(ctx, subjectForm, nil) // 调用登记接口
				if err != nil {
					tools.EdcResponse(ctx, err)
					return
				}
				acceptParameter["subject_id"] = subjectID // 参数受试者ID
				// 查询访视配置的研究产品配置信息
				_, labels, err := findDrugConfigureInfo(ctx, parameter, acceptParameter, visitInfo, subject)
				if err != nil {
					tools.EdcResponse(ctx, err)
					return
				}
				acceptParameter["visit_labels"] = labels
				acceptParameter["dispensing_id"] = getDispensingId(subjectID, visitInfo.ID.Hex(), acceptParameter["blockRepeatNo"].(string), acceptParameter["instanceRepeatNo"].(string))
				resultSubjectDispensing, _, err = c.s.AddDispensingVisit(ctx, acceptParameter, 1, edcUserName)
				if err != nil {
					tools.EdcResponse(ctx, err)
					return
				}

				// 受试者不存在 查询中心
				projectSiteOID, _ := primitive.ObjectIDFromHex(subjectForm["projectSiteId"].(string))
				err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": projectSiteOID}).Decode(&projectSite)
				if err != nil {
					tools.EdcResponse(ctx, err)
					return
				}

				data, err := ResultSetTwo(resultSubjectDispensing, visitInfo, acceptParameter, projectSite)
				tools.EdcResponse(ctx, err, data)
				return
			} else { // 如果已登记
				acceptParameter["subject_id"] = subject.ID.Hex()      // 参数受试者ID
				dsp := repeatedDispensing(acceptParameter, visitInfo) // 查询是否重复发药
				if subject.Status == 1 || subject.Status == 2 {
					// 查询访视配置的研究产品配置信息
					_, labels, err := findDrugConfigureInfo(ctx, parameter, acceptParameter, visitInfo, subject)
					if err != nil {
						tools.EdcResponse(ctx, err)
						return
					}
					acceptParameter["visit_labels"] = labels

					// 判断是否重复发药
					if len(dsp.DispensingMedicines) > 0 || len(dsp.OtherDispensingMedicines) > 0 {
						// 已发研究产品不在进行发药操作返回上次发送的研究产品信息
						resultSetOne, err := ResultSetOne(subject, visitInfo, acceptParameter, dsp, projectOID, projectSite)
						if err != nil {
							tools.EdcResponse(ctx, err)
							return
						}
						tools.EdcResponse(ctx, tools.BuildServerError(ctx, "common.operation.edc.dsp.fail", 5), resultSetOne)
						return
					}
					// 判断中心是否一致
					err = findDispensingSite(ctx, parameter, subject, projectSite)
					if err != nil {
						tools.EdcResponse(ctx, err)
						return
					}
					acceptParameter["dispensing_id"] = getDispensingId(subject.ID.Hex(), visitInfo.ID.Hex(), acceptParameter["blockRepeatNo"].(string), acceptParameter["instanceRepeatNo"].(string))
					resultSubjectDispensing, _, err = c.s.AddDispensingVisit(ctx, acceptParameter, 1, edcUserName)
					if err != nil {
						tools.EdcResponse(ctx, err)
						return
					}
					resultSetTwo, err := ResultSetTwo(resultSubjectDispensing, visitInfo, acceptParameter, projectSite)
					if err != nil {
						tools.EdcResponse(ctx, err)
						return
					}
					tools.EdcResponse(ctx, err, resultSetTwo)
					return
				} else { // 状态错误
					if len(dsp.DispensingMedicines) > 0 || len(dsp.OtherDispensingMedicines) > 0 {
						// 已发研究产品不在进行发药操作返回上次发送的研究产品信息
						resultSetOne, err := ResultSetOne(subject, visitInfo, acceptParameter, dsp, projectOID, projectSite)
						if err != nil {
							tools.EdcResponse(ctx, err)
							return
						}
						tools.EdcResponse(ctx, tools.BuildServerError(ctx, "common.operation.edc.dsp.fail", 5), resultSetOne)
						return
					} else {
						tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.subject.status.dispensing.error"))
						return
					}
				}
			}
		} else { // 随机后发药
			if subject.Info == nil || len(subject.Info) == 0 { // 如果没有录入受试者
				tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.subject.dispensing.error"))
				return
			} else {
				acceptParameter["subject_id"] = subject.ID.Hex()      // 参数受试者ID
				dsp := repeatedDispensing(acceptParameter, visitInfo) // 查询是否重复发药
				if subject.Status == 3 || subject.Status == 6 {
					_, labels, err := findDrugConfigureInfo(ctx, parameter, acceptParameter, visitInfo, subject)
					if err != nil {
						tools.EdcResponse(ctx, err)
						return
					}
					acceptParameter["visit_labels"] = labels
					// 判断是否重复发药
					if len(dsp.DispensingMedicines) > 0 || len(dsp.OtherDispensingMedicines) > 0 {
						// 已发研究产品不在进行发药操作返回上次发送的研究产品信息
						resultSetOne, err := ResultSetOne(subject, visitInfo, acceptParameter, dsp, projectOID, projectSite)
						if err != nil {
							tools.EdcResponse(ctx, err)
							return
						}
						tools.EdcResponse(ctx, tools.BuildServerError(ctx, "common.operation.edc.dsp.fail", 5), resultSetOne)
						return
					}
					// 判断中心是否一致
					err = findDispensingSite(ctx, parameter, subject, projectSite)
					if err != nil {
						tools.EdcResponse(ctx, err)
						return
					}
					acceptParameter["dispensing_id"] = getDispensingId(subject.ID.Hex(), visitInfo.ID.Hex(), acceptParameter["blockRepeatNo"].(string), acceptParameter["instanceRepeatNo"].(string))
					resultSubjectDispensing, _, err = c.s.AddDispensingVisit(ctx, acceptParameter, 1, edcUserName)
					if err != nil {
						tools.EdcResponse(ctx, err)
						return
					}
					resultSetTwo, err := ResultSetTwo(resultSubjectDispensing, visitInfo, acceptParameter, projectSite)
					if err != nil {
						tools.EdcResponse(ctx, err)
						return
					}
					tools.Response(ctx, err, resultSetTwo)
					return
				} else {
					if len(dsp.DispensingMedicines) > 0 || len(dsp.OtherDispensingMedicines) > 0 {
						// 已发研究产品不在进行发药操作返回上次发送的研究产品信息
						resultSetOne, err := ResultSetOne(subject, visitInfo, acceptParameter, dsp, projectOID, projectSite)
						if err != nil {
							tools.EdcResponse(ctx, err)
							return
						}
						tools.EdcResponse(ctx, tools.BuildServerError(ctx, "common.operation.edc.dsp.fail", 5), resultSetOne)
						return
					} else {
						tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.subject.after.dispensing.error"))
						return
					}
				}
			}
		}
	} else if typeSign == "replace" { // 替换
		// 判断中心是否一致
		err = findDispensingSite(ctx, parameter, subject, projectSite)
		if err != nil {
			tools.EdcResponse(ctx, err)
			return
		}

		if parameter["replaceDrug"] == nil {
			tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.drug.replace.nil.nil"))
			return
		}
		replaceDrug, _ := parameter["replaceDrug"].(string)
		if replaceDrug != "" {
			acceptParameter["info"], err = ScreenDrugNumber(ctx, acceptParameter, replaceDrug) // 查询出来要替换的药物号ID
			if err != nil {
				tools.EdcResponse(ctx, err)
				return
			}
			acceptParameter["subject_id"] = subject.ID.Hex() // 受试者ID
			//dsp := repeatedDispensing(acceptParameter, visitInfo)                         // 发药实体
			visitInfoID, _ := primitive.ObjectIDFromHex(acceptParameter["visit_id"].(string))
			//dsp, err := findReplaceDispensing(ctx, subject.ID, replaceDrug, visitInfoID) // 发药实体
			dsp, err := findReplaceDispensing(ctx, subject.ID, replaceDrug, visitInfoID, acceptParameter["blockRepeatNo"].(string), acceptParameter["instanceRepeatNo"].(string)) // 发药实体
			if err != nil {
				tools.EdcResponse(ctx, err)
				return
			}
			acceptParameter["id"] = dsp.ID.Hex()
			acceptParameter["reason"] = "source edc" // 原因 来源EDC
		} else {
			tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.drug.replace.nil.nil"))
			return
		}
		_, err := c.s.ReplaceDrug(ctx, acceptParameter, edcUserName) // 药物替换
		if err != nil {
			tools.EdcResponse(ctx, err)
			return
		}
		dsp := repeatedDispensing(acceptParameter, visitInfo) // 查询发送的药物
		resultSetOne, err := ResultSetOne(subject, visitInfo, acceptParameter, dsp, projectOID, projectSite)
		if err != nil {
			tools.EdcResponse(ctx, err)
			return
		}
		tools.EdcResponse(ctx, err, resultSetOne)
		return
	} else if typeSign == "reissue" { // 补发
		// 判断中心是否一致
		err = findDispensingSite(ctx, parameter, subject, projectSite)
		if err != nil {
			tools.EdcResponse(ctx, err)
			return
		}

		// 查询当前访视已经发出的药物信息
		acceptParameter["subject_id"] = subject.ID.Hex()      // 受试者ID
		dsp := repeatedDispensing(acceptParameter, visitInfo) // 查询发送的药物
		var drugValue []models.DrugValue
		for _, medicine := range dsp.DispensingMedicines {
			index := -1
			for i, value := range drugValue {
				if value.DrugName == medicine.Name {
					index = i
				}
			}
			if index == -1 {
				drugValue = append(drugValue, models.DrugValue{
					DrugName:         medicine.Name,
					DispensingNumber: 1,
					PkgSpec:          "",
					DrugSpec:         "",
				})
			} else {
				drugValue[index].DispensingNumber = drugValue[index].DispensingNumber + 1
			}
		}
		for _, medicine := range dsp.OtherDispensingMedicines {
			index := -1
			for i, value := range drugValue {
				if value.DrugName == medicine.Name {
					index = i
				}
			}
			if index == -1 {
				drugValue = append(drugValue, models.DrugValue{
					DrugName:         medicine.Name,
					DispensingNumber: medicine.Count,
					PkgSpec:          "",
					DrugSpec:         "",
				})
			} else {
				drugValue[index].DispensingNumber = drugValue[index].DispensingNumber + medicine.Count
			}
		}
		// 根据发药标签或者 药物配置
		drugConfigureInfo, _, err := findDrugConfigureInfo(ctx, parameter, acceptParameter, visitInfo, subject)
		if err != nil {
			tools.EdcResponse(ctx, err)
			return
		}
		// 计算药物补发数量
		prepareDrug, err := getReissueCount(ctx, drugConfigureInfo.Values, drugValue)
		if err != nil {
			tools.EdcResponse(ctx, err)
			return
		}
		// 整合请求补发接口参数
		var reissueData = make(map[string]interface{})
		customerID := acceptParameter["customer_id"].(string)
		projectID := acceptParameter["project_id"].(string)
		envID := acceptParameter["env_id"].(string)
		subjectID := subject.ID.Hex()
		cohortID := ""
		if acceptParameter["cohort_id"] != nil {
			cohortID = acceptParameter["cohort_id"].(string)
		}
		reissueData["app"] = 0.0
		reissueData["customer_id"] = customerID
		reissueData["project_id"] = projectID
		reissueData["env_id"] = envID
		reissueData["cohort_id"] = cohortID
		reissueData["subject_id"] = subjectID
		reissueData["visit_id"] = visitInfo.ID.Hex()
		reissueData["instanceRepeatNo"] = acceptParameter["instanceRepeatNo"].(string)
		reissueData["blockRepeatNo"] = acceptParameter["blockRepeatNo"].(string)
		reissueData["sign"] = 1
		var medicineInfo []interface{}
		for _, value := range prepareDrug {
			medicineInfo = append(medicineInfo, map[string]interface{}{
				"name":  value.DrugName,
				"spec":  value.DrugSpec,
				"count": float64(value.DispensingNumber),
			})
		}
		reissueData["medicine_info"] = medicineInfo
		_, _, err = c.s.ReissueDispensing(ctx, reissueData, 1, edcUserName)
		if err != nil {
			tools.EdcResponse(ctx, err)
			return
		}

		dsp = repeatedDispensing(acceptParameter, visitInfo) // 查询发送的药物
		resultSetOne, err := ResultSetOne(subject, visitInfo, acceptParameter, dsp, projectOID, projectSite)
		if err != nil {
			tools.EdcResponse(ctx, err)
			return
		}
		tools.EdcResponse(ctx, err, resultSetOne)
		return
	} else {
		tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.drug.type.error"))
		return
	}
}

// ResultSetOne 返回结果集整合（一）
func ResultSetOne(subject models.Subject, visitInfo models.VisitCycleInfo, acceptParameter map[string]interface{}, dsp models.Dispensing, projectOID primitive.ObjectID, projectSite models.ProjectSite) (models.RemoteSubjectDispensing, error) {
	envOID, _ := primitive.ObjectIDFromHex(acceptParameter["env_id"].(string))
	var dispensingMedicineArray []models.DispensingMedicine
	var otherDispensingMedicineArray []models.OtherDispensingMedicine

	var dispensingResultSet models.RemoteSubjectDispensing
	if projectSite.Tz == "" {
		strTimeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
		if err != nil {
			return models.RemoteSubjectDispensing{}, errors.WithStack(err)
		}
		if strTimeZone == "" {
			projectTimeZone, err := tools.GetTimeZone(subject.ProjectID)
			if err != nil {
				return models.RemoteSubjectDispensing{}, errors.WithStack(err)
			}
			strTimeZone = tools.FormatOffsetToZoneStringUtc(projectTimeZone)
		}
		//timeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
		intTimeZone, err := tools.ParseTimezoneOffset(strTimeZone)
		hours := time.Duration(intTimeZone)
		minutes := time.Duration((intTimeZone - float64(hours)) * 60)
		duration := hours*time.Hour + minutes*time.Minute
		dispensingResultSet.DispensingTime = time.Unix(dsp.DispensingTime.Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
		dispensingResultSet.TimeZone = strTimeZone
	} else {
		timeStr, err := tools.GetLocation(projectSite.Tz, int64(dsp.DispensingTime))
		if err != nil {
			return models.RemoteSubjectDispensing{}, errors.WithStack(err)
		}
		offset, err := tools.GetLocationTZ(projectSite.Tz, int64(dsp.DispensingTime))
		if err != nil {
			return models.RemoteSubjectDispensing{}, errors.WithStack(err)
		}
		dispensingResultSet.TimeZone = offset
		dispensingResultSet.DispensingTime = timeStr
	}

	dispensingResultSet.Timestamp = dsp.DispensingTime
	dispensingResultSet.SubjectNo = subject.Info[0].Value.(string)
	dispensingResultSet.VisitNo = visitInfo.Number
	dispensingResultSet.InstanceRepeatNo = acceptParameter["instanceRepeatNo"].(string)
	dispensingResultSet.BlockRepeatNo = acceptParameter["blockRepeatNo"].(string)

	// 盲态处理
	if dsp.DispensingMedicines != nil && len(dsp.DispensingMedicines) > 0 {
		for _, dm := range dsp.DispensingMedicines {
			isBlindedDrug, _ := tools.IsBlindedDrug(envOID, dm.Name)
			if tools.BlockProject(acceptParameter["project_id"].(string)) && isBlindedDrug {
				dm.Name = tools.BlindData
			}
			dispensingMedicineArray = append(dispensingMedicineArray, dm)
		}
	}
	dispensingResultSet.DispensingMedicines = dispensingMedicineArray

	if dsp.OtherDispensingMedicines != nil && len(dsp.OtherDispensingMedicines) > 0 {
		for _, odm := range dsp.OtherDispensingMedicines {
			isBlindedDrug, _ := tools.IsBlindedDrug(envOID, odm.Name)
			if tools.BlockProject(acceptParameter["project_id"].(string)) && isBlindedDrug {
				odm.Name = tools.BlindData
			}
			otherDispensingMedicineArray = append(otherDispensingMedicineArray, odm)
		}
	}
	dispensingResultSet.OtherDispensingMedicines = otherDispensingMedicineArray
	dispensingResultSet.SiteNo = projectSite.Number
	dispensingResultSet.StandardSiteName = projectSite.Name
	return dispensingResultSet, nil
}

// ResultSetTwo 返回结果集整合（二）
func ResultSetTwo(remoteSubjectDispensing models.RemoteSubjectDispensing, visitInfo models.VisitCycleInfo, acceptParameter map[string]interface{}, projectSite models.ProjectSite) (models.RemoteSubjectDispensing, error) {
	envOID, _ := primitive.ObjectIDFromHex(acceptParameter["env_id"].(string))
	var dispensingMedicineArray []models.DispensingMedicine
	var otherDispensingMedicineArray []models.OtherDispensingMedicine

	var dispensingResultSet models.RemoteSubjectDispensing

	dispensingResultSet.SubjectNo = remoteSubjectDispensing.SubjectNo
	//dispensingResultSet.DispensingMedicines = remoteSubjectDispensing.DispensingMedicines
	//dispensingResultSet.OtherDispensingMedicines = remoteSubjectDispensing.OtherDispensingMedicines
	dispensingResultSet.DispensingTime = remoteSubjectDispensing.DispensingTime
	dispensingResultSet.TimeZone = remoteSubjectDispensing.TimeZone
	dispensingResultSet.Timestamp = remoteSubjectDispensing.Timestamp

	dispensingResultSet.VisitNo = visitInfo.Number
	dispensingResultSet.InstanceRepeatNo = acceptParameter["instanceRepeatNo"].(string)
	dispensingResultSet.BlockRepeatNo = acceptParameter["blockRepeatNo"].(string)

	// 盲态处理
	if remoteSubjectDispensing.DispensingMedicines != nil && len(remoteSubjectDispensing.DispensingMedicines) > 0 {
		for _, dm := range remoteSubjectDispensing.DispensingMedicines {
			isBlindedDrug, _ := tools.IsBlindedDrug(envOID, dm.Name)
			if tools.BlockProject(acceptParameter["project_id"].(string)) && isBlindedDrug {
				dm.Name = tools.BlindData
			}
			dispensingMedicineArray = append(dispensingMedicineArray, dm)
		}
	}
	dispensingResultSet.DispensingMedicines = dispensingMedicineArray

	if remoteSubjectDispensing.OtherDispensingMedicines != nil && len(remoteSubjectDispensing.OtherDispensingMedicines) > 0 {
		for _, odm := range remoteSubjectDispensing.OtherDispensingMedicines {
			isBlindedDrug, _ := tools.IsBlindedDrug(envOID, odm.Name)
			if tools.BlockProject(acceptParameter["project_id"].(string)) && isBlindedDrug {
				odm.Name = tools.BlindData
			}
			otherDispensingMedicineArray = append(otherDispensingMedicineArray, odm)
		}
	}
	dispensingResultSet.OtherDispensingMedicines = otherDispensingMedicineArray
	dispensingResultSet.SiteNo = projectSite.Number
	dispensingResultSet.StandardSiteName = projectSite.Name
	return dispensingResultSet, nil
}

// ScreenDrugNumber 筛选被替换的药物号ID
func ScreenDrugNumber(ctx *gin.Context, acceptParameter map[string]interface{}, replaceDrug string) ([]interface{}, error) {
	// 声明返回数组
	var info []interface{}
	drugNumbers := strings.Split(replaceDrug, ",")
	// 查询药物ID
	customerOID, _ := primitive.ObjectIDFromHex(acceptParameter["customer_id"].(string))
	projectOID, _ := primitive.ObjectIDFromHex(acceptParameter["project_id"].(string))
	envOID, _ := primitive.ObjectIDFromHex(acceptParameter["env_id"].(string))
	// 查询属性配置拿到动态配置的药物配置label
	filter := bson.M{
		"customer_id": customerOID,
		"project_id":  projectOID,
		"env_id":      envOID,
		"number":      bson.M{"$in": drugNumbers},
	}
	var medicines []models.Medicine
	cursor, err := tools.Database.Collection("medicine").Find(nil, filter)
	if err != nil {
		return nil, tools.BuildServerError(ctx, "edc.drug.number.error")

	}
	err = cursor.All(nil, &medicines)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	for _, medicine := range medicines {
		info = append(info, medicine.ID.Hex())
	}

	if len(drugNumbers) != len(info) {
		return nil, tools.BuildServerError(ctx, "edc.drug.number.mismatch")
	}

	return info, nil
}

// ExclusionVisitId 根据方式ID筛选研究产品信息
func ExclusionVisitId(ids []primitive.ObjectID, visitCycleInfoId primitive.ObjectID) bool {
	for _, id := range ids {
		if id == visitCycleInfoId {
			return true
		}
	}
	return false
}

// CommonParameter 公共参数提取
func CommonParameter(ctx *gin.Context, parameter map[string]interface{}) (map[string]interface{}, error) {
	// 返回参数
	type resultMap map[string]interface{}
	dispensingParameter := make(resultMap)
	// 获取项目编号和环境(参数)
	if parameter["projectNo"] == nil || parameter["env"] == nil {
		return nil, tools.BuildServerError(ctx, "edc.project.env.number.error")

	}
	projectNo, _ := parameter["projectNo"].(string)
	envName, _ := parameter["env"].(string)
	if projectNo == "" || envName == "" {
		return nil, tools.BuildServerError(ctx, "edc.project.env.number.error")

	}

	// 查询项目
	var project models.Project
	var projects []models.Project
	pjCursor, pjErr := tools.Database.Collection("project").Find(nil, bson.M{"info.number": projectNo, "info.connect_edc": 1})
	if pjErr != nil {
		return nil, tools.BuildServerError(ctx, "edc.query.project.error")
	}
	err := pjCursor.All(nil, &projects)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if projects == nil || len(projects) != 1 {
		return nil, tools.BuildServerError(ctx, "edc.query.project.number.error")
	}
	project = projects[0]
	if project.PushMode == 2 { // irt全量推送不可以从EDC请求
		return nil, tools.BuildServerError(ctx, "edc.error")
	}
	if project.ResearchAttribute == 1 {
		return nil, tools.BuildServerError(ctx, "edc.query.project.dtp.error")
	}

	// 查询环境信息
	var environment models.Environment
	for _, env := range project.Environments {
		if env.Name == envName {
			environment = env
		}
	}
	if environment.Name == "" {
		return nil, tools.BuildServerError(ctx, "edc.env.error")

	}

	// 查询cohort信息
	if project.Type != 1 {
		if parameter["factor"] == nil {
			return nil, tools.BuildServerError(ctx, "edc.factor.error")

		}
		factor, _ := parameter["factor"].(string)
		if factor == "" {
			return nil, tools.BuildServerError(ctx, "edc.factor.error")
		}
		var cohort models.Cohort
		for _, ch := range environment.Cohorts {
			if ch.Factor == factor {
				cohort = ch
			}
		}
		if cohort.Name == "" {
			return nil, tools.BuildServerError(ctx, "edc.query.factor.error")
		}
		dispensingParameter["cohort_id"] = cohort.ID.Hex() // cohortID
	}
	dispensingParameter["customer_id"] = project.CustomerID.Hex()
	dispensingParameter["project_id"] = project.ID.Hex() // projectID
	dispensingParameter["env_id"] = environment.ID.Hex() // envID

	return dispensingParameter, nil
}

// 查询访视信息
func inquiryVisit(ctx *gin.Context, parameter map[string]interface{}, acceptParameter map[string]interface{}) (models.VisitCycleInfo, error) {
	if parameter["visitNo"] == nil {
		return models.VisitCycleInfo{}, tools.BuildServerError(ctx, "edc.visit.number.error")
	}
	visitNo, _ := parameter["visitNo"].(string)
	if visitNo == "" {
		return models.VisitCycleInfo{}, tools.BuildServerError(ctx, "edc.visit.number.error")

	}

	projectOID, _ := primitive.ObjectIDFromHex(acceptParameter["project_id"].(string))
	envOID, _ := primitive.ObjectIDFromHex(acceptParameter["env_id"].(string))

	// 查询条件
	filter := bson.M{"project_id": projectOID, "env_id": envOID}

	if acceptParameter["cohort_id"] != nil {
		cohortOID, _ := primitive.ObjectIDFromHex(acceptParameter["cohort_id"].(string))
		filter = bson.M{"project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID}
	}

	// 查询访视信息
	var visitCycle models.VisitCycle
	var visitCycleInfo models.VisitCycleInfo
	err := tools.Database.Collection("visit_cycle").FindOne(nil, filter).Decode(&visitCycle)
	if err != nil {
		return models.VisitCycleInfo{}, errors.WithStack(err)
	}

	randomBoole := false
	if visitCycle.Infos != nil && len(visitCycle.Infos) > 0 {
		for _, vif := range visitCycle.Infos {
			if vif.Random {
				randomBoole = true
			}
			if vif.Number == visitNo {
				vif.Random = randomBoole
				visitCycleInfo = vif
			}
		}
		if visitCycleInfo.Name == "" {
			return models.VisitCycleInfo{}, tools.BuildServerError(ctx, "edc.visit.error")

		}
	} else {
		return models.VisitCycleInfo{}, tools.BuildServerError(ctx, "edc.configure.visit.error")

	}
	return visitCycleInfo, nil
}

// 查询受试者信息
func findSubject(ctx *gin.Context, parameter map[string]interface{}, acceptParameter map[string]interface{}) (models.Subject, error) {
	if parameter["subjectNo"] == nil {
		return models.Subject{}, tools.BuildServerError(ctx, "edc.subject.random.number.error")
	}
	shortname, _ := parameter["subjectNo"].(string)
	if shortname == "" {
		return models.Subject{}, tools.BuildServerError(ctx, "edc.subject.random.number.error")

	}
	projectOID, _ := primitive.ObjectIDFromHex(acceptParameter["project_id"].(string))
	envOID, _ := primitive.ObjectIDFromHex(acceptParameter["env_id"].(string))

	// 项目
	projectFilter := bson.M{"_id": projectOID}
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, projectFilter).Decode(&project)
	if err != nil {
		return models.Subject{}, errors.WithStack(err)
	}

	// 查询受试者条件
	filter := bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
		"info": bson.M{
			"$elemMatch": bson.M{
				"name":  "shortname",
				"value": parameter["subjectNo"],
			},
		},
		"deleted": bson.M{"$ne": true},
	}
	if project.ProjectInfo.Type == 3 {
		cohortOID, _ := primitive.ObjectIDFromHex(acceptParameter["cohort_id"].(string))
		filter["cohort_id"] = cohortOID
	}

	// 查询受试者信息
	var subject models.Subject
	err = tools.Database.Collection("subject").FindOne(nil, filter).Decode(&subject)
	if err != nil && err != mongo.ErrNoDocuments {
		return models.Subject{}, errors.WithStack(err)
	}

	return subject, nil
}

// 查询研究产品配置信息
func findDrugConfigureInfo(ctx *gin.Context, parameter map[string]interface{}, acceptParameter map[string]interface{}, visitCycleInfo models.VisitCycleInfo, subject models.Subject) (models.DrugConfigureInfo, []interface{}, error) {

	projectOID, _ := primitive.ObjectIDFromHex(acceptParameter["project_id"].(string))
	envOID, _ := primitive.ObjectIDFromHex(acceptParameter["env_id"].(string))
	var dispensingLabel = ""

	// 查询条件
	filter := bson.M{"project_id": projectOID, "env_id": envOID}
	if acceptParameter["cohort_id"] != nil {
		cohortOID, _ := primitive.ObjectIDFromHex(acceptParameter["cohort_id"].(string))
		filter = bson.M{"project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID}
	}

	// 查询属性配置拿到动态配置的研究产品配置label
	var attribute models.Attribute
	_ = tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute)
	// 判断irt是否配置发药标签
	if attribute.AttributeInfo.EdcDrugConfigLabel == "" { // 没配置
		if parameter["label"] != nil { // 默认label
			dispensingLabel, _ = parameter["label"].(string)
		}
	} else {
		if parameter[attribute.AttributeInfo.EdcDrugConfigLabel] != nil {
			dispensingLabel, _ = parameter[attribute.AttributeInfo.EdcDrugConfigLabel].(string)
		}
	}

	// 查询研究产品配置信息
	var drugConfigure models.DrugConfigure
	var drugConfigureInfo models.DrugConfigureInfo
	_ = tools.Database.Collection("drug_configure").FindOne(nil, filter).Decode(&drugConfigure)
	if drugConfigure.Configures == nil || len(drugConfigure.Configures) == 0 {
		return models.DrugConfigureInfo{}, nil, tools.BuildServerError(ctx, "edc.configure.drug.error")
	}
	labelName := ""
	var drugCount int
	if !visitCycleInfo.Random { // 随机前发药
		for _, cfg := range drugConfigure.Configures {
			if dispensingLabel == "" {
				if ExclusionVisitId(cfg.VisitCycles, visitCycleInfo.ID) && cfg.Group == "N/A" {
					//for _, value := range cfg.Values {
					//	if value.Label != "" {
					//		drugCount++
					//		labelName = value.Label
					//	}
					//}
					drugCount++
					labelName = cfg.Label
					drugConfigureInfo = cfg
				}
			} else {
				if ExclusionVisitId(cfg.VisitCycles, visitCycleInfo.ID) && cfg.Group == "N/A" {
					if cfg.Label == dispensingLabel {
						drugCount++
						labelName = cfg.Label
						drugConfigureInfo = cfg
					}
					//for _, value := range cfg.Values {
					//	if value.Label != "" && value.Label == dispensingLabel {
					//		drugCount++
					//	}
					//}
				}
			}
		}
	} else { // 随机后发药
		for _, cfg := range drugConfigure.Configures {
			if dispensingLabel == "" {
				if ExclusionVisitId(cfg.VisitCycles, visitCycleInfo.ID) && cfg.Group == subject.Group {
					if cfg.Label != "" {

					}
					//for _, value := range cfg.Values {
					//	if value.Label != "" {
					//		drugCount++
					//		labelName = value.Label
					//		drugConfigureInfo.Values = []models.DrugValue{value}
					//	}
					//}
					drugCount++
					labelName = cfg.Label
					drugConfigureInfo = cfg
				}
			} else {
				if ExclusionVisitId(cfg.VisitCycles, visitCycleInfo.ID) && cfg.Group == subject.Group {
					if cfg.Label == dispensingLabel {
						drugCount++
						labelName = cfg.Label
						drugConfigureInfo = cfg
					}
					//for _, value := range cfg.Values {
					//	if value.Label != "" && value.Label == dispensingLabel {
					//		drugCount++
					//		drugConfigureInfo.Values = []models.DrugValue{value}
					//		labelName = value.Label
					//
					//	}
					//}

				}
			}
		}
	}

	if drugCount != 1 {
		return models.DrugConfigureInfo{}, nil, tools.BuildServerError(ctx, "edc.visit.number.drug.configure.error")
	}
	labels := make([]interface{}, 0)
	labels = append(labels, map[string]interface{}{"id": drugConfigureInfo.ID.Hex(), "label": labelName})

	return drugConfigureInfo, labels, nil
}

// 查询是否重复发药
func repeatedDispensing(acceptParameter map[string]interface{}, visitInfo models.VisitCycleInfo) models.Dispensing {
	var returnDsp models.Dispensing
	subjectOID, _ := primitive.ObjectIDFromHex(acceptParameter["subject_id"].(string))
	filter := bson.M{
		"subject_id":                    subjectOID,
		"visit_info.number":             visitInfo.Number,
		"visit_info.instance_repeat_no": acceptParameter["instanceRepeatNo"].(string),
		"visit_info.block_repeat_no":    acceptParameter["blockRepeatNo"].(string)}
	// 查询发药信息
	var dispensingArray []models.Dispensing
	dspCursor, _ := tools.Database.Collection("dispensing").Find(nil, filter)
	err := dspCursor.All(nil, &dispensingArray)
	if err != nil {
		return models.Dispensing{}
	}
	var dispensingMedicineArray []models.DispensingMedicine
	var otherDispensingMedicineArray []models.OtherDispensingMedicine
	for _, da := range dispensingArray {
		// 已经编号药物
		for _, md := range da.DispensingMedicines {
			dispensingMedicineArray = append(dispensingMedicineArray, md)
		}
		// 未编号药物
		for _, odm := range da.OtherDispensingMedicines {
			otherDispensingMedicineArray = append(otherDispensingMedicineArray, odm)
		}
		returnDsp.DispensingTime = da.DispensingTime // 发药时间
	}
	returnDsp.DispensingMedicines = dispensingMedicineArray           // 已编号药物
	returnDsp.OtherDispensingMedicines = otherDispensingMedicineArray // 未编号药物
	return returnDsp
}

// 查询发药ID并返回
func getDispensingId(subjectId string, visitCycleInfoId string, blockRepeatNo string, instanceRepeatNo string) string {
	subjectOID, _ := primitive.ObjectIDFromHex(subjectId)
	visitCycleInfoOID, _ := primitive.ObjectIDFromHex(visitCycleInfoId)
	filter := bson.M{"subject_id": subjectOID, "status": 1, "visit_info.visit_cycle_info_id": visitCycleInfoOID, "visit_info.block_repeat_no": blockRepeatNo, "visit_info.instance_repeat_no": instanceRepeatNo}

	// 查询发药信息
	var dispensing models.Dispensing
	_ = tools.Database.Collection("dispensing").FindOne(nil, filter).Decode(&dispensing)
	if dispensing.ID != primitive.NilObjectID {
		return dispensing.ID.Hex()
	} else {
		return ""
	}
}

// 查询药物应补发数量
func getReissueCount(ctx *gin.Context, drugConfig []models.DrugValue, repeatedDrug []models.DrugValue) ([]models.DrugValue, error) {
	var prepareDrug []models.DrugValue
	if len(drugConfig) < len(repeatedDrug) {

		return nil, tools.BuildServerError(ctx, "edc.drug.reissue.error")
	}

	for i, config := range drugConfig {
		index := -1
		needCount := 0
		for _, repeated := range repeatedDrug {
			if config.DrugName == repeated.DrugName {
				if config.DispensingNumber < repeated.DispensingNumber {
					return nil, tools.BuildServerError(ctx, "edc.drug.reissue.error")
				} else {
					index = i
					needCount = config.DispensingNumber - repeated.DispensingNumber
				}
			}
		}
		if index == -1 {
			prepareDrug = append(prepareDrug, models.DrugValue{
				DrugName:         config.DrugName,
				DispensingNumber: config.DispensingNumber,
				PkgSpec:          config.PkgSpec,
				DrugSpec:         config.DrugSpec,
			})
		} else {
			if needCount != 0 {
				prepareDrug = append(prepareDrug, models.DrugValue{
					DrugName:         config.DrugName,
					DispensingNumber: needCount,
					PkgSpec:          config.PkgSpec,
					DrugSpec:         config.DrugSpec,
				})
			}
		}
	}
	if len(prepareDrug) == 0 {
		return nil, tools.BuildServerError(ctx, "edc.drug.reissue.error")
	}
	return prepareDrug, nil
}

// 查看替换药物所在的访视
func findReplaceDispensing(ctx *gin.Context, subjectID primitive.ObjectID, drugNumber string, visitInfoID primitive.ObjectID, blockRepeatNo string, instanceRepeatNo string) (models.Dispensing, error) {
	match := bson.M{"subject_id": subjectID, "dispensing_medicines.number": drugNumber, "visit_info.visit_cycle_info_id": visitInfoID, "visit_info.block_repeat_no": blockRepeatNo, "visit_info.instance_repeat_no": instanceRepeatNo}
	var dispensing models.Dispensing
	_ = tools.Database.Collection("dispensing").FindOne(nil, match).Decode(&dispensing)
	if dispensing.ID == primitive.NilObjectID {
		return models.Dispensing{}, tools.BuildServerError(ctx, "edc.drug.replace.nil.error")
	}
	return dispensing, nil
}

// 判断发药中心是否一致
func findDispensingSite(ctx *gin.Context, parameter map[string]interface{}, subject models.Subject, projectSite models.ProjectSite) error {
	// 判断EDC和IRT的中心是否一致
	if parameter["siteNo"] == nil {
		return tools.BuildServerError(ctx, "edc.subject.site.error")
	} else {
		// 中心编号
		siteNo, _ := parameter["siteNo"].(string)
		// 标准中心名称
		//standardSiteName := ""
		//if parameter["standardSiteName"] != nil {
		//	standardSiteName, _ = parameter["standardSiteName"].(string)
		//}
		// 普通中心名称
		//siteName := ""
		//if parameter["siteName"] != nil {
		//	siteName, _ = parameter["siteName"].(string)
		//}

		//if standardSiteName != "" && projectSite.Name != "" {
		//	if siteNo != projectSite.Number || standardSiteName != projectSite.Name {
		//		return tools.BuildServerError(ctx, "edc.subject.site.error")
		//	}
		//} else {
		//
		//}
		if siteNo != projectSite.Number {
			return tools.BuildServerError(ctx, "edc.subject.site.error")
		}
	}
	return nil
}
