package web

import (
	"clinflash-irt/models"
	"clinflash-irt/service"
	"clinflash-irt/tools"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

// ProjectController struct
type ProjectController struct {
	s service.ProjectService
}

// Update 更新项目
// Method:	PATCH
func (c *ProjectController) Update(ctx *gin.Context) {
	id := ctx.Query("id")
	var project models.Project
	err := ctx.ShouldBindBodyWith(&project, binding.JSON)
	if err != nil {
		tools.Response(ctx, err)
		return
	} else {
		err := c.s.Update(ctx, id, project)
		tools.Response(ctx, err)
	}
}

// Delete 删除项目
// Method:	Delete
// Path:	/projects/{id: string}
func (c *ProjectController) Delete(ctx *gin.Context) {
	id, _ := ctx.GetQuery("id")
	err := c.s.Delete(ctx, id)
	tools.Response(ctx, err)

}

// GetBy 获取项目信息
// Method:	GET
func (c *ProjectController) GetBy(ctx *gin.Context) {
	id := ctx.Query("id")
	data, err := c.s.Get(ctx, id)
	tools.Response(ctx, err, data)

}

// Find 获取项目信息
// Method:	GET
func (c *ProjectController) Find(ctx *gin.Context) {
	id := ctx.Query("id")
	data, err := c.s.Find(ctx, id)
	tools.Response(ctx, err, data)

}

// AddAdministrator 设置项目管理员
// Method:	POST
func (c *ProjectController) AddAdministrator(ctx *gin.Context) {
	projectID := ctx.Query("id")
	userID := ctx.Query("userId")
	err := c.s.AddAdministrator(ctx, projectID, userID)
	tools.Response(ctx, err)

}

// RemoveAdministrator 移除项目管理员
// Method:	Delete
func (c *ProjectController) RemoveAdministrator(ctx *gin.Context) {
	projectID := ctx.Query("id")
	userID := ctx.Query("userId")
	err := c.s.RemoveAdministrator(ctx, projectID, userID)
	tools.Response(ctx, err)

}

// AddEnvironment 添加项目环境
// Method:	POST
func (c *ProjectController) AddEnvironment(ctx *gin.Context) {
	err := c.s.AddEnvironment(ctx)
	tools.Response(ctx, err)
}

// EditEnvironment 编辑项目环境
// Method:	PATCH
func (c *ProjectController) EditEnvironment(ctx *gin.Context) {
	err := c.s.EditEnvironment(ctx)
	tools.Response(ctx, err)
}

// DeleteEnvironment 删除项目环境
// Method:	DELETE
func (c *ProjectController) DeleteEnvironment(ctx *gin.Context) {
	projectID := ctx.Query("id")
	envID := ctx.Query("envID")
	err := c.s.DeleteEnvironment(ctx, projectID, envID)
	tools.Response(ctx, err)
}

// 查询环境项目下，是否有盲态属性的项目
func (c *ProjectController) GetProjectEnvIsBlind(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	data, err := c.s.GetProjectEnvIsBlind(ctx, projectID, envID)
	tools.Response(ctx, err, data)
}

// AddCohort ...
// Method:	POST
func (c *ProjectController) AddCohort(ctx *gin.Context) {
	projectID := ctx.Query("id")
	envID := ctx.Query("envID")
	var data models.Cohort
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	err := c.s.AddCohort(ctx, projectID, envID, data)
	tools.Response(ctx, err)

}

// UpdateCohort ...
// Method:	POST
func (c *ProjectController) UpdateCohort(ctx *gin.Context) {
	projectID := ctx.Query("id")
	envID := ctx.Query("envID")
	cohortID := ctx.Query("cohortID")
	var data models.UpdateCohort
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	err := c.s.UpdateCohort(ctx, projectID, envID, cohortID, data)
	tools.Response(ctx, err)
}

func (c *ProjectController) UpdateCohortStatus(ctx *gin.Context) {
	err := c.s.UpdateCohortStatus(ctx)
	tools.Response(ctx, err)
}

// CopyCohort ...
// Method:	POST
func (c *ProjectController) CopyCohort(ctx *gin.Context) {
	projectID := ctx.Query("id")
	envID := ctx.Query("envID")
	cohortID := ctx.Query("cohortID")
	isContain, _ := strconv.ParseBool(ctx.Query("isContain"))
	var data models.UpdateCohort
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	err := c.s.CopyCohort(ctx, projectID, envID, cohortID, isContain, data)
	tools.Response(ctx, err)

}

// DeleteCohort 删除Cohort
// Method:	DELETE
func (c *ProjectController) DeleteCohort(ctx *gin.Context) {
	projectID := ctx.Query("id")
	envID := ctx.Query("envID")
	cohortID := ctx.Query("cohortID")
	err := c.s.DeleteCohort(ctx, projectID, envID, cohortID)
	tools.Response(ctx, err)
}

// ProjectEnvironmentUsersSearchList 分页查询项目 user
// Method:	GET
func (c *ProjectController) ProjectEnvironmentUsersSearchList(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	email := ctx.Query("email")
	start, _ := strconv.Atoi(ctx.DefaultQuery("start", "0"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	data, err := c.s.ProjectEnvironmentUsersSearchList(ctx, projectID, envID, email, start, limit)
	tools.Response(ctx, err, data)
}

// AddProjectEnvironmentUser 在项目的环境下添加用户
// Method:	POST
func (c *ProjectController) AddProjectEnvironmentUser(ctx *gin.Context) {
	var data models.ProjectEnvironmentUser
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	err := c.s.AddProjectEnvironmentUser(ctx, data)
	tools.ResponseWithMsg(ctx, "user.customer.authorization.success", err)

}

// AddProjectEnvironmentBatchUserVerify 批量添加---在项目的环境下添加用户验证邮箱
// Method:	POST
func (c *ProjectController) AddProjectEnvironmentBatchUserVerify(ctx *gin.Context) {
	var projectEnvironmentUserList models.ProjectEnvironmentUserList
	_ = ctx.ShouldBindBodyWith(&projectEnvironmentUserList, binding.JSON)
	data, err := c.s.AddProjectEnvironmentBatchUserVerify(ctx, projectEnvironmentUserList)
	tools.Response(ctx, err, data)

}

// AddProjectEnvironmentBatchUser 批量添加---在项目的环境下添加用户
// Method:	POST
func (c *ProjectController) AddProjectEnvironmentBatchUser(ctx *gin.Context) {
	var projectEnvironmentUserList models.ProjectEnvironmentUserList
	_ = ctx.ShouldBindBodyWith(&projectEnvironmentUserList, binding.JSON)
	data, err := c.s.AddProjectEnvironmentBatchUser(ctx, projectEnvironmentUserList)
	tools.Response(ctx, err, data)

}

// SetProjectEnvironmentUserRoles 设置用户在项目环境下的角色
// Method:	PATCH
func (c *ProjectController) SetProjectEnvironmentUserRoles(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	userID := ctx.Query("userId")
	var data models.ProjectEnvironmentUser
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	err := c.s.SetProjectEnvironmentUserRoles(ctx, projectID, envID, userID, data)
	tools.Response(ctx, err)
}

// UnbindProjectEnvironmentUser 解绑项目环境下的用户
// Method:	DELETE
func (c *ProjectController) UnbindProjectEnvironmentUser(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	userID := ctx.Query("userId")
	err := c.s.UnbindProjectEnvironmentUser(ctx, projectID, envID, userID)
	tools.Response(ctx, err)
}

// BatchUnbindProjectEnvironmentUser 批量解绑项目环境下的用户
func (c *ProjectController) BatchUnbindProjectEnvironmentUser(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	type BodyData struct {
		Ids []string `json:"ids"`
	}
	var data BodyData
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	err := c.s.BatchUnbindProjectEnvironmentUser(ctx, projectID, envID, data.Ids)
	tools.Response(ctx, err)
}

// CopyEnvironment 复制环境
// Method:	POST
func (c *ProjectController) CopyEnvironment(ctx *gin.Context) {
	var data map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	err := c.s.CopyEnvironment(ctx, data)
	tools.Response(ctx, err)

}

// DownloadProjectEnvironmentUserData 人员管理列表数据下载
// Method:	GET
func (c *ProjectController) DownloadProjectEnvironmentUserData(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	err := c.s.DownloadProjectEnvironmentUserData(ctx, projectID, envID)
	if err != nil {
		tools.Response(ctx, err)
	}
}

// ResendInviteEmail 在项目的环境下添加用户
// Method:	POST
func (c *ProjectController) ResendInviteEmail(ctx *gin.Context) {
	customerId := ctx.Query("customerId")
	userID := ctx.Query("userId")
	envID := ctx.Query("envId")
	emailLanguage := ctx.Query("emailLanguage")
	err := c.s.ResendInviteEmail(ctx, customerId, userID, envID, emailLanguage)
	tools.Response(ctx, err)

}

// UpdateLockConfig ...
// Method:	POST
func (c *ProjectController) UpdateLockConfig(ctx *gin.Context) {
	projectID := ctx.Query("id")
	envID := ctx.Query("envID")
	var data models.Environment
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	err := c.s.UpdateLockConfig(ctx, projectID, envID, data)
	tools.Response(ctx, err)

}

func (c *ProjectController) SetEnvUserUnblindingCode(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	userID := ctx.Query("userId")
	var data models.ProjectEnvironmentUser
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	resp, err := c.s.SetEnvUserUnblindingCode(ctx, projectID, envID, userID, data)
	tools.Response(ctx, err, resp)
}

func (c *ProjectController) GetList(ctx *gin.Context) {
	resp, err := c.s.GetList(ctx)
	tools.Response(ctx, err, resp)
}

func (c *ProjectController) GetViewMultiLanguage(ctx *gin.Context) {
	resp, err := c.s.GetViewMultiLanguage(ctx)
	tools.Response(ctx, err, resp)
}

func (c *ProjectController) GetHomeList(ctx *gin.Context) {
	resp, err := c.s.GetHomeList(ctx)
	tools.Response(ctx, err, resp)
}

func (c *ProjectController) GetSettingList(ctx *gin.Context) {
	resp, err := c.s.GetSettingList(ctx)
	tools.Response(ctx, err, resp)
}

func (c *ProjectController) GetUsers(ctx *gin.Context) {
	resp, err := c.s.GetUsers(ctx)
	tools.Response(ctx, err, resp)
}

func (c *ProjectController) GetOverview(ctx *gin.Context) {
	customerID := ctx.Request.URL.Query().Get("customerId")
	projectID := ctx.Request.URL.Query().Get("projectId")
	envID := ctx.Request.URL.Query().Get("envId")
	roleID := ctx.Request.URL.Query().Get("roleId")
	resp, err := c.s.GetOverview(ctx, customerID, projectID, envID, roleID)
	tools.Response(ctx, err, resp)
}

func (c *ProjectController) GetCohortStatus(ctx *gin.Context) {
	customerID := ctx.Request.URL.Query().Get("customerId")
	projectID := ctx.Request.URL.Query().Get("projectId")
	envID := ctx.Request.URL.Query().Get("envId")
	roleID := ctx.Request.URL.Query().Get("roleId")
	cohortID := ctx.Request.URL.Query().Get("cohortId")
	resp, err := c.s.GetCohortStatus(ctx, customerID, projectID, envID, roleID, cohortID)
	tools.Response(ctx, err, resp)
}

func (c *ProjectController) ReauthorizationProjectEnvironmentUser(ctx *gin.Context) {
	err := c.s.ReauthorizationProjectEnvironmentUser(ctx)
	tools.Response(ctx, err)
}

// GetPermissions 获取项目的Project-Admin的权限
// Method:	GET
func (c *ProjectController) GetPermissions(ctx *gin.Context) {
	id := ctx.Query("id")
	data, err := c.s.GetPermissions(ctx, id)
	tools.Response(ctx, err, data)

}

// UpdateProjectType 更新项目类型
// Method:	PATCH
func (c *ProjectController) UpdateProjectType(ctx *gin.Context) {
	id := ctx.Query("id")
	modifyType := ctx.Query("modifyType")
	err := c.s.UpdateProjectType(ctx, id, modifyType)
	tools.Response(ctx, err)
}

// UserFocus 关注项目与取消关注项目
// Method:	POST
func (c *ProjectController) UserFocus(ctx *gin.Context) {

	err := c.s.UserFocus(ctx)
	tools.Response(ctx, err)
}

// Card 获取项目信息(卡片)
// Method:	GET
func (c *ProjectController) Card(ctx *gin.Context) {
	id := ctx.Query("id")
	data, err := c.s.Card(ctx, id)
	tools.Response(ctx, err, data)
}

// GetUserProjectElearning 查询所有项目的eLearning对接情况（系统学习）
// Method:	GET
func (c *ProjectController) GetUserProjectElearning(ctx *gin.Context) {
	resp, err := c.s.GetUserProjectElearning(ctx)
	tools.Response(ctx, err, resp)
}

// GetProjectEnvRole 查询项目环境下所有角色关联的用户
// Method:	GET
func (c *ProjectController) GetProjectEnvRole(ctx *gin.Context) {
	projectID := ctx.Request.URL.Query().Get("projectId")
	envID := ctx.Request.URL.Query().Get("envId")
	resp, err := c.s.GetProjectEnvRole(ctx, projectID, envID)
	tools.Response(ctx, err, resp)
}

// GetProjectNotice 查询项目环境下所有角色关联的用户
// Method:	GET
func (c *ProjectController) GetProjectNotice(ctx *gin.Context) {
	projectID := ctx.Request.URL.Query().Get("projectId")
	envID := ctx.Request.URL.Query().Get("envId")
	resp, err := c.s.GetProjectNotice(ctx, projectID, envID)
	tools.Response(ctx, err, resp)
}
func (c *ProjectController) GetRoleNoticeUser(ctx *gin.Context) {
	projectID := ctx.Request.URL.Query().Get("projectId")
	envID := ctx.Request.URL.Query().Get("envId")
	roleID := ctx.Request.URL.Query().Get("roleId")
	resp, err := c.s.GetRoleNoticeUser(ctx, projectID, envID, roleID)
	tools.Response(ctx, err, resp)
}

// UpdateProjectNotice 查询项目环境下所有角色关联的用户
// Method:	POST
func (c *ProjectController) UpdateProjectNotice(ctx *gin.Context) {
	var data models.ProjectNotice
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	err := c.s.UpdateProjectNotice(ctx, data)
	tools.Response(ctx, err)
}

// GetRoleList 查询项目下的所有角色
func (c *ProjectController) GetRoleList(ctx *gin.Context) {
	customerID := ctx.Request.URL.Query().Get("customerId")
	projectID := ctx.Request.URL.Query().Get("projectId")
	resp, err := c.s.GetRoleList(ctx, customerID, projectID)
	tools.Response(ctx, err, resp)
}

// GetRoleUserList 查询项目下的角色对应的所有用户
func (c *ProjectController) GetRoleUserList(ctx *gin.Context) {
	customerID := ctx.Request.URL.Query().Get("customerId")
	projectID := ctx.Request.URL.Query().Get("projectId")
	envID := ctx.Request.URL.Query().Get("envId")
	roleID := ctx.Request.URL.Query().Get("roleId")
	email := ctx.Request.URL.Query().Get("email")
	resp, err := c.s.GetRoleUserList(ctx, customerID, projectID, envID, roleID, email)
	tools.Response(ctx, err, resp)
}

// GetBy 获取项目信息
// Method:	GET
func (c *ProjectController) GetByCohort(ctx *gin.Context) {
	customerID := ctx.Request.URL.Query().Get("customerId")
	projectID := ctx.Request.URL.Query().Get("projectId")
	envID := ctx.Request.URL.Query().Get("envId")
	cohortID := ctx.Request.URL.Query().Get("cohortId")
	resp, err := c.s.GetByCohort(ctx, customerID, projectID, envID, cohortID)
	tools.Response(ctx, err, resp)
}

// GetProjectTimeZone 获取项目时区
// Method:	GET
func (c *ProjectController) GetProjectTimeZone(ctx *gin.Context) {
	projectID := ctx.Request.URL.Query().Get("projectId")
	resp, err := c.s.GetProjectTimeZone(ctx, projectID)
	tools.Response(ctx, err, resp)
}

// GetCopyIsProd PROD改库是否有受试者、订单数据
// Method:	GET
func (c *ProjectController) GetCopyIsProd(ctx *gin.Context) {
	customerID := ctx.Request.URL.Query().Get("customerId")
	projectID := ctx.Request.URL.Query().Get("projectId")
	envID := ctx.Request.URL.Query().Get("envId")
	envName := ctx.Request.URL.Query().Get("envName")
	resp, err := c.s.GetCopyIsProd(ctx, customerID, projectID, envID, envName)
	tools.Response(ctx, err, resp)
}

// GetCopyIsProdData PROD是否有受试者、订单数据
// Method:	GET
func (c *ProjectController) GetCopyIsProdData(ctx *gin.Context) {
	customerID := ctx.Request.URL.Query().Get("customerId")
	projectID := ctx.Request.URL.Query().Get("projectId")
	envID := ctx.Request.URL.Query().Get("envId")
	resp, err := c.s.GetCopyIsProdData(ctx, customerID, projectID, envID)
	tools.Response(ctx, err, resp)
}

// GetProjectType 获取项目状态
// Method:	GET
func (c *ProjectController) GetProjectType(ctx *gin.Context) {
	customerID := ctx.Request.URL.Query().Get("customerId")
	projectID := ctx.Request.URL.Query().Get("projectId")
	resp, err := c.s.GetProjectType(ctx, customerID, projectID)
	tools.Response(ctx, err, resp)
}

// GetProjectType 获取项目状态
// Method:	GET
func (c *ProjectController) UpdateCohortSort(ctx *gin.Context) {
	var updateCohortSortReq models.UpdateCohortSortReq
	_ = ctx.ShouldBindBodyWith(&updateCohortSortReq, binding.JSON)
	err := c.s.UpdateCohortSort(ctx, updateCohortSortReq)
	tools.Response(ctx, err)
}
