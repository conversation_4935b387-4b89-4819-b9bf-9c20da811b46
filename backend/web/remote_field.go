package web

import (
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/service"
	"clinflash-irt/tools"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

// RemoteFieldController struct
type RemoteFieldController struct {
	s service.SiteService
}

// FieldList ..
// IRT反推EDC的对接项目，对接前要把必要的字段label和name传输给EDC
// Method:	POST
func (c *RemoteFieldController) FieldList(ctx *gin.Context) {
	// 传入参数
	var parameter map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&parameter, binding.JSON)
	if parameter["projectNo"] == nil || parameter["env"] == nil {
		tools.EdcResponse(ctx, tools.BuildServerError(ctx, "common.wrong.parameters"), nil)
		return
	}
	projectNo, _ := parameter["projectNo"].(string)
	envName, _ := parameter["env"].(string)
	//pushTypeEdc, _ := parameter["type"].(string)  // 已废弃
	if projectNo == "" || envName == "" {
		tools.EdcResponse(ctx, tools.BuildServerError(ctx, "common.wrong.parameters"), nil)
		return
	}

	var projects []models.Project
	pjCursor, pjErr := tools.Database.Collection("project").Find(nil, bson.M{"info.number": projectNo, "info.connect_edc": 1, "info.push_mode": 2})
	if pjErr != nil {
		tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.query.project.error"), nil)
		return
	}
	err := pjCursor.All(nil, &projects)
	if err != nil {
		tools.EdcResponse(ctx, errors.WithStack(err), nil)
		return
	}
	if projects == nil || len(projects) != 1 {
		tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.query.project.number.error"), nil)
		return
	}
	if projects[0].ResearchAttribute == 1 {
		tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.query.project.dtp.error"), nil)
		return
	}

	// 筛选环境信息
	var environment models.Environment
	for _, env := range projects[0].Environments {
		if env.Name == envName {
			environment = env
		}
	}
	if environment.Name == "" {
		tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.env.error"), nil)
		return
	}

	// 返回对象
	var data map[string]interface{}
	var form []map[string]interface{}

	// 判断是基本研究项目还是cohort项目
	if projects[0].Type == 1 {

		var factor []map[string]interface{}  // 随机分层
		var actualF []map[string]interface{} // 实际分层

		var random []map[string]interface{}
		var actualFactor []map[string]interface{} // 实际分层返回数据
		var drug []map[string]interface{}
		var visits []map[string]interface{}

		filter := bson.M{"customer_id": projects[0].CustomerID, "env_id": environment.ID, "status": 1}
		var randomLists []models.RandomList
		cursor, err := tools.Database.Collection("random_list").Find(nil, filter)
		if err != nil {
			tools.EdcResponse(ctx, errors.WithStack(err), nil)
			return
		}
		err = cursor.All(nil, &randomLists)
		if err != nil {
			tools.EdcResponse(ctx, errors.WithStack(err), nil)
			return
		}
		groupSign := false
		for i := 0; i < len(randomLists); i++ {
			randomList := randomLists[i]
			for i := 0; i < len(randomList.Design.Factors); i++ {
				// 拼接label
				var label string
				var optLabel string
				for _, opt := range randomList.Design.Factors[i].Options {
					optLabel = optLabel + opt.Label + "/"
				}
				optLabel = optLabel[0 : len(optLabel)-1]
				label = randomList.Design.Factors[i].Label + "(" + optLabel + ")"
				// 随机分层
				factor = append(factor, map[string]interface{}{
					"field": randomList.Design.Factors[i].Name,
					"label": label,
				})
				// 实际分层
				actualF = append(actualF, map[string]interface{}{
					"field": randomList.Design.Factors[i].Name + "_actual",
					"label": label,
				})
			}

			for _, rdg := range randomList.Design.Groups {
				// 子组别不为空
				if rdg.SubName != "" {
					groupSign = true
					break
				}
			}
		}
		// 随机分层
		if factor != nil && len(factor) > 0 {
			for _, f := range factor {
				if factorBl(random, f) {
					random = append(random, f)
				}
			}
		}
		// 实际分层
		if actualF != nil && len(actualF) > 0 {
			for _, af := range actualF {
				if factorBl(actualFactor, af) {
					actualFactor = append(actualFactor, af)
				}
			}
		}

		random = append(random, map[string]interface{}{
			"field": "randomNo",
			"label": locales.Tr(ctx, "edc_push_randomization_number"),
		})
		random = append(random, map[string]interface{}{
			"field": "randomTime",
			"label": locales.Tr(ctx, "edc_push_randomization_time"),
		})
		random = append(random, map[string]interface{}{
			"field": "group",
			"label": locales.Tr(ctx, "edc_push_group"), // 组别
		})

		// 如果配置了子组别
		if groupSign {
			//主组别
			random = append(random, map[string]interface{}{
				"field": "parGroupName",
				"label": locales.Tr(ctx, "operation_log.drug_configure.preGroup"),
			})
			// 子组别
			random = append(random, map[string]interface{}{
				"field": "subGroupName",
				"label": locales.Tr(ctx, "operation_log.drug_configure.subGroup"),
			})
		}

		// 查询表单
		var randomForm models.Form
		formFilter := bson.M{"env_id": environment.ID}
		if err := tools.Database.Collection("form").FindOne(nil, formFilter).Decode(&randomForm); err != nil && err != mongo.ErrNoDocuments {
			tools.EdcResponse(ctx, errors.WithStack(err), nil)
			return
		}
		if randomForm.Fields != nil && len(randomForm.Fields) > 0 {
			for _, rf := range randomForm.Fields {
				if (rf.Status == nil || *rf.Status == 1) && (rf.ApplicationType == nil || *rf.ApplicationType == 1 || *rf.ApplicationType == 4) {
					random = append(random, map[string]interface{}{
						"field": rf.Name,
						"label": rf.Label + "(aidInfo.)",
					})
				}
			}
		}

		// 筛选字段放到random 里
		random = append(random, map[string]interface{}{
			"field": "isScreen",
			"label": locales.Tr(ctx, "is_screen"),
		})
		random = append(random, map[string]interface{}{
			"field": "screenTime",
			"label": locales.Tr(ctx, "screen_time"),
		})
		random = append(random, map[string]interface{}{
			"field": "icfTime",
			"label": locales.Tr(ctx, "icf_time"),
		})

		drug = append(drug, map[string]interface{}{
			"field": "drugNo",
			"label": locales.Tr(ctx, "medicine_number"),
		})
		drug = append(drug, map[string]interface{}{
			"field": "drugTime",
			"label": locales.Tr(ctx, "edc_push_dispense_time"),
		})
		drug = append(drug, map[string]interface{}{
			"field": "drug",
			"label": locales.Tr(ctx, "edc_push_drug"),
		})
		drug = append(drug, map[string]interface{}{ // 水平
			"field": "doseLevel",
			"label": locales.Tr(ctx, "edc_push_drug_level"),
		})
		drug = append(drug, map[string]interface{}{ // 标签
			"field": "labels",
			"label": locales.Tr(ctx, "edc_push_drug_label"),
		})

		// 查询访视信息
		visitFilter := bson.M{"customer_id": projects[0].CustomerID, "env_id": environment.ID}
		var visitCycle models.VisitCycle
		err = tools.Database.Collection("visit_cycle").FindOne(nil, visitFilter).Decode(&visitCycle)
		if err != nil && err != mongo.ErrNoDocuments {
			tools.EdcResponse(ctx, errors.WithStack(err), nil)
			return
		}
		if visitCycle.Infos != nil && len(visitCycle.Infos) > 0 {
			for _, vif := range visitCycle.Infos {
				visits = append(visits, map[string]interface{}{
					"visit": vif.Number,
					"name":  vif.Name,
				})
			}
		}

		form = append(form, map[string]interface{}{
			"cohort":        "",
			"random":        random,
			"actual_factor": actualFactor,
			"drug":          drug,
			"visits":        visits,
		})

		data = map[string]interface{}{
			"projectNo": projects[0].Number,
			"env":       environment.Name,
			"form":      form,
		}
	} else {
		// 查询cohort
		for i := 0; i < len(environment.Cohorts); i++ {
			var factor []map[string]interface{}  // 随机分层
			var actualF []map[string]interface{} // 实际分层

			var random []map[string]interface{}
			var actualFactor []map[string]interface{} // 实际分层返回数据
			var drug []map[string]interface{}
			var visits []map[string]interface{}

			filter := bson.M{"customer_id": projects[0].CustomerID, "env_id": environment.ID, "cohort_id": environment.Cohorts[i].ID, "status": 1}
			var randomLists []models.RandomList
			cursor, err := tools.Database.Collection("random_list").Find(nil, filter)
			if err != nil {
				tools.EdcResponse(ctx, errors.WithStack(err), nil)
				return
			}
			err = cursor.All(nil, &randomLists)
			if err != nil {
				tools.EdcResponse(ctx, errors.WithStack(err), nil)
				return
			}
			for i := 0; i < len(randomLists); i++ {
				randomList := randomLists[i]
				for i := 0; i < len(randomList.Design.Factors); i++ {
					// 拼接label
					var label string
					var optLabel string
					for _, opt := range randomList.Design.Factors[i].Options {
						optLabel = optLabel + opt.Label + "/"
					}
					optLabel = optLabel[0 : len(optLabel)-1]
					label = randomList.Design.Factors[i].Label + "(" + optLabel + ")"
					// 随机分层
					factor = append(factor, map[string]interface{}{
						"field": randomList.Design.Factors[i].Name,
						"label": label,
					})
					// 实际分层
					actualF = append(actualF, map[string]interface{}{
						"field": randomList.Design.Factors[i].Name + "_actual",
						"label": label,
					})
				}
			}
			// 随机分层
			if factor != nil && len(factor) > 0 {
				for _, f := range factor {
					if factorBl(random, f) {
						random = append(random, f)
					}
				}
			}
			// 实际分层
			if actualF != nil && len(actualF) > 0 {
				for _, af := range actualF {
					if factorBl(actualFactor, af) {
						actualFactor = append(actualFactor, af)
					}
				}
			}

			random = append(random, map[string]interface{}{
				"field": "randomNo",
				"label": locales.Tr(ctx, "edc_push_randomization_number"),
			})
			random = append(random, map[string]interface{}{
				"field": "randomTime",
				"label": locales.Tr(ctx, "edc_push_randomization_time"),
			})
			random = append(random, map[string]interface{}{
				"field": "group",
				"label": locales.Tr(ctx, "edc_push_group"),
			})
			random = append(random, map[string]interface{}{
				"field": "cohortName",
				"label": locales.Tr(ctx, "edc_push_cohort"),
			})

			// 查询表单
			var randomForm models.Form
			formFilter := bson.M{"env_id": environment.ID, "cohort_id": environment.Cohorts[i].ID}
			if err := tools.Database.Collection("form").FindOne(nil, formFilter).Decode(&randomForm); err != nil && err != mongo.ErrNoDocuments {
				tools.EdcResponse(ctx, errors.WithStack(err), nil)
				return
			}
			if randomForm.Fields != nil && len(randomForm.Fields) > 0 {
				for _, rf := range randomForm.Fields {
					if (rf.Status == nil || *rf.Status == 1) && (rf.ApplicationType == nil || *rf.ApplicationType == 1 || *rf.ApplicationType == 4) {
						random = append(random, map[string]interface{}{
							"field": rf.Name,
							"label": rf.Label + "(aidInfo.)",
						})
					}
				}
			}

			// 筛选字段放到random 里
			random = append(random, map[string]interface{}{
				"field": "isScreen",
				"label": locales.Tr(ctx, "is_screen"),
			})
			random = append(random, map[string]interface{}{
				"field": "screenTime",
				"label": locales.Tr(ctx, "screen_time"),
			})
			random = append(random, map[string]interface{}{
				"field": "icfTime",
				"label": locales.Tr(ctx, "icf_time"),
			})

			drug = append(drug, map[string]interface{}{
				"field": "drugNo",
				"label": locales.Tr(ctx, "medicine_number"),
			})
			drug = append(drug, map[string]interface{}{
				"field": "drugTime",
				"label": locales.Tr(ctx, "edc_push_dispense_time"),
			})
			drug = append(drug, map[string]interface{}{
				"field": "drug",
				"label": locales.Tr(ctx, "edc_push_drug"),
			})
			drug = append(drug, map[string]interface{}{ // 水平
				"field": "doseLevel",
				"label": locales.Tr(ctx, "edc_push_drug_level"),
			})
			drug = append(drug, map[string]interface{}{ // 标签
				"field": "labels",
				"label": locales.Tr(ctx, "edc_push_drug_label"),
			})
			drug = append(drug, map[string]interface{}{
				"field": "cohortName",
				"label": locales.Tr(ctx, "edc_push_cohort"),
			})

			// 查询访视信息
			visitFilter := bson.M{"customer_id": projects[0].CustomerID, "env_id": environment.ID, "cohort_id": environment.Cohorts[i].ID}
			var visitCycle models.VisitCycle
			err = tools.Database.Collection("visit_cycle").FindOne(nil, visitFilter).Decode(&visitCycle)
			if err != nil && err != mongo.ErrNoDocuments {
				tools.EdcResponse(ctx, errors.WithStack(err), nil)
				return
			}
			if visitCycle.Infos != nil && len(visitCycle.Infos) > 0 {
				for _, vif := range visitCycle.Infos {
					visits = append(visits, map[string]interface{}{
						"visit": vif.Number,
						"name":  vif.Name,
					})
				}
			}

			form = append(form, map[string]interface{}{
				"cohort":        models.GetCohortReRandomName(environment.Cohorts[i]),
				"random":        random,
				"actual_factor": actualFactor,
				"drug":          drug,
				"visits":        visits,
			})
		}
		data = map[string]interface{}{
			"projectNo": projects[0].Number,
			"env":       environment.Name,
			"form":      form,
		}
	}
	// 修改项目属性（EDC推送方式） push_type_edc 字段在2.11 版本已经废弃
	//update := bson.M{"$set": bson.M{"info.push_type_edc": pushTypeEdc}}
	//if _, err = tools.Database.Collection("project").UpdateOne(nil, bson.M{"_id": projects[0].ID}, update); err != nil {
	//	tools.EdcResponse(ctx, errors.WithStack(err), nil)
	//	return
	//}

	tools.EdcResponse(ctx, nil, data)
	return
}

// 判断分层因素是否重复
func factorBl(randoms []map[string]interface{}, f map[string]interface{}) bool {
	if randoms != nil && len(randoms) > 0 {
		for _, random := range randoms {
			if random["field"] == f["field"] && random["label"] == f["label"] {
				return false
			}
		}
	}
	return true
}
