package service

import (
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"strconv"
	"strings"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// PushServices
type PushServices struct {
}

func (s *PushServices) PushList(ctx *gin.Context, customerID string, projectID string, envID string, cohortID string, start int, limit int, startTime int, endTime int, status string, subjectNo string) (map[string]interface{}, error) {
	collection := tools.Database.Collection("edc_push")
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)

	match := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
		"status":      bson.M{"$ne": 5},
	}

	filter := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
	}

	if !cohortOID.IsZero() {
		match["cohort_id"] = cohortOID
		filter["cohort_id"] = cohortOID
	}
	if startTime != 0 && endTime != 0 {
		match["send_time"] = bson.M{
			"$gt": startTime,
			"$lt": endTime,
		}
	} else if startTime != 0 {
		match["send_time"] = bson.M{"$gt": startTime}
	} else if endTime != 0 {
		match["send_time"] = bson.M{"$lt": endTime}
	}

	if status != "" {
		s, _ := strconv.Atoi(status)
		match["status"] = s
	}
	if subjectNo != "" {
		match["content.subject_no"] = bson.M{"$regex": subjectNo}
	}

	project := bson.M{
		"id":             "$_id",
		"_id":            0,
		"oid":            "$oid",
		"subjectNo":      "$content.subject_no",
		"source":         "$source",
		"sourceType":     "$source_type",
		"sourceTypeList": "$source_type_list",
		"pushMode":       "$push_mode",
		"status":         "$status",
		"retry":          "$retry",
		"sendTime":       "$send_time",
		"result":         "$result",
	}
	total, err := collection.CountDocuments(nil, match)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var data []map[string]interface{}
	// 分页数据查询
	pipepine := mongo.Pipeline{
		{{"$match", match}},
		{{Key: "$project", Value: project}},
		{{Key: "$sort", Value: bson.D{{"sendTime", -1}}}},
		{{Key: "$skip", Value: start}},
		{{Key: "$limit", Value: limit}},
	}

	cursor, err := collection.Aggregate(nil, pipepine)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询发送次数
	if data != nil && len(data) > 0 {
		for _, d := range data {
			edcPushFilter := bson.M{
				"edc_push_id": d["id"],
			}
			count, _ := tools.Database.Collection("edc_push_log").CountDocuments(nil, edcPushFilter)
			d["count"] = count
		}
	}

	// 推送中
	filter["status"] = 0
	afootCount, _ := tools.Database.Collection("edc_push").CountDocuments(nil, filter)

	// 成功
	filter["status"] = 1
	successCount, _ := tools.Database.Collection("edc_push").CountDocuments(nil, filter)

	// 失败
	filter["status"] = 2
	failCount, _ := tools.Database.Collection("edc_push").CountDocuments(nil, filter)

	// 无效
	filter["status"] = 3
	loseCount, _ := tools.Database.Collection("edc_push").CountDocuments(nil, filter)

	// 处理中
	filter["status"] = 4
	handleCount, _ := tools.Database.Collection("edc_push").CountDocuments(nil, filter)

	return map[string]interface{}{"total": total, "items": data, "afootCount": afootCount, "successCount": successCount, "failCount": failCount, "loseCount": loseCount, "handleCount": handleCount}, nil
}

// 重新发送
func (s *PushServices) PushSend(ctx *gin.Context, pushSend models.PushSend) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		operatorID := primitive.NilObjectID
		// 修改重试次数
		update := bson.M{
			"$set": bson.M{
				"status":    0,
				"retry":     0,
				"push_mode": 2,
			},
		}
		filter := bson.M{}
		if pushSend.Type == 1 {
			filter = bson.M{"_id": pushSend.PushId}
			operatorID = pushSend.PushId
		} else if pushSend.Type == 2 {
			filter = bson.M{"_id": bson.M{"$in": pushSend.PushIds}}
		} else {
			filter = bson.M{"status": 2}
		}
		if _, err := tools.Database.Collection("edc_push").UpdateMany(sctx, filter, update); err != nil {
			return nil, errors.WithStack(err)
		}

		// 查询数据
		var edcPushAll []models.EdcPush
		edcPushAllCursor, err := tools.Database.Collection("edc_push").Find(sctx, filter)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if err = edcPushAllCursor.All(nil, &edcPushAll); err != nil {
			return nil, errors.WithStack(err)
		}
		OID := primitive.NilObjectID
		if edcPushAll != nil && len(edcPushAll) > 0 {
			if edcPushAll[0].CohortID != primitive.NilObjectID {
				OID = edcPushAll[0].CohortID
			} else {
				OID = edcPushAll[0].EnvironmentID
			}
			// 插入项目日志
			err = insertPushLog(ctx, sctx, OID, 1, edcPushAll, operatorID)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
		return nil, nil
	}

	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *PushServices) Details(ctx *gin.Context, id string, roleID string) (map[string]interface{}, error) {
	oid, _ := primitive.ObjectIDFromHex(id)
	var edcPush models.EdcPush
	err := tools.Database.Collection("edc_push").FindOne(nil, bson.M{"_id": oid}).Decode(&edcPush)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询当前数据的所有发送记录
	var edcPushLogList []models.EdcPushLog
	pushLogFilter := bson.M{"edc_push_id": oid}
	opts := &options.FindOptions{
		Sort: bson.D{{"send_time", -1}},
	}
	cursor, err := tools.Database.Collection("edc_push_log").Find(nil, pushLogFilter, opts)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &edcPushLogList)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	filter := bson.M{
		"project_id":  edcPush.ProjectID,
		"env_id":      edcPush.EnvironmentID,
		"customer_id": edcPush.CustomerID,
	}
	if edcPush.CohortID != primitive.NilObjectID {
		filter["cohort_id"] = edcPush.CohortID
	}
	// 查询项目属性配置
	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	isBlindedRole := false
	if roleID != "" {
		isBlindedRole, err = tools.IsBlindedRole(roleID)
		if err != nil {
			return nil, err
		}
	}

	var formData []map[string]interface{}  // 表头data
	var dataArray []map[string]interface{} // 数据array

	var findSubject models.Subject
	var project models.Project
	var randomList models.RandomList
	var randomForm models.Form
	var findDispensing models.Dispensing
	var visitCycle models.VisitCycle

	// 组合表头
	if edcPush.Source == 1 { // 随机
		// 查询受试者信息
		subjectFilter := bson.M{"_id": edcPush.OID}
		tools.Database.Collection("subject").FindOne(nil, subjectFilter).Decode(&findSubject)

		// 查询项目
		projectFilter := bson.M{"_id": findSubject.ProjectID}
		tools.Database.Collection("project").FindOne(nil, projectFilter).Decode(&project)

		// 查询RandomList
		randomFilter := bson.M{
			"customer_id": findSubject.CustomerID,
			"project_id":  findSubject.ProjectID,
			"env_id":      findSubject.EnvironmentID,
			"status":      1,
			"$or": bson.A{
				bson.M{"site_ids": nil},
				bson.M{"site_ids": findSubject.ProjectSiteID},
			}}
		if findSubject.CohortID != primitive.NilObjectID {
			randomFilter = bson.M{
				"customer_id": findSubject.CustomerID,
				"project_id":  findSubject.ProjectID,
				"env_id":      findSubject.EnvironmentID,
				"status":      1,
				"cohort_id":   findSubject.CohortID,
				"$or": bson.A{
					bson.M{"site_ids": nil},
					bson.M{"site_ids": findSubject.ProjectSiteID},
				}}
		}
		if project.ProjectInfo.Type == 3 && findSubject.LastGroup != "" { // 在随机逻辑
			randomFilter = bson.M{
				"customer_id": findSubject.CustomerID,
				"project_id":  findSubject.ProjectID,
				"env_id":      findSubject.EnvironmentID,
				"status":      1,
				"cohort_id":   findSubject.CohortID,
				"last_group":  findSubject.LastGroup,
				"$or": bson.A{
					bson.M{"site_ids": nil},
					bson.M{"site_ids": findSubject.ProjectSiteID},
				}}
		}
		tools.Database.Collection("random_list").FindOne(nil, randomFilter).Decode(&randomList)

		if edcPush.SourceType == 3 || edcPush.SourceType == 11 || slice.Contain(edcPush.SourceTypeList, 3) { // 只有随机或者受试者替换才展示随机号 随机组别和随机时间

			// 随机号
			if determineVariable(edcPush.EdcFields, "randomNo") {
				formData = append(formData, map[string]interface{}{
					"label": locales.Tr(ctx, "edc_push_randomization_number"),
					"name":  "randomNo",
				})
			}

			// 组别
			if determineVariable(edcPush.EdcFields, "group") {
				formData = append(formData, map[string]interface{}{
					"label": locales.Tr(ctx, "edc_push_group"),
					"name":  "group",
				})
			}

			// 随机时间
			if determineVariable(edcPush.EdcFields, "randomTime") {
				formData = append(formData, map[string]interface{}{
					"label": locales.Tr(ctx, "edc_push_randomization_time"),
					"name":  "randomTime",
				})
			}
		}

		// 添加筛选的字段
		{
			if determineVariable(edcPush.EdcFields, "isScreen") {
				formData = append(formData, map[string]interface{}{
					"label": locales.Tr(ctx, "is_screen"),
					"name":  "isScreen",
				})
			}
			if determineVariable(edcPush.EdcFields, "screenTime") {
				formData = append(formData, map[string]interface{}{
					"label": locales.Tr(ctx, "screen_time"),
					"name":  "screenTime",
				})
			}
			if determineVariable(edcPush.EdcFields, "icfTime") {
				formData = append(formData, map[string]interface{}{
					"label": locales.Tr(ctx, "icf_time"),
					"name":  "icfTime",
				})
			}
		}

		if randomList.Design.Factors != nil && len(randomList.Design.Factors) > 0 {
			for _, factor := range randomList.Design.Factors { // 分层因素
				find, b := slice.Find(findSubject.Info, func(index int, item models.Info) bool {
					return item.Name == factor.Name
				})
				if b && (find.Value != nil || factor.Status == nil || *factor.Status == 1) {
					if determineVariable(edcPush.EdcFields, factor.Name) {
						formData = append(formData, map[string]interface{}{
							"label": factor.Label,
							"name":  factor.Name,
						})
					}
				}
				// 实际分层
				findActual, b := slice.Find(findSubject.ActualInfo, func(index int, item models.Info) bool {
					return item.Name == factor.Name
				})
				if b && (findActual.Value != nil || factor.Status == nil || *factor.Status == 1) {
					if determineVariable(edcPush.EdcFields, factor.Name+"_actual") {
						// 历史纪录推送的实际分层label 要标注出来
						if edcPush.SourceTypeList != nil && len(edcPush.SourceTypeList) > 0 {
							formData = append(formData, map[string]interface{}{
								"label": factor.Label + "(" + locales.Tr(ctx, "operation_log.attribute.minimize_calc_actual") + ")",
								"name":  factor.Name + "_actual",
							})
						} else {
							formData = append(formData, map[string]interface{}{
								"label": factor.Label,
								"name":  factor.Name + "_actual",
							})
						}
					}
				}
			}
		}

		// 查询表单
		formFilter := bson.M{"env_id": findSubject.EnvironmentID}
		if findSubject.CohortID != primitive.NilObjectID {
			formFilter = bson.M{"env_id": findSubject.EnvironmentID, "cohort_id": findSubject.CohortID}
		}
		if err := tools.Database.Collection("form").FindOne(nil, formFilter).Decode(&randomForm); err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		if randomForm.Fields != nil && len(randomForm.Fields) > 0 {
			for _, rf := range randomForm.Fields {
				find, b := slice.Find(findSubject.Info, func(index int, item models.Info) bool {
					return item.Name == rf.Name
				})
				if (rf.ApplicationType == nil || *rf.ApplicationType == 1 || *rf.ApplicationType == 4) && b && (find.Value != nil || rf.Status == nil || *rf.Status == 1) {
					if determineVariable(edcPush.EdcFields, rf.Name) {
						formData = append(formData, map[string]interface{}{
							"label": rf.Label,
							"name":  rf.Name,
						})
					}
				}
			}
		}
	} else if edcPush.Source == 2 { // 发药
		// 查询发药信息
		dispensingFilter := bson.M{"_id": edcPush.OID}
		tools.Database.Collection("dispensing").FindOne(nil, dispensingFilter).Decode(&findDispensing)

		// 查询受试者信息
		subjectFilter := bson.M{"_id": findDispensing.SubjectID}
		tools.Database.Collection("subject").FindOne(nil, subjectFilter).Decode(&findSubject)

		// 查询项目
		projectFilter := bson.M{"_id": findSubject.ProjectID}
		tools.Database.Collection("project").FindOne(nil, projectFilter).Decode(&project)

		// 查询访视信息
		visitCycleFilter := bson.M{"customer_id": findSubject.CustomerID, "project_id": findSubject.ProjectID, "env_id": findSubject.EnvironmentID, "cohort_id": findSubject.CohortID}
		tools.Database.Collection("visit_cycle").FindOne(nil, visitCycleFilter).Decode(&visitCycle)
		formData = append(formData, map[string]interface{}{
			"label": locales.Tr(ctx, "edc_push_visit_number"),
			"name":  "visitNum",
		})

		if determineVariable(edcPush.EdcFields, "drugNo") || determineVariable(edcPush.EdcFields, "drugName(DrugNo)") {
			formData = append(formData, map[string]interface{}{
				"label": locales.Tr(ctx, "edc_push_dispense"),
				"name":  "drugNo",
			})
		}

		if determineVariable(edcPush.EdcFields, "drugTime") {
			formData = append(formData, map[string]interface{}{
				"label": locales.Tr(ctx, "edc_push_dispense_time"),
				"name":  "drugTime",
			})
		}
	}

	// 组合数据
	for _, edcPushLog := range edcPushLogList {
		data := make(map[string]interface{}) // 数据data

		data["id"] = edcPushLog.ID
		data["subjectNo"] = edcPushLog.Content.SubjectNo
		data["status"] = edcPushLog.Status
		data["sendTime"] = edcPushLog.SendTime
		if edcPushLog.Source == 1 { // 随机
			if findSubject.Status != 1 && findSubject.Status != 2 {
				// 判断是否显示随机号
				rdm := getValue(edcPushLog, "randomNo", 1, findSubject)
				if !attribute.AttributeInfo.IsRandomNumber {
					rdm = tools.BlindData
				}
				data["randomNo"] = rdm

				// 如果是盲法项目且分配了盲态权限则隐藏组别
				if findSubject.SubGroupName != "" {
					gP, b := slice.Find(randomList.Design.Groups, func(index int, item models.RandomListGroup) bool {
						return item.Name == findSubject.Group
					})
					if b {
						if isBlindedRole {
							group := *gP
							if attribute.AttributeInfo.Blind && group.Blind {
								findSubject.Group = tools.BlindData + " " + tools.BlindData
							} else if attribute.AttributeInfo.Blind && !group.Blind {
								findSubject.Group = tools.BlindData + " " + findSubject.SubGroupName
							} else if !attribute.AttributeInfo.Blind && group.Blind {
								findSubject.Group = findSubject.ParGroupName + " " + tools.BlindData
							}
						}
					}
					data["group"] = getValue(edcPushLog, "group", 2, findSubject)
				} else {
					if attribute.AttributeInfo.Blind && isBlindedRole {
						data["group"] = getValue(edcPushLog, "group", 3, findSubject)
					} else {
						data["group"] = getValue(edcPushLog, "group", 2, findSubject)
					}
				}
				// 随机时间
				data["randomTime"] = getValue(edcPushLog, "randomTime", 1, findSubject)
			}
			// 分层因素
			if randomList.Design.Factors != nil && len(randomList.Design.Factors) > 0 {
				for _, factor := range randomList.Design.Factors { // 分层因素
					if factor.Name != "" {
						data[factor.Name] = getValue(edcPushLog, factor.Name, 1, findSubject)
						// 实际分层
						data[factor.Name+"_actual"] = getValue(edcPushLog, factor.Name+"_actual", 1, findSubject)
					}
				}
			}

			// 表单配置
			if randomForm.Fields != nil && len(randomForm.Fields) > 0 {
				for _, rf := range randomForm.Fields {
					if rf.ApplicationType == nil || *rf.ApplicationType == 1 || *rf.ApplicationType == 4 {
						if rf.Name != "" {
							data[rf.Name] = getValue(edcPushLog, rf.Name, 1, findSubject)
						}
					}
				}
			}

			// 筛选
			data["isScreen"] = getValue(edcPushLog, "isScreen", 1, findSubject)
			data["screenTime"] = getValue(edcPushLog, "screenTime", 1, findSubject)
			data["icfTime"] = getValue(edcPushLog, "icfTime", 1, findSubject)

		} else if edcPush.Source == 2 { // 发药
			// 筛选访视
			visit := models.VisitCycleInfo{}
			for _, info := range visitCycle.Infos {
				if findDispensing.VisitInfo.VisitCycleInfoID == info.ID {
					visit = info
				}
			}
			data["visitNum"] = visit.Number

			for _, sbj := range edcPushLog.Content.SubjectData {
				// 药物号
				if sbj.Field == "drugNo" {
					var drugNameNumberStr strings.Builder
					// 已编号药物
					for _, dm := range sbj.DispensingMedicines {
						isBlindedDrug, _ := tools.IsBlindedDrug(findSubject.EnvironmentID, dm.Name)
						if isBlindedRole && isBlindedDrug {
							drugNameNumberStr.WriteString(tools.BlindData)
						} else {
							drugNameNumberStr.WriteString(dm.Name)
						}
						drugNameNumberStr.WriteString("(")
						drugNameNumberStr.WriteString(dm.Number)
						drugNameNumberStr.WriteString(")")
						drugNameNumberStr.WriteString(" ")
					}
					// 未编号药物
					for _, odm := range sbj.OtherDispensingMedicines {
						isBlindedDrug, _ := tools.IsBlindedDrug(findSubject.EnvironmentID, odm.Name)
						if isBlindedRole && isBlindedDrug {
							drugNameNumberStr.WriteString(tools.BlindData)
						} else {
							drugNameNumberStr.WriteString(odm.Name)
						}
						drugNameNumberStr.WriteString("(")
						drugNameNumberStr.WriteString(strconv.Itoa(odm.Count))
						drugNameNumberStr.WriteString("/")
						drugNameNumberStr.WriteString(odm.Batch)
						drugNameNumberStr.WriteString("/")
						drugNameNumberStr.WriteString(odm.ExpireDate)
						drugNameNumberStr.WriteString(")")
						drugNameNumberStr.WriteString(" ")
					}
					//实际使用的药物
					if sbj.RealDispensingMedicines != nil && len(sbj.RealDispensingMedicines) > 0 {
						drugNameNumberStr.WriteString("【")
						for _, rdm := range sbj.RealDispensingMedicines {
							isBlindedDrug, _ := tools.IsBlindedDrug(findSubject.EnvironmentID, rdm.Name)
							if isBlindedRole && isBlindedDrug {
								drugNameNumberStr.WriteString(tools.BlindData)
							} else {
								drugNameNumberStr.WriteString(rdm.Name)
							}
							drugNameNumberStr.WriteString("(")
							drugNameNumberStr.WriteString(rdm.Number)
							drugNameNumberStr.WriteString(")")
							drugNameNumberStr.WriteString(" ")
						}
						drugNameNumberStr.WriteString("】")
					}
					//实际使用的其它药物
					if sbj.RealOtherDispensingMedicines != nil && len(sbj.RealOtherDispensingMedicines) > 0 {
						drugNameNumberStr.WriteString("【")
						for _, rodm := range sbj.RealOtherDispensingMedicines {
							isBlindedDrug, _ := tools.IsBlindedDrug(findSubject.EnvironmentID, rodm.Name)
							if isBlindedRole && isBlindedDrug {
								drugNameNumberStr.WriteString(tools.BlindData)
							} else {
								drugNameNumberStr.WriteString(rodm.Name)
							}
							drugNameNumberStr.WriteString("(")
							drugNameNumberStr.WriteString(strconv.Itoa(rodm.Count))
							drugNameNumberStr.WriteString("/")
							drugNameNumberStr.WriteString(rodm.Batch)
							drugNameNumberStr.WriteString("/")
							drugNameNumberStr.WriteString(rodm.ExpireDate)
							drugNameNumberStr.WriteString(")")
							drugNameNumberStr.WriteString(" ")
						}
						drugNameNumberStr.WriteString("】")
					}
					data["drugNo"] = drugNameNumberStr.String()
				}

				// 发药时间
				if sbj.Field == "drugTime" {
					data["drugTime"] = sbj.Value
				}
			}
		}

		// EDC返回结果
		if edcPushLog.Status == 1 || edcPushLog.Status == 5 {
			if edcPushLog.EecErrorCode != 0 {
				data["edcResult"] = edcErrorCodeValue(ctx, edcPushLog.EecErrorCode)
			} else {
				// 文本翻译
				if edcPushLog.ProcessingResults == "IRT对接项目，edc受试者编号前缀设置规则与IRT推送数据的受试者编号前缀不符" {
					data["edcResult"] = locales.Tr(ctx, "project_edc_irt_return")
				} else {
					data["edcResult"] = edcPushLog.ProcessingResults
				}
			}
		} else {
			data["edcResult"] = edcPushLog.HttpRspBody
		}

		dataArray = append(dataArray, data)
	}

	return map[string]interface{}{"formData": formData, "data": dataArray}, nil
}

// fields []models.EdcFiles
func (s *PushServices) Update(ctx *gin.Context, edcPushId string, edcPushLogId string, status string, message string, edcFields []interface{}) error {
	edcPushOID, _ := primitive.ObjectIDFromHex(edcPushId)
	edcPushLogOID, _ := primitive.ObjectIDFromHex(edcPushLogId)
	var edcPush models.EdcPush
	err := tools.Database.Collection("edc_push").FindOne(nil, bson.M{"_id": edcPushOID}).Decode(&edcPush)
	if err != nil {
		return errors.WithStack(err)
	}

	ss := 2            // 失败（推送表）
	pushLogSs := 5     // 入库失败（推送记录表）
	if status == "1" { // 成功
		ss = 1
		pushLogSs = 1
	} else if status == "3" { // 不处理
		ss = 5
		pushLogSs = 6
	}

	// 推送
	update := bson.M{
		"$set": bson.M{
			"status":             ss,        // 状态
			"edc_fields":         edcFields, // edc处理的字段
			"processing_results": message,   // 原因
		},
	}

	// 推送记录
	pushLogUpdate := bson.M{
		"$set": bson.M{
			"status":             pushLogSs, // 状态
			"processing_results": message,   // 原因
		},
	}

	// 非失效状态才能改推送表状态
	if (edcPush.Status != 3) || (edcPush.Status == 3 && ss == 1) || (edcPush.Status == 3 && ss == 5) {
		tools.Database.Collection("edc_push").UpdateOne(nil, bson.M{"_id": edcPushOID}, update)
		tools.Database.Collection("edc_push_log").UpdateOne(nil, bson.M{"_id": edcPushLogOID}, pushLogUpdate)
	}

	return nil
}

// 筛选动态表单的值(sign 1.取value 2.取明文 3.取暗文)
func getValue(edcPushLog models.EdcPushLog, itemOid string, sign int, subject models.Subject) interface{} {
	for _, sbj := range edcPushLog.Content.SubjectData {
		if sbj.Field == itemOid {
			if sign == 2 {
				return subject.Group
			} else if sign == 3 {
				return tools.BlindData
			} else {
				return sbj.Value
			}
		}
	}
	return ""
}

func determineVariable(fields []string, field string) bool {
	if fields == nil || len(fields) == 0 {
		return true
	} else {
		for _, f := range fields {
			if f == field {
				return true
			}
		}
		return false
	}
}

// EDC返回的errorCode翻译
func edcErrorCodeValue(ctx *gin.Context, edcErrorCode int) string {
	if edcErrorCode == 200 { //成功
		return locales.Tr(ctx, "edc_push_error_code_200")
	} else if edcErrorCode == 201 { //时间戳、签名和基础的参数错误
		return locales.Tr(ctx, "edc_push_error_code_201")
	} else if edcErrorCode == 202 { //项目编号错误
		return locales.Tr(ctx, "edc_push_error_code_202")
	} else if edcErrorCode == 203 { //环境参数错误
		return locales.Tr(ctx, "edc_push_error_code_203")
	} else if edcErrorCode == 204 { //中心编号错误
		return locales.Tr(ctx, "edc_push_error_code_204")
	} else if edcErrorCode == 205 { //edc中受试者重复存在了，需要人工处理
		return locales.Tr(ctx, "edc_push_error_code_205")
	} else if edcErrorCode == 206 { //受试者规则前缀规则不一致
		return locales.Tr(ctx, "edc_push_error_code_206")
	} else if edcErrorCode == 207 { //受试者已经存在
		return locales.Tr(ctx, "edc_push_error_code_207")
	} else if edcErrorCode == 208 { //受试者正在创建（有并发锁的控制，该请求会执行失败）
		return locales.Tr(ctx, "edc_push_error_code_208")
	} else if edcErrorCode == 209 { //edc版本未推送，无法处理受试者
		return locales.Tr(ctx, "edc_push_error_code_209")
	} else if edcErrorCode == 210 { //edc受试者姓名缩写设置问题
		return locales.Tr(ctx, "edc_push_error_code_210")
	} else if edcErrorCode == 211 { //edc添加受试者错误
		return locales.Tr(ctx, "edc_push_error_code_211")
	} else if edcErrorCode == 212 { //保存解析后的irt数据到edc受试者错误
		return locales.Tr(ctx, "edc_push_error_code_212")
	} else if edcErrorCode == 213 { //按edc配置解析IRT数据错误
		return locales.Tr(ctx, "edc_push_error_code_213")
	} else if edcErrorCode == 299 { //其他错误未直接定义和动态拼接的错误
		return locales.Tr(ctx, "edc_push_error_code_299")
	} else {
		return "-"
	}
}
