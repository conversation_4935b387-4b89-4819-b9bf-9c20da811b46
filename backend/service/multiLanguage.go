package service

import (
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"strconv"
	"strings"
	"time"
)

type MultiLanguageService struct{}

func (s *MultiLanguageService) GetName(ctx *gin.Context) ([]models.Language, error) {

	languageList := make([]models.Language, 0)

	var settingConfig map[string]interface{}
	err := tools.Database.Collection("setting_config").FindOne(nil, bson.M{"key": "multiLanguage"}).Decode(&settingConfig)
	if err != nil && err != mongo.ErrNoDocuments {
		return languageList, errors.WithStack(err)
	}

	if settingConfig != nil {
		dataList := settingConfig["data"].(primitive.A)
		for _, element := range dataList {
			if item, ok := element.(map[string]interface{}); ok {
				var language models.Language
				language.Code = item["code"].(string)
				language.Name = item["name"].(string)
				languageList = append(languageList, language)
			}
		}
	}

	return languageList, nil
}

func (s *MultiLanguageService) GetList(ctx *gin.Context, customerId string, projectId string) ([]models.ProjectMultiLanguageVo, error) {

	customerOID, _ := primitive.ObjectIDFromHex(customerId)
	projectOID, _ := primitive.ObjectIDFromHex(projectId)

	projectMultiLanguageVoList := make([]models.ProjectMultiLanguageVo, 0)

	//简体中文
	var projectMultiLanguageVoZh models.ProjectMultiLanguageVo
	projectMultiLanguageVoZh.ID = primitive.NewObjectID()
	projectMultiLanguageVoZh.CustomerID = customerOID
	projectMultiLanguageVoZh.ProjectID = projectOID
	projectMultiLanguageVoZh.Code = "zh"
	projectMultiLanguageVoZh.Language = "简体中文"
	projectMultiLanguageVoZh.Status = 1
	projectMultiLanguageVoZh.SharedSystemLibrary = 1
	projectMultiLanguageVoList = append(projectMultiLanguageVoList, projectMultiLanguageVoZh)

	//English
	var projectMultiLanguageVoEn models.ProjectMultiLanguageVo
	projectMultiLanguageVoEn.ID = primitive.NewObjectID()
	projectMultiLanguageVoEn.CustomerID = customerOID
	projectMultiLanguageVoEn.ProjectID = projectOID
	projectMultiLanguageVoEn.Code = "en"
	projectMultiLanguageVoEn.Language = "English"
	projectMultiLanguageVoEn.Status = 1
	projectMultiLanguageVoEn.SharedSystemLibrary = 1
	projectMultiLanguageVoList = append(projectMultiLanguageVoList, projectMultiLanguageVoEn)

	projectMultiLanguages := make([]models.ProjectMultiLanguage, 0)
	filter := bson.M{"project_id": projectOID, "customer_id": customerOID}
	opts := &options.FindOptions{
		Sort: bson.D{{"meta.created_at", -1}},
	}
	cursor, err := tools.Database.Collection("project_multi_language").Find(ctx, filter, opts)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if err := cursor.All(ctx, &projectMultiLanguages); err != nil {
		return nil, errors.WithStack(err)
	}
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}

	if projectMultiLanguages != nil && len(projectMultiLanguages) > 0 {
		for _, language := range projectMultiLanguages {
			var languageVo models.ProjectMultiLanguageVo
			languageVo.ID = language.ID
			languageVo.CustomerID = language.CustomerID
			languageVo.ProjectID = language.ProjectID
			languageVo.Code = language.Code
			languageVo.Language = language.Language
			languageVo.Status = language.Status
			languageVo.SharedSystemLibrary = language.SharedSystemLibrary

			translateFilter := bson.M{"customer_id": customerOID, "project_id": projectOID, "language_id": language.ID, "name": bson.M{"$ne": ""}}
			count, err := tools.Database.Collection("project_multi_Language_Translate").CountDocuments(ctx, translateFilter)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			languageVo.TranslationQuantity = count
			projectMultiLanguageVoList = append(projectMultiLanguageVoList, languageVo)
		}
	}

	return projectMultiLanguageVoList, nil
}

func (s *MultiLanguageService) Add(ctx *gin.Context, customerId string, projectId string, projectMultiLanguage models.ProjectMultiLanguage) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		projectMultiLanguage.ID = primitive.NewObjectID()
		projectMultiLanguage.Meta.CreatedAt = time.Duration(time.Now().Unix())
		if _, err := tools.Database.Collection("project_multi_language").InsertOne(sctx, projectMultiLanguage); err != nil {
			return nil, errors.WithStack(err)
		}

		// 轨迹
		err := insertMultiLanguageLog(ctx, sctx, 1, models.ProjectMultiLanguage{}, projectMultiLanguage, projectMultiLanguage.ProjectID, projectMultiLanguage.ID)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *MultiLanguageService) Update(ctx *gin.Context, customerId string, projectId string, projectMultiLanguage models.ProjectMultiLanguage) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		customerOID, _ := primitive.ObjectIDFromHex(customerId)
		projectOID, _ := primitive.ObjectIDFromHex(projectId)

		filter := bson.M{"project_id": projectOID, "customer_id": customerOID, "_id": projectMultiLanguage.ID}

		oldProjectMultiLanguage := models.ProjectMultiLanguage{}
		err := tools.Database.Collection("project_multi_language").FindOne(nil, filter).Decode(&oldProjectMultiLanguage)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		update := bson.M{"$set": bson.M{"code": projectMultiLanguage.Code, "language": projectMultiLanguage.Language,
			"status": projectMultiLanguage.Status, "shared_system_library": projectMultiLanguage.SharedSystemLibrary,
			"meta.updated_at": time.Duration(time.Now().Unix()),
		}}
		_, err = tools.Database.Collection("project_multi_language").UpdateOne(sctx, filter, update)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		// 轨迹
		err = insertMultiLanguageLog(ctx, sctx, 2, oldProjectMultiLanguage, projectMultiLanguage, projectMultiLanguage.ProjectID, projectMultiLanguage.ID)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *MultiLanguageService) Delete(ctx *gin.Context, id string) error {
	oid, _ := primitive.ObjectIDFromHex(id)

	oldProjectMultiLanguage := models.ProjectMultiLanguage{}
	err := tools.Database.Collection("project_multi_language").FindOne(nil, bson.M{"_id": oid}).Decode(&oldProjectMultiLanguage)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	_, err = tools.Database.Collection("project_multi_language").DeleteOne(ctx, bson.M{"_id": oid})
	if err != nil {
		return errors.WithStack(err)
	}

	// 轨迹
	newProjectMultiLanguage := oldProjectMultiLanguage
	newProjectMultiLanguage.Language = ""
	newProjectMultiLanguage.Status = 0
	newProjectMultiLanguage.SharedSystemLibrary = 0
	err = insertMultiLanguageLog(ctx, nil, 3, newProjectMultiLanguage, oldProjectMultiLanguage, oldProjectMultiLanguage.ProjectID, oldProjectMultiLanguage.ID)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (s *MultiLanguageService) GetTranslateList(ctx *gin.Context, customerId string, projectId string, projectMultiLanguageTranslate models.ProjectMultiLanguageTranslate) ([]models.ProjectMultiLanguageTranslate, error) {

	customerOID, _ := primitive.ObjectIDFromHex(customerId)
	projectOID, _ := primitive.ObjectIDFromHex(projectId)

	projectMultiLanguageTranslateList := make([]models.ProjectMultiLanguageTranslate, 0)
	filter := bson.M{"project_id": projectOID, "customer_id": customerOID}
	if projectMultiLanguageTranslate.EnvironmentID != primitive.NilObjectID {
		filter["env_id"] = projectMultiLanguageTranslate.EnvironmentID
	}
	if projectMultiLanguageTranslate.CohortID != primitive.NilObjectID {
		filter["cohort_id"] = projectMultiLanguageTranslate.CohortID
	}
	if projectMultiLanguageTranslate.LanguageID != primitive.NilObjectID {
		filter["language_id"] = projectMultiLanguageTranslate.LanguageID
	}
	if projectMultiLanguageTranslate.LanguageLibrary != 0 {
		filter["language_library"] = projectMultiLanguageTranslate.LanguageLibrary
	}
	if projectMultiLanguageTranslate.PagePath != nil && len(projectMultiLanguageTranslate.PagePath) > 0 {
		filter["page_path"] = projectMultiLanguageTranslate.PagePath
	}
	if projectMultiLanguageTranslate.Type != "" {
		filter["type"] = projectMultiLanguageTranslate.Type
	}

	cursor, err := tools.Database.Collection("project_multi_Language_Translate").Find(ctx, filter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if err := cursor.All(ctx, &projectMultiLanguageTranslateList); err != nil {
		return nil, errors.WithStack(err)
	}
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}

	return projectMultiLanguageTranslateList, nil
}

func (s *MultiLanguageService) UpdateTranslate(ctx *gin.Context, customerId string, projectId string, translateVo models.ProjectMultiLanguageTranslateVo) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		customerOID, _ := primitive.ObjectIDFromHex(customerId)
		projectOID, _ := primitive.ObjectIDFromHex(projectId)

		filter := bson.M{
			"key":              translateVo.Key,
			"project_id":       projectOID,
			"customer_id":      customerOID,
			"language_id":      translateVo.LanguageID,
			"env_id":           translateVo.EnvironmentID,
			"cohort_id":        translateVo.CohortID,
			"language_library": translateVo.LanguageLibrary,
		}

		_id := primitive.NewObjectID()
		types := 1
		oldTranslate := models.ProjectMultiLanguageTranslateVo{}
		err := tools.Database.Collection("project_multi_Language_Translate").FindOne(ctx, filter).Decode(&oldTranslate)
		if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errors.WithStack(err)
		}
		if oldTranslate.ID != primitive.NilObjectID {
			types = 2
			_id = oldTranslate.ID
		}
		update := bson.M{"$set": bson.M{"_id": _id, "name": translateVo.Name, "page_path": translateVo.PagePath, "type": translateVo.Type}}
		opts := options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)
		var newTranslate models.ProjectMultiLanguageTranslateVo
		err = tools.Database.Collection("project_multi_Language_Translate").FindOneAndUpdate(ctx, filter, update, opts).Decode(&newTranslate)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 轨迹
		OID := translateVo.ProjectID
		if translateVo.EnvironmentID != primitive.NilObjectID {
			OID = translateVo.EnvironmentID
		}
		if translateVo.CohortID != primitive.NilObjectID {
			OID = translateVo.CohortID
		}
		err = insertMultiLanguageTranslateLog(ctx, sctx, types, oldTranslate, translateVo, OID, newTranslate.ID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *MultiLanguageService) GetHistoryList(ctx *gin.Context, id string, projectId string, start int, limit int, modules string) (map[string]interface{}, error) {
	projectOId, _ := primitive.ObjectIDFromHex(projectId)
	type OperationLog struct {
		ID        primitive.ObjectID              `json:"id" bson:"_id"`
		Operator  primitive.ObjectID              `json:"operator" bson:"operator"`
		OID       primitive.ObjectID              `bson:"oid" json:"oid"`
		Time      time.Duration                   `json:"time" bson:"time"`
		Type      int                             `json:"type" bson:"type"` //1新增 2编辑 3删除  5编辑	6复制
		Module    string                          `json:"module" bson:"module"`
		Fields    []models.OperationLogFieldGroup `json:"fields" bson:"fields"`
		Mark      []models.Mark                   `json:"mark" bson:"mark"`
		User      models.User                     `json:"user" bson:"user"`
		CloudType int                             `json:"cloudType" bson:"cloud_type"` //1用户删除 2用户禁用
	}
	var operationLogs []OperationLog

	var project models.Project
	if err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOId}).Decode(&project); err != nil {
		return nil, errors.WithStack(err)
	}
	//查询出所有的cohortId

	oIds := make([]primitive.ObjectID, 0)
	oIds = append(oIds, project.ID)
	if project.Type != 1 {
		for _, environment := range project.Environments {
			oIds = append(oIds, environment.ID)
			for _, cohort := range environment.Cohorts {
				oIds = append(oIds, cohort.ID)
			}
		}
	}

	filter := bson.M{"oid": bson.M{"$in": oIds}}

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "user",
			"localField":   "operator",
			"foreignField": "_id",
			"as":           "user",
		}}},
		{{"$unwind", "$user"}},
		{{"$sort", bson.D{{"time", -1}}}},
	}
	if len(modules) > 0 {
		pipeline = append(pipeline, bson.D{{"$match", bson.M{"module": bson.M{"$in": strings.Split(modules, ",")}}}})
	}
	var counts []map[string]interface{}
	pipeline = append(pipeline, bson.D{{"$count", "count"}})

	cursor, err := tools.Database.Collection("operation_log").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &counts)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	count := int32(0)
	if len(counts) > 0 {
		count = counts[0]["count"].(int32)
	}
	ctx.Set("isRoomBlind", false)
	ctx.Set("isBlindedRole", false)
	ctx.Set("isBlind", false)

	pipeline[len(pipeline)-1] = bson.D{{"$skip", start}}
	pipeline = append(pipeline, bson.D{{"$limit", limit}})
	cursor, err = tools.Database.Collection("operation_log").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &operationLogs)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	RetOperationLogs := []models.RetOperationLog{}

	user, err := tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))

	//num := int32(0)
	for _, log := range operationLogs {
		var context []string
		labelName := ""
		oldValue := ""
		newValue := ""
		language := ""

		if log.Mark != nil && len(log.Mark) > 0 {
			for _, mark := range log.Mark {
				if mark.Label != "" {
					value := mark.Value
					if mark.Label == "operation_log.module.project_multi_language_translate.language" {
						if log.Module == "operation_log.module.project_multi_language_translate" {
							language = value
						}
					} else if mark.Label == "operation_log.module.project_multi_language_batch_upload.language" {
						if log.Module == "operation_log.module.project_multi_language_batch_upload" {
							language = value
						}
					}
				}
			}
		}

		if language != "" {
			context = append(context, locales.Tr(ctx, log.Module)+language)
		} else {
			context = append(context, locales.Tr(ctx, log.Module))
		}

		lang := locales.Lang(ctx)
		for _, field := range log.Fields {
			var tmp string
			labelName = locales.Tr(ctx, field.TranKey)

			if field.Old.Type != 3 {
				if field.Old.Type == 16 {
					oldValue = converRoles(ctx, field.Old.Value)
				} else if field.Old.Type == 17 || field.Old.Type == 18 ||
					field.Old.Type == 19 || field.Old.Type == 20 || field.Old.Type == 22 || field.Old.Type == 23 || field.Old.Type == 24 || field.Old.Type == 25 {
					oldValue = converContentConfiguration(ctx, field.Old.Type, field.Old.Value)
				} else if field.Old.Type == 21 {
					value := field.Old.Value
					oldValue = strconv.Itoa(int(value.(int32)))
				} else if field.Old.Type == 30 {
					if lang == "zh" {
						oldValue = models.TypeHandle(ctx, 7, field.Old.Value, nil, log.OID, field.TranKey)
					} else {
						oldValue = models.TypeHandle(ctx, 7, field.Old.ENValue, nil, log.OID, field.TranKey)
					}
				} else {
					if field.Old.Type == 1 && (field.TranKey == "operation_log.project_multi_language.status" || field.TranKey == "operation_log.project_multi_language.sharedSystemLibrary") {
						if field.Old.Value.(int32) == 1 {
							oldValue = locales.Tr(ctx, "common.yes")
						} else if field.Old.Value.(int32) == 0 {
							oldValue = locales.Tr(ctx, "common.no")
						}
					} else {
						oldValue = models.TypeHandle(ctx, field.Old.Type, field.Old.Value, nil, log.OID, field.TranKey)
					}
				}
			} else {
				oldValue = converCountry(ctx, field.Old.Value)
			}
			if field.New.Type != 3 {
				if field.New.Type == 16 {
					newValue = converRoles(ctx, field.New.Value)
				} else if field.New.Type == 17 || field.New.Type == 18 ||
					field.New.Type == 19 || field.New.Type == 20 || field.New.Type == 22 || field.New.Type == 23 || field.New.Type == 24 || field.New.Type == 25 {
					newValue = converContentConfiguration(ctx, field.New.Type, field.New.Value)
				} else if field.New.Type == 21 {
					value := field.New.Value
					newValue = strconv.Itoa(int(value.(int32)))
				} else if field.New.Type == 30 {
					if lang == "zh" {
						newValue = models.TypeHandle(ctx, 7, field.New.Value, nil, log.OID, field.TranKey)
					} else {
						newValue = models.TypeHandle(ctx, 7, field.New.ENValue, nil, log.OID, field.TranKey)
					}
				} else {
					if field.New.Type == 1 && (field.TranKey == "operation_log.project_multi_language.status" || field.TranKey == "operation_log.project_multi_language.sharedSystemLibrary") {
						if field.New.Value.(int32) == 1 {
							newValue = locales.Tr(ctx, "common.yes")
						} else if field.New.Value.(int32) == 0 {
							newValue = locales.Tr(ctx, "common.no")
						}
					} else {
						newValue = models.TypeHandle(ctx, field.New.Type, field.New.Value, nil, log.OID, field.TranKey)
					}
				}
			} else {
				newValue = converCountry(ctx, field.New.Value)
			}
			if log.Type != 1 && oldValue == newValue && field.Old.Type != 7 && (field.Old.Mark == 0 || field.New.Mark == 0) {
				// 值未改变的 不需要展示
				continue
			}

			if log.Type == 1 {
				if field.TranKey == "operation_log.project_multi_language_translate.label" {
					tmp = language + " : " + newValue
				} else {
					tmp = labelName + " : " + newValue
				}
			} else if log.Type == 2 {
				if field.TranKey == "operation_log.project_multi_language.projectName" ||
					field.TranKey == "operation_log.project_multi_language.language" ||
					field.TranKey == "operation_log.project_multi_language_translate.projectName" ||
					field.TranKey == "operation_log.project_multi_language_translate.languageLibrary" ||
					field.TranKey == "operation_log.project_multi_language_translate.pagePath" ||
					field.TranKey == "operation_log.project_multi_language_translate.envName" ||
					field.TranKey == "operation_log.project_multi_language_translate.cohortName" ||
					field.TranKey == "operation_log.project_multi_language_translate.stageName" ||
					field.TranKey == "operation_log.project_multi_language_translate.type" ||
					field.TranKey == "operation_log.project_multi_language_translate.key" ||
					field.TranKey == "operation_log.project_multi_language_translate.name" {
					tmp = labelName + " : " + newValue
				} else {
					if field.TranKey == "operation_log.project_multi_language_translate.label" {
						tmp = language + " : " + oldValue + " -> " + newValue
					} else {
						tmp = labelName + " : " + oldValue + " -> " + newValue
					}
				}
			} else if log.Type == 3 {
				if field.TranKey == "operation_log.project_multi_language.projectName" {
					tmp = labelName + " : " + newValue
				} else {
					if field.TranKey == "operation_log.project_multi_language.language" ||
						field.TranKey == "operation_log.project_multi_language.status" ||
						field.TranKey == "operation_log.project_multi_language.sharedSystemLibrary" {
						tmp = labelName + " : " + newValue + " -> "
					} else {
						tmp = labelName + " : " + oldValue + " -> " + newValue
					}
				}
			} else if log.Type == 4 {
				tmp = labelName
			} else if log.Type == 5 {
				tmp = labelName + " : " + newValue
			} else if log.Type == 6 {
				tmp = labelName + " : " + newValue
			} else if log.Type == 7 {
				tmp = labelName + " : " + oldValue + " -> " + newValue
			} else if log.Type == 8 {
				tmp = labelName + " : " + oldValue + " -> " + newValue
			} else if log.Type == 10 {
				tmp = labelName + " : " + newValue
			} else if log.Type == 11 {
				tmp = labelName + " : " + newValue
			} else if log.Type == 12 {
				tmp = labelName + " : " + newValue
			} else if log.Type == 13 {
				tmp = labelName + " : " + newValue
			} else if log.Type == 14 {
				tmp = labelName + " : " + newValue
			} else if log.Type == 15 {
				tmp = labelName + " : " + newValue
			} else if log.Type == 16 {
				tmp = labelName + " : " + newValue
			} else if log.Type == 17 {
				tmp = labelName + " : " + newValue
			}

			context = append(context, tmp)

		}
		userName := ""
		if log.User.Unicode == 0 {
			userName = log.User.Name
		} else {
			userName = fmt.Sprintf("%s(%d)", log.User.Name, log.User.Unicode)
		}

		location := "Asia/Shanghai"
		if len(users) > 0 && users[0].Settings.Tz != "" {
			location = users[0].Settings.Tz
		}
		timeStr, err := tools.GetLocationUtc(location, int64(log.Time))
		if err != nil {
			panic(err)
		}

		RetOperationLogs = append(RetOperationLogs, models.RetOperationLog{
			ID:            log.ID,
			UserEmail:     log.User.Email,
			UserName:      userName,
			OperationType: locales.Tr(ctx, models.OperationLogMap["operation_type"].(bson.M)[convertor.ToString(log.Type)].(string)),
			Time:          log.Time,
			TimeStr:       timeStr,
			Context:       context,
		})

	}
	return map[string]interface{}{
		"items": RetOperationLogs,
		"total": count,
	}, nil
}

func (s *MultiLanguageService) UploadTranslate(ctx *gin.Context) error {
	customerOID, _ := primitive.ObjectIDFromHex(ctx.PostForm("customerId"))
	projectOID, _ := primitive.ObjectIDFromHex(ctx.PostForm("projectId"))
	envOID, _ := primitive.ObjectIDFromHex(ctx.PostForm("envId"))
	cohortOID, _ := primitive.ObjectIDFromHex(ctx.PostForm("cohortId"))
	languageOID, _ := primitive.ObjectIDFromHex(ctx.PostForm("languageId"))
	languageLibrary, _ := strconv.Atoi(ctx.PostForm("languageLibrary"))
	keyInfoMapStr := ctx.PostForm("keyInfoMap")
	// 定义一个 map 来存储解析后的数据
	type KeyInfo struct {
		Path []string `json:"path"`
		Type string   `json:"type"`
	}
	var keyInfoMap map[string]KeyInfo

	// 将 JSON 字符串解析为 map
	err := json.Unmarshal([]byte(keyInfoMapStr), &keyInfoMap)
	if err != nil {
		return errors.WithStack(err)
	}

	var projectMultiLanguage models.ProjectMultiLanguage
	err = tools.Database.Collection("project_multi_language").FindOne(ctx, bson.M{"_id": languageOID}).Decode(&projectMultiLanguage)
	if err != nil {
		return errors.WithStack(err)
	}

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		files, err := ctx.FormFile("file")
		if err != nil {
			return nil, errors.WithStack(err)
		}
		file, err := files.Open()
		if err != nil {
			return nil, errors.WithStack(err)
		}
		f, _ := excelize.OpenReader(file)
		rows, _ := f.GetRows(f.GetSheetList()[0])

		if len(rows) > 2 {
			headerRow := rows[0]
			translatePosition := -1
			keyPosition := -1
			for index, v := range headerRow {
				if v == projectMultiLanguage.Language {
					translatePosition = index
				}
				if strings.ToLower(v) == "key" {
					keyPosition = index
				}
			}

			for _, v := range rows[1:] {
				// 如果第四列是空单元格，读取不到
				if len(v) == 3 {
					v = append(v, "")
				}

				key := v[keyPosition]
				translateName := v[translatePosition]
				// 遇到空翻译就跳过
				if translateName != "" {
					filter := bson.M{
						"customer_id":      customerOID,
						"project_id":       projectOID,
						"env_id":           envOID,
						"cohort_id":        cohortOID,
						"language_id":      languageOID,
						"language_library": languageLibrary,
						"key":              key,
					}

					update := bson.M{
						"$set": bson.M{
							"name": translateName,
						},
					}

					// 设置选项：按_id降序排序，确保只更新最新文档
					opts := options.FindOneAndUpdate().
						SetSort(bson.D{{Key: "_id", Value: -1}}). // 按_id倒序（最新）
						SetProjection(bson.M{"_id": 1})           // 只返回_id字段提高效率

					// 尝试更新最新匹配文档
					var resultDoc bson.M
					err := tools.Database.Collection("project_multi_Language_Translate").
						FindOneAndUpdate(sctx, filter, update, opts).
						Decode(&resultDoc)

					// 处理更新结果
					if err != nil {
						if errors.Is(err, mongo.ErrNoDocuments) {
							// 没有匹配文档，执行插入操作
							doc := bson.M{
								"customer_id":      customerOID,
								"project_id":       projectOID,
								"env_id":           envOID,
								"cohort_id":        cohortOID,
								"language_id":      languageOID,
								"language_library": languageLibrary,
								"page_path":        keyInfoMap[key].Path,
								"type":             keyInfoMap[key].Type,
								"key":              key,
								"name":             translateName,
							}
							_, err := tools.Database.Collection("project_multi_Language_Translate").InsertOne(sctx, doc)
							if err != nil {
								return nil, errors.WithStack(err)
							}
						} else {
							// 其他错误
							return nil, errors.WithStack(err)
						}
					}
				}

			}

			// 轨迹
			OID := projectOID
			if envOID != primitive.NilObjectID {
				OID = envOID
			}
			if cohortOID != primitive.NilObjectID {
				OID = cohortOID
			}
			err = insertMultiLanguageBatchImportLog(ctx, sctx, 1, projectOID, files.Filename, projectMultiLanguage.Language, OID, primitive.NilObjectID)
		} else {
			return nil, tools.BuildServerError(ctx, "upload_translate_info")
		}
		return nil, nil
	}

	err = tools.Transaction(callback)
	if err != nil {
		return err
	}

	return nil
}

func (s *MultiLanguageService) GetTranslateMap(ctx *gin.Context, customerId string, projectId string, params models.ProjectMultiLanguageTranslate) (map[string]string, error) {
	_customerId, _ := primitive.ObjectIDFromHex(customerId)
	_projectId, _ := primitive.ObjectIDFromHex(projectId)
	filter := bson.M{"project_id": _projectId, "customer_id": _customerId, "language_id": params.LanguageID}
	pipeline := mongo.Pipeline{
		{{"$match", filter}},
		{{"$project", bson.M{"key": 1, "name": 1}}},
	}
	data := make([]models.ProjectMultiLanguageTranslate, 0)
	result := make(map[string]string, 0)
	cursor, err := tools.Database.Collection("project_multi_Language_Translate").Aggregate(ctx, pipeline, nil)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if err := cursor.All(ctx, &data); err != nil {
		return result, errors.WithStack(err)
	}
	slice.ForEach(data, func(index int, item models.ProjectMultiLanguageTranslate) {
		result[item.Key] = item.Name
	})
	return result, nil
}
