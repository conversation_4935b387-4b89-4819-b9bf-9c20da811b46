package service

import (
	"bytes"
	"clinflash-irt/data"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"sync"

	"github.com/duke-git/lancet/v2/slice"

	"strings"
	"time"

	"github.com/alexmullins/zip"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/pkg/errors"
	"github.com/wxnacy/wgo/arrays"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ReportService struct {
}

func (s *ReportService) GetReports(ctx *gin.Context, projectId string, envId string) ([]*data.Report, error) {
	projectOid, _ := primitive.ObjectIDFromHex(projectId)
	envOid, _ := primitive.ObjectIDFromHex(envId)
	user, err := tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// 查询编码配置
	var barcodeRule models.BarcodeRule
	barcodeRuleFilter := bson.M{"project_id": projectOid, "env_id": envOid}
	_ = tools.Database.Collection("barcode_rule").FindOne(nil, barcodeRuleFilter).Decode(&barcodeRule)

	attributes := make([]models.Attribute, 0)
	cursor, err := tools.Database.Collection("attribute").Find(nil, bson.M{"env_id": envOid})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &attributes)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var dataReports []data.Report
	_, ok := slice.Find(attributes, func(index int, item models.Attribute) bool {
		return item.AttributeInfo.Random
	})

	if ok {
		dataReports = data.Reports
	} else {
		dataReports = data.OnlyDispensingReports
	}
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOid}).Decode(&project)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var reports []*data.Report
	for _, report := range dataReports {
		r := report
		if r.Type == data.ReportTypeRandomizationSimulationResult {
			if project.Type == 2 {
				r.DefaultFields = slice.InsertAt(r.DefaultFields, 5, data.ReportFields{Key: "report.attributes.random.cohort"})
			} else if project.Type == 3 {
				r.DefaultFields = slice.InsertAt(r.DefaultFields, 5, data.ReportFields{Key: "report.attributes.random.stage"})
			}
		}

		// 研究产品盲底导出（如果是自动编码则剔除序列号字段）
		if r.Type == data.ReportTypeSourceIPExport && barcodeRule.CodeRule == 1 {
			var reportFields []data.ReportFields
			for _, df := range r.DefaultFields {
				if df.Key != "report.attributes.research.medicine.serial-number" {
					reportFields = append(reportFields, df)
				}
			}
			r.DefaultFields = reportFields
		}

		// 受试者明细（如果是有一个开启了筛选）
		_, b := slice.Find(attributes, func(index int, item models.Attribute) bool {
			return item.AttributeInfo.IsScreen
		})
		//受试者明细报表，如果都没开启筛选流程就提出筛选时间
		if r.Type == data.ReportTypeRandomizeReport && !b {
			mfs := make([]data.MultiDefaultFields, 0)
			for _, mf := range r.MultiDefaultFields {
				reportFields := make([]data.ReportFields, 0)
				for _, df := range mf.DefaultFields {
					if df.Key != data.ReportAttributesRandomScreenTime {
						reportFields = append(reportFields, df)

					}
				}
				mf.DefaultFields = reportFields
				mfs = append(mfs, mf)
			}
			r.MultiDefaultFields = mfs
		}
		//受试者统计报表 开启筛选的两个字段
		if r.Type == data.ReportTypeSubjectStatisticsExport && !b {
			mfs := make([]data.MultiDefaultFields, 0)
			for _, mf := range r.MultiDefaultFields {
				reportFields := make([]data.ReportFields, 0)
				for _, df := range mf.DefaultFields {
					if df.Key != "subject.status.screen.success" && df.Key != "subject.status.screen.fail" {
						reportFields = append(reportFields, df)
					}
				}
				mf.DefaultFields = reportFields
				mfs = append(mfs, mf)
			}
			r.MultiDefaultFields = mfs
		}
		reports = append(reports, &r)
	}

	//获取报表的最新下载时间
	var histories []bson.M
	stage := mongo.Pipeline{
		{{"$match", bson.D{
			{"project_id", projectOid},
			{"env_id", envOid},
			{"user_id", user.ID},
		}}},
		{{"$sort", bson.D{
			{"download_time", -1},
		}}},
		{{"$group", bson.D{
			{"_id", "$report_type"},
			{"download_time", bson.M{
				"$first": "$download_time",
			}},
		}}},
	}
	cursor, err = tools.Database.Collection(models.ReportHistoryTableName).Aggregate(context.Background(), stage)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if err = cursor.All(context.Background(), &histories); err != nil {
		return nil, errors.WithStack(err)
	}
	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))

	for _, report := range reports {
		for _, history := range histories {
			if report.Type == int(history["_id"].(int32)) {
				downloadTime := history["download_time"].(int64)

				location := "Asia/Shanghai"
				if len(users) > 0 && users[0].Settings.Tz != "" {
					location = users[0].Settings.Tz
				}
				timeStr, err := tools.GetLocationUtc(location, downloadTime)
				if err != nil {
					panic(err)
				}
				report.LatestDownloadTime = timeStr
				break
			}
		}
	}

	//sort.SliceStable(reports, func(i, j int) bool {
	//	return reports[i].LatestDownloadTime > reports[j].LatestDownloadTime
	//})
	return reports, nil
}

func (s *ReportService) GetTemplate(ctx *gin.Context, templateType int, projectId string, envId string) ([]*models.CustomTemplate, error) {
	user, err := tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	projectOid, _ := primitive.ObjectIDFromHex(projectId)
	envOid, _ := primitive.ObjectIDFromHex(envId)
	var templates []*models.CustomTemplate
	filter := bson.M{
		"type":       templateType,
		"project_id": projectOid,
		"env_id":     envOid,
		"user_id":    user.ID,
	}
	opts := &options.FindOptions{
		Sort: bson.M{
			"meta.created_at": -1,
		},
	}

	cursor, err := tools.Database.Collection(models.CustomTemplateTable).Find(context.Background(), filter, opts)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if err = cursor.All(context.Background(), &templates); err != nil {
		return nil, errors.WithStack(err)
	}

	return templates, nil
}

func (s *ReportService) AddTemplate(ctx *gin.Context, template models.CustomTemplate) error {
	user, err := tools.Me(ctx)
	if err != nil {
		return errors.WithStack(err)
	}

	if template.Name == "默认模板" || template.Name == "Default Template" {
		return tools.BuildServerError(ctx, "report.template.name_exist")
	}
	if strings.TrimSpace(template.Name) == "" {
		return tools.BuildServerError(ctx, "report.template.name_invalid")
	}

	count, err := tools.Database.Collection(models.CustomTemplateTable).CountDocuments(context.Background(), bson.M{
		"project_id": template.ProjectID,
		"env_id":     template.EnvID,
		"user_id":    user.ID,
		"type":       template.Type,
		"name":       template.Name,
	})
	if count > 0 {
		return tools.BuildServerError(ctx, "report.template.name_exist")
	}

	template.ID = primitive.NewObjectID()
	template.UserId = user.ID
	template.Meta = models.Meta{
		CreatedBy: user.ID,
		CreatedAt: time.Duration(time.Now().Unix()),
	}
	if _, err := tools.Database.Collection(models.CustomTemplateTable).InsertOne(context.Background(), template); err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (s *ReportService) UpdateTemplate(ctx *gin.Context, id string, template models.CustomTemplate) error {
	user, err := tools.Me(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	oid, _ := primitive.ObjectIDFromHex(id)

	if template.Name == "默认模板" || template.Name == "Default Template" {
		return tools.BuildServerError(ctx, "report.template.name_exist")
	}
	if strings.TrimSpace(template.Name) == "" {
		return tools.BuildServerError(ctx, "report.template.name_invalid")
	}

	var curTemplate models.CustomTemplate
	err = tools.Database.Collection(models.CustomTemplateTable).FindOne(context.Background(), bson.M{
		"_id": oid,
	}).Decode(&curTemplate)
	if err != nil {
		return errors.WithStack(err)
	}

	count, err := tools.Database.Collection(models.CustomTemplateTable).CountDocuments(context.Background(), bson.M{
		"_id": bson.M{
			"$ne": oid,
		},
		"project_id": template.ProjectID,
		"env_id":     template.EnvID,
		"user_id":    user.ID,
		"type":       curTemplate.Type,
		"name":       template.Name,
	})
	if count > 0 {
		return tools.BuildServerError(ctx, "report.template.name_exist")
	}

	update := bson.M{
		"$set": bson.M{
			"name":            template.Name,
			"fields":          template.Fields,
			"meta.updated_at": time.Duration(time.Now().Unix()),
			"meta.updated_by": user.ID,
		},
	}

	if _, err := tools.Database.Collection(models.CustomTemplateTable).UpdateByID(context.Background(), oid, update); err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (s *ReportService) DeleteTemplate(ctx *gin.Context, id string) error {
	oid, _ := primitive.ObjectIDFromHex(id)

	if _, err := tools.Database.Collection(models.CustomTemplateTable).DeleteOne(context.Background(), bson.M{"_id": oid}); err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (s *ReportService) CopyTemplate(ctx *gin.Context, id string) error {
	user, err := tools.Me(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	oid, _ := primitive.ObjectIDFromHex(id)
	var template models.CustomTemplate

	if err := tools.Database.Collection(models.CustomTemplateTable).FindOne(context.Background(), bson.M{"_id": oid}).Decode(&template); err != nil {
		return errors.WithStack(err)
	}

	times := 1
	newTemplate := models.CustomTemplate{
		ID:        primitive.NewObjectID(),
		ProjectID: template.ProjectID,
		EnvID:     template.EnvID,
		Type:      template.Type,
		Fields:    template.Fields,
		UserId:    user.ID,
		Meta: models.Meta{
			CreatedBy: user.ID,
			CreatedAt: time.Duration(time.Now().Unix()),
		},
	}
	for {
		newTemplate.Name = fmt.Sprintf("%s(cpoy%d)", template.Name, times)
		count, err := tools.Database.Collection(models.CustomTemplateTable).CountDocuments(context.Background(), bson.M{
			"project_id": template.ProjectID,
			"env_id":     template.EnvID,
			"type":       template.Type,
			"name":       newTemplate.Name,
			"user_id":    user.ID,
		})
		if err != nil {
			return errors.WithStack(err)
		}

		if count != 0 {
			times += 1
		} else {
			break
		}
	}

	if _, err := tools.Database.Collection(models.CustomTemplateTable).InsertOne(context.Background(), newTemplate); err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (s *ReportService) Export(ctx *gin.Context, reportType int, projectId string, envId string, customTemplateId string, cohortID string, roleId string, startTime int, endTime int, exportTypes string, cipher string, simulateRandomId string) error {
	user, err := tools.Me(ctx)
	if err != nil {
		return err
	}
	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))

	timezone := "08:00"
	offset := float64(8)
	if len(users) > 0 && users[0].Settings.Tz != "" {
		timezone, err = tools.GetLocationTimeZone(users[0].Settings.Tz)
		if err != nil {
			return errors.WithStack(err)
		}
		offset, err = tools.GetLocationFloat(users[0].Settings.Tz)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	if err != nil {
		return errors.WithStack(err)
	}
	//if offset > 0 {
	//	timezone = "+" + timezone
	//}

	poid, _ := primitive.ObjectIDFromHex(projectId)
	eoid, _ := primitive.ObjectIDFromHex(envId)
	now := time.Now().In(time.FixedZone(fmt.Sprintf("UTC%s", timezone), int(offset*60*60)))

	var (
		filename string
		content  []byte
	)

	//压缩包名称
	b := new(bytes.Buffer)

	var parameter map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&parameter, binding.JSON)

	isBlindedRole, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return errors.WithStack(err)
	}
	ctx.Set("isBlindedRole", isBlindedRole)

	//根据报表类型选择不同的导出方法
	if reportType == data.ReportTypeSiteIPStatisticsExport { //中心单品统计导出
		filename, content, err = ExportSiteIPStatisticsExport(ctx, projectId, envId, roleId, now, customTemplateId)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if reportType == data.ReportTypeDepotIPStatisticsExport { //库房单品统计导出
		filename, content, err = ExportDepotIPStatisticsExport(ctx, projectId, envId, roleId, now, customTemplateId)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if reportType == data.ReportTypeUserRoleStatus {
		filename, content, err = ExportUserRoleStatusReport(ctx, projectId, envId, now)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if reportType == data.ReportTypeProjectPermissionConfigurationExport {
		var projectRolePermissionIds []string
		for _, i := range parameter["projectRolePermissionIds"].([]interface{}) {
			projectRolePermissionIds = append(projectRolePermissionIds, i.(string))
		}
		filename, content, err = ExportProjectPermissionConfigurationExport(ctx, projectId, envId, now, projectRolePermissionIds)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if reportType == data.ReportTypeConfigureReport {
		//cohortIds := make([]string, 0)
		//for _, i := range parameter["cohortIds"].([]interface{}) {
		//	cohortIds = append(cohortIds, i.(string))
		//}
		//if cohortIds != nil && len(cohortIds) > 0 {
		//	filename, content, err = ExportConfigureReportPlusieurs(ctx, projectId, envId, cohortIds, roleId, now)
		//	if err != nil {
		//		return errors.WithStack(err)
		//	}
		//	for _, coId := range cohortIds {
		//		OID, _ := primitive.ObjectIDFromHex(coId)
		//		var OperationLogFieldGroups []models.OperationLogFieldGroup
		//		marks := []models.Mark{}
		//		e := tools.SaveOperation(ctx, nil, "operation_log.module.configure_export", OID, 9, OperationLogFieldGroups, marks, OID)
		//		if e != nil {
		//			return errors.WithStack(err)
		//		}
		//	}
		//} else {
		//	//filename, content, err = ExportConfigureReport(ctx, projectId, envId, cohortID, roleId, now)
		//	filename, content, err = ExportConfigureReportPlusieurs(ctx, projectId, envId, cohortIds, roleId, now)
		//	if err != nil {
		//		return errors.WithStack(err)
		//	}
		//	OID, _ := primitive.ObjectIDFromHex(envId)
		//	var OperationLogFieldGroups []models.OperationLogFieldGroup
		//	marks := []models.Mark{}
		//	e := tools.SaveOperation(ctx, nil, "operation_log.module.configure_export", OID, 9, OperationLogFieldGroups, marks, OID)
		//	if e != nil {
		//		return errors.WithStack(err)
		//	}
		//}

	} else if reportType == data.ReportTypeSourceIPExport {
		filename, content, err = ExportSourceIPExport(ctx, projectId, envId, cohortID, roleId, customTemplateId, now)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if reportType == data.ReportTypeUnblindingReport {
		var cohortIds []string
		var projectSiteIds []string
		for _, i := range parameter["projectSiteIds"].([]interface{}) {
			projectSiteIds = append(projectSiteIds, i.(string))
		}
		for _, i := range parameter["cohortIds"].([]interface{}) {
			cohortIds = append(cohortIds, i.(string))
		}
		filename, content, err = ExportUnblindingReport(ctx, projectId, envId, customTemplateId, projectSiteIds, now, cohortIds)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if reportType == data.ReportTypeShipmentOrdersReport { //研究产品订单报表
		var receiveIds []string
		if parameter["receiveIds"] != nil {
			for _, storehouseId := range parameter["receiveIds"].([]interface{}) {
				receiveIds = append(receiveIds, storehouseId.(string))
			}
		}
		var sendIds []string
		if parameter["sendIds"] != nil {
			for _, sendId := range parameter["sendIds"].([]interface{}) {
				sendIds = append(sendIds, sendId.(string))
			}
		}
		filename, content, err = ExportShipmentOrdersReport(ctx, projectId, envId, roleId, customTemplateId, now, sendIds, receiveIds)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if reportType == data.ReportTypeReturnOrdersReport { //回收订单报表
		filename, content, err = ExportReturnOrdersReport(ctx, projectId, envId, roleId, customTemplateId, now)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if reportType == data.ReportTypeDepotItemReport { //库房单品报表下载
		var storehouseIds []string
		if parameter["storehouseIds"] != nil {
			for _, storehouseId := range parameter["storehouseIds"].([]interface{}) {
				storehouseIds = append(storehouseIds, storehouseId.(string))
			}
		}
		var cohortId string
		if parameter["cohortIds"] != nil {
			cohortId = parameter["cohortIds"].(string)
		}
		filename, content, err = ExportDepotItemReport(ctx, projectId, envId, cohortId, roleId, customTemplateId, storehouseIds, now)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if reportType == data.ReportTypeSiteItemReport { //中心单品报表下载
		var projectSiteIds []string
		if parameter["projectSiteIds"] != nil {
			for _, projectSiteId := range parameter["projectSiteIds"].([]interface{}) {
				projectSiteIds = append(projectSiteIds, projectSiteId.(string))
			}
		}
		var cohortId string
		// if parameter["cohortIds"] != nil {
		// 	cohortId = parameter["cohortIds"].(string)
		// }
		filename, content, err = ExportSiteItemReport(ctx, projectId, envId, cohortId, roleId, customTemplateId, projectSiteIds, now)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if reportType == data.ReportTypeSubjectStatisticsExport {
		var cohortIds []string
		for _, i := range parameter["cohortIds"].([]interface{}) {
			cohortIds = append(cohortIds, i.(string))
		}
		filename, content, err = ExportSubjectStatisticsReport(ctx, envId, roleId, cohortIds, now)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if reportType == data.ReportTypeDispenseReport {
		var cohortIds []string
		for _, i := range parameter["cohortIds"].([]interface{}) {
			cohortIds = append(cohortIds, i.(string))
		}
		filename, content, err = ExportDispensingReport(ctx, projectId, envId, cohortIds, customTemplateId, roleId, now)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if reportType == data.ReportTypeRandomizationStatisticsExport {
		var cohortIds []string
		for _, i := range parameter["cohortIds"].([]interface{}) {
			cohortIds = append(cohortIds, i.(string))
		}
		filename, content, err = ExportRandomizationStatisticsExport(ctx, projectId, envId, cohortIds, roleId, now)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if reportType == data.ReportTypeUserLoginHistory {
		filename, content, err = ExportUserLoginHistory(ctx, projectId, envId, now)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if reportType == data.ReportTypeRandomizeReport { //受试者明细报表
		cohortIds := parameter["cohortIds"].([]interface{})
		subject := parameter["subject"].([]interface{})
		projectSiteIds := parameter["projectSiteIds"].([]interface{})
		filename, content, err = ExportReportTypeRandomizeReport(ctx, projectId, envId, roleId, cohortIds, projectSiteIds, subject, customTemplateId, now)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if reportType == data.ReportTypeRandomizationSimulationResult {
		cohortIds := parameter["cohortIds"].([]interface{})
		simulateRandomIds := parameter["simulateRandomIds"].([]interface{})

		filename, content, err = ExportRandomizationSimulationResult(ctx, projectId, envId, cohortIds, simulateRandomIds, now)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if reportType == data.ReportTypeUserRoleAssignHistory { //用户角色分配记录
		filename, content, err = ExportUserRoleAssignHistoryReport(ctx, projectId, envId, roleId, now)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if reportType == data.ReportTypeAuditTrailExport { //稽查轨迹导出
		zipWriter := zip.NewWriter(b)
		oid, _ := primitive.ObjectIDFromHex(roleId)
		var document models.ProjectRolePermission
		opts := &options.FindOneOptions{
			Projection: bson.M{"meta": 0},
		}
		if err := tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{"_id": oid}, opts).Decode(&document); err != nil {
			return errors.WithStack(err)
		}
		subjectIdList := make([]primitive.ObjectID, 0)
		if parameter["subject"] != nil {
			subject := parameter["subject"].([]interface{})
			if subject != nil && len(subject) > 0 {
				for _, id := range subject {
					objectID, _ := primitive.ObjectIDFromHex(id.(string))
					subjectIdList = append(subjectIdList, objectID)
				}
			}
		}
		permissions := document.Permissions
		exportTypeArr := strings.Split(exportTypes, ",")
		for _, exportType := range exportTypeArr {
			if exportType == "1" { //项目构建轨迹
				index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.build")
				if index != -1 {
					//-1说明不存在
					filename, content, err = ExportReportTypeProjectConstructionExport(ctx, projectId, envId, roleId, now, startTime, endTime, exportType)
					if err != nil {
						return errors.WithStack(err)
					}
					//给excel1加密
					if cipher != "" {
						w, _ := zipWriter.Encrypt(filename, cipher)
						io.Copy(w, bytes.NewReader(content))
					} else {
						w, _ := zipWriter.Create(filename)
						io.Copy(w, bytes.NewReader(content))
					}
				}
			} else if exportType == "2" { //项目设置轨迹
				index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.settings")
				if index != -1 {
					//-1说明不存在
					filename, content, err = ExportReportTypeProjectConstructionExport(ctx, projectId, envId, roleId, now, startTime, endTime, exportType)
					if err != nil {
						return errors.WithStack(err)
					}
					//给excel1加密
					if cipher != "" {
						w, _ := zipWriter.Encrypt(filename, cipher)
						io.Copy(w, bytes.NewReader(content))
					} else {
						w, _ := zipWriter.Create(filename)
						io.Copy(w, bytes.NewReader(content))
					}
				}
			} else if exportType == "3" {
				index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.release-record")
				if index != -1 {
					//-1说明不存在
					filename, content, err = ExportReportTypeExampleTypeExport(ctx, projectId, envId, now, startTime, endTime, exportType, subjectIdList)
					if err != nil {
						return errors.WithStack(err)
					}
					//给excel1加密
					if cipher != "" {
						w, _ := zipWriter.Encrypt(filename, cipher)
						io.Copy(w, bytes.NewReader(content))
					} else {
						w, _ := zipWriter.Create(filename)
						io.Copy(w, bytes.NewReader(content))
					}
				}
			} else if exportType == "4" {
				index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.order")
				if index != -1 {
					//-1说明不存在
					filename, content, err = ExportReportTypeExampleTypeExport(ctx, projectId, envId, now, startTime, endTime, exportType, subjectIdList)
					if err != nil {
						return errors.WithStack(err)
					}
					//给excel1加密
					if cipher != "" {
						w, _ := zipWriter.Encrypt(filename, cipher)
						io.Copy(w, bytes.NewReader(content))
					} else {
						w, _ := zipWriter.Create(filename)
						io.Copy(w, bytes.NewReader(content))
					}
				}
			} else if exportType == "5" {
				index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.drug_recovery")
				if index != -1 {
					//-1说明不存在
					filename, content, err = ExportReportTypeExampleTypeExport(ctx, projectId, envId, now, startTime, endTime, exportType, subjectIdList)
					if err != nil {
						return errors.WithStack(err)
					}
					//给excel1加密
					if cipher != "" {
						w, _ := zipWriter.Encrypt(filename, cipher)
						io.Copy(w, bytes.NewReader(content))
					} else {
						w, _ := zipWriter.Create(filename)
						io.Copy(w, bytes.NewReader(content))
					}
				}
			} else if exportType == "6" {
				index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.subject")
				if index != -1 {
					//-1说明不存在
					filename, content, err = ExportReportTypeExampleTypeExport(ctx, projectId, envId, now, startTime, endTime, exportType, subjectIdList)
					if err != nil {
						return errors.WithStack(err)
					}
					//给excel1加密
					if cipher != "" {
						w, _ := zipWriter.Encrypt(filename, cipher)
						io.Copy(w, bytes.NewReader(content))
					} else {
						w, _ := zipWriter.Create(filename)
						io.Copy(w, bytes.NewReader(content))
					}
				}
			} else if exportType == "7" {
				index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.dispensing")
				if index != -1 {
					//-1说明不存在
					filename, content, err = ExportReportTypeExampleTypeExport(ctx, projectId, envId, now, startTime, endTime, exportType, subjectIdList)
					if err != nil {
						return errors.WithStack(err)
					}
					//给excel1加密
					if cipher != "" {
						w, _ := zipWriter.Encrypt(filename, cipher)
						io.Copy(w, bytes.NewReader(content))
					} else {
						w, _ := zipWriter.Create(filename)
						io.Copy(w, bytes.NewReader(content))
					}
				}
			} else if exportType == "8" {
				index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.ip")
				if index != -1 {
					//-1说明不存在
					filename, content, err = ExportReportTypeExampleTypeExport(ctx, projectId, envId, now, startTime, endTime, exportType, subjectIdList)
					if err != nil {
						return errors.WithStack(err)
					}
					//给excel1加密
					if cipher != "" {
						w, _ := zipWriter.Encrypt(filename, cipher)
						io.Copy(w, bytes.NewReader(content))
					} else {
						w, _ := zipWriter.Create(filename)
						io.Copy(w, bytes.NewReader(content))
					}
				}
			}
			//else if exportType == "9" {
			//	index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.userLoginHistory")
			//	if index != -1 {
			//		//-1说明不存在
			//		filename, content, err = ExportAuditTrailUserLoginHistory(ctx, projectId, envId, now, startTime, endTime, exportType)
			//		if err != nil {
			//			return errors.WithStack(err)
			//		}
			//		//给excel1加密
			//		if cipher != "" {
			//			w, _ := zipWriter.Encrypt(filename, cipher)
			//			io.Copy(w, bytes.NewReader(content))
			//		} else {
			//			w, _ := zipWriter.Create(filename)
			//			io.Copy(w, bytes.NewReader(content))
			//		}
			//	}
			//} else if exportType == "10" {
			//	index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.userRoleAssignHistory")
			//	if index != -1 {
			//		//-1说明不存在
			//		filename, content, err = ExportAuditTrailUserRoleAssignHistoryReport(ctx, projectId, envId, roleId, now, startTime, endTime, exportType)
			//		if err != nil {
			//			return errors.WithStack(err)
			//		}
			//		//给excel1加密
			//		if cipher != "" {
			//			w, _ := zipWriter.Encrypt(filename, cipher)
			//			io.Copy(w, bytes.NewReader(content))
			//		} else {
			//			w, _ := zipWriter.Create(filename)
			//			io.Copy(w, bytes.NewReader(content))
			//		}
			//	}
			//}
		}
		//直接调用close不要用defer
		zipWriter.Close()

	} else if reportType == data.ReportTypeSourceRandomizationListExport { //随机盲底
		randomListID := parameter["randomList"].([]interface{})
		cohortIDs := parameter["cohortIds"].([]interface{})
		filename, content, err = ExportReportTypeSourceRandomizationList(ctx, projectId, envId, cohortIDs, randomListID, customTemplateId, now)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if reportType == data.ReportTypeRandomizationSimulationReportPDF { //模拟随机报告PDF

		filename, content, err = ExportRandomizationSimulationReportPDF(ctx, projectId, envId, cohortID, roleId, simulateRandomId, now)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if reportType == data.ReportTypeProjectNotificationsConfigurationReport { //项目通知配置报表
		filename, content, err = ExportProjectNotificationsConfigurationReport(ctx, projectId, envId, roleId, now)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if reportType == data.ReportTypeProjectSourceIpUploadHistoryReport { // 研究产品盲低上传记录
		filename, content, err = ExportSourceIPUploadHistory(ctx, projectId, envId, now)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if reportType == data.ReportTypeForecastingPredictionReportReport {
		startDay := parameter["startDay"].(string)
		forecastDay := parameter["forecastDay"].(string)
		filename, content, err = ExportForecastReport(ctx, projectId, envId, roleId, startDay, forecastDay, now)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if reportType == data.ReportTypeVisitForecastRecordReportReport {
		cohortIDs := parameter["cohortIds"].([]interface{})
		subjectIDs := parameter["subject"].([]interface{})
		filename, content, err = ExportVisitForecastReport(ctx, projectId, envId, cohortIDs, subjectIDs, customTemplateId, now)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	//重新计算时间
	now = time.Now().In(time.FixedZone(fmt.Sprintf("UTC%s", timezone), int(offset*60*60)))

	if reportType == data.ReportTypeAuditTrailExport {
		envOID, _ := primitive.ObjectIDFromHex(envId)
		var project models.Project
		err = tools.Database.Collection("project").FindOne(nil, bson.M{"envs.id": envOID}).Decode(&project)
		if err != nil {
			return errors.WithStack(err)
		}
		zipname := fmt.Sprintf("%s[%s]AuditTrail_%s.zip", project.Number, getEnvName(project, envOID), now.Format("20060102150405"))

		gridFS := tools.NewGridFS()
		fileId, err := gridFS.Upload(zipname, b.Bytes())
		if err != nil {
			return errors.WithStack(err)
		}

		if _, err := tools.Database.Collection(models.ReportHistoryTableName).InsertOne(context.Background(), models.ReportHistory{
			ID:           primitive.NewObjectID(),
			ReportType:   reportType,
			ProjectID:    poid,
			EnvID:        eoid,
			UserID:       user.ID,
			Filename:     zipname,
			FileID:       fileId,
			FileSize:     int64(len(b.Bytes())),
			DownloadTime: now.Unix(),
			Meta: models.Meta{
				CreatedBy: user.ID,
				CreatedAt: time.Duration(now.Unix()),
			},
		}); err != nil {
			return errors.WithStack(err)
		}

		ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", url.PathEscape(zipname)))
		ctx.Data(200, "application/zip", b.Bytes())
	} else {

		//IRT-4204【报表下载】报表导出，最近下载时间记录的是下载开始时间，不是完成时间
		lastIndex := strings.LastIndex(filename, ".")
		filename = fmt.Sprintf("%s%s%s", filename[0:lastIndex-14], now.Format("20060102150405"), filename[lastIndex:])

		gridFS := tools.NewGridFS()
		fileId, err := gridFS.Upload(filename, content)
		if err != nil {
			return errors.WithStack(err)
		}

		if _, err := tools.Database.Collection(models.ReportHistoryTableName).InsertOne(context.Background(), models.ReportHistory{
			ID:           primitive.NewObjectID(),
			ReportType:   reportType,
			ProjectID:    poid,
			EnvID:        eoid,
			UserID:       user.ID,
			Filename:     filename,
			FileID:       fileId,
			FileSize:     int64(len(content)),
			DownloadTime: now.Unix(),
			Meta: models.Meta{
				CreatedBy: user.ID,
				CreatedAt: time.Duration(now.Unix()),
			},
		}); err != nil {
			return errors.WithStack(err)
		}

		ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", url.PathEscape(filename)))
		ctx.Data(200, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8", content)
	}

	return nil
}

func (s *ReportService) GetHistory(ctx *gin.Context, projectId string, envId string, reportType int) ([]*models.ReportHistoryResp, error) {
	user, err := tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var histories []*models.ReportHistoryResp
	poid, _ := primitive.ObjectIDFromHex(projectId)
	eoid, _ := primitive.ObjectIDFromHex(envId)

	filter := bson.M{
		"project_id":  poid,
		"env_id":      eoid,
		"report_type": reportType,
		"user_id":     user.ID,
		"deleted":     0,
	}
	opts := &options.FindOptions{
		Sort: bson.M{
			"download_time": -1,
		},
	}
	cursor, err := tools.Database.Collection(models.ReportHistoryTableName).Find(context.Background(), filter, opts)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if err = cursor.All(context.Background(), &histories); err != nil {
		return nil, errors.WithStack(err)
	}
	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))

	for _, history := range histories {
		downloadTime := history.DownloadTime

		location := "Asia/Shanghai"
		if len(users) > 0 && users[0].Settings.Tz != "" {
			location = users[0].Settings.Tz
		}
		timeStr, err := tools.GetLocationUtc(location, downloadTime)
		if err != nil {
			panic(err)
		}
		history.DownloadTimeStr = timeStr
	}
	return histories, nil
}

func (s *ReportService) DownloadHistory(ctx *gin.Context, id string) error {
	oid, _ := primitive.ObjectIDFromHex(id)

	var history models.ReportHistory
	if err := tools.Database.Collection(models.ReportHistoryTableName).FindOne(context.Background(), bson.M{"_id": oid}).Decode(&history); errors.WithStack(err) != nil {
		return errors.WithStack(err)
	}

	gridFS := tools.NewGridFS()
	content, err := gridFS.Download(history.FileID)
	if err != nil {
		return errors.WithStack(err)
	}

	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", url.PathEscape(history.Filename)))
	ctx.Data(200, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8", content)

	return nil
}

func (s *ReportService) ExportAuthority(ctx *gin.Context, roleId string) (interface{}, error) {

	type AuditTrailType struct {
		ID   string `json:"id" `
		Name string `json:"name" `
	}

	oid, _ := primitive.ObjectIDFromHex(roleId)
	var result []AuditTrailType
	var document models.ProjectRolePermission
	opts := &options.FindOneOptions{
		Projection: bson.M{"meta": 0},
	}
	if err := tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{"_id": oid}, opts).Decode(&document); err != nil {
		return "", errors.WithStack(err)
	}
	permissions := document.Permissions

	build := arrays.ContainsString(permissions, "operation.report.auditTrailExport.build")

	if build != -1 {
		//-1说明不存在
		var auditTrailType AuditTrailType
		auditTrailType.ID = "1"
		auditTrailType.Name = locales.Tr(ctx, "menu.report.auditTrailExport.build")
		result = append(result, auditTrailType)
	}

	settings := arrays.ContainsString(permissions, "operation.report.auditTrailExport.settings")
	if settings != -1 {
		//-1说明不存在
		var auditTrailType AuditTrailType
		auditTrailType.ID = "2"
		auditTrailType.Name = locales.Tr(ctx, "menu.report.auditTrailExport.settings")
		result = append(result, auditTrailType)
	}

	record := arrays.ContainsString(permissions, "operation.report.auditTrailExport.release-record")
	if record != -1 {
		//-1说明不存在
		var auditTrailType AuditTrailType
		auditTrailType.ID = "3"
		auditTrailType.Name = locales.Tr(ctx, "menu.report.auditTrailExport.release-record")
		result = append(result, auditTrailType)
	}

	order := arrays.ContainsString(permissions, "operation.report.auditTrailExport.order")
	if order != -1 {
		//-1说明不存在
		var auditTrailType AuditTrailType
		auditTrailType.ID = "4"
		auditTrailType.Name = locales.Tr(ctx, "menu.report.auditTrailExport.order")
		result = append(result, auditTrailType)
	}

	recovery := arrays.ContainsString(permissions, "operation.report.auditTrailExport.drug_recovery")
	if recovery != -1 {
		//-1说明不存在
		var auditTrailType AuditTrailType
		auditTrailType.ID = "5"
		auditTrailType.Name = locales.Tr(ctx, "menu.report.auditTrailExport.drug_recovery")
		result = append(result, auditTrailType)
	}

	subject := arrays.ContainsString(permissions, "operation.report.auditTrailExport.subject")
	if subject != -1 {
		//-1说明不存在
		var auditTrailType AuditTrailType
		auditTrailType.ID = "6"
		auditTrailType.Name = locales.Tr(ctx, "menu.report.auditTrailExport.subject")
		result = append(result, auditTrailType)
	}

	dispensing := arrays.ContainsString(permissions, "operation.report.auditTrailExport.dispensing")
	if dispensing != -1 {
		//-1说明不存在
		var auditTrailType AuditTrailType
		auditTrailType.ID = "7"
		auditTrailType.Name = locales.Tr(ctx, "menu.report.auditTrailExport.dispensing")
		result = append(result, auditTrailType)
	}

	ip := arrays.ContainsString(permissions, "operation.report.auditTrailExport.ip")
	if ip != -1 {
		//-1说明不存在
		var auditTrailType AuditTrailType
		auditTrailType.ID = "8"
		auditTrailType.Name = locales.Tr(ctx, "menu.report.auditTrailExport.ip")
		result = append(result, auditTrailType)
	}

	//userLoginHistory := arrays.ContainsString(permissions, "operation.report.auditTrailExport.userLoginHistory")
	//if userLoginHistory != -1 {
	//	//-1说明不存在
	//	var auditTrailType AuditTrailType
	//	auditTrailType.ID = "9"
	//	auditTrailType.Name = locales.Tr(ctx, "menu.report.auditTrailExport.userLoginHistory")
	//	result = append(result, auditTrailType)
	//}
	//
	//userRoleAssignHistory := arrays.ContainsString(permissions, "operation.report.auditTrailExport.userRoleAssignHistory")
	//if userRoleAssignHistory != -1 {
	//	//-1说明不存在
	//	var auditTrailType AuditTrailType
	//	auditTrailType.ID = "10"
	//	auditTrailType.Name = locales.Tr(ctx, "menu.report.auditTrailExport.userRoleAssignHistory")
	//	result = append(result, auditTrailType)
	//}

	return result, nil
}

func (s *ReportService) ExportRandomizationSimulation(ctx *gin.Context, customerId string, projectId string, envId string, cohortId string) (interface{}, error) {

	type RandomizationSimulationName struct {
		ID   string `json:"id" `
		Name string `json:"name" `
	}

	projectOId, _ := primitive.ObjectIDFromHex(projectId)
	envOId, _ := primitive.ObjectIDFromHex(envId)
	cohortOId, _ := primitive.ObjectIDFromHex(cohortId)

	var result []RandomizationSimulationName

	filter := bson.M{"project_id": projectOId, "env_id": envOId}
	if !cohortOId.IsZero() {
		filter = bson.M{"project_id": projectOId, "env_id": envOId, "cohort_id": cohortOId}
	}
	var simulateRandoms []models.SimulateRandom
	cursor, err := tools.Database.Collection("simulate_random").Find(nil, filter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &simulateRandoms)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	for _, random := range simulateRandoms {
		condition := bson.M{"simulate_random_id": random.ID}
		count, _ := tools.Database.Collection("simulate_subject").CountDocuments(nil, condition)

		if count > 0 {
			var randomizationSimulationName RandomizationSimulationName
			randomizationSimulationName.ID = random.ID.Hex()
			randomizationSimulationName.Name = random.SimulateRandomInfo.Name
			result = append(result, randomizationSimulationName)
		}
	}

	return result, nil
}

func (s *ReportService) ExportConfigurePdf(ctx *gin.Context, reportType int, projectId string, envId string, customTemplateId string, cohortID string, roleId string, startTime int, endTime int, exportTypes string, cipher string, simulateRandomId string) (models.ConfigureReport, error) {

	var configureReport models.ConfigureReport

	user, err := tools.Me(ctx)
	if err != nil {
		return configureReport, errors.WithStack(err)
	}
	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))

	timezone := "08:00"
	offset := float64(8)
	if len(users) > 0 && users[0].Settings.Tz != "" {
		timezone, err = tools.GetLocationTimeZone(users[0].Settings.Tz)
		if err != nil {
			return configureReport, errors.WithStack(err)
		}
		offset, err = tools.GetLocationFloat(users[0].Settings.Tz)
		if err != nil {
			return configureReport, errors.WithStack(err)
		}
	}

	if err != nil {
		return configureReport, errors.WithStack(err)
	}

	//poid, _ := primitive.ObjectIDFromHex(projectId)
	//eoid, _ := primitive.ObjectIDFromHex(envId)
	now := time.Now().In(time.FixedZone(fmt.Sprintf("UTC%s", timezone), int(offset*60*60)))

	//var (
	//	filename string
	//	content  models.ConfigureReport
	//)
	//
	////压缩包名称
	//b := new(bytes.Buffer)

	var parameter map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&parameter, binding.JSON)

	isBlindedRole, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return configureReport, errors.WithStack(err)
	}
	ctx.Set("isBlindedRole", isBlindedRole)

	//根据报表类型选择不同的导出方法
	if reportType == data.ReportTypeConfigureReport {
		cohortIds := make([]string, 0)
		for _, i := range parameter["cohortIds"].([]interface{}) {
			cohortIds = append(cohortIds, i.(string))
		}
		if cohortIds != nil && len(cohortIds) > 0 {
			configureReport, err = ExportConfigureReportPdf(ctx, projectId, envId, cohortIds, roleId, now)
			if err != nil {
				return configureReport, errors.WithStack(err)
			}
			for _, coId := range cohortIds {
				OID, _ := primitive.ObjectIDFromHex(coId)
				var OperationLogFieldGroups []models.OperationLogFieldGroup
				marks := []models.Mark{}
				e := tools.SaveOperation(ctx, nil, "operation_log.module.configure_export", OID, 9, OperationLogFieldGroups, marks, OID)
				if e != nil {
					return configureReport, errors.WithStack(err)
				}
			}
		} else {
			//filename, content, err = ExportConfigureReport(ctx, projectId, envId, cohortID, roleId, now)
			configureReport, err = ExportConfigureReportPdf(ctx, projectId, envId, cohortIds, roleId, now)
			if err != nil {
				return configureReport, errors.WithStack(err)
			}
			OID, _ := primitive.ObjectIDFromHex(envId)
			var OperationLogFieldGroups []models.OperationLogFieldGroup
			marks := []models.Mark{}
			e := tools.SaveOperation(ctx, nil, "operation_log.module.configure_export", OID, 9, OperationLogFieldGroups, marks, OID)
			if e != nil {
				return configureReport, errors.WithStack(err)
			}
		}

	}

	return configureReport, nil
}

func (s *ReportService) ExportConfigurePdfNew(ctx *gin.Context, reportType int, projectId string, envId string, customTemplateId string, cohortID string, roleId string, startTime int, endTime int, exportTypes string, cipher string, simulateRandomId string) error {

	var configureReport models.ConfigureReport

	user, err := tools.Me(ctx)
	if err != nil {
		return err
	}
	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))

	timezone := "08:00"
	offset := float64(8)
	if len(users) > 0 && users[0].Settings.Tz != "" {
		timezone, err = tools.GetLocationTimeZone(users[0].Settings.Tz)
		if err != nil {
			return errors.WithStack(err)
		}
		offset, err = tools.GetLocationFloat(users[0].Settings.Tz)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	if err != nil {
		return errors.WithStack(err)
	}

	//poid, _ := primitive.ObjectIDFromHex(projectId)
	//eoid, _ := primitive.ObjectIDFromHex(envId)
	now := time.Now().In(time.FixedZone(fmt.Sprintf("UTC%s", timezone), int(offset*60*60)))

	//var (
	//	filename string
	//	content  models.ConfigureReport
	//)
	//
	////压缩包名称
	//b := new(bytes.Buffer)

	var parameter map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&parameter, binding.JSON)

	isBlindedRole, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return errors.WithStack(err)
	}
	ctx.Set("isBlindedRole", isBlindedRole)

	//根据报表类型选择不同的导出方法
	if reportType == data.ReportTypeConfigureReport {
		cohortIds := make([]string, 0)
		for _, i := range parameter["cohortIds"].([]interface{}) {
			cohortIds = append(cohortIds, i.(string))
		}
		if cohortIds != nil && len(cohortIds) > 0 {
			configureReport, err = ExportConfigureReportPdf(ctx, projectId, envId, cohortIds, roleId, now)
			if err != nil {
				return errors.WithStack(err)
			}
			for _, coId := range cohortIds {
				OID, _ := primitive.ObjectIDFromHex(coId)
				var OperationLogFieldGroups []models.OperationLogFieldGroup
				marks := []models.Mark{}
				e := tools.SaveOperation(ctx, nil, "operation_log.module.configure_export", OID, 9, OperationLogFieldGroups, marks, OID)
				if e != nil {
					return errors.WithStack(err)
				}
			}
		} else {
			//filename, content, err = ExportConfigureReport(ctx, projectId, envId, cohortID, roleId, now)
			configureReport, err = ExportConfigureReportPdf(ctx, projectId, envId, cohortIds, roleId, now)
			if err != nil {
				return errors.WithStack(err)
			}
			OID, _ := primitive.ObjectIDFromHex(envId)
			var OperationLogFieldGroups []models.OperationLogFieldGroup
			marks := []models.Mark{}
			e := tools.SaveOperation(ctx, nil, "operation_log.module.configure_export", OID, 9, OperationLogFieldGroups, marks, OID)
			if e != nil {
				return errors.WithStack(err)
			}
		}
	}

	pdfContent, err := tools.ExportReportPdf("configure_report_content_new.html", configureReport)
	if err != nil {
		return err
	}

	return s.ExportConfigureReportNew(ctx, reportType, projectId, envId, pdfContent)
}

func (s *ReportService) ExportConfigureReport(ctx *gin.Context, reportType int, projectId string, envId string, customTemplateId string, cohortID string, roleId string, startTime int, endTime int, exportTypes string, cipher string, simulateRandomId string, pdfFile []byte) error {

	user, err := tools.Me(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))

	timezone := "08:00"
	offset := float64(8)
	if len(users) > 0 && users[0].Settings.Tz != "" {
		timezone, err = tools.GetLocationTimeZone(users[0].Settings.Tz)
		if err != nil {
			return errors.WithStack(err)
		}
		offset, err = tools.GetLocationFloat(users[0].Settings.Tz)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	if err != nil {
		return errors.WithStack(err)
	}

	//poid, _ := primitive.ObjectIDFromHex(projectId)
	//eoid, _ := primitive.ObjectIDFromHex(envId)
	now := time.Now().In(time.FixedZone(fmt.Sprintf("UTC%s", timezone), int(offset*60*60)))

	//var (
	//	filename string
	//	content  models.ConfigureReport
	//)
	//
	////压缩包名称
	//b := new(bytes.Buffer)

	//// 读取上传的 PDF 文件
	//file, _, err := ctx.Request.FormFile("pdfFile")
	//if err != nil {
	//	ctx.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read file"})
	//	return errors.WithStack(err)
	//}
	//defer file.Close()
	//
	//// 读取文件数据
	//fileData, err := io.ReadAll(file)
	//if err != nil {
	//	ctx.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read file data"})
	//	return errors.WithStack(err)
	//}

	projectOID, _ := primitive.ObjectIDFromHex(projectId)
	envOID, _ := primitive.ObjectIDFromHex(envId)

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"envs.id": envOID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}

	envCode := getEnvName(project, envOID)
	fileName := fmt.Sprintf("%s[%s]ConfigureReport_%s.pdf", project.Number, envCode, now.Format("20060102150405"))

	//IRT-4204【报表下载】报表导出，最近下载时间记录的是下载开始时间，不是完成时间
	lastIndex := strings.LastIndex(fileName, ".")
	fileName = fmt.Sprintf("%s%s%s", fileName[0:lastIndex-14], now.Format("20060102150405"), fileName[lastIndex:])

	content := pdfFile

	gridFS := tools.NewGridFS()
	fileId, err := gridFS.Upload(fileName, content)
	if err != nil {
		return errors.WithStack(err)
	}

	if _, err := tools.Database.Collection(models.ReportHistoryTableName).InsertOne(context.Background(), models.ReportHistory{
		ID:           primitive.NewObjectID(),
		ReportType:   reportType,
		ProjectID:    projectOID,
		EnvID:        envOID,
		UserID:       user.ID,
		Filename:     fileName,
		FileID:       fileId,
		FileSize:     int64(len(content)),
		DownloadTime: now.Unix(),
		Meta: models.Meta{
			CreatedBy: user.ID,
			CreatedAt: time.Duration(now.Unix()),
		},
	}); err != nil {
		return errors.WithStack(err)
	}

	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", url.PathEscape(fileName)))
	ctx.Data(200, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8", content)

	return nil
}

func (s *ReportService) ExportConfigureReportNew(ctx *gin.Context, reportType int, projectId string, envId string, pdfFile []byte) error {

	user, err := tools.Me(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))

	timezone := "08:00"
	offset := float64(8)
	if len(users) > 0 && users[0].Settings.Tz != "" {
		timezone, err = tools.GetLocationTimeZone(users[0].Settings.Tz)
		if err != nil {
			return errors.WithStack(err)
		}
		offset, err = tools.GetLocationFloat(users[0].Settings.Tz)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	if err != nil {
		return errors.WithStack(err)
	}

	//poid, _ := primitive.ObjectIDFromHex(projectId)
	//eoid, _ := primitive.ObjectIDFromHex(envId)
	now := time.Now().In(time.FixedZone(fmt.Sprintf("UTC%s", timezone), int(offset*60*60)))

	//var (
	//	filename string
	//	content  models.ConfigureReport
	//)
	//
	////压缩包名称
	//b := new(bytes.Buffer)

	//// 读取上传的 PDF 文件
	//file, _, err := ctx.Request.FormFile("pdfFile")
	//if err != nil {
	//	ctx.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read file"})
	//	return errors.WithStack(err)
	//}
	//defer file.Close()
	//
	//// 读取文件数据
	//fileData, err := io.ReadAll(file)
	//if err != nil {
	//	ctx.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read file data"})
	//	return errors.WithStack(err)
	//}

	projectOID, _ := primitive.ObjectIDFromHex(projectId)
	envOID, _ := primitive.ObjectIDFromHex(envId)

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"envs.id": envOID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}

	envCode := getEnvName(project, envOID)
	fileName := fmt.Sprintf("%s[%s]ConfigureReport_%s.pdf", project.Number, envCode, now.Format("20060102150405"))

	//IRT-4204【报表下载】报表导出，最近下载时间记录的是下载开始时间，不是完成时间
	lastIndex := strings.LastIndex(fileName, ".")
	fileName = fmt.Sprintf("%s%s%s", fileName[0:lastIndex-14], now.Format("20060102150405"), fileName[lastIndex:])

	content := pdfFile

	gridFS := tools.NewGridFS()
	fileId, err := gridFS.Upload(fileName, content)
	if err != nil {
		return errors.WithStack(err)
	}

	if _, err := tools.Database.Collection(models.ReportHistoryTableName).InsertOne(context.Background(), models.ReportHistory{
		ID:           primitive.NewObjectID(),
		ReportType:   reportType,
		ProjectID:    projectOID,
		EnvID:        envOID,
		UserID:       user.ID,
		Filename:     fileName,
		FileID:       fileId,
		FileSize:     int64(len(content)),
		DownloadTime: now.Unix(),
		Meta: models.Meta{
			CreatedBy: user.ID,
			CreatedAt: time.Duration(now.Unix()),
		},
	}); err != nil {
		return errors.WithStack(err)
	}

	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", url.PathEscape(fileName)))
	ctx.Data(200, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8", content)

	return nil
}

func (s *ReportService) ExportSubjectPdf(ctx *gin.Context, reportType int, projectId string, envId string, customTemplateId string, cohortID string, roleId string, startTime int, endTime int, exportTypes string, cipher string, simulateRandomId string, subjects []interface{}) ([]models.SubjectReport, error) {

	subjectReportList := make([]models.SubjectReport, 0)

	subjectOIDs := make([]primitive.ObjectID, 0)
	for _, subject := range subjects {
		subjectOID, _ := primitive.ObjectIDFromHex(subject.(string))
		subjectOIDs = append(subjectOIDs, subjectOID)
	}

	projectOID, _ := primitive.ObjectIDFromHex(projectId)
	envOID, _ := primitive.ObjectIDFromHex(envId)
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if subjectOIDs != nil && len(subjectOIDs) > 0 {
		for _, subjectOID := range subjectOIDs {
			var subject models.Subject
			err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOID}).Decode(&subject)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			var subjectNumber interface{}
			if subject.Info != nil && len(subject.Info) > 0 {
				for _, info := range subject.Info {
					if info.Name == "shortname" {
						subjectNumber = info.Value
					}
				}
			}

			cohortName := ""
			if project.Environments != nil && len(project.Environments) > 0 {
				for _, environment := range project.Environments {
					if environment.ID == envOID {
						if environment.Cohorts != nil && len(environment.Cohorts) > 0 {
							if project.Type == 3 && !tools.InRandomIsolation(project.Number) {
								subjectFilter := bson.M{
									"customer_id": subject.CustomerID,
									"project_id":  subject.ProjectID,
									"env_id":      subject.EnvironmentID,
									"info": bson.M{
										"$elemMatch": bson.M{
											"name":  "shortname",
											"value": subjectNumber,
										},
									},
									"deleted": bson.M{"$ne": true},
								}
								opts := &options.FindOptions{
									Sort: bson.D{{"_id", 1}},
								}
								subjectList := make([]models.Subject, 0)
								subjectCursor, err := tools.Database.Collection("subject").Find(nil, subjectFilter, opts)
								if err != nil {
									return nil, errors.WithStack(err)
								}
								err = subjectCursor.All(nil, &subjectList)
								if err != nil {
									return nil, errors.WithStack(err)
								}
								if len(subjectList) > 0 {
									if len(subjectList) == 1 {
										for _, cohort := range environment.Cohorts {
											if cohort.ID == subject.CohortID {
												cohortName = cohort.Name
											}
										}
									} else if len(subjectList) > 1 {
										for _, cohort := range environment.Cohorts {
											if cohort.ID == subject.CohortID {
												if cohort.LastID != primitive.NilObjectID {
													cohortName = cohort.Name
												} else {
													cohortName = ""
												}

											}
										}
									}
								}
							} else {
								for _, cohort := range environment.Cohorts {
									if cohort.ID == subject.CohortID {
										cohortName = cohort.Name
									}
								}
							}
						}
					}
				}
			}

			var projectSite models.ProjectSite
			err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			history, err := HistoryList(ctx, "history.subject", subjectOID.Hex(), nil, nil, envId, subject.CohortID.Hex(), roleId, startTime, endTime)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			timeZone, err := tools.GetTimeZone(projectOID)
			if history["total"].(int64) > 0 {
				var subjectReport models.SubjectReport
				subjectReport.Title = locales.Tr(ctx, "subjectReport.title")
				subjectReport.Number = subjectNumber.(string)
				subjectReport.ProjectNameLabel = locales.Tr(ctx, "subjectReport.projectName")
				subjectReport.ProjectName = project.Name
				subjectReport.SponsorLabel = locales.Tr(ctx, "subjectReport.sponsor")
				subjectReport.Sponsor = project.Sponsor
				if project.Type == 2 {
					subjectReport.CohortLabel = locales.Tr(ctx, "subjectReport.type.cohort")
					subjectReport.Cohort = cohortName
				} else if project.Type == 3 {
					subjectReport.CohortLabel = locales.Tr(ctx, "subjectReport.type.stage")
					subjectReport.Cohort = cohortName
				}
				subjectReport.AutographLabel = locales.Tr(ctx, "subjectReport.autograph")
				subjectReport.Autograph = ""
				subjectReport.SigningDateLabel = locales.Tr(ctx, "subjectReport.signingDate")
				subjectReport.SigningDate = ""
				subjectReport.SerialNumberLabel = locales.Tr(ctx, "subjectReport.history.serialNumber")
				subjectReport.OperatorLabel = locales.Tr(ctx, "subjectReport.history.operator")
				subjectReport.OperationTimeLabel = locales.Tr(ctx, "subjectReport.history.operationTime")
				subjectReport.OperationContentLabel = locales.Tr(ctx, "subjectReport.history.operationContent")
				trackList := make([]models.Track, 0)
				for i, item := range history["items"].([]models.HistoryView) {
					var track models.Track
					track.SerialNumber = strconv.Itoa(i + 1)
					track.Operator = item.User
					siteTimeZone, _ := tools.GetSiteTimeZone(subject.ProjectSiteID)
					if len(siteTimeZone) != 0 {
						number := siteTimeZone[3:]
						character := number[0:1]
						if character == "+" {
							s := number[1:]
							n, _ := strconv.Atoi(s)
							timeZone = float64(n)
						} else {
							n, _ := strconv.Atoi(number)
							timeZone = float64(n)
						}
					}

					hours := time.Duration(timeZone)
					minutes := time.Duration((timeZone - float64(hours)) * 60)
					duration := hours*time.Hour + minutes*time.Minute
					strTimeZone := tools.FormatOffsetToZoneStringUtc(timeZone)
					timeStr := time.Unix(item.Time.Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02 15:04:05") + "(" + strTimeZone + ")"

					//timeStr, err := tools.GetLocationUtc(siteTimeZone, int64(item.Time))
					//if err != nil {
					//	return nil, errors.WithStack(err)
					//}
					track.OperationTime = timeStr
					track.OperationContent = item.Content
					trackList = append(trackList, track)
				}
				subjectReport.TrackList = trackList

				if project.Type != 3 {
					subjectReportList = append(subjectReportList, subjectReport)
				} else {
					if cohortName != "" {
						subjectReportList = append(subjectReportList, subjectReport)
					}
				}
			}

		}
	}

	return subjectReportList, nil
}

func (s *ReportService) ExportSubjectPdfNew(ctx *gin.Context, reportType int, projectId string, envId string, customTemplateId string, cohortID string, roleId string, startTime int, endTime int, exportTypes string, exportFormat int, cipher string, simulateRandomId string, subjects []interface{}) error {

	subjectReportList := make([]models.SubjectReport, 0)

	subjectOIDs := make([]primitive.ObjectID, 0)
	for _, subject := range subjects {
		subjectOID, _ := primitive.ObjectIDFromHex(subject.(string))
		subjectOIDs = append(subjectOIDs, subjectOID)
	}

	projectOID, _ := primitive.ObjectIDFromHex(projectId)
	envOID, _ := primitive.ObjectIDFromHex(envId)
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}

	if subjectOIDs != nil && len(subjectOIDs) > 0 {
		for _, subjectOID := range subjectOIDs {
			var subject models.Subject
			err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOID}).Decode(&subject)
			if err != nil {
				return errors.WithStack(err)
			}

			var subjectNumber interface{}
			if subject.Info != nil && len(subject.Info) > 0 {
				for _, info := range subject.Info {
					if info.Name == "shortname" {
						subjectNumber = info.Value
					}
				}
			}

			cohortName := ""
			if project.Environments != nil && len(project.Environments) > 0 {
				for _, environment := range project.Environments {
					if environment.ID == envOID {
						if environment.Cohorts != nil && len(environment.Cohorts) > 0 {
							if project.Type == 3 && !tools.InRandomIsolation(project.Number) {
								subjectFilter := bson.M{
									"customer_id": subject.CustomerID,
									"project_id":  subject.ProjectID,
									"env_id":      subject.EnvironmentID,
									"info": bson.M{
										"$elemMatch": bson.M{
											"name":  "shortname",
											"value": subjectNumber,
										},
									},
									"$or": []bson.M{
										{"deleted": subject.Deleted},
										{"deleted": bson.M{"$exists": false}},
									},
								}
								opts := &options.FindOptions{
									Sort: bson.D{{"_id", 1}},
								}
								subjectList := make([]models.Subject, 0)
								subjectCursor, err := tools.Database.Collection("subject").Find(nil, subjectFilter, opts)
								if err != nil {
									return errors.WithStack(err)
								}
								err = subjectCursor.All(nil, &subjectList)
								if err != nil {
									return errors.WithStack(err)
								}
								if len(subjectList) > 0 {
									if len(subjectList) == 1 {
										for _, cohort := range environment.Cohorts {
											if cohort.ID == subject.CohortID {
												cohortName = cohort.Name
											}
										}
									} else if len(subjectList) > 1 {
										for _, cohort := range environment.Cohorts {
											if cohort.LastID != primitive.NilObjectID {
												cohortName = cohort.Name
											}
										}
									}
								}
							} else {
								for _, cohort := range environment.Cohorts {
									if cohort.ID == subject.CohortID {
										cohortName = cohort.Name
									}
								}
							}
						}
					}
				}
			}

			var projectSite models.ProjectSite
			err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
			if err != nil {
				return errors.WithStack(err)
			}

			history, err := HistoryList(ctx, "history.subject", subjectOID.Hex(), nil, nil, envId, subject.CohortID.Hex(), roleId, startTime, endTime)
			if err != nil {
				return errors.WithStack(err)
			}

			timeZone, err := tools.GetTimeZone(projectOID)
			if err != nil {
				return errors.WithStack(err)
			}
			siteTimeZone, _ := tools.GetSiteTimeZone(subject.ProjectSiteID)
			if len(siteTimeZone) != 0 {
				number := siteTimeZone[3:]
				character := number[0:1]
				if character == "+" {
					s := number[1:]
					n, _ := strconv.Atoi(s)
					timeZone = float64(n)
				} else {
					n, _ := strconv.Atoi(number)
					timeZone = float64(n)
				}
			}

			if history["total"].(int64) > 0 {
				var subjectReport models.SubjectReport
				subjectReport.SubjectId = subjectOID.Hex()
				subjectReport.Title = locales.Tr(ctx, "subjectReport.title")
				subjectReport.Number = subjectNumber.(string)
				subjectReport.ProjectNameLabel = locales.Tr(ctx, "subjectReport.projectName")
				subjectReport.ProjectName = project.Name
				subjectReport.SponsorLabel = locales.Tr(ctx, "subjectReport.sponsor")
				subjectReport.Sponsor = project.Sponsor
				if project.Type == 2 {
					subjectReport.CohortLabel = locales.Tr(ctx, "subjectReport.type.cohort")
					subjectReport.Cohort = cohortName
				} else if project.Type == 3 {
					subjectReport.CohortLabel = locales.Tr(ctx, "subjectReport.type.stage")
					subjectReport.Cohort = cohortName
				}
				subjectReport.AutographLabel = locales.Tr(ctx, "subjectReport.autograph")
				subjectReport.Autograph = ""
				subjectReport.SigningDateLabel = locales.Tr(ctx, "subjectReport.signingDate")
				subjectReport.SigningDate = ""
				subjectReport.SerialNumberLabel = locales.Tr(ctx, "subjectReport.history.serialNumber")
				subjectReport.OperatorLabel = locales.Tr(ctx, "subjectReport.history.operator")
				subjectReport.OperationTimeLabel = locales.Tr(ctx, "subjectReport.history.operationTime")
				subjectReport.OperationContentLabel = locales.Tr(ctx, "subjectReport.history.operationContent")
				trackList := make([]models.Track, 0)
				for i, item := range history["items"].([]models.HistoryView) {
					var track models.Track
					track.SerialNumber = strconv.Itoa(i + 1)
					track.Operator = item.User

					hours := time.Duration(timeZone)
					minutes := time.Duration((timeZone - float64(hours)) * 60)
					duration := hours*time.Hour + minutes*time.Minute
					strTimeZone := tools.FormatOffsetToZoneStringUtc(timeZone)
					timeStr := time.Unix(item.Time.Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02 15:04:05") + "(" + strTimeZone + ")"

					track.OperationTime = timeStr
					track.OperationContent = item.Content
					trackList = append(trackList, track)
				}
				subjectReport.TrackList = trackList

				if project.Type != 3 {
					subjectReportList = append(subjectReportList, subjectReport)
				} else {
					if cohortName != "" {
						subjectReportList = append(subjectReportList, subjectReport)
					}
				}
			}

		}
	}

	// 使用 sync.Map 存储报告数据
	var reportDataMap sync.Map
	var wg sync.WaitGroup
	var errMu sync.Mutex             // 用于保护错误切片的互斥锁
	var generateReportErrors []error // 用于存储所有错误

	// 限制并发数量
	const maxConcurrent = 2 // 修改为服务器的cpu 核心数量
	semaphore := make(chan struct{}, maxConcurrent)

	for _, subjectReport := range subjectReportList {
		wg.Add(1)               // 增加 WaitGroup 的计数
		semaphore <- struct{}{} // 获取一个并发许可
		go func(report models.SubjectReport) {
			defer wg.Done()                // 完成后减少 WaitGroup 的计数
			defer func() { <-semaphore }() // 释放并发许可
			pdfContent, err := tools.ExportReportPdf("subject_report_content.html", report)
			if err != nil {
				// 锁定互斥锁，安全地将错误添加到错误切片中
				errMu.Lock()
				generateReportErrors = append(generateReportErrors, err)
				errMu.Unlock()
				return
			}
			// 使用 sync.Map 存储数据
			//reportDataMap.Store(report.Number, pdfContent)
			reportDataMap.Store(report.SubjectId, models.SubjectReportData{
				PDFContent: pdfContent,
				Number:     report.Number,
			})
		}(subjectReport)
	}

	wg.Wait() // 等待所有 goroutine 完成

	// 检查是否有错误发生
	if len(generateReportErrors) > 0 {
		fmt.Println("Errors occurred during subject report generation:")
		for _, err := range generateReportErrors {
			fmt.Println(err)
		}
		// 返回第一个错误
		return generateReportErrors[0]
	}

	var resultMap = make(map[string]models.SubjectReportData)
	reportDataMap.Range(func(key, value interface{}) bool {
		resultMap[key.(string)] = value.(models.SubjectReportData)
		return true
	})

	// 打包成一个压缩包文件并返回
	return s.ExportSubjectReportNew(ctx, resultMap, reportType, projectId, envId, roleId, startTime, endTime, exportTypes, cipher, exportFormat, subjectOIDs)
}

func (s *ReportService) ExportSubjectReportNew(ctx *gin.Context, reportDataMap map[string]models.SubjectReportData, reportType int, projectId string, envId string, roleId string, startTime int, endTime int, exportTypes string, cipher string, exportFormat int, subjectIdList []primitive.ObjectID) error {

	user, err := tools.Me(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))

	timezone := "08:00"
	offset := float64(8)
	if len(users) > 0 && users[0].Settings.Tz != "" {
		timezone, err = tools.GetLocationTimeZone(users[0].Settings.Tz)
		if err != nil {
			return errors.WithStack(err)
		}
		offset, err = tools.GetLocationFloat(users[0].Settings.Tz)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	if err != nil {
		return errors.WithStack(err)
	}

	poid, _ := primitive.ObjectIDFromHex(projectId)
	eoid, _ := primitive.ObjectIDFromHex(envId)
	now := time.Now().In(time.FixedZone(fmt.Sprintf("UTC%s", timezone), int(offset*60*60)))

	//压缩包名称
	b := new(bytes.Buffer)
	zipWriter := zip.NewWriter(b)

	if exportFormat == 2 && len(reportDataMap) != 0 {

		for _, subjectReportData := range reportDataMap {
			filename := subjectReportData.Number + ".pdf"
			content := subjectReportData.PDFContent
			//给excel1加密
			if cipher != "" {
				w, _ := zipWriter.Encrypt(filename, cipher)
				io.Copy(w, bytes.NewReader(content))
			} else {
				w, _ := zipWriter.Create(filename)
				io.Copy(w, bytes.NewReader(content))
			}

		}

	}

	oid, _ := primitive.ObjectIDFromHex(roleId)
	var document models.ProjectRolePermission
	opts := &options.FindOneOptions{
		Projection: bson.M{"meta": 0},
	}
	if err := tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{"_id": oid}, opts).Decode(&document); err != nil {
		return errors.WithStack(err)
	}
	permissions := document.Permissions
	exportTypeArr := strings.Split(exportTypes, ",")
	for _, exportType := range exportTypeArr {
		if exportType == "1" { //项目构建轨迹
			index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.build")
			if index != -1 {
				//-1说明不存在
				filename, content, err := ExportReportTypeProjectConstructionExport(ctx, projectId, envId, roleId, now, startTime, endTime, exportType)
				if err != nil {
					return errors.WithStack(err)
				}
				//给excel1加密
				if cipher != "" {
					w, _ := zipWriter.Encrypt(filename, cipher)
					io.Copy(w, bytes.NewReader(content))
				} else {
					w, _ := zipWriter.Create(filename)
					io.Copy(w, bytes.NewReader(content))
				}
			}
		} else if exportType == "2" { //项目设置轨迹
			index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.settings")
			if index != -1 {
				//-1说明不存在
				filename, content, err := ExportReportTypeProjectConstructionExport(ctx, projectId, envId, roleId, now, startTime, endTime, exportType)
				if err != nil {
					return errors.WithStack(err)
				}
				//给excel1加密
				if cipher != "" {
					w, _ := zipWriter.Encrypt(filename, cipher)
					io.Copy(w, bytes.NewReader(content))
				} else {
					w, _ := zipWriter.Create(filename)
					io.Copy(w, bytes.NewReader(content))
				}
			}
		} else if exportType == "3" {
			index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.release-record")
			if index != -1 {
				//-1说明不存在
				filename, content, err := ExportReportTypeExampleTypeExport(ctx, projectId, envId, now, startTime, endTime, exportType, subjectIdList)
				if err != nil {
					return errors.WithStack(err)
				}
				//给excel1加密
				if cipher != "" {
					w, _ := zipWriter.Encrypt(filename, cipher)
					io.Copy(w, bytes.NewReader(content))
				} else {
					w, _ := zipWriter.Create(filename)
					io.Copy(w, bytes.NewReader(content))
				}
			}
		} else if exportType == "4" {
			index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.order")
			if index != -1 {
				//-1说明不存在
				filename, content, err := ExportReportTypeExampleTypeExport(ctx, projectId, envId, now, startTime, endTime, exportType, subjectIdList)
				if err != nil {
					return errors.WithStack(err)
				}
				//给excel1加密
				if cipher != "" {
					w, _ := zipWriter.Encrypt(filename, cipher)
					io.Copy(w, bytes.NewReader(content))
				} else {
					w, _ := zipWriter.Create(filename)
					io.Copy(w, bytes.NewReader(content))
				}
			}
		} else if exportType == "5" {
			index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.drug_recovery")
			if index != -1 {
				//-1说明不存在
				filename, content, err := ExportReportTypeExampleTypeExport(ctx, projectId, envId, now, startTime, endTime, exportType, subjectIdList)
				if err != nil {
					return errors.WithStack(err)
				}
				//给excel1加密
				if cipher != "" {
					w, _ := zipWriter.Encrypt(filename, cipher)
					io.Copy(w, bytes.NewReader(content))
				} else {
					w, _ := zipWriter.Create(filename)
					io.Copy(w, bytes.NewReader(content))
				}
			}
		} else if exportType == "6" && exportFormat == 1 {
			index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.subject")
			if index != -1 {
				//-1说明不存在
				filename, content, err := ExportReportTypeExampleTypeExport(ctx, projectId, envId, now, startTime, endTime, exportType, subjectIdList)
				if err != nil {
					return errors.WithStack(err)
				}
				//给excel1加密
				if cipher != "" {
					w, _ := zipWriter.Encrypt(filename, cipher)
					io.Copy(w, bytes.NewReader(content))
				} else {
					w, _ := zipWriter.Create(filename)
					io.Copy(w, bytes.NewReader(content))
				}
			}
		} else if exportType == "7" {
			index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.dispensing")
			if index != -1 {
				//-1说明不存在
				filename, content, err := ExportReportTypeExampleTypeExport(ctx, projectId, envId, now, startTime, endTime, exportType, subjectIdList)
				if err != nil {
					return errors.WithStack(err)
				}
				//给excel1加密
				if cipher != "" {
					w, _ := zipWriter.Encrypt(filename, cipher)
					io.Copy(w, bytes.NewReader(content))
				} else {
					w, _ := zipWriter.Create(filename)
					io.Copy(w, bytes.NewReader(content))
				}
			}
		} else if exportType == "8" {
			index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.ip")
			if index != -1 {
				//-1说明不存在
				filename, content, err := ExportReportTypeExampleTypeExport(ctx, projectId, envId, now, startTime, endTime, exportType, subjectIdList)
				if err != nil {
					return errors.WithStack(err)
				}
				//给excel1加密
				if cipher != "" {
					w, _ := zipWriter.Encrypt(filename, cipher)
					io.Copy(w, bytes.NewReader(content))
				} else {
					w, _ := zipWriter.Create(filename)
					io.Copy(w, bytes.NewReader(content))
				}
			}
		}
	}
	//直接调用close不要用defer
	zipWriter.Close()

	envOID, _ := primitive.ObjectIDFromHex(envId)
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"envs.id": envOID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}
	zipname := fmt.Sprintf("%s[%s]AuditTrail_%s.zip", project.Number, getEnvName(project, envOID), now.Format("20060102150405"))

	gridFS := tools.NewGridFS()
	fileId, err := gridFS.Upload(zipname, b.Bytes())
	if err != nil {
		return errors.WithStack(err)
	}

	if _, err := tools.Database.Collection(models.ReportHistoryTableName).InsertOne(context.Background(), models.ReportHistory{
		ID:           primitive.NewObjectID(),
		ReportType:   reportType,
		ProjectID:    poid,
		EnvID:        eoid,
		UserID:       user.ID,
		Filename:     zipname,
		FileID:       fileId,
		FileSize:     int64(len(b.Bytes())),
		DownloadTime: now.Unix(),
		Meta: models.Meta{
			CreatedBy: user.ID,
			CreatedAt: time.Duration(now.Unix()),
		},
	}); err != nil {
		return errors.WithStack(err)
	}

	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", url.PathEscape(zipname)))
	ctx.Data(200, "application/zip", b.Bytes())

	return nil
}

func (s *ReportService) ExportSubjectReport(ctx *gin.Context, reportType int, projectId string, envId string, customTemplateId string, cohortID string, roleId string, startTime int, endTime int, exportTypes string, cipher string, simulateRandomId string, exportFormat int, subject string) error {

	user, err := tools.Me(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))

	timezone := "08:00"
	offset := float64(8)
	if len(users) > 0 && users[0].Settings.Tz != "" {
		timezone, err = tools.GetLocationTimeZone(users[0].Settings.Tz)
		if err != nil {
			return errors.WithStack(err)
		}
		offset, err = tools.GetLocationFloat(users[0].Settings.Tz)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	if err != nil {
		return errors.WithStack(err)
	}

	poid, _ := primitive.ObjectIDFromHex(projectId)
	eoid, _ := primitive.ObjectIDFromHex(envId)
	now := time.Now().In(time.FixedZone(fmt.Sprintf("UTC%s", timezone), int(offset*60*60)))

	//压缩包名称
	b := new(bytes.Buffer)
	zipWriter := zip.NewWriter(b)

	if exportFormat == 2 && len(ctx.PostForm("length")) != 0 {
		// 将字符串转换为 int
		f, err := strconv.Atoi(ctx.PostForm("length"))
		if err != nil {
			fmt.Println("Error converting string to length:", err)
			return errors.WithStack(err)
		}

		for i := 0; i < f; i++ {

			// 获取文件头
			fileHeader, e := ctx.FormFile("file" + strconv.Itoa(i))
			if e != nil {
				ctx.JSON(http.StatusBadRequest, gin.H{"error": "Failed to get file"})
				return errors.WithStack(err)
			}

			// 打开文件
			file, e := fileHeader.Open()
			if e != nil {
				ctx.JSON(http.StatusBadRequest, gin.H{"error": "Failed to open file"})
				return errors.WithStack(err)
			}
			defer file.Close()

			// 读取文件数据
			fileData, e := io.ReadAll(file)
			if e != nil {
				ctx.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read file data"})
				return errors.WithStack(err)
			}

			filename := fileHeader.Filename + ".pdf"
			content := fileData
			//给excel1加密
			if cipher != "" {
				w, _ := zipWriter.Encrypt(filename, cipher)
				io.Copy(w, bytes.NewReader(content))
			} else {
				w, _ := zipWriter.Create(filename)
				io.Copy(w, bytes.NewReader(content))
			}

		}

	}

	subjectIdList := make([]primitive.ObjectID, 0)
	if len(subject) != 0 {
		// 使用 strings.Split() 函数将字符串按逗号分割成数组
		subjectIds := strings.Split(subject, ",")
		for _, id := range subjectIds {
			objectID, _ := primitive.ObjectIDFromHex(id)
			subjectIdList = append(subjectIdList, objectID)
		}
	}

	oid, _ := primitive.ObjectIDFromHex(roleId)
	var document models.ProjectRolePermission
	opts := &options.FindOneOptions{
		Projection: bson.M{"meta": 0},
	}
	if err := tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{"_id": oid}, opts).Decode(&document); err != nil {
		return errors.WithStack(err)
	}
	permissions := document.Permissions
	exportTypeArr := strings.Split(exportTypes, ",")
	for _, exportType := range exportTypeArr {
		if exportType == "1" { //项目构建轨迹
			index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.build")
			if index != -1 {
				//-1说明不存在
				filename, content, err := ExportReportTypeProjectConstructionExport(ctx, projectId, envId, roleId, now, startTime, endTime, exportType)
				if err != nil {
					return errors.WithStack(err)
				}
				//给excel1加密
				if cipher != "" {
					w, _ := zipWriter.Encrypt(filename, cipher)
					io.Copy(w, bytes.NewReader(content))
				} else {
					w, _ := zipWriter.Create(filename)
					io.Copy(w, bytes.NewReader(content))
				}
			}
		} else if exportType == "2" { //项目设置轨迹
			index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.settings")
			if index != -1 {
				//-1说明不存在
				filename, content, err := ExportReportTypeProjectConstructionExport(ctx, projectId, envId, roleId, now, startTime, endTime, exportType)
				if err != nil {
					return errors.WithStack(err)
				}
				//给excel1加密
				if cipher != "" {
					w, _ := zipWriter.Encrypt(filename, cipher)
					io.Copy(w, bytes.NewReader(content))
				} else {
					w, _ := zipWriter.Create(filename)
					io.Copy(w, bytes.NewReader(content))
				}
			}
		} else if exportType == "3" {
			index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.release-record")
			if index != -1 {
				//-1说明不存在
				filename, content, err := ExportReportTypeExampleTypeExport(ctx, projectId, envId, now, startTime, endTime, exportType, subjectIdList)
				if err != nil {
					return errors.WithStack(err)
				}
				//给excel1加密
				if cipher != "" {
					w, _ := zipWriter.Encrypt(filename, cipher)
					io.Copy(w, bytes.NewReader(content))
				} else {
					w, _ := zipWriter.Create(filename)
					io.Copy(w, bytes.NewReader(content))
				}
			}
		} else if exportType == "4" {
			index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.order")
			if index != -1 {
				//-1说明不存在
				filename, content, err := ExportReportTypeExampleTypeExport(ctx, projectId, envId, now, startTime, endTime, exportType, subjectIdList)
				if err != nil {
					return errors.WithStack(err)
				}
				//给excel1加密
				if cipher != "" {
					w, _ := zipWriter.Encrypt(filename, cipher)
					io.Copy(w, bytes.NewReader(content))
				} else {
					w, _ := zipWriter.Create(filename)
					io.Copy(w, bytes.NewReader(content))
				}
			}
		} else if exportType == "5" {
			index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.drug_recovery")
			if index != -1 {
				//-1说明不存在
				filename, content, err := ExportReportTypeExampleTypeExport(ctx, projectId, envId, now, startTime, endTime, exportType, subjectIdList)
				if err != nil {
					return errors.WithStack(err)
				}
				//给excel1加密
				if cipher != "" {
					w, _ := zipWriter.Encrypt(filename, cipher)
					io.Copy(w, bytes.NewReader(content))
				} else {
					w, _ := zipWriter.Create(filename)
					io.Copy(w, bytes.NewReader(content))
				}
			}
		} else if exportType == "6" && exportFormat == 1 {
			index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.subject")
			if index != -1 {
				//-1说明不存在
				filename, content, err := ExportReportTypeExampleTypeExport(ctx, projectId, envId, now, startTime, endTime, exportType, subjectIdList)
				if err != nil {
					return errors.WithStack(err)
				}
				//给excel1加密
				if cipher != "" {
					w, _ := zipWriter.Encrypt(filename, cipher)
					io.Copy(w, bytes.NewReader(content))
				} else {
					w, _ := zipWriter.Create(filename)
					io.Copy(w, bytes.NewReader(content))
				}
			}
		} else if exportType == "7" {
			index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.dispensing")
			if index != -1 {
				//-1说明不存在
				filename, content, err := ExportReportTypeExampleTypeExport(ctx, projectId, envId, now, startTime, endTime, exportType, subjectIdList)
				if err != nil {
					return errors.WithStack(err)
				}
				//给excel1加密
				if cipher != "" {
					w, _ := zipWriter.Encrypt(filename, cipher)
					io.Copy(w, bytes.NewReader(content))
				} else {
					w, _ := zipWriter.Create(filename)
					io.Copy(w, bytes.NewReader(content))
				}
			}
		} else if exportType == "8" {
			index := arrays.ContainsString(permissions, "operation.report.auditTrailExport.ip")
			if index != -1 {
				//-1说明不存在
				filename, content, err := ExportReportTypeExampleTypeExport(ctx, projectId, envId, now, startTime, endTime, exportType, subjectIdList)
				if err != nil {
					return errors.WithStack(err)
				}
				//给excel1加密
				if cipher != "" {
					w, _ := zipWriter.Encrypt(filename, cipher)
					io.Copy(w, bytes.NewReader(content))
				} else {
					w, _ := zipWriter.Create(filename)
					io.Copy(w, bytes.NewReader(content))
				}
			}
		}
	}
	//直接调用close不要用defer
	zipWriter.Close()

	envOID, _ := primitive.ObjectIDFromHex(envId)
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"envs.id": envOID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}
	zipname := fmt.Sprintf("%s[%s]AuditTrail_%s.zip", project.Number, getEnvName(project, envOID), now.Format("20060102150405"))

	gridFS := tools.NewGridFS()
	fileId, err := gridFS.Upload(zipname, b.Bytes())
	if err != nil {
		return errors.WithStack(err)
	}

	if _, err := tools.Database.Collection(models.ReportHistoryTableName).InsertOne(context.Background(), models.ReportHistory{
		ID:           primitive.NewObjectID(),
		ReportType:   reportType,
		ProjectID:    poid,
		EnvID:        eoid,
		UserID:       user.ID,
		Filename:     zipname,
		FileID:       fileId,
		FileSize:     int64(len(b.Bytes())),
		DownloadTime: now.Unix(),
		Meta: models.Meta{
			CreatedBy: user.ID,
			CreatedAt: time.Duration(now.Unix()),
		},
	}); err != nil {
		return errors.WithStack(err)
	}

	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", url.PathEscape(zipname)))
	ctx.Data(200, "application/zip", b.Bytes())

	return nil
}

func (s *ReportService) ExportSimulateRandomPdf(ctx *gin.Context, reportType int, projectId string, envId string, customTemplateId string, cohortID string, roleId string, startTime int, endTime int, exportTypes string, cipher string, simulateRandomId string) (models.SimulateRandomReport, error) {

	var simulateRandomReport models.SimulateRandomReport

	user, err := tools.Me(ctx)
	if err != nil {
		return simulateRandomReport, errors.WithStack(err)
	}
	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))

	timezone := "08:00"
	offset := float64(8)
	if len(users) > 0 && users[0].Settings.Tz != "" {
		timezone, err = tools.GetLocationTimeZone(users[0].Settings.Tz)
		if err != nil {
			return simulateRandomReport, errors.WithStack(err)
		}
		offset, err = tools.GetLocationFloat(users[0].Settings.Tz)
		if err != nil {
			return simulateRandomReport, errors.WithStack(err)
		}
	}
	if err != nil {
		return simulateRandomReport, errors.WithStack(err)
	}

	//poid, _ := primitive.ObjectIDFromHex(projectId)
	//eoid, _ := primitive.ObjectIDFromHex(envId)
	now := time.Now().In(time.FixedZone(fmt.Sprintf("UTC%s", timezone), int(offset*60*60)))

	//var (
	//	filename string
	//	content  models.ConfigureReport
	//)
	//
	////压缩包名称
	//b := new(bytes.Buffer)

	var parameter map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&parameter, binding.JSON)

	isBlindedRole, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return simulateRandomReport, errors.WithStack(err)
	}
	ctx.Set("isBlindedRole", isBlindedRole)

	simulateRandomReport, err = ExportSimulateRandomReportPdf(ctx, projectId, envId, cohortID, roleId, simulateRandomId, now)
	if err != nil {
		return simulateRandomReport, errors.WithStack(err)
	}

	return simulateRandomReport, nil
}

func (s *ReportService) ExportSimulateRandomPdfNew(ctx *gin.Context, reportType int, projectId string, envId string, customTemplateId string, cohortID string, roleId string, startTime int, endTime int, exportTypes string, cipher string, simulateRandomId string) error {

	var simulateRandomReport models.SimulateRandomReport

	user, err := tools.Me(ctx)
	if err != nil {
		return err
	}
	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))

	timezone := "08:00"
	offset := float64(8)
	if len(users) > 0 && users[0].Settings.Tz != "" {
		timezone, err = tools.GetLocationTimeZone(users[0].Settings.Tz)
		if err != nil {
			return errors.WithStack(err)
		}
		offset, err = tools.GetLocationFloat(users[0].Settings.Tz)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	if err != nil {
		return errors.WithStack(err)
	}

	//poid, _ := primitive.ObjectIDFromHex(projectId)
	//eoid, _ := primitive.ObjectIDFromHex(envId)
	now := time.Now().In(time.FixedZone(fmt.Sprintf("UTC%s", timezone), int(offset*60*60)))

	//var (
	//	filename string
	//	content  models.ConfigureReport
	//)
	//
	////压缩包名称
	//b := new(bytes.Buffer)

	var parameter map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&parameter, binding.JSON)

	isBlindedRole, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return errors.WithStack(err)
	}
	ctx.Set("isBlindedRole", isBlindedRole)

	simulateRandomReport, err = ExportSimulateRandomReportPdf(ctx, projectId, envId, cohortID, roleId, simulateRandomId, now)
	if err != nil {
		return err
	}

	pdfContent, err := tools.ExportReportPdf("simulate_random_report_content.html", simulateRandomReport)
	if err != nil {
		return err
	}

	return s.ExportSimulateRandomReportNew(ctx, reportType, projectId, envId, pdfContent)
}

func (s *ReportService) ExportSimulateRandomReport(ctx *gin.Context, reportType int, projectId string, envId string, customTemplateId string, cohortID string, roleId string, startTime int, endTime int, exportTypes string, cipher string, simulateRandomId string, pdfFile []byte) error {

	user, err := tools.Me(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))

	timezone := "08:00"
	offset := float64(8)
	if len(users) > 0 && users[0].Settings.Tz != "" {
		timezone, err = tools.GetLocationTimeZone(users[0].Settings.Tz)
		if err != nil {
			return errors.WithStack(err)
		}
		offset, err = tools.GetLocationFloat(users[0].Settings.Tz)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	if err != nil {
		return errors.WithStack(err)
	}

	//poid, _ := primitive.ObjectIDFromHex(projectId)
	//eoid, _ := primitive.ObjectIDFromHex(envId)
	now := time.Now().In(time.FixedZone(fmt.Sprintf("UTC%s", timezone), int(offset*60*60)))

	//var (
	//	filename string
	//	content  models.ConfigureReport
	//)
	//
	////压缩包名称
	//b := new(bytes.Buffer)

	//// 读取上传的 PDF 文件
	//file, _, err := ctx.Request.FormFile("pdfFile")
	//if err != nil {
	//	ctx.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read file"})
	//	return errors.WithStack(err)
	//}
	//defer file.Close()
	//
	//// 读取文件数据
	//fileData, err := io.ReadAll(file)
	//if err != nil {
	//	ctx.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read file data"})
	//	return errors.WithStack(err)
	//}

	projectOID, _ := primitive.ObjectIDFromHex(projectId)
	envOID, _ := primitive.ObjectIDFromHex(envId)

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"envs.id": envOID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}

	envCode := getEnvName(project, envOID)
	fileName := fmt.Sprintf("%s[%s]RandomizationSimulationReport_%s.pdf", project.Number, envCode, now.Format("20060102150405"))

	//IRT-4204【报表下载】报表导出，最近下载时间记录的是下载开始时间，不是完成时间
	lastIndex := strings.LastIndex(fileName, ".")
	fileName = fmt.Sprintf("%s%s%s", fileName[0:lastIndex-14], now.Format("20060102150405"), fileName[lastIndex:])

	content := pdfFile

	gridFS := tools.NewGridFS()
	fileId, err := gridFS.Upload(fileName, content)
	if err != nil {
		return errors.WithStack(err)
	}

	if _, err := tools.Database.Collection(models.ReportHistoryTableName).InsertOne(context.Background(), models.ReportHistory{
		ID:           primitive.NewObjectID(),
		ReportType:   reportType,
		ProjectID:    projectOID,
		EnvID:        envOID,
		UserID:       user.ID,
		Filename:     fileName,
		FileID:       fileId,
		FileSize:     int64(len(content)),
		DownloadTime: now.Unix(),
		Meta: models.Meta{
			CreatedBy: user.ID,
			CreatedAt: time.Duration(now.Unix()),
		},
	}); err != nil {
		return errors.WithStack(err)
	}

	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", url.PathEscape(fileName)))
	ctx.Data(200, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8", content)

	return nil
}

func (s *ReportService) ExportSimulateRandomReportNew(ctx *gin.Context, reportType int, projectId string, envId string, pdfFile []byte) error {
	user, err := tools.Me(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))

	timezone := "08:00"
	offset := float64(8)
	if len(users) > 0 && users[0].Settings.Tz != "" {
		timezone, err = tools.GetLocationTimeZone(users[0].Settings.Tz)
		if err != nil {
			return errors.WithStack(err)
		}
		offset, err = tools.GetLocationFloat(users[0].Settings.Tz)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	if err != nil {
		return errors.WithStack(err)
	}

	//poid, _ := primitive.ObjectIDFromHex(projectId)
	//eoid, _ := primitive.ObjectIDFromHex(envId)
	now := time.Now().In(time.FixedZone(fmt.Sprintf("UTC%s", timezone), int(offset*60*60)))

	//var (
	//	filename string
	//	content  models.ConfigureReport
	//)
	//
	////压缩包名称
	//b := new(bytes.Buffer)

	//// 读取上传的 PDF 文件
	//file, _, err := ctx.Request.FormFile("pdfFile")
	//if err != nil {
	//	ctx.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read file"})
	//	return errors.WithStack(err)
	//}
	//defer file.Close()
	//
	//// 读取文件数据
	//fileData, err := io.ReadAll(file)
	//if err != nil {
	//	ctx.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read file data"})
	//	return errors.WithStack(err)
	//}

	projectOID, _ := primitive.ObjectIDFromHex(projectId)
	envOID, _ := primitive.ObjectIDFromHex(envId)

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"envs.id": envOID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}

	envCode := getEnvName(project, envOID)
	fileName := fmt.Sprintf("%s[%s]RandomizationSimulationReport_%s.pdf", project.Number, envCode, now.Format("20060102150405"))

	//IRT-4204【报表下载】报表导出，最近下载时间记录的是下载开始时间，不是完成时间
	lastIndex := strings.LastIndex(fileName, ".")
	fileName = fmt.Sprintf("%s%s%s", fileName[0:lastIndex-14], now.Format("20060102150405"), fileName[lastIndex:])

	content := pdfFile

	gridFS := tools.NewGridFS()
	fileId, err := gridFS.Upload(fileName, content)
	if err != nil {
		return errors.WithStack(err)
	}

	if _, err := tools.Database.Collection(models.ReportHistoryTableName).InsertOne(context.Background(), models.ReportHistory{
		ID:           primitive.NewObjectID(),
		ReportType:   reportType,
		ProjectID:    projectOID,
		EnvID:        envOID,
		UserID:       user.ID,
		Filename:     fileName,
		FileID:       fileId,
		FileSize:     int64(len(content)),
		DownloadTime: now.Unix(),
		Meta: models.Meta{
			CreatedBy: user.ID,
			CreatedAt: time.Duration(now.Unix()),
		},
	}); err != nil {
		return errors.WithStack(err)
	}

	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", url.PathEscape(fileName)))
	ctx.Data(200, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8", content)

	return nil
}
