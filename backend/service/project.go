package service

import (
	"clinflash-irt/config"
	"clinflash-irt/data"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/task"
	"clinflash-irt/tools"
	"clinflash-irt/ws"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"reflect"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/wxnacy/wgo/arrays"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin/binding"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/xuri/excelize/v2"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ProjectService struct {
	raveService RaveService
}

func (s *ProjectService) Update(ctx *gin.Context, id string, project models.Project) error {
	//如果开启了揭盲控制，揭盲短信 揭盲流程操作 揭盲码 pv揭盲短信 pv揭盲流程操作 至少五选一
	if project.UnblindingControl == 1 &&
		project.UnblindingProcess == 0 && project.UnblindingSms == 0 && project.UnblindingCode == 0 &&
		project.PvUnblindingSms == 0 && project.PvUnblindingProcess == 0 &&
		project.IpUnblindingSms == 0 && project.IpUnblindingProcess == 0 {
		return tools.BuildServerError(ctx, "common_configuration_error")
	}
	//如果开启了审批控制，审批短信、审批流程操作至少二选一
	if project.OrderApprovalControl == 1 {
		project.OrderApprovalProcess = 1
	}

	var oldProject models.Project

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		collection := tools.Database.Collection("project")
		oid, _ := primitive.ObjectIDFromHex(id)
		err := tools.Database.Collection("project").FindOne(sctx, bson.M{"_id": oid}).Decode(&oldProject)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		//rava映射配置
		var visitRandomization models.VisitRandomization
		if project.EdcSupplier == 2 {
			visitRandomization = project.VisitRandomization
		}
		// 订单核查自定义执行时间，非定时核查保持原配置
		orderCheckDay := oldProject.ProjectInfo.OrderCheckDay
		orderCheckTime := oldProject.ProjectInfo.OrderCheckTime
		if project.ProjectInfo.OrderCheck == 1 {
			orderCheckDay = project.ProjectInfo.OrderCheckDay
			orderCheckTime = project.ProjectInfo.OrderCheckTime
		}

		update := bson.M{"$set": bson.M{
			"status":                           project.Status,
			"administrators":                   project.Administrators,
			"info.phone":                       project.Phone,
			"info.name":                        project.Name,
			"info.sponsor":                     project.Sponsor,
			"info.connect_edc":                 project.ConnectEdc,
			"info.edc_supplier":                project.EdcSupplier,
			"info.visit_Randomization":         visitRandomization,
			"info.push_mode":                   project.PushMode,
			"info.synchronization_mode":        project.SynchronizationMode,
			"info.edc_url":                     project.EdcUrl,
			"info.push_rules":                  project.PushRules,
			"info.push_scenario.register_push": project.PushScenario.RegisterPush,
			"info.push_scenario.update_random_front_push": project.PushScenario.UpdateRandomFrontPush,
			"info.push_scenario.update_random_after_push": project.PushScenario.UpdateRandomAfterPush,
			"info.push_scenario.random_push":              project.PushScenario.RandomPush,
			"info.push_scenario.random_block_push":        project.PushScenario.RandomBlockPush,
			"info.push_scenario.form_random_block_push":   project.PushScenario.FormRandomBlockPush,
			"info.push_scenario.cohort_random_block_push": project.PushScenario.CohortRandomBlockPush,
			"info.push_scenario.dispensing_push":          project.PushScenario.DispensingPush,
			"info.push_scenario.screen_push":              project.PushScenario.ScreenPush,
			"info.description":                            project.Description,
			"info.timeZoneStr":                            project.TimeZoneStr,
			"info.tz":                                     project.Tz,
			"info.connect_learning":                       project.ConnectLearning,
			"info.order_check":                            project.OrderCheck,
			"info.order_check_day":                        orderCheckDay,
			"info.order_check_time":                       orderCheckTime,
			"info.order_confirmation":                     project.OrderConfirmation,
			"info.de_isolation_approval":                  project.DeIsolationApproval,
			"info.unblinding_control":                     project.UnblindingControl,
			"info.unblinding_type":                        project.UnblindingType,
			"info.unblinding_sms":                         project.UnblindingSms,
			"info.unblinding_process":                     project.UnblindingProcess,
			"info.unblinding_code":                        project.UnblindingCode,
			"info.pv_unblinding_type":                     project.PvUnblindingType,
			"info.pv_unblinding_sms":                      project.PvUnblindingSms,
			"info.pv_unblinding_process":                  project.PvUnblindingProcess,
			"info.ip_unblinding_type":                     project.IpUnblindingType,
			"info.ip_unblinding_sms":                      project.IpUnblindingSms,
			"info.ip_unblinding_process":                  project.IpUnblindingProcess,
			"info.order_approval_control":                 project.OrderApprovalControl,
			"info.order_approval_sms":                     project.OrderApprovalSms,
			"info.order_approval_process":                 project.OrderApprovalProcess,
			"info.need_learning":                          project.NeedLearning,
			"info.need_learning_env":                      project.NeedLearningEnv,
			"info.start_time":                             project.StartTime,
			"info.end_time":                               project.EndTime,
			"info.status_reason":                          project.ProjectInfo.StatusReason,
			"info.system_courses":                         project.ProjectInfo.SystemCourses,
			"info.need_system_courses":                    project.ProjectInfo.NeedSystemCourses,
		}}
		if _, err := collection.UpdateOne(sctx, bson.M{"_id": oid}, update); err != nil {
			return nil, errors.WithStack(err)
		}
		projectAdminRoleId := struct {
			ID primitive.ObjectID `bson:"_id"`
		}{}
		err = tools.Database.Collection("project_role_permission").FindOne(sctx, bson.M{
			"project_id": oid,
			"name":       "Project-Admin",
		}, &options.FindOneOptions{
			Projection: bson.M{"_id": 1},
		}).Decode(&projectAdminRoleId)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		//查询项目下的人员
		envUsers := make([]models.UserProjectEnvironment, 0)
		cursor, err := tools.Database.Collection("user_project_environment").Find(sctx, bson.M{"project_id": oid})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(sctx, &envUsers)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		//分配管理员
		insertUserProjectEnvs := make([]interface{}, 0)
		for _, env := range oldProject.Environments {
			for _, adminUserId := range project.Administrators {
				count := slice.Count(envUsers, func(index int, item models.UserProjectEnvironment) bool {
					return item.UserID == adminUserId && item.EnvID == env.ID && item.ProjectID == project.ID
				})
				if count == 0 {
					insertUserProjectEnvs = append(insertUserProjectEnvs, models.UserProjectEnvironment{
						ID:             primitive.NewObjectID(),
						CustomerID:     project.CustomerID,
						ProjectID:      project.ID,
						EnvID:          env.ID,
						UserID:         adminUserId,
						Roles:          []primitive.ObjectID{},
						App:            false,
						UnblindingCode: models.UnblindingCode{},
					})
				}
			}
		}
		if len(insertUserProjectEnvs) > 0 {
			_, err := tools.Database.Collection("user_project_environment").InsertMany(sctx, insertUserProjectEnvs)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			cursor, err := tools.Database.Collection("user_project_environment").Find(sctx, bson.M{"project_id": oid})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(sctx, &envUsers)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		for _, envUser := range envUsers {
			contain := slice.Contain(project.Administrators, envUser.UserID)
			if contain {
				_, err = tools.Database.Collection("user_project_environment").UpdateOne(sctx,
					bson.M{"_id": envUser.ID},
					bson.M{"$addToSet": bson.M{"roles": projectAdminRoleId.ID}})
				if err != nil {
					return nil, errors.WithStack(err)
				}
			} else {
				_, err = tools.Database.Collection("user_project_environment").UpdateOne(sctx,
					bson.M{"_id": envUser.ID},
					bson.M{"$pull": bson.M{"roles": projectAdminRoleId.ID}})
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}
		}

		//项目关闭删除除Admin外关联人员账号
		if project.Status == models.ProjectStatusClose {
			var adminRole models.ProjectRolePermission
			err := tools.Database.Collection("project_role_permission").FindOne(sctx, bson.M{
				"project_id": oid,
				"name":       "Project-Admin",
			}).Decode(&adminRole)
			if err != nil {
				if err != mongo.ErrNoDocuments {
					return nil, errors.WithStack(err)
				}
			} else {
				if err != mongo.ErrNoDocuments {
					_, err := tools.Database.Collection("user_project_environment").UpdateMany(sctx, bson.M{
						"project_id": oid,
						"roles": bson.M{
							"$ne": adminRole.ID,
						},
					}, bson.M{"$set": bson.M{"unbind": true}})
					if err != nil {
						return nil, errors.WithStack(err)
					}
				}
			}
		}
		//插入操作日志
		insertProjectInfoLog(ctx, sctx, oid, oldProject, project, oid)

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *ProjectService) Delete(ctx *gin.Context, id string) error {
	oid, _ := primitive.ObjectIDFromHex(id)
	_, err := tools.Database.Collection("project").DeleteOne(nil, bson.M{"_id": oid})
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *ProjectService) Get(ctx *gin.Context, id string) (models.Project, error) {
	oid, _ := primitive.ObjectIDFromHex(id)
	var document models.Project
	opts := &options.FindOneOptions{
		Projection: bson.M{"meta": 0},
	}
	if err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": oid}, opts).Decode(&document); err != nil {
		return document, errors.WithStack(err)
	}
	return document, nil
}

func (s *ProjectService) Find(ctx *gin.Context, id string) (models.Project, error) {
	oid, _ := primitive.ObjectIDFromHex(id)
	var document models.Project
	opts := &options.FindOneOptions{
		Projection: nil, // 或者不为其赋值
	}
	if err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": oid}, opts).Decode(&document); err != nil {
		return document, errors.WithStack(err)
	}
	return document, nil
}

func (s *ProjectService) AddAdministrator(ctx *gin.Context, projectID string, userID string) error {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	userOID, _ := primitive.ObjectIDFromHex(userID)
	filter := bson.M{"_id": projectOID}
	update := bson.M{"$addToSet": bson.M{"administrators": userOID}}
	_, err := tools.Database.Collection("project").UpdateOne(nil, filter, update)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *ProjectService) RemoveAdministrator(ctx *gin.Context, projectID string, userID string) error {
	// TODO 检查当前用户是否为此客户管理员
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	userOID, _ := primitive.ObjectIDFromHex(userID)
	filter := bson.M{"_id": projectOID}
	update := bson.M{"$pull": bson.M{"administrators": userOID}}
	_, err := tools.Database.Collection("project").UpdateOne(nil, filter, update)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *ProjectService) AddEnvironment(ctx *gin.Context) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		req := struct {
			ProjectId       primitive.ObjectID      `json:"projectId"`
			EnvName         string                  `json:"env"`
			AlertThresholds []models.AlertThreshold `json:"alertThresholds" bson:"alert_thresholds"`
			Status          *int                    `json:"status" bson:"status"`
		}{}
		err := ctx.ShouldBindBodyWith(&req, binding.JSON)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		me, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		collection := tools.Database.Collection("project")
		if count, _ := collection.CountDocuments(sctx, bson.M{"_id": req.ProjectId, "envs.name": req.EnvName}); count > 0 {
			return nil, tools.BuildServerError(ctx, "environment.duplicated.names")
		}
		envID := primitive.NewObjectID()
		environment := models.Environment{
			ID:              envID,
			Name:            req.EnvName,
			AlertThresholds: req.AlertThresholds,
			Status:          req.Status,
			Cohorts:         []models.Cohort{},
		}
		update := bson.M{
			"$push": bson.M{
				"envs": environment,
			},
		}
		if _, err := collection.UpdateOne(sctx, bson.M{"_id": req.ProjectId}, update); err != nil {
			return nil, errors.WithStack(err)
		}
		//新增编码初始化配置
		var project models.Project
		_ = collection.FindOne(sctx, bson.M{"_id": req.ProjectId}).Decode(&project)
		codeRule := 0
		codeConfigInit := false
		if project.ResearchAttribute == 1 {
			codeConfigInit = true
		}
		barcodeRule := models.BarcodeRule{
			ID:             primitive.NewObjectID(),
			CustomerID:     project.CustomerID,
			ProjectID:      req.ProjectId,
			EnvironmentID:  environment.ID,
			CohortID:       primitive.NilObjectID,
			CodeRule:       codeRule,
			CodeConfigInit: codeConfigInit,
		}
		if _, err := tools.Database.Collection("barcode_rule").InsertOne(sctx, barcodeRule); err != nil {
			return nil, errors.WithStack(err)
		}
		//新增属性初始化配置
		random := false
		dispensing := false
		if project.ResearchAttribute == 1 {
			random = true
			dispensing = true
		}
		configs := []models.UnblindingReasonConfig{
			{Reason: "SAE", AllowRemark: false},
			{Reason: "妊娠", AllowRemark: false},
			{Reason: "政策要求", AllowRemark: false},
			{Reason: "其他", AllowRemark: true},
		}
		attribute := models.Attribute{
			ID:            primitive.NewObjectID(),
			CustomerID:    project.CustomerID,
			ProjectID:     req.ProjectId,
			EnvironmentID: environment.ID,
			CohortID:      primitive.NilObjectID,
			AttributeInfo: models.AttributeInfo{
				Random:                   random,
				IsRandomNumber:           false,
				Dispensing:               dispensing,
				Blind:                    false,
				InstituteLayered:         false,
				Prefix:                   false,
				PrefixExpression:         "",
				SubjectReplaceText:       "",
				Digit:                    0,
				Accuracy:                 0,
				Field:                    models.Field{},
				PrefixSymbol:             "",
				IsFreeze:                 false,
				IsRandom:                 false,
				EdcDrugConfigLabel:       "",
				Segment:                  false,
				CountryLayered:           false,
				UnblindingReasonConfig:   configs,
				UnBlindingRestrictions:   true,
				PvUnBlindingRestrictions: false,
				IsScreen:                 false,
				AllowReplace:             true,
				MinimizeCalc:             0,
			},
		}
		if project.Type == 1 {
			if _, err := tools.Database.Collection("attribute").InsertOne(sctx, attribute); err != nil {
				return nil, errors.WithStack(err)
			}
		}

		//通知初始化配置
		err = initNoticeConfig(sctx, project.CustomerID, req.ProjectId, environment.ID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		type ProjectAdmins struct {
			ID         primitive.ObjectID   `bson:"_id"`
			Admins     []primitive.ObjectID `bson:"administrators"`
			CustomerId primitive.ObjectID   `bson:"customer_id"`
		}
		projectAdmins := ProjectAdmins{}
		opts := &options.FindOneOptions{
			Projection: bson.M{
				"_id":            1,
				"administrators": 1,
				"customer_id":    1,
			},
		}
		err = tools.Database.Collection("project").FindOne(sctx, bson.M{"_id": req.ProjectId}, opts).Decode(&projectAdmins)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		projectRoleId := struct {
			ID primitive.ObjectID `bson:"_id"`
		}{}
		err = tools.Database.Collection("project_role_permission").FindOne(sctx, bson.M{"" +
			"project_id": projectAdmins.ID,
			"name": "Project-Admin",
		}, &options.FindOneOptions{
			Projection: bson.M{"_id": 1},
		}).Decode(&projectRoleId)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		envUsers := make([]interface{}, 0)
		envUsers = slice.Map(projectAdmins.Admins, func(index int, item primitive.ObjectID) interface{} {
			return bson.M{
				"customer_id": projectAdmins.CustomerId,
				"project_id":  projectAdmins.ID,
				"env_id":      envID,
				"user_id":     item,
				"roles":       []primitive.ObjectID{projectRoleId.ID},
				"meta": models.Meta{
					CreatedBy: me.ID,
					CreatedAt: time.Duration(time.Now().Unix()),
				},
			}
		})
		if len(envUsers) > 0 {
			_, err = tools.Database.Collection("user_project_environment").InsertMany(sctx, envUsers)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		//插入操作日志
		err = insertAddProjectEnvLog(ctx, sctx, 1, req.ProjectId, models.Environment{}, models.Environment{Name: req.EnvName, AlertThresholds: req.AlertThresholds}, req.ProjectId)
		if err != nil {
			return nil, err
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *ProjectService) EditEnvironment(ctx *gin.Context) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		req := struct {
			ProjectId       primitive.ObjectID      `json:"projectId"`
			EnvId           primitive.ObjectID      `json:"envId"`
			EnvName         string                  `json:"envName"`
			AlertThresholds []models.AlertThreshold `json:"alertThresholds" bson:"alert_thresholds"`
			Status          *int                    `json:"status" bson:"status"`
			Password        *string                 `json:"password" bson:"password"`
		}{}
		err := ctx.ShouldBindBodyWith(&req, binding.JSON)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if req.Password != nil {
			me, err := tools.Me(ctx)
			if err != nil {
				return nil, err
			}
			err = tools.PasswordDetection(ctx, me.Email, *req.Password)
			if err != nil {
				return nil, err
			}
		}
		project := models.Project{}
		err = tools.Database.Collection("project").FindOne(sctx, bson.M{"_id": req.ProjectId}).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		_, b := slice.Find(project.Environments, func(index int, item models.Environment) bool {
			return item.ID != req.EnvId && item.Name == req.EnvName
		})
		if b {
			return nil, tools.BuildServerError(ctx, "environment.duplicated.names")
		}
		oldEnv, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
			return item.ID == req.EnvId
		})
		if project.Type == 1 {
			attribute := models.Attribute{}
			err = tools.Database.Collection("attribute").FindOne(sctx, bson.M{"env_id": req.EnvId}).Decode(&attribute)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if attribute.AttributeInfo.Dispensing && !attribute.AttributeInfo.Random {
				types := slice.Map(req.AlertThresholds, func(i int, item models.AlertThreshold) int {
					return item.Type
				})
				if (slice.Contain(types, 1) && slice.Contain(types, 3)) || (slice.Contain(types, 7) && slice.Contain(types, 3)) {
					return nil, tools.BuildServerError(ctx, "environment.alertThresholds.limitError")
				}
			}
		}

		// 修改操作
		update := bson.M{
			"$set": bson.M{
				"envs.$[env].name":             req.EnvName,
				"envs.$[env].alert_thresholds": req.AlertThresholds,
				"envs.$[env].status":           req.Status,
			},
		}
		opts := &options.UpdateOptions{
			ArrayFilters: &options.ArrayFilters{
				Filters: bson.A{bson.M{"env.id": req.EnvId}},
			},
		}
		if _, err := tools.Database.Collection("project").UpdateOne(sctx, bson.M{"_id": req.ProjectId}, update, opts); err != nil {
			return nil, errors.WithStack(err)
		}
		//插入操作日志
		err = insertAddProjectEnvLog(ctx, sctx, 2, req.ProjectId,
			models.Environment{
				Name:            oldEnv.Name,
				AlertThresholds: oldEnv.AlertThresholds,
				Status:          oldEnv.Status,
			},
			models.Environment{
				Name:            req.EnvName,
				AlertThresholds: req.AlertThresholds,
				Status:          req.Status,
			},
			req.ProjectId)
		if err != nil {
			return nil, err
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *ProjectService) DeleteEnvironment(ctx *gin.Context, projectID string, envID string) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		projectOID, _ := primitive.ObjectIDFromHex(projectID)
		envOID, _ := primitive.ObjectIDFromHex(envID)
		update := bson.M{
			"$pull": bson.M{
				"envs": bson.M{
					"id": envOID,
				},
			},
		}
		_, err := tools.Database.Collection("project").UpdateOne(nil, bson.M{"_id": projectOID}, update)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		//删除编码初始化配置
		var project models.Project
		_ = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
		filter := bson.M{
			"customer_id": project.CustomerID,
			"project_id":  projectID,
			"env_id":      envOID,
		}
		if _, err := tools.Database.Collection("barcode_rule").DeleteMany(nil, filter); err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *ProjectService) GetProjectEnvIsBlind(ctx *gin.Context, projectID string, envID string) (bool, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	blind := false
	//查询
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return blind, errors.WithStack(err)
	}

	match := bson.M{"project_id": projectOID, "env_id": envOID}
	if project.Type != 1 {
		cohortIds := make([]primitive.ObjectID, 0)
		// 筛选环境
		for _, env := range project.Environments {
			if env.ID == envOID {
				for _, cohort := range env.Cohorts {
					cohortIds = append(cohortIds, cohort.ID)
				}
				match = bson.M{"project_id": projectOID, "env_id": envOID, "cohort_id": bson.M{"$in": cohortIds}}
				break
			}
		}
	}

	attributes := make([]models.Attribute, 0)
	cursor, err := tools.Database.Collection("attribute").Find(ctx, match)
	if err != nil {
		return blind, errors.WithStack(err)
	}
	err = cursor.All(nil, &attributes)
	if err != nil {
		return blind, errors.WithStack(err)
	}
	if len(attributes) > 0 {
		for _, attribute := range attributes {
			if attribute.AttributeInfo.Blind {
				blind = true
				return blind, nil
			}
		}
	}
	return blind, nil
}

func (s *ProjectService) AddCohort(ctx *gin.Context, projectID string, envID string, cohortData models.Cohort) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		collection := tools.Database.Collection("project")
		projectOID, _ := primitive.ObjectIDFromHex(projectID)
		envOID, _ := primitive.ObjectIDFromHex(envID)

		// 查询项目
		projectFilter := bson.M{"_id": projectOID}
		var atRandomProject models.Project
		err := tools.Database.Collection("project").FindOne(ctx, projectFilter).Decode(&atRandomProject)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 如果是在随机
		if atRandomProject.Type == 3 {
			for _, env := range atRandomProject.Environments {
				if env.ID == envOID {
					// 再随机只能添加到两个阶段
					if len(env.Cohorts) >= 2 {
						return nil, tools.BuildServerError(ctx, "project_at_random_error")
					}
				}
			}
		} else if atRandomProject.Type == 2 && cohortData.Type == 1 {
			for _, env := range atRandomProject.Environments {
				if env.ID == envOID {
					// 直接统计同名的 cohort 数量
					count := slice.Count(env.Cohorts, func(index int, item models.Cohort) bool {
						return item.Name == cohortData.Name
					})
					if count > 1 {
						return nil, tools.BuildServerError(ctx, "project_at_random_error")
					}
					break
				}
			}
		}
		if cohortData.Type == 0 {
			// 判断名称是否重复
			filterName := bson.M{
				"_id": projectOID,
				"envs": bson.M{
					"$elemMatch": bson.M{
						"id": envOID,
						"cohorts": bson.M{
							"$elemMatch": bson.M{
								"name": cohortData.Name,
							},
						},
					},
				},
			}
			if countName, _ := collection.CountDocuments(sctx, filterName); countName > 0 {
				return nil, tools.BuildServerError(ctx, "common.duplicated.names")
			}
		} else if cohortData.Type == 1 {
			// 判断名称是否重复
			filterName := bson.M{
				"_id": projectOID,
				"envs": bson.M{
					"$elemMatch": bson.M{
						"id": envOID,
						"cohorts": bson.M{
							"$elemMatch": bson.M{
								"name":           cohortData.Name,
								"re_random_name": cohortData.ReRandomName,
							},
						},
					},
				},
			}
			if countName, _ := collection.CountDocuments(sctx, filterName); countName > 0 {
				return nil, tools.BuildServerError(ctx, "common.duplicated.names")
			}
		}

		// 判断因素是否重复
		if cohortData.Factor != "" {
			filterFactor := bson.M{
				"_id": projectOID,
				"envs": bson.M{
					"$elemMatch": bson.M{
						"id": envOID,
						"cohorts": bson.M{
							"$elemMatch": bson.M{
								"factor": cohortData.Factor,
							},
						},
					},
				},
			}
			if countFactor, _ := collection.CountDocuments(sctx, filterFactor); countFactor > 0 {
				return nil, tools.BuildServerError(ctx, "common.duplicated.factors")

			}
		}

		// 添加操作
		cohortData.ID = primitive.NewObjectID()
		update := bson.M{
			"$push": bson.M{
				"envs.$[env].cohorts": cohortData,
			},
		}
		opts := &options.UpdateOptions{
			ArrayFilters: &options.ArrayFilters{
				Filters: bson.A{bson.M{"env.id": envOID}},
			},
		}
		if _, err := collection.UpdateOne(sctx, bson.M{"_id": projectOID}, update, opts); err != nil {
			return nil, errors.WithStack(err)
		}

		var project models.Project
		err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		var envName = ""
		var envCohorts []models.Cohort
		for _, environment := range project.Environments {
			if environment.ID == envOID {
				envName = environment.Name
				envCohorts = environment.Cohorts
			}
		}
		err = initEnvCohort(sctx, project, projectOID, envOID, cohortData, err)
		if err != nil {
			return nil, err
		}

		var old models.Cohort
		err = insertUpdateProjectEnvCohort(ctx, sctx, projectOID, 1, project.Type, "", envName, old, cohortData, projectOID, envCohorts)
		if err != nil {
			return nil, err
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func initEnvCohort(sctx mongo.SessionContext, project models.Project, projectOID primitive.ObjectID, envOID primitive.ObjectID, cohortData models.Cohort, err error) error {
	//新增编码初始化配置
	codeRule := 0
	codeConfigInit := false
	if project.ResearchAttribute == 1 {
		codeConfigInit = true
	}
	barcodeRule := models.BarcodeRule{
		ID:             primitive.NewObjectID(),
		CustomerID:     project.CustomerID,
		ProjectID:      projectOID,
		EnvironmentID:  envOID,
		CohortID:       cohortData.ID,
		CodeRule:       codeRule,
		CodeConfigInit: codeConfigInit,
	}
	if _, err := tools.Database.Collection("barcode_rule").InsertOne(sctx, barcodeRule); err != nil {
		return errors.WithStack(err)
	}
	random := false
	dispensing := false
	if project.ResearchAttribute == 1 {
		random = true
		dispensing = true
	}
	if (project.Type == 3 && !tools.InRandomIsolation(project.Number)) || (project.Type == 2 && cohortData.Type == 1) {
		random = true
	}
	configs := []models.UnblindingReasonConfig{
		{Reason: "SAE", AllowRemark: false},
		{Reason: "妊娠", AllowRemark: false},
		{Reason: "政策要求", AllowRemark: false},
		{Reason: "其他", AllowRemark: true},
	}
	attribute := models.Attribute{
		ID:            primitive.NewObjectID(),
		CustomerID:    project.CustomerID,
		ProjectID:     projectOID,
		EnvironmentID: envOID,
		CohortID:      cohortData.ID,
		AttributeInfo: models.AttributeInfo{
			Random:                 random,
			IsRandomNumber:         false,
			Dispensing:             dispensing,
			Blind:                  false,
			InstituteLayered:       false,
			Prefix:                 false,
			PrefixExpression:       "",
			SubjectReplaceText:     "",
			Digit:                  0,
			Accuracy:               0,
			Field:                  models.Field{},
			PrefixSymbol:           "",
			IsFreeze:               false,
			IsRandom:               false,
			EdcDrugConfigLabel:     "",
			Segment:                false,
			CountryLayered:         false,
			UnblindingReasonConfig: configs,
			AllowReplace:           true,
			MinimizeCalc:           0,
		},
	}
	_, err = tools.Database.Collection("attribute").InsertOne(sctx, attribute)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *ProjectService) UpdateCohort(ctx *gin.Context, projectID string, envID string, cohortID string, cohortData models.UpdateCohort) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		collection := tools.Database.Collection("project")
		projectOID, _ := primitive.ObjectIDFromHex(projectID)
		envOID, _ := primitive.ObjectIDFromHex(envID)
		cohortOID, _ := primitive.ObjectIDFromHex(cohortID)

		var oldCohortData models.Cohort
		envName := ""
		var project models.Project
		err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		attribute := models.Attribute{}
		err = tools.Database.Collection("attribute").FindOne(sctx, bson.M{"env_id": envOID, "cohort_id": cohortOID}).Decode(&attribute)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		if attribute.AttributeInfo.Dispensing && !attribute.AttributeInfo.Random {
			types := slice.Map(cohortData.AlertThresholds, func(i int, item models.AlertThreshold) int {
				return item.Type
			})
			if (slice.Contain(types, 1) && slice.Contain(types, 3)) || (slice.Contain(types, 7) && slice.Contain(types, 3)) {
				return nil, tools.BuildServerError(ctx, "environment.alertThresholds.limitError")
			}
		}
		for _, environment := range project.Environments {
			if environment.ID == envOID {
				envName = environment.Name
				for _, cohort := range environment.Cohorts {
					if cohort.ID == cohortOID {
						oldCohortData = cohort
					}
				}
			}
		}

		// 判断名称是否重复
		if cohortData.Type == 0 {
			// 判断名称是否重复
			filterName := bson.M{
				"_id": projectOID,
				"envs": bson.M{
					"$elemMatch": bson.M{
						"id": envOID,
						"cohorts": bson.M{
							"$elemMatch": bson.M{
								"name": cohortData.Name,
							},
						},
					},
				},
			}
			if countName, _ := collection.CountDocuments(sctx, filterName); countName > 1 {
				return nil, tools.BuildServerError(ctx, "common.duplicated.names")
			}
		} else if cohortData.Type == 1 {
			// 判断名称是否重复
			filterName := bson.M{
				"_id": projectOID,
				"envs": bson.M{
					"$elemMatch": bson.M{
						"id": envOID,
						"cohorts": bson.M{
							"$elemMatch": bson.M{
								"name":           cohortData.Name,
								"re_random_name": cohortData.ReRandomName,
							},
						},
					},
				},
			}
			if countName, _ := collection.CountDocuments(sctx, filterName); countName > 2 {
				return nil, tools.BuildServerError(ctx, "common.duplicated.names")
			}
		}

		// 判断因素是否重复
		if cohortData.Factor != "" {
			filterFactor := bson.M{
				"_id": projectOID,
				"envs": bson.M{
					"$elemMatch": bson.M{
						"id": envOID,
						"cohorts": bson.M{
							"$elemMatch": bson.M{
								"id":     bson.M{"$ne": cohortOID},
								"factor": cohortData.Factor,
							},
						},
					},
				},
			}
			if countFactor, _ := collection.CountDocuments(sctx, filterFactor); countFactor > 0 {
				return nil, tools.BuildServerError(ctx, "common.duplicated.factors")

			}
		}
		user, _ := tools.Me(ctx)
		//判断密码是否正确
		if cohortData.Password != "" {
			err := tools.PasswordDetection(ctx, user.Email, cohortData.Password)
			if err != nil {
				return nil, err
			}
		}
		// 修改操作
		update := bson.M{
			"$set": bson.M{
				"envs.$[env].cohorts.$[cohort].last_id":          cohortData.LastID,
				"envs.$[env].cohorts.$[cohort].type":             cohortData.Type,
				"envs.$[env].cohorts.$[cohort].name":             cohortData.Name,
				"envs.$[env].cohorts.$[cohort].alert_thresholds": cohortData.AlertThresholds,
				"envs.$[env].cohorts.$[cohort].factor":           cohortData.Factor,
				"envs.$[env].cohorts.$[cohort].status":           cohortData.Status,
				"envs.$[env].cohorts.$[cohort].re_random_name":   cohortData.ReRandomName,
			},
		}
		opts := &options.UpdateOptions{
			ArrayFilters: &options.ArrayFilters{
				Filters: bson.A{bson.M{"env.id": envOID}, bson.M{"cohort.id": cohortOID}},
			},
		}
		if _, err := collection.UpdateOne(sctx, bson.M{"_id": projectOID}, update, opts); err != nil {
			return nil, errors.WithStack(err)
		}
		var envCohorts []models.Cohort
		if project.Type == 3 && cohortData.LastID != primitive.NilObjectID {
			for _, environment := range project.Environments {
				if environment.ID == envOID {
					envCohorts = environment.Cohorts
				}
			}
		}
		var newCohortData models.Cohort
		newCohortData.Name = cohortData.Name
		newCohortData.LastID = cohortData.LastID
		newCohortData.Status = int(cohortData.Status)
		newCohortData.AlertThresholds = cohortData.AlertThresholds

		err = insertUpdateProjectEnvCohort(ctx, sctx, projectOID, 2, project.Type, "", envName, oldCohortData, newCohortData, projectOID, envCohorts)
		if err != nil {
			return nil, err
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *ProjectService) DeleteCohort(ctx *gin.Context, projectID string, envID string, cohortID string) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		projectOID, _ := primitive.ObjectIDFromHex(projectID)
		envOID, _ := primitive.ObjectIDFromHex(envID)
		cohortOID, _ := primitive.ObjectIDFromHex(cohortID)

		//添加轨迹
		var project models.Project
		_ = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
		user, _ := tools.Me(ctx)
		var histories []models.History
		var key = "history.project.cohort.delete"
		var cohortName = ""
		var envName = ""
		for _, environment := range project.Environments {
			if environment.ID == envOID {
				for _, cohort := range environment.Cohorts {
					if cohort.ID == cohortOID {
						envName = environment.Name
						cohortName = models.GetCohortReRandomName(cohort)
					}
				}
			}
		}

		history := models.History{
			Key:  key,
			OID:  envOID,
			Data: bson.M{"cohortName": cohortName},
			Time: time.Duration(time.Now().Unix()),
			UID:  user.ID,
			User: user.Name,
		}
		histories = append(histories, history)
		ctx.Set("HISTORY", histories)

		update := bson.M{
			"$pull": bson.M{
				"envs.$[env].cohorts": bson.M{
					"id": cohortOID,
				},
			},
		}
		opts := &options.UpdateOptions{
			ArrayFilters: &options.ArrayFilters{
				Filters: bson.A{bson.M{"env.id": envOID}},
			},
		}
		_, err := tools.Database.Collection("project").UpdateOne(nil, bson.M{"_id": projectOID}, update, opts)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		//删除编码初始化配置

		filter := bson.M{
			"customer_id": project.CustomerID,
			"project_id":  projectID,
			"env_id":      envOID,
			"cohort_id":   cohortOID,
		}
		if _, err := tools.Database.Collection("barcode_rule").DeleteOne(nil, filter); err != nil {
			return nil, errors.WithStack(err)
		}

		//删除属性配置初始化配置
		if _, err := tools.Database.Collection("attribute").DeleteOne(nil, filter); err != nil {
			return nil, errors.WithStack(err)
		}

		//删除访视初始化配置
		if _, err := tools.Database.Collection("visit_cycle").DeleteOne(nil, filter); err != nil {
			return nil, errors.WithStack(err)
		}

		//删除研究产品配置初始化配置
		if _, err := tools.Database.Collection("drug_configure").DeleteOne(nil, filter); err != nil {
			return nil, errors.WithStack(err)
		}

		//操作日志
		insertProjectEnvDelCohortLog(ctx, sctx, projectOID, projectOID, project.Type, envName, cohortName)

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *ProjectService) CopyCohort(ctx *gin.Context, projectID string, envID string, cohortID string, isContain bool, cohortData models.UpdateCohort) error {
	//callback := func(sctx mongo.SessionContext) (interface{}, error) {
	collection := tools.Database.Collection("project")
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)

	var oldCohortData models.Cohort
	envName := ""
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}

	// 如果是在随机
	if project.Type == 3 {
		for _, env := range project.Environments {
			if env.ID == envOID {
				// 再随机只能复制两个阶段
				if len(env.Cohorts) >= 2 {
					return tools.BuildServerError(ctx, "project_at_random_error")
				}
				break
			}
		}
	}

	for _, environment := range project.Environments {
		if environment.ID == envOID {
			envName = environment.Name
			for _, cohort := range environment.Cohorts {
				if cohort.ID == cohortOID {
					oldCohortData = cohort
				}
			}
		}
	}

	// 判断名称是否重复
	filterName := bson.M{
		"_id": projectOID,
		"envs": bson.M{
			"$elemMatch": bson.M{
				"id": envOID,
				"cohorts": bson.M{
					"$elemMatch": bson.M{
						//"id":   bson.M{"$ne": cohortOID},
						"name": cohortData.Name,
					},
				},
			},
		},
	}
	if countName, _ := collection.CountDocuments(nil, filterName); countName > 0 {
		return tools.BuildServerError(ctx, "common.duplicated.names")
	}

	// 判断因素是否重复
	if cohortData.Factor != "" {
		filterFactor := bson.M{
			"_id": projectOID,
			"envs": bson.M{
				"$elemMatch": bson.M{
					"id": envOID,
					"cohorts": bson.M{
						"$elemMatch": bson.M{
							//"id":     bson.M{"$ne": cohortOID},
							"factor": cohortData.Factor,
						},
					},
				},
			},
		}
		if countFactor, _ := collection.CountDocuments(nil, filterFactor); countFactor > 0 {
			return tools.BuildServerError(ctx, "common.duplicated.factors")

		}
	}
	user, _ := tools.Me(ctx)
	//判断密码是否正确
	if cohortData.Password != "" {
		err := tools.PasswordDetection(ctx, user.Email, cohortData.Password)
		if err != nil {
			return err
		}
	}

	// 添加操作
	cohortData.ID = primitive.NewObjectID()
	update := bson.M{
		"$push": bson.M{
			"envs.$[env].cohorts": cohortData,
		},
	}
	opts := &options.UpdateOptions{
		ArrayFilters: &options.ArrayFilters{
			Filters: bson.A{bson.M{"env.id": envOID}},
		},
	}
	if _, err := collection.UpdateOne(nil, bson.M{"_id": projectOID}, update, opts); err != nil {
		return errors.WithStack(err)
	}

	var envCohorts []models.Cohort
	for _, environment := range project.Environments {
		if environment.ID == envOID {
			envName = environment.Name
			envCohorts = environment.Cohorts
		}
	}
	//新增编码初始化配置
	codeRule := 0
	codeConfigInit := false
	if project.ResearchAttribute == 1 {
		codeConfigInit = true
	}
	barcodeRule := models.BarcodeRule{
		ID:             primitive.NewObjectID(),
		CustomerID:     project.CustomerID,
		ProjectID:      projectOID,
		EnvironmentID:  envOID,
		CohortID:       cohortData.ID,
		CodeRule:       codeRule,
		CodeConfigInit: codeConfigInit,
	}
	if _, err := tools.Database.Collection("barcode_rule").InsertOne(nil, barcodeRule); err != nil {
		return errors.WithStack(err)
	}
	random := false
	dispensing := false
	if project.ResearchAttribute == 1 {
		random = true
		dispensing = true
	}
	if project.Type == 3 && !tools.InRandomIsolation(project.Number) {
		random = true
	}
	configs := []models.UnblindingReasonConfig{
		{Reason: "SAE", AllowRemark: false},
		{Reason: "妊娠", AllowRemark: false},
		{Reason: "政策要求", AllowRemark: false},
		{Reason: "其他", AllowRemark: true},
	}
	attribute := models.Attribute{
		ID:            primitive.NewObjectID(),
		CustomerID:    project.CustomerID,
		ProjectID:     projectOID,
		EnvironmentID: envOID,
		CohortID:      cohortData.ID,
		AttributeInfo: models.AttributeInfo{
			Random:                 random,
			IsRandomNumber:         false,
			Dispensing:             dispensing,
			Blind:                  false,
			InstituteLayered:       false,
			Prefix:                 false,
			PrefixExpression:       "",
			SubjectReplaceText:     "",
			SubjectReplaceTextEn:   "",
			Digit:                  0,
			Accuracy:               0,
			Field:                  models.Field{},
			PrefixSymbol:           "",
			IsFreeze:               false,
			IsRandom:               false,
			EdcDrugConfigLabel:     "",
			Segment:                false,
			CountryLayered:         false,
			UnblindingReasonConfig: configs,
			AllowReplace:           true,
			MinimizeCalc:           0,
		},
	}
	_, err = tools.Database.Collection("attribute").InsertOne(nil, attribute)
	if err != nil {
		return errors.WithStack(err)
	}

	var newCohortData models.Cohort
	newCohortData.ID = cohortData.ID
	newCohortData.Type = cohortData.Type
	newCohortData.Factor = cohortData.Factor
	newCohortData.Name = cohortData.Name
	newCohortData.LastID = cohortData.LastID
	newCohortData.Status = int(cohortData.Status)
	newCohortData.AlertThresholds = cohortData.AlertThresholds
	_ = insertUpdateProjectEnvCohort(ctx, nil, projectOID, 11, project.Type, "", envName, oldCohortData, newCohortData, projectOID, envCohorts)

	//TODO:项目构建(除 项目日志，随机配置-随机列表、模拟随机 )复制操作
	//不需要做的(跟cohort无关)------库房管理、中心管理、研究产品管理-研究产品列表、研究产品管理-未编号研究产品、研究产品管理-批次管理、供应计划

	if isContain == true {
		_ = s.CopyAttribute(ctx, projectOID, envOID, project.CustomerID, cohortOID, cohortData.ID, attribute)
		//属性配置复制
		//if attribute.Blind{
		//随机配置(除随机配置-随机列表)
		_ = s.CopyRandomDesign(ctx, projectOID, envOID, project.CustomerID, cohortOID, cohortData.ID)
		//随机配置(区域因素)
		//_ = s.CopyRegion(ctx, projectOID, envOID, project.CustomerID, cohortOID, cohortData.ID)
		//随机配置(表单配置)
		_ = s.CopyForm(ctx, projectOID, envOID, project.CustomerID, cohortOID, cohortData.ID)
		//}
		//编码配置---不复制
		//_ = s.CopyBarcodeRule(ctx, projectOID, envOID, project.CustomerID, cohortOID, cohortData.ID, barcodeRule)
		//研究产品管理-访视管理
		_ = s.CopyVisitCycle(ctx, projectOID, envOID, project.CustomerID, cohortOID, cohortData.ID)
		//研究产品管理-研究产品配置
		_ = s.CopyDrugConfigure(ctx, projectOID, envOID, project.CustomerID, cohortOID, cohortData.ID)
		//研究产品管理-研究产品配置-设置
		_ = s.CopyDrugConfigureSetting(ctx, projectOID, envOID, project.CustomerID, cohortOID, cohortData.ID)
	}

	//	return nil, nil
	//}
	//err := tools.Transaction(callback)
	//if err != nil {
	//	return err
	//}
	return nil
}

// CopyDrugConfigureSetting
func (s *ProjectService) CopyDrugConfigureSetting(ctx *gin.Context, projectOID primitive.ObjectID, envOID primitive.ObjectID, customerOID primitive.ObjectID, cohortOID primitive.ObjectID, targetCohortOID primitive.ObjectID) error {
	match := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
		"cohort_id":   cohortOID,
	}

	var err error
	collection := tools.Database.Collection("drug_configure_setting")
	OID := targetCohortOID
	var drugConfigureSetting models.DrugConfigureSetting
	err = collection.FindOne(ctx, match).Decode(&drugConfigureSetting)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	if count, _ := tools.Database.Collection("drug_configure_setting").CountDocuments(ctx, match); count == 0 {
		return nil
	}

	var newDrugConfigureSetting models.DrugConfigureSetting
	newDrugConfigureSetting.ID = primitive.NewObjectID()
	newDrugConfigureSetting.CustomerID = drugConfigureSetting.CustomerID
	newDrugConfigureSetting.ProjectID = drugConfigureSetting.ProjectID
	newDrugConfigureSetting.EnvironmentID = drugConfigureSetting.EnvironmentID
	newDrugConfigureSetting.CohortID = targetCohortOID
	newDrugConfigureSetting.IsOpen = drugConfigureSetting.IsOpen
	newDrugConfigureSetting.SelectType = drugConfigureSetting.SelectType
	newDrugConfigureSetting.DtpIpList = drugConfigureSetting.DtpIpList

	var form models.Form
	if err := tools.Database.Collection("form").FindOne(ctx, match).Decode(&form); err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}
	doseFormName := ""
	if form.Fields != nil && len(form.Fields) > 0 {
		for _, field := range form.Fields {
			if field.ID.Hex() == drugConfigureSetting.DoseFormId {
				doseFormName = field.Variable
			}
		}
	}

	targetDoseFormId := ""
	filter := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
		"cohort_id":   targetCohortOID,
	}
	var targetForm models.Form
	if err := tools.Database.Collection("form").FindOne(ctx, filter).Decode(&targetForm); err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}
	if targetForm.Fields != nil && len(targetForm.Fields) > 0 {
		for _, field := range targetForm.Fields {
			if field.Variable == doseFormName {
				targetDoseFormId = field.ID.Hex()
			}
		}
	}
	newDrugConfigureSetting.DoseFormId = targetDoseFormId

	newDrugConfigureSetting.IsFirstInitial = drugConfigureSetting.IsFirstInitial
	newDrugConfigureSetting.IsDoseReduction = drugConfigureSetting.IsDoseReduction
	newDrugConfigureSetting.Frequency = drugConfigureSetting.Frequency

	var targetDrugConfigure models.DrugConfigure
	err = tools.Database.Collection("drug_configure").FindOne(nil, filter).Decode(&targetDrugConfigure)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	targetDoseLevelList := make([]models.DoseLevel, 0)
	if drugConfigureSetting.DoseLevelList != nil && len(drugConfigureSetting.DoseLevelList) > 0 {
		for _, doseLevel := range drugConfigureSetting.DoseLevelList {
			var targetDoseLevel models.DoseLevel
			targetDoseLevel.ID = primitive.NewObjectID()
			targetDoseLevel.Name = doseLevel.Name
			targetDoseLevel.Group = doseLevel.Group
			targetDoseDistribution := make([]string, 0)
			if doseLevel.DoseDistribution != nil && len(doseLevel.DoseDistribution) > 0 {
				for _, doseDistribution := range doseLevel.DoseDistribution {
					var jsonObject map[string]interface{}
					json.Unmarshal([]byte(doseDistribution), &jsonObject)
					//fmt.Println(jsonObject)
					name := jsonObject["name"].(string)
					if targetDrugConfigure.Configures != nil && len(targetDrugConfigure.Configures) > 0 {
						for _, configure := range targetDrugConfigure.Configures {
							if doseLevel.Group != nil && len(doseLevel.Group) > 0 {
								index := arrays.ContainsString(doseLevel.Group, configure.Group)
								if index != -1 {
									if configure.OpenSetting == 1 {
										//按标签配置
										if len(configure.Label) > 0 {
											if name == configure.Label {
												var doseLabel models.DoseLabel
												doseLabel.ID = configure.ID
												doseLabel.Name = configure.Label
												jsonString, e := json.Marshal(doseLabel)
												if e != nil {
													return errors.WithStack(e)
												}
												whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
												if whether == -1 {
													targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
												}
											}
										} else {
											if configure.Values != nil && len(configure.Values) != 0 {
												for _, drugValue := range configure.Values {
													if len(drugValue.Label) != 0 {
														if name == drugValue.Label {
															var doseLabel models.DoseLabel
															doseLabel.ID = configure.ID
															doseLabel.Name = drugValue.Label
															jsonString, e := json.Marshal(doseLabel)
															if e != nil {
																return errors.WithStack(e)
															}
															whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
															if whether == -1 {
																targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
															}
														}
													}
												}
											}
										}
									} else if configure.OpenSetting == 2 {
										//开放配置
										if len(configure.Label) != 0 {
											if name == configure.Label {
												var doseLabel models.DoseLabel
												doseLabel.ID = configure.ID
												doseLabel.Name = configure.Label

												jsonString, e := json.Marshal(doseLabel)
												if e != nil {
													return errors.WithStack(e)
												}
												whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
												if whether == -1 {
													targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
												}
											}
										} else {
											if configure.Values != nil && len(configure.Values) != 0 {
												for _, drugValue := range configure.Values {
													drug := ""
													if len(drugValue.DrugSpec) != 0 {
														drug = drugValue.DrugName + "/" + drugValue.CustomDispensingNumber + "/" + drugValue.DrugSpec
													} else {
														drug = drugValue.DrugName + "/" + drugValue.CustomDispensingNumber + "/"
													}

													if name == drug {
														var doseLabel models.DoseLabel
														doseLabel.ID = configure.ID
														doseLabel.Name = drug

														jsonString, e := json.Marshal(doseLabel)
														if e != nil {
															return errors.WithStack(e)
														}
														whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
														if whether == -1 {
															targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
														}
													}
												}
											}
										}

									}
								}
							}
						}
					}
				}
			}
			targetDoseLevel.DoseDistribution = targetDoseDistribution
			targetDoseLevel.InitialDose = doseLevel.InitialDose
			targetDoseLevel.IsUsed = false
			targetDoseLevelList = append(targetDoseLevelList, targetDoseLevel)
		}
	}
	newDrugConfigureSetting.DoseLevelList = targetDoseLevelList

	targetVisitJudgmentList := make([]models.VisitJudgment, 0)
	if drugConfigureSetting.VisitJudgmentList != nil && len(drugConfigureSetting.VisitJudgmentList) > 0 {
		for _, visitJudgment := range drugConfigureSetting.VisitJudgmentList {
			var targetVisitJudgment models.VisitJudgment
			targetVisitJudgment.ID = primitive.NewObjectID()
			targetVisitJudgment.Name = visitJudgment.Name
			targetVisitJudgment.Group = visitJudgment.Group
			targetDoseDistribution := make([]string, 0)
			if visitJudgment.DoseDistribution != nil && len(visitJudgment.DoseDistribution) > 0 {
				for _, doseDistribution := range visitJudgment.DoseDistribution {
					var jsonObject map[string]interface{}
					json.Unmarshal([]byte(doseDistribution), &jsonObject)
					//fmt.Println(jsonObject)
					name := jsonObject["name"].(string)
					if targetDrugConfigure.Configures != nil && len(targetDrugConfigure.Configures) > 0 {
						for _, configure := range targetDrugConfigure.Configures {
							if visitJudgment.Group != nil && len(visitJudgment.Group) > 0 {
								index := arrays.ContainsString(visitJudgment.Group, configure.Group)
								if index != -1 {
									if configure.OpenSetting == 1 {
										//按标签配置
										if len(configure.Label) > 0 {
											if name == configure.Label {
												var doseLabel models.DoseLabel
												doseLabel.ID = configure.ID
												doseLabel.Name = configure.Label
												jsonString, e := json.Marshal(doseLabel)
												if e != nil {
													return errors.WithStack(e)
												}
												whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
												if whether == -1 {
													targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
												}
											}
										} else {
											if configure.Values != nil && len(configure.Values) != 0 {
												for _, drugValue := range configure.Values {
													if len(drugValue.Label) != 0 {
														if name == drugValue.Label {
															var doseLabel models.DoseLabel
															doseLabel.ID = configure.ID
															doseLabel.Name = drugValue.Label
															jsonString, e := json.Marshal(doseLabel)
															if e != nil {
																return errors.WithStack(e)
															}
															whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
															if whether == -1 {
																targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
															}
														}
													}
												}
											}
										}
									} else if configure.OpenSetting == 2 {
										//开放配置
										if len(configure.Label) != 0 {
											if name == configure.Label {
												var doseLabel models.DoseLabel
												doseLabel.ID = configure.ID
												doseLabel.Name = configure.Label

												jsonString, e := json.Marshal(doseLabel)
												if e != nil {
													return errors.WithStack(e)
												}
												whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
												if whether == -1 {
													targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
												}
											}
										} else {
											if configure.Values != nil && len(configure.Values) != 0 {
												for _, drugValue := range configure.Values {
													drug := ""
													if len(drugValue.DrugSpec) != 0 {
														drug = drugValue.DrugName + "/" + drugValue.CustomDispensingNumber + "/" + drugValue.DrugSpec
													} else {
														drug = drugValue.DrugName + "/" + drugValue.CustomDispensingNumber + "/"
													}

													if name == drug {
														var doseLabel models.DoseLabel
														doseLabel.ID = configure.ID
														doseLabel.Name = drug

														jsonString, e := json.Marshal(doseLabel)
														if e != nil {
															return errors.WithStack(e)
														}
														whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
														if whether == -1 {
															targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
														}
													}
												}
											}
										}

									}
								}
							}
						}
					}
				}
			}
			targetVisitJudgment.DoseDistribution = targetDoseDistribution
			targetVisitJudgment.VisitInheritance = visitJudgment.VisitInheritance
			targetVisitJudgment.IsUsed = false
			targetVisitJudgmentList = append(targetVisitJudgmentList, targetVisitJudgment)
		}
	}
	newDrugConfigureSetting.VisitJudgmentList = targetVisitJudgmentList

	logOID := newDrugConfigureSetting.EnvironmentID
	if newDrugConfigureSetting.CohortID != primitive.NilObjectID {
		logOID = newDrugConfigureSetting.CohortID
	}

	_, err = tools.Database.Collection("drug_configure_setting").InsertOne(ctx, newDrugConfigureSetting)
	if err != nil {
		return errors.WithStack(err)
	}

	err = insertDrugConfigureSettingLog(ctx, nil, logOID, 11, models.DrugConfigureSetting{}, newDrugConfigureSetting, OID, drugConfigureSetting.EnvironmentID, targetCohortOID, "")

	return nil
}

func (s *ProjectService) CopyAttribute(ctx *gin.Context, projectOID primitive.ObjectID, envOID primitive.ObjectID, customerOID primitive.ObjectID, cohortOID primitive.ObjectID, targetCohortOID primitive.ObjectID, newAttribute models.Attribute) error {
	var project models.Project
	_ = tools.Database.Collection("project").FindOne(ctx, bson.M{"_id": projectOID}).Decode(&project)

	var old models.Attribute
	var attribute models.AttributeInfo

	//upsert := true

	match := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
		"cohort_id":   cohortOID,
	}
	OID := targetCohortOID
	err := tools.Database.Collection("attribute").FindOne(nil, match).Decode(&old)
	if err != nil {
		return errors.WithStack(err)
	}

	attribute.Random = old.AttributeInfo.Random
	attribute.RandomControl = old.AttributeInfo.RandomControl
	attribute.RandomControlRule = old.AttributeInfo.RandomControlRule
	attribute.RandomControlGroup = old.AttributeInfo.RandomControlGroup
	attribute.SubjectNumberRule = old.AttributeInfo.SubjectNumberRule
	attribute.IsRandomNumber = old.AttributeInfo.IsRandomNumber
	attribute.IsRandomSequenceNumber = old.AttributeInfo.IsRandomSequenceNumber
	attribute.RandomSequenceNumberPrefix = old.AttributeInfo.RandomSequenceNumberPrefix
	attribute.RandomSequenceNumberDigit = old.AttributeInfo.RandomSequenceNumberDigit
	attribute.RandomSequenceNumberStart = old.AttributeInfo.RandomSequenceNumberStart
	attribute.Dispensing = old.AttributeInfo.Dispensing
	attribute.Blind = old.AttributeInfo.Blind
	attribute.CountryLayered = old.AttributeInfo.CountryLayered
	attribute.InstituteLayered = old.AttributeInfo.InstituteLayered
	attribute.RegionLayered = old.AttributeInfo.RegionLayered
	attribute.Prefix = old.AttributeInfo.Prefix
	attribute.PrefixExpression = old.AttributeInfo.PrefixExpression
	attribute.SubjectReplaceText = old.AttributeInfo.SubjectReplaceText
	attribute.SubjectReplaceTextEn = old.AttributeInfo.SubjectReplaceTextEn
	attribute.Digit = old.AttributeInfo.Digit
	attribute.Accuracy = old.AttributeInfo.Accuracy
	attribute.AllowReplace = old.AttributeInfo.AllowReplace
	attribute.ReplaceRule = old.AttributeInfo.ReplaceRule
	attribute.ReplaceRuleNumber = old.AttributeInfo.ReplaceRuleNumber
	attribute.DtpRule = old.AttributeInfo.DtpRule
	attribute.MinimizeCalc = old.AttributeInfo.MinimizeCalc

	var field = models.Field{
		ID:             primitive.NewObjectID(),
		Name:           old.AttributeInfo.Field.Name,
		Label:          old.AttributeInfo.Field.Label,
		Type:           old.AttributeInfo.Field.Type,
		Status:         old.AttributeInfo.Field.Status,
		Options:        old.AttributeInfo.Field.Options,
		List:           old.AttributeInfo.Field.List,
		Modifiable:     old.AttributeInfo.Field.Modifiable,
		Required:       old.AttributeInfo.Field.Required,
		Stratification: old.AttributeInfo.Field.Stratification,
		Digit:          old.AttributeInfo.Field.Digit,
		Accuracy:       old.AttributeInfo.Field.Accuracy,
		DateFormat:     old.AttributeInfo.Field.DateFormat,
		FormatType:     old.AttributeInfo.Field.FormatType,
		TimeFormat:     old.AttributeInfo.Field.TimeFormat,
		Length:         old.AttributeInfo.Field.Length,
	}
	attribute.Field = field
	attribute.PrefixSymbol = old.AttributeInfo.PrefixSymbol
	attribute.IsFreeze = old.AttributeInfo.IsFreeze
	attribute.IsRandom = old.AttributeInfo.IsRandom
	attribute.EdcDrugConfigLabel = old.AttributeInfo.EdcDrugConfigLabel
	attribute.Segment = old.AttributeInfo.Segment
	//for i, config := range old.AttributeInfo.UnblindingReasonConfig {
	//	attribute.UnblindingReasonConfig[i].Reason = config.Reason
	//	attribute.UnblindingReasonConfig[i].AllowRemark = config.AllowRemark
	//}
	if old.AttributeInfo.UnblindingReasonConfig != nil {
		attribute.UnblindingReasonConfig = old.AttributeInfo.UnblindingReasonConfig
	}
	attribute.UnBlindingRestrictions = old.AttributeInfo.UnBlindingRestrictions
	attribute.PvUnBlindingRestrictions = old.AttributeInfo.PvUnBlindingRestrictions
	attribute.IsScreen = old.AttributeInfo.IsScreen
	attribute.ConnectAli = old.AttributeInfo.ConnectAli
	attribute.AliProjectNO = old.AttributeInfo.AliProjectNO

	//opts := &options.FindOneAndUpdateOptions{
	//	Upsert: &upsert,
	//}
	filter := bson.M{"_id": newAttribute.ID}
	update := bson.M{"$set": bson.M{"info": attribute}}
	_, err = tools.Database.Collection("attribute").UpdateOne(ctx, filter, update)
	if err != nil {
		return errors.WithStack(err)
	}
	//号段随机长度
	err = insertAttributeLog(ctx, nil, OID, 11, newAttribute.AttributeInfo, attribute, "", "", "")
	if err != nil {
		return err
	}

	return nil
}

// CopyDrugConfigure
func (s *ProjectService) CopyDrugConfigure(ctx *gin.Context, projectOID primitive.ObjectID, envOID primitive.ObjectID, customerOID primitive.ObjectID, cohortOID primitive.ObjectID, targetCohortOID primitive.ObjectID) error {
	match := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
		"cohort_id":   cohortOID,
	}
	var err error
	collection := tools.Database.Collection("drug_configure")
	OID := targetCohortOID
	var drugConfigure models.DrugConfigure
	err = collection.FindOne(nil, match).Decode(&drugConfigure)
	if err != nil {
		return err
	}

	// 查询旧的访视周期
	var oldVisitCycle models.VisitCycle
	_ = tools.Database.Collection("visit_cycle").FindOne(nil, match).Decode(&oldVisitCycle)

	// 查询新的访视周期
	var newVisitCycle models.VisitCycle
	_ = tools.Database.Collection("visit_cycle").FindOne(nil, bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
		"cohort_id":   targetCohortOID,
	}).Decode(&newVisitCycle)

	drugConfigure.CohortID = targetCohortOID
	drugConfigure.ID = primitive.NewObjectID()
	if drugConfigure.Configures != nil {
		configures := make([]models.DrugConfigureInfo, 0)
		for _, configure := range drugConfigure.Configures {
			configure.ID = primitive.NewObjectID()
			if configure.VisitCycles != nil {
				var visitCyclesIds []primitive.ObjectID
				// 根据访视编号匹配访视配置里面的访视周期ID
				for _, oldVisitId := range configure.VisitCycles {
					var oldVisitNumber string
					for _, oldVisit := range oldVisitCycle.Infos {
						if oldVisit.ID == oldVisitId {
							oldVisitNumber = oldVisit.Number
							break
						}
					}
					for _, newVisit := range newVisitCycle.Infos {
						if newVisit.Number == oldVisitNumber {
							visitCyclesIds = append(visitCyclesIds, newVisit.ID)
							break
						}
					}

					var oldVisitNameZh string
					var oldVisitNameEn string
					if oldVisitCycle.SetInfo.IsOpen {
						if oldVisitCycle.SetInfo.Id == oldVisitId {
							oldVisitNameZh = oldVisitCycle.SetInfo.NameZh
							oldVisitNameEn = oldVisitCycle.SetInfo.NameEn
						}
					}

					if newVisitCycle.SetInfo.IsOpen {
						if newVisitCycle.SetInfo.NameZh == oldVisitNameZh && newVisitCycle.SetInfo.NameEn == oldVisitNameEn {
							visitCyclesIds = append(visitCyclesIds, newVisitCycle.SetInfo.Id)
						}
					}

				}
				configure.VisitCycles = visitCyclesIds
			}
			if configure.RoutineVisitMappingList != nil && len(configure.RoutineVisitMappingList) > 0 {
				routineVisitMappingList := make([]models.RoutineVisitMapping, 0)
				for _, mapping := range configure.RoutineVisitMappingList {
					mapping.ID = primitive.NewObjectID()
					if mapping.VisitList != nil && len(mapping.VisitList) > 0 {
						mappinfVisitList := make([]primitive.ObjectID, 0)
						// 根据访视编号匹配访视配置里面的访视周期ID
						for _, mappingVisitId := range mapping.VisitList {
							var oldVisitNumber string
							for _, oldVisit := range oldVisitCycle.Infos {
								if oldVisit.ID == mappingVisitId {
									oldVisitNumber = oldVisit.Number
									break
								}
							}

							for _, newVisit := range newVisitCycle.Infos {
								if newVisit.Number == oldVisitNumber {
									mappinfVisitList = append(mappinfVisitList, newVisit.ID)
									break
								}
							}
						}
						mapping.VisitList = mappinfVisitList
					}
					mappingDrugList := make([]models.Drug, 0)
					if mapping.DrugList != nil && len(mapping.DrugList) > 0 {
						for _, drug := range mapping.DrugList {
							drug.ID = primitive.NewObjectID()
							mappingDrugList = append(mappingDrugList, drug)
						}
					}
					mapping.DrugList = mappingDrugList
					routineVisitMappingList = append(routineVisitMappingList, mapping)
				}
				configure.RoutineVisitMappingList = routineVisitMappingList
			}
			configures = append(configures, configure)
			//新增研究产品配置操作轨迹
			u, _ := ctx.Get("user")
			user := u.(models.User)
			var histories []models.History
			var drugConfigurs string
			for _, v := range configure.Values {
				drugConfigurs = drugConfigurs + v.DrugName + "~" + strconv.Itoa(v.DispensingNumber) + "~" + v.DrugSpec + "/"
			}
			history := models.History{
				Key:  "history.project.drugConfigure.add",
				OID:  OID,
				Data: map[string]interface{}{"group": configure.Group, "drugConfigures": drugConfigurs},
				Time: time.Duration(time.Now().Unix()),
				UID:  user.ID,
				User: user.Name,
			}
			histories = append(histories, history)
			ctx.Set("HISTORY", histories)
			_ = insertDrugConfigureLog(ctx, nil, OID, 11, models.DrugConfigureInfo{}, configure, OID, envOID, targetCohortOID, "", models.VisitCycle{})
		}
		drugConfigure.Configures = configures
	}
	_, rderr := tools.Database.Collection("drug_configure").InsertOne(nil, drugConfigure)
	if rderr != nil {
		return errors.WithStack(rderr)
	}

	return nil
}

// CopyVisitCycle
func (s *ProjectService) CopyVisitCycle(ctx *gin.Context, projectOID primitive.ObjectID, envOID primitive.ObjectID, customerOID primitive.ObjectID, cohortOID primitive.ObjectID, targetCohortOID primitive.ObjectID) error {
	match := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
		"cohort_id":   cohortOID,
	}
	var err error
	collection := tools.Database.Collection("visit_cycle")

	var project models.Project
	projectErr := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if projectErr != nil {
		return errors.WithStack(projectErr)
	}
	OID := targetCohortOID
	var visitCycle models.VisitCycle
	err = collection.FindOne(nil, match).Decode(&visitCycle)
	if err != nil {
		return err
	}

	visitCycle.CohortID = targetCohortOID
	visitCycle.ID = primitive.NewObjectID()
	visitCycle.SetInfo.Id = primitive.NewObjectID()

	if visitCycle.SetInfo.IsOpen {
		err = insertVisitSettingLog(ctx, nil, OID, 11, models.VisitSetting{}, visitCycle.SetInfo, visitCycle.ID, "")
		if err != nil {
			return errors.WithStack(err)
		}
	}

	var visitCycleInfos []models.VisitCycleInfo
	for _, visitCycleInfo := range visitCycle.ConfigInfo.Infos {
		visitCycleInfo.ID = primitive.NewObjectID()
		err := insertVisitCycleLog(ctx, nil, OID, 11, models.VisitCycleInfo{}, visitCycleInfo, visitCycle.ID, project, "")
		if err != nil {
			return errors.WithStack(err)
		}
		visitCycleInfos = append(visitCycleInfos, visitCycleInfo)
	}
	visitCycle.ConfigInfo.Infos = visitCycleInfos
	visitCycle.Infos = visitCycleInfos
	visitCycle.HistoryInfo = nil
	_, rderr := tools.Database.Collection("visit_cycle").InsertOne(nil, visitCycle)
	if rderr != nil {
		return errors.WithStack(rderr)
	}

	return nil

}

// CopyBarcodeRule
func (s *ProjectService) CopyBarcodeRule(ctx *gin.Context, projectOID primitive.ObjectID, envOID primitive.ObjectID, customerOID primitive.ObjectID, cohortOID primitive.ObjectID, targetCohortOID primitive.ObjectID, newBarcodeRule models.BarcodeRule) error {
	match := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
		"cohort_id":   cohortOID,
	}
	var err error
	var barcodeRule models.BarcodeRule
	err = tools.Database.Collection("barcode_rule").FindOne(ctx, match).Decode(&barcodeRule)
	if err != nil {
		return err
	}

	filter := bson.M{"_id": newBarcodeRule.ID}
	update := bson.M{"$set": bson.M{"code_rule": barcodeRule.CodeRule, "code_config_init": barcodeRule.CodeConfigInit}}
	_, rderr := tools.Database.Collection("barcode_rule").UpdateOne(nil, filter, update)
	if rderr != nil {
		return errors.WithStack(rderr)
	}
	// TODO保存项目日志
	types := 10
	oldField := models.OperationLogField{
		Type:  1,
		Value: "",
	}

	var OperationLogFieldGroups []models.OperationLogFieldGroup
	tranKey := "operation_log.barcode.random"
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "barcode.edit",
		TranKey: tranKey,
		Old:     oldField,
		New: models.OperationLogField{
			Type:  6,
			Value: barcodeRule.CodeRule,
		},
	})
	OID := targetCohortOID
	err = tools.SaveOperation(ctx, nil, "operation_log.module.barcode", OID, types, OperationLogFieldGroups, []models.Mark{}, barcodeRule.ID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil

}

//CopyRegion
//func (s *ProjectService) CopyRegion(ctx *gin.Context, projectOID primitive.ObjectID, envOID primitive.ObjectID, customerOID primitive.ObjectID, cohortOID primitive.ObjectID, targetCohortOID primitive.ObjectID) error {
//	match := bson.M{
//		"project_id":  projectOID,
//		"env_id":      envOID,
//		"customer_id": customerOID,
//		"cohort_id":   cohortOID,
//		"deleted":     true,
//	}
//	opts := &options.FindOptions{
//		Sort: bson.D{{"_id", 1}},
//	}
//	var err error
//	var regions []models.Region
//	cursor, err := tools.Database.Collection("region").Find(nil, match, opts)
//	if err != nil {
//		return errors.WithStack(err)
//	}
//	err = cursor.All(nil, &regions)
//	if err != nil {
//		return errors.WithStack(err)
//	}
//	operationOID := targetCohortOID
//	if regions != nil {
//		for _, region := range regions {
//			region.CohortID = targetCohortOID
//			region.ID = primitive.NewObjectID()
//			_, rderr := tools.Database.Collection("region").InsertOne(nil, region)
//			if rderr != nil {
//				return errors.WithStack(rderr)
//			}
//			// 项目日志
//			err = insertRegionLog(ctx, nil, operationOID, 10, models.Region{}, region, region.ID)
//			if err != nil {
//				return errors.WithStack(err)
//			}
//		}
//	}
//
//	return nil
//}

// CopyForm
func (s *ProjectService) CopyForm(ctx *gin.Context, projectOID primitive.ObjectID, envOID primitive.ObjectID, customerOID primitive.ObjectID, cohortOID primitive.ObjectID, targetCohortOID primitive.ObjectID) error {
	match := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
		"cohort_id":   cohortOID,
	}
	var err error
	var form models.Form
	err = tools.Database.Collection("form").FindOne(nil, match).Decode(&form)
	if err != nil {
		return err
	}
	form.CohortID = targetCohortOID
	form.ID = primitive.NewObjectID()
	fieldList := make([]models.Field, 0)
	if form.Fields != nil {
		for _, field := range form.Fields {
			field.ID = primitive.NewObjectID()
			fieldList = append(fieldList, field)
			// 项目日志
			err = insertFormLog(ctx, nil, targetCohortOID, 11, models.Field{}, field, form.ID, "")
			if err != nil {
				return errors.WithStack(err)
			}
		}
	}
	form.Fields = fieldList
	_, rderr := tools.Database.Collection("form").InsertOne(nil, form)
	if rderr != nil {
		return errors.WithStack(rderr)
	}

	return nil
}

// CopyRandomDesign
func (s *ProjectService) CopyRandomDesign(ctx *gin.Context, projectOID primitive.ObjectID, envOID primitive.ObjectID, customerOID primitive.ObjectID, cohortOID primitive.ObjectID, targetCohortOID primitive.ObjectID) error {
	//var randomDesignInfo models.RandomDesignInfo
	historyID := targetCohortOID
	var err error
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}

	match := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
		"cohort_id":   cohortOID,
	}

	var randomDesign models.RandomDesign
	err = tools.Database.Collection("random_design").FindOne(nil, match).Decode(&randomDesign)
	if err != nil {
		return err
	}

	if randomDesign.Info.Groups != nil {
		for _, group := range randomDesign.Info.Groups {
			group.ID = primitive.NewObjectID()
		}
	}
	if randomDesign.Info.Factors != nil {
		for _, factor := range randomDesign.Info.Factors {
			factor.ID = primitive.NewObjectID()
		}
	}
	randomDesign.CohortID = targetCohortOID
	randomDesign.ID = primitive.NewObjectID()
	_, rderr := tools.Database.Collection("random_design").InsertOne(nil, randomDesign)
	if rderr != nil {
		return errors.WithStack(rderr)
	}

	_ = insertRandomDesignLog(ctx, nil, historyID, 11, models.RandomDesignInfo{}, randomDesign.Info, randomDesign.ID, "", project, "")
	return nil
}

func (s *ProjectService) projectEnvironmentUsersCount(matchPipeLine mongo.Pipeline) (int, error) {
	var totalPipeLine mongo.Pipeline
	totalPipeLine = append(totalPipeLine, matchPipeLine...)
	totalPipeLine = append(totalPipeLine, bson.D{{Key: "$count", Value: "total"}})
	cur, err := tools.Database.Collection("user_project_environment").Aggregate(context.TODO(), totalPipeLine)
	if err != nil {
		return 0, err
	}
	var tmp []struct {
		Total int `bson:"total"`
	}
	if err := cur.All(context.TODO(), &tmp); err != nil {
		return 0, err
	}
	if len(tmp) == 0 {
		return 0, nil
	} else {
		return tmp[0].Total, nil
	}
}

func (s *ProjectService) ProjectEnvironmentUsersSearchList(ctx *gin.Context, projectID string, envID string, email string, start int, limit int) (map[string]interface{}, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)

	d := make([]map[string]interface{}, 0)
	total := 0

	//获取当前用户登录信息
	me, err := tools.Me(ctx)
	if err != nil {
		return nil, err
	}

	//判断当前用户是否有study角色
	var projectEnvUser models.UserProjectEnvironment
	err = tools.Database.Collection("user_project_environment").FindOne(nil, bson.M{"env_id": envOID, "user_id": me.ID}).Decode(&projectEnvUser)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var scopeCountData []models.ScopeCount
	studyCountPipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"project_id": projectOID, "_id": bson.M{"$in": projectEnvUser.Roles}}}},
		{{Key: "$group", Value: bson.M{"_id": bson.M{"scope": "$scope"}, "count": bson.M{"$sum": 1}}}},
		{{Key: "$project", Value: bson.M{
			"_id":   0,
			"scope": "$_id.scope",
			"count": 1,
		}}},
	}
	cursor, err := tools.Database.Collection("project_role_permission").Aggregate(ctx, studyCountPipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if err = cursor.All(ctx, &scopeCountData); err != nil {
		return nil, errors.WithStack(err)
	}

	studyCount := 0
	existUserIds := make(map[primitive.ObjectID]primitive.ObjectID)
	existUserIds[me.ID] = me.ID
	matchSiteDepot := bson.M{"user_id": me.ID, "project_id": projectOID, "env_id": envOID}

	for _, scopeCount := range scopeCountData {
		if scopeCount.Scope == "study" {
			studyCount = scopeCount.Count
			if studyCount > 0 {
				break
			}
		} else if scopeCount.Scope == "site" {
			//查询人员管理里面，有site权限的人员
			var userRolesData []map[string]interface{}
			searchPipeLine := mongo.Pipeline{
				{{Key: "$match", Value: bson.M{"env_id": envOID}}},
				{{Key: "$lookup", Value: bson.M{"from": "project_role_permission", "localField": "roles", "foreignField": "_id", "as": "roles"}}},
				{{Key: "$match", Value: bson.M{"roles.scope": "site"}}},
			}

			cursor, err = tools.Database.Collection("user_project_environment").Aggregate(nil, searchPipeLine)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &userRolesData)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			//说明有分配中心权限的人员
			if len(userRolesData) > 0 {
				var userIds []primitive.ObjectID
				for _, user := range userRolesData {
					userIds = append(userIds, user["user_id"].(primitive.ObjectID))
				}
				instituteIds := make([]primitive.ObjectID, 0)
				var userSites []models.UserSite
				cursor, err := tools.Database.Collection("user_site").Find(nil, matchSiteDepot)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = cursor.All(nil, &userSites)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				for _, userSite := range userSites {
					instituteIds = append(instituteIds, userSite.SiteID)
				}
				//分配相同中心的用户
				if len(instituteIds) > 0 {
					var userSameSites []models.UserSite
					cursor, err = tools.Database.Collection("user_site").Find(nil, bson.M{"project_id": projectOID, "env_id": envOID, "user_id": bson.M{"$in": userIds}, "site_id": bson.M{"$in": instituteIds}})
					if err != nil {
						return nil, errors.WithStack(err)
					}
					err = cursor.All(nil, &userSameSites)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					for _, userSite := range userSameSites {
						existUserIds[userSite.UserID] = userSite.UserID
					}
				}
			}

		} else if scopeCount.Scope == "depot" {
			//查询人员管理里面，有site权限的人员
			var userRolesData []map[string]interface{}
			searchPipeLine := mongo.Pipeline{
				{{Key: "$match", Value: bson.M{"env_id": envOID}}},
				{{Key: "$lookup", Value: bson.M{"from": "project_role_permission", "localField": "roles", "foreignField": "_id", "as": "roles"}}},
				{{Key: "$match", Value: bson.M{"roles.scope": "depot"}}},
			}

			cursor, err = tools.Database.Collection("user_project_environment").Aggregate(nil, searchPipeLine)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &userRolesData)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			//说明有分配仓库权限的人员
			if len(userRolesData) > 0 {
				var userIds []primitive.ObjectID
				for _, user := range userRolesData {
					userIds = append(userIds, user["user_id"].(primitive.ObjectID))
				}
				instituteIds := make([]primitive.ObjectID, 0)
				var userDepots []models.UserDepot
				cursor, err := tools.Database.Collection("user_depot").Find(nil, matchSiteDepot)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = cursor.All(nil, &userDepots)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				for _, userDepot := range userDepots {
					instituteIds = append(instituteIds, userDepot.DepotID)
				}
				//分配相同仓库的用户
				if len(instituteIds) > 0 {
					var userSameDepots []models.UserDepot
					cursor, err = tools.Database.Collection("user_depot").Find(nil, bson.M{"project_id": projectOID, "env_id": envOID, "user_id": bson.M{"$in": userIds}, "depot_id": bson.M{"$in": instituteIds}})
					if err != nil {
						return nil, errors.WithStack(err)
					}
					err = cursor.All(nil, &userSameDepots)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					for _, userDepot := range userSameDepots {
						existUserIds[userDepot.UserID] = userDepot.UserID
					}
				}
			}

		}
	}

	match := bson.M{"project_id": projectOID, "env_id": envOID}
	if studyCount > 0 { //存在study角色，查看所有的人员列表
		match = bson.M{"project_id": projectOID, "env_id": envOID}
	} else {
		//查询人员管理里面，有study权限的人员
		var userRolesData []map[string]interface{}
		searchPipeLine := mongo.Pipeline{
			{{Key: "$match", Value: bson.M{"env_id": envOID}}},
			{{Key: "$lookup", Value: bson.M{"from": "project_role_permission", "localField": "roles", "foreignField": "_id", "as": "roles"}}},
			{{Key: "$match", Value: bson.M{"roles.scope": "study"}}},
		}

		cursor, err = tools.Database.Collection("user_project_environment").Aggregate(nil, searchPipeLine)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &userRolesData)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		if len(userRolesData) > 0 {
			for _, user := range userRolesData {
				existUserIds[user["user_id"].(primitive.ObjectID)] = user["user_id"].(primitive.ObjectID)
			}
		}

		var userIds []primitive.ObjectID
		if len(existUserIds) > 0 {
			for _, v := range existUserIds {
				userIds = append(userIds, v)
			}
			match = bson.M{"project_id": projectOID, "env_id": envOID, "user_id": bson.M{"$in": userIds}}
		} else {
			match = bson.M{"project_id": projectOID, "env_id": envOID, "user_id": me.ID}
		}
	}

	userLookup := bson.M{"from": "user", "localField": "user_id", "foreignField": "_id", "as": "user"}
	if email != "" {
		userLookup = bson.M{"from": "user",
			"let": bson.M{"user_id": "$user_id"},
			"pipeline": mongo.Pipeline{
				{{Key: "$match", Value: bson.M{
					"$and": bson.A{
						bson.M{"$expr": bson.M{"$eq": bson.A{"$_id", "$$user_id"}}},
						bson.M{"$expr": bson.M{"$regexMatch": bson.M{"input": "$info.email", "regex": strings.ToLower(email)}}},
						bson.M{"$expr": bson.M{"$ne": bson.A{"$delete", true}}},
					},
				}}},
			},
			"as": "user"}
	}
	searchPipeLine := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: userLookup}},
		{{Key: "$unwind", Value: "$user"}},
	}
	total, err = s.projectEnvironmentUsersCount(searchPipeLine)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// 加分页设置
	searchPipeLine = append(searchPipeLine, bson.D{{Key: "$skip", Value: start}}, bson.D{{Key: "$limit", Value: limit}}, bson.D{{Key: "$sort", Value: bson.D{{"_id", 1}}}})
	lookupPipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"$and": bson.A{
				bson.M{"$expr": bson.M{"$eq": bson.A{"$user_id", "$$user_id"}}},
				bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", envOID}}},
			},
		}}},
	}
	sitePipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"$and": bson.A{
				bson.M{"$expr": bson.M{"$eq": bson.A{"$user_id", "$$user_id"}}},
				bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", envOID}}},
			},
		}}},
		{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "site_id", "foreignField": "_id", "as": "project_site"}}},
		{{Key: "$match", Value: bson.M{"project_site.deleted": 2}}},
	}
	// 连接查询
	pipeline := mongo.Pipeline{
		{{Key: "$lookup", Value: bson.M{"from": "project_role_permission", "localField": "roles", "foreignField": "_id", "as": "roles"}}},
		{{Key: "$lookup", Value: bson.M{"from": "user_site", "let": bson.M{"env_id": "$env_id", "user_id": "$user_id"}, "pipeline": sitePipeline, "as": "sites"}}},
		{{Key: "$lookup", Value: bson.M{"from": "user_depot", "let": bson.M{"env_id": "$env_id", "user_id": "$user_id"}, "pipeline": lookupPipeline, "as": "depots"}}},
		{{
			Key: "$project",
			Value: bson.M{
				"_id":  1,
				"id":   "$user._id",
				"info": "$user.info",
				//"settings":                     "$user.settings",
				"settings": bson.M{
					"$ifNull": []interface{}{
						"$user.settings",
						bson.M{},
					},
				},
				"app":                          bson.M{"$ifNull": bson.A{"$app", false}},
				"cloudId":                      "$user.cloud_id",
				"closeCustomer":                "$user.close_customer",
				"roles._id":                    1,
				"roles.name":                   1,
				"roles.scope":                  1,
				"roles.status":                 1,
				"sites.site_id":                1,
				"depots.depot_id":              1,
				"createAt":                     "$meta.created_at",
				"unblindingCode.availableCode": "$unblinding_code.available_code",
				"unblindingCode.usedCode":      "$unblinding_code.used_code",
				"unbind":                       1,
			},
		}},
	}

	searchPipeLine = append(searchPipeLine, pipeline...)
	// 设置 AggregateOptions
	aggregateOptions := options.Aggregate().SetAllowDiskUse(true)
	cursor, err = tools.Database.Collection("user_project_environment").Aggregate(nil, searchPipeLine, aggregateOptions)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &d)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	meIds := make([]string, 0)
	meIds = append(meIds, me.CloudId.Hex())
	meUsers, err := tools.UserFetch(&models.UserFetchRequest{Ids: meIds}, locales.Lang(ctx))
	if err != nil {
		return nil, errors.WithStack(err)
	}

	tz := ""
	timezone := ""
	for _, u := range meUsers {
		tz = u.Settings.Tz
		timezone = u.Settings.Timezone
	}

	//替换user info的内容
	cloudIds := slice.Map(d, func(index int, item map[string]interface{}) string {
		return item["cloudId"].(primitive.ObjectID).Hex()
	})
	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: cloudIds}, locales.Lang(ctx))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	m := make(map[string]*models.UserData)
	for _, u := range users {
		m[u.Id] = u
	}
	for _, v := range d {
		if v["closeCustomer"] == nil {
			v["closeCustomer"] = []primitive.ObjectID{}
		}
		e := v["cloudId"].(primitive.ObjectID).Hex()
		if m[e] != nil {
			v["info"].(map[string]interface{})["name"] = m[e].Info.Name
			v["info"].(map[string]interface{})["phone"] = m[e].Info.Mobile
			v["info"].(map[string]interface{})["company"] = m[e].Info.Company
			v["info"].(map[string]interface{})["description"] = m[e].Info.Description
			v["info"].(map[string]interface{})["status"] = m[e].Status
			v["settings"].(map[string]interface{})["tz"] = tz
			v["settings"].(map[string]interface{})["timezone"] = timezone
		}
	}
	var result = map[string]interface{}{
		"total": total,
		"list":  d,
	}
	return result, nil
}

// AddProjectEnvironmentUser 检查与绑定客户与用户的关系的逻辑移到cloud
func (s *ProjectService) AddProjectEnvironmentUser(ctx *gin.Context, data models.ProjectEnvironmentUser) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		la := ctx.Request.Header.Get("Accept-Language")
		lang := ctx.GetHeader("Accept-Language")
		if data.EmailLanguage != nil {
			lang = *data.EmailLanguage
		}
		if len(lang) != 0 {
			ctx.Request.Header.Set("Accept-Language", lang)
		}
		// 检查用户是否已激活
		projectAdmins := struct {
			ID     primitive.ObjectID   `bson:"_id"`
			Admins []primitive.ObjectID `bson:"administrators"`
		}{}
		opts := &options.FindOneOptions{
			Projection: bson.M{
				"_id":            1,
				"administrators": 1},
		}
		err := tools.Database.Collection("project").FindOne(sctx, bson.M{"_id": data.ProjectID}, opts).Decode(&projectAdmins)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 按email查询匹配的用户
		userCollection, err := tools.UserFetch(&models.UserFetchRequest{Emails: []string{data.Email}}, locales.Lang(ctx))
		if err != nil {
			return nil, errors.WithStack(err)
		}
		users := make([]*models.UserData, 0)
		if len(userCollection) > 0 {
			for _, u := range userCollection {
				if u.Deleted == false {
					users = append(users, u)
				}
			}
		}
		if len(users) == 0 {
			_, err := tools.Database.Collection("user").UpdateOne(sctx, bson.M{"info.email": strings.ToLower(data.Email)}, bson.M{"$set": bson.M{"deleted": true}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			ctx.Request.Header.Set("Accept-Language", la)
			return nil, tools.BuildServerError(ctx, "user.no.exist")
		}
		if len(lang) != 0 {
			ctx.Request.Header.Set("Accept-Language", lang)
		}
		userData := users[0]
		cloudId, err := primitive.ObjectIDFromHex(userData.Id)
		if err != nil {
			return nil, err
		}
		var user models.User
		if err := tools.Database.Collection("user").FindOne(sctx, bson.M{"cloud_id": cloudId, "deleted": bson.M{"$ne": true}}).Decode(&user); err != nil {
			ctx.Request.Header.Set("Accept-Language", la)
			return nil, tools.BuildServerError(ctx, "user.no.exist")
		}
		if len(lang) != 0 {
			ctx.Request.Header.Set("Accept-Language", lang)
		}
		// 检查用户是在已经存在与此环境下
		if count, _ := tools.Database.Collection("user_project_environment").CountDocuments(sctx, bson.M{
			"customer_id": data.CustomerID,
			"project_id":  data.ProjectID,
			"env_id":      data.EnvID,
			"user_id":     user.ID,
		}); count > 0 {
			ctx.Request.Header.Set("Accept-Language", la)
			return nil, tools.BuildServerError(ctx, "user.exist.env")

		}

		if userData.Status == int32(2) {
			ctx.Request.Header.Set("Accept-Language", la)
			return nil, tools.BuildServerError(ctx, "users.duplicated.cloud-customer")
		}

		customerBool := false
	Loop:
		for _, customer := range userData.Customers {
			if customer.Id == data.CustomerID.Hex() {
				for _, app := range customer.Apps {
					if app == config.CLOUD_KEY {
						customerBool = true
						break Loop
					}
				}
			}
		}
		if customerBool {
			// user_project_environment中添加记录
			roles := make([]primitive.ObjectID, 0)
			if data.Roles != nil {
				roles = data.Roles
			}
			_, ok := slice.Find(projectAdmins.Admins, func(index int, item primitive.ObjectID) bool {
				return item == user.ID
			})
			if ok {
				id := struct {
					ID primitive.ObjectID `bson:"_id"`
				}{}
				err := tools.Database.Collection("project_role_permission").FindOne(sctx, bson.M{"" +
					"project_id": projectAdmins.ID,
					"name": "Project-Admin",
				}, &options.FindOneOptions{
					Projection: bson.M{"_id": 1},
				}).Decode(&id)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				roles = append(roles, id.ID)
			}
			roles = slice.Unique(roles)
			projectEnvUserId := primitive.NewObjectID()
			doc := bson.M{
				"_id":            projectEnvUserId,
				"customer_id":    data.CustomerID,
				"project_id":     data.ProjectID,
				"env_id":         data.EnvID,
				"user_id":        user.ID,
				"roles":          roles,
				"email_language": data.EmailLanguage,
				"meta": models.Meta{
					CreatedBy: user.ID,
					CreatedAt: time.Duration(time.Now().Unix()),
				},
			}
			_, err := tools.Database.Collection("user_project_environment").InsertOne(sctx, doc)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			//添加轨迹
			insertAddProjectUserLog(ctx, sctx, data.EnvID, 1, data, projectEnvUserId)

			var projectInfo []map[string]interface{}
			pipeline := mongo.Pipeline{
				{{Key: "$match", Value: bson.M{"_id": data.ProjectID}}},
				{{Key: "$unwind", Value: "$envs"}},
				{{Key: "$match", Value: bson.M{"envs.id": data.EnvID}}},
				{
					{
						Key: "$project",
						Value: bson.M{
							"_id":    0,
							"number": "$info.number",
							"name":   "$info.name",
							"env":    "$envs.name",
						},
					},
				},
			}
			cursor, err := tools.Database.Collection("project").Aggregate(nil, pipeline)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(sctx, &projectInfo)
			if err != nil {
				return nil, errors.WithStack(err)

			}
			project := projectInfo[0]
			if err != nil {
				return nil, errors.WithStack(err)
			}
			//补发邀请邮件
			if userData.Status == 0 {
				if len(lang) != 0 {
					ctx.Request.Header.Set("Accept-Language", lang)
				}
				_, err = tools.UserInvite(&models.UserInviteRequest{Email: userData.Info.Email, CustomerId: data.CustomerID.Hex(), Admin: false}, locales.Lang(ctx))
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}
			var mails []models.Mail
			//项目授权
			mails = append(mails, models.Mail{
				ID:      primitive.NewObjectID(),
				Subject: "user.notice.title",
				HTML:    "user_notice_new.html",
				ContentData: bson.M{
					"Title":    locales.Tr(ctx, "user.notice.project.bind.title"),
					"Activate": locales.Tr(ctx, "user.notice.return.login"),
					"Content": locales.Tr(ctx, "user.notice_project", bson.M{
						"email":   user.Email,
						"project": fmt.Sprintf("%s-%s", project["number"], project["env"]),
					}),
					"Link": config.CLOUD,
				},
				To:           []string{user.Email},
				Lang:         ctx.GetHeader("Accept-Language"),
				Status:       0,
				CreatedTime:  time.Duration(time.Now().Unix()),
				ExpectedTime: time.Duration(time.Now().Unix()),
				SendTime:     time.Duration(time.Now().Unix()),
			})
			ctx.Set("MAIL", mails)

			if len(mails) > 0 {
				var envs []models.MailEnv
				for _, m := range mails {
					envs = append(envs, models.MailEnv{
						ID:         primitive.NewObjectID(),
						MailID:     m.ID,
						CustomerID: data.CustomerID,
						ProjectID:  data.ProjectID,
						EnvID:      data.EnvID,
						CohortID:   primitive.NilObjectID,
					})
				}
				ctx.Set("MAIL-ENV", envs)
			}
		} else {
			ctx.Request.Header.Set("Accept-Language", la)
			return nil, tools.BuildServerError(ctx, "user.customer.bind.error")
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

// AddProjectEnvironmentBatchUserVerify 批量添加---在项目的环境下添加用户验证邮箱
func (s *ProjectService) AddProjectEnvironmentBatchUserVerify(ctx *gin.Context, data models.ProjectEnvironmentUserList) ([]models.NotAvailableEmail, error) {

	notAvailableEmailList := make([]models.NotAvailableEmail, 0)

	// 检查用户是否已激活
	projectAdmins := struct {
		ID     primitive.ObjectID   `bson:"_id"`
		Admins []primitive.ObjectID `bson:"administrators"`
	}{}
	opts := &options.FindOneOptions{
		Projection: bson.M{
			"_id":            1,
			"administrators": 1},
	}
	err := tools.Database.Collection("project").FindOne(ctx, bson.M{"_id": data.ProjectID}, opts).Decode(&projectAdmins)
	if err != nil {
		return notAvailableEmailList, errors.WithStack(err)
	}

	list := make([]string, 0)
	if data.EmailList != nil && len(data.EmailList) > 0 {
		for _, e := range data.EmailList {
			list = append(list, strings.ToLower(e))
		}
	}
	data.EmailList = list
	// 按email查询匹配的用户
	response, err := tools.UserFetch(&models.UserFetchRequest{Emails: data.EmailList}, locales.Lang(ctx))
	if err != nil {
		return notAvailableEmailList, errors.WithStack(err)
	}
	users := make([]*models.UserData, 0)
	if len(response) > 0 {
		for _, u := range response {
			if u.Deleted == false {
				users = append(users, u)
			}
		}
	}

	totalEmailList := data.EmailList
	cloudEmailList := make([]string, 0)
	if users != nil && len(users) > 0 {
		for _, u := range users {
			cloudEmailList = append(cloudEmailList, u.Info.Email)
			if u.Status == int32(2) {
				//Cloud中用户已禁用，无法添加。
				var notAvailableEmail models.NotAvailableEmail
				notAvailableEmail.Email = u.Info.Email
				notAvailableEmail.Reason = locales.Tr(ctx, "users.duplicated.cloud-disable")
				notAvailableEmailList = append(notAvailableEmailList, notAvailableEmail)
				continue
			}

			customerBool := false
			for _, customer := range u.Customers {
				if customer.Id == data.CustomerID.Hex() {
					for _, app := range customer.Apps {
						if app == config.CLOUD_KEY {
							customerBool = true
						}
					}
				}
			}

			if !customerBool {
				var notAvailableEmail models.NotAvailableEmail
				notAvailableEmail.Email = u.Info.Email
				notAvailableEmail.Reason = locales.Tr(ctx, "user.customer.bind.error1")
				notAvailableEmailList = append(notAvailableEmailList, notAvailableEmail)
				continue
			}

			cloudOID, _ := primitive.ObjectIDFromHex(u.Id)

			var user models.User
			err = tools.Database.Collection("user").FindOne(ctx, bson.M{"cloud_id": cloudOID, "deleted": bson.M{"$ne": true}}).Decode(&user)
			if err != nil && err != mongo.ErrNoDocuments {
				return notAvailableEmailList, errors.WithStack(err)
			}
			if user.ID != primitive.NilObjectID {
				// 检查用户是在已经存在与此环境下
				if count, _ := tools.Database.Collection("user_project_environment").CountDocuments(ctx, bson.M{
					"customer_id": data.CustomerID,
					"project_id":  data.ProjectID,
					"env_id":      data.EnvID,
					"user_id":     user.ID,
				}); count > 0 {
					var notAvailableEmail models.NotAvailableEmail
					notAvailableEmail.Email = u.Info.Email
					notAvailableEmail.Reason = locales.Tr(ctx, "user.exist.env")
					notAvailableEmailList = append(notAvailableEmailList, notAvailableEmail)
					continue
				}
			} else {
				var notAvailableEmail models.NotAvailableEmail
				notAvailableEmail.Email = u.Info.Email
				notAvailableEmail.Reason = locales.Tr(ctx, "user.no.exist")
				notAvailableEmailList = append(notAvailableEmailList, notAvailableEmail)
			}

		}
	}

	noEmailList := removeElements(totalEmailList, cloudEmailList)
	if noEmailList != nil && len(noEmailList) > 0 {
		for _, email := range noEmailList {
			var notAvailableEmail models.NotAvailableEmail
			notAvailableEmail.Email = email
			notAvailableEmail.Reason = locales.Tr(ctx, "user.no.exist")
			notAvailableEmailList = append(notAvailableEmailList, notAvailableEmail)
		}
	}

	return notAvailableEmailList, nil
}

// 删除一个切片中的另一个切片的元素
func removeElements(slice []string, toRemove []string) []string {
	// 创建一个 map 来存储要删除的元素
	toRemoveMap := make(map[string]struct{})
	for _, v := range toRemove {
		toRemoveMap[v] = struct{}{}
	}

	// 创建一个新的切片来存储结果
	result := []string{}
	for _, v := range slice {
		if _, found := toRemoveMap[v]; !found {
			result = append(result, v) // 仅添加未被删除的元素
		}
	}

	return result
}

// AddProjectEnvironmentBatchUser 批量添加---检查与绑定客户与用户的关系的逻辑移到cloud
func (s *ProjectService) AddProjectEnvironmentBatchUser(ctx *gin.Context, data models.ProjectEnvironmentUserList) ([]models.UserRoleEmail, error) {

	userRoleEmailList := make([]models.UserRoleEmail, 0)

	lang := ctx.GetHeader("Accept-Language")
	if data.EmailLanguage != nil {
		lang = *data.EmailLanguage
	}
	ctx.Request.Header.Set("Accept-Language", lang)

	list := make([]string, 0)
	if data.EmailList != nil && len(data.EmailList) > 0 {
		for _, e := range data.EmailList {
			list = append(list, strings.ToLower(e))
		}
	}
	data.EmailList = list

	callback := func(sctx mongo.SessionContext) (interface{}, error) {

		// 检查用户是否已激活
		projectAdmins := struct {
			ID     primitive.ObjectID   `bson:"_id"`
			Admins []primitive.ObjectID `bson:"administrators"`
		}{}
		opts := &options.FindOneOptions{
			Projection: bson.M{
				"_id":            1,
				"administrators": 1},
		}
		err := tools.Database.Collection("project").FindOne(sctx, bson.M{"_id": data.ProjectID}, opts).Decode(&projectAdmins)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 按email查询匹配的用户
		response, err := tools.UserFetch(&models.UserFetchRequest{Emails: data.EmailList}, locales.Lang(ctx))
		if err != nil {
			return nil, errors.WithStack(err)
		}
		userList := make([]*models.UserData, 0)
		if len(response) > 0 {
			for _, u := range response {
				if u.Deleted == false {
					userList = append(userList, u)
				} else {
					_, err := tools.Database.Collection("user").UpdateOne(sctx, bson.M{"info.email": strings.ToLower(u.Info.Email)}, bson.M{"$set": bson.M{"deleted": true}})
					if err != nil {
						return nil, errors.WithStack(err)
					}
				}
			}
		}

		userCloudList, err := sortEmailInfo(userList, data.EmailList)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		if userCloudList != nil && len(userCloudList) > 0 {
			mails := make([]models.Mail, 0)
			for _, u := range userCloudList {

				cloudOID, _ := primitive.ObjectIDFromHex(u.Id)
				var user models.User
				err = tools.Database.Collection("user").FindOne(nil, bson.M{"cloud_id": cloudOID, "deleted": bson.M{"$ne": true}}).Decode(&user)
				if err != nil && err != mongo.ErrNoDocuments {
					return nil, errors.WithStack(err)
				}

				customerBool := false
			Loop:
				for _, customer := range u.Customers {
					if customer.Id == data.CustomerID.Hex() {
						for _, app := range customer.Apps {
							if app == config.CLOUD_KEY {
								customerBool = true
								break Loop
							}
						}
					}
				}
				if customerBool && user.ID != primitive.NilObjectID {
					// user_project_environment中添加记录
					roles := make([]primitive.ObjectID, 0)
					if data.Roles != nil {
						roles = data.Roles
					}
					_, ok := slice.Find(projectAdmins.Admins, func(index int, item primitive.ObjectID) bool {
						return item == user.ID
					})
					if ok {
						id := struct {
							ID primitive.ObjectID `bson:"_id"`
						}{}
						err := tools.Database.Collection("project_role_permission").FindOne(sctx, bson.M{"" +
							"project_id": projectAdmins.ID,
							"name": "Project-Admin",
						}, &options.FindOneOptions{
							Projection: bson.M{"_id": 1},
						}).Decode(&id)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						roles = append(roles, id.ID)
					}
					roles = slice.Unique(roles)
					projectEnvUserId := primitive.NewObjectID()
					doc := bson.M{
						"_id":            projectEnvUserId,
						"customer_id":    data.CustomerID,
						"project_id":     data.ProjectID,
						"env_id":         data.EnvID,
						"user_id":        user.ID,
						"roles":          roles,
						"email_language": data.EmailLanguage,
						"meta": models.Meta{
							CreatedBy: user.ID,
							CreatedAt: time.Duration(time.Now().Unix()),
						},
					}
					_, err := tools.Database.Collection("user_project_environment").InsertOne(sctx, doc)
					if err != nil {
						return nil, errors.WithStack(err)
					}

					var projectEnvironmentUser models.ProjectEnvironmentUser
					projectEnvironmentUser.CustomerID = data.CustomerID
					projectEnvironmentUser.ProjectID = data.ProjectID
					projectEnvironmentUser.EnvID = data.EnvID
					projectEnvironmentUser.EmailLanguage = data.EmailLanguage
					projectEnvironmentUser.Email = u.Info.Email
					projectEnvironmentUser.Roles = data.Roles
					projectEnvironmentUser.UnblindingAvailableCount = data.UnblindingAvailableCount

					//添加轨迹
					insertAddProjectUserLog(ctx, sctx, data.EnvID, 1, projectEnvironmentUser, projectEnvUserId)

					var userRoleEmail models.UserRoleEmail
					userRoleEmail.UserId = user.ID
					userRoleEmail.UserName = u.Info.Name
					userRoleEmail.Email = user.Email
					roleList, err := GetBatchUserRoles(ctx, data.ProjectID.Hex(), user.ID.Hex())
					if err != nil {
						return nil, errors.WithStack(err)
					}
					userRoleEmail.RoleList = roleList
					userRoleEmailList = append(userRoleEmailList, userRoleEmail)

					var projectInfo []map[string]interface{}
					pipeline := mongo.Pipeline{
						{{Key: "$match", Value: bson.M{"_id": data.ProjectID}}},
						{{Key: "$unwind", Value: "$envs"}},
						{{Key: "$match", Value: bson.M{"envs.id": data.EnvID}}},
						{
							{
								Key: "$project",
								Value: bson.M{
									"_id":    0,
									"number": "$info.number",
									"name":   "$info.name",
									"env":    "$envs.name",
								},
							},
						},
					}
					cursor, err := tools.Database.Collection("project").Aggregate(ctx, pipeline)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					err = cursor.All(sctx, &projectInfo)
					if err != nil {
						return nil, errors.WithStack(err)

					}
					project := projectInfo[0]
					if err != nil {
						return nil, errors.WithStack(err)
					}
					//补发邀请邮件
					if u.Status == 0 {
						if len(lang) != 0 {
							ctx.Request.Header.Set("Accept-Language", lang)
						}
						_, err = tools.UserInvite(&models.UserInviteRequest{Email: u.Info.Email, CustomerId: data.CustomerID.Hex(), Admin: false}, locales.Lang(ctx))
						if err != nil {
							return nil, errors.WithStack(err)
						}
					}

					//项目授权
					mails = append(mails, models.Mail{
						ID:      primitive.NewObjectID(),
						Subject: "user.notice.title",
						HTML:    "user_notice_new.html",
						ContentData: bson.M{
							"Title":    locales.Tr(ctx, "user.notice.project.bind.title"),
							"Activate": locales.Tr(ctx, "user.notice.return.login"),
							"Content": locales.Tr(ctx, "user.notice_project", bson.M{
								"email":   user.Email,
								"project": fmt.Sprintf("%s-%s", project["number"], project["env"]),
							}),
							"Link": config.CLOUD,
						},
						To:           []string{user.Email},
						Lang:         ctx.GetHeader("Accept-Language"),
						Status:       0,
						CreatedTime:  time.Duration(time.Now().Unix()),
						ExpectedTime: time.Duration(time.Now().Unix()),
						SendTime:     time.Duration(time.Now().Unix()),
					})

					if len(mails) > 0 {
						var envs []models.MailEnv
						for _, m := range mails {
							envs = append(envs, models.MailEnv{
								ID:         primitive.NewObjectID(),
								MailID:     m.ID,
								CustomerID: data.CustomerID,
								ProjectID:  data.ProjectID,
								EnvID:      data.EnvID,
								CohortID:   primitive.NilObjectID,
							})
						}
						ctx.Set("MAIL-ENV", envs)
					}
				}
			}
			ctx.Set("MAIL", mails)
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return userRoleEmailList, nil
}

func sortEmailInfo(UserData []*models.UserData, emailList []string) ([]*models.UserData, error) {
	// 创建一个map用于快速查找data中每个Email对应的Info
	indexMap := make(map[string]*models.UserData)
	for _, user := range UserData {
		indexMap[user.Info.Email] = user
	}

	// 按照A[]数组的顺序重构data
	sortedData := make([]*models.UserData, 0, len(emailList))
	for _, email := range emailList {
		if info, found := indexMap[email]; found {
			sortedData = append(sortedData, info)
		}
	}

	return sortedData, nil
}

func GetBatchUserRoles(ctx *gin.Context, projectID string, userID string) ([]models.ProjectRole, error) {
	userOID, _ := primitive.ObjectIDFromHex(userID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": userOID}}},
		{{Key: "$unwind", Value: "$roles"}},
		{{Key: "$lookup", Value: bson.M{"from": "project_role_permission", "localField": "roles", "foreignField": "role_id", "as": "role"}}},
		{{Key: "$unwind", Value: "$role"}},
		{{Key: "$match", Value: bson.M{"role.project_id": projectOID, "role.status": 1}}},
		{{Key: "$project", Value: bson.M{
			"_id":      0,
			"id":       "$role._id",
			"name":     "$role.name",
			"scope":    "$role.scope",
			"template": "$role.template",
			"status":   "$role.status"}}},
		{{Key: "$sort", Value: bson.D{{"name", 1}}}},
	}

	d := make([]models.ProjectRole, 0)
	rs := make([]models.ProjectRole, 0)
	cursor, err := tools.Database.Collection("user").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &d)

	project := models.Project{}
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if slice.Contain(project.Administrators, userOID) {
		projectAdmin := models.ProjectRolePermission{}
		err := tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{
			"project_id": projectOID,
			"name":       "Project-Admin",
		}).Decode(&projectAdmin)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		d = append(d, models.ProjectRole{
			ID:       projectAdmin.ID,
			Name:     projectAdmin.Name,
			Scope:    projectAdmin.Scope,
			Template: projectAdmin.Template,
			Type:     0,
			Status:   projectAdmin.Status,
		})
	}
	for _, r := range d {
		rs = append(rs, models.ProjectRole{
			ID:       r.ID,
			Name:     r.Name,
			Scope:    r.Scope,
			Template: r.Template,
			Type:     data.RolePoolMap[r.Name].Type,
			Status:   r.Status,
		})
	}

	if err != nil {
		return nil, errors.WithStack(err)
	}

	return rs, nil
}

// updateVisitNoticeUser 访视通知: 如勾选对应角色的全部用户后，在后续对应角色新增用户后，同步默认进行通知，启用对应的通知规则。
func updateVisitNoticeUser(ctx *gin.Context, projectID string, envID string) error {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)

	visitNoticeList := make([]models.VisitNotice, 0)
	visitNoticeCursor, err := tools.Database.Collection("visit_notice").Find(ctx, bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
		"status":     0,
		"$and": bson.A{
			bson.M{"all_roleIDs": bson.M{"$ne": nil}},
			bson.M{"all_roleIDs": bson.M{"$ne": bson.A{}}},
		},
	})
	if err != nil {
		return errors.WithStack(err)
	}
	err = visitNoticeCursor.All(ctx, &visitNoticeList)
	if err != nil {
		return errors.WithStack(err)
	}

	if visitNoticeList != nil && len(visitNoticeList) > 0 {
		for _, notice := range visitNoticeList {
			if notice.NoticeUser != nil && len(notice.NoticeUser) > 0 {
				addUserIdList := make([]primitive.ObjectID, 0)
				deleteUserIdList := make([]primitive.ObjectID, 0)
				if notice.AllRoleIDs != nil && len(notice.AllRoleIDs) > 0 {
					for _, roleID := range notice.AllRoleIDs {
						userEnvFilter := bson.M{"roles": bson.M{"$elemMatch": bson.M{"$eq": roleID}}, "project_id": notice.ProjectID, "env_id": notice.EnvID}
						userEnvs := make([]models.UserProjectEnvironment, 0)
						userEnvCursor, err := tools.Database.Collection("user_project_environment").Find(ctx, userEnvFilter)
						if err != nil {
							return errors.WithStack(err)
						}
						err = userEnvCursor.All(nil, &userEnvs)
						if err != nil {
							return errors.WithStack(err)
						}

						if userEnvs != nil && len(userEnvs) > 0 {
							for _, userEnv := range userEnvs {
								index := arrays.Contains(notice.NoticeUser, userEnv.UserID)
								if index == -1 {
									if !userEnv.Unbind {
										addUserIdList = append(addUserIdList, userEnv.UserID)
									}
								} else {
									if userEnv.Unbind {
										deleteUserIdList = append(deleteUserIdList, userEnv.UserID)
									}
								}
							}
						}
					}
				}

				newUserIdList := make([]primitive.ObjectID, 0)
				if notice.NoticeUser != nil && len(notice.NoticeUser) > 0 {
					if deleteUserIdList != nil && len(deleteUserIdList) > 0 {
						for _, newId := range notice.NoticeUser {
							for _, deleteId := range deleteUserIdList {
								if newId != deleteId {
									newUserIdList = append(newUserIdList, newId)
								}
							}
						}
					} else {
						newUserIdList = notice.NoticeUser
					}
					if addUserIdList != nil && len(addUserIdList) > 0 {
						for _, addId := range addUserIdList {
							index := arrays.Contains(newUserIdList, addId)
							if index == -1 {
								newUserIdList = append(newUserIdList, addId)
							}
						}
					}
				}

				update := bson.M{"$set": bson.M{"notice_user": newUserIdList}}
				_, err = tools.Database.Collection("visit_notice").UpdateOne(ctx,
					bson.M{"_id": notice.ID},
					update,
				)
				if err != nil {
					return errors.WithStack(err)
				}
			}

		}
	}

	return nil
}

// updateProjectNoticeUser 项目通知: 如勾选对应角色的全部用户后，在后续对应角色新增用户后，同步默认进行通知，启用对应的通知规则。
func updateProjectNoticeUser(ctx *gin.Context, projectID string, envID string) error {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)

	projectNoticeList := make([]models.ProjectNotice, 0)
	projectNoticeCursor, err := tools.Database.Collection("project_notice").Find(ctx, bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
	})
	if err != nil {
		return errors.WithStack(err)
	}
	err = projectNoticeCursor.All(ctx, &projectNoticeList)
	if err != nil {
		return errors.WithStack(err)
	}

	if projectNoticeList != nil && len(projectNoticeList) > 0 {
		for _, notice := range projectNoticeList {
			roleUserList := make([]models.RoleUsers, 0)
			if notice.RoleUser != nil && len(notice.RoleUser) > 0 {
				for _, ru := range notice.RoleUser {
					userEnvFilter := bson.M{"roles": bson.M{"$elemMatch": bson.M{"$eq": ru.RoleID}}, "project_id": notice.ProjectID, "env_id": notice.EnvID}
					userEnvs := make([]models.UserProjectEnvironment, 0)
					userEnvCursor, err := tools.Database.Collection("user_project_environment").Find(ctx, userEnvFilter)
					if err != nil {
						return errors.WithStack(err)
					}
					err = userEnvCursor.All(nil, &userEnvs)
					if err != nil {
						return errors.WithStack(err)
					}

					addUserIdList := make([]primitive.ObjectID, 0)
					deleteUserIdList := make([]primitive.ObjectID, 0)

					if userEnvs != nil && len(userEnvs) > 0 && ru.Users != nil && len(ru.Users) > 0 && len(userEnvs) != len(ru.Users) {
						for _, userEnv := range userEnvs {
							index := arrays.Contains(ru.Users, userEnv.UserID)
							if index == -1 {
								if !userEnv.Unbind {
									addUserIdList = append(addUserIdList, userEnv.UserID)
								}
							} else {
								if userEnv.Unbind {
									deleteUserIdList = append(deleteUserIdList, userEnv.UserID)
								}
							}
						}
					}

					newUserIdList := make([]primitive.ObjectID, 0)
					if ru.Users != nil && len(ru.Users) > 0 {
						if deleteUserIdList != nil && len(deleteUserIdList) > 0 {
							for _, newId := range ru.Users {
								for _, deleteId := range deleteUserIdList {
									if newId != deleteId {
										newUserIdList = append(newUserIdList, newId)
									}
								}
							}
						} else {
							newUserIdList = ru.Users
						}
						if addUserIdList != nil && len(addUserIdList) > 0 {
							for _, addId := range addUserIdList {
								index := arrays.Contains(newUserIdList, addId)
								if index == -1 {
									newUserIdList = append(newUserIdList, addId)
								}
							}
						}
					}

					ru.Users = newUserIdList

					roleUserList = append(roleUserList, ru)
				}
			}

			update := bson.M{"$set": bson.M{"user_ids": roleUserList}}
			_, err = tools.Database.Collection("visit_notice").UpdateOne(ctx,
				bson.M{
					"project_id": projectOID,
					"env_id":     envOID,
				},
				update,
			)
			if err != nil {
				return errors.WithStack(err)
			}

		}
	}

	return nil
}

func (s *ProjectService) SetProjectEnvironmentUserRoles(ctx *gin.Context, projectID string, envID string, userID string, projectEnvUser models.ProjectEnvironmentUser) error {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	userOID, _ := primitive.ObjectIDFromHex(userID)
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		userEmail := ""
		userName := ""
		var oldProjectEnvUser models.UserProjectEnvironment
		err := tools.Database.Collection("user_project_environment").FindOne(sctx, bson.M{"env_id": envOID, "user_id": userOID}).Decode(&oldProjectEnvUser)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var project models.Project
		err = tools.Database.Collection("project").FindOne(sctx, bson.M{"_id": projectOID}).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		projectRoleId := struct {
			ID primitive.ObjectID `bson:"_id"`
		}{}
		err = tools.Database.Collection("project_role_permission").FindOne(sctx, bson.M{"" +
			"project_id": project.ID,
			"name": "Project-Admin",
		}, &options.FindOneOptions{
			Projection: bson.M{"_id": 1},
		}).Decode(&projectRoleId)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if slice.Contain(project.Administrators, userOID) {
			projectEnvUser.Roles = append(projectEnvUser.Roles, projectRoleId.ID)
		}
		projectEnvUser.Roles = slice.Unique(projectEnvUser.Roles)
		//项目动态
		{
			projectRoles := make([]models.ProjectRolePermission, 0)
			cursor, err := tools.Database.Collection("project_role_permission").Find(sctx, bson.M{"_id": bson.M{"$in": projectEnvUser.Roles}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(sctx, &projectRoles)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			roleNames := slice.Map(projectRoles, func(index int, item models.ProjectRolePermission) string {
				return item.Name
			})
			roles := strings.Join(roleNames, "、")
			me, err := tools.Me(ctx)
			if err != nil {
				return nil, err
			}
			user := models.User{}
			err = tools.Database.Collection("user").FindOne(sctx, bson.M{"_id": userOID}).Decode(&user)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if len(users) > 0 {
				userData := users[0]
				userEmail = userData.Info.Email
				userName = userData.Info.Name
			} else {
				return nil, tools.BuildServerError(ctx, "common.operation.fail")
			}

			dynamics := models.ProjectDynamics{
				ID:          primitive.NewObjectID(),
				Operator:    me.ID,
				OID:         envOID,
				Time:        time.Duration(time.Now().Unix()),
				SceneTran:   "project_dynamics_scene_personnel",
				TypeTran:    "project_dynamics_type_role_assignment",
				ContentTran: "project_dynamics_content_role_assignment",
				ContentData: map[string]interface{}{
					"userId":  userOID,
					"email":   userEmail,
					"roleIds": projectEnvUser.Roles,
					"roles":   roles,
				},
			}
			_, err = tools.Database.Collection("project_dynamics").InsertOne(sctx, dynamics)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
		filter := bson.M{
			"project_id": projectOID,
			"env_id":     envOID,
			"user_id":    userOID,
		}
		update := bson.M{
			"$set": bson.M{"roles": projectEnvUser.Roles},
		}
		if _, err = tools.Database.Collection("user_project_environment").UpdateOne(sctx, filter, update); err != nil {
			return nil, errors.WithStack(err)
		}

		//增加轨迹
		userNameEmail := userEmail
		if userName != "" {
			userNameEmail = userName + "," + userNameEmail
		}

		insertRolesProjectUserLog(ctx, sctx, envOID, 5, oldProjectEnvUser, projectEnvUser, userNameEmail, oldProjectEnvUser.ID)

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}

	//更新项目通知、访视通知的通知用户
	err = updateVisitNoticeUser(ctx, projectID, envID)
	if err != nil {
		return errors.WithStack(err)
	}
	err = updateProjectNoticeUser(ctx, projectID, envID)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (s *ProjectService) SetEnvUserUnblindingCode(ctx *gin.Context, projectID string, envID string, userID string, projectEnvUser models.ProjectEnvironmentUser) (interface{}, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	userOID, _ := primitive.ObjectIDFromHex(userID)
	codes := struct {
		UnblindingCode models.UnblindingCode `json:"unblindingCode" bson:"unblinding_code"`
	}{}
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var project models.Project
		err := tools.Database.Collection("project").FindOne(sctx, bson.M{"_id": projectOID}).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		filter := bson.M{
			"project_id": projectOID,
			"env_id":     envOID,
			"user_id":    userOID,
		}
		var userProjectEnv models.UserProjectEnvironment
		newAvailableCount := projectEnvUser.UnblindingAvailableCount
		if project.UnblindingControl == 1 && newAvailableCount >= 0 {
			err = tools.Database.Collection("user_project_environment").FindOne(sctx, filter).Decode(&userProjectEnv)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			availableCode := userProjectEnv.UnblindingCode.AvailableCode
			if newAvailableCount > len(availableCode) {
				addCount := newAvailableCount - len(availableCode)
				for i := 0; i < addCount; i++ {
					newCodes := tools.RandString(8)
					availableCode = append(availableCode, newCodes)
				}
			} else if newAvailableCount < len(availableCode) {
				availableCode = availableCode[:newAvailableCount]
			}
			update := bson.M{
				"$set": bson.M{"unblinding_code.available_code": availableCode},
			}
			opts := options.FindOneAndUpdate().SetReturnDocument(options.After)
			err = tools.Database.Collection("user_project_environment").FindOneAndUpdate(sctx, filter, update, opts).Decode(&codes)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			user := models.User{}
			err = tools.Database.Collection("user").FindOne(sctx, bson.M{"_id": userOID}).Decode(&user)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))
			if err != nil {
				return nil, errors.WithStack(err)
			}
			userEmail := ""
			userName := ""
			if len(users) > 0 {
				userData := users[0]
				userEmail = userData.Info.Email
				userName = userData.Info.Name
			} else {
				return nil, tools.BuildServerError(ctx, "common.operation.fail")
			}
			//增加轨迹
			userNameEmail := userEmail
			if userName != "" {
				userNameEmail = userName + "," + userNameEmail
			}
			insertUnblindingCodeProjectUserLog(ctx, sctx, userProjectEnv.EnvID, 1, newAvailableCount, userNameEmail, userProjectEnv.ID)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return nil, err
	}
	return codes, nil
}

func (s *ProjectService) UnbindProjectEnvironmentUser(ctx *gin.Context, projectID string, envID string, userID string) error {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	userOID, _ := primitive.ObjectIDFromHex(userID)
	filter := bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
		"user_id":    userOID,
	}

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var user models.User
		err := tools.Database.Collection("user").FindOne(sctx, bson.M{"_id": userOID}).Decode(&user)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var userProjectEnv models.UserProjectEnvironment
		tools.Database.Collection("user_project_environment").FindOneAndUpdate(sctx, filter,
			bson.M{"$set": bson.M{
				"unbind": true,
			}}).Decode(&userProjectEnv)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = insertUnbindInviteProjectUserLog(ctx, sctx, envOID, 7, 1, 0, user.Email, userProjectEnv.ID, "")
		if err != nil {
			return nil, err
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}

	//更新项目通知、访视通知的通知用户
	err = updateVisitNoticeUser(ctx, projectID, envID)
	if err != nil {
		return errors.WithStack(err)
	}
	err = updateProjectNoticeUser(ctx, projectID, envID)
	if err != nil {
		return errors.WithStack(err)
	}

	//websocket通知
	go func() {
		wsResult := models.WsResult{
			EnvId:  envOID,
			UserId: userOID,
			Text:   "unbindUser",
		}

		jsonData, err := json.Marshal(wsResult)
		if err != nil {
			log.Fatalf("JSON marshaling failed: %s", err)
		}
		jsonString := string(jsonData)
		ws.Notify(userID, jsonString)
	}()

	return nil
}

// 批量解绑
func (s *ProjectService) BatchUnbindProjectEnvironmentUser(ctx *gin.Context, projectID string, envID string, userIDs []string) error {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)

	userOIDs := make([]primitive.ObjectID, 0, len(userIDs))
	// 遍历转换每个ID
	for _, id := range userIDs {
		oid, _ := primitive.ObjectIDFromHex(id)
		userOIDs = append(userOIDs, oid)
	}

	filter := bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
		"user_id":    bson.M{"$in": userOIDs},
	}

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		// 查询所有用户
		cursor, err := tools.Database.Collection("user").Find(sctx, bson.M{"_id": bson.M{"$in": userOIDs}})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		defer cursor.Close(sctx)
		var users []models.User
		if err = cursor.All(sctx, &users); err != nil {
			return nil, errors.WithStack(err)
		}

		// 更新所有匹配的用户项目环境记录
		_, err = tools.Database.Collection("user_project_environment").UpdateMany(
			sctx,
			filter,
			bson.M{"$set": bson.M{"unbind": true}},
		)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 为每个用户插入日志
		for _, user := range users {
			var userProjectEnv models.UserProjectEnvironment
			err := tools.Database.Collection("user_project_environment").FindOne(
				sctx,
				bson.M{
					"project_id": projectOID,
					"env_id":     envOID,
					"user_id":    user.ID,
				},
			).Decode(&userProjectEnv)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			err = insertUnbindInviteProjectUserLog(
				ctx, sctx, envOID, 7, 1, 0, user.Email, userProjectEnv.ID, "",
			)
			if err != nil {
				return nil, err
			}
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}

	//更新项目通知、访视通知的通知用户
	err = updateVisitNoticeUser(ctx, projectID, envID)
	if err != nil {
		return errors.WithStack(err)
	}
	err = updateProjectNoticeUser(ctx, projectID, envID)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

// 复制环境
func (s *ProjectService) CopyEnvironment(ctx *gin.Context, data map[string]interface{}) error {
	req := struct {
		ProjectID primitive.ObjectID   `json:"projectId"`
		EnvID     primitive.ObjectID   `json:"envId"`
		CohortIDs []primitive.ObjectID `json:"cohortIds"`
		EnvName   string               `json:"envName"`
		IsContain bool                 `json:"isContain"`
	}{}
	_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
	if req.ProjectID.IsZero() || req.EnvID.IsZero() || req.EnvName == "" {
		return tools.BuildServerError(ctx, "common.wrong.parameters")
	}
	projectID := req.ProjectID
	oldEnvID := req.EnvID
	envName := req.EnvName

	newCopyEnv := req.EnvName

	oldEnvName := ""

	ipList := make([]string, 0)
	unnumberedIpList := make([]string, 0)

	collection := tools.Database.Collection("project")
	project := models.Project{}
	// 先检查环境是否重复 只检查基本研究
	err := collection.FindOne(ctx, bson.M{"_id": projectID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}

	newEnvID := primitive.NewObjectID()
	isNewEnvExist := false
	existEnvId := primitive.NilObjectID

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		me, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		if project.Environments != nil && len(project.Environments) > 0 {
			for _, environment := range project.Environments {
				if environment.ID == oldEnvID {
					oldEnvName = environment.Name
				}
			}
		}

		//判断环境名称是否重复
		if project.Type == 1 {
			_, a := slice.Find(project.Environments, func(index int, item models.Environment) bool {
				return item.Name == envName
			})

			if a {
				envName = envName + "-copy"
			}
			exists := true
			num := 1
			newName := envName
			for exists {
				_, b := slice.Find(project.Environments, func(index int, item models.Environment) bool {
					return item.Name == newName
				})

				if b {
					num += 1
					newName = envName + strconv.Itoa(num)
				} else {
					envName = newName
					exists = false
				}
			}
			if req.EnvName == "PROD" {
				newCopyEnv = req.EnvName
			} else {
				newCopyEnv = envName
			}
		}
		// 插入环境信息

		//cohort和再随机先判断目标环境是否存在
		newEnvp, newEnvExist := slice.Find(project.Environments, func(index int, item models.Environment) bool {
			return item.Name == req.EnvName
		})
		isNewEnvExist = newEnvExist
		oldEnvp, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
			return req.EnvID == item.ID
		})
		var newEnv models.Environment
		if newEnvExist {
			newEnv = *newEnvp
			existEnvId = newEnvp.ID
		}
		oldEnv := *oldEnvp
		if project.Type != 1 && newEnvExist && oldEnvName != "PROD" {
			newEnvID = newEnv.ID
		}

		//新建的
		sourceList := make([]models.Cohort, 0)
		//共有的
		targetNameList := make([]string, 0)

		if newEnvExist && req.EnvName == "PROD" {
			old, oldEnvExist := slice.Find(project.Environments, func(index int, item models.Environment) bool {
				return item.ID == req.EnvID
			})
			newEnvID = newEnv.ID
			if oldEnvExist {

				cohortMappingList := make([]models.CohortMapping, 0)
				if project.Type == 1 {
					if !old.IsCopy {
						return nil, tools.BuildServerError(ctx, "env.copy.prod.isCopy")
					}
				} else {
					sourceCohortList := make([]models.Cohort, 0)
					if len(req.CohortIDs) > 0 {
						sourceCohortList = slice.Filter(oldEnv.Cohorts, func(index int, item models.Cohort) bool {
							return slice.Contain(req.CohortIDs, item.ID)
						})
					} else {
						if old.Cohorts != nil && len(old.Cohorts) > 0 {
							sourceCohortList = old.Cohorts
						}
					}
					targetCohortList := make([]models.Cohort, 0)
					if newEnvp.Cohorts != nil && len(newEnvp.Cohorts) > 0 {
						targetCohortList = newEnvp.Cohorts
					}

					nameMap := make(map[string]bool)
					// 将A数组中的所有name加入到map中
					for _, item := range targetCohortList {
						nameMap[item.Name] = true
					}

					targetList := make([]models.Cohort, 0)

					// 遍历B数组，检查B中的name是否不在A中
					for _, item := range sourceCohortList {
						if !nameMap[item.Name] {
							sourceList = append(sourceList, item)
						} else {
							//都有
							targetList = append(targetList, item)
							targetNameList = append(targetNameList, item.Name)
						}
					}

					if sourceList != nil && len(sourceList) > 0 {
						for _, cohort := range sourceList {
							// 修改操作
							update := bson.M{
								"$set": bson.M{
									"envs.$[env].cohorts.$[cohort].is_copy_to_prod": true,
								},
							}
							opts := &options.UpdateOptions{
								ArrayFilters: &options.ArrayFilters{
									Filters: bson.A{bson.M{"env.id": old.ID}, bson.M{"cohort.id": cohort.ID}},
								},
							}
							if _, err := collection.UpdateOne(sctx, bson.M{"_id": projectID}, update, opts); err != nil {
								return nil, errors.WithStack(err)
							}
						}
					}

					if targetList != nil && len(targetList) > 0 {
						//if !old.IsCopy {
						//	return nil, tools.BuildServerError(ctx, "env.copy.prod.isCopy")
						//}

						for _, cohort := range targetList {
							if cohort.IsCopyToProd || !cohort.IsCopy {
								return nil, tools.BuildServerError(ctx, "env.copy.prod.isCopy")
							}
						}

						if newEnvp.Cohorts != nil && len(newEnvp.Cohorts) > 0 {
							for _, newCohort := range newEnvp.Cohorts {
								if old.Cohorts != nil && len(old.Cohorts) > 0 {
									for _, oldCohort := range old.Cohorts {
										if newCohort.Name == oldCohort.Name {
											for _, cohort := range targetList {
												if cohort.ID == oldCohort.ID {
													if project.Type == 3 {
														//再随机项目--
														var cohortMapping models.CohortMapping
														cohortMapping.OldCohortID = oldCohort.ID
														cohortMapping.NewCohortID = newCohort.ID
														cohortMappingList = append(cohortMappingList, cohortMapping)
													} else {
														//群组项目--
														var cohortMapping models.CohortMapping
														cohortMapping.OldCohortID = oldCohort.ID
														cohortMapping.NewCohortID = newCohort.ID
														cohortMappingList = append(cohortMappingList, cohortMapping)
													}
												}
											}
										}
									}
								}
							}
						}

						factor, err := judgeTypeGroupFactor(ctx, sctx, project, oldEnvID, newEnvID, oldEnvID, cohortMappingList)
						if err != nil {
							return nil, errors.WithStack(err)
						}

						if !factor && req.IsContain {
							return nil, tools.BuildServerError(ctx, "env.copy.prod.isCopy.core")
						}

					}

					//if len(newCohortList) != len(oldCohortList) || len(cohortMappingList) != len(newCohortList) || len(cohortMappingList) != len(oldCohortList) {
					//	if project.ProjectInfo.Type == 2 {
					//		return nil, tools.BuildServerError(ctx, "env.copy.prod.isCopy.cohort.fail")
					//	} else if project.ProjectInfo.Type == 3 {
					//		return nil, tools.BuildServerError(ctx, "env.copy.prod.isCopy.stage.fail")
					//	}
					//}
				}

			}
		}

		filter := bson.M{}
		update := bson.M{}
		newCohorts := make([]models.Cohort, 0)
		if project.Type == 1 {
			//基本研究直接创建环境
			filter = bson.M{"_id": projectID}
			update = bson.M{
				"$addToSet": bson.M{
					"envs": bson.M{
						"id":                  newEnvID,
						"name":                envName,
						"lockConfig":          oldEnv.LockConfig,
						"capacity":            oldEnv.Capacity,
						"reminder_thresholds": oldEnv.ReminderThresholds,
						"alert_thresholds":    oldEnv.AlertThresholds,
						"status":              1,
						"cohorts":             []models.Cohort{},
					},
				},
			}
			if oldEnvName == "PROD" {
				update = bson.M{
					"$addToSet": bson.M{
						"envs": bson.M{
							"id":                  newEnvID,
							"name":                envName,
							"lockConfig":          oldEnv.LockConfig,
							"capacity":            oldEnv.Capacity,
							"reminder_thresholds": oldEnv.ReminderThresholds,
							"alert_thresholds":    oldEnv.AlertThresholds,
							"status":              1,
							"cohorts":             []models.Cohort{},
							"is_copy":             true,
						},
					},
				}
			}
			if !newEnvExist && oldEnvName != "PROD" && req.EnvName == "PROD" {
				update = bson.M{
					"$addToSet": bson.M{
						"envs": bson.M{
							"id":                  newEnvID,
							"name":                envName,
							"lockConfig":          oldEnv.LockConfig,
							"capacity":            oldEnv.Capacity,
							"reminder_thresholds": oldEnv.ReminderThresholds,
							"alert_thresholds":    oldEnv.AlertThresholds,
							"status":              1,
							"cohorts":             []models.Cohort{},
							"is_copy":             true,
						},
					},
				}
			}
		} else {

			//目标环境存在
			if newEnvExist && oldEnvName != "PROD" {
				//确定需要复制的cohort
				oldCohort := oldEnv.Cohorts
				if len(req.CohortIDs) > 0 {
					oldCohort = slice.Filter(oldEnv.Cohorts, func(index int, item models.Cohort) bool {
						return slice.Contain(req.CohortIDs, item.ID)
					})
				}

				if req.EnvName == "PROD" {
					newEnvID = existEnvId
					existEnvp, is := slice.Find(project.Environments, func(index int, item models.Environment) bool {
						return existEnvId == item.ID
					})
					if is {
						cohortList := make([]models.Cohort, 0)
						if existEnvp.Cohorts != nil && len(existEnvp.Cohorts) > 0 {
							for _, ec := range existEnvp.Cohorts {
								nc := ec
								existCohort, c := slice.Find(oldCohort, func(index int, item models.Cohort) bool {
									return nc.Name == item.Name
								})
								if c {
									nc.CopyFrom = existCohort.ID
								}
								cohortList = append(cohortList, nc)
							}
						}
						//newCohorts = cohortList
						if sourceList != nil && len(sourceList) > 0 {
							for _, cohort := range sourceList {
								index := arrays.ContainsString(targetNameList, cohort.Name)
								if index == -1 {
									nc := models.Cohort{
										ID:              primitive.NewObjectID(),
										LastID:          primitive.ObjectID{},
										Type:            cohort.Type,
										Name:            cohort.Name,
										AlertThresholds: cohort.AlertThresholds,
										Factor:          cohort.Factor,
										ReRandomName:    cohort.ReRandomName,
										Status:          1,
										CopyFrom:        cohort.ID,
									}
									_ = insertUpdateProjectEnvCohort(ctx, sctx, projectID, 11, project.Type, oldEnv.Name, envName, cohort, nc, projectID, sourceList)
									newCohorts = append(newCohorts, nc)
								}
							}
							filter = bson.M{
								"_id":       projectID,
								"envs.name": envName,
							}
							update = bson.M{
								"$push": bson.M{
									"envs.$.cohorts": bson.M{
										"$each": newCohorts,
									},
								},
							}
						}
					}
				} else {
					for _, oc := range oldCohort {
						cohortName := oc.Name
						//目标环境如果有同名就再加-copy
						_, e := slice.Find(newEnv.Cohorts, func(index int, item models.Cohort) bool {
							return item.Name == oc.Name
						})
						if e {
							cohortName = oc.Name + "-copy"
						}
						_, ce := slice.Find(newEnv.Cohorts, func(index int, item models.Cohort) bool {
							return item.Name == cohortName
						})
						if ce {
							cohortName = cohortName + "-copy"
						}
						nc := models.Cohort{
							ID:              primitive.NewObjectID(),
							LastID:          primitive.ObjectID{},
							Type:            oc.Type,
							Name:            cohortName,
							ReRandomName:    oc.ReRandomName,
							AlertThresholds: oc.AlertThresholds,
							Factor:          oc.Factor,
							Status:          1,
							CopyFrom:        oc.ID,
						}
						_ = insertUpdateProjectEnvCohort(ctx, sctx, projectID, 11, project.Type, oldEnv.Name, envName, oc, nc, projectID, oldCohort)
						newCohorts = append(newCohorts, nc)
					}
					filter = bson.M{
						"_id":       projectID,
						"envs.name": envName,
					}
					update = bson.M{
						"$push": bson.M{
							"envs.$.cohorts": bson.M{
								"$each": newCohorts,
							},
						},
					}
				}
			} else {
				//目标环境不存在
				filter = bson.M{"_id": projectID}
				//确定需要复制的cohort
				oldCohort := oldEnv.Cohorts
				if len(req.CohortIDs) > 0 {
					oldCohort = slice.Filter(oldEnv.Cohorts, func(index int, item models.Cohort) bool {
						return slice.Contain(req.CohortIDs, item.ID)
					})
				}
				for _, oc := range oldCohort {
					cohortName := oc.Name
					//目标环境如果有同名就再加-copy
					//_, e := slice.Find(newEnv.Cohorts, func(index int, item models.Cohort) bool {
					//	return item.Name == oc.Name
					//})
					//if e {
					//	cohortName = oc.Name + "-copy"
					//}
					nc := models.Cohort{
						ID:              primitive.NewObjectID(),
						LastID:          primitive.ObjectID{},
						Type:            oc.Type,
						Name:            cohortName,
						AlertThresholds: oc.AlertThresholds,
						ReRandomName:    oc.ReRandomName,
						Factor:          oc.Factor,
						Status:          1,
						CopyFrom:        oc.ID,
					}
					if newEnvExist && oldEnvName == "PROD" {
						_ = insertUpdateProjectEnvCohort(ctx, sctx, projectID, 11, project.Type, oldEnv.Name, envName+"-copy", oc, nc, projectID, oldCohort)
					} else {
						_ = insertUpdateProjectEnvCohort(ctx, sctx, projectID, 11, project.Type, oldEnv.Name, envName, oc, nc, projectID, oldCohort)
					}

					newCohorts = append(newCohorts, nc)
				}

				if newEnvExist && oldEnvName == "PROD" {
					name := envName
					_, a := slice.Find(project.Environments, func(index int, item models.Environment) bool {
						return item.Name == envName
					})

					if a {
						name = envName + "-copy"
					}
					exists := true
					num := 1
					newName := name
					for exists {
						_, b := slice.Find(project.Environments, func(index int, item models.Environment) bool {
							return item.Name == newName
						})

						if b {
							num += 1
							newName = name + strconv.Itoa(num)
						} else {
							name = newName
							exists = false
						}
					}
					newCopyEnv = name
					if newCohorts != nil && len(newCohorts) > 0 {
						for i := range newCohorts {
							newCohorts[i].IsCopy = true
						}
					}
					update = bson.M{
						"$addToSet": bson.M{
							"envs": bson.M{
								"id":      newEnvID,
								"name":    name,
								"cohorts": newCohorts,
								"is_copy": true,
							},
						},
					}
				} else {
					newCopyEnv = envName
					update = bson.M{
						"$addToSet": bson.M{
							"envs": bson.M{
								"id":      newEnvID,
								"name":    envName,
								"cohorts": newCohorts,
							},
						},
					}
					if oldEnvName == "PROD" {
						if newCohorts != nil && len(newCohorts) > 0 {
							for i := range newCohorts {
								newCohorts[i].IsCopy = true
							}
						}
						update = bson.M{
							"$addToSet": bson.M{
								"envs": bson.M{
									"id":      newEnvID,
									"name":    envName,
									"cohorts": newCohorts,
									"is_copy": true,
								},
							},
						}
					}
				}

			}
		}

		if (!newEnvExist && req.EnvName == "PROD") ||
			(newEnvExist && req.EnvName != "PROD") ||
			(newEnvExist && req.EnvName == "PROD" && project.Type != 1) ||
			(!newEnvExist && req.EnvName != "PROD") {
			if len(filter) > 0 && len(update) > 0 {
				// 在随机项目限制只能添加两个阶段
				if project.Type == 3 {
					num := 0
					if newEnvp != nil {
						num = len(newEnvp.Cohorts)
					}
					if newCohorts != nil {
						num = num + len(newCohorts)
					}
					if num > 2 {
						return nil, tools.BuildServerError(ctx, "project_at_random_error")
					}
				}
				if _, err := collection.UpdateOne(sctx, filter, update); err != nil {
					return nil, errors.WithStack(err)
				}
			}
		}

		// 查询项目信息
		if !newEnvExist || (newEnvExist && (oldEnvName == "PROD" || (project.Type == 1 && req.EnvName != "PROD"))) {
			projectRoleId := struct {
				ID primitive.ObjectID `bson:"_id"`
			}{}
			err = tools.Database.Collection("project_role_permission").FindOne(sctx, bson.M{
				"project_id": project.ID,
				"name":       "Project-Admin",
			}, &options.FindOneOptions{
				Projection: bson.M{"_id": 1},
			}).Decode(&projectRoleId)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			envUsers := slice.Map(project.Administrators, func(index int, item primitive.ObjectID) interface{} {
				return bson.M{
					"customer_id": project.CustomerID,
					"project_id":  project.ID,
					"env_id":      newEnvID,
					"user_id":     item,
					"roles":       []primitive.ObjectID{projectRoleId.ID},
					"meta": models.Meta{
						CreatedBy: me.ID,
						CreatedAt: time.Duration(time.Now().Unix()),
					},
				}
			})
			_, err = tools.Database.Collection("user_project_environment").InsertMany(sctx, envUsers)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
		if project.ProjectInfo.Type == 1 { // 基本研究项目
			if req.IsContain {
				copyEnv := oldEnvName + "->" + newCopyEnv
				// 复制项目属性
				err := copyAttribute(ctx, sctx, project, oldEnvID, primitive.NilObjectID, newEnvID, primitive.NilObjectID, copyEnv)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				// 复制表单
				form, err := copyForm(ctx, sctx, project, oldEnvID, primitive.NilObjectID, newEnvID, primitive.NilObjectID, copyEnv)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				// 复制随机配置
				err = copyRandomDesign(ctx, sctx, project, oldEnvID, primitive.NilObjectID, newEnvID, primitive.NilObjectID, copyEnv)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				// 复制访视周期
				visitCycleInfo, err := copyVisitCycle(ctx, sctx, project, oldEnvID, primitive.NilObjectID, newEnvID, primitive.NilObjectID, copyEnv)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				// 复制研究产品配置
				drugConfigure, err := copyDrugConfig(ctx, sctx, project, oldEnvID, primitive.NilObjectID, newEnvID, primitive.NilObjectID, copyEnv, visitCycleInfo)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				//盲底-----研究产品、未编号研究产品
				if drugConfigure.Configures != nil && len(drugConfigure.Configures) > 0 {
					for _, configure := range drugConfigure.Configures {
						if configure.Values != nil && len(configure.Values) > 0 {
							for _, value := range configure.Values {
								if value.IsOther {
									//未编号研究产品
									index := arrays.ContainsString(unnumberedIpList, value.DrugName)
									if index == -1 {
										unnumberedIpList = append(unnumberedIpList, value.DrugName)
									}
								} else {
									//研究产品
									index := arrays.ContainsString(ipList, value.DrugName)
									if index == -1 {
										ipList = append(ipList, value.DrugName)
									}
								}

							}
						}
					}
				}

				// 复制研究产品配置-设置
				err = copyDrugConfigSetting(ctx, sctx, project, oldEnvID, primitive.NilObjectID, newEnvID, primitive.NilObjectID, form, drugConfigure, copyEnv)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				// 复制包装配置
				err = copyDrugPackageConfigure(ctx, sctx, project, oldEnvID, newEnvID)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				//复制编码配置
				err = copyCodeRule(ctx, sctx, project, oldEnvID, primitive.NilObjectID, newEnvID, primitive.NilObjectID, copyEnv)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				if (newEnvExist && req.EnvName != "PROD") || (!newEnvExist && req.EnvName == "PROD") || (!newEnvExist && req.EnvName != "PROD") {
					//复制通知配置
					err = copyNoticeConfig(ctx, sctx, project, oldEnvID, newEnvID, copyEnv, oldEnvName, newCopyEnv)
					if err != nil {
						return nil, errors.WithStack(err)
					}
				}
			} else {
				err = initEnvCohort(sctx, project, project.ID, newEnvID, models.Cohort{}, err)
				if err != nil {
					return nil, err
				}
			}

		} else {
			if req.IsContain {
				copyEnv := oldEnvName + "->" + newCopyEnv
				if (newEnvExist && req.EnvName != "PROD") || (!newEnvExist && req.EnvName == "PROD") || (!newEnvExist && req.EnvName != "PROD") {
					//复制通知配置
					err = copyNoticeConfig(ctx, sctx, project, oldEnvID, newEnvID, copyEnv, oldEnvName, newCopyEnv)
					if err != nil {
						return nil, errors.WithStack(err)
					}
				}
				for _, c := range newCohorts {
					// 复制项目属性
					err := copyAttribute(ctx, sctx, project, oldEnvID, c.CopyFrom, newEnvID, c.ID, copyEnv)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					// 复制表单
					form, err := copyForm(ctx, sctx, project, oldEnvID, c.CopyFrom, newEnvID, c.ID, copyEnv)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					// 复制随机配置
					err = copyRandomDesign(ctx, sctx, project, oldEnvID, c.CopyFrom, newEnvID, c.ID, copyEnv)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					// 复制访视周期
					visitCycleInfo, err := copyVisitCycle(ctx, sctx, project, oldEnvID, c.CopyFrom, newEnvID, c.ID, copyEnv)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					// 复制研究产品配置
					drugConfigure, err := copyDrugConfig(ctx, sctx, project, oldEnvID, c.CopyFrom, newEnvID, c.ID, copyEnv, visitCycleInfo)
					if err != nil {
						return nil, errors.WithStack(err)
					}

					//盲底-----研究产品、未编号研究产品
					if drugConfigure.Configures != nil && len(drugConfigure.Configures) > 0 {
						for _, configure := range drugConfigure.Configures {
							if configure.Values != nil && len(configure.Values) > 0 {
								for _, value := range configure.Values {
									if value.IsOther {
										//未编号研究产品
										index := arrays.ContainsString(unnumberedIpList, value.DrugName)
										if index == -1 {
											unnumberedIpList = append(unnumberedIpList, value.DrugName)
										}
									} else {
										//研究产品
										index := arrays.ContainsString(ipList, value.DrugName)
										if index == -1 {
											ipList = append(ipList, value.DrugName)
										}
									}

								}
							}
						}
					}

					// 复制包装配置
					err = copyDrugPackageConfigure(ctx, sctx, project, oldEnvID, newEnvID)
					if err != nil {
						return nil, errors.WithStack(err)
					}

					// 复制研究产品配置-设置
					err = copyDrugConfigSetting(ctx, sctx, project, oldEnvID, c.CopyFrom, newEnvID, c.ID, form, drugConfigure, copyEnv)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					//复制编码配置
					err = copyCodeRule(ctx, sctx, project, oldEnvID, c.CopyFrom, newEnvID, c.ID, copyEnv)
					if err != nil {
						return nil, errors.WithStack(err)
					}
				}
			} else {
				for _, c := range newCohorts {
					err = initEnvCohort(sctx, project, project.ID, newEnvID, c, err)
					if err != nil {
						return nil, err
					}
				}
			}
		}

		//插入日志
		oldEnvName := ""
		for _, env := range project.Environments {
			if env.ID == oldEnvID {
				oldEnvName = env.Name
			}
		}
		err = insertCopyProjectEnvLog(ctx, sctx, 6, projectID, oldEnvName, envName, projectID)
		if err != nil {
			return nil, err
		}
		return nil, nil
	}

	err = tools.Transaction(callback)
	if err != nil {
		return err
	}

	// PROD数据复制出来场景要求--库房管理、中心管理、模拟随机、供应计划
	if req.IsContain && oldEnvName == "PROD" {
		copyEnv := oldEnvName + "->" + newCopyEnv
		//复制库房管理-----project_storehouse
		projectStorehouseList, err := copyStorehouse(ctx, nil, project, oldEnvID, newEnvID, copyEnv)
		if err != nil {
			return errors.WithStack(err)
		}

		//复制中心管理-----project_site
		projectSiteList, err := copySite(ctx, nil, project, oldEnvID, newEnvID, projectStorehouseList)
		if err != nil {
			return errors.WithStack(err)
		}

		//复制供应计划-----supply_plan\supply_plan_medicine
		supplyPlanList, err := copyPlan(ctx, nil, project, oldEnvID, newEnvID, projectSiteList, copyEnv)
		if err != nil {
			return errors.WithStack(err)
		}

		//中心添加对应的供应计划并保存
		if projectSiteList != nil && len(projectSiteList) > 0 {
			for _, site := range projectSiteList {
				if site.SupplyPlanID != primitive.NilObjectID {
					var oldSupplyPlan models.SupplyPlan
					err = tools.Database.Collection("supply_plan").FindOne(nil, bson.M{"_id": site.SupplyPlanID}).Decode(&oldSupplyPlan)
					if err != nil {
						return errors.WithStack(err)
					}
					if supplyPlanList != nil && len(supplyPlanList) > 0 {
						for _, plan := range supplyPlanList {
							if oldSupplyPlan.SupplyPlanInfo.Name == plan.SupplyPlanInfo.Name {
								site.SupplyPlanID = plan.ID
								_, err := tools.Database.Collection("project_site").InsertOne(ctx, site)
								if err != nil {
									return errors.WithStack(err)
								}

								err = insertProjectSiteLog(ctx, nil, newEnvID, 10, models.ProjectSite{}, site, copyEnv)
								if err != nil {
									return errors.WithStack(err)
								}
							}
						}
					} else {
						_, err := tools.Database.Collection("project_site").InsertOne(ctx, site)
						if err != nil {
							return errors.WithStack(err)
						}

						err = insertProjectSiteLog(ctx, nil, newEnvID, 10, models.ProjectSite{}, site, copyEnv)
						if err != nil {
							return errors.WithStack(err)
						}
					}
				} else {
					_, err := tools.Database.Collection("project_site").InsertOne(ctx, site)
					if err != nil {
						return errors.WithStack(err)
					}

					err = insertProjectSiteLog(ctx, nil, newEnvID, 10, models.ProjectSite{}, site, copyEnv)
					if err != nil {
						return errors.WithStack(err)
					}
				}
			}
		}

		//随机列表(盲底)、研究产品列表(盲底)、未编号研究产品(盲底)
		if isNewEnvExist && oldEnvName == "PROD" && req.EnvName != "PROD" {
			copyEnv = req.EnvName + "->" + newCopyEnv

			//复制研究产品列表(盲底)-----medicine
			err = copyMedicine(ctx, nil, project, existEnvId, newEnvID, projectStorehouseList, ipList, copyEnv)
			if err != nil {
				return errors.WithStack(err)
			}

			//复制未编号研究产品列表(盲底)-----medicine_others
			err = copyUnnumberedMedicine(ctx, nil, project, existEnvId, newEnvID, projectStorehouseList, unnumberedIpList, copyEnv)
			if err != nil {
				return errors.WithStack(err)
			}

			//复制条形码列表(盲底)-----barcode_group
			err := copyBarCode(ctx, nil, project.ID, existEnvId, newEnvID, existEnvId, projectStorehouseList, copyEnv)
			if err != nil {
				return errors.WithStack(err)
			}

			//复制随机列表(盲底)-----random_list
			err = copyRandomList(ctx, nil, project.ID, oldEnvID, newEnvID, existEnvId, projectStorehouseList, unnumberedIpList, projectSiteList, copyEnv)
			if err != nil {
				return errors.WithStack(err)
			}

			//复制模拟随机-----simulate_random
			err = copySimulateRandom(ctx, nil, projectID, oldEnvID, newEnvID, existEnvId, copyEnv)
			if err != nil {
				return errors.WithStack(err)
			}

		}
	}

	return nil
}

// 复制模拟随机-----simulate_random
func copySimulateRandom(ctx *gin.Context, sctx mongo.SessionContext, projectID primitive.ObjectID, oldEnvID primitive.ObjectID, newEnvID primitive.ObjectID, existEnvId primitive.ObjectID, copyEnv string) error {

	collection := tools.Database.Collection("project")
	project := models.Project{}
	// 先检查环境是否重复 只检查基本研究
	err := collection.FindOne(ctx, bson.M{"_id": projectID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}

	filter := bson.M{
		"customer_id": project.CustomerID,
		"project_id":  project.ID,
		"env_id":      oldEnvID,
	}

	cohortMappingList := make([]models.CohortMapping, 0)
	if project.Type != 1 {
		oldCohortIdList := make([]primitive.ObjectID, 0)
		oldEnvCohortIdList := make([]models.Cohort, 0)
		newEnvCohortList := make([]models.Cohort, 0)
		if project.Environments != nil && len(project.Environments) > 0 {
			for _, environment := range project.Environments {
				if environment.ID == oldEnvID {
					if environment.Cohorts != nil && len(environment.Cohorts) > 0 {
						for _, cohort := range environment.Cohorts {
							oldCohortIdList = append(oldCohortIdList, cohort.ID)
						}
					}
					oldEnvCohortIdList = environment.Cohorts
				}
				if environment.ID == newEnvID {
					newEnvCohortList = environment.Cohorts
				}
			}
		}
		filter["cohort_id"] = bson.M{"$in": oldCohortIdList}

		if newEnvCohortList != nil && len(newEnvCohortList) > 0 {
			for _, newCohort := range newEnvCohortList {
				if oldEnvCohortIdList != nil && len(oldEnvCohortIdList) > 0 {
					for _, oldCohort := range oldEnvCohortIdList {
						if newCohort.Name == oldCohort.Name {
							if project.Type == 3 {
								//再随机项目--
								if (newCohort.LastID != primitive.NilObjectID && oldCohort.LastID != primitive.NilObjectID) ||
									(newCohort.LastID == primitive.NilObjectID && oldCohort.LastID == primitive.NilObjectID) {
									var cohortMapping models.CohortMapping
									cohortMapping.OldCohortID = oldCohort.ID
									cohortMapping.NewCohortID = newCohort.ID
									cohortMappingList = append(cohortMappingList, cohortMapping)
								}
							} else {
								//群组项目--
								var cohortMapping models.CohortMapping
								cohortMapping.OldCohortID = oldCohort.ID
								cohortMapping.NewCohortID = newCohort.ID
								cohortMappingList = append(cohortMappingList, cohortMapping)
							}
						}
					}
				}
			}
		}
	}

	oldEnvSimulateRandomList := make([]models.SimulateRandom, 0)
	cursor, err := tools.Database.Collection("simulate_random").Find(nil, filter)
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &oldEnvSimulateRandomList)
	if err != nil {
		return errors.WithStack(err)
	}

	if oldEnvSimulateRandomList != nil && len(oldEnvSimulateRandomList) > 0 {
		for _, simulateRandom := range oldEnvSimulateRandomList {
			newSimulateRandom := simulateRandom
			newSimulateRandom.ID = primitive.NewObjectID()
			newSimulateRandom.EnvironmentID = newEnvID
			cohortId := newSimulateRandom.CohortID
			newSimulateRandom.CohortID = primitive.NilObjectID
			if cohortId != primitive.NilObjectID {
				if cohortMappingList != nil && len(cohortMappingList) > 0 {
					for _, mapping := range cohortMappingList {
						if mapping.OldCohortID == cohortId {
							newSimulateRandom.CohortID = mapping.NewCohortID
						}
					}
				}
			}
			if project.ProjectInfo.Type != 1 && newSimulateRandom.CohortID == primitive.NilObjectID {
				continue
			}
			newRandomIdList := make([]string, 0)
			newRandomNameList := make([]string, 0)
			if newSimulateRandom.SimulateRandomInfo.RandomListIds != nil && len(newSimulateRandom.SimulateRandomInfo.RandomListIds) > 0 {
				for _, id := range newSimulateRandom.SimulateRandomInfo.RandomListIds {
					OID, err := primitive.ObjectIDFromHex(id)
					if err != nil {
						return errors.WithStack(err)
					}

					var oldRandomList models.RandomList
					err = tools.Database.Collection("random_list").FindOne(sctx, bson.M{"_id": OID}).Decode(&oldRandomList)
					if err != nil && err != mongo.ErrNoDocuments {
						return errors.WithStack(err)
					}

					kk := bson.M{
						"customer_id": project.CustomerID,
						"project_id":  project.ID,
						"env_id":      newEnvID,
						"name":        oldRandomList.Name,
					}
					newRandomList := models.RandomList{}
					err = tools.Database.Collection("random_list").FindOne(sctx, kk).Decode(&newRandomList)
					if err != nil && err != mongo.ErrNoDocuments {
						return errors.WithStack(err)
					}

					if newRandomList.ID != primitive.NilObjectID {
						newRandomIdList = append(newRandomIdList, newRandomList.ID.Hex())
						newRandomNameList = append(newRandomNameList, newRandomList.Name)
					}
				}
			}

			if len(newRandomIdList) == len(newSimulateRandom.SimulateRandomInfo.RandomListIds) && len(newRandomIdList) > 0 {
				newSimulateRandom.SimulateRandomInfo.RandomListIds = newRandomIdList
				if _, err := tools.Database.Collection("simulate_random").InsertOne(sctx, newSimulateRandom); err != nil {
					return errors.WithStack(err)
				}
				historyOID := newSimulateRandom.EnvironmentID
				if !newSimulateRandom.CohortID.IsZero() {
					historyOID = newSimulateRandom.CohortID
				}
				var newRandomListName strings.Builder
				for _, rl := range newRandomNameList {
					newRandomListName.WriteString(" [")
					newRandomListName.WriteString(rl)
					newRandomListName.WriteString("]")
				}
				var randomDesign models.RandomDesign
				designFilter := bson.M{"env_id": simulateRandom.EnvironmentID}
				if !simulateRandom.CohortID.IsZero() {
					designFilter["cohort_id"] = simulateRandom.CohortID
				}
				err = tools.Database.Collection("random_design").FindOne(sctx, designFilter).Decode(&randomDesign)
				if err != nil {
					return errors.WithStack(err)
				}
				err = insertSimulateRandomFormLog(ctx, sctx, historyOID, 10, models.SimulateRandomInfo{}, newSimulateRandom.SimulateRandomInfo, randomDesign, newSimulateRandom.ID, "", newRandomListName.String(), copyEnv)
				if err != nil {
					return errors.WithStack(err)
				}

			}

		}
	}

	return nil
}

// 复制研究产品列表(盲底)-----medicine
func copyMedicine(ctx *gin.Context, sctx mongo.SessionContext, project models.Project, oldEnvID primitive.ObjectID, newEnvID primitive.ObjectID, projectStorehouseList []models.ProjectStorehouse, ipList []string, copyEnv string) error {
	filter := bson.M{
		"customer_id": project.CustomerID,
		"project_id":  project.ID,
		"env_id":      oldEnvID,
	}
	medicineList := make([]models.Medicine, 0)
	cursor, err := tools.Database.Collection("medicine").Find(nil, filter)
	if err != nil {
		return errors.WithStack(err)
	}
	if err := cursor.All(nil, &medicineList); err != nil {
		return errors.WithStack(err)
	}

	OID := newEnvID

	medicineOrderList := make([]models.MedicineOrder, 0)
	cus, err := tools.Database.Collection("medicine_order").Find(nil, filter)
	if err != nil {
		return errors.WithStack(err)

	}
	if err := cus.All(nil, &medicineOrderList); err != nil {
		return errors.WithStack(err)
	}

	filter["deleted"] = 2
	oldProjectStorehouseList := make([]models.ProjectStorehouse, 0)
	cu, err := tools.Database.Collection("project_storehouse").Find(nil, filter)
	if err != nil {
		return errors.WithStack(err)
	}
	if err := cu.All(nil, &oldProjectStorehouseList); err != nil {
		return errors.WithStack(err)
	}

	idMappingList := make([]models.IdMapping, 0)
	if oldProjectStorehouseList != nil && len(oldProjectStorehouseList) > 0 {
		for _, oldStorehouse := range oldProjectStorehouseList {
			if projectStorehouseList != nil && len(projectStorehouseList) > 0 {
				for _, newStorehouse := range projectStorehouseList {
					if oldStorehouse.StorehouseID == newStorehouse.StorehouseID {
						var idMapping models.IdMapping
						idMapping.OldID = oldStorehouse.ID
						idMapping.NewID = newStorehouse.ID
						idMappingList = append(idMappingList, idMapping)
					}
				}
			}
		}
	}

	newMedicineList := make([]models.Medicine, 0)
	if medicineList != nil && len(medicineList) > 0 {
		for _, medicine := range medicineList {
			newMedicine := medicine
			newMedicine.ID = primitive.NewObjectID()
			newMedicine.EnvironmentID = newEnvID
			newMedicine.OrderID = primitive.NilObjectID
			if medicineOrderList != nil && len(medicineOrderList) > 0 {
				medicineOrder, err := containsObjectID(medicineOrderList, medicine.ID)
				if err != nil {
					return errors.WithStack(err)
				}
				if medicineOrder.ID != primitive.NilObjectID {
					newMedicine.StorehouseID = medicineOrder.SendID
					newMedicine.SiteID = primitive.NilObjectID
					newMedicine.Status = 1
				}
			}
			if idMappingList != nil && len(idMappingList) > 0 {
				for _, idMapping := range idMappingList {
					if newMedicine.StorehouseID == idMapping.OldID {
						if ipList != nil && len(ipList) > 0 {
							index := arrays.ContainsString(ipList, newMedicine.Name)
							if index != -1 {
								newMedicine.StorehouseID = idMapping.NewID
								if newMedicine.Status != 0 {
									newMedicine.Status = 1
								}
								newMedicineList = append(newMedicineList, newMedicine)
							}
						}
					}
				}
			}
		}
	}

	if newMedicineList != nil && len(newMedicineList) > 0 {
		var docs []interface{}
		for _, medicine := range newMedicineList {
			docs = append(docs, medicine)
		}
		if _, err := tools.Database.Collection("medicine").InsertMany(nil, docs); err != nil {
			return errors.WithStack(err)
		}

		// 创建两个map，用于快速查找
		newMap := make(map[string]models.CopyMedicine)
		for _, medicine := range newMedicineList {
			var newCopyMedicine models.CopyMedicine
			newCopyMedicine.Name = medicine.Name
			newCopyMedicine.StorehouseID = medicine.StorehouseID
			newCopyMedicine.ExpirationDate = medicine.ExpirationDate
			newCopyMedicine.BatchNumber = medicine.BatchNumber
			newCopyMedicine.Spec = medicine.Spec

			// 判断 map 中是否包含特定的 key
			key := medicine.Name + medicine.StorehouseID.Hex() + newCopyMedicine.ExpirationDate +
				newCopyMedicine.BatchNumber + newCopyMedicine.Spec
			if val, ok := newMap[key]; ok {
				newCopyMedicine.Count = val.Count + 1
				newMap[key] = newCopyMedicine
			} else {
				newCopyMedicine.Count = 1
				newMap[key] = newCopyMedicine
			}
		}

		copyMedicineList := make([]models.CopyMedicine, 0)
		// 遍历 map
		for _, value := range newMap {
			copyMedicineList = append(copyMedicineList, value)
		}

		err := InsertMedicineLog(ctx, nil, OID, 10, nil, copyMedicineList, copyEnv)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	//OID := newEnvID
	//u, _ := ctx.Get("user")
	//user := u.(models.User)

	return nil
}

// 复制随机列表(盲底)-----random_list
func copyRandomList(ctx *gin.Context, sctx mongo.SessionContext, projectID primitive.ObjectID, oldEnvID primitive.ObjectID, newEnvID primitive.ObjectID, existEnvId primitive.ObjectID, projectStorehouseList []models.ProjectStorehouse, unnumberedIpList []string, projectSiteList []models.ProjectSite, copyEnv string) error {

	collection := tools.Database.Collection("project")
	project := models.Project{}
	// 先检查环境是否重复 只检查基本研究
	err := collection.FindOne(ctx, bson.M{"_id": projectID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}

	filter := bson.M{
		"customer_id": project.CustomerID,
		"project_id":  project.ID,
		"env_id":      existEnvId,
		"status":      1,
	}

	cohortMappingList := make([]models.CohortMapping, 0)

	if project.Type != 1 {
		oldEnvCohortIdList := make([]primitive.ObjectID, 0)
		existEnvCohortList := make([]models.Cohort, 0)
		newEnvCohortList := make([]models.Cohort, 0)
		if project.Environments != nil && len(project.Environments) > 0 {
			for _, environment := range project.Environments {
				if environment.ID == existEnvId {
					if environment.Cohorts != nil && len(environment.Cohorts) > 0 {
						for _, cohort := range environment.Cohorts {
							oldEnvCohortIdList = append(oldEnvCohortIdList, cohort.ID)
						}
					}
					existEnvCohortList = environment.Cohorts
				}
				if environment.ID == newEnvID {
					newEnvCohortList = environment.Cohorts
				}
			}
		}
		filter["cohort_id"] = bson.M{"$in": oldEnvCohortIdList}

		if newEnvCohortList != nil && len(newEnvCohortList) > 0 {
			for _, newCohort := range newEnvCohortList {
				if existEnvCohortList != nil && len(existEnvCohortList) > 0 {
					for _, existCohort := range existEnvCohortList {
						if newCohort.Name == existCohort.Name {
							if project.Type == 3 {
								//再随机项目--
								if (newCohort.LastID != primitive.NilObjectID && existCohort.LastID != primitive.NilObjectID) ||
									(newCohort.LastID == primitive.NilObjectID && existCohort.LastID == primitive.NilObjectID) {
									var cohortMapping models.CohortMapping
									cohortMapping.OldCohortID = existCohort.ID
									cohortMapping.NewCohortID = newCohort.ID
									cohortMappingList = append(cohortMappingList, cohortMapping)
								}
							} else {
								//群组项目--
								var cohortMapping models.CohortMapping
								cohortMapping.OldCohortID = existCohort.ID
								cohortMapping.NewCohortID = newCohort.ID
								cohortMappingList = append(cohortMappingList, cohortMapping)
							}
						}
					}
				}
			}
		}
	}

	randomList := make([]models.RandomList, 0)
	cursor, err := tools.Database.Collection("random_list").Find(ctx, filter)
	if err != nil {
		return errors.WithStack(err)
	}
	if err := cursor.All(ctx, &randomList); err != nil {
		return errors.WithStack(err)
	}

	//OID := newEnvID
	u, _ := ctx.Get("user")
	user := u.(models.User)

	existEnvCondition := bson.M{"customer_id": project.CustomerID, "project_id": project.ID, "env_id": existEnvId}
	newEnvCondition := bson.M{"customer_id": project.CustomerID, "project_id": project.ID, "env_id": newEnvID}

	if randomList != nil && len(randomList) > 0 {
		for _, rand := range randomList {
			newRandom := rand
			newRandom.ID = primitive.NewObjectID()
			newRandom.EnvironmentID = newEnvID
			cohortId := newRandom.CohortID
			newRandom.CohortID = primitive.NilObjectID
			if cohortId != primitive.NilObjectID {
				if cohortMappingList != nil && len(cohortMappingList) > 0 {
					for _, mapping := range cohortMappingList {
						if mapping.OldCohortID == cohortId {
							newRandom.CohortID = mapping.NewCohortID
							existEnvCondition["cohort_id"] = mapping.OldCohortID
							newEnvCondition["cohort_id"] = mapping.NewCohortID
						}
					}
				}
			}

			if project.ProjectInfo.Type != 1 && newRandom.CohortID == primitive.NilObjectID {
				continue
			}

			equal, err := transformationEqual(ctx, existEnvCondition, newEnvCondition, newEnvID, existEnvId)
			if err != nil {
				return errors.WithStack(err)
			}
			siteIdList := make([]primitive.ObjectID, 0)
			if equal {
				if newRandom.SiteIds != nil && len(newRandom.SiteIds) > 0 {
					for _, id := range newRandom.SiteIds {
						var oldProjectSite models.ProjectSite
						err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": id}).Decode(&oldProjectSite)
						if err != nil {
							return errors.WithStack(err)
						}
						if projectSiteList != nil && len(projectSiteList) > 0 {
							for _, projectSite := range projectSiteList {
								if projectSite.Number == oldProjectSite.Number && projectSite.DmpID == oldProjectSite.DmpID {
									siteIdList = append(siteIdList, projectSite.ID)
								}
							}
						}
					}
					newRandom.SiteIds = siteIdList
				}

				var existEnvRandomDesign models.RandomDesign
				if err := tools.Database.Collection("random_design").FindOne(ctx, existEnvCondition).Decode(&existEnvRandomDesign); err != nil {
					return errors.WithStack(err)
				}

				var newEnvRandomDesign models.RandomDesign
				if err := tools.Database.Collection("random_design").FindOne(ctx, newEnvCondition).Decode(&newEnvRandomDesign); err != nil {
					return errors.WithStack(err)
				}

				grpList := make([]models.RandomListGroup, 0)
				if newRandom.Design.Groups != nil && len(newRandom.Design.Groups) > 0 {
					for _, group := range newRandom.Design.Groups {
						grp := group
						if newEnvRandomDesign.Info.Groups != nil && len(newEnvRandomDesign.Info.Groups) > 0 {
							for _, gp := range newEnvRandomDesign.Info.Groups {
								if grp.Code == gp.Code {
									grp.ID = gp.ID
								}
							}
						}
						grpList = append(grpList, grp)
					}
				}
				newRandom.Design.Groups = grpList

				factorList := make([]models.RandomFactor, 0)
				if newRandom.Design.Factors != nil && len(newRandom.Design.Factors) > 0 {
					for _, factor := range newRandom.Design.Factors {
						fac := factor
						if newEnvRandomDesign.Info.Factors != nil && len(newEnvRandomDesign.Info.Factors) > 0 {
							for _, fr := range newEnvRandomDesign.Info.Factors {
								if fac.Number == fr.Number {
									fac.ID = fr.ID
								}
							}
						}
						factorList = append(factorList, fac)
					}
				}
				newRandom.Design.Factors = factorList

				factorsCombinationList := make([]models.FactorsCombination, 0)
				if newRandom.Design.Combination != nil && len(newRandom.Design.Combination) > 0 {
					for _, combination := range newRandom.Design.Combination {
						comb := combination
						comb.ID = primitive.NewObjectID()
						factorsCombinationList = append(factorsCombinationList, comb)
					}
				}
				newRandom.Design.Combination = factorsCombinationList
				newRandom.Meta = models.Meta{
					CreatedAt: time.Duration(time.Now().Unix()),
					CreatedBy: user.ID,
				}

				//randomNumberArray
				randomNumbers, _, blockNumber, err := randomNumberGenerate(ctx, newRandom, existEnvRandomDesign.Info.BlockNumber)
				if err != nil {
					return errors.WithStack(err)
				}

				//添加randomList表
				if _, err := tools.Database.Collection("random_list").InsertOne(ctx, newRandom); err != nil {
					return errors.WithStack(err)
				}

				randomNumberList := make([]models.RandomNumber, 0)
				randomNumberCursor, err := tools.Database.Collection("random_number").Find(nil, bson.M{"random_list_id": rand.ID})
				if err != nil {
					return errors.WithStack(err)
				}
				if err = randomNumberCursor.All(nil, &randomNumberList); err != nil {
					return errors.WithStack(err)
				}
				if randomNumberList != nil && len(randomNumberList) > 0 {
					if randomNumbers != nil && len(randomNumbers) > 0 {
						rnList := make([]interface{}, 0)
						for _, number := range randomNumbers {
							randomNumber := number
							for _, rn := range randomNumberList {
								// 使用类型断言将 interface{} 类型转换为 RandomNumber 类型
								if n, ok := randomNumber.(models.RandomNumber); ok {
									if n.Number == rn.Number {
										// 转换成功，现在 number 变量包含了 RandomNumber 类型的值
										n.Factors = rn.Factors

										if rn.ProjectSiteID != primitive.NilObjectID {
											var projectSite models.ProjectSite
											err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": rn.ProjectSiteID}).Decode(&projectSite)
											if err != nil && err != mongo.ErrNoDocuments {
												return errors.WithStack(err)
											}

											var newProjectSite models.ProjectSite
											err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"env_id": newEnvID, "dmp_id": projectSite.DmpID, "number": projectSite.Number}).Decode(&newProjectSite)
											if err != nil && err != mongo.ErrNoDocuments {
												return errors.WithStack(err)
											}
											n.ProjectSiteID = newProjectSite.ID
										}

										if rn.RegionID != primitive.NilObjectID {
											var region models.Region
											err = tools.Database.Collection("region").FindOne(nil, bson.M{"_id": rn.RegionID}).Decode(&region)
											if err != nil && err != mongo.ErrNoDocuments {
												return errors.WithStack(err)
											}

											regionCondition := bson.M{"env_id": newEnvID}
											if newRandom.CohortID != primitive.NilObjectID {
												regionCondition["cohort_id"] = newRandom.CohortID
											}
											var newRegion models.Region
											err = tools.Database.Collection("region").FindOne(nil, regionCondition).Decode(&newRegion)
											if err != nil && err != mongo.ErrNoDocuments {
												return errors.WithStack(err)
											}
											n.RegionID = newRegion.ID
										}
										n.Country = rn.Country
										randomNumber = interface{}(n)
									}
								}
							}
							rnList = append(rnList, randomNumber)
						}
						randomNumbers = rnList
					}
				}

				// 批量添加随机号
				if _, err := tools.Database.Collection("random_number").InsertMany(ctx, randomNumbers); err != nil {
					return errors.WithStack(err)
				}

				// 区组随机每次生成随机号更新区组号
				if newEnvRandomDesign.Info.Type == 1 {
					upsert := true
					update := bson.M{}
					opts := &options.UpdateOptions{
						Upsert: &upsert,
					}
					update = bson.M{"$set": bson.M{"info.block_number": blockNumber}}
					res, err := tools.Database.Collection("random_design").UpdateOne(sctx, newEnvCondition, update, opts)
					if err != nil || (res.ModifiedCount != 1 && res.UpsertedCount != 1) {
						return tools.BuildServerError(ctx, "common.operation.fail")
					}
				}

				randomListConfigPt := models.RandomListConfigPt{}
				randomListConfigPt.CustomerID = newRandom.CustomerID.Hex()
				randomListConfigPt.ProjectID = newRandom.ProjectID.Hex()
				randomListConfigPt.EnvID = newRandom.EnvironmentID.Hex()
				randomListConfigPt.LastGroup = newRandom.LastGroup
				randomListConfigPt.Name = newRandom.Name

				siteListId := make([]string, 0)
				if siteIdList != nil && len(siteIdList) > 0 {
					for _, sId := range siteIdList {
						siteListId = append(siteListId, sId.Hex())
					}
				}
				randomListConfigPt.SiteIds = siteListId
				randomListConfigPt.InitialValue = newRandom.Config.InitialValue
				randomListConfigPt.EndValue = newRandom.Config.EndValue
				randomListConfigPt.Groups = newRandom.Design.Groups
				randomListConfigPt.RandomNumberRule = newRandom.Config.Rule
				randomListConfigPt.Blocks = newRandom.Config.Blocks
				randomListConfigPt.BlocksRule = newRandom.Config.BlocksRule
				randomListConfigPt.Factors = newRandom.Design.Factors
				randomListConfigPt.Ratio = newRandom.Design.Ratio
				randomListConfigPt.Total = newRandom.Config.Total
				randomListConfigPt.NumberLength = newRandom.Config.NumberLength
				randomListConfigPt.Probability = newRandom.Config.Probability
				randomListConfigPt.Seed = newRandom.Config.Seed
				randomListConfigPt.Prefix = newRandom.Config.Prefix
				randomListConfigPt.SiteAllocation = newRandom.Config.SiteAllocation

				historyOID := newEnvID
				condition := bson.M{
					"customer_id": project.CustomerID,
					"project_id":  project.ID,
					"env_id":      newEnvID,
				}
				if newRandom.CohortID != primitive.NilObjectID {
					condition["cohort_id"] = newRandom.CohortID
					historyOID = newRandom.CohortID
					randomListConfigPt.CohortID = newRandom.CohortID.Hex()
				}
				var attribute models.Attribute
				if err := tools.Database.Collection("attribute").FindOne(nil, condition).Decode(&attribute); err != nil {
					return errors.WithStack(err)
				}
				var randomDesign models.RandomDesign
				if err := tools.Database.Collection("random_design").FindOne(nil, condition).Decode(&randomDesign); err != nil {
					return errors.WithStack(err)
				}

				// 查询中心
				var projectSiteStr strings.Builder
				if siteIdList != nil && len(siteIdList) > 0 {
					projectSites := make([]models.ProjectSite, 0)
					projectSiteCursor, err := tools.Database.Collection("project_site").Find(sctx, bson.M{"_id": bson.M{"$in": siteIdList}})
					if err != nil {
						return errors.WithStack(err)
					}
					err = projectSiteCursor.All(nil, &projectSites)
					if err != nil {
						return errors.WithStack(err)
					}
					for _, site := range projectSites {
						projectSiteStr.WriteString(" [")
						siteName := ""
						if site.ShortName != "" {
							siteName = site.ShortName
						} else {
							siteName = site.Name
						}
						projectSiteStr.WriteString(siteName)
						projectSiteStr.WriteString("]")
					}
				} else {
					projectSiteStr.WriteString("All")
				}

				// 项目日志
				err = insertGenerateRandomListLog(ctx, sctx, attribute, randomDesign, historyOID, 10, randomListConfigPt, historyOID, projectSiteStr.String(), copyEnv)
				if err != nil {
					return errors.WithStack(err)
				}

			}

		}
	}

	return nil
}

// 判断随机类型、治疗组别、分层因素是否一致
func judgeTypeGroupFactor(ctx *gin.Context, sctx mongo.SessionContext, project models.Project, oldEnvID primitive.ObjectID, newEnvID primitive.ObjectID, existEnvId primitive.ObjectID, cohortMappingList []models.CohortMapping) (bool, error) {

	existEnvCondition := bson.M{"customer_id": project.CustomerID, "project_id": project.ID, "env_id": existEnvId}
	newEnvCondition := bson.M{"customer_id": project.CustomerID, "project_id": project.ID, "env_id": newEnvID}

	if cohortMappingList != nil && len(cohortMappingList) > 0 {
		result := false
		for _, mapping := range cohortMappingList {
			existEnvCondition["cohort_id"] = mapping.OldCohortID
			newEnvCondition["cohort_id"] = mapping.NewCohortID
			equal, err := transformationCoreEqual(ctx, existEnvCondition, newEnvCondition, newEnvID, existEnvId)
			if err != nil {
				return false, errors.WithStack(err)
			}
			if !equal {
				return result, nil
			} else {
				result = equal
			}
		}
		return result, nil
	} else {
		//TODO 判断PROD的订单、受试者中的研究产品、访视、组别、分层、表单再复制的环境中是否存在
		equal, err := transformationCoreEqual(ctx, existEnvCondition, newEnvCondition, newEnvID, existEnvId)
		if err != nil {
			return false, errors.WithStack(err)
		}
		return equal, nil
	}
}

func transformationCoreEqual(ctx *gin.Context, existEnvCondition bson.M, newEnvCondition bson.M, newEnvID primitive.ObjectID, existEnvId primitive.ObjectID) (bool, error) {

	//订单中的研究产品、未编号研究产品
	medicineNameList, otherMedicineNameList, err := findOrderMedicine(ctx, nil, newEnvID)
	if err != nil {
		return false, errors.WithStack(err)
	}

	// 查询旧的访视管理
	visitCycle := models.VisitCycle{}
	err = tools.Database.Collection("visit_cycle").FindOne(ctx, existEnvCondition).Decode(&visitCycle)
	if err != nil && err != mongo.ErrNoDocuments {
		return false, errors.WithStack(err)
	}

	// 查询新的访视管理
	newVisitCycle := models.VisitCycle{}
	err = tools.Database.Collection("visit_cycle").FindOne(ctx, newEnvCondition).Decode(&newVisitCycle)
	if err != nil && err != mongo.ErrNoDocuments {
		return false, errors.WithStack(err)
	}

	// 查询旧的研究产品配置
	drugConfigure := models.DrugConfigure{}
	err = tools.Database.Collection("drug_configure").FindOne(ctx, existEnvCondition).Decode(&drugConfigure)
	if err != nil && err != mongo.ErrNoDocuments {
		return false, errors.WithStack(err)
	}

	// 查询新的研究产品配置
	newDrugConfigure := models.DrugConfigure{}
	err = tools.Database.Collection("drug_configure").FindOne(ctx, newEnvCondition).Decode(&newDrugConfigure)
	if err != nil && err != mongo.ErrNoDocuments {
		return false, errors.WithStack(err)
	}

	nameList := make([]string, 0)
	otherNameList := make([]string, 0)
	if drugConfigure.Configures != nil && len(drugConfigure.Configures) > 0 {
		for _, configure := range drugConfigure.Configures {
			if configure.Values != nil && len(configure.Values) > 0 {
				for _, value := range configure.Values {
					if value.IsOther {
						//未编号研究产品
						index := arrays.ContainsString(otherNameList, value.DrugName)
						if index == -1 {
							//-1说明不存在
							otherNameList = append(otherNameList, value.DrugName)
						}
					} else {
						//研究产品
						index := arrays.ContainsString(nameList, value.DrugName)
						if index == -1 {
							//-1说明不存在
							nameList = append(nameList, value.DrugName)
						}
					}
				}
			}
		}
	}

	var existEnvAttribute models.Attribute
	if err := tools.Database.Collection("attribute").FindOne(nil, existEnvCondition).Decode(&existEnvAttribute); err != nil && err != mongo.ErrNoDocuments {
		return false, errors.WithStack(err)
	}

	var newEnvAttribute models.Attribute
	if err := tools.Database.Collection("attribute").FindOne(nil, newEnvCondition).Decode(&newEnvAttribute); err != nil && err != mongo.ErrNoDocuments {
		return false, errors.WithStack(err)
	}

	isExistence := false
	if existEnvAttribute.AttributeInfo.CountryLayered != newEnvAttribute.AttributeInfo.CountryLayered &&
		existEnvAttribute.AttributeInfo.InstituteLayered != newEnvAttribute.AttributeInfo.InstituteLayered &&
		existEnvAttribute.AttributeInfo.RegionLayered != newEnvAttribute.AttributeInfo.RegionLayered {
		isExistence = false
	} else {
		isExistence = true
		if existEnvAttribute.AttributeInfo.RegionLayered == newEnvAttribute.AttributeInfo.RegionLayered {
			existEnvMatch := bson.M{
				"env_id":  existEnvId,
				"deleted": bson.M{"$ne": true},
			}
			existEnvRegionList := make([]models.Region, 0)
			cursor, err := tools.Database.Collection("region").Find(nil, existEnvMatch)
			if err != nil {
				return false, errors.WithStack(err)
			}
			err = cursor.All(nil, &existEnvRegionList)
			if err != nil {
				return false, errors.WithStack(err)
			}
			existEnvNameList := make([]string, 0)
			if existEnvRegionList != nil && len(existEnvRegionList) > 0 {
				for _, region := range existEnvRegionList {
					existEnvNameList = append(existEnvNameList, region.Name)
				}
			}

			newEnvMatch := bson.M{
				"env_id":  newEnvID,
				"deleted": bson.M{"$ne": true},
			}
			newEnvRegionList := make([]models.Region, 0)
			cu, err := tools.Database.Collection("region").Find(nil, newEnvMatch)
			if err != nil {
				return false, errors.WithStack(err)
			}
			err = cu.All(nil, &newEnvRegionList)
			if err != nil {
				return false, errors.WithStack(err)
			}
			newEnvNameList := make([]string, 0)
			if newEnvRegionList != nil && len(newEnvRegionList) > 0 {
				for _, region := range newEnvRegionList {
					newEnvNameList = append(newEnvNameList, region.Name)
				}
			}

			// 对数组进行排序
			sort.Strings(existEnvNameList)
			sort.Strings(newEnvNameList)

			// 比较两个排序后的字符串数组是否相等
			if !reflect.DeepEqual(existEnvNameList, newEnvNameList) {
				//fmt.Println("两个字符串数组不相等")
				isExistence = false
			} else {
				isExistence = true
			}
		}
	}

	if isExistence {

		//访视管理
		if newVisitCycle.ConfigInfo.Infos != nil && len(newVisitCycle.ConfigInfo.Infos) > 0 {
			for _, newInfo := range newVisitCycle.ConfigInfo.Infos {
				// 校验访视是否发药过 随机过
				b, err := isDispensingVisit(ctx, nil, newVisitCycle.ID, newInfo.ID)
				if err != nil {
					return false, errors.WithStack(err)
				}

				if b {
					if visitCycle.ConfigInfo.Infos != nil && len(visitCycle.ConfigInfo.Infos) > 0 {
						_, bex := slice.Find(visitCycle.ConfigInfo.Infos, func(index int, item models.VisitCycleInfo) bool {
							return item.Number == newInfo.Number && item.Name == newInfo.Name &&
								reflect.DeepEqual(item.Group, newInfo.Group) && item.Random == newInfo.Random && item.Dispensing == newInfo.Dispensing
						})
						if !bex {
							return false, nil
						}
					} else {
						return false, nil
					}
				}
			}
		}

		// 研究产品
		list := make([]string, 0)
		if medicineNameList != nil && len(medicineNameList) > 0 {
			if nameList != nil && len(nameList) > 0 {
				for _, medicineName := range medicineNameList {
					_, b := slice.Find(nameList, func(index int, item string) bool {
						return item == medicineName
					})
					if b {
						list = append(list, medicineName)
					}
				}
			}
		}

		// 未编号研究产品
		otherList := make([]string, 0)
		if otherMedicineNameList != nil && len(otherMedicineNameList) > 0 {
			if otherNameList != nil && len(otherNameList) > 0 {
				for _, otherMedicineName := range otherMedicineNameList {
					_, b := slice.Find(otherNameList, func(index int, item string) bool {
						return item == otherMedicineName
					})
					if b {
						otherList = append(otherList, otherMedicineName)
					}
				}
			}
		}

		type D struct {
			DrugName               string `json:"drugName" bson:"drugname"`
			CustomDispensingNumber string `json:"customDispensingNumber" bson:"custom_dispensing_number"` //   开放配置自定义数量 1,3~7
		}

		type RVMapping struct {
			VisitList []string `json:"visitList" bson:"visit_list"`
			DrugList  []D      `json:"drugList" bson:"drug_list"`
		}

		type DrugConfigInfo struct {
			Group                   string             `json:"group" bson:"group"`
			ParName                 string             `json:"parName" bson:"par_name"`
			SubName                 string             `json:"subName" bson:"sub_name"`
			Values                  []models.DrugValue `json:"values" bson:"values"`
			VisitCycles             []string           `json:"visitCycles" bson:"visit_cycles"`
			Label                   string             `json:"label" bson:"label"`
			RoomNumbers             []string           `json:"roomNumbers" bson:"room_numbers"`
			OpenSetting             int                `json:"openSetting" bson:"open_setting"`                          // 1 原始配置、 2、开放配置 3、公式配置
			CalculationType         int                `json:"calculationType" bson:"calculation_type"`                  // 年龄/体重/简易体表面积BSA/其他体表面积BSA
			CustomerCalculation     string             `bson:"customer_calculation" json:"customerCalculation"`          // 自定义公式
			IsFormula               bool               `bson:"is_formula" json:"isFormula"`                              // 是否开启按公式计算
			KeepDecimal             *int               `bson:"keep_decimal" json:"keepDecimal"`                          // 小数位
			CustomerCalculationSpec string             `bson:"customer_calculation_spec" json:"customerCalculationSpec"` // 自定义公式规格
			RoutineVisitMappingList []RVMapping        `json:"routineVisitMappingList" bson:"routine_visit_mapping_list"`
		}

		drugConfigureInfoList := make([]DrugConfigInfo, 0)
		if drugConfigure.Configures != nil && len(drugConfigure.Configures) > 0 {
			for _, configure := range drugConfigure.Configures {
				if configure.Values != nil && len(configure.Values) > 0 {
					for _, value := range configure.Values {
						if value.IsOther {
							//未编号研究产品
							index := arrays.ContainsString(otherList, value.DrugName)
							if index != -1 {
								//-1说明不存在
								info := DrugConfigInfo{
									Group:                   configure.Group,
									ParName:                 configure.ParName,
									SubName:                 configure.SubName,
									Label:                   configure.Group,
									RoomNumbers:             configure.RoomNumbers,
									OpenSetting:             configure.OpenSetting,
									CalculationType:         configure.CalculationType,
									CustomerCalculation:     configure.CustomerCalculation,
									IsFormula:               configure.IsFormula,
									KeepDecimal:             configure.KeepDecimal,
									CustomerCalculationSpec: configure.CustomerCalculationSpec,
								}

								drugValueList := make([]models.DrugValue, 0)
								if configure.Values != nil && len(configure.Values) > 0 {
									for _, drugValue := range configure.Values {
										drugValue.IsCopyEditDelete = false
										drugValueList = append(drugValueList, drugValue)
									}
								}
								sort.Slice(drugValueList, func(i, j int) bool {
									return drugValueList[i].DrugName < drugValueList[j].DrugName
								})
								info.Values = drugValueList

								vcNameList := make([]string, 0)
								if configure.VisitCycles != nil && len(configure.VisitCycles) > 0 {
									for _, vc := range configure.VisitCycles {
										if visitCycle.ConfigInfo.Infos != nil && len(visitCycle.ConfigInfo.Infos) > 0 {
											for _, cycleInfo := range visitCycle.ConfigInfo.Infos {
												if vc == cycleInfo.ID {
													vcNameList = append(vcNameList, cycleInfo.Name)
												}
											}
										}
									}
								}
								sort.Slice(vcNameList, func(i, j int) bool {
									return vcNameList[i] < vcNameList[j]
								})
								info.VisitCycles = vcNameList
								rVMappingList := make([]RVMapping, 0)
								if configure.RoutineVisitMappingList != nil && len(configure.RoutineVisitMappingList) > 0 {
									for _, mapping := range configure.RoutineVisitMappingList {
										m := RVMapping{}
										vNameList := make([]string, 0)
										if mapping.VisitList != nil && len(mapping.VisitList) > 0 {
											for _, v := range mapping.VisitList {
												if visitCycle.ConfigInfo.Infos != nil && len(visitCycle.ConfigInfo.Infos) > 0 {
													for _, cycleInfo := range visitCycle.ConfigInfo.Infos {
														if v == cycleInfo.ID {
															vNameList = append(vNameList, cycleInfo.Name)
														}
													}
												}
											}
										}
										sort.Slice(vNameList, func(i, j int) bool {
											return vNameList[i] < vNameList[j]
										})
										m.VisitList = vNameList
										dList := make([]D, 0)
										if mapping.DrugList != nil && len(mapping.DrugList) > 0 {
											for _, drug := range mapping.DrugList {
												d := D{
													DrugName:               drug.DrugName,
													CustomDispensingNumber: drug.CustomDispensingNumber,
												}
												dList = append(dList, d)
											}
										}
										sort.Slice(dList, func(i, j int) bool {
											return dList[i].DrugName < dList[j].DrugName
										})
										m.DrugList = dList
										rVMappingList = append(rVMappingList, m)
									}
								}
								info.RoutineVisitMappingList = rVMappingList
								drugConfigureInfoIndex := arrays.Contains(drugConfigureInfoList, info)
								if drugConfigureInfoIndex == -1 {
									drugConfigureInfoList = append(drugConfigureInfoList, info)
								}
							}
						} else {
							//研究产品
							index := arrays.ContainsString(list, value.DrugName)
							if index != -1 {
								//-1说明不存在
								info := DrugConfigInfo{
									Group:                   configure.Group,
									ParName:                 configure.ParName,
									SubName:                 configure.SubName,
									Label:                   configure.Group,
									RoomNumbers:             configure.RoomNumbers,
									OpenSetting:             configure.OpenSetting,
									CalculationType:         configure.CalculationType,
									CustomerCalculation:     configure.CustomerCalculation,
									IsFormula:               configure.IsFormula,
									KeepDecimal:             configure.KeepDecimal,
									CustomerCalculationSpec: configure.CustomerCalculationSpec,
								}

								drugValueList := make([]models.DrugValue, 0)
								if configure.Values != nil && len(configure.Values) > 0 {
									for _, drugValue := range configure.Values {
										drugValue.IsCopyEditDelete = false
										drugValueList = append(drugValueList, drugValue)
									}
								}
								sort.Slice(drugValueList, func(i, j int) bool {
									return drugValueList[i].DrugName < drugValueList[j].DrugName
								})
								info.Values = drugValueList

								vcNameList := make([]string, 0)
								if configure.VisitCycles != nil && len(configure.VisitCycles) > 0 {
									for _, vc := range configure.VisitCycles {
										if visitCycle.ConfigInfo.Infos != nil && len(visitCycle.ConfigInfo.Infos) > 0 {
											for _, cycleInfo := range visitCycle.ConfigInfo.Infos {
												if vc == cycleInfo.ID {
													vcNameList = append(vcNameList, cycleInfo.Name)
												}
											}
										}
									}
								}
								sort.Slice(vcNameList, func(i, j int) bool {
									return vcNameList[i] < vcNameList[j]
								})
								info.VisitCycles = vcNameList
								rVMappingList := make([]RVMapping, 0)
								if configure.RoutineVisitMappingList != nil && len(configure.RoutineVisitMappingList) > 0 {
									for _, mapping := range configure.RoutineVisitMappingList {
										m := RVMapping{}
										vNameList := make([]string, 0)
										if mapping.VisitList != nil && len(mapping.VisitList) > 0 {
											for _, v := range mapping.VisitList {
												if visitCycle.ConfigInfo.Infos != nil && len(visitCycle.ConfigInfo.Infos) > 0 {
													for _, cycleInfo := range visitCycle.ConfigInfo.Infos {
														if v == cycleInfo.ID {
															vNameList = append(vNameList, cycleInfo.Name)
														}
													}
												}
											}
										}
										sort.Slice(vNameList, func(i, j int) bool {
											return vNameList[i] < vNameList[j]
										})
										m.VisitList = vNameList
										dList := make([]D, 0)
										if mapping.DrugList != nil && len(mapping.DrugList) > 0 {
											for _, drug := range mapping.DrugList {
												d := D{
													DrugName:               drug.DrugName,
													CustomDispensingNumber: drug.CustomDispensingNumber,
												}
												dList = append(dList, d)
											}
										}
										sort.Slice(dList, func(i, j int) bool {
											return dList[i].DrugName < dList[j].DrugName
										})
										m.DrugList = dList
										rVMappingList = append(rVMappingList, m)
									}
								}
								info.RoutineVisitMappingList = rVMappingList
								drugConfigureInfoIndex := arrays.Contains(drugConfigureInfoList, info)
								if drugConfigureInfoIndex == -1 {
									drugConfigureInfoList = append(drugConfigureInfoList, info)
								}
							}
						}
					}
				}
			}
		}

		newDrugConfigureInfoList := make([]DrugConfigInfo, 0)
		if newDrugConfigure.Configures != nil && len(newDrugConfigure.Configures) > 0 {
			for _, configure := range newDrugConfigure.Configures {
				if configure.Values != nil && len(configure.Values) > 0 {
					for _, value := range configure.Values {
						if value.IsOther {
							//未编号研究产品
							index := arrays.ContainsString(otherList, value.DrugName)
							if index != -1 {
								//-1说明不存在
								info := DrugConfigInfo{
									Group:                   configure.Group,
									ParName:                 configure.ParName,
									SubName:                 configure.SubName,
									Label:                   configure.Group,
									RoomNumbers:             configure.RoomNumbers,
									OpenSetting:             configure.OpenSetting,
									CalculationType:         configure.CalculationType,
									CustomerCalculation:     configure.CustomerCalculation,
									IsFormula:               configure.IsFormula,
									KeepDecimal:             configure.KeepDecimal,
									CustomerCalculationSpec: configure.CustomerCalculationSpec,
								}

								drugValueList := make([]models.DrugValue, 0)
								if configure.Values != nil && len(configure.Values) > 0 {
									for _, drugValue := range configure.Values {
										drugValue.IsCopyEditDelete = false
										drugValueList = append(drugValueList, drugValue)
									}
								}
								sort.Slice(drugValueList, func(i, j int) bool {
									return drugValueList[i].DrugName < drugValueList[j].DrugName
								})
								info.Values = drugValueList

								vcNameList := make([]string, 0)
								if configure.VisitCycles != nil && len(configure.VisitCycles) > 0 {
									for _, vc := range configure.VisitCycles {
										if newVisitCycle.ConfigInfo.Infos != nil && len(newVisitCycle.ConfigInfo.Infos) > 0 {
											for _, cycleInfo := range newVisitCycle.ConfigInfo.Infos {
												if vc == cycleInfo.ID {
													vcNameList = append(vcNameList, cycleInfo.Name)
												}
											}
										}
									}
								}
								sort.Slice(vcNameList, func(i, j int) bool {
									return vcNameList[i] < vcNameList[j]
								})
								info.VisitCycles = vcNameList
								rVMappingList := make([]RVMapping, 0)
								if configure.RoutineVisitMappingList != nil && len(configure.RoutineVisitMappingList) > 0 {
									for _, mapping := range configure.RoutineVisitMappingList {
										m := RVMapping{}
										vNameList := make([]string, 0)
										if mapping.VisitList != nil && len(mapping.VisitList) > 0 {
											for _, v := range mapping.VisitList {
												if newVisitCycle.ConfigInfo.Infos != nil && len(newVisitCycle.ConfigInfo.Infos) > 0 {
													for _, cycleInfo := range newVisitCycle.ConfigInfo.Infos {
														if v == cycleInfo.ID {
															vNameList = append(vNameList, cycleInfo.Name)
														}
													}
												}
											}
										}
										sort.Slice(vNameList, func(i, j int) bool {
											return vNameList[i] < vNameList[j]
										})
										m.VisitList = vNameList
										dList := make([]D, 0)
										if mapping.DrugList != nil && len(mapping.DrugList) > 0 {
											for _, drug := range mapping.DrugList {
												d := D{
													DrugName:               drug.DrugName,
													CustomDispensingNumber: drug.CustomDispensingNumber,
												}
												dList = append(dList, d)
											}
										}
										sort.Slice(dList, func(i, j int) bool {
											return dList[i].DrugName < dList[j].DrugName
										})
										m.DrugList = dList
										rVMappingList = append(rVMappingList, m)
									}
								}
								info.RoutineVisitMappingList = rVMappingList
								drugConfigureInfoIndex := arrays.Contains(newDrugConfigureInfoList, info)
								if drugConfigureInfoIndex == -1 {
									newDrugConfigureInfoList = append(newDrugConfigureInfoList, info)
								}
							}
						} else {
							//研究产品
							index := arrays.ContainsString(list, value.DrugName)
							if index != -1 {
								//-1说明不存在
								info := DrugConfigInfo{
									Group:                   configure.Group,
									ParName:                 configure.ParName,
									SubName:                 configure.SubName,
									Label:                   configure.Group,
									RoomNumbers:             configure.RoomNumbers,
									OpenSetting:             configure.OpenSetting,
									CalculationType:         configure.CalculationType,
									CustomerCalculation:     configure.CustomerCalculation,
									IsFormula:               configure.IsFormula,
									KeepDecimal:             configure.KeepDecimal,
									CustomerCalculationSpec: configure.CustomerCalculationSpec,
								}

								drugValueList := make([]models.DrugValue, 0)
								if configure.Values != nil && len(configure.Values) > 0 {
									for _, drugValue := range configure.Values {
										drugValue.IsCopyEditDelete = false
										drugValueList = append(drugValueList, drugValue)
									}
								}
								sort.Slice(drugValueList, func(i, j int) bool {
									return drugValueList[i].DrugName < drugValueList[j].DrugName
								})
								info.Values = drugValueList

								vcNameList := make([]string, 0)
								if configure.VisitCycles != nil && len(configure.VisitCycles) > 0 {
									for _, vc := range configure.VisitCycles {
										if newVisitCycle.ConfigInfo.Infos != nil && len(newVisitCycle.ConfigInfo.Infos) > 0 {
											for _, cycleInfo := range newVisitCycle.ConfigInfo.Infos {
												if vc == cycleInfo.ID {
													vcNameList = append(vcNameList, cycleInfo.Name)
												}
											}
										}
									}
								}
								sort.Slice(vcNameList, func(i, j int) bool {
									return vcNameList[i] < vcNameList[j]
								})
								info.VisitCycles = vcNameList
								rVMappingList := make([]RVMapping, 0)
								if configure.RoutineVisitMappingList != nil && len(configure.RoutineVisitMappingList) > 0 {
									for _, mapping := range configure.RoutineVisitMappingList {
										m := RVMapping{}
										vNameList := make([]string, 0)
										if mapping.VisitList != nil && len(mapping.VisitList) > 0 {
											for _, v := range mapping.VisitList {
												if newVisitCycle.ConfigInfo.Infos != nil && len(newVisitCycle.ConfigInfo.Infos) > 0 {
													for _, cycleInfo := range newVisitCycle.ConfigInfo.Infos {
														if v == cycleInfo.ID {
															vNameList = append(vNameList, cycleInfo.Name)
														}
													}
												}
											}
										}
										sort.Slice(vNameList, func(i, j int) bool {
											return vNameList[i] < vNameList[j]
										})
										m.VisitList = vNameList
										dList := make([]D, 0)
										if mapping.DrugList != nil && len(mapping.DrugList) > 0 {
											for _, drug := range mapping.DrugList {
												d := D{
													DrugName:               drug.DrugName,
													CustomDispensingNumber: drug.CustomDispensingNumber,
												}
												dList = append(dList, d)
											}
										}
										sort.Slice(dList, func(i, j int) bool {
											return dList[i].DrugName < dList[j].DrugName
										})
										m.DrugList = dList
										rVMappingList = append(rVMappingList, m)
									}
								}
								info.RoutineVisitMappingList = rVMappingList
								drugConfigureInfoIndex := arrays.Contains(newDrugConfigureInfoList, info)
								if drugConfigureInfoIndex == -1 {
									newDrugConfigureInfoList = append(newDrugConfigureInfoList, info)
								}
							}
						}
					}
				}
			}
		}

		// 比较两个排序后的字符串数组是否相等
		if !reflect.DeepEqual(drugConfigureInfoList, newDrugConfigureInfoList) {
			return false, nil
		}

		existEnvRandomDesign := models.RandomDesign{}
		if err := tools.Database.Collection("random_design").FindOne(ctx, existEnvCondition).Decode(&existEnvRandomDesign); err != nil && err != mongo.ErrNoDocuments {
			return false, errors.WithStack(err)
		}

		newEnvRandomDesign := models.RandomDesign{}
		if err := tools.Database.Collection("random_design").FindOne(ctx, newEnvCondition).Decode(&newEnvRandomDesign); err != nil && err != mongo.ErrNoDocuments {
			return false, errors.WithStack(err)
		}

		if existEnvRandomDesign.Info.Type != newEnvRandomDesign.Info.Type {
			return false, nil
		} else {

			existEnvMainGroupList := transformationGroup(existEnvRandomDesign)
			newEnvMainGroupList := transformationGroup(newEnvRandomDesign)

			// 对数组进行排序
			sort.Slice(existEnvMainGroupList, func(i, j int) bool {
				return existEnvMainGroupList[i].Code < existEnvMainGroupList[j].Code
			})
			// 对 sub_group 数组进行排序
			for _, group := range existEnvMainGroupList {
				sort.Slice(group.ViceGroup, func(i, j int) bool {
					return group.ViceGroup[i].Name < group.ViceGroup[j].Name
				})
			}

			sort.Slice(newEnvMainGroupList, func(i, j int) bool {
				return newEnvMainGroupList[i].Code < newEnvMainGroupList[j].Code
			})
			for _, group := range newEnvMainGroupList {
				sort.Slice(group.ViceGroup, func(i, j int) bool {
					return group.ViceGroup[i].Name < group.ViceGroup[j].Name
				})
			}

			// 比较两个排序后的字符串数组是否相等
			if !reflect.DeepEqual(existEnvMainGroupList, newEnvMainGroupList) {
				//fmt.Println("两个字符串数组不相等")
				for _, newMainGroup := range newEnvMainGroupList {
					_, b := slice.Find(existEnvMainGroupList, func(index int, item models.MainGroup) bool {
						return item.Code == newMainGroup.Code && item.Name == newMainGroup.Name &&
							reflect.DeepEqual(item.ViceGroup, newMainGroup.ViceGroup)
					})
					if !b {
						groupList := make([]string, 0)
						if newMainGroup.ViceGroup != nil && len(newMainGroup.ViceGroup) > 0 {
							for _, vg := range newMainGroup.ViceGroup {
								gp := newMainGroup.Name + " " + vg.Name
								groupList = append(groupList, gp)
							}
						} else {
							groupList = append(groupList, newMainGroup.Name)
						}
						condition := bson.M{"customer_id": newEnvAttribute.CustomerID, "project_id": newEnvAttribute.ProjectID, "env_id": newEnvAttribute.EnvironmentID, "deleted": bson.M{"$ne": true}}
						condition["group"] = bson.M{"$in": groupList}
						if count, _ := tools.Database.Collection("subject").CountDocuments(ctx, condition); count > 0 {
							return false, nil
						}
					}
				}
			} else {
				isExistence = true
			}

			if isExistence {

				existEnvForm := models.Form{}
				if err := tools.Database.Collection("form").FindOne(ctx, existEnvCondition).Decode(&existEnvRandomDesign); err != nil && err != mongo.ErrNoDocuments {
					return false, errors.WithStack(err)
				}

				newEnvForm := models.Form{}
				if err := tools.Database.Collection("form").FindOne(ctx, newEnvCondition).Decode(&newEnvRandomDesign); err != nil && err != mongo.ErrNoDocuments {
					return false, errors.WithStack(err)
				}

				existEnvFactorList := transformationFactors(existEnvRandomDesign)
				newEnvFactorList := transformationFactors(newEnvRandomDesign)

				// 对数组进行排序
				sort.Slice(existEnvFactorList, func(i, j int) bool {
					return existEnvFactorList[i].Number < existEnvFactorList[j].Number
				})
				sort.Slice(newEnvFactorList, func(i, j int) bool {
					return newEnvFactorList[i].Number < newEnvFactorList[j].Number
				})

				existEnvFieldList := transformationFields(existEnvForm)
				newEnvFieldList := transformationFields(newEnvForm)

				// 对数组进行排序
				sort.Slice(existEnvFieldList, func(i, j int) bool {
					return existEnvFieldList[i].Name < existEnvFieldList[j].Name
				})
				sort.Slice(newEnvFieldList, func(i, j int) bool {
					return newEnvFieldList[i].Name < newEnvFieldList[j].Name
				})

				// 比较两个排序后的字符串数组是否相等
				if !reflect.DeepEqual(existEnvFactorList, newEnvFactorList) {
					//fmt.Println("两个字符串数组不相等")
					for _, newEnvFactor := range newEnvFactorList {
						_, b := slice.Find(existEnvFactorList, func(index int, item models.RandomFactor) bool {
							return item.Number == newEnvFactor.Number && item.Name == newEnvFactor.Name &&
								item.Label == newEnvFactor.Label &&
								item.CustomFormulas == newEnvFactor.CustomFormulas && item.Round == newEnvFactor.Round &&
								item.Type == newEnvFactor.Type && item.DateFormat == newEnvFactor.DateFormat &&
								item.Precision == newEnvFactor.Precision && item.IsCalc == newEnvFactor.IsCalc &&
								item.CalcType == newEnvFactor.CalcType && item.Modifiable == newEnvFactor.Modifiable &&
								item.Ratio == newEnvFactor.Ratio &&
								reflect.DeepEqual(item.Options, newEnvFactor.Options)
						})
						if !b {
							filter := bson.M{"customer_id": newEnvAttribute.CustomerID, "project_id": newEnvAttribute.ProjectID, "env_id": newEnvAttribute.EnvironmentID, "deleted": bson.M{"$ne": true}}
							filter["info"] = bson.M{
								"$elemMatch": bson.M{
									"name": newEnvFactor.Name,
								},
							}
							if count, _ := tools.Database.Collection("subject").CountDocuments(ctx, filter); count > 0 {
								return false, nil
							}
						}
					}

					if !reflect.DeepEqual(existEnvFieldList, newEnvFieldList) {
						for _, newEnvField := range newEnvFieldList {
							_, b := slice.Find(existEnvFieldList, func(index int, item models.Field) bool {
								return item.Name == newEnvField.Name && item.IsCalc == newEnvField.IsCalc &&
									item.CalcType == newEnvField.CalcType && item.Label == newEnvField.Label &&
									item.LabelEn == newEnvField.LabelEn &&
									//item.InputLabel == newEnvField.InputLabel &&
									//item.InputWeightLabel == newEnvField.InputWeightLabel && item.InputHeightLabel == newEnvField.InputHeightLabel &&
									item.Precision == newEnvField.Precision && item.Type == newEnvField.Type &&
									item.Round == newEnvField.Round && item.CustomFormulas == newEnvField.CustomFormulas &&
									item.ApplicationType == newEnvField.ApplicationType && item.Variable == newEnvField.Variable &&
									item.Digit == newEnvField.Digit && item.Accuracy == newEnvField.Accuracy &&
									item.DateFormat == newEnvField.DateFormat && item.FormatType == newEnvField.FormatType &&
									item.TimeFormat == newEnvField.TimeFormat && item.Length == newEnvField.Length &&
									item.Range == newEnvField.Range &&
									reflect.DeepEqual(item.Options, newEnvField.Options)
							})
							if !b {
								filter := bson.M{"customer_id": newEnvAttribute.CustomerID, "project_id": newEnvAttribute.ProjectID, "env_id": newEnvAttribute.EnvironmentID, "deleted": bson.M{"$ne": true}}
								filter["info"] = bson.M{
									"$elemMatch": bson.M{
										"name": newEnvField.Name,
									},
								}
								if count, _ := tools.Database.Collection("subject").CountDocuments(ctx, filter); count > 0 {
									return false, nil
								}
							}
						}
						return true, nil
					} else {
						return true, nil
					}

				} else {
					if !reflect.DeepEqual(existEnvFieldList, newEnvFieldList) {
						for _, newEnvField := range newEnvFieldList {
							_, b := slice.Find(existEnvFieldList, func(index int, item models.Field) bool {
								return item.Name == newEnvField.Name && item.IsCalc == newEnvField.IsCalc &&
									item.CalcType == newEnvField.CalcType && item.Label == newEnvField.Label &&
									item.LabelEn == newEnvField.LabelEn &&
									//item.InputLabel == newEnvField.InputLabel &&
									//item.InputWeightLabel == newEnvField.InputWeightLabel && item.InputHeightLabel == newEnvField.InputHeightLabel &&
									item.Precision == newEnvField.Precision && item.Type == newEnvField.Type &&
									item.Round == newEnvField.Round && item.CustomFormulas == newEnvField.CustomFormulas &&
									item.ApplicationType == newEnvField.ApplicationType && item.Variable == newEnvField.Variable &&
									item.Digit == newEnvField.Digit && item.Accuracy == newEnvField.Accuracy &&
									item.DateFormat == newEnvField.DateFormat && item.FormatType == newEnvField.FormatType &&
									item.TimeFormat == newEnvField.TimeFormat && item.Length == newEnvField.Length &&
									item.Range == newEnvField.Range &&
									reflect.DeepEqual(item.Options, newEnvField.Options)
							})
							if !b {
								filter := bson.M{"customer_id": newEnvAttribute.CustomerID, "project_id": newEnvAttribute.ProjectID, "env_id": newEnvAttribute.EnvironmentID, "deleted": bson.M{"$ne": true}}
								filter["info"] = bson.M{
									"$elemMatch": bson.M{
										"name": newEnvField.Name,
									},
								}
								if count, _ := tools.Database.Collection("subject").CountDocuments(ctx, filter); count > 0 {
									return false, nil
								}
							}
						}
						return true, nil
					} else {
						return true, nil
					}
				}

			} else {
				return false, nil
			}
		}
	} else {
		return false, nil
	}
}

func isDispensingVisit(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, visitOID primitive.ObjectID) (bool, error) {
	var visitCycle models.VisitCycle
	err := tools.Database.Collection("visit_cycle").FindOne(ctx, bson.M{"_id": OID}).Decode(&visitCycle)
	if err != nil {
		return false, errors.WithStack(err)
	}
	infos := models.VisitCycleInfo{}
	for _, info := range visitCycle.ConfigInfo.Infos {
		if info.ID == visitOID {
			infos = info
			break
		}
	}
	if infos.Random {
		// 查询是否已经有随机的受试者 已经有随机的不给删除修改
		documents, err := tools.Database.Collection("subject").CountDocuments(ctx, bson.M{"group": bson.M{"$ne": ""}, "env_id": visitCycle.EnvironmentID, "cohort_id": visitCycle.CohortID, "status": bson.M{"$in": bson.A{3, 4, 5, 6}}})
		if err != nil {
			return false, errors.WithStack(err)
		}
		if documents > 0 {
			//return tools.BuildServerError(ctx, "visit_cycle_duplicated_random3")
			return true, nil
		}
	} else {
		// 查询是否已经有发药的访视 已经有发药的不给删除修改
		documents, err := tools.Database.Collection("dispensing").CountDocuments(ctx, bson.M{"env_id": visitCycle.EnvironmentID, "cohort_id": visitCycle.CohortID, "visit_info.visit_cycle_info_id": visitOID, "status": bson.M{"$ne": 1}})
		if err != nil {
			return false, errors.WithStack(err)
		}
		if documents > 0 {
			return true, nil
		}
	}

	return false, nil
}

func transformationEqual(ctx *gin.Context, existEnvCondition bson.M, newEnvCondition bson.M, newEnvID primitive.ObjectID, existEnvId primitive.ObjectID) (bool, error) {
	var existEnvAttribute models.Attribute
	if err := tools.Database.Collection("attribute").FindOne(nil, existEnvCondition).Decode(&existEnvAttribute); err != nil {
		return false, errors.WithStack(err)
	}

	var newEnvAttribute models.Attribute
	if err := tools.Database.Collection("attribute").FindOne(nil, newEnvCondition).Decode(&newEnvAttribute); err != nil {
		return false, errors.WithStack(err)
	}

	isExistence := false
	if existEnvAttribute.AttributeInfo.CountryLayered != newEnvAttribute.AttributeInfo.CountryLayered &&
		existEnvAttribute.AttributeInfo.InstituteLayered != newEnvAttribute.AttributeInfo.InstituteLayered &&
		existEnvAttribute.AttributeInfo.RegionLayered != newEnvAttribute.AttributeInfo.RegionLayered {
		isExistence = false
	} else {
		isExistence = true
		if existEnvAttribute.AttributeInfo.RegionLayered == newEnvAttribute.AttributeInfo.RegionLayered {
			existEnvMatch := bson.M{
				"env_id":  existEnvId,
				"deleted": bson.M{"$ne": true},
			}
			existEnvRegionList := make([]models.Region, 0)
			cursor, err := tools.Database.Collection("region").Find(nil, existEnvMatch)
			if err != nil {
				return false, errors.WithStack(err)
			}
			err = cursor.All(nil, &existEnvRegionList)
			if err != nil {
				return false, errors.WithStack(err)
			}
			existEnvNameList := make([]string, 0)
			if existEnvRegionList != nil && len(existEnvRegionList) > 0 {
				for _, region := range existEnvRegionList {
					existEnvNameList = append(existEnvNameList, region.Name)
				}
			}

			newEnvMatch := bson.M{
				"env_id":  newEnvID,
				"deleted": bson.M{"$ne": true},
			}
			newEnvRegionList := make([]models.Region, 0)
			cu, err := tools.Database.Collection("region").Find(nil, newEnvMatch)
			if err != nil {
				return false, errors.WithStack(err)
			}
			err = cu.All(nil, &newEnvRegionList)
			if err != nil {
				return false, errors.WithStack(err)
			}
			newEnvNameList := make([]string, 0)
			if newEnvRegionList != nil && len(newEnvRegionList) > 0 {
				for _, region := range newEnvRegionList {
					newEnvNameList = append(newEnvNameList, region.Name)
				}
			}

			// 对数组进行排序
			sort.Strings(existEnvNameList)
			sort.Strings(newEnvNameList)

			// 比较两个排序后的字符串数组是否相等
			if !reflect.DeepEqual(existEnvNameList, newEnvNameList) {
				//fmt.Println("两个字符串数组不相等")
				isExistence = false
			} else {
				isExistence = true
			}
		}
	}

	if isExistence {
		existEnvRandomDesign := models.RandomDesign{}
		if err := tools.Database.Collection("random_design").FindOne(ctx, existEnvCondition).Decode(&existEnvRandomDesign); err != nil && err != mongo.ErrNoDocuments {
			return false, errors.WithStack(err)
		}

		newEnvRandomDesign := models.RandomDesign{}
		if err := tools.Database.Collection("random_design").FindOne(ctx, newEnvCondition).Decode(&newEnvRandomDesign); err != nil && err != mongo.ErrNoDocuments {
			return false, errors.WithStack(err)
		}

		if existEnvRandomDesign.Info.Type != newEnvRandomDesign.Info.Type {
			return false, nil
		} else {

			existEnvMainGroupList := transformationGroup(existEnvRandomDesign)
			newEnvMainGroupList := transformationGroup(newEnvRandomDesign)

			// 对数组进行排序
			sort.Slice(existEnvMainGroupList, func(i, j int) bool {
				return existEnvMainGroupList[i].Code < existEnvMainGroupList[j].Code
			})
			// 对 sub_group 数组进行排序
			for _, group := range existEnvMainGroupList {
				sort.Slice(group.ViceGroup, func(i, j int) bool {
					return group.ViceGroup[i].Name < group.ViceGroup[j].Name
				})
			}

			sort.Slice(newEnvMainGroupList, func(i, j int) bool {
				return newEnvMainGroupList[i].Code < newEnvMainGroupList[j].Code
			})
			for _, group := range newEnvMainGroupList {
				sort.Slice(group.ViceGroup, func(i, j int) bool {
					return group.ViceGroup[i].Name < group.ViceGroup[j].Name
				})
			}

			// 比较两个排序后的字符串数组是否相等
			if !reflect.DeepEqual(existEnvMainGroupList, newEnvMainGroupList) {
				//fmt.Println("两个字符串数组不相等")
				isExistence = false
			} else {
				isExistence = true
			}

			if isExistence {
				existEnvFactorList := transformationFactors(existEnvRandomDesign)
				newEnvFactorList := transformationFactors(newEnvRandomDesign)

				// 对数组进行排序
				sort.Slice(existEnvFactorList, func(i, j int) bool {
					return existEnvFactorList[i].Number < existEnvFactorList[j].Number
				})
				sort.Slice(newEnvFactorList, func(i, j int) bool {
					return newEnvFactorList[i].Number < newEnvFactorList[j].Number
				})

				// 比较两个排序后的字符串数组是否相等
				if !reflect.DeepEqual(existEnvFactorList, newEnvFactorList) {
					//fmt.Println("两个字符串数组不相等")
					return false, nil
				} else {
					return true, nil
				}

			} else {
				return false, nil
			}
		}
	} else {
		return false, nil
	}
}

func transformationFields(form models.Form) []models.Field {
	fieldList := make([]models.Field, 0)
	if form.Fields != nil && len(form.Fields) > 0 {
		for _, field := range form.Fields {
			if field.Status == nil || *field.Status == 1 {
				fd := field
				fd.ID = primitive.NilObjectID
				fd.CohortId = primitive.NilObjectID
				optionList := make([]models.Option, 0)
				if fd.Options != nil && len(fd.Options) > 0 {
					for _, op := range fd.Options {
						option := op
						option.Disable = false
						option.IsCopyData = false
						optionList = append(optionList, option)
					}
				}
				fd.Used = false
				fd.List = false
				fd.Modifiable = false
				fd.Required = false
				fd.Stratification = false
				fd.Options = optionList
				fieldList = append(fieldList, fd)
			}
		}
	}
	return fieldList
}

func transformationFactors(randomDesign models.RandomDesign) []models.RandomFactor {
	factorList := make([]models.RandomFactor, 0)
	if randomDesign.Info.Factors != nil && len(randomDesign.Info.Factors) > 0 {
		for _, factor := range randomDesign.Info.Factors {
			if factor.Status == nil || *factor.Status == 1 {
				fact := factor
				fact.ID = primitive.NilObjectID
				optionList := make([]models.Option, 0)
				if fact.Options != nil && len(fact.Options) > 0 {
					for _, op := range fact.Options {
						option := op
						option.Disable = false
						option.IsCopyData = false
						optionList = append(optionList, option)
					}
				}
				fact.IsCopyData = false
				fact.Options = optionList
				factorList = append(factorList, fact)
			}
		}
	}
	return factorList
}

func transformationGroup(randomDesign models.RandomDesign) []models.MainGroup {
	mainGroupList := make([]models.MainGroup, 0)
	if randomDesign.Info.Groups != nil && len(randomDesign.Info.Groups) > 0 {
		for _, group := range randomDesign.Info.Groups {
			if group.Status == nil || *group.Status == 1 {
				var mainGroup models.MainGroup
				mainGroup.Code = group.Code
				mainGroup.Name = group.Name
				viceGroupList := make([]models.ViceGroup, 0)
				if group.SubGroup != nil && len(group.SubGroup) > 0 {
					for _, subGroup := range group.SubGroup {
						var viceGroup models.ViceGroup
						viceGroup.Name = subGroup.Name
						viceGroup.Blind = subGroup.Blind
						viceGroupList = append(viceGroupList, viceGroup)
					}
				}
				mainGroup.ViceGroup = viceGroupList
				mainGroupList = append(mainGroupList, mainGroup)
			}
		}
	}
	return mainGroupList
}

// 复制未编号研究产品列表(盲底)-----medicine_others
func copyUnnumberedMedicine(ctx *gin.Context, sctx mongo.SessionContext, project models.Project, oldEnvID primitive.ObjectID, newEnvID primitive.ObjectID, projectStorehouseList []models.ProjectStorehouse, unnumberedIpList []string, copyEnv string) error {
	filter := bson.M{
		"customer_id": project.CustomerID,
		"project_id":  project.ID,
		"env_id":      oldEnvID,
	}
	medicineOtherList := make([]models.OtherMedicine, 0)
	cursor, err := tools.Database.Collection("medicine_others").Find(sctx, filter)
	if err != nil {
		return errors.WithStack(err)
	}
	if err := cursor.All(sctx, &medicineOtherList); err != nil {
		return errors.WithStack(err)
	}

	OID := newEnvID
	medicineOrderList := make([]models.MedicineOrder, 0)
	cus, err := tools.Database.Collection("medicine_order").Find(nil, filter)
	if err != nil {
		return errors.WithStack(err)

	}
	if err := cus.All(nil, &medicineOrderList); err != nil {
		return errors.WithStack(err)
	}

	filter["deleted"] = 2
	oldProjectStorehouseList := make([]models.ProjectStorehouse, 0)
	cu, err := tools.Database.Collection("project_storehouse").Find(nil, filter)
	if err != nil {
		return errors.WithStack(err)
	}
	if err := cu.All(nil, &oldProjectStorehouseList); err != nil {
		return errors.WithStack(err)
	}

	idMappingList := make([]models.IdMapping, 0)
	if oldProjectStorehouseList != nil && len(oldProjectStorehouseList) > 0 {
		for _, oldStorehouse := range oldProjectStorehouseList {
			if projectStorehouseList != nil && len(projectStorehouseList) > 0 {
				for _, newStorehouse := range projectStorehouseList {
					if oldStorehouse.StorehouseID == newStorehouse.StorehouseID {
						var idMapping models.IdMapping
						idMapping.OldID = oldStorehouse.ID
						idMapping.NewID = newStorehouse.ID
						idMappingList = append(idMappingList, idMapping)
					}
				}
			}
		}
	}

	newOtherMedicineList := make([]models.OtherMedicine, 0)
	if medicineOtherList != nil && len(medicineOtherList) > 0 {
		for _, otherMedicine := range medicineOtherList {
			newOtherMedicine := otherMedicine
			newOtherMedicine.ID = primitive.NewObjectID()
			newOtherMedicine.EnvironmentID = newEnvID
			newOtherMedicine.OrderID = primitive.NilObjectID
			if medicineOrderList != nil && len(medicineOrderList) > 0 {
				medicineOrder, err := containsObjectID(medicineOrderList, otherMedicine.ID)
				if err != nil {
					return errors.WithStack(err)
				}
				if medicineOrder.ID != primitive.NilObjectID {
					newOtherMedicine.StorehouseID = medicineOrder.SendID
					newOtherMedicine.SiteID = primitive.NilObjectID
					newOtherMedicine.Status = 1
				}
			}
			if idMappingList != nil && len(idMappingList) > 0 {
				for _, idMapping := range idMappingList {
					if newOtherMedicine.StorehouseID == idMapping.OldID {
						if unnumberedIpList != nil && len(unnumberedIpList) > 0 {
							index := arrays.ContainsString(unnumberedIpList, newOtherMedicine.Name)
							if index != -1 {
								newOtherMedicine.StorehouseID = idMapping.NewID
								if newOtherMedicine.Status != 0 {
									newOtherMedicine.Status = 1
								}
								newOtherMedicineList = append(newOtherMedicineList, newOtherMedicine)
							}
						}
					}
				}
			}
		}
	}

	if newOtherMedicineList != nil && len(newOtherMedicineList) > 0 {
		var docs []interface{}
		for _, medicine := range newOtherMedicineList {
			docs = append(docs, medicine)
		}
		if _, err := tools.Database.Collection("medicine_others").InsertMany(nil, docs); err != nil {
			return errors.WithStack(err)
		}

		// 创建两个map，用于快速查找
		newMap := make(map[string]models.CopyMedicine)
		for _, medicine := range newOtherMedicineList {
			var newCopyMedicine models.CopyMedicine
			newCopyMedicine.Name = medicine.Name
			newCopyMedicine.StorehouseID = medicine.StorehouseID
			newCopyMedicine.ExpirationDate = medicine.ExpirationDate
			newCopyMedicine.BatchNumber = medicine.BatchNumber
			newCopyMedicine.Spec = medicine.Spec

			// 判断 map 中是否包含特定的 key
			key := medicine.Name + medicine.StorehouseID.Hex() + newCopyMedicine.ExpirationDate +
				newCopyMedicine.BatchNumber + newCopyMedicine.Spec
			if val, ok := newMap[key]; ok {
				newCopyMedicine.Count = val.Count + 1
				newMap[key] = newCopyMedicine
			} else {
				newCopyMedicine.Count = 1
				newMap[key] = newCopyMedicine
			}
		}

		copyMedicineList := make([]models.CopyMedicine, 0)
		// 遍历 map
		for _, value := range newMap {
			copyMedicineList = append(copyMedicineList, value)
		}

		err := InsertMedicineLog(ctx, nil, OID, 10, nil, copyMedicineList, copyEnv)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

// // 复制未编号研究产品列表(盲底)-----medicine_other_institute
// func copyUnnumberedMedicine(ctx *gin.Context, sctx mongo.SessionContext, project models.Project, oldEnvID primitive.ObjectID, newEnvID primitive.ObjectID, projectStorehouseList []models.ProjectStorehouse, unnumberedIpList []string, copyEnv string) error {
// 	filter := bson.M{
// 		"customer_id": project.CustomerID,
// 		"project_id":  project.ID,
// 		"env_id":      oldEnvID,
// 	}
// 	medicineOtherList := make([]models.MedicineOther, 0)
// 	cursor, err := tools.Database.Collection("medicine_other_institute").Find(nil, filter)
// 	if err != nil {
// 		return errors.WithStack(err)
// 	}
// 	if err := cursor.All(nil, &medicineOtherList); err != nil {
// 		return errors.WithStack(err)
// 	}

// 	OID := newEnvID

// 	filter["deleted"] = 2
// 	oldProjectStorehouseList := make([]models.ProjectStorehouse, 0)
// 	cu, err := tools.Database.Collection("project_storehouse").Find(nil, filter)
// 	if err != nil {
// 		return errors.WithStack(err)
// 	}
// 	if err := cu.All(nil, &oldProjectStorehouseList); err != nil {
// 		return errors.WithStack(err)
// 	}

// 	idMappingList := make([]models.IdMapping, 0)
// 	if oldProjectStorehouseList != nil && len(oldProjectStorehouseList) > 0 {
// 		for _, oldStorehouse := range oldProjectStorehouseList {
// 			if projectStorehouseList != nil && len(projectStorehouseList) > 0 {
// 				for _, newStorehouse := range projectStorehouseList {
// 					if oldStorehouse.StorehouseID == newStorehouse.StorehouseID {
// 						var idMapping models.IdMapping
// 						idMapping.OldID = oldStorehouse.ID
// 						idMapping.NewID = newStorehouse.ID
// 						idMappingList = append(idMappingList, idMapping)
// 					}
// 				}
// 			}
// 		}
// 	}

// 	newMedicineOtherList := make([]models.MedicineOther, 0)
// 	if medicineOtherList != nil && len(medicineOtherList) > 0 {
// 		for _, medicineOther := range medicineOtherList {
// 			newMedicineOther := medicineOther
// 			newMedicineOther.ID = primitive.NewObjectID()
// 			newMedicineOther.EnvironmentID = newEnvID
// 			newMedicineOther.Info.ID = primitive.NewObjectID()
// 			if idMappingList != nil && len(idMappingList) > 0 {
// 				for _, idMapping := range idMappingList {
// 					if newMedicineOther.StorehouseID == idMapping.OldID {
// 						if unnumberedIpList != nil && len(unnumberedIpList) > 0 {
// 							index := arrays.ContainsString(unnumberedIpList, newMedicineOther.Info.Name)
// 							if index != -1 {
// 								newMedicineOther.StorehouseID = idMapping.NewID
// 								newMedicineOtherList = append(newMedicineOtherList, newMedicineOther)
// 							}
// 						}
// 					}
// 				}
// 			}
// 		}
// 	}

// 	if newMedicineOtherList != nil && len(newMedicineOtherList) > 0 {
// 		for _, medicineOther := range newMedicineOtherList {
// 			if _, err := tools.Database.Collection("medicine_other_institute").InsertOne(nil, medicineOther); err != nil {
// 				return errors.WithStack(err)
// 			}
// 			var addMedicineOther models.AddMedicineOther
// 			addMedicineOther.ID = medicineOther.ID
// 			addMedicineOther.CustomerID = medicineOther.CustomerID
// 			addMedicineOther.ProjectID = medicineOther.ProjectID
// 			addMedicineOther.EnvironmentID = medicineOther.EnvironmentID
// 			addMedicineOther.StorehouseID = medicineOther.StorehouseID
// 			//addMedicineOther.Info = medicineOther.Info
// 			//TODO
// 			err = insertOtherMedicineLog(ctx, addMedicineOther, OID, 10, copyEnv)
// 			if err != nil {
// 				return errors.WithStack(err)
// 			}
// 		}
// 	}

// 	return nil
// }

// 复制条形码列表(盲底)-----barcode_group
func copyBarCode(ctx *gin.Context, sctx mongo.SessionContext, projectID primitive.ObjectID, oldEnvID primitive.ObjectID, newEnvID primitive.ObjectID, existEnvId primitive.ObjectID, projectStorehouseList []models.ProjectStorehouse, copyEnv string) error {

	collection := tools.Database.Collection("project")
	project := models.Project{}
	// 先检查环境是否重复 只检查基本研究
	err := collection.FindOne(ctx, bson.M{"_id": projectID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}

	filter := bson.M{"customer_id": project.CustomerID, "project_id": project.ID, "env_id": existEnvId}
	filter["deleted"] = 2
	oldProjectStorehouseList := make([]models.ProjectStorehouse, 0)
	cu, err := tools.Database.Collection("project_storehouse").Find(nil, filter)
	if err != nil {
		return errors.WithStack(err)
	}
	if err := cu.All(nil, &oldProjectStorehouseList); err != nil {
		return errors.WithStack(err)
	}

	idMappingList := make([]models.IdMapping, 0)
	if oldProjectStorehouseList != nil && len(oldProjectStorehouseList) > 0 {
		for _, oldStorehouse := range oldProjectStorehouseList {
			if projectStorehouseList != nil && len(projectStorehouseList) > 0 {
				for _, newStorehouse := range projectStorehouseList {
					if oldStorehouse.StorehouseID == newStorehouse.StorehouseID {
						var idMapping models.IdMapping
						idMapping.OldID = oldStorehouse.ID
						idMapping.NewID = newStorehouse.ID
						idMappingList = append(idMappingList, idMapping)
					}
				}
			}
		}
	}

	cohortMappingList := make([]models.CohortMapping, 0)

	if project.Type != 1 {
		oldEnvCohortIdList := make([]primitive.ObjectID, 0)
		existEnvCohortList := make([]models.Cohort, 0)
		newEnvCohortList := make([]models.Cohort, 0)
		if project.Environments != nil && len(project.Environments) > 0 {
			for _, environment := range project.Environments {
				if environment.ID == existEnvId {
					if environment.Cohorts != nil && len(environment.Cohorts) > 0 {
						for _, cohort := range environment.Cohorts {
							oldEnvCohortIdList = append(oldEnvCohortIdList, cohort.ID)
						}
					}
					existEnvCohortList = environment.Cohorts
				}
				if environment.ID == newEnvID {
					newEnvCohortList = environment.Cohorts
				}
			}
		}

		if newEnvCohortList != nil && len(newEnvCohortList) > 0 {
			for _, newCohort := range newEnvCohortList {
				if existEnvCohortList != nil && len(existEnvCohortList) > 0 {
					for _, existCohort := range existEnvCohortList {
						if newCohort.Name == existCohort.Name {
							if project.Type == 3 {
								//再随机项目--
								if (newCohort.LastID != primitive.NilObjectID && existCohort.LastID != primitive.NilObjectID) ||
									(newCohort.LastID == primitive.NilObjectID && existCohort.LastID == primitive.NilObjectID) {
									var cohortMapping models.CohortMapping
									cohortMapping.OldCohortID = existCohort.ID
									cohortMapping.NewCohortID = newCohort.ID
									cohortMappingList = append(cohortMappingList, cohortMapping)
								}
							} else {
								//群组项目--
								var cohortMapping models.CohortMapping
								cohortMapping.OldCohortID = existCohort.ID
								cohortMapping.NewCohortID = newCohort.ID
								cohortMappingList = append(cohortMappingList, cohortMapping)
							}
						}
					}
				}
			}
		}
	}

	existEnvCondition := bson.M{"customer_id": project.CustomerID, "project_id": project.ID, "env_id": existEnvId}
	newEnvCondition := bson.M{"customer_id": project.CustomerID, "project_id": project.ID, "env_id": newEnvID}
	if project.Type != 1 {
		if cohortMappingList != nil && len(cohortMappingList) > 0 {
			for _, cohortMapping := range cohortMappingList {
				existEnvCondition["cohort_id"] = cohortMapping.OldCohortID
				newEnvCondition["cohort_id"] = cohortMapping.NewCohortID
				err = barcodeGeneration(ctx, existEnvCondition, newEnvCondition, newEnvID, existEnvId, idMappingList, copyEnv)
				if err != nil {
					return errors.WithStack(err)
				}
			}
		}
	} else {
		err = barcodeGeneration(ctx, existEnvCondition, newEnvCondition, newEnvID, existEnvId, idMappingList, copyEnv)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

func barcodeGeneration(ctx *gin.Context, existEnvCondition bson.M, newEnvCondition bson.M, newEnvID primitive.ObjectID, existEnvId primitive.ObjectID, idMappingList []models.IdMapping, copyEnv string) error {

	var existEnvBarcodeRule models.BarcodeRule
	if err := tools.Database.Collection("barcode_rule").FindOne(nil, existEnvCondition).Decode(&existEnvBarcodeRule); err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	var newEnvBarcodeRule models.BarcodeRule
	if err := tools.Database.Collection("barcode_rule").FindOne(nil, newEnvCondition).Decode(&newEnvBarcodeRule); err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	if existEnvBarcodeRule.CodeRule == 1 && newEnvBarcodeRule.CodeRule == 1 {
		barcodeGroupList := make([]models.BarcodeGroup, 0)
		cus, err := tools.Database.Collection("barcode_group").Find(nil, existEnvCondition)
		if err != nil && err != mongo.ErrNoDocuments {
			return errors.WithStack(err)

		}
		if err := cus.All(nil, &barcodeGroupList); err != nil && err != mongo.ErrNoDocuments {
			return errors.WithStack(err)
		}

		if barcodeGroupList != nil && len(barcodeGroupList) > 0 {
			for _, barcodeGroup := range barcodeGroupList {
				if idMappingList != nil && len(idMappingList) > 0 {
					for _, idMapping := range idMappingList {
						if barcodeGroup.StorehouseID == idMapping.OldID {
							timestamp := int64(barcodeGroup.EffectiveTime)
							// 将Unix时间戳转换为时间类型
							t := time.Unix(timestamp, 0)
							// 格式化时间为字符串类型
							timeStr := t.Format("2006-01-02")
							err = generationBarcode(ctx, nil, newEnvBarcodeRule.CustomerID, newEnvBarcodeRule.ProjectID, newEnvBarcodeRule.EnvironmentID, newEnvBarcodeRule.CohortID, barcodeGroup.BatchNumber, len(barcodeGroup.MedicineIDs), barcodeGroup.Prefix, idMapping.NewID, timeStr, 21, copyEnv, barcodeGroup.IsPackageBarcode, barcodeGroup.BarcodeRule, barcodeGroup.PackageRule)
							if err != nil {
								return errors.WithStack(err)
							}
						}
					}
				}
			}
		}

	}

	return nil
}

func containsObjectID(medicineOrderList []models.MedicineOrder, targetID primitive.ObjectID) (models.MedicineOrder, error) {
	filteredOrderList := make([]models.MedicineOrder, 0)
	if medicineOrderList != nil && len(medicineOrderList) > 0 {
		for _, medicineOrder := range medicineOrderList {
			b := containsID(medicineOrder.Medicines, targetID)
			if b {
				filteredOrderList = append(filteredOrderList, medicineOrder)
			}
		}
	}

	if filteredOrderList != nil && len(filteredOrderList) > 0 {
		// 按照 CreatedAt 字段降序排序
		sort.Slice(filteredOrderList, func(i, j int) bool {
			timeI := time.Now().Add(-filteredOrderList[i].Meta.CreatedAt)
			timeJ := time.Now().Add(-filteredOrderList[j].Meta.CreatedAt)
			return timeI.After(timeJ)
		})

		return filteredOrderList[0], nil
	}

	return models.MedicineOrder{}, nil
}

func containsID(arr []primitive.ObjectID, targetID primitive.ObjectID) bool {
	for _, id := range arr {
		if id == targetID {
			return true
		}
	}
	return false
}

// 复制供应计划-----supply_plan\supply_plan_medicine
func copyPlan(ctx *gin.Context, sctx mongo.SessionContext, project models.Project, oldEnvID primitive.ObjectID, newEnvID primitive.ObjectID, projectSiteList []models.ProjectSite, copyEnv string) ([]models.SupplyPlan, error) {
	filter := bson.M{
		"customer_id": project.CustomerID,
		"project_id":  project.ID,
		"env_id":      oldEnvID,
	}
	supplyPlanList := make([]models.SupplyPlan, 0)
	cursor, err := tools.Database.Collection("supply_plan").Find(nil, filter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if err := cursor.All(nil, &supplyPlanList); err != nil {
		return nil, errors.WithStack(err)
	}
	OID := newEnvID

	newSupplyPlanList := make([]models.SupplyPlan, 0)

	if supplyPlanList != nil && len(supplyPlanList) > 0 {
		for _, plan := range supplyPlanList {
			supplyPlan := plan
			supplyPlan.ID = primitive.NewObjectID()
			supplyPlan.EnvironmentID = newEnvID
			siteIdList := make([]primitive.ObjectID, 0)
			if plan.SupplyPlanInfo.SiteIds != nil && len(plan.SupplyPlanInfo.SiteIds) > 0 {
				for _, siteId := range plan.SupplyPlanInfo.SiteIds {
					var oldProjectSite models.ProjectSite
					err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": siteId}).Decode(&oldProjectSite)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					if projectSiteList != nil && len(projectSiteList) > 0 {
						for _, projectSite := range projectSiteList {
							if projectSite.Number == oldProjectSite.Number && projectSite.DmpID == oldProjectSite.DmpID {
								siteIdList = append(siteIdList, projectSite.ID)
							}
						}
					}
				}
			}
			supplyPlan.SupplyPlanInfo.SiteIds = siteIdList

			_, err := tools.Database.Collection("supply_plan").InsertOne(sctx, supplyPlan)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			newSupplyPlanList = append(newSupplyPlanList, supplyPlan)

			err = insertSupplyPlanLog(ctx, sctx, supplyPlan.EnvironmentID, 10, models.SupplyPlan{}, supplyPlan, OID, copyEnv)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			condition := bson.M{
				"customer_id":    project.CustomerID,
				"project_id":     project.ID,
				"env_id":         oldEnvID,
				"supply_plan_id": plan.ID,
			}
			supplyPlanMedicineList := make([]models.SupplyPlanMedicine, 0)
			cus, err := tools.Database.Collection("supply_plan_medicine").Find(nil, condition)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if err := cus.All(nil, &supplyPlanMedicineList); err != nil {
				return nil, errors.WithStack(err)
			}

			if supplyPlanMedicineList != nil && len(supplyPlanMedicineList) > 0 {
				for _, medicine := range supplyPlanMedicineList {
					supplyPlanMedicine := medicine
					supplyPlanMedicine.ID = primitive.NewObjectID()
					supplyPlanMedicine.EnvironmentID = newEnvID
					supplyPlanMedicine.SupplyPlanID = supplyPlan.ID

					_, err := tools.Database.Collection("supply_plan_medicine").InsertOne(sctx, supplyPlanMedicine)
					if err != nil {
						return nil, errors.WithStack(err)
					}

					err = insertSupplyPlanMedicineLog(ctx, sctx, supplyPlanMedicine.EnvironmentID, 10, models.MedicinePlanInfo{}, supplyPlanMedicine.Medicine, supplyPlanMedicine.ID, supplyPlanMedicine.SupplyPlanID, copyEnv)
					if err != nil {
						return nil, errors.WithStack(err)
					}

				}
			}

		}
	}
	return newSupplyPlanList, nil
}

// 复制中心管理-----project_site
func copySite(ctx *gin.Context, sctx mongo.SessionContext, project models.Project, oldEnvID primitive.ObjectID, newEnvID primitive.ObjectID, projectStorehouseList []models.ProjectStorehouse) ([]models.ProjectSite, error) {
	filter := bson.M{
		"customer_id": project.CustomerID,
		"project_id":  project.ID,
		"env_id":      oldEnvID,
	}
	oldProjectSiteList := make([]models.ProjectSite, 0)
	projectSiteList := make([]models.ProjectSite, 0)
	cursor, err := tools.Database.Collection("project_site").Find(nil, filter)
	if err != nil {
		return nil, errors.WithStack(err)

	}
	if err := cursor.All(nil, &oldProjectSiteList); err != nil {
		return nil, errors.WithStack(err)
	}
	//OID := newEnvID
	u, _ := ctx.Get("user")
	user := u.(models.User)

	if oldProjectSiteList != nil && len(oldProjectSiteList) > 0 {
		for _, site := range oldProjectSiteList {
			projectSite := site
			projectSite.ID = primitive.NewObjectID()
			projectSite.EnvironmentID = newEnvID
			//仓库名称
			if site.SupplyPlanID != primitive.NilObjectID {
				if projectStorehouseList != nil && len(projectStorehouseList) > 0 {
					for _, newProjectStorehouse := range projectStorehouseList {
						newStoreHouseID := make([]primitive.ObjectID, 0)
						newStoreHouseID = append(newStoreHouseID, newProjectStorehouse.ID)
						projectSite.StoreHouseID = newStoreHouseID
					}
				}

			}
			projectSite.Meta = models.Meta{
				CreatedAt: time.Duration(time.Now().Unix()),
				CreatedBy: user.ID,
			}

			projectSiteList = append(projectSiteList, projectSite)

			//_, err := tools.Database.Collection("project_site").InsertOne(sctx, projectSite)
			//if err != nil {
			//	return errors.WithStack(err)
			//}
			//
			//err = insertProjectSiteLog(ctx, sctx, OID, 10, models.ProjectSite{}, projectSite)
			//if err != nil {
			//	return errors.WithStack(err)
			//}
		}
	}
	return projectSiteList, nil
}

// 复制库房管理-----project_storehouse
func copyStorehouse(ctx *gin.Context, sctx mongo.SessionContext, project models.Project, oldEnvID primitive.ObjectID, newEnvID primitive.ObjectID, copyEnv string) ([]models.ProjectStorehouse, error) {
	filter := bson.M{
		"customer_id": project.CustomerID,
		"project_id":  project.ID,
		"env_id":      oldEnvID,
		"deleted":     2,
	}
	projectStorehouseList := make([]models.ProjectStorehouse, 0)
	cursor, err := tools.Database.Collection("project_storehouse").Find(nil, filter)
	if err != nil {
		return nil, errors.WithStack(err)

	}
	if err := cursor.All(nil, &projectStorehouseList); err != nil {
		return nil, errors.WithStack(err)
	}
	OID := newEnvID
	u, _ := ctx.Get("user")
	user := u.(models.User)

	newProjectStorehouseList := make([]models.ProjectStorehouse, 0)

	if projectStorehouseList != nil && len(projectStorehouseList) > 0 {
		for _, storehouse := range projectStorehouseList {
			projectStorehouse := storehouse
			projectStorehouse.ID = primitive.NewObjectID()
			projectStorehouse.EnvironmentID = newEnvID
			projectStorehouse.Meta = models.Meta{
				CreatedAt: time.Duration(time.Now().Unix()),
				CreatedBy: user.ID,
			}

			isUsed := false
			////删除之前判断是否已被使用
			//if count, _ := tools.Database.Collection("user_depot").CountDocuments(nil, bson.M{"depot_id": storehouse.ID}); count > 0 {
			//	isUsed = true
			//}
			////有编号的
			//cond := bson.M{"env_id": storehouse.EnvironmentID, "storehouse_id": storehouse.ID}
			//if total, _ := tools.Database.Collection("medicine").CountDocuments(ctx, cond); total > 0 {
			//	isUsed = true
			//}
			////未编号的
			//if otherTotal, _ := tools.Database.Collection("medicine_other_institute").CountDocuments(ctx, cond); otherTotal > 0 {
			//	isUsed = true
			//}
			//判断是否有订单
			condition := bson.M{"env_id": storehouse.EnvironmentID}
			condition["$or"] = bson.A{
				bson.M{"receive_id": storehouse.ID},
				bson.M{"send_id": storehouse.ID},
			}
			if count, _ := tools.Database.Collection("medicine_order").CountDocuments(ctx, condition); count > 0 {
				isUsed = true
			}

			projectStorehouse.IsUsed = isUsed

			_, err := tools.Database.Collection("project_storehouse").InsertOne(sctx, projectStorehouse)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			newProjectStorehouseList = append(newProjectStorehouseList, projectStorehouse)

			err = insertStorehouseLog(ctx, sctx, projectStorehouse.EnvironmentID, 10, models.ProjectStorehouse{}, projectStorehouse, OID, copyEnv)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
	}
	return newProjectStorehouseList, nil
}

// 复制项目属性
func copyAttribute(ctx *gin.Context, sctx mongo.SessionContext, project models.Project, oldEnvID primitive.ObjectID, oldCohortID primitive.ObjectID, newEnvID primitive.ObjectID, newCohortID primitive.ObjectID, copyEnv string) error {
	filter := bson.M{"env_id": oldEnvID}
	if project.ProjectInfo.Type != 1 {
		filter["cohort_id"] = oldCohortID
	}
	var attribute models.Attribute
	_ = tools.Database.Collection("attribute").FindOne(sctx, filter).Decode(&attribute)
	if attribute.ID != primitive.NilObjectID {

		oldInfo := models.AttributeInfo{}

		attribute.ID = primitive.NewObjectID()
		attribute.EnvironmentID = newEnvID
		if project.ProjectInfo.Type == 1 {
			attribute.AttributeInfo.ConnectAli = false
			attribute.AttributeInfo.AliProjectNO = ""
		} else {
			attribute.CohortID = newCohortID
		}
		attribute.AttributeInfo.Field.ID = primitive.NewObjectID()

		condition := bson.M{"env_id": newEnvID}
		if project.ProjectInfo.Type != 1 {
			condition["cohort_id"] = newCohortID
		}
		if count, _ := tools.Database.Collection("attribute").CountDocuments(sctx, condition); count > 0 {
			var ab models.Attribute
			err := tools.Database.Collection("attribute").FindOne(ctx, condition).Decode(&ab)
			if err != nil {
				return errors.WithStack(err)
			}

			ud := bson.M{"$set": bson.M{"info": attribute.AttributeInfo}}
			_, err = tools.Database.Collection("attribute").UpdateOne(ctx, condition, ud)
			if err != nil {
				return errors.WithStack(err)
			}

			//oldInfo = ab.AttributeInfo

		} else {
			_, err := tools.Database.Collection("attribute").InsertOne(sctx, attribute)
			if err != nil {
				return errors.WithStack(err)
			}

			//oldInfo = models.AttributeInfo{}
		}

		OID := newEnvID
		if !newCohortID.IsZero() {
			OID = newCohortID
		}
		err := insertAttributeLog(ctx, sctx, OID, 10, oldInfo, attribute.AttributeInfo, "", "", copyEnv)
		if err != nil {
			return errors.WithStack(err)
		}

	}
	return nil
}

// 复制表单
func copyForm(ctx *gin.Context, sctx mongo.SessionContext, project models.Project, oldEnvID primitive.ObjectID, oldCohortID primitive.ObjectID, newEnvID primitive.ObjectID, newCohortID primitive.ObjectID, copyEnv string) (models.Form, error) {
	filter := bson.M{"env_id": oldEnvID}
	if project.ProjectInfo.Type != 1 {
		filter["cohort_id"] = oldCohortID
	}
	var form models.Form
	_ = tools.Database.Collection("form").FindOne(sctx, filter).Decode(&form)
	if form.ID != primitive.NilObjectID {
		form.ID = primitive.NewObjectID()
		form.EnvironmentID = newEnvID
		if project.ProjectInfo.Type != 1 {
			form.CohortID = newCohortID
		}
		OID := newEnvID
		if !newCohortID.IsZero() {
			OID = newCohortID
		}
		fields := make([]models.Field, 0)
		for _, field := range form.Fields {
			field.ID = primitive.NewObjectID()
			if field.Options != nil && len(field.Options) > 0 {
				optionList := make([]models.Option, 0)
				for _, option := range field.Options {
					option.Disable = false
					optionList = append(optionList, option)
				}
				field.Options = optionList
			}

			err := insertFormLog(ctx, nil, OID, 10, models.Field{}, field, form.ID, copyEnv)
			if err != nil {
				return form, errors.WithStack(err)
			}
			fields = append(fields, field)
		}
		form.Fields = fields

		condition := bson.M{"env_id": newEnvID}
		if project.ProjectInfo.Type != 1 {
			condition["cohort_id"] = newCohortID
		}
		if count, _ := tools.Database.Collection("form").CountDocuments(sctx, condition); count > 0 {

			ud := bson.M{"$set": bson.M{"fields": form.Fields}}
			_, err := tools.Database.Collection("form").UpdateOne(ctx, condition, ud)
			if err != nil {
				return form, errors.WithStack(err)
			}

		} else {
			_, err := tools.Database.Collection("form").InsertOne(sctx, form)
			if err != nil {
				return form, errors.WithStack(err)
			}
		}

	}
	return form, nil
}

// 复制随机设置
func copyRandomDesign(ctx *gin.Context, sctx mongo.SessionContext, project models.Project, oldEnvID primitive.ObjectID, oldCohortID primitive.ObjectID, newEnvID primitive.ObjectID, newCohortID primitive.ObjectID, copyEnv string) error {
	filter := bson.M{"env_id": oldEnvID}
	if project.ProjectInfo.Type != 1 {
		filter["cohort_id"] = oldCohortID
	}
	var randomDesign models.RandomDesign
	_ = tools.Database.Collection("random_design").FindOne(sctx, filter).Decode(&randomDesign)

	isCopyData := false

	oldEnv, b := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == oldEnvID
	})
	if b {
		if oldEnv.Name == "PROD" {
			//判断是否有受试者
			subjectFilter := filter
			subjectFilter["deleted"] = bson.M{"$ne": true}
			if count, _ := tools.Database.Collection("subject").CountDocuments(sctx, subjectFilter); count > 0 {
				isCopyData = true
			}

			//判断是否有订单
			if count, _ := tools.Database.Collection("medicine_order").CountDocuments(ctx, filter); count > 0 {
				isCopyData = true
			}
		}
	}

	match := bson.M{
		"env_id":  oldEnvID,
		"deleted": bson.M{"$ne": true},
	}
	regions := make([]models.Region, 0)
	cursor, err := tools.Database.Collection("region").Find(nil, match)
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &regions)
	if err != nil {
		return errors.WithStack(err)
	}

	if regions != nil && len(regions) > 0 {
		for _, region := range regions {
			region.ID = primitive.NewObjectID()
			region.EnvID = newEnvID
			condition := bson.M{
				"env_id":  newEnvID,
				"name":    region.Name,
				"deleted": bson.M{"$ne": true},
			}
			if count, _ := tools.Database.Collection("region").CountDocuments(sctx, condition); count == 0 {
				_, err := tools.Database.Collection("region").InsertOne(sctx, region)
				if err != nil {
					return errors.WithStack(err)
				}
				err = insertRegionLog(ctx, sctx, newEnvID, 1, models.Region{}, region, region.ID, copyEnv)
				if err != nil {
					return errors.WithStack(err)
				}
			}

		}
	}

	if randomDesign.ID != primitive.NilObjectID {
		randomDesign.ID = primitive.NewObjectID()
		randomDesign.EnvironmentID = newEnvID
		if project.ProjectInfo.Type != 1 {
			randomDesign.CohortID = newCohortID
		}

		// 组别
		groups := make([]models.RandomGroup, 0)
		for _, group := range randomDesign.Info.Groups {
			group.ID = primitive.NewObjectID()
			group.IsCopyData = isCopyData
			subGroupList := make([]models.SubGroup, 0)
			if group.SubGroup != nil && len(group.SubGroup) > 0 {
				for _, sub := range group.SubGroup {
					sub.IsCopyData = isCopyData
					subGroupList = append(subGroupList, sub)
				}
			}
			group.SubGroup = subGroupList
			groups = append(groups, group)
		}
		randomDesign.Info.Groups = groups

		// 分层因素
		randomFactors := make([]models.RandomFactor, 0)
		for _, factor := range randomDesign.Info.Factors {
			factor.ID = primitive.NewObjectID()
			optionList := make([]models.Option, 0)
			if factor.Options != nil && len(factor.Options) > 0 {
				for _, option := range factor.Options {
					option.IsCopyData = isCopyData
					optionList = append(optionList, option)
				}
			}
			factor.IsCopyData = isCopyData
			factor.Options = optionList
			randomFactors = append(randomFactors, factor)
		}
		randomDesign.Info.Factors = randomFactors

		// 排列组合
		randomDesign.Info.BlockNumber = 1 // 返回从1开始
		randomDesign.Info.Combination = []models.FactorsCombination{}

		oldInfo := models.RandomDesignInfo{}

		condition := bson.M{"env_id": newEnvID}
		if project.ProjectInfo.Type != 1 {
			condition["cohort_id"] = newCohortID
		}
		if count, _ := tools.Database.Collection("random_design").CountDocuments(sctx, condition); count > 0 {
			var ab models.RandomDesign
			err := tools.Database.Collection("random_design").FindOne(ctx, condition).Decode(&ab)
			if err != nil {
				return errors.WithStack(err)
			}

			//组别
			if randomDesign.Info.Groups != nil && len(randomDesign.Info.Groups) > 0 {
				groupList := make([]models.RandomGroup, 0)
				for _, group := range randomDesign.Info.Groups {
					if ab.Info.Groups != nil && len(ab.Info.Groups) > 0 {
						rg, ba := slice.Find(ab.Info.Groups, func(index int, item models.RandomGroup) bool {
							return item.Code == group.Code && item.Status == group.Status
						})
						if ba {
							group.ID = rg.ID
						}
					}
					group.IsCopyData = false
					subGroupList := make([]models.SubGroup, 0)
					if group.SubGroup != nil && len(group.SubGroup) > 0 {
						for _, sub := range group.SubGroup {
							sub.IsCopyData = false
							subGroupList = append(subGroupList, sub)
						}
					}
					group.SubGroup = subGroupList
					groupList = append(groupList, group)
				}
				randomDesign.Info.Groups = groups
			}

			//分层因素
			if randomDesign.Info.Factors != nil && len(randomDesign.Info.Factors) > 0 {
				randomFactorList := make([]models.RandomFactor, 0)
				for _, factor := range randomDesign.Info.Factors {
					if ab.Info.Factors != nil && len(ab.Info.Factors) > 0 {
						rg, ba := slice.Find(ab.Info.Factors, func(index int, item models.RandomFactor) bool {
							return item.Number == factor.Number && item.Name == factor.Name && item.Status == factor.Status
						})
						if ba {
							factor.ID = rg.ID
						}
					}
					optionList := make([]models.Option, 0)
					if factor.Options != nil && len(factor.Options) > 0 {
						for _, option := range factor.Options {
							option.IsCopyData = false
							optionList = append(optionList, option)
						}
					}
					factor.IsCopyData = false
					factor.Options = optionList
					randomFactorList = append(randomFactorList, factor)
				}
				randomDesign.Info.Factors = randomFactorList
			}

			ud := bson.M{"$set": bson.M{"info": randomDesign.Info}}
			_, err = tools.Database.Collection("random_design").UpdateOne(ctx, condition, ud)
			if err != nil {
				return errors.WithStack(err)
			}

			//oldInfo = ab.Info

		} else {
			_, err := tools.Database.Collection("random_design").InsertOne(sctx, randomDesign)
			if err != nil {
				return errors.WithStack(err)
			}

			//oldInfo = models.RandomDesignInfo{}
		}

		OID := newEnvID
		if !newCohortID.IsZero() {
			OID = newCohortID
		}
		_ = insertRandomDesignLog(ctx, sctx, OID, 10, oldInfo, randomDesign.Info, randomDesign.ID, "", project, copyEnv)
	}
	return nil
}

// 复制访视周期
func copyVisitCycle(ctx *gin.Context, sctx mongo.SessionContext, project models.Project, oldEnvID primitive.ObjectID, oldCohortID primitive.ObjectID, newEnvID primitive.ObjectID, newCohortID primitive.ObjectID, copyEnv string) (models.VisitCycle, error) {

	isOldEnvNameProd := false
	oldEnv, b := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == oldEnvID
	})
	if b {
		if oldEnv.Name == "PROD" {
			isOldEnvNameProd = true
		}
	}

	filter := bson.M{"env_id": oldEnvID}
	if project.ProjectInfo.Type != 1 {
		filter["cohort_id"] = oldCohortID
	}
	var visitCycle models.VisitCycle
	err := tools.Database.Collection("visit_cycle").FindOne(sctx, filter).Decode(&visitCycle)
	if err != nil && err != mongo.ErrNoDocuments {
		return visitCycle, errors.WithStack(err)
	}

	if visitCycle.ID != primitive.NilObjectID {
		visitCycle.ID = primitive.NewObjectID()
		visitCycle.EnvironmentID = newEnvID
		if project.ProjectInfo.Type != 1 {
			visitCycle.CohortID = newCohortID
		}
		OID := newEnvID
		if !newCohortID.IsZero() {
			OID = newCohortID
		}
		visitCycle.SetInfo.Id = primitive.NewObjectID()

		if visitCycle.SetInfo.IsOpen {
			err = insertVisitSettingLog(ctx, nil, OID, 10, models.VisitSetting{}, visitCycle.SetInfo, visitCycle.ID, copyEnv)
			if err != nil {
				return visitCycle, errors.WithStack(err)
			}
		}
		visitCycleInfos := make([]models.VisitCycleInfo, 0)
		for _, visitCycleInfo := range visitCycle.ConfigInfo.Infos {
			visitOID := visitCycleInfo.ID
			visitCycleInfo.ID = primitive.NewObjectID()
			err = insertVisitCycleLog(ctx, nil, OID, 10, models.VisitCycleInfo{}, visitCycleInfo, visitCycle.ID, project, copyEnv)
			if err != nil && err != mongo.ErrNoDocuments {
				return visitCycle, errors.WithStack(err)
			}
			isUsed := false
			if visitCycleInfo.Random {
				// 查询是否已经有随机的受试者 已经有随机的不给删除修改
				documents, err := tools.Database.Collection("subject").CountDocuments(sctx, bson.M{"group": bson.M{"$ne": ""}, "env_id": oldEnvID, "status": bson.M{"$in": bson.A{3, 4, 5, 6}}})
				if project.ProjectInfo.Type != 1 {
					documents, err = tools.Database.Collection("subject").CountDocuments(sctx, bson.M{"group": bson.M{"$ne": ""}, "env_id": oldEnvID, "cohort_id": oldCohortID, "status": bson.M{"$in": bson.A{3, 4, 5, 6}}})
				}
				if err != nil && err != mongo.ErrNoDocuments {
					return visitCycle, errors.WithStack(err)
				}
				if documents > 0 && isOldEnvNameProd {
					isUsed = true
				}
			}
			if visitCycleInfo.Dispensing {
				// 查询是否已经有发药的访视 已经有发药的不给删除修改
				dispensingFilter := bson.M{
					"env_id":                         oldEnvID,
					"visit_info.visit_cycle_info_id": visitOID,
					"status":                         bson.M{"$ne": 1},
					"$or": []bson.M{
						{"dispensing_medicines": bson.M{"$exists": true, "$ne": bson.A{}}},
						{"other_dispensing_medicines": bson.M{"$exists": true, "$ne": bson.A{}}},
					},
				}

				if project.ProjectInfo.Type != 1 {
					dispensingFilter["cohort_id"] = oldCohortID
				}
				documents, err := tools.Database.Collection("dispensing").CountDocuments(sctx, dispensingFilter)
				if err != nil && err != mongo.ErrNoDocuments {
					return visitCycle, errors.WithStack(err)
				}
				if documents > 0 && isOldEnvNameProd {
					isUsed = true
					visitCycleInfo.IsCopyEditDelete = true
				}
			}
			visitCycleInfo.IsUsed = isUsed
			visitCycleInfos = append(visitCycleInfos, visitCycleInfo)
		}
		visitCycle.ConfigInfo.Infos = visitCycleInfos
		visitCycle.Infos = visitCycleInfos
		visitCycle.HistoryInfo = nil

		condition := bson.M{"env_id": newEnvID}
		if project.ProjectInfo.Type != 1 {
			condition["cohort_id"] = newCohortID
		}
		if count, _ := tools.Database.Collection("visit_cycle").CountDocuments(sctx, condition); count > 0 {

			var newVisitCycle models.VisitCycle
			e := tools.Database.Collection("visit_cycle").FindOne(sctx, condition).Decode(&newVisitCycle)
			if e != nil && e != mongo.ErrNoDocuments {
				return visitCycle, errors.WithStack(e)
			}

			visitCycleInfoList := make([]models.VisitCycleInfo, 0)
			if visitCycle.ConfigInfo.Infos != nil && len(visitCycle.ConfigInfo.Infos) > 0 {
				for _, info := range visitCycle.ConfigInfo.Infos {
					find, b := slice.Find(newVisitCycle.ConfigInfo.Infos, func(index int, item models.VisitCycleInfo) bool {
						return item.Number == info.Number
					})
					if b {
						info.ID = find.ID
					}
					visitCycleInfoList = append(visitCycleInfoList, info)
				}
			}
			visitCycle.Infos = visitCycleInfoList
			ud := bson.M{"$set": bson.M{"infos": visitCycleInfoList, "update_infos.infos": visitCycleInfoList}}
			_, err := tools.Database.Collection("visit_cycle").UpdateOne(ctx, condition, ud)
			if err != nil && err != mongo.ErrNoDocuments {
				return visitCycle, errors.WithStack(err)
			}

		} else {

			_, err := tools.Database.Collection("visit_cycle").InsertOne(sctx, visitCycle)
			if err != nil && err != mongo.ErrNoDocuments {
				return visitCycle, errors.WithStack(err)
			}

		}

	}
	return visitCycle, nil
}

// 查询订单中的研究产品、未编号研究产品
func findOrderMedicine(ctx *gin.Context, sctx mongo.SessionContext, oldEnvID primitive.ObjectID) ([]string, []string, error) {

	medicineNameList := make([]string, 0)
	otherMedicineNameList := make([]string, 0)

	//判断是否有订单
	condition := bson.M{"env_id": oldEnvID}
	medicineOrderList := make([]models.MedicineOrder, 0)
	medicineOrderCursor, err := tools.Database.Collection("medicine_order").Find(ctx, condition)
	if err != nil {
		return medicineNameList, otherMedicineNameList, errors.WithStack(err)
	}
	err = medicineOrderCursor.All(nil, &medicineOrderList)
	if err != nil {
		return medicineNameList, otherMedicineNameList, errors.WithStack(err)
	}

	medicineIdList := make([]primitive.ObjectID, 0)
	if medicineOrderList != nil && len(medicineOrderList) > 0 {
		for _, medicineOrder := range medicineOrderList {
			if medicineOrder.Medicines != nil && len(medicineOrder.Medicines) > 0 {
				for _, medicineId := range medicineOrder.Medicines {
					medicineIdList = append(medicineIdList, medicineId)
				}
			}
			if medicineOrder.OtherMedicines != nil && len(medicineOrder.OtherMedicines) > 0 {
				for _, otherMedicine := range medicineOrder.OtherMedicines {
					index := arrays.ContainsString(otherMedicineNameList, otherMedicine.Name)
					if index == -1 {
						//-1说明不存在
						otherMedicineNameList = append(otherMedicineNameList, otherMedicine.Name)
					}
				}
			}
		}
	}

	medicineList := make([]models.Medicine, 0)
	cursor, err := tools.Database.Collection("medicine").Find(sctx, bson.M{"_id": bson.M{"$in": medicineIdList}})
	if err != nil {
		return medicineNameList, otherMedicineNameList, errors.WithStack(err)
	}
	err = cursor.All(nil, &medicineList)
	if err != nil {
		return medicineNameList, otherMedicineNameList, errors.WithStack(err)
	}

	if medicineList != nil && len(medicineList) > 0 {
		for _, medicine := range medicineList {
			index := arrays.ContainsString(medicineNameList, medicine.Name)
			if index == -1 {
				//-1说明不存在
				medicineNameList = append(medicineNameList, medicine.Name)
			}
		}
	}

	return medicineNameList, otherMedicineNameList, nil
}

// 复制研究产品配置
func copyDrugConfig(ctx *gin.Context, sctx mongo.SessionContext, project models.Project, oldEnvID primitive.ObjectID, oldCohortID primitive.ObjectID, newEnvID primitive.ObjectID, newCohortID primitive.ObjectID, copyEnv string, visitCycleInfo models.VisitCycle) (models.DrugConfigure, error) {

	isOldEnvNameProd := false

	oldEnv, b := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == oldEnvID
	})
	if b {
		if oldEnv.Name == "PROD" {
			isOldEnvNameProd = true
		}
	}

	medicineNameList, otherMedicineNameList, err := findOrderMedicine(ctx, sctx, oldEnvID)
	if err != nil {
		return models.DrugConfigure{}, errors.WithStack(err)
	}

	oldFilter := bson.M{"env_id": oldEnvID}
	if project.ProjectInfo.Type != 1 {
		oldFilter["cohort_id"] = oldCohortID
	}

	newFilter := bson.M{"env_id": newEnvID}
	if project.ProjectInfo.Type != 1 {
		newFilter["cohort_id"] = newCohortID
	}

	// 查询旧的访视周期
	var oldVisitCycle models.VisitCycle
	_ = tools.Database.Collection("visit_cycle").FindOne(sctx, oldFilter).Decode(&oldVisitCycle)

	// 查询新的访视周期
	var newVisitCycle models.VisitCycle
	_ = tools.Database.Collection("visit_cycle").FindOne(sctx, newFilter).Decode(&newVisitCycle)

	// 查询旧的研究产品配置
	var drugConfigure models.DrugConfigure
	_ = tools.Database.Collection("drug_configure").FindOne(sctx, oldFilter).Decode(&drugConfigure)

	if drugConfigure.ID != primitive.NilObjectID {
		drugConfigure.ID = primitive.NewObjectID()
		drugConfigure.EnvironmentID = newEnvID
		if project.ProjectInfo.Type != 1 {
			drugConfigure.CohortID = newCohortID
		}
		OID := newEnvID
		if !newCohortID.IsZero() {
			OID = newCohortID
		}
		drugConfigureInfos := make([]models.DrugConfigureInfo, 0)
		for _, configureInfo := range drugConfigure.Configures {
			visitCyclesIds := make([]primitive.ObjectID, 0)
			// 根据访视编号匹配访视配置里面的访视周期ID

			for _, oldVisitId := range configureInfo.VisitCycles {
				var oldVisitNumber string
				for _, oldVisit := range oldVisitCycle.Infos {
					if oldVisit.ID == oldVisitId {
						oldVisitNumber = oldVisit.Number
						break
					}
				}

				if visitCycleInfo.Infos != nil && len(visitCycleInfo.Infos) > 0 {
					for _, newVisit := range visitCycleInfo.Infos {
						if newVisit.Number == oldVisitNumber {
							visitCyclesIds = append(visitCyclesIds, newVisit.ID)
							break
						}
					}
				} else {
					for _, newVisit := range newVisitCycle.Infos {
						if newVisit.Number == oldVisitNumber {
							visitCyclesIds = append(visitCyclesIds, newVisit.ID)
							break
						}
					}
				}

				var oldVisitNameZh string
				var oldVisitNameEn string
				if oldVisitCycle.SetInfo.IsOpen {
					if oldVisitCycle.SetInfo.Id == oldVisitId {
						oldVisitNameZh = oldVisitCycle.SetInfo.NameZh
						oldVisitNameEn = oldVisitCycle.SetInfo.NameEn
					}
				}

				if newVisitCycle.SetInfo.IsOpen {
					if newVisitCycle.SetInfo.NameZh == oldVisitNameZh && newVisitCycle.SetInfo.NameEn == oldVisitNameEn {
						visitCyclesIds = append(visitCyclesIds, newVisitCycle.SetInfo.Id)
					}
				}

			}
			if configureInfo.RoutineVisitMappingList != nil && len(configureInfo.RoutineVisitMappingList) > 0 {
				routineVisitMappingList := make([]models.RoutineVisitMapping, 0)
				for _, mapping := range configureInfo.RoutineVisitMappingList {
					mapping.ID = primitive.NewObjectID()
					if mapping.VisitList != nil && len(mapping.VisitList) > 0 {
						mappinfVisitList := make([]primitive.ObjectID, 0)
						// 根据访视编号匹配访视配置里面的访视周期ID
						for _, mappingVisitId := range mapping.VisitList {
							var oldVisitNumber string
							for _, oldVisit := range oldVisitCycle.Infos {
								if oldVisit.ID == mappingVisitId {
									oldVisitNumber = oldVisit.Number
									break
								}
							}

							if visitCycleInfo.Infos != nil && len(visitCycleInfo.Infos) > 0 {
								for _, newVisit := range visitCycleInfo.Infos {
									if newVisit.Number == oldVisitNumber {
										mappinfVisitList = append(mappinfVisitList, newVisit.ID)
										break
									}
								}
							}
						}
						mapping.VisitList = mappinfVisitList
					}
					mappingDrugList := make([]models.Drug, 0)
					if mapping.DrugList != nil && len(mapping.DrugList) > 0 {
						for _, drug := range mapping.DrugList {
							drug.ID = primitive.NewObjectID()
							mappingDrugList = append(mappingDrugList, drug)
						}
					}
					mapping.DrugList = mappingDrugList
					routineVisitMappingList = append(routineVisitMappingList, mapping)
				}
				configureInfo.RoutineVisitMappingList = routineVisitMappingList
			}
			configureInfo.ID = primitive.NewObjectID()
			configureInfo.VisitCycles = visitCyclesIds
			err := insertDrugConfigureLog(ctx, nil, OID, 10, models.DrugConfigureInfo{}, configureInfo, OID, drugConfigure.EnvironmentID, drugConfigure.CohortID, copyEnv, visitCycleInfo)
			if err != nil {
				return drugConfigure, errors.WithStack(err)
			}
			drugConfigureInfos = append(drugConfigureInfos, configureInfo)
		}

		if drugConfigureInfos != nil && len(drugConfigureInfos) > 0 {
			drugConfigureInfoList := make([]models.DrugConfigureInfo, 0)
			for _, info := range drugConfigureInfos {
				isCopyEditDelete := false
				drugConfigureInfo := info
				if info.Values != nil && len(info.Values) > 0 {
					drugValueList := make([]models.DrugValue, 0)
					for _, value := range info.Values {
						drugValue := value
						if value.IsOther {
							//未编号研究产品
							index := arrays.ContainsString(otherMedicineNameList, value.DrugName)
							if index != -1 && isOldEnvNameProd {
								//-1说明不存在
								drugValue.IsCopyEditDelete = true
								isCopyEditDelete = true
							} else {
								drugValue.IsCopyEditDelete = false
							}
						} else {
							//研究产品
							index := arrays.ContainsString(medicineNameList, value.DrugName)
							if index != -1 && isOldEnvNameProd {
								//-1说明不存在
								drugValue.IsCopyEditDelete = true
								isCopyEditDelete = true
							} else {
								drugValue.IsCopyEditDelete = false
							}
						}
						drugValueList = append(drugValueList, drugValue)
					}
					drugConfigureInfo.Values = drugValueList
					drugConfigureInfo.IsCopyEditDelete = isCopyEditDelete
				}
				drugConfigureInfoList = append(drugConfigureInfoList, drugConfigureInfo)
			}
			drugConfigureInfos = drugConfigureInfoList
		}

		drugConfigure.Configures = drugConfigureInfos

		condition := bson.M{"env_id": newEnvID}
		if project.ProjectInfo.Type != 1 {
			condition["cohort_id"] = newCohortID
		}
		if count, _ := tools.Database.Collection("drug_configure").CountDocuments(sctx, condition); count > 0 {
			ud := bson.M{"$set": bson.M{"configures": drugConfigure.Configures}}
			_, err := tools.Database.Collection("drug_configure").UpdateOne(ctx, condition, ud)
			if err != nil {
				return drugConfigure, errors.WithStack(err)
			}

		} else {

			_, err := tools.Database.Collection("drug_configure").InsertOne(sctx, drugConfigure)
			if err != nil {
				return drugConfigure, errors.WithStack(err)
			}

		}

	}
	return drugConfigure, nil
}

// 复制研究产品配置-设置
func copyDrugConfigSetting(ctx *gin.Context, sctx mongo.SessionContext, project models.Project, oldEnvID primitive.ObjectID, oldCohortID primitive.ObjectID, newEnvID primitive.ObjectID, newCohortID primitive.ObjectID, targetForm models.Form, targetDrugConfigure models.DrugConfigure, copyEnv string) error {
	oldFilter := bson.M{"env_id": oldEnvID}
	if project.ProjectInfo.Type != 1 {
		oldFilter["cohort_id"] = oldCohortID
	}

	OID := newEnvID
	if project.ProjectInfo.Type != 1 {
		OID = newCohortID
	}

	// 查询旧的研究产品配置
	var drugConfigureSetting models.DrugConfigureSetting
	err := tools.Database.Collection("drug_configure_setting").FindOne(sctx, oldFilter).Decode(&drugConfigureSetting)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	if count, _ := tools.Database.Collection("drug_configure_setting").CountDocuments(ctx, oldFilter); count == 0 {
		return nil
	}

	var newDrugConfigureSetting models.DrugConfigureSetting
	newDrugConfigureSetting.ID = primitive.NewObjectID()
	newDrugConfigureSetting.CustomerID = drugConfigureSetting.CustomerID
	newDrugConfigureSetting.ProjectID = drugConfigureSetting.ProjectID
	newDrugConfigureSetting.EnvironmentID = newEnvID
	newDrugConfigureSetting.CohortID = newCohortID
	newDrugConfigureSetting.IsOpen = drugConfigureSetting.IsOpen
	newDrugConfigureSetting.SelectType = drugConfigureSetting.SelectType
	newDrugConfigureSetting.DtpIpList = drugConfigureSetting.DtpIpList

	match := bson.M{
		"project_id":  drugConfigureSetting.ProjectID,
		"env_id":      oldEnvID,
		"customer_id": drugConfigureSetting.CustomerID,
	}
	if project.ProjectInfo.Type != 1 {
		match["cohort_id"] = oldCohortID
	}

	var form models.Form
	if err := tools.Database.Collection("form").FindOne(ctx, match).Decode(&form); err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}
	doseFormName := ""
	if form.Fields != nil && len(form.Fields) > 0 {
		for _, field := range form.Fields {
			if field.ID.Hex() == drugConfigureSetting.DoseFormId {
				doseFormName = field.Variable
			}
		}
	}

	targetDoseFormId := ""
	filter := bson.M{
		"project_id":  drugConfigureSetting.ProjectID,
		"env_id":      newEnvID,
		"customer_id": drugConfigureSetting.CustomerID,
	}
	if project.ProjectInfo.Type != 1 {
		filter["cohort_id"] = newCohortID
	}
	//var targetForm models.Form
	//if err := tools.Database.Collection("form").FindOne(ctx, filter).Decode(&targetForm); err != nil && err != mongo.ErrNoDocuments {
	//	return errors.WithStack(err)
	//}
	if targetForm.Fields != nil && len(targetForm.Fields) > 0 {
		for _, field := range targetForm.Fields {
			if field.Variable == doseFormName {
				targetDoseFormId = field.ID.Hex()
			}
		}
	}
	newDrugConfigureSetting.DoseFormId = targetDoseFormId

	newDrugConfigureSetting.IsFirstInitial = drugConfigureSetting.IsFirstInitial
	newDrugConfigureSetting.IsDoseReduction = drugConfigureSetting.IsDoseReduction
	newDrugConfigureSetting.Frequency = drugConfigureSetting.Frequency

	//var targetDrugConfigure models.DrugConfigure
	//err := tools.Database.Collection("drug_configure").FindOne(nil, filter).Decode(&targetDrugConfigure)
	//if err != nil && err != mongo.ErrNoDocuments {
	//	return errors.WithStack(err)
	//}

	targetDoseLevelList := make([]models.DoseLevel, 0)
	if drugConfigureSetting.DoseLevelList != nil && len(drugConfigureSetting.DoseLevelList) > 0 {
		for _, doseLevel := range drugConfigureSetting.DoseLevelList {
			var targetDoseLevel models.DoseLevel
			targetDoseLevel.ID = primitive.NewObjectID()
			targetDoseLevel.Name = doseLevel.Name
			targetDoseLevel.Group = doseLevel.Group
			targetDoseDistribution := make([]string, 0)
			if doseLevel.DoseDistribution != nil && len(doseLevel.DoseDistribution) > 0 {
				for _, doseDistribution := range doseLevel.DoseDistribution {
					var jsonObject map[string]interface{}
					json.Unmarshal([]byte(doseDistribution), &jsonObject)
					//fmt.Println(jsonObject)
					name := jsonObject["name"].(string)
					if targetDrugConfigure.Configures != nil && len(targetDrugConfigure.Configures) > 0 {
						for _, configure := range targetDrugConfigure.Configures {
							if doseLevel.Group != nil && len(doseLevel.Group) > 0 {
								index := arrays.ContainsString(doseLevel.Group, configure.Group)
								if index != -1 {
									if configure.OpenSetting == 1 {
										//按标签配置
										if len(configure.Label) > 0 {
											if name == configure.Label {
												var doseLabel models.DoseLabel
												doseLabel.ID = configure.ID
												doseLabel.Name = configure.Label
												jsonString, e := json.Marshal(doseLabel)
												if e != nil {
													return errors.WithStack(e)
												}
												whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
												if whether == -1 {
													targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
												}
											}
										} else {
											if configure.Values != nil && len(configure.Values) != 0 {
												for _, drugValue := range configure.Values {
													if len(drugValue.Label) != 0 {
														if name == drugValue.Label {
															var doseLabel models.DoseLabel
															doseLabel.ID = configure.ID
															doseLabel.Name = drugValue.Label
															jsonString, e := json.Marshal(doseLabel)
															if e != nil {
																return errors.WithStack(e)
															}
															whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
															if whether == -1 {
																targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
															}
														}
													}
												}
											}
										}
									} else if configure.OpenSetting == 2 {
										//开放配置
										if len(configure.Label) != 0 {
											if name == configure.Label {
												var doseLabel models.DoseLabel
												doseLabel.ID = configure.ID
												doseLabel.Name = configure.Label

												jsonString, e := json.Marshal(doseLabel)
												if e != nil {
													return errors.WithStack(e)
												}
												whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
												if whether == -1 {
													targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
												}
											}
										} else {
											if configure.Values != nil && len(configure.Values) != 0 {
												for _, drugValue := range configure.Values {
													drug := ""
													if len(drugValue.DrugSpec) != 0 {
														drug = drugValue.DrugName + "/" + drugValue.CustomDispensingNumber + "/" + drugValue.DrugSpec
													} else {
														drug = drugValue.DrugName + "/" + drugValue.CustomDispensingNumber + "/"
													}

													if name == drug {
														var doseLabel models.DoseLabel
														doseLabel.ID = configure.ID
														doseLabel.Name = drug

														jsonString, e := json.Marshal(doseLabel)
														if e != nil {
															return errors.WithStack(e)
														}
														whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
														if whether == -1 {
															targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
														}
													}
												}
											}
										}

									}
								}
							}
						}
					}
				}
			}
			targetDoseLevel.DoseDistribution = targetDoseDistribution
			targetDoseLevel.InitialDose = doseLevel.InitialDose
			targetDoseLevel.IsUsed = false
			targetDoseLevelList = append(targetDoseLevelList, targetDoseLevel)
		}
	}
	newDrugConfigureSetting.DoseLevelList = targetDoseLevelList

	targetVisitJudgmentList := make([]models.VisitJudgment, 0)
	if drugConfigureSetting.VisitJudgmentList != nil && len(drugConfigureSetting.VisitJudgmentList) > 0 {
		for _, visitJudgment := range drugConfigureSetting.VisitJudgmentList {
			var targetVisitJudgment models.VisitJudgment
			targetVisitJudgment.ID = primitive.NewObjectID()
			targetVisitJudgment.Name = visitJudgment.Name
			targetVisitJudgment.Group = visitJudgment.Group
			targetDoseDistribution := make([]string, 0)
			if visitJudgment.DoseDistribution != nil && len(visitJudgment.DoseDistribution) > 0 {
				for _, doseDistribution := range visitJudgment.DoseDistribution {
					var jsonObject map[string]interface{}
					json.Unmarshal([]byte(doseDistribution), &jsonObject)
					//fmt.Println(jsonObject)
					name := jsonObject["name"].(string)
					if targetDrugConfigure.Configures != nil && len(targetDrugConfigure.Configures) > 0 {
						for _, configure := range targetDrugConfigure.Configures {
							if visitJudgment.Group != nil && len(visitJudgment.Group) > 0 {
								index := arrays.ContainsString(visitJudgment.Group, configure.Group)
								if index != -1 {
									if configure.OpenSetting == 1 {
										//按标签配置
										if len(configure.Label) > 0 {
											if name == configure.Label {
												var doseLabel models.DoseLabel
												doseLabel.ID = configure.ID
												doseLabel.Name = configure.Label
												jsonString, e := json.Marshal(doseLabel)
												if e != nil {
													return errors.WithStack(e)
												}
												whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
												if whether == -1 {
													targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
												}
											}
										} else {
											if configure.Values != nil && len(configure.Values) != 0 {
												for _, drugValue := range configure.Values {
													if len(drugValue.Label) != 0 {
														if name == drugValue.Label {
															var doseLabel models.DoseLabel
															doseLabel.ID = configure.ID
															doseLabel.Name = drugValue.Label
															jsonString, e := json.Marshal(doseLabel)
															if e != nil {
																return errors.WithStack(e)
															}
															whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
															if whether == -1 {
																targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
															}
														}
													}
												}
											}
										}
									} else if configure.OpenSetting == 2 {
										//开放配置
										if len(configure.Label) != 0 {
											if name == configure.Label {
												var doseLabel models.DoseLabel
												doseLabel.ID = configure.ID
												doseLabel.Name = configure.Label

												jsonString, e := json.Marshal(doseLabel)
												if e != nil {
													return errors.WithStack(e)
												}
												whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
												if whether == -1 {
													targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
												}
											}
										} else {
											if configure.Values != nil && len(configure.Values) != 0 {
												for _, drugValue := range configure.Values {
													drug := ""
													if len(drugValue.DrugSpec) != 0 {
														drug = drugValue.DrugName + "/" + drugValue.CustomDispensingNumber + "/" + drugValue.DrugSpec
													} else {
														drug = drugValue.DrugName + "/" + drugValue.CustomDispensingNumber + "/"
													}

													if name == drug {
														var doseLabel models.DoseLabel
														doseLabel.ID = configure.ID
														doseLabel.Name = drug

														jsonString, e := json.Marshal(doseLabel)
														if e != nil {
															return errors.WithStack(e)
														}
														whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
														if whether == -1 {
															targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
														}
													}
												}
											}
										}

									}
								}
							}
						}
					}
				}
			}
			targetVisitJudgment.DoseDistribution = targetDoseDistribution
			targetVisitJudgment.VisitInheritance = visitJudgment.VisitInheritance
			targetVisitJudgment.IsUsed = false
			targetVisitJudgmentList = append(targetVisitJudgmentList, targetVisitJudgment)
		}
	}
	newDrugConfigureSetting.VisitJudgmentList = targetVisitJudgmentList

	logOID := newDrugConfigureSetting.EnvironmentID
	if newDrugConfigureSetting.CohortID != primitive.NilObjectID {
		logOID = newDrugConfigureSetting.CohortID
	}

	condition := bson.M{"env_id": newEnvID}
	if project.ProjectInfo.Type != 1 {
		condition["cohort_id"] = newCohortID
	}
	if count, _ := tools.Database.Collection("drug_configure_setting").CountDocuments(sctx, condition); count > 0 {

		edit := bson.M{"is_open": newDrugConfigureSetting.IsOpen, "select_type": newDrugConfigureSetting.SelectType}

		edit["dose_form_id"] = newDrugConfigureSetting.DoseFormId
		edit["is_first_initial"] = newDrugConfigureSetting.IsFirstInitial
		edit["is_dose_reduction"] = newDrugConfigureSetting.IsDoseReduction
		edit["frequency"] = newDrugConfigureSetting.Frequency
		edit["dose_level_list"] = newDrugConfigureSetting.DoseLevelList
		edit["visit_judgment_list"] = newDrugConfigureSetting.VisitJudgmentList
		edit["form_record_list"] = newDrugConfigureSetting.FormRecordList

		change := bson.M{"$set": edit}
		_, err := tools.Database.Collection("drug_configure_setting").UpdateOne(ctx, condition, change)
		if err != nil {
			return errors.WithStack(err)
		}
		err = insertDrugConfigureSettingLog(ctx, nil, logOID, 10, models.DrugConfigureSetting{}, newDrugConfigureSetting, OID, newEnvID, newCohortID, copyEnv)
		if err != nil {
			return errors.WithStack(err)
		}

	} else {

		_, err := tools.Database.Collection("drug_configure_setting").InsertOne(ctx, newDrugConfigureSetting)
		if err != nil {
			return errors.WithStack(err)
		}
		err = insertDrugConfigureSettingLog(ctx, nil, logOID, 10, models.DrugConfigureSetting{}, newDrugConfigureSetting, OID, newEnvID, newCohortID, copyEnv)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

// 复制研究产品包装配置
func copyDrugPackageConfigure(ctx *gin.Context, sctx mongo.SessionContext, project models.Project, oldEnvID primitive.ObjectID, newEnvID primitive.ObjectID) error {
	filter := bson.M{"customer_id": project.CustomerID, "project_id": project.ID, "env_id": oldEnvID}
	var oldDrugPackageConfigure models.DrugPackageConfigure
	e := tools.Database.Collection("drug_package_configure").FindOne(nil, filter).Decode(&oldDrugPackageConfigure)
	if e != nil && e != mongo.ErrNoDocuments {
		return errors.WithStack(e)
	}

	drugPackageConfigure := models.DrugPackageConfigure{
		ID:                     primitive.NewObjectID(),
		CustomerID:             oldDrugPackageConfigure.CustomerID,
		ProjectID:              oldDrugPackageConfigure.ProjectID,
		EnvironmentID:          newEnvID,
		IsOpen:                 oldDrugPackageConfigure.IsOpen,
		MixedPackage:           oldDrugPackageConfigure.MixedPackage,
		IsOpenUnProvideDate:    oldDrugPackageConfigure.IsOpenUnProvideDate,
		UnProvideDateConfig:    oldDrugPackageConfigure.UnProvideDateConfig,
		OrderApprovalName:      oldDrugPackageConfigure.OrderApprovalName,
		IsOpenApplication:      oldDrugPackageConfigure.IsOpenApplication,
		SupplyRatio:            oldDrugPackageConfigure.SupplyRatio,
		OrderApplicationConfig: oldDrugPackageConfigure.OrderApplicationConfig,
	}

	condition := bson.M{"env_id": newEnvID}

	if count, _ := tools.Database.Collection("drug_package_configure").CountDocuments(sctx, condition); count > 0 {
		updatePackageConfig := bson.M{"$set": bson.M{"is_open": oldDrugPackageConfigure.IsOpen, "mixed_package": oldDrugPackageConfigure.MixedPackage, "is_open_un_provide_date": oldDrugPackageConfigure.IsOpenUnProvideDate,
			"un_provide_date_config": oldDrugPackageConfigure.UnProvideDateConfig, "order_approval_name": oldDrugPackageConfigure.OrderApprovalName,
			"is_open_application": oldDrugPackageConfigure.IsOpenApplication, "is_supply_ratio": oldDrugPackageConfigure.SupplyRatio, "order_application_config": oldDrugPackageConfigure.OrderApplicationConfig}}
		_, err := tools.Database.Collection("drug_package_configure").UpdateOne(ctx, condition, updatePackageConfig)
		if err != nil {
			return errors.WithStack(err)
		}

	} else {
		_, err := tools.Database.Collection("drug_package_configure").InsertOne(sctx, drugPackageConfigure)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil

}

// 复制编码配置
func copyCodeRule(ctx *gin.Context, sctx mongo.SessionContext, project models.Project, oldEnvID primitive.ObjectID, oldCohortID primitive.ObjectID, newEnvID primitive.ObjectID, newCohortID primitive.ObjectID, copyEnv string) error {
	filter := bson.M{"customer_id": project.CustomerID, "project_id": project.ID, "env_id": oldEnvID}
	if oldCohortID != primitive.NilObjectID {
		filter["cohort_id"] = oldCohortID
	}
	var oldBarcodeRule models.BarcodeRule
	e := tools.Database.Collection("barcode_rule").FindOne(nil, filter).Decode(&oldBarcodeRule)
	if e != nil && e != mongo.ErrNoDocuments {
		return errors.WithStack(e)
	}

	barcodeRule := models.BarcodeRule{
		ID:             primitive.NewObjectID(),
		CustomerID:     project.CustomerID,
		ProjectID:      project.ID,
		EnvironmentID:  newEnvID,
		CohortID:       newCohortID,
		CodeRule:       oldBarcodeRule.CodeRule,
		CodeConfigInit: oldBarcodeRule.CodeConfigInit,
	}

	condition := bson.M{"env_id": newEnvID}
	if project.ProjectInfo.Type != 1 {
		condition["cohort_id"] = newCohortID
	}
	if count, _ := tools.Database.Collection("barcode_rule").CountDocuments(sctx, condition); count > 0 {

		ud := bson.M{"$set": bson.M{"code_rule": barcodeRule.CodeRule, "code_config_init": barcodeRule.CodeConfigInit}}
		_, err := tools.Database.Collection("barcode_rule").UpdateOne(ctx, condition, ud)
		if err != nil {
			return errors.WithStack(err)
		}

	} else {
		_, err := tools.Database.Collection("barcode_rule").InsertOne(sctx, barcodeRule)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	//if barcodeRule.CodeConfigInit {
	//	// 保存项目日志
	//	oldField := models.OperationLogField{
	//		Type:  6,
	//		Value: "",
	//	}
	//	var OperationLogFieldGroups []models.OperationLogFieldGroup
	//	if copyEnv != "" {
	//		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
	//			Key:     "barcode.edit",
	//			TranKey: "operation_log.barcode.env_name",
	//			Old: models.OperationLogField{
	//				Type:  2,
	//				Value: nil,
	//			},
	//			New: models.OperationLogField{
	//				Type:  2,
	//				Value: copyEnv,
	//			},
	//		})
	//	}
	//	tranKey := "operation_log.barcode.random"
	//	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
	//		Key:     "barcode.edit",
	//		TranKey: tranKey,
	//		Old:     oldField,
	//		New: models.OperationLogField{
	//			Type:  6,
	//			Value: barcodeRule.CodeRule,
	//		},
	//	})
	//	OID := barcodeRule.EnvironmentID
	//	if !barcodeRule.CohortID.IsZero() {
	//		OID = barcodeRule.CohortID
	//	}
	//	err := tools.SaveOperation(ctx, sctx, "operation_log.module.barcode", OID, 10, OperationLogFieldGroups, []models.Mark{}, barcodeRule.ID)
	//	if err != nil {
	//		return errors.WithStack(err)
	//	}
	//}

	return nil

}

// 复制通知配置
func copyNoticeConfig(ctx *gin.Context, sctx mongo.SessionContext, project models.Project, oldEnvID primitive.ObjectID, newEnvID primitive.ObjectID, copyEnv string, oldEnvName string, newCopyEnv string) error {
	noticeConfig := make([]models.NoticeConfig, 0)
	cursor, err := tools.Database.Collection("notice_config").Find(sctx, bson.M{"env_id": oldEnvID})
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &noticeConfig)
	if err != nil {
		return errors.WithStack(err)
	}

	insertData := make([]interface{}, 0)
	newNoticeConfig := make([]models.NoticeConfig, 0)
	stringList := make([]string, 0)
	for _, m := range noticeConfig {
		nc := m
		nc.ID = primitive.NewObjectID()
		nc.EnvironmentID = newEnvID
		nc.ExcludeRecipientList = stringList
		nc.UnbindEmailList = stringList
		insertData = append(insertData, nc)
		newNoticeConfig = append(newNoticeConfig, nc)
	}
	if len(insertData) > 0 {

		if oldEnvName != "PROD" || newCopyEnv == "PROD" {
			//先清空
			// 删除多个文档
			_, err = tools.Database.Collection("notice_config").DeleteMany(sctx, bson.M{"env_id": newEnvID})
			if err != nil && err != mongo.ErrNoDocuments {
				return errors.WithStack(err)
			}
		}

		_, err = tools.Database.Collection("notice_config").InsertMany(sctx, insertData)
		if err != nil {
			return errors.WithStack(err)
		}
		OID := newEnvID
		for _, nc := range newNoticeConfig {
			err = insertNotificationsLog(ctx, sctx, nc.Key, OID, 10, models.NoticeConfig{}, nc, copyEnv)
		}
	}

	return nil

}

func (s *ProjectService) DownloadProjectEnvironmentUserData(ctx *gin.Context, projectID string, envID string) error {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)

	var project models.Project
	if err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project); err != nil {
		return errors.WithStack(err)
	}

	match := bson.M{"project_id": projectOID, "env_id": envOID}
	var d []map[string]interface{}
	lookupPipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"$and": bson.A{
				bson.M{"$expr": bson.M{"$eq": bson.A{"$user_id", "$$user_id"}}},
				bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", envOID}}},
			},
		}}},
	}
	sitePipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"$and": bson.A{
				bson.M{"$expr": bson.M{"$in": bson.A{"$_id", "$$siteId"}}},
				bson.M{"$expr": bson.M{"$eq": bson.A{"$deleted", 2}}},
			},
		}}},
		{{"$project", bson.M{
			"_id":    1,
			"number": 1,
			"name":   models.ProjectSiteNameBson(ctx),
		}}},
	}
	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "user_id", "foreignField": "_id", "as": "user"}}},
		{{Key: "$unwind", Value: "$user"}},
		{{Key: "$lookup", Value: bson.M{"from": "project_role_permission", "localField": "roles", "foreignField": "_id", "as": "roles"}}},
		{{Key: "$lookup", Value: bson.M{"from": "user_site", "let": bson.M{"env_id": "$env_id", "user_id": "$user_id"}, "pipeline": lookupPipeline, "as": "userSites"}}},
		{{Key: "$lookup", Value: bson.M{"from": "project_site", "let": bson.M{"siteId": "$userSites.site_id"}, "pipeline": sitePipeline, "as": "sites"}}},
		{{Key: "$lookup", Value: bson.M{"from": "user_depot", "let": bson.M{"env_id": "$env_id", "user_id": "$user_id"}, "pipeline": lookupPipeline, "as": "userDepots"}}},
		{{Key: "$lookup", Value: bson.M{"from": "project_storehouse", "localField": "userDepots.depot_id", "foreignField": "_id", "as": "projectDepots"}}},
		{{Key: "$lookup", Value: bson.M{"from": "storehouse", "localField": "projectDepots.storehouse_id", "foreignField": "_id", "as": "depots"}}},
		{{
			Key: "$project",
			Value: bson.M{
				"_id":         0,
				"id":          "$user._id",
				"email":       "$user.info.email",
				"cloudId":     "$user.cloud_id",
				"roles._id":   1,
				"roles.name":  1,
				"roles.scope": 1,
				"sites":       1,
				"depots":      1,
				"createAt":    "$meta.created_at",
			},
		}},
	}
	cursor, err := tools.Database.Collection("user_project_environment").Aggregate(nil, pipepine)
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &d)
	if err != nil {
		return errors.WithStack(err)
	}

	//写入excel文件
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	index := f.NewSheet("Sheet1")
	f.SetActiveSheet(index)
	f.SetCellValue("Sheet1", "A1", locales.Tr(ctx, "user.name"))
	f.SetCellValue("Sheet1", "B1", locales.Tr(ctx, "user.email"))
	f.SetCellValue("Sheet1", "C1", locales.Tr(ctx, "user.createDate"))
	f.SetCellValue("Sheet1", "D1", locales.Tr(ctx, "user.status"))
	f.SetCellValue("Sheet1", "E1", locales.Tr(ctx, "user.roles"))
	f.SetCellValue("Sheet1", "F1", locales.Tr(ctx, "user.site"))
	f.SetCellValue("Sheet1", "G1", locales.Tr(ctx, "user.depot"))

	cloudIds := slice.Map(d, func(index int, item map[string]interface{}) string {
		return item["cloudId"].(primitive.ObjectID).Hex()
	})
	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: cloudIds}, locales.Lang(ctx))
	if err != nil {
		return errors.WithStack(err)
	}
	m := make(map[string]*models.UserData)
	for _, u := range users {
		m[u.Info.Email] = u
	}
	axisIndex := 2
	for i := 0; i < len(d); i++ {
		email := d[i]["email"].(string)
		userData := m[email]
		f.SetCellValue("Sheet1", "A"+strconv.Itoa(axisIndex), userData.Info.Name)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(axisIndex), userData.Info.Email)
		if d[i]["createAt"] != nil {
			createAt := d[i]["createAt"].(int64)
			timeZone, err := tools.GetTimeZone(projectOID)
			if err != nil {
				return errors.WithStack(err)
			}
			hour := time.Duration(timeZone)
			minute := time.Duration((timeZone - float64(hour)) * 60)
			duration := hour*time.Hour + minute*time.Minute
			createDate := time.Unix(createAt, 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
			f.SetCellValue("Sheet1", "C"+strconv.Itoa(axisIndex), createDate)
		}
		status := userData.Status
		var statusStr string
		switch status {
		case 0:
			statusStr = locales.Tr(ctx, "user.status.not.active")
		case 1:
			statusStr = locales.Tr(ctx, "user.status.enable")
		case 2:
			statusStr = locales.Tr(ctx, "user.status.disable")
		default:
			statusStr = ""
		}
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(axisIndex), statusStr)
		var roles primitive.A
		if d[i]["roles"] != nil {
			roles = d[i]["roles"].(primitive.A)
		}
		var sites primitive.A
		if d[i]["sites"] != nil {
			sites = d[i]["sites"].(primitive.A)
		}
		var depots primitive.A
		if d[i]["depots"] != nil {
			depots = d[i]["depots"].(primitive.A)
		}
		max := 1
		if len(roles) > max {
			max = len(roles)
		}
		if len(sites) > max {
			max = len(sites)
		}
		if len(depots) > max {
			max = len(depots)
		}

		f.MergeCell("Sheet1", "A"+strconv.Itoa(axisIndex), "A"+strconv.Itoa(axisIndex+max-1))
		f.MergeCell("Sheet1", "B"+strconv.Itoa(axisIndex), "B"+strconv.Itoa(axisIndex+max-1))
		f.MergeCell("Sheet1", "C"+strconv.Itoa(axisIndex), "C"+strconv.Itoa(axisIndex+max-1))
		f.MergeCell("Sheet1", "D"+strconv.Itoa(axisIndex), "D"+strconv.Itoa(axisIndex+max-1))
		f.SetColWidth("Sheet1", "A", "G", 25)
		for m := 0; m < len(roles); m++ {
			role := roles[m].(map[string]interface{})
			f.SetCellValue("Sheet1", "E"+strconv.Itoa(axisIndex+m), role["name"])
		}

		for n := 0; n < len(sites); n++ {
			site := sites[n].(map[string]interface{})
			f.SetCellValue("Sheet1", "F"+strconv.Itoa(axisIndex+n), site["name"])
		}

		for o := 0; o < len(depots); o++ {
			depot := depots[o].(map[string]interface{})
			f.SetCellValue("Sheet1", "G"+strconv.Itoa(axisIndex+o), depot["name"])
		}

		axisIndex = axisIndex + max

	}
	if err := f.SaveAs("users.xlsx"); err != nil {
		return errors.WithStack(err)
	}
	defer os.Remove("users.xlsx")
	ctx.Header("Content-Type",
		"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.FileAttachment("users.xlsx", "users.xlsx")
	return nil
}

func (s *ProjectService) ResendInviteEmail(ctx *gin.Context, customerID string, userID string, envID string, emailLanguage string) error {
	//判断该用户是否已激活
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		userOID, _ := primitive.ObjectIDFromHex(userID)
		la := ctx.Request.Header.Get("Accept-Language")
		var user models.User
		err := tools.Database.Collection("user").FindOne(sctx, bson.M{"_id": userOID}).Decode(&user)
		if err != nil {
			ctx.Request.Header.Set("Accept-Language", la)
			return nil, tools.BuildServerError(ctx, "user.no.exist")
		} else {
			if len(emailLanguage) != 0 {
				ctx.Request.Header.Set("Accept-Language", emailLanguage)
			}
			userFetchResp, e := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))
			if e != nil {
				return nil, errors.WithStack(e)
			}
			userData := userFetchResp[0]
			if userData.Status == 1 {
				ctx.Request.Header.Set("Accept-Language", la)
				return nil, tools.BuildServerError(ctx, "user.resend.email.info")
			} else {
				if len(emailLanguage) != 0 {
					ctx.Request.Header.Set("Accept-Language", emailLanguage)
				}
				_, err = tools.UserInvite(&models.UserInviteRequest{Email: userData.Info.Email, CustomerId: customerID, Admin: false}, locales.Lang(ctx))
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}
			if len(emailLanguage) != 0 {
				ctx.Request.Header.Set("Accept-Language", emailLanguage)
			}
			if envID != "" {
				envOID, _ := primitive.ObjectIDFromHex(envID)
				filter := bson.M{
					"env_id":  envOID,
					"user_id": userOID,
				}
				var userProjectEnv models.UserProjectEnvironment
				err := tools.Database.Collection("user_project_environment").FindOne(sctx, filter).Decode(&userProjectEnv)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = insertUnbindInviteProjectUserLog(ctx, sctx, envOID, 12, 1, 0, user.Email, userProjectEnv.ID, emailLanguage)
				if err != nil {
					return nil, err
				}
			}

			return nil, nil
		}
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *ProjectService) UpdateLockConfig(ctx *gin.Context, projectID string, envID string, environment models.Environment) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		collection := tools.Database.Collection("project")
		projectOID, _ := primitive.ObjectIDFromHex(projectID)
		envOID, _ := primitive.ObjectIDFromHex(envID)

		// 修改操作
		update := bson.M{
			"$set": bson.M{
				"envs.$[env].lockConfig": environment.LockConfig,
			},
		}
		opts := &options.UpdateOptions{
			ArrayFilters: &options.ArrayFilters{
				Filters: bson.A{bson.M{"env.id": envOID}},
			},
		}
		if _, err := collection.UpdateOne(sctx, bson.M{"_id": projectOID}, update, opts); err != nil {
			return nil, errors.WithStack(err)
		}

		//插入锁定解锁日志
		insertLockProjectEnvLog(ctx, sctx, projectOID, !environment.LockConfig, environment.LockConfig, projectOID, environment.Name)
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *ProjectService) GetList(ctx *gin.Context) ([]*models.Project, error) {
	user, err := tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var envUsers []models.UserProjectEnvironment
	cursor, err := tools.Database.Collection("user_project_environment").Find(context.Background(), bson.M{
		"user_id": user.ID,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(context.Background(), &envUsers)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	envUsers = slice.Filter(envUsers, func(index int, item models.UserProjectEnvironment) bool {
		return item.Unbind == false
	})

	projectIdSet := tools.SetFactory()
	envIdSet := tools.SetFactory()
	for _, envUser := range envUsers {
		projectIdSet.Add(envUser.ProjectID)
		envIdSet.Add(envUser.EnvID)
	}

	if projectIdSet.Len() == 0 {
		return nil, nil
	}

	var projects []*models.Project
	cursor, err = tools.Database.Collection("project").Find(context.Background(), bson.M{
		"_id": bson.M{
			"$in": projectIdSet.IterKey(),
		},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(context.Background(), &projects)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	projects = slice.Filter(projects, func(index int, item *models.Project) bool {
		return item.ProjectInfo.ResearchAttribute == 0
	})
	//过滤项目环境
	for _, project := range projects {
		var envs []models.Environment
		for _, env := range project.Environments {
			if envIdSet.Has(env.ID) {
				envs = append(envs, env)
			}
		}
		project.Environments = envs
	}

	return projects, nil
}

func (s *ProjectService) GetViewMultiLanguage(ctx *gin.Context) ([]*models.Project, error) {
	user, err := tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var envUsers []models.UserProjectEnvironment
	cursor, err := tools.Database.Collection("user_project_environment").Find(context.Background(), bson.M{
		"user_id": user.ID,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(context.Background(), &envUsers)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	envUsers = slice.Filter(envUsers, func(index int, item models.UserProjectEnvironment) bool {
		return item.Unbind == false
	})

	projectIdSet := tools.SetFactory()
	envIdSet := tools.SetFactory()
	for _, envUser := range envUsers {
		projectIdSet.Add(envUser.ProjectID)
		envIdSet.Add(envUser.EnvID)
	}

	if projectIdSet.Len() == 0 {
		return nil, nil
	}

	var projects []*models.Project
	cursor, err = tools.Database.Collection("project").Find(context.Background(), bson.M{
		"_id": bson.M{
			"$in": projectIdSet.IterKey(),
		},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(context.Background(), &projects)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	projects = slice.Filter(projects, func(index int, item *models.Project) bool {
		return item.ProjectInfo.ResearchAttribute == 0
	})

	var projectList []*models.Project
	if projects != nil && len(projects) > 0 {

		projectIdList := make([]primitive.ObjectID, 0)
		for _, project := range projects {
			projectIdList = append(projectIdList, project.ID)
		}
		nameList := make([]string, 0)
		nameList = append(nameList, "Project-Admin")
		nameList = append(nameList, "Customer-Admin")
		nameList = append(nameList, "Sys-Admin")

		permission := make([]string, 0)
		permission = append(permission, "operation.projects.project.multiLanguage.view")

		projectRolePermissionList := make([]models.ProjectRolePermission, 0)
		projectRolePermissionCursor, err := tools.Database.Collection("project_role_permission").Find(ctx, bson.M{
			"project_id":  bson.M{"$in": projectIdList},
			"name":        bson.M{"$in": nameList},
			"permissions": bson.M{"$in": permission},
		})
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		err = projectRolePermissionCursor.All(ctx, &projectRolePermissionList)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		newProjectIdList := make([]primitive.ObjectID, 0)
		if projectRolePermissionList != nil && len(projectRolePermissionList) > 0 {
			for _, projectRolePermission := range projectRolePermissionList {
				newProjectIdList = append(newProjectIdList, projectRolePermission.ProjectID)
			}
		}

		//过滤项目环境
		for _, project := range projects {
			index := arrays.Contains(newProjectIdList, project.ID)
			administratorList := make([]primitive.ObjectID, 0)
			if project.Administrators != nil && len(project.Administrators) > 0 {
				administratorList = project.Administrators
			}
			adminIndex := arrays.Contains(administratorList, user.ID)
			if index != -1 && adminIndex != -1 { //存在
				var envs []models.Environment
				for _, env := range project.Environments {
					if envIdSet.Has(env.ID) {
						envs = append(envs, env)
					}
				}
				project.Environments = envs
				projectList = append(projectList, project)
			}
		}
	}

	out, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	userData := out[0]

	customers := userData.Customers
	adminCustomerIds := make([]primitive.ObjectID, 0)
	if len(customers) > 0 {
		adminCustomers := slice.Filter(customers, func(index int, item *models.CloudUserCustomer) bool {
			admins := item.Admin
			_, ok := slice.Find(admins, func(index int, i string) bool {
				return i == config.CLOUD_KEY
			})
			return ok
		})
		adminCustomerIds = slice.Map(adminCustomers, func(index int, item *models.CloudUserCustomer) primitive.ObjectID {
			OID, _ := primitive.ObjectIDFromHex(item.Id)
			return OID
		})
		if len(adminCustomerIds) > 0 {
			filter := bson.M{"$or": bson.A{
				bson.M{"customer_id": bson.M{"$in": adminCustomerIds}},
			}}

			// 查询是客户管理员或项目管理员的项目
			cur, err := tools.Database.Collection("project").Find(nil, filter)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			projectSettingRespList := make([]models.ProjectSettingResp, 0)
			if err := cur.All(nil, &projectSettingRespList); err != nil {
				return nil, errors.WithStack(err)
			}

			var rolePermission models.RolePermission
			if err := tools.Database.Collection("role_permission").FindOne(nil, bson.M{"name": "Customer-Admin"}).Decode(&rolePermission); err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}

			isMult := false
			if rolePermission.Permissions != nil && len(rolePermission.Permissions) > 0 {
				index := arrays.Contains(rolePermission.Permissions, "operation.projects.project.multiLanguage.view")
				if index != -1 {
					isMult = true
				}
			}

			if projectSettingRespList != nil && len(projectSettingRespList) > 0 {
				for _, resp := range projectSettingRespList {
					if isMult {
						projectList = append(projectList, &resp.Project)
					}
				}
			}

		}
	}

	// 使用map记录已经遇到的ID
	idMap := make(map[primitive.ObjectID]models.Project)
	// 创建一个新的切片用于存放结果
	var result []*models.Project

	for _, item := range projectList {
		if _, exists := idMap[item.ID]; !exists {
			// 如果ID还没有被添加到map中，则将其添加
			idMap[item.ID] = *item
			// 并将该项添加到结果切片中
			result = append(result, item)
		}
	}

	return result, nil
}

func (s *ProjectService) GetHomeList(ctx *gin.Context) (interface{}, error) {
	keyword := ctx.Query("keyword")
	focusOn := ctx.Query("focusOn")

	start, _ := strconv.Atoi(ctx.DefaultQuery("start", "0"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	var err error
	me, err := tools.Me(ctx)
	if err != nil {
		return nil, err
	}

	match := bson.M{
		"user_id": me.ID,
		"roles.0": bson.M{"$exists": 1},
		"unbind":  bson.M{"$ne": true},
	}

	var projectIds []primitive.ObjectID
	if focusOn == "true" {
		//筛选只关注的项目
		var userFocusProjects []models.UserFocusProject
		userFocusProjectCursor, err := tools.Database.Collection("user_focus_project").Find(nil, bson.M{"user_id": me.ID})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = userFocusProjectCursor.All(nil, &userFocusProjects)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		for _, ufp := range userFocusProjects {
			projectIds = append(projectIds, ufp.ProjectID)
		}

		if projectIds != nil && len(projectIds) > 0 {
			match["project_id"] = bson.M{"$in": projectIds}
		} else {
			projectIds = append(projectIds, primitive.NilObjectID)
			match["project_id"] = bson.M{"$in": projectIds}
		}
	}

	var total []map[string]interface{}
	totalPipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
	}
	if keyword != "" {
		totalPipeline = append(totalPipeline,
			bson.D{{Key: "$lookup", Value: bson.M{"from": "project", "localField": "project_id", "foreignField": "_id", "as": "project"}}},
			bson.D{{Key: "$unwind", Value: "$project"}},
			bson.D{{Key: "$lookup", Value: bson.M{
				"from": "user_focus_project",
				"let": bson.M{
					"project_id": "$project_id",
				},
				"pipeline": mongo.Pipeline{
					{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$project_id", "$$project_id"}}}}},
					{{Key: "$match", Value: bson.M{"user_id": me.ID}}},
				},
				"as": "user_focus_project",
			}}},
			bson.D{{"$match", bson.M{
				"$or": bson.A{
					bson.M{"project.info.name": bson.M{"$regex": keyword, "$options": "im"}},
					bson.M{"project.info.number": bson.M{"$regex": keyword, "$options": "im"}},
				},
			}}},
		)
	}
	totalPipeline = append(totalPipeline,
		bson.D{{Key: "$group", Value: bson.M{"_id": "$project_id"}}},
		bson.D{{Key: "$count", Value: "total"}},
	)
	cur, err := tools.Database.Collection("user_project_environment").Aggregate(nil, totalPipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cur.All(nil, &total)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{"from": "project", "localField": "project_id", "foreignField": "_id", "as": "project"}}},
		{{Key: "$unwind", Value: "$project"}},
		{{Key: "$lookup", Value: bson.M{
			"from": "user_focus_project",
			"let": bson.M{
				"project_id": "$project_id",
			},
			"pipeline": mongo.Pipeline{
				{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$project_id", "$$project_id"}}}}},
				{{Key: "$match", Value: bson.M{"user_id": me.ID}}},
			},
			"as": "user_focus_project",
		}}},
	}
	if keyword != "" {
		pipeline = append(pipeline,
			bson.D{{"$match", bson.M{
				"$or": bson.A{
					bson.M{"project.info.name": bson.M{"$regex": keyword, "$options": "im"}},
					bson.M{"project.info.number": bson.M{"$regex": keyword, "$options": "im"}},
				},
			}}},
		)
	}
	pipeline = append(pipeline,
		bson.D{{Key: "$unwind", Value: "$project.envs"}},
		bson.D{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$project.envs.id"}}}}},
		//bson.D{{Key: "$group", Value: bson.M{"_id": "$project_id", "project": bson.M{"$push": "$project"}}}},
		bson.D{{Key: "$group", Value: bson.M{
			"_id": "$project_id",
			"project": bson.M{"$push": bson.M{
				"customer_id":    "$project.customer_id",
				"administrators": "$project.administrators",
				"status":         "$project.status",
				"info":           "$project.info",
				"envs":           "$project.envs",
				"meta":           "$project.meta",
				"create_time":    "$user_focus_project.create_time",
			},
			},
		}}},
		//"medicines":      bson.M{"$push": bson.M{"_id": "$_id", "serial_number": "$serial_number"}},
		bson.D{{Key: "$project", Value: bson.M{
			"_id":            0,
			"id":             "$_id",
			"customerId":     bson.M{"$first": "$project.customer_id"},
			"administrators": bson.M{"$first": "$project.administrators"},
			"status":         bson.M{"$first": "$project.status"},
			"info":           bson.M{"$first": "$project.info"},
			"envs":           "$project.envs",
			"meta":           bson.M{"$first": "$project.meta"},
			"create_time":    bson.M{"$first": "$project.create_time"},
			"sign":           "0",
		}}},
		bson.D{{Key: "$sort", Value: bson.D{{"create_time", -1}, {"meta.created_at", -1}}}},
		bson.D{{Key: "$skip", Value: start}},
		bson.D{{Key: "$limit", Value: limit}},
	)

	var d []map[string]interface{}
	cursor, err := tools.Database.Collection("user_project_environment").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &d)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	allCustomers, err := tools.ListAllCustomer(locales.Lang(ctx))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	d = slice.Map(d, func(index int, item map[string]interface{}) map[string]interface{} {
		customerP, ok := slice.Find(allCustomers, func(index int, c models.CrmCustomerData) bool {
			return c.Id == item["customerId"].(primitive.ObjectID).Hex()
		})
		if ok {
			customer := *customerP
			item["customerName"] = customer.Name
			if ctx.GetHeader("Accept-Language") == "en" && customer.NameEn != "" {
				item["customerName"] = customer.NameEn
			}
		}
		return item
	})

	var count int32 = 0
	if len(total) > 0 {
		count = total[0]["total"].(int32)
	}
	return map[string]interface{}{"total": count, "items": d}, nil
}

func (s *ProjectService) GetSettingList(ctx *gin.Context) (interface{}, error) {
	var err error
	start, err := strconv.Atoi(ctx.DefaultQuery("start", "0"))
	limit, err := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	keyword := ctx.Query("keyword")
	keyword = regexp.QuoteMeta(keyword)
	var me models.User
	me, err = tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	out, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{me.CloudId.Hex()}}, locales.Lang(ctx))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	userData := out[0]
	filter := bson.M{"administrators": me.ID}
	if keyword != "" {
		filter = bson.M{"administrators": me.ID, "$or": bson.A{
			bson.M{"info.name": bson.M{"$regex": keyword, "$options": "im"}},
			bson.M{"info.number": bson.M{"$regex": keyword, "$options": "im"}},
		}}
	}

	customers := userData.Customers
	adminCustomerIds := make([]primitive.ObjectID, 0)
	if len(customers) > 0 {
		adminCustomers := slice.Filter(customers, func(index int, item *models.CloudUserCustomer) bool {
			admins := item.Admin
			_, ok := slice.Find(admins, func(index int, i string) bool {
				return i == config.CLOUD_KEY
			})
			return ok
		})
		adminCustomerIds = slice.Map(adminCustomers, func(index int, item *models.CloudUserCustomer) primitive.ObjectID {
			OID, _ := primitive.ObjectIDFromHex(item.Id)
			return OID
		})
		if len(adminCustomerIds) > 0 {
			filter = bson.M{"$or": bson.A{
				bson.M{"administrators": me.ID},
				bson.M{"customer_id": bson.M{"$in": adminCustomerIds}},
			}}
			if keyword != "" {
				filter = bson.M{"$and": bson.A{
					bson.M{"$or": bson.A{
						bson.M{"administrators": me.ID},
						bson.M{"customer_id": bson.M{"$in": adminCustomerIds}},
					}},
					bson.M{"$or": bson.A{
						bson.M{"info.name": bson.M{"$regex": keyword, "$options": "im"}},
						bson.M{"info.number": bson.M{"$regex": keyword, "$options": "im"}},
					}},
				}}
			}
		}

	}

	// 查询是客户管理员或项目管理员的项目
	total, err := tools.Database.Collection("project").CountDocuments(nil, filter)
	skip := int64(start)
	l := int64(limit)
	opts := &options.FindOptions{
		Sort:  bson.D{{"meta.created_at", -1}},
		Skip:  &skip,
		Limit: &l,
	}
	cursor, err := tools.Database.Collection("project").Find(nil, filter, opts)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var d []models.ProjectSettingResp
	if err := cursor.All(nil, &d); err != nil {
		return nil, errors.WithStack(err)
	}
	allCustomers, err := tools.ListAllCustomer(locales.Lang(ctx))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	enBool := ctx.GetHeader("Accept-Language") == "en"

	d = slice.Map(d, func(index int, item models.ProjectSettingResp) models.ProjectSettingResp {
		customerP, ok := slice.Find(allCustomers, func(index int, c models.CrmCustomerData) bool {
			return c.Id == item.CustomerID.Hex()
		})
		if ok {
			customer := *customerP
			item.CustomerName = customer.Name
			if enBool && customer.NameEn != "" {
				item.CustomerName = customer.NameEn
			}
		}
		_, ok = slice.Find(adminCustomerIds, func(index int, id primitive.ObjectID) bool {
			return id == item.CustomerID
		})
		if ok {
			item.CustomerAdmin = true
		}
		visitRandomization, e := s.raveService.GetCohortVisitRandomization(ctx, item.ID.Hex(), item.CustomerID.Hex())
		if e == nil {
			item.VisitRandomization = visitRandomization
		}
		// 7549新增逻辑，订单核查时间支持自定义，为空时添加默认展示，如数据清洗后移除此处代码需要同步修改前端是否编辑hash值计算
		if item.ProjectInfo.OrderCheckDay == nil || len(item.ProjectInfo.OrderCheckDay) == 0 {
			item.ProjectInfo.OrderCheckDay = []int{1, 2, 3, 4, 5, 6, 7}
			item.ProjectInfo.OrderCheckTime = "08:00"
		}
		return item
	})

	return map[string]interface{}{"total": total, "items": d}, nil
}

func (s *ProjectService) GetUsers(ctx *gin.Context) (interface{}, error) {
	var err error
	req := struct {
		CustomerIds []string `json:"customerIds"`
	}{}
	err = ctx.ShouldBindBodyWith(&req, binding.JSON)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	type projectUser struct {
		ID         primitive.ObjectID `json:"id"`
		CustomerId string             `json:"customerId"`
		CloudId    string             `json:"cloudId"`
		Admin      bool               `json:"admin"`
		Deleted    bool               `json:"deleted"`
		Info       struct {
			Name   string `json:"name"`
			Email  string `json:"email"`
			Status int32  `json:"status"`
		} `json:"info"`
	}

	response := make([]projectUser, 0)
	for _, customerId := range req.CustomerIds {
		//调用cloud 获取用户列表
		resp, err := tools.UserFetchWithCustomer(&models.UserFetchWithCustomerRequest{
			CustomerId: customerId,
			Keyword:    "",
			Start:      int64(0),
			Limit:      int64(0),
		}, locales.Lang(ctx))
		if err != nil {
			return nil, errors.WithStack(err)
		}
		cloudUsers := resp.Users
		for _, cloudUser := range cloudUsers {
			//循环user下的客户id 判断是否为admin
			admin := false
		Loop:
			for _, customer := range cloudUser.Customers {
				if customer.Id == customerId {
					for _, a := range customer.Admin {
						if a == config.CLOUD_KEY {
							admin = true
							break Loop
						}
					}
				}
			}
			d := projectUser{
				ID:         primitive.NilObjectID,
				CustomerId: customerId,
				CloudId:    cloudUser.Id,
				Admin:      admin,
				Deleted:    cloudUser.Deleted,
				Info: struct {
					Name   string `json:"name"`
					Email  string `json:"email"`
					Status int32  `json:"status"`
				}{
					Name:   cloudUser.Info.Name,
					Email:  cloudUser.Info.Email,
					Status: cloudUser.Status,
				},
			}
			response = append(response, d)
		}
	}

	cloudIds := slice.Map(response, func(index int, item projectUser) primitive.ObjectID {
		OID, _ := primitive.ObjectIDFromHex(item.CloudId)
		return OID
	})
	cloudIds = slice.Filter(cloudIds, func(index int, item primitive.ObjectID) bool {
		return !item.IsZero()
	})
	cloudIds = slice.Unique(cloudIds)

	users := make([]models.User, 0)
	cursor, err := tools.Database.Collection("user").Aggregate(nil, mongo.Pipeline{
		{{"$match", bson.M{"cloud_id": bson.M{"$in": cloudIds}, "deleted": bson.M{"$ne": true}}}},
		{{"$project", bson.M{
			"_id":         1,
			"info.name":   1,
			"info.email":  1,
			"info.status": 1,
			"deleted":     1,
			"cloud_id":    1,
		}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &users)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	projectUserList := make([]projectUser, 0)
	for i, projectUser := range response {
		up, ok := slice.Find(users, func(index int, item models.User) bool {
			return item.CloudId.Hex() == projectUser.CloudId
		})
		if ok {
			user := *up
			response[i].ID = user.ID
			if user.ID != primitive.NilObjectID {
				projectUserList = append(projectUserList, response[i])
			}
		}
	}

	return projectUserList, nil
}

func (s *ProjectService) GetOverview(ctx *gin.Context, customerID string, projectID string, envID string, roleID string) (interface{}, error) {
	oid, _ := primitive.ObjectIDFromHex(projectID)
	eid, _ := primitive.ObjectIDFromHex(envID)
	rid, _ := primitive.ObjectIDFromHex(roleID)
	var project models.Project
	var attributes []models.Attribute
	opts := &options.FindOneOptions{
		Projection: bson.M{"meta": 0},
	}
	if err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": oid}, opts).Decode(&project); err != nil {
		return project, errors.WithStack(err)
	}

	cursor, err := tools.Database.Collection("attribute").Find(nil, bson.M{"env_id": eid})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &attributes)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var projectRolePermission models.ProjectRolePermission
	if err := tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{"_id": rid, "project_id": oid}, opts).Decode(&projectRolePermission); err != nil {
		return projectRolePermission, errors.WithStack(err)
	}
	var siteCount int64
	var cohortCountMaps []map[string]interface{}

	var overview models.ProjectOverview
	overview.ProjectStatus = project.Status
	overview.StartTime = project.ProjectInfo.StartTime
	overview.EndTime = project.ProjectInfo.EndTime

	projectTimeZone, err := tools.GetTimeZone(oid)
	if err != nil {
		return "", errors.WithStack(err)
	}
	//offset := int(projectTimeZone)

	//overview.StartTimeStr = time.Unix(int64(project.ProjectInfo.StartTime), 0).In(time.FixedZone(fmt.Sprintf("UTC%s", projectTimeZone), offset*60*60)).Format("2006.01.02")
	//overview.EndTimeStr = time.Unix(int64(project.ProjectInfo.EndTime), 0).In(time.FixedZone(fmt.Sprintf("UTC%s", projectTimeZone), offset*60*60)).Format("2006.01.02")

	overview.ProjectTimeZone = int(projectTimeZone)

	siteCount, _ = tools.Database.Collection("project_site").CountDocuments(
		nil,
		bson.M{"project_id": oid, "env_id": eid, "deleted": 2},
	)
	overview.SiteCount = siteCount

	cursor, err = tools.Database.Collection("subject").Aggregate(
		nil,
		mongo.Pipeline{
			{{"$match", bson.M{"group": bson.M{"$ne": ""}, "random_list_id": bson.M{"$ne": primitive.NilObjectID}, "project_id": oid, "env_id": eid, "status": bson.M{"$in": []int64{3, 4, 5, 6, 9}}}}},
			{{Key: "$group", Value: bson.M{"_id": "$cohort_id", "count": bson.M{"$sum": 1}}}},
		},
	)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &cohortCountMaps)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	cohortCountMap := map[string]interface{}{}
	for _, countMap := range cohortCountMaps {
		cohortCountMap[countMap["_id"].(primitive.ObjectID).Hex()] = countMap["count"]
	}
	if project.Type != 1 {
		for _, env := range project.Environments {
			if env.ID == eid {
				for _, cohort := range env.Cohorts {
					attribute := models.Attribute{}
					actualCases := 0
					if cohortCountMap[cohort.ID.Hex()] != nil {
						actualCases = int(cohortCountMap[cohort.ID.Hex()].(int32))
					}
					isRandom := false
					attributeP, ok := slice.Find(attributes, func(index int, item models.Attribute) bool {
						return item.CohortID == cohort.ID
					})
					if ok {
						attribute = *attributeP
						isRandom = attribute.AttributeInfo.Random
					}
					find, b := slice.Find(cohort.AlertThresholds, func(index int, item models.AlertThreshold) bool {
						return item.Type == 3
					})
					capacity := 0
					if b {
						capacity = find.Capacity
					}
					cohortName := models.GetCohortReRandomName(cohort)
					if isRandom {
						overview.CohortsCases = append(overview.CohortsCases, models.CohortsCases{
							CohortName:   cohortName,
							PlannedCases: capacity,
							ActualCases:  actualCases,
							IsRandom:     isRandom,
						})
					} else {
						actualCases, err = tools.CountSubject(nil, attribute, bson.M{"env_id": env.ID, "cohort_id": cohort.ID})
						if err != nil {
							return overview, errors.WithStack(err)
						}
						overview.CohortsCases = append(overview.CohortsCases, models.CohortsCases{
							CohortName:   cohortName,
							PlannedCases: capacity,
							ActualCases:  actualCases,
							IsRandom:     isRandom,
						})
					}
				}
			}
		}
	} else {
		actualCases := 0
		if cohortCountMaps != nil {
			actualCases = int(cohortCountMaps[0]["count"].(int32))
		}
		envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
			return item.ID == eid
		})
		env := *envP

		if len(attributes) > 0 && !attributes[0].AttributeInfo.Random {
			actualCases, err = tools.CountSubject(nil, attributes[0], bson.M{"env_id": env.ID})
		}

		cases := models.CohortsCases{
			ActualCases: actualCases,
		}
		if env.AlertThresholds != nil {
			find, b := slice.Find(env.AlertThresholds, func(index int, item models.AlertThreshold) bool {
				return item.Type == 3
			})
			if b {
				cases.PlannedCases = find.Capacity
			}

		}
		overview.CohortsCases = append(overview.CohortsCases, cases)
	}
	return overview, nil
}

func (s *ProjectService) GetCohortStatus(ctx *gin.Context, customerID string, projectID string, envID string, roleID string, cohortID string) (int, error) {
	oid, _ := primitive.ObjectIDFromHex(projectID)
	eid, _ := primitive.ObjectIDFromHex(envID)
	coid, _ := primitive.ObjectIDFromHex(cohortID)
	var project models.Project
	opts := &options.FindOneOptions{
		Projection: bson.M{"meta": 0},
	}
	if err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": oid}, opts).Decode(&project); err != nil {
		return 0, errors.WithStack(err)
	}

	cohortStatus := 0
	for _, env := range project.Environments {
		if env.ID == eid {
			for _, cohort := range env.Cohorts {
				if cohort.ID == coid {
					cohortStatus = cohort.Status
					break
				}
			}
		}

	}
	return cohortStatus, nil
}

func (s *ProjectService) ReauthorizationProjectEnvironmentUser(ctx *gin.Context) error {
	customerID := ctx.Query("customerId")
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectId := ctx.Query("projectId")
	projectOID, _ := primitive.ObjectIDFromHex(projectId)
	envId := ctx.Query("envId")
	envOID, _ := primitive.ObjectIDFromHex(envId)
	userID := ctx.Query("userId")
	userOID, _ := primitive.ObjectIDFromHex(userID)
	emailLanguage := ctx.Query("emailLanguage")
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var user models.User
		err := tools.Database.Collection("user").FindOne(sctx, bson.M{"_id": userOID}).Decode(&user)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var userProjectEnv models.UserProjectEnvironment
		err = tools.Database.Collection("user_project_environment").FindOneAndUpdate(sctx, bson.M{"user_id": userOID, "env_id": envOID}, bson.M{"$set": bson.M{"unbind": false}}).Decode(&userProjectEnv)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		//补发邀请邮件
		//c := tools.GrpcContext()
		la := ctx.Request.Header.Get("Accept-Language")
		if len(emailLanguage) != 0 {
			ctx.Request.Header.Set("Accept-Language", emailLanguage)
		}
		// 按email查询匹配的用户
		response, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if len(response) == 0 {
			ctx.Request.Header.Set("Accept-Language", la)
			return nil, tools.BuildServerError(ctx, "user.no.exist")
		}
		if len(emailLanguage) != 0 {
			ctx.Request.Header.Set("Accept-Language", emailLanguage)
		}
		userData := response[0]
		if userData.Status == 0 {
			_, err = tools.UserInvite(&models.UserInviteRequest{Email: userData.Info.Email, CustomerId: customerID, Admin: false}, locales.Lang(ctx))
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
		var project models.Project
		err = tools.Database.Collection("project").FindOne(sctx, bson.M{"_id": projectOID}).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var mails []models.Mail
		envName := ""
		for _, env := range project.Environments {
			if env.ID == envOID {
				envName = env.Name
				break
			}
		}
		err = insertUnbindInviteProjectUserLog(ctx, sctx, envOID, 8, 0, 1, user.Email, userProjectEnv.ID, emailLanguage)
		if err != nil {
			return nil, err
		}
		//项目授权
		mails = append(mails, models.Mail{
			ID:      primitive.NewObjectID(),
			Subject: "user.notice.title",
			HTML:    "user_notice_new.html",
			ContentData: bson.M{
				"Title":    locales.Tr(ctx, "user.notice.project.bind.title"),
				"Activate": locales.Tr(ctx, "user.notice.return.login"),
				"Content": locales.Tr(ctx, "user.notice_project", bson.M{
					"email":   user.Email,
					"project": fmt.Sprintf("%s-%s", project.Number, envName),
				}),
				"Link": config.CLOUD,
			},
			To:           []string{user.Email},
			Lang:         ctx.GetHeader("Accept-Language"),
			Status:       0,
			CreatedTime:  time.Duration(time.Now().Unix()),
			ExpectedTime: time.Duration(time.Now().Unix()),
			SendTime:     time.Duration(time.Now().Unix()),
		})
		ctx.Set("MAIL", mails)

		if len(mails) > 0 {
			var envs []models.MailEnv
			for _, m := range mails {
				envs = append(envs, models.MailEnv{
					ID:         primitive.NewObjectID(),
					MailID:     m.ID,
					CustomerID: customerOID,
					ProjectID:  projectOID,
					EnvID:      envOID,
					CohortID:   primitive.NilObjectID,
				})
			}
			ctx.Set("MAIL-ENV", envs)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}

	//更新项目通知、访视通知的通知用户
	err = updateVisitNoticeUser(ctx, projectId, envId)
	if err != nil {
		return errors.WithStack(err)
	}
	err = updateProjectNoticeUser(ctx, projectId, envId)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

// 初始化通知配置
func initNoticeConfig(sctx mongo.SessionContext, CustomerOID primitive.ObjectID, projectOID primitive.ObjectID, envOID primitive.ObjectID) error {
	var projectRolePermissions []models.ProjectRolePermission
	cursor, err := tools.Database.Collection("project_role_permission").Find(nil, bson.M{"project_id": projectOID})
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &projectRolePermissions)
	if err != nil {
		return errors.WithStack(err)
	}
	noticeConfigs := []interface{}{}
	for key, value := range data.RoleNoticeInit {
		Roles := []primitive.ObjectID{}
		FieldsConfig := []string{}
		State := []string{}
		automatic := 0
		manual := 0
		slice.Map(projectRolePermissions, func(index int, projectRolePermission models.ProjectRolePermission) interface{} {
			_, ok := slice.Find(value, func(index int, item string) bool {
				return item == projectRolePermission.Name
			})
			if ok {
				Roles = append(Roles, projectRolePermission.ID)
			}
			return nil
		})
		if key == "notice.subject.dispensing" {
			FieldsConfig = []string{"projectName", "projectNumber", "envName", "siteName", "siteNumber", "random_number"}
			State = []string{"dispensing.plan-title", "dispensing.unscheduled-plan-title", "dispensing.reissue-title", "dispensing.replace-title"}
		} else if key == "notice.medicine.isolation" {
			FieldsConfig = []string{"projectName", "projectNumber", "envName"}
			State = []string{"medicine.freeze.title", "medicine.freeze.release"}
		} else if key == "notice.medicine.order" {
			FieldsConfig = []string{"projectName", "projectNumber", "envName", "expectedArrivalTime"}
			State = []string{"order.no_automatic_success_title", "order.close_title", "order.send_title", "order.receive_title", "order.end_title", "order.lost_title", "order.automatic_success_title", "order.automatic_error_title"}
		} else if key == "notice.subject.add" || key == "notice.subject.random" || key == "notice.subject.unblinding" || key == "notice.subject.signOut" || key == "notice.subject.replace" {
			FieldsConfig = []string{"projectName", "projectNumber", "envName", "siteName", "siteNumber"}
		} else if key == "notice.medicine.alarm" {
			FieldsConfig = []string{"projectName", "projectNumber", "envName", "siteName", "siteNumber"}
			State = []string{"order.no_automatic_title"}
		} else if key == "notice.subject.screen" {
			FieldsConfig = []string{"projectName", "projectNumber", "envName", "siteName", "siteNumber"}
			State = []string{"subject.screen.success", "subject.screen.fail"}
		} else if key == "notice.subject.update" {
			FieldsConfig = []string{"projectName", "projectNumber", "envName", "siteName", "siteNumber"}
			State = []string{"subject.update.form_factor"}
		} else if key == "notice.basic.settings" {
			automatic = 1
			manual = 1
		} else {
			FieldsConfig = []string{"projectName", "projectNumber", "envName"}
		}
		noticeConfigs = append(noticeConfigs, models.NoticeConfig{
			ID:            primitive.NewObjectID(),
			CustomerID:    CustomerOID,
			ProjectID:     projectOID,
			EnvironmentID: envOID,
			Key:           key,
			State:         State,
			FieldsConfig:  FieldsConfig,
			Roles:         Roles,
			Automatic:     automatic,
			Manual:        manual,
		})
	}
	if _, err := tools.Database.Collection("notice_config").InsertMany(sctx, noticeConfigs); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *ProjectService) GetPermissions(ctx *gin.Context, id string) ([]string, error) {
	var projectPermissions []string
	if id == "" {
		return projectPermissions, nil
	}
	oid, _ := primitive.ObjectIDFromHex(id)
	var document models.ProjectRolePermission
	opts := &options.FindOneOptions{
		Projection: bson.M{"meta": 0},
	}
	if err := tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{"project_id": oid, "name": "Project-Admin"}, opts).Decode(&document); err != nil {
		return projectPermissions, errors.WithStack(err)
	}

	return document.Permissions, nil
}

func (s *ProjectService) UpdateProjectType(ctx *gin.Context, id string, modifyType string) error {
	var oldProject models.Project

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		oid, _ := primitive.ObjectIDFromHex(id)
		err := tools.Database.Collection("project").FindOne(sctx, bson.M{"_id": oid}).Decode(&oldProject)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		visitRandomization := oldProject.VisitRandomization
		oldVisits := oldProject.VisitRandomization.Visits
		oldRandomizations := oldProject.VisitRandomization.Randomizations
		cohortName := "Cohort1"
		if modifyType == "2" {
			cohortName = "Cohort1"
			if len(oldVisits) > 0 {
				oldVisits[0].Name = cohortName
			}
			if len(oldRandomizations) > 0 {
				if oldRandomizations[0].RandomizationConfigs != nil && len(oldRandomizations[0].RandomizationConfigs) > 0 {
					var rc models.RandomizationConfig
					rc.RandomizationField = "cohort"
					oldRandomizations[0].RandomizationConfigs = append(oldRandomizations[0].RandomizationConfigs, rc)
				}
			}
		} else if modifyType == "3" {
			cohortName = "阶段1"
			if len(oldVisits) > 0 {
				oldVisits[0].Name = cohortName
			}
			if len(oldRandomizations) > 0 {
				oldRandomizations[0].Name = cohortName
				if oldRandomizations[0].RandomizationConfigs != nil && len(oldRandomizations[0].RandomizationConfigs) > 0 {
					var rc models.RandomizationConfig
					rc.RandomizationField = "cohort"
					oldRandomizations[0].RandomizationConfigs = append(oldRandomizations[0].RandomizationConfigs, rc)
				}
			}
		}
		visitRandomization.Visits = oldVisits
		visitRandomization.Randomizations = oldRandomizations

		projectType, _ := strconv.Atoi(modifyType)

		//project表更新
		//var newEnvironments []models.Environment
		newEnvironments := make([]models.Environment, 0)
		oldEnvironments := oldProject.Environments
		for _, oldEnvironment := range oldEnvironments {
			cohorts := oldEnvironment.Cohorts
			var cohort models.Cohort
			cohort.ID = primitive.NewObjectID()
			cohort.Type = 0
			cohort.Name = cohortName
			//if oldProject.PlannedCases != nil {
			//	cohort.Capacity = *(oldProject.PlannedCases)
			//}
			cohort.Factor = ""
			cohort.Status = 1
			cohorts = append(cohorts, cohort)
			oldEnvironment.Cohorts = cohorts
			oldEnvironment.Status = nil
			newEnvironments = append(newEnvironments, oldEnvironment)

			//attribute表更新
			updateAttribute := bson.M{"$set": bson.M{
				"cohort_id": cohort.ID,
			}}
			if _, err := tools.Database.Collection("attribute").UpdateMany(sctx, bson.M{"customer_id": oldProject.CustomerID, "env_id": oldEnvironment.ID, "project_id": oldProject.ID}, updateAttribute); err != nil {
				return nil, errors.WithStack(err)
			}

			//visit_cycle表更新
			updateVisitCycle := bson.M{"$set": bson.M{
				"cohort_id": cohort.ID,
			}}
			if _, err := tools.Database.Collection("visit_cycle").UpdateMany(sctx, bson.M{"customer_id": oldProject.CustomerID, "env_id": oldEnvironment.ID, "project_id": oldProject.ID}, updateVisitCycle); err != nil {
				return nil, errors.WithStack(err)
			}

			//drug_configure表更新
			updateDrugConfigure := bson.M{"$set": bson.M{
				"cohort_id": cohort.ID,
			}}
			if _, err := tools.Database.Collection("drug_configure").UpdateMany(sctx, bson.M{"customer_id": oldProject.CustomerID, "env_id": oldEnvironment.ID, "project_id": oldProject.ID}, updateDrugConfigure); err != nil {
				return nil, errors.WithStack(err)
			}

			//random_design表更新
			updateRandomDesign := bson.M{"$set": bson.M{
				"cohort_id": cohort.ID,
			}}
			if _, err := tools.Database.Collection("random_design").UpdateMany(sctx, bson.M{"customer_id": oldProject.CustomerID, "env_id": oldEnvironment.ID, "project_id": oldProject.ID}, updateRandomDesign); err != nil {
				return nil, errors.WithStack(err)
			}

			//barcode_group表更新
			updateBarcodeGroup := bson.M{"$set": bson.M{
				"cohort_id": cohort.ID,
			}}
			if _, err := tools.Database.Collection("barcode_group").UpdateMany(sctx, bson.M{"customer_id": oldProject.CustomerID, "env_id": oldEnvironment.ID, "project_id": oldProject.ID}, updateBarcodeGroup); err != nil {
				return nil, errors.WithStack(err)
			}

			//barcode_rule表更新
			updateBarcodeRule := bson.M{"$set": bson.M{
				"cohort_id": cohort.ID,
			}}
			if _, err := tools.Database.Collection("barcode_rule").UpdateMany(sctx, bson.M{"customer_id": oldProject.CustomerID, "env_id": oldEnvironment.ID, "project_id": oldProject.ID}, updateBarcodeRule); err != nil {
				return nil, errors.WithStack(err)
			}

			//approval_process表更新
			updateApprovalProcess := bson.M{"$set": bson.M{
				"cohort_id": cohort.ID,
			}}
			if _, err := tools.Database.Collection("approval_process").UpdateMany(sctx, bson.M{"customer_id": oldProject.CustomerID, "env_id": oldEnvironment.ID, "project_id": oldProject.ID}, updateApprovalProcess); err != nil {
				return nil, errors.WithStack(err)
			}

			//dispensing表更新
			updateDispensing := bson.M{"$set": bson.M{
				"cohort_id": cohort.ID,
			}}
			if _, err := tools.Database.Collection("dispensing").UpdateMany(sctx, bson.M{"customer_id": oldProject.CustomerID, "env_id": oldEnvironment.ID, "project_id": oldProject.ID}, updateDispensing); err != nil {
				return nil, errors.WithStack(err)
			}

			//dispensing_room_record表更新
			updateDispensingRoomRecord := bson.M{"$set": bson.M{
				"cohort_id": cohort.ID,
			}}
			if _, err := tools.Database.Collection("dispensing_room_record").UpdateMany(sctx, bson.M{"customer_id": oldProject.CustomerID, "env_id": oldEnvironment.ID, "project_id": oldProject.ID}, updateDispensingRoomRecord); err != nil {
				return nil, errors.WithStack(err)
			}

			//edc_push表更新
			updateEdcPush := bson.M{"$set": bson.M{
				"cohort_id": cohort.ID,
			}}
			if _, err := tools.Database.Collection("edc_push").UpdateMany(sctx, bson.M{"customer_id": oldProject.CustomerID, "env_id": oldEnvironment.ID, "project_id": oldProject.ID}, updateEdcPush); err != nil {
				return nil, errors.WithStack(err)
			}

			//edc_push表更新
			updateEdcPushLog := bson.M{"$set": bson.M{
				"cohort_id": cohort.ID,
			}}
			if _, err := tools.Database.Collection("edc_push_log").UpdateMany(sctx, bson.M{"customer_id": oldProject.CustomerID, "env_id": oldEnvironment.ID, "project_id": oldProject.ID}, updateEdcPushLog); err != nil {
				return nil, errors.WithStack(err)
			}

			//form表更新
			updateForm := bson.M{"$set": bson.M{
				"cohort_id": cohort.ID,
			}}
			if _, err := tools.Database.Collection("form").UpdateMany(sctx, bson.M{"customer_id": oldProject.CustomerID, "env_id": oldEnvironment.ID, "project_id": oldProject.ID}, updateForm); err != nil {
				return nil, errors.WithStack(err)
			}

			//operation_log表更新
			modules := []string{
				"operation_log.module.projectUser",
				"operation_log.module.projectUser_role",
				"operation_log.module.projectUser_site",
				"operation_log.module.projectUser_depot",
				"operation_log.module.project_information",
				"operation_log.module.project_env",
				"operation_log.module.project_function",
				"operation_log.module.project_docking",
				"operation_log.module.project_custom",
				"operation_log.module.project_permission",
				"operation_log.module.notifications",
				"operation_log.module.configure_export",
				"operation_log.module.project_basic_information",
			}
			updateOperationLog := bson.M{"$set": bson.M{
				"oid": cohort.ID,
			}}
			if _, err := tools.Database.Collection("operation_log").UpdateMany(sctx, bson.M{"oid": oldEnvironment.ID, "module": bson.M{"$nin": modules}}, updateOperationLog); err != nil {
				return nil, errors.WithStack(err)
			}

			//project_dynamics表更新
			updateProjectDynamics := bson.M{"$set": bson.M{
				"oid": cohort.ID,
			}}
			if _, err := tools.Database.Collection("project_dynamics").UpdateMany(sctx, bson.M{"oid": oldEnvironment.ID}, updateProjectDynamics); err != nil {
				return nil, errors.WithStack(err)
			}

			//random_list表更新
			updateRandomList := bson.M{"$set": bson.M{
				"cohort_id": cohort.ID,
			}}
			if _, err := tools.Database.Collection("random_list").UpdateMany(sctx, bson.M{"customer_id": oldProject.CustomerID, "env_id": oldEnvironment.ID, "project_id": oldProject.ID}, updateRandomList); err != nil {
				return nil, errors.WithStack(err)
			}

			//random_number表更新
			updateRandomNumber := bson.M{"$set": bson.M{
				"cohort_id": cohort.ID,
			}}
			if _, err := tools.Database.Collection("random_number").UpdateMany(sctx, bson.M{"customer_id": oldProject.CustomerID, "env_id": oldEnvironment.ID, "project_id": oldProject.ID}, updateRandomNumber); err != nil {
				return nil, errors.WithStack(err)
			}

			//region表更新
			//updateRegion := bson.M{"$set": bson.M{
			//	"cohort_id": cohort.ID,
			//}}
			//if _, err := tools.Database.Collection("region").UpdateMany(sctx, bson.M{"customer_id": oldProject.CustomerID, "env_id": oldEnvironment.ID, "project_id": oldProject.ID}, updateRegion); err != nil {
			//	return nil, errors.WithStack(err)
			//}

			//simulate_random表更新
			updateSimulateRandom := bson.M{"$set": bson.M{
				"cohort_id": cohort.ID,
			}}
			if _, err := tools.Database.Collection("simulate_random").UpdateMany(sctx, bson.M{"customer_id": oldProject.CustomerID, "env_id": oldEnvironment.ID, "project_id": oldProject.ID}, updateSimulateRandom); err != nil {
				return nil, errors.WithStack(err)
			}

			//subject表更新
			updateSubject := bson.M{"$set": bson.M{
				"cohort_id": cohort.ID,
			}}
			if _, err := tools.Database.Collection("subject").UpdateMany(sctx, bson.M{"customer_id": oldProject.CustomerID, "env_id": oldEnvironment.ID, "project_id": oldProject.ID, "deleted": bson.M{"$ne": true}}, updateSubject); err != nil {
				return nil, errors.WithStack(err)
			}

			// 项目日志
			_ = insertProjectLog(ctx, nil, cohort.ID, 2, "1", modifyType, cohort.ID)

		}

		updateProject := bson.M{"$set": bson.M{
			"envs":                     newEnvironments,
			"info.type":                projectType,
			"info.visit_Randomization": visitRandomization,
		}}
		if _, err := tools.Database.Collection("project").UpdateOne(sctx, bson.M{"_id": oid}, updateProject); err != nil {
			return nil, errors.WithStack(err)
		}

		//TODO 插入操作日志---项目日志：项目类型：基本研究->群组/再随机研究
		//var newProject models.Project
		//newProject = oldProject
		//newProject.Type = projectType
		//insertProjectInfoLog(ctx, sctx, oid, oldProject, newProject, oid)

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *ProjectService) UpdateCohortStatus(ctx *gin.Context) error {
	var req models.UpdateCohortStatus
	_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
	user, _ := tools.Me(ctx)
	//判断密码是否正确
	if req.Password != "" {
		err := tools.PasswordDetection(ctx, user.Email, req.Password)
		if err != nil {
			return err
		}
	}
	callback := func(sctx mongo.SessionContext) (interface{}, error) {

		// 修改操作
		update := bson.M{
			"$set": bson.M{
				"envs.$[env].cohorts.$[cohort].status": req.Status,
			},
		}
		opts := &options.FindOneAndUpdateOptions{
			ArrayFilters: &options.ArrayFilters{
				Filters: bson.A{bson.M{"env.id": req.EnvID}, bson.M{"cohort.id": req.CohortID}},
			},
		}
		var project models.Project
		err := tools.Database.Collection("project").FindOneAndUpdate(sctx, bson.M{"_id": req.ProjectID}, update, opts).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var oldCohort models.Cohort
		for _, env := range project.Environments {
			if env.ID == req.EnvID {
				for _, cohort := range env.Cohorts {
					if cohort.ID == req.CohortID {
						oldCohort = cohort
					}
				}
			}
		}
		//err = insertUpdateCohortStatus(ctx, sctx, req.ProjectID, 2, oldCohort, req.Status, oldCohort.ID)
		err = insertUpdateSubjectsCohortStatus(ctx, sctx, req.ProjectID, 2, oldCohort, req.Status, oldCohort.ID)
		if err != nil {
			return nil, err
		}
		return nil, nil
	}

	err := tools.Transaction(callback)
	if err != nil {
		return err
	}

	return nil
}

func (s *ProjectService) UserFocus(ctx *gin.Context) error {
	u, _ := ctx.Get("user")
	user := u.(models.User)

	projectOID, _ := primitive.ObjectIDFromHex(ctx.Query("projectId"))
	mark, _ := strconv.ParseBool(ctx.Query("mark"))

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		filter := bson.M{"_id": projectOID}
		var project models.Project
		err := tools.Database.Collection("project").FindOne(sctx, filter).Decode(&project)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		filter = bson.M{"customer_id": project.CustomerID, "project_id": project.ID, "user_id": user.ID}
		var userFocusProject models.UserFocusProject
		err = tools.Database.Collection("user_focus_project").FindOne(sctx, filter).Decode(&userFocusProject)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		if mark {
			// 关注项目
			if userFocusProject.ID == primitive.NilObjectID {
				if _, err = tools.Database.Collection("user_focus_project").InsertOne(sctx, models.UserFocusProject{
					ID:         primitive.NewObjectID(),
					CustomerID: project.CustomerID,
					ProjectID:  project.ID,
					UserID:     user.ID,
					CreateTime: time.Duration(time.Now().Unix()),
				}); err != nil {
					return nil, errors.WithStack(err)
				}
			}
		} else {
			// 取消关注
			if userFocusProject.ID != primitive.NilObjectID {
				if _, err = tools.Database.Collection("user_focus_project").DeleteOne(sctx, bson.M{
					"customer_id": project.CustomerID,
					"project_id":  project.ID,
					"user_id":     user.ID,
				}); err != nil {
					return nil, errors.WithStack(err)
				}
			}
		}
		return nil, nil
	}

	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *ProjectService) Card(ctx *gin.Context, id string) (map[string]interface{}, error) {
	oid, _ := primitive.ObjectIDFromHex(id)
	var project models.Project
	if err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": oid}).Decode(&project); err != nil {
		return nil, errors.WithStack(err)
	}

	var customer models.Customer
	allCustomers, err := tools.ListAllCustomer(locales.Lang(ctx))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	//筛选客户
	for _, ct := range allCustomers {
		if project.CustomerID.Hex() == ct.Id {
			customer.Name = ct.Name
			if ctx.GetHeader("Accept-Language") == "en" && ct.NameEn != "" {
				customer.Name = ct.NameEn
			}
			break
		}
	}
	// 查询管理员
	userFilter := bson.M{"_id": bson.M{"$in": project.Administrators}}
	var administrators []models.User
	administratorsCursor, err := tools.Database.Collection("user").Find(nil, userFilter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = administratorsCursor.All(nil, &administrators)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return map[string]interface{}{"project": project, "customer": customer, "administrators": administrators}, nil
}

// 查询所有项目的eLearning对接情况（系统学习）
func (s *ProjectService) GetUserProjectElearning(ctx *gin.Context) (interface{}, error) {
	var err error
	me, err := tools.Me(ctx)
	if err != nil {
		return nil, err
	}

	match := bson.M{
		"user_id": me.ID,
		"roles.0": bson.M{"$exists": 1},
		"unbind":  bson.M{"$ne": true},
	}

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{"from": "project", "localField": "project_id", "foreignField": "_id", "as": "project"}}},
		{{Key: "$unwind", Value: "$project"}},
	}
	pipeline = append(pipeline,
		bson.D{{Key: "$unwind", Value: "$project.envs"}},
		bson.D{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$project.envs.id"}}}}},
		bson.D{{Key: "$group", Value: bson.M{
			"_id": "$project_id",
			"project": bson.M{"$push": bson.M{
				"info": "$project.info",
			},
			},
		}}},
		bson.D{{Key: "$project", Value: bson.M{
			"_id":  0,
			"info": bson.M{"$first": "$project.info"},
		}}},
		bson.D{{Key: "$sort", Value: bson.D{{"create_time", -1}, {"meta.created_at", -1}}}},
	)

	var projects []models.Project
	cursor, err := tools.Database.Collection("user_project_environment").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &projects)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	nonDocking := 0           // 非对接项目个数
	compulsoryLearning := 0   // 对接项目强制学习个数
	nonMandatoryLearning := 0 // 对接项目非强制学习个数

	projectLearning := 0 // 开启的项目对接个数
	for _, p := range projects {
		if p.ProjectInfo.SystemCourses == 1 { // 对接系统课程项目
			if p.ProjectInfo.NeedSystemCourses == 1 { // 强制学习
				compulsoryLearning++
			} else { // 非强制学习
				nonMandatoryLearning++
			}
		} else { // 非对接系统课程项目
			nonDocking++
		}

		if p.ProjectInfo.ConnectLearning != 1 {
			projectLearning++
		}
	}

	systemMark := "1"                // mark 1.无需弹框提示学习 2.弹不可关闭的提示学习框 3.弹可关闭的提示学习框
	if len(projects) == nonDocking { // 1.如果所有项目都是非对接项目
		systemMark = "1"
	} else if len(projects) == compulsoryLearning { // 2.如果所有的对接项目都是强制学习
		systemMark = "2"
	} else if len(projects) == nonMandatoryLearning { // 3.如果所有的对接项目都是非强制学习
		systemMark = "3"
	} else { // 4.如果有的项目是强制学习有的不是强制学习
		systemMark = "3"
	}

	projectMark := "1"
	if len(projects) == projectLearning {
		projectMark = "1"
	} else {
		projectMark = "2"
	}

	return map[string]interface{}{"systemMark": systemMark, "projectMark": projectMark}, nil
}

func (s *ProjectService) GetProjectEnvRole(ctx *gin.Context, projectID, envID string) ([]models.ProjectRolePermissionUser, error) {
	var projectRolePermissionUser []models.ProjectRolePermissionUser
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)

	cursor, err := tools.Database.Collection("project_role_permission").Aggregate(nil, mongo.Pipeline{
		{{"$match", bson.M{"project_id": projectOID}}},
		{{"$lookup", bson.M{
			"from":         "user_project_environment",
			"as":           "user_project_environment",
			"localField":   "_id",
			"foreignField": "roles",
		}}},
		{{"$lookup", bson.M{
			"from": "user_project_environment",
			"let": bson.M{
				"id": "$_id",
			},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"env_id": envOID}},
				bson.M{"$match": bson.M{"$expr": bson.M{"$in": bson.A{"$$id", "$roles"}}}},
			},
			"as": "user_project_environment",
		}}},

		{{"$lookup", bson.M{
			"from":         "user",
			"as":           "user",
			"localField":   "user_project_environment.user_id",
			"foreignField": "_id",
		}}},
		{{"$project", bson.M{
			"role_name":       "$name",
			"user._id":        1,
			"user.info.email": 1,
		}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &projectRolePermissionUser)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return projectRolePermissionUser, err

}

func (s *ProjectService) GetProjectNotice(ctx *gin.Context, projectID, envID string) (interface{}, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	total, err := tools.Database.Collection("project_notice").CountDocuments(nil, bson.M{"project_id": projectOID, "open": true})
	var projectNotice models.ProjectNotice
	err = tools.Database.Collection("project_notice").FindOne(nil, bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
	}).Decode(&projectNotice)
	if total > 0 {
		projectNotice.Open = true
	}
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	return projectNotice, nil
}

func (s *ProjectService) GetRoleNoticeUser(ctx *gin.Context, projectID, envID string, roleID string) (interface{}, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	roleOID, _ := primitive.ObjectIDFromHex(roleID)

	var projectNotice models.ProjectNotice
	err := tools.Database.Collection("project_notice").FindOne(nil, bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
		"open":       true,
	}).Decode(&projectNotice)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}

	var userIds []primitive.ObjectID
	for _, ru := range projectNotice.RoleUser {
		if ru.RoleID == roleOID {
			userIds = ru.Users
			break
		}
	}
	var userList []models.User
	if userIds != nil && len(userIds) > 0 {
		cursor, _ := tools.Database.Collection("user").Find(nil, bson.M{"_id": bson.M{"$in": userIds}})
		cursor.All(nil, &userList)
	}

	return userList, nil
}

func (s *ProjectService) UpdateProjectNotice(ctx *gin.Context, projectNotice models.ProjectNotice) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		_, err := tools.Database.Collection("project_notice").UpdateMany(sctx,
			bson.M{"project_id": projectNotice.ProjectID},
			bson.M{"$set": bson.M{"open": projectNotice.Open}},
		)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		var oldProjectNotice models.ProjectNotice
		err = tools.Database.Collection("project_notice").FindOne(sctx, bson.M{"env_id": projectNotice.EnvID}).Decode(&oldProjectNotice)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		if projectNotice.NoticeRule != nil && len(projectNotice.NoticeRule) > 0 {
			noticeRuleList := make([]models.NoticeRule, 0)
			for _, rule := range projectNotice.NoticeRule {
				noticeRule := rule
				if noticeRule.ID == primitive.NilObjectID {
					noticeRule.ID = primitive.NewObjectID()
				}
				noticeRuleList = append(noticeRuleList, noticeRule)
			}
			projectNotice.NoticeRule = noticeRuleList
		}

		upsert := true
		_, err = tools.Database.Collection("project_notice").UpdateOne(sctx,
			bson.M{"project_id": projectNotice.ProjectID, "env_id": projectNotice.EnvID},
			bson.M{"$set": projectNotice},
			&options.UpdateOptions{
				Upsert: &upsert,
			},
		)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		//插入操作日志
		err = insertProjectNoticeLog(ctx, sctx, projectNotice.ProjectID, 2, oldProjectNotice, projectNotice, projectNotice.ProjectID)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		err = task.UpdateNotice(sctx, 1, projectNotice.EnvID, primitive.NilObjectID, primitive.NilObjectID, primitive.NilObjectID)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil

}

func (s *ProjectService) GetRoleList(ctx *gin.Context, customerID string, projectID string) ([]models.ProjectRolePermission, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	projectRolePermissions := make([]models.ProjectRolePermission, 0)
	permission := make([]string, 0)
	permission = append(permission, "operation.subject.medicine.dispensing")
	permission = append(permission, "operation.subject-dtp.medicine.dispensing")
	cursor, err := tools.Database.Collection("project_role_permission").Find(ctx, bson.M{
		"customer_id": customerOID,
		"project_id":  projectOID,
		"permissions": bson.M{"$in": permission},
	})
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(ctx, &projectRolePermissions)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}

	return projectRolePermissions, nil
}

func (s *ProjectService) GetRoleUserList(ctx *gin.Context, customerID string, projectID string, envID string, roleID string, email string) ([]models.RoleUserInfo, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	roleOID, _ := primitive.ObjectIDFromHex(roleID)
	userProjectEnvironments := make([]models.UserProjectEnvironment, 0)
	cursor, err := tools.Database.Collection("user_project_environment").Find(ctx, bson.M{
		"customer_id": customerOID,
		"project_id":  projectOID,
		"env_id":      envOID,
		"roles":       bson.M{"$in": [1]primitive.ObjectID{roleOID}},
	})
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(ctx, &userProjectEnvironments)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}

	roleUserInfos := make([]models.RoleUserInfo, 0)
	cloudIdList := make([]string, 0)
	for _, userProjectEnvironment := range userProjectEnvironments {
		var user models.User
		match := bson.M{
			"_id": userProjectEnvironment.UserID,
		}
		if len(email) > 0 {
			match = bson.M{
				"_id":         userProjectEnvironment.UserID,
				"info.email":  bson.M{"$regex": email},
				"info.Status": 1,
			}
		}
		err = tools.Database.Collection("user").FindOne(nil, match).Decode(&user)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		if user.ID != primitive.NilObjectID {
			var roleUserInfo models.RoleUserInfo
			roleUserInfo.ID = userProjectEnvironment.ID
			roleUserInfo.UserID = userProjectEnvironment.UserID
			roleUserInfo.CloudId = user.CloudId
			roleUserInfo.Unbind = userProjectEnvironment.Unbind
			roleUserInfo.Name = user.Name
			roleUserInfo.Email = user.Email
			roleUserInfo.Phone = user.Phone
			roleUserInfos = append(roleUserInfos, roleUserInfo)
			cloudIdList = append(cloudIdList, user.CloudId.Hex())
		}
	}

	//替换user info的内容
	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: cloudIdList}, locales.Lang(ctx))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	m := make(map[string]*models.UserData)
	for _, u := range users {
		m[u.Id] = u
	}

	resultList := make([]models.RoleUserInfo, 0)
	for _, roleUserInfo := range roleUserInfos {
		e := roleUserInfo.CloudId.Hex()
		if m[e] != nil && m[e].Status == 1 && !roleUserInfo.Unbind {
			resultList = append(resultList, roleUserInfo)
		}
	}

	return resultList, nil
}

func (s *ProjectService) GetByCohort(ctx *gin.Context, customerID string, projectID string, envID string, cohortID string) (string, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID, "customer_id": customerOID}).Decode(&project)
	if err != nil {
		return "", errors.WithStack(err)
	}

	cohortName := ""
	if project.Environments != nil && len(project.Environments) > 0 {
		for _, environment := range project.Environments {
			if environment.ID == envOID {
				if environment.Cohorts != nil && len(environment.Cohorts) > 0 {
					for _, cohort := range environment.Cohorts {
						if cohort.ID == cohortOID {
							cohortName = cohort.Name
						}
					}
				}
			}
		}
	}

	return cohortName, nil
}

func (s *ProjectService) GetProjectTimeZone(ctx *gin.Context, projectID string) (string, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)

	var project models.Project
	err := tools.Database.Collection("project").FindOne(ctx, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return "", errors.WithStack(err)
	}

	timeZone := ""
	if project.Tz != "" {
		offsetString, err := tools.GetUTCOffsetString(project.Tz)
		if err != nil {
			return "", errors.WithStack(err)
		}
		timeZone = offsetString
	} else if project.TimeZoneStr != "" {
		float, err := strconv.ParseFloat(project.TimeZoneStr, 64)
		if err != nil {
			return "", errors.WithStack(err)
		}
		strTimeZone := tools.FormatOffsetToZoneStringUtc(float)
		timeZone = strTimeZone
	}

	return timeZone, nil
}

func (s *ProjectService) GetCopyIsProd(ctx *gin.Context, customerID string, projectID string, envID string, envName string) (bool, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)

	project := models.Project{}
	err := tools.Database.Collection("project").FindOne(ctx, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return false, errors.WithStack(err)
	}
	newEnv, b := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.Name == envName
	})

	isData := false

	if b {
		//判断是否有受试者
		condition := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": newEnv.ID, "deleted": bson.M{"$ne": true}}
		if count, _ := tools.Database.Collection("subject").CountDocuments(ctx, condition); count > 0 {
			isData = true
		}
		//判断是否有订单
		if count, _ := tools.Database.Collection("medicine_order").CountDocuments(ctx, condition); count > 0 {
			isData = true
		}
	} else {
		isData = true
	}

	return isData, nil
}

func (s *ProjectService) GetCopyIsProdData(ctx *gin.Context, customerID string, projectID string, envID string) (bool, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)

	project := models.Project{}
	err := tools.Database.Collection("project").FindOne(ctx, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return false, errors.WithStack(err)
	}

	isData := false

	//判断是否有受试者
	condition := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "deleted": bson.M{"$ne": true}}
	if count, _ := tools.Database.Collection("subject").CountDocuments(ctx, condition); count > 0 {
		isData = true
	}
	//判断是否有订单
	if count, _ := tools.Database.Collection("medicine_order").CountDocuments(ctx, condition); count > 0 {
		isData = true
	}

	return isData, nil
}

func (s *ProjectService) GetProjectType(ctx *gin.Context, customerID string, projectID string) (int, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)

	project := models.Project{}
	err := tools.Database.Collection("project").FindOne(ctx, bson.M{"customer_id": customerOID, "_id": projectOID}).Decode(&project)
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return project.Status, nil
}

func (s *ProjectService) UpdateCohortSort(ctx *gin.Context, updateCohortSortReq models.UpdateCohortSortReq) error {
	project := models.Project{}
	err := tools.Database.Collection("project").FindOne(ctx, bson.M{"_id": updateCohortSortReq.ProjectOID}).Decode(&project)

	envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == updateCohortSortReq.EnvOID
	})
	env := *envP

	cohorts := []models.Cohort{}
	for _, ID := range updateCohortSortReq.SortIDs {
		cohortP, _ := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
			return item.ID == ID
		})
		cohort := *cohortP
		cohorts = append(cohorts, cohort)

	}
	env.Cohorts = cohorts
	_, err = tools.Database.Collection("project").UpdateOne(nil, bson.M{"_id": updateCohortSortReq.ProjectOID}, bson.M{"$set": bson.M{"envs.$[env].cohorts": env.Cohorts}}, &options.UpdateOptions{
		ArrayFilters: &options.ArrayFilters{
			Filters: bson.A{bson.M{"env.id": updateCohortSortReq.EnvOID}},
		},
	})
	if err != nil {
		return errors.WithStack(err)
	}

	return nil

}
