package service

import (
	"clinflash-irt/data"
	"clinflash-irt/database"
	"clinflash-irt/locales"
	"clinflash-irt/task"
	"clinflash-irt/templates"
	"clinflash-irt/tools"
	"fmt"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/pkg/errors"
	"github.com/wxnacy/wgo/arrays"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/mongo/options"

	"clinflash-irt/models"
	"io/ioutil"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type MedicineService struct {
	//medicineOrderService MedicineOrderService
}

func (s *MedicineService) GetMedicineList(ctx *gin.Context, customerID string, projectID string, envID string, start int, limit int, attributeId string, roleId string) (map[string]interface{}, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	//cohortOID, _ := primitive.ObjectIDFromHex(cohortID)

	status, _ := strconv.Atoi(ctx.DefaultQuery("status", "0"))
	drugName := ctx.Query("drugName")
	drugNumber := ctx.Query("drugNumber")
	batch := ctx.Query("batch")
	startTime := ctx.Query("startTime")
	endTime := ctx.Query("endTime")

	filter := bson.M{"customer_id": customerOID, "env_id": envOID}
	if status != 0 {
		filter["status"] = status
	}
	if drugName != "" {
		filter["name"] = drugName
	}
	if drugNumber != "" {
		filter["number"] = bson.M{"$regex": drugNumber}
	}
	if batch != "" {
		filter["batch_number"] = bson.M{"$regex": batch}
	}
	if startTime != "" && endTime != "" {
		filter["expiration_date"] = bson.M{
			"$gt": startTime,
			"$lt": endTime,
		}
	}

	total, err := tools.Database.Collection("medicine").CountDocuments(nil, filter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var d []models.Medicine
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		models.MedicineProject,
		{{Key: "$sort", Value: bson.D{{"serial_number", 1}}}},
		{{Key: "$skip", Value: start}},
		{{Key: "$limit", Value: limit}},
	}
	optTrue := true
	opt := &options.AggregateOptions{
		AllowDiskUse: &optTrue,
	}
	cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pipeline, opt)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &d)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	//盲态处理
	isBlindedRole, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return nil, err
	}
	if isBlindedRole {
		for i := 0; i < len(d); i++ {
			isBlindedDrug, _ := tools.IsBlindedDrug(envOID, d[i].Name)
			if isBlindedDrug {
				d[i].Name = tools.BlindData
			}
		}
	}
	packageIsOpen, _, _, _, _, _ := tools.IsOpenPackage(envOID)

	// 如果是基本研究并且是手动编码 去掉页面审核、修改入口
	basicMovement := true
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}

	if project.ProjectInfo.Type == 1 {
		var barcodeRule models.BarcodeRule
		err = tools.Database.Collection("barcode_rule").FindOne(nil, bson.M{"env_id": envOID}).Decode(&barcodeRule)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		// 手动编码
		if barcodeRule.CodeRule == 0 {
			basicMovement = false
		}
	}

	return map[string]interface{}{"total": total, "items": d, "packageIsOpen": packageIsOpen, "basicMovement": basicMovement}, nil
}

func (s *MedicineService) GetMedicineFreezeList(ctx *gin.Context, customerID string, projectID string, envID string, cohortID string, roleID string, freezeInstituteId string, start int, limit int) (map[string]interface{}, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	roleOID, _ := primitive.ObjectIDFromHex(roleID)
	freezeInstituteOID, _ := primitive.ObjectIDFromHex(freezeInstituteId)
	ipNumber := ctx.Query("ipNumber")
	var project models.Project
	d := make([]map[string]interface{}, 0)

	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}

	//判断当前登录的角色的分类
	var role models.ProjectRolePermission
	err = tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{"_id": roleOID}).Decode(&role)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	user, err := tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	match := bson.M{"user_id": user.ID, "customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	var instituteIds []primitive.ObjectID
	if role.Scope == "depot" {
		//如果当前角色的分类是depot,查看该用户分配的仓库，
		var userDepots []models.UserDepot
		cursor, err := tools.Database.Collection("user_depot").Find(nil, match)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &userDepots)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		for _, userDepot := range userDepots {
			instituteIds = append(instituteIds, userDepot.DepotID)
		}
	} else if role.Scope == "site" {
		//如果当前角色的分类是site，查看该用户分配的中心
		var userSites []models.UserSite
		cursor, err := tools.Database.Collection("user_site").Find(nil, match)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &userSites)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		for _, userSite := range userSites {
			instituteIds = append(instituteIds, userSite.SiteID)
		}
	}

	if (role.Scope == "depot" || role.Scope == "site") && len(instituteIds) <= 0 {
		return map[string]interface{}{"total": 0, "items": d}, nil
	}

	filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}

	if role.Scope != "study" {
		filter["institute_id"] = bson.M{"$in": instituteIds}
	}

	if freezeInstituteId != "" {
		filter["institute_id"] = freezeInstituteOID
	}

	if ipNumber != "" {
		var medicine models.Medicine
		err = tools.Database.Collection("medicine").FindOne(nil, bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "number": ipNumber}).Decode(&medicine)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		filter["$or"] = []bson.M{
			{"medicines": medicine.ID},
			{"history": medicine.ID},
		}
	}

	total, err := tools.Database.Collection("medicine_freeze").CountDocuments(nil, filter)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$sort", Value: bson.D{{"number", -1}}}},
		{{Key: "$skip", Value: start}},
		{{Key: "$limit", Value: limit}},
		{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "meta.created_by", "foreignField": "_id", "as": "meta.createdUser"}}},
		//{{Key: "$lookup", Value: bson.M{"from": "medicine", "localField": "medicines", "foreignField": "_id", "as": "m"}}},
		//{{Key: "$unwind", Value: bson.M{"path": "$m", "preserveNullAndEmptyArrays": true}}},
		//{{Key: "$sort", Value: bson.D{{"m.number", 1}}}},
		//{{Key: "$group", Value: bson.M{"_id": "$_id", "freeze": bson.M{"$first": "$$ROOT"}, "mediciness": bson.M{"$push": "$m"}}}},
		//{{Key: "$replaceRoot", Value: bson.M{"newRoot": bson.M{"$mergeObjects": bson.A{bson.M{"mediciness": "$mediciness"}, "$freeze"}}}}},
		{{Key: "$sort", Value: bson.D{{"number", -1}}}},
	}
	optTrue := true
	opt := &options.AggregateOptions{
		AllowDiskUse: &optTrue,
	}
	cursor, err := tools.Database.Collection("medicine_freeze").Aggregate(nil, pipepine, opt)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &d)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var attribute models.Attribute
	var cohortOID primitive.ObjectID
	attributeFilter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if cohortID != "" {
		cohortOID, _ = primitive.ObjectIDFromHex(cohortID)
		attributeFilter = bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID}
	}
	err = tools.Database.Collection("attribute").FindOne(nil, attributeFilter).Decode(&attribute)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, err
	}
	// isBlindedRole, err := tools.IsBlindedRole(roleID)
	// if err != nil {
	// 	return nil, err
	// }

	// isBlindDrugMap, _ := tools.IsBlindDrugMap(envOID)

	for _, freeze := range d {
		count := 0
		show := 0
		//if freeze["medicines"] != nil {
		//d[i]["medicines"] = d[i]["m"]
		if freeze["medicines"] != nil {
			show = show + len(freeze["medicines"].(primitive.A))
		}
		if freeze["history"] != nil {
			count = count + len(freeze["history"].(primitive.A))
		}

		if freeze["other_history_new"] != nil {
			count = count + len(freeze["other_history_new"].(primitive.A))
		}
		if freeze["other_medicines_new"] != nil {
			show = show + len(freeze["other_medicines_new"].(primitive.A))
		}
		freeze["count"] = count
		freeze["show"] = show

		//bl true解隔离 false解隔离审批
		bl := true
		isolationCount := 0
		if freeze["medicines"] != nil && project.DeIsolationApproval == 1 {
			//TODO 查询解隔离待审批的药物
			// for j := 0; j < len(d[i]["medicines"].(primitive.A)); j++ {
			// 	if d[i]["medicines"].(primitive.A)[j].(map[string]interface{})["isolation_approval_sign"] != nil && int(d[i]["medicines"].(primitive.A)[j].(map[string]interface{})["isolation_approval_sign"].(int32)) == 1 {
			// 		bl = false
			// 		isolationCount++
			// 	}
			// }

			filter := bson.M{"_id": bson.M{"$in": freeze["medicines"]}, "isolation_approval_sign": 1}
			isolationApprovalCount, err := tools.Database.Collection("medicine").CountDocuments(nil, filter)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if isolationApprovalCount > 0 {
				isolationCount = int(isolationApprovalCount)
				bl = false
			}

		}
		if freeze["other_medicines_new"] != nil && project.DeIsolationApproval == 1 {
			filter := bson.M{"_id": bson.M{"$in": freeze["other_medicines_new"]}, "isolation_approval_sign": 1}
			isolationApprovalCount, err := tools.Database.Collection("medicine_others").CountDocuments(nil, filter)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if isolationApprovalCount > 0 {
				isolationCount = isolationCount + int(isolationApprovalCount)
				bl = false
			}
		}
		freeze["bl"] = bl
		freeze["isolationCount"] = isolationCount
	}

	return map[string]interface{}{"total": total, "items": d}, nil
}

func (s *MedicineService) GetMedicineFreezeDetailsList(ctx *gin.Context, cohortId string, id string, ipNumber string, upperNumber string, lowerNumber string, attributeId string, roleId string, status int, sign int) (map[string]interface{}, error) {
	OID, _ := primitive.ObjectIDFromHex(id)

	// 查询隔离订单
	var medicineFreeze models.MedicineFreeze
	err := tools.Database.Collection("medicine_freeze").FindOne(nil, bson.M{"_id": OID}).Decode(&medicineFreeze)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	packageIsOpen, _, _, _, _, _ := tools.IsOpenPackage(medicineFreeze.EnvironmentID)
	isOtherDrugMap, _ := tools.IsOtherDrugMap(medicineFreeze.EnvironmentID)

	packageDrugNames := make(map[string]models.MedicinePackage)
	//otherSingleDrugNames := make([]string, 0)
	otherPackageDrugNames := make([]string, 0)
	for _, medicinePackage := range medicineFreeze.MedicinesPackage {
		packageDrugNames[medicinePackage.Name] = models.MedicinePackage{
			PackageMethod: medicinePackage.PackageMethod,
			PackageNumber: medicinePackage.PackageNumber,
		}
		if isOtherDrugMap[medicinePackage.Name] {
			if medicinePackage.PackageMethod {
				otherPackageDrugNames = append(otherPackageDrugNames, medicinePackage.Name)
			}
			// else {
			// 	otherSingleDrugNames = append(otherSingleDrugNames, medicinePackage.Name)
			// }
		}
	}

	//盲态处理
	isBlindedRole, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return nil, err
	}

	isBlindDrugMap, err := tools.IsBlindDrugMap(medicineFreeze.EnvironmentID)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	//编号药物
	var total int64
	var d []map[string]interface{}
	var medicineIds []string
	if len(medicineFreeze.History) > 0 {
		filter := bson.M{"_id": bson.M{"$in": medicineFreeze.Medicines}}
		if sign == 1 {
			filter = bson.M{"_id": bson.M{"$in": medicineFreeze.History}}
		}

		if sign == 1 {
			if status != 0 {
				filter["status"] = status
			}
		} else {
			if status == 4 {
				filter["status"] = status
				filter["isolation_approval_sign"] = bson.M{"$ne": 1}
			} else if status == 15 {
				filter["isolation_approval_sign"] = bson.M{"$eq": 1}
			} else {
				filter["status"] = status
			}
		}

		if ipNumber != "" {
			filter["number"] = ipNumber
		}

		if upperNumber != "" && lowerNumber != "" {
			filter["number"] = bson.M{"$gte": upperNumber, "$lte": lowerNumber}
		} else if upperNumber != "" && lowerNumber == "" {
			filter["number"] = bson.M{"$gte": upperNumber}
		} else if upperNumber == "" && lowerNumber != "" {
			filter["number"] = bson.M{"$lte": lowerNumber}
		}

		var err error
		total, err = tools.Database.Collection("medicine").CountDocuments(nil, filter)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		sortP := bson.D{{"number", 1}}
		if packageIsOpen {
			sortP = bson.D{{"package_number", 1}, {"number", 1}}
		}

		pipeline := mongo.Pipeline{
			{{Key: "$match", Value: filter}},
			{{Key: "$sort", Value: sortP}},
			// {{Key: "$skip", Value: start}},
			// {{Key: "$limit", Value: limit}},
		}
		cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pipeline)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &d)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		//解隔离页面输入框查询，如果改查询的药物是包装中的其中一条药物，需要整包展示数据
		var data []map[string]interface{}
		if ipNumber != "" {
			for i := 0; i < len(d); i++ {
				name := d[i]["name"].(string)
				if packageDrugNames[name].PackageMethod && d[i]["package_number"] != nil {
					var queryData []map[string]interface{}
					filter := bson.M{"name": name, "package_number": d[i]["package_number"].(string), "_id": bson.M{"$in": medicineFreeze.Medicines}}
					pipeline := mongo.Pipeline{
						{{Key: "$match", Value: filter}},
					}
					cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pipeline)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					err = cursor.All(nil, &queryData)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					data = append(data, queryData...)
				} else {
					data = append(data, d[i])
				}
			}
			d = data
		}

		//判断隔离订单里面的药物，同一个研究产品，同一个包装号的数量
		statistics := make(map[string][]models.StatisticsMedicinePackage)
		for _, medicine := range d {
			name := medicine["name"].(string)
			if packageDrugNames[name].PackageMethod && medicine["package_number"] != nil { //判断这个药物是否按照包装隔离，并且隔离的时候是否是整包隔离。
				//packageNumber := medicine["package_number"].(string)
				statisticsMedicine := statistics[name]
				if statisticsMedicine != nil {
					havePackage := false
					for index, statisticsMedicinePackage := range statisticsMedicine {
						if statisticsMedicinePackage.Number == medicine["package_number"] {
							havePackage = true
							statisticsMedicinePackage.Count = statisticsMedicinePackage.Count + 1
							statisticsMedicine[index] = statisticsMedicinePackage
							break
						}
					}
					if !havePackage { //有这个药物，但是没有这个包装号的数据
						statisticsMedicine = append(statisticsMedicine, models.StatisticsMedicinePackage{
							Number: medicine["package_number"].(string),
							Count:  1,
						})
					}
					statistics[name] = statisticsMedicine
				} else {
					statisticsMedicine = append(statisticsMedicine, models.StatisticsMedicinePackage{
						Number: medicine["package_number"].(string),
						Count:  1,
					})
					statistics[name] = statisticsMedicine
				}
			}
		}

		for i := 0; i < len(d); i++ {
			name := d[i]["name"].(string)
			if isBlindedRole && isBlindDrugMap[name] {
				d[i]["name"] = tools.BlindData
			}

			//查询这个药物下所有包装号的统计的数量
			count := 0
			statisticsMedicine := statistics[name]
			if statisticsMedicine != nil && d[i]["package_number"] != nil {
				for _, statisticsMedicinePackage := range statisticsMedicine {
					if statisticsMedicinePackage.Number == d[i]["package_number"] {
						count = statisticsMedicinePackage.Count
						break
					}
				}
			}

			if packageDrugNames[name].PackageMethod && d[i]["package_number"] != nil && count == packageDrugNames[name].PackageNumber { //判断这个药物是否按照包装隔离，并且隔离的时候是否是整包隔离。
				d[i]["packageDrug"] = true
			} else {
				d[i]["packageDrug"] = false
			}
		}

		// 返回待审批的药物ID

		var md []models.Medicine
		pipelineMd := mongo.Pipeline{
			{{Key: "$match", Value: filter}},
			{{Key: "$sort", Value: bson.D{{"number", 1}}}},
		}
		cursorMd, errMd := tools.Database.Collection("medicine").Aggregate(nil, pipelineMd)
		if errMd != nil {
			return nil, errors.WithStack(err)
		}
		err = cursorMd.All(nil, &md)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		for i := 0; i < len(md); i++ {
			medicineIds = append(medicineIds, md[i].ID.Hex())
		}
	}

	//未编号研究产品
	var otherData []map[string]interface{}
	if len(medicineFreeze.OtherHistoryNew) > 0 {
		otherFilter := bson.M{"_id": bson.M{"$in": medicineFreeze.OtherMedicinesNew}}
		if sign == 1 {
			otherFilter = bson.M{"_id": bson.M{"$in": medicineFreeze.OtherHistoryNew}}
		}

		if sign == 1 {
			if status != 0 {
				otherFilter["status"] = status
			}
		} else {
			if status == 4 {
				otherFilter["status"] = status
				otherFilter["isolation_approval_sign"] = bson.M{"$ne": 1}
			} else if status == 15 {
				otherFilter["isolation_approval_sign"] = bson.M{"$eq": 1}
			} else {
				otherFilter["status"] = status
			}
		}

		var groupOtherMedicineData []map[string]interface{}
		if medicineFreeze.InstituteType != 1 || medicineFreeze.OrderType == 1 {
			otherFilter["name"] = bson.M{"$nin": otherPackageDrugNames}
		}

		//单品数据
		groupPipeline := mongo.Pipeline{
			{{Key: "$match", Value: otherFilter}},
			{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "batchNumber": "$batch_number", "expirationDate": "$expiration_date"}, "availableCount": bson.M{"$sum": 1}}}},
			{{Key: "$project", Value: bson.M{"_id": 0,
				"name":           "$_id.name",
				"batchNumber":    "$_id.batchNumber",
				"expirationDate": "$_id.expirationDate",
				"count":          "$availableCount",
			}}},
		}
		cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, groupPipeline)
		err = cursor.All(nil, &groupOtherMedicineData)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		for _, other := range groupOtherMedicineData {
			name := other["name"].(string)
			if isBlindDrugMap[name] && isBlindedRole {
				encrypted, salt := tools.Encrypt(name)
				other["name"] = tools.BlindData
				other["salt"] = salt
				other["saltName"] = encrypted
			}
			if packageDrugNames[name].PackageMethod {
				other["package_method"] = true
			} else {
				other["package_method"] = false
			}
		}
		otherData = append(otherData, groupOtherMedicineData...)

		//包装数据
		if len(otherPackageDrugNames) > 0 && (medicineFreeze.InstituteType != 1 || medicineFreeze.OrderType == 1) {
			var packageOtherMedicineData []map[string]interface{}
			otherFilter["name"] = bson.M{"$in": otherPackageDrugNames}
			//判断药物列表是否有数据
			groupPipeline := mongo.Pipeline{
				{{Key: "$match", Value: otherFilter}},
				{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "batchNumber": "$batch_number", "expirationDate": "$expiration_date", "packageNumber": "$package_number"}}}},
				{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$_id.name", "batchNumber": "$_id.batchNumber", "expirationDate": "$_id.expirationDate"}, "availableCount": bson.M{"$sum": 1}}}},
				{{Key: "$project", Value: bson.M{"_id": 0,
					"name":           "$_id.name",
					"batchNumber":    "$_id.batchNumber",
					"expirationDate": "$_id.expirationDate",
					"count":          "$availableCount",
				}}},
			}
			cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, groupPipeline)
			err = cursor.All(nil, &packageOtherMedicineData)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			for _, other := range packageOtherMedicineData {
				name := other["name"].(string)
				if isBlindDrugMap[name] && isBlindedRole {
					encrypted, salt := tools.Encrypt(name)
					other["name"] = tools.BlindData
					other["salt"] = salt
					other["saltName"] = encrypted
				}
				if packageDrugNames[name].PackageMethod {
					other["package_method"] = true
					other["allCount"] = int(other["count"].(int32)) * packageDrugNames[name].PackageNumber
				} else {
					other["package_method"] = false
				}
			}
			otherData = append(otherData, packageOtherMedicineData...)
		}
		// otherPipeline := mongo.Pipeline{
		// 	{{Key: "$match", Value: otherFilter}},
		// 	{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "batchNumber": "$batch_number", "expirationDate": "$expiration_date"}, "count": bson.M{"$sum": 1}}}},
		// 	{{Key: "$project", Value: bson.M{
		// 		"_id":            0,
		// 		"count":          1,
		// 		"batchNumber":    "$_id.batchNumber",
		// 		"expirationDate": "$_id.expirationDate",
		// 		"name":           "$_id.name",
		// 		"packageNumber":  "$_id.packageNumber",
		// 	}}},
		// 	{{Key: "$sort", Value: bson.D{{"name", 1}}}},
		// }
		// otherCursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, otherPipeline)
		// if err != nil {
		// 	return nil, errors.WithStack(err)
		// }
		// err = otherCursor.All(nil, &otherData)
		// if err != nil {
		// 	return nil, errors.WithStack(err)
		// }
		// for i := 0; i < len(otherData); i++ {
		// 	name := otherData[i]["name"].(string)
		// 	if isBlindedRole && isBlindDrugMap[name] {
		// 		otherData[i]["name"] = tools.BlindData
		// 	}

		// if packageDrugNames[name].PackageMethod && d[i]["package_number"] != nil && count == packageDrugNames[name].PackageNumber { //判断这个药物是否按照包装隔离，并且隔离的时候是否是整包隔离。
		// 	otherData[i]["packageDrug"] = true
		// } else {
		// 	otherData[i]["packageDrug"] = false
		// }
		// }
	}

	return map[string]interface{}{"total": total, "items": d, "otherData": otherData, "medicineIds": medicineIds, "packageIsOpen": packageIsOpen, "packageDrugNames": packageDrugNames}, nil
}

func (s *MedicineService) UploadMedicines(ctx *gin.Context) error {
	customerOID, _ := primitive.ObjectIDFromHex(ctx.PostForm("customerId"))
	projectOID, _ := primitive.ObjectIDFromHex(ctx.PostForm("projectId"))
	envOID, _ := primitive.ObjectIDFromHex(ctx.PostForm("envId"))
	OID := envOID
	cohortOID, _ := primitive.ObjectIDFromHex(ctx.PostForm("cohortId"))
	if cohortOID != primitive.NilObjectID {
		OID = cohortOID
	}

	// 保存上传记录（报表）
	var medicineUploadRecord models.MedicineUploadRecord
	medicineUploadRecord.ID = primitive.NewObjectID()
	medicineUploadRecord.CustomerID = customerOID
	medicineUploadRecord.ProjectID = projectOID
	medicineUploadRecord.EnvironmentID = envOID
	medicineUploadRecord.CohortID = cohortOID
	medicineUploadRecord.Status = 0
	medicineUploadRecord.Time = time.Duration(time.Now().Unix())

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		files, err := ctx.FormFile("file")
		if err != nil {
			return nil, errors.WithStack(err)
		}
		file, err := files.Open()
		if err != nil {
			return nil, errors.WithStack(err)
		}
		f, _ := excelize.OpenReader(file)
		cell, _ := f.GetRows(f.GetSheetList()[0])
		var length = len(cell) - 1

		medicineUploadRecord.Name = files.Filename
		medicineUploadRecord.Row = length

		// 获取当前登陆用户
		user, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		medicineUploadRecord.UserId = user.ID

		drugNameSize, _ := strconv.Atoi(ctx.PostForm("drugNameLength"))
		drugNames := make([]models.DrugNameInfo, 0)
		checkDrugNames := make([]string, 0)
		drugSpecs := make(map[string]string)
		for index := 0; index < drugNameSize; index++ {
			drugNameStr := ctx.PostForm(fmt.Sprintf("drugNames[%d].drugName", index))
			specStr := ctx.PostForm(fmt.Sprintf("drugNames[%d].spec", index))
			drugNames = append(drugNames, models.DrugNameInfo{
				DrugName: drugNameStr,
				Spec:     specStr,
			})
			checkDrugNames = append(checkDrugNames, drugNameStr)
			drugSpecs[drugNameStr] = specStr
		}

		//混合包装是否打开、混包的药物
		isOpenPackage, _, packageDrugNames, mixedPackages, allMixedPackage, _ := tools.IsOpenPackage(envOID)

		if isOpenPackage {
			if len(drugNames) > 1 {
				//判断药物是否是混包
				isDrugFlag := false
				for _, mixedPackage := range mixedPackages {
					if len(mixedPackage) == len(drugNames) {
						for _, drugName := range drugNames {
							index := arrays.ContainsString(mixedPackage, drugName.DrugName)
							if index == -1 { //不存在
								break
							}
						}
						isDrugFlag = true
					}
				}
				if !isDrugFlag {
					return nil, tools.BuildServerError(ctx, "medicine_upload_drug_name")
				}
			} else {
				for _, mixedPackage := range mixedPackages {
					index := arrays.ContainsString(mixedPackage, drugNames[0].DrugName)
					if index != -1 && len(mixedPackage) > 1 { //存在
						return nil, tools.BuildServerError(ctx, "medicine_upload_drug_name")
					}
				}
			}
		}

		storehouseID, _ := primitive.ObjectIDFromHex(ctx.PostForm("storehouseId"))
		expirationDate := ctx.PostForm("expirationDate")
		batch := ctx.PostForm("batch")

		if length > 0 {
			packageNumber := 0
			drugNamePackage := make(map[string]int, len(drugNames))
			if isOpenPackage {
				var packageConfig models.MixedPackage
				for _, mixedPackage := range allMixedPackage {
					mixFlag := true
					if len(drugNames) == len(mixedPackage.PackageConfig) {
						for _, drugName := range drugNames {
							flag := false
							for _, packageConfig := range mixedPackage.PackageConfig {
								if drugName.DrugName == packageConfig.Name {
									flag = true
								}
							}
							if !flag {
								mixFlag = false
								break
							}
						}
						if mixFlag {
							packageConfig = mixedPackage
							break
						}
					}
				}
				for _, v := range packageConfig.PackageConfig {
					drugNamePackage[v.Name] = v.Number
					packageNumber = packageNumber + v.Number
				}
				if packageNumber > 0 && length%packageNumber != 0 {
					return nil, tools.BuildServerError(ctx, "medicine_upload_package_count")
				}
			}

			status := 0

			//利用map集合来验证重复
			medicineNumberMap := make(map[string]string)
			medicineSerialNumberMap := make(map[string]string)
			medicinePackageMap := make(map[string]bool)
			medicineSerialPackageMap := make(map[string]bool)
			packageNumberMap := make(map[string]int)
			packageSerialNumberMap := make(map[string]int)

			//每种药物数量
			drugNameCountMap := make(map[string]int)
			//每个包装每种药物数量
			packageCountMap := make(map[string]map[string]int)

			//验证研究产品号是否重复
			haveMedicines := []map[string]interface{}{}
			match := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
			pipepine := mongo.Pipeline{
				{{Key: "$match", Value: match}},
				{{Key: "$project", Value: bson.M{
					"_id":                 0,
					"id":                  "$_id",
					"name":                "$name",
					"number":              "$number",
					"packageNumber":       "$package_number",
					"serialNumber":        bson.M{"$ifNull": bson.A{"$serial_number", "$number"}},
					"packageSerialNumber": "$package_serial_number",
				}}},
			}
			cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pipepine)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &haveMedicines)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			// for _, hasMedicine := range hasMedicines {
			// 	MedicineNumberMap[hasMedicine.Number] = hasMedicine
			// 	MedicineSerialNumberMap[hasMedicine.SerialNumber] = hasMedicine
			// }

			for _, medicine := range haveMedicines {
				if medicine["number"] != nil && medicine["number"] != "" {
					medicineNumberMap[medicine["number"].(string)] = medicine["number"].(string)
				}
				if medicine["serialNumber"] != nil && medicine["serialNumber"] != "" {
					medicineSerialNumberMap[medicine["serialNumber"].(string)] = medicine["number"].(string)
				}
				if isOpenPackage && packageNumber > 0 {
					if medicine["packageNumber"] != nil && medicine["packageNumber"] != "" {
						if !medicinePackageMap[medicine["packageNumber"].(string)] {
							medicinePackageMap[medicine["packageNumber"].(string)] = true
						}
					}
					if medicine["packageSerialNumber"] != nil && medicine["packageSerialNumber"] != "" {
						if !medicineSerialPackageMap[medicine["packageSerialNumber"].(string)] {
							medicineSerialPackageMap[medicine["packageSerialNumber"].(string)] = true
						}
					}
				}
			}

			var medicines = make([]interface{}, len(cell)-1)
			var histories []models.History
			//var products []models.WmsProduct
			for index, v := range cell {
				if index >= 1 {
					if v == nil {
						return nil, tools.BuildServerError(ctx, "upload_medicines_cell")
					}

					indexExist := arrays.ContainsString(checkDrugNames, v[1])
					if indexExist == -1 {
						return nil, tools.BuildServerError(ctx, "upload_medicines_drugName")
					}

					if isOpenPackage && packageNumber > 0 {
						if len(v) < 4 || v[3] == "" {
							return nil, tools.BuildServerError(ctx, "medicine_upload_package_number")
						}
						if len(v) < 5 || v[4] == "" {
							return nil, tools.BuildServerError(ctx, "medicine_upload_package_serial_number")
						}
					}

					if v[0] != strings.TrimSpace(v[0]) || v[1] != strings.TrimSpace(v[1]) || v[2] != strings.TrimSpace(v[2]) || (isOpenPackage && packageNumber > 0 && v[3] != strings.TrimSpace(v[3]) && v[4] != strings.TrimSpace(v[4])) {
						return nil, tools.BuildServerError(ctx, "random_number_format_error_trim")
					}

					//判断该研究产品号是否已经存在
					medicineId := primitive.NewObjectID()
					if v[2] != "" && v[0] != "" {
						spec := ""
						drugSpec, Ok := drugSpecs[v[1]]
						if Ok {
							spec = drugSpec
						}
						newMedicine := models.Medicine{
							ID:             medicineId,
							CustomerID:     customerOID,
							ProjectID:      projectOID,
							EnvironmentID:  envOID,
							Name:           v[1],
							Number:         v[2],
							SerialNumber:   v[0],
							StorehouseID:   storehouseID,
							ExpirationDate: expirationDate,
							BatchNumber:    batch,
							Spec:           spec,
							Status:         status,
							//TtxSkuID:       ttxSkuId,
						}
						if isOpenPackage && packageNumber > 0 {
							if v[3] != "" {
								if _, ok := medicinePackageMap[v[3]]; ok {
									return nil, tools.BuildCustomError(locales.Tr(ctx, "medicine_duplicated_package_number") + v[3])
								}
								newMedicine.PackageNumber = v[3]
								packageNumberMap[v[3]] = packageNumberMap[v[3]] + 1
							} else {
								return nil, tools.BuildCustomError(locales.Tr(ctx, "medicine_upload_package_number"))
							}
							if v[4] != "" {
								if _, ok := medicineSerialPackageMap[v[4]]; ok {
									return nil, tools.BuildCustomError(locales.Tr(ctx, "medicine_duplicated_serial_package_number") + v[4])
								}
								newMedicine.PackageSerialNumber = v[4]
								packageSerialNumberMap[v[4]] = packageSerialNumberMap[v[4]] + 1
							} else {
								return nil, tools.BuildCustomError(locales.Tr(ctx, "medicine_upload_package_serial_number"))
							}

							packageCount, ok := packageCountMap[v[3]]
							if ok {
								pcount, pok := packageCount[v[1]]
								if pok {
									packageCount[v[1]] = pcount + 1
								} else {
									packageCount[v[1]] = 1
								}
								packageCountMap[v[3]] = packageCount
							} else {
								packageCount := make(map[string]int)
								packageCount[v[1]] = 1
								packageCountMap[v[3]] = packageCount
							}

						}
						medicines[index-1] = newMedicine
						// products = append(products, models.WmsProduct{
						// 	Number:         v[1],
						// 	Name:           drugName,
						// 	Spec:           spec,
						// 	BatchNumber:    batch,
						// 	ExpirationDate: expirationDate,
						// })
						if _, ok := medicineNumberMap[v[2]]; ok {
							return nil, tools.BuildCustomError(locales.Tr(ctx, "medicine_duplicated_number") + v[2])
						} else if _, ok := medicineSerialNumberMap[v[0]]; ok {
							return nil, tools.BuildCustomError(locales.Tr(ctx, "medicine_duplicated_serial_number") + v[0])
						} else {
							medicineNumberMap[v[2]] = v[2]
							medicineSerialNumberMap[v[0]] = v[0]
						}

						count, ok := drugNameCountMap[v[1]]
						if ok {
							drugNameCountMap[v[1]] = count + 1
						} else {
							drugNameCountMap[v[1]] = 1
						}

						//药物history
						history := models.History{
							Key:  "history.medicine.uploadCanUse",
							OID:  medicineId,
							Data: bson.M{"number": v[2]},
							Time: time.Duration(time.Now().Unix()),
							UID:  user.ID,
							User: user.Name,
						}
						histories = append(histories, history)
					}
				}
			}

			if isOpenPackage && packageNumber > 0 {
				for _, packageCount := range packageNumberMap {
					if packageCount != packageNumber {
						return nil, tools.BuildCustomError(locales.Tr(ctx, "medicine_upload_package_count"))
					}
				}
				for _, packageSerialNumberCount := range packageSerialNumberMap {
					if packageSerialNumberCount != packageNumber {
						return nil, tools.BuildCustomError(locales.Tr(ctx, "medicine_upload_package_serial_count"))
					}
				}

				for drugName, packageNumber := range drugNamePackage {
					if drugNameCountMap[drugName]%packageNumber != 0 {
						return nil, tools.BuildCustomError(locales.Tr(ctx, "medicine_upload_package_count"))
					}
				}

				for pnumber, drugPackCount := range packageCountMap {
					for drugName, count := range drugPackCount {
						if packageDrugNames[drugName] != count {
							return nil, tools.BuildCustomError(pnumber + locales.Tr(ctx, "medicine_upload_package_count"))
						}
					}
				}

			}

			if _, err := tools.Database.Collection("medicine").InsertMany(nil, medicines); err != nil {
				return nil, errors.WithStack(err)
			}
			ctx.Set("HISTORY", histories)
			InsertUploadMedicineLog(ctx, nil, OID, 1, length, files.Filename, drugNames)
		} else {
			return nil, tools.BuildServerError(ctx, "upload_medicines_info")
		}
		return nil, nil
	}

	err := tools.Transaction(callback)
	if err != nil {
		//添加文件上传记录
		medicineUploadRecord.Status = 1
		medicineUploadRecord.Reason = err
		tools.Database.Collection("medicine_upload_record").InsertOne(nil, medicineUploadRecord)
		return err
	}
	//添加文件上传记录
	tools.Database.Collection("medicine_upload_record").InsertOne(nil, medicineUploadRecord)
	return nil
}

func (s *MedicineService) UploadMedicinesPacklist(ctx *gin.Context) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		customerOID, _ := primitive.ObjectIDFromHex(ctx.PostForm("customerId"))
		envOID, _ := primitive.ObjectIDFromHex(ctx.PostForm("envId"))
		OID := envOID
		cohortOID, _ := primitive.ObjectIDFromHex(ctx.PostForm("cohortId"))
		if cohortOID != primitive.NilObjectID {
			OID = cohortOID
		}
		files, err := ctx.FormFile("file")
		if err != nil {
			return nil, errors.WithStack(err)
		}
		file, err := files.Open()
		if err != nil {
			return nil, errors.WithStack(err)
		}
		f, _ := excelize.OpenReader(file)
		cell, _ := f.GetRows(f.GetSheetList()[0])
		var length = len(cell) - 1
		if length > 0 {
			for index, v := range cell {
				if index >= 1 {
					if v == nil {
						return nil, tools.BuildServerError(ctx, "upload_medicines_cell")
					}
					//验证研究产品号是否存在
					var hasMedicines models.Medicine
					filter := bson.M{"customer_id": customerOID, "env_id": envOID, "number": v[0]}
					if err := tools.Database.Collection("medicine").FindOne(sctx, filter).Decode(&hasMedicines); err != nil {
						return nil, tools.BuildCustomError(v[0] + locales.Tr(ctx, "medicine_packlist_upload_check"))

					} else {
						var packlist []string
						for vindex, packageInfo := range v {
							if vindex == 1 && packageInfo == "" {
								return nil, tools.BuildCustomError(v[0] + locales.Tr(ctx, "medicine_packlist_upload_firstPack"))
							}
							if vindex >= 1 && packageInfo != "" {
								packlist = append(packlist, packageInfo)
							}
						}

						update := bson.M{"$set": bson.M{
							"packlist": packlist,
						}}
						if _, err := tools.Database.Collection("medicine").UpdateOne(sctx, filter, update); err != nil {
							return nil, errors.WithStack(err)
						}
					}
				}
			}
			insertUploadPacklistLog(ctx, nil, OID, 1, length, files.Filename)
		} else {
			return nil, tools.BuildServerError(ctx, "upload_medicines_info")
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func insertUploadPacklistLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, count int, fileName string) error {
	// 保存项目日志
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	if types != 3 {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.medicinesList",
			TranKey: "operation_log.uploadMedicines.count",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: count,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.medicinesList",
			TranKey: "operation_log.uploadMedicines.fileName",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: fileName,
			},
		})
	}
	marks := []models.Mark{}
	// marks = append(marks,
	// 	models.Mark{
	// 		Label: "operation_log.label.uploadPacklist",
	// 		Value: "",
	// 		Blind: false,
	// 	})

	err := tools.SaveOperation(ctx, sctx, "operation_log.module.medicinesList_uploadPacklist", OID, types, OperationLogFieldGroups, marks, OID)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func getBatchManagementByStatusCount(ctx *gin.Context, customerOID primitive.ObjectID, envOID primitive.ObjectID, updateBatch models.BatchManagement, result map[int32]int) (map[int32]int, error) {
	batchNumber := updateBatch.BatchNumber
	expirationDate := updateBatch.ExpirationDate
	name := updateBatch.Name
	positionOID := updateBatch.PositionId
	typeV := updateBatch.Type
	if typeV != 3 {
		if name == "-" { //有编号
			var data []map[string]interface{}
			filter := bson.M{}
			if typeV == 1 { //中心
				filter = bson.M{"customer_id": customerOID, "env_id": envOID, "batch_number": batchNumber, "expiration_date": expirationDate,
					"site_id": positionOID, "status": bson.M{"$in": bson.A{1, 4, 6, 7, 14, 20}}}
			} else if typeV == 2 { //库房
				filter = bson.M{"customer_id": customerOID, "env_id": envOID, "batch_number": batchNumber, "expiration_date": expirationDate,
					"storehouse_id": positionOID, "status": bson.M{"$in": bson.A{1, 4, 6, 7, 14, 20}}}
			}
			pipepine := mongo.Pipeline{
				{{Key: "$match", Value: filter}},
				{{Key: "$group", Value: bson.M{"_id": bson.M{"status": "$status"}, "count": bson.M{"$sum": 1}}}},
			}
			cursor, err := tools.Database.Collection("medicine").Aggregate(ctx, pipepine)
			if err != nil {
				return result, errors.WithStack(err)
			}
			err = cursor.All(nil, &data)
			if err != nil {
				return result, errors.WithStack(err)
			}
			for _, medicine := range data {
				id := medicine["_id"].(map[string]interface{})
				key := id["status"].(int32)
				if v, ok := result[key]; ok {
					result[key] = v + int(medicine["count"].(int32))
				} else {
					result[key] = int(medicine["count"].(int32))
				}
			}
		} else { //未编号
			var otherMedicineData []map[string]interface{}
			otherFilter := bson.M{}
			if typeV == 1 { //中心
				otherFilter = bson.M{"customer_id": customerOID, "env_id": envOID, "batch_number": batchNumber, "expiration_date": expirationDate, "name": name,
					"site_id": positionOID, "status": bson.M{"$in": bson.A{1, 4, 6, 7, 14, 20}}}
			} else if typeV == 2 { //库房
				otherFilter = bson.M{"customer_id": customerOID, "env_id": envOID, "batch_number": batchNumber, "expiration_date": expirationDate, "name": name,
					"storehouse_id": positionOID, "status": bson.M{"$in": bson.A{1, 4, 6, 7, 14, 20}}}
			}
			pipepine := mongo.Pipeline{
				{{Key: "$match", Value: otherFilter}},
				{{Key: "$group", Value: bson.M{"_id": bson.M{"status": "$status"}, "count": bson.M{"$sum": 1}}}},
			}
			cursor, err := tools.Database.Collection("medicine_others").Aggregate(ctx, pipepine)
			if err != nil {
				return result, errors.WithStack(err)
			}
			err = cursor.All(nil, &otherMedicineData)
			if err != nil {
				return result, errors.WithStack(err)
			}
			for _, otherMedicine := range otherMedicineData {
				id := otherMedicine["_id"].(map[string]interface{})
				key := id["status"].(int32)
				if v, ok := result[key]; ok {
					result[key] = v + int(otherMedicine["count"].(int32))
				} else {
					result[key] = int(otherMedicine["count"].(int32))
				}
			}
		}
	} else {
		//查询订单号
		var order models.MedicineOrder
		err := tools.Database.Collection("medicine_order").FindOne(ctx, bson.M{"_id": positionOID}).Decode(&order)
		if err != nil {
			return result, errors.WithStack(err)
		}
		var medOrderStatus int32
		if order.Status == 6 {
			medOrderStatus = 11
		} else if order.Status == 1 {
			medOrderStatus = 2
		} else if order.Status == 2 {
			medOrderStatus = 3
		}
		if v, ok := result[medOrderStatus]; ok {
			result[medOrderStatus] = v + int(updateBatch.Count)
		} else {
			result[medOrderStatus] = int(updateBatch.Count)
		}
	}

	return result, nil
}

func (s *MedicineService) GetBatchGroupStatusMult(ctx *gin.Context, batchManagement models.BatchManagementRequst) (map[int32]int, error) {
	customerOID := batchManagement.CustomerID
	envOID := batchManagement.EnvironmentID
	result := make(map[int32]int)
	var err error

	for _, updateBatch := range batchManagement.UpdateBatchs {
		result, err = getBatchManagementByStatusCount(ctx, customerOID, envOID, updateBatch, result)
		if err != nil {
			return result, errors.WithStack(err)
		}
	}

	return result, nil
}

func (s *MedicineService) GetBatchGroupStatus(ctx *gin.Context, customerID string, envID string, roleID string) (map[int32]int, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	result := make(map[int32]int)
	var err error

	var updateBatch models.BatchManagement
	updateBatch.BatchNumber = ctx.Query("batchNumber")
	updateBatch.ExpirationDate = ctx.Query("expirationDate")
	updateBatch.Name = ctx.Query("name")
	countV, _ := strconv.ParseInt(ctx.Query("count"), 10, 64)
	updateBatch.Count = int32(countV)

	positionId := ctx.Query("positionId")
	positionOID, _ := primitive.ObjectIDFromHex(positionId)
	updateBatch.PositionId = positionOID
	typeV, _ := strconv.Atoi(ctx.Query("type"))
	updateBatch.Type = typeV

	result, err = getBatchManagementByStatusCount(ctx, customerOID, envOID, updateBatch, result)
	if err != nil {
		return result, errors.WithStack(err)
	}

	return result, nil
}

func (s *MedicineService) GetBatchList(ctx *gin.Context, customerID string, envID string, roleID string, queryValue string, queryTypes string, start int, limit int) (map[string]interface{}, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)

	filter := bson.M{"customer_id": customerOID, "env_id": envOID, "status": bson.M{"$in": bson.A{1, 4, 6, 7, 14, 20}}}

	//编号药物，库房数据
	var data []map[string]interface{}
	if (queryTypes != "" && strings.Contains(queryTypes, "2")) || queryTypes == "" {
		stroehouseMatch := bson.M{"$and": bson.A{
			filter,
			bson.M{"storehouse_id": bson.M{"$ne": nil}},
			bson.M{"storehouse_id": bson.M{"$ne": ""}},
			bson.M{"storehouse_id": bson.M{"$ne": primitive.NilObjectID}},
		}}
		pipepine := mongo.Pipeline{
			{{Key: "$match", Value: stroehouseMatch}},
			{{Key: "$group", Value: bson.M{"_id": bson.M{"batchNumber": "$batch_number", "expirationDate": "$expiration_date",
				"storehouse": "$storehouse_id"}, "count": bson.M{"$sum": 1}}}},
		}
		cursor, err := tools.Database.Collection("medicine").Aggregate(ctx, pipepine)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &data)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	//编号药物，中心数据
	var siteData []map[string]interface{}
	if (queryTypes != "" && strings.Contains(queryTypes, "1")) || queryTypes == "" {

		siteMatch := bson.M{"$and": bson.A{
			filter,
			bson.M{"site_id": bson.M{"$ne": nil}},
			bson.M{"site_id": bson.M{"$ne": ""}},
			bson.M{"site_id": bson.M{"$ne": primitive.NilObjectID}},
		}}
		siteDataPipepine := mongo.Pipeline{
			{{Key: "$match", Value: siteMatch}},
			{{Key: "$group", Value: bson.M{"_id": bson.M{"batchNumber": "$batch_number", "expirationDate": "$expiration_date",
				"site": "$site_id"}, "count": bson.M{"$sum": 1}}}},
		}
		siteDataCursor, err := tools.Database.Collection("medicine").Aggregate(ctx, siteDataPipepine)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = siteDataCursor.All(ctx, &siteData)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	//编号药物，订单数据
	var orderData []map[string]interface{}
	if (queryTypes != "" && strings.Contains(queryTypes, "3")) || queryTypes == "" {
		orderFilter := bson.M{"customer_id": customerOID, "env_id": envOID, "status": bson.M{"$in": bson.A{2, 3, 11}}}
		orderMatch := bson.M{"$and": bson.A{
			orderFilter,
			bson.M{"order_id": bson.M{"$ne": nil}},
			bson.M{"order_id": bson.M{"$ne": ""}},
			bson.M{"order_id": bson.M{"$ne": primitive.NilObjectID}},
		}}
		orderPipepine := mongo.Pipeline{
			{{Key: "$match", Value: orderMatch}},
			{{Key: "$group", Value: bson.M{"_id": bson.M{"batchNumber": "$batch_number", "expirationDate": "$expiration_date",
				"order": "$order_id"}, "count": bson.M{"$sum": 1}}}},
		}
		orderCursor, err := tools.Database.Collection("medicine").Aggregate(ctx, orderPipepine)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = orderCursor.All(nil, &orderData)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	//未编号药物，库房数据
	//otherFilter := bson.M{"customer_id": customerOID, "env_id": envOID}
	var otherData []map[string]interface{}
	if (queryTypes != "" && strings.Contains(queryTypes, "2")) || queryTypes == "" {
		otherStroehouseMatch := bson.M{"$and": bson.A{
			filter,
			bson.M{"storehouse_id": bson.M{"$ne": nil}},
			bson.M{"storehouse_id": bson.M{"$ne": ""}},
			bson.M{"storehouse_id": bson.M{"$ne": primitive.NilObjectID}},
		}}
		otherPipepine := mongo.Pipeline{
			{{Key: "$match", Value: otherStroehouseMatch}},
			{{Key: "$group", Value: bson.M{"_id": bson.M{"batchNumber": "$batch_number", "expirationDate": "$expiration_date", "name": "$name",
				"storehouse": "$storehouse_id"}, "count": bson.M{"$sum": 1}}}},
		}

		// otherPipepine := mongo.Pipeline{
		// 	{{Key: "$match", Value: otherStroehouseMatch}},
		// 	{{Key: "$group", Value: bson.M{"_id": bson.M{"batchNumber": "$info.batch", "expirationDate": "$info.expire_date", "name": "$info.name",
		// 		"storehouse": "$storehouse_id"}, "count": bson.M{"$sum": "$info.count"},
		// 		"quarantined_count": bson.M{"$sum": "$info.quarantined_count"},
		// 		"lost_count":        bson.M{"$sum": "$info.lost_count"},
		// 		"expired_count":     bson.M{"$sum": "$info.expired_count"},
		// 		"frozen_count":      bson.M{"$sum": "$info.frozen_count"},
		// 		"locked_count":      bson.M{"$sum": "$info.locked_count"}}}},
		// }
		otherCursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, otherPipepine)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = otherCursor.All(nil, &otherData)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}
	//未编号药物，中心数据
	var siteOtherData []map[string]interface{}
	if (queryTypes != "" && strings.Contains(queryTypes, "1")) || queryTypes == "" {
		siteOtherMatch := bson.M{"$and": bson.A{
			filter,
			bson.M{"site_id": bson.M{"$ne": nil}},
			bson.M{"site_id": bson.M{"$ne": ""}},
			bson.M{"site_id": bson.M{"$ne": primitive.NilObjectID}},
		}}
		siteOtherDataPipepine := mongo.Pipeline{
			{{Key: "$match", Value: siteOtherMatch}},
			{{Key: "$group", Value: bson.M{"_id": bson.M{"batchNumber": "$batch_number", "expirationDate": "$expiration_date", "name": "$name",
				"site": "$site_id"}, "count": bson.M{"$sum": 1}}}},
		}
		// siteOtherDataPipepine := mongo.Pipeline{
		// 	{{Key: "$match", Value: siteOtherMatch}},
		// 	{{Key: "$group", Value: bson.M{"_id": bson.M{"batchNumber": "$info.batch", "expirationDate": "$info.expire_date", "name": "$info.name",
		// 		"site": "$institute_id"}, "count": bson.M{"$sum": "$info.count"},
		// 		"quarantined_count": bson.M{"$sum": "$info.quarantined_count"},
		// 		"lost_count":        bson.M{"$sum": "$info.lost_count"},
		// 		"expired_count":     bson.M{"$sum": "$info.expired_count"},
		// 		"frozen_count":      bson.M{"$sum": "$info.frozen_count"},
		// 		"locked_count":      bson.M{"$sum": "$info.locked_count"}}}},
		// }
		siteOtherDataCursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, siteOtherDataPipepine)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = siteOtherDataCursor.All(nil, &siteOtherData)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	//未编号药物，订单数据
	var orderOtherData []map[string]interface{}
	if (queryTypes != "" && strings.Contains(queryTypes, "3")) || queryTypes == "" {
		//编号药物，订单数据
		orderOtherFilter := bson.M{"customer_id": customerOID, "env_id": envOID, "status": bson.M{"$in": bson.A{2, 3, 11}}}
		orderOtherMatch := bson.M{"$and": bson.A{
			orderOtherFilter,
			bson.M{"order_id": bson.M{"$ne": nil}},
			bson.M{"order_id": bson.M{"$ne": ""}},
			bson.M{"order_id": bson.M{"$ne": primitive.NilObjectID}},
		}}
		orderPipepine := mongo.Pipeline{
			{{Key: "$match", Value: orderOtherMatch}},
			{{Key: "$group", Value: bson.M{"_id": bson.M{"batchNumber": "$batch_number", "expirationDate": "$expiration_date", "name": "$name",
				"order": "$order_id"}, "count": bson.M{"$sum": 1}}}},
		}
		orderCursor, err := tools.Database.Collection("medicine_others").Aggregate(ctx, orderPipepine)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = orderCursor.All(nil, &orderOtherData)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}
	//查询仓库
	match := bson.M{
		"customer_id": customerOID,
		"env_id":      envOID,
		"deleted":     2,
	}
	storeMap := make(map[primitive.ObjectID]string)
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "storehouse",
			"localField":   "storehouse_id",
			"foreignField": "_id",
			"as":           "detail",
		}}},
		{{Key: "$unwind", Value: "$detail"}},
		{{Key: "$project", Value: bson.M{
			"_id":    0,
			"id":     "$_id",
			"number": "$detail.number",
			"name":   "$detail.name",
			"nameEn": "$detail.name",
		}}},
	}
	var projectStorehouse []map[string]interface{}
	storeCursor, storeErr := tools.Database.Collection("project_storehouse").Aggregate(nil, pipeline)
	if storeErr != nil {
		return nil, errors.WithStack(storeErr)
	}
	storeErr = storeCursor.All(nil, &projectStorehouse)
	if storeErr != nil {
		return nil, errors.WithStack(storeErr)
	}
	for _, projectStore := range projectStorehouse {
		storeMap[projectStore["id"].(primitive.ObjectID)] = projectStore["name"].(string)
	}

	//查询中心名称
	siteMap := make(map[primitive.ObjectID]string)
	pipeline = mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$project", Value: bson.M{
			"_id":    0,
			"id":     "$_id",
			"number": "$number",
			"name":   bson.M{"$concat": bson.A{"$number", "-", models.ProjectSiteNameBson(ctx)}},
		}}},
	}
	var projectSites []map[string]interface{}
	siteCursor, siteErr := tools.Database.Collection("project_site").Aggregate(nil, pipeline)
	if siteErr != nil {
		return nil, errors.WithStack(siteErr)
	}
	siteErr = siteCursor.All(nil, &projectSites)
	if siteErr != nil {
		return nil, errors.WithStack(siteErr)
	}
	for _, projectSite := range projectSites {
		siteMap[projectSite["id"].(primitive.ObjectID)] = projectSite["name"].(string)
	}
	//返回的数据
	var result []models.BatchManagement
	//编号数据处理
	for _, v := range data {
		var coverData models.BatchManagement
		id := v["_id"].(map[string]interface{})
		if id["storehouse"] != nil && id["storehouse"].(primitive.ObjectID) != primitive.NilObjectID {
			coverData.Type = 2
			coverData.PositionId = id["storehouse"].(primitive.ObjectID)
			coverData.Position = storeMap[id["storehouse"].(primitive.ObjectID)]
		}
		coverData.ExpirationDate = id["expirationDate"].(string)
		coverData.BatchNumber = id["batchNumber"].(string)
		coverData.Name = "-"
		coverData.Count = v["count"].(int32)
		coverData.IsOther = false
		if queryValue != "" {
			if strings.Contains(coverData.BatchNumber, queryValue) || strings.Contains(coverData.ExpirationDate, queryValue) || strings.Contains(coverData.Position, queryValue) {
				result = append(result, coverData)
			}
		} else {
			result = append(result, coverData)
		}
	}
	for _, v := range siteData {
		var coverData models.BatchManagement
		id := v["_id"].(map[string]interface{})
		if id["site"] != nil && id["site"].(primitive.ObjectID) != primitive.NilObjectID {
			coverData.Type = 1
			coverData.PositionId = id["site"].(primitive.ObjectID)
			coverData.Position = siteMap[id["site"].(primitive.ObjectID)]
		}
		coverData.ExpirationDate = id["expirationDate"].(string)
		coverData.BatchNumber = id["batchNumber"].(string)
		coverData.Name = "-"
		coverData.Count = v["count"].(int32)
		coverData.IsOther = false
		if queryValue != "" {
			if strings.Contains(coverData.BatchNumber, queryValue) || strings.Contains(coverData.ExpirationDate, queryValue) || strings.Contains(coverData.Position, queryValue) {
				result = append(result, coverData)
			}
		} else {
			result = append(result, coverData)
		}
	}
	orderMap := make(map[primitive.ObjectID]models.MedicineOrder)
	for _, v := range orderData {
		var coverData models.BatchManagement
		id := v["_id"].(map[string]interface{})
		coverData.Type = 3
		if id["order"] != nil {
			coverData.PositionId = id["order"].(primitive.ObjectID)
			//查询订单号
			position, ok := orderMap[id["order"].(primitive.ObjectID)]
			if !ok {
				var order models.MedicineOrder
				_ = tools.Database.Collection("medicine_order").FindOne(nil, bson.M{"_id": id["order"].(primitive.ObjectID)}).Decode(&order)
				orderMap[id["order"].(primitive.ObjectID)] = order
				position = order
			}
			coverData.Position = position.OrderNumber
			coverData.Status = int32(position.Status)
		}
		coverData.ExpirationDate = id["expirationDate"].(string)
		coverData.BatchNumber = id["batchNumber"].(string)
		coverData.Name = "-"
		coverData.Count = v["count"].(int32)
		coverData.IsOther = false
		if queryValue != "" {
			if strings.Contains(coverData.BatchNumber, queryValue) || strings.Contains(coverData.ExpirationDate, queryValue) || strings.Contains(coverData.Position, queryValue) {
				result = append(result, coverData)
			}
		} else {
			result = append(result, coverData)
		}
	}

	isOpenPackage, packageAllDrugNames, _, _, _, err := tools.IsOpenPackage(envOID)

	//未编号数据处理
	for _, v := range otherData {
		var coverData models.BatchManagement
		coverData.Count = v["count"].(int32)
		coverData.IsOther = true
		id := v["_id"].(map[string]interface{})
		name := id["name"].(string)
		if isOpenPackage && packageAllDrugNames[name] > 0 {
			coverData.IsPackage = true
			coverData.PackageNumber = packageAllDrugNames[name]
		}
		if coverData.Count > 0 {
			if id["storehouse"] != nil && id["storehouse"].(primitive.ObjectID) != primitive.NilObjectID {
				coverData.Type = 2
				coverData.PositionId = id["storehouse"].(primitive.ObjectID)
				coverData.Position = storeMap[id["storehouse"].(primitive.ObjectID)]
			}
			coverData.ExpirationDate = id["expirationDate"].(string)
			coverData.BatchNumber = id["batchNumber"].(string)
			coverData.Name = name
			if queryValue != "" {
				if strings.Contains(coverData.BatchNumber, queryValue) || strings.Contains(coverData.ExpirationDate, queryValue) || strings.Contains(coverData.Position, queryValue) {
					result = append(result, coverData)
				}
			} else {
				result = append(result, coverData)
			}
		}
	}
	for _, v := range siteOtherData {
		var coverData models.BatchManagement
		coverData.Count = v["count"].(int32)
		coverData.IsOther = true
		id := v["_id"].(map[string]interface{})
		name := id["name"].(string)
		if isOpenPackage && packageAllDrugNames[name] > 0 {
			coverData.IsPackage = true
			coverData.PackageNumber = packageAllDrugNames[name]
		}
		if coverData.Count > 0 {
			if id["site"] != nil && id["site"].(primitive.ObjectID) != primitive.NilObjectID {
				coverData.Type = 1
				coverData.PositionId = id["site"].(primitive.ObjectID)
				coverData.Position = siteMap[id["site"].(primitive.ObjectID)]
			}
			coverData.ExpirationDate = id["expirationDate"].(string)
			coverData.BatchNumber = id["batchNumber"].(string)
			coverData.Name = name
			if queryValue != "" {
				if strings.Contains(coverData.BatchNumber, queryValue) || strings.Contains(coverData.ExpirationDate, queryValue) || strings.Contains(coverData.Position, queryValue) {
					result = append(result, coverData)
				}
			} else {
				result = append(result, coverData)
			}
		}
	}
	for _, v := range orderOtherData {
		var coverData models.BatchManagement
		id := v["_id"].(map[string]interface{})
		coverData.Type = 3
		if id["order"] != nil {
			coverData.PositionId = id["order"].(primitive.ObjectID)
			//查询订单号
			position, ok := orderMap[id["order"].(primitive.ObjectID)]
			if !ok {
				var order models.MedicineOrder
				_ = tools.Database.Collection("medicine_order").FindOne(nil, bson.M{"_id": id["order"].(primitive.ObjectID)}).Decode(&order)
				orderMap[id["order"].(primitive.ObjectID)] = order
				position = order
			}
			coverData.Position = position.OrderNumber
			coverData.Status = int32(position.Status)
			coverData.OrderType = position.Type
		}
		coverData.ExpirationDate = id["expirationDate"].(string)
		coverData.BatchNumber = id["batchNumber"].(string)
		coverData.Name = id["name"].(string)
		coverData.IsOther = true
		coverData.Count = v["count"].(int32)
		if isOpenPackage && packageAllDrugNames[id["name"].(string)] > 0 {
			coverData.IsPackage = true
			coverData.PackageNumber = packageAllDrugNames[id["name"].(string)]
		}
		if queryValue != "" {
			if strings.Contains(coverData.BatchNumber, queryValue) || strings.Contains(coverData.ExpirationDate, queryValue) || strings.Contains(coverData.Position, queryValue) {
				result = append(result, coverData)
			}
		} else {
			result = append(result, coverData)
		}
	}

	//数据排序处理
	sort.SliceStable(result, func(i int, j int) bool {
		datai := result[i]
		dataj := result[j]
		if datai.ExpirationDate < dataj.ExpirationDate {
			return true
		}

		if datai.ExpirationDate > dataj.ExpirationDate {
			return false
		}

		if datai.BatchNumber > dataj.BatchNumber {
			return true
		}

		if datai.BatchNumber < dataj.BatchNumber {
			return false
		}

		if datai.Type < dataj.Type {
			return false
		}

		if datai.Type > dataj.Type {
			return true
		}

		return datai.Position < dataj.Position
	})

	end := start + limit
	if len(result) < end {
		end = len(result)
	}

	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return nil, err
	}
	items := result[start:end]

	blindDrug := make(map[string]bool)
	for i, v := range items {
		isBlind, ok := blindDrug[v.Name]
		if ok {
			if isBlindedRole && isBlind {
				items[i].Name = tools.BlindData
			}
		} else {
			isBlindedDrug, _ := tools.IsBlindedDrug(envOID, v.Name)
			if isBlindedRole && isBlindedDrug {
				items[i].Name = tools.BlindData
				blindDrug[v.Name] = true
			}
		}

	}

	return map[string]interface{}{"items": items, "total": len(result)}, nil
}

// 此方法已作废
// func (s *MedicineService) QueryMedicineBatchList(ctx *gin.Context, orderInfo models.QueryOrderInfo) (map[string]interface{}, error) {
// 	canCreateOrder := true
// 	message := ""
// 	var medicineData []models.Medicine
// 	projectOID := orderInfo.ProjectID
// 	customerOID := orderInfo.CustomerID
// 	envOID := orderInfo.EnvID

// 	//起运地
// 	sendOID := orderInfo.SendID
// 	//补充方式
// 	mode := orderInfo.Mode
// 	//供应计划
// 	supplyPlanOID := orderInfo.SupplyPlanID

// 	filter := bson.M{"project_id": projectOID, "customer_id": customerOID, "env_id": envOID, "status": 1}
// 	sendId := "site_id"
// 	if sendOID != primitive.NilObjectID {
// 		//判断该sendId是仓库还是中心
// 		match := bson.M{"_id": sendOID}
// 		siteCount, _ := tools.Database.Collection("project_site").CountDocuments(nil, match)
// 		if siteCount > 0 {
// 			filter["site_id"] = sendOID
// 		} else {
// 			sendId = "storehouse_id"
// 			filter["storehouse_id"] = sendOID
// 		}
// 	}

// 	var data []map[string]interface{}

// 	//补充方式：发药数量
// 	if mode == 1 {
// 		pipepine := mongo.Pipeline{
// 			{{Key: "$match", Value: filter}},
// 			{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "batchNumber": "$batch_number", "expirationDate": "$expiration_date"}, "availableCount": bson.M{"$sum": 1}}}},
// 		}
// 		cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pipepine)
// 		if err != nil {
// 			return nil, errors.WithStack(err)
// 		}
// 		err = cursor.All(nil, &data)
// 		if err != nil {
// 			return nil, errors.WithStack(err)
// 		}
// 	} else { //补充方式：再供应量/最大缓冲量
// 		//查询项目属性
// 		var attribute models.Attribute
// 		attributeMatch := bson.M{
// 			"customer_id": customerOID,
// 			"env_id":      envOID,
// 			"project_id":  projectOID,
// 		}
// 		if orderInfo.CohortID != primitive.NilObjectID {
// 			attributeMatch["cohort_id"] = orderInfo.CohortID
// 		}
// 		aerr := tools.Database.Collection("attribute").FindOne(nil, attributeMatch).Decode(&attribute)
// 		if aerr != nil {
// 			return nil, errors.WithStack(aerr)
// 		}

// 		if len(orderInfo.DrugNames) > 0 {
// 			//查询这个供应计划下，这个药物是否都设置了
// 			var supplyPlanMedicines []models.SupplyPlanMedicine
// 			supplyFilter := bson.M{
// 				"env_id":             envOID,
// 				"customer_id":        customerOID,
// 				"supply_plan_id":     supplyPlanOID,
// 				"info.medicine_name": bson.M{"$in": orderInfo.DrugNames},
// 			}
// 			decodeName := []string{}

// 			for i, item := range orderInfo.DrugNames {
// 				if len(orderInfo.DrugNamesWithSalts) > 0 && orderInfo.DrugNamesWithSalts[i].SaltName != "" && orderInfo.DrugNamesWithSalts[i].Salt != "" {
// 					drugName := tools.Decrypt(orderInfo.DrugNamesWithSalts[i].SaltName, orderInfo.DrugNamesWithSalts[i].Salt)
// 					decodeName = append(decodeName, drugName)
// 				} else {
// 					decodeName = append(decodeName, item)
// 				}
// 			}
// 			supplyFilter["info.medicine_name"] = bson.M{"$in": decodeName}
// 			cursor, err := tools.Database.Collection("supply_plan_medicine").Find(nil, supplyFilter)
// 			if err != nil {
// 				return nil, errors.WithStack(err)
// 			}
// 			err = cursor.All(nil, &supplyPlanMedicines)
// 			if err != nil {
// 				return nil, errors.WithStack(err)
// 			}

// 			var mapSupply = make(map[string]models.SupplyPlanMedicine)
// 			for _, supply := range supplyPlanMedicines {
// 				mapSupply[supply.Medicine.MedicineName] = supply
// 			}

// 			for i, drugName := range orderInfo.DrugNames {
// 				if len(orderInfo.DrugNamesWithSalts) > 0 && orderInfo.DrugNamesWithSalts[i].SaltName != "" && orderInfo.DrugNamesWithSalts[i].Salt != "" {
// 					drugName = tools.Decrypt(orderInfo.DrugNamesWithSalts[i].SaltName, orderInfo.DrugNamesWithSalts[i].Salt)
// 				}
// 				isOpen := false
// 				isBlindedDrug, _ := tools.IsBlindedDrug(envOID, drugName)
// 				isOpen = !isBlindedDrug
// 				//判断是否是未编码药物
// 				match := bson.M{"customer_id": customerOID, "env_id": envOID, "project_id": projectOID, "info.name": drugName}
// 				total, err := tools.Database.Collection("medicine_other_institute").CountDocuments(nil, match)
// 				if err != nil {
// 					return nil, errors.WithStack(err)
// 				}
// 				if total > 0 { //编码药物，继续循环下一个药物
// 					continue
// 				}

// 				//请确认选中的研究产品是否都在接收单位下设置了供应计划
// 				if _, ok := mapSupply[drugName]; ok {
// 					supplyPlanMedicine := mapSupply[drugName]
// 					count := 0
// 					switch mode {
// 					case 2: //再供应量
// 						count = supplyPlanMedicine.Medicine.SecondSupply
// 					case 3: //最大缓冲量
// 						//查询该中心现有库存量
// 						var bufferStatus = []int{1, 2, 3}

// 						//if attribute.AttributeInfo.IsFreeze {
// 						//	bufferStatus = append(bufferStatus, 4)
// 						//}

// 						existFilter := bson.M{
// 							"customer_id": customerOID,
// 							"env_id":      envOID,
// 							"site_id":     orderInfo.ReceiveID,
// 							"name":        drugName,
// 							"status":      bson.M{"$in": bufferStatus},
// 						}

// 						total, err := tools.Database.Collection("medicine").CountDocuments(ctx, existFilter)
// 						if err != nil {
// 							return nil, errors.WithStack(err)
// 						}

// 						count = supplyPlanMedicine.Medicine.Buffer - int(total)

// 						// if count <= 0 {
// 						// 	//当前中心的库存量大于最大缓冲量
// 						// 	return nil, tools.BuildServerError(ctx, "shipment_order_buffer_info")
// 						// 	continue
// 						// }
// 					}
// 					//var statuss = []int{1}
// 					//if attribute.AttributeInfo.IsFreeze {
// 					//	statuss = append(statuss, 4)
// 					//}
// 					//供应计划，不配送天数
// 					timeZone, err := tools.GetTimeZone(projectOID)
// 					if err != nil {
// 						return nil, errors.WithStack(err)

// 					}
// 					unDistributionDate := supplyPlanMedicine.Medicine.UnDistributionDate
// 					unDistributionDateStr := time.Now().AddDate(0, 0, unDistributionDate).UTC().Add(time.Hour * time.Duration(timeZone)).Format("2006-01-02")
// 					// queryFilter := bson.M{
// 					// 	"customer_id":     customerOID,
// 					// 	"project_id":      projectOID,
// 					// 	"env_id":          envOID,
// 					// 	"storehouse_id":   orderInfo.SendID,
// 					// 	"name":            drugName,
// 					// 	"status":          bson.M{"$in": statuss},
// 					// 	"expiration_date": bson.M{"$gt": unDistributionDateStr},
// 					// }

// 					queryFilter := bson.M{"project_id": projectOID, "customer_id": customerOID, "env_id": envOID, "status": 1}
// 					queryFilter[sendId] = orderInfo.SendID
// 					queryFilter["name"] = drugName
// 					queryFilter["expiration_date"] = bson.M{"$gt": unDistributionDateStr}

// 					var drugData []models.Medicine
// 					if count > 0 {
// 						medicinePipepine := mongo.Pipeline{
// 							{{Key: "$match", Value: queryFilter}},
// 							models.MedicineProject,
// 							models.MedicineSort,
// 							{{Key: "$limit", Value: count}},
// 						}
// 						cursor, err := tools.Database.Collection("medicine").Aggregate(ctx, medicinePipepine)
// 						if err != nil {
// 							return nil, errors.WithStack(err)
// 						}
// 						err = cursor.All(ctx, &drugData)
// 						if err != nil {
// 							return nil, errors.WithStack(err)
// 						}
// 						medicineData = append(medicineData, drugData...)
// 					}

// 					//获取分组数据
// 					groupData := []map[string]interface{}{}
// 					groupPipepine := mongo.Pipeline{
// 						{{Key: "$match", Value: queryFilter}},
// 						{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "batchNumber": "$batch_number", "expirationDate": "$expiration_date"}, "availableCount": bson.M{"$sum": 1}}}},
// 					}
// 					groupCursor, groupErr := tools.Database.Collection("medicine").Aggregate(nil, groupPipepine)
// 					if groupErr != nil {
// 						return nil, errors.WithStack(groupErr)
// 					}
// 					groupErr = groupCursor.All(nil, &groupData)
// 					if groupErr != nil {
// 						return nil, errors.WithStack(groupErr)
// 					}

// 					//按照药物有效期批次号排序
// 					sort.SliceStable(groupData, func(i int, j int) bool {
// 						groupDatai := groupData[i]["_id"].(map[string]interface{})
// 						groupDataj := groupData[j]["_id"].(map[string]interface{})
// 						if groupDatai["name"].(string) < groupDataj["name"].(string) {
// 							return true
// 						}

// 						if groupDatai["name"].(string) > groupDataj["name"].(string) {
// 							return false
// 						}

// 						if groupDatai["expirationDate"].(string) < groupDataj["expirationDate"].(string) {
// 							return true
// 						}

// 						if groupDatai["expirationDate"].(string) > groupDataj["expirationDate"].(string) {
// 							return false
// 						}

// 						return groupDatai["batchNumber"].(string) < groupDataj["batchNumber"].(string)
// 					})

// 					if count <= 0 {
// 						for index, medicineGroupData := range groupData {
// 							if index == 0 {
// 								medicineGroupData["count"] = 0
// 								medicineGroupData["isOpen"] = isOpen
// 								if mode == 3 && !isOpen {
// 									medicineGroupData["message"] = locales.Tr(ctx, "shipment_order_create_modeMax_info")
// 								}
// 								data = append(data, medicineGroupData)
// 							}
// 						}
// 						continue
// 					}

// 					//该研究产品库存不足
// 					if len(drugData) < count {
// 						if !isOpen {
// 							canCreateOrder = false
// 							if message == "" {
// 								message = locales.Tr(ctx, "shipment_out_of_stock")
// 							}
// 						}
// 						haveCount := 0
// 						for index, medicineGroupData := range groupData {
// 							medicineGroupData["isOpen"] = isOpen
// 							availableCount := int(medicineGroupData["availableCount"].(int32))
// 							medicineGroupData["count"] = availableCount
// 							if index == len(groupData)-1 {
// 								medicineGroupData["count"] = count - haveCount
// 							}
// 							data = append(data, medicineGroupData)
// 							haveCount += availableCount
// 						}
// 					} else {
// 						haveCount := 0
// 						syCount := count
// 						for _, medicineGroupData := range groupData {
// 							medicineGroupData["isOpen"] = isOpen
// 							availableCount := int(medicineGroupData["availableCount"].(int32))
// 							haveCount += availableCount
// 							if availableCount <= syCount {
// 								medicineGroupData["count"] = availableCount
// 								syCount = syCount - availableCount
// 							} else {
// 								medicineGroupData["count"] = syCount
// 								syCount = 0
// 							}
// 							data = append(data, medicineGroupData)
// 							if syCount <= 0 {
// 								break
// 							}
// 						}
// 					}
// 				} else {
// 					if isOpen { //如果是开放药物
// 						//获取分组数据
// 						queryFilter := filter
// 						queryFilter["name"] = drugName

// 						groupData := []map[string]interface{}{}
// 						groupPipepine := mongo.Pipeline{
// 							{{Key: "$match", Value: queryFilter}},
// 							{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "batchNumber": "$batch_number", "expirationDate": "$expiration_date"}, "availableCount": bson.M{"$sum": 1}}}},
// 						}
// 						groupCursor, groupErr := tools.Database.Collection("medicine").Aggregate(nil, groupPipepine)
// 						if groupErr != nil {
// 							return nil, errors.WithStack(groupErr)
// 						}
// 						groupErr = groupCursor.All(nil, &groupData)
// 						if groupErr != nil {
// 							return nil, errors.WithStack(groupErr)
// 						}
// 						//按照药物有效期批次号排序
// 						sort.SliceStable(groupData, func(i int, j int) bool {
// 							groupDatai := groupData[i]["_id"].(map[string]interface{})
// 							groupDataj := groupData[j]["_id"].(map[string]interface{})
// 							if groupDatai["name"].(string) < groupDataj["name"].(string) {
// 								return true
// 							}

// 							if groupDatai["name"].(string) > groupDataj["name"].(string) {
// 								return false
// 							}

// 							if groupDatai["expirationDate"].(string) < groupDataj["expirationDate"].(string) {
// 								return true
// 							}

// 							if groupDatai["expirationDate"].(string) > groupDataj["expirationDate"].(string) {
// 								return false
// 							}

// 							return groupDatai["batchNumber"].(string) < groupDataj["batchNumber"].(string)
// 						})
// 						for _, medicineGroupData := range groupData {
// 							medicineGroupData["isOpen"] = isOpen
// 							data = append(data, medicineGroupData)
// 						}
// 					} else {
// 						//请确认选中的研究产品是否都在接收单位下设置了供应计划
// 						canCreateOrder = false
// 						if message == "" {
// 							message = locales.Tr(ctx, "shipment_order_supply_info")
// 						}
// 						//return nil, tools.BuildServerError(ctx, "shipment_order_supply_info")
// 						continue
// 					}

// 				}
// 			}
// 		}
// 	}

// 	// if len(medicineData) <= 0 && mode != 1 {
// 	// 	canCreateOrder = false
// 	// }

// 	// if len(data) <= 0 {
// 	// 	canCreateOrder = false
// 	// 	if message == "" {
// 	// 		message = locales.Tr(ctx, "shipment_out_of_stock")
// 	// 	}
// 	// } else {
// 	sort.SliceStable(data, func(i int, j int) bool {
// 		datai := data[i]["_id"].(map[string]interface{})
// 		dataj := data[j]["_id"].(map[string]interface{})
// 		if datai["name"].(string) < dataj["name"].(string) {
// 			return true
// 		}

// 		if datai["name"].(string) > dataj["name"].(string) {
// 			return false
// 		}

// 		if datai["expirationDate"].(string) < dataj["expirationDate"].(string) {
// 			return true
// 		}

// 		if datai["expirationDate"].(string) > dataj["expirationDate"].(string) {
// 			return false
// 		}

// 		return datai["batchNumber"].(string) < dataj["batchNumber"].(string)
// 	})
// 	//}

// 	return map[string]interface{}{"data": data, "medicines": medicineData, "canCreateOrder": canCreateOrder, "message": message}, nil
// }

func (s *MedicineService) QueryMedicineBatchPackageList(ctx *gin.Context, orderInfo models.QueryOrderInfo, isBlindedRole bool) (map[string]interface{}, error) {
	canCreateOrder := true
	message := ""
	var medicineData []models.Medicine
	projectOID := orderInfo.ProjectID
	customerOID := orderInfo.CustomerID
	envOID := orderInfo.EnvID

	//起运地
	sendOID := orderInfo.SendID
	//补充方式
	mode := orderInfo.Mode
	//供应计划
	supplyPlanOID := orderInfo.SupplyPlanID
	match := bson.M{"_id": sendOID}
	siteCount, _ := tools.Database.Collection("project_site").CountDocuments(nil, match)
	subjectIDs := []interface{}{primitive.NilObjectID}
	if !orderInfo.ReceiveID.IsZero() && siteCount == 0 {
		var subject []models.Subject
		cursor, err := tools.Database.Collection("subject").Find(nil, bson.M{"project_site_id": orderInfo.ReceiveID}, &options.FindOptions{Projection: bson.M{"_id": 1}})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &subject)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		for _, item := range subject {
			subjectIDs = append(subjectIDs, item.ID)
		}
	}

	filter := bson.M{
		"project_id":  projectOID,
		"customer_id": customerOID,
		"env_id":      envOID,
		"status":      bson.M{"$in": bson.A{1, 14}},
		"subject_id":  bson.M{"$in": subjectIDs},
		"frozen":      bson.M{"$ne": true},
	}
	packageFilter := bson.M{"project_id": projectOID, "customer_id": customerOID, "env_id": envOID,
		"status":         bson.M{"$in": bson.A{1, 14}},
		"subject_id":     bson.M{"$in": subjectIDs},
		"frozen":         bson.M{"$ne": true},
		"package_number": bson.M{"$nin": [2]interface{}{nil, ""}}}
	sendId := "site_id"
	if sendOID != primitive.NilObjectID {
		//判断该sendId是仓库还是中心
		if siteCount > 0 {
			filter["site_id"] = sendOID
			packageFilter["site_id"] = sendOID
		} else {
			sendId = "storehouse_id"
			filter["storehouse_id"] = sendOID
			packageFilter["storehouse_id"] = sendOID
		}
	}

	//查询哪些药物是按照包装运输
	packageIsOpen, packageDrugNamesAllCount, packageDrugNames, packageConfigs, _, _ := tools.IsOpenPackage(envOID)

	var drugNames []string
	if packageIsOpen && len(packageDrugNames) > 0 {
		for key, _ := range packageDrugNames {
			drugNames = append(drugNames, key)
		}
		filter["name"] = bson.M{"$nin": drugNames}
	}

	var data []map[string]interface{}
	//混包药物，包装数量
	drugPackageCount := make(map[string]int)
	//标记是否有开放药物或者是未编码药物
	haveOpenOrOtherDrug := false
	//补充方式：发药数量
	if mode == 1 {
		allDrugMap, _ := tools.AllDrugMap(envOID)
		isBlindedRole := true
		//如果角色是盲态角色,只能运输开放药物
		if orderInfo.RoleID != "" {
			isBlindedRole, _ = tools.IsBlindedRole(orderInfo.RoleID)
			if isBlindedRole {
				var openDrugs []string
				for key, isOpen := range allDrugMap {
					index := arrays.ContainsString(drugNames, key)
					if !isOpen && index == -1 {
						openDrugs = append(openDrugs, key)
					}
				}
				filter["name"] = bson.M{"$in": openDrugs}
			}
		}

		//单品运输
		pipepine := mongo.Pipeline{
			{{Key: "$match", Value: filter}},
			{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "batchNumber": "$batch_number", "expirationDate": "$expiration_date"}, "availableCount": bson.M{"$sum": 1}}}},
			{{Key: "$project", Value: bson.M{"_id": "$_id", "availableCount": "$availableCount", "packageMethod": "false"}}},
			{{Key: "$sort", Value: bson.D{{"_id.name", 1}, {"_id.expirationDate", 1}, {"_id.batchNumber", 1}}}},
		}
		cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pipepine)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &data)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 包装运输
		if packageIsOpen && len(packageDrugNames) > 0 {
			for drugName, count := range packageDrugNames {
				if isBlindedRole && allDrugMap[drugName] { //盲态角色的时候，不是开放开药物，就下一次循环
					continue
				}
				packageFilter["name"] = bson.M{"$in": []string{drugName}}
				packagePipepine := mongo.Pipeline{
					{{Key: "$match", Value: packageFilter}},
					{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "package_number": "$package_number", "batch_number": "$batch_number", "expiration_date": "$expiration_date"}, "availableCount": bson.M{"$sum": 1}}}},
					{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$availableCount", count}}}}},
					{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$_id.name", "batchNumber": "$_id.batch_number", "expirationDate": "$_id.expiration_date"}, "availableCount": bson.M{"$sum": 1}}}},
					{{Key: "$project", Value: bson.M{"_id": "$_id", "availableCount": "$availableCount", "packageMethod": "true"}}},
					{{Key: "$sort", Value: bson.D{{"_id.name", 1}, {"_id.expirationDate", 1}, {"_id.batchNumber", 1}}}},
				}
				packageCursor, err := tools.Database.Collection("medicine").Aggregate(nil, packagePipepine)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				var packageData []map[string]interface{}
				err = packageCursor.All(nil, &packageData)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				data = append(data, packageData...)

			}
		}
	} else if mode == 6 {
		var blindDrugNames []string

		isBlindDrugMap, _ := tools.IsBlindDrugMap(envOID)
		if isBlindDrugMap != nil && len(isBlindDrugMap) > 0 {
			for key, _ := range isBlindDrugMap {
				blindDrugNames = append(blindDrugNames, key)
			}
		}

		//包装研究产品
		var packDrugNames []string
		if packageIsOpen && len(packageDrugNames) > 0 { //包装配置打开,查询序列号是否在包装序列号范围之内
			for key, _ := range packageDrugNames {
				packDrugNames = append(packDrugNames, key)
			}
			//查询包装的研究产品数据
			if packageIsOpen && len(packageConfigs) > 0 {
				var medicineIds []primitive.ObjectID
				packageFilter["$or"] = bson.A{
					bson.M{"package_serial_number": bson.M{"$gte": orderInfo.IntervalStart, "$lte": orderInfo.IntervalEnd}},
					bson.M{"serial_number": bson.M{"$gte": orderInfo.IntervalStart, "$lte": orderInfo.IntervalEnd}},
				}
				for drugName, count := range packageDrugNamesAllCount {
					if isBlindedRole && isBlindDrugMap[drugName] { //盲态角色的时候，不是开发药物，就下一次循环
						continue
					}
					packageFilter["name"] = bson.M{"$in": packageConfigs[drugName]}
					packagePipepine := mongo.Pipeline{
						{{Key: "$match", Value: packageFilter}},
						{{Key: "$group", Value: bson.M{"_id": bson.M{"package_number": "$package_number"}, "availableCount": bson.M{"$sum": 1}, "ids": bson.M{"$addToSet": "$_id"}}}},
						{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$availableCount", count}}}}},
						{{Key: "$project", Value: bson.M{
							"ids": 1,
						}}},
					}
					packageCursor, err := tools.Database.Collection("medicine").Aggregate(ctx, packagePipepine)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					var packageData []map[string]interface{}
					err = packageCursor.All(nil, &packageData)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					for _, medicine := range packageData {
						medicines := medicine["ids"].(primitive.A)
						for _, id := range medicines {
							medicineIds = append(medicineIds, id.(primitive.ObjectID))
						}
					}
				}
				// 查询包装的药物
				if len(medicineIds) > 0 {
					packageIdsPipepine := mongo.Pipeline{
						{{Key: "$match", Value: bson.M{"_id": bson.M{"$in": medicineIds}}}},
						{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "package_number": "$package_number", "batchNumber": "$batch_number", "expirationDate": "$expiration_date"}, "availableCount": bson.M{"$sum": 1}}}},
						{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$_id.name", "batchNumber": "$_id.batchNumber", "expirationDate": "$_id.expirationDate"}, "availableCount": bson.M{"$sum": 1}}}},
						{{Key: "$project", Value: bson.M{"_id": "$_id", "availableCount": "$availableCount", "packageMethod": "true"}}},
						{{Key: "$sort", Value: bson.D{{"_id.name", 1}, {"_id.expirationDate", 1}, {"_id.batchNumber", 1}}}},
					}
					packageCursor, err := tools.Database.Collection("medicine").Aggregate(nil, packageIdsPipepine)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					var packageData []map[string]interface{}
					err = packageCursor.All(nil, &packageData)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					data = append(data, packageData...)
				}

			}
		}
		//判断是否是盲态角色,如果盲态角色,只查询范围内的开放研究产品的数据
		var neDrugNames []string
		if isBlindedRole && len(blindDrugNames) > 0 {
			neDrugNames = append(neDrugNames, blindDrugNames...)
		}
		//查询单品的序列号,去掉包装研究产品
		if packageIsOpen && len(packageDrugNames) > 0 {
			neDrugNames = append(neDrugNames, packDrugNames...)
		}
		groupData := []map[string]interface{}{}
		queryFilter := bson.M{
			"project_id":    projectOID,
			"customer_id":   customerOID,
			"env_id":        envOID,
			"status":        bson.M{"$in": bson.A{1, 14}},
			"frozen":        bson.M{"$ne": true},
			"serial_number": bson.M{"$gte": orderInfo.IntervalStart, "$lte": orderInfo.IntervalEnd},
		}
		if neDrugNames != nil && len(neDrugNames) > 0 {
			queryFilter["name"] = bson.M{"$nin": neDrugNames}
		}
		if sendOID != primitive.NilObjectID {
			queryFilter[sendId] = sendOID
		}
		medicinePipepine := mongo.Pipeline{
			{{Key: "$match", Value: queryFilter}},
			{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "batchNumber": "$batch_number", "expirationDate": "$expiration_date"}, "availableCount": bson.M{"$sum": 1}}}},
			{{Key: "$project", Value: bson.M{"_id": "$_id", "availableCount": "$availableCount", "packageMethod": "false"}}},
		}
		cursor, err := tools.Database.Collection("medicine").Aggregate(ctx, medicinePipepine)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(ctx, &groupData)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		for _, medicineGroupData := range groupData {
			data = append(data, medicineGroupData)
		}

	} else { //补充方式：再供应量/最大缓冲量
		//查询项目属性
		var attribute models.Attribute
		attributeMatch := bson.M{
			"customer_id": customerOID,
			"env_id":      envOID,
			"project_id":  projectOID,
		}
		if orderInfo.CohortID != primitive.NilObjectID {
			attributeMatch["cohort_id"] = orderInfo.CohortID
		}
		aerr := tools.Database.Collection("attribute").FindOne(nil, attributeMatch).Decode(&attribute)
		if aerr != nil {
			return nil, errors.WithStack(aerr)
		}
		if len(orderInfo.DrugNames) > 0 {
			//查询这个供应计划下，这个药物是否都设置了
			var supplyPlanMedicines []models.SupplyPlanMedicine
			supplyFilter := bson.M{
				"env_id":             envOID,
				"customer_id":        customerOID,
				"supply_plan_id":     supplyPlanOID,
				"info.medicine_name": bson.M{"$in": orderInfo.DrugNames},
			}
			decodeName := []string{}
			allDrugMap, err := tools.AllDrugMap(envOID)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			for i, item := range orderInfo.DrugNames {
				_, ok := allDrugMap[item]
				if !ok && len(orderInfo.DrugNamesWithSalts) > 0 && orderInfo.DrugNamesWithSalts[i].SaltName != "" && orderInfo.DrugNamesWithSalts[i].Salt != "" {
					drugName := tools.Decrypt(orderInfo.DrugNamesWithSalts[i].SaltName, orderInfo.DrugNamesWithSalts[i].Salt)
					decodeName = append(decodeName, drugName)
				} else {
					decodeName = append(decodeName, item)
				}
			}

			supplyFilter["info.medicine_name"] = bson.M{"$in": decodeName}
			cursor, err := tools.Database.Collection("supply_plan_medicine").Find(nil, supplyFilter)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &supplyPlanMedicines)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			var mapSupply = make(map[string]models.SupplyPlanMedicine)
			for _, supply := range supplyPlanMedicines {
				mapSupply[supply.Medicine.MedicineName] = supply
			}

			allDrugMap, err = tools.AllDrugMap(orderInfo.EnvID)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			if packageIsOpen {
				for i, drugName := range orderInfo.DrugNames {
					_, ok := allDrugMap[drugName]
					if !ok && len(orderInfo.DrugNamesWithSalts) > 0 && orderInfo.DrugNamesWithSalts[i].SaltName != "" && orderInfo.DrugNamesWithSalts[i].Salt != "" {
						drugName = tools.Decrypt(orderInfo.DrugNamesWithSalts[i].SaltName, orderInfo.DrugNamesWithSalts[i].Salt)
					}
					//如果该药物是按照包装发
					packageNumber, exist := packageDrugNames[drugName]
					if exist {
						count := 0
						//请确认选中的研究产品是否都在接收单位下设置了供应计划
						if _, ok := mapSupply[drugName]; ok {
							supplyPlanMedicine := mapSupply[drugName]
							switch mode {
							case 2: //再供应量
								count = supplyPlanMedicine.Medicine.SecondSupply
							case 3: //最大缓冲量
								//查询该中心现有库存量
								var bufferStatus = []int{1, 2, 3}
								existFilter := bson.M{
									"customer_id": customerOID,
									"env_id":      envOID,
									"site_id":     orderInfo.ReceiveID,
									"name":        drugName,
									"status":      bson.M{"$in": bufferStatus},
								}

								total, err := tools.Database.Collection("medicine").CountDocuments(ctx, existFilter)
								if err != nil {
									return nil, errors.WithStack(err)
								}
								count = supplyPlanMedicine.Medicine.Buffer - int(total)
							}
						}
						syCount := count / packageNumber
						if count%packageNumber > 0 {
							syCount = count/packageNumber + 1
						}
						drugPackageCount[drugName] = syCount
					}
				}
				for i, drugName := range orderInfo.DrugNames {
					_, ok := allDrugMap[drugName]
					if !ok && len(orderInfo.DrugNamesWithSalts) > 0 && orderInfo.DrugNamesWithSalts[i].SaltName != "" && orderInfo.DrugNamesWithSalts[i].Salt != "" {
						drugName = tools.Decrypt(orderInfo.DrugNamesWithSalts[i].SaltName, orderInfo.DrugNamesWithSalts[i].Salt)
					}
					maxCount := drugPackageCount[drugName]
					durgNames := packageConfigs[drugName]
					for _, v := range durgNames {
						if maxCount < drugPackageCount[v] {
							maxCount = drugPackageCount[v]
							drugPackageCount[drugName] = maxCount
						}
					}
				}
			}

			otherDrugMap, _ := tools.IsOtherDrugMap(envOID)
			for i, drugName := range orderInfo.DrugNames {
				_, ok := allDrugMap[drugName]
				if !ok && len(orderInfo.DrugNamesWithSalts) > 0 && orderInfo.DrugNamesWithSalts[i].SaltName != "" && orderInfo.DrugNamesWithSalts[i].Salt != "" {
					drugName = tools.Decrypt(orderInfo.DrugNamesWithSalts[i].SaltName, orderInfo.DrugNamesWithSalts[i].Salt)
				}

				isOpen := false
				isBlindedDrug, _ := tools.IsBlindedDrug(envOID, drugName)
				isOpen = !isBlindedDrug
				if isOpen {
					haveOpenOrOtherDrug = true
				}

				if otherDrugMap[drugName] { //未编码药物，继续循环下一个药物
					haveOpenOrOtherDrug = true
					continue
				}
				packageNumber, exist := packageDrugNames[drugName]
				//请确认选中的研究产品是否都在接收单位下设置了供应计划
				if _, ok := mapSupply[drugName]; ok {
					supplyPlanMedicine := mapSupply[drugName]
					count := 0
					switch mode {
					case 2: //再供应量
						count = supplyPlanMedicine.Medicine.SecondSupply
					case 3: //最大缓冲量
						//查询该中心现有库存量
						var bufferStatus = []int{1, 2, 3}
						existFilter := bson.M{
							"customer_id": customerOID,
							"env_id":      envOID,
							"site_id":     orderInfo.ReceiveID,
							"name":        drugName,
							"status":      bson.M{"$in": bufferStatus},
						}

						total, err := tools.Database.Collection("medicine").CountDocuments(ctx, existFilter)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						count = supplyPlanMedicine.Medicine.Buffer - int(total)
					}

					//供应计划，不配送天数
					timeZone, err := tools.GetTimeZone(projectOID)
					if err != nil {
						return nil, errors.WithStack(err)

					}
					unDistributionDate := supplyPlanMedicine.Medicine.UnDistributionDate
					hours := time.Duration(timeZone)
					minutes := time.Duration((timeZone - float64(hours)) * 60)
					duration := hours*time.Hour + minutes*time.Minute
					unDistributionDateStr := time.Now().AddDate(0, 0, unDistributionDate).UTC().Add(duration).Format("2006-01-02")

					queryFilter := bson.M{"project_id": projectOID, "customer_id": customerOID, "env_id": envOID,
						"status":     bson.M{"$in": bson.A{1, 14}},
						"subject_id": bson.M{"$in": subjectIDs},
						"frozen":     bson.M{"$ne": true},
					}
					if sendOID != primitive.NilObjectID {
						queryFilter[sendId] = sendOID
					}
					queryFilter["name"] = drugName
					//queryFilter["expiration_date"] = bson.M{"$gt": unDistributionDateStr}
					queryFilter["$or"] = bson.A{
						bson.M{"expiration_date": ""},
						bson.M{"expiration_date": nil},
						bson.M{"expiration_date": bson.M{"$gt": unDistributionDateStr}},
					}

					var drugData []models.Medicine

					if count > 0 { //说明需要发药
						//如果该药物是按照包装发，判断count数量是否是整盒

						if packageIsOpen && exist {
							// if count%packageNumber > 0 {
							// 	count = (count/packageNumber + 1) * packageNumber
							// }
							count = drugPackageCount[drugName] * packageNumber
							queryFilter["package_number"] = bson.M{"$nin": [2]interface{}{nil, ""}}
						}

						medicinePipepine := mongo.Pipeline{
							{{Key: "$match", Value: queryFilter}},
							models.MedicineProject,
							models.MedicineSort,
							{{Key: "$limit", Value: count}},
						}
						cursor, err := tools.Database.Collection("medicine").Aggregate(ctx, medicinePipepine)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						err = cursor.All(ctx, &drugData)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						medicineData = append(medicineData, drugData...)
					}

					//获取分组数据
					groupData := []map[string]interface{}{}
					groupPipepine := mongo.Pipeline{
						{{Key: "$match", Value: queryFilter}},
						{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "batchNumber": "$batch_number", "expirationDate": "$expiration_date"}, "availableCount": bson.M{"$sum": 1}}}},
						{{Key: "$project", Value: bson.M{"_id": "$_id", "availableCount": "$availableCount", "packageMethod": "false"}}},
					}
					if packageIsOpen && exist {
						groupPipepine = mongo.Pipeline{
							{{Key: "$match", Value: queryFilter}},
							{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "package_number": "$package_number", "batch_number": "$batch_number", "expiration_date": "$expiration_date"}, "availableCount": bson.M{"$sum": 1}}}},
							{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$availableCount", packageNumber}}}}},
							{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$_id.name", "batchNumber": "$_id.batch_number", "expirationDate": "$_id.expiration_date"}, "availableCount": bson.M{"$sum": 1}}}},
							{{Key: "$project", Value: bson.M{"_id": "$_id", "availableCount": "$availableCount", "packageMethod": "true"}}},
						}
					}

					groupCursor, groupErr := tools.Database.Collection("medicine").Aggregate(nil, groupPipepine)
					if groupErr != nil {
						return nil, errors.WithStack(groupErr)
					}
					groupErr = groupCursor.All(nil, &groupData)
					if groupErr != nil {
						return nil, errors.WithStack(groupErr)
					}

					//按照药物有效期批次号排序
					sort.SliceStable(groupData, func(i int, j int) bool {
						groupDatai := groupData[i]["_id"].(map[string]interface{})
						groupDataj := groupData[j]["_id"].(map[string]interface{})
						if groupDatai["name"].(string) < groupDataj["name"].(string) {
							return true
						}

						if groupDatai["name"].(string) > groupDataj["name"].(string) {
							return false
						}

						if groupDatai["expirationDate"] != nil && groupDatai["expirationDate"] != "" && groupDataj["expirationDate"] != nil && groupDataj["expirationDate"] != "" {
							if groupDatai["expirationDate"].(string) < groupDataj["expirationDate"].(string) {
								return true
							}

							if groupDatai["expirationDate"].(string) > groupDataj["expirationDate"].(string) {
								return false
							}
						} else {
							if (groupDatai["expirationDate"] == nil || groupDatai["expirationDate"] == "") && (groupDataj["expirationDate"] != nil && groupDataj["expirationDate"] != "") {
								return false
							} else {
								return true
							}
						}

						return groupDatai["batchNumber"].(string) < groupDataj["batchNumber"].(string)
					})

					if count <= 0 {
						for index, medicineGroupData := range groupData {
							if index == 0 {
								medicineGroupData["count"] = 0
								medicineGroupData["isOpen"] = isOpen
								if mode == 3 && !isOpen {
									medicineGroupData["message"] = locales.Tr(ctx, "shipment_order_create_modeMax_info")
								}
								data = append(data, medicineGroupData)
							}
						}
						continue
					}

					//该研究产品库存不足
					if len(drugData) < count {
						if !isOpen {
							canCreateOrder = false
							if message == "" {
								message = locales.Tr(ctx, "shipment_out_of_stock")
							}
						}
						haveCount := 0
						for index, medicineGroupData := range groupData {
							medicineGroupData["isOpen"] = isOpen
							availableCount := int(medicineGroupData["availableCount"].(int32))
							medicineGroupData["count"] = availableCount
							if index == len(groupData)-1 {
								medicineGroupData["count"] = count - haveCount
							}
							data = append(data, medicineGroupData)
							haveCount += availableCount
						}
					} else {
						haveCount := 0
						syCount := count
						//如果该药物是按照包装发
						if packageIsOpen && exist {
							syCount = count / packageNumber
						}
						for _, medicineGroupData := range groupData {
							medicineGroupData["isOpen"] = isOpen
							availableCount := int(medicineGroupData["availableCount"].(int32))
							haveCount += availableCount
							if availableCount <= syCount {
								medicineGroupData["count"] = availableCount
								syCount = syCount - availableCount
							} else {
								medicineGroupData["count"] = syCount
								syCount = 0
							}
							data = append(data, medicineGroupData)
							if syCount <= 0 {
								break
							}
						}
					}
				} else {
					if isOpen { //如果是开放药物
						//获取分组数据
						queryFilter := filter
						queryFilter["name"] = drugName

						groupData := []map[string]interface{}{}
						groupPipepine := mongo.Pipeline{
							{{Key: "$match", Value: queryFilter}},
							{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "batchNumber": "$batch_number", "expirationDate": "$expiration_date"}, "availableCount": bson.M{"$sum": 1}}}},
							{{Key: "$project", Value: bson.M{"_id": "$_id", "availableCount": "$availableCount", "packageMethod": "false"}}},
						}
						if packageIsOpen && exist {
							//如果该药物是按照包装发，判断count数量是否是整盒
							queryFilter["package_number"] = bson.M{"$nin": [2]interface{}{nil, ""}}
							groupPipepine = mongo.Pipeline{
								{{Key: "$match", Value: queryFilter}},
								{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "package_number": "$package_number", "batch_number": "$batch_number", "expiration_date": "$expiration_date"}, "availableCount": bson.M{"$sum": 1}}}},
								{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$availableCount", packageNumber}}}}},
								{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$_id.name", "batchNumber": "$_id.batch_number", "expirationDate": "$_id.expiration_date"}, "availableCount": bson.M{"$sum": 1}}}},
								{{Key: "$project", Value: bson.M{"_id": "$_id", "availableCount": "$availableCount", "packageMethod": "true"}}},
							}
						}

						groupCursor, groupErr := tools.Database.Collection("medicine").Aggregate(nil, groupPipepine)
						if groupErr != nil {
							return nil, errors.WithStack(groupErr)
						}
						groupErr = groupCursor.All(nil, &groupData)
						if groupErr != nil {
							return nil, errors.WithStack(groupErr)
						}
						//按照药物有效期批次号排序
						sort.SliceStable(groupData, func(i int, j int) bool {
							groupDatai := groupData[i]["_id"].(map[string]interface{})
							groupDataj := groupData[j]["_id"].(map[string]interface{})
							if groupDatai["name"].(string) < groupDataj["name"].(string) {
								return true
							}

							if groupDatai["name"].(string) > groupDataj["name"].(string) {
								return false
							}

							if groupDatai["expirationDate"] != nil && groupDatai["expirationDate"] != "" && groupDataj["expirationDate"] != nil && groupDataj["expirationDate"] != "" {
								if groupDatai["expirationDate"].(string) < groupDataj["expirationDate"].(string) {
									return true
								}

								if groupDatai["expirationDate"].(string) > groupDataj["expirationDate"].(string) {
									return false
								}
							} else {
								if (groupDatai["expirationDate"] == nil || groupDatai["expirationDate"] == "") && (groupDataj["expirationDate"] != nil && groupDataj["expirationDate"] != "") {
									return false
								} else {
									return true
								}
							}

							return groupDatai["batchNumber"].(string) < groupDataj["batchNumber"].(string)
						})
						for _, medicineGroupData := range groupData {
							medicineGroupData["isOpen"] = isOpen
							data = append(data, medicineGroupData)
						}
					} else {
						//请确认选中的研究产品是否都在接收单位下设置了供应计划
						canCreateOrder = false
						if message == "" {
							message = locales.Tr(ctx, "shipment_order_supply_info")
						}
						//return nil, tools.BuildServerError(ctx, "shipment_order_supply_info")
						continue
					}
				}
			}
		}
	}

	sort.SliceStable(data, func(i int, j int) bool {
		datai := data[i]["_id"].(map[string]interface{})
		dataj := data[j]["_id"].(map[string]interface{})
		if datai["name"].(string) < dataj["name"].(string) {
			return true
		}

		if datai["name"].(string) > dataj["name"].(string) {
			return false
		}

		if datai["expirationDate"] != nil && datai["expirationDate"] != "" && dataj["expirationDate"] != nil && dataj["expirationDate"] != "" {
			if datai["expirationDate"].(string) < dataj["expirationDate"].(string) {
				return true
			}

			if datai["expirationDate"].(string) > dataj["expirationDate"].(string) {
				return false
			}
		} else {
			if (datai["expirationDate"] == nil || datai["expirationDate"] == "") && (dataj["expirationDate"] != nil && dataj["expirationDate"] != "") {
				return false
			} else if (dataj["expirationDate"] == nil || dataj["expirationDate"] == "") && (datai["expirationDate"] != nil && datai["expirationDate"] != "") {
				return false
			} else {
				if datai["batchNumber"] != nil && datai["batchNumber"] != "" && dataj["batchNumber"] != nil && dataj["batchNumber"] != "" {
					if datai["batchNumber"].(string) < dataj["batchNumber"].(string) {
						return true
					}

					if datai["batchNumber"].(string) > dataj["batchNumber"].(string) {
						return false
					}
				} else {
					if (datai["batchNumber"] == nil || datai["batchNumber"] == "") && (dataj["batchNumber"] != nil && dataj["batchNumber"] != "") {
						return false
					} else {
						return false
					}
				}
			}
		}

		return datai["batchNumber"].(string) < dataj["batchNumber"].(string)
	})

	if mode != 1 && mode != 6 && len(medicineData) <= 0 && !haveOpenOrOtherDrug { //如果只有不开放的药物，并且生成的药物数量小于等于0
		canCreateOrder = false
	}

	if mode == 6 && (data == nil || len(data) <= 0) {
		canCreateOrder = false
	}

	if medicineData != nil && len(medicineData) > 0 {
		for _, md := range medicineData {
			if md.ExpirationDate == "" {
				md.ExpirationDate = "-"
			}
			if md.BatchNumber == "" {
				md.BatchNumber = "-"
			}
		}
	}

	if data != nil && len(data) > 0 {
		for _, d := range data {
			if _, outerOk := d["_id"]; outerOk {
				innerMap := d["_id"]
				if innerMap != nil {
					if _, innerOk := innerMap.(map[string]interface{})["expirationDate"]; innerOk {
						if innerMap.(map[string]interface{})["expirationDate"] != nil {
							if innerMap.(map[string]interface{})["expirationDate"].(string) == "" {
								innerMap.(map[string]interface{})["expirationDate"] = "-"
							}
						}
						if innerMap.(map[string]interface{})["batchNumber"] != nil {
							if innerMap.(map[string]interface{})["batchNumber"].(string) == "" {
								innerMap.(map[string]interface{})["batchNumber"] = "-"
							}
						}
					}
				}
			}

		}
	}

	return map[string]interface{}{"data": data, "medicines": medicineData, "canCreateOrder": canCreateOrder, "message": message}, nil
}

func (s *MedicineService) GetMedicineBatchList(ctx *gin.Context, orderInfo models.QueryOrderInfo) (map[string]interface{}, error) {
	envOID := orderInfo.EnvID
	//cohortOID := orderInfo.CohortID
	isBlindedRole := false
	if orderInfo.RoleID != "" {
		isBlindedRole, _ = tools.IsBlindedRole(orderInfo.RoleID)
	}
	//查询数据
	data, _ := s.QueryMedicineBatchPackageList(ctx, orderInfo, isBlindedRole)
	batchData := make([]map[string]interface{}, 0)

	// 查询混包配置的药 存在不能创建订单的药 加标识
	// nameMap, nameMapArr, err := matchCloseCohortMedicine(envOID)
	// if err != nil {
	// 	return nil, errors.WithStack(err)
	// }
	if data["data"] != nil {
		batchData = data["data"].([]map[string]interface{})
		// batchData = slice.Filter(data["data"].([]map[string]interface{}), func(index int, item map[string]interface{}) bool {
		// 	name := item["_id"].(map[string]interface{})["name"].(string)
		// 	return nameMap[name] != 0
		// })
		if isBlindedRole {
			for index, value := range batchData {
				isOpen := false
				if value["isOpen"] != nil {
					isOpen = value["isOpen"].(bool)
				}
				name := value["_id"].(map[string]interface{})["name"].(string)
				// value["mixPage"] = nameMap[name]
				// value["mixPageOther"] = slice.Map(nameMapArr[name], func(index int, item string) string {
				// 	isBlindedDrug, _ := tools.IsBlindedDrug(envOID, name)
				// 	if isBlindedDrug {
				// 		item = tools.BlindData
				// 	}
				// 	return item
				// })
				encrypted, randomSalt := tools.Encrypt(name)
				isBlindedDrug, _ := tools.IsBlindedDrug(envOID, name)
				if isBlindedDrug {
					value["_id"].(map[string]interface{})["name"] = tools.BlindData
					value["availableCount"] = tools.BlindData
					value["count"] = tools.BlindData
				}

				value["salt"] = randomSalt
				value["name"] = encrypted
				value["isOpen"] = isOpen

				batchData[index] = value
			}
		}
		// else {
		// 	for index, value := range batchData {
		// 		name := value["_id"].(map[string]interface{})["name"].(string)
		// 		value["mixPage"] = nameMap[name]
		// 		value["mixPageOther"] = nameMapArr[name]
		// 		batchData[index] = value
		// 	}
		// }
	}

	return map[string]interface{}{"items": batchData, "canCreateOrder": data["canCreateOrder"], "message": data["message"]}, nil
}

func matchCloseCohortMedicine(envOID primitive.ObjectID) (map[string]int, map[string][]string, error) {

	// nameMap : 0  停止cohort下的  1  可用cohort下的 2 混包 存在不同状态cohort下的

	var drugConfigures []models.DrugConfigure
	nameMap := map[string]int{}
	nameMapArr := map[string][]string{}

	cursor, err := tools.Database.Collection("drug_configure").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return nameMap, nameMapArr, errors.WithStack(err)
	}
	err = cursor.All(nil, &drugConfigures)
	if err != nil {
		return nameMap, nameMapArr, errors.WithStack(err)
	}
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"envs.id": envOID}).Decode(&project)
	if err != nil {
		return nameMap, nameMapArr, errors.WithStack(err)
	}
	var drugPackageConfigure models.DrugPackageConfigure
	err = tools.Database.Collection("drug_package_configure").FindOne(nil, bson.M{"env_id": envOID}).Decode(&drugPackageConfigure)
	if err != nil && err != mongo.ErrNoDocuments {
		return nameMap, nameMapArr, errors.WithStack(err)
	}
	envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return envOID == item.ID
	})
	env := *envP
	for _, configure := range drugConfigures {
		if project.Type != 1 {
			_, ok := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
				return configure.CohortID == item.ID && (item.Status == 2 || item.Status == 5)
			})
			if !ok {
				continue
			}
		} else {
			if !(*env.Status == 2 || *env.Status == 5) {
				continue
			}
		}
		for _, info := range configure.Configures {
			for _, value := range info.Values {
				nameMap[value.DrugName] = 1
			}
		}
	}
	// 筛选出存在不允许发放的混包药物
	for _, mixedPackage := range drugPackageConfigure.MixedPackage {
		needUpdate := false
		for _, config := range mixedPackage.PackageConfig {
			if nameMap[config.Name] == 0 {
				needUpdate = true
				continue
			}
		}
		if needUpdate {
			for _, config := range mixedPackage.PackageConfig {
				if nameMap[config.Name] == 1 {
					nameMap[config.Name] = 2
					items := slice.Filter(mixedPackage.PackageConfig, func(index int, item models.PackageConfig) bool {
						return nameMap[item.Name] == 0
					})
					nameMapArr[config.Name] = slice.Map(items, func(index int, item models.PackageConfig) string {
						return item.Name
					})
				}
			}
		}
	}
	return nameMap, nameMapArr, nil
}

func (s *MedicineService) UpdateBatch(ctx *gin.Context, batchInfo models.BatchInfo) error {
	customerOID := batchInfo.CustomerID
	envOID := batchInfo.EnvironmentID
	projectOID := batchInfo.ProjectID
	OID := envOID

	idList := batchInfo.IdList
	// if batchInfo.CohortID != primitive.NilObjectID {
	// 	OID = batchInfo.CohortID
	// }
	var histories []models.History

	user, err := tools.Me(ctx)
	if err != nil {
		return errors.WithStack(err)
	}

	// 查询 projectinfo
	var project models.Project
	if err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project); err != nil {
		return errors.WithStack(err)
	}
	timeZone, err := tools.GetTimeZone(projectOID)
	if err != nil {
		return errors.WithStack(err)
	}
	hours := time.Duration(timeZone)
	minutes := time.Duration((timeZone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute
	currentDate := time.Now().UTC().Add(duration).Format("2006-01-02")

	//库房、中心研究产品状态
	siteStauts := []string{"1", "4", "6", "7", "14", "20"}
	orderStatus := []string{"2", "3", "11"}

	//批次管理更新
	updateBatch := batchInfo.UpdateBatchs[0]

	//判断是有编号的研究产品，还是未编号的研究产品更新
	if updateBatch.Name == "-" { //有编号的研究产品
		if (idList == nil || len(idList) == 0) && batchInfo.UpdateCount == 0 {
			return nil
		}
		var updateIds []primitive.ObjectID
		var sitesOIDs []primitive.ObjectID
		var storehousesOIDs []primitive.ObjectID
		var ordersIDS []primitive.ObjectID
		//是否更新锁定状态
		lockStatus := false
		for _, updateBatch := range batchInfo.UpdateBatchs {
			if updateBatch.Type == 2 { //仓库
				storehousesOIDs = append(storehousesOIDs, updateBatch.PositionId)
			} else if updateBatch.Type == 1 { //中心
				sitesOIDs = append(sitesOIDs, updateBatch.PositionId)
			} else { //订单
				ordersIDS = append(ordersIDS, updateBatch.PositionId)
			}
		}
		if idList != nil && len(idList) > 0 {
			updateIds = idList
			batchInfo.UpdateCount = len(idList)
		}
		//更新仓库的研究产品
		if len(storehousesOIDs) > 0 {
			var newStatus []int
			//更新有效期的时候，已过期的研究产品，自动变为可用
			for _, status := range batchInfo.Status {
				if status == 7 && batchInfo.UpdateExpirationDate > currentDate { //需要更新已过期的研究产品
					storehouseFilter := bson.M{"customer_id": customerOID, "env_id": envOID, "batch_number": updateBatch.BatchNumber, "storehouse_id": bson.M{"$in": storehousesOIDs}, "expiration_date": updateBatch.ExpirationDate, "status": 7}
					if idList != nil && len(idList) > 0 {
						storehouseFilter["_id"] = bson.M{"$in": idList}
					} else {
						//查询id
						var drugData []map[string]interface{}
						queryPipeline := mongo.Pipeline{
							{{Key: "$match", Value: storehouseFilter}},
							{{Key: "$project", Value: bson.M{"_id": 0, "id": "$_id"}}},
						}
						cursor, err := tools.Database.Collection("medicine").Aggregate(nil, queryPipeline)
						if err != nil {
							return errors.WithStack(err)
						}
						err = cursor.All(nil, &drugData)
						if err != nil {
							return errors.WithStack(err)
						}
						for _, item := range drugData {
							updateIds = append(updateIds, item["id"].(primitive.ObjectID))
						}
					}

					update := bson.M{
						"$set": bson.M{
							"status":          1,
							"batch_number":    batchInfo.UpdateBatch,
							"expiration_date": batchInfo.UpdateExpirationDate,
						},
					}

					if _, err := tools.Database.Collection("medicine").UpdateMany(ctx, storehouseFilter, update); err != nil {
						return errors.WithStack(err)
					}
				} else {
					index := arrays.ContainsString(siteStauts, strconv.Itoa(status))
					if index != -1 {
						newStatus = append(newStatus, status)
					}
					if status == 20 {
						lockStatus = true
					}
				}
			}
			//更新数据
			if len(newStatus) > 0 {
				filter := bson.M{"customer_id": customerOID, "env_id": envOID, "batch_number": updateBatch.BatchNumber, "expiration_date": updateBatch.ExpirationDate, "storehouse_id": bson.M{"$in": storehousesOIDs}, "status": bson.M{"$in": newStatus}}
				if idList != nil && len(idList) > 0 {
					filter["_id"] = bson.M{"$in": idList}
				} else {
					//查询id
					var drugData []map[string]interface{}
					queryPipeline := mongo.Pipeline{
						{{Key: "$match", Value: filter}},
						{{Key: "$project", Value: bson.M{"_id": 0, "id": "$_id"}}},
					}
					cursor, err := tools.Database.Collection("medicine").Aggregate(nil, queryPipeline)
					if err != nil {
						return errors.WithStack(err)
					}
					err = cursor.All(nil, &drugData)
					if err != nil {
						return errors.WithStack(err)
					}
					for _, item := range drugData {
						updateIds = append(updateIds, item["id"].(primitive.ObjectID))
					}
				}

				update := bson.M{
					"$set": bson.M{
						"batch_number":    batchInfo.UpdateBatch,
						"expiration_date": batchInfo.UpdateExpirationDate,
					},
				}
				if _, err := tools.Database.Collection("medicine").UpdateMany(ctx, filter, update); err != nil {
					return errors.WithStack(err)
				}
			}
		}
		//更新中心的研究产品
		if len(sitesOIDs) > 0 {
			var newStatus []int
			//更新有效期的时候，已过期的研究产品，自动变为可用
			for _, status := range batchInfo.Status {
				if status == 7 && batchInfo.UpdateExpirationDate > currentDate { //需要更新已过期的研究产品
					siteFilter := bson.M{"customer_id": customerOID, "env_id": envOID, "batch_number": updateBatch.BatchNumber, "site_id": bson.M{"$in": sitesOIDs}, "expiration_date": updateBatch.ExpirationDate, "status": 7}
					if idList != nil && len(idList) > 0 {
						siteFilter["_id"] = bson.M{"$in": idList}
					} else {
						//查询id
						var drugData []map[string]interface{}
						queryPipeline := mongo.Pipeline{
							{{Key: "$match", Value: siteFilter}},
							{{Key: "$project", Value: bson.M{"_id": 0, "id": "$_id"}}},
						}
						cursor, err := tools.Database.Collection("medicine").Aggregate(nil, queryPipeline)
						if err != nil {
							return errors.WithStack(err)
						}
						err = cursor.All(nil, &drugData)
						if err != nil {
							return errors.WithStack(err)
						}
						for _, item := range drugData {
							updateIds = append(updateIds, item["id"].(primitive.ObjectID))
						}
					}

					update := bson.M{
						"$set": bson.M{
							"status":          1,
							"batch_number":    batchInfo.UpdateBatch,
							"expiration_date": batchInfo.UpdateExpirationDate,
						},
					}
					if _, err := tools.Database.Collection("medicine").UpdateMany(ctx, siteFilter, update); err != nil {
						return errors.WithStack(err)
					}
					continue
				} else {
					index := arrays.ContainsString(siteStauts, strconv.Itoa(status))
					if index != -1 {
						newStatus = append(newStatus, status)
					}
					if status == 20 {
						lockStatus = true
					}
				}
			}
			//更新数据
			if len(newStatus) > 0 {
				filter := bson.M{"customer_id": customerOID, "env_id": envOID, "batch_number": updateBatch.BatchNumber, "expiration_date": updateBatch.ExpirationDate, "site_id": bson.M{"$in": sitesOIDs}, "status": bson.M{"$in": newStatus}}
				if idList != nil && len(idList) > 0 {
					filter["_id"] = bson.M{"$in": idList}
				} else {
					//查询id
					var drugData []map[string]interface{}
					queryPipeline := mongo.Pipeline{
						{{Key: "$match", Value: filter}},
						{{Key: "$project", Value: bson.M{"_id": 0, "id": "$_id"}}},
					}
					cursor, err := tools.Database.Collection("medicine").Aggregate(nil, queryPipeline)
					if err != nil {
						return errors.WithStack(err)
					}
					err = cursor.All(nil, &drugData)
					if err != nil {
						return errors.WithStack(err)
					}
					for _, item := range drugData {
						updateIds = append(updateIds, item["id"].(primitive.ObjectID))
					}
				}

				update := bson.M{
					"$set": bson.M{
						"batch_number":    batchInfo.UpdateBatch,
						"expiration_date": batchInfo.UpdateExpirationDate,
					},
				}
				if _, err := tools.Database.Collection("medicine").UpdateMany(ctx, filter, update); err != nil {
					return errors.WithStack(err)
				}
			}
		}

		//如果更新的数据中锁定状态，同时更新订单审批申请信息
		sendIds := append(storehousesOIDs, sitesOIDs...)
		if lockStatus && len(sendIds) > 0 {
			filter := bson.M{"customer_id": customerOID, "env_id": envOID,
				"data.detail_data.expirationdate": updateBatch.ExpirationDate, "data.detail_data.batchnumber": updateBatch.BatchNumber,
				"data.send_id": bson.M{"$in": sendIds}, "status": 0}
			var orderAddTasks []models.OrderAddTask
			cursor, err := tools.Database.Collection("approval_process").Find(nil, filter)
			if err != nil {
				return errors.WithStack(err)
			}
			err = cursor.All(nil, &orderAddTasks)
			if err != nil {
				return errors.WithStack(err)
			}
			for _, orderAddTask := range orderAddTasks {
				detailDatas := orderAddTask.Data.DetailData
				for i, detailData := range detailDatas {
					if detailData.BatchNumber == updateBatch.BatchNumber && detailData.ExpirationDate == updateBatch.ExpirationDate {
						detailData.BatchNumber = batchInfo.UpdateBatch
						detailData.ExpirationDate = batchInfo.UpdateExpirationDate
						detailDatas[i] = detailData
					}
				}
				//更新
				update := bson.M{
					"$set": bson.M{
						"data.detail_data": detailDatas,
					},
				}
				_, err = tools.Database.Collection("approval_process").UpdateOne(ctx, bson.M{"_id": orderAddTask.ID}, update)
				if err != nil {
					return errors.WithStack(err)
				}
			}
		}

		//更新订单的研究产品
		if len(ordersIDS) > 0 {
			var newStatus []int
			for _, status := range batchInfo.Status {
				index := arrays.ContainsString(orderStatus, strconv.Itoa(status))
				if index != -1 {
					newStatus = append(newStatus, status)
				}
			}
			//更新数据
			if len(newStatus) > 0 {
				filter := bson.M{"customer_id": customerOID, "env_id": envOID, "batch_number": updateBatch.BatchNumber, "expiration_date": updateBatch.ExpirationDate, "order_id": bson.M{"$in": ordersIDS}, "status": bson.M{"$in": newStatus}}
				if idList != nil && len(idList) > 0 {
					filter["_id"] = bson.M{"$in": idList}
				} else {
					//查询id
					var drugData []map[string]interface{}
					queryPipeline := mongo.Pipeline{
						{{Key: "$match", Value: filter}},
						{{Key: "$project", Value: bson.M{"_id": 0, "id": "$_id"}}},
					}
					cursor, err := tools.Database.Collection("medicine").Aggregate(nil, queryPipeline)
					if err != nil {
						return errors.WithStack(err)
					}
					err = cursor.All(nil, &drugData)
					if err != nil {
						return errors.WithStack(err)
					}
					for _, item := range drugData {
						updateIds = append(updateIds, item["id"].(primitive.ObjectID))
					}
				}

				update := bson.M{
					"$set": bson.M{
						"batch_number":    batchInfo.UpdateBatch,
						"expiration_date": batchInfo.UpdateExpirationDate,
					},
				}
				if _, err := tools.Database.Collection("medicine").UpdateMany(ctx, filter, update); err != nil {
					return errors.WithStack(err)
				}

				//更新发药信息
				orderFilter := mongo.Pipeline{
					{{Key: "$match", Value: bson.M{"_id": bson.M{"$in": ordersIDS}, "type": bson.M{"$in": bson.A{5, 6}}}}},
					{{Key: "$project", Value: bson.M{"_id": 0, "id": "$_id", "dispensingId": "$dispensing_id"}}},
				}
				medicineOrderCursor, medicineOrderErr := tools.Database.Collection("medicine_order").Aggregate(nil, orderFilter)
				if medicineOrderErr != nil {
					return errors.WithStack(medicineOrderErr)
				}
				var medicineOrderData []map[string]primitive.ObjectID
				medicineOrderErr = medicineOrderCursor.All(nil, &medicineOrderData)
				if medicineOrderErr != nil {
					return errors.WithStack(medicineOrderErr)
				}

				for _, medicineOrder := range medicineOrderData {
					dispensingId := medicineOrder["dispensingId"]
					var dispensing models.Dispensing
					err := tools.Database.Collection("dispensing").FindOne(nil, bson.M{"_id": dispensingId}).Decode(&dispensing)
					if err != nil {
						return errors.WithStack(err)
					}
					for index, dispensingMedicine := range dispensing.DispensingMedicines {
						if dispensingMedicine.BatchNumber == updateBatch.BatchNumber && dispensingMedicine.ExpirationDate == updateBatch.ExpirationDate {
							dispensingMedicine.BatchNumber = batchInfo.UpdateBatch
							dispensingMedicine.ExpirationDate = batchInfo.UpdateExpirationDate
							dispensing.DispensingMedicines[index] = dispensingMedicine
						}
					}
					update = bson.M{
						"$set": bson.M{"dispensing_medicines": dispensing.DispensingMedicines},
					}
					_, err = tools.Database.Collection("dispensing").UpdateOne(ctx, bson.M{"_id": dispensing.ID}, update)
					if err != nil {
						return errors.WithStack(err)
					}
					history := models.History{
						Key:  "history.dispensing.updateBatch",
						OID:  dispensing.ID,
						Data: bson.M{"batchNumber": batchInfo.UpdateBatch, "expirationDate": batchInfo.UpdateExpirationDate, "count": batchInfo.UpdateCount, "status": batchInfo.Status},
						Time: time.Duration(time.Now().Unix()),
						UID:  user.ID,
						User: user.Name,
					}
					histories = append(histories, history)
				}

			}

			//订单轨迹---编号研究产品
			medicineList := make([]models.Medicine, 0)
			if updateIds != nil && len(updateIds) > 0 {
				filter := bson.M{"_id": bson.M{"$in": updateIds}}
				cursor, err := tools.Database.Collection("medicine").Find(ctx, filter)
				if err != nil {
					return errors.WithStack(err)
				}
				err = cursor.All(ctx, &medicineList)
				if err != nil {
					return errors.WithStack(err)
				}
			}
			medicineOrderList := make([]models.MedicineOrder, 0)
			medicineOrderCursor, err := tools.Database.Collection("medicine_order").Find(nil, bson.M{"_id": bson.M{"$in": ordersIDS}})
			if err != nil {
				return errors.WithStack(err)
			}
			err = medicineOrderCursor.All(nil, &medicineOrderList)
			if err != nil {
				return errors.WithStack(err)
			}
			//当前操作人
			user, err := tools.Me(ctx)
			if err != nil {
				return errors.WithStack(err)
			}
			//操作时间
			time := time.Duration(time.Now().Unix())
			for _, order := range medicineOrderList {

				ipExpireDateBatchList := make([]string, 0)
				ipList := make([]string, 0)
				ed := ""
				bt := ""
				if medicineList != nil && len(medicineList) > 0 {
					for _, medicine := range medicineList {
						if medicine.OrderID == order.ID {
							expireDate := "-"
							if &medicine.ExpirationDate != nil && medicine.ExpirationDate != "" {
								expireDate = medicine.ExpirationDate
							}
							if batchInfo.UpdateExpirationDate != "" {
								expireDate = batchInfo.UpdateExpirationDate
							}
							batch := "-"
							if &medicine.BatchNumber != nil && medicine.BatchNumber != "" {
								batch = medicine.BatchNumber
							}
							if batchInfo.UpdateBatch != "" {
								batch = batchInfo.UpdateBatch
							}
							ipExpireDateBatch := "[" + medicine.Number + "]-[" + expireDate + "]-[" + batch + "]"
							ipExpireDateBatchList = append(ipExpireDateBatchList, ipExpireDateBatch)
							ipList = append(ipList, medicine.Number)
							ed = expireDate
							bt = batch
						}
					}
				}

				history := models.History{
					Key:  "history.order.expireDate",
					OID:  order.ID,
					Data: bson.M{},
					Time: time,
					UID:  user.ID,
					User: user.Name,
				}
				showCustomTemps := []models.CustomTemp{}
				customTempOptions := []models.CustomTempOption{}
				customTempOptions = append(customTempOptions, models.CustomTempOption{
					Index: 0,
					Key:   "history.order.expireDateBatch.orderNumber",
					Data:  bson.M{"orderNumber": order.OrderNumber},
				})
				customTempOptions = append(customTempOptions, models.CustomTempOption{
					Index: 1,
					Key:   "history.order.expireDateBatch.ipExpireDateBatch",
					Data:  bson.M{"ipExpireDateBatch": strings.Join(ipExpireDateBatchList, "、")},
				})
				showCustomTemps = append(showCustomTemps, models.CustomTemp{
					ParKey:              "data",
					ConnectingSymbolKey: "history.dispensing.single.comma",
					LastSymbolKey:       "history.dispensing.single.period",
					CustomTempOptions:   customTempOptions,
				})

				history.ShowCustomTemps = &showCustomTemps
				tableInfo := models.TableInfo{}
				tableInfo.Title = append(tableInfo.Title, []string{
					"report.attributes.research",
					"report.attributes.research.expireDate",
					"report.attributes.research.batch",
				}...)
				if ipList != nil && len(ipList) > 0 {
					tmpCol := []models.CustomTempOption{}
					tmpCol = append(tmpCol, models.CustomTempOption{
						Index: 1,
						Key:   "history.dispensing.single.noKey",
						Data:  bson.M{"data": strings.Join(ipList, ",")},
					})

					tmpCol = append(tmpCol, models.CustomTempOption{
						Index: 2,
						Key:   "history.dispensing.single.noKey",
						Data:  bson.M{"data": ed},
						//TransData: "history.dispensing.send-type-" + convertor.ToString(value),
						//TransType: models.KeyData,
					})

					tmpCol = append(tmpCol, models.CustomTempOption{
						Index: 3,
						Key:   "history.dispensing.single.noKey",
						Data:  bson.M{"data": bt},
					})
					tableInfo.Value = append(tableInfo.Value, tmpCol)
				}
				history.TableInfo = &tableInfo

				histories = append(histories, history)
			}

		}

		//插入轨迹
		if len(updateIds) > 0 {
			for _, id := range updateIds {
				history := models.History{
					Key:  "history.medicine.updateBatch",
					OID:  id,
					Data: bson.M{"batchNumber": batchInfo.UpdateBatch, "expirationDate": batchInfo.UpdateExpirationDate, "count": 1, "status": batchInfo.Status},
					Time: time.Duration(time.Now().Unix()),
					UID:  user.ID,
					User: user.Name,
				}
				histories = append(histories, history)
			}
		}

		// 保存项目日志
		insertBatchManagementLog(ctx, OID, batchInfo, OID)
		ctx.Set("HISTORY", histories)

	} else { //未编号研究产品更新
		//包装是否打开
		isOpenPackage, _, packageDrugNames, _, _, err := tools.IsOpenPackage(envOID)
		if err != nil {
			return errors.WithStack(err)
		}

		var sitesOIDs []primitive.ObjectID
		var storehousesOIDs []primitive.ObjectID
		var ordersIDS []primitive.ObjectID
		var dtpOrdersIDS []primitive.ObjectID
		for _, updateBatch := range batchInfo.UpdateBatchs {
			if updateBatch.Type == 2 { //仓库
				storehousesOIDs = append(storehousesOIDs, updateBatch.PositionId)
			} else if updateBatch.Type == 1 { //中心
				sitesOIDs = append(sitesOIDs, updateBatch.PositionId)
			} else { //订单
				if updateBatch.OrderType == 5 || updateBatch.OrderType == 6 {
					dtpOrdersIDS = append(dtpOrdersIDS, updateBatch.PositionId)
				} else {
					ordersIDS = append(ordersIDS, updateBatch.PositionId)
				}
			}
		}

		if isOpenPackage && packageDrugNames[updateBatch.Name] > 0 { //未编号包装配置
			var newStatus []int
			//是否更新锁定状态
			//lockStatus := false
			for _, status := range batchInfo.Status {
				index := arrays.ContainsString(siteStauts, strconv.Itoa(status))
				if index != -1 {
					newStatus = append(newStatus, status)
				}
				// if status == 20 {
				// 	lockStatus = true
				// }
			}

			var allUpdateIds []primitive.ObjectID
			//查询出库房和仓库中符合状态的未编号研究产品
			if len(storehousesOIDs) > 0 || len(sitesOIDs) > 0 {
				orMatch := bson.A{}
				if len(storehousesOIDs) > 0 {
					orMatch = append(orMatch, bson.M{"storehouse_id": bson.M{"$in": storehousesOIDs}})
				}
				if len(sitesOIDs) > 0 {
					orMatch = append(orMatch, bson.M{"site_id": bson.M{"$in": sitesOIDs}})
				}
				filter := bson.M{"customer_id": customerOID, "env_id": envOID, "name": updateBatch.Name,
					"batch_number":    updateBatch.BatchNumber,
					"$or":             orMatch,
					"expiration_date": updateBatch.ExpirationDate, "status": bson.M{"$in": newStatus}}
				queryPipeline := mongo.Pipeline{
					{{Key: "$match", Value: filter}},
					{{Key: "$project", Value: bson.M{"_id": 0, "id": "$_id"}}},
				}
				var drugData []map[string]interface{}
				cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, queryPipeline)
				if err != nil {
					return errors.WithStack(err)
				}
				err = cursor.All(nil, &drugData)
				if err != nil {
					return errors.WithStack(err)
				}

				for _, item := range drugData {
					allUpdateIds = append(allUpdateIds, item["id"].(primitive.ObjectID))
				}
			}
			//查询订单
			if len(ordersIDS) > 0 {
				var newStatus []int
				for _, status := range batchInfo.Status {
					index := arrays.ContainsString(orderStatus, strconv.Itoa(status))
					if index != -1 {
						newStatus = append(newStatus, status)
					}
				}

				filter := bson.M{"customer_id": customerOID, "env_id": envOID, "name": updateBatch.Name,
					"batch_number": updateBatch.BatchNumber, "order_id": bson.M{"$in": ordersIDS},
					"expiration_date": updateBatch.ExpirationDate, "status": bson.M{"$in": newStatus}}
				queryPipeline := mongo.Pipeline{
					{{Key: "$match", Value: filter}},
					{{Key: "$project", Value: bson.M{"_id": 0, "id": "$_id"}}},
				}
				var drugData []map[string]interface{}
				cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, queryPipeline)
				if err != nil {
					return errors.WithStack(err)
				}
				err = cursor.All(nil, &drugData)
				if err != nil {
					return errors.WithStack(err)
				}

				for _, item := range drugData {
					allUpdateIds = append(allUpdateIds, item["id"].(primitive.ObjectID))
				}
			}
			//查询dtp订单
			if len(dtpOrdersIDS) > 0 {
				var packageNumbers []string
				var newStatus []int
				for _, status := range batchInfo.Status {
					index := arrays.ContainsString(orderStatus, strconv.Itoa(status))
					if index != -1 {
						newStatus = append(newStatus, status)
					}
				}
				filter := bson.M{"customer_id": customerOID, "env_id": envOID, "name": updateBatch.Name,
					"batch_number": updateBatch.BatchNumber, "order_id": bson.M{"$in": dtpOrdersIDS},
					"expiration_date": updateBatch.ExpirationDate, "status": bson.M{"$in": newStatus}}
				queryPipeline := mongo.Pipeline{
					{{Key: "$match", Value: filter}},
					{{Key: "$group", Value: bson.M{"_id": "$package_number"}}},
					{{Key: "$project", Value: bson.M{"_id": 0, "id": "$_id"}}},
				}
				var packageNumberData []map[string]interface{}
				cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, queryPipeline)
				if err != nil {
					return errors.WithStack(err)
				}
				err = cursor.All(nil, &packageNumberData)
				if err != nil {
					return errors.WithStack(err)
				}

				for _, item := range packageNumberData {
					packageNumbers = append(packageNumbers, item["id"].(string))
				}

				var drugData []map[string]interface{}
				queryPipeline = mongo.Pipeline{
					{{Key: "$match", Value: bson.M{"customer_id": customerOID, "env_id": envOID, "name": updateBatch.Name,
						"batch_number": updateBatch.BatchNumber, "expiration_date": updateBatch.ExpirationDate, "package_number": bson.M{"$in": packageNumbers}}}},
					{{Key: "$project", Value: bson.M{"_id": 0, "id": "$_id"}}},
				}
				cursor, err = tools.Database.Collection("medicine_others").Aggregate(nil, queryPipeline)
				if err != nil {
					return errors.WithStack(err)
				}
				err = cursor.All(nil, &drugData)
				if err != nil {
					return errors.WithStack(err)
				}
				for _, item := range drugData {
					allUpdateIds = append(allUpdateIds, item["id"].(primitive.ObjectID))
				}
			}
			//整包更新
			if len(allUpdateIds) > 0 {
				queryPipeline := mongo.Pipeline{
					{{Key: "$match", Value: bson.M{"_id": bson.M{"$in": allUpdateIds}}}},
					{{Key: "$group", Value: bson.M{"_id": bson.M{"package_number": "$package_number"}, "availableCount": bson.M{"$sum": 1}, "otherIds": bson.M{"$push": "$_id"}}}},
					{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$availableCount", packageDrugNames[updateBatch.Name]}}}}},
					{{Key: "$project", Value: bson.M{"otherIds": 1}}},
				}
				var drugData []map[string]interface{}
				cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, queryPipeline)
				if err != nil {
					return errors.WithStack(err)
				}
				err = cursor.All(nil, &drugData)
				if err != nil {
					return errors.WithStack(err)
				}

				var updateDrugData []map[string]interface{}
				if len(batchInfo.Status) == 1 && !batchInfo.MultUpdate && len(dtpOrdersIDS) <= 0 {
					slicCount := batchInfo.UpdateCount / packageDrugNames[updateBatch.Name]
					if slicCount > len(drugData) {
						slicCount = len(drugData)
					}
					updateDrugData = drugData[:slicCount]
				} else {
					updateDrugData = drugData
				}

				var updateIds []primitive.ObjectID
				for _, otherMedicine := range updateDrugData {
					otherIds := otherMedicine["otherIds"].(primitive.A)
					for _, id := range otherIds {
						updateIds = append(updateIds, id.(primitive.ObjectID))
					}
				}

				if len(updateIds) > 0 {
					//轨迹
					histories, _ = s.insertUpdateBatchHistory(ctx, user, batchInfo, updateBatch, updateIds, histories, true, packageDrugNames[updateBatch.Name])

					update := bson.M{
						"$set": bson.M{
							"batch_number":    batchInfo.UpdateBatch,
							"expiration_date": batchInfo.UpdateExpirationDate,
						},
					}
					if _, err := tools.Database.Collection("medicine_others").UpdateMany(ctx, bson.M{"_id": bson.M{"$in": updateIds}}, update); err != nil {
						return errors.WithStack(err)
					}

					//订单轨迹-未编号研究产品
					otherMedicineList := make([]models.OtherMedicine, 0)
					if updateIds != nil && len(updateIds) > 0 {
						filter := bson.M{"_id": bson.M{"$in": updateIds}}
						cursor, err := tools.Database.Collection("medicine_others").Find(ctx, filter)
						if err != nil {
							return errors.WithStack(err)
						}
						err = cursor.All(ctx, &otherMedicineList)
						if err != nil {
							return errors.WithStack(err)
						}
					}
					if len(ordersIDS) > 0 {
						medicineOrderList := make([]models.MedicineOrder, 0)
						medicineOrderCursor, err := tools.Database.Collection("medicine_order").Find(nil, bson.M{"_id": bson.M{"$in": ordersIDS}})
						if err != nil {
							return errors.WithStack(err)
						}
						err = medicineOrderCursor.All(nil, &medicineOrderList)
						if err != nil {
							return errors.WithStack(err)
						}
						//当前操作人
						user, err := tools.Me(ctx)
						if err != nil {
							return errors.WithStack(err)
						}
						//操作时间
						time := time.Duration(time.Now().Unix())
						for _, order := range medicineOrderList {

							ipExpireDateBatchList := make([]string, 0)
							oIpList := make([]string, 0)
							ed := ""
							bt := ""
							if otherMedicineList != nil && len(otherMedicineList) > 0 {
								for _, otherMedicine := range otherMedicineList {
									if otherMedicine.OrderID == order.ID {
										expireDate := "-"
										if &otherMedicine.ExpirationDate != nil && otherMedicine.ExpirationDate != "" {
											expireDate = otherMedicine.ExpirationDate
										}
										if batchInfo.UpdateExpirationDate != "" {
											expireDate = batchInfo.UpdateExpirationDate
										}
										batch := "-"
										if &otherMedicine.BatchNumber != nil && otherMedicine.BatchNumber != "" {
											batch = otherMedicine.BatchNumber
										}
										if batchInfo.UpdateBatch != "" {
											batch = batchInfo.UpdateBatch
										}
										ipExpireDateBatch := "[" + otherMedicine.Name + "]-[" + expireDate + "]-[" + batch + "]"
										ipExpireDateBatchList = append(ipExpireDateBatchList, ipExpireDateBatch)
										oIpList = append(oIpList, otherMedicine.Name)
										ed = expireDate
										bt = batch
									}
								}
							}

							history := models.History{
								Key:  "history.order.expireDate",
								OID:  order.ID,
								Data: bson.M{},
								Time: time,
								UID:  user.ID,
								User: user.Name,
							}
							showCustomTemps := []models.CustomTemp{}
							customTempOptions := []models.CustomTempOption{}
							customTempOptions = append(customTempOptions, models.CustomTempOption{
								Index: 0,
								Key:   "history.order.expireDateBatch.orderNumber",
								Data:  bson.M{"orderNumber": order.OrderNumber},
							})
							customTempOptions = append(customTempOptions, models.CustomTempOption{
								Index: 1,
								Key:   "history.order.expireDateBatch.ipExpireDateBatch",
								Data:  bson.M{"ipExpireDateBatch": strings.Join(ipExpireDateBatchList, "、")},
							})
							showCustomTemps = append(showCustomTemps, models.CustomTemp{
								ParKey:              "data",
								ConnectingSymbolKey: "history.dispensing.single.comma",
								LastSymbolKey:       "history.dispensing.single.period",
								CustomTempOptions:   customTempOptions,
							})

							history.ShowCustomTemps = &showCustomTemps
							tableInfo := models.TableInfo{}
							tableInfo.Title = append(tableInfo.Title, []string{
								"report.attributes.research",
								"report.attributes.research.expireDate",
								"report.attributes.research.batch",
							}...)
							ipList := make([]string, 0)
							// 创建一个 map 用于存储不同 name 值的计数
							nameCount := make(map[string]int)
							// 遍历对象数组，对不同 name 值进行计数
							for _, obj := range oIpList {
								nameCount[obj]++
							}
							//未编号研究产品单品轨迹
							otherKeyOrder := models.OtherMedicineFreeze{
								CustomerID:    customerOID,
								ProjectID:     projectOID,
								EnvironmentID: envOID,
							}
							if otherMedicineList[0].StorehouseID != primitive.NilObjectID {
								//otherKeyOrder.InstituteID = otherMedicineList[0].StorehouseID
								otherKeyOrder.InstituteType = 2
							}
							if otherMedicineList[0].SiteID != primitive.NilObjectID {
								//otherKeyOrder.InstituteID = otherMedicineList[0].SiteID
								otherKeyOrder.InstituteType = 1
							}
							// 打印每个不同 name 值的计数
							for name, count := range nameCount {
								otherName := name + "/" + strconv.Itoa(count)
								ipList = append(ipList, otherName)
								om := models.FreezeOtherMedicines{
									Name:         name,
									ExpireDate:   batchInfo.UpdateExpirationDate,
									Batch:        batchInfo.UpdateBatch,
									Count:        count,
									StorehouseID: otherMedicineList[0].StorehouseID,
									SiteID:       otherMedicineList[0].SiteID,
								}

								//轨迹 查询otherKey
								medicineOtherKey, _ := s.getOtherMedicineKey(ctx, otherKeyOrder, om)
								histories = append(histories, models.History{
									Key:  "history.medicine.updateBatch",
									OID:  medicineOtherKey.ID,
									Data: bson.M{"batchNumber": batchInfo.UpdateBatch, "expirationDate": batchInfo.UpdateExpirationDate, "count": count, "status": batchInfo.Status},
									Time: time,
									UID:  user.ID,
									User: user.Name,
								})
							}
							if ipList != nil && len(ipList) > 0 {
								tmpCol := []models.CustomTempOption{}
								tmpCol = append(tmpCol, models.CustomTempOption{
									Index: 1,
									Key:   "history.dispensing.single.noKey",
									Data:  bson.M{"data": strings.Join(ipList, ",")},
								})

								tmpCol = append(tmpCol, models.CustomTempOption{
									Index: 2,
									Key:   "history.dispensing.single.noKey",
									Data:  bson.M{"data": ed},
									//TransData: "history.dispensing.send-type-" + convertor.ToString(value),
									//TransType: models.KeyData,
								})

								tmpCol = append(tmpCol, models.CustomTempOption{
									Index: 3,
									Key:   "history.dispensing.single.noKey",
									Data:  bson.M{"data": bt},
								})
								tableInfo.Value = append(tableInfo.Value, tmpCol)
							}
							history.TableInfo = &tableInfo
							histories = append(histories, history)
						}
					}
				}
			}
		} else { // 单品
			//是否更新锁定状态
			lockStatus := false
			var update7Ids []primitive.ObjectID
			var updateSIds []primitive.ObjectID
			var updateIds []primitive.ObjectID
			//更新仓库的研究产品
			if len(storehousesOIDs) > 0 {
				var newStatus []int
				//更新有效期的时候，已过期的研究产品，自动变为可用
				for _, status := range batchInfo.Status {
					if status == 7 && batchInfo.UpdateExpirationDate > currentDate { //需要更新已过期的研究产品
						var allIds []primitive.ObjectID
						storehouseFilter := bson.M{"customer_id": customerOID, "env_id": envOID, "name": updateBatch.Name, "batch_number": updateBatch.BatchNumber, "storehouse_id": bson.M{"$in": storehousesOIDs}, "expiration_date": updateBatch.ExpirationDate, "status": 7}
						//查询id
						var drugData []map[string]interface{}
						queryPipeline := mongo.Pipeline{
							{{Key: "$match", Value: storehouseFilter}},
							{{Key: "$project", Value: bson.M{"_id": 0, "id": "$_id"}}},
						}
						cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, queryPipeline)
						if err != nil {
							return errors.WithStack(err)
						}
						err = cursor.All(nil, &drugData)
						if err != nil {
							return errors.WithStack(err)
						}
						for _, item := range drugData {
							allIds = append(allIds, item["id"].(primitive.ObjectID))
						}

						if len(batchInfo.Status) == 1 && !batchInfo.MultUpdate {
							update7Ids = append(update7Ids, allIds[:batchInfo.UpdateCount]...)
						} else {
							update7Ids = append(update7Ids, allIds...)
						}
						updateIds = append(updateIds, update7Ids...)
					} else {
						index := arrays.ContainsString(siteStauts, strconv.Itoa(status))
						if index != -1 {
							newStatus = append(newStatus, status)
						}
						if status == 20 {
							lockStatus = true
						}
					}
				}
				//更新数据
				if len(newStatus) > 0 {
					filter := bson.M{"customer_id": customerOID, "env_id": envOID, "name": updateBatch.Name, "batch_number": updateBatch.BatchNumber, "expiration_date": updateBatch.ExpirationDate, "storehouse_id": bson.M{"$in": storehousesOIDs}, "status": bson.M{"$in": newStatus}}
					//查询id
					var drugData []map[string]interface{}
					queryPipeline := mongo.Pipeline{
						{{Key: "$match", Value: filter}},
						{{Key: "$project", Value: bson.M{"_id": 0, "id": "$_id"}}},
					}
					cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, queryPipeline)
					if err != nil {
						return errors.WithStack(err)
					}
					err = cursor.All(nil, &drugData)
					if err != nil {
						return errors.WithStack(err)
					}
					var allIds []primitive.ObjectID
					for _, item := range drugData {
						allIds = append(allIds, item["id"].(primitive.ObjectID))
					}

					if len(batchInfo.Status) == 1 && !batchInfo.MultUpdate {
						updateSIds = append(updateSIds, allIds[:batchInfo.UpdateCount]...)
					} else {
						updateSIds = append(updateSIds, allIds...)
					}
					updateIds = append(updateIds, updateSIds...)
				}

				// if len(update7Ids) > 0 {
				// 	update := bson.M{
				// 		"$set": bson.M{
				// 			"status":          1,
				// 			"batch_number":    batchInfo.UpdateBatch,
				// 			"expiration_date": batchInfo.UpdateExpirationDate,
				// 		},
				// 	}
				// 	if _, err := tools.Database.Collection("medicine_others").UpdateMany(ctx, bson.M{"_id": bson.M{"$in": update7Ids}}, update); err != nil {
				// 		return errors.WithStack(err)
				// 	}
				// }
				// if len(updateSIds) > 0 {
				// 	update := bson.M{
				// 		"$set": bson.M{
				// 			"batch_number":    batchInfo.UpdateBatch,
				// 			"expiration_date": batchInfo.UpdateExpirationDate,
				// 		},
				// 	}
				// 	if _, err := tools.Database.Collection("medicine_others").UpdateMany(ctx, bson.M{"_id": bson.M{"$in": updateSIds}}, update); err != nil {
				// 		return errors.WithStack(err)
				// 	}
				// }
			}

			//更新中心的研究产品
			if len(sitesOIDs) > 0 {
				var newStatus []int
				// var update7Ids []primitive.ObjectID
				// var updateSIds []primitive.ObjectID
				//更新有效期的时候，已过期的研究产品，自动变为可用
				for _, status := range batchInfo.Status {
					if status == 7 && batchInfo.UpdateExpirationDate > currentDate { //需要更新已过期的研究产品
						siteFilter := bson.M{"customer_id": customerOID, "env_id": envOID, "name": updateBatch.Name, "batch_number": updateBatch.BatchNumber, "site_id": bson.M{"$in": sitesOIDs}, "expiration_date": updateBatch.ExpirationDate, "status": 7}
						//查询id
						var drugData []map[string]interface{}
						queryPipeline := mongo.Pipeline{
							{{Key: "$match", Value: siteFilter}},
							{{Key: "$project", Value: bson.M{"_id": 0, "id": "$_id"}}},
						}
						cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, queryPipeline)
						if err != nil {
							return errors.WithStack(err)
						}
						err = cursor.All(nil, &drugData)
						if err != nil {
							return errors.WithStack(err)
						}
						var allIds []primitive.ObjectID
						for _, item := range drugData {
							allIds = append(allIds, item["id"].(primitive.ObjectID))
						}

						if len(batchInfo.Status) == 1 && !batchInfo.MultUpdate {
							if allIds != nil && len(allIds) > 0 {
								update7Ids = append(update7Ids, allIds[:batchInfo.UpdateCount]...)
							}
						} else {
							if allIds != nil && len(allIds) > 0 {
								update7Ids = append(update7Ids, allIds...)
							}
						}
						updateIds = append(updateIds, update7Ids...)

						continue
					} else {
						index := arrays.ContainsString(siteStauts, strconv.Itoa(status))
						if index != -1 {
							newStatus = append(newStatus, status)
						}
						if status == 20 {
							lockStatus = true
						}
					}
				}
				//更新数据
				if len(newStatus) > 0 {
					filter := bson.M{"customer_id": customerOID, "env_id": envOID, "name": updateBatch.Name, "batch_number": updateBatch.BatchNumber, "expiration_date": updateBatch.ExpirationDate, "site_id": bson.M{"$in": sitesOIDs}, "status": bson.M{"$in": newStatus}}
					//查询id
					var drugData []map[string]interface{}
					queryPipeline := mongo.Pipeline{
						{{Key: "$match", Value: filter}},
						{{Key: "$project", Value: bson.M{"_id": 0, "id": "$_id"}}},
					}
					cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, queryPipeline)
					if err != nil {
						return errors.WithStack(err)
					}
					err = cursor.All(nil, &drugData)
					if err != nil {
						return errors.WithStack(err)
					}
					var allIds []primitive.ObjectID
					for _, item := range drugData {
						allIds = append(allIds, item["id"].(primitive.ObjectID))
					}

					if len(batchInfo.Status) == 1 && !batchInfo.MultUpdate {
						if allIds != nil && len(allIds) > 0 {
							updateSIds = append(updateSIds, allIds[:batchInfo.UpdateCount]...)
						}
					} else {
						if allIds != nil && len(allIds) > 0 {
							updateSIds = append(updateSIds, allIds...)
						}
					}
					updateIds = append(updateIds, updateSIds...)

				}
				// histories, _ = s.insertUpdateBatchHistory(ctx, user, batchInfo, updateBatch, updateIds, histories)
				// if len(update7Ids) > 0 {
				// 	update := bson.M{
				// 		"$set": bson.M{
				// 			"status":          1,
				// 			"batch_number":    batchInfo.UpdateBatch,
				// 			"expiration_date": batchInfo.UpdateExpirationDate,
				// 		},
				// 	}

				// 	if _, err := tools.Database.Collection("medicine_others").UpdateMany(ctx, bson.M{"_id": bson.M{"$in": update7Ids}}, update); err != nil {
				// 		return errors.WithStack(err)
				// 	}
				// }
				// if len(updateSIds) > 0 {
				// 	update := bson.M{
				// 		"$set": bson.M{
				// 			"batch_number":    batchInfo.UpdateBatch,
				// 			"expiration_date": batchInfo.UpdateExpirationDate,
				// 		},
				// 	}
				// 	if _, err := tools.Database.Collection("medicine_others").UpdateMany(ctx, bson.M{"_id": bson.M{"$in": updateSIds}}, update); err != nil {
				// 		return errors.WithStack(err)
				// 	}
				// }
			}

			//如果更新的数据中锁定状态，同时更新订单审批申请信息
			sendIds := append(storehousesOIDs, sitesOIDs...)
			if lockStatus && len(sendIds) > 0 {
				if lockStatus {
					filter := bson.M{"customer_id": customerOID, "env_id": envOID, "data.other_detail_data.name": updateBatch.Name,
						"data.other_detail_data.expirationdate": updateBatch.ExpirationDate, "data.other_detail_data.batchnumber": updateBatch.BatchNumber,
						"data.send_id": updateBatch.PositionId, "status": 0}
					var orderAddTasks []models.OrderAddTask
					cursor, err := tools.Database.Collection("approval_process").Find(nil, filter)
					if err != nil {
						return errors.WithStack(err)
					}
					err = cursor.All(nil, &orderAddTasks)
					if err != nil {
						return errors.WithStack(err)
					}
					for _, orderAddTask := range orderAddTasks {
						otherDetailDatas := orderAddTask.Data.OtherDetailData
						for i, detailData := range otherDetailDatas {
							if detailData.Name == updateBatch.Name && detailData.BatchNumber == updateBatch.BatchNumber && detailData.ExpirationDate == updateBatch.ExpirationDate {
								detailData.BatchNumber = batchInfo.UpdateBatch
								detailData.ExpirationDate = batchInfo.UpdateExpirationDate
								otherDetailDatas[i] = detailData
							}
						}
						//更新
						update := bson.M{
							"$set": bson.M{
								"data.other_detail_data": otherDetailDatas,
							},
						}
						_, err = tools.Database.Collection("approval_process").UpdateOne(ctx, bson.M{"_id": orderAddTask.ID}, update)
						if err != nil {
							return errors.WithStack(err)
						}
					}
				}
			}

			//更新订单的研究产品
			allOrdersIDS := append(ordersIDS, dtpOrdersIDS...)
			if len(allOrdersIDS) > 0 {

				//var updateSIds []primitive.ObjectID
				var newStatus []int
				for _, status := range batchInfo.Status {
					index := arrays.ContainsString(orderStatus, strconv.Itoa(status))
					if index != -1 {
						newStatus = append(newStatus, status)
					}
				}
				//更新数据
				if len(newStatus) > 0 {
					filter := bson.M{"customer_id": customerOID, "env_id": envOID, "name": updateBatch.Name, "order_id": bson.M{"$in": allOrdersIDS}, "status": bson.M{"$in": newStatus}}
					if updateBatch.ExpirationDate != "" && updateBatch.ExpirationDate != "-" {
						filter["expiration_date"] = updateBatch.ExpirationDate
					}
					if updateBatch.BatchNumber != "" && updateBatch.BatchNumber != "-" {
						filter["batch_number"] = updateBatch.BatchNumber
					}
					//查询id
					var drugData []map[string]interface{}
					queryPipeline := mongo.Pipeline{
						{{Key: "$match", Value: filter}},
						{{Key: "$project", Value: bson.M{"_id": 0, "id": "$_id"}}},
					}
					cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, queryPipeline)
					if err != nil {
						return errors.WithStack(err)
					}
					err = cursor.All(nil, &drugData)
					if err != nil {
						return errors.WithStack(err)
					}
					var allIds []primitive.ObjectID
					for _, item := range drugData {
						allIds = append(allIds, item["id"].(primitive.ObjectID))
					}
					if len(batchInfo.Status) == 1 && !batchInfo.MultUpdate {
						updateSIds = append(updateSIds, allIds[:batchInfo.UpdateCount]...)
					} else {
						updateSIds = append(updateSIds, allIds...)
					}
					updateIds = append(updateIds, updateSIds...)
					// //插入轨迹
					// histories, _ = s.insertUpdateBatchHistory(ctx, user, batchInfo, updateBatch, updateIds, histories)
					// update := bson.M{
					// 	"$set": bson.M{
					// 		"batch_number":    batchInfo.UpdateBatch,
					// 		"expiration_date": batchInfo.UpdateExpirationDate,
					// 	},
					// }
					// if _, err := tools.Database.Collection("medicine_others").UpdateMany(ctx, bson.M{"_id": bson.M{"$in": updateSIds}}, update); err != nil {
					// 	return errors.WithStack(err)
					// }

				}
			}

			//先插入轨迹，在更新数据
			histories, _ = s.insertUpdateBatchHistory(ctx, user, batchInfo, updateBatch, updateIds, histories, false, 0)

			if len(update7Ids) > 0 {
				update := bson.M{
					"$set": bson.M{
						"status":          1,
						"batch_number":    batchInfo.UpdateBatch,
						"expiration_date": batchInfo.UpdateExpirationDate,
					},
				}

				if _, err := tools.Database.Collection("medicine_others").UpdateMany(ctx, bson.M{"_id": bson.M{"$in": update7Ids}}, update); err != nil {
					return errors.WithStack(err)
				}
			}
			if len(updateSIds) > 0 {
				update := bson.M{
					"$set": bson.M{
						"batch_number":    batchInfo.UpdateBatch,
						"expiration_date": batchInfo.UpdateExpirationDate,
					},
				}
				if _, err := tools.Database.Collection("medicine_others").UpdateMany(ctx, bson.M{"_id": bson.M{"$in": updateSIds}}, update); err != nil {
					return errors.WithStack(err)
				}
			}

		}

		// 保存项目日志
		insertBatchManagementLog(ctx, OID, batchInfo, OID)
		ctx.Set("HISTORY", histories)
	}
	return nil

}

func (s *MedicineService) insertUpdateBatchHistory(ctx *gin.Context, user models.User, batchInfo models.BatchInfo,
	oldUpdateBatch models.BatchManagement, updateIds []primitive.ObjectID, histories []models.History, isPackage bool, packageNumber int) ([]models.History, error) {
	//插入轨迹
	customerOID := batchInfo.CustomerID
	envOID := batchInfo.EnvironmentID
	projectOID := batchInfo.ProjectID
	if len(updateIds) > 0 {
		oldHistoryKey := models.FreezeOtherMedicines{
			Name:       oldUpdateBatch.Name,
			ExpireDate: oldUpdateBatch.ExpirationDate,
			Batch:      oldUpdateBatch.BatchNumber,
		}

		var groupOtherMedicineData []map[string]interface{}
		match := bson.M{"$and": bson.A{
			bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "_id": bson.M{"$in": updateIds}},
			bson.M{"storehouse_id": bson.M{"$ne": nil}},
			bson.M{"storehouse_id": bson.M{"$ne": ""}},
			bson.M{"storehouse_id": bson.M{"$ne": primitive.NilObjectID}},
		}}
		//判断药物列表是否有数据
		groupPipeline := mongo.Pipeline{
			{{Key: "$match", Value: match}},
			{{Key: "$group", Value: bson.M{"_id": bson.M{"storehouseId": "$storehouse_id"}, "count": bson.M{"$sum": 1}, "otherIds": bson.M{"$push": "$_id"}}}},
		}
		cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, groupPipeline)
		err = cursor.All(ctx, &groupOtherMedicineData)
		if err != nil {
			return histories, errors.WithStack(err)
		}
		for _, groupOther := range groupOtherMedicineData {
			other := groupOther["_id"].(map[string]interface{})
			count := groupOther["count"].(int32)
			historyCount := fmt.Sprintf("%v", count)
			if isPackage {
				historyCount = fmt.Sprintf("%v(%v)", int(count), int(count)/packageNumber)
			}
			//未编号研究产品单品轨迹
			otherKeyOrder := models.OtherMedicineFreeze{
				CustomerID:    customerOID,
				ProjectID:     projectOID,
				EnvironmentID: envOID,
				//InstituteID:   other["storehouseId"].(primitive.ObjectID),
				InstituteType: 2,
			}
			oldHistoryKey.StorehouseID = other["storehouseId"].(primitive.ObjectID)
			medicineOldOtherKey, _ := s.getOtherMedicineKey(ctx, otherKeyOrder, oldHistoryKey)
			//判断批次管理的更新的时候是否全部更新
			countFilter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "storehouse_id": other["storehouseId"].(primitive.ObjectID),
				"name": oldHistoryKey.Name, "expiration_date": oldHistoryKey.ExpireDate, "batch_number": oldHistoryKey.Batch}
			total, err := tools.Database.Collection("medicine_others").CountDocuments(ctx, countFilter)
			if err != nil {
				return histories, nil
			}
			if int(total) == int(count) { //此key的数据全部更新，去更新key的批次有效期的
				//查询otherKey
				var medicineOtherKey models.MedicineOtherKey
				filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "site_id": other["storehouseId"].(primitive.ObjectID),
					"name": oldHistoryKey.Name, "expiration_date": batchInfo.UpdateExpirationDate, "batch_number": batchInfo.UpdateBatch}
				err := tools.Database.Collection("medicine_other_key").FindOne(ctx, filter).Decode(&medicineOtherKey)
				if err == mongo.ErrNoDocuments {
					_, err = tools.Database.Collection("medicine_other_key").UpdateOne(nil, bson.M{"_id": medicineOldOtherKey.ID}, bson.M{"$set": bson.M{"batch_number": batchInfo.UpdateBatch, "expiration_date": batchInfo.UpdateExpirationDate}})
					if err != nil {
						return histories, errors.WithStack(err)
					}
				}
			} else {
				om := models.FreezeOtherMedicines{
					Name:       oldHistoryKey.Name,
					ExpireDate: batchInfo.UpdateExpirationDate,
					Batch:      batchInfo.UpdateBatch,
					Count:      int(count),
				}
				//轨迹 查询otherKey
				medicineOtherKey, _ := s.getOtherMedicineKey(ctx, otherKeyOrder, om)
				histories = append(histories, models.History{
					Key:  "history.medicine.updateOtherBatch",
					OID:  medicineOtherKey.ID,
					Data: bson.M{"batchNumber": batchInfo.UpdateBatch, "expirationDate": batchInfo.UpdateExpirationDate, "count": historyCount, "status": batchInfo.Status},
					Time: time.Duration(time.Now().Unix()),
					UID:  user.ID,
					User: user.Name,
				})
			}

			histories = append(histories, models.History{
				Key:  "history.medicine.updateOtherBatch",
				OID:  medicineOldOtherKey.ID,
				Data: bson.M{"batchNumber": batchInfo.UpdateBatch, "expirationDate": batchInfo.UpdateExpirationDate, "count": historyCount, "status": batchInfo.Status},
				Time: time.Duration(time.Now().Unix()),
				UID:  user.ID,
				User: user.Name,
			})
		}

		var siteGroupOtherMedicineData []map[string]interface{}
		siteMatch := bson.M{"$and": bson.A{
			bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "_id": bson.M{"$in": updateIds}},
			bson.M{"site_id": bson.M{"$ne": nil}},
			bson.M{"site_id": bson.M{"$ne": ""}},
			bson.M{"site_id": bson.M{"$ne": primitive.NilObjectID}},
		}}
		//判断药物列表是否有数据
		siteGroupPipeline := mongo.Pipeline{
			{{Key: "$match", Value: siteMatch}},
			{{Key: "$group", Value: bson.M{"_id": bson.M{"siteId": "$site_id"}, "count": bson.M{"$sum": 1}, "otherIds": bson.M{"$push": "$_id"}}}},
		}
		cursor, err = tools.Database.Collection("medicine_others").Aggregate(nil, siteGroupPipeline)
		err = cursor.All(ctx, &siteGroupOtherMedicineData)
		if err != nil {
			return histories, errors.WithStack(err)
		}
		for _, groupOther := range siteGroupOtherMedicineData {
			other := groupOther["_id"].(map[string]interface{})
			count := groupOther["count"].(int32)
			historyCount := fmt.Sprintf("%v", count)
			if isPackage {
				historyCount = fmt.Sprintf("%v(%v)", int(count), int(count)/packageNumber)
			}
			//未编号研究产品单品轨迹
			otherKeyOrder := models.OtherMedicineFreeze{
				CustomerID:    customerOID,
				ProjectID:     projectOID,
				EnvironmentID: envOID,
				//InstituteID:   other["siteId"].(primitive.ObjectID),
				InstituteType: 1,
			}
			oldHistoryKey.SiteID = other["siteId"].(primitive.ObjectID)
			medicineOldOtherKey, _ := s.getOtherMedicineKey(ctx, otherKeyOrder, oldHistoryKey)
			//判断批次管理的更新的时候是否全部更新
			countFilter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "site_id": other["siteId"].(primitive.ObjectID),
				"name": oldHistoryKey.Name, "expiration_date": oldHistoryKey.ExpireDate, "batch_number": oldHistoryKey.Batch}
			total, err := tools.Database.Collection("medicine_others").CountDocuments(ctx, countFilter)
			if err != nil {
				return histories, nil
			}
			if int(total) == int(count) { //此key的数据全部更新，去更新key的批次有效期的
				//查询otherKey
				var medicineOtherKey models.MedicineOtherKey
				filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "site_id": other["siteId"].(primitive.ObjectID),
					"name": oldHistoryKey.Name, "expiration_date": batchInfo.UpdateExpirationDate, "batch_number": batchInfo.UpdateBatch}
				err := tools.Database.Collection("medicine_other_key").FindOne(ctx, filter).Decode(&medicineOtherKey)
				if err == mongo.ErrNoDocuments {
					_, err = tools.Database.Collection("medicine_other_key").UpdateOne(nil, bson.M{"_id": medicineOldOtherKey.ID}, bson.M{"$set": bson.M{"batch_number": batchInfo.UpdateBatch, "expiration_date": batchInfo.UpdateExpirationDate}})
					if err != nil {
						return histories, errors.WithStack(err)
					}
				}
				if medicineOtherKey.ID != primitive.NilObjectID {
					histories = append(histories, models.History{
						Key:  "history.medicine.updateOtherBatch",
						OID:  medicineOtherKey.ID,
						Data: bson.M{"batchNumber": batchInfo.UpdateBatch, "expirationDate": batchInfo.UpdateExpirationDate, "count": historyCount, "status": batchInfo.Status},
						Time: time.Duration(time.Now().Unix()),
						UID:  user.ID,
						User: user.Name,
					})
				}
			} else {
				om := models.FreezeOtherMedicines{
					Name:       oldHistoryKey.Name,
					ExpireDate: batchInfo.UpdateExpirationDate,
					Batch:      batchInfo.UpdateBatch,
					Count:      int(count),
				}
				//轨迹 查询otherKey
				medicineOtherKey, _ := s.getOtherMedicineKey(ctx, otherKeyOrder, om)
				histories = append(histories, models.History{
					Key:  "history.medicine.updateOtherBatch",
					OID:  medicineOtherKey.ID,
					Data: bson.M{"batchNumber": batchInfo.UpdateBatch, "expirationDate": batchInfo.UpdateExpirationDate, "count": historyCount, "status": batchInfo.Status},
					Time: time.Duration(time.Now().Unix()),
					UID:  user.ID,
					User: user.Name,
				})
			}

			histories = append(histories, models.History{
				Key:  "history.medicine.updateOtherBatch",
				OID:  medicineOldOtherKey.ID,
				Data: bson.M{"batchNumber": batchInfo.UpdateBatch, "expirationDate": batchInfo.UpdateExpirationDate, "count": historyCount, "status": batchInfo.Status},
				Time: time.Duration(time.Now().Unix()),
				UID:  user.ID,
				User: user.Name,
			})
		}
	}
	return histories, nil
}

func (s *MedicineService) FreezeMedicines(ctx *gin.Context, updateMedicines models.UpdateSiteMedicines) error {
	customerOID := updateMedicines.CustomerID
	projectOID := updateMedicines.ProjectID
	envOID := updateMedicines.EnvironmentID
	//cohortOID := updateMedicines.CohortID
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		//处理一下隔离原因
		if len(updateMedicines.Remark) > 0 {
			updateMedicines.Reason = fmt.Sprintf("%s %s", updateMedicines.Reason, updateMedicines.Remark)
		}

		//判断包装配置是否打开
		isOpenPackage, _, packageDrugNames, _, _, _ := tools.IsOpenPackage(envOID)
		//先判断状态是否已经发生更新
		queryFilter := bson.M{"_id": bson.M{"$in": updateMedicines.MedicineIds}, "status": bson.M{"$nin": bson.A{1, 14}}}
		count, err := tools.Database.Collection("medicine").CountDocuments(nil, queryFilter)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if count > 0 {
			return nil, tools.BuildServerError(ctx, "update_medicine_status_error")
		}

		//创建隔离记录
		freezeNumber, err := s.getFreezeNumber(ctx)
		if err != nil {
			return primitive.NilObjectID, errors.WithStack(err)
		}
		// 邮件
		state, err := tools.MailCustomContentState(envOID, "medicine.freeze.title")
		if err != nil {
			return primitive.NilObjectID, errors.WithStack(err)
		}

		//按照研究产品位置隔离
		var groupMedicineData []map[string]interface{}
		//判断药物列表是否有数据
		groupPipeline := mongo.Pipeline{
			{{Key: "$match", Value: bson.M{"_id": bson.M{"$in": updateMedicines.MedicineIds}}}},
			{{Key: "$group", Value: bson.M{"_id": "$site_id", "ids": bson.M{"$push": "$_id"}}}},
		}
		if updateMedicines.InstituteType == 2 {
			groupPipeline = mongo.Pipeline{
				{{Key: "$match", Value: bson.M{"_id": bson.M{"$in": updateMedicines.MedicineIds}}}},
				{{Key: "$group", Value: bson.M{"_id": "$storehouse_id", "ids": bson.M{"$push": "$_id"}}}},
			}
		}
		cursor, err := tools.Database.Collection("medicine").Aggregate(nil, groupPipeline)
		err = cursor.All(ctx, &groupMedicineData)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var mails []models.Mail
		var histories []models.History
		for groupIndex, group := range groupMedicineData {
			freezeNumberInt, _ := strconv.Atoi(freezeNumber)
			freezeNumber = strconv.Itoa(freezeNumberInt + groupIndex)
			instituteId := group["_id"].(primitive.ObjectID)
			medicineIds := group["ids"].(primitive.A)
			var ids []primitive.ObjectID
			for _, id := range medicineIds {
				ids = append(ids, id.(primitive.ObjectID))
			}
			// 查询已冻结的药物
			var medicines []models.Medicine
			medicineCursor, medicineErr := tools.Database.Collection("medicine").Find(sctx, bson.M{"_id": bson.M{"$in": ids}, "status": 14})
			if medicineErr != nil {
				return nil, errors.WithStack(medicineErr)
			}
			if err := medicineCursor.All(sctx, &medicines); err != nil {
				return nil, errors.WithStack(err)
			}
			// 冻结药物ids
			var frozenMedicineIds []primitive.ObjectID

			if len(medicines) > 0 {
				for _, medicine := range medicines {
					frozenMedicineIds = append(frozenMedicineIds, medicine.ID)
				}
			}

			filter := bson.M{"_id": bson.M{"$in": ids}}
			update := bson.M{
				"$set": bson.M{
					"status": updateMedicines.Status,
				},
			}

			if _, err := tools.Database.Collection("medicine").UpdateMany(sctx, filter, update); err != nil {
				return nil, errors.WithStack(err)
			}

			//查询研究产品信息
			var data []models.Medicine
			opts := &options.FindOptions{
				Sort: bson.D{{"number", 1}},
			}
			cursor, err := tools.Database.Collection("medicine").Find(sctx, filter, opts)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if err := cursor.All(sctx, &data); err != nil {
				return nil, errors.WithStack(err)
			}

			//每种药物的包装方式
			packageMethods := make(map[string]bool)
			if isOpenPackage && len(data) > 0 {
				//判断map集合是否已经有 药物，没有就放到集合中
				for _, medicine := range data {
					packageMethod := false
					packageNumber, isPackage := packageDrugNames[medicine.Name]
					if packageNumber > 0 && isPackage {
						packageMethod = true
					}
					_, ok := packageMethods[medicine.Name]
					if !ok {
						packageMethods[medicine.Name] = packageMethod
					}
				}
			}

			if err != nil {
				return nil, errors.WithStack(err)
			}
			user, err := tools.Me(ctx)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			medicineFreezeOID := primitive.NewObjectID()
			medicineFreeze := models.MedicineFreeze{
				ID:                medicineFreezeOID,
				CustomerID:        customerOID,
				ProjectID:         projectOID,
				EnvironmentID:     envOID,
				Number:            freezeNumber,
				InstituteType:     updateMedicines.InstituteType,
				InstituteID:       instituteId,
				Reason:            updateMedicines.Reason,
				Medicines:         ids,
				FrozenMedicineIds: frozenMedicineIds,
				History:           ids,
				Meta: models.Meta{
					CreatedAt: time.Duration(time.Now().Unix()),
					CreatedBy: user.ID,
				},
			}

			var medicinesPackage []models.MedicinePackage
			if isOpenPackage && updateMedicines.InstituteType == 2 { //包装配置打开，并且是仓库单品隔离。中心隔离即使也是包装，也支持单品隔离
				for key, value := range packageMethods {
					medicinesPackage = append(medicinesPackage, models.MedicinePackage{
						Name:          key,
						PackageMethod: value,
						PackageNumber: packageDrugNames[key],
					})
				}
				medicineFreeze.MedicinesPackage = medicinesPackage
			}

			if _, err := tools.Database.Collection("medicine_freeze").InsertOne(sctx, medicineFreeze); err != nil {
				return nil, errors.WithStack(err)
			}

			instituteName := ""
			instituteNameEn := ""
			instituteNumber := ""
			instituteType := false
			if len(data) > 0 {
				// siteID := data[0].SiteID
				// storehouseID := data[0].StorehouseID
				//instituteID := updateMedicines.InstituteID
				document := "project_site"
				pipeline := mongo.Pipeline{}
				if updateMedicines.InstituteType == 1 {
					pipeline = mongo.Pipeline{
						{{Key: "$match", Value: bson.M{"_id": instituteId}}},
						{{Key: "$project", Value: bson.M{
							"_id":    0,
							"id":     "$_id",
							"number": "$number",
							"name":   models.ProjectSiteNameBsonLang("zh"),
							"nameEn": models.ProjectSiteNameBsonLang("en"),
						}}},
					}
					instituteType = true
				} else {
					pipeline = mongo.Pipeline{
						{{Key: "$match", Value: bson.M{"_id": instituteId}}},
						{{Key: "$lookup", Value: bson.M{
							"from":         "storehouse",
							"localField":   "storehouse_id",
							"foreignField": "_id",
							"as":           "detail",
						}}},
						{{Key: "$unwind", Value: "$detail"}},
						{{Key: "$project", Value: bson.M{
							"_id":    0,
							"id":     "$_id",
							"number": "$detail.number",
							"name":   "$detail.name",
							"nameEn": "$detail.name",
						}}},
					}
					document = "project_storehouse"
				}
				var instituteData []map[string]interface{}
				cursor, err := tools.Database.Collection(document).Aggregate(nil, pipeline)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = cursor.All(nil, &instituteData)
				if err != nil {
					return nil, errors.WithStack(err)

				}

				if len(instituteData) > 0 {
					instituteName = instituteData[0]["name"].(string)
					instituteNameEn = instituteData[0]["nameEn"].(string)
					instituteNumber = instituteData[0]["number"].(string)
				}
			}

			instituteInfo := instituteName
			instituteInfoEn := instituteNameEn
			subjectInstituteInfo := instituteName
			subjectInstituteInfoEn := instituteNameEn
			if instituteType {
				instituteInfo = fmt.Sprintf("%s-%s", instituteNumber, instituteName)
				instituteInfoEn = fmt.Sprintf("%s-%s", instituteNumber, instituteNameEn)
				subjectInstituteInfo = fmt.Sprintf("%s %s", instituteNumber, instituteName)
				subjectInstituteInfoEn = fmt.Sprintf("%s %s", instituteNumber, instituteNameEn)
			}

			key := "history.medicine.new-freeze"
			var frozenMedicinesNumber []string
			packageIsOpen, _, _, _, _, _ := tools.IsOpenPackage(envOID)
			if packageIsOpen {
				key = "history.medicine.new-freeze"
			}
			now := time.Duration(time.Now().Unix())
			for _, medicine := range data {
				historyNumber := medicine.Number
				packageNumber := "-"
				if packageIsOpen {
					if medicine.PackageNumber != "" {
						packageNumber = medicine.PackageNumber
					}
					historyNumber = fmt.Sprintf("%s/%s", medicine.Number, packageNumber)
				}
				frozenMedicinesNumber = append(frozenMedicinesNumber, historyNumber)
				historyData := bson.M{"freezeNumber": freezeNumber, "position": instituteInfo, "positionEn": instituteInfoEn, "reason": updateMedicines.Reason, "packNumber": medicine.Number, "packageNumber": packageNumber}
				err = s.FreezeMedicineHistoryData(ctx, medicine.ID, key, &histories, user, user.Name, now, historyData, packageIsOpen)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				// history := models.History{
				// 	Key:  key,
				// 	OID:  medicine.ID,
				// 	Data: historyData,
				// 	Time: now,
				// 	UID:  user.ID,
				// 	User: user.Name,
				// }
				//histories = append(histories, history)
			}

			freezeKey := "history.medicine.drugFreeze-new.freeze"
			if packageIsOpen {
				freezeKey = "history.medicine.drugFreeze-new.freeze"
			}
			//排序 IRT-5620 轨迹中，研究产品编号排序问题
			sort.Strings(frozenMedicinesNumber)
			// history := models.History{
			// 	Key:  freezeKey,
			// 	OID:  medicineFreezeOID,
			// 	Data: bson.M{"freezeNumber": freezeNumber, "freezeReason": updateMedicines.Reason, "medicines": frozenMedicinesNumber},
			// 	Time: time.Duration(time.Now().Unix()),
			// 	UID:  user.ID,
			// 	User: user.Name,
			// }
			// histories = append(histories, history)
			freezeHistory := bson.M{"freezeNumber": freezeNumber, "position": instituteInfo, "positionEn": instituteInfoEn, "reason": updateMedicines.Reason, "medicines": frozenMedicinesNumber}
			err = s.FreezeHistoryData(ctx, medicineFreezeOID, freezeKey, nil, &histories, user, user.Name, now, freezeHistory, packageIsOpen)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if !state {
				continue
			}
			var projectInfo []map[string]interface{}
			pipeline := mongo.Pipeline{
				{{Key: "$match", Value: bson.M{"_id": projectOID, "customer_id": customerOID}}},
				{{Key: "$unwind", Value: "$envs"}},
				{{Key: "$match", Value: bson.M{"envs.id": envOID}}},
				{
					{
						Key: "$project",
						Value: bson.M{
							"_id":      0,
							"number":   "$info.number",
							"name":     "$info.name",
							"env":      "$envs.name",
							"timeZone": "$info.timeZoneStr",
						},
					},
				},
			}
			cursor, err = tools.Database.Collection("project").Aggregate(nil, pipeline)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &projectInfo)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			var timeZone float64
			if projectInfo[0]["timeZone"] != nil {
				//timeZone = projectInfo[0]["timeZone"].(int32)
				timeZone, _ = strconv.ParseFloat(projectInfo[0]["timeZone"].(string), 64)
			} else {
				timeZone = float64(8)
			}
			hours := time.Duration(timeZone)
			minutes := time.Duration((timeZone - float64(hours)) * 60)
			duration := hours*time.Hour + minutes*time.Minute

			medicineItems := []map[string]interface{}{}
			// 邮件通知
			if len(ids) > 0 {
				pipeline := mongo.Pipeline{
					{{Key: "$match", Value: bson.M{"_id": bson.M{"$in": ids}}}},
					{{Key: "$group", Value: bson.M{
						"_id":    bson.M{"batch_number": "$batch_number", "expiration_date": "$expiration_date", "package_number": bson.M{"$ifNull": bson.A{"$package_number", ""}}},
						"number": bson.M{"$push": "$number"},
					}}},
					{{Key: "$project", Value: bson.M{
						"batch_number":    "$_id.batch_number",
						"expiration_date": "$_id.expiration_date",
						"package_number":  "$_id.package_number",
						"number":          1,
					}}},
					{{Key: "$sort", Value: bson.D{{"expiration_date", 1}, {Key: "batch_number", Value: 1}}}},
					{{Key: "$group", Value: bson.M{
						"_id": bson.M{"batch_number": "$batch_number", "expiration_date": "$expiration_date"},
						"detail": bson.M{"$push": bson.M{
							"number":         "$number",
							"package_number": "$package_number",
						}},
					}}},
				}

				cursor, err = tools.Database.Collection("medicine").Aggregate(nil, pipeline)
				var medicineDatas []map[string]interface{}

				err := cursor.All(nil, &medicineDatas)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				for _, data := range medicineDatas {
					var details []map[string]interface{}
					dataId := data["_id"].(map[string]interface{})
					for _, detail := range data["detail"].(primitive.A) {
						medicine := detail.(map[string]interface{})
						var medicines []string
						for _, item := range medicine["number"].(primitive.A) {
							medicines = append(medicines, item.(string))
						}
						sort.Sort(sort.StringSlice(medicines))
						details = append(details, map[string]interface{}{
							"number":        strings.Join(medicines, ","),
							"count":         len(medicines),
							"packageNumber": medicine["package_number"],
						})
					}
					resultData := map[string]interface{}{
						"batch":         dataId["batch_number"],
						"expiryDate":    dataId["expiration_date"],
						"rowsSpan":      len(data["detail"].(primitive.A)),
						"packageIsOpen": isOpenPackage,
						"details":       details,
					}
					medicineItems = append(medicineItems, resultData)
				}
			}

			contentData := bson.M{
				"projectNumber":   projectInfo[0]["number"],
				"projectName":     projectInfo[0]["name"],
				"envName":         projectInfo[0]["env"],
				"instituteNumber": instituteNumber,
				"instituteName":   instituteName,
				"instituteNameEn": instituteNameEn,
				"instituteType":   instituteType,
				"freezeNumber":    freezeNumber,
				"reason":          updateMedicines.Reason,
				"freezeDate":      time.Now().UTC().Add(duration).Format("2006-01-02"),
				"results":         medicineItems,
				"packageIsOpen":   isOpenPackage,
			}

			subjectData := bson.M{
				"projectNumber":   projectInfo[0]["number"],
				"projectName":     projectInfo[0]["name"],
				"envName":         projectInfo[0]["env"],
				"instituteInfo":   subjectInstituteInfo,
				"instituteInfoEn": subjectInstituteInfoEn,
				"freezeNumber":    freezeNumber,
			}

			// 获取用户的邮箱
			userMail, err := tools.GetRoleUsersMail(projectOID, envOID, "notice.medicine.isolation", instituteId)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			if len(userMail) > 0 {
				mailBodyContet, err := tools.MailBodyContent(nil, envOID, "notice.medicine.isolation")
				for key, v := range mailBodyContet {
					contentData[key] = v
				}
				if err != nil {
					return primitive.NilObjectID, errors.WithStack(err)
				}
				var noticeConfig models.NoticeConfig
				err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": envOID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
				if err != nil && err != mongo.ErrNoDocuments {
					return mails, errors.WithStack(err)
				}
				langList := make([]string, 0)
				html := "medicine_freeze_new_zh_en.html"
				if noticeConfig.Manual != 0 {
					if noticeConfig.Manual == 1 {
						langList = append(langList, "zh")
						html = "medicine_freeze_new_zh.html"
					} else if noticeConfig.Manual == 2 {
						langList = append(langList, "en")
						html = "medicine_freeze_new_en.html"
					} else if noticeConfig.Manual == 3 {
						langList = append(langList, "zh")
						langList = append(langList, "en")
						html = "medicine_freeze_new_zh_en.html"
					}
				} else {
					langList = append(langList, ctx.GetHeader("Accept-Language"))
					if locales.Lang(ctx) == "zh" {
						html = "medicine_freeze_new_zh.html"
					} else if locales.Lang(ctx) == "en" {
						html = "medicine_freeze_new_en.html"
					}
				}

				for _, userEmail := range userMail {
					var toUserMail []string
					toUserMail = append(toUserMail, userEmail)
					mails = append(mails, models.Mail{
						ID:           primitive.NewObjectID(),
						Subject:      "medicine.freeze.title",
						SubjectData:  subjectData,
						ContentData:  contentData,
						To:           toUserMail,
						Lang:         ctx.GetHeader("Accept-Language"),
						LangList:     langList,
						Status:       0,
						CreatedTime:  time.Duration(time.Now().Unix()),
						ExpectedTime: time.Duration(time.Now().Unix()),
						SendTime:     time.Duration(time.Now().Unix()),
						HTML:         html,
					})
				}
			}

		}
		ctx.Set("HISTORY", histories)
		ctx.Set("MAIL", mails)
		if len(mails) > 0 {
			var envs []models.MailEnv
			for _, m := range mails {
				envs = append(envs, models.MailEnv{
					ID:         primitive.NewObjectID(),
					MailID:     m.ID,
					CustomerID: customerOID,
					ProjectID:  projectOID,
					EnvID:      envOID,
					//	CohortID:   cohortOID,
				})
			}
			ctx.Set("MAIL-ENV", envs)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	// 实时订单核查
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}
	if project.ProjectInfo.OrderCheck == 2 {
		err := task.AlarmMedicineNew(2, envOID)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

// 丢失/作废未编码药物
func (s *MedicineService) UpdateOtherMedicines(ctx *gin.Context, otherMedicineFreeze models.OtherMedicineFreeze) error {
	projectOID := otherMedicineFreeze.ProjectID
	envOID := otherMedicineFreeze.EnvironmentID
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		user, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		//创建轨迹
		var histories []models.History
		//key := "history.medicine.otherMedicineLost"
		key := "history.medicine.new-lost.lost"
		now := time.Duration(time.Now().Unix())
		//判断包装配置是否打开
		_, _, packageDrugNames, _, _, _ := tools.IsOpenPackage(envOID)
		instituteInfoMap := make(map[primitive.ObjectID]map[string]interface{})
		for _, otherMedicine := range otherMedicineFreeze.OtherMedicines {
			if otherMedicine.Salt != "" && otherMedicine.SaltName != "" {
				name := tools.Decrypt(otherMedicine.SaltName, otherMedicine.Salt)
				otherMedicine.Name = name
			}
			otherMedicineIds := make([]primitive.ObjectID, 0)
			var otherMedicineData []map[string]interface{}
			otherFilter := bson.M{"name": otherMedicine.Name}
			if otherMedicine.Batch != "" && otherMedicine.Batch != "-" {
				otherFilter["batch_number"] = otherMedicine.Batch
			}
			if otherMedicine.ExpireDate != "" && otherMedicine.ExpireDate != "-" {
				otherFilter["expiration_date"] = otherMedicine.ExpireDate
			}
			if otherMedicineFreeze.InstituteType == 1 {
				otherFilter["site_id"] = otherMedicine.SiteID
			} else {
				otherFilter["storehouse_id"] = otherMedicine.StorehouseID
			}
			var instituteId primitive.ObjectID
			if otherMedicineFreeze.InstituteType == 1 {
				instituteId = otherMedicine.SiteID
			} else {
				instituteId = otherMedicine.StorehouseID
			}
			instituteInfo, exist := instituteInfoMap[instituteId]
			if !exist {
				instituteInfo, err = getInstituteName(ctx, otherMedicineFreeze.InstituteType, instituteId)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				instituteInfoMap[instituteId] = instituteInfo
			}
			if otherMedicine.ExpiredCount != 0 {
				otherFilter["status"] = 7
			} else if otherMedicine.Count != 0 {
				otherFilter["status"] = 1
			}
			if otherMedicine.IsOpenPackage {
				if otherMedicineFreeze.InstituteType == 2 {
					groupPipeline := mongo.Pipeline{
						{{Key: "$match", Value: otherFilter}},
						{{Key: "$group", Value: bson.M{"_id": bson.M{"packageNumber": "$package_number"}, "otherIds": bson.M{"$push": "$_id"}}}},
						{{Key: "$limit", Value: otherMedicine.LostCount}},
					}
					cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, groupPipeline)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					err = cursor.All(nil, &otherMedicineData)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					for _, otherMedicine := range otherMedicineData {
						otherIds := otherMedicine["otherIds"].(primitive.A)
						for _, id := range otherIds {
							otherMedicineIds = append(otherMedicineIds, id.(primitive.ObjectID))
						}
					}
				} else {
					groupPipeline := mongo.Pipeline{
						{{Key: "$match", Value: otherFilter}},
						{{Key: "$group", Value: bson.M{"_id": bson.M{"packageNumber": "$package_number"}, "otherIds": bson.M{"$push": "$_id"}}}},
						{{Key: "$project", Value: bson.M{"_id": 1, "otherIds": 1, "arrayLength": bson.M{"$size": "$otherIds"}}}},
						{{Key: "$unwind", Value: "$otherIds"}},
						{{Key: "$sort", Value: bson.D{{"arrayLength", 1}, {"_id.packageNumber", 1}}}},
						{{Key: "$limit", Value: otherMedicine.LostCount}},
					}
					cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, groupPipeline)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					err = cursor.All(nil, &otherMedicineData)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					for _, otherMedicine := range otherMedicineData {
						otherMedicineIds = append(otherMedicineIds, otherMedicine["otherIds"].(primitive.ObjectID))
					}
				}
			} else {
				pipepine := mongo.Pipeline{
					{{Key: "$match", Value: otherFilter}},
					{{Key: "$limit", Value: otherMedicine.LostCount}},
					{{Key: "$project", Value: bson.M{
						"_id": 1,
					}}},
				}
				cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, pipepine)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = cursor.All(nil, &otherMedicineData)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				for _, otherMedicine := range otherMedicineData {
					otherMedicineIds = append(otherMedicineIds, otherMedicine["_id"].(primitive.ObjectID))
				}
			}
			update := bson.M{}
			if otherMedicineFreeze.Status == 6 { //丢失作废
				update = bson.M{
					"$set": bson.M{
						"status": 6,
					},
				}
			}

			if _, err := tools.Database.Collection("medicine_others").UpdateMany(sctx, bson.M{"_id": bson.M{"$in": otherMedicineIds}}, update); err != nil {
				return nil, errors.WithStack(err)
			}

			medicineOtherKey, _ := s.getOtherMedicineKey(ctx, otherMedicineFreeze, otherMedicine)
			historyBatch := otherMedicine.Batch
			historyExpireDate := otherMedicine.ExpireDate
			freezeCount := fmt.Sprintf("%v", otherMedicine.LostCount)
			if historyBatch == "" {
				historyBatch = "-"
			}
			if historyExpireDate == "" {
				historyBatch = "-"
			}
			packageNumber := packageDrugNames[otherMedicine.Name]
			if otherMedicine.IsOpenPackage && otherMedicineFreeze.InstituteType == 2 {
				freezeCount = fmt.Sprintf("%v(%v)", otherMedicine.LostCount*packageNumber, otherMedicine.LostCount)
			}

			// history := models.History{
			// 	Key:  key,
			// 	OID:  medicineOtherKey.ID,
			// 	Data: bson.M{"freezeReason": otherMedicineFreeze.Reason, "name": otherMedicine.Name, "batch": historyBatch, "expireDate": historyExpireDate, "freezeCount": freezeCount},
			// 	Time: time.Duration(time.Now().Unix()),
			// 	UID:  user.ID,
			// 	User: user.Name,
			// }
			// histories = append(histories, history)
			historyData := bson.M{"position": instituteInfo["instituteName"], "positionEn": instituteInfo["instituteNameEn"], "reason": otherMedicineFreeze.Reason, "name": otherMedicine.Name, "batch": historyBatch, "expireDate": historyExpireDate, "freezeCount": freezeCount}
			err = s.FreezeMedicineHistoryData(ctx, medicineOtherKey.ID, key, &histories, user, user.Name, now, historyData, false)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		ctx.Set("HISTORY", histories)
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	// 实时订单核查
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}
	if project.ProjectInfo.OrderCheck == 2 {
		err := task.AlarmMedicineNew(2, envOID)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

func (s *MedicineService) getOtherMedicineKey(ctx *gin.Context, otherMedicine models.OtherMedicineFreeze, otherMedicineInfo models.FreezeOtherMedicines) (models.MedicineOtherKey, error) {
	orderType := otherMedicine.InstituteType
	if otherMedicineInfo.ExpireDate == "-" {
		otherMedicineInfo.ExpireDate = ""
	}
	if otherMedicineInfo.Batch == "-" {
		otherMedicineInfo.Batch = ""
	}
	//查询otherKey
	var medicineOtherKey models.MedicineOtherKey
	otherKeyMatch := bson.M{"customer_id": otherMedicine.CustomerID, "project_id": otherMedicine.ProjectID,
		"env_id": otherMedicine.EnvironmentID, "name": otherMedicineInfo.Name, "expiration_date": otherMedicineInfo.ExpireDate, "batch_number": otherMedicineInfo.Batch}
	if orderType == 2 { //仓库
		otherKeyMatch["storehouse_id"] = otherMedicineInfo.StorehouseID
	} else {
		otherKeyMatch["site_id"] = otherMedicineInfo.SiteID
	}
	err := tools.Database.Collection("medicine_other_key").FindOne(ctx, otherKeyMatch).Decode(&medicineOtherKey)
	if err == mongo.ErrNoDocuments {
		//轨迹key表数据
		medicineOtherKey.ID = primitive.NewObjectID()
		medicineOtherKey.ProjectID = otherMedicine.ProjectID
		medicineOtherKey.EnvironmentID = otherMedicine.EnvironmentID
		medicineOtherKey.CustomerID = otherMedicine.CustomerID
		if orderType == 2 {
			medicineOtherKey.StorehouseID = otherMedicineInfo.StorehouseID
		} else {
			medicineOtherKey.SiteID = otherMedicineInfo.SiteID
		}
		medicineOtherKey.Name = otherMedicineInfo.Name
		medicineOtherKey.Batch = otherMedicineInfo.Batch
		medicineOtherKey.ExpireDate = otherMedicineInfo.ExpireDate
		//medicineOtherKey.Spec = otherMedicine.Spec
		_, err = tools.Database.Collection("medicine_other_key").InsertOne(nil, medicineOtherKey)
		if err != nil {
			return medicineOtherKey, errors.WithStack(err)
		}
	}
	if err != nil && err != mongo.ErrNoDocuments {
		return medicineOtherKey, errors.WithStack(err)
	}
	return medicineOtherKey, nil

}

func (s *MedicineService) FreezeOtherMedicines(ctx *gin.Context, otherMedicineFreeze models.OtherMedicineFreeze) error {
	customerOID := otherMedicineFreeze.CustomerID
	projectOID := otherMedicineFreeze.ProjectID
	envOID := otherMedicineFreeze.EnvironmentID
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		//处理一下隔离原因
		if len(otherMedicineFreeze.Remark) > 0 {
			otherMedicineFreeze.Reason = fmt.Sprintf("%s %s", otherMedicineFreeze.Reason, otherMedicineFreeze.Remark)
		}

		//判断包装配置是否打开
		isOpenPackage, _, packageDrugNames, _, _, _ := tools.IsOpenPackage(envOID)

		//隔离记录号
		freezeNumber, err := s.getFreezeNumber(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		user, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		//创建轨迹
		now := time.Duration(time.Now().Unix())
		var histories []models.History
		var mails []models.Mail
		groups := make(map[primitive.ObjectID][]models.FreezeOtherMedicines)
		if otherMedicineFreeze.InstituteType == 1 {
			for _, otherMedicine := range otherMedicineFreeze.OtherMedicines {
				groups[otherMedicine.SiteID] = append(groups[otherMedicine.SiteID], otherMedicine)
			}
		} else {
			for _, otherMedicine := range otherMedicineFreeze.OtherMedicines {
				groups[otherMedicine.StorehouseID] = append(groups[otherMedicine.StorehouseID], otherMedicine)
			}
		}

		groupIndex := 0
		for instituteID, otherMedicines := range groups {
			//otherMedicineFreeze.InstituteID = instituteID
			freezeNumberInt, _ := strconv.Atoi(freezeNumber)
			freezeNumber = strconv.Itoa(freezeNumberInt + groupIndex)
			groupIndex++
			instituteName := ""
			instituteNameEn := ""
			instituteNumber := ""

			instituteType := false
			var instituteData []map[string]interface{}
			matchID := bson.M{"_id": instituteID}

			if otherMedicineFreeze.InstituteType == 1 {
				instituteType = true
				projectMongo := bson.M{
					"_id":    0,
					"id":     "$_id",
					"number": "$number",
					"name":   models.ProjectSiteNameBsonLang("zh"),
					"nameEn": models.ProjectSiteNameBsonLang("en"),
				}
				pipeline := mongo.Pipeline{
					{{Key: "$match", Value: matchID}},
					{{Key: "$project", Value: projectMongo}},
				}
				cursor, err := tools.Database.Collection("project_site").Aggregate(nil, pipeline)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = cursor.All(nil, &instituteData)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			} else {
				lookup := bson.M{
					"from":         "storehouse",
					"localField":   "storehouse_id",
					"foreignField": "_id",
					"as":           "detail",
				}
				projectMongo := bson.M{
					"_id":    0,
					"id":     "$_id",
					"number": "$detail.number",
					"name":   "$detail.name",
					"nameEn": "$detail.name",
				}
				storehousePipeline := mongo.Pipeline{
					{{Key: "$match", Value: matchID}},
					{{Key: "$lookup", Value: lookup}},
					{{Key: "$unwind", Value: "$detail"}},
					{{Key: "$project", Value: projectMongo}},
				}
				storehouseCursor, err := tools.Database.Collection("project_storehouse").Aggregate(nil, storehousePipeline)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = storehouseCursor.All(nil, &instituteData)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}

			if len(instituteData) > 0 {
				instituteName = instituteData[0]["name"].(string)
				instituteNameEn = instituteData[0]["nameEn"].(string)
				instituteNumber = instituteData[0]["number"].(string)
			}

			instituteInfo := instituteName
			instituteInfoEn := instituteNameEn
			if instituteType {
				instituteInfo = instituteNumber + "—" + instituteName
				instituteInfoEn = instituteNumber + "—" + instituteNameEn
			}

			var freezeOtherMedicines []models.FreezeOtherMedicine
			otherMedicineItems := []map[string]interface{}{}
			blindOtherMedicineItems := []map[string]interface{}{}

			packageMethods := make(map[string]bool)
			otherMedicineIds := make([]primitive.ObjectID, 0)
			for _, otherMedicine := range otherMedicines {
				if otherMedicine.Salt != "" && otherMedicine.SaltName != "" {
					name := tools.Decrypt(otherMedicine.SaltName, otherMedicine.Salt)
					otherMedicine.Name = name
				}
				packageMethod := false
				packageNumber, isPackage := packageDrugNames[otherMedicine.Name]
				if packageNumber > 0 && isPackage {
					packageMethod = true
				}
				_, ok := packageMethods[otherMedicine.Name]
				if !ok {
					packageMethods[otherMedicine.Name] = packageMethod
				}
				var otherMedicineData []map[string]interface{}
				otherFilter := bson.M{"status": 1, "name": otherMedicine.Name}
				if otherMedicine.ExpireDate != "" && otherMedicine.ExpireDate != "-" {
					otherFilter["expiration_date"] = otherMedicine.ExpireDate
				} else {
					otherFilter["$or"] = bson.A{
						bson.M{"expiration_date": ""},
						bson.M{"expiration_date": nil},
					}
				}
				if otherMedicine.Batch != "" && otherMedicine.Batch != "-" {
					otherFilter["batch_number"] = otherMedicine.Batch
				} else {
					otherFilter["$or"] = bson.A{
						bson.M{"batch_number": ""},
						bson.M{"expiration_date": nil},
					}
				}
				if otherMedicineFreeze.InstituteType == 1 {
					otherFilter["site_id"] = instituteID
				} else if otherMedicineFreeze.InstituteType == 2 {
					otherFilter["storehouse_id"] = instituteID
				}
				if otherMedicine.IsOpenPackage {
					if otherMedicineFreeze.InstituteType == 2 { //仓库
						groupPipeline := mongo.Pipeline{
							{{Key: "$match", Value: otherFilter}},
							{{Key: "$group", Value: bson.M{"_id": bson.M{"packageNumber": "$package_number"}, "otherIds": bson.M{"$push": "$_id"}}}},
							{{Key: "$limit", Value: otherMedicine.FreezeCount}},
						}
						cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, groupPipeline)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						err = cursor.All(nil, &otherMedicineData)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						for _, otherMedicine := range otherMedicineData {
							otherIds := otherMedicine["otherIds"].(primitive.A)
							for _, id := range otherIds {
								otherMedicineIds = append(otherMedicineIds, id.(primitive.ObjectID))
							}
						}
					} else { //中心包装的，优先隔离拆包的
						groupPipeline := mongo.Pipeline{
							{{Key: "$match", Value: otherFilter}},
							{{Key: "$group", Value: bson.M{"_id": bson.M{"packageNumber": "$package_number"}, "otherIds": bson.M{"$push": "$_id"}}}},
							{{Key: "$project", Value: bson.M{"_id": 1, "otherIds": 1, "arrayLength": bson.M{"$size": "$otherIds"}}}},
							{{Key: "$unwind", Value: "$otherIds"}},
							{{Key: "$sort", Value: bson.D{{"arrayLength", 1}, {"_id.packageNumber", 1}}}},
							{{Key: "$limit", Value: otherMedicine.FreezeCount}},
						}
						cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, groupPipeline)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						err = cursor.All(nil, &otherMedicineData)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						for _, otherMedicine := range otherMedicineData {
							otherMedicineIds = append(otherMedicineIds, otherMedicine["otherIds"].(primitive.ObjectID))
						}
					}
				} else {
					pipepine := mongo.Pipeline{
						{{Key: "$match", Value: otherFilter}},
						{{Key: "$limit", Value: otherMedicine.FreezeCount}},
						{{Key: "$project", Value: bson.M{
							"_id": 1,
						}}},
					}
					cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, pipepine)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					err = cursor.All(nil, &otherMedicineData)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					for _, otherMedicine := range otherMedicineData {
						otherMedicineIds = append(otherMedicineIds, otherMedicine["_id"].(primitive.ObjectID))
					}
				}

				//轨迹
				otherMedicinekey, _ := s.getOtherMedicineKey(ctx, otherMedicineFreeze, otherMedicine)
				historyBatch := otherMedicine.Batch
				historyExpireDate := otherMedicine.ExpireDate
				freezeCount := fmt.Sprintf("%v", otherMedicine.FreezeCount)
				if historyBatch == "" {
					historyBatch = "-"
				}
				if historyExpireDate == "" {
					historyBatch = "-"
				}
				if otherMedicine.IsOpenPackage && otherMedicineFreeze.InstituteType == 2 {
					freezeCount = fmt.Sprintf("%v(%v)", otherMedicine.FreezeCount*packageNumber, otherMedicine.FreezeCount)
				}

				key := "history.medicine.new-freeze"

				historyData := bson.M{"freezeNumber": freezeNumber, "position": instituteInfo, "positionEn": instituteInfoEn, "reason": otherMedicineFreeze.Reason, "name": otherMedicine.Name, "batch": historyBatch, "expireDate": historyExpireDate, "freezeCount": freezeCount}
				// history := models.History{
				// 	Key:  "history.medicine.otherFreeze",
				// 	OID:  otherMedicinekey.ID,
				// 	Data: bson.M{"freezeNumber": freezeNumber, "freezeReason": otherMedicineFreeze.Reason, "name": otherMedicine.Name, "batch": historyBatch, "expireDate": historyExpireDate, "freezeCount": freezeCount},
				// 	Time: time.Duration(time.Now().Unix()),
				// 	UID:  user.ID,
				// 	User: user.Name,
				// }
				// histories = append(histories, history)
				err = s.FreezeMedicineHistoryData(ctx, otherMedicinekey.ID, key, &histories, user, user.Name, now, historyData, false)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				//隔离管理
				freezeOtherMedicines = append(freezeOtherMedicines, models.FreezeOtherMedicine{
					Name: otherMedicine.Name,
					MedicineOtherInfo: models.MedicineOtherInfo{
						QuarantinedCount: otherMedicine.FreezeCount,
					},
					ExpireDate: otherMedicine.ExpireDate,
					Batch:      otherMedicine.Batch,
				})

				// medicineItem := map[string]interface{}{}
				// medicineItem["batchNumber"] = medicineOtherInstitute.Info.Batch
				// medicineItem["number"] = otherMedicine.Name
				// medicineItem["expiryDate"] = medicineOtherInstitute.Info.ExpireDate
				// medicineItem["count"] = otherMedicine.FreezeCount
				count := otherMedicine.FreezeCount
				singleCount := count
				if packageMethod && otherMedicineFreeze.InstituteType == 2 {
					singleCount = count * packageNumber
				}
				//邮件展示的时候，如果是中心隔离，不展示包装数量，设置packageMethod为false
				if otherMedicineFreeze.InstituteType == 1 {
					packageMethod = false
				}
				var details []map[string]interface{}
				details = append(details, map[string]interface{}{
					"number":        otherMedicine.Name,
					"count":         singleCount,
					"packageMethod": packageMethod,
					"packageCount":  count,
					"packageNumber": nil,
				})
				resultData := map[string]interface{}{
					"batch":         otherMedicine.Batch,
					"expiryDate":    otherMedicine.ExpireDate,
					"rowsSpan":      1,
					"packageIsOpen": isOpenPackage,
					"details":       details,
				}

				//邮件内容
				otherMedicineItems = append(otherMedicineItems, resultData)

				name := otherMedicine.Name
				isBlindedDrug, _ := tools.IsBlindedDrug(envOID, name)
				if isBlindedDrug {
					name = tools.BlindData
				}

				var blindDetails []map[string]interface{}
				blindDetails = append(blindDetails, map[string]interface{}{
					"number":        name,
					"count":         singleCount,
					"packageMethod": packageMethod,
					"packageCount":  count,
					"packageNumber": nil,
				})
				blindResultData := map[string]interface{}{
					"batch":         otherMedicine.Batch,
					"expiryDate":    otherMedicine.ExpireDate,
					"rowsSpan":      1,
					"packageIsOpen": isOpenPackage,
					"details":       details,
				}

				blindOtherMedicineItems = append(blindOtherMedicineItems, blindResultData)

				// if _, err := tools.Database.Collection("medicine_other_institute").UpdateMany(sctx, filter, update); err != nil {
				// 	return nil, errors.WithStack(err)
				// }
			}

			filter := bson.M{"_id": bson.M{"$in": otherMedicineIds}}
			update := bson.M{
				"$set": bson.M{
					"status": otherMedicineFreeze.Status,
				},
			}
			if _, err := tools.Database.Collection("medicine_others").UpdateMany(sctx, filter, update); err != nil {
				return nil, errors.WithStack(err)
			}

			//创建隔离记录
			medicineFreezeOID := primitive.NewObjectID()
			medicineFreeze := models.MedicineFreeze{
				ID:                medicineFreezeOID,
				CustomerID:        customerOID,
				ProjectID:         projectOID,
				EnvironmentID:     envOID,
				Number:            freezeNumber,
				InstituteType:     otherMedicineFreeze.InstituteType,
				InstituteID:       instituteID,
				Reason:            otherMedicineFreeze.Reason,
				OtherMedicinesNew: otherMedicineIds,
				OtherHistoryNew:   otherMedicineIds,
				Meta: models.Meta{
					CreatedAt: time.Duration(time.Now().Unix()),
					CreatedBy: user.ID,
				},
			}

			var medicinesPackage []models.MedicinePackage
			if isOpenPackage { //包装配置打开，并且是仓库单品隔离。中心隔离即使也是包装，也支持单品隔离
				for key, value := range packageMethods {
					medicinesPackage = append(medicinesPackage, models.MedicinePackage{
						Name:          key,
						PackageMethod: value,
						PackageNumber: packageDrugNames[key],
					})
				}
				medicineFreeze.MedicinesPackage = medicinesPackage
			}

			if _, err := tools.Database.Collection("medicine_freeze").InsertOne(sctx, medicineFreeze); err != nil {
				return nil, errors.WithStack(err)
			}
			var frozenMedicinesNumber []string
			for _, otherMedicine := range freezeOtherMedicines {
				historyBatch := otherMedicine.Batch
				historyExpireDate := otherMedicine.ExpireDate
				packageNumber, isPackage := packageDrugNames[otherMedicine.Name]
				isOpenPackage := false
				if isPackage && packageNumber > 0 {
					isOpenPackage = true
				}
				quarantinedCount := fmt.Sprintf("%v", otherMedicine.QuarantinedCount)
				if historyBatch == "" {
					historyBatch = "-"
				}
				if historyExpireDate == "" {
					historyBatch = "-"
				}
				if isOpenPackage && otherMedicineFreeze.InstituteType == 2 {
					quarantinedCount = fmt.Sprintf("%v(%v)", otherMedicine.QuarantinedCount*packageNumber, otherMedicine.QuarantinedCount)
				}
				frozenMedicinesNumber = append(frozenMedicinesNumber, fmt.Sprintf("%s/%s/%s/%s", otherMedicine.Name, historyBatch, historyExpireDate, quarantinedCount))
			}
			// history := models.History{
			// 	Key:  "history.medicine.drugFreeze.otherDrugFreeze",
			// 	OID:  medicineFreezeOID,
			// 	Data: bson.M{"freezeNumber": freezeNumber, "freezeReason": otherMedicineFreeze.Reason, "otherMedicines": frozenMedicinesNumber},
			// 	Time: time.Duration(time.Now().Unix()),
			// 	UID:  user.ID,
			// 	User: user.Name,
			// }
			// histories = append(histories, history)

			historyData := bson.M{"freezeNumber": freezeNumber, "position": instituteInfo, "positionEn": instituteInfoEn, "reason": otherMedicineFreeze.Reason, "otherMedicines": frozenMedicinesNumber}
			err = s.FreezeHistoryData(ctx, medicineFreezeOID, "history.medicine.drugFreeze-new.freeze", nil, &histories, user, user.Name, now, historyData, false)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			// 邮件
			state, err := tools.MailCustomContentState(envOID, "medicine.freeze.title")
			if err != nil {
				return primitive.NilObjectID, errors.WithStack(err)
			}
			if !state {
				continue
			}

			var projectInfo []map[string]interface{}
			projectPipeline := mongo.Pipeline{
				{{Key: "$match", Value: bson.M{"_id": projectOID, "customer_id": customerOID}}},
				{{Key: "$unwind", Value: "$envs"}},
				{{Key: "$match", Value: bson.M{"envs.id": envOID}}},
				{
					{
						Key: "$project",
						Value: bson.M{
							"_id":      0,
							"number":   "$info.number",
							"name":     "$info.name",
							"env":      "$envs.name",
							"timeZone": "$info.timeZoneStr",
						},
					},
				},
			}
			cursor, err := tools.Database.Collection("project").Aggregate(nil, projectPipeline)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &projectInfo)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			var timeZone float64
			if projectInfo[0]["timeZone"] != nil {
				//timeZone = projectInfo[0]["timeZone"].(int32)
				timeZone, _ = strconv.ParseFloat(projectInfo[0]["timeZone"].(string), 64)
			} else {
				timeZone = float64(8)
			}
			hours := time.Duration(timeZone)
			minutes := time.Duration((timeZone - float64(hours)) * 60)
			duration := hours*time.Hour + minutes*time.Minute

			contentData := bson.M{
				"projectNumber":   projectInfo[0]["number"],
				"projectName":     projectInfo[0]["name"],
				"envName":         projectInfo[0]["env"],
				"instituteNumber": instituteNumber,
				"instituteName":   instituteName,
				"instituteNameEn": instituteNameEn,
				"instituteType":   instituteType,
				"freezeNumber":    freezeNumber,
				"reason":          otherMedicineFreeze.Reason,
				"freezeDate":      time.Now().UTC().Add(duration).Format("2006-01-02"),
				"results":         otherMedicineItems,
				"packageIsOpen":   isOpenPackage,
			}

			blindContentData := bson.M{
				"projectNumber":   projectInfo[0]["number"],
				"projectName":     projectInfo[0]["name"],
				"envName":         projectInfo[0]["env"],
				"instituteNumber": instituteNumber,
				"instituteName":   instituteName,
				"instituteNameEn": instituteNameEn,
				"instituteType":   instituteType,
				"freezeNumber":    freezeNumber,
				"reason":          otherMedicineFreeze.Reason,
				"freezeDate":      time.Now().UTC().Add(duration).Format("2006-01-02"),
				"results":         blindOtherMedicineItems,
				"packageIsOpen":   isOpenPackage,
			}

			subjectData := bson.M{
				"projectNumber":   projectInfo[0]["number"],
				"projectName":     projectInfo[0]["name"],
				"envName":         projectInfo[0]["env"],
				"instituteInfo":   instituteInfo,
				"instituteInfoEn": instituteInfoEn,
				"freezeNumber":    freezeNumber,
			}

			// 获取用户的邮箱
			userMail, err := tools.GetRoleUsersMailWithRole(projectOID, envOID, "notice.medicine.isolation", instituteID)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			if len(userMail) > 0 {
				mailBodyContet, err := tools.MailBodyContent(nil, envOID, "notice.medicine.isolation")
				for key, v := range mailBodyContet {
					contentData[key] = v
					blindContentData[key] = v
				}
				if err != nil {
					return primitive.NilObjectID, errors.WithStack(err)
				}
				var noticeConfig models.NoticeConfig
				err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": envOID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
				if err != nil && err != mongo.ErrNoDocuments {
					return primitive.NilObjectID, errors.WithStack(err)
				}
				langList := make([]string, 0)
				html := "medicine_freeze_new_zh_en.html"
				if noticeConfig.Manual != 0 {
					if noticeConfig.Manual == 1 {
						langList = append(langList, "zh")
						html = "medicine_freeze_new_zh.html"
					} else if noticeConfig.Manual == 2 {
						langList = append(langList, "en")
						html = "medicine_freeze_new_en.html"
					} else if noticeConfig.Manual == 3 {
						langList = append(langList, "zh")
						langList = append(langList, "en")
						html = "medicine_freeze_new_zh_en.html"
					}
				} else {
					langList = append(langList, ctx.GetHeader("Accept-Language"))
					if locales.Lang(ctx) == "zh" {
						html = "medicine_freeze_new_zh.html"
					} else if locales.Lang(ctx) == "en" {
						html = "medicine_freeze_new_en.html"
					}
				}
				for _, userEmail := range userMail {
					var toUserMail []string
					toUserMail = append(toUserMail, userEmail.Email)
					sendContent := contentData
					if userEmail.IsBlind {
						sendContent = blindContentData
					}
					mails = append(mails, models.Mail{
						ID:          primitive.NewObjectID(),
						Subject:     "medicine.freeze.title",
						SubjectData: subjectData,
						//Content:      "medicine.freeze.content",
						ContentData:  sendContent,
						To:           toUserMail, // test测试
						Lang:         ctx.GetHeader("Accept-Language"),
						LangList:     langList,
						Status:       0,
						CreatedTime:  time.Duration(time.Now().Unix()),
						ExpectedTime: time.Duration(time.Now().Unix()),
						SendTime:     time.Duration(time.Now().Unix()),
						HTML:         html,
					})
				}

			}
		}
		ctx.Set("HISTORY", histories)
		ctx.Set("MAIL", mails)
		if len(mails) > 0 {
			var envs []models.MailEnv
			for _, m := range mails {
				envs = append(envs, models.MailEnv{
					ID:         primitive.NewObjectID(),
					MailID:     m.ID,
					CustomerID: customerOID,
					ProjectID:  projectOID,
					EnvID:      envOID,
				})
			}
			ctx.Set("MAIL-ENV", envs)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	// 实时订单核查
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}
	if project.ProjectInfo.OrderCheck == 2 {
		err := task.AlarmMedicineNew(2, envOID)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

func (s *MedicineService) UpdateMedicines(ctx *gin.Context, updateStatus models.UpdateMedicines) error {
	var projectOID primitive.ObjectID
	var envOID primitive.ObjectID
	var project models.Project
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		filter := bson.M{"_id": bson.M{"$in": updateStatus.MedicineIds}}
		//查询研究产品信息
		var d []models.Medicine
		cursor, err := tools.Database.Collection("medicine").Find(sctx, filter)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if err := cursor.All(sctx, &d); err != nil {
			return nil, errors.WithStack(err)
		}
		// 传值事务外 药物警戒
		envOID = d[0].EnvironmentID
		projectOID = d[0].ProjectID
		err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		update := bson.M{
			"$set": bson.M{
				"status": updateStatus.Status,
			},
		}

		if updateStatus.Status == 1 {
			freezeIDs := []primitive.ObjectID{}
			useIDs := []primitive.ObjectID{}
			updateFreeze := bson.M{
				"$set": bson.M{
					"status": 14,
				},
			}
			for _, medicine := range d {
				if medicine.SubjectID.IsZero() {
					useIDs = append(useIDs, medicine.ID)
				} else {
					freezeIDs = append(freezeIDs, medicine.ID)
				}
			}
			if project.ResearchAttribute == 1 {
				updateFreeze["$set"].(bson.M)["order_id"] = nil
				update["$set"].(bson.M)["order_id"] = nil
			}

			if len(freezeIDs) > 0 {
				if _, err := tools.Database.Collection("medicine").UpdateMany(sctx, bson.M{"_id": bson.M{"$in": freezeIDs}}, updateFreeze); err != nil {
					return nil, errors.WithStack(err)
				}
			}
			if len(useIDs) > 0 {
				if _, err := tools.Database.Collection("medicine").UpdateMany(sctx, bson.M{"_id": bson.M{"$in": useIDs}}, update); err != nil {
					return nil, errors.WithStack(err)
				}
			}
		} else {
			if _, err := tools.Database.Collection("medicine").UpdateMany(sctx, bson.M{"_id": bson.M{"$in": updateStatus.MedicineIds}}, update); err != nil {
				return nil, errors.WithStack(err)
			}
		}

		user, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		key := ""
		if updateStatus.Status == 1 { //设置为可用
			key = "history.medicine.use-new.use"

		} else if updateStatus.Status == 6 { //设置为丢失/作废
			//key = "history.medicine.lost"
			key = "history.medicine.new-lost.lost"
		}

		now := time.Duration(time.Now().Unix())
		instituteInfoMap := make(map[primitive.ObjectID]map[string]interface{})
		var histories []models.History
		for _, medicine := range d {
			var instituteId primitive.ObjectID
			if updateStatus.InstituteType == 1 {
				instituteId = medicine.SiteID
			} else {
				instituteId = medicine.StorehouseID
			}
			instituteInfo, exist := instituteInfoMap[instituteId]
			if !exist {
				instituteInfo, err = getInstituteName(ctx, updateStatus.InstituteType, instituteId)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				instituteInfoMap[instituteId] = instituteInfo
			}

			tmpKey := key
			if updateStatus.Status == 1 && !medicine.SubjectID.IsZero() {
				tmpKey = "history.medicine.sku-freeze-reason"
			}
			isOpenPackage, _, _, _, _, _ := tools.IsOpenPackage(medicine.EnvironmentID)
			// if isOpenPackage && number[medicine.Name] > 0 {
			// 	if updateStatus.Status == 1 {
			// 		tmpKey = "history.medicine.use-package"
			// 	} else if updateStatus.Status == 6 {
			// 		tmpKey = "history.medicine.lost-package"
			// 	}
			// }
			packageNumber := "-"
			if medicine.PackageNumber != "" {
				packageNumber = medicine.PackageNumber
			}
			historyData := bson.M{"position": instituteInfo["instituteName"], "positionEn": instituteInfo["instituteNameEn"], "reason": updateStatus.Reason, "packNumber": medicine.Number, "packageNumber": packageNumber}
			err := s.FreezeMedicineHistoryData(ctx, medicine.ID, tmpKey, &histories, user, user.Name, now, historyData, isOpenPackage)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			// history := models.History{
			// 	Key:  tmpKey,
			// 	OID:  medicine.ID,
			// 	Data: bson.M{"reason": updateStatus.Reason, "packNumber": medicine.Number, "packageNumber": packageNumber},
			// 	Time: time.Duration(time.Now().Unix()),
			// 	UID:  user.ID,
			// 	User: user.Name,
			// }
			// histories = append(histories, history)
		}
		ctx.Set("HISTORY", histories)
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	if updateStatus.Status == 6 {
		// 实时订单核查
		if project.ProjectInfo.OrderCheck == 2 {
			err := task.AlarmMedicineNew(2, envOID)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

// 解隔离
func (s *MedicineService) ReleaseMedicines(ctx *gin.Context, updateMedicines models.UpdateMedicines) error {
	envOID := updateMedicines.EnvironmentID
	projectOID := updateMedicines.ProjectID
	//cohortOID := updateMedicines.CohortID
	callback := func(sctx mongo.SessionContext) (interface{}, error) {

		now := time.Duration(time.Now().Unix())
		packageIsOpen, _, _, _, _, _ := tools.IsOpenPackage(envOID)
		var frozenMedicinesNumber string
		var histories []models.History
		//var medicinesCount = 0
		// 查询 projectinfo
		var project models.Project
		if err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project); err != nil {
			return nil, errors.WithStack(err)
		}

		user, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		otherReleaseCount := 0
		for _, otherMedicine := range updateMedicines.OtherMedicines {
			useOrApprovedCount := otherMedicine.UseCount
			// 解隔离审批通过或者拒绝
			if project.DeIsolationApproval == 1 && updateMedicines.Status == 1 || updateMedicines.Status == 4 {
				useOrApprovedCount = otherMedicine.ApprovedQuantity
			}
			otherReleaseCount = otherReleaseCount + useOrApprovedCount
		}

		if len(updateMedicines.MedicineIds) <= 0 && otherReleaseCount <= 0 {
			return nil, tools.BuildServerError(ctx, "medicine_release_err")
		}

		//更新隔离记录
		freezeFilter := bson.M{"_id": updateMedicines.MedicineFreezeID}
		var medicineFreeze models.MedicineFreeze
		if err := tools.Database.Collection("medicine_freeze").FindOne(sctx, freezeFilter).Decode(&medicineFreeze); err != nil {
			return nil, errors.WithStack(err)
		}

		instituteInfo, err := getInstituteName(ctx, medicineFreeze.InstituteType, medicineFreeze.InstituteID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		//审批记录是否按照包装
		medicinesPackage := medicineFreeze.MedicinesPackage
		packageDrugNames := make(map[string]models.MedicinePackage)
		for _, medicinePackage := range medicinesPackage {
			packageDrugNames[medicinePackage.Name] = medicinePackage
		}

		if updateMedicines.Status == 15 { //待审批
			// 已编号药物
			if len(medicineFreeze.Medicines) > 0 {
				var mdc []models.Medicine
				filter := bson.M{"_id": bson.M{"$in": medicineFreeze.Medicines}, "isolation_approval_sign": 1}
				cursor, err := tools.Database.Collection("medicine").Find(sctx, filter)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				if err = cursor.All(sctx, &mdc); err != nil {
					return nil, errors.WithStack(err)
				}
				if len(mdc) > 0 {
					return nil, tools.BuildServerError(ctx, "medicine_approval_err")
				}
			}
			// 未编号药物
			if len(medicineFreeze.OtherMedicinesNew) > 0 {
				var mdc []models.OtherMedicine
				filter := bson.M{"_id": bson.M{"$in": medicineFreeze.OtherMedicinesNew}, "isolation_approval_sign": 1}
				cursor, err := tools.Database.Collection("medicine_others").Find(sctx, filter)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				if err = cursor.All(sctx, &mdc); err != nil {
					return nil, errors.WithStack(err)
				}
				if len(mdc) > 0 {
					return nil, tools.BuildServerError(ctx, "medicine_approval_err")
				}
			}
		}

		// 研究产品名称
		// medicineName := ""
		// // 批次号
		// batchNumber := ""
		// // 有效期
		// expirationDate := ""
		//编码研究产品的批量解隔离操作
		freezeKey := ""
		if len(updateMedicines.MedicineIds) > 0 { //有编号的药物
			//处理包装药物id
			if len(updateMedicines.MedicineIds) != len(medicineFreeze.Medicines) {
				//处理一下勾选的包装药物的id，包装药物的只勾选了一条数据，需要处理把整个包装的id放到updateMedicines.MedicineIds里面去
				selectedMedicines := make([]models.Medicine, len(updateMedicines.MedicineIds))
				cursor, _ := tools.Database.Collection("medicine").Find(nil, bson.M{"_id": bson.M{"$in": updateMedicines.MedicineIds}})
				_ = cursor.All(nil, &selectedMedicines)
				isOpenPackage, packageNumbers, _, _, _, _ := tools.IsOpenPackage(updateMedicines.EnvironmentID)

				if isOpenPackage {

					var newMedicines []primitive.ObjectID
					//用来判断这个药物的这个包装号的是否已经处理过
					medicinesPakcage := make(map[primitive.ObjectID]primitive.ObjectID)
					for _, selectedMedicine := range selectedMedicines {
						if packageDrugNames[selectedMedicine.Name].PackageMethod && selectedMedicine.PackageNumber != "" { //说明该药物是包装运送，获取整个包装的药物Id
							_, ok := medicinesPakcage[selectedMedicine.ID]
							if !ok {
								var medicinesData []models.Medicine
								medicineFilter := bson.M{"_id": bson.M{"$in": medicineFreeze.Medicines}, "env_id": selectedMedicine.EnvironmentID, "package_number": selectedMedicine.PackageNumber}
								if selectedMedicine.ExpirationDate != "" && selectedMedicine.ExpirationDate != "-" {
									medicineFilter["expiration_date"] = selectedMedicine.ExpirationDate
								}
								if selectedMedicine.BatchNumber != "" && selectedMedicine.BatchNumber != "-" {
									medicineFilter["batch_number"] = selectedMedicine.BatchNumber
								}
								cursor, err := tools.Database.Collection("medicine").Find(sctx, medicineFilter)
								if err != nil {
									return nil, errors.WithStack(err)
								}
								err = cursor.All(nil, &medicinesData)
								if err != nil {
									return nil, errors.WithStack(err)
								}
								packageNumber := packageNumbers[selectedMedicine.Name]
								if len(medicinesData) == packageNumber {
									for _, medicine := range medicinesData {
										newMedicines = append(newMedicines, medicine.ID)
										medicinesPakcage[medicine.ID] = medicine.ID
									}
								} else {
									newMedicines = append(newMedicines, selectedMedicine.ID)
								}
							}
						} else {
							newMedicines = append(newMedicines, selectedMedicine.ID)
						}
					}
					updateMedicines.MedicineIds = newMedicines
				}
			}

			filter := bson.M{"_id": bson.M{"$in": updateMedicines.MedicineIds}}
			//判断研究产品是否过期，如果已过有效期，更新状态为已过期
			if updateMedicines.Status == 6 { //设置为作废
				update := bson.M{
					"$set": bson.M{
						"status": updateMedicines.Status,
					},
				}
				if _, err := tools.Database.Collection("medicine").UpdateMany(sctx, filter, update); err != nil {
					return nil, errors.WithStack(err)
				}
			} else if updateMedicines.Status == 1 { //设置为可用
				timeZone, err := tools.GetTimeZone(projectOID)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				hours := time.Duration(timeZone)
				minutes := time.Duration((timeZone - float64(hours)) * 60)
				duration := hours*time.Hour + minutes*time.Minute
				currentDate := time.Now().UTC().Add(duration).Format("2006-01-02")
				//有效期大于当前日期的，研究产品状态设置为可用
				//ufilter := bson.M{"_id": bson.M{"$in": updateMedicines.MedicineIds}, "expiration_date": bson.M{"$gt": currentDate}}
				ufilter := bson.M{
					"_id": bson.M{"$in": updateMedicines.MedicineIds},
					"$or": bson.A{
						bson.M{"expiration_date": bson.M{"$gt": currentDate}},
						bson.M{"expiration_date": bson.M{"$ne": nil}},
						bson.M{"expiration_date": bson.M{"$ne": ""}},
						bson.M{"expiration_date": bson.M{"$ne": "-"}},
					},
					//"expiration_date": bson.M{
					//	"$gt":     currentDate,
					//	"$nin":    []interface{}{nil, "", "-"},
					//	"$exists": true,
					//},
				}
				update := bson.M{
					"$set": bson.M{
						"status":                  updateMedicines.Status,
						"isolation_approval_sign": 0,
					},
				}
				if _, err := tools.Database.Collection("medicine").UpdateMany(sctx, ufilter, update); err != nil {
					return nil, errors.WithStack(err)
				}

				// 检查本次解隔离的产品中是否有隔离前是冻结的药物
				var frozenMedicineIds []primitive.ObjectID
				for _, medicineId := range updateMedicines.MedicineIds {
					for _, frozenMedicineId := range medicineFreeze.FrozenMedicineIds {
						if medicineId == frozenMedicineId {
							frozenMedicineIds = append(frozenMedicineIds, frozenMedicineId)
						}
					}
				}
				if len(frozenMedicineIds) > 0 {
					frozenFilter := bson.M{"_id": bson.M{"$in": frozenMedicineIds}}
					frozenUpdate := bson.M{
						"$set": bson.M{
							"status":                  14,
							"isolation_approval_sign": 0,
						},
					}
					if _, err := tools.Database.Collection("medicine").UpdateMany(sctx, frozenFilter, frozenUpdate); err != nil {
						return nil, errors.WithStack(err)
					}
				}

				//有效期小于等于当前日期的，研究产品状态设置为已过期
				//expiredFilter := bson.M{"_id": bson.M{"$in": updateMedicines.MedicineIds}, "expiration_date": bson.M{"$lte": currentDate}}
				expiredFilter := bson.M{
					"_id": bson.M{"$in": updateMedicines.MedicineIds},
					//"$or": []bson.M{
					//	bson.M{"expiration_date": bson.M{"$lte": currentDate}},
					//	bson.M{"expiration_date": bson.M{"$ne": nil}},
					//	bson.M{"expiration_date": bson.M{"$ne": ""}},
					//	bson.M{"expiration_date": bson.M{"$ne": "-"}},
					//},
					"expiration_date": bson.M{
						"$lte":    currentDate,
						"$nin":    []interface{}{nil, "", "-"},
						"$exists": true,
					},
				}
				expiredUpdate := bson.M{
					"$set": bson.M{
						"status":                  7,
						"isolation_approval_sign": 0,
					},
				}
				if _, err := tools.Database.Collection("medicine").UpdateMany(sctx, expiredFilter, expiredUpdate); err != nil {
					return nil, errors.WithStack(err)
				}
			} else if updateMedicines.Status == 4 { // 隔离
				update := bson.M{
					"$set": bson.M{
						"isolation_approval_sign": 0,
					},
				}
				if _, err := tools.Database.Collection("medicine").UpdateMany(sctx, filter, update); err != nil {
					return nil, errors.WithStack(err)
				}
			} else if updateMedicines.Status == 15 { // 待审批
				//medicineName, batchNumber, expirationDate = drugAttributeDeDuplication(sctx, updateMedicines.MedicineIds)
				update := bson.M{
					"$set": bson.M{
						"isolation_approval_sign": 1,
					},
				}
				if _, err := tools.Database.Collection("medicine").UpdateMany(sctx, filter, update); err != nil {
					return nil, errors.WithStack(err)
				}
			}
			var data []models.Medicine
			cursor, err := tools.Database.Collection("medicine").Find(sctx, filter)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if err := cursor.All(sctx, &data); err != nil {
				return nil, errors.WithStack(err)
			}
			sort.Slice(data, func(i, j int) bool {
				return data[i].Number < data[j].Number
			})
			//查询研究产品信息
			key := ""

			if updateMedicines.Status == 6 {
				key = "history.medicine.releaseLost-new.release"
			} else if updateMedicines.Status == 1 {
				key = "history.medicine.release-new.release"
			}

			// if packageIsOpen {
			// 	if updateMedicines.Status == 6 {
			// 		key = "history.medicine.releaseLost-package"
			// 	} else if updateMedicines.Status == 1 {
			// 		key = "history.medicine.release-package"
			// 	}
			// }

			//medicinesCount = len(data)
			for _, medicine := range data {
				packageNumber := "-"
				if medicine.PackageNumber != "" {
					packageNumber = medicine.PackageNumber
				}
				if packageIsOpen {
					frozenMedicinesNumber = frozenMedicinesNumber + medicine.Number + "/" + packageNumber + " "
				} else {
					frozenMedicinesNumber = frozenMedicinesNumber + medicine.Number + " "
				}

				// history := models.History{
				// 	Key:  key,
				// 	OID:  medicine.ID,
				// 	Data: bson.M{"reason": updateMedicines.Reason, "packNumber": medicine.Number, "packageNumber": packageNumber},
				// 	Time: time.Duration(time.Now().Unix()),
				// 	UID:  user.ID,
				// 	User: user.Name,
				// }
				// histories = append(histories, history)
				historyData := bson.M{"position": instituteInfo["instituteName"], "positionEn": instituteInfo["instituteNameEn"], "reason": updateMedicines.Reason, "packNumber": medicine.Number, "packageNumber": packageNumber}
				err = s.FreezeMedicineHistoryData(ctx, medicine.ID, key, &histories, user, user.Name, now, historyData, packageIsOpen)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}
		}

		//未编码研究产品的批量解隔离操作
		//var updateOtherMedicine []models.FreezeOtherMedicine
		allOtherMedicineIds := make([]primitive.ObjectID, 0)
		var otherFrozenMedicinesNumber string
		if len(updateMedicines.OtherMedicines) > 0 {
			key := ""
			if updateMedicines.Status == 6 {
				key = "history.medicine.otherReleaseLost-new.release"
			} else if updateMedicines.Status == 1 {
				key = "history.medicine.otherRelease-new.release"
			}
			// else if updateMedicines.Status == 4 { // 隔离
			// 	key = "history.medicine.other-quarantine-no"
			// }
			// else if updateMedicines.Status == 15 { // 待审批
			// 	key = "history.medicine.other-approval"
			// }

			for _, otherMedicine := range updateMedicines.OtherMedicines {
				if otherMedicine.Salt != "" && otherMedicine.SaltName != "" {
					name := tools.Decrypt(otherMedicine.SaltName, otherMedicine.Salt)
					otherMedicine.Name = name
				}
				if otherMedicine.UseCount > 0 || otherMedicine.ApprovedQuantity > 0 {

					//var otherOID primitive.ObjectID
					historyBatch := otherMedicine.Batch
					historyExpireDate := otherMedicine.ExpireDate
					historyUseCount := fmt.Sprintf("%v", otherMedicine.UseCount)
					historyApprovedCount := fmt.Sprintf("%v", otherMedicine.ApprovedQuantity)
					if historyBatch == "" {
						historyBatch = "-"
					}
					if historyExpireDate == "" {
						historyBatch = "-"
					}
					if packageDrugNames[otherMedicine.Name].PackageMethod && packageDrugNames[otherMedicine.Name].PackageNumber > 0 && (medicineFreeze.OrderType == 1 || medicineFreeze.InstituteType == 2) {
						historyUseCount = fmt.Sprintf("%v(%v)", otherMedicine.UseCount*packageDrugNames[otherMedicine.Name].PackageNumber, otherMedicine.UseCount)
						historyApprovedCount = fmt.Sprintf("%v(%v)", otherMedicine.ApprovedQuantity*packageDrugNames[otherMedicine.Name].PackageNumber, otherMedicine.ApprovedQuantity)
					}
					otherMedicineHistory := fmt.Sprintf("%s/%s/%s/%s", otherMedicine.Name, historyBatch, historyExpireDate, historyUseCount)
					// 解隔离审批通过或者拒绝
					if project.DeIsolationApproval == 1 && updateMedicines.Status == 1 || updateMedicines.Status == 4 {
						otherMedicineHistory = fmt.Sprintf("%s/%s/%s/%s", otherMedicine.Name, historyBatch, historyExpireDate, historyApprovedCount)
					}
					otherFrozenMedicinesNumber = otherFrozenMedicinesNumber + otherMedicineHistory + ";"
					queryMatch := bson.M{"_id": bson.M{"$in": medicineFreeze.OtherMedicinesNew}, "name": otherMedicine.Name}
					if otherMedicine.ExpireDate != "" && otherMedicine.ExpireDate != "-" {
						queryMatch["expiration_date"] = otherMedicine.ExpireDate
					}
					if otherMedicine.Batch != "" && otherMedicine.Batch != "-" {
						queryMatch["batch_number"] = otherMedicine.Batch
					}
					if medicineFreeze.InstituteID != primitive.NilObjectID {
						if medicineFreeze.InstituteType == 1 {
							queryMatch["site_id"] = medicineFreeze.InstituteID
						} else if medicineFreeze.InstituteType == 2 {
							queryMatch["storehouse_id"] = medicineFreeze.InstituteID
						}
					}
					limitCount := otherMedicine.UseCount
					if project.DeIsolationApproval == 1 && updateMedicines.Status == 1 || updateMedicines.Status == 4 {
						queryMatch["isolation_approval_sign"] = 1
						limitCount = otherMedicine.ApprovedQuantity
					}
					var otherMedicineData []map[string]interface{}
					otherMedicineIds := make([]primitive.ObjectID, 0)
					if packageDrugNames[otherMedicine.Name].PackageMethod && (medicineFreeze.InstituteType == 2 || medicineFreeze.OrderType == 1) {
						pipepine := mongo.Pipeline{
							{{Key: "$match", Value: queryMatch}},
							{{Key: "$group", Value: bson.M{"_id": bson.M{"package_number": "$package_number", "expiration_date": "$expiration_date", "package_serial_number": "$package_serial_number"}, "availableCount": bson.M{"$sum": 1}, "ids": bson.M{"$addToSet": "$_id"}}}},
							{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$availableCount", packageDrugNames[otherMedicine.Name].PackageNumber}}}}},
							{{Key: "$limit", Value: limitCount}},
							{{Key: "$project", Value: bson.M{
								"ids": 1,
							}}},
						}
						cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, pipepine)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						err = cursor.All(nil, &otherMedicineData)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						for _, otherMedicine := range otherMedicineData {
							otherMedicines := otherMedicine["ids"].(primitive.A)
							for _, id := range otherMedicines {
								otherMedicineIds = append(otherMedicineIds, id.(primitive.ObjectID))
								allOtherMedicineIds = append(allOtherMedicineIds, id.(primitive.ObjectID))
							}
						}

					} else {
						pipepine := mongo.Pipeline{
							{{Key: "$match", Value: queryMatch}},
							{{Key: "$limit", Value: limitCount}},
							{{Key: "$project", Value: bson.M{
								"_id": 1,
							}}},
						}
						cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, pipepine)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						err = cursor.All(nil, &otherMedicineData)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						for _, otherMedicine := range otherMedicineData {
							otherMedicineIds = append(otherMedicineIds, otherMedicine["_id"].(primitive.ObjectID))
							allOtherMedicineIds = append(allOtherMedicineIds, otherMedicine["_id"].(primitive.ObjectID))
						}
					}

					filter := bson.M{"_id": bson.M{"$in": otherMedicineIds}}
					//判断研究产品是否过期，如果已过有效期，更新状态为已过期
					if updateMedicines.Status == 6 { //设置为作废
						update := bson.M{
							"$set": bson.M{
								"status": updateMedicines.Status,
							},
						}
						if _, err := tools.Database.Collection("medicine_others").UpdateMany(sctx, filter, update); err != nil {
							return nil, errors.WithStack(err)
						}
					} else if updateMedicines.Status == 1 { //设置为可用
						timeZone, err := tools.GetTimeZone(projectOID)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						hours := time.Duration(timeZone)
						minutes := time.Duration((timeZone - float64(hours)) * 60)
						duration := hours*time.Hour + minutes*time.Minute
						currentDate := time.Now().UTC().Add(duration).Format("2006-01-02")
						//有效期大于当前日期的，研究产品状态设置为可用
						//ufilter := bson.M{"_id": bson.M{"$in": otherMedicineIds}, "expiration_date": bson.M{"$gt": currentDate}}
						ufilter := bson.M{
							"_id": bson.M{"$in": otherMedicineIds},
							"$or": []bson.M{
								{"expiration_date": bson.M{"$gt": currentDate}},
								{"expiration_date": bson.M{"$ne": nil}},
								{"expiration_date": bson.M{"$ne": ""}},
								{"expiration_date": bson.M{"$ne": "-"}},
							},
							//"expiration_date": bson.M{
							//	"$gt":     currentDate,
							//	"$nin":    []interface{}{nil, "", "-"},
							//	"$exists": true,
							//},
						}
						update := bson.M{
							"$set": bson.M{
								"status":                  updateMedicines.Status,
								"isolation_approval_sign": 0,
							},
						}
						if _, err := tools.Database.Collection("medicine_others").UpdateMany(sctx, ufilter, update); err != nil {
							return nil, errors.WithStack(err)
						}

						// 检查本次解隔离的产品中是否有隔离前是冻结的药物
						var frozenMedicineIds []primitive.ObjectID
						for _, medicineId := range updateMedicines.MedicineIds {
							for _, frozenMedicineId := range medicineFreeze.FrozenMedicineIds {
								if medicineId == frozenMedicineId {
									frozenMedicineIds = append(frozenMedicineIds, frozenMedicineId)
								}
							}
						}
						if len(frozenMedicineIds) > 0 {
							frozenFilter := bson.M{"_id": bson.M{"$in": frozenMedicineIds}}
							frozenUpdate := bson.M{
								"$set": bson.M{
									"status":                  14,
									"isolation_approval_sign": 0,
								},
							}
							if _, err := tools.Database.Collection("medicine_others").UpdateMany(sctx, frozenFilter, frozenUpdate); err != nil {
								return nil, errors.WithStack(err)
							}
						}

						//有效期小于等于当前日期的，研究产品状态设置为已过期
						//expiredFilter := bson.M{"_id": bson.M{"$in": otherMedicineIds}, "expiration_date": bson.M{"$lte": currentDate}}
						expiredFilter := bson.M{
							"_id": bson.M{"$in": otherMedicineIds},
							//"$or": []bson.M{
							//	bson.M{"expiration_date": bson.M{"$lte": currentDate}},
							//	bson.M{"expiration_date": bson.M{"$ne": nil}},
							//	bson.M{"expiration_date": bson.M{"$ne": ""}},
							//	bson.M{"expiration_date": bson.M{"$ne": "-"}},
							//},
							"expiration_date": bson.M{
								"$lte":    currentDate,
								"$nin":    []interface{}{nil, "", "-"},
								"$exists": true,
							},
						}
						expiredUpdate := bson.M{
							"$set": bson.M{
								"status":                  7,
								"isolation_approval_sign": 0,
							},
						}
						if _, err := tools.Database.Collection("medicine_others").UpdateMany(sctx, expiredFilter, expiredUpdate); err != nil {
							return nil, errors.WithStack(err)
						}
					} else if updateMedicines.Status == 4 { // 隔离
						update := bson.M{
							"$set": bson.M{
								"isolation_approval_sign": 0,
							},
						}
						if _, err := tools.Database.Collection("medicine_others").UpdateMany(sctx, filter, update); err != nil {
							return nil, errors.WithStack(err)
						}
					} else if updateMedicines.Status == 15 { // 待审批
						//medicineName, batchNumber, expirationDate = drugAttributeDeDuplication(sctx, updateMedicines.OtherMedicines)
						update := bson.M{
							"$set": bson.M{
								"isolation_approval_sign": 1,
							},
						}
						if _, err := tools.Database.Collection("medicine_others").UpdateMany(sctx, filter, update); err != nil {
							return nil, errors.WithStack(err)
						}
					}

					useOrApprovedCount := otherMedicine.UseCount
					// 解隔离审批通过或者拒绝
					if project.DeIsolationApproval == 1 && updateMedicines.Status == 1 || updateMedicines.Status == 4 {
						useOrApprovedCount = otherMedicine.ApprovedQuantity
					}
					//轨迹
					updateMedicinesHistory := models.OtherMedicineFreeze{
						CustomerID:    medicineFreeze.CustomerID,
						ProjectID:     medicineFreeze.ProjectID,
						EnvironmentID: medicineFreeze.EnvironmentID,
						//InstituteID:   medicineFreeze.InstituteID,
						InstituteType: medicineFreeze.InstituteType,
					}
					otherMedicineInfo := models.FreezeOtherMedicines{
						Name:       otherMedicine.Name,
						ExpireDate: otherMedicine.ExpireDate,
						Batch:      otherMedicine.Batch,
					}
					if medicineFreeze.InstituteType == 1 {
						otherMedicineInfo.SiteID = medicineFreeze.InstituteID
					} else {
						otherMedicineInfo.StorehouseID = medicineFreeze.InstituteID
					}
					otherMedicineKey, _ := s.getOtherMedicineKey(ctx, updateMedicinesHistory, otherMedicineInfo)
					historyCount := fmt.Sprintf("%v", useOrApprovedCount)
					if historyBatch == "" {
						historyBatch = "-"
					}
					if historyExpireDate == "" {
						historyBatch = "-"
					}
					if packageDrugNames[otherMedicine.Name].PackageMethod && packageDrugNames[otherMedicine.Name].PackageNumber > 0 && (medicineFreeze.OrderType == 1 || medicineFreeze.InstituteType == 2) {
						historyCount = fmt.Sprintf("%v(%v)", useOrApprovedCount*packageDrugNames[otherMedicine.Name].PackageNumber, useOrApprovedCount)
					}
					// history := models.History{
					// 	Key:  key,
					// 	OID:  otherMedicineKey.ID,
					// 	Data: bson.M{"reason": updateMedicines.Reason, "name": otherMedicine.Name, "batch": historyBatch, "expireDate": historyExpireDate, "count": historyCount},
					// 	Time: time.Duration(time.Now().Unix()),
					// 	UID:  user.ID,
					// 	User: user.Name,
					// }
					// histories = append(histories, history)
					historyData := bson.M{"position": instituteInfo["instituteName"], "positionEn": instituteInfo["instituteNameEn"], "reason": updateMedicines.Reason, "name": otherMedicine.Name, "batch": historyBatch, "expireDate": historyExpireDate, "count": historyCount}
					err = s.FreezeMedicineHistoryData(ctx, otherMedicineKey.ID, key, &histories, user, user.Name, now, historyData, false)
					if err != nil {
						return nil, errors.WithStack(err)
					}
				}
				// else {
				// 	updateOtherMedicine = append(updateOtherMedicine, models.FreezeOtherMedicine{
				// 		Name:              otherMedicine.Name,
				// 		MedicineOtherInfo: medicineOtherInfo,
				// 		// 隔离数量
				// 		ApprovedQuantity: 0, // 待审批数量
				// 		ExpireDate:       otherMedicine.ExpireDate,
				// 		Batch:            otherMedicine.Batch,
				// 	})
				// }
			}
		}

		freezeUpdate := bson.M{}
		if len(medicineFreeze.Medicines) == len(updateMedicines.MedicineIds) && len(medicineFreeze.OtherMedicinesNew) == len(allOtherMedicineIds) {
			freezeUpdate = bson.M{
				"$set": bson.M{
					"medicines":           nil,
					"other_medicines_new": nil,
					"untie_reason":        updateMedicines.Reason,
					"close_date":          time.Duration(time.Now().Unix()),
					"meta.updated_at":     time.Duration(time.Now().Unix()),
					"meta.updated_by":     user.ID,
				},
			}
			if updateMedicines.Status == 15 {
				freezeUpdate = bson.M{
					"$set": bson.M{
						"other_medicines_new": nil,
						"untie_reason":        updateMedicines.Reason,
						"close_date":          time.Duration(time.Now().Unix()),
						"meta.updated_at":     time.Duration(time.Now().Unix()),
						"meta.updated_by":     user.ID,
					},
				}
			}
		} else {
			//编号药物
			mapMedicines := make(map[primitive.ObjectID]primitive.ObjectID)
			for _, medicineID := range updateMedicines.MedicineIds {
				mapMedicines[medicineID] = medicineID
			}
			frozenMedicines := make([]primitive.ObjectID, len(medicineFreeze.Medicines)-len(updateMedicines.MedicineIds))
			index := 0
			for _, medicineID := range medicineFreeze.Medicines {
				_, ok := mapMedicines[medicineID]
				if !ok {
					frozenMedicines[index] = medicineID
					index++
				}
			}
			//未编号药物id
			mapOtherMedicines := make(map[primitive.ObjectID]primitive.ObjectID)
			for _, medicineID := range allOtherMedicineIds {
				mapOtherMedicines[medicineID] = medicineID
			}
			frozenOtherMedicines := make([]primitive.ObjectID, len(medicineFreeze.OtherMedicinesNew)-len(allOtherMedicineIds))
			otherIndex := 0
			for _, otherMedicineID := range medicineFreeze.OtherMedicinesNew {
				_, ok := mapOtherMedicines[otherMedicineID]
				if !ok {
					frozenOtherMedicines[otherIndex] = otherMedicineID
					otherIndex++
				}
			}
			freezeUpdate = bson.M{
				"$set": bson.M{
					"medicines":           frozenMedicines,
					"other_medicines_new": frozenOtherMedicines,
					"untie_reason":        updateMedicines.Reason,
				},
			}
		}

		// if _, err := tools.Database.Collection("medicine_freeze").UpdateOne(sctx, freezeFilter, freezeUpdate); err != nil {
		// 	return nil, errors.WithStack(err)
		// }

		if updateMedicines.Status == 1 || updateMedicines.Status == 6 { // 可用/丢弃
			if _, err := tools.Database.Collection("medicine_freeze").UpdateOne(sctx, freezeFilter, freezeUpdate); err != nil {
				return nil, errors.WithStack(err)
			}
		} else if updateMedicines.Status == 15 { // 记录申请原因
			freezeUpdate = bson.M{
				"$set": bson.M{
					"untie_reason": updateMedicines.Reason,
				},
			}
			if _, err := tools.Database.Collection("medicine_freeze").UpdateOne(sctx, freezeFilter, freezeUpdate); err != nil {
				return nil, errors.WithStack(err)
			}
		}

		// if project.DeIsolationApproval == 1 && updateMedicines.Status == 1 { // 解隔离审批通过
		// 	//TODO
		// 	freezeUpdate = bson.M{
		// 		"$set": bson.M{
		// 			"other_medicines": updateOtherMedicine,
		// 		},
		// 	}
		// 	if _, err := tools.Database.Collection("medicine_freeze").UpdateOne(sctx, freezeFilter, freezeUpdate); err != nil {
		// 		return nil, errors.WithStack(err)
		// 	}
		// }

		if len(frozenMedicinesNumber) > 1 {
			frozenMedicinesNumber = frozenMedicinesNumber[0 : len(frozenMedicinesNumber)-1]
		}
		if len(otherFrozenMedicinesNumber) > 1 {
			otherFrozenMedicinesNumber = otherFrozenMedicinesNumber[0 : len(otherFrozenMedicinesNumber)-1]
		}
		// freezekeyPackage := ""
		// if packageIsOpen {
		// 	freezekeyPackage = "-package"
		// }
		// if len(frozenMedicinesNumber) > 0 && len(otherFrozenMedicinesNumber) == 0 {
		// 	if updateMedicines.Status == 6 {
		// 		freezeKey = "history.medicine.drugFreeze.lost" + freezekeyPackage
		// 	} else if updateMedicines.Status == 1 {
		// 		if project.DeIsolationApproval == 1 {
		// 			freezeKey = "history.medicine.drugFreeze.quarantine-yes" + freezekeyPackage
		// 		} else {
		// 			freezeKey = "history.medicine.drugFreeze.release" + freezekeyPackage
		// 		}
		// 	} else if updateMedicines.Status == 4 { // 隔离
		// 		freezeKey = "history.medicine.drugFreeze.quarantine-no" + freezekeyPackage
		// 	} else if updateMedicines.Status == 15 { // 待审批
		// 		freezeKey = "history.medicine.drugFreeze.approval" + freezekeyPackage
		// 	}
		// } else if len(frozenMedicinesNumber) == 0 && len(otherFrozenMedicinesNumber) > 0 {
		// 	if updateMedicines.Status == 6 {
		// 		freezeKey = "history.medicine.drugFreeze.otherLost"
		// 	} else if updateMedicines.Status == 1 {
		// 		if project.DeIsolationApproval == 1 {
		// 			freezeKey = "history.medicine.drugFreeze.other-quarantine-yes"
		// 		} else {
		// 			freezeKey = "history.medicine.drugFreeze.otherRelease"
		// 		}
		// 	} else if updateMedicines.Status == 4 { // 隔离
		// 		freezeKey = "history.medicine.drugFreeze.other-quarantine-no"
		// 	} else if updateMedicines.Status == 15 { // 待审批
		// 		freezeKey = "history.medicine.drugFreeze.other-approval"
		// 	}
		// } else if len(frozenMedicinesNumber) > 0 && len(otherFrozenMedicinesNumber) > 0 {
		// 	if updateMedicines.Status == 6 {
		// 		freezeKey = "history.medicine.drugFreeze.allLost" + freezekeyPackage
		// 	} else if updateMedicines.Status == 1 {
		// 		if project.DeIsolationApproval == 1 {
		// 			freezeKey = "history.medicine.drugFreeze.all-quarantine-yes" + freezekeyPackage
		// 		} else {
		// 			freezeKey = "history.medicine.drugFreeze.allRelease" + freezekeyPackage
		// 		}
		// 	} else if updateMedicines.Status == 4 { // 隔离
		// 		freezeKey = "history.medicine.drugFreeze.all-quarantine-no" + freezekeyPackage
		// 	} else if updateMedicines.Status == 15 { // 待审批
		// 		freezeKey = "history.medicine.drugFreeze.all-approval" + freezekeyPackage
		// 	}
		// }
		// history := models.History{
		// 	Key:  freezeKey,
		// 	OID:  updateMedicines.MedicineFreezeID,
		// 	Data: bson.M{"freezeNumber": medicineFreeze.Number, "freezeReason": updateMedicines.Reason, "medicines": "[" + frozenMedicinesNumber + "]", "otherMedicines": otherFrozenMedicinesNumber, "medicineName": medicineName, "batchNumber": batchNumber, "expirationDate": expirationDate, "count": medicinesCount, "untieReason": medicineFreeze.UntieReason},
		// 	Time: time.Duration(time.Now().Unix()),
		// 	UID:  user.ID,
		// 	User: user.Name,
		// }
		// histories = append(histories, history)
		hData := make(map[string]interface{})
		historyData := bson.M{"position": instituteInfo["instituteName"], "positionEn": instituteInfo["instituteNameEn"], "freezeNumber": medicineFreeze.Number}
		if updateMedicines.Status == 6 {
			freezeKey = "history.medicine.drugFreeze-new.lost"
			historyData["reason"] = updateMedicines.Reason
		} else if updateMedicines.Status == 1 {
			if project.DeIsolationApproval == 1 {
				freezeKey = "history.medicine.drugFreeze-new.quarantine-yes"
				historyData["untieReason"] = medicineFreeze.UntieReason
			} else {
				freezeKey = "history.medicine.drugFreeze-new.release"
				historyData["reason"] = updateMedicines.Reason
			}
		} else if updateMedicines.Status == 4 { // 隔离
			freezeKey = "history.medicine.drugFreeze-new.quarantine-no"
			historyData["untieReason"] = medicineFreeze.UntieReason
			hData["freezeReason"] = updateMedicines.Reason
		} else if updateMedicines.Status == 15 { // 待审批
			freezeKey = "history.medicine.drugFreeze-new.approval"
			historyData["reason"] = updateMedicines.Reason
		}
		if len(frozenMedicinesNumber) > 0 {
			historyData["medicines"] = "[" + frozenMedicinesNumber + "]"
		}
		if len(otherFrozenMedicinesNumber) > 0 {
			historyData["otherMedicines"] = otherFrozenMedicinesNumber
		}
		err = s.FreezeHistoryData(ctx, updateMedicines.MedicineFreezeID, freezeKey, hData, &histories, user, user.Name, now, historyData, packageIsOpen)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		ctx.Set("HISTORY", histories)
		// 邮件
		state, err := tools.MailCustomContentState(envOID, "medicine.freeze.release")
		if err != nil {
			return primitive.NilObjectID, errors.WithStack(err)
		}
		if updateMedicines.Status == 1 && state {
			var mails []models.Mail
			instituteName := ""
			instituteNameEn := ""
			instituteNumber := ""
			var instituteData []map[string]interface{}
			if medicineFreeze.InstituteType == 1 { //中心
				matchID := bson.M{"_id": medicineFreeze.InstituteID}
				projectMongo := bson.M{
					"_id":    0,
					"id":     "$_id",
					"number": "$number",
					"name":   models.ProjectSiteNameBsonLang("zh"),
					"nameEn": models.ProjectSiteNameBsonLang("en"),
				}
				sitePipeline := mongo.Pipeline{
					{{Key: "$match", Value: matchID}},
					{{Key: "$project", Value: projectMongo}},
				}
				cursor, err := tools.Database.Collection("project_site").Aggregate(nil, sitePipeline)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = cursor.All(nil, &instituteData)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				if len(instituteData) > 0 {
					instituteName = instituteData[0]["name"].(string)
					instituteNameEn = instituteData[0]["nameEn"].(string)
					instituteNumber = instituteData[0]["number"].(string)
				}
			} else {
				matchID := bson.M{"_id": medicineFreeze.InstituteID}
				lookup := bson.M{
					"from":         "storehouse",
					"localField":   "storehouse_id",
					"foreignField": "_id",
					"as":           "detail",
				}
				projectMongo := bson.M{
					"_id":    0,
					"id":     "$_id",
					"number": "$detail.number",
					"name":   "$detail.name",
					"nameEn": "$detail.name",
				}
				pipeline := mongo.Pipeline{
					{{Key: "$match", Value: matchID}},
					{{Key: "$lookup", Value: lookup}},
					{{Key: "$unwind", Value: "$detail"}},
					{{Key: "$project", Value: projectMongo}},
				}
				storehouseCursor, err := tools.Database.Collection("project_storehouse").Aggregate(nil, pipeline)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				var storehouseData []map[string]interface{}
				err = storehouseCursor.All(nil, &storehouseData)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				if len(storehouseData) > 0 {
					instituteName = storehouseData[0]["name"].(string)
					instituteNameEn = storehouseData[0]["name"].(string)
					instituteNumber = storehouseData[0]["number"].(string)
				}
			}
			var projectInfo []map[string]interface{}
			pipeline := mongo.Pipeline{
				{{Key: "$match", Value: bson.M{"_id": medicineFreeze.ProjectID, "customer_id": medicineFreeze.CustomerID}}},
				{{Key: "$unwind", Value: "$envs"}},
				{{Key: "$match", Value: bson.M{"envs.id": medicineFreeze.EnvironmentID}}},
				{
					{
						Key: "$project",
						Value: bson.M{
							"_id":      0,
							"number":   "$info.number",
							"name":     "$info.name",
							"env":      "$envs.name",
							"timeZone": "$info.timeZoneStr",
						},
					},
				},
			}
			cursor, err := tools.Database.Collection("project").Aggregate(nil, pipeline)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &projectInfo)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			var timeZone float64
			if projectInfo[0]["timeZone"] != nil {
				//timeZone = projectInfo[0]["timeZone"].(int32)
				timeZone, _ = strconv.ParseFloat(projectInfo[0]["timeZone"].(string), 64)
			} else {
				timeZone = float64(8)
			}
			hours := time.Duration(timeZone)
			minutes := time.Duration((timeZone - float64(hours)) * 60)
			duration := hours*time.Hour + minutes*time.Minute

			//查询研究产品信息
			isOpenPackage, _, _, _, _, _ := tools.IsOpenPackage(envOID)
			medicineItems := []map[string]interface{}{}
			blindMedicineItems := []map[string]interface{}{}
			medicinePipeline := mongo.Pipeline{
				{{Key: "$match", Value: bson.M{"_id": bson.M{"$in": updateMedicines.MedicineIds}}}},
				{{Key: "$group", Value: bson.M{
					"_id":    bson.M{"batch_number": "$batch_number", "expiration_date": "$expiration_date", "package_number": bson.M{"$ifNull": bson.A{"$package_number", ""}}},
					"number": bson.M{"$push": "$number"},
				}}},
				{{Key: "$project", Value: bson.M{
					"batch_number":    "$_id.batch_number",
					"expiration_date": "$_id.expiration_date",
					"package_number":  "$_id.package_number",
					"number":          1,
				}}},
				{{Key: "$sort", Value: bson.D{{"expiration_date", 1}, {Key: "batch_number", Value: 1}}}},
				{{Key: "$group", Value: bson.M{
					"_id": bson.M{"batch_number": "$batch_number", "expiration_date": "$expiration_date"},
					"detail": bson.M{"$push": bson.M{
						"number":         "$number",
						"package_number": "$package_number",
					}},
				}}},
			}

			cursor, err = tools.Database.Collection("medicine").Aggregate(nil, medicinePipeline)
			var medicineDatas []map[string]interface{}

			err = cursor.All(nil, &medicineDatas)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			//编号研究产品
			for _, data := range medicineDatas {
				var details []map[string]interface{}
				dataId := data["_id"].(map[string]interface{})
				for _, detail := range data["detail"].(primitive.A) {
					medicine := detail.(map[string]interface{})
					var medicines []string
					for _, item := range medicine["number"].(primitive.A) {
						medicines = append(medicines, item.(string))
					}
					sort.Sort(sort.StringSlice(medicines))
					details = append(details, map[string]interface{}{
						"number":        strings.Join(medicines, ","),
						"count":         len(medicines),
						"packageNumber": medicine["package_number"],
					})
				}
				resultData := map[string]interface{}{
					"batch":         dataId["batch_number"],
					"expiryDate":    dataId["expiration_date"],
					"rowsSpan":      len(data["detail"].(primitive.A)),
					"packageIsOpen": isOpenPackage,
					"details":       details,
				}
				medicineItems = append(medicineItems, resultData)
				blindMedicineItems = append(blindMedicineItems, resultData)
			}
			// var frozenData []models.Medicine
			// if len(updateMedicines.MedicineIds) > 0 {
			// 	opts := &options.FindOptions{
			// 		Sort: bson.D{{"number", 1}},
			// 	}
			// 	dataCursor, err := tools.Database.Collection("medicine").Find(sctx, bson.M{"_id": bson.M{"$in": updateMedicines.MedicineIds}}, opts)
			// 	if err != nil {
			// 		return nil, errors.WithStack(err)
			// 	}
			// 	if err := dataCursor.All(sctx, &frozenData); err != nil {
			// 		return nil, errors.WithStack(err)
			// 	}
			// }
			// medicineItems := []map[string]interface{}{}
			// blindMedicineItems := []map[string]interface{}{}
			// floor := func(item models.Medicine) string {
			// 	return item.BatchNumber + item.ExpirationDate
			// }
			// res := slice.GroupWith(frozenData, floor)
			// for _, item := range res {
			// 	medicineItem := map[string]interface{}{}
			// 	var medicineNumber []string
			// 	for _, value := range item {
			// 		medicineNumber = append(medicineNumber, value.Number)
			// 	}
			// 	sort.Sort(sort.StringSlice(medicineNumber))
			// 	medicineItem["batchNumber"] = item[0].BatchNumber
			// 	medicineItem["number"] = strings.Join(medicineNumber, ",")
			// 	medicineItem["expiryDate"] = item[0].ExpirationDate
			// 	medicineItem["packageNumber"] = item[0].PackageNumber
			// 	medicineItem["count"] = len(item)
			// 	medicineItems = append(medicineItems, medicineItem)
			// 	blindMedicineItems = append(blindMedicineItems, medicineItem)
			// }
			//未编号研究产品
			for _, otherMedicine := range updateMedicines.OtherMedicines {
				if otherMedicine.Salt != "" && otherMedicine.SaltName != "" {
					name := tools.Decrypt(otherMedicine.SaltName, otherMedicine.Salt)
					otherMedicine.Name = name
				}
				medicineItem := map[string]interface{}{}
				medicineItem["batchNumber"] = otherMedicine.Batch
				medicineItem["number"] = otherMedicine.Name
				medicineItem["expiryDate"] = otherMedicine.ExpireDate
				count := otherMedicine.UseCount
				// 解隔离审批通过或者拒绝
				if project.DeIsolationApproval == 1 && updateMedicines.Status == 1 || updateMedicines.Status == 4 {
					count = otherMedicine.ApprovedQuantity
				}
				medicineItem["count"] = count

				medicinePackage := packageDrugNames[otherMedicine.Name]
				singleCount := count
				if medicinePackage.PackageMethod && (medicineFreeze.InstituteType == 2 || medicineFreeze.OrderType == 1) {
					singleCount = count * medicinePackage.PackageNumber
				}

				mailPackageMethod := false
				if medicineFreeze.InstituteType == 2 || medicineFreeze.OrderType == 1 {
					mailPackageMethod = medicinePackage.PackageMethod
				}

				var details []map[string]interface{}
				details = append(details, map[string]interface{}{
					"number":        otherMedicine.Name,
					"count":         singleCount,
					"packageMethod": mailPackageMethod,
					"packageCount":  count,
					"packageNumber": nil,
				})
				resultData := map[string]interface{}{
					"batch":         otherMedicine.Batch,
					"expiryDate":    otherMedicine.ExpireDate,
					"rowsSpan":      1,
					"packageIsOpen": isOpenPackage,
					"details":       details,
				}
				medicineItems = append(medicineItems, resultData)

				name := otherMedicine.Name
				isBlindedDrug, _ := tools.IsBlindedDrug(envOID, otherMedicine.Name)
				if isBlindedDrug {
					name = tools.BlindData
				}
				var blindDetails []map[string]interface{}
				blindDetails = append(blindDetails, map[string]interface{}{
					"number":        name,
					"count":         singleCount,
					"packageNumber": nil,
					"packageMethod": mailPackageMethod,
					"packageCount":  count,
				})
				blindResultData := map[string]interface{}{
					"batch":         otherMedicine.Batch,
					"expiryDate":    otherMedicine.ExpireDate,
					"rowsSpan":      1,
					"packageIsOpen": isOpenPackage,
					"details":       blindDetails,
				}
				blindMedicineItems = append(blindMedicineItems, blindResultData)
			}

			instituteInfo := instituteName
			instituteInfoEn := instituteNameEn
			instituteType := false
			if medicineFreeze.InstituteType == 1 {
				instituteType = true
				instituteInfo = instituteNumber + " " + instituteName
				instituteInfoEn = instituteNumber + " " + instituteNameEn
			}

			contentData := bson.M{
				"projectNumber":   projectInfo[0]["number"],
				"projectName":     projectInfo[0]["name"],
				"envName":         projectInfo[0]["env"],
				"instituteNumber": instituteNumber,
				"instituteName":   instituteName,
				"instituteNameEn": instituteNameEn,
				"instituteType":   instituteType,
				"reason":          updateMedicines.Reason,
				"freezeDate":      time.Now().UTC().Add(duration).Format("2006-01-02"),
				"results":         medicineItems,
				"freezeNumber":    medicineFreeze.Number,
				"packageIsOpen":   isOpenPackage,
			}

			blindContent := bson.M{
				"projectNumber":   projectInfo[0]["number"],
				"projectName":     projectInfo[0]["name"],
				"envName":         projectInfo[0]["env"],
				"instituteNumber": instituteNumber,
				"instituteName":   instituteName,
				"instituteNameEn": instituteNameEn,
				"instituteType":   instituteType,
				"reason":          updateMedicines.Reason,
				"freezeDate":      time.Now().UTC().Add(duration).Format("2006-01-02"),
				"results":         blindMedicineItems,
				"packageIsOpen":   isOpenPackage,
				"freezeNumber":    medicineFreeze.Number,
			}

			subjectData := bson.M{
				"projectNumber":   projectInfo[0]["number"],
				"projectName":     projectInfo[0]["name"],
				"envName":         projectInfo[0]["env"],
				"instituteInfo":   instituteInfo,
				"instituteInfoEn": instituteInfoEn,
				"freezeNumber":    medicineFreeze.Number,
			}

			// 获取用户的邮箱
			var siteOrStoreIDs = []primitive.ObjectID{medicineFreeze.InstituteID}
			userMail, err := tools.GetRoleUsersMailWithRole(medicineFreeze.ProjectID, medicineFreeze.EnvironmentID, "notice.medicine.isolation", siteOrStoreIDs...)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			var noticeConfig models.NoticeConfig
			err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": medicineFreeze.EnvironmentID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}
			langList := make([]string, 0)
			html := "medicine_release_new_zh_en.html"
			if noticeConfig.Manual != 0 {
				if noticeConfig.Manual == 1 {
					langList = append(langList, "zh")
					html = "medicine_release_new_zh.html"
				} else if noticeConfig.Manual == 2 {
					langList = append(langList, "en")
					html = "medicine_release_new_en.html"
				} else if noticeConfig.Manual == 3 {
					langList = append(langList, "zh")
					langList = append(langList, "en")
					html = "medicine_release_new_zh_en.html"
				}
			} else {
				langList = append(langList, ctx.GetHeader("Accept-Language"))
				if locales.Lang(ctx) == "zh" {
					html = "medicine_release_new_zh.html"
				} else if locales.Lang(ctx) == "en" {
					html = "medicine_release_new_en.html"
				}
			}

			if len(userMail) > 0 {
				mailBodyContet, err := tools.MailBodyContent(nil, envOID, "notice.medicine.isolation")
				for key, v := range mailBodyContet {
					contentData[key] = v
					blindContent[key] = v
				}
				if err != nil {
					return primitive.NilObjectID, errors.WithStack(err)
				}
				for _, userEmail := range userMail {
					sendContent := contentData
					if userEmail.IsBlind {
						sendContent = blindContent
					}
					var toUserMail []string
					toUserMail = append(toUserMail, userEmail.Email)
					mails = append(mails, models.Mail{
						ID:          primitive.NewObjectID(),
						Subject:     "medicine.release.title",
						SubjectData: subjectData,
						//Content:      "medicine.freeze.content",
						ContentData:  sendContent,
						To:           toUserMail, // test测试
						Lang:         ctx.GetHeader("Accept-Language"),
						LangList:     langList,
						Status:       0,
						CreatedTime:  time.Duration(time.Now().Unix()),
						ExpectedTime: time.Duration(time.Now().Unix()),
						SendTime:     time.Duration(time.Now().Unix()),
						HTML:         html,
					})
				}
				ctx.Set("MAIL", mails)
			}
			if len(mails) > 0 {
				var envs []models.MailEnv
				for _, m := range mails {
					envs = append(envs, models.MailEnv{
						ID:         primitive.NewObjectID(),
						MailID:     m.ID,
						CustomerID: project.CustomerID,
						ProjectID:  project.ID,
						EnvID:      envOID,
						//CohortID:   cohortOID,
					})
				}
				ctx.Set("MAIL-ENV", envs)
			}
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

// 药物研究产品名称 批次号 有效期去重
func drugAttributeDeDuplication(sctx mongo.SessionContext, medicineIds []primitive.ObjectID) (string, string, string) {
	// 研究产品名称
	medicineName := ""
	// 批次号
	batchNumber := ""
	// 有效期
	expirationDate := ""
	var m []models.Medicine
	filter := bson.M{"_id": bson.M{"$in": medicineIds}}
	cursor, _ := tools.Database.Collection("medicine").Find(sctx, filter)
	cursor.All(sctx, &m)
	for i := 0; i < len(m); i++ {
		medicineNameRepeat := false
		batchNumberRepeat := false
		expirationDateRepeat := false
		for j := i + 1; j < len(m); j++ {
			if m[i].Name == m[j].Name {
				medicineNameRepeat = true
				break
			}
		}
		for j := i + 1; j < len(m); j++ {
			if m[i].BatchNumber == m[j].BatchNumber {
				batchNumberRepeat = true
				break
			}
		}
		for j := i + 1; j < len(m); j++ {
			if m[i].ExpirationDate == m[j].ExpirationDate {
				expirationDateRepeat = true
				break
			}
		}
		if !medicineNameRepeat {
			medicineName += m[i].Name + " "
		}
		if !batchNumberRepeat {
			batchNumber += m[i].BatchNumber + " "
		}
		if !expirationDateRepeat {
			expirationDate += m[i].ExpirationDate + " "
		}
	}
	return medicineName, batchNumber, expirationDate
}

func (s *MedicineService) getFreezeNumber(ctx *gin.Context) (string, error) {
	var orderNumber string
	var data []models.MedicineFreeze
	pipepine := mongo.Pipeline{
		{{Key: "$sort", Value: bson.D{{"number", -1}}}},
		{{Key: "$limit", Value: 1}},
	}
	cursor, err := tools.Database.Collection("medicine_freeze").Aggregate(nil, pipepine)
	if err != nil {
		return "", errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return "", errors.WithStack(err)
	}

	if data != nil {
		maxOrderNumber := data[0].Number
		maxNumber, _ := strconv.Atoi(maxOrderNumber)
		orderNumber = strconv.Itoa(maxNumber + 1)
	} else {
		orderNumber = "10001"
	}

	return orderNumber, nil
}

// GetTemplateFile 研究产品上传模板下载
func (s *MedicineService) GetTemplateFile(ctx *gin.Context, customerID string, envID string) error {
	envOID, _ := primitive.ObjectIDFromHex(envID)

	isOpenPackage, _, _, _, _, _ := tools.IsOpenPackage(envOID)
	fileName := fmt.Sprintf("Source_IP_Template_%s.xlsx", ctx.GetHeader("Accept-Language"))
	if isOpenPackage { //包装运输勾选
		fileName = fmt.Sprintf("Source_IP_Package_Template_%s.xlsx", ctx.GetHeader("Accept-Language"))
	}

	file, err := templates.Templates.ReadFile(fileName)
	if err != nil {
		return errors.WithStack(err)
	}
	if err := ioutil.WriteFile(fileName, file, 0666); err != nil {
		return errors.WithStack(err)

	}
	defer func(name string) {
		_ = os.Remove(name)
	}(fileName)
	ctx.Header("Content-Type",
		"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.FileAttachment(fileName, fileName)
	return nil
}

// GetPacklistTemplateFile 研究产品包装清单模板下载
func (s *MedicineService) GetPacklistTemplateFile(ctx *gin.Context) error {
	fileName := fmt.Sprintf("packlistTemplate_%s.xlsx", ctx.GetHeader("Accept-Language"))
	file, err := templates.Templates.ReadFile(fileName)
	if err != nil {
		return errors.WithStack(err)

	}
	if err := ioutil.WriteFile(fileName, file, 0666); err != nil {
		return errors.WithStack(err)

	}
	defer func(name string) {
		_ = os.Remove(name)
	}(fileName)
	ctx.Header("Content-Type",
		"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.FileAttachment(fileName, fileName)
	return nil
}

func (s *MedicineService) DownloadData(ctx *gin.Context, customerID string, projectID string, envID string, attributeId string, roleId string) error {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	filter := bson.M{"customer_id": customerOID, "env_id": envOID}
	var medicineData []models.Medicine
	optTrue := true
	opt := &options.AggregateOptions{
		AllowDiskUse: &optTrue,
	}
	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		models.MedicineProject,
		{{Key: "$sort", Value: bson.D{{"serial_number", 1}}}},
	}
	cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pipepine, opt)
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &medicineData)
	if err != nil {
		return errors.WithStack(err)
	}

	// attribute, err := database.GetAttribute(nil, attributeId)
	// if err != nil {
	// 	return err
	// }
	isBlindedRole := false
	// if attribute.AttributeInfo.Blind {
	isBlindedRole, err = tools.IsBlindedRole(roleId)
	// 	if err != nil {
	// 		return err
	// 	}
	// }

	//写入excel文件
	var title []interface{}
	title = append(title, locales.Tr(ctx, "medicine_name"))
	title = append(title, locales.Tr(ctx, "medicine_serial_number"))
	title = append(title, locales.Tr(ctx, "medicine_number"))
	title = append(title, locales.Tr(ctx, "medicine_expiration_date"))
	title = append(title, locales.Tr(ctx, "medicine_batch_number"))
	title = append(title, locales.Tr(ctx, "medicine_status"))
	content := make([][]interface{}, len(medicineData))
	isBlindDrugMap, _ := tools.IsBlindDrugMap(envOID)
	for i := 0; i < len(medicineData); i++ {
		var row []interface{}
		if isBlindedRole && isBlindDrugMap[medicineData[i].Name] {
			row = append(row, tools.BlindData)
		} else {
			row = append(row, medicineData[i].Name)
		}
		row = append(row, medicineData[i].SerialNumber)
		row = append(row, medicineData[i].Number)
		row = append(row, medicineData[i].ExpirationDate)
		row = append(row, medicineData[i].BatchNumber)
		row = append(row, data.GetMedicineStatus(ctx, medicineData[i].Status))
		content[i] = row
	}

	var projectInfo []map[string]interface{}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": projectOID}}},
		{{Key: "$unwind", Value: "$envs"}},
		{{Key: "$match", Value: bson.M{"envs.id": envOID}}},
		{
			{
				Key: "$project",
				Value: bson.M{
					"_id":      0,
					"number":   "$info.number",
					"name":     "$info.name",
					"env":      "$envs.name",
					"timeZone": "$info.timeZoneStr",
				},
			},
		},
	}
	cursor, err = tools.Database.Collection("project").Aggregate(nil, pipeline)
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &projectInfo)
	if err != nil {
		return errors.WithStack(err)
	}

	var timeZone float64
	if projectInfo[0]["timeZone"] != nil {
		//timeZone = projectInfo[0]["timeZone"].(int32)
		timeZone, _ = strconv.ParseFloat(projectInfo[0]["timeZone"].(string), 64)
	} else {
		timeZone = float64(8)
	}
	hours := time.Duration(timeZone)
	minutes := time.Duration((timeZone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute

	now := time.Now().UTC().Add(duration).Format("20060102")

	number := ""
	env := ""
	if len(projectInfo) > 0 {
		number = projectInfo[0]["number"].(string)
		env = projectInfo[0]["env"].(string)
	}

	fileName := fmt.Sprintf("%s[%s]—%s—%s.xlsx", number, env, locales.Tr(ctx, "medicine_list_download_name"), now)
	err = tools.ExportExcelStream(ctx, fileName, title, content)
	if err != nil {
		return err
	}
	return nil
}

func (s *MedicineService) GetSummaryStorehouse(ctx *gin.Context) (map[string]interface{}, error) {
	var params map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&params, binding.JSON)
	envOID, _ := primitive.ObjectIDFromHex(params["envId"].(string))

	match := bson.A{}
	filter := bson.M{
		"env_id":  envOID,
		"deleted": 2,
	}
	var storehouseIDs = params["storehouseId"].([]interface{})
	var roleID = params["roleId"].(string)
	if len(storehouseIDs) > 0 {
		storehouseList := bson.A{}
		for _, shID := range storehouseIDs {
			if shID != "" {
				shOID, _ := primitive.ObjectIDFromHex(shID.(string))
				storehouseList = append(storehouseList, bson.M{"_id": shOID})
			}
		}
		if len(storehouseList) > 0 {
			match = append(match, bson.M{"$or": storehouseList})
		}
	}
	match = append(match, filter)
	var projection = bson.M{
		"_id":             1,
		"customer_id":     1,
		"project_id":      1,
		"env_id":          1,
		"storehouse_id":   1,
		"storehouse":      1,
		"medicine":        1,
		"medicine_others": 1,
	}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"$and": match}}},
	}
	if params["start"] != nil {
		pipeline = append(pipeline, bson.D{{Key: "$skip", Value: params["start"].(float64)}})
	}
	if params["limit"] != nil {
		pipeline = append(pipeline, bson.D{{Key: "$limit", Value: params["limit"].(float64)}})
	}

	statusArray := [4]int{0, 21, 22, 23}
	pipeline = append(pipeline, []bson.D{
		{{Key: "$lookup", Value: bson.M{
			"from": "medicine",
			"let": bson.M{
				"storehouse_id": "$_id",
			},
			"pipeline": bson.A{
				bson.M{
					"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$storehouse_id", "$$storehouse_id"}}, "status": bson.M{"$nin": statusArray}},
				},
				bson.M{
					"$group": bson.M{
						"_id": bson.M{
							"storehouse_id": "$storehouse_id",
							"name":          "$name",
							"status":        "$status",
							"spec":          "$spec",
							"batch_number":  "$batch_number",
						},
						"count": bson.M{"$sum": 1},
					},
				},
				bson.M{"$sort": bson.D{{"_id.storehouse_id", 1}, {"_id.name", 1}, {"_id.batch_number", 1}}},
			},
			"as": "medicine",
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from": "medicine_others",
			"let": bson.M{
				"storehouse_id": "$_id",
			},
			"pipeline": bson.A{
				bson.M{
					"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$storehouse_id", "$$storehouse_id"}}, "status": bson.M{"$nin": statusArray}},
				},
				bson.M{
					"$group": bson.M{
						"_id": bson.M{
							"storehouse_id": "$storehouse_id",
							"name":          "$name",
							"status":        "$status",
							"spec":          "$spec",
							"batch_number":  "$batch_number",
							//			"package_number": "$package_number",
						},
						"count": bson.M{"$sum": 1},
					},
				},
				// bson.M{
				// 	"$group": bson.M{
				// 		"_id": bson.M{
				// 			"storehouse_id": "$_id.storehouse_id",
				// 			"name":          "$_id.name",
				// 			"status":        "$_id.status",
				// 			"spec":          "$_id.spec",
				// 			"batch_number":  "$_id.batch_number",
				// 		},
				// 		"count":        bson.M{"$sum": "$count"},
				// 		"packageCount": bson.M{"$sum": 1},
				// 	},
				// },
				bson.M{"$sort": bson.D{{"_id.storehouse_id", 1}, {"_id.name", 1}, {"_id.batch_number", 1}}},
			},
			"as": "medicine_others",
		}}},
		{{Key: "$lookup", Value: bson.M{"from": "storehouse", "localField": "storehouse_id", "foreignField": "_id", "as": "storehouse"}}},
		{{Key: "$unwind", Value: "$storehouse"}},
		{{Key: "$project", Value: projection}}}...)
	role, err := tools.GetRole(roleID)
	if err != nil {
		return nil, err
	}
	if role.Scope == "site" {
		return map[string]interface{}{"total": 0, "items": make([]interface{}, 0)}, nil
	}
	if role.Scope == "depot" {
		user, err := tools.Me(ctx)
		if err != nil {
			return nil, err
		}
		filter["user_depot.user_id"] = user.ID
		pipeline = mongo.Pipeline{
			{{Key: "$lookup", Value: bson.M{"from": "user_depot", "localField": "_id", "foreignField": "depot_id", "as": "user_depot"}}},
			{{Key: "$unwind", Value: "$user_depot"}},
			{{Key: "$match", Value: bson.M{"$and": match}}},
		}
		if params["start"] != nil {
			pipeline = append(pipeline, bson.D{{Key: "$skip", Value: params["start"].(float64)}})
		}
		if params["limit"] != nil {
			pipeline = append(pipeline, bson.D{{Key: "$limit", Value: params["limit"].(float64)}})
		}
		pipeline = append(pipeline, []bson.D{
			{{Key: "$lookup", Value: bson.M{
				"from": "medicine",
				"let": bson.M{
					"storehouse_id": "$_id",
				},
				"pipeline": bson.A{
					bson.M{
						"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$storehouse_id", "$$storehouse_id"}}, "status": bson.M{"$nin": statusArray}},
					},
					bson.M{
						"$group": bson.M{
							"_id": bson.M{
								"storehouse_id": "$storehouse_id",
								"name":          "$name",
								"status":        "$status",
								"batch_number":  "$batch_number",
								"spec":          "$spec",
							},
							"count": bson.M{"$sum": 1},
						},
					},
					bson.M{"$sort": bson.D{{"_id.storehouse_id", 1}, {"_id.name", 1}, {"_id.batch_number", 1}}},
				},
				"as": "medicine",
			}}},
			{{Key: "$lookup", Value: bson.M{
				"from": "medicine_others",
				"let": bson.M{
					"storehouse_id": "$_id",
				},
				"pipeline": bson.A{
					bson.M{
						"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$storehouse_id", "$$storehouse_id"}}, "status": bson.M{"$nin": statusArray}},
					},
					bson.M{
						"$group": bson.M{
							"_id": bson.M{
								"storehouse_id": "$storehouse_id",
								"name":          "$name",
								"status":        "$status",
								"spec":          "$spec",
								"batch_number":  "$batch_number",
								//			"package_number": "$package_number",
							},
							"count": bson.M{"$sum": 1},
						},
					},
					bson.M{"$sort": bson.D{{"_id.storehouse_id", 1}, {"_id.name", 1}, {"_id.batch_number", 1}}},
					// bson.M{
					// 	"$group": bson.M{
					// 		"_id": bson.M{
					// 			"storehouse_id": "$_id.storehouse_id",
					// 			"name":          "$_id.name",
					// 			"status":        "$_id.status",
					// 			"spec":          "$_id.spec",
					// 			"batch_number":  "$_id.batch_number",
					// 		},
					// 		"count":        bson.M{"$sum": "$count"},
					// 		"packageCount": bson.M{"$sum": 1},
					// 	},
					// },
				},
				"as": "medicine_others",
			}}},
			{{Key: "$lookup", Value: bson.M{"from": "storehouse", "localField": "storehouse_id", "foreignField": "_id", "as": "storehouse"}}},
			{{Key: "$unwind", Value: "$storehouse"}},
			{{Key: "$project", Value: projection}},
		}...)
	}
	collection := tools.Database.Collection("project_storehouse")
	total, err := collection.CountDocuments(nil, bson.M{"$and": match})

	var d []map[string]interface{}
	cursor, err := collection.Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &d)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	//包装的研究产品
	isOpenPackage, packageAllDrugNames, _, _, _, err := tools.IsOpenPackage(envOID)
	if err != nil {
		return nil, err
	}

	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return nil, err
	}

	for _, storehouse := range d {
		if isBlindedRole && storehouse["medicine"] != nil {

			for _, med := range storehouse["medicine"].(primitive.A) {
				name := med.(map[string]interface{})["_id"].(map[string]interface{})["name"].(string)
				isBlindedDrug, _ := tools.IsBlindedDrug(envOID, name)
				if isBlindedDrug {
					med.(map[string]interface{})["_id"].(map[string]interface{})["name"] = tools.BlindData
				}
			}

		}
		if storehouse["medicine_others"] != nil {
			for _, other := range storehouse["medicine_others"].(primitive.A) {
				name := other.(map[string]interface{})["_id"].(map[string]interface{})["name"].(string)
				isBlindedDrug, _ := tools.IsBlindedDrug(envOID, name)
				if isBlindedDrug && isBlindedRole {
					other.(map[string]interface{})["_id"].(map[string]interface{})["name"] = tools.BlindData
				}
				if isOpenPackage && packageAllDrugNames[name] > 0 {
					other.(map[string]interface{})["_id"].(map[string]interface{})["isOpenPackage"] = true
					other.(map[string]interface{})["_id"].(map[string]interface{})["packageNumber"] = packageAllDrugNames[name]
				}
			}
		}

	}

	//}
	return map[string]interface{}{"total": total, "items": d}, nil
}

func (s *MedicineService) GetOtherMedicineCount(ctx *gin.Context) (map[string]interface{}, error) {
	customerID := ctx.Query("customerId")
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)

	otherMedicineCount := make(map[string]int32, 0)
	var otherMedicineData []map[string]interface{}
	otherFilter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID,
		"$or": bson.A{
			bson.M{"package_number": nil},
			bson.M{"package_number": ""},
		},
		"$and": bson.A{
			bson.M{"storehouse_id": bson.M{"$ne": nil}},
			bson.M{"storehouse_id": bson.M{"$ne": ""}},
			bson.M{"storehouse_id": bson.M{"$ne": primitive.NilObjectID}},
		},
	}
	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: otherFilter}},
		{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name"}, "count": bson.M{"$sum": 1}}}},
		{{Key: "$project", Value: bson.M{
			"name":  "$_id.name",
			"count": 1,
		}}},
	}
	cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, pipepine)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &otherMedicineData)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	for _, otherMedicine := range otherMedicineData {
		otherMedicineCount[otherMedicine["name"].(string)] = otherMedicine["count"].(int32)
	}
	return map[string]interface{}{"otherCount": otherMedicineCount}, nil
}

func (s *MedicineService) GetMedicineStorehouseSku(ctx *gin.Context, req models.MedicineSkuReq) (map[string]interface{}, error) {
	// customerID := ctx.Query("customerId")
	// projectID := ctx.Query("projectId")
	// envID := ctx.Query("envId")
	customerOID := req.CustomerID //primitive.ObjectIDFromHex(customerID)
	envOID := req.EnvID           //primitive.ObjectIDFromHex(envID)
	projectOID := req.ProjectID   // primitive.ObjectIDFromHex(projectID)

	field := req.Field           //ctx.Query("field")
	fieldValue := req.FieldValue //ctx.Query("fieldValue")

	start := req.Start
	limit := req.Limit

	// start, _ := strconv.Atoi(ctx.DefaultQuery("start", "0"))
	// limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	// customerOID, _ := primitive.ObjectIDFromHex(customerID)
	// envOID, _ := primitive.ObjectIDFromHex(envID)
	// projectOID, _ := primitive.ObjectIDFromHex(projectID)
	// roleID := ctx.Query("roleId")
	// roleOID, _ := primitive.ObjectIDFromHex(roleID)
	roleOID := req.RoleID
	role, err := tools.GetRole(roleOID.Hex())
	if err != nil {
		return nil, errors.WithStack(err)
	}
	isOenPackage, _, _, _, _, _ := tools.IsOpenPackage(envOID)
	if role.Scope == "site" {
		return map[string]interface{}{"total": 0, "items": make([]interface{}, 0), "isOpenPackage": false}, nil
	}

	storehouseID := req.StorehouseIDs
	status := req.Status
	//storehouseID := ctx.Query("storehouseId")
	//cohortID := ctx.Query("cohortId")
	//status := ctx.Query("status")
	// field := ctx.Query("field")
	// fieldValue := ctx.Query("fieldValue")
	match := bson.A{}
	filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if len(storehouseID) <= 0 { //查询全部仓库
		//判断当前登录用户的角色，如果是study，查询正常状态下的库房，如果是depot，查询绑定的仓库
		storehouseIDs := make([]primitive.ObjectID, 0)
		if role.Scope == "study" {
			match := bson.M{
				"customer_id": customerOID,
				"project_id":  projectOID,
				"env_id":      envOID,
				"deleted":     2,
			}
			var data []models.ProjectStorehouse
			cursor, err := tools.Database.Collection("project_storehouse").Find(nil, match)
			if err != nil {
				return nil, errors.WithStack(err)

			}
			if err := cursor.All(nil, &data); err != nil {
				return nil, errors.WithStack(err)
			}
			for _, projectStorehouse := range data {
				storehouseIDs = append(storehouseIDs, projectStorehouse.ID)
			}
		} else if role.Scope == "depot" {
			u, _ := ctx.Get("user")
			user := u.(models.User)
			match := bson.M{
				"customer_id": customerOID,
				"project_id":  projectOID,
				"env_id":      envOID,
				"user_id":     user.ID,
			}
			var userDepots []models.UserDepot
			cursor, err := tools.Database.Collection("user_depot").Find(nil, match)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &userDepots)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			for _, userDepot := range userDepots {
				storehouseIDs = append(storehouseIDs, userDepot.DepotID)
			}
		}
		filter["storehouse_id"] = bson.M{"$in": storehouseIDs}
	} else {
		// storehouseOID, _ := primitive.ObjectIDFromHex(storehouseID)
		// filter["storehouse_id"] = storehouseOID
		filter["storehouse_id"] = bson.M{"$in": storehouseID}
	}

	filter["status"] = bson.M{"$nin": [4]int{0, 21, 22, 23}}
	if status != "" && status != "undefined" {
		filter["status"], _ = strconv.Atoi(status)
	}
	isBlindedRole, err := tools.IsBlindedRole(roleOID.Hex())
	if err != nil {
		return nil, err
	}

	IsBlindDrugMap, err := tools.IsBlindDrugMap(envOID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	match = append(match, filter)
	var orderNumber = ""
	if field != "" && fieldValue != "" {
		if field != "order_number" {
			searchList := strings.Split(fieldValue, ",")
			multiFilter := bson.A{}
			for _, searchCondition := range searchList {
				if field != "name" {
					multiFilter = append(multiFilter, bson.M{field: bson.M{"$regex": tools.EscapeSpecialWord(searchCondition), "$options": "im"}})
				} else {
					notIncluded := make([]string, 0)
					if isBlindedRole {
						// 遍历map
						for key, value := range IsBlindDrugMap {
							//fmt.Printf("Key: %s, Value: %d\n", key, value)
							if value {
								notIncluded = append(notIncluded, key)
							}
						}
					}
					//multiFilter = append(multiFilter, bson.M{field: bson.M{"$regex": "fever", "$options": "im", "$not": bson.M{"$regex": "cold", "$options": "im"}}})
					multiFilter = append(multiFilter, bson.M{field: bson.M{"$regex": tools.EscapeSpecialWord(searchCondition), "$options": "im", "$not": bson.M{"$in": notIncluded}}})
				}
			}
			if len(multiFilter) > 0 {
				match = append(match, bson.M{"$or": multiFilter})
			}
		} else {
			orderNumber = fieldValue
		}
	}
	medicinesOrder, err := s.GetMedicineOrder(customerOID, projectOID, envOID, orderNumber)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if orderNumber != "" {
		filter["_id"] = bson.M{"$in": medicinesOrder["medicines"].([]primitive.ObjectID)}
	}

	totalPipeline := bson.M{"$and": match}
	total, err := tools.Database.Collection("medicine").CountDocuments(nil, totalPipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	pagePipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"$and": match}}},
		{{Key: "$sort", Value: bson.D{{"number", 1}}}},
		{{Key: "$skip", Value: start}},
		{{Key: "$limit", Value: limit}},
		{{Key: "$lookup", Value: bson.M{"from": "project_storehouse", "localField": "storehouse_id", "foreignField": "_id", "as": "project_storehouse"}}},
		{{Key: "$unwind", Value: "$project_storehouse"}},
		{{Key: "$lookup", Value: bson.M{"from": "storehouse", "localField": "project_storehouse.storehouse_id", "foreignField": "_id", "as": "storehouse"}}},
		{{Key: "$unwind", Value: "$storehouse"}},
		{{
			Key: "$project",
			Value: bson.M{
				"id":             "$_id",
				"_id":            0,
				"storehouseName": "$storehouse.name",
				"place":          "$project_storehouse.address",
				"number":         1,
				"name":           1,
				"batchNumber":    "$batch_number",
				"expirationDate": "$expiration_date",
				"status":         1,
				"packageNumber":  "$package_number",
				"shortCode":      "$short_code",
			},
		}},
	}

	var data []map[string]interface{}
	optTrue := true
	opt := &options.AggregateOptions{
		AllowDiskUse: &optTrue,
	}
	cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pagePipeline, opt)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// attribute, err := s.GetAttribute(ctx, customerOID.Hex(), projectOID.Hex(), envOID.Hex(), cohortID)
	// if err != nil {
	// 	return nil, errors.WithStack(err)
	// }
	for _, item := range data {
		item["orderNumber"] = medicinesOrder["medicineOrderMap"].(map[primitive.ObjectID][]string)[item["id"].(primitive.ObjectID)]
		// isOpenDrug, _ := tools.IsOpenDrug(envOID, attribute.CohortID, item["name"].(string))
		// isOtherDrug, _ := tools.IsOtherDrug(envOID, item["name"].(string))
		// if attribute.AttributeInfo.Blind && isBlindedRole && !isOpenDrug && !isOtherDrug {
		// 	item["name"] = tools.BlindData
		// }
		// if isBlindedRole {
		// 	isBlindedDrug, _ := tools.IsBlindedDrug(envOID, item["name"].(string))
		// 	if isBlindedDrug {
		// 		item["name"] = tools.BlindData
		// 	}
		// }
		if isBlindedRole && IsBlindDrugMap[item["name"].(string)] {
			item["name"] = tools.BlindData
		}
	}
	return map[string]interface{}{"total": total, "items": data, "isOpenPackage": isOenPackage}, nil
}

// func (s *MedicineService) GetMedicineSiteSku(ctx *gin.Context, customerID string, projectID string, envID string, roleID string, start int, limit int) (map[string]interface{}, error) {
func (s *MedicineService) GetMedicineSiteSku(ctx *gin.Context, req models.MedicineSkuReq) (map[string]interface{}, error) {
	// customerOID, _ := primitive.ObjectIDFromHex(customerID)
	// envOID, _ := primitive.ObjectIDFromHex(envID)
	// projectOID, _ := primitive.ObjectIDFromHex(projectID)

	isOpenPackage, _, _, _, _, _ := tools.IsOpenPackage(req.EnvID)

	role, err := tools.GetRole(req.RoleID.Hex())
	if err != nil {
		tools.Response(ctx, err)
		return nil, errors.WithStack(err)
	}
	if role.Scope == "depot" {
		return map[string]interface{}{"total": 0, "items": make([]interface{}, 0), "isOpenPackage": isOpenPackage}, nil
	}
	siteID := req.SiteIDs //ctx.Query("siteIds")
	//cohortID := ctx.Query("cohortId")
	status := req.Status         //ctx.Query("status")
	field := req.Field           //ctx.Query("field")
	fieldValue := req.FieldValue //ctx.Query("fieldValue")
	match := bson.A{}
	filter := bson.M{"customer_id": req.CustomerID, "project_id": req.ProjectID, "env_id": req.EnvID}
	if len(siteID) > 0 {
		//siteOID, _ := primitive.ObjectIDFromHex(siteID)
		//filter["site_id"] = siteOID
		filter["site_id"] = bson.M{"$in": siteID}
	} else {
		//判断当前登录用户的角色，如果是study，查询正常状态下的中心，如果是site，查询绑定的中心
		siteIDs := make([]primitive.ObjectID, 0)
		if role.Scope == "study" {
			match := bson.M{
				"customer_id": req.CustomerID,
				"project_id":  req.ProjectID,
				"env_id":      req.EnvID,
				"deleted":     2,
			}
			var data []models.ProjectSite
			cursor, err := tools.Database.Collection("project_site").Find(nil, match)
			if err != nil {
				return nil, errors.WithStack(err)

			}
			if err := cursor.All(nil, &data); err != nil {
				return nil, errors.WithStack(err)
			}
			for _, projectSite := range data {
				siteIDs = append(siteIDs, projectSite.ID)
			}
		} else if role.Scope == "site" {
			u, _ := ctx.Get("user")
			user := u.(models.User)
			match := bson.M{
				"customer_id": req.CustomerID,
				"project_id":  req.ProjectID,
				"env_id":      req.EnvID,
				"user_id":     user.ID,
			}
			var userSites []models.UserSite
			cursor, err := tools.Database.Collection("user_site").Find(nil, match)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &userSites)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			for _, userSite := range userSites {
				siteIDs = append(siteIDs, userSite.SiteID)
			}
		}
		filter["site_id"] = bson.M{"$in": siteIDs}
	}

	if status != "" && status != "undefined" {
		filter["status"], _ = strconv.Atoi(status)
	}

	match = append(match, filter)

	isBlindedRole, err := tools.IsBlindedRole(req.RoleID.Hex())
	if err != nil {
		return nil, err
	}
	IsBlindDrugMap, err := tools.IsBlindDrugMap(req.EnvID)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var orderNumber = ""
	if field != "" && fieldValue != "" {
		if field != "order_number" {
			searchList := strings.Split(fieldValue, ",")
			multiFilter := bson.A{}
			for _, searchCondition := range searchList {
				if field != "name" {
					multiFilter = append(multiFilter, bson.M{field: bson.M{"$regex": tools.EscapeSpecialWord(searchCondition), "$options": "im"}})
				} else {
					notIncluded := make([]string, 0)
					if isBlindedRole {
						// 遍历map
						for key, value := range IsBlindDrugMap {
							//fmt.Printf("Key: %s, Value: %d\n", key, value)
							if value {
								notIncluded = append(notIncluded, key)
							}
						}
					}
					//multiFilter = append(multiFilter, bson.M{field: bson.M{"$regex": "fever", "$options": "im", "$not": bson.M{"$regex": "cold", "$options": "im"}}})
					multiFilter = append(multiFilter, bson.M{field: bson.M{"$regex": tools.EscapeSpecialWord(searchCondition), "$options": "im", "$not": bson.M{"$in": notIncluded}}})
				}
			}
			if len(multiFilter) > 0 {
				match = append(match, bson.M{"$or": multiFilter})
			}
		} else {
			orderNumber = fieldValue
		}
	}
	totalPipeline := bson.M{"$and": match}
	total, err := tools.Database.Collection("medicine").CountDocuments(nil, totalPipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	pagePipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"$and": match}}},
		{{Key: "$sort", Value: bson.D{{"project_site.number", 1}, {"number", 1}}}},
		{{Key: "$skip", Value: req.Start}},
		{{Key: "$limit", Value: req.Limit}},
		{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "site_id", "foreignField": "_id", "as": "project_site"}}},
		{{Key: "$unwind", Value: "$project_site"}},
		{{
			Key: "$project",
			Value: bson.M{
				"id":  "$_id",
				"_id": 0,
				"siteName": bson.M{"$concat": bson.A{"$project_site.number", "-",
					models.ProjectSiteNameLookUpBson(ctx),
				}},
				"place":          "$project_site.address",
				"number":         1,
				"name":           1,
				"batchNumber":    "$batch_number",
				"expirationDate": "$expiration_date",
				"timeZone":       "$project_site.time_zone",
				"tz":             "$project_site.tz",
				"status":         1,
				"packageNumber":  "$package_number",
				"shortCode":      "$short_code",
			},
		}},
	}

	medicinesOrder, err := s.GetMedicineOrder(req.CustomerID, req.ProjectID, req.EnvID, orderNumber)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if orderNumber != "" {
		filter["_id"] = bson.M{"$in": medicinesOrder["medicines"].([]primitive.ObjectID)}
	}

	var data []map[string]interface{}
	optTrue := true
	opt := &options.AggregateOptions{
		AllowDiskUse: &optTrue,
	}
	cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pagePipeline, opt)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	for _, item := range data {
		item["orderNumber"] = medicinesOrder["medicineOrderMap"].(map[primitive.ObjectID][]string)[item["id"].(primitive.ObjectID)]
		if isBlindedRole && IsBlindDrugMap[item["name"].(string)] {
			item["name"] = tools.BlindData
		}
	}

	return map[string]interface{}{"total": total, "items": data, "isOpenPackage": isOpenPackage}, nil
}

func (s *MedicineService) GetMedicineDtpSku(ctx *gin.Context, customerID string, projectID string, envID string, roleID string, start int, limit int) (map[string]interface{}, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	//cohortID := ctx.Query("cohortId")
	status := ctx.Query("status")
	field := ctx.Query("field")
	fieldValue := ctx.Query("fieldValue")
	subjectNumber := ctx.Query("subjectNumber")
	match := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if status != "" {
		match["status"], _ = strconv.Atoi(status)
	}
	var orderNumber = ""
	if field != "" && fieldValue != "" {
		if field != "order_number" {
			searchList := strings.Split(fieldValue, ",")
			multiFilter := bson.A{}
			for _, searchCondition := range searchList {
				multiFilter = append(multiFilter, bson.M{field: bson.M{"$regex": tools.EscapeSpecialWord(searchCondition), "$options": "im"}})
			}
			if len(multiFilter) > 0 {
				match["$or"] = multiFilter
			}
		} else {
			orderNumber = fieldValue
		}
	}
	me, err := tools.Me(ctx)
	if err != nil {
		return nil, err
	}
	role, err := tools.GetRole(roleID)
	if err != nil {
		return nil, err
	}
	totalPipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{
			"from": "subject", // 关联的集合名称
			"let": bson.M{
				"subject_id": "$subject_id", // 将当前文档的 subject_id 赋值给变量 subject_id
			},
			"pipeline": bson.A{ // 使用 pipeline 添加过滤条件
				bson.M{
					"$match": bson.M{
						"$expr": bson.M{
							"$and": bson.A{
								bson.M{"$eq": bson.A{"$_id", "$$subject_id"}}, // 关联条件：_id 等于 subject_id
								bson.M{"$ne": bson.A{"$deleted", true}},       // 过滤条件：deleted 不为 true
							},
						},
					},
				},
			},
			"as": "subject", // 关联结果的字段名
		}}},
		{{Key: "$unwind", Value: "$subject"}},
		{{Key: "$unwind", Value: "$subject.info"}},
		{{Key: "$match", Value: bson.M{"subject.info.name": "shortname", "subject.info.value": bson.M{"$regex": subjectNumber}}}},
		{{Key: "$lookup", Value: bson.M{"from": "medicine_order", "localField": "order_id", "foreignField": "_id", "as": "medicine_order"}}},
		{{Key: "$unwind", Value: "$medicine_order"}},
		{{Key: "$match", Value: bson.M{"medicine_order.order_number": bson.M{"$regex": orderNumber}}}},
		{{Key: "$lookup", Value: bson.M{"from": "dispensing", "localField": "medicine_order.dispensing_id", "foreignField": "_id", "as": "dispensing"}}},
		{{Key: "$unwind", Value: "$dispensing"}},
		{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "subject.project_site_id", "foreignField": "_id", "as": "project_site"}}},
		{{Key: "$unwind", Value: "$project_site"}},
		{{Key: "$match", Value: bson.M{"project_site.deleted": 2}}},
		{{Key: "$match", Value: bson.M{"status": bson.M{"$ne": 1}}}},
		{{Key: "$count", Value: "count"}},
	}
	pagePipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{
			"from": "subject", // 关联的集合名称
			"let": bson.M{
				"subject_id": "$subject_id", // 将当前文档的 subject_id 赋值给变量 subject_id
			},
			"pipeline": bson.A{ // 使用 pipeline 添加过滤条件
				bson.M{
					"$match": bson.M{
						"$expr": bson.M{
							"$and": bson.A{
								bson.M{"$eq": bson.A{"$_id", "$$subject_id"}}, // 关联条件：_id 等于 subject_id
								bson.M{"$ne": bson.A{"$deleted", true}},       // 过滤条件：deleted 不为 true
							},
						},
					},
				},
			},
			"as": "subject", // 关联结果的字段名
		}}},
		{{Key: "$unwind", Value: "$subject"}},
		{{Key: "$unwind", Value: "$subject.info"}},
		{{Key: "$match", Value: bson.M{"subject.info.name": "shortname", "subject.info.value": bson.M{"$regex": subjectNumber}}}},
		{{Key: "$lookup", Value: bson.M{"from": "medicine_order", "localField": "order_id", "foreignField": "_id", "as": "medicine_order"}}},
		{{Key: "$unwind", Value: "$medicine_order"}},
		{{Key: "$match", Value: bson.M{"medicine_order.order_number": bson.M{"$regex": orderNumber}}}},
		{{Key: "$lookup", Value: bson.M{"from": "dispensing", "localField": "medicine_order.dispensing_id", "foreignField": "_id", "as": "dispensing"}}},
		{{Key: "$unwind", Value: "$dispensing"}},
		{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "subject.project_site_id", "foreignField": "_id", "as": "project_site"}}},
		{{Key: "$unwind", Value: "$project_site"}},
		{{Key: "$match", Value: bson.M{"project_site.deleted": 2}}},
		{{Key: "$match", Value: bson.M{"status": bson.M{"$ne": 1}}}},
		{{Key: "$sort", Value: bson.D{{"number", 1}}}},
		{{Key: "$skip", Value: start}},
		{{Key: "$limit", Value: limit}},
		{{
			Key: "$project",
			Value: bson.M{
				"id":            "$_id",
				"_id":           0,
				"subjectNumber": "$subject.info.value",
				"orderNumber":   "$medicine_order.order_number",
				"visitNumber":   "$dispensing.visit_info.name",
				"visitSign":     "$dispensing.visit_sign",
				"reissue":       "$dispensing.reissue",
				"number":        1,
				"name":          1,

				"siteName":       models.ProjectSiteNameLookUpBson(ctx),
				"batchNumber":    "$batch_number",
				"expirationDate": "$expiration_date",
				"status":         1,
			},
		}},
	}

	if role.Scope == "site" {
		totalPipeline = mongo.Pipeline{
			{{Key: "$match", Value: match}},
			{{Key: "$lookup", Value: bson.M{
				"from": "subject", // 关联的集合名称
				"let": bson.M{
					"subject_id": "$subject_id", // 将当前文档的 subject_id 赋值给变量 subject_id
				},
				"pipeline": bson.A{ // 使用 pipeline 添加过滤条件
					bson.M{
						"$match": bson.M{
							"$expr": bson.M{
								"$and": bson.A{
									bson.M{"$eq": bson.A{"$_id", "$$subject_id"}}, // 关联条件：_id 等于 subject_id
									bson.M{"$ne": bson.A{"$deleted", true}},       // 过滤条件：deleted 不为 true
								},
							},
						},
					},
				},
				"as": "subject", // 关联结果的字段名
			}}},
			{{Key: "$unwind", Value: "$subject"}},
			{{Key: "$unwind", Value: "$subject.info"}},
			{{Key: "$match", Value: bson.M{"subject.info.name": "shortname", "subject.info.value": bson.M{"$regex": subjectNumber}}}},
			{{Key: "$lookup", Value: bson.M{"from": "medicine_order", "localField": "order_id", "foreignField": "_id", "as": "medicine_order"}}},
			{{Key: "$unwind", Value: "$medicine_order"}},
			{{Key: "$match", Value: bson.M{"medicine_order.order_number": bson.M{"$regex": orderNumber}}}},
			{{Key: "$lookup", Value: bson.M{"from": "dispensing", "localField": "medicine_order.dispensing_id", "foreignField": "_id", "as": "dispensing"}}},
			{{Key: "$unwind", Value: "$dispensing"}},
			{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "subject.project_site_id", "foreignField": "_id", "as": "project_site"}}},
			{{Key: "$unwind", Value: "$project_site"}},
			{{Key: "$match", Value: bson.M{"project_site.deleted": 2}}},
			{{Key: "$match", Value: bson.M{"status": bson.M{"$ne": 1}}}},
			{{Key: "$lookup", Value: bson.M{"from": "user_site", "localField": "medicine_order.receive_id", "foreignField": "site_id", "as": "user_site"}}},
			{{Key: "$unwind", Value: "$user_site"}},
			{{Key: "$match", Value: bson.M{"user_site.user_id": me.ID}}},
			{{Key: "$count", Value: "count"}},
		}
		pagePipeline = mongo.Pipeline{
			{{Key: "$match", Value: match}},
			{{Key: "$lookup", Value: bson.M{
				"from": "subject", // 关联的集合名称
				"let": bson.M{
					"subject_id": "$subject_id", // 将当前文档的 subject_id 赋值给变量 subject_id
				},
				"pipeline": bson.A{ // 使用 pipeline 添加过滤条件
					bson.M{
						"$match": bson.M{
							"$expr": bson.M{
								"$and": bson.A{
									bson.M{"$eq": bson.A{"$_id", "$$subject_id"}}, // 关联条件：_id 等于 subject_id
									bson.M{"$ne": bson.A{"$deleted", true}},       // 过滤条件：deleted 不为 true
								},
							},
						},
					},
				},
				"as": "subject", // 关联结果的字段名
			}}},
			{{Key: "$unwind", Value: "$subject"}},
			{{Key: "$unwind", Value: "$subject.info"}},
			{{Key: "$match", Value: bson.M{"subject.info.name": "shortname", "subject.info.value": bson.M{"$regex": subjectNumber}}}},
			{{Key: "$lookup", Value: bson.M{"from": "medicine_order", "localField": "order_id", "foreignField": "_id", "as": "medicine_order"}}},
			{{Key: "$unwind", Value: "$medicine_order"}},
			{{Key: "$match", Value: bson.M{"medicine_order.order_number": bson.M{"$regex": orderNumber}}}},
			{{Key: "$lookup", Value: bson.M{"from": "dispensing", "localField": "medicine_order.dispensing_id", "foreignField": "_id", "as": "dispensing"}}},
			{{Key: "$unwind", Value: "$dispensing"}},
			{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "subject.project_site_id", "foreignField": "_id", "as": "project_site"}}},
			{{Key: "$unwind", Value: "$project_site"}},
			{{Key: "$match", Value: bson.M{"project_site.deleted": 2}}},
			{{Key: "$match", Value: bson.M{"status": bson.M{"$ne": 1}}}},
			{{Key: "$lookup", Value: bson.M{"from": "user_site", "localField": "medicine_order.receive_id", "foreignField": "site_id", "as": "user_site"}}},
			{{Key: "$unwind", Value: "$user_site"}},
			{{Key: "$match", Value: bson.M{"user_site.user_id": me.ID}}},
			{{Key: "$sort", Value: bson.D{{"number", 1}}}},
			{{Key: "$skip", Value: start}},
			{{Key: "$limit", Value: limit}},
			{{
				Key: "$project",
				Value: bson.M{
					"id":            "$_id",
					"_id":           0,
					"subjectNumber": "$subject.info.value",
					"orderNumber":   "$medicine_order.order_number",
					"visitNumber":   "$dispensing.visit_info.name",
					"visitSign":     "$dispensing.visit_sign",
					"reissue":       "$dispensing.reissue",
					"number":        1,
					"name":          1,

					"siteName":       models.ProjectSiteNameLookUpBson(ctx),
					"batchNumber":    "$batch_number",
					"expirationDate": "$expiration_date",
					"status":         1,
				},
			}},
		}
	}

	var all []map[string]interface{}
	total, err := tools.Database.Collection("medicine").Aggregate(nil, totalPipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = total.All(nil, &all)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var d []map[string]interface{}
	cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pagePipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &d)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// attribute, err := s.GetAttribute(ctx, customerID, projectID, envID, cohortID)
	// if err != nil {
	// 	return nil, errors.WithStack(err)
	// }
	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return nil, err
	}
	for i := 0; i < len(d); i++ {
		//盲态处理
		isBlindDrug, _ := tools.IsBlindedDrug(envOID, d[i]["name"].(string))
		if err != nil {
			return nil, err
		}
		if isBlindedRole && isBlindDrug {
			d[i]["name"] = tools.BlindData
		}
		// 访视名称-访视类型
		if d[i]["visitSign"].(bool) {
			if int(d[i]["reissue"].(int32)) == 1.0 {
				d[i]["visitNumber"] = d[i]["visitNumber"].(string) + "-" + locales.Tr(ctx, "export.dispensing.reissue")
			} else {
				d[i]["visitNumber"] = d[i]["visitNumber"].(string) + "-" + locales.Tr(ctx, "export.dispensing.outVisit")
			}
		}
	}
	count := 0
	if len(all) > 0 {
		count = int(all[0]["count"].(int32))
	}
	return map[string]interface{}{"total": count, "items": d}, nil
}

// GetAllMedicineStorehouseSTAT 项目概览——库房单品统计(编号和未编号研究产品)
func (s *MedicineService) GetAllMedicineStorehouseSTAT(ctx *gin.Context, customerID string, projectID string, envID string, roleID string) (map[string]interface{}, error) {
	role, err := tools.GetRole(roleID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if role.Scope == "site" {
		return map[string]interface{}{"total": 0, "items": make([]interface{}, 0)}, nil
	}
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)

	//cohortID := ctx.Query("cohortId")
	storehouseID := ctx.Query("storehouseId")

	//关联库房
	var projectStorehouse []models.ProjectStorehouse
	cursor, err := tools.Database.Collection("project_storehouse").Find(nil, bson.M{"env_id": envOID, "deleted": 2})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &projectStorehouse)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	storeIDS := slice.Map(projectStorehouse, func(index int, item models.ProjectStorehouse) primitive.ObjectID {
		return item.ID
	})

	if role.Scope == "depot" {
		user, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var userDepot []models.UserDepot
		depotCursor, err := tools.Database.Collection("user_depot").Find(nil, bson.M{"env_id": envOID, "user_id": user.ID})
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		err = depotCursor.All(nil, &userDepot)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		storeIDS = slice.Filter(storeIDS, func(index int, item primitive.ObjectID) bool {
			_, ok := slice.Find(userDepot, func(index int, depot models.UserDepot) bool {
				return item == depot.ID
			})
			return ok
		})
	}
	if len(storeIDS) == 0 {
		return map[string]interface{}{"total": 0, "items": make([]interface{}, 0)}, nil
	}

	match := bson.M{
		"customer_id": customerOID,
		"project_id":  projectOID,
		"env_id":      envOID,
		"status":      bson.M{"$ne": 0}, // 剔除待入仓的药物
	}
	if storehouseID != "" {
		storehouseOID, _ := primitive.ObjectIDFromHex(storehouseID)
		match["storehouse_id"] = storehouseOID
	} else {
		match["storehouse_id"] = bson.M{"$in": storeIDS}
	}

	medicine, err := AllMedicineStat(match, roleID, envOID)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return map[string]interface{}{"items": medicine}, nil
}

// GetAllMedicineSiteSTAT 项目概览——中心单品统计(编号和未编号研究产品)
func (s *MedicineService) GetAllMedicineSiteSTAT(ctx *gin.Context, customerID string, projectID string, envID string, roleID string) (map[string]interface{}, error) {
	role, err := tools.GetRole(roleID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if role.Scope == "depot" {
		return map[string]interface{}{"total": 0, "items": make([]interface{}, 0)}, nil
	}
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	//关联中心
	var projectSite []models.ProjectSite
	cursor, err := tools.Database.Collection("project_site").Find(nil, bson.M{"env_id": envOID, "deleted": 2})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &projectSite)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	siteOIDS := slice.Map(projectSite, func(index int, item models.ProjectSite) primitive.ObjectID {
		return item.ID
	})

	if role.Scope == "site" {
		user, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var userSite []models.UserSite
		depotCursor, err := tools.Database.Collection("user_site").Find(nil, bson.M{"env_id": envOID, "user_id": user.ID})
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		err = depotCursor.All(nil, &userSite)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		siteOIDS = slice.Filter(siteOIDS, func(index int, item primitive.ObjectID) bool {
			_, ok := slice.Find(userSite, func(index int, site models.UserSite) bool {
				return item == site.ID
			})
			return ok
		})
	}
	if len(siteOIDS) == 0 {
		return map[string]interface{}{"total": 0, "items": make([]interface{}, 0)}, nil
	}

	//cohortID := ctx.Query("cohortId")
	siteID := ctx.Query("siteId")

	//编码药物统计

	match := bson.M{
		"customer_id": customerOID,
		"project_id":  projectOID,
		"env_id":      envOID,
	}
	if siteID != "" {
		siteOID, _ := primitive.ObjectIDFromHex(siteID)
		match["site_id"] = siteOID
	} else {
		match["site_id"] = bson.M{"$in": siteOIDS}
	}
	medicine, err := AllMedicineStat(match, roleID, envOID)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return map[string]interface{}{"items": medicine}, nil
}

func AllMedicineStat(match bson.M, roleID string, envOID primitive.ObjectID) ([]map[string]interface{}, error) {
	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return nil, err
	}
	blindDataMap, err := tools.IsBlindDrugMap(envOID)
	mpipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "spec": "$spec", "status": "$status"},
			"count": bson.M{"$sum": 1}}}},
		{{
			Key: "$project",
			Value: bson.M{
				"_id":    0,
				"name":   "$_id.name",
				"status": "$_id.status",
				"spec":   "$_id.spec",
				"count":  1,
			},
		}},
	}

	medicines := make([]map[string]interface{}, 0)
	mall, err := tools.Database.Collection("medicine").Aggregate(nil, mpipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = mall.All(nil, &medicines)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	medicine := make([]map[string]interface{}, 0)
	medicineMap := make(map[string]models.MedicineOtherPackageInfo, 0)
	medicineSpecMap := make(map[string]string, 0)
	for _, m := range medicines {
		name := m["name"].(string)
		if isBlindedRole && blindDataMap[name] {
			name = tools.BlindData
		}
		spec := m["spec"].(string)
		status := m["status"].(int32)
		tmpCount := m["count"].(int32)
		mp, exist := medicineMap[name+" "+spec]
		medicineSpecMap[name+" "+spec] = name
		count := 0
		to_be_send_count := 0
		in_transit_count := 0
		quarantined_count := 0
		used_count := 0
		lost_count := 0
		expired_count := 0
		to_be_confirm_count := 0
		locked_count := 0
		frozen_count := 0
		switch status {
		case 1:
			count = int(tmpCount)
		case 2:
			to_be_send_count = int(tmpCount)
		case 3:
			in_transit_count = int(tmpCount)
		case 4:
			quarantined_count = int(tmpCount)
		case 5:
			used_count = int(tmpCount)
		case 6:
			lost_count = int(tmpCount)
		case 7:
			expired_count = int(tmpCount)
		case 11:
			to_be_confirm_count = int(tmpCount)
		case 20:
			locked_count = int(tmpCount)
		case 14:
			frozen_count = int(tmpCount)
		}

		if !exist {
			mp = models.MedicineOtherPackageInfo{}
		}
		mp.Count = mp.Count + count
		mp.ToBeSendCount = mp.ToBeSendCount + to_be_send_count
		mp.InTransitCount = mp.InTransitCount + in_transit_count
		mp.QuarantinedCount = mp.QuarantinedCount + quarantined_count
		mp.UsedCount = mp.UsedCount + used_count
		mp.LostCount = mp.LostCount + lost_count
		mp.ExpiredCount = mp.ExpiredCount + expired_count
		mp.ToBeConfirmCount = mp.ToBeConfirmCount + to_be_confirm_count
		mp.LockedCount = mp.LockedCount + locked_count
		mp.FrozenCount = mp.FrozenCount + frozen_count
		mp.Spec = spec
		medicineMap[name+" "+spec] = mp
	}
	for key, v := range medicineMap {
		data := map[string]interface{}{
			"name":                medicineSpecMap[key],
			"count":               v.Count,
			"to_be_send_count":    v.ToBeSendCount,
			"in_transit_count":    v.InTransitCount,
			"quarantined_count":   v.QuarantinedCount,
			"used_count":          v.UsedCount,
			"lost_count":          v.LostCount,
			"expired_count":       v.ExpiredCount,
			"to_be_confirm_count": v.ToBeConfirmCount,
			"locked_count":        v.LockedCount,
			"frozen_count":        v.FrozenCount,
			"spec":                v.Spec,
		}
		medicine = append(medicine, data)
	}
	sort.Slice(medicine, func(i, j int) bool {
		a := medicine[i]
		b := medicine[j]
		// 比较日期
		if a["name"].(string) > b["name"].(string) {
			return false
		} else if a["name"].(string) < b["name"].(string) {
			return true
		}
		return false
	})
	//未编号
	otherMedicines := make([]map[string]interface{}, 0)
	otherPipepine := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "spec": "$spec", "status": "$status", "packageNumber": "$package_number"},
			"packageNumberCount": bson.M{"$sum": 1}}}},
		{{
			Key: "$project",
			Value: bson.M{
				"_id":                0,
				"name":               "$_id.name",
				"status":             "$_id.status",
				"packageNumber":      "$_id.packageNumber",
				"packageNumberCount": 1,
				"spec":               "$_id.spec",
			},
		}},
	}

	otherMedicineAll, err := tools.Database.Collection("medicine_others").Aggregate(nil, otherPipepine)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = otherMedicineAll.All(nil, &otherMedicines)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	//查询每个包装号的数量
	otherPackageNumbers := make([]map[string]interface{}, 0)
	packageMatch := bson.M{
		"env_id": envOID,
	}
	packagePipepine := mongo.Pipeline{
		{{Key: "$match", Value: packageMatch}},
		{{
			Key: "$project",
			Value: bson.M{
				"_id":           0,
				"packageNumber": "$package_number",
				"packageCount":  "$package_count",
			},
		}},
	}
	otherPackageAll, err := tools.Database.Collection("other_package_number").Aggregate(nil, packagePipepine)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = otherPackageAll.All(nil, &otherPackageNumbers)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	otherPackageNumberMap := make(map[int32]int32, len(otherPackageNumbers))
	for _, n := range otherPackageNumbers {
		packageNumber := n["packageNumber"].(int32)
		if n["packageCount"] != nil {
			packageCount := n["packageCount"].(int32)
			otherPackageNumberMap[packageNumber] = packageCount
		}
	}

	otherMedicineMap := make(map[string]models.MedicineOtherPackageInfo, 0)
	for _, m := range otherMedicines {
		name := m["name"].(string)
		spec := m["spec"].(string)
		status := m["status"].(int32)
		addPackageNumberCount := 0
		packageNumberCount := m["packageNumberCount"].(int32)
		if m["packageNumber"] != nil {
			packageNumber, _ := strconv.ParseInt(m["packageNumber"].(string), 10, 64)
			allPackageNumberCount := otherPackageNumberMap[int32(packageNumber)]
			if packageNumberCount == allPackageNumberCount { //整包s
				addPackageNumberCount = 1
			}
		}
		mp, exist := otherMedicineMap[name+" "+spec]
		medicineSpecMap[name+" "+spec] = name
		count := 0
		package_count := 0
		to_be_send_count := 0
		to_be_send_package_count := 0
		in_transit_count := 0
		in_transit_package_count := 0
		quarantined_count := 0
		quarantined_package_count := 0
		used_count := 0
		used_package_count := 0
		lost_count := 0
		lost_package_count := 0
		expired_count := 0
		expired_package_count := 0
		to_be_confirm_count := 0
		to_be_confirm_package_count := 0
		locked_count := 0
		locked_package_count := 0
		frozen_count := 0
		frozen_package_count := 0
		packageNumberCountInt := int(packageNumberCount)
		switch status {
		case 1:
			count = packageNumberCountInt
			package_count = addPackageNumberCount
		case 2:
			to_be_send_count = packageNumberCountInt
			to_be_send_package_count = addPackageNumberCount
		case 3:
			in_transit_count = packageNumberCountInt
			in_transit_package_count = addPackageNumberCount
		case 4:
			quarantined_count = packageNumberCountInt
			quarantined_package_count = addPackageNumberCount
		case 5:
			used_count = packageNumberCountInt
			used_package_count = addPackageNumberCount
		case 6:
			lost_count = packageNumberCountInt
			lost_package_count = addPackageNumberCount
		case 7:
			expired_count = packageNumberCountInt
			expired_package_count = addPackageNumberCount
		case 11:
			to_be_confirm_count = packageNumberCountInt
			to_be_confirm_package_count = addPackageNumberCount
		case 20:
			locked_count = packageNumberCountInt
			locked_package_count = addPackageNumberCount
		case 14:
			frozen_count = packageNumberCountInt
			frozen_package_count = addPackageNumberCount
		}

		if !exist {
			mp = models.MedicineOtherPackageInfo{}
		}
		mp.Count = mp.Count + count
		mp.ToBeSendCount = mp.ToBeSendCount + to_be_send_count
		mp.InTransitCount = mp.InTransitCount + in_transit_count
		mp.QuarantinedCount = mp.QuarantinedCount + quarantined_count
		mp.UsedCount = mp.UsedCount + used_count
		mp.LostCount = mp.LostCount + lost_count
		mp.ExpiredCount = mp.ExpiredCount + expired_count
		mp.ToBeConfirmCount = mp.ToBeConfirmCount + to_be_confirm_count
		mp.LockedCount = mp.LockedCount + locked_count
		mp.FrozenCount = mp.FrozenCount + frozen_count

		mp.PackageCount = mp.PackageCount + package_count
		mp.ToBeSendPackageCount = mp.ToBeSendPackageCount + to_be_send_package_count
		mp.InTransitPackageCount = mp.InTransitPackageCount + in_transit_package_count
		mp.QuarantinedPackageCount = mp.QuarantinedPackageCount + quarantined_package_count
		mp.UsedPackageCount = mp.UsedPackageCount + used_package_count
		mp.LostPackageCount = mp.LostPackageCount + lost_package_count
		mp.ExpiredPackageCount = mp.ExpiredPackageCount + expired_package_count
		mp.ToBeConfirmPackageCount = mp.ToBeConfirmPackageCount + to_be_confirm_package_count
		mp.LockedPackageCount = mp.LockedPackageCount + locked_package_count
		mp.FrozenPackageCount = mp.FrozenPackageCount + frozen_package_count
		mp.Spec = spec
		otherMedicineMap[name+" "+spec] = mp
	}

	isOpenPackage, _, packageDrugNames, _, _, _ := tools.IsOpenPackage(envOID)
	tmpOtherMedicine := []map[string]interface{}{}
	for key, v := range otherMedicineMap {
		data := map[string]interface{}{
			"name":                medicineSpecMap[key],
			"count":               v.Count,
			"to_be_send_count":    v.ToBeSendCount,
			"in_transit_count":    v.InTransitCount,
			"quarantined_count":   v.QuarantinedCount,
			"used_count":          v.UsedCount,
			"lost_count":          v.LostCount,
			"expired_count":       v.ExpiredCount,
			"to_be_confirm_count": v.ToBeConfirmCount,
			"locked_count":        v.LockedCount,
			"frozen_count":        v.FrozenCount,
			"spec":                v.Spec,
		}
		//判断研究产品是否是未编号包装的研究产品
		if isOpenPackage && packageDrugNames[medicineSpecMap[key]] > 0 {
			data = map[string]interface{}{
				"name":                medicineSpecMap[key],
				"count":               fmt.Sprintf("%v(%v)", v.Count, v.PackageCount),
				"to_be_send_count":    fmt.Sprintf("%v(%v)", v.ToBeSendCount, v.ToBeSendPackageCount),
				"in_transit_count":    fmt.Sprintf("%v(%v)", v.InTransitCount, v.InTransitPackageCount),
				"quarantined_count":   fmt.Sprintf("%v(%v)", v.QuarantinedCount, v.QuarantinedPackageCount),
				"used_count":          fmt.Sprintf("%v(%v)", v.UsedCount, v.UsedPackageCount),
				"lost_count":          fmt.Sprintf("%v(%v)", v.LostCount, v.LostPackageCount),
				"expired_count":       fmt.Sprintf("%v(%v)", v.ExpiredCount, v.ExpiredPackageCount),
				"to_be_confirm_count": fmt.Sprintf("%v(%v)", v.ToBeConfirmCount, v.ToBeConfirmPackageCount),
				"locked_count":        fmt.Sprintf("%v(%v)", v.LockedCount, v.LockedPackageCount),
				"frozen_count":        fmt.Sprintf("%v(%v)", v.FrozenCount, v.FrozenPackageCount),
				"spec":                v.Spec,
			}
		}

		tmpOtherMedicine = append(tmpOtherMedicine, data)
	}

	sort.Slice(tmpOtherMedicine, func(i, j int) bool {
		a := tmpOtherMedicine[i]
		b := tmpOtherMedicine[j]
		// 比较日期
		if a["name"].(string) > b["name"].(string) {
			return false
		} else if a["name"].(string) < b["name"].(string) {
			return true
		}
		return false
	})

	medicine = append(medicine, tmpOtherMedicine...)

	return medicine, nil
}

// func (s *MedicineService) GetOtherMedicineStorehouseSku(ctx *gin.Context, customerID string, projectID string, envID string, roleID string, start int, limit int) (map[string]interface{}, error) {
func (s *MedicineService) GetOtherMedicineStorehouseSku(ctx *gin.Context, req models.MedicineSkuReq) (map[string]interface{}, error) {
	role, err := tools.GetRole(req.RoleID.Hex())
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if role.Scope == "site" {
		return map[string]interface{}{"total": 0, "items": make([]interface{}, 0)}, nil
	}
	customerOID := req.CustomerID //primitive.ObjectIDFromHex(customerID)
	envOID := req.EnvID           //primitive.ObjectIDFromHex(envID)
	projectOID := req.ProjectID   // primitive.ObjectIDFromHex(projectID)

	field := req.Field           //ctx.Query("field")
	fieldValue := req.FieldValue //ctx.Query("fieldValue")

	// customerOID, _ := primitive.ObjectIDFromHex(customerID)
	// envOID, _ := primitive.ObjectIDFromHex(envID)
	// projectOID, _ := primitive.ObjectIDFromHex(projectID)

	// field := ctx.Query("field")
	// fieldValue := ctx.Query("fieldValue")

	//cohortID := ctx.Query("cohortId")
	//storehouseID := ctx.Query("storehouseId")
	storehouseID := req.StorehouseIDs
	//包装的研究产品
	isOpenPackage, packageAllDrugNames, _, _, _, err := tools.IsOpenPackage(envOID)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	match := bson.A{}
	filter := bson.M{
		"customer_id": customerOID,
		"project_id":  projectOID,
		"env_id":      envOID,
	}

	storehouseIDs := make([]primitive.ObjectID, 0)
	if len(storehouseID) > 0 {
		// storehouseOID, _ := primitive.ObjectIDFromHex(storehouseID)
		// filter["storehouse_id"] = storehouseOID
		storehouseIDs = append(storehouseIDs, storehouseID...)
		filter["storehouse_id"] = bson.M{"$in": storehouseIDs}
	} else {
		//判断当前登录用户的角色，如果是study，查询正常状态下的库房，如果是depot，查询绑定的仓库
		if role.Scope == "study" {
			match := bson.M{
				"customer_id": customerOID,
				"project_id":  projectOID,
				"env_id":      envOID,
				"deleted":     2,
			}
			var data []models.ProjectStorehouse
			cursor, err := tools.Database.Collection("project_storehouse").Find(nil, match)
			if err != nil {
				return nil, errors.WithStack(err)

			}
			if err := cursor.All(nil, &data); err != nil {
				return nil, errors.WithStack(err)
			}
			for _, projectStorehouse := range data {
				storehouseIDs = append(storehouseIDs, projectStorehouse.ID)
			}
		} else if role.Scope == "depot" {
			u, _ := ctx.Get("user")
			user := u.(models.User)
			match := bson.M{
				"customer_id": customerOID,
				"project_id":  projectOID,
				"env_id":      envOID,
				"user_id":     user.ID,
			}
			var userDepots []models.UserDepot
			cursor, err := tools.Database.Collection("user_depot").Find(nil, match)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &userDepots)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			for _, userDepot := range userDepots {
				storehouseIDs = append(storehouseIDs, userDepot.DepotID)
			}
		}
		filter["storehouse_id"] = bson.M{"$in": storehouseIDs}
	}
	match = append(match, filter)
	if field != "" && fieldValue != "" {
		// if strings.HasPrefix(field, "info.status.") {
		// 	field = strings.ReplaceAll(field, "info.status.", "info.")
		// 	value, _ := strconv.Atoi(fieldValue)
		// 	filter[field] = value
		// } else {
		searchList := strings.Split(fieldValue, ",")
		multiFilter := bson.A{}
		for _, searchCondition := range searchList {
			multiFilter = append(multiFilter, bson.M{field: bson.M{"$regex": tools.EscapeSpecialWord(searchCondition), "$options": "im"}})
		}
		if len(multiFilter) > 0 {
			match = append(match, bson.M{"$or": multiFilter})
		}
		// }
	}
	medicine := make([]map[string]interface{}, 0)
	otherPipepine := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"$and": match}}},
		{{Key: "$group", Value: bson.M{"_id": bson.M{"storehouse": "$storehouse_id", "name": "$name", "batchNumber": "$batch_number", "expirationDate": "$expiration_date", "status": "$status"},
			"statusCount":    bson.M{"$sum": 1},
			"packageNumbers": bson.M{"$push": "$package_number"}}}},
		{{Key: "$group", Value: bson.M{"_id": bson.M{"storehouse": "$_id.storehouse", "name": "$_id.name", "batchNumber": "$_id.batchNumber", "expirationDate": "$_id.expirationDate"},
			"groupInfo": bson.M{"$push": bson.M{"status": "$_id.status", "packageNumbers": "$packageNumbers", "statusCount": "$statusCount"}}}}},
		{{
			Key: "$project",
			Value: bson.M{
				"_id":            0,
				"storehouse":     "$_id.storehouse",
				"name":           "$_id.name",
				"batchNumber":    "$_id.batchNumber",
				"expirationDate": "$_id.expirationDate",
				"groupInfo":      1,
			},
		}},
		{{Key: "$sort", Value: bson.D{{"storehouse", 1}, {"name", 1}, {"expirationDate", 1}, {"batchNumber", 1}}}},
	}

	otherMedicineAll, err := tools.Database.Collection("medicine_others").Aggregate(nil, otherPipepine)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = otherMedicineAll.All(nil, &medicine)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	//查询每个包装号的数量
	otherPackageNumbers := make([]map[string]interface{}, 0)
	packageMatch := bson.M{
		"customer_id": customerOID,
		"project_id":  projectOID,
		"env_id":      envOID,
	}
	packagePipepine := mongo.Pipeline{
		{{Key: "$match", Value: packageMatch}},
		{{
			Key: "$project",
			Value: bson.M{
				"_id":           0,
				"packageNumber": "$package_number",
				"packageCount":  "$package_count",
			},
		}},
	}
	otherPackageAll, err := tools.Database.Collection("other_package_number").Aggregate(nil, packagePipepine)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = otherPackageAll.All(nil, &otherPackageNumbers)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	otherPackageNumberMap := make(map[int32]int32, len(otherPackageNumbers))
	for _, n := range otherPackageNumbers {
		packageNumber := n["packageNumber"].(int32)
		if n["packageCount"] != nil {
			packageCount := n["packageCount"].(int32)
			otherPackageNumberMap[packageNumber] = packageCount
		}
	}

	for _, other := range medicine {
		groupInfo := other["groupInfo"].(primitive.A)
		groupMap := make([]map[string]interface{}, 0)
		for _, group := range groupInfo {
			groupInterface := group.(map[string]interface{})
			packageNumberMap := make(map[int32]int32, 0)
			for _, packageNumber := range groupInterface["packageNumbers"].(primitive.A) {
				packageNumberInt, _ := strconv.ParseInt(packageNumber.(string), 10, 64)
				numberMap, exist := packageNumberMap[int32(packageNumberInt)]
				if exist {
					packageNumberMap[int32(packageNumberInt)] = numberMap + 1
				} else {
					packageNumberMap[int32(packageNumberInt)] = 1
				}
			}
			packageCount := 0
			for key, value := range packageNumberMap {
				if otherPackageNumberMap[key] == value {
					packageCount = packageCount + 1
				}
			}
			groupMap = append(groupMap, map[string]interface{}{
				"status":       groupInterface["status"],
				"statusCount":  groupInterface["statusCount"],
				"packageCount": packageCount,
			})
		}
		other["group"] = groupMap
	}

	// otherPipepine := mongo.Pipeline{
	// 	{{Key: "$match", Value: bson.M{"$and": match}}},
	// 	{{Key: "$group", Value: bson.M{"_id": bson.M{"storehouse": "$storehouse_id", "name": "$name", "batchNumber": "$batch_number",
	// 		"expirationDate": "$expiration_date", "status": "$status", "packageNumber": "$package_number"},
	// 		"status": bson.M{"$sum": 1}}}},
	// 	{{Key: "$group", Value: bson.M{"_id": bson.M{"storehouse": "$_id.storehouse", "name": "$_id.name", "batchNumber": "$_id.batchNumber",
	// 		"expirationDate": "$_id.expirationDate", "status": "$_id.status"},
	// 		"statusCount":  bson.M{"$sum": "$status"},
	// 		"packageCount": bson.M{"$sum": 1}}}},
	// 	{{Key: "$group", Value: bson.M{"_id": bson.M{"storehouse": "$_id.storehouse", "name": "$_id.name", "batchNumber": "$_id.batchNumber",
	// 		"expirationDate": "$_id.expirationDate"},
	// 		"group": bson.M{"$push": bson.M{
	// 			"status": "$_id.status", "statusCount": "$statusCount", "packageCount": "$packageCount"},
	// 		}},
	// 	}},
	// 	{{Key: "$project", Value: bson.M{"_id": 0,
	// 		"storehouse":     "$_id.storehouse",
	// 		"name":           "$_id.name",
	// 		"batchNumber":    "$_id.batchNumber",
	// 		"expirationDate": "$_id.expirationDate",
	// 		"group":          1}}},
	// 	{{Key: "$sort", Value: bson.D{{"storehouse", 1}, {"name", 1}, {"batchNumber", 1}, {"expirationDate", 1}}}},
	// }
	// otherCursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, otherPipepine)
	// if err != nil {
	// 	return nil, errors.WithStack(err)
	// }
	// err = otherCursor.All(nil, &medicine)
	// if err != nil {
	// 	return nil, errors.WithStack(err)
	// }

	//查询仓库
	storeMap := make(map[primitive.ObjectID]string)
	storehouseMatch := bson.M{
		"_id": bson.M{"$in": storehouseIDs},
	}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: storehouseMatch}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "storehouse",
			"localField":   "storehouse_id",
			"foreignField": "_id",
			"as":           "detail",
		}}},
		{{Key: "$unwind", Value: "$detail"}},
		{{Key: "$project", Value: bson.M{
			"_id":    0,
			"id":     "$_id",
			"number": "$detail.number",
			"name":   "$detail.name",
			"nameEn": "$detail.name",
		}}},
	}
	var projectStorehouse []map[string]interface{}
	storeCursor, storeErr := tools.Database.Collection("project_storehouse").Aggregate(nil, pipeline)
	if storeErr != nil {
		return nil, errors.WithStack(storeErr)
	}
	storeErr = storeCursor.All(nil, &projectStorehouse)
	if storeErr != nil {
		return nil, errors.WithStack(storeErr)
	}
	for _, projectStore := range projectStorehouse {
		storeMap[projectStore["id"].(primitive.ObjectID)] = projectStore["name"].(string)
	}

	isBlindedRole, err := tools.IsBlindedRole(req.RoleID.Hex())
	if err != nil {
		return nil, err
	}
	for _, otherItem := range medicine {
		otherItem["storehouseName"] = storeMap[otherItem["storehouse"].(primitive.ObjectID)]
		name := otherItem["name"].(string)
		if isOpenPackage && packageAllDrugNames[name] > 0 {
			otherItem["isOpenPackage"] = true
			otherItem["packageNumber"] = packageAllDrugNames[name]
		}

		if isBlindedRole {
			isBlindedDrug, _ := tools.IsBlindedDrug(envOID, otherItem["name"].(string))
			if isBlindedDrug {
				saltName, salt := tools.Encrypt(otherItem["name"].(string))
				otherItem["name"] = tools.BlindData
				otherItem["saltName"] = saltName
				otherItem["salt"] = salt
			}
		}

		var medicineOtherKey models.MedicineOtherKey
		otherKeyMatch := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "storehouse_id": otherItem["storehouse"].(primitive.ObjectID),
			"name": name, "expiration_date": otherItem["expirationDate"].(string), "batch_number": otherItem["batchNumber"].(string)}
		err := tools.Database.Collection("medicine_other_key").FindOne(nil, otherKeyMatch).Decode(&medicineOtherKey)
		if err != nil && err != mongo.ErrNoDocuments {
			return map[string]interface{}{"total": len(medicine), "items": medicine}, errors.WithStack(err)
		}
		if err == mongo.ErrNoDocuments {
			otherItem["id"] = nil
		} else {
			otherItem["id"] = medicineOtherKey.ID
		}

	}

	return map[string]interface{}{"total": len(medicine), "items": medicine}, nil
}

// func (s *MedicineService) GetOtherMedicineSiteSku(ctx *gin.Context, customerID string, projectID string, envID string, roleID string, start int, limit int) (map[string]interface{}, error) {
func (s *MedicineService) GetOtherMedicineSiteSku(ctx *gin.Context, req models.MedicineSkuReq) (map[string]interface{}, error) {
	role, err := tools.GetRole(req.RoleID.Hex())
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if role.Scope == "depot" {
		return map[string]interface{}{"total": 0, "items": make([]interface{}, 0)}, nil
	}
	customerOID := req.CustomerID //primitive.ObjectIDFromHex(customerID)
	envOID := req.EnvID           //primitive.ObjectIDFromHex(envID)
	projectOID := req.ProjectID   // primitive.ObjectIDFromHex(projectID)

	field := req.Field           //ctx.Query("field")
	fieldValue := req.FieldValue //ctx.Query("fieldValue")

	//cohortID := ctx.Query("cohortId")
	siteID := req.SiteIDs //ctx.Query("siteId")
	match := bson.A{}
	filter := bson.M{
		"customer_id": customerOID,
		"project_id":  projectOID,
		"env_id":      envOID,
	}

	//包装的研究产品
	isOpenPackage, packageAllDrugNames, _, _, _, err := tools.IsOpenPackage(envOID)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	siteIDs := make([]primitive.ObjectID, 0)
	if len(siteID) > 0 {
		//siteOID, _ := primitive.ObjectIDFromHex(siteID)
		// filter["site_id"] = siteOID
		siteIDs = append(siteIDs, siteID...)
		filter["site_id"] = bson.M{"$in": siteIDs}
	} else {
		//判断当前登录用户的角色，如果是study，查询正常状态下的中心，如果是site，查询绑定的中心
		if role.Scope == "study" {
			match := bson.M{
				"customer_id": customerOID,
				"project_id":  projectOID,
				"env_id":      envOID,
				"deleted":     2,
			}
			var data []models.ProjectSite
			cursor, err := tools.Database.Collection("project_site").Find(nil, match)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if err := cursor.All(nil, &data); err != nil {
				return nil, errors.WithStack(err)
			}
			for _, projectSite := range data {
				siteIDs = append(siteIDs, projectSite.ID)
			}
		} else if role.Scope == "site" {
			u, _ := ctx.Get("user")
			user := u.(models.User)
			match := bson.M{
				"customer_id": customerOID,
				"project_id":  projectOID,
				"env_id":      envOID,
				"user_id":     user.ID,
			}
			var userSites []models.UserSite
			cursor, err := tools.Database.Collection("user_site").Find(nil, match)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &userSites)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			for _, userSite := range userSites {
				siteIDs = append(siteIDs, userSite.SiteID)
			}
		}
		filter["site_id"] = bson.M{"$in": siteIDs}
	}
	match = append(match, filter)
	if field != "" && fieldValue != "" {
		// if strings.HasPrefix(field, "info.status.") {
		// 	field = strings.ReplaceAll(field, "info.status.", "info.")
		// 	value, _ := strconv.Atoi(fieldValue)
		// 	filter[field] = value
		// } else {
		searchList := strings.Split(fieldValue, ",")
		multiFilter := bson.A{}
		for _, searchCondition := range searchList {
			multiFilter = append(multiFilter, bson.M{field: bson.M{"$regex": tools.EscapeSpecialWord(searchCondition), "$options": "im"}})
		}
		if len(multiFilter) > 0 {
			match = append(match, bson.M{"$or": multiFilter})
		}
		// }
	}

	// project := bson.M{
	// 	"id":                  "$_id",
	// 	"_id":                 0,
	// 	"siteName":            bson.M{"$concat": bson.A{"$project_site.number", "-", models.ProjectSiteNameLookUpBson(ctx)}},
	// 	"name":                "$info.name",
	// 	"batchNumber":         "$info.batch",
	// 	"expirationDate":      "$info.expire_date",
	// 	"place":               "$site.address",
	// 	"count":               "$info.count",
	// 	"to_be_send_count":    "$info.to_be_send_count",
	// 	"in_transit_count":    "$info.in_transit_count",
	// 	"quarantined_count":   "$info.quarantined_count",
	// 	"used_count":          "$info.used_count",
	// 	"lost_count":          "$info.lost_count",
	// 	"expired_count":       "$info.expired_count",
	// 	"to_be_confirm_count": "$info.to_be_confirm_count",
	// 	"locked_count":        "$info.locked_count",
	// 	"frozen_count":        "$info.frozen_count",
	// }

	// totalPipeline := bson.M{"$and": match}
	// total, err := tools.Database.Collection("medicine_other_institute").CountDocuments(nil, totalPipeline)
	// if err != nil {
	// 	return nil, errors.WithStack(err)
	// }
	// pagePipeline := mongo.Pipeline{
	// 	{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "institute_id", "foreignField": "_id", "as": "project_site"}}},
	// 	{{Key: "$unwind", Value: "$project_site"}},
	// 	{{Key: "$match", Value: bson.M{"$and": match}}},
	// 	{{Key: "$sort", Value: bson.D{{"project_site.name", 1}, {"project_site.number", 1}, {"_id", 1}}}}, // 需要_id排序，否则分页顺序与不分页顺序不一致
	// 	{{Key: "$skip", Value: start}},
	// 	{{Key: "$limit", Value: limit}},
	// 	{{
	// 		Key:   "$project",
	// 		Value: project,
	// 	}},
	// }

	medicine := make([]map[string]interface{}, 0)
	otherPipepine := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"$and": match}}},
		{{Key: "$group", Value: bson.M{"_id": bson.M{"site": "$site_id", "name": "$name", "batchNumber": "$batch_number", "expirationDate": "$expiration_date", "status": "$status"},
			"statusCount":    bson.M{"$sum": 1},
			"packageNumbers": bson.M{"$push": "$package_number"}}}},
		{{Key: "$group", Value: bson.M{"_id": bson.M{"site": "$_id.site", "name": "$_id.name", "batchNumber": "$_id.batchNumber", "expirationDate": "$_id.expirationDate"},
			"groupInfo": bson.M{"$push": bson.M{"status": "$_id.status", "packageNumbers": "$packageNumbers", "statusCount": "$statusCount"}}}}},
		{{
			Key: "$project",
			Value: bson.M{
				"_id":            0,
				"site":           "$_id.site",
				"name":           "$_id.name",
				"batchNumber":    "$_id.batchNumber",
				"expirationDate": "$_id.expirationDate",
				"groupInfo":      1,
			},
		}},
		{{Key: "$sort", Value: bson.D{{"site", 1}, {"name", 1}, {"expirationDate", 1}, {"batchNumber", 1}}}},
	}

	otherMedicineAll, err := tools.Database.Collection("medicine_others").Aggregate(nil, otherPipepine)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = otherMedicineAll.All(nil, &medicine)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	//查询每个包装号的数量
	otherPackageNumbers := make([]map[string]interface{}, 0)
	packageMatch := bson.M{
		"customer_id": customerOID,
		"project_id":  projectOID,
		"env_id":      envOID,
	}
	packagePipepine := mongo.Pipeline{
		{{Key: "$match", Value: packageMatch}},
		{{
			Key: "$project",
			Value: bson.M{
				"_id":           0,
				"packageNumber": "$package_number",
				"packageCount":  "$package_count",
			},
		}},
	}
	otherPackageAll, err := tools.Database.Collection("other_package_number").Aggregate(nil, packagePipepine)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = otherPackageAll.All(nil, &otherPackageNumbers)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	otherPackageNumberMap := make(map[int32]int32, len(otherPackageNumbers))
	for _, n := range otherPackageNumbers {
		packageNumber := n["packageNumber"].(int32)
		if n["packageCount"] != nil {
			packageCount := n["packageCount"].(int32)
			otherPackageNumberMap[packageNumber] = packageCount
		}
	}

	for _, other := range medicine {
		groupInfo := other["groupInfo"].(primitive.A)
		groupMap := make([]map[string]interface{}, 0)
		for _, group := range groupInfo {
			groupInterface := group.(map[string]interface{})
			packageNumberMap := make(map[int32]int32, 0)
			for _, packageNumber := range groupInterface["packageNumbers"].(primitive.A) {
				packageNumberInt, _ := strconv.ParseInt(packageNumber.(string), 10, 64)
				numberMap, exist := packageNumberMap[int32(packageNumberInt)]
				if exist {
					packageNumberMap[int32(packageNumberInt)] = numberMap + 1
				} else {
					packageNumberMap[int32(packageNumberInt)] = 1
				}
			}
			packageCount := 0
			for key, value := range packageNumberMap {
				if otherPackageNumberMap[key] == value {
					packageCount = packageCount + 1
				}
			}
			groupMap = append(groupMap, map[string]interface{}{
				"status":       groupInterface["status"],
				"statusCount":  groupInterface["statusCount"],
				"packageCount": packageCount,
			})
		}
		other["group"] = groupMap
	}

	//查询中心
	siteMap := make(map[primitive.ObjectID]string)
	siteMatch := bson.M{
		"_id": bson.M{"$in": siteIDs},
	}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: siteMatch}},
		{{Key: "$project", Value: bson.M{
			"_id":    0,
			"id":     "$_id",
			"number": "$number",
			"name":   bson.M{"$concat": bson.A{"$number", "-", models.ProjectSiteNameBson(ctx)}},
		}}},
	}
	var projectSite []map[string]interface{}
	storeCursor, storeErr := tools.Database.Collection("project_site").Aggregate(nil, pipeline)
	if storeErr != nil {
		return nil, errors.WithStack(storeErr)
	}
	storeErr = storeCursor.All(nil, &projectSite)
	if storeErr != nil {
		return nil, errors.WithStack(storeErr)
	}
	for _, projectStore := range projectSite {
		siteMap[projectStore["id"].(primitive.ObjectID)] = projectStore["name"].(string)
	}

	isBlindedRole, err := tools.IsBlindedRole(req.RoleID.Hex())
	if err != nil {
		return nil, err
	}
	for _, otherItem := range medicine {
		otherItem["siteName"] = siteMap[otherItem["site"].(primitive.ObjectID)]
		name := otherItem["name"].(string)
		if isOpenPackage && packageAllDrugNames[name] > 0 {
			otherItem["isOpenPackage"] = true
			otherItem["packageNumber"] = packageAllDrugNames[name]
		}

		var medicineOtherKey models.MedicineOtherKey
		otherKeyMatch := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "site_id": otherItem["site"].(primitive.ObjectID),
			"name": name, "expiration_date": otherItem["expirationDate"].(string), "batch_number": otherItem["batchNumber"].(string)}
		err := tools.Database.Collection("medicine_other_key").FindOne(nil, otherKeyMatch).Decode(&medicineOtherKey)
		if err != nil && err != mongo.ErrNoDocuments {
			return map[string]interface{}{"total": len(medicine), "items": medicine}, errors.WithStack(err)
		}
		otherItem["id"] = medicineOtherKey.ID

		if isBlindedRole {
			isBlindedDrug, _ := tools.IsBlindedDrug(envOID, otherItem["name"].(string))
			if isBlindedDrug {
				saltName, salt := tools.Encrypt(otherItem["name"].(string))
				otherItem["name"] = tools.BlindData
				otherItem["saltName"] = saltName
				otherItem["salt"] = salt
			}
		}

	}

	return map[string]interface{}{"total": len(medicine), "items": medicine}, nil
}

func (s *MedicineService) GetMedicineOrder(customerOID primitive.ObjectID, projectOID primitive.ObjectID, envOID primitive.ObjectID, orderNumber string) (map[string]interface{}, error) {
	filter := bson.M{"project_id": projectOID, "env_id": envOID, "status": bson.M{"$ne": 5}}
	if customerOID != primitive.NilObjectID {
		filter["customer_id"] = customerOID
	}
	var d []map[string]interface{}
	cursor, err := tools.Database.Collection("medicine_order").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{
			Key: "$project",
			Value: bson.M{
				"id":                  "$_id",
				"_id":                 0,
				"order_number":        1,
				"medicines":           1,
				"other_medicines_new": 1,
			},
		}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &d)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var medicineOrderMap = make(map[primitive.ObjectID][]string)
	var otherMedicineOrderMap = make(map[primitive.ObjectID][]string)
	for _, item := range d {
		if item["medicines"] != nil {
			for _, med := range item["medicines"].(primitive.A) {
				if medicineOrderMap[med.(primitive.ObjectID)] == nil {
					medicineOrderMap[med.(primitive.ObjectID)] = make([]string, 0)
				}
				medicineOrderMap[med.(primitive.ObjectID)] = append(medicineOrderMap[med.(primitive.ObjectID)], item["order_number"].(string))
			}
		}
		if item["other_medicines_new"] != nil {
			for _, med := range item["other_medicines_new"].(primitive.A) {
				if otherMedicineOrderMap[med.(primitive.ObjectID)] == nil {
					otherMedicineOrderMap[med.(primitive.ObjectID)] = make([]string, 0)
				}
				otherMedicineOrderMap[med.(primitive.ObjectID)] = append(otherMedicineOrderMap[med.(primitive.ObjectID)], item["order_number"].(string))
			}
		}
	}
	var medicines = make([]primitive.ObjectID, 0)
	for key := range medicineOrderMap {
		if orderNumber != "" {
			// 查询订单号, 逗号多选
			var fieldChecked = 0
			var list = strings.Split(orderNumber, ",")
			for _, fieldOrder := range list {
				var orderChecked = 0
				for _, medicineOrder := range medicineOrderMap[key] {
					if strings.Contains(medicineOrder, strings.TrimSpace(fieldOrder)) {
						orderChecked = orderChecked + 1
					}
				}
				if orderChecked > 0 {
					fieldChecked = fieldChecked + 1
				}
			}
			if fieldChecked == len(list) {
				medicines = append(medicines, key)
			}
		} else {
			medicines = append(medicines, key)
		}
	}
	return map[string]interface{}{"medicines": medicines, "medicineOrderMap": medicineOrderMap, "otherMedicineOrderMap": otherMedicineOrderMap}, nil
}

func (s *MedicineService) GetSummarySite(ctx *gin.Context) (map[string]interface{}, error) {
	var params map[string]interface{}
	err := ctx.ShouldBindBodyWith(&params, binding.JSON)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	customerOID, _ := primitive.ObjectIDFromHex(params["customerId"].(string))
	envOID, _ := primitive.ObjectIDFromHex(params["envId"].(string))
	projectOID, _ := primitive.ObjectIDFromHex(params["projectId"].(string))

	match := bson.A{}
	filter := bson.M{"customer_id": customerOID, "env_id": envOID, "project_id": projectOID, "deleted": 2}

	var siteIDs = params["siteId"].([]interface{})
	var roleID = params["roleId"].(string)
	var start = params["start"].(float64)
	var limit = params["limit"].(float64)
	if len(siteIDs) > 0 {
		siteList := bson.A{}
		for _, shID := range siteIDs {
			if shID != "" {
				shOID, _ := primitive.ObjectIDFromHex(shID.(string))
				siteList = append(siteList, bson.M{"_id": shOID})
			}
		}
		if len(siteList) > 0 {
			match = append(match, bson.M{"$or": siteList})
		}
	}
	var projection = bson.M{
		"site_id":         1,
		"_id":             1,
		"name":            models.ProjectSiteNameBson(ctx),
		"number":          1,
		"medicine":        1,
		"medicine_others": 1,
	}
	match = append(match, filter)
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"$and": match}}},
		{{Key: "$sort", Value: bson.D{{"number", 1}}}},
		{{Key: "$skip", Value: start}},
		{{Key: "$limit", Value: limit}},
		{{Key: "$lookup", Value: bson.M{
			"from": "medicine",
			"let": bson.M{
				"site_id": "$_id",
			},
			"pipeline": bson.A{
				bson.M{
					"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$site_id", "$$site_id"}}},
				},
				bson.M{
					"$group": bson.M{
						"_id": bson.M{
							"site_id":      "$site_id",
							"name":         "$name",
							"spec":         "$spec",
							"status":       "$status",
							"batch_number": "$batch_number",
						},
						"count": bson.M{"$sum": 1},
					},
				},
				bson.M{"$sort": bson.D{{"_id.site_id", 1}, {"_id.name", 1}, {"_id.batch_number", 1}}},
			},
			"as": "medicine",
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from": "medicine_others",
			"let": bson.M{
				"site_id": "$_id",
			},
			"pipeline": bson.A{
				bson.M{
					"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$site_id", "$$site_id"}}},
				},
				bson.M{
					"$group": bson.M{
						"_id": bson.M{
							"site_id":      "$site_id",
							"name":         "$name",
							"spec":         "$spec",
							"status":       "$status",
							"batch_number": "$batch_number",
							//	"package_number": "$package_number",
						},
						"count": bson.M{"$sum": 1},
					},
				},
				bson.M{"$sort": bson.D{{"_id.site_id", 1}, {"_id.name", 1}, {"_id.batch_number", 1}}},
				// bson.M{
				// 	"$group": bson.M{
				// 		"_id": bson.M{
				// 			"site_id":      "$_id.site_id",
				// 			"name":         "$_id.name",
				// 			"spec":         "$_id.spec",
				// 			"status":       "$_id.status",
				// 			"batch_number": "$_id.batch_number",
				// 		},
				// 		"count":        bson.M{"$sum": "$count"},
				// 		"packageCount": bson.M{"$sum": 1},
				// 	},
				// },
			},
			"as": "medicine_others",
		}}},
		//{{Key: "$lookup", Value: bson.M{"from": "site", "localField": "site_id", "foreignField": "_id", "as": "site"}}},
		//{{Key: "$unwind", Value: "$site"}},
		{{Key: "$project", Value: projection}},
	}
	role, err := tools.GetRole(roleID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if "depot" == role.Scope {
		return map[string]interface{}{"total": 0, "items": make([]interface{}, 0)}, nil
	}
	if role.Scope == "site" {
		user, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		pipeline = mongo.Pipeline{
			{{Key: "$match", Value: bson.M{"$and": match}}},
			{{Key: "$lookup", Value: bson.M{"from": "user_site", "localField": "_id", "foreignField": "site_id", "as": "user_site"}}},
			{{Key: "$unwind", Value: "$user_site"}},
			{{Key: "$match", Value: bson.M{"user_site.user_id": user.ID}}},
			{{Key: "$sort", Value: bson.D{{"number", 1}}}},
			{{Key: "$skip", Value: start}},
			{{Key: "$limit", Value: limit}},
			{{Key: "$lookup", Value: bson.M{
				"from": "medicine",
				"let": bson.M{
					"site_id": "$_id",
				},
				"pipeline": bson.A{
					bson.M{
						"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$site_id", "$$site_id"}}},
					},
					bson.M{
						"$group": bson.M{
							"_id": bson.M{
								"site_id":      "$site_id",
								"name":         "$name",
								"spec":         "$spec",
								"status":       "$status",
								"batch_number": "$batch_number",
							},
							"count": bson.M{"$sum": 1},
						},
					},
					bson.M{"$sort": bson.D{{"_id.site_id", 1}, {"_id.name", 1}, {"_id.batch_number", 1}}},
				},
				"as": "medicine",
			}}},
			{{Key: "$lookup", Value: bson.M{
				"from": "medicine_others",
				"let": bson.M{
					"site_id": "$_id",
				},
				"pipeline": bson.A{
					bson.M{
						"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$site_id", "$$site_id"}}},
					},
					bson.M{
						"$group": bson.M{
							"_id": bson.M{
								"site_id":      "$site_id",
								"name":         "$name",
								"spec":         "$spec",
								"status":       "$status",
								"batch_number": "$batch_number",
								//		"package_number": "$package_number",
							},
							"count": bson.M{"$sum": 1},
						},
					},
					bson.M{"$sort": bson.D{{"_id.site_id", 1}, {"_id.name", 1}, {"_id.batch_number", 1}}},
					// bson.M{
					// 	"$group": bson.M{
					// 		"_id": bson.M{
					// 			"site_id":      "$_id.site_id",
					// 			"name":         "$_id.name",
					// 			"spec":         "$_id.spec",
					// 			"status":       "$_id.status",
					// 			"batch_number": "$_id.batch_number",
					// 		},
					// 		"count":        bson.M{"$sum": "$count"},
					// 		"packageCount": bson.M{"$sum": 1},
					// 	},
					// },
				},
				"as": "medicine_others",
			}}},
			//{{Key: "$lookup", Value: bson.M{"from": "site", "localField": "site_id", "foreignField": "_id", "as": "site"}}},
			//{{Key: "$unwind", Value: "$site"}},

			{{Key: "$project", Value: projection}},
		}
	}

	collection := tools.Database.Collection("project_site")
	total, err := collection.CountDocuments(nil, bson.M{"$and": match})

	var data []map[string]interface{}
	cursor, err := collection.Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	//包装的研究产品
	isOpenPackage, packageAllDrugNames, _, _, _, err := tools.IsOpenPackage(envOID)
	if err != nil {
		return nil, err
	}

	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return nil, err
	}

	for _, site := range data {
		if isBlindedRole && site["medicine"] != nil {

			for _, med := range site["medicine"].(primitive.A) {
				name := med.(map[string]interface{})["_id"].(map[string]interface{})["name"].(string)
				isBlindedDrug, _ := tools.IsBlindedDrug(envOID, name)
				if isBlindedDrug {
					med.(map[string]interface{})["_id"].(map[string]interface{})["name"] = tools.BlindData
				}
			}

		}
		if site["medicine_others"] != nil {
			for _, other := range site["medicine_others"].(primitive.A) {
				name := other.(map[string]interface{})["_id"].(map[string]interface{})["name"].(string)
				isBlindedDrug, _ := tools.IsBlindedDrug(envOID, name)
				if isBlindedDrug && isBlindedRole {
					other.(map[string]interface{})["_id"].(map[string]interface{})["name"] = tools.BlindData
				}
				if isOpenPackage && packageAllDrugNames[name] > 0 {
					other.(map[string]interface{})["_id"].(map[string]interface{})["isOpenPackage"] = true
					other.(map[string]interface{})["_id"].(map[string]interface{})["packageNumber"] = packageAllDrugNames[name]
				}
			}
		}
	}

	//}
	return map[string]interface{}{"total": total, "items": data}, nil

}

func (s *MedicineService) GetSiteMedicineList(ctx *gin.Context, customerID string, projectID string, envID string) (map[string]interface{}, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)

	var params map[string]string
	err := ctx.ShouldBindBodyWith(&params, binding.JSON)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	siteOID, _ := primitive.ObjectIDFromHex(params["siteId"])
	var data []map[string]interface{}
	filter := bson.M{"customer_id": customerOID, "env_id": envOID, "project_id": projectOID, "site_id": siteOID}
	cursor, err := tools.Database.Collection("medicine").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$sort", Value: bson.D{{"number", 1}}}},
		{{Key: "$project",
			Value: bson.M{
				"id":   "$_id",
				"_id":  0,
				"name": 1,
			},
		}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var orderData []map[string]interface{}

	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, bson.M{
		"customer_id": customerOID,
		"env_id":      envOID,
		"project_id":  projectOID,
	}).Decode(&attribute)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var statusList = bson.A{1, 2}
	if attribute.AttributeInfo.IsFreeze {
		statusList = append(statusList, 4)
	}

	orderFilter := bson.M{
		"customer_id": customerOID,
		"env_id":      envOID,
		"project_id":  projectOID,
		"receive_id":  siteOID,
		"status":      bson.M{"$in": statusList},
		"type":        bson.M{"$in": bson.A{1, 3}},
	}
	orderCursor, err := tools.Database.Collection("medicine_order").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: orderFilter}},
		{{Key: "$lookup", Value: bson.M{"from": "medicine", "localField": "medicines", "foreignField": "_id", "as": "medicines"}}},
		{{Key: "$unwind", Value: "$medicines"}},
		{{Key: "$project",
			Value: bson.M{
				"_id":       0,
				"sendId":    "$send_id",
				"receiveId": "$receive_id",
			},
		}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = orderCursor.All(nil, &orderData)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return bson.M{"siteMedicine": data, "siteOrder": orderData}, nil
}

func (s *MedicineService) GetAttribute(ctx *gin.Context, customerID string, projectID string, envID string, cohortID string) (models.Attribute, error) {
	envOID, _ := primitive.ObjectIDFromHex(envID)

	var data models.Attribute
	filter := bson.M{"env_id": envOID}
	if cohortID != "" {
		cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
		filter = bson.M{"env_id": envOID, "cohort_id": cohortOID}
	}
	_ = tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&data)
	return data, nil
}

func (s *MedicineService) DownloadStorehouseSku(ctx *gin.Context, customerID string, projectID string, envID string, roleID string) error {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)

	storehouseID := ctx.Query("storehouseId")

	status := ctx.Query("status")
	field := ctx.Query("field")
	fieldValue := ctx.Query("fieldValue")
	cohortID := ctx.Query("cohortId")
	match := bson.A{}
	filter := bson.M{"project_storehouse.deleted": 2, "customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if storehouseID != "" {
		storehouseOID, _ := primitive.ObjectIDFromHex(storehouseID)
		filter["storehouse_id"] = storehouseOID
	} else {
		match = append(match, bson.M{"$and": bson.A{
			bson.M{"storehouse_id": bson.M{"$ne": primitive.NilObjectID}},
			bson.M{"storehouse_id": bson.M{"$ne": nil}},
		}})
	}
	if status != "" {
		filter["status"], _ = strconv.Atoi(status)
	}
	match = append(match, filter)
	var orderNumber = ""
	if field != "" && fieldValue != "" {
		if field != "order_number" {
			searchList := strings.Split(fieldValue, ",")
			multiFilter := bson.A{}
			for _, searchCondition := range searchList {
				multiFilter = append(multiFilter, bson.M{field: bson.M{"$regex": tools.EscapeSpecialWord(searchCondition), "$options": "im"}})
			}
			if len(multiFilter) > 0 {
				match = append(match, bson.M{"$or": multiFilter})
			}
		} else {
			orderNumber = fieldValue
		}
	}
	medicinesOrder, err := s.GetMedicineOrder(customerOID, projectOID, envOID, orderNumber)
	if err != nil {
		return errors.WithStack(err)
	}
	if orderNumber != "" {
		filter["_id"] = bson.M{"$in": medicinesOrder["medicines"].([]primitive.ObjectID)}
	}

	role, err := tools.GetRole(roleID)
	if err != nil {
		return errors.WithStack(err)
	}
	optTrue := true
	opt := &options.AggregateOptions{
		AllowDiskUse: &optTrue,
	}
	pagePipeline := mongo.Pipeline{
		{{Key: "$lookup", Value: bson.M{"from": "project_storehouse", "localField": "storehouse_id", "foreignField": "_id", "as": "project_storehouse"}}},
		{{Key: "$unwind", Value: "$project_storehouse"}},
		{{Key: "$lookup", Value: bson.M{"from": "storehouse", "localField": "project_storehouse.storehouse_id", "foreignField": "_id", "as": "storehouse"}}},
		{{Key: "$match", Value: bson.M{"$and": match}}},
		{{Key: "$sort", Value: bson.D{{"number", 1}}}},
		{{Key: "$unwind", Value: "$storehouse"}},
		{{
			Key: "$project",
			Value: bson.M{
				"id":             "$_id",
				"_id":            0,
				"storehouseName": "$storehouse.name",
				"place":          "$project_storehouse.address",
				"number":         1,
				"name":           1,
				"batchNumber":    "$batch_number",
				"expirationDate": "$expiration_date",
				"status":         1,
			},
		}},
	}

	var medicineData []map[string]interface{}
	if role.Scope == "depot" {
		user, err := tools.Me(ctx)
		filter["user_depot.user_id"] = user.ID

		pagePipeline = mongo.Pipeline{
			{{Key: "$lookup", Value: bson.M{"from": "user_depot", "localField": "storehouse_id", "foreignField": "depot_id", "as": "user_depot"}}},
			{{Key: "$unwind", Value: "$user_depot"}},
			{{Key: "$lookup", Value: bson.M{"from": "project_storehouse", "localField": "user_depot.depot_id", "foreignField": "_id", "as": "project_storehouse"}}},
			{{Key: "$unwind", Value: "$project_storehouse"}},
			{{Key: "$lookup", Value: bson.M{"from": "storehouse", "localField": "project_storehouse.storehouse_id", "foreignField": "_id", "as": "storehouse"}}},
			{{Key: "$match", Value: bson.M{"$and": match}}},
			{{Key: "$sort", Value: bson.D{{"number", 1}}}},
			{{Key: "$unwind", Value: "$storehouse"}},
			{{
				Key: "$project",
				Value: bson.M{
					"id":             "$_id",
					"_id":            0,
					"storehouseName": "$storehouse.name",
					"place":          "$project_storehouse.address",
					"number":         1,
					"name":           1,
					"batchNumber":    "$batch_number",
					"expirationDate": "$expiration_date",
					"status":         1,
				},
			}},
		}

		cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pagePipeline, opt)
		if err != nil {
			return errors.WithStack(err)
		}
		err = cursor.All(nil, &medicineData)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if role.Scope == "study" {
		cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pagePipeline, opt)
		if err != nil {
			return errors.WithStack(err)
		}
		err = cursor.All(nil, &medicineData)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	attribute, err := s.GetAttribute(ctx, customerID, projectID, envID, cohortID)
	if err != nil {
		return errors.WithStack(err)
	}
	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return err
	}
	var title []interface{}
	title = append(title, locales.Tr(ctx, "medicine_download_storehouse"))
	title = append(title, locales.Tr(ctx, "medicine_download_number"))
	if !isBlindedRole || !attribute.AttributeInfo.Blind {
		title = append(title, locales.Tr(ctx, "medicine_download_name"))
	}
	title = append(title, locales.Tr(ctx, "medicine_download_batch"))
	title = append(title, locales.Tr(ctx, "medicine_download_expiredDate"))
	//title = append(title, locales.Tr(ctx, "medicine_download_location"))
	title = append(title, locales.Tr(ctx, "medicine_download_orderNumber"))
	title = append(title, locales.Tr(ctx, "medicine_download_status"))

	content := make([][]interface{}, len(medicineData))
	for i := 0; i < len(medicineData); i++ {
		item := medicineData[i]
		orderNumber := medicinesOrder["medicineOrderMap"].(map[primitive.ObjectID][]string)[item["id"].(primitive.ObjectID)]
		var row []interface{}
		row = append(row, item["storehouseName"])
		row = append(row, item["number"])
		if !isBlindedRole || !attribute.AttributeInfo.Blind {
			row = append(row, item["name"])
		}
		row = append(row, item["batchNumber"])
		row = append(row, item["expirationDate"])
		//row = append(row, item["place"])
		row = append(row, strings.Join(orderNumber, ","))
		row = append(row, data.GetMedicineStatus(ctx, int(item["status"].(int32))))
		content[i] = row
	}

	var projectInfo []map[string]interface{}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": projectOID}}},
		{{Key: "$unwind", Value: "$envs"}},
		{{Key: "$match", Value: bson.M{"envs.id": envOID}}},
		{
			{
				Key: "$project",
				Value: bson.M{
					"_id":      0,
					"number":   "$info.number",
					"name":     "$info.name",
					"env":      "$envs.name",
					"timeZone": "$info.timeZoneStr",
				},
			},
		},
	}
	projectCursor, projectErr := tools.Database.Collection("project").Aggregate(nil, pipeline)
	if projectErr != nil {
		return errors.WithStack(projectErr)

	}
	err = projectCursor.All(nil, &projectInfo)
	if err != nil {
		return errors.WithStack(err)
	}

	var timeZone float64
	if projectInfo[0]["timeZone"] != nil {
		//timeZone = projectInfo[0]["timeZone"].(int32)
		timeZone, _ = strconv.ParseFloat(projectInfo[0]["timeZone"].(string), 64)
	} else {
		timeZone = float64(8)
	}
	hours := time.Duration(timeZone)
	minutes := time.Duration((timeZone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute

	now := time.Now().UTC().Add(duration).Format("20060102")

	number := ""
	env := ""
	if len(projectInfo) > 0 {
		number = projectInfo[0]["number"].(string)
		env = projectInfo[0]["env"].(string)
	}

	fileName := fmt.Sprintf("%s[%s]—%s—%s.xlsx", number, env, locales.Tr(ctx, "medicine_download_depot_name"), now)
	err = tools.ExportExcelStream(ctx, fileName, title, content)
	if err != nil {
		return err
	}
	return nil
}

func (s *MedicineService) DownloadSiteSku(ctx *gin.Context, customerID string, projectID string, envID string, roleID string) error {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)

	cohortID := ctx.Query("cohortId")
	siteID := ctx.Query("siteId")
	status := ctx.Query("status")
	field := ctx.Query("field")
	fieldValue := ctx.Query("fieldValue")
	match := bson.A{}
	filter := bson.M{"project_site.deleted": 2, "customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if siteID != "" {
		siteOID, _ := primitive.ObjectIDFromHex(siteID)
		filter["site_id"] = siteOID
	} else {
		match = append(match, bson.M{"$and": bson.A{
			bson.M{"site_id": bson.M{"$ne": primitive.NilObjectID}},
			bson.M{"site_id": bson.M{"$ne": nil}},
		}})
	}
	if status != "" {
		filter["status"], _ = strconv.Atoi(status)
	}
	match = append(match, filter)
	var orderNumber = ""
	if field != "" && fieldValue != "" {
		if field != "order_number" {
			searchList := strings.Split(fieldValue, ",")
			multiFilter := bson.A{}
			for _, searchCondition := range searchList {
				multiFilter = append(multiFilter, bson.M{field: bson.M{"$regex": tools.EscapeSpecialWord(searchCondition), "$options": "im"}})
			}
			if len(multiFilter) > 0 {
				match = append(match, bson.M{"$or": multiFilter})
			}
		} else {
			orderNumber = fieldValue
		}
	}

	medicinesOrder, err := s.GetMedicineOrder(customerOID, projectOID, envOID, orderNumber)
	if err != nil {
		return errors.WithStack(err)
	}

	if orderNumber != "" {
		filter["_id"] = bson.M{"$in": medicinesOrder["medicines"].([]primitive.ObjectID)}
	}

	pagePipeline := mongo.Pipeline{
		{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "site_id", "foreignField": "_id", "as": "project_site"}}},
		{{Key: "$unwind", Value: "$project_site"}},
		{{Key: "$match", Value: bson.M{"$and": match}}},
		{{Key: "$sort", Value: bson.D{{"project_site.number", 1}, {"project_site.name", 1}, {"number", 1}}}},
		{{
			Key: "$project",
			Value: bson.M{
				"id":  "$_id",
				"_id": 0,

				"country":        bson.M{"$first": "$project_site.country"},
				"siteName":       models.ProjectSiteNameLookUpBson(ctx),
				"place":          "$project_site.address",
				"number":         1,
				"name":           1,
				"batchNumber":    "$batch_number",
				"expirationDate": "$expiration_date",
				"status":         1,
			},
		}},
	}
	role, err := tools.GetRole(roleID)
	if err != nil {
		return errors.WithStack(err)
	}

	var medicineData []map[string]interface{}
	if role.Scope == "site" {
		user, err := tools.Me(ctx)
		filter["user_site.user_id"] = user.ID

		pagePipeline = mongo.Pipeline{
			{{Key: "$lookup", Value: bson.M{"from": "user_site", "localField": "site_id", "foreignField": "site_id", "as": "user_site"}}},
			{{Key: "$unwind", Value: "$user_site"}},
			{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "site_id", "foreignField": "_id", "as": "project_site"}}},
			{{Key: "$unwind", Value: "$project_site"}},
			{{Key: "$match", Value: bson.M{"$and": match}}},
			{{Key: "$sort", Value: bson.D{{"project_site.number", 1}, {"project_site.name", 1}, {"number", 1}}}},
			{{
				Key: "$project",
				Value: bson.M{
					"id":      "$_id",
					"_id":     0,
					"country": bson.M{"$first": "$project_site.country"},

					"siteName":       models.ProjectSiteNameLookUpBson(ctx),
					"place":          "$project_site.address",
					"number":         1,
					"name":           1,
					"batchNumber":    "$batch_number",
					"expirationDate": "$expiration_date",
					"status":         1,
				},
			}},
		}
		cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pagePipeline)
		if err != nil {
			return errors.WithStack(err)
		}
		err = cursor.All(nil, &medicineData)
		if err != nil {
			return errors.WithStack(err)
		}
	} else if role.Scope == "study" {
		cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pagePipeline)
		if err != nil {
			return errors.WithStack(err)
		}
		err = cursor.All(nil, &medicineData)
		if err != nil {
			return errors.WithStack(err)

		}
	}

	attribute, err := s.GetAttribute(ctx, customerID, projectID, envID, cohortID)
	if err != nil {
		return errors.WithStack(err)
	}
	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return err
	}
	var title []interface{}
	countries := bson.M{}

	if attribute.AttributeInfo.CountryLayered {
		countries, err = database.GetCountries(ctx)
		title = append(title, locales.Tr(ctx, "common.country"))
	}
	title = append(title, locales.Tr(ctx, "medicine_download_site"))
	title = append(title, locales.Tr(ctx, "medicine_download_number"))
	if !isBlindedRole || !attribute.AttributeInfo.Blind {
		title = append(title, locales.Tr(ctx, "medicine_download_name"))
	}
	title = append(title, locales.Tr(ctx, "medicine_download_batch"))
	title = append(title, locales.Tr(ctx, "medicine_download_expiredDate"))
	//title = append(title, locales.Tr(ctx, "medicine_download_location"))
	title = append(title, locales.Tr(ctx, "medicine_download_orderNumber"))
	title = append(title, locales.Tr(ctx, "medicine_download_status"))

	content := make([][]interface{}, len(medicineData))
	for i := 0; i < len(medicineData); i++ {
		item := medicineData[i]
		orderNumber := medicinesOrder["medicineOrderMap"].(map[primitive.ObjectID][]string)[item["id"].(primitive.ObjectID)]
		var row []interface{}
		if attribute.AttributeInfo.CountryLayered {
			if _, ok := item["country"]; ok {
				row = append(row, countries[item["country"].(string)])
			} else {
				row = append(row, "")
			}
		}
		row = append(row, item["siteName"])
		row = append(row, item["number"])
		if !isBlindedRole || !attribute.AttributeInfo.Blind {
			row = append(row, item["name"])
		}
		row = append(row, item["batchNumber"])
		row = append(row, item["expirationDate"])
		//row = append(row, item["place"])
		row = append(row, strings.Join(orderNumber, ","))
		row = append(row, data.GetMedicineStatus(ctx, int(item["status"].(int32))))
		content[i] = row
	}

	var projectInfo []map[string]interface{}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": projectOID}}},
		{{Key: "$unwind", Value: "$envs"}},
		{{Key: "$match", Value: bson.M{"envs.id": envOID}}},
		{
			{
				Key: "$project",
				Value: bson.M{
					"_id":      0,
					"number":   "$info.number",
					"name":     "$info.name",
					"env":      "$envs.name",
					"timeZone": "$info.timeZoneStr",
				},
			},
		},
	}
	projectCursor, projectErr := tools.Database.Collection("project").Aggregate(nil, pipeline)
	if projectErr != nil {
		return errors.WithStack(projectErr)
	}
	err = projectCursor.All(nil, &projectInfo)
	if err != nil {
		return errors.WithStack(err)
	}

	var timeZone float64
	if projectInfo[0]["timeZone"] != nil {
		//timeZone = projectInfo[0]["timeZone"].(int32)
		timeZone, _ = strconv.ParseFloat(projectInfo[0]["timeZone"].(string), 64)
	} else {
		timeZone = float64(8)
	}
	hours := time.Duration(timeZone)
	minutes := time.Duration((timeZone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute

	now := time.Now().UTC().Add(duration).Format("20060102")

	number := ""
	env := ""
	if len(projectInfo) > 0 {
		number = projectInfo[0]["number"].(string)
		env = projectInfo[0]["env"].(string)
	}

	fileName := fmt.Sprintf("%s[%s]—%s—%s.xlsx", number, env, locales.Tr(ctx, "medicine_download_site_name"), now)
	err = tools.ExportExcelStream(ctx, fileName, title, content)
	if err != nil {
		return err
	}
	return nil
}

func (s *MedicineService) DownloadDtpSku(ctx *gin.Context, customerID string, projectID string, envID string, roleID string) error {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	cohortID := ctx.Query("cohortId")
	status := ctx.Query("status")
	field := ctx.Query("field")
	fieldValue := ctx.Query("fieldValue")
	subjectNumber := ctx.Query("subjectNumber")
	match := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if status != "" {
		match["status"], _ = strconv.Atoi(status)
	}
	var orderNumber = ""
	if field != "" && fieldValue != "" {
		if field != "order_number" {
			searchList := strings.Split(fieldValue, ",")
			multiFilter := bson.A{}
			for _, searchCondition := range searchList {
				multiFilter = append(multiFilter, bson.M{field: bson.M{"$regex": tools.EscapeSpecialWord(searchCondition), "$options": "im"}})
			}
			if len(multiFilter) > 0 {
				match["$or"] = multiFilter
			}
		} else {
			orderNumber = fieldValue
		}
	}
	me, err := tools.Me(ctx)
	if err != nil {
		return err
	}
	role, err := tools.GetRole(roleID)
	if err != nil {
		return err
	}
	pagePipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{
			"from": "subject", // 关联的集合名称
			"let": bson.M{
				"subject_id": "$subject_id", // 将当前文档的 subject_id 赋值给变量 subject_id
			},
			"pipeline": bson.A{ // 使用 pipeline 添加过滤条件
				bson.M{
					"$match": bson.M{
						"$expr": bson.M{
							"$and": bson.A{
								bson.M{"$eq": bson.A{"$_id", "$$subject_id"}}, // 关联条件：_id 等于 subject_id
								bson.M{"$ne": bson.A{"$deleted", true}},       // 过滤条件：deleted 不为 true
							},
						},
					},
				},
			},
			"as": "subject", // 关联结果的字段名
		}}},
		{{Key: "$unwind", Value: "$subject"}},
		{{Key: "$unwind", Value: "$subject.info"}},
		{{Key: "$match", Value: bson.M{"subject.info.name": "shortname", "subject.info.value": bson.M{"$regex": subjectNumber}}}},
		{{Key: "$lookup", Value: bson.M{"from": "medicine_order", "localField": "order_id", "foreignField": "_id", "as": "medicine_order"}}},
		{{Key: "$unwind", Value: "$medicine_order"}},
		{{Key: "$match", Value: bson.M{"medicine_order.order_number": bson.M{"$regex": orderNumber}}}},
		{{Key: "$lookup", Value: bson.M{"from": "dispensing", "localField": "medicine_order.dispensing_id", "foreignField": "_id", "as": "dispensing"}}},
		{{Key: "$unwind", Value: "$dispensing"}},
		{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "subject.project_site_id", "foreignField": "_id", "as": "project_site"}}},
		{{Key: "$unwind", Value: "$project_site"}},
		{{Key: "$match", Value: bson.M{"project_site.deleted": 2}}},
		{{Key: "$match", Value: bson.M{"status": bson.M{"$ne": 1}}}},
		{{Key: "$sort", Value: bson.D{{"number", 1}}}},
		{{
			Key: "$project",
			Value: bson.M{
				"id":            "$_id",
				"_id":           0,
				"subjectNumber": "$subject.info.value",
				"orderNumber":   "$medicine_order.order_number",
				"visitNumber":   "$dispensing.visit_info.name",
				"visitSign":     "$dispensing.visit_sign",
				"reissue":       "$dispensing.reissue",
				"number":        1,
				"name":          1,

				"country":        bson.M{"$first": "$project_site.country"},
				"siteName":       models.ProjectSiteNameLookUpBson(ctx),
				"batchNumber":    "$batch_number",
				"expirationDate": "$expiration_date",
				"status":         1,
			},
		}},
	}
	if role.Scope == "site" {
		pagePipeline = mongo.Pipeline{
			{{Key: "$match", Value: match}},
			{{Key: "$lookup", Value: bson.M{
				"from": "subject", // 关联的集合名称
				"let": bson.M{
					"subject_id": "$subject_id", // 将当前文档的 subject_id 赋值给变量 subject_id
				},
				"pipeline": bson.A{ // 使用 pipeline 添加过滤条件
					bson.M{
						"$match": bson.M{
							"$expr": bson.M{
								"$and": bson.A{
									bson.M{"$eq": bson.A{"$_id", "$$subject_id"}}, // 关联条件：_id 等于 subject_id
									bson.M{"$ne": bson.A{"$deleted", true}},       // 过滤条件：deleted 不为 true
								},
							},
						},
					},
				},
				"as": "subject", // 关联结果的字段名
			}}},
			{{Key: "$unwind", Value: "$subject"}},
			{{Key: "$unwind", Value: "$subject.info"}},
			{{Key: "$match", Value: bson.M{"subject.info.name": "shortname", "subject.info.value": bson.M{"$regex": subjectNumber}}}},
			{{Key: "$lookup", Value: bson.M{"from": "medicine_order", "localField": "order_id", "foreignField": "_id", "as": "medicine_order"}}},
			{{Key: "$unwind", Value: "$medicine_order"}},
			{{Key: "$match", Value: bson.M{"medicine_order.order_number": bson.M{"$regex": orderNumber}}}},
			{{Key: "$lookup", Value: bson.M{"from": "dispensing", "localField": "medicine_order.dispensing_id", "foreignField": "_id", "as": "dispensing"}}},
			{{Key: "$unwind", Value: "$dispensing"}},
			{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "subject.project_site_id", "foreignField": "_id", "as": "project_site"}}},
			{{Key: "$unwind", Value: "$project_site"}},
			{{Key: "$match", Value: bson.M{"project_site.deleted": 2}}},
			{{Key: "$match", Value: bson.M{"status": bson.M{"$ne": 1}}}},
			{{Key: "$lookup", Value: bson.M{"from": "user_site", "localField": "medicine_order.receive_id", "foreignField": "site_id", "as": "user_site"}}},
			{{Key: "$unwind", Value: "$user_site"}},
			{{Key: "$match", Value: bson.M{"user_site.user_id": me.ID}}},
			{{Key: "$sort", Value: bson.D{{"number", 1}}}},
			{{
				Key: "$project",
				Value: bson.M{
					"id":            "$_id",
					"_id":           0,
					"subjectNumber": "$subject.info.value",
					"orderNumber":   "$medicine_order.order_number",
					"visitNumber":   "$dispensing.visit_info.name",
					"visitSign":     "$dispensing.visit_sign",
					"reissue":       "$dispensing.reissue",
					"number":        1,
					"name":          1,

					"country":        bson.M{"$first": "$project_site.country"},
					"siteName":       models.ProjectSiteNameLookUpBson(ctx),
					"batchNumber":    "$batch_number",
					"expirationDate": "$expiration_date",
					"status":         1,
				},
			}},
		}
	}

	var d []map[string]interface{}
	cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pagePipeline)
	if err != nil {
		return errors.WithStack(err)

	}
	err = cursor.All(nil, &d)
	if err != nil {
		return errors.WithStack(err)

	}

	var firstAttribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, bson.M{"env_id": envOID}, &options.FindOneOptions{
		Sort: bson.D{{"_id", 1}},
	}).Decode(&firstAttribute)
	if err != nil {
		return errors.WithStack(err)
	}
	attribute, err := s.GetAttribute(ctx, customerID, projectID, envID, cohortID)

	if err != nil {
		return errors.WithStack(err)
	}
	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return err
	}
	var title []interface{}
	subjectReplaceText := GetSubjectReplaceText(ctx, firstAttribute)
	title = append(title, subjectReplaceText)
	title = append(title, locales.Tr(ctx, "medicine_download_number"))
	if !isBlindedRole || !attribute.AttributeInfo.Blind {
		title = append(title, locales.Tr(ctx, "medicine_download_name"))
	}
	title = append(title, locales.Tr(ctx, "export.dispensing.visit_number"))
	title = append(title, locales.Tr(ctx, "medicine_download_batch"))
	title = append(title, locales.Tr(ctx, "medicine_download_expiredDate"))
	title = append(title, locales.Tr(ctx, "medicine_download_orderNumber"))
	countries := bson.M{}
	if attribute.AttributeInfo.CountryLayered {
		countries, err = database.GetCountries(ctx)
		title = append(title, locales.Tr(ctx, "common.country"))
	}

	title = append(title, locales.Tr(ctx, "medicine_download_site"))
	title = append(title, locales.Tr(ctx, "medicine_download_status"))

	content := make([][]interface{}, len(d))
	for i := 0; i < len(d); i++ {
		item := d[i]
		var row []interface{}
		row = append(row, item["subjectNumber"])
		row = append(row, item["number"])
		if !isBlindedRole || !attribute.AttributeInfo.Blind {
			row = append(row, item["name"])
		}
		// 访视名称-访视类型
		if item["visitSign"].(bool) {
			if int(item["reissue"].(int32)) == 1.0 {
				item["visitNumber"] = item["visitNumber"].(string) + "-" + locales.Tr(ctx, "export.dispensing.reissue")
			} else {
				item["visitNumber"] = item["visitNumber"].(string) + "-" + locales.Tr(ctx, "export.dispensing.outVisit")
			}
		}
		row = append(row, item["visitNumber"])
		row = append(row, item["batchNumber"])
		row = append(row, item["expirationDate"])
		row = append(row, item["orderNumber"])
		if attribute.AttributeInfo.CountryLayered {
			row = append(row, countries[item["country"].(string)])
		}
		row = append(row, item["siteName"])
		row = append(row, data.GetMedicineStatus(ctx, int(item["status"].(int32))))
		content[i] = row
	}

	var projectInfo []map[string]interface{}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": projectOID}}},
		{{Key: "$unwind", Value: "$envs"}},
		{{Key: "$match", Value: bson.M{"envs.id": envOID}}},
		{
			{
				Key: "$project",
				Value: bson.M{
					"_id":      0,
					"number":   "$info.number",
					"name":     "$info.name",
					"env":      "$envs.name",
					"timeZone": "$info.timeZoneStr",
				},
			},
		},
	}
	projectCursor, projectErr := tools.Database.Collection("project").Aggregate(nil, pipeline)
	if projectErr != nil {
		return errors.WithStack(projectErr)
	}
	err = projectCursor.All(nil, &projectInfo)
	if err != nil {
		return errors.WithStack(err)
	}

	var timeZone float64
	if projectInfo[0]["timeZone"] != nil {
		//timeZone = projectInfo[0]["timeZone"].(int32)
		timeZone, _ = strconv.ParseFloat(projectInfo[0]["timeZone"].(string), 64)
	} else {
		timeZone = float64(8)
	}
	hours := time.Duration(timeZone)
	minutes := time.Duration((timeZone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute

	now := time.Now().UTC().Add(duration).Format("20060102")

	number := ""
	env := ""
	if len(projectInfo) > 0 {
		number = projectInfo[0]["number"].(string)
		env = projectInfo[0]["env"].(string)
	}

	fileName := fmt.Sprintf("%s[%s]—%s—%s.xlsx", number, env, locales.Tr(ctx, "medicine_download_dtp_sku"), now)
	err = tools.ExportExcelStream(ctx, fileName, title, content)
	if err != nil {
		return err
	}
	return nil
}

func (s *MedicineService) DeleteData(ctx *gin.Context, deleteData models.DeleteData) error {
	customerOID := deleteData.CustomerID
	envOID := deleteData.EnvironmentID
	OID := envOID
	// if deleteData.CohortID != primitive.NilObjectID {
	// 	OID = deleteData.CohortID
	// }

	//删除的时候，判断是否存在订单中，如果是不允许删除
	for _, delId := range deleteData.DeleteIds {
		pipelineMatch := bson.M{"env_id": envOID, "medicines": delId}
		var total []map[string]interface{}
		cur, err := tools.Database.Collection("medicine_order").Aggregate(nil, mongo.Pipeline{
			{{Key: "$match", Value: pipelineMatch}},
			{{Key: "$count", Value: "total"}},
		})
		if err != nil {
			return errors.WithStack(err)
		}
		err = cur.All(nil, &total)
		if err != nil {
			return errors.WithStack(err)
		}
		var count int32 = 0
		if len(total) > 0 {
			count = total[0]["total"].(int32)
		}
		if count > 0 {
			var medicine models.Medicine
			err = tools.Database.Collection("medicine").FindOne(nil, bson.M{"_id": delId}).Decode(&medicine)
			if err != nil {
				return errors.WithStack(err)
			}
			return tools.BuildCustomError(medicine.Number + locales.Tr(ctx, "medicine_delete_error"))
		}
	}

	insertDeleteMedicinesLog(ctx, nil, OID, 3, deleteData.DeleteIds)
	match := bson.M{
		"customer_id": customerOID,
		"env_id":      envOID,
		"_id":         bson.M{"$in": deleteData.DeleteIds},
	}
	if _, err := tools.Database.Collection("medicine").DeleteMany(nil, match); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func insertDeleteMedicinesLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, ids []primitive.ObjectID) error {
	var medicines []models.Medicine
	cursor, err := tools.Database.Collection("medicine").Find(nil, bson.M{"_id": bson.M{"$in": ids}})
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &medicines)
	if err != nil {
		return errors.WithStack(err)
	}
	var deleteMedicines string
	for _, v := range medicines {
		deleteMedicines = deleteMedicines + v.Number + "    "
	}
	// 保存项目日志
	var OperationLogFieldGroups []models.OperationLogFieldGroup

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.medicinesList",
		TranKey: "operation_log.uploadMedicines.deleteMedicines",
		Old: models.OperationLogField{
			Type:  2,
			Value: deleteMedicines,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: "",
		},
	})

	marks := []models.Mark{}
	marks = append(marks,
		models.Mark{
			Label: "operation_log.label.deleteMedicine",
			Value: fmt.Sprintf("%d", len(ids)),
			Blind: false,
		})

	err = tools.SaveOperation(ctx, sctx, "operation_log.module.medicinesList_delete", OID, types, OperationLogFieldGroups, marks, OID)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (s *MedicineService) GetOtherMedicineSiteSkuWithDTP(ctx *gin.Context, customerID string, projectID string, envID string, subject string, roleID string, start int, limit int) (map[string]interface{}, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)

	match := bson.M{
		"customer_id": customerOID,
		"project_id":  projectOID,
		"env_id":      envOID,
	}
	subjectMatch := bson.M{"subject.info.name": "shortname"}
	if subject != "" {
		subjectMatch = bson.M{"subject.info.name": "shortname", "subject.info.value": bson.M{"$regex": subject}}
	}
	user, _ := tools.Me(ctx)

	countPipeline := mongo.Pipeline{
		{{"$match", match}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "subject",
			"localField":   "subject_id",
			"foreignField": "_id",
			"as":           "subject",
		}}},
		{{"$unwind", "$subject"}},
		{{"$unwind", "$subject.info"}},
		{{"$match", subjectMatch}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "project_site",
			"localField":   "institute_id",
			"foreignField": "_id",
			"as":           "project_site",
		}}},
		{{Key: "$match", Value: bson.M{"project_site.deleted": 2}}},
		{{Key: "$count", Value: "count"}},
	}

	pagePipeline := mongo.Pipeline{
		{{"$match", match}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "subject",
			"localField":   "subject_id",
			"foreignField": "_id",
			"as":           "subject",
		}}},
		{{"$unwind", "$subject"}},
		{{"$unwind", "$subject.info"}},
		{{"$match", subjectMatch}},
		{{Key: "$skip", Value: start}},
		{{Key: "$limit", Value: limit}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "project_site",
			"localField":   "institute_id",
			"foreignField": "_id",
			"as":           "project_site",
		}}},
		{{"$unwind", "$project_site"}},
		{{Key: "$match", Value: bson.M{"project_site.deleted": 2}}},
		{{"$project", bson.M{
			"subject":             "$subject.info.value",
			"apply_count":         "$info.apply_count",
			"batch":               "$info.batch",
			"count":               "$info.count",
			"expire_date":         "$info.expire_date",
			"in_transit_count":    "$info.in_transit_count",
			"lost_count":          "$info.lost_count",
			"name":                "$info.name",
			"quarantined_count":   "$info.quarantined_count",
			"to_be_confirm_count": "$info.to_be_confirm_count",
			"to_be_send_count":    "$info.to_be_send_count",
			"used_count":          "$info.used_count",

			"site": models.ProjectSiteNameLookUpBson(ctx),
		}}},
	}
	role, err := tools.GetRole(roleID)
	if err != nil {
		return nil, err
	}
	if role.Scope == "site" {
		countPipeline = mongo.Pipeline{
			{{"$match", match}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "subject",
				"localField":   "subject_id",
				"foreignField": "_id",
				"as":           "subject",
			}}},
			{{"$unwind", "$subject"}},
			{{"$unwind", "$subject.info"}},
			{{"$match", subjectMatch}},
			{{Key: "$lookup", Value: bson.M{"from": "user_site", "localField": "institute_id", "foreignField": "site_id", "as": "user_site"}}},
			{{Key: "$unwind", Value: "$user_site"}},
			{{Key: "$match", Value: bson.M{"user_site.user_id": user.ID}}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "project_site",
				"localField":   "institute_id",
				"foreignField": "_id",
				"as":           "project_site",
			}}},
			{{Key: "$match", Value: bson.M{"project_site.deleted": 2}}},
			{{Key: "$count", Value: "count"}},
		}

		pagePipeline = mongo.Pipeline{
			{{"$match", match}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "subject",
				"localField":   "subject_id",
				"foreignField": "_id",
				"as":           "subject",
			}}},
			{{"$unwind", "$subject"}},
			{{"$unwind", "$subject.info"}},
			{{"$match", subjectMatch}},
			{{Key: "$skip", Value: start}},
			{{Key: "$limit", Value: limit}},
			{{Key: "$lookup", Value: bson.M{"from": "user_site", "localField": "institute_id", "foreignField": "site_id", "as": "user_site"}}},
			{{Key: "$unwind", Value: "$user_site"}},
			{{Key: "$match", Value: bson.M{"user_site.user_id": user.ID}}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "project_site",
				"localField":   "institute_id",
				"foreignField": "_id",
				"as":           "project_site",
			}}},
			{{"$unwind", "$project_site"}},
			{{Key: "$match", Value: bson.M{"project_site.deleted": 2}}},
			{{"$project", bson.M{
				"subject":             "$subject.info.value",
				"apply_count":         "$info.apply_count",
				"batch":               "$info.batch",
				"count":               "$info.count",
				"expire_date":         "$info.expire_date",
				"in_transit_count":    "$info.in_transit_count",
				"lost_count":          "$info.lost_count",
				"name":                "$info.name",
				"quarantined_count":   "$info.quarantined_count",
				"to_be_confirm_count": "$info.to_be_confirm_count",
				"to_be_send_count":    "$info.to_be_send_count",
				"used_count":          "$info.used_count",

				"site": models.ProjectSiteNameLookUpBson(ctx),
			}}},
		}
	}

	cursor, err := tools.Database.Collection("medicine_other_institute").Aggregate(nil, countPipeline)
	var count []map[string]interface{}
	err = cursor.All(nil, &count)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	cursor, err = tools.Database.Collection("medicine_other_institute").Aggregate(nil, pagePipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	total := 0
	if len(count) > 0 {
		total = int(count[0]["count"].(int32))
	}
	var data []map[string]interface{}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return nil, err
	}
	for _, item := range data {
		if isBlindedRole {
			isBlindedDrug, _ := tools.IsBlindedDrug(envOID, item["name"].(string))
			if isBlindedDrug {
				saltName, salt := tools.Encrypt(item["name"].(string))
				item["name"] = tools.BlindData
				item["saltName"] = saltName
				item["salt"] = salt
			}
		}
	}

	return map[string]interface{}{"total": total, "item": data}, nil
}

func (s *MedicineService) GetMedicineForOrderGroup(ctx *gin.Context, customerID string, envID string, cohortId string, roleID string) (map[string]interface{}, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	//cohortOID, _ := primitive.ObjectIDFromHex(cohortId)
	sendID := ctx.Query("sendId")

	isBlindedRole := true
	if roleID != "" {
		isBlindedRole, _ = tools.IsBlindedRole(roleID)
	}
	filter := bson.M{"customer_id": customerOID, "env_id": envOID, "status": bson.M{"$in": [4]int{1, 6, 7}}, "frozen": bson.M{"$ne": true}}
	packageFilter := bson.M{"customer_id": customerOID, "env_id": envOID, "status": bson.M{"$in": [4]int{1, 6, 7}}, "package_number": bson.M{"$nin": [2]interface{}{nil, ""}}, "frozen": bson.M{"$ne": true}}
	if sendID != "" {
		sendOID, _ := primitive.ObjectIDFromHex(sendID)
		//判断该sendId是仓库还是中心
		match := bson.M{"_id": sendOID}
		siteCount, _ := tools.Database.Collection("project_site").CountDocuments(nil, match)
		if siteCount > 0 {
			filter = bson.M{"site_id": sendOID, "customer_id": customerOID, "env_id": envOID, "status": bson.M{"$in": [4]int{1, 6, 7, 14}}, "frozen": bson.M{"$ne": true}}
			packageFilter = bson.M{"site_id": sendOID, "customer_id": customerOID, "env_id": envOID, "status": bson.M{"$in": [4]int{1, 6, 7, 14}}, "package_number": bson.M{"$nin": [2]interface{}{nil, ""}}, "frozen": bson.M{"$ne": true}}

		} else {
			filter["storehouse_id"] = sendOID
			packageFilter["storehouse_id"] = sendOID
		}
	}

	//查询哪些药物是按照包装运输
	isOpenPackage, _, _, packageConfigs, mixedPackages, _ := tools.IsOpenPackage(envOID)
	if isOpenPackage {
		var packageDrugNames []string
		for key, _ := range packageConfigs {
			packageDrugNames = append(packageDrugNames, key)
		}
		filter["name"] = bson.M{"$nin": packageDrugNames}
	}

	var data []models.MedicinesGroup
	var groupUnPackData []map[string]interface{}
	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$group", Value: bson.M{
			"_id": bson.M{
				"name":            "$name",
				"expiration_date": "$expiration_date",
				"batch_number":    "$batch_number",
			},
			"count": bson.M{
				"$sum": 1,
			},
		}}},
		{{Key: "$sort", Value: bson.D{
			{"_id.expiration_date", 1},
			{"_id.batch_number", 1},
			{"_id.name", 1},
		}}},
		{{Key: "$project", Value: bson.M{
			"_id": "$_id", "count": "$count", "packageMethod": "false"}}},
	}
	countPipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$group", Value: bson.M{
			"_id": bson.M{
				"name":            "$name",
				"expiration_date": "$expiration_date",
				"batch_number":    "$batch_number",
			},
		}}},
		{{Key: "$count", Value: "count"}},
	}
	cursor, err := tools.Database.Collection("medicine").Aggregate(ctx, countPipeline)
	var count []map[string]interface{}
	err = cursor.All(nil, &count)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	total := 0
	if len(count) > 0 {
		total = int(count[0]["count"].(int32))
	}
	cursor, err = tools.Database.Collection("medicine").Aggregate(nil, pipepine)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &groupUnPackData)
	for _, v := range groupUnPackData {
		unPackId := v["_id"].(map[string]interface{})
		name := unPackId["name"].(string)
		var strArray []string
		// 给数组赋值
		strArray = append(strArray, name)
		expirationDate := unPackId["expiration_date"].(string)
		batchNumber := unPackId["batch_number"].(string)
		medicinesGroup := models.MedicinesGroup{
			MedicinesGroupID: models.MedicinesGroupID{
				Name:           strArray,
				ExpirationDate: expirationDate,
				BatchNumber:    batchNumber,
			},
			Count: int(v["count"].(int32)),
		}
		data = append(data, medicinesGroup)
	}

	// 包装运输
	if isOpenPackage && len(packageConfigs) > 0 {
		//var pdata []map[string]interface{}
		for _, mixedPackage := range mixedPackages {
			var packageData []models.MedicinesGroup
			var packageDrugNames []string
			var packageNumber int
			for _, packageConfig := range mixedPackage.PackageConfig {
				packageDrugNames = append(packageDrugNames, packageConfig.Name)
				packageNumber = packageNumber + packageConfig.Number
			}
			packageFilter["name"] = bson.M{"$in": packageDrugNames}
			packagePipepine := mongo.Pipeline{
				{{Key: "$match", Value: packageFilter}},
				{{Key: "$group", Value: bson.M{"_id": bson.M{"package_number": "$package_number", "batch_number": "$batch_number", "expiration_date": "$expiration_date"}, "count": bson.M{"$sum": 1}}}},
				{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$count", packageNumber}}}}},
				{{Key: "$group", Value: bson.M{"_id": bson.M{"name": packageDrugNames, "batch_number": "$_id.batch_number", "expiration_date": "$_id.expiration_date"}, "count": bson.M{"$sum": 1}}}},
				{{Key: "$sort", Value: bson.D{
					{"_id.expiration_date", 1},
					{"_id.batch_number", 1},
				}}},
				{{Key: "$project", Value: bson.M{"_id": "$_id", "count": "$count", "packageMethod": "true"}}},
			}
			packageCursor, err := tools.Database.Collection("medicine").Aggregate(nil, packagePipepine)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = packageCursor.All(nil, &packageData)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			data = append(data, packageData...)
		}

	}

	if isBlindedRole {
		for index, value := range data {
			var salts []models.Salt
			var names []string
			for _, v := range value.Name {
				isBlindedDrug, _ := tools.IsBlindedDrug(envOID, v)
				if isBlindedDrug {
					salt := models.Salt{}
					saltName, saltV := tools.Encrypt(v)
					salt.Name = tools.BlindData
					salt.SaltName = saltName
					salt.Salt = saltV
					salts = append(salts, salt)
					names = append(names, tools.BlindData)
				} else {
					names = append(names, v)
				}
			}
			value.Name = names
			value.Salts = salts
			data[index] = value
		}
	}
	return map[string]any{"total": total, "items": data}, nil
}

func (s *MedicineService) GetMedicineForOrder(ctx *gin.Context, recoveryOrderGroupData models.RecoveryOrderGroupData) (map[string]interface{}, error) {
	customerOID := recoveryOrderGroupData.CustomerID
	envOID := recoveryOrderGroupData.EnvironmentID
	sendOID := recoveryOrderGroupData.SendID
	isBlindedRole := false
	if recoveryOrderGroupData.RoleID != primitive.NilObjectID {
		isBlindedRole, _ = tools.IsBlindedRole(recoveryOrderGroupData.RoleID.Hex())
	}
	var names []string
	if len(recoveryOrderGroupData.Salts) > 0 {
		for index, salt := range recoveryOrderGroupData.Salts {
			if salt.Salt != "" && salt.SaltName != "" {
				name := tools.Decrypt(salt.SaltName, salt.Salt)
				salt.Name = name
				names = append(names, name)
			} else {
				names = append(names, salt.Name)
			}
			recoveryOrderGroupData.Salts[index] = salt
		}
	} else {
		names = recoveryOrderGroupData.Name
	}

	filter := bson.M{"customer_id": customerOID, "env_id": envOID, "status": bson.M{"$in": [4]int{1, 6, 7}}, "frozen": bson.M{"$ne": true}}
	if sendOID != primitive.NilObjectID {
		//判断该sendId是仓库还是中心
		match := bson.M{"_id": sendOID}
		siteCount, _ := tools.Database.Collection("project_site").CountDocuments(nil, match)
		if siteCount > 0 {
			filter = bson.M{"site_id": sendOID, "customer_id": customerOID, "env_id": envOID, "status": bson.M{"$in": [4]int{1, 6, 7, 14}}, "frozen": bson.M{"$ne": true}}

		} else {
			filter["storehouse_id"] = sendOID
		}
	}

	//判断该药物是否按照包装运输
	isOpenPackage, packageAllDrugNames, packageDrugNames, mixPackageConfig, _, _ := tools.IsOpenPackage(envOID)
	if isOpenPackage && packageDrugNames[names[0]] > 0 {
		filter["package_number"] = bson.M{"$nin": [2]interface{}{nil, ""}}
	}

	if len(names) > 0 {
		filter["name"] = bson.M{"$in": names}
	}
	if recoveryOrderGroupData.BatchNumber != "" {
		if recoveryOrderGroupData.BatchNumber != "-" {
			filter["batch_number"] = recoveryOrderGroupData.BatchNumber
		} else {
			filter["batch_number"] = ""
		}
	}
	if recoveryOrderGroupData.ExpirationDate != "" {
		if recoveryOrderGroupData.ExpirationDate != "-" {
			filter["expiration_date"] = recoveryOrderGroupData.ExpirationDate
		} else {
			filter["expiration_date"] = ""
		}

	}
	if recoveryOrderGroupData.Status != 0 {
		filter["status"] = recoveryOrderGroupData.Status
	}
	if recoveryOrderGroupData.Number != "" {
		filter["number"] = bson.M{"$regex": primitive.Regex{Pattern: ".*" + recoveryOrderGroupData.Number + ".*", Options: "i"}}
	}

	countPipeine := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$count", Value: "count"}},
	}

	if isOpenPackage && packageDrugNames[names[0]] > 0 {
		//判断是否是混包
		for _, mixPackage := range mixPackageConfig {
			medicineNameIndex := arrays.ContainsString(mixPackage, names[0])
			if medicineNameIndex != -1 { //存在
				filter["name"] = bson.M{"$in": mixPackage}
				break
			}
		}
		countPipeine = mongo.Pipeline{
			{{Key: "$match", Value: filter}},
			{{Key: "$group", Value: bson.M{"_id": bson.M{"package_number": "$package_number"}, "availableCount": bson.M{"$sum": 1}, "ids": bson.M{"$addToSet": "$_id"}}}},
			{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$availableCount", packageAllDrugNames[names[0]]}}}}},
			{{Key: "$count", Value: "count"}},
		}
	}

	cursor, err := tools.Database.Collection("medicine").Aggregate(nil, countPipeine)
	var count []map[string]interface{}
	err = cursor.All(nil, &count)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	total := 0
	if len(count) > 0 {
		total = int(count[0]["count"].(int32))
	}

	var data []map[string]interface{}

	if isOpenPackage && packageDrugNames[names[0]] > 0 {
		//判断查询条件是否为空
		if recoveryOrderGroupData.Number != "" {
			//先查询出记录，然后判断是否是整包
			var queryData []map[string]interface{}
			queryPipeline := mongo.Pipeline{
				{{Key: "$match", Value: filter}},
				{{Key: "$sort", Value: bson.D{{"number", 1}}}},
				{{Key: "$project", Value: models.MedicineFields}},
			}
			cursor, err = tools.Database.Collection("medicine").Aggregate(nil, queryPipeline)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &queryData)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			var newMedicines []map[string]interface{}
			hasData := make(map[string]string)
			//查询字段不为空的时候，包装方式打开并在设置中配置，需要去把这个包装中的所有数据查询出来
			for _, medicine := range queryData {
				name := medicine["name"].(string)
				hasPackageNumber, hasOk := hasData[name]
				packageNumber := medicine["package_number"].(string)
				//判断这个订单是按照包装还是单品
				if packageNumber != "" && (!hasOk || (hasOk && hasPackageNumber != packageNumber)) {
					hasData[name] = packageNumber
					medicinesData := make([]map[string]interface{}, 0)
					medicineFilter := bson.M{"env_id": envOID, "package_number": packageNumber}
					pipepine := mongo.Pipeline{
						{{Key: "$match", Value: medicineFilter}},
						{{Key: "$project", Value: models.MedicineFields}},
					}
					cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pipepine)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					err = cursor.All(nil, &medicinesData)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					newMedicines = append(newMedicines, medicinesData...)
				}

			}

			sort.SliceStable(newMedicines, func(i int, j int) bool {
				datai := newMedicines[i]
				dataj := newMedicines[j]
				if datai["name"].(string) < dataj["name"].(string) {
					return true
				}

				if datai["name"].(string) > dataj["name"].(string) {
					return false
				}

				if datai["package_number"].(string) != "" && dataj["package_number"].(string) != "" {
					if datai["package_number"].(string) < dataj["package_number"].(string) {
						return true
					}

					if datai["package_number"].(string) > dataj["package_number"].(string) {
						return false
					}
				} else {
					return false
				}
				return datai["number"].(string) < dataj["number"].(string)
			})

			data = newMedicines

		} else {
			var dataIDs []primitive.ObjectID
			drugData := []map[string]interface{}{}
			packagePipeline := mongo.Pipeline{
				{{Key: "$match", Value: filter}},
				{{Key: "$group", Value: bson.M{"_id": bson.M{"package_number": "$package_number"}, "availableCount": bson.M{"$sum": 1}, "ids": bson.M{"$addToSet": "$_id"}}}},
				{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$availableCount", packageAllDrugNames[names[0]]}}}}},
			}
			cursor, err = tools.Database.Collection("medicine").Aggregate(nil, packagePipeline)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &drugData)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if len(drugData) > 0 {
				for _, drug := range drugData {
					drugs := drug["ids"].(primitive.A)
					for _, id := range drugs {
						dataIDs = append(dataIDs, id.(primitive.ObjectID))
					}
				}
				filter["_id"] = bson.M{"$in": dataIDs}
				pipeline := mongo.Pipeline{
					{{Key: "$match", Value: filter}},
					{{Key: "$sort", Value: bson.D{{"package_number", 1}, {"number", 1}}}},
					{{Key: "$project", Value: models.MedicineFields}},
				}
				cursor, err = tools.Database.Collection("medicine").Aggregate(nil, pipeline)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = cursor.All(nil, &data)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			} else {
				return map[string]interface{}{"total": total, "items": data, "packageIsOpen": isOpenPackage, "packageDrugNames": packageDrugNames}, nil
			}
		}
	} else {
		pipeline := mongo.Pipeline{
			{{Key: "$match", Value: filter}},
			{{Key: "$sort", Value: bson.D{{"number", 1}}}},
			{{Key: "$project", Value: models.MedicineFields}},
		}

		//如果是包装运输的药物，需要查出所有记录，不可以按照limit查询，要不然分页数据有问题，不正确
		if recoveryOrderGroupData.Start != 0 && !isOpenPackage {
			pipeline = append(pipeline, bson.D{{Key: "$skip", Value: recoveryOrderGroupData.Start}})
		}
		if recoveryOrderGroupData.Limit != 0 && !isOpenPackage {
			pipeline = append(pipeline, bson.D{{Key: "$limit", Value: recoveryOrderGroupData.Limit}})
		}
		cursor, err = tools.Database.Collection("medicine").Aggregate(nil, pipeline)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &data)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	isBlindedDrug, _ := tools.IsBlindDrugMap(envOID)

	if isBlindedRole {
		for index, value := range data {
			name := value["name"].(string)
			if isBlindedDrug[name] {
				value["name"] = tools.BlindData
				data[index] = value
			}

		}
	}
	return map[string]interface{}{"total": total, "items": data, "packageIsOpen": isOpenPackage, "packageDrugNames": packageDrugNames}, nil
}

func (s *MedicineService) GetMedicineForOrderAll(ctx *gin.Context, recoveryOrderGroupData models.RecoveryOrderGroupData) (map[string]interface{}, error) {
	customerOID := recoveryOrderGroupData.CustomerID
	envOID := recoveryOrderGroupData.EnvironmentID
	sendOID := recoveryOrderGroupData.SendID
	var names []string
	if len(recoveryOrderGroupData.Salts) > 0 {
		for index, salt := range recoveryOrderGroupData.Salts {
			if salt.Salt != "" && salt.SaltName != "" {
				name := tools.Decrypt(salt.SaltName, salt.Salt)
				salt.Name = name
				names = append(names, name)
			} else {
				names = append(names, salt.Name)
			}
			recoveryOrderGroupData.Salts[index] = salt
		}
	} else {
		names = recoveryOrderGroupData.Name
	}

	filter := bson.M{"customer_id": customerOID, "env_id": envOID, "status": bson.M{"$in": [4]int{1, 6, 7}}, "frozen": bson.M{"$ne": true}}
	if sendOID != primitive.NilObjectID {
		//判断该sendId是仓库还是中心
		match := bson.M{"_id": sendOID}
		siteCount, _ := tools.Database.Collection("project_site").CountDocuments(nil, match)
		if siteCount > 0 {
			filter = bson.M{"site_id": sendOID, "customer_id": customerOID, "env_id": envOID, "status": bson.M{"$in": [4]int{1, 6, 7, 14}}, "frozen": bson.M{"$ne": true}}
		} else {
			filter["storehouse_id"] = sendOID
		}
	}

	if len(names) > 0 {
		filter["name"] = bson.M{"$in": names}
	}
	if recoveryOrderGroupData.BatchNumber != "" && recoveryOrderGroupData.BatchNumber != "-" {
		filter["batch_number"] = recoveryOrderGroupData.BatchNumber
	}
	if recoveryOrderGroupData.ExpirationDate != "" && recoveryOrderGroupData.ExpirationDate != "-" {
		filter["expiration_date"] = recoveryOrderGroupData.ExpirationDate
	}

	var data []map[string]interface{}
	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$project", Value: bson.D{{Key: "_id", Value: 1}}}},
	}

	//判断该药物是否按照包装运输
	isOpenPackage, packageAllDrugNames, packageDrugNames, packageConfigs, _, _ := tools.IsOpenPackage(envOID)
	if isOpenPackage && packageDrugNames[names[0]] > 0 {
		//判断是否是混包
		for _, mixPackage := range packageConfigs {
			medicineNameIndex := arrays.ContainsString(mixPackage, names[0])
			if medicineNameIndex != -1 { //存在
				filter["name"] = bson.M{"$in": mixPackage}
				break
			}
		}

		filter["package_number"] = bson.M{"$nin": [2]interface{}{nil, ""}}
		pipepine = mongo.Pipeline{
			{{Key: "$match", Value: filter}},
			{{Key: "$sort", Value: bson.D{{"_id", 1}}}},
			{{Key: "$group", Value: bson.M{"_id": bson.M{"package_number": "$package_number"}, "availableCount": bson.M{"$sum": 1}, "ids": bson.M{"$push": "$_id"}}}},
			{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$availableCount", packageAllDrugNames[names[0]]}}}}},
			{{Key: "$project", Value: bson.M{
				"_id": bson.M{"$first": "$ids"},
				"ids": "$ids",
			}}},
		}
	}

	cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pipepine)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var items []string
	var allItems []string
	for _, medicine := range data {
		items = append(items, medicine["_id"].(primitive.ObjectID).Hex())
		if isOpenPackage && packageDrugNames[names[0]] > 0 {
			ids := medicine["ids"].(primitive.A)
			for _, v := range ids {
				allItems = append(allItems, v.(primitive.ObjectID).Hex())
			}
		} else {
			allItems = append(allItems, medicine["_id"].(primitive.ObjectID).Hex())
		}
	}

	return map[string]interface{}{"items": items, "allItems": allItems}, nil
}

func (s *MedicineService) GetMedicineFreezeByIds(ctx *gin.Context, freezeMedicines models.UpdateMedicines) (map[string]interface{}, error) {

	filter := bson.M{"customer_id": freezeMedicines.CustomerID, "project_id": freezeMedicines.ProjectID, "env_id": freezeMedicines.EnvironmentID, "_id": bson.M{"$in": freezeMedicines.MedicineIds}}
	var data []models.MedicinePackageDrug
	cursor, err := tools.Database.Collection("medicine").Find(ctx, filter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var medicineData []models.MedicinePackageDrug
	var medicineIds []primitive.ObjectID
	isOpenPackage, packageDrugNames, _, _, _, _ := tools.IsOpenPackage(freezeMedicines.EnvironmentID)
	if isOpenPackage {
		hasMedicineIds := make(map[primitive.ObjectID]primitive.ObjectID)
		for _, medicine := range data {
			_, ok := hasMedicineIds[medicine.ID]
			if !ok {
				packageNumber := 0
				packageNumber, isPackage := packageDrugNames[medicine.Name]
				if isPackage && packageNumber > 0 && medicine.PackageNumber != "" {
					//混包了，不能按照研究产品查询
					filter := bson.M{"customer_id": medicine.CustomerID, "project_id": medicine.ProjectID, "env_id": medicine.EnvironmentID, "status": medicine.Status, "storehouse_id": medicine.StorehouseID,
						"package_number": medicine.PackageNumber, "expiration_date": medicine.ExpirationDate, "batch_number": medicine.BatchNumber}
					var packageData []models.MedicinePackageDrug
					cursor, err := tools.Database.Collection("medicine").Find(ctx, filter)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					err = cursor.All(nil, &packageData)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					if len(packageData) == packageNumber {
						for _, packageMedicine := range packageData {
							packageMedicine.PackageDrug = true
							medicineData = append(medicineData, packageMedicine)
							medicineIds = append(medicineIds, packageMedicine.ID)
							hasMedicineIds[packageMedicine.ID] = packageMedicine.ID
						}
					} else {
						medicineData = append(medicineData, medicine)
						medicineIds = append(medicineIds, medicine.ID)
					}
				} else {
					medicineData = append(medicineData, medicine)
					medicineIds = append(medicineIds, medicine.ID)
				}
			}
		}
	} else {
		medicineData = data
		medicineIds = freezeMedicines.MedicineIds
	}

	return map[string]interface{}{"ids": medicineIds, "medicines": medicineData}, nil
}

func (s *MedicineService) SiteForecast(ctx *gin.Context, envID string, roleID string) (interface{}, error) {
	role, err := tools.GetRole(roleID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	noticeSite := make([]models.NoticeSite, 0)

	envOID, _ := primitive.ObjectIDFromHex(envID)
	if role.Scope == "study" {
		noticeSite, err = task.ForecastMedicine(envOID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	} else {
		siteOIDs, err := tools.GetRoleSite(ctx, envID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if len(siteOIDs) > 0 {
			noticeSite, err = task.ForecastMedicine(envOID, siteOIDs)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
	}
	return noticeSite, err
}

// 根据研究产品名称、有效期和批次号获取数据
func (s *MedicineService) GetGroupNameDataNumber(
	ctx *gin.Context,
	status string,
	customerID string,
	projectID string,
	envID string) (map[string]interface{}, error) {

	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	statusInt, _ := strconv.Atoi(status)
	var data []map[string]interface{}

	//查询是否开启了包装运输
	configureFilter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	var drugPackageConfigure models.DrugPackageConfigureShow
	_ = tools.Database.Collection("drug_package_configure").FindOne(nil, configureFilter).Decode(&drugPackageConfigure)
	packageIsOpen, _, _, _, _, _ := tools.IsOpenPackage(envOID)

	// status 0待入仓 22待审核  23 审核失败
	filterA := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "status": statusInt}
	groupA := bson.M{"name": "$name", "batchNumber": "$batch_number", "expirationDate": "$expiration_date"}
	if packageIsOpen {
		groupA = bson.M{"packageNumber": "$package_number", "name": "$name", "batchNumber": "$batch_number", "expirationDate": "$expiration_date"}
	}
	// 审核失败的根据批次号和有效期修改药物名称
	// if status == "23" {
	// 	groupA = bson.M{"batchNumber": "$batch_number", "expirationDate": "$expiration_date"}
	// }
	// "medicineIds": bson.M{"$push": bson.M{ "_id": "$_id"}
	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: filterA}},
		{{Key: "$group", Value: bson.M{"_id": groupA, "count": bson.M{"$sum": 1}, "medicineIds": bson.M{"$push": bson.M{"_id": "$_id"}}}}},
		{{Key: "$project", Value: bson.M{
			"_id":            0,
			"medicineIds":    1,
			"count":          1,
			"batchNumber":    "$_id.batchNumber",
			"expirationDate": "$_id.expirationDate",
			"name":           "$_id.name",
			"packageNumber":  "$_id.packageNumber",
		}}},
		{{Key: "$sort", Value: bson.D{{"packageNumber", -1}}}},
	}
	cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pipepine)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return map[string]interface{}{"data": data, "packageIsOpen": packageIsOpen}, err
}

func (s *MedicineService) ToExamineFlowPath(ctx *gin.Context, toExamineFlowPathParameter models.ToExamineFlowPathParameter) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		packageIsOpen, _, _, _, mixedPackages, _ := tools.IsOpenPackage(toExamineFlowPathParameter.EnvironmentID)
		if toExamineFlowPathParameter.Sign == 0 || toExamineFlowPathParameter.MedicineParameter == nil || len(toExamineFlowPathParameter.MedicineParameter) == 0 {
			return nil, tools.BuildServerError(ctx, "common.wrong.parameters")
		}

		user, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		dateTime := time.Duration(time.Now().Unix())

		// 记录报表轨迹
		drugTrack := []interface{}{}
		// 药物轨迹
		var histories []models.History
		packageSign := false
		if toExamineFlowPathParameter.MedicineParameter != nil && len(toExamineFlowPathParameter.MedicineParameter) > 0 {
			for _, item := range toExamineFlowPathParameter.MedicineParameter {
				updateStatus := 0
				key := ""
				drugTrackRemarks := ""
				drugTrackStatus := 0
				if toExamineFlowPathParameter.Sign == 1 { // 审核
					if toExamineFlowPathParameter.RadioValue == 1 {
						// 待入仓
						updateStatus = 0
						key = "history.medicine.examinePassThrough"
						drugTrackStatus = 2
					} else {
						// 审核失败
						updateStatus = 23
						key = "history.medicine.examineRefuse"
						drugTrackStatus = 3
					}
				} else if toExamineFlowPathParameter.Sign == 2 { // 修改
					// 待审核
					updateStatus = 22
					key = "history.medicine.updateName"
					drugTrackStatus = 4
					if item.NewName != "" {
						drugTrackRemarks = "[" + item.Name + "]->[" + item.NewName + "]"
					} else {
						drugTrackRemarks = "[" + item.Name + "]->[" + item.Name + "]"
					}

					if packageIsOpen {
						for _, item := range toExamineFlowPathParameter.MedicineParameter {
							if item.PackageNumber == "" { // 修改的是单品药物
								// 修改了单品药物
								if item.NewName != "" {
									pcSign := false
									for _, pk := range mixedPackages {
										for _, pc := range pk.PackageConfig {
											if pc.Name == item.NewName {
												pcSign = true
												break
											}
										}
									}
									if pcSign {
										// 单品改为包装号药物，药物状态改为待扫码
										updateStatus = 21
										key = "history.medicine.updateNameScanCan"
										// 添加包装扫码任务
										packageSign = true
									}
								}
							} else { // 修改的是包装号药物
								if item.NewName != "" {
									// 改的整个包装号药物是否能和其它包装号匹配
									var medicineParamenters []models.MedicineParameter
									for _, tmp := range toExamineFlowPathParameter.MedicineParameter {
										if tmp.PackageNumber == item.PackageNumber {
											medicineParamenters = append(medicineParamenters, tmp)
										}
									}

									var packageConfigs []models.PackageConfig
									for _, pk := range mixedPackages {
										for _, pc := range pk.PackageConfig {
											for _, mp := range medicineParamenters {
												if pc.Name == mp.NewName {
													packageConfigs = pk.PackageConfig
													break
												}
											}
										}
									}

									pcCount := 0
									for _, pc := range packageConfigs {
										for _, mp := range medicineParamenters {
											newName := mp.Name
											if mp.NewName != "" {
												newName = mp.NewName
											}
											if pc.Name == newName && pc.Number == mp.Count {
												pcCount++
												break
											}
										}
									}

									// 报错 包装号不匹配
									if pcCount != len(packageConfigs) || pcCount != len(medicineParamenters) {
										return nil, tools.BuildServerError(ctx, "medicine_package_err")
									}
								}
							}
						}
					}
				} else { // 放行
					// 可用
					updateStatus = 1
					key = "history.medicine.release-usable"
					drugTrackStatus = 5
				}

				update := bson.M{
					"$set": bson.M{
						"status": updateStatus,
					},
				}

				// 写入入仓时间
				if updateStatus == 1 {
					update = bson.M{
						"$set": bson.M{
							"status":     updateStatus,
							"entry_time": time.Duration(time.Now().Unix()),
						},
					}
				}
				// 修改要修改药物名称
				if updateStatus == 22 || updateStatus == 21 {
					newName := ""
					if item.NewName != "" {
						newName = item.NewName
					} else {
						newName = item.Name
					}

					update = bson.M{
						"$set": bson.M{
							"status": updateStatus,
							"name":   newName,
						},
					}
				}
				updateFile := bson.M{
					"_id": bson.M{"$in": item.MesicineIds},
				}
				if _, err := tools.Database.Collection("medicine").UpdateMany(sctx, updateFile, update); err != nil {
					return nil, errors.WithStack(err)
				}

				// 记录轨迹报表数据
				for _, mid := range item.MesicineIds {
					drugTrack = append(drugTrack, models.DrugTrack{
						ID:            primitive.NewObjectID(),
						CustomerID:    toExamineFlowPathParameter.CustomerID,
						ProjectID:     toExamineFlowPathParameter.ProjectID,
						EnvironmentID: toExamineFlowPathParameter.EnvironmentID,
						MedicineID:    mid,
						Status:        drugTrackStatus,
						Remarks:       drugTrackRemarks, //备注
						OperatorID:    user.ID,
						OperatorTime:  dateTime,
					})

					//药物history
					history := models.History{
						Key: key,
						OID: mid,
						//Data: bson.M{"number": v[2]},
						Time: time.Duration(time.Now().Unix()),
						UID:  user.ID,
						User: user.Name,
					}
					histories = append(histories, history)
				}
			}

			// 记录报表
			if drugTrack != nil && len(drugTrack) > 0 {
				_, err := tools.Database.Collection("drug_track").InsertMany(sctx, drugTrack)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}

			// 添加药物轨迹
			if histories != nil && len(histories) > 0 {
				ctx.Set("HISTORY", histories)
			}

			// 自动添加扫码入仓任务
			if packageSign {
				// 查询是否有未完成的扫码入仓任务，有的话不添加任务，没有再添加
				//existFilter := bson.M{
				//	"customer_id":    toExamineFlowPathParameter.CustomerID,
				//	"project_id":     toExamineFlowPathParameter.ProjectID,
				//	"env_id":         toExamineFlowPathParameter.EnvironmentID,
				//	"info.work_type": 1, // 扫码入仓任务
				//	"info.status":    0, // 状态未完成
				//}
				//if !toExamineFlowPathParameter.CohortID.IsZero() {
				//	existFilter["cohort_id"] = toExamineFlowPathParameter.CohortID
				//}
				//
				//var existWorkTask models.WorkTask
				//err = tools.Database.Collection("work_task").FindOne(sctx, existFilter).Decode(&existWorkTask)
				//if err != nil && err != mongo.ErrNoDocuments {
				//	return nil, errors.WithStack(err)
				//}
				//existUserIds := make(map[primitive.ObjectID]primitive.ObjectID)
				//for _, v := range existWorkTask.UserIDs {
				//	existUserIds[v] = v
				//}
				//// 查询权限扫码入仓权限的用户
				//permissions := []string{"operation.build.medicine.barcode.scan"}
				//
				//查询所有仓库
				var projectStorehouses []models.ProjectStorehouse
				psCursor, err := tools.Database.Collection("project_storehouse").Find(nil, bson.M{
					"env_id": toExamineFlowPathParameter.EnvironmentID,
				})
				err = psCursor.All(nil, &projectStorehouses)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				siteOrStoreIDs := []primitive.ObjectID{}
				for _, ps := range projectStorehouses {
					siteOrStoreIDs = append(siteOrStoreIDs, ps.ID)
				}
				//userIds, err := tools.GetPermissionUserIds(sctx, permissions, toExamineFlowPathParameter.ProjectID, toExamineFlowPathParameter.EnvironmentID, siteOrStoreIDs...)
				//if err != nil {
				//	return nil, errors.WithStack(err)
				//}
				//if existWorkTask.ID.IsZero() {
				//	//判断已存在的任务里面的用户是否都包含userIds
				//	if userIds != nil {
				//		workTask := models.WorkTask{
				//			ID:            primitive.NewObjectID(),
				//			CustomerID:    toExamineFlowPathParameter.CustomerID,
				//			ProjectID:     toExamineFlowPathParameter.ProjectID,
				//			EnvironmentID: toExamineFlowPathParameter.EnvironmentID,
				//			CohortID:      toExamineFlowPathParameter.CohortID,
				//			UserIDs:       userIds,
				//			Info: models.WorkTaskInfo{
				//				WorkType:    1,
				//				Status:      0,
				//				CreatedTime: time.Duration(time.Now().Unix()),
				//				Deadline:    time.Duration(time.Now().AddDate(0, 0, 3).Unix()),
				//				MedicineIDs: []primitive.ObjectID{},
				//			},
				//		}
				//		_, err = tools.Database.Collection("work_task").InsertOne(sctx, workTask)
				//		if err != nil {
				//			return nil, errors.WithStack(err)
				//		}
				//	}
				//} else {
				//	addUserIds := existWorkTask.UserIDs
				//	//判断已存在任务的user是否都在存在
				//	for _, userId := range userIds {
				//		// 检查元素是否在map
				//		if _, ok := existUserIds[userId]; ok {
				//		} else {
				//			addUserIds = append(addUserIds, userId)
				//		}
				//	}
				//	if len(addUserIds) > 0 {
				//		workTaskUpdate := bson.M{
				//			"$set": bson.M{
				//				"user_ids": addUserIds,
				//			},
				//		}
				//		_, err = tools.Database.Collection("work_task").UpdateOne(sctx, bson.M{"_id": existWorkTask.ID}, workTaskUpdate)
				//		if err != nil {
				//			return nil, errors.WithStack(err)
				//		}
				//	}
				//}
				//包装扫码任务
				existFilter := bson.M{
					"customer_id":    toExamineFlowPathParameter.CustomerID,
					"project_id":     toExamineFlowPathParameter.ProjectID,
					"env_id":         toExamineFlowPathParameter.EnvironmentID,
					"info.work_type": 12, // 包转扫码任务
					"info.status":    0,  // 状态未完成
				}
				if !toExamineFlowPathParameter.CohortID.IsZero() {
					existFilter["cohort_id"] = toExamineFlowPathParameter.CohortID
				}
				var packageWorkTask models.WorkTask
				err = tools.Database.Collection("work_task").FindOne(sctx, existFilter).Decode(&packageWorkTask)
				if err != nil && err != mongo.ErrNoDocuments {
					return nil, errors.WithStack(err)
				}
				existUserIds := make(map[primitive.ObjectID]primitive.ObjectID)
				for _, v := range packageWorkTask.UserIDs {
					existUserIds[v] = v
				}
				// 查询权限包装扫码权限的用户
				permissions := []string{"operation.build.medicine.barcode.scanPackage"}
				userIds, err := tools.GetPermissionUserIds(sctx, permissions, toExamineFlowPathParameter.ProjectID, toExamineFlowPathParameter.EnvironmentID, siteOrStoreIDs...)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				if packageWorkTask.ID.IsZero() {
					if userIds != nil {
						workTask := models.WorkTask{
							ID:            primitive.NewObjectID(),
							CustomerID:    toExamineFlowPathParameter.CustomerID,
							ProjectID:     toExamineFlowPathParameter.ProjectID,
							EnvironmentID: toExamineFlowPathParameter.EnvironmentID,
							CohortID:      toExamineFlowPathParameter.CohortID,
							UserIDs:       userIds,
							Info: models.WorkTaskInfo{
								WorkType:    12,
								Status:      0,
								CreatedTime: time.Duration(time.Now().Unix()),
								Deadline:    time.Duration(time.Now().AddDate(0, 0, 3).Unix()),
								MedicineIDs: []primitive.ObjectID{},
							},
						}
						_, err = tools.Database.Collection("work_task").InsertOne(sctx, workTask)
						if err != nil {
							return nil, errors.WithStack(err)
						}
					}
				} else {
					addUserIds := packageWorkTask.UserIDs
					//判断已存在任务的userk是否都在存在
					for _, userId := range userIds {
						// 检查元素是否在map
						if _, ok := existUserIds[userId]; ok {
						} else {
							addUserIds = append(addUserIds, userId)
						}
					}
					if len(addUserIds) > 0 {
						workTaskUpdate := bson.M{
							"$set": bson.M{
								"user_ids": addUserIds,
							},
						}
						_, err = tools.Database.Collection("work_task").UpdateOne(sctx, bson.M{"_id": packageWorkTask.ID}, workTaskUpdate)
						if err != nil {
							return nil, errors.WithStack(err)
						}
					}
				}
			}

			// 添加项目日志
			InsertExamineUpdateReleaseLog(ctx, sctx, toExamineFlowPathParameter.EnvironmentID, 1, toExamineFlowPathParameter)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

// 查询自动编码已编号药物的药物名称
func (s *MedicineService) GetMedicineName(ctx *gin.Context, projectId string, envId string) ([]string, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectId)
	envOID, _ := primitive.ObjectIDFromHex(envId)

	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var barcodeRules []models.BarcodeRule
	cursor, err := tools.Database.Collection("barcode_rule").Find(nil, bson.M{"project_id": projectOID, "env_id": envOID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &barcodeRules)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var medicineName []string

	var cohortIDs []primitive.ObjectID
	drugConfigureFile := bson.M{}
	sign := false
	if project.Type == 1 { // 基本研究项目
		// 查询编码配置
		if barcodeRules != nil && len(barcodeRules) > 0 {
			if barcodeRules[0].CodeRule == 1 { // 自动编码
				drugConfigureFile["env_id"] = barcodeRules[0].EnvironmentID
				sign = true
			}
		}
	} else { // cohort项目
		if barcodeRules != nil && len(barcodeRules) > 0 {
			for _, br := range barcodeRules {
				if br.CodeRule == 1 {
					cohortIDs = append(cohortIDs, br.CohortID)
					sign = true
				}
			}
			drugConfigureFile["cohort_id"] = bson.M{"$in": cohortIDs}
		}
	}

	if sign {
		var drugConfigure []models.DrugConfigure
		drugConfigureCursor, err := tools.Database.Collection("drug_configure").Find(nil, drugConfigureFile)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = drugConfigureCursor.All(nil, &drugConfigure)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		for _, dcf := range drugConfigure {
			for _, cf := range dcf.Configures {
				for _, v := range cf.Values {
					medicineName = append(medicineName, v.DrugName)
				}
			}
		}
	}

	// 数据去重 并返回
	return removeDuplicates(medicineName), nil
}

// string数组去重
func removeDuplicates(array []string) []string {
	seen := make(map[string]bool)
	result := []string{}

	for _, value := range array {
		if !seen[value] {
			seen[value] = true
			result = append(result, value)
		}
	}

	return result
}

func (s *MedicineService) GetBatchSelectList(ctx *gin.Context, selectMedicines models.SelectMedicines) ([]models.Medicine, error) {
	medicineList := make([]models.Medicine, 0)

	customerOID := selectMedicines.CustomerID
	envOID := selectMedicines.EnvironmentID
	projectOID := selectMedicines.ProjectID

	//库房、中心研究产品状态
	siteStauts := []int{1, 4, 6, 7, 14, 20}
	orderStatus := []int{2, 3, 11}

	newSiteStatus := make([]int, 0)
	newOrderStatus := make([]int, 0)
	if selectMedicines.Status != nil && len(selectMedicines.Status) > 0 {
		for _, status := range selectMedicines.Status {
			siteIndex := arrays.Contains(siteStauts, status)
			if siteIndex != -1 {
				newSiteStatus = append(newSiteStatus, status)
			}
			orderIndex := arrays.Contains(orderStatus, status)
			if orderIndex != -1 {
				newOrderStatus = append(newOrderStatus, status)
			}
		}
	}

	//批次管理更新
	findBatch := selectMedicines.FindBatchs[0]
	//判断是有编号的研究产品，还是未编号的研究产品更新
	if findBatch.Name == "-" && selectMedicines.Status != nil && len(selectMedicines.Status) > 0 && selectMedicines.FindBatchs != nil && len(selectMedicines.FindBatchs) > 0 {
		storehousesFilter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "status": bson.M{"$in": selectMedicines.Status}}
		sitesFilter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "status": bson.M{"$in": newSiteStatus}}
		ordersFilter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "status": bson.M{"$in": newOrderStatus}}

		if &selectMedicines.Batch != nil {
			storehousesFilter["batch_number"] = selectMedicines.Batch
			sitesFilter["batch_number"] = selectMedicines.Batch
			ordersFilter["batch_number"] = selectMedicines.Batch
		}
		if &selectMedicines.ExpirationDate != nil {
			storehousesFilter["expiration_date"] = selectMedicines.ExpirationDate
			sitesFilter["expiration_date"] = selectMedicines.ExpirationDate
			ordersFilter["expiration_date"] = selectMedicines.ExpirationDate
		}

		if (&selectMedicines.UpperNumber != nil && selectMedicines.UpperNumber != "") && (&selectMedicines.LowerNumber != nil && selectMedicines.LowerNumber != "") {
			storehousesFilter["number"] = bson.M{"$gte": selectMedicines.UpperNumber, "$lte": selectMedicines.LowerNumber}
			sitesFilter["number"] = bson.M{"$gte": selectMedicines.UpperNumber, "$lte": selectMedicines.LowerNumber}
			ordersFilter["number"] = bson.M{"$gte": selectMedicines.UpperNumber, "$lte": selectMedicines.LowerNumber}
		} else if (&selectMedicines.UpperNumber != nil && selectMedicines.UpperNumber != "") && (&selectMedicines.LowerNumber == nil || selectMedicines.LowerNumber == "") {
			storehousesFilter["number"] = bson.M{"$gte": selectMedicines.UpperNumber}
			sitesFilter["number"] = bson.M{"$gte": selectMedicines.UpperNumber}
			ordersFilter["number"] = bson.M{"$gte": selectMedicines.UpperNumber}
		} else if (&selectMedicines.UpperNumber == nil || selectMedicines.UpperNumber == "") && (&selectMedicines.LowerNumber != nil && selectMedicines.LowerNumber != "") {
			storehousesFilter["number"] = bson.M{"$lte": selectMedicines.LowerNumber}
			sitesFilter["number"] = bson.M{"$lte": selectMedicines.LowerNumber}
			ordersFilter["number"] = bson.M{"$lte": selectMedicines.LowerNumber}
		}

		if (&selectMedicines.UpperSequence != nil && selectMedicines.UpperSequence != "") && (&selectMedicines.LowerSequence != nil && selectMedicines.LowerSequence != "") {
			storehousesFilter["serial_number"] = bson.M{"$gte": selectMedicines.UpperSequence, "$lte": selectMedicines.LowerSequence}
			sitesFilter["serial_number"] = bson.M{"$gte": selectMedicines.UpperSequence, "$lte": selectMedicines.LowerSequence}
			ordersFilter["serial_number"] = bson.M{"$gte": selectMedicines.UpperSequence, "$lte": selectMedicines.LowerSequence}
		} else if (&selectMedicines.UpperSequence != nil && selectMedicines.UpperSequence != "") && (&selectMedicines.LowerSequence == nil || selectMedicines.LowerSequence == "") {
			storehousesFilter["serial_number"] = bson.M{"$gte": selectMedicines.UpperSequence}
			sitesFilter["serial_number"] = bson.M{"$gte": selectMedicines.UpperSequence}
			ordersFilter["serial_number"] = bson.M{"$gte": selectMedicines.UpperSequence}
		} else if (&selectMedicines.UpperSequence == nil || selectMedicines.UpperSequence == "") && (&selectMedicines.LowerSequence != nil && selectMedicines.LowerSequence != "") {
			storehousesFilter["serial_number"] = bson.M{"$lte": selectMedicines.LowerSequence}
			sitesFilter["serial_number"] = bson.M{"$lte": selectMedicines.LowerSequence}
			ordersFilter["serial_number"] = bson.M{"$lte": selectMedicines.LowerSequence}
		}

		sitesOIDs := make([]primitive.ObjectID, 0)
		storehousesOIDs := make([]primitive.ObjectID, 0)
		ordersIDS := make([]primitive.ObjectID, 0)
		for _, fb := range selectMedicines.FindBatchs {
			if fb.Type == 2 { //仓库
				storehousesOIDs = append(storehousesOIDs, fb.PositionId)
			} else if fb.Type == 1 { //中心
				sitesOIDs = append(sitesOIDs, fb.PositionId)
			} else { //订单
				ordersIDS = append(ordersIDS, fb.PositionId)
			}
		}
		//仓库的研究产品
		if len(storehousesOIDs) > 0 {
			storehousesFilter["storehouse_id"] = bson.M{"$in": storehousesOIDs}
			storehousesFilter["$or"] = []bson.M{
				{"site_id": primitive.NilObjectID},
				{"site_id": nil},
				{"site_id": ""},
			}
			//storehousesFilter["$or"] = []bson.M{
			//	{"order_id": primitive.NilObjectID},
			//	{"order_id": nil},
			//	{"order_id": ""},
			//}

			storehouseMedicineList := make([]models.Medicine, 0)
			cursor, err := tools.Database.Collection("medicine").Find(ctx, storehousesFilter)
			if err != nil {
				return medicineList, errors.WithStack(err)
			}
			err = cursor.All(nil, &storehouseMedicineList)
			if err != nil {
				return medicineList, errors.WithStack(err)
			}

			if storehouseMedicineList != nil && len(storehouseMedicineList) > 0 {
				for _, medicine := range storehouseMedicineList {
					medicineList = append(medicineList, medicine)
				}
			}
		}
		//中心的研究产品
		if len(sitesOIDs) > 0 {
			sitesFilter["site_id"] = bson.M{"$in": sitesOIDs}

			siteMedicineList := make([]models.Medicine, 0)
			cursor, err := tools.Database.Collection("medicine").Find(ctx, sitesFilter)
			if err != nil {
				return medicineList, errors.WithStack(err)
			}
			err = cursor.All(nil, &siteMedicineList)
			if err != nil {
				return medicineList, errors.WithStack(err)
			}

			if siteMedicineList != nil && len(siteMedicineList) > 0 {
				for _, medicine := range siteMedicineList {
					medicineList = append(medicineList, medicine)
				}
			}
		}

		//订单的研究产品
		if len(ordersIDS) > 0 {
			ordersFilter["order_id"] = bson.M{"$in": ordersIDS}

			//查询id
			orderMedicineList := make([]models.Medicine, 0)
			cursor, err := tools.Database.Collection("medicine").Find(ctx, ordersFilter)
			if err != nil {
				return medicineList, errors.WithStack(err)
			}
			err = cursor.All(nil, &orderMedicineList)
			if err != nil {
				return medicineList, errors.WithStack(err)
			}

			if orderMedicineList != nil && len(orderMedicineList) > 0 {
				for _, medicine := range orderMedicineList {
					medicineList = append(medicineList, medicine)
				}
			}
		}

	}

	return medicineList, nil
}

func (s *MedicineService) FreezeMedicineHistoryData(
	ctx *gin.Context,
	oid primitive.ObjectID,
	key string,
	histories *[]models.History,
	user models.User,
	userName string,
	now time.Duration,
	data map[string]interface{},
	isOpenPackage bool,
) error {
	const (
		freezeNumber = iota + 1
		position
		ipNumber
		packageNumber
		ipName
		batch
		expireDate
		freezeCount
		count
		changeOtherMedicine
		orderNumber
		reason
		changeReason
	)
	customTempOption := []models.CustomTempOption{}
	if data["freezeNumber"] != nil {
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: freezeNumber,
			Key:   "history.medicine.new-freeze-label.freezeNumber",
			Data:  bson.M{"freezeNumber": data["freezeNumber"]},
		})
	}
	if data["position"] != nil {
		customTempOptionKey := "history.medicine.new-freeze-label.position"
		if key == "history.medicine.use-new.use" {
			customTempOptionKey = "history.medicine.use-new.position"
		} else if key == "history.medicine.new-lost.lost" {
			customTempOptionKey = "history.medicine.new-lost.position"
		}
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: position,
			Key:   customTempOptionKey,
			Data:  bson.M{"position": data["position"], "positionEn": data["positionEn"]},
		})
	}
	if data["packNumber"] != nil {
		customTempOptionKey := "history.medicine.new-freeze-label.ipNumber"
		if key == "history.medicine.releaseLost-new.release" {
			customTempOptionKey = "history.medicine.releaseLost-new.packNumber"
		} else if key == "history.medicine.release-new.release" {
			customTempOptionKey = "history.medicine.release-new.packNumber"
		} else if key == "history.medicine.use-new.use" {
			customTempOptionKey = "history.medicine.use-new.packNumber"
		}
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: ipNumber,
			Key:   customTempOptionKey,
			Data:  bson.M{"packNumber": data["packNumber"]},
		})
	}
	if isOpenPackage {
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: packageNumber,
			Key:   "history.medicine.new-freeze-label.packageNumber",
			Data:  bson.M{"packageNumber": data["packageNumber"]},
		})
	}
	if data["reason"] != nil {
		customTempOptionKey := "history.medicine.new-freeze-label.reason"
		if key == "history.medicine.releaseLost-new.release" {
			customTempOptionKey = "history.medicine.releaseLost-new.reason"
		} else if key == "history.medicine.otherRelease-new.release" {
			customTempOptionKey = "history.medicine.otherRelease-new.reason"
		} else if key == "history.medicine.otherReleaseLost-new.release" {
			customTempOptionKey = "history.medicine.otherReleaseLost-new.reason"
		} else if key == "history.medicine.use-new.use" {
			customTempOptionKey = "history.medicine.use-new.reason"
		}
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: reason,
			Key:   customTempOptionKey,
			Data:  bson.M{"reason": data["reason"]},
		})
	}

	if data["name"] != nil {
		customTempOptionKey := "history.medicine.new-freeze-label.ipName"
		if key == "history.medicine.otherRelease-new.release" {
			customTempOptionKey = "history.medicine.otherRelease-new.name"
		} else if key == "history.medicine.otherReleaseLost-new.release" {
			customTempOptionKey = "history.medicine.otherReleaseLost-new.name"
		}
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: ipName,
			Key:   customTempOptionKey,
			Data:  bson.M{"name": data["name"]},
		})
	}
	if data["batch"] != nil {
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: batch,
			Key:   "history.medicine.new-freeze-label.batch",
			Data:  bson.M{"batch": data["batch"]},
		})
	}
	if data["expireDate"] != nil {
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: expireDate,
			Key:   "history.medicine.new-freeze-label.expireDate",
			Data:  bson.M{"expireDate": data["expireDate"]},
		})
	}
	if data["freezeCount"] != nil {
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: freezeCount,
			Key:   "history.medicine.new-freeze-label.freezeCount",
			Data:  bson.M{"freezeCount": data["freezeCount"]},
		})
	}
	if data["changeOtherMedicine"] != nil {
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: changeOtherMedicine,
			Key:   "history.medicine.new-freeze-label.changeOtherMedicine",
			Data:  bson.M{"changeOtherMedicine": data["changeOtherMedicine"]},
		})
	}
	if data["orderNumber"] != nil {
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: orderNumber,
			Key:   "history.medicine.new-freeze-label.orderNumber",
			Data:  bson.M{"orderNumber": data["orderNumber"]},
		})
	}
	if data["changeReason"] != nil {
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: changeReason,
			Key:   "history.medicine.new-freeze-label.changeReason",
			Data:  bson.M{"changeReason": data["changeReason"]},
		})
	}
	if data["count"] != nil {
		customTempOptionKey := "history.medicine.new-freeze-label.ipName"
		if key == "history.medicine.otherRelease-new.release" {
			customTempOptionKey = "history.medicine.otherRelease-new.count"
		} else if key == "history.medicine.otherReleaseLost-new.release" {
			customTempOptionKey = "history.medicine.otherReleaseLost-new.count"
		}
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: count,
			Key:   customTempOptionKey,
			Data:  bson.M{"count": data["count"]},
		})
	}
	customTemps := []models.CustomTemp{}
	if len(customTempOption) > 0 {
		customTemps = append(customTemps, models.CustomTemp{
			ParKey:              "data",
			ConnectingSymbolKey: "history.dispensing.single.comma",
			LastSymbolKey:       "history.dispensing.single.period",
			CustomTempOptions:   customTempOption,
		})
	}

	// TableInfo
	history := models.History{
		Key:         key,
		OID:         oid,
		Time:        now,
		CustomTemps: customTemps,
		Data:        data,
		UID:         user.ID,
		User:        userName,
	}
	*histories = append(*histories, history)
	return nil
}

func (s *MedicineService) FreezeHistoryData(
	ctx *gin.Context,
	oid primitive.ObjectID,
	key string,
	historyData map[string]interface{},
	histories *[]models.History,
	user models.User,
	userName string,
	now time.Duration,
	data map[string]interface{},
	isOpenPackage bool,
) error {
	const (
		freezeNumber = iota + 1
		position
		medicines
		medicinesPackage
		otherMedicines
		reason
	)
	customTempOption := []models.CustomTempOption{}
	if data["freezeNumber"] != nil {
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: freezeNumber,
			Key:   "history.medicine.drugFreeze-new.freezeNumber",
			Data:  bson.M{"freezeNumber": data["freezeNumber"]},
		})
	}
	if data["position"] != nil {
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: position,
			Key:   "history.medicine.drugFreeze-new.position",
			Data:  bson.M{"position": data["position"], "positionEn": data["positionEn"]},
		})
	}
	if data["medicines"] != nil {
		if isOpenPackage {
			customTempOption = append(customTempOption, models.CustomTempOption{
				Index: medicinesPackage,
				Key:   "history.medicine.drugFreeze-new.medicinesPackage",
				Data:  bson.M{"medicines": data["medicines"]},
			})
		} else {
			customTempOption = append(customTempOption, models.CustomTempOption{
				Index: medicines,
				Key:   "history.medicine.drugFreeze-new.medicines",
				Data:  bson.M{"medicines": data["medicines"]},
			})
		}
	}

	if data["otherMedicines"] != nil {
		if key == "history.medicine.drugFreeze-new.freeze" || key == "history.medicine.drugFreeze-new.approval" {
			customTempOption = append(customTempOption, models.CustomTempOption{
				Index: otherMedicines,
				Key:   "history.medicine.drugFreeze-new.freezeOtherMedicine",
				Data:  bson.M{"otherMedicines": data["otherMedicines"]},
			})
		}
		if key == "history.medicine.drugFreeze-new.lost" {
			customTempOption = append(customTempOption, models.CustomTempOption{
				Index: otherMedicines,
				Key:   "history.medicine.drugFreeze-new.lostOtherMedicine",
				Data:  bson.M{"otherMedicines": data["otherMedicines"]},
			})
		}
		if key == "history.medicine.drugFreeze-new.release" || key == "history.medicine.drugFreeze-new.quarantine-no" || key == "history.medicine.drugFreeze-new.quarantine-yes" {
			customTempOption = append(customTempOption, models.CustomTempOption{
				Index: otherMedicines,
				Key:   "history.medicine.drugFreeze-new.releaseOtherMedicine",
				Data:  bson.M{"otherMedicines": data["otherMedicines"]},
			})
		}
	}

	if data["untieReason"] != nil {
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: reason,
			Key:   "history.medicine.drugFreeze-new.reason",
			Data:  bson.M{"reason": data["untieReason"]},
		})
	}

	if data["reason"] != nil {
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: reason,
			Key:   "history.medicine.new-freeze-label.reason",
			Data:  bson.M{"reason": data["reason"]},
		})
	}

	customTemps := []models.CustomTemp{}
	if len(customTempOption) > 0 {
		customTemps = append(customTemps, models.CustomTemp{
			ParKey:              "data",
			ConnectingSymbolKey: "history.dispensing.single.comma",
			LastSymbolKey:       "history.dispensing.single.period",
			CustomTempOptions:   customTempOption,
		})
	}

	// TableInfo
	history := models.History{
		Key:         key,
		Data:        historyData,
		OID:         oid,
		Time:        now,
		CustomTemps: customTemps,
		UID:         user.ID,
		User:        userName,
	}
	*histories = append(*histories, history)
	return nil
}

func getInstituteName(ctx *gin.Context, instituteType int, instituteId primitive.ObjectID) (map[string]interface{}, error) {
	instituteName := ""
	instituteNameEn := ""
	instituteInfoName := ""
	instituteInfoNameEn := ""
	instituteInfoNumber := ""
	var instituteData []map[string]interface{}
	matchID := bson.M{"_id": instituteId}
	if instituteType == 1 {
		projectMongo := bson.M{
			"_id":    0,
			"id":     "$_id",
			"number": "$number",
			"name":   models.ProjectSiteNameBsonLang("zh"),
			"nameEn": models.ProjectSiteNameBsonLang("en"),
		}
		sitePipeline := mongo.Pipeline{
			{{Key: "$match", Value: matchID}},
			{{Key: "$project", Value: projectMongo}},
		}
		cursor, err := tools.Database.Collection("project_site").Aggregate(nil, sitePipeline)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &instituteData)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	} else {
		storeHousePipeline := mongo.Pipeline{
			{{Key: "$match", Value: matchID}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "storehouse",
				"localField":   "storehouse_id",
				"foreignField": "_id",
				"as":           "storehouse",
			}}},
			{{
				Key: "$project",
				Value: bson.M{
					"_id": 0,
					"number": bson.M{
						"$arrayElemAt": bson.A{"$storehouse.number", 0},
					},
					"name": bson.M{
						"$arrayElemAt": bson.A{"$storehouse.name", 0},
					},
					"nameEn": bson.M{
						"$arrayElemAt": bson.A{"$storehouse.name", 0},
					},
				},
			}},
		}
		cursor, err := tools.Database.Collection("project_storehouse").Aggregate(nil, storeHousePipeline)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &instituteData)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	if len(instituteData) > 0 {
		instituteInfoName = instituteData[0]["name"].(string)
		instituteInfoNameEn = instituteData[0]["nameEn"].(string)
		instituteInfoNumber = instituteData[0]["number"].(string)
	}

	if instituteType == 1 { //中心
		instituteName = instituteInfoNumber + "-" + instituteInfoName
		instituteNameEn = instituteInfoNumber + "-" + instituteInfoNameEn
	} else { //仓库
		instituteName = instituteInfoName
		instituteNameEn = instituteInfoNameEn
	}
	return map[string]interface{}{"instituteName": instituteName, "instituteNameEn": instituteNameEn}, nil
}
