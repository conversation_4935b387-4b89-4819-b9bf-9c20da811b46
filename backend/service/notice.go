package service

import (
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"fmt"
	"github.com/xuri/excelize/v2"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type NoticeService struct{}

func (s *NoticeService) Get(ctx *gin.Context, customerID string, projectID string, envID string) ([]models.NoticeConfig, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	noticeConfigList := make([]models.NoticeConfig, 0)
	match := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	noticeConfigs := make([]models.NoticeConfig, 0)
	cursor, err := tools.Database.Collection("notice_config").Find(nil, match)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &noticeConfigs)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if noticeConfigs != nil && len(noticeConfigs) > 0 {

		//查询项目下的人员
		userProjectEnvironmentList := make([]models.UserProjectEnvironment, 0)
		userProjectEnvironmentCursor, err := tools.Database.Collection("user_project_environment").Find(ctx, bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = userProjectEnvironmentCursor.All(ctx, &userProjectEnvironmentList)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		userIdList := make([]primitive.ObjectID, 0)
		if userProjectEnvironmentList != nil && len(userProjectEnvironmentList) > 0 {
			for _, userProjectEnvironment := range userProjectEnvironmentList {
				if !userProjectEnvironment.Unbind {
					userIdList = append(userIdList, userProjectEnvironment.UserID)
				}
			}
		}

		userList := make([]models.User, 0)
		userFilter := bson.M{"_id": bson.M{"$in": userIdList}}
		userCursor, err := tools.Database.Collection("user").Find(nil, userFilter)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = userCursor.All(nil, &userList)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		existEmailList := make([]string, 0)
		if userList != nil && len(userList) > 0 {
			for _, user := range userList {
				existEmailList = append(existEmailList, user.UserInfo.Email)
			}
		}

		// 使用一个 map 来提高查找效率
		exists := make(map[string]bool)

		// 将 array2 的元素存入 map
		for _, value := range existEmailList {
			exists[value] = true
		}

		for _, config := range noticeConfigs {
			emailList := make([]string, 0)
			if config.ExcludeRecipientList != nil && len(config.ExcludeRecipientList) > 0 {
				// 遍历 array1，检查每个元素是否在 map 中
				for _, value := range config.ExcludeRecipientList {
					if !exists[value] {
						emailList = append(emailList, value)
					}
				}

			}

			config.UnbindEmailList = emailList
			noticeConfigList = append(noticeConfigList, config)
		}

	}

	return noticeConfigList, nil
}

func (s *NoticeService) GetByKey(ctx *gin.Context, customerID string, projectID string, envID string, key string) (models.NoticeConfig, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	match := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "key": key}
	var noticeConfig models.NoticeConfig
	if err := tools.Database.Collection("notice_config").FindOne(nil, match).Decode(&noticeConfig); err != nil {
		return noticeConfig, errors.WithStack(err)
	}
	return noticeConfig, nil
}

func (s *NoticeService) Set(ctx *gin.Context, data models.NoticeConfig) error {
	//判断是新增还是编辑
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var err error
		OID := data.EnvironmentID
		match := bson.M{"customer_id": data.CustomerID, "project_id": data.ProjectID, "env_id": data.EnvironmentID, "key": data.Key}
		var noticeConfig models.NoticeConfig
		var old models.NoticeConfig
		if err = tools.Database.Collection("notice_config").FindOne(sctx, match).Decode(&noticeConfig); err != nil {
			//新增
			data.ID = primitive.NewObjectID()
			if data.Roles == nil {
				data.Roles = []primitive.ObjectID{}
			}
			if _, err = tools.Database.Collection("notice_config").InsertOne(sctx, data); err != nil {
				return nil, errors.WithStack(err)
			}
			noticeConfig.Roles = data.Roles
			noticeConfig.FieldsConfig = data.FieldsConfig
			noticeConfig.State = data.State
			noticeConfig.TimeoutDays = data.TimeoutDays
			noticeConfig.ForecastTime = data.ForecastTime
			noticeConfig.SendDays = data.SendDays
			noticeConfig.ExcludeRecipientList = data.ExcludeRecipientList
			err := insertNotificationsLog(ctx, nil, data.Key, OID, 1, old, noticeConfig, "")
			if err != nil {
				return nil, err
			}
		} else {
			old = noticeConfig
			//编辑
			update := bson.M{"$set": bson.M{
				"roles":                  data.Roles,
				"timeout_days":           data.TimeoutDays,
				"forecast_time":          data.ForecastTime,
				"send_days":              data.SendDays,
				"state":                  data.State,
				"fields_config":          data.FieldsConfig,
				"automatic":              data.Automatic,
				"manual":                 data.Manual,
				"exclude_recipient_list": data.ExcludeRecipientList,
			}}
			if _, err = tools.Database.Collection("notice_config").UpdateOne(sctx, bson.M{"_id": noticeConfig.ID}, update); err != nil {
				return nil, errors.WithStack(err)
			}
			noticeConfig.Roles = data.Roles
			noticeConfig.FieldsConfig = data.FieldsConfig
			noticeConfig.State = data.State
			noticeConfig.TimeoutDays = data.TimeoutDays
			noticeConfig.SendDays = data.SendDays
			noticeConfig.Automatic = data.Automatic
			noticeConfig.Manual = data.Manual
			noticeConfig.ExcludeRecipientList = data.ExcludeRecipientList
			err := insertNotificationsLog(ctx, nil, data.Key, OID, 2, old, noticeConfig, "")
			if err != nil {
				return nil, err
			}
		}
		if err != nil {
			return nil, err
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *NoticeService) GetNoticeConfigVerify(ctx *gin.Context, data models.NoticeConfig) ([]string, error) {
	emailList := make([]string, 0)
	if data.ExcludeRecipientList != nil && len(data.ExcludeRecipientList) > 0 {
		//查询项目下的人员
		userProjectEnvironmentList := make([]models.UserProjectEnvironment, 0)
		userProjectEnvironmentCursor, err := tools.Database.Collection("user_project_environment").Find(ctx, bson.M{"customer_id": data.CustomerID, "project_id": data.ProjectID, "env_id": data.EnvironmentID})
		if err != nil {
			return emailList, errors.WithStack(err)
		}
		err = userProjectEnvironmentCursor.All(ctx, &userProjectEnvironmentList)
		if err != nil {
			return emailList, errors.WithStack(err)
		}

		userIdList := make([]primitive.ObjectID, 0)
		if userProjectEnvironmentList != nil && len(userProjectEnvironmentList) > 0 {
			for _, userProjectEnvironment := range userProjectEnvironmentList {
				if !userProjectEnvironment.Unbind {
					userIdList = append(userIdList, userProjectEnvironment.UserID)
				}
			}
		}

		userList := make([]models.User, 0)
		userFilter := bson.M{"_id": bson.M{"$in": userIdList}}
		userCursor, err := tools.Database.Collection("user").Find(nil, userFilter)
		if err != nil {
			return emailList, errors.WithStack(err)
		}
		err = userCursor.All(nil, &userList)
		if err != nil {
			return emailList, errors.WithStack(err)
		}

		existEmailList := make([]string, 0)
		if userList != nil && len(userList) > 0 {
			for _, user := range userList {
				existEmailList = append(existEmailList, user.UserInfo.Email)
			}
		}

		// 使用一个 map 来提高查找效率
		exists := make(map[string]bool)

		// 将 array2 的元素存入 map
		for _, value := range existEmailList {
			exists[value] = true
		}

		// 遍历 array1，检查每个元素是否在 map 中
		for _, value := range data.ExcludeRecipientList {
			if !exists[value] {
				emailList = append(emailList, value)
			}
		}

	}

	return emailList, nil
}

func insertNotificationsLog(ctx *gin.Context, sctx mongo.SessionContext, key string, OID primitive.ObjectID, types int, old models.NoticeConfig, new models.NoticeConfig, copyEnv string) error {
	// 保存项目日志
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	if types != 3 {
		//基本设置
		if key == "notice.basic.settings" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.basic.settings",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			//邮件语言-自动任务
			oldAutomaticZh := ""
			oldAutomaticEn := ""
			if &old.Automatic != nil && old.Automatic != 0 {
				if old.Automatic == 1 {
					oldAutomaticZh = "中文"
					oldAutomaticEn = "Chinese"
				} else if old.Automatic == 2 {
					oldAutomaticZh = "英文"
					oldAutomaticEn = "English"
				} else if old.Automatic == 3 {
					oldAutomaticZh = "中英文"
					oldAutomaticEn = "Chinese And English"
				}
			}
			newAutomaticZh := ""
			newAutomaticEn := ""
			if &new.Automatic != nil && old.Automatic != 0 {
				if new.Automatic == 1 {
					newAutomaticZh = "中文"
					newAutomaticEn = "Chinese"
				} else if new.Automatic == 2 {
					newAutomaticZh = "英文"
					newAutomaticEn = "English"
				} else if new.Automatic == 3 {
					newAutomaticZh = "中英文"
					newAutomaticEn = "Chinese And English"
				}
			}
			if oldAutomaticZh != newAutomaticZh || oldAutomaticEn != newAutomaticEn {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.basic.settings",
					TranKey: "notifications.notice.basic.settings.emailLanguage_automatedTasks",
					Old: models.OperationLogField{
						Type:    30,
						Value:   oldAutomaticZh,
						ENValue: oldAutomaticEn,
					},
					New: models.OperationLogField{
						Type:    30,
						Value:   newAutomaticZh,
						ENValue: newAutomaticEn,
					},
				})
			}

			//邮件语言-手动任务
			oldManualZh := ""
			oldManualEn := ""
			if &old.Manual != nil && old.Manual != 0 {
				if old.Manual == 1 {
					oldManualZh = "中文"
					oldManualEn = "Chinese"
				} else if old.Manual == 2 {
					oldManualZh = "英文"
					oldManualEn = "English"
				} else if old.Manual == 3 {
					oldManualZh = "中英文"
					oldManualEn = "Chinese And English"
				}
			}
			newManualZh := ""
			newManualEn := ""
			if &new.Manual != nil && new.Manual != 0 {
				if new.Manual == 1 {
					newManualZh = "中文"
					newManualEn = "Chinese"
				} else if new.Manual == 2 {
					newManualZh = "英文"
					newManualEn = "English"
				} else if new.Manual == 3 {
					newManualZh = "中英文"
					newManualEn = "Chinese And English"
				}
			}
			if oldManualZh != newManualZh || oldManualEn != newManualEn {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.basic.settings",
					TranKey: "notifications.notice.basic.settings.emailLanguage_manualTasks",
					Old: models.OperationLogField{
						Type:    30,
						Value:   oldManualZh,
						ENValue: oldManualEn,
					},
					New: models.OperationLogField{
						Type:    30,
						Value:   newManualZh,
						ENValue: newManualEn,
					},
				})
			}
		}
		//受试者登记
		if key == "notice.subject.add" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.add",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			if !areArrayStringEqual(old.FieldsConfig, new.FieldsConfig) {
				//内容配置
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.add",
					TranKey: "notifications.dispensing.contentConfiguration",
					Old: models.OperationLogField{
						Type:  17,
						Value: old.FieldsConfig,
					},
					New: models.OperationLogField{
						Type:  17,
						Value: new.FieldsConfig,
					},
				})
			}

			if !areArrayObjectIDEqual(old.Roles, new.Roles) {
				//角色
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.add",
					TranKey: "notifications.roles",
					Old: models.OperationLogField{
						Type:  16,
						Value: old.Roles,
					},
					New: models.OperationLogField{
						Type:  16,
						Value: new.Roles,
					},
				})
			}
			if !areArrayStringEqual(old.ExcludeRecipientList, new.ExcludeRecipientList) {
				//排除收件人
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.add",
					TranKey: "notifications.exclusiveReceivers",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(old.ExcludeRecipientList, ","),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(new.ExcludeRecipientList, ","),
					},
				})
			}
		}
		//受试者随机
		if key == "notice.subject.random" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.random",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			if !areArrayStringEqual(old.FieldsConfig, new.FieldsConfig) {
				//内容配置
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.random",
					TranKey: "notifications.dispensing.contentConfiguration",
					Old: models.OperationLogField{
						Type:  17,
						Value: old.FieldsConfig,
					},
					New: models.OperationLogField{
						Type:  17,
						Value: new.FieldsConfig,
					},
				})
			}
			if !areArrayObjectIDEqual(old.Roles, new.Roles) {
				//角色
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.random",
					TranKey: "notifications.roles",
					Old: models.OperationLogField{
						Type:  16,
						Value: old.Roles,
					},
					New: models.OperationLogField{
						Type:  16,
						Value: new.Roles,
					},
				})
			}
			if !areArrayStringEqual(old.ExcludeRecipientList, new.ExcludeRecipientList) {
				//排除收件人
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.random",
					TranKey: "notifications.exclusiveReceivers",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(old.ExcludeRecipientList, ","),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(new.ExcludeRecipientList, ","),
					},
				})
			}
		}
		//受试者停用
		if key == "notice.subject.signOut" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.signOut",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			if !areArrayStringEqual(old.FieldsConfig, new.FieldsConfig) {
				//内容配置
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.signOut",
					TranKey: "notifications.dispensing.contentConfiguration",
					Old: models.OperationLogField{
						Type:  17,
						Value: old.FieldsConfig,
					},
					New: models.OperationLogField{
						Type:  17,
						Value: new.FieldsConfig,
					},
				})
			}
			if !areArrayObjectIDEqual(old.Roles, new.Roles) {
				//角色
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.signOut",
					TranKey: "notifications.roles",
					Old: models.OperationLogField{
						Type:  16,
						Value: old.Roles,
					},
					New: models.OperationLogField{
						Type:  16,
						Value: new.Roles,
					},
				})
			}
			if !areArrayStringEqual(old.ExcludeRecipientList, new.ExcludeRecipientList) {
				//排除收件人
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.signOut",
					TranKey: "notifications.exclusiveReceivers",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(old.ExcludeRecipientList, ","),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(new.ExcludeRecipientList, ","),
					},
				})
			}
		}
		//受试者替换
		if key == "notice.subject.replace" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.replace",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			if !areArrayStringEqual(old.FieldsConfig, new.FieldsConfig) {
				//内容配置
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.replace",
					TranKey: "notifications.dispensing.contentConfiguration",
					Old: models.OperationLogField{
						Type:  17,
						Value: old.FieldsConfig,
					},
					New: models.OperationLogField{
						Type:  17,
						Value: new.FieldsConfig,
					},
				})
			}
			if !areArrayObjectIDEqual(old.Roles, new.Roles) {
				//角色
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.replace",
					TranKey: "notifications.roles",
					Old: models.OperationLogField{
						Type:  16,
						Value: old.Roles,
					},
					New: models.OperationLogField{
						Type:  16,
						Value: new.Roles,
					},
				})
			}
			if !areArrayStringEqual(old.ExcludeRecipientList, new.ExcludeRecipientList) {
				//排除收件人
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.replace",
					TranKey: "notifications.exclusiveReceivers",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(old.ExcludeRecipientList, ","),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(new.ExcludeRecipientList, ","),
					},
				})
			}
		}
		//受试者修改
		if key == "notice.subject.update" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.update",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			if !areArrayStringEqual(old.FieldsConfig, new.FieldsConfig) {
				//内容配置
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.update",
					TranKey: "notifications.dispensing.contentConfiguration",
					Old: models.OperationLogField{
						Type:  17,
						Value: old.FieldsConfig,
					},
					New: models.OperationLogField{
						Type:  17,
						Value: new.FieldsConfig,
					},
				})
			}
			if !areArrayStringEqual(old.State, new.State) {
				//场景
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.update",
					TranKey: "notifications.dispensing.scene",
					Old: models.OperationLogField{
						Type:  25,
						Value: old.State,
					},
					New: models.OperationLogField{
						Type:  25,
						Value: new.State,
					},
				})
			}
			if !areArrayObjectIDEqual(old.Roles, new.Roles) {
				//角色
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.update",
					TranKey: "notifications.roles",
					Old: models.OperationLogField{
						Type:  16,
						Value: old.Roles,
					},
					New: models.OperationLogField{
						Type:  16,
						Value: new.Roles,
					},
				})
			}
			if !areArrayStringEqual(old.ExcludeRecipientList, new.ExcludeRecipientList) {
				//排除收件人
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.update",
					TranKey: "notifications.exclusiveReceivers",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(old.ExcludeRecipientList, ","),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(new.ExcludeRecipientList, ","),
					},
				})
			}
		}
		//受试者筛选
		if key == "notice.subject.screen" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.screen",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			if !areArrayStringEqual(old.FieldsConfig, new.FieldsConfig) {
				//内容配置
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.screen",
					TranKey: "notifications.dispensing.contentConfiguration",
					Old: models.OperationLogField{
						Type:  17,
						Value: old.FieldsConfig,
					},
					New: models.OperationLogField{
						Type:  17,
						Value: new.FieldsConfig,
					},
				})
			}
			if !areArrayStringEqual(old.State, new.State) {
				//场景
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.screen",
					TranKey: "notifications.dispensing.scene",
					Old: models.OperationLogField{
						Type:  24,
						Value: old.State,
					},
					New: models.OperationLogField{
						Type:  24,
						Value: new.State,
					},
				})
			}
			if !areArrayObjectIDEqual(old.Roles, new.Roles) {
				//角色
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.screen",
					TranKey: "notifications.roles",
					Old: models.OperationLogField{
						Type:  16,
						Value: old.Roles,
					},
					New: models.OperationLogField{
						Type:  16,
						Value: new.Roles,
					},
				})
			}
			if !areArrayStringEqual(old.ExcludeRecipientList, new.ExcludeRecipientList) {
				//排除收件人
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.screen",
					TranKey: "notifications.exclusiveReceivers",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(old.ExcludeRecipientList, ","),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(new.ExcludeRecipientList, ","),
					},
				})
			}
		}
		//受试者发放
		if key == "notice.subject.dispensing" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.dispensing",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			if !areArrayStringEqual(old.FieldsConfig, new.FieldsConfig) {
				//内容配置
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.dispensing",
					TranKey: "notifications.dispensing.contentConfiguration",
					Old: models.OperationLogField{
						Type:  17,
						Value: old.FieldsConfig,
					},
					New: models.OperationLogField{
						Type:  17,
						Value: new.FieldsConfig,
					},
				})
			}
			if !areArrayStringEqual(old.State, new.State) {
				//场景
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.dispensing",
					TranKey: "notifications.dispensing.scene",
					Old: models.OperationLogField{
						Type:  18,
						Value: old.State,
					},
					New: models.OperationLogField{
						Type:  18,
						Value: new.State,
					},
				})
			}
			if !areArrayObjectIDEqual(old.Roles, new.Roles) {
				//角色
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.dispensing",
					TranKey: "notifications.roles",
					Old: models.OperationLogField{
						Type:  16,
						Value: old.Roles,
					},
					New: models.OperationLogField{
						Type:  16,
						Value: new.Roles,
					},
				})
			}
			if !areArrayStringEqual(old.ExcludeRecipientList, new.ExcludeRecipientList) {
				//排除收件人
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.dispensing",
					TranKey: "notifications.exclusiveReceivers",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(old.ExcludeRecipientList, ","),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(new.ExcludeRecipientList, ","),
					},
				})
			}
		}
		//受试者警戒
		if key == "notice.subject.alarm" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.alarm",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			if !areArrayStringEqual(old.FieldsConfig, new.FieldsConfig) {
				//内容配置
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.alarm",
					TranKey: "notifications.dispensing.contentConfiguration",
					Old: models.OperationLogField{
						Type:  17,
						Value: old.FieldsConfig,
					},
					New: models.OperationLogField{
						Type:  17,
						Value: new.FieldsConfig,
					},
				})
			}
			if !areArrayObjectIDEqual(old.Roles, new.Roles) {
				//角色
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.alarm",
					TranKey: "notifications.roles",
					Old: models.OperationLogField{
						Type:  16,
						Value: old.Roles,
					},
					New: models.OperationLogField{
						Type:  16,
						Value: new.Roles,
					},
				})
			}
			if !areArrayStringEqual(old.ExcludeRecipientList, new.ExcludeRecipientList) {
				//排除收件人
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.alarm",
					TranKey: "notifications.exclusiveReceivers",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(old.ExcludeRecipientList, ","),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(new.ExcludeRecipientList, ","),
					},
				})
			}
		}
		//紧急揭盲
		if key == "notice.subject.unblinding" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.unblinding",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			if !areArrayStringEqual(old.FieldsConfig, new.FieldsConfig) {
				//内容配置
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.unblinding",
					TranKey: "notifications.dispensing.contentConfiguration",
					Old: models.OperationLogField{
						Type:  17,
						Value: old.FieldsConfig,
					},
					New: models.OperationLogField{
						Type:  17,
						Value: new.FieldsConfig,
					},
				})
			}
			if !areArrayObjectIDEqual(old.Roles, new.Roles) {
				//角色
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.unblinding",
					TranKey: "notifications.roles",
					Old: models.OperationLogField{
						Type:  16,
						Value: old.Roles,
					},
					New: models.OperationLogField{
						Type:  16,
						Value: new.Roles,
					},
				})
			}
			if !areArrayStringEqual(old.ExcludeRecipientList, new.ExcludeRecipientList) {
				//排除收件人
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.unblinding",
					TranKey: "notifications.exclusiveReceivers",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(old.ExcludeRecipientList, ","),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(new.ExcludeRecipientList, ","),
					},
				})
			}
		}
		//研究产品隔离
		if key == "notice.medicine.isolation" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.medicine.isolation",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			if !areArrayStringEqual(old.FieldsConfig, new.FieldsConfig) {
				//内容配置
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.medicine.isolation",
					TranKey: "notifications.dispensing.contentConfiguration",
					Old: models.OperationLogField{
						Type:  17,
						Value: old.FieldsConfig,
					},
					New: models.OperationLogField{
						Type:  17,
						Value: new.FieldsConfig,
					},
				})
			}
			if !areArrayStringEqual(old.State, new.State) {
				//场景
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.medicine.isolation",
					TranKey: "notifications.isolation.scene",
					Old: models.OperationLogField{
						Type:  19,
						Value: old.State,
					},
					New: models.OperationLogField{
						Type:  19,
						Value: new.State,
					},
				})
			}
			if !areArrayObjectIDEqual(old.Roles, new.Roles) {
				//角色
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.medicine.isolation",
					TranKey: "notifications.roles",
					Old: models.OperationLogField{
						Type:  16,
						Value: old.Roles,
					},
					New: models.OperationLogField{
						Type:  16,
						Value: new.Roles,
					},
				})
			}
			if !areArrayStringEqual(old.ExcludeRecipientList, new.ExcludeRecipientList) {
				//排除收件人
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.medicine.isolation",
					TranKey: "notifications.exclusiveReceivers",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(old.ExcludeRecipientList, ","),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(new.ExcludeRecipientList, ","),
					},
				})
			}
		}
		//研究产品订单
		if key == "notice.medicine.order" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.medicine.order",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			if !areArrayStringEqual(old.FieldsConfig, new.FieldsConfig) {
				//内容配置
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.medicine.order",
					TranKey: "notifications.dispensing.contentConfiguration",
					Old: models.OperationLogField{
						Type:  17,
						Value: old.FieldsConfig,
					},
					New: models.OperationLogField{
						Type:  17,
						Value: new.FieldsConfig,
					},
				})
			}
			if !areArrayStringEqual(old.State, new.State) {
				//场景
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.medicine.order",
					TranKey: "notifications.order.scene",
					Old: models.OperationLogField{
						Type:  20,
						Value: old.State,
					},
					New: models.OperationLogField{
						Type:  20,
						Value: new.State,
					},
				})
			}
			if !areArrayObjectIDEqual(old.Roles, new.Roles) {
				//角色
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.medicine.order",
					TranKey: "notifications.roles",
					Old: models.OperationLogField{
						Type:  16,
						Value: old.Roles,
					},
					New: models.OperationLogField{
						Type:  16,
						Value: new.Roles,
					},
				})
			}
			if !areArrayStringEqual(old.ExcludeRecipientList, new.ExcludeRecipientList) {
				//排除收件人
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.medicine.order",
					TranKey: "notifications.exclusiveReceivers",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(old.ExcludeRecipientList, ","),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(new.ExcludeRecipientList, ","),
					},
				})
			}
		}
		//研究产品有效期
		if key == "notice.medicine.reminder" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.medicine.reminder",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			if !areArrayStringEqual(old.FieldsConfig, new.FieldsConfig) {
				//内容配置
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.medicine.reminder",
					TranKey: "notifications.dispensing.contentConfiguration",
					Old: models.OperationLogField{
						Type:  17,
						Value: old.FieldsConfig,
					},
					New: models.OperationLogField{
						Type:  17,
						Value: new.FieldsConfig,
					},
				})
			}
			if !areArrayObjectIDEqual(old.Roles, new.Roles) {
				//角色
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.medicine.reminder",
					TranKey: "notifications.roles",
					Old: models.OperationLogField{
						Type:  16,
						Value: old.Roles,
					},
					New: models.OperationLogField{
						Type:  16,
						Value: new.Roles,
					},
				})
			}
			if !areArrayStringEqual(old.ExcludeRecipientList, new.ExcludeRecipientList) {
				//排除收件人
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.medicine.reminder",
					TranKey: "notifications.exclusiveReceivers",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(old.ExcludeRecipientList, ","),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(new.ExcludeRecipientList, ","),
					},
				})
			}
		}
		//研究产品警戒
		if key == "notice.medicine.alarm" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.medicine.alarm",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			if !areArrayStringEqual(old.FieldsConfig, new.FieldsConfig) {
				//内容配置
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.medicine.alarm",
					TranKey: "notifications.dispensing.contentConfiguration",
					Old: models.OperationLogField{
						Type:  17,
						Value: old.FieldsConfig,
					},
					New: models.OperationLogField{
						Type:  17,
						Value: new.FieldsConfig,
					},
				})
			}
			if !areArrayObjectIDEqual(old.Roles, new.Roles) {
				//角色
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.medicine.alarm",
					TranKey: "notifications.roles",
					Old: models.OperationLogField{
						Type:  16,
						Value: old.Roles,
					},
					New: models.OperationLogField{
						Type:  16,
						Value: new.Roles,
					},
				})
			}
			if !areArrayStringEqual(old.ExcludeRecipientList, new.ExcludeRecipientList) {
				//排除收件人
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.medicine.alarm",
					TranKey: "notifications.exclusiveReceivers",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(old.ExcludeRecipientList, ","),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(new.ExcludeRecipientList, ","),
					},
				})
			}
		}
		//库房警戒
		if key == "notice.storehouse.alarm" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.storehouse.alarm",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			if !areArrayStringEqual(old.FieldsConfig, new.FieldsConfig) {
				//内容配置
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.storehouse.alarm",
					TranKey: "notifications.dispensing.contentConfiguration",
					Old: models.OperationLogField{
						Type:  17,
						Value: old.FieldsConfig,
					},
					New: models.OperationLogField{
						Type:  17,
						Value: new.FieldsConfig,
					},
				})
			}
			if !areArrayObjectIDEqual(old.Roles, new.Roles) {
				//角色
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.storehouse.alarm",
					TranKey: "notifications.roles",
					Old: models.OperationLogField{
						Type:  16,
						Value: old.Roles,
					},
					New: models.OperationLogField{
						Type:  16,
						Value: new.Roles,
					},
				})
			}
			if !areArrayStringEqual(old.ExcludeRecipientList, new.ExcludeRecipientList) {
				//排除收件人
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.storehouse.alarm",
					TranKey: "notifications.exclusiveReceivers",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(old.ExcludeRecipientList, ","),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(new.ExcludeRecipientList, ","),
					},
				})
			}
		}
		//受试者上限设置提醒
		if key == "notice.subject.alert.threshold" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.alert.threshold",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			if !areArrayStringEqual(old.FieldsConfig, new.FieldsConfig) {
				//内容配置
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.alert.threshold",
					TranKey: "notifications.dispensing.contentConfiguration",
					Old: models.OperationLogField{
						Type:  17,
						Value: old.FieldsConfig,
					},
					New: models.OperationLogField{
						Type:  17,
						Value: new.FieldsConfig,
					},
				})
			}
			if !areArrayObjectIDEqual(old.Roles, new.Roles) {
				//角色
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.alert.threshold",
					TranKey: "notifications.roles",
					Old: models.OperationLogField{
						Type:  16,
						Value: old.Roles,
					},
					New: models.OperationLogField{
						Type:  16,
						Value: new.Roles,
					},
				})
			}
			if !areArrayStringEqual(old.ExcludeRecipientList, new.ExcludeRecipientList) {
				//排除收件人
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.alert.threshold",
					TranKey: "notifications.exclusiveReceivers",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(old.ExcludeRecipientList, ","),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(new.ExcludeRecipientList, ","),
					},
				})
			}
		}
		//订单超时
		if key == "notice.order.timeout" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.order.timeout",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			if !areArrayStringEqual(old.FieldsConfig, new.FieldsConfig) {
				//内容配置
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.order.timeout",
					TranKey: "notifications.dispensing.contentConfiguration",
					Old: models.OperationLogField{
						Type:  17,
						Value: old.FieldsConfig,
					},
					New: models.OperationLogField{
						Type:  17,
						Value: new.FieldsConfig,
					},
				})
			}
			if !areArrayObjectIDEqual(old.Roles, new.Roles) {
				//角色
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.order.timeout",
					TranKey: "notifications.roles",
					Old: models.OperationLogField{
						Type:  16,
						Value: old.Roles,
					},
					New: models.OperationLogField{
						Type:  16,
						Value: new.Roles,
					},
				})
			}
			//订单超时天数设置
			if old.TimeoutDays != new.TimeoutDays {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.order.timeout",
					TranKey: "notifications.notice.order.timeout.lateShipmentAlertSetting",
					Old: models.OperationLogField{
						Type:  21,
						Value: old.TimeoutDays,
					},
					New: models.OperationLogField{
						Type:  21,
						Value: new.TimeoutDays,
					},
				})
			}
			if old.SendDays != new.SendDays {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.order.timeout",
					TranKey: "notifications.notice.order.timeout.lateShipmentSendAlertSetting",
					Old: models.OperationLogField{
						Type:  21,
						Value: old.SendDays,
					},
					New: models.OperationLogField{
						Type:  21,
						Value: new.SendDays,
					},
				})
			}
			if !areArrayStringEqual(old.ExcludeRecipientList, new.ExcludeRecipientList) {
				//排除收件人
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.order.timeout",
					TranKey: "notifications.exclusiveReceivers",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(old.ExcludeRecipientList, ","),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(new.ExcludeRecipientList, ","),
					},
				})
			}
		}
	}
	if OperationLogFieldGroups != nil && len(OperationLogFieldGroups) > 0 {
		marks := []models.Mark{}
		err := tools.SaveOperation(ctx, sctx, "operation_log.module.notifications", OID, types, OperationLogFieldGroups, marks, OID)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

func areArrayStringEqual(arr1, arr2 []string) bool {

	if arr1 == nil {
		arr1 = make([]string, 0)
	}

	if arr2 == nil {
		arr2 = make([]string, 0)
	}

	// 首先判断数组长度是否相等
	if len(arr1) != len(arr2) {
		return false
	}

	// 逐个比较数组元素
	for i := range arr1 {
		if arr1[i] != arr2[i] {
			return false
		}
	}

	return true
}

func areArrayObjectIDEqual(arr1, arr2 []primitive.ObjectID) bool {

	if arr1 == nil {
		arr1 = make([]primitive.ObjectID, 0)
	}

	if arr2 == nil {
		arr2 = make([]primitive.ObjectID, 0)
	}

	// 首先判断数组长度是否相等
	if len(arr1) != len(arr2) {
		return false
	}

	// 逐个比较数组元素
	for i := range arr1 {
		if arr1[i] != arr2[i] {
			return false
		}
	}

	return true
}

func (s *NoticeService) GetTemplateFile(ctx *gin.Context) error {
	f := excelize.NewFile()
	fileName := "ExcludedRecipientTemplate.xlsx"
	f.SetDefaultFont("黑体")
	title := make([]interface{}, 0)
	title = append(title, locales.Tr(ctx, "notice.exclude_recipient_list.email.account"))
	content := make([][]interface{}, 0)
	tools.ExportSheet(f, "Sheet1", title, content)
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return errors.WithStack(err)
	}
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", url.PathEscape(fileName)))
	ctx.Data(200, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8", buffer.Bytes())
	return errors.WithStack(err)
}
