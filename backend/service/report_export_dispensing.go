package service

import (
	"clinflash-irt/data"
	"clinflash-irt/database"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/pkg/errors"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"strconv"
	"strings"
	"time"
)

func ExportDispensingReport(ctx *gin.Context, projectID string, envID string, cohortIDs []string, templateId string, roleId string, now time.Time) (string, []byte, error) {
	// 选择中心导出
	// 不选择中心，根据用户分配的中心
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)

	//projectSiteID := ctx.DefaultQuery("projectSiteId", "")
	var attributes []models.Attribute
	var visitCycles []models.VisitCycle
	attributeFilter := bson.M{"env_id": envOID}

	cursor, err := tools.Database.Collection("attribute").Find(nil, attributeFilter, &options.FindOptions{
		Sort: bson.D{{"_id", 1}},
	})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &attributes)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	cursor, err = tools.Database.Collection("visit_cycle").Find(nil, attributeFilter)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &visitCycles)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	if len(cohortIDs) > 0 {
		visitCycles = slice.Filter(visitCycles, func(index int, item models.VisitCycle) bool {
			_, ok := slice.Find(cohortIDs, func(index int, cohortID string) bool {
				return cohortID == item.CohortID.Hex()
			})
			return ok
		})
	}

	showRandomNumber := map[primitive.ObjectID]bool{}
	showRegisterGroup := map[primitive.ObjectID]bool{}
	for _, attribute := range attributes {
		ok := true
		if len(cohortIDs) > 0 {
			_, ok = slice.Find(cohortIDs, func(index int, cohortID string) bool {
				return cohortID == attribute.CohortID.Hex()
			})
		}

		if attribute.AttributeInfo.IsRandomNumber && ok {
			showRandomNumber[attribute.CohortID] = true
		}
		if attribute.AttributeInfo.AllowRegisterGroup && ok {
			showRegisterGroup[attribute.CohortID] = true
		}
	}

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	var parameter map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&parameter, binding.JSON)
	var subjects []primitive.ObjectID
	for _, subject := range parameter["subject"].([]interface{}) {
		subjectOID, _ := primitive.ObjectIDFromHex(subject.(string))
		subjects = append(subjects, subjectOID)
	}

	subjectReplaceText := ""
	subjectReplaceText = attributes[0].AttributeInfo.SubjectReplaceText

	match := bson.M{"_id": bson.M{"$in": subjects}, "deleted": bson.M{"$ne": true}}

	var datas []models.ReportDispensing

	{
		pipeline := mongo.Pipeline{
			{{Key: "$match", Value: match}},
			{{Key: "$unwind", Value: "$info"}},
			{{Key: "$match", Value: bson.M{"info.name": "shortname"}}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "dispensing",
				"localField":   "_id",
				"foreignField": "subject_id",
				"as":           "dispensing",
			}}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "project_site",
				"localField":   "project_site_id",
				"foreignField": "_id",
				"as":           "project_site",
			}}},
			{{Key: "$unwind", Value: "$dispensing"}},
			{{Key: "$lookup", Value: bson.M{
				"from": "history",
				"let":  bson.M{"id": "$dispensing._id"},
				"pipeline": bson.A{
					bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$oid", "$$id"}}}},
					bson.M{"$sort": bson.D{{"time", -1}}},
				},
				"as": "history",
			}}},
			{{Key: "$unwind", Value: "$project_site"}}, //{{Key: "$match", Value: bson.M{"dispensing.status": 2}}},
			{{Key: "$lookup", Value: bson.M{"from": "region", "localField": "project_site.region_id", "foreignField": "_id", "as": "region"}}},
			{{Key: "$unwind", Value: bson.M{"path": "$region", "preserveNullAndEmptyArrays": true}}},
			{{Key: "$project", Value: bson.M{
				"_id":                      1,
				"cohort_id":                1,
				"country":                  bson.M{"$first": "$project_site.country"},
				"serial_number":            "$dispensing.serial_number",
				"site":                     "$project_site.number",
				"name":                     models.ProjectSiteNameLookUpBson(ctx),
				"timeZone":                 "$project_site.time_zone",
				"tz":                       "$project_site.tz",
				"env_id":                   1,
				"status":                   "$dispensing.status",
				"subject":                  "$info.value",
				"group":                    1,
				"register_group":           1,
				"random_number":            1,
				"visit":                    "$dispensing.visit_info.name",
				"time":                     "$dispensing.dispensing_time",
				"visit_sign":               "$dispensing.visit_sign",
				"reissue":                  "$dispensing.reissue",
				"medicine":                 "$dispensing.dispensing_medicines",
				"realMedicine":             "$dispensing.real_dispensing_medicines",
				"replace_medicines":        "$dispensing.replace_medicines",
				"replace_other_medicines":  "$dispensing.replace_other_medicines",
				"other_medicines":          "$dispensing.other_dispensing_medicines",
				"cancel_medicines_history": "$dispensing.cancel_medicines_history",
				"form":                     "$dispensing.form",
				"room":                     "$room_number",
				"medicines": bson.M{"$setUnion": bson.A{
					bson.M{"$ifNull": bson.A{"$dispensing.replace_medicines", bson.A{}}},
					bson.M{"$ifNull": bson.A{"$dispensing.real_dispensing_medicines", bson.A{}}},
					bson.M{"$ifNull": bson.A{"$dispensing.dispensing_medicines", bson.A{}}},
					bson.M{"$ifNull": bson.A{"$dispensing.cancel_medicines_history", bson.A{}}},
					bson.M{"$ifNull": bson.A{"$dispensing.other_medicines_history", bson.A{}}},
					bson.M{"$ifNull": bson.A{"$history", bson.A{}}},
				}},
				"history":       1,
				"dispensing":    "$dispensing._id",
				"region":        "$region.name",
				"label":         "$dispensing.label",
				"labels":        "$dispensing.labels",
				"formula_info":  "$dispensing.formula_info",
				"dose_info":     "$dispensing.dose_info.dose_level_list",
				"dose_form":     "$dispensing.dose_info.form",
				"dispensing_id": "$dispensing._id",
			}}},
			{{Key: "$unwind", Value: bson.M{"path": "$medicines"}}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "medicine_order",
				"localField":   "medicines.order_oid",
				"foreignField": "_id",
				"as":           "order",
			}}},
			{{Key: "$unwind", Value: bson.M{"path": "$order", "preserveNullAndEmptyArrays": true}}},
			{{Key: "$sort", Value: bson.D{{"site", 1}, {"_id", 1}, {"serial_number", 1}, {"medicines.time", 1}}}},
		}
		optTrue := true
		opt := &options.AggregateOptions{
			AllowDiskUse: &optTrue,
		}
		cursor, err := tools.Database.Collection("subject").Aggregate(nil, pipeline, opt)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &datas)
		if err != nil {
			return "", nil, errors.WithStack(err)

		}
	}
	userIds := make([]primitive.ObjectID, 0)
	for _, d := range datas {
		for _, h := range d.History {
			userIds = append(userIds, h.UID)
		}
	}
	userIds = slice.Unique(userIds)
	users := make([]models.User, 0)
	userMap := make(map[primitive.ObjectID]int32)
	if len(userIds) > 0 {
		cursor, err := tools.Database.Collection("user").Find(nil, bson.M{"_id": bson.M{"$in": userIds}})
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &users)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		for _, u := range users {
			userMap[u.ID] = u.Unicode
		}
	}
	// 获取该环境下开放药物 和 未编号药物名称
	blindMedicineMap, err := tools.IsBlindDrugMap(envOID)
	if err != nil {
		return "", nil, err
	}

	title := []interface{}{}
	countries := bson.M{}
	countries, err = database.GetCountries(ctx)

	isBlindedRole, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	var projectInfo []map[string]interface{}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": projectOID}}},
		{{Key: "$unwind", Value: "$envs"}},
		{{Key: "$match", Value: bson.M{"envs.id": envOID}}},
		{
			{
				Key: "$project",
				Value: bson.M{
					"_id":      0,
					"number":   "$info.number",
					"name":     "$info.name",
					"env":      "$envs.name",
					"timeZone": "$info.timeZoneStr",
				},
			},
		},
	}
	cursor, err = tools.Database.Collection("project").Aggregate(nil, pipeline)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &projectInfo)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	var timeZone float64
	if projectInfo[0]["timeZone"] != nil {
		timeZone, _ = strconv.ParseFloat(projectInfo[0]["timeZone"].(string), 64)
	} else {
		timeZone = float64(8)
	}
	roomShowBlind, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	var content [][]interface{}
	var registerContent [][]interface{}
	fields := map[string]bool{}
	fieldsArray := []string{}
	doseFieldCol := []string{}
	fieldCol := []string{}
	formulaBSATitle := []interface{}{}
	formulaBSAKey := []string{}
	//var col = int32(0)
	cohortOIDs := make([]primitive.ObjectID, 0)
	for _, v := range cohortIDs {
		cohortOID, _ := primitive.ObjectIDFromHex(v)
		cohortOIDs = append(cohortOIDs, cohortOID)
	}

	//变量
	formMatch := bson.M{"env_id": envOID, "fields": bson.M{"$ne": nil}}
	if len(cohortIDs) > 0 {
		formMatch["cohort_id"] = bson.M{"$in": cohortOIDs}
	}
	var forms []map[string]interface{}
	cursor, _ = tools.Database.Collection("form").Aggregate(ctx, mongo.Pipeline{
		{{Key: "$match", Value: formMatch}},
		{{Key: "$unwind", Value: "$fields"}},
		{{Key: "$match", Value: bson.M{"fields.application_type": bson.M{"$in": bson.A{2, 3}}}}},
	})
	err = cursor.All(nil, &forms)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	//公式
	var customerReportTitles []models.CustomerReportTitle
	filter := bson.M{"env_id": envOID}
	if len(cohortIDs) > 0 {
		filter["cohort_id"] = bson.M{"$in": cohortOIDs}
	}
	cursor, err = tools.Database.Collection("customer_report_title").Find(nil, filter)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &customerReportTitles)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	var logisticsCompanyCodes []models.LogisticsCompanyCode
	cursor, err = tools.Database.Collection("logistics_company_code").Find(nil, bson.M{})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &logisticsCompanyCodes)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	mapLogisticsCompanyCode := map[string]string{"qita": locales.Tr(ctx, "order_logistics_9")}
	for _, item := range logisticsCompanyCodes {
		mapLogisticsCompanyCode[item.Code] = item.Name

	}

	var mapPeriod map[primitive.ObjectID]models.Period

	if templateId != "" {
		var template models.CustomTemplate
		templateOID, _ := primitive.ObjectIDFromHex(templateId)
		err = tools.Database.Collection("custom_template").FindOne(nil, bson.M{
			"_id": templateOID,
		}).Decode(&template)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		for _, field := range template.Fields {
			// colIdx := fmt.Sprintf("%s", string('A'+col%26))
			// if col > 25 {
			// 	colIdx = "A" + colIdx
			// }
			if field == data.ReportAttributesInfoSubjectNumber && subjectReplaceText != "" {
				title = append(title, subjectReplaceText)
				fieldsArray = append(fieldsArray, field)
				fields[field] = true
				// fieldCol[field] = colIdx
				// col++
				continue
			}
			if field == data.ReportAttributesRandomNumber && len(showRandomNumber) == 0 {
				continue
			}
			if field == data.ReportAttributesDispensingMedicineRealGroup && len(showRegisterGroup) == 0 {
				continue
			}

			// 字段有计划时间和超窗时间
			if field == data.ReportAttributesDispensingOutSize || field == data.ReportAttributesDispensingPlanTime {
				mapPeriod, err = getPeriod(ctx, envOID, attributes, timeZone)
				if err != nil {
					return "", nil, errors.WithStack(err)
				}
			}

			// 剂量表单
			if field == data.ReportAttributesDispensingDoseFormulas {

				fieldOID := envOID
				for _, form := range forms {
					fields := form["fields"].(map[string]interface{})
					if int(fields["application_type"].(int32)) != 3 {
						continue
					}
					label := fields["label"]
					variable := fields["variable"]
					status := fields["status"].(int32)
					statusStr := ""
					if status == 2 {
						statusStr = "-" + locales.Tr(ctx, "operation_log.form.status_invalid")
					}
					formTitle := fmt.Sprintf("%s(%s)%s", label, variable, statusStr)
					title = append(title, formTitle)
					if project.Type != 1 {
						fieldOID = form["cohort_id"].(primitive.ObjectID)
					} else {
						fieldOID = envOID
					}
					doseFieldColTitle := fmt.Sprintf("%s%s", fieldOID.Hex(), variable)
					doseFieldCol = append(doseFieldCol, doseFieldColTitle)
					//col++
				}
				fieldsArray = append(fieldsArray, field)
				fields[field] = true
				continue
			}
			//公式计算字段
			if field == data.ReportAttributesDispensingUseFormulas {

				if len(customerReportTitles) > 0 {
					// 公式发药变量
					formulaBSATitle, formulaBSAKey, err = getFormulaForm(ctx, envOID, cohortOIDs)
					if err != nil {
						return "", nil, errors.WithStack(err)
					}
					title = append(title, formulaBSATitle...)

					fieldOID := envOID
					for _, form := range forms {
						fields := form["fields"].(map[string]interface{})
						if int(fields["application_type"].(int32)) != 2 {
							continue
						}
						label := fields["label"]
						variable := fields["variable"]
						status := fields["status"].(int32)
						statusStr := ""
						if status == 2 {
							statusStr = "-" + locales.Tr(ctx, "operation_log.form.status_invalid")
						}
						formTitle := fmt.Sprintf("%s(%s)%s", label, variable, statusStr)
						title = append(title, formTitle)
						if project.Type != 1 {
							fieldOID = form["cohort_id"].(primitive.ObjectID)
						} else {
							fieldOID = envOID
						}
						fieldColTitle := fmt.Sprintf("%s%s", fieldOID.Hex(), variable)
						fieldCol = append(fieldCol, fieldColTitle)
						//col++
					}

					for _, customerReportTitle := range customerReportTitles {
						for _, useFormula := range customerReportTitle.Title {
							t := fmt.Sprintf("%s(%s)", locales.Tr(ctx, "system_suggest_value"), useFormula)
							if useFormula == "age" || useFormula == "weight" {
								t = fmt.Sprintf("%s(%s)", locales.Tr(ctx, "system_suggest_value"), locales.Tr(ctx, "export.dispensing."+useFormula))
							}
							title = append(title, t)
							fieldCol = append(fieldCol, useFormula)
							//fieldCol[useFormula] = useFormula
							//col++
						}
					}

					fieldsArray = append(fieldsArray, field)
					fields[field] = true

				}
				continue
			}

			// 计划外原因
			if field == data.ReportAttributesDispensingOutVisitDispensingReason {
				unscheduledReasonTitle := locales.Tr(ctx, field)
				if len(visitCycles) > 0 {
					visitCycle := visitCycles[0]
					if visitCycle.SetInfo.IsOpen {
						if ctx.GetHeader("Accept-Language") == "en" {
							unscheduledReasonTitle = visitCycle.SetInfo.NameEn + " " + locales.Tr(ctx, "operator.reason")

						} else {
							unscheduledReasonTitle = visitCycle.SetInfo.NameZh + locales.Tr(ctx, "operator.reason")
						}
					}
				}
				title = append(title, unscheduledReasonTitle)
				fieldsArray = append(fieldsArray, field)
				fields[field] = true
				continue
			}

			//计划外备注
			if field == data.ReportAttributesDispensingOutVisitDispensingRemark {
				unscheduledReasonTitle := locales.Tr(ctx, field)
				if len(visitCycles) > 0 {
					visitCycle := visitCycles[0]
					if visitCycle.SetInfo.IsOpen {
						if ctx.GetHeader("Accept-Language") == "en" {
							unscheduledReasonTitle = visitCycle.SetInfo.NameEn + "-" + locales.Tr(ctx, "common.remark")
						} else {
							unscheduledReasonTitle = visitCycle.SetInfo.NameZh + "-" + locales.Tr(ctx, "common.remark")
						}
					}
				}
				title = append(title, unscheduledReasonTitle)
				fieldsArray = append(fieldsArray, field)
				fields[field] = true
				continue

			}

			title = append(title, locales.Tr(ctx, field))
			fieldsArray = append(fieldsArray, field)
			fields[field] = true
		}
	} else {
		double := false
		for _, field := range data.DispenseReport.MultiDefaultFields {

			if field.Type == project.Type && !double {
				double = true
				for _, defaultField := range field.DefaultFields {
					// 字段有计划时间和超窗时间
					if defaultField.Key == data.ReportAttributesDispensingOutSize || defaultField.Key == data.ReportAttributesDispensingPlanTime {
						mapPeriod, err = getPeriod(ctx, envOID, attributes, timeZone)
						if err != nil {
							return "", nil, errors.WithStack(err)
						}
					}
					// colIdx := fmt.Sprintf("%s", string('A'+col%26))
					// if col > 25 {
					// 	colIdx = "A" + colIdx
					// }

					if defaultField.Key == data.ReportAttributesInfoSubjectNumber && subjectReplaceText != "" {
						title = append(title, subjectReplaceText)
						fieldsArray = append(fieldsArray, defaultField.Key)
						fields[defaultField.Key] = true
						// fieldCol[defaultField.Key] = colIdx
						// col++
						continue
					}
					if defaultField.Key == data.ReportAttributesRandomNumber && len(showRandomNumber) == 0 {
						continue
					}

					if defaultField.Key == data.ReportAttributesDispensingMedicineRealGroup && len(showRegisterGroup) == 0 {
						continue
					}

					if defaultField.Key == data.ReportAttributesDispensingDoseFormulas {
						// 剂量表单
						fieldOID := envOID
						for _, form := range forms {
							fields := form["fields"].(map[string]interface{})
							if fields["application_type"] != 3 {
								continue
							}
							label := fields["label"]
							variable := fields["variable"]
							status := fields["status"].(int32)
							statusStr := ""
							if status == 2 {
								statusStr = "-" + locales.Tr(ctx, "operation_log.form.status_invalid")
							}
							formTitle := fmt.Sprintf("%s(%s)%s", label, variable, statusStr)
							title = append(title, formTitle)
							if project.Type != 1 {
								fieldOID = form["cohort_id"].(primitive.ObjectID)
							} else {
								fieldOID = envOID
							}
							fieldColTitle := fmt.Sprintf("%s%s", fieldOID.Hex(), variable)
							fieldCol = append(fieldCol, fieldColTitle)
							//col++
						}
					}

					//公式计算
					if defaultField.Key == data.ReportAttributesDispensingUseFormulas {
						//公式
						if len(customerReportTitles) > 0 {
							// 公式发药变量
							formulaBSATitle, formulaBSAKey, err = getFormulaForm(ctx, envOID, cohortOIDs)
							if err != nil {
								return "", nil, errors.WithStack(err)
							}
							title = append(title, formulaBSATitle...)
							fieldOID := envOID
							for _, form := range forms {
								fields := form["fields"].(map[string]interface{})
								label := fields["label"]
								variable := fields["variable"]
								status := fields["status"].(int32)
								statusStr := ""
								if status == 2 {
									statusStr = "-" + locales.Tr(ctx, "operation_log.form.status_invalid")
								}
								formTitle := fmt.Sprintf("%s(%s)%s", label, variable, statusStr)
								title = append(title, formTitle)
								if project.Type != 1 {
									fieldOID = form["cohort_id"].(primitive.ObjectID)
								} else {
									fieldOID = envOID
								}
								fieldColTitle := fmt.Sprintf("%s%s", fieldOID.Hex(), variable)
								fieldCol = append(fieldCol, fieldColTitle)
								//fieldCol[fieldColTitle] = fieldColTitle
								//col++
							}

							for _, customerReportTitle := range customerReportTitles {
								for _, useFormula := range customerReportTitle.Title {
									t := fmt.Sprintf("%s(%s)", locales.Tr(ctx, "system_suggest_value"), useFormula)

									if useFormula == "age" || useFormula == "weight" {
										t = fmt.Sprintf("%s(%s)", locales.Tr(ctx, "system_suggest_value"), locales.Tr(ctx, "export.dispensing."+useFormula))
									}
									title = append(title, t)
									fieldCol = append(fieldCol, useFormula)
									//fieldCol[useFormula] = useFormula
									//col++
								}
							}

							fieldsArray = append(fieldsArray, defaultField.Key)
							fields[defaultField.Key] = true

						}
						continue
					}
					//计划外备注
					if defaultField.Key == data.ReportAttributesDispensingOutVisitDispensingRemark {
						unscheduledReasonTitle := locales.Tr(ctx, defaultField.Key)
						if len(visitCycles) > 0 {
							visitCycle := visitCycles[0]
							if visitCycle.SetInfo.IsOpen {
								if ctx.GetHeader("Accept-Language") == "en" {
									unscheduledReasonTitle = visitCycle.SetInfo.NameEn + " " + locales.Tr(ctx, "common.remark")
								} else {
									unscheduledReasonTitle = visitCycle.SetInfo.NameZh + locales.Tr(ctx, "common.remark")
								}
							}
						}
						title = append(title, unscheduledReasonTitle)
						fieldsArray = append(fieldsArray, defaultField.Key)
						fields[defaultField.Key] = true
						continue

					}
					// fieldCol[defaultField.Key] = colIdx
					// col++
					title = append(title, locales.Tr(ctx, defaultField.Key))
					fieldsArray = append(fieldsArray, defaultField.Key)
					fields[defaultField.Key] = true
				}
			}
		}
	}

	cohortMap := map[primitive.ObjectID]string{}
	for _, environment := range project.Environments {
		if environment.ID == envOID {
			for _, cohort := range environment.Cohorts {
				cohortMap[cohort.ID] = models.GetCohortReRandomName(cohort)
			}
		}
	}

	for _, item := range datas {
		roomShow := roomShowBlind
		attributeP, _ := slice.Find(attributes, func(index int, data models.Attribute) bool {
			return item.CohortID == data.CohortID
		})
		attribute := *attributeP
		if !attribute.AttributeInfo.Blind {
			roomShow = false
		}
		isNumber := true
		if item.Medicines.Count != 0 {
			isNumber = false
		}
		visitCycle := models.VisitCycle{}
		visitCycleP, ok := slice.Find(visitCycles, func(index int, data models.VisitCycle) bool {
			return item.CohortID == data.CohortID
		})
		if ok {
			visitCycle = *visitCycleP
		}
		country := ""
		region := ""
		site := ""
		siteName := ""
		room := ""
		subject := ""
		randomNumber := ""
		visit := ""
		operateTime := ""
		operateTypeStr := ""
		number := ""
		replace := ""
		name := ""
		otherNumber := ""
		realNumber := ""
		registerGroup := ""
		operator := ""
		mark := ""
		unscheduledReason := ""
		reissueReason := ""
		cohort := ""
		batch := ""
		expire := ""
		packageNumber := ""
		label := ""
		doseLevel := ""
		planTime := ""
		outSize := ""
		reissueRemark := ""
		unscheduledRemark := ""
		replaceRemark := ""
		retrieveRemark := ""
		registerRemark := ""
		invalidRemark := ""
		sendType := locales.Tr(ctx, "history.dispensing.send-type-0")
		logistics := ""
		logisticsRemark := ""
		var tmp []interface{}
		operateType := 0 // 操作类型

		// 国家
		{
			if attribute.AttributeInfo.CountryLayered {
				if item.Country != "" {
					country = countries[item.Country].(string)
				}
			}
		}
		// 区域
		{
			if attribute.AttributeInfo.RegionLayered {
				if item.Region != "" {
					region = item.Region
				}
			}
		}
		// 国家
		{
			if attribute.AttributeInfo.IsRandomNumber {
				randomNumber = item.RandomNumber
			}
		}
		site = item.Site     // 中心编号
		siteName = item.Name // 中心名称
		// 房间号
		{
			if item.Room != "" && !roomShow {
				room = item.Room
			}
		}
		subject = item.Subject // 受试者

		// 访视名称-访视类型
		{
			if item.VisitSign {
				if item.Reissue == 1 {
					visit = item.Visit + "-" + locales.Tr(ctx, "export.dispensing.reissue")

				} else {
					outPlan := locales.Tr(ctx, "export.dispensing.outVisit")
					if visitCycle.SetInfo.IsOpen {
						if ctx.GetHeader("Accept-Language") == "zh" {
							outPlan = visitCycle.SetInfo.NameZh
						} else {
							outPlan = visitCycle.SetInfo.NameEn
						}
					}
					visit = item.Visit + "-" + outPlan
				}
			} else {
				visit = item.Visit
			}
		}

		// 操作时间
		{
			if item.Time != 0.0 || item.Medicines.Time != 0 {
				if item.Medicines.Time != 0 {
					//

					if item.TZ != "" {
						//siteTimeZone, _ := strconv.Atoi(strings.Replace(item["timeZone"].(string), "UTC", "", 1))
						//var dataTime string
						//dataTime = time.Unix(time.Duration(item["medicines"].(map[string]interface{})["time"].(int64)).Nanoseconds(), 0).UTC().Add(time.Hour * time.Duration(siteTimeZone)).Format("2006-01-02 15:04:05")
						//dataTime = dataTime + "(" + item["timeZone"].(string) + ")"

						timeStr, err := tools.GetLocationUtc(item.TZ, int64(item.Medicines.Time))
						if err != nil {
							panic(err)
						}

						operateTime = timeStr
					} else {
						hours := time.Duration(timeZone)
						minutes := time.Duration((timeZone - float64(hours)) * 60)
						duration := hours*time.Hour + minutes*time.Minute

						var dataTime string
						dataTime = time.Unix(time.Duration(item.Medicines.Time).Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						//strTimeZone := fmt.Sprintf("UTC%+d", timeZone)
						strTimeZone := tools.FormatOffsetToZoneString(timeZone)
						dataTime = dataTime + "(" + strTimeZone + ")"
						operateTime = dataTime
					}

				} else {

					// 旧数据 无药物时间的 使用发药时间

					if item.TZ != "" {
						//SiteTimeZone, _ := strconv.Atoi(strings.Replace(item["timeZone"].(string), "UTC", "", 1))
						//var dataTime string
						//dataTime = time.Unix(item["time"].(int64), 0).UTC().Add(time.Hour * time.Duration(SiteTimeZone)).Format("2006-01-02 15:04:05")
						//dataTime = dataTime + "(" + item["timeZone"].(string) + ")"

						timeStr, err := tools.GetLocationUtc(item.TZ, int64(item.Time))
						if err != nil {
							panic(err)
						}

						operateTime = timeStr

					} else {

						hours := time.Duration(timeZone)
						minutes := time.Duration((timeZone - float64(hours)) * 60)
						duration := hours*time.Hour + minutes*time.Minute

						var dataTime string
						dataTime = time.Unix(item.Time.Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						//strTimeZone := fmt.Sprintf("UTC%+d", timeZone)
						strTimeZone := tools.FormatOffsetToZoneString(timeZone)
						dataTime = dataTime + "(" + strTimeZone + ")"
						operateTime = dataTime

					}

				}
			}
		}

		// 操作类型
		{
			if project.ResearchAttribute == 0 {
				// 通用模式
				if !(item.Medicines.Type == 0 || item.Medicines.Type == 8 || item.Medicines.Type == 9) {
					if item.Medicines.Type == 1 {
						operateTypeStr = locales.Tr(ctx, "export.dispensing.first")
						operateType = 1
					}
					if item.Medicines.Type == 2.0 || item.Reissue == 1 {
						operateTypeStr = locales.Tr(ctx, "export.dispensing.reissue")
						operateType = 2
					}
					if item.Medicines.Type == 3.0 {
						operateTypeStr = locales.Tr(ctx, "export.dispensing.replace")
						operateType = 3
					}
					// 记录取回数据
					if item.Medicines.Type == 6.0 {
						operateTypeStr = locales.Tr(ctx, "export.dispensing.retrieve")
						operateType = 6
					}
					if item.Medicines.Type == 7.0 {
						operateTypeStr = locales.Tr(ctx, "export.dispensing.cancel")
						operateType = 7
					}

					if item.Medicines.Type == 4.0 {
						operateTypeStr = locales.Tr(ctx, "export.dispensing.register")
						operateType = 4
					}

				} else if item.Medicines.Type == 0 || item.Medicines.Type == 8 || item.Medicines.Type == 9 { // 旧数据处理

					if item.VisitSign {
						if item.Reissue == 1 {
							operateTypeStr = locales.Tr(ctx, "export.dispensing.reissue")
							operateType = 2
						} else {
							operateTypeStr = locales.Tr(ctx, "export.dispensing.first")
							operateType = 1
						}
					} else {
						operateTypeStr = locales.Tr(ctx, "export.dispensing.first")
						operateType = 1
					}
					if (item.Medicines.Type == 8 || item.Medicines.Type == 9) && item.Medicines.BeInfo != nil && item.Medicines.BeInfo.Name != "" {
						operateTypeStr = locales.Tr(ctx, "export.dispensing.register")
						operateType = 4
					}
				}
			} else {
				// DTP 模式
				if !(item.Medicines.Type == 0 || item.Medicines.Type == 8 || item.Medicines.Type == 9) {
					if item.Medicines.Type == 1 {
						operateTypeStr = locales.Tr(ctx, "export.dispensing.visit_apply")
					}
					if item.Medicines.Type == 2 {
						operateTypeStr = locales.Tr(ctx, "export.dispensing.reissue_apply")
					}
					if item.Medicines.Type == 3 {
						operateTypeStr = locales.Tr(ctx, "export.dispensing.replace_apply")
					}
					if item.Medicines.Type == 5 {
						operateTypeStr = locales.Tr(ctx, "export.dispensing.out_visit_apply")
					}
				} else if item.Medicines.Type == 0 || item.Medicines.Type == 8 || item.Medicines.Type == 9 {
					if item.VisitSign {
						if item.Reissue == 1.0 {
							operateTypeStr = locales.Tr(ctx, "export.dispensing.reissue_apply")
						} else {
							operateTypeStr = locales.Tr(ctx, "export.dispensing.out_visit_apply")
						}
					} else {
						operateTypeStr = locales.Tr(ctx, "export.dispensing.visit_apply")
					}
					if (item.Medicines.Type == 8 || item.Medicines.Type == 9) && !item.Medicines.MedicineOtherID.IsZero() {
						operateTypeStr = locales.Tr(ctx, "export.dispensing.register")
						operateType = 4
					}
				}
			}
		}

		// 计划访视时间
		{
			if mapPeriod[item.DispensingID].LineTime != "" && operateType == 1 {
				planTime = mapPeriod[item.DispensingID].LineTime
				if mapPeriod[item.DispensingID].OutSize {
					outSize = locales.Tr(ctx, "common.yes")
				} else {
					outSize = locales.Tr(ctx, "common.no")
				}
			}
		}

		// 研究产品编号
		if item.Medicines.Number != "" {
			number = item.Medicines.Number
		}

		//包装号

		if item.Medicines.PackageNumber != "" {
			packageNumber = item.Medicines.PackageNumber
		}

		// 批次号
		{
			if item.Medicines.Batch != "" {
				batch = item.Medicines.Batch
			}
			if item.Medicines.BatchNumber != "" {
				batch = item.Medicines.BatchNumber
			}
		}

		// 有效期
		{
			if item.Medicines.ExpirationDate != "" {
				expire = item.Medicines.ExpirationDate
			}
			if item.Medicines.ExpireDate != "" {
				expire = item.Medicines.ExpireDate
			}
		}

		// 未编号研究产品数量
		if !isNumber {
			otherNumber = fmt.Sprintf("%d", item.Medicines.Count)
		}

		// 已替换研究产品号
		if isNumber {
			oldNumber := ""
			if len(item.ReplaceMedicines) > 0 {
				for _, replaceMedicine := range item.ReplaceMedicines {
					if replaceMedicine.NewNumber == item.Medicines.Number {
						if item.Medicines.Type != 0 {
							if item.Medicines.Type != 6 && item.Medicines.Type != 7 {
								operateTypeStr = locales.Tr(ctx, "export.dispensing.replace")
								operateType = 3
							}
						} else {
							operateTypeStr = locales.Tr(ctx, "export.dispensing.replace")
							operateType = 3

						}
						oldNumber = replaceMedicine.Number
					}
				}
			}
			replace = oldNumber
		} else {
			if len(item.ReplaceOtherMedicines) > 0 {
				for _, replaceMedicine := range item.ReplaceOtherMedicines {
					if replaceMedicine.Time == item.Medicines.Time &&
						replaceMedicine.MedicineOtherID == item.Medicines.MedicineOtherID {
						if isBlindedRole && blindMedicineMap[replaceMedicine.BeInfo.Name] {
							replaceMedicine.BeInfo.Name = tools.BlindData
						}
						replace = replaceMedicine.BeInfo.Name + "/" + otherNumber + "/" + replaceMedicine.BeInfo.ExpireDate + "/" + replaceMedicine.BeInfo.Batch
						operateTypeStr = locales.Tr(ctx, "export.dispensing.replace")
						break
					}
				}
			}
		}

		// 研究产品名称
		{
			name = item.Medicines.Name
			if !isBlindedRole || !blindMedicineMap[name] {
				name = item.Medicines.Name
			} else {
				name = tools.BlindData
			}
		}

		// 发药标签
		{
			if operateType != 3 {
				label = item.Medicines.Label
				if item.Medicines.Label == "" && item.Medicines.Type == 0 && item.Medicines.NewNumber != "" { // 替换药物需查找关联的
					replaceMedicineP, ok := slice.Find(item.Medicine, func(index int, it models.DispensingMedicine) bool {
						return it.MedicineID == item.Medicines.MedicineNewID
					})
					if ok {
						replaceMedicine := *replaceMedicineP
						label = replaceMedicine.Label
					}
				}
			}
		}

		// 实际使用产品
		if isNumber {
			{
				var realMedicines string

				if operateType == 4 {
					realMedicineP, ok := slice.Find(item.CancelMedicinesHistory, func(index int, it models.DispensingMedicine) bool {
						return item.Medicines.RealMedicineID == it.MedicineID
					})
					if ok {
						realMedicine := *realMedicineP
						realMedicines = realMedicine.Number
					}
				}

				realNumber = realMedicines
			}
		} else {
			if item.Medicines.BeInfo != nil && operateType != 3 {
				medicines := item.Medicines
				realName := medicines.Name
				if isBlindedRole && blindMedicineMap[realName] {
					realName = tools.BlindData
				}
				realExpireDate := medicines.BeInfo.ExpireDate
				realBatch := medicines.BeInfo.Batch
				realNumber = item.Medicines.BeInfo.Name + "/" + convertor.ToString(medicines.BeInfo.Count) + "/" + realExpireDate + "/" + realBatch
			}
		}

		fieldValue := map[string]string{}
		if len(item.Form) > 0 {
			for _, formInfo := range item.Form {
				key := formInfo.Key
				value := strconv.FormatFloat(formInfo.Value, 'f', -1, 64)

				formP, ok := slice.Find(forms, func(index int, formInfo map[string]interface{}) bool {
					return formInfo["env_id"] == item.EnvID && formInfo["cohort_id"] == item.CohortID && formInfo["fields"].(map[string]interface{})["variable"] == key
				})
				if ok {
					form := *formP
					if value != "" {
						if form["fields"].(map[string]interface{})["type"].(string) == "inputNumber" &&
							form["fields"].(map[string]interface{})["formatType"].(string) == "decimalLength" &&
							form["fields"].(map[string]interface{})["length"].(float64) != 0 {
							lengthString := strconv.FormatFloat(form["fields"].(map[string]interface{})["length"].(float64), 'f', -1, 64)
							if strings.Contains(lengthString, ".") {
								digits, _ := getFractionDigits(form["fields"].(map[string]interface{})["length"].(float64))
								str := strconv.FormatFloat(formInfo.Value, 'f', digits, 64)
								value = str
							} else {
								value = strconv.FormatFloat(formInfo.Value, 'f', -1, 64)
							}
						} else {
							value = strconv.FormatFloat(formInfo.Value, 'f', -1, 64)
						}
					}
				}
				OID := envOID
				if attribute.CohortID != primitive.NilObjectID {
					OID = attribute.CohortID
				}
				fkey := fmt.Sprintf("%s%s", OID.Hex(), key)
				fieldValue[fkey] = value
			}
		}
		if item.Medicines.DoseInfo != nil && item.Medicines.DoseInfo.Form != nil {
			OID := envOID
			if attribute.CohortID != primitive.NilObjectID {
				OID = attribute.CohortID
			}
			fkey := fmt.Sprintf("%s%s", OID.Hex(), item.Medicines.DoseInfo.Form.Key)

			fieldValue[fkey] = item.Medicines.DoseInfo.Form.Value

		}

		// 发药水平
		{

			{
				if item.Medicines.DoseInfo != nil &&
					item.Medicines.DoseInfo.DoseLevelList != nil {
					doseLevel = item.Medicines.DoseInfo.DoseLevelList.Name
				}

				if item.Medicines.DoseInfo != nil {
					doseInfo := item.Medicines.DoseInfo
					doseForm := doseInfo.Form
					key := doseForm.Key
					value := doseForm.Value
					OID := envOID
					if attribute.CohortID != primitive.NilObjectID {
						OID = attribute.CohortID
					}
					fkey := fmt.Sprintf("%s%s", OID.Hex(), key)
					formP, ok := slice.Find(forms, func(index int, formInfo map[string]interface{}) bool {
						return formInfo["env_id"] == item.EnvID && formInfo["cohort_id"] == item.CohortID && formInfo["fields"].(map[string]interface{})["variable"] == key
					})
					if ok {
						form := *formP
						optionP, _ := slice.Find(form["fields"].(map[string]interface{})["options"].(primitive.A), func(index int, formItem interface{}) bool {
							return formItem.(map[string]interface{})["value"] == value
						})
						option := *optionP
						if doseInfo.DoseLevelList != nil {
							fieldValue[fkey] = locales.Tr(ctx, option.(map[string]interface{})["label"].(string))
						} else {
							fieldValue[fkey] = option.(map[string]interface{})["label"].(string)
						}
					}
				}
			}
		}

		// 实际用药组别
		{
			if len(item.Medicines.RegisterGroup) != 0 {
				for _, groupValue := range item.Medicines.RegisterGroup {
					if item.Group != "" && item.Group != groupValue {
						registerGroup = strings.Join(item.Medicines.RegisterGroup, " ")
					}

				}
				if isBlindedRole && attribute.AttributeInfo.Blind && registerGroup != "" {
					registerGroup = tools.BlindData
				}
			}
		}

		for _, history := range item.History {
			keys := []string{"dispensing.dispensing", "dispensing.dtp-dispensing"}
			if operateType == 1 || operateType == 2 {
				keys = []string{"dispensing.dispensing", "dispensing.dtp-dispensing",
					"dispensing.reissue", "dispensing.dtp-reissue", "dispensingCustomer-reissue"}
			} else if operateType == 3 {
				keys = []string{"dispensing.replace", "dispensingCustomer-replace"}
			} else if operateType == 4 {
				keys = []string{"dispensing.register", "dispensingCustomer-register"}
			} else if operateType == 6 {
				keys = []string{"dispensing.retrieval", "dispensingCustomer-retrieval"}
			}
			//if history.Key == "history.dispensing.invalid" && history.Data["remark"] != nil {
			//	invalidRemark = history.Data["remark"].(string)
			//}
			for _, key := range keys {
				if strings.Contains(history.Key, key) {
					unicode := userMap[history.UID]
					operator = fmt.Sprintf("%s(%d)", history.User, unicode)
					if history.Data["remark"] != nil && operateType == 1 {
						mark = history.Data["remark"].(string)
					}

					if operateType == 1 && history.Data["reason"] != nil {
						unscheduledReason = history.Data["reason"].(string)
					} else if operateType == 2 && history.Data["remark"] != nil {
						reissueReason = history.Data["remark"].(string)
					}

					// 新轨迹
					if operateType == 1 || operateType == 2 || operateType == 3 || operateType == 4 || operateType == 6 {
						if len(history.CustomTemps) > 0 && history.Time == item.Medicines.Time {
							for _, customTemps := range history.CustomTemps {
								for _, customTempOptions := range customTemps.CustomTempOptions {
									if customTempOptions.Key == "history.dispensing.single.remark" {
										if operateType == 1 {
											if item.VisitSign {
												unscheduledRemark = customTempOptions.Data["remark"].(string)
											} else {
												mark = customTempOptions.Data["remark"].(string)
											}
										}
										if operateType == 2 { // 旧数据处理
											reissueRemark = customTempOptions.Data["remark"].(string)
										}
										if operateType == 3 {
											replaceRemark = customTempOptions.Data["remark"].(string)
										}
										if operateType == 4 {
											registerRemark = customTempOptions.Data["remark"].(string)
										}
										if operateType == 6 {
											retrieveRemark = customTempOptions.Data["remark"].(string)
										}
									}
									if customTempOptions.Key == "history.dispensing.single.reasonDispensingVisit" || customTempOptions.Key == "history.dispensing.single.reasonDispensingVisitCustomer" {
										if operateType == 1 {
											unscheduledReason = customTempOptions.Data["reasonDispensingVisit"].(string)
										}
										if operateType == 2 { // 旧数据处理
											reissueReason = customTempOptions.Data["reasonDispensingVisit"].(string)
										}
									}
									if customTempOptions.Key == "history.dispensing.single.reasonReissue" {
										reissueReason = customTempOptions.Data["reasonDispensingVisit"].(string)

									}

									if customTempOptions.Key == "history.dispensing.single.sendType" {
										sendType = locales.Tr(ctx, customTempOptions.TransData)
									}
								}
							}
						}

					}

					if operateType == 3 {

						// 替换操作，需要匹配对应的key + number
						if history.Data["medicine"] != nil {
							for _, medicine := range history.Data["medicine"].(primitive.A) {
								if medicine.(string) == number || medicine.(string) == otherNumber {
									operator = fmt.Sprintf("%s(%d)", history.User, unicode)
									break
								}
							}
						}

					} else if operateType == 4 {
						operator = fmt.Sprintf("%s(%d)", history.User, unicode)

						// TODO登记操作，需要匹配对应的key + number
						medicines := history.Data["medicine"]
						if medicines == number {
							operator = fmt.Sprintf("%s(%d)", history.User, unicode)
							break
						}

					}
				}

			}
		}

		if &item.Medicines.Key != nil {
			if item.Medicines.Key != "history.dispensing.invalid" && item.Medicines.Key != "history.dispensing.resume" && item.Medicines.Key != "" {
				continue
			} else {
				if item.Medicines.Key == "history.dispensing.invalid" || item.Medicines.Key == "history.dispensing.resume" {
					unicode := userMap[item.Medicines.UID]
					operator = fmt.Sprintf("%s(%d)", item.Medicines.User, unicode)
					if item.Medicines.Data["remark"] != nil {
						invalidRemark = item.Medicines.Data["remark"].(string)
					}
					if item.Medicines.Key == "history.dispensing.invalid" {
						operateTypeStr = locales.Tr(ctx, "export.dispensing.invalid")
					} else if item.Medicines.Key == "history.dispensing.resume" {
						operateTypeStr = locales.Tr(ctx, "export.dispensing.recover")
					}
					outSize = ""
					sendType = ""
				}
			}
		}

		// 发放方式
		{
			if item.Medicines.DTP != 0 {
				sendType = locales.Tr(ctx, "history.dispensing.send-type-"+convertor.ToString(item.Medicines.DTP))
			}
		}

		// 物流
		{
			if item.Order.LogisticsInfo.Number != "" {
				logistics = mapLogisticsCompanyCode[item.Order.LogisticsInfo.Logistics] + "/" + item.Order.LogisticsInfo.Number
			}
		}

		// 物流
		{
			logisticsRemark = item.Order.LogisticsInfo.Other
		}

		// 过滤字段
		for _, field := range fieldsArray {
			{
				//项目 "report.attributes.project"
				if field == data.ReportAttributesProjectNumber {
					tmp = append(tmp, project.ProjectInfo.Number)
				}
				if field == data.ReportAttributesProjectName {
					tmp = append(tmp, project.ProjectInfo.Name)
				}
				//国家
				if field == data.ReportAttributesInfoCountry {
					tmp = append(tmp, country)
				}
				//区域
				if field == data.ReportAttributesInfoRegion {
					tmp = append(tmp, region)
				}
				//中心编号
				if field == data.ReportAttributesInfoSiteNumber {
					tmp = append(tmp, site)
				}
				//中心名称
				if field == data.ReportAttributesInfoSiteName {
					tmp = append(tmp, siteName)

				}
				//房间号
				if field == data.ReportAttributesDispensingRoom {
					tmp = append(tmp, room)

				}

				//群组
				if field == data.ReportAttributesRandomCohort {
					cohort = cohortMap[item.CohortID]
					tmp = append(tmp, cohort)
				}

				//受试者号
				if field == data.ReportAttributesInfoSubjectNumber {
					tmp = append(tmp, subject)

				}

				//阶段
				if field == data.ReportAttributesRandomStage {
					cohort = cohortMap[item.CohortID]
					tmp = append(tmp, cohort)
				}

				//随机号
				if field == data.ReportAttributesRandomNumber {
					tmp = append(tmp, randomNumber)

				}
				//访视周期
				if field == data.ReportAttributesDispensingCycleName {
					tmp = append(tmp, visit)

				}
				//发药操作类型
				if field == data.ReportAttributesDispensingType {
					tmp = append(tmp, operateTypeStr)

				}

				//计划访视时间
				if field == data.ReportAttributesDispensingPlanTime {
					tmp = append(tmp, planTime)
				}

				//发药操作时间
				if field == data.ReportAttributesDispensingTime {
					tmp = append(tmp, operateTime)

				}

				//发药操作人
				if field == data.ReportAttributesDispensingOperator {
					tmp = append(tmp, operator)

				}
				//研究产品编号
				if field == data.ReportAttributesDispensingMedicine {
					tmp = append(tmp, number)
				}

				//研究产品名称
				if field == data.ReportAttributesDispensingDrugName {
					tmp = append(tmp, name)
				}

				//发药标签
				if field == data.ReportAttributesDispensingLabel {
					if operateType == 6 { // 取回操作不展示发药标签
						tmp = append(tmp, "")
					} else {
						tmp = append(tmp, label)
					}
				}
				//发药标签
				if field == data.ReportAttributesDispensingDose {
					if operateType == 6 { // 取回操作不展示发药标签
						tmp = append(tmp, "")
					} else {
						tmp = append(tmp, doseLevel)
					}
				}

				//已替换研究产品编号
				if field == data.ReportAttributesDispensingMedicineReplace {
					tmp = append(tmp, replace)

				}
				//实际使用研究产品
				if field == data.ReportAttributesDispensingMedicineReal {
					tmp = append(tmp, realNumber)

				}

				//实际使用研究产品
				if field == data.ReportAttributesDispensingMedicineRealGroup {
					tmp = append(tmp, registerGroup)
				}

				//未编号研究产品数量
				if field == data.ReportAttributesDispensingDrugOtherNumber {
					tmp = append(tmp, otherNumber)

				}

				//发药备注
				if field == data.ReportAttributesDispensingRemark {
					tmp = append(tmp, mark)

				}
				//计划外发药原因
				if field == data.ReportAttributesDispensingOutVisitDispensingReason {
					tmp = append(tmp, unscheduledReason)

				}
				//补发原因
				if field == data.ReportAttributesDispensingReissueReason {
					tmp = append(tmp, reissueReason)

				}
				//批次号
				if field == data.ReportAttributesResearchBatch {
					tmp = append(tmp, batch)

				}
				//有效期
				if field == data.ReportAttributesResearchExpireDate {
					tmp = append(tmp, expire)
				}
				//包装号
				if field == data.ReportAttributesResearchPackageNumber {
					tmp = append(tmp, packageNumber)
				}

				//是否超窗
				if field == data.ReportAttributesDispensingOutSize {
					tmp = append(tmp, outSize)
				}

				// 剂量表单
				if field == data.ReportAttributesDispensingDoseFormulas {
					for _, v := range doseFieldCol {
						if operateType == 1 { // 取回操作不展示发药标签

							if fieldValue[v] == "form.control.type.options.one" || fieldValue[v] == "form.control.type.options.two" || fieldValue[v] == "form.control.type.options.three" {
								fieldValue[v] = locales.Tr(ctx, fieldValue[v])
							}

							tmp = append(tmp, fieldValue[v])
						} else {
							tmp = append(tmp, "")

						}

					}
				}

				//公式计算
				if field == data.ReportAttributesDispensingUseFormulas {

					var formulasKey string
					var formulasValue float64
					if isNumber {
						if item.Medicines.Time != 0 {
							if item.Medicines.UseFormulas != nil {
								useFormulas := item.Medicines.UseFormulas
								formulasKey = useFormulas.Key
								formulasValue = useFormulas.Value
							}
						}
					} else {
						if len(item.OtherDispensingMedicines) > 0 {
							otherMedicines := item.OtherDispensingMedicines
							if len(otherMedicines) > 0 {
								for _, otherMedicine := range otherMedicines {
									if otherMedicine.Name == item.Medicines.Name && otherMedicine.UseFormulas != nil {
										useFormulas := otherMedicine.UseFormulas
										formulasKey = useFormulas.Key
										formulasValue = useFormulas.Value
									}
								}
							}
						}
					}
					for _, key := range formulaBSAKey {
						value := ""
						if item.FormulaInfo != nil {
							formula := item.FormulaInfo

							if formula.Weight != nil && key == "weight" {
								value = convertor.ToString(formula.Weight)
							}
							if formula.Age != nil && key == "age" {
								value = convertor.ToString(formula.Age)
							}
							if formula.Height != nil && key == "height" {
								value = convertor.ToString(formula.Height)
							}
						}
						tmp = append(tmp, value)
					}

					for _, v := range fieldCol {
						value, exists := fieldValue[v]
						if !exists && v == formulasKey {
							value = strconv.FormatFloat(formulasValue, 'f', -1, 64)
						}
						tmp = append(tmp, value)
					}
				}

				//计划外备注
				if field == data.ReportAttributesDispensingOutVisitDispensingRemark {
					tmp = append(tmp, unscheduledRemark)
				}

				//计划外备注
				if field == data.ReportAttributesDispensingMedicineReissueRemark {
					tmp = append(tmp, reissueRemark)
				}

				// 替换备注
				if field == data.ReportAttributesDispensingMedicineReplaceRemark {
					tmp = append(tmp, replaceRemark)
				}
				// 取回备注
				if field == data.ReportAttributesDispensingMedicineRegisterRemark {
					tmp = append(tmp, retrieveRemark)
				}
				// 登记备注
				if field == data.ReportAttributesDispensingMedicineRegisterRemark {
					tmp = append(tmp, registerRemark)
				}

				// 不参加访视-备注
				if field == data.ReportAttributesDispensingMedicineInvalidRemark {
					tmp = append(tmp, invalidRemark)
				}
				// 发放方式
				if field == data.ReportAttributesDispensingMedicineSendType {
					tmp = append(tmp, sendType)
				}
				// 物流
				if field == data.ReportAttributesDispensingMedicineLogisticsInfo {
					tmp = append(tmp, logistics)
				}
				// 物流备注
				if field == data.ReportAttributesDispensingMedicineLogisticsRemark {
					tmp = append(tmp, logisticsRemark)
				}
			}
		}

		if item.Time != 0 || item.Medicines.Time != 0 {
			content = append(content, tmp)

			if registerGroup != "" {

				registerContent = append(registerContent, tmp)
			}

		}

	}
	env := ""
	if len(projectInfo) > 0 {
		env = projectInfo[0]["env"].(string)
	}
	fileName := fmt.Sprintf("%s[%s]DispenseReport_%s.xlsx", project.Number, env, now.Format("20060102150405"))
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	{
		streamWriter, _ := f.NewStreamWriter("Sheet1")
		t := make([]interface{}, len(title))

		for i, item := range title {
			t[i] = excelize.Cell{Value: item}
		}
		_ = streamWriter.SetRow("A1", t)
		for i := 1; i <= len(content); i++ {
			r := make([]interface{}, len(content[i-1]))
			for j := 0; j < len(content[i-1]); j++ {
				r[j] = excelize.Cell{Value: content[i-1][j]}
			}
			cell, _ := excelize.CoordinatesToCellName(1, i+1)
			_ = streamWriter.SetRow(cell, r)
		}
		_ = streamWriter.Flush()
	}
	{
		f.NewSheet(locales.Tr(ctx, "report.attributes.dispensing.sheet.actual"))
		streamWriter, _ := f.NewStreamWriter(locales.Tr(ctx, "report.attributes.dispensing.sheet.actual"))
		t := make([]interface{}, len(title))

		for i, item := range title {
			t[i] = excelize.Cell{Value: item}
		}
		_ = streamWriter.SetRow("A1", t)
		for i := 1; i <= len(registerContent); i++ {
			r := make([]interface{}, len(registerContent[i-1]))
			for j := 0; j < len(registerContent[i-1]); j++ {
				r[j] = excelize.Cell{Value: registerContent[i-1][j]}
			}
			cell, _ := excelize.CoordinatesToCellName(1, i+1)
			_ = streamWriter.SetRow(cell, r)
		}
		_ = streamWriter.Flush()
	}
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	return fileName, buffer.Bytes(), nil

}

//生成一个研究产品揭盲报表代码逻辑。
// 根据当前环境env_id，下载当前环境的
