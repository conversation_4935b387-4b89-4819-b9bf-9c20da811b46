package service

import (
	"clinflash-irt/database"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/maputil"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"regexp"
	"strings"
	"time"
)

// GetList 查询数据
func (s *SubjectService) GetList(ctx *gin.Context, req models.SubjectListReq) (map[string]interface{}, error) {

	project, err := database.GetProject(nil, req.ProjectID.Hex())
	if err != nil {
		return nil, err
	}
	match := bson.M{
		"project_id": req.ProjectID,
		"env_id":     req.EnvID,
		"deleted":    bson.M{"$ne": true},
	}
	dispatchMatch := bson.M{
		"project_id": req.ProjectID,
		"env_id":     req.EnvID,
	}
	attributeMatch := bson.M{
		"project_id": req.ProjectID,
		"env_id":     req.EnvID,
	}
	ids, exist := models.GetCohortIds(project, req.EnvID.Hex())
	//判断是否包含cohort的再随机类型
	c, e := models.GetCohort(project, req.EnvID.Hex(), req.CohortID.Hex())
	haveCohortReRandom := false
	if e {
		haveCohortReRandom = models.IsCohortReRandom(project, req.EnvID.Hex(), c.Name)
	} else {
		haveCohortReRandom = models.HaveCohortReRandom(project, req.EnvID.Hex())
	}

	if project.Type != 1 {
		if exist {
			// 根据项目类型与随机隔离状态判断查询条件
			if project.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
				if !req.CohortID.IsZero() {
					match["cohort_id"] = req.CohortID
					dispatchMatch["cohort_id"] = req.CohortID
					attributeMatch["cohort_id"] = req.CohortID
				} else {
					match["cohort_id"] = bson.M{"$in": ids}
					dispatchMatch["cohort_id"] = bson.M{"$in": ids}
					attributeMatch["cohort_id"] = bson.M{"$in": ids}
				}
			} else if project.Type == 2 && !req.CohortID.IsZero() {
				c, _ := models.GetCohort(project, req.EnvID.Hex(), req.CohortID.Hex())
				cIds, b := models.GetReRandomCohortIds(project, req.EnvID.Hex(), c.Name)
				if b {
					match["cohort_id"] = bson.M{"$in": cIds}
					dispatchMatch["cohort_id"] = bson.M{"$in": cIds}
					attributeMatch["cohort_id"] = bson.M{"$in": cIds}
				}
			} else {
				if !req.CohortID.IsZero() {
					match["cohort_id"] = req.CohortID
					dispatchMatch["cohort_id"] = req.CohortID
					attributeMatch["cohort_id"] = req.CohortID
				} else {
					match["cohort_id"] = bson.M{"$in": ids}
					dispatchMatch["cohort_id"] = bson.M{"$in": ids}
					attributeMatch["cohort_id"] = bson.M{"$in": ids}
				}
			}
		}
	}

	// 查询项目属性配置
	attributes := make([]models.Attribute, 0)
	cursor, err := tools.Database.Collection("attribute").Find(nil, attributeMatch)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &attributes)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	haveJustDispensing := false
	haveJustDispensingCount := slice.Count(attributes, func(index int, item models.Attribute) bool {
		return item.AttributeInfo.Dispensing && !item.AttributeInfo.Random
	})
	if haveJustDispensingCount > 0 {
		haveJustDispensing = true
	}
	haveJustRandom := false
	haveJustRandomCount := slice.Count(attributes, func(index int, item models.Attribute) bool {
		return item.AttributeInfo.Random
	})
	if haveJustRandomCount > 0 {
		haveJustRandom = true
	}

	//判断属性配置的盲态，多个cohort如果有一个盲态就算盲态
	isRandomNumber := false
	isRandomSequenceNumber := false
	blind := false
	attribute := models.Attribute{}
	if len(attributes) == 1 {
		attribute = attributes[0]
		isRandomNumber = attribute.AttributeInfo.IsRandomNumber
		isRandomSequenceNumber = attribute.AttributeInfo.IsRandomSequenceNumber
		blind = attribute.AttributeInfo.Blind
	} else {
		if project.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) || (project.Type == 2 && haveCohortReRandom) {
			if len(attributes) > 1 {
				for _, attribute := range attributes {
					// 多个属性配置有一个展示随机号就展示随机号列
					if attribute.AttributeInfo.IsRandomNumber {
						isRandomNumber = true
					}
					// 多个属性配置有一个展示盲态就展示揭盲列
					if attribute.AttributeInfo.Blind {
						blind = true
					}
					// 多个属性配置有一个展示盲态就展示揭盲列
					if attribute.AttributeInfo.IsRandomSequenceNumber {
						isRandomSequenceNumber = true
					}
				}
				attribute = attributes[0]
			}
		} else {
			if len(attributes) > 1 {
				isRandomNumberGroup := slice.GroupWith(attributes, func(item models.Attribute) bool {
					return item.AttributeInfo.IsRandomNumber
				})
				if len(maputil.Keys(isRandomNumberGroup)) == 1 {
					randomAttribute := isRandomNumberGroup[maputil.Keys(isRandomNumberGroup)[0]][0]
					isRandomNumber = randomAttribute.AttributeInfo.IsRandomNumber
				}
				blindGroup := slice.GroupWith(attributes, func(item models.Attribute) bool {
					return item.AttributeInfo.Blind
				})
				if len(maputil.Keys(blindGroup)) == 1 {
					blindAttribute := blindGroup[maputil.Keys(blindGroup)[0]][0]
					blind = blindAttribute.AttributeInfo.Blind
				}

				count := slice.Count(attributes, func(index int, item models.Attribute) bool {
					return item.AttributeInfo.IsRandomSequenceNumber
				})
				isRandomSequenceNumber = count > 0

				attribute = attributes[0]
			}
		}
	}
	// 随机列表
	randomLists := make([]models.RandomList, 0)
	//forms表单
	forms := make([]models.Form, 0)
	cursor, err = tools.Database.Collection("form").Find(nil, attributeMatch)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &forms)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	cursor, err = tools.Database.Collection("random_list").Find(nil, attributeMatch)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &randomLists)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	//模糊匹配受试者号 分层 表单
	if req.Keyword != "" {
		//列出所有分层的信息
		type regexFactor struct {
			EnvID        primitive.ObjectID
			CohortID     primitive.ObjectID
			RandomListID primitive.ObjectID
			Label        string
			Name         string
			OptionLabel  string
			OptionValue  string
		}
		regexFactors := make([]regexFactor, 0)
		for _, list := range randomLists {
			for _, factor := range list.Design.Factors {
				for _, option := range factor.Options {
					regexFactors = append(regexFactors, regexFactor{
						EnvID:        list.EnvironmentID,
						CohortID:     list.CohortID,
						RandomListID: list.ID,
						Label:        factor.Label,
						Name:         factor.Name,
						OptionLabel:  option.Label,
						OptionValue:  option.Value,
					})
				}
			}
		}
		regexFactors = slice.Unique(regexFactors)
		//列出所有表单分层的信息
		type regexForm struct {
			EnvID       primitive.ObjectID
			CohortID    primitive.ObjectID
			FormID      primitive.ObjectID
			Label       string
			Name        string
			OptionLabel string
			OptionValue string
		}
		regexForms := make([]regexForm, 0)
		for _, form := range forms {
			for _, field := range form.Fields {
				for _, option := range field.Options {
					regexForms = append(regexForms, regexForm{
						EnvID:       form.EnvironmentID,
						CohortID:    form.CohortID,
						FormID:      form.ID,
						Label:       field.Label,
						Name:        field.Name,
						OptionLabel: option.Label,
						OptionValue: option.Value,
					})
				}
			}
		}
		regexForms = slice.Unique(regexForms)
		regexBsonA := bson.A{}
		regexBsonA = append(regexBsonA, bson.M{"info": bson.M{"$elemMatch": bson.M{"name": "shortname", "value": bson.M{"$regex": req.Keyword, "$options": "i"}}}})
		if len(regexFactors) > 0 {
			factors := slice.Filter(regexFactors, func(index int, item regexFactor) bool {
				return strings.Contains(item.OptionLabel, req.Keyword)
			})
			for _, factor := range factors {
				if factor.CohortID.IsZero() {
					regexBsonA = append(regexBsonA, bson.M{"info": bson.M{"$elemMatch": bson.M{"name": factor.Name, "value": factor.OptionValue}}})
				} else {
					regexBsonA = append(regexBsonA, bson.M{"cohort_id": factor.CohortID, "info": bson.M{"$elemMatch": bson.M{"name": factor.Name, "value": factor.OptionValue}}})
				}

			}
		}
		if len(regexForms) > 0 {
			rForms := slice.Filter(regexForms, func(index int, item regexForm) bool {
				return strings.Contains(item.OptionLabel, req.Keyword)
			})
			for _, form := range rForms {
				if form.CohortID.IsZero() {
					regexBsonA = append(regexBsonA, bson.M{"info": bson.M{"$elemMatch": bson.M{"name": form.Name, "value": form.OptionValue}}})
				} else {
					regexBsonA = append(regexBsonA, bson.M{"cohort_id": form.CohortID, "info": bson.M{"$elemMatch": bson.M{"name": form.Name, "value": form.OptionValue}}})
				}

			}
		}
		match["$or"] = regexBsonA
	}
	if req.SiteIDs != nil && len(req.SiteIDs) > 0 {
		match["project_site_id"] = bson.M{"$in": req.SiteIDs}
	} else {
		service := ProjectSiteService{}
		sites, err := service.UserSites(ctx, project.CustomerID.Hex(), req.ProjectID.Hex(), req.EnvID.Hex(), req.RoleID.Hex())
		if err != nil {
			return nil, err
		}
		if sites != nil && len(sites) > 0 {
			siteIds := slice.Map(sites, func(index int, item map[string]interface{}) primitive.ObjectID {
				return item["id"].(primitive.ObjectID)
			})
			match["project_site_id"] = bson.M{"$in": siteIds}
		} else {
			return map[string]interface{}{"total": 0,
				"items":                  []models.SubjectView{},
				"fields":                 []models.ListField{},
				"blind":                  blind,
				"isRandomNumber":         isRandomNumber,
				"isRandomSequenceNumber": isRandomSequenceNumber,
				"reRandomForm":           []models.ReRandomInfo{},
				"haveJustDispensing":     haveJustDispensing,
				"haveJustRandom":         haveJustRandom,
				"haveCohortReRandom":     haveCohortReRandom,
			}, nil
		}
	}
	after := bson.M{}
	if req.Status != nil && len(req.Status) > 0 {
		pv := false
		statusList := make([]int, 0)
		registerScreenStatus := make([]int, 0)
		isToBeRandom := false
		joinStatus := false
		// 新增状态10处理
		for _, st := range req.Status {
			if st == 10 { // 待随机状态
				isToBeRandom = true
			} else if st == 1 || st == 7 {
				registerScreenStatus = append(registerScreenStatus, st)
			} else if st == 11 {
				joinStatus = true
			} else if st == 12 {
				pv = true
			} else {
				statusList = append(statusList, st)
			}
		}
		// 构建复合查询条件
		matchs := bson.A{}
		// 原有条件继续保留
		if len(statusList) > 0 {
			matchs = append(matchs,
				bson.M{"status": bson.M{"$in": statusList}})
		}
		// 入组状态特殊处理
		if joinStatus {
			matchs = append(matchs,
				bson.M{
					"dispensing.status":     bson.M{"$in": []int{2, 3}},
					"status":                bson.M{"$in": []int{1, 7}},
					"attribute.info.random": false,
				})
		}
		// 登记/筛选状态处理
		if len(registerScreenStatus) > 0 {
			matchs = append(matchs,
				bson.M{
					"status":                bson.M{"$in": registerScreenStatus},
					"attribute.info.random": false,
					"dispensing.status":     bson.M{"$nin": []int{2, 3}},
				},
				bson.M{
					"status":                bson.M{"$in": registerScreenStatus},
					"attribute.info.random": true,
				})
		}

		// PV揭盲状态
		if pv {
			matchs = append(matchs,
				bson.M{"pv_unblinding_status": 1})
		}
		if project.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
			env, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
				return item.ID == req.EnvID
			})
			cohort, _ := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
				return item.LastID == env.Cohorts[0].ID
			})

			// 待随机处理
			if isToBeRandom && cohort != nil {
				matchs = append(matchs,
					bson.M{"cohort_id": cohort.ID, "status": bson.M{"$in": bson.A{1, 7}}},
					bson.M{"cohort_id": env.Cohorts[0].ID, "status": bson.M{"$in": registerScreenStatus}})
			} else {
				matchs = append(matchs,
					bson.M{"cohort_id": env.Cohorts[0].ID, "status": bson.M{"$in": registerScreenStatus}})
			}
		} else if project.Type == 2 && (e && c.Type == 1 && !req.CohortID.IsZero()) || (req.CohortID.IsZero() && haveCohortReRandom) {
			// 需要关联特定队列的待随机状态
			firstIds := make([]primitive.ObjectID, 0)
			secondIds := make([]primitive.ObjectID, 0)
			if !req.CohortID.IsZero() {
				firstId, b := models.GetReRandomCohortFirstId(project, req.EnvID.Hex(), c.Name)
				if b {
					firstIds = append(firstIds, firstId)
				}
				secondId, b := models.GetReRandomCohortSecondId(project, req.EnvID.Hex(), c.Name)
				if b {
					secondIds = append(secondIds, secondId)
				}
			} else {
				firstIds, _ = models.GetReRandomCohortFirstIds(project, req.EnvID.Hex())
				secondIds, _ = models.GetReRandomCohortSecondIds(project, req.EnvID.Hex())
			}
			// 处理待随机状态
			if isToBeRandom {
				matchs = append(matchs,
					bson.M{"cohort_id": bson.M{"$in": secondIds}, "status": bson.M{"$in": bson.A{1, 7}}},
					bson.M{"cohort_id": bson.M{"$in": firstIds}, "status": bson.M{"$in": registerScreenStatus}})
			} else {
				matchs = append(matchs,
					bson.M{"cohort_id": bson.M{"$in": firstIds}, "status": bson.M{"$in": registerScreenStatus}})
			}
		}
		after = bson.M{"$or": matchs}
	}
	pipeline := mongo.Pipeline{}
	pipeline = append(pipeline, bson.D{{Key: "$match", Value: match}})
	pipeline = append(pipeline, bson.D{{Key: "$unwind", Value: "$info"}})
	pipeline = append(pipeline, bson.D{{Key: "$match", Value: bson.M{"info.name": "shortname"}}})
	pipeline = append(pipeline, bson.D{
		{Key: "$sort", Value: bson.M{"_id": 1}}, // 按 _id 升序确保 $last 是最大 _id
	})
	pipeline = append(pipeline, bson.D{{Key: "$group", Value: bson.M{
		"_id":                    "$info.value",
		"id":                     bson.M{"$max": "$_id"},
		"sort_id":                bson.M{"$first": "$_id"}, // 再随机项目按id 排序时使用第一阶段的id
		"random_number":          bson.M{"$last": "$random_number"},
		"random_sequence_number": bson.M{"$last": "$random_sequence_number"},
		"cohort_id":              bson.M{"$last": "$cohort_id"},
		"env_id":                 bson.M{"$last": "$env_id"},
		"status":                 bson.M{"$last": "$status"},
		"pv_unblinding_status":   bson.M{"$last": "$pv_unblinding_status"},
		"ids":                    bson.M{"$push": "$_id"},
	}}})
	pipeline = append(pipeline, bson.D{{Key: "$addFields", Value: bson.M{
		"random_sequence_number_index": bson.M{
			"$cond": bson.M{"if": bson.M{
				"$or": bson.A{
					bson.M{"$eq": bson.A{"$random_sequence_number", nil}},
					bson.M{"$eq": bson.A{"$random_sequence_number", ""}},
				}},
				"then": 1, "else": 0}},
		"random_number_index": bson.M{
			"$cond": bson.M{"if": bson.M{
				"$or": bson.A{
					bson.M{"$eq": bson.A{"$random_number", nil}},
					bson.M{"$eq": bson.A{"$random_number", ""}},
				}},
				"then": 1, "else": 0}},
	}}})
	pipeline = append(pipeline, bson.D{{Key: "$lookup", Value: bson.M{"from": "dispensing", "localField": "id", "foreignField": "subject_id", "as": "dispensing"}}}) //关联发药

	if project.Type == 1 {
		pipeline = append(pipeline, bson.D{{Key: "$lookup", Value: bson.M{"from": "attribute", "localField": "env_id", "foreignField": "env_id", "as": "attribute"}}}) //关联发药
	} else {
		pipeline = append(pipeline, bson.D{{Key: "$lookup", Value: bson.M{"from": "attribute", "localField": "cohort_id", "foreignField": "cohort_id", "as": "attribute"}}}) //关联发药
	}

	pipeline = append(pipeline, bson.D{{Key: "$match", Value: after}})
	if req.SortField != "" {
		if (req.SortField == "random_number" || req.SortField == "random_sequence_number") && req.SortCollation != 0 {
			pipeline = append(pipeline, bson.D{{Key: "$sort", Value: bson.D{{req.SortField + "_index", 1}, {req.SortField, req.SortCollation}}}})
		} else if req.SortField == "shortname" && req.SortCollation != 0 {
			pipeline = append(pipeline, bson.D{{Key: "$sort", Value: bson.D{{"_id", req.SortCollation}}}})
		} else {
			pipeline = append(pipeline, bson.D{{Key: "$sort", Value: bson.D{{"sort_id", 1}}}})
		}
	} else {
		pipeline = append(pipeline, bson.D{{Key: "$sort", Value: bson.D{{"sort_id", 1}}}})
	}
	optTrue := true
	opt := &options.AggregateOptions{
		AllowDiskUse: &optTrue,
	}
	cursor, err = tools.Database.Collection("subject").Aggregate(nil, pipeline, opt)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	resps := make([]models.SubjectCountResp, 0)
	err = cursor.All(nil, &resps)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(resps) == 0 {
		return map[string]interface{}{"total": 0,
			"items":                  []models.SubjectView{},
			"fields":                 []models.ListField{},
			"blind":                  blind,
			"isRandomNumber":         isRandomNumber,
			"isRandomSequenceNumber": isRandomSequenceNumber,
			"reRandomForm":           []models.ReRandomInfo{},
			"haveJustDispensing":     haveJustDispensing,
			"haveJustRandom":         haveJustRandom,
			"haveCohortReRandom":     haveCohortReRandom,
		}, nil
	}
	//先后端分页出group后的数据
	endIndex := req.Start + req.Limit
	if endIndex > len(resps) {
		endIndex = len(resps)
	}
	subjectIds := make([]primitive.ObjectID, 0)
	respsIds := resps[req.Start:endIndex]
	for _, resp := range respsIds {
		for _, id := range resp.IDs {
			subjectIds = append(subjectIds, id)
		}
	}
	var subjectData []models.SubjectView
	detailPipe := mongo.Pipeline{
		{{"$match", bson.M{"_id": bson.M{"$in": subjectIds}, "deleted": bson.M{"$ne": true}}}},
		{{"$addFields", bson.M{
			"random_sequence_number_index": bson.M{
				"$cond": bson.M{"if": bson.M{
					"$or": bson.A{
						bson.M{"$not": bson.M{"$ifNull": bson.A{"$random_sequence_number", false}}},
						bson.M{"$eq": bson.A{"$random_sequence_number", ""}},
					}},
					"then": 1, "else": 0}},
			"random_number_index": bson.M{
				"$cond": bson.M{"if": bson.M{
					"$or": bson.A{
						bson.M{"$not": bson.M{"$ifNull": bson.A{"$random_number", false}}},
						bson.M{"$eq": bson.A{"$random_number", ""}},
					}},
					"then": 1, "else": 0}},
		}}},
	}
	if req.SortField != "" {
		if (req.SortField == "random_number" || req.SortField == "random_sequence_number") && req.SortCollation != 0 {
			detailPipe = append(detailPipe, bson.D{{Key: "$sort", Value: bson.D{{req.SortField + "_index", 1}, {req.SortField, req.SortCollation}}}})
		} else if req.SortField == "shortname" && req.SortCollation != 0 {
			detailPipe = append(detailPipe, bson.D{{Key: "$sort", Value: bson.D{{"info.0.value", req.SortCollation}}}})
		} else {
			detailPipe = append(detailPipe, bson.D{{Key: "$sort", Value: bson.D{{"sort_id", 1}}}})
		}
	} else {
		pipeline = append(pipeline, bson.D{{Key: "$sort", Value: bson.D{{"sort_id", 1}}}})
	}
	cursor, err = tools.Database.Collection("subject").Aggregate(nil, detailPipe)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &subjectData)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	//选了全部时，需要查完受试者后再查询中心
	projectSiteIds := slice.Map(subjectData, func(index int, item models.SubjectView) primitive.ObjectID {
		return item.ProjectSiteID
	})
	projectSiteIds = slice.Union(projectSiteIds)
	// 查询中心
	projectSites := make([]models.ProjectSite, 0)
	projectSiteMap := make(map[primitive.ObjectID]models.ProjectSite)
	siteProjectMatch := bson.M{"_id": bson.M{"$in": projectSiteIds}}
	cursor, err = tools.Database.Collection("project_site").Find(nil, siteProjectMatch)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &projectSites)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	for _, site := range projectSites {
		projectSiteMap[site.ID] = site
	}

	subNameSubjects := slice.Filter(subjectData, func(index int, item models.SubjectView) bool {
		return item.SubGroupName != ""
	})
	if len(subNameSubjects) > 0 {
		randomListIds := slice.Map(subNameSubjects, func(index int, item models.SubjectView) primitive.ObjectID {
			return item.RandomListID
		})
		replaceSubject := slice.Filter(subNameSubjects, func(index int, item models.SubjectView) bool {
			return item.RandomListID.IsZero()
		})
		beReplaceSubjectViews := make([]models.Subject, 0)
		if len(replaceSubject) > 0 {
			replaceSubjectIds := slice.Map(replaceSubject, func(index int, item models.SubjectView) primitive.ObjectID {
				return item.ID
			})
			cursor, err = tools.Database.Collection("subject").Find(nil, bson.M{"replace_subject_id": bson.M{"$in": replaceSubjectIds}, "deleted": bson.M{"$ne": true}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &beReplaceSubjectViews)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			beReplaceRandomListIds := slice.Map(beReplaceSubjectViews, func(index int, item models.Subject) primitive.ObjectID {
				return item.RandomListID
			})
			randomListIds = append(randomListIds, beReplaceRandomListIds...)
		}

		randomListIds = slice.Unique(randomListIds)
		replaceRandomLists := make([]models.RandomList, 0)
		//如果前面有做模糊匹配查询过了randomlist就直接用，否则才去查
		if len(randomLists) == 0 {
			cursor, err = tools.Database.Collection("random_list").Find(nil, bson.M{"_id": bson.M{"$in": randomListIds}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &replaceRandomLists)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		} else {
			replaceRandomLists = slice.Filter(randomLists, func(index int, item models.RandomList) bool {
				return slice.Contain(randomListIds, item.ID)
			})
		}

		//子组别是否需要盲态
		for i, subject := range subjectData {
			if subject.SubGroupName != "" {
				listP, b := slice.Find(replaceRandomLists, func(index int, item models.RandomList) bool {
					return subject.RandomListID == item.ID
				})
				if b {
					list := *listP
					groupP, b2 := slice.Find(list.Design.Groups, func(index int, item models.RandomListGroup) bool {
						return subject.ParGroupName == item.ParName && subject.SubGroupName == item.SubName
					})
					if b2 {
						subjectData[i].SubNameBlind = groupP.Blind
					}
				} else {
					sP, b3 := slice.Find(beReplaceSubjectViews, func(index int, item models.Subject) bool {
						return item.ReplaceSubjectID == subject.ID
					})
					if b3 {
						s := *sP
						replaceListP, b4 := slice.Find(randomLists, func(index int, item models.RandomList) bool {
							return s.RandomListID == item.ID
						})
						if b4 {
							list := *replaceListP
							groupP, b2 := slice.Find(list.Design.Groups, func(index int, item models.RandomListGroup) bool {
								return subject.ParGroupName == item.ParName && subject.SubGroupName == item.SubName
							})
							if b2 {
								subjectData[i].SubNameBlind = groupP.Blind
							}
						}
					}
				}
			}
		}
	}

	// 查询访视周期
	visitCycles := make([]models.VisitCycle, 0)
	cursor, err = tools.Database.Collection("visit_cycle").Find(nil, dispatchMatch)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &visitCycles)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// 处理数据

	// 查询发药记录

	/* 查询优化筛选查到的受试者，不查全部发药信息
	   jira 4376 发药项目，在受试者进行发药后，隐藏对应的删除操作
	*/
	dispatchMatch["subject_id"] = bson.M{"$in": subjectIds}
	var dispensingArray []models.DispensingHistory
	dispensingAllCursor, _ := tools.Database.Collection("dispensing").Aggregate(nil, mongo.Pipeline{
		{{"$match", dispatchMatch}},
		{{Key: "$lookup", Value: bson.M{"from": "history", "localField": "_id", "foreignField": "oid", "as": "history"}}},
		{{"$sort", bson.D{{"subject_id", 1}, {"serial_number", 1}}}},
	})
	err = dispensingAllCursor.All(nil, &dispensingArray)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// 查询是否有分层因素
	randomFilter := bson.M{"env_id": req.EnvID, "status": 1}
	// 在随机
	if ((project.ProjectInfo.Type == 2 && !haveCohortReRandom) || tools.InRandomIsolation(project.ProjectInfo.Number)) && !req.CohortID.IsZero() {
		randomFilter = bson.M{"env_id": req.EnvID, "cohort_id": req.CohortID, "status": 1}
	}

	randomActiveLists := make([]models.RandomList, 0)
	cursor, err = tools.Database.Collection("random_list").Find(nil, randomFilter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &randomActiveLists)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询该环境关联的所有用户
	userProjectEnvironmentList := make([]models.UserProjectEnvironment, 0)
	userProjectEnvironmentCursor, err := tools.Database.Collection("user_project_environment").Find(nil, bson.M{"env_id": req.EnvID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = userProjectEnvironmentCursor.All(nil, &userProjectEnvironmentList)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var userIDs []primitive.ObjectID
	for _, upel := range userProjectEnvironmentList {
		userIDs = append(userIDs, upel.UserID)
	}
	// 查询user
	var userList []models.User
	userCursor, err := tools.Database.Collection("user").Find(nil, bson.M{"_id": bson.M{"$in": userIDs}})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = userCursor.All(nil, &userList)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询项目角色权限
	projectRolePermissionList := make([]models.ProjectRolePermission, 0)
	projectRolePermissionCursor, err := tools.Database.Collection("project_role_permission").Find(nil, bson.M{"project_id": req.ProjectID, "name": bson.M{"$ne": "Project-Admin"}})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = projectRolePermissionCursor.All(nil, &projectRolePermissionList)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// 查询用户关联的角色
	userSiteList := make([]models.UserSite, 0)
	userSiteCursor, err := tools.Database.Collection("user_site").Find(nil, bson.M{"env_id": req.EnvID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = userSiteCursor.All(nil, &userSiteList)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 处理数据
	resultData, reRandomResultData, err := handleData(
		ctx,
		project,
		attributes,
		respsIds,
		subjectData,
		dispensingArray,
		req.RoleID.Hex(),
		visitCycles,
		projectSiteMap,
		randomLists,
		randomActiveLists,
		forms,
		userProjectEnvironmentList,
		userList,
		projectRolePermissionList,
		userSiteList,
		haveCohortReRandom,
	)
	if err != nil {
		return nil, err
	}
	//过滤未使用的fields
	usedField := make([]models.ListField, 0)
	reRandomUsedInfos := make([]models.ReRandomForm, 0)
	if (project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number)) || (project.ProjectInfo.Type == 2 && haveCohortReRandom) {
		cohortMap := make(map[primitive.ObjectID][]models.SubjectView)
		cohortMap = slice.GroupWith(reRandomResultData, func(item models.SubjectView) primitive.ObjectID {
			return item.CohortID
		})
		env, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
			return item.ID == req.EnvID
		})
		for _, cohort := range env.Cohorts {
			cohortData := cohortMap[cohort.ID]
			usedField = getUsedInfo(cohortData, forms, randomLists, attribute)
			reRandomUsedInfos = append(reRandomUsedInfos, models.ReRandomForm{
				CohortID:     cohort.ID,
				CohortName:   cohort.Name,
				ReRandomName: cohort.ReRandomName,
				Fields:       usedField,
			})
		}
		if len(reRandomUsedInfos[0].Fields) == 0 {
			// 查找第一个 Fields 非空的 reRandomUsedInfo
			for _, info := range reRandomUsedInfos {
				if len(info.Fields) > 0 {
					reRandomUsedInfos[0].Fields = append(reRandomUsedInfos[0].Fields, info.Fields[0])
					break
				}
			}
		}
	} else {
		usedField = getUsedInfo(resultData, forms, randomLists, attribute)
	}
	return map[string]interface{}{
		"total":                  len(resps),
		"items":                  resultData,
		"fields":                 usedField,
		"blind":                  blind,
		"isRandomNumber":         isRandomNumber,
		"isRandomSequenceNumber": isRandomSequenceNumber,
		"reRandomForm":           reRandomUsedInfos,
		"haveJustDispensing":     haveJustDispensing,
		"haveJustRandom":         haveJustRandom,
		"haveCohortReRandom":     haveCohortReRandom,
	}, nil
}

func handleData(
	ctx *gin.Context,
	project models.Project,
	attributes []models.Attribute,
	subjectResp []models.SubjectCountResp,
	subjectData []models.SubjectView,
	dispensingArray []models.DispensingHistory,
	roleID string,
	visitCycles []models.VisitCycle,
	projectSiteMap map[primitive.ObjectID]models.ProjectSite,
	randomLists []models.RandomList,
	randomActiveLists []models.RandomList,
	forms []models.Form,
	userProjectEnvironmentList []models.UserProjectEnvironment,
	userList []models.User,
	projectRolePermissionList []models.ProjectRolePermission,
	userSiteList []models.UserSite,
	haveCohortReRandom bool) ([]models.SubjectView, []models.SubjectView, error) {
	timezone, _ := tools.GetProjectLocation(project.ProjectInfo.TimeZoneStr, project.ProjectInfo.Tz, "")
	var resultData []models.SubjectView
	var reRandomResultData []models.SubjectView
	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return nil, nil, err
	}
	isBlindedRoomRole, err := tools.IsBlindedRoomRole(roleID)
	if err != nil {
		return nil, nil, err
	}
	//基本研究、普通群组、特殊再随机，只做基本处理
	if project.ProjectInfo.Type == 1 || tools.InRandomIsolation(project.ProjectInfo.Number) || (project.ProjectInfo.Type == 2 && !haveCohortReRandom) {
		for _, subjectView := range subjectData {
			err := subjectDataToSubjectView(
				ctx,
				project,
				attributes,
				&subjectView,
				visitCycles,
				dispensingArray,
				isBlindedRole,
				isBlindedRoomRole,
				timezone,
				projectSiteMap,
				randomLists,
				randomActiveLists,
				forms,
				userProjectEnvironmentList,
				userList,
				projectRolePermissionList,
				userSiteList,
			)
			if err != nil {
				return nil, nil, err
			}
			resultData = append(resultData, subjectView)
		}
	} else {
		//再随机以及包含再随机类型的群组项目走该合并逻辑
		for _, resp := range subjectResp {
			subjectView, _ := slice.Find(subjectData, func(index int, item models.SubjectView) bool {
				return item.ID == resp.Id
			})
			err := subjectDataToSubjectView(
				ctx,
				project,
				attributes,
				subjectView,
				visitCycles,
				dispensingArray,
				isBlindedRole,
				isBlindedRoomRole,
				timezone,
				projectSiteMap,
				randomLists,
				randomActiveLists,
				forms,
				userProjectEnvironmentList,
				userList,
				projectRolePermissionList,
				userSiteList,
			)
			if err != nil {
				return nil, nil, err
			}
			reRandomInfo := make([]models.ReRandomInfo, 0)
			reRandomActualInfo := make([]models.ReRandomInfo, 0)
			reRandomJoinTime := make([]models.ReRandomJoinTime, 0)
			if len(resp.IDs) == 1 {
				joinTime, err := getJoinTime(subjectView.Attribute, *subjectView)
				if err != nil {
					return nil, nil, err
				}
				reRandomJoinTime = append(reRandomJoinTime, models.ReRandomJoinTime{
					CohortID: subjectView.CohortID,
					JoinTime: joinTime,
				})

				// 第一阶段数据
				for _, info := range subjectView.Info {
					reRandomInfo = append(reRandomInfo, models.ReRandomInfo{
						CohortID: subjectView.CohortID,
						Name:     info.Name,
						Value:    info.Value,
					})
				}
				for _, info := range subjectView.ActualInfo {
					reRandomActualInfo = append(reRandomActualInfo, models.ReRandomInfo{
						CohortID: subjectView.CohortID,
						Name:     info.Name,
						Value:    info.Value,
					})
				}
				form := models.Form{}
				formP, _ := slice.Find(forms, func(index int, item models.Form) bool {
					return item.EnvironmentID == subjectView.EnvironmentID && item.CohortID == subjectView.CohortID
				})
				if formP != nil {
					form = *formP
				}
				//表单
				if form.Fields != nil {
					for _, fm := range form.Fields {
						//这里过滤掉无效并且该受试者没使用过的表单字段
						infoP, b := slice.Find(reRandomInfo, func(index int, item models.ReRandomInfo) bool {
							return item.Name == fm.Name && item.CohortID == subjectView.CohortID
						})
						if (fm.ApplicationType == nil || *fm.ApplicationType == 1 || *fm.ApplicationType == 4) && b && (fm.Status == nil || *fm.Status == 1 || infoP.Value != nil) {
							err = reRandomInfoLabelFormHandler(ctx, fm, reRandomInfo, infoP)
							if err != nil {
								return nil, nil, err
							}
						}
						aInfoP, b := slice.Find(reRandomActualInfo, func(index int, item models.ReRandomInfo) bool {
							return item.Name == fm.Name && item.CohortID == subjectView.CohortID
						})
						if (fm.ApplicationType == nil || *fm.ApplicationType == 1 || *fm.ApplicationType == 4) && b && (fm.Status == nil || *fm.Status == 1 || infoP.Value != nil) {
							err = reRandomInfoLabelFormHandler(ctx, fm, reRandomActualInfo, aInfoP)
							if err != nil {
								return nil, nil, err
							}
						}
					}
				}
				randomList := models.RandomList{}
				//先查是不是已随机，没随机的时候就拿第一个激活的
				if !subjectView.RandomListID.IsZero() {
					randomListP, _ := slice.Find(randomLists, func(index int, item models.RandomList) bool {
						return subjectView.RandomListID == item.ID
					})
					randomList = *randomListP
				} else {
					randomListP, b := slice.Find(randomActiveLists, func(index int, item models.RandomList) bool {
						return item.EnvironmentID == subjectView.EnvironmentID && item.CohortID == subjectView.CohortID && fileSiteIds(item.SiteIds, subjectView.ProjectSiteID)
					})
					if b {
						randomList = *randomListP
					}
				}
				if randomList.Design.Factors != nil {
					for i := 0; i < len(randomList.Design.Factors); i++ {
						ft := randomList.Design.Factors[i]
						//这里过滤掉无效并且该受试者没使用过的分层字段
						infoP, exist := slice.Find(reRandomInfo, func(index int, item models.ReRandomInfo) bool {
							return item.Name == ft.Name && (item.Value != nil || item.Value != "") && item.CohortID == subjectView.CohortID
						})
						status := ft.Status
						if (status == nil || *status == 1) && exist {
							err = reRandomInfoLabelHandler(ctx, ft, reRandomInfo, infoP)
							if err != nil {
								return nil, nil, err
							}
						}
						aInfoP, exist := slice.Find(reRandomActualInfo, func(index int, item models.ReRandomInfo) bool {
							return item.Name == ft.Name && (item.Value != nil || item.Value != "") && item.CohortID == subjectView.CohortID
						})
						if (status == nil || *status == 1) && exist {
							err = reRandomInfoLabelHandler(ctx, ft, reRandomActualInfo, aInfoP)
							if err != nil {
								return nil, nil, err
							}
						}
					}
				}
				reRandomResultData = append(reRandomResultData, *subjectView)
			} else {
				for _, id := range resp.IDs {
					sv, _ := slice.Find(subjectData, func(index int, item models.SubjectView) bool {
						return item.ID == id
					})
					//优先中心时区再到项目时区
					site := projectSiteMap[sv.ProjectSiteID]
					if site.TimeZone != "" {
						strTimeZone, err := tools.GetSiteTimeZoneInfo(site)
						siteTimeZone, err := tools.ParseTimezoneOffset(strTimeZone)
						//timezoneStr := strings.ReplaceAll(site.TimeZone, "UTC", "")
						//siteTimeZone, err := strconv.ParseFloat(timezoneStr, 64)
						if err != nil {
							siteTimeZone = float64(8)
						}
						sv.TimeZone = siteTimeZone
					} else {
						sv.TimeZone = timezone
					}
					if site.Tz != "" {
						sv.Tz = site.Tz
					}
					attribute, _ := slice.Find(attributes, func(index int, item models.Attribute) bool {
						return item.EnvironmentID == sv.EnvironmentID && item.CohortID == sv.CohortID
					})
					sv.Attribute = *attribute
					joinTime, err := getJoinTime(sv.Attribute, *sv)
					if err != nil {
						return nil, nil, err
					}
					reRandomJoinTime = append(reRandomJoinTime, models.ReRandomJoinTime{
						CohortID: sv.CohortID,
						JoinTime: joinTime,
					})

					for _, info := range sv.Info {
						reRandomInfo = append(reRandomInfo, models.ReRandomInfo{
							CohortID: sv.CohortID,
							Name:     info.Name,
							Value:    info.Value,
						})
					}
					for _, info := range sv.ActualInfo {
						reRandomActualInfo = append(reRandomActualInfo, models.ReRandomInfo{
							CohortID: sv.CohortID,
							Name:     info.Name,
							Value:    info.Value,
						})
					}
					form := models.Form{}
					formP, _ := slice.Find(forms, func(index int, item models.Form) bool {
						return item.EnvironmentID == subjectView.EnvironmentID && item.CohortID == sv.CohortID
					})
					if formP != nil {
						form = *formP
					}
					//表单
					if form.Fields != nil {
						for _, fm := range form.Fields {
							//这里过滤掉无效并且该受试者没使用过的表单字段
							infoP, b := slice.Find(reRandomInfo, func(index int, item models.ReRandomInfo) bool {
								return item.Name == fm.Name && item.CohortID == sv.CohortID
							})
							if (fm.ApplicationType == nil || *fm.ApplicationType == 1 || *fm.ApplicationType == 4) && b && (fm.Status == nil || *fm.Status == 1 || infoP.Value != nil) {
								err = reRandomInfoLabelFormHandler(ctx, fm, reRandomInfo, infoP)
								if err != nil {
									return nil, nil, err
								}
							}
							aInfoP, b := slice.Find(reRandomActualInfo, func(index int, item models.ReRandomInfo) bool {
								return item.Name == fm.Name && item.CohortID == sv.CohortID
							})
							if (fm.ApplicationType == nil || *fm.ApplicationType == 1 || *fm.ApplicationType == 4) && b && (fm.Status == nil || *fm.Status == 1 || infoP.Value != nil) {
								err = reRandomInfoLabelFormHandler(ctx, fm, reRandomActualInfo, aInfoP)
								if err != nil {
									return nil, nil, err
								}
							}
						}
					}
					randomList := models.RandomList{}
					//先查是不是已随机，没随机的时候就拿第一个激活的
					if !sv.RandomListID.IsZero() {
						randomListP, _ := slice.Find(randomLists, func(index int, item models.RandomList) bool {
							return sv.RandomListID == item.ID
						})
						randomList = *randomListP
					} else {
						randomListP, b := slice.Find(randomActiveLists, func(index int, item models.RandomList) bool {
							return item.EnvironmentID == sv.EnvironmentID && item.CohortID == sv.CohortID && fileSiteIds(item.SiteIds, subjectView.ProjectSiteID)
						})
						if b {
							randomList = *randomListP
						}
					}
					if randomList.Design.Factors != nil {
						for i := 0; i < len(randomList.Design.Factors); i++ {
							ft := randomList.Design.Factors[i]
							//这里过滤掉无效并且该受试者没使用过的分层字段
							infoP, exist := slice.Find(reRandomInfo, func(index int, item models.ReRandomInfo) bool {
								return item.Name == ft.Name && (item.Value != nil || item.Value != "") && item.CohortID == sv.CohortID
							})
							status := ft.Status
							if (status == nil || *status == 1) && exist {
								err = reRandomInfoLabelHandler(ctx, ft, reRandomInfo, infoP)
								if err != nil {
									return nil, nil, err
								}
							}
							aInfoP, exist := slice.Find(reRandomActualInfo, func(index int, item models.ReRandomInfo) bool {
								return item.Name == ft.Name && (item.Value != nil || item.Value != "") && item.CohortID == sv.CohortID
							})
							if (status == nil || *status == 1) && exist {
								err = reRandomInfoLabelHandler(ctx, ft, reRandomActualInfo, aInfoP)
								if err != nil {
									return nil, nil, err
								}
							}
						}
					}
					reRandomResultData = append(reRandomResultData, *sv)
				}
			}
			subjectView.ReRandomInfo = reRandomInfo
			subjectView.ReRandomActualInfo = reRandomActualInfo
			subjectView.ReRandomJoinTime = reRandomJoinTime

			resultData = append(resultData, *subjectView)
		}
	}
	return resultData, reRandomResultData, nil
}

func subjectDataToSubjectView(ctx *gin.Context,
	project models.Project,
	attributes []models.Attribute,
	subjectView *models.SubjectView,
	visitCycles []models.VisitCycle,
	dispensingArray []models.DispensingHistory,
	isBlindedRole bool,
	isBlindedRoomRole bool,
	timezone float64,
	projectSiteMap map[primitive.ObjectID]models.ProjectSite,
	randomLists []models.RandomList,
	randomActiveLists []models.RandomList,
	forms []models.Form,
	userProjectEnvironmentList []models.UserProjectEnvironment,
	userList []models.User,
	projectRolePermissionList []models.ProjectRolePermission,
	userSiteList []models.UserSite) error {

	attribute, _ := slice.Find(attributes, func(index int, item models.Attribute) bool {
		return item.EnvironmentID == subjectView.EnvironmentID && item.CohortID == subjectView.CohortID
	})
	visitCycleP, _ := slice.Find(visitCycles, func(index int, item models.VisitCycle) bool {
		return item.EnvironmentID == subjectView.EnvironmentID && item.CohortID == subjectView.CohortID
	})
	visitCycle := models.VisitCycle{}
	if visitCycleP != nil {
		visitCycle = *visitCycleP
	}

	randomList := models.RandomList{}
	//先查是不是已随机，没随机的时候就拿第一个激活的
	if !subjectView.RandomListID.IsZero() {
		randomListP, _ := slice.Find(randomLists, func(index int, item models.RandomList) bool {
			return subjectView.RandomListID == item.ID
		})
		randomList = *randomListP
	} else {
		randomListP, b := slice.Find(randomActiveLists, func(index int, item models.RandomList) bool {
			return item.EnvironmentID == subjectView.EnvironmentID && item.CohortID == subjectView.CohortID && fileSiteIds(item.SiteIds, subjectView.ProjectSiteID)
		})
		if b {
			randomList = *randomListP
		}
	}

	form := models.Form{}
	formP, _ := slice.Find(forms, func(index int, item models.Form) bool {
		return item.EnvironmentID == subjectView.EnvironmentID && item.CohortID == subjectView.CohortID
	})
	if formP != nil {
		form = *formP
	}

	if project.ProjectInfo.Type != 1 {
		env, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
			return item.ID == subjectView.EnvironmentID
		})
		cohort, _ := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
			return item.ID == subjectView.CohortID
		})
		subjectView.CohortName = cohort.Name
		subjectView.ReRandomName = cohort.ReRandomName
		subjectView.Cohort = *cohort
		subjectView.CohortStatus = cohort.Status
	}

	visitInfoArray := make([]models.VisitCycleInfo, 0) // 随机前访视
	hasRandom := false
	randomIndex := -1
	for index, visitCycleInfo := range visitCycle.Infos {
		if !visitCycleInfo.Random {
			visitInfoArray = append(visitInfoArray, visitCycleInfo)
		} else { // 碰到是随机的数据终止循环
			hasRandom = true
			randomIndex = index
			break
		}
	}
	//优先中心时区再到项目时区
	site := projectSiteMap[subjectView.ProjectSiteID]
	if site.TimeZone != "" {
		strTimeZone, err := tools.GetSiteTimeZoneInfo(site)
		siteTimeZone, err := tools.ParseTimezoneOffset(strTimeZone)
		//timezoneStr := strings.ReplaceAll(site.TimeZone, "UTC", "")
		//siteTimeZone, err := strconv.ParseFloat(timezoneStr, 64)
		if err != nil {
			siteTimeZone = float64(8)
		}
		subjectView.TimeZone = siteTimeZone
	} else {
		subjectView.TimeZone = timezone
	}
	if site.Tz != "" {
		subjectView.Tz = site.Tz
	}
	subjectView.Attribute = *attribute
	subjectView.SiteName = models.GetProjectSiteName(ctx, site)
	subjectView.SiteNumber = site.Number
	var fields []models.Field
	attribute.AttributeInfo.Field.CohortId = attribute.CohortID
	cohortName := ""
	var env models.Environment
	if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
		cohort, b := models.GetCohort(project, subjectView.EnvironmentID.Hex(), subjectView.CohortID.Hex())
		if b {
			cohortName = cohort.Name
		}
	} else if project.ProjectInfo.Type == 2 {
		envP, b := slice.Find(project.Environments, func(index int, item models.Environment) bool {
			return item.ID == subjectView.EnvironmentID
		})
		if b {
			env = *envP
			cohortP, b2 := slice.Find(envP.Cohorts, func(index int, item models.Cohort) bool {
				return item.ID == subjectView.CohortID
			})
			if b2 && cohortP.Type == 1 {
				cohortName = cohortP.Name + " - " + cohortP.ReRandomName
			}
		}
	}
	attribute.AttributeInfo.Field.CohortName = cohortName
	fields = append(fields, attribute.AttributeInfo.Field)
	infos := subjectView.Info
	actualInfos := subjectView.ActualInfo
	//表单

	if form.Fields != nil {
		vars := make([]string, 0)
		calcFactor := slice.Filter(randomList.Design.Factors, func(index int, item models.RandomFactor) bool {
			return (item.Status == nil || *item.Status == 1) && item.IsCalc
		})
		formulas := slice.Map(calcFactor, func(index int, item models.RandomFactor) string {
			return item.CustomFormulas
		})
		//提取自定义公式里面的变量
		for _, formula := range formulas {
			re := regexp.MustCompile(`\{([\x{4e00}-\x{9fa5}\w]+)\}`)
			matches := re.FindAllStringSubmatch(formula, -1)
			fieldNames := slice.Map(matches, func(index int, item []string) string {
				return item[1]
			})
			noCurrentTimeFieldNames := slice.Filter(fieldNames, func(index int, item string) bool {
				return item != "CurrentTime"
			})
			for _, name := range noCurrentTimeFieldNames {
				vars = append(vars, name)
			}
		}
		for _, fm := range form.Fields {
			//这里过滤掉无效并且该受试者没使用过的表单字段
			infoP, b := slice.Find(infos, func(index int, item models.Info) bool {
				return item.Name == fm.Name
			})
			actualInfoP, b3 := slice.Find(actualInfos, func(index int, item models.Info) bool {
				return item.Name == fm.Name
			})
			if (fm.ApplicationType == nil || *fm.ApplicationType == 1 || *fm.ApplicationType == 4) && (fm.Status == nil || *fm.Status == 1) && ((b && (infoP.Value != nil || infoP.Value != "")) || !b) {
				fm.CohortId = form.CohortID
				cohortP, b2 := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
					return item.ID == fm.CohortId
				})
				if b2 {
					fm.CohortName = cohortP.Name
				}
				if b {
					err := infoLabelFormHandler(ctx, fm, infos, infoP)
					if err != nil {
						return err
					}
				}
				if b3 {
					err := infoLabelFormHandler(ctx, fm, actualInfos, actualInfoP)
					if err != nil {
						return err
					}
				}
				if slice.Contain(vars, fm.Variable) {
					fm.IsCustomFormulaField = true
				}
				fields = append(fields, fm)
			}
		}
	}

	if randomList.Design.Factors != nil {
		for i := 0; i < len(randomList.Design.Factors); i++ {
			ft := randomList.Design.Factors[i]
			dateFormatP := ft.DateFormat
			dateFormat := ""
			if dateFormatP != nil {
				dateFormat = *dateFormatP
			}
			//这里过滤掉无效并且该受试者没使用过的分层字段
			infoP, exist := slice.Find(infos, func(index int, item models.Info) bool {
				return item.Name == ft.Name && (item.Value != nil || item.Value != "")
			})
			status := ft.Status
			if (status == nil || *status == 1) && exist {
				err := infoLabelHandler(ctx, ft, infoP)
				if err != nil {
					return err
				}
			}
			randomListCohortName := ""
			cohortP, b2 := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
				return item.ID == randomList.CohortID
			})
			if b2 {
				randomListCohortName = cohortP.Name
			}
			//组装该受试者的分层配置
			fields = append(fields, models.Field{
				CohortId:       randomList.CohortID,
				CohortName:     randomListCohortName,
				IsCalc:         randomList.Design.Factors[i].IsCalc,
				CalcType:       randomList.Design.Factors[i].CalcType,
				Name:           randomList.Design.Factors[i].Name,
				Label:          randomList.Design.Factors[i].Label,
				Round:          randomList.Design.Factors[i].Round,
				CustomFormulas: randomList.Design.Factors[i].CustomFormulas,
				Precision:      randomList.Design.Factors[i].Precision,
				Type:           randomList.Design.Factors[i].Type,
				Options:        randomList.Design.Factors[i].Options,
				Modifiable:     true, // 标记可修改
				Stratification: true, // 标记是分层因素
				Status:         status,
				DateFormat:     dateFormat,
			})
			actualInfoP, aExist := slice.Find(actualInfos, func(index int, item models.Info) bool {
				return item.Name == ft.Name && (item.Value != nil || item.Value != "")
			})
			if (status == nil || *status == 1) && aExist {
				err := infoLabelHandler(ctx, ft, actualInfoP)
				if err != nil {
					return err
				}
			}
		}
	}
	form.Fields = fields
	subjectView.Form = form
	factorSign := LayeredBl(*attribute, visitCycle, randomList.Design.Factors)
	subjectView.FactorSign = factorSign
	allowReplace := false
	allowReplaceVisitMap := map[string]models.VisitCycleInfo{}
	for _, info := range visitCycle.Infos {
		allowReplaceVisitMap[info.ID.Hex()] = info
	}
	if project.Type != 3 || tools.InRandomIsolation(project.Number) {
		joinTime, err := getJoinTime(*attribute, *subjectView)
		if err != nil {
			return err
		}
		subjectView.JoinTime = joinTime
	}
	subjectDispensing := slice.Filter(dispensingArray, func(index int, item models.DispensingHistory) bool {
		return item.SubjectID == subjectView.ID
	})
	for _, dispensing := range subjectDispensing {
		if subjectView.ID == dispensing.SubjectID {
			if len(dispensing.History) > 0 {
				subjectView.HasDispensing = true // 有发药轨迹代表发过药 隐藏删除按钮
			}
		}
	}
	if attribute.AttributeInfo.Dispensing {
		count := 0
		visitInfoID := ""
		for _, dispensing := range subjectDispensing {
			if subjectView.ID == dispensing.SubjectID {
				if checkVisitCycle(visitInfoArray, dispensing.VisitInfo.VisitCycleInfoID) &&
					dispensing.VisitInfo.Dispensing &&
					dispensing.Status == 1 {
					count++
				}

				// 已发药访视，已随机访视最新的
				if subjectView.Group != "" && dispensing.Status == 1 && !dispensing.VisitSign && visitInfoID == "" {
					visitInfoID = dispensing.VisitInfo.VisitCycleInfoID.Hex()
				}
				if dispensing.Status != 1 {
					visitInfoID = ""
				}
				if dispensing.Status == 2 {
					subjectView.IsDispensing = true

				}
			}
		}
		allowReplace = allowReplaceVisitMap[visitInfoID].Replace

		if count == 0 && hasRandom {
			subjectView.Sign = true
		} else {
			subjectView.Sign = false
		}
		subjectView.DispensingSign = true
		subjectView.ReplaceSign = attribute.AttributeInfo.AllowReplace && allowReplace

		if (project.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number)) || (project.Type == 2 && subjectView.Cohort.Type == 1) {
			if subjectView.Cohort.LastID.IsZero() { // 如果是第一阶段
				if randomIndex == 0 && (subjectView.Status == 1 || subjectView.Status == 2 || subjectView.Status == 7 || subjectView.Status == 8) {
					subjectView.DispensingSign = false
				}
			} else {
				// 查询第一阶段属性配置是否发药
				attribute, _ := slice.Find(attributes, func(index int, item models.Attribute) bool {
					return item.EnvironmentID == subjectView.EnvironmentID && item.CohortID == subjectView.Cohort.ID
				})
				// 如果第一阶段不发药
				if !attribute.AttributeInfo.Dispensing {
					if randomIndex == 0 && (subjectView.Status == 1 || subjectView.Status == 2 || subjectView.Status == 7 || subjectView.Status == 8) {
						subjectView.DispensingSign = false
					}
				}
			}
		} else {
			if randomIndex == 0 && (subjectView.Status == 1 || subjectView.Status == 2 || subjectView.Status == 7 || subjectView.Status == 8) { //第一个是随机操作  受试者未随机 不显示发药
				subjectView.DispensingSign = false
			}
		}

		if subjectView.ParGroupName != "" {
			subjectView.Group = subjectView.ParGroupName
		}

		if attribute.AttributeInfo.Blind && isBlindedRole && subjectView.Group != "" { // 如果是盲法项目并且分配了盲态权限则隐藏组别
			subjectView.Group = tools.BlindData        // 盲法项目隐藏组别
			subjectView.ParGroupName = tools.BlindData // 盲法项目隐藏组别
		}
		if isBlindedRole && subjectView.SubGroupName != "" && subjectView.SubNameBlind { // 如果是盲法项目并且分配了盲态权限则隐藏组别
			subjectView.SubGroupName = tools.BlindData // 盲法项目隐藏组别
		}
		// 不展示随机号
		if !attribute.AttributeInfo.IsRandomNumber {
			//	在随机如果不是第一阶段配置了不展示随机号。那么在待随机状态下要直接展示空 不能展示******
			if (project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) || (project.ProjectInfo.Type == 2 && subjectView.Cohort.Type == 1)) && ((!subjectView.Cohort.LastID.IsZero() && (subjectView.Status == 1 || subjectView.Status == 7)) || (subjectView.Cohort.LastID.IsZero() && (subjectView.Status == 1 || subjectView.Status == 4 || subjectView.Status == 5 || subjectView.Status == 7 || subjectView.Status == 8))) {
				subjectView.RandomNumber = ""
			} else {
				subjectView.RandomNumber = tools.BlindData
			}
		}
		if attribute.AttributeInfo.Blind && isBlindedRoomRole { // 如果是盲法项目且未分配了盲态权限则隐藏组别
			subjectView.RoomNumber = tools.BlindData // 盲法项目隐藏组别
		}
		if !attribute.AttributeInfo.Random {
			subjectView.Sign = false
		}
	} else {
		subjectView.ReplaceSign = attribute.AttributeInfo.AllowReplace
		if subjectView.SubGroupName != "" {
			subjectView.Group = subjectView.ParGroupName
		}
		if attribute.AttributeInfo.Blind && isBlindedRole && subjectView.Group != "" { // 如果是盲法项目且未分配了盲态权限则隐藏组别
			subjectView.Group = tools.BlindData        // 盲法项目隐藏组别
			subjectView.ParGroupName = tools.BlindData // 盲法项目隐藏组别
		}
		if isBlindedRole && subjectView.SubGroupName != "" && subjectView.SubNameBlind { // 如果是盲法项目并且分配了盲态权限则隐藏组别
			subjectView.SubGroupName = tools.BlindData // 盲法项目隐藏组别
		}
		// 不展示随机号
		if !attribute.AttributeInfo.IsRandomNumber {
			subjectView.RandomNumber = tools.BlindData
		}
		// 不展示房间号
		if attribute.AttributeInfo.Blind && isBlindedRoomRole { // 如果是盲法项目且未分配了盲态权限则隐藏组别
			subjectView.RoomNumber = tools.BlindData // 盲法项目隐藏组别
		}
		subjectView.Sign = true
		if !attribute.AttributeInfo.Random {
			subjectView.Sign = false
		}
	}

	// 盲法项目查询紧急揭盲和pv揭盲权限的用户
	if attribute.AttributeInfo.Blind {
		unblindingApprovalUser, pvUnblindingApprovalUser, _ := queryApprovalUser(userProjectEnvironmentList, userList, projectRolePermissionList, userSiteList, subjectView.ProjectSiteID)
		subjectView.UnblindingApprovalUser = unblindingApprovalUser
		subjectView.PvUnblindingApprovalUser = pvUnblindingApprovalUser
	}

	// 紧急揭盲
	var urgentUnblindingApprovals []models.UrgentUnblindingApproval
	if subjectView.UrgentUnblindingApprovals != nil && len(subjectView.UrgentUnblindingApprovals) > 0 {
		for _, uua := range subjectView.UrgentUnblindingApprovals {
			// 筛选申请人
			for _, u := range userList {
				if u.ID == uua.ApplicationBy {
					uua.ApplicationByEmail = u.Name + "/" + u.Phone
					break
				}
			}

			// 筛选审批人
			for _, u := range userList {
				if u.ID == uua.ApprovalBy {
					uua.ApprovalByEmail = u.Name + "/" + u.Phone
					break
				}
			}
			urgentUnblindingApprovals = append(urgentUnblindingApprovals, uua)
		}
		subjectView.UrgentUnblindingApprovals = urgentUnblindingApprovals
	}

	// pv揭盲
	var pvUrgentUnblindingApprovals []models.UrgentUnblindingApproval
	if subjectView.PvUrgentUnblindingApprovals != nil && len(subjectView.PvUrgentUnblindingApprovals) > 0 {
		for _, pua := range subjectView.PvUrgentUnblindingApprovals {
			// 筛选申请人
			for _, u := range userList {
				if u.ID == pua.ApplicationBy {
					pua.ApplicationByEmail = u.Name + "/" + u.Phone
					break
				}
			}

			// 筛选审批人
			for _, u := range userList {
				if u.ID == pua.ApprovalBy {
					pua.ApprovalByEmail = u.Name + "/" + u.Phone
					break
				}
			}
			pvUrgentUnblindingApprovals = append(pvUrgentUnblindingApprovals, pua)
		}

		subjectView.PvUrgentUnblindingApprovals = pvUrgentUnblindingApprovals
	}
	return nil
}

func getUsedInfo(resultData []models.SubjectView, forms []models.Form, randomLists []models.RandomList, attribute models.Attribute) []models.ListField {
	usedInfos := make([]models.ListField, 0)
	for _, result := range resultData {
		//获取表单
		currentForm := models.Form{}
		findF, b := slice.Find(forms, func(index int, item models.Form) bool {
			return item.CohortID == result.CohortID
		})
		if b {
			currentForm = *findF
		}
		//获取登记的分层
		currentRandomList := models.RandomList{}
		find, b := slice.Find(randomLists, func(index int, item models.RandomList) bool {
			return item.Status == 1 && item.CohortID == result.CohortID
		})
		if b {
			currentRandomList = *find
		}
		if !result.RandomListID.IsZero() {
			find, b := slice.Find(randomLists, func(index int, item models.RandomList) bool {
				return item.ID == result.RandomListID
			})
			if b {
				currentRandomList = *find
			}
		}
		for _, info := range result.Info {
			if info.Name == "shortname" {
				usedInfos = append(usedInfos, models.ListField{
					ID:      primitive.NilObjectID,
					Label:   attribute.AttributeInfo.Field.Label,
					LabelEn: attribute.AttributeInfo.Field.LabelEn,
					Name:    "shortname",
				})
			} else {
				f, b2 := slice.Find(currentForm.Fields, func(index int, item models.Field) bool {
					return item.Name == info.Name && info.Value != nil
				})
				if b2 {
					usedInfos = append(usedInfos, models.ListField{
						ID:         result.ID,
						Label:      f.Label,
						Type:       f.Type,
						Options:    f.Options,
						Name:       f.Name,
						Status:     f.Status,
						DateFormat: f.DateFormat,
						TimeFormat: f.TimeFormat,
					})
				}
				f2, b3 := slice.Find(currentRandomList.Design.Factors, func(index int, item models.RandomFactor) bool {
					return item.Name == info.Name && info.Value != nil
				})
				if b3 {
					usedInfos = append(usedInfos, models.ListField{
						ID:    result.ID,
						Label: f2.Label,

						Type:     f2.Type,
						Options:  f2.Options,
						Name:     f2.Name,
						Status:   f2.Status,
						IsCalc:   f2.IsCalc,
						CalcType: f2.CalcType,
					})
				}
			}
		}
	}
	usedInfos = slice.Unique(usedInfos)
	usedInfoOrder := slice.Map(usedInfos, func(index int, item models.ListField) string {
		return item.Label
	})
	usedInfoOrder = slice.Unique(usedInfoOrder)
	usedInfoIdLabelMap := make(map[string][]models.ListField)
	usedInfoLabelMap := make(map[string][]models.ListField)
	for _, info := range usedInfos {
		if usedInfoIdLabelMap[info.ID.Hex()+info.Label] == nil {
			usedInfoIdLabelMap[info.ID.Hex()+info.Label] = []models.ListField{info}
		} else {
			usedInfoIdLabelMap[info.ID.Hex()+info.Label] = append(usedInfoIdLabelMap[info.ID.Hex()+info.Label], info)
		}
		if usedInfoLabelMap[info.Label] == nil {
			usedInfoLabelMap[info.Label] = []models.ListField{info}
		} else {
			usedInfoLabelMap[info.Label] = append(usedInfoLabelMap[info.Label], info)
		}

	}
	usedField := make([]models.ListField, 0)
	for _, v := range usedInfoLabelMap {
		usedField = append(usedField, models.ListField{
			Label:      v[0].Label,
			LabelEn:    v[0].LabelEn,
			Type:       v[0].Type,
			Options:    v[0].Options,
			Name:       v[0].Name,
			IsCalc:     v[0].IsCalc,
			CalcType:   v[0].CalcType,
			DateFormat: v[0].DateFormat,
			TimeFormat: v[0].TimeFormat,
		})
	}
	for _, v := range usedInfoIdLabelMap {
		if len(v) > 1 {
			find, b := slice.Find(v, func(index int, item models.ListField) bool {
				return item.Status != nil && *item.Status == 2
			})
			count := slice.Count(usedField, func(index int, item models.ListField) bool {
				return item.Label == v[0].Label
			})
			if b && count < 2 {
				usedField = append(usedField, models.ListField{
					ID:             find.ID,
					Label:          v[0].Label,
					LabelEn:        v[0].LabelEn,
					Type:           v[0].Type,
					Options:        v[0].Options,
					Name:           v[0].Name,
					InvalidDisplay: find.Name,
					IsCalc:         find.IsCalc,
					CalcType:       find.CalcType,
					DateFormat:     v[0].DateFormat,
					TimeFormat:     v[0].TimeFormat,
				})
			}
		}
	}
	finishField := make([]models.ListField, 0)
	for _, label := range usedInfoOrder {
		for _, field := range usedField {
			if label == field.Label {
				if field.Name == "shortname" {
					field.Label = GetEmailSubjectReplaceText("zh", attribute)
					field.LabelEn = GetEmailSubjectReplaceText("en", attribute)
				}
				finishField = append(finishField, field)
			}
		}
	}
	return finishField
}

func infoLabelHandler(ctx *gin.Context, ft models.RandomFactor, infoP *models.Info) error {
	//组装info的label
	if ft.IsCalc {
		for _, option := range ft.Options {
			if infoP.Value != nil && option.Value == infoP.Value.(string) {
				infoP.Value = option.Value
				infoP.Label = option.Label
				break
			}
		}
	} else {
		if ft.Type == "select" || ft.Type == "radio" {
			for _, option := range ft.Options {
				if option.Value == infoP.Value {
					infoP.Label = option.Label
					break
				}
			}
		} else if ft.Type == "checkbox" {
			values := infoP.Value.([]interface{})
			ops := make([]string, 0)
			for _, value := range values {
				for _, option := range ft.Options {
					if option.Value == value.(string) {
						ops = append(ops, option.Label)
						break
					}
				}
			}
			infoP.Label = strings.Join(ops, ",")
		} else if ft.Type == "switch" {
			infoP.Label = locales.Tr(ctx, "common.no")
			if infoP.Value == true {
				infoP.Label = locales.Tr(ctx, "common.yes")
			}
		} else if ft.Type == "inputNumber" {
			label := convertor.ToString(infoP.Value)
			infoP.Label = label
		} else if ft.Type == "datePicker" {
			dateFormat := "YYYY-MM-DD"
			if ft.DateFormat != nil && *ft.DateFormat != "" {
				dateFormat = *ft.DateFormat
			}
			if infoP.Value != nil {
				label := fmt.Sprint(infoP.Value)
				if label != "" {
					parse, err := time.Parse("2006-01-02", label)
					if err != nil {
						return errors.WithStack(err)
					}
					label = parse.Format(tools.DateFormatParse(dateFormat))
					infoP.Label = label
				}
			}
		} else if ft.Type == "timePicker" {
			dateFormat := "YYYY-MM-DD HH:mm:ss"
			if ft.DateFormat != nil && *ft.DateFormat != "" {
				dateFormat = *ft.DateFormat
			}
			if infoP.Value != nil {
				label := fmt.Sprint(infoP.Value)
				if label != "" {
					parse, err := time.Parse("2006-01-02 15:04:05", label)
					if err != nil {
						return errors.WithStack(err)
					}
					label = parse.Format(tools.DateFormatParse(dateFormat))
					infoP.Label = label
				}
			}

		} else {
			infoP.Label = infoP.Value.(string)
		}
	}
	return nil
}
func reRandomInfoLabelHandler(ctx *gin.Context, ft models.RandomFactor, infos []models.ReRandomInfo, infoP *models.ReRandomInfo) error {
	//组装info的label
	if ft.IsCalc {
		for _, option := range ft.Options {
			if infoP.Value != nil && option.Value == infoP.Value.(string) {
				infoP.Value = option.Value
				infoP.Label = option.Label
				break
			}
		}
	} else {
		if ft.Type == "select" || ft.Type == "radio" {
			for _, option := range ft.Options {
				if option.Value == infoP.Value {
					infoP.Label = option.Label
					break
				}
			}
		} else if ft.Type == "checkbox" {
			values := infoP.Value.([]interface{})
			ops := make([]string, 0)
			for _, value := range values {
				for _, option := range ft.Options {
					if option.Value == value.(string) {
						ops = append(ops, option.Label)
						break
					}
				}
			}
			infoP.Label = strings.Join(ops, ",")
		} else if ft.Type == "switch" {
			infoP.Label = locales.Tr(ctx, "common.no")
			if infoP.Value == true {
				infoP.Label = locales.Tr(ctx, "common.yes")
			}
		} else if ft.Type == "inputNumber" {
			label := convertor.ToString(infoP.Value)
			infoP.Label = label
		} else if ft.Type == "datePicker" {
			dateFormat := "YYYY-MM-DD"
			if ft.DateFormat != nil && *ft.DateFormat != "" {
				dateFormat = *ft.DateFormat
			}
			if infoP.Value != nil {
				label := fmt.Sprint(infoP.Value)
				if label != "" {
					parse, err := time.Parse("2006-01-02", label)
					if err != nil {
						return errors.WithStack(err)
					}
					label = parse.Format(tools.DateFormatParse(dateFormat))
					infoP.Label = label
				}
			}
		} else if ft.Type == "timePicker" {
			dateFormat := "YYYY-MM-DD HH:mm:ss"
			if ft.DateFormat != nil && *ft.DateFormat != "" {
				dateFormat = *ft.DateFormat
			}
			if infoP.Value != nil {
				label := fmt.Sprint(infoP.Value)
				if label != "" {
					parse, err := time.Parse("2006-01-02 15:04:05", label)
					if err != nil {
						return errors.WithStack(err)
					}
					label = parse.Format(tools.DateFormatParse(dateFormat))
					infoP.Label = label
				}
			}
		} else {
			infoP.Label = infoP.Value.(string)
		}
	}
	return nil
}
func infoLabelFormHandler(ctx *gin.Context, ft models.Field, infos []models.Info, infoP *models.Info) error {
	if ft.Type == "select" || ft.Type == "radio" {
		for _, option := range ft.Options {
			if option.Value == infoP.Value {
				infoP.Label = option.Label
				break
			}
		}
	} else if ft.Type == "checkbox" {
		if infoP.Value != nil {
			values := infoP.Value.(primitive.A)
			ops := make([]string, 0)
			for _, value := range values {
				for _, option := range ft.Options {
					if option.Value == value.(string) {
						ops = append(ops, option.Label)
						break
					}
				}
			}
			infoP.Label = strings.Join(ops, ",")
		}
	} else if ft.Type == "switch" {
		infoP.Label = locales.Tr(ctx, "common.no")
		if infoP.Value == true {
			infoP.Label = locales.Tr(ctx, "common.yes")
		}
	} else if ft.Type == "inputNumber" {
		label := convertor.ToString(infoP.Value)
		infoP.Label = label
	} else if ft.Type == "datePicker" {
		dateFormat := "YYYY-MM-DD"
		if ft.DateFormat != "" {
			dateFormat = ft.DateFormat
		}
		if infoP.Value != nil {
			label := fmt.Sprint(infoP.Value)
			if label != "" {
				parse, err := time.Parse("2006-01-02", label)
				if err != nil {
					return errors.WithStack(err)
				}
				label = parse.Format(tools.DateFormatParse(dateFormat))
				infoP.Label = label
			}
		}
	} else if ft.Type == "timePicker" {
		dateFormat := "YYYY-MM-DD HH:mm:ss"
		if ft.TimeFormat != "" {
			dateFormat = ft.TimeFormat
		}
		if infoP.Value != nil {
			label := fmt.Sprint(infoP.Value)
			if label != "" {
				parse, err := time.Parse("2006-01-02 15:04:05", label)
				if err != nil {
					return errors.WithStack(err)
				}
				label = parse.Format(tools.DateFormatParse(dateFormat))
				infoP.Label = label
			}
		}
	} else {
		if infoP.Value != nil {
			infoP.Label = infoP.Value.(string)
		}
	}
	return nil
}
func reRandomInfoLabelFormHandler(ctx *gin.Context, ft models.Field, infos []models.ReRandomInfo, infoP *models.ReRandomInfo) error {
	if ft.Type == "select" || ft.Type == "radio" {
		for _, option := range ft.Options {
			if option.Value == infoP.Value {
				infoP.Label = option.Label
				break
			}
		}
	} else if ft.Type == "checkbox" {
		values := infoP.Value.(primitive.A)
		ops := make([]string, 0)
		for _, value := range values {
			for _, option := range ft.Options {
				if option.Value == value.(string) {
					ops = append(ops, option.Label)
					break
				}
			}
		}
		infoP.Label = strings.Join(ops, ",")
	} else if ft.Type == "switch" {
		infoP.Label = locales.Tr(ctx, "common.no")
		if infoP.Value == true {
			infoP.Label = locales.Tr(ctx, "common.yes")
		}
	} else if ft.Type == "inputNumber" {
		label := convertor.ToString(infoP.Value)
		infoP.Label = label
	} else if ft.Type == "datePicker" {
		dateFormat := "YYYY-MM-DD"
		if ft.DateFormat != "" {
			dateFormat = ft.DateFormat
		}
		if infoP.Value != nil {
			label := fmt.Sprint(infoP.Value)
			if label != "" {
				parse, err := time.Parse("2006-01-02", label)
				if err != nil {
					return errors.WithStack(err)
				}
				label = parse.Format(tools.DateFormatParse(dateFormat))
				infoP.Label = label
			}
		}
	} else if ft.Type == "timePicker" {
		dateFormat := "YYYY-MM-DD HH:mm:ss"
		if ft.TimeFormat != "" {
			dateFormat = ft.TimeFormat
		}
		if infoP.Value != nil {
			label := fmt.Sprint(infoP.Value)
			if label != "" {
				parse, err := time.Parse("2006-01-02 15:04:05", label)
				if err != nil {
					return errors.WithStack(err)
				}
				label = parse.Format(tools.DateFormatParse(dateFormat))
				infoP.Label = label
			}
		}
	} else {
		if infoP.Value != nil {
			infoP.Label = infoP.Value.(string)
		}
	}
	return nil
}
