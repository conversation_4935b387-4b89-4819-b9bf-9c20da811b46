package service

import (
	"bytes"
	"clinflash-irt/edc_push_task"
	"clinflash-irt/models"
	"clinflash-irt/task"
	"clinflash-irt/tools"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"strconv"
	"strings"
	"time"
)

type EDCExecutor struct{}

var _ task.EdcPushTaskExecutor = (*EDCExecutor)(nil)

// 随机信息推送(转发方法)
func (e *EDCExecutor) SubjectRandomPush(
	subjectId primitive.ObjectID,
	sourceType int,
	now time.Duration,
	replaceSubjectId string,
	replaceSubjectNo string,
	oldCohort string) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		// 查询受试者信息
		subjectFilter := bson.M{"_id": subjectId}
		var findSubject models.Subject
		err := tools.Database.Collection("subject").FindOne(sctx, subjectFilter).Decode(&findSubject)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 查询项目
		projectFilter := bson.M{"_id": findSubject.ProjectID}
		var project models.Project
		err = tools.Database.Collection("project").FindOne(sctx, projectFilter).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 查询属性
		var attribute models.Attribute
		match := bson.M{
			"project_id":  findSubject.ProjectID,
			"env_id":      findSubject.EnvironmentID,
			"customer_id": findSubject.CustomerID,
		}
		if findSubject.CohortID != primitive.NilObjectID {
			match = bson.M{
				"project_id":  findSubject.ProjectID,
				"env_id":      findSubject.EnvironmentID,
				"customer_id": findSubject.CustomerID,
				"cohort_id":   findSubject.CohortID,
			}
		}
		err = tools.Database.Collection("attribute").FindOne(sctx, match).Decode(&attribute)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 查询RandomList
		randomFilter := bson.M{
			"customer_id": findSubject.CustomerID,
			"project_id":  findSubject.ProjectID,
			"env_id":      findSubject.EnvironmentID,
			"status":      1,
			"$or": bson.A{
				bson.M{"site_ids": nil},
				bson.M{"site_ids": findSubject.ProjectSiteID},
			}}
		if findSubject.CohortID != primitive.NilObjectID {
			randomFilter = bson.M{
				"customer_id": findSubject.CustomerID,
				"project_id":  findSubject.ProjectID,
				"env_id":      findSubject.EnvironmentID,
				"status":      1,
				"cohort_id":   findSubject.CohortID,
				"$or": bson.A{
					bson.M{"site_ids": nil},
					bson.M{"site_ids": findSubject.ProjectSiteID},
				}}
		}
		if project.ProjectInfo.Type == 3 && findSubject.LastGroup != "" { // 在随机逻辑
			randomFilter = bson.M{
				"customer_id": findSubject.CustomerID,
				"project_id":  findSubject.ProjectID,
				"env_id":      findSubject.EnvironmentID,
				"status":      1,
				"cohort_id":   findSubject.CohortID,
				"last_group":  findSubject.LastGroup,
				"$or": bson.A{
					bson.M{"site_ids": nil},
					bson.M{"site_ids": findSubject.ProjectSiteID},
				}}
		}
		var randomList models.RandomList
		// 只有随机才查询random_list
		if attribute.AttributeInfo.Random {
			err = tools.Database.Collection("random_list").FindOne(sctx, randomFilter).Decode(&randomList)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		// 查询表单
		var randomForm models.Form
		formFilter := bson.M{"env_id": findSubject.EnvironmentID}
		if findSubject.CohortID != primitive.NilObjectID {
			formFilter = bson.M{"env_id": findSubject.EnvironmentID, "cohort_id": findSubject.CohortID}
		}

		if err := tools.Database.Collection("form").FindOne(nil, formFilter).Decode(&randomForm); err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		// 随机信息组装
		edcPush, err := RandomDataAssembly(sctx, findSubject, project, randomList, randomForm, sourceType, attribute, now, replaceSubjectId, replaceSubjectNo, oldCohort)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 数据推送
		err = Push(sctx, edcPush, project.EdcUrl)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}

	err := tools.Transaction(callback)
	//if err != nil {
	//	tools.SavePushEdcErrorLogNew(err, logData)
	//}

	return err
}

// 携带外部传入的会话上下文
func (e *EDCExecutor) SubjectRandomPushWithSession(
	sctx mongo.SessionContext, // 接收会话上下文
	subjectId primitive.ObjectID,
	sourceType int,
	now time.Duration,
	replaceSubjectId string,
	replaceSubjectNo string,
	oldCohort string,
) error {
	// 直接复用原事务逻辑（移除了外部的tools.Transaction包装）

	// 查询受试者信息
	subjectFilter := bson.M{"_id": subjectId}
	var findSubject models.Subject
	err := tools.Database.Collection("subject").FindOne(sctx, subjectFilter).Decode(&findSubject)
	if err != nil {
		return errors.WithStack(err)
	}
	// 查询项目
	projectFilter := bson.M{"_id": findSubject.ProjectID}
	var project models.Project
	err = tools.Database.Collection("project").FindOne(sctx, projectFilter).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}

	// 查询属性
	var attribute models.Attribute
	match := bson.M{
		"project_id":  findSubject.ProjectID,
		"env_id":      findSubject.EnvironmentID,
		"customer_id": findSubject.CustomerID,
	}
	if findSubject.CohortID != primitive.NilObjectID {
		match = bson.M{
			"project_id":  findSubject.ProjectID,
			"env_id":      findSubject.EnvironmentID,
			"customer_id": findSubject.CustomerID,
			"cohort_id":   findSubject.CohortID,
		}
	}
	err = tools.Database.Collection("attribute").FindOne(sctx, match).Decode(&attribute)
	if err != nil {
		return errors.WithStack(err)
	}
	// 查询RandomList
	randomFilter := bson.M{
		"customer_id": findSubject.CustomerID,
		"project_id":  findSubject.ProjectID,
		"env_id":      findSubject.EnvironmentID,
		"status":      1,
		"$or": bson.A{
			bson.M{"site_ids": nil},
			bson.M{"site_ids": findSubject.ProjectSiteID},
		}}
	if findSubject.CohortID != primitive.NilObjectID {
		randomFilter = bson.M{
			"customer_id": findSubject.CustomerID,
			"project_id":  findSubject.ProjectID,
			"env_id":      findSubject.EnvironmentID,
			"status":      1,
			"cohort_id":   findSubject.CohortID,
			"$or": bson.A{
				bson.M{"site_ids": nil},
				bson.M{"site_ids": findSubject.ProjectSiteID},
			}}
	}
	if project.ProjectInfo.Type == 3 && findSubject.LastGroup != "" { // 在随机逻辑
		randomFilter = bson.M{
			"customer_id": findSubject.CustomerID,
			"project_id":  findSubject.ProjectID,
			"env_id":      findSubject.EnvironmentID,
			"status":      1,
			"cohort_id":   findSubject.CohortID,
			"last_group":  findSubject.LastGroup,
			"$or": bson.A{
				bson.M{"site_ids": nil},
				bson.M{"site_ids": findSubject.ProjectSiteID},
			}}
	}
	var randomList models.RandomList
	// 只有随机才查询random_list
	if attribute.AttributeInfo.Random {
		err = tools.Database.Collection("random_list").FindOne(sctx, randomFilter).Decode(&randomList)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	// 查询表单
	var randomForm models.Form
	formFilter := bson.M{"env_id": findSubject.EnvironmentID}
	if findSubject.CohortID != primitive.NilObjectID {
		formFilter = bson.M{"env_id": findSubject.EnvironmentID, "cohort_id": findSubject.CohortID}
	}

	if err := tools.Database.Collection("form").FindOne(nil, formFilter).Decode(&randomForm); err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	// 随机信息组装
	edcPush, err := RandomDataAssembly(sctx, findSubject, project, randomList, randomForm, sourceType, attribute, now, replaceSubjectId, replaceSubjectNo, oldCohort)
	if err != nil {
		return errors.WithStack(err)
	}
	// 推送操作
	return Push(sctx, edcPush, project.EdcUrl)
}

// 随机数据组装
func RandomDataAssembly(
	sctx mongo.SessionContext,
	subject models.Subject,
	project models.Project,
	randomList models.RandomList,
	randomForm models.Form,
	sourceType int,
	attribute models.Attribute,
	now time.Duration,
	replaceSubjectId string,
	replaceSubjectNo string,
	oldCohort string) (models.EdcPush, error) {
	// 查询环境
	var env models.Environment
	for _, environment := range project.Environments {
		if environment.ID == subject.EnvironmentID {
			env = environment
			break
		}
	}

	// 查询cohort
	var cohort models.Cohort
	if project.Type != 1 {
		for _, ch := range env.Cohorts {
			if ch.ID == subject.CohortID {
				cohort = ch
				break
			}
		}
	}

	// 查询中心
	var projectSite models.ProjectSite
	err := tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
	if err != nil {
		return models.EdcPush{}, err
	}

	// 提取受试者号
	subjectNo := ""
	for _, info := range subject.Info {
		if info.Name == "shortname" {
			subjectNo = fmt.Sprintf("%v", info.Value)
			break
		}
	}
	// 分层因素数据
	var data []models.Data
	// 如果是修改实际分层的推送，sourceType = 12，只需要 cohortName 和实际分层的字段，别的都不要
	var dataForSourceType12 []models.Data
	if sourceType == 12 {
		dataForSourceType12 = append(dataForSourceType12, models.Data{
			Field: "cohortName",
			Value: models.GetCohortReRandomName(cohort),
		})
	}

	if randomList.Design.Factors != nil && len(randomList.Design.Factors) > 0 {
		for _, factor := range randomList.Design.Factors { // 分层因素
			// 随机分层
			for _, info := range subject.Info {
				// 随机分层
				if info.Name == factor.Name {
					if info.Value != nil {
						// 提取选项值
						label := ""
						for _, opt := range factor.Options {
							if opt.Value == info.Value {
								label = opt.Label
							}
						}
						// 追加数据
						data = append(data, models.Data{
							Field: factor.Name,
							Value: label,
						})
					}
					break
				}
			}
			// 实际分层
			for _, actualInfo := range subject.ActualInfo {
				// 随机分层
				if actualInfo.Name == factor.Name {
					if actualInfo.Value != nil {
						// 提取选项值
						label := ""
						for _, opt := range factor.Options {
							if opt.Value == actualInfo.Value {
								label = opt.Label
							}
						}
						// 追加数据
						data = append(data, models.Data{
							Field: factor.Name + "_actual",
							Value: label,
						})
						if sourceType == 12 {
							dataForSourceType12 = append(dataForSourceType12, models.Data{
								Field: factor.Name + "_actual",
								Value: label,
							})
						}
					}
					break
				}
			}
		}
	}

	// 表单数据
	if randomForm.Fields != nil && len(randomForm.Fields) > 0 {
		for _, rf := range randomForm.Fields {
			if (rf.Status == nil || *rf.Status == 1) && (rf.ApplicationType == nil || *rf.ApplicationType == 1 || *rf.ApplicationType == 4) {
				if rf.Type == "radio" || rf.Type == "select" { // 单选框或者下拉框
					for _, info := range subject.Info { // 受试者信息
						if info.Name == rf.Name {
							if info.Value != nil {
								// 提取选项值
								label := ""
								for _, opt := range rf.Options {
									if opt.Value == info.Value {
										label = opt.Label
									}
								}
								// 追加数据
								data = append(data, models.Data{
									Field: rf.Name,
									Value: label,
								})
							}
							break
						}
					}
				}
				if rf.Type == "checkbox" { // 复选框
					for _, info := range subject.Info { // 受试者信息
						if info.Name == rf.Name {
							if info.Value != nil {
								var checkboxBf bytes.Buffer
								str := info.Value.([]interface{})
								for _, option := range rf.Options {
									for j := 0; j < len(str); j++ {
										if option.Value == str[j].(string) {
											checkboxBf.WriteString(option.Label)
											checkboxBf.WriteString(",")
										}
									}
								}
								// 追加数据
								data = append(data, models.Data{
									Field: rf.Name,
									Value: checkboxBf.String(),
								})
							}
							break
						}
					}
				}
				if rf.Type == "switch" { // 开关
					for _, info := range subject.Info { // 受试者信息
						if info.Name == rf.Name {
							if info.Value != nil {
								v := ""
								if info.Value == true {
									v = "yes"
								} else {
									v = "no"
								}
								// 追加数据
								data = append(data, models.Data{
									Field: rf.Name,
									Value: v,
								})
							}
							break
						}
					}
				}

				if rf.Type == "input" || rf.Type == "inputNumber" || rf.Type == "textArea" || rf.Type == "datePicker" || rf.Type == "timePicker" { // 其它类型字段
					for _, info := range subject.Info { // 受试者信息
						if info.Name == rf.Name {
							if info.Value != nil {
								// 追加数据
								otherValue := info.Value
								if rf.Type == "inputNumber" && rf.FormatType == "decimalLength" && rf.Length != nil {
									lengthString := strconv.FormatFloat(*rf.Length, 'f', -1, 64)
									if strings.Contains(lengthString, ".") {
										digits, _ := getFractionDigits(*rf.Length)
										str := strconv.FormatFloat(info.Value.(float64), 'f', digits, 64)
										otherValue = str
									} else {
										otherValue = info.Value
									}
								} else {
									otherValue = info.Value
								}
								data = append(data, models.Data{
									Field: rf.Name,
									Value: otherValue,
								})
							}
							break
						}
					}
				}
			}
		}
	}

	// 如果是筛选的推送，sourceType = 13，只需要 cohortName 和实际分层的字段，别的都不要
	// 当前为筛选推送，或者（当前为修改推送，且筛选推送处于开启状态）
	var dataForSourceType13 []models.Data
	var switchCohortPushAll bool
	if sourceType == 13 || ((sourceType == 2 || sourceType == 14) && project.ProjectInfo.PushScenario.ScreenPush && subject.IsScreen != nil) {
		// 切换群组推送时，如果此时是已筛选，需要判断推送场景，如果开启了登记和随机前修改，则全部推送，否则如果开启了筛选，就只推筛选数据
		if sourceType == 14 && subject.IsScreen != nil && project.ProjectInfo.PushScenario.ScreenPush {
			if project.ProjectInfo.PushScenario.RegisterPush || project.ProjectInfo.PushScenario.UpdateRandomFrontPush {
				switchCohortPushAll = true
			}
		}

		if sourceType == 13 || sourceType == 14 { // 这里如果切换群组要借用 dataForSourceType13，也要添加这个字段
			dataForSourceType13 = append(dataForSourceType13, models.Data{
				Field: "cohortName",
				Value: models.GetCohortReRandomName(cohort),
			})
		}

		isScreenStr := ""
		if *subject.IsScreen == true {
			isScreenStr = "yes"
		} else {
			isScreenStr = "no"
		}

		data = append(data, models.Data{
			Field: "isScreen",
			Value: isScreenStr,
		})
		data = append(data, models.Data{
			Field: "screenTime",
			Value: subject.ScreenTime,
		})
		data = append(data, models.Data{
			Field: "icfTime",
			Value: subject.ICFTime,
		})
		dataForSourceType13 = append(dataForSourceType13, models.Data{
			Field: "isScreen",
			Value: isScreenStr,
		})
		dataForSourceType13 = append(dataForSourceType13, models.Data{
			Field: "screenTime",
			Value: subject.ScreenTime,
		})
		dataForSourceType13 = append(dataForSourceType13, models.Data{
			Field: "icfTime",
			Value: subject.ICFTime,
		})
	}

	data = append(data, models.Data{
		Field: "cohortName",
		Value: models.GetCohortReRandomName(cohort),
	})

	// 除去已随机和已筛选状态其它状态都表示受试者已经随机. 判断data是否要追加(随机号/随机组别/随机时间)字段
	// 如果sourceType 为修改，也不需要追加随机相关字段
	if subject.Status != 1 && subject.Status != 2 && subject.Status != 7 && subject.Status != 8 && sourceType != 2 {
		data = append(data, models.Data{
			Field: "randomNo",
			Value: subject.RandomNumber,
		})

		value := subject.Group
		parGroupName := "" // 主组别
		subGroupName := "" // 子组别
		// 如果是盲法项目且分配了盲态权限则隐藏组别
		if subject.SubGroupName != "" {
			gP, b := slice.Find(randomList.Design.Groups, func(index int, item models.RandomListGroup) bool {
				return item.Name == subject.Group
			})
			if b {
				group := *gP
				if attribute.AttributeInfo.Blind && group.Blind {
					value = tools.BlindData + " " + tools.BlindData
					parGroupName = tools.BlindData
					subGroupName = tools.BlindData
				} else if attribute.AttributeInfo.Blind && !group.Blind {
					value = tools.BlindData + " " + subject.SubGroupName
					parGroupName = tools.BlindData
					subGroupName = subject.SubGroupName
				} else if !attribute.AttributeInfo.Blind && group.Blind {
					value = subject.ParGroupName + " " + tools.BlindData
					parGroupName = subject.ParGroupName
					subGroupName = tools.BlindData
				}
			}
		} else {
			if attribute.AttributeInfo.Blind {
				value = tools.BlindData
			}
		}
		data = append(data, models.Data{
			Field: "group",
			Value: value, // 该数据推送的时候在写入
		})
		// 主组别
		data = append(data, models.Data{
			Field: "parGroupName",
			Value: parGroupName,
		})
		// 子组别
		data = append(data, models.Data{
			Field: "subGroupName",
			Value: subGroupName,
		})

		timeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
		if err != nil {
			return models.EdcPush{}, err
		}
		if timeZone == "" {
			zone, err := tools.GetTimeZone(project.ID)
			if err != nil {
				return models.EdcPush{}, err
			}
			timeZone = tools.FormatOffsetToZoneStringUtc(zone)
			//timeZone = fmt.Sprintf("UTC%+d", strTimeZone)
		}
		//intTimeZone, err := strconv.Atoi(strings.Replace(timeZone, "UTC", "", 1))
		intTimeZone, err := tools.ParseTimezoneOffset(timeZone)
		if err != nil {
			return models.EdcPush{}, err
		}

		hours := time.Duration(intTimeZone)
		minutes := time.Duration((intTimeZone - float64(hours)) * 60)
		duration := hours*time.Hour + minutes*time.Minute
		randomTime := time.Unix(subject.RandomTime.Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
		data = append(data, models.Data{
			Field: "randomTime",
			Value: randomTime + "(" + timeZone + ")",
		})
	}

	timestamp := time.Now().UnixMilli()
	md5Str := GetMD5Hash(timestamp) // 签名串
	var irtSubjectID = subject.ID.Hex()
	if project.Type == 3 && cohort.LastID != primitive.NilObjectID { // 在随机项目
		irtSubjectID = ""
	}

	id := primitive.NewObjectID()
	remarks, err := MakeRemarks(sourceType, subject, now)
	if err != nil {
		return models.EdcPush{}, err
	}

	subjectNoPrefix := ""
	// 追加受试者号前缀
	if attribute.AttributeInfo.Prefix && attribute.AttributeInfo.PrefixExpression != "" {
		subjectNoPrefix = strings.ReplaceAll(attribute.AttributeInfo.PrefixExpression, "{siteNO}", projectSite.Number)
	}
	// TODO v2.11 新增逻辑 推送规则如果是受试者号就不传受试者ID
	if project.PushRules == 1 {
		irtSubjectID = ""
		replaceSubjectId = ""
	}
	if sourceType == 12 {
		data = dataForSourceType12
	}

	if sourceType == 13 {
		data = dataForSourceType13
	}

	// 如果是切换群组推送，且受试者是已筛选状态，且没开始登记或随机前修改推送，则只推送筛选数据
	if sourceType == 14 && subject.IsScreen != nil && switchCohortPushAll == false {
		data = dataForSourceType13
	}

	var content = models.Content{
		Env:              env.Name,
		Project:          project.Number,
		Sign:             md5Str,
		Site:             projectSite.Number,
		IrtSubjectID:     irtSubjectID,
		ReplaceSubjectID: replaceSubjectId,
		ReplaceSubjectNo: replaceSubjectNo,
		EdcPushId:        id.Hex(),
		SubjectNoPrefix:  subjectNoPrefix,
		SubjectNo:        subjectNo,
		Timestamp:        timestamp,
		Type:             "",
		Remarks:          remarks,
		Cohort:           models.GetCohortReRandomName(cohort),
		SubjectData:      data,
		OldCohort:        oldCohort,
	}

	// 数据组装
	var edcPush = models.EdcPush{
		ID:            id,
		CustomerID:    subject.CustomerID,
		ProjectID:     subject.ProjectID,
		EnvironmentID: subject.EnvironmentID,
		CohortID:      subject.CohortID,
		ProjectSiteID: subject.ProjectSiteID,
		OID:           subject.ID,
		Content:       content,
		PushMode:      1,
		Source:        1,
		SourceType:    sourceType,
	}
	if len(projectSite.Tz) > 0 {
		edcPush.Location = projectSite.Tz
	}
	return edcPush, nil
}

// 发药信息推送(转发方法)
func (e *EDCExecutor) SubjectDispensingPush(
	dispensingId primitive.ObjectID,
	sourceType int,
	now time.Duration) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		// 查询发药信息
		dispensingFilter := bson.M{"_id": dispensingId}
		var findDispensing models.Dispensing
		err := tools.Database.Collection("dispensing").FindOne(sctx, dispensingFilter).Decode(&findDispensing)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 查询受试者信息
		subjectFilter := bson.M{"_id": findDispensing.SubjectID}
		var findSubject models.Subject
		err = tools.Database.Collection("subject").FindOne(sctx, subjectFilter).Decode(&findSubject)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 查询项目
		projectFilter := bson.M{"_id": findSubject.ProjectID}
		var project models.Project
		err = tools.Database.Collection("project").FindOne(sctx, projectFilter).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 查询属性
		var attribute models.Attribute
		match := bson.M{
			"project_id":  findSubject.ProjectID,
			"env_id":      findSubject.EnvironmentID,
			"customer_id": findSubject.CustomerID,
		}
		if findSubject.CohortID != primitive.NilObjectID {
			match = bson.M{
				"project_id":  findSubject.ProjectID,
				"env_id":      findSubject.EnvironmentID,
				"customer_id": findSubject.CustomerID,
				"cohort_id":   findSubject.CohortID,
			}
		}
		err = tools.Database.Collection("attribute").FindOne(sctx, match).Decode(&attribute)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 查询访视信息
		visitCycleFilter := bson.M{"customer_id": findSubject.CustomerID, "project_id": findDispensing.ProjectID, "env_id": findDispensing.EnvironmentID, "cohort_id": findDispensing.CohortID}
		var visitCycle models.VisitCycle
		err = tools.Database.Collection("visit_cycle").FindOne(sctx, visitCycleFilter).Decode(&visitCycle)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		visitInfo := models.VisitCycleInfo{}
		for _, info := range visitCycle.Infos {
			if info.ID == findDispensing.VisitInfo.VisitCycleInfoID {
				visitInfo = info
				break
			}
		}

		edcPush, err := DispensingDataAssembly(sctx, findDispensing, findSubject, project, visitInfo, sourceType, attribute, now)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 推送数据
		err = Push(sctx, edcPush, project.EdcUrl)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	//if err != nil {
	//	tools.SavePushEdcErrorLogNew(err, logData)
	//}
	return err
}

// 发药数据组装
func DispensingDataAssembly(sctx mongo.SessionContext, dispensing models.Dispensing, subject models.Subject, project models.Project, visitInfo models.VisitCycleInfo, sourceType int, attribute models.Attribute, now time.Duration) (models.EdcPush, error) {
	// 查询环境
	var env models.Environment
	for _, environment := range project.Environments {
		if environment.ID == subject.EnvironmentID {
			env = environment
			break
		}
	}

	// 查询cohort
	var cohort models.Cohort
	if project.Type != 1 {
		for _, ch := range env.Cohorts {
			if ch.ID == subject.CohortID {
				cohort = ch
				break
			}
		}
	}

	// 查询中心
	var projectSite models.ProjectSite
	err := tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
	if err != nil {
		return models.EdcPush{}, err
	}

	// 提取受试者号
	subjectNo := ""
	for _, info := range subject.Info {
		if info.Name == "shortname" {
			subjectNo = fmt.Sprintf("%v", info.Value)
			break
		}
	}

	// 组装推送的药物数据
	var data []models.Data
	// 药物号
	var drugNameNumberStr strings.Builder

	drug := make([]map[string]interface{}, 0)

	var labels []string
	// 对药物号进行排序
	err = slice.SortByField(dispensing.DispensingMedicines, "Number")
	if err != nil {
		return models.EdcPush{}, err
	}
	// 已编号药物
	for _, dm := range dispensing.DispensingMedicines {
		isBlindedDrug, err := tools.IsBlindedDrug(subject.EnvironmentID, dm.Name)
		if err != nil {
			return models.EdcPush{}, err
		}

		drugName := tools.BlindData // 药物名称
		if !isBlindedDrug {
			drugName = dm.Name
		}

		labels = append(labels, dm.Label)

		drugNameNumberStr.WriteString(dm.Number)
		drugNameNumberStr.WriteString("/")

		m := make(map[string]interface{})
		m["drugNo"] = dm.Number
		m["drugName"] = drugName
		drug = append(drug, m)

	}

	// 未编号药物
	for _, odm := range dispensing.OtherDispensingMedicines {
		isBlindedDrug, err := tools.IsBlindedDrug(subject.EnvironmentID, odm.Name)
		if err != nil {
			return models.EdcPush{}, err
		}

		drugName := tools.BlindData // 药物名称
		if !isBlindedDrug {
			drugName = odm.Name
		}

		labels = append(labels, odm.Label)

		//drugNameNumberStr.WriteString(drugName)
		drugNameNumberStr.WriteString("(")
		drugNameNumberStr.WriteString(strconv.Itoa(odm.Count))
		drugNameNumberStr.WriteString("/")
		drugNameNumberStr.WriteString(odm.Batch)
		drugNameNumberStr.WriteString("/")
		drugNameNumberStr.WriteString(odm.ExpireDate)
		drugNameNumberStr.WriteString(")")
		drugNameNumberStr.WriteString(" / ")

		m := make(map[string]interface{})
		m["drugName"] = drugName
		m["drugCount"] = odm.Count
		m["drugBatch"] = odm.Batch
		m["drugExpireDate"] = odm.ExpireDate
		drug = append(drug, m)
	}

	// 对药物号进行排序
	err = slice.SortByField(dispensing.RealDispensingMedicines, "Number")
	if err != nil {
		return models.EdcPush{}, err
	}
	//实际使用的药物
	if dispensing.RealDispensingMedicines != nil && len(dispensing.RealDispensingMedicines) > 0 {
		drugNameNumberStr.WriteString("【")
		for i, rdm := range dispensing.RealDispensingMedicines {
			index := i + 1

			isBlindedDrug, err := tools.IsBlindedDrug(subject.EnvironmentID, rdm.Name)
			if err != nil {
				return models.EdcPush{}, err
			}

			drugName := tools.BlindData // 药物名称
			if !isBlindedDrug {
				drugName = rdm.Name
			}
			drugNameNumberStr.WriteString(rdm.Number)
			if len(dispensing.RealDispensingMedicines) > index {
				drugNameNumberStr.WriteString("/ ")
			}

			m := make(map[string]interface{})
			m["drugNo"] = rdm.Number
			m["drugName"] = drugName
			drug = append(drug, m)
		}
		drugNameNumberStr.WriteString("】")
	}

	//实际使用的其它药物
	if dispensing.RealOtherDispensingMedicines != nil && len(dispensing.RealOtherDispensingMedicines) > 0 {
		drugNameNumberStr.WriteString("【")
		for _, rodm := range dispensing.RealOtherDispensingMedicines {
			isBlindedDrug, err := tools.IsBlindedDrug(subject.EnvironmentID, rodm.Name)
			if err != nil {
				return models.EdcPush{}, err
			}

			drugName := tools.BlindData // 药物名称
			if !isBlindedDrug {
				drugName = rodm.Name
			}

			//drugNameNumberStr.WriteString(drugName)
			drugNameNumberStr.WriteString("(")
			drugNameNumberStr.WriteString(strconv.Itoa(rodm.Count))
			drugNameNumberStr.WriteString("/")
			drugNameNumberStr.WriteString(rodm.Batch)
			drugNameNumberStr.WriteString("/")
			drugNameNumberStr.WriteString(rodm.ExpireDate)
			drugNameNumberStr.WriteString(")")
			drugNameNumberStr.WriteString(" / ")

			m := make(map[string]interface{})
			m["drugName"] = drugName
			m["drugCount"] = rodm.Count
			m["drugBatch"] = rodm.Batch
			m["drugExpireDate"] = rodm.ExpireDate
			drug = append(drug, m)
		}
		drugNameNumberStr.WriteString("】")
	}

	// 去除 /
	drugNo := ""
	if drugNameNumberStr.String() != "" {
		drugNo = drugNameNumberStr.String()
		last1 := drugNo[len(drugNo)-1:]
		last2 := drugNo[len(drugNo)-3:]
		if last1 == "/" {
			drugNo = drugNo[0 : len(drugNo)-1]
		} else if last2 == " / " {
			drugNo = drugNo[0 : len(drugNo)-3]
		}
	}

	drugValue := ""
	if drug != nil && len(drug) > 0 {
		drugStr, err := json.Marshal(drug)
		if err != nil {
			return models.EdcPush{}, err
		}
		drugValue = string(drugStr)
	} else {
		drugValue = "[]"
	}

	data = append(data, models.Data{
		Field:                        "drugNo",
		Value:                        drugNo,
		Visit:                        visitInfo.Number,
		InstanceRepeatNo:             dispensing.VisitInfo.InstanceRepeatNo,
		BlockRepeatNo:                dispensing.VisitInfo.BlockRepeatNo,
		DispensingMedicines:          dispensing.DispensingMedicines,
		OtherDispensingMedicines:     dispensing.OtherDispensingMedicines,
		RealDispensingMedicines:      dispensing.RealDispensingMedicines,
		RealOtherDispensingMedicines: dispensing.RealOtherDispensingMedicines,
	})

	data = append(data, models.Data{
		Field:                        "drug",
		Value:                        drugValue,
		Visit:                        visitInfo.Number,
		InstanceRepeatNo:             dispensing.VisitInfo.InstanceRepeatNo,
		BlockRepeatNo:                dispensing.VisitInfo.BlockRepeatNo,
		DispensingMedicines:          dispensing.DispensingMedicines,
		OtherDispensingMedicines:     dispensing.OtherDispensingMedicines,
		RealDispensingMedicines:      dispensing.RealDispensingMedicines,
		RealOtherDispensingMedicines: dispensing.RealOtherDispensingMedicines,
	})

	// 水平
	doseLevelValue := ""
	if dispensing.DoseInfo != nil && dispensing.DoseInfo.DoseLevelList != nil && dispensing.DoseInfo.DoseLevelList.Name != "" {
		doseLevelValue = dispensing.DoseInfo.DoseLevelList.Name
	}
	data = append(data, models.Data{
		Field:                        "doseLevel",
		Value:                        doseLevelValue,
		Visit:                        visitInfo.Number,
		InstanceRepeatNo:             dispensing.VisitInfo.InstanceRepeatNo,
		BlockRepeatNo:                dispensing.VisitInfo.BlockRepeatNo,
		DispensingMedicines:          dispensing.DispensingMedicines,
		OtherDispensingMedicines:     dispensing.OtherDispensingMedicines,
		RealDispensingMedicines:      dispensing.RealDispensingMedicines,
		RealOtherDispensingMedicines: dispensing.RealOtherDispensingMedicines,
	})

	//标签
	var labelValue strings.Builder

	labels = slice.Unique(labels)

	//if dispensing.Labels != nil && len(dispensing.Labels) > 0 {
	if labels != nil && len(labels) > 0 {
		for _, label := range labels {
			labelValue.WriteString(label)
			labelValue.WriteString(" ")
		}
	}
	data = append(data, models.Data{
		Field:                        "labels",
		Value:                        labelValue.String(),
		Visit:                        visitInfo.Number,
		InstanceRepeatNo:             dispensing.VisitInfo.InstanceRepeatNo,
		BlockRepeatNo:                dispensing.VisitInfo.BlockRepeatNo,
		DispensingMedicines:          dispensing.DispensingMedicines,
		OtherDispensingMedicines:     dispensing.OtherDispensingMedicines,
		RealDispensingMedicines:      dispensing.RealDispensingMedicines,
		RealOtherDispensingMedicines: dispensing.RealOtherDispensingMedicines,
	})

	dispensingTime := ""
	timeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
	if err != nil {
		return models.EdcPush{}, err
	}

	if timeZone == "" {
		zone, err := tools.GetTimeZone(project.ID)
		if err != nil {
			return models.EdcPush{}, err
		}
		timeZone = tools.FormatOffsetToZoneStringUtc(zone)
		//timeZone = fmt.Sprintf("UTC%+d", strTimeZone)
	}
	if dispensing.DispensingTime != 0 {
		//intTimeZone, err := strconv.Atoi(strings.Replace(timeZone, "UTC", "", 1))
		intTimeZone, err := tools.ParseTimezoneOffset(timeZone)
		if err != nil {
			return models.EdcPush{}, err
		}

		hours := time.Duration(intTimeZone)
		minutes := time.Duration((intTimeZone - float64(hours)) * 60)
		duration := hours*time.Hour + minutes*time.Minute
		dispensingTime = time.Unix(dispensing.DispensingTime.Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
		dispensingTime = dispensingTime + "(" + timeZone + ")"
	}
	data = append(data, models.Data{
		Field:            "drugTime",
		Value:            dispensingTime,
		Visit:            visitInfo.Number,
		InstanceRepeatNo: dispensing.VisitInfo.InstanceRepeatNo,
		BlockRepeatNo:    dispensing.VisitInfo.BlockRepeatNo,
	})

	data = append(data, models.Data{
		Field:            "cohortName",
		Value:            models.GetCohortReRandomName(cohort),
		Visit:            visitInfo.Number,
		InstanceRepeatNo: dispensing.VisitInfo.InstanceRepeatNo,
		BlockRepeatNo:    dispensing.VisitInfo.BlockRepeatNo,
	})

	timestamp := time.Now().UnixMilli()
	md5Str := GetMD5Hash(timestamp) // 签名串

	var irtSubjectID = subject.ID.Hex()
	if project.Type == 3 && cohort.LastID != primitive.NilObjectID { // 在随机项目
		irtSubjectID = ""
	}

	id := primitive.NewObjectID()
	remarks, err := MakeRemarks(sourceType, subject, now)
	if err != nil {
		return models.EdcPush{}, err
	}

	subjectNoPrefix := ""
	// 追加受试者号前缀
	if attribute.AttributeInfo.Prefix && attribute.AttributeInfo.PrefixExpression != "" {
		subjectNoPrefix = strings.ReplaceAll(attribute.AttributeInfo.PrefixExpression, "{siteNO}", projectSite.Number)
	}
	var content = models.Content{
		Env:             env.Name,
		Project:         project.Number,
		Sign:            md5Str,
		Site:            projectSite.Number,
		IrtSubjectID:    irtSubjectID,
		EdcPushId:       id.Hex(),
		SubjectNo:       subjectNo,
		SubjectNoPrefix: subjectNoPrefix,
		Timestamp:       timestamp,
		Type:            "",
		Remarks:         remarks,
		Cohort:          models.GetCohortReRandomName(cohort),
		SubjectData:     data,
	}
	if sourceType == 5 { // 访视外发药
		content.Type = "Unscheduled"
	} else if sourceType == 7 { // 补发
		content.Type = "Re-dispense"
	}

	// 数据组装
	var edcPush = models.EdcPush{
		ID:            id,
		CustomerID:    subject.CustomerID,
		ProjectID:     subject.ProjectID,
		EnvironmentID: subject.EnvironmentID,
		CohortID:      subject.CohortID,
		ProjectSiteID: subject.ProjectSiteID,
		OID:           dispensing.ID,
		Content:       content,
		PushMode:      1,
		Source:        2,
		SourceType:    sourceType,
	}
	if len(projectSite.Tz) > 0 {
		edcPush.Location = projectSite.Tz
	}

	return edcPush, nil
}

// 数据推送
func Push(sctx mongo.SessionContext, edcPush models.EdcPush, url string) error {
	// 检查推送表记录
	edcPushFilter := bson.M{"oid": edcPush.OID}
	var findEdcPushs []models.EdcPush
	cursor, _ := tools.Database.Collection("edc_push").Find(sctx, edcPushFilter)
	cursor.All(nil, &findEdcPushs)

	// 发药的数据需要把type字段记录下来（针对的是推送失败，发药数据type字段为空，使EDC分不清楚是补发还是计划外发药的问题）
	if edcPush.Source == 2 && findEdcPushs != nil && len(findEdcPushs) > 0 {
		edcPush.Content.Type = findEdcPushs[0].Content.Type
	}

	edcPush.SerialNumber = len(findEdcPushs)                     // 序号
	edcPush.Content.EdcPushLogId = primitive.NewObjectID().Hex() // 记录推送日志ID
	edcPush.SendTime = time.Duration(time.Now().Unix())          // 推送时间
	edcPush.CreateTime = time.Duration(time.Now().Unix())        // 创建时间
	//result, httpRspBody, err := tools.HttpPost(url, edcPush.Content) // 调用推送接口

	var project models.Project

	// 查询项目
	projectFilter := bson.M{"_id": edcPush.ProjectID}
	tools.Database.Collection("project").FindOne(sctx, projectFilter).Decode(&project)

	var result string
	// v2.11 作废
	//var httpRspBody models.HTTPRspBody
	// TODO v2.11新增
	var httpRspBodyNew models.HTTPRspBodyNew
	var err error

	// 组装成功开始推送
	if project.ProjectInfo.EdcSupplier == 1 {
		// edc对接
		// v2.11作废
		//res, rspBody, e := tools.HttpPost(project.EdcUrl, edcPush.Content) // 调用推送接口
		//TODO v2.11新增
		res, rspBody, e := tools.HttpPostNew(project.EdcUrl, edcPush.Content) // 调用推送接口
		result = res
		httpRspBodyNew = rspBody
		err = e
	} else if project.ProjectInfo.EdcSupplier == 2 {
		//rave edc对接
		res, rspBody, e := tools.HttpPostRave(project, edcPush) // 调用推送接口
		result = res
		//TODO v2.11新增
		httpRspBodyNew.Status = rspBody.Status
		httpRspBodyNew.Success = rspBody.Success
		httpRspBodyNew.Code = rspBody.Code
		httpRspBodyNew.Error = rspBody.Error
		httpRspBodyNew.Message = rspBody.Message
		// v2.11作废
		//httpRspBody = rspBody
		err = e
	}
	edcPushLogStatus := 0               // TODO v2.11新增
	if httpRspBodyNew.Success == true { // 成功 直接改状态为成功
		//edcPush.Status = 4 // 处理中 v2.11作废
		if project.ProjectInfo.EdcSupplier == 2 {
			edcPush.Status = 1
			edcPushLogStatus = 1
		} else {
			if httpRspBodyNew.Data.Status == "1" { // 成功
				edcPush.Status = 1
				edcPushLogStatus = 1
				edcPush.EdcFields = httpRspBodyNew.Data.SaveField
				edcPush.EecErrorCode = 200 // 成功了edc没有返回errorCode,只能IRT这边自己去定义
				edcPush.ProcessingResults = httpRspBodyNew.Data.Message
			} else if httpRspBodyNew.Data.Status == "2" { // 失败
				edcPush.Status = 2 // 入库失败
				edcPushLogStatus = 5
				edcPush.EdcFields = httpRspBodyNew.Data.SaveField
				edcPush.EecErrorCode = httpRspBodyNew.Data.ErrorCode
				edcPush.ProcessingResults = httpRspBodyNew.Data.Message
			} else { // 没有任何被处理的内容
				edcPush.Status = 5   // 不处理
				edcPushLogStatus = 6 // 不处理
			}
		}
		edcPush.Retry = 0
		edcPush.Result = result
		edcPush.HttpRspBody = httpRspBodyNew
	} else { // 失败
		if err != nil {
			edcPush.Result = err
		} else {
			edcPush.Result = result
		}
		edcPush.Status = 0 // 状态改为推送中（因为还要走定时任务）
		edcPushLogStatus = 0
		edcPush.Retry = 0
		edcPush.HttpRspBody = httpRspBodyNew
	}

	// 不论成功失败 如果存在数据就把之前推送中或者推送失败的数据改为“已失效”状态，防止之前没推送过去的数据被定时任务推过去把最新的数据覆盖

	// 修改推送表
	update := bson.M{
		"$set": bson.M{
			"status": 3, // 状态改为已失效
		},
	}

	// 查询推送表，用于修改推送记录
	filter := bson.M{"oid": edcPush.OID, "status": bson.M{"$in": bson.A{0, 2, 4}}}
	var edcPushOne models.EdcPush
	tools.Database.Collection("edc_push").FindOne(sctx, filter).Decode(&edcPushOne)
	// 修改推送表
	_, err = tools.Database.Collection("edc_push").UpdateMany(sctx, filter, update)
	if err != nil {
		return err
	}

	// 同时修改推送记录表
	if edcPushOne.Content.SubjectNo != "" {
		pushLogId, err := primitive.ObjectIDFromHex(edcPushOne.Content.EdcPushLogId)
		if err != nil {
			// 有些历史脏数据会在这里导致报错后返回，改为纪录但是不返回
			logrus.Info("edc_push_log id 转换失败，oid:"+edcPushOne.OID.Hex()+"旧的推送信息如下\n", edcPushOne)
			//return err
		}
		_, err = tools.Database.Collection("edc_push_log").UpdateOne(sctx, bson.M{"_id": pushLogId, "status": bson.M{"$in": bson.A{0, 2, 4, 5}}}, update)
		if err != nil {
			return err
		}
	}

	// 添加推送记录信息
	// 实际分层修改推送时，如果edc没有配置实际分层映射，即返回的字段没有关于实际分层的，则推送状态改为不处理
	if edcPush.SourceType == 12 {
		saveFields := edcPush.HttpRspBody.Data.SaveField
		actualFlag := false
		for _, field := range saveFields {
			// 检查是否有以 _actual 结尾的
			if strings.HasSuffix(field, "_actual") {
				actualFlag = true
				break
			}
		}
		if !actualFlag {
			edcPush.Status = 5
		}
	}

	// 历史数据推送，根据edc 是否配置了映射来做一些过滤
	if edcPush.SourceTypeList != nil && len(edcPush.SourceTypeList) > 0 {
		if slice.Contain(edcPush.SourceTypeList, 12) || slice.Contain(edcPush.SourceTypeList, 13) {
			saveFields := edcPush.HttpRspBody.Data.SaveField
			actualFlag := false
			screenFlag := false
			for _, field := range saveFields {
				// 检查是否有以 _actual 结尾的
				if strings.HasSuffix(field, "_actual") {
					actualFlag = true
				}
				// 检查是否有 isScreen, screenTime, icfTime
				if field == "isScreen" || field == "screenTime" || field == "icfTime" {
					screenFlag = true
				}
				if actualFlag && screenFlag {
					break
				}
			}
			if slice.Contain(edcPush.SourceTypeList, 12) && !actualFlag && httpRspBodyNew.Data.Status == "1" {
				// 移除实际分层的修改
				//  如果只有实际分层的修改，就不移除了，改为不处理
				if len(edcPush.SourceTypeList) != 1 {
					edcPush.SourceTypeList = slice.Filter(edcPush.SourceTypeList, func(_, item int) bool {
						return item != 12
					})
				} else {
					edcPush.Status = 5
				}
			}
			if slice.Contain(edcPush.SourceTypeList, 13) && !screenFlag && httpRspBodyNew.Data.Status == "1" {
				// 移除筛选
				//  如果只有筛选，就不移除了，改为不处理
				if len(edcPush.SourceTypeList) != 1 {
					edcPush.SourceTypeList = slice.Filter(edcPush.SourceTypeList, func(_, item int) bool {
						return item != 13
					})
				} else {
					edcPush.Status = 5
				}
			}
			// 如果移除之后没有其他的推送，则改为不处理
			if len(edcPush.SourceTypeList) == 0 {
				edcPush.Status = 5
			}
		}
	}

	_, err = tools.Database.Collection("edc_push").InsertOne(sctx, edcPush)
	if err != nil {
		return err
	}
	// 添加推送日志信息
	err = AddEdcPushLog(sctx, edcPush, edcPushLogStatus)
	if err != nil {
		return err
	}

	return nil
}

// 获取签名串
func GetMD5Hash(time int64) string {
	hash := md5.Sum([]byte(fmt.Sprint("ClinflashIRT", time)))
	return hex.EncodeToString(hash[:])
}

// 添加推送记录信息
func AddEdcPushLog(sctx mongo.SessionContext, edcPush models.EdcPush, edcPushLogStatus int) error {
	id, err := primitive.ObjectIDFromHex(edcPush.Content.EdcPushLogId)
	if err != nil {
		return err
	}

	var edcPushLog = models.EdcPushLog{
		ID:                id,
		CustomerID:        edcPush.CustomerID,
		ProjectID:         edcPush.ProjectID,
		EnvironmentID:     edcPush.EnvironmentID,
		CohortID:          edcPush.CohortID,
		EdcPushID:         edcPush.ID,
		ProjectSiteID:     edcPush.ProjectSiteID,
		OID:               edcPush.OID,
		Content:           edcPush.Content,
		PushMode:          edcPush.PushMode,
		Source:            edcPush.Source,
		SourceType:        edcPush.SourceType,
		SerialNumber:      edcPush.SerialNumber,
		Status:            edcPushLogStatus,
		Retry:             edcPush.Retry,
		SendTime:          edcPush.SendTime,
		CreateTime:        edcPush.CreateTime,
		Result:            edcPush.Result,
		HttpRspBody:       edcPush.HttpRspBody,
		EecErrorCode:      edcPush.EecErrorCode,
		ProcessingResults: edcPush.ProcessingResults,
	}

	// 查询中心
	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": edcPush.ProjectSiteID}).Decode(&projectSite)
	if err != nil {
		return err
	}
	if len(projectSite.Tz) > 0 {
		edcPush.Location = projectSite.Tz
	}
	_, err = tools.Database.Collection("edc_push_log").InsertOne(sctx, edcPushLog)
	if err != nil {
		return err
	}
	return nil
}

// 备注处理
func MakeRemarks(sourceType int, subject models.Subject, now time.Duration) (string, error) {
	var remarks strings.Builder
	remarks.WriteString("Operation: ")
	// sourceType 来源具体细分 0.未知 1.登记 2.修改 3.随机 4.发药 5.访视外发药 6.药物替换 7.药物补发 8.药物撤销 9.药物取回 10.实际用药 11.受试者替换 12.编辑实际分层 13.筛选
	if sourceType == 1 {
		remarks.WriteString("Register,")
	} else if sourceType == 2 {
		remarks.WriteString("Edit,")
	} else if sourceType == 3 {
		remarks.WriteString("Randomize,")
	} else if sourceType == 4 {
		remarks.WriteString("Dispense,")
	} else if sourceType == 5 {
		remarks.WriteString("Unscheduled Dispensing,")
	} else if sourceType == 6 {
		remarks.WriteString("Replace IP,")
	} else if sourceType == 7 {
		remarks.WriteString("Re-dispense,")
	} else if sourceType == 8 {
		remarks.WriteString("Withdraw IP,")
	} else if sourceType == 9 {
		remarks.WriteString("Retrieve IP,")
	} else if sourceType == 10 {
		remarks.WriteString("Actually Used IP,")
	} else if sourceType == 11 {
		remarks.WriteString("Subject Replacement,")
	} else if sourceType == 12 {
		remarks.WriteString("Subject Actual Stratification,")
	} else if sourceType == 13 {
		remarks.WriteString("Screening,")
	} else if sourceType == 14 {
		remarks.WriteString("Switch Cohort,")
	} else {
		remarks.WriteString("Unknown,")
	}
	remarks.WriteString("Time: ")
	timeStr := ""
	timeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
	if err != nil {
		return "", err
	}
	if timeZone == "" {
		zone, err := tools.GetTimeZone(subject.ProjectID)
		if err != nil {
			return "", err
		}
		timeZone = tools.FormatOffsetToZoneStringUtc(zone)
		//timeZone = fmt.Sprintf("UTC%+d", strTimeZone)
	}
	if now != 0 {
		intTimeZone, err := tools.ParseTimezoneOffset(timeZone)
		//intTimeZone, err := strconv.Atoi(strings.Replace(timeZone, "UTC", "", 1))
		if err != nil {
			return "", err
		}
		hours := time.Duration(intTimeZone)
		minutes := time.Duration((intTimeZone - float64(hours)) * 60)
		duration := hours*time.Hour + minutes*time.Minute
		timeStr = time.Unix(now.Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
	}
	remarks.WriteString(timeStr + "(" + timeZone + ").")

	return remarks.String(), nil
}

// 公共异步推送接口
func AsyncSubjectRandomPush(
	logData map[string]interface{},
	subjectId primitive.ObjectID,
	sourceType int,
	now time.Duration,
	replaceSubjectId string,
	replaceSubjectNo string,
	oldCohort string,
) {
	pushTask := &edc_push_task.RandomPushTask{
		BaseTask: edc_push_task.BaseTask{
			LogData: logData,
		},
		SubjectID:        subjectId,
		SourceType:       sourceType,
		NowDuration:      now,
		ReplaceSubjectId: replaceSubjectId,
		ReplaceSubjectNo: replaceSubjectNo,
		OldCohort:        oldCohort,
	}
	edc_push_task.SubmitTask(pushTask)
}

func AsyncSubjectDispensingPush(
	logData map[string]interface{},
	dispensingId primitive.ObjectID,
	sourceType int,
	now time.Duration,
) {
	pushTask := &edc_push_task.DispensingPushTask{
		BaseTask: edc_push_task.BaseTask{
			LogData: logData,
		},
		DispensingID: dispensingId,
		SourceType:   sourceType,
		NowDuration:  now,
	}
	edc_push_task.SubmitTask(pushTask)
}

// 新增带会话上下文的同步推送方法（专用于事务内调用）
func SyncSubjectRandomPush(
	sctx mongo.SessionContext, // 新增会话上下文参数
	subjectId primitive.ObjectID,
	sourceType int,
	now time.Duration,
	replaceSubjectId string,
	replaceSubjectNo string,
	oldCohort string,
) error {
	executor := &EDCExecutor{}

	// 将会话上下文传递给执行器
	return executor.SubjectRandomPushWithSession(
		sctx, // 传入事务会话上下文
		subjectId,
		sourceType,
		now,
		replaceSubjectId,
		replaceSubjectNo,
		oldCohort,
	)
}
