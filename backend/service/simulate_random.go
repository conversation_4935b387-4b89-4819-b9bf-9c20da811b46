package service

import (
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"encoding/base64"
	"fmt"
	"github.com/duke-git/lancet/v2/maputil"
	"github.com/duke-git/lancet/v2/mathutil"
	"math"
	"math/rand"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/slice"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	//"math/rand"

	"sort"
	"strconv"
)

type SimulateRandomService struct{}

func (s *SimulateRandomService) AddSimulateRandom(ctx *gin.Context, info models.SimulateRandomInfo) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		customerID, _ := primitive.ObjectIDFromHex(ctx.Query("customerId"))
		projectID, _ := primitive.ObjectIDFromHex(ctx.Query("projectId"))
		envID, _ := primitive.ObjectIDFromHex(ctx.Query("envId"))
		chtId, _ := ctx.GetQuery("cohortId")
		cohortID, _ := primitive.ObjectIDFromHex(chtId)
		historyOID := envID
		if !cohortID.IsZero() {
			historyOID = cohortID
		}
		OID := primitive.NewObjectID()
		if info.CountryQuantity > info.SiteQuantity || info.RegionQuantity > info.SiteQuantity {
			return nil, tools.BuildServerError(ctx, "simulate_random_site_cont_error")
		}
		simulateRandom := models.SimulateRandom{
			ID:                 OID,
			CustomerID:         customerID,
			ProjectID:          projectID,
			EnvironmentID:      envID,
			CohortID:           cohortID,
			SimulateRandomInfo: info,
		}
		if _, err := tools.Database.Collection("simulate_random").InsertOne(sctx, simulateRandom); err != nil {
			return nil, errors.WithStack(err)
		}
		var randomDesign models.RandomDesign
		filter := bson.M{"env_id": envID}
		if !cohortID.IsZero() {
			filter["cohort_id"] = cohortID
		}
		err := tools.Database.Collection("random_design").FindOne(sctx, filter).Decode(&randomDesign)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 查询randomList
		var randomListName strings.Builder
		if info.RandomListIds != nil && len(info.RandomListIds) > 0 {
			var randomIds []primitive.ObjectID
			for _, randomId := range info.RandomListIds {
				randomOID, _ := primitive.ObjectIDFromHex(randomId)
				randomIds = append(randomIds, randomOID)
			}
			random_list_filter := bson.M{"_id": bson.M{"$in": randomIds}}
			var randomLists []models.RandomList
			randomListCursor, err := tools.Database.Collection("random_list").Find(nil, random_list_filter)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = randomListCursor.All(nil, &randomLists)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			for _, rl := range randomLists {
				randomListName.WriteString(" [")
				randomListName.WriteString(rl.Name)
				randomListName.WriteString("]")
			}
		}
		// 项目日志
		err = insertSimulateRandomFormLog(ctx, sctx, historyOID, 1, models.SimulateRandomInfo{}, info, randomDesign, OID, "", randomListName.String(), "")
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}

	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *SimulateRandomService) UpdateSimulateRandom(ctx *gin.Context, id string, info models.SimulateRandomInfo) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		oId, err := primitive.ObjectIDFromHex(id)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		filter := bson.M{"_id": oId}
		if info.CountryQuantity > info.SiteQuantity || info.RegionQuantity > info.SiteQuantity {
			return nil, tools.BuildServerError(ctx, "simulate_random_site_cont_error")
		}
		//根据id查询模拟随机数据
		var simulateRandom models.SimulateRandom
		err = tools.Database.Collection("simulate_random").FindOne(sctx, filter).Decode(&simulateRandom)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		historyOID := simulateRandom.EnvironmentID
		if !simulateRandom.CohortID.IsZero() {
			historyOID = simulateRandom.CohortID
		}

		chtId, _ := ctx.GetQuery("cohortId")
		cohortID, _ := primitive.ObjectIDFromHex(chtId)
		ub := bson.M{"$set": bson.M{"simulate_random_info": info, "cohort_id": cohortID}}
		if _, err := tools.Database.Collection("simulate_random").UpdateOne(sctx, filter, ub); err != nil {
			return nil, errors.WithStack(err)
		}
		deleteFilter := bson.M{"simulate_random_id": oId}
		if _, err := tools.Database.Collection("simulate_subject").DeleteMany(sctx, deleteFilter); err != nil {
			return nil, errors.WithStack(err)
		}
		var randomDesign models.RandomDesign
		designFilter := bson.M{"env_id": simulateRandom.EnvironmentID}
		if !cohortID.IsZero() {
			designFilter["cohort_id"] = cohortID
		}
		err = tools.Database.Collection("random_design").FindOne(sctx, designFilter).Decode(&randomDesign)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 查询randomList(旧的)
		var oldRandomListName strings.Builder
		if simulateRandom.SimulateRandomInfo.RandomListIds != nil && len(simulateRandom.SimulateRandomInfo.RandomListIds) > 0 {
			var randomIds []primitive.ObjectID
			for _, randomId := range simulateRandom.SimulateRandomInfo.RandomListIds {
				randomOID, _ := primitive.ObjectIDFromHex(randomId)
				randomIds = append(randomIds, randomOID)
			}

			random_list_filter := bson.M{"_id": bson.M{"$in": randomIds}}
			var randomLists []models.RandomList
			randomListCursor, err := tools.Database.Collection("random_list").Find(nil, random_list_filter)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = randomListCursor.All(nil, &randomLists)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			for _, rl := range randomLists {
				oldRandomListName.WriteString(" [")
				oldRandomListName.WriteString(rl.Name)
				oldRandomListName.WriteString("]")
			}
		}

		// 查询randomList(新的)
		var newRandomListName strings.Builder
		if info.RandomListIds != nil && len(info.RandomListIds) > 0 {
			var randomIds []primitive.ObjectID
			for _, randomId := range info.RandomListIds {
				randomOID, _ := primitive.ObjectIDFromHex(randomId)
				randomIds = append(randomIds, randomOID)
			}

			random_list_filter := bson.M{"_id": bson.M{"$in": randomIds}}
			var randomLists []models.RandomList
			randomListCursor, err := tools.Database.Collection("random_list").Find(nil, random_list_filter)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = randomListCursor.All(nil, &randomLists)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			for _, rl := range randomLists {
				newRandomListName.WriteString(" [")
				newRandomListName.WriteString(rl.Name)
				newRandomListName.WriteString("]")
			}
		}

		// 项目日志
		err = insertSimulateRandomFormLog(ctx, sctx, historyOID, 2, simulateRandom.SimulateRandomInfo, info, randomDesign, oId, oldRandomListName.String(), newRandomListName.String(), "")
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *SimulateRandomService) RunSimulateRandom(ctx *gin.Context, id string) error {
	//callback := func(sctx mongo.SessionContext) (interface{}, error) {
	simulateRandomId, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		//return nil, errors.WithStack(err)
		return errors.WithStack(err)
	}
	_, err = tools.Database.Collection("simulate_subject").DeleteMany(nil, bson.M{"simulate_random_id": simulateRandomId})
	if err != nil {
		//return nil, errors.WithStack(err)
		return errors.WithStack(err)
	}

	simulateRandomFilter := bson.M{"_id": simulateRandomId}
	var simulateRandom models.SimulateRandom
	err = tools.Database.Collection("simulate_random").FindOne(nil, simulateRandomFilter).Decode(&simulateRandom)
	if err != nil {
		//return nil, errors.WithStack(err)
		return errors.WithStack(err)
	}

	// project
	projectFilter := bson.M{"_id": simulateRandom.ProjectID}
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, projectFilter).Decode(&project)
	if err != nil {
		//return nil, errors.WithStack(err)
		return errors.WithStack(err)
	}
	// cohort项目或者在随机项目
	var cohort models.Cohort
	var env models.Environment
	for _, e := range project.Environments {
		if e.ID == simulateRandom.EnvironmentID {
			env = e
		}
	}
	if project.ProjectInfo.Type == 2 || project.ProjectInfo.Type == 3 {
		// 找到当前cohort对应的入组上限
		for _, ch := range env.Cohorts {
			if ch.ID == simulateRandom.CohortID {
				cohort = ch
			}
		}
		if cohort.Status == 1 || cohort.Status == 3 || cohort.Status == 4 || cohort.Status == 5 {
			return tools.BuildServerError(ctx, "subject_factor_check_error")
		}
	} else {
		if *env.Status == 1 || *env.Status == 3 || *env.Status == 4 || *env.Status == 5 {
			return tools.BuildServerError(ctx, "subject_factor_check_error")
		}
	}
	// Attribute
	filter := bson.M{"customer_id": simulateRandom.CustomerID, "project_id": simulateRandom.ProjectID, "env_id": simulateRandom.EnvironmentID}
	if simulateRandom.CohortID != primitive.NilObjectID {
		filter = bson.M{"customer_id": simulateRandom.CustomerID, "project_id": simulateRandom.ProjectID, "env_id": simulateRandom.EnvironmentID, "cohort_id": simulateRandom.CohortID}
	}
	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute)
	if err != nil {
		//return nil, errors.WithStack(err)
		return errors.WithStack(err)
	}
	var randomDesign models.RandomDesign
	err = tools.Database.Collection("random_design").FindOne(nil, filter).Decode(&randomDesign)
	if err != nil {
		//return nil, errors.WithStack(err)
		return errors.WithStack(err)
	}
	// RandomList
	historyOID := simulateRandom.EnvironmentID
	if simulateRandom.CohortID != primitive.NilObjectID {
		historyOID = simulateRandom.CohortID
	}

	// 中心绑定随机表需求，需要调整查询条件
	var randomIds []primitive.ObjectID
	for _, randomListId := range simulateRandom.SimulateRandomInfo.RandomListIds {
		rid, _ := primitive.ObjectIDFromHex(randomListId)
		randomIds = append(randomIds, rid)
	}
	var randomLists []models.RandomList
	if randomIds != nil && len(randomIds) > 0 {
		randomFilter := bson.M{"_id": bson.M{"$in": randomIds}}
		cursor, err := tools.Database.Collection("random_list").Find(nil, randomFilter)
		if err != nil {
			//return nil, errors.WithStack(err)
			return errors.WithStack(err)
		}
		err = cursor.All(nil, &randomLists)
		if err != nil {
			//return nil, errors.WithStack(err)
			return errors.WithStack(err)
		}
	}

	if len(randomLists) == 0 {
		//return nil, tools.BuildServerError(ctx, "subject_no_random")
		return tools.BuildServerError(ctx, "subject_no_random")
	}
	//随机表校验组别
	randomDesign.Info.Groups = slice.Filter(randomDesign.Info.Groups, func(index int, item models.RandomGroup) bool {
		return item.Status == nil || *item.Status == 1
	})
	groups := randomDesign.Info.Groups
	groupLen := 0
	for _, group := range groups {
		if group.SubGroup != nil && len(group.SubGroup) > 0 {
			groupLen = groupLen + len(group.SubGroup)
		} else {
			groupLen++
		}
	}
	for _, list := range randomLists {
		list.Design.Groups = slice.Filter(list.Design.Groups, func(index int, item models.RandomListGroup) bool {
			return item.Status == nil || *item.Status == 1
		})
		if list.Design.Type != randomDesign.Info.Type {
			return tools.BuildServerError(ctx, "random_type_error")
		}
		if len(list.Design.Groups) != groupLen {
			return tools.BuildServerError(ctx, "random_group_error")
		}
		for _, group := range groups {
			if group.SubGroup != nil && len(group.SubGroup) > 0 {
				for _, subGroup := range group.SubGroup {
					_, b := slice.Find(list.Design.Groups, func(index int, item models.RandomListGroup) bool {
						return item.ParName == group.Name && item.SubName == subGroup.Name
					})
					if !b {
						return tools.BuildServerError(ctx, "random_group_error")
					}
				}
			} else {
				_, b := slice.Find(list.Design.Groups, func(index int, item models.RandomListGroup) bool {
					return item.Name == group.Name
				})
				if !b {
					return tools.BuildServerError(ctx, "random_group_error")
				}
			}
		}

	}
	//校验各随机表的分层因素
	factors := randomDesign.Info.Factors
	factors = slice.Filter(factors, func(index int, item models.RandomFactor) bool {
		if item.Status == nil {
			return true
		}
		return *item.Status != 2
	})
	{
		if len(randomLists) > 1 {
			for _, list := range randomLists {
				listFactors := list.Design.Factors
				listFactors = slice.Filter(listFactors, func(index int, item models.RandomFactor) bool {
					if item.Status == nil {
						return true
					}
					return *item.Status != 2
				})
				if len(listFactors) != len(factors) {
					return tools.BuildServerError(ctx, "simulate_random_factor_error")
				}
				for i := 0; i < len(factors); i++ {
					if factors[i].Name != listFactors[i].Name || factors[i].Label != listFactors[i].Label {
						return tools.BuildServerError(ctx, "simulate_random_factor_error")
					}
					if len(factors[i].Options) != len(listFactors[i].Options) {
						return tools.BuildServerError(ctx, "simulate_random_factor_error")
					}
					for j := 0; j < len(factors[i].Options); j++ {
						if factors[i].Options[j].Label != listFactors[i].Options[j].Label ||
							factors[i].Options[j].Value != listFactors[i].Options[j].Value {
							return tools.BuildServerError(ctx, "simulate_random_factor_error")
						}
					}
				}
			}
		}
	}
	//校验比例
	factorRatio := simulateRandom.SimulateRandomInfo.FactorRatio
	if factorRatio != nil && len(factorRatio) > 0 {
		// 构建快速查找map（二维数组转嵌套map）
		ratioMap := make(map[string]map[string]struct{})

		for _, ratioGroup := range factorRatio {
			totalRatio := 0
			nilCount := slice.Count(ratioGroup, func(index int, item models.FactorRatio) bool {
				return item.Ratio == nil
			})
			notNilCount := slice.Count(ratioGroup, func(index int, item models.FactorRatio) bool {
				return item.Ratio != nil
			})
			if nilCount != len(ratioGroup) && notNilCount != len(ratioGroup) {
				return tools.BuildServerError(ctx, "simulate_random_factor_ratio_lack_error")
			}
			for _, ratio := range ratioGroup {
				if _, ok := ratioMap[ratio.FactorName]; !ok {
					ratioMap[ratio.FactorName] = make(map[string]struct{})
				}
				ratioMap[ratio.FactorName][ratio.OptionValue] = struct{}{}
				if ratio.Ratio != nil {
					totalRatio += *ratio.Ratio
				}
			}
			if ratioGroup[0].Ratio != nil && totalRatio != simulateRandom.SimulateRandomInfo.SubjectQuantity {
				return tools.BuildServerError(ctx, "simulate_random_factor_ratio_total_error")
			}
		}

		// 校验每个factor的option都存在
		for _, factor := range factors {
			factorOptions := ratioMap[factor.Name]
			for _, option := range factor.Options {
				if _, exists := factorOptions[option.Value]; !exists {
					return tools.BuildServerError(ctx, "simulate_random_factor_ratio_error")
				}
			}
		}
		listFactors := randomLists[0].Design.Factors
		listFactors = slice.Filter(listFactors, func(index int, item models.RandomFactor) bool {
			if item.Status == nil {
				return true
			}
			return *item.Status != 2
		})
		if len(listFactors) != len(factors) {
			return tools.BuildServerError(ctx, "simulate_random_factor_ratio_error")
		}
		for i := 0; i < len(factors); i++ {
			if factors[i].Name != listFactors[i].Name || factors[i].Label != listFactors[i].Label {
				return tools.BuildServerError(ctx, "simulate_random_factor_ratio_error")
			}
			if len(factors[i].Options) != len(listFactors[i].Options) {
				return tools.BuildServerError(ctx, "simulate_random_factor_ratio_error")
			}
			for j := 0; j < len(factors[i].Options); j++ {
				if factors[i].Options[j].Label != listFactors[i].Options[j].Label ||
					factors[i].Options[j].Value != listFactors[i].Options[j].Value {
					return tools.BuildServerError(ctx, "simulate_random_factor_ratio_error")
				}
			}
		}
		for _, factor := range listFactors {
			factorOptions := ratioMap[factor.Name]
			for _, option := range factor.Options {
				if _, exists := factorOptions[option.Value]; !exists {
					return tools.BuildServerError(ctx, "simulate_random_factor_ratio_error")
				}
			}
		}

	}

	//不够满足的随机号就退出
	// RandomNumber
	var numbers []models.RandomNumber
	// 排序
	opts := &options.FindOptions{
		Sort: bson.D{{"_id", 1}},
	}
	// 条件
	randomListIds := slice.Map(randomLists, func(index int, item models.RandomList) primitive.ObjectID {
		return item.ID
	})
	match := bson.M{
		"random_list_id":  bson.M{"$in": randomListIds},
		"project_site_id": primitive.NilObjectID,
		"status":          1,
	}
	cursor, err := tools.Database.Collection("random_number").Find(nil, match, opts)
	if err != nil {
		return errors.WithStack(err)
	}
	if err := cursor.All(nil, &numbers); err != nil {
		return errors.WithStack(err)
	}
	if len(numbers) < simulateRandom.SimulateRandomInfo.SubjectQuantity {
		return tools.BuildServerError(ctx, "simulated.random.number.not.enough")
	}
	//构造模拟中心
	siteSize := simulateRandom.SimulateRandomInfo.SiteQuantity
	var sites []string
	for i := 0; i < siteSize; i++ {
		sites = append(sites, fmt.Sprintf("site_%d", i+1))
	}
	//构造模拟国家
	countrySize := simulateRandom.SimulateRandomInfo.CountryQuantity
	var countries []string
	for i := 0; i < countrySize; i++ {
		countries = append(countries, fmt.Sprintf("country_%d", i+1))
	}
	//构造模拟区域
	regionSize := simulateRandom.SimulateRandomInfo.RegionQuantity
	var regions []string
	for i := 0; i < regionSize; i++ {
		regions = append(regions, fmt.Sprintf("region_%d", i+1))
	}

	simulateSites := make([]models.SimulateSite, 0)
	shuffleSites := slice.Shuffle(sites)
	shuffleCountries := slice.Shuffle(countries)
	shuffleRegions := slice.Shuffle(regions)
	rand.New(rand.NewSource(time.Now().UnixNano()))
	for _, site := range shuffleSites {
		country := ""
		if countrySize > 0 {
			countryIndex := rand.Intn(len(shuffleCountries))
			country = shuffleCountries[countryIndex]
		}

		region := ""
		if regionSize > 0 {
			regionIndex := rand.Intn(len(shuffleRegions))
			region = shuffleRegions[regionIndex]
		}
		simulateSites = append(simulateSites, models.SimulateSite{
			SiteName:    site,
			CountryName: country,
			RegionName:  region,
		})
	}

	//运行次数
	allSubject := make([]interface{}, 0)
	for i := 0; i < simulateRandom.SimulateRandomInfo.RunQuantity; i++ {
		var randomNumbers []models.RandomNumber
		for o := 0; o < len(numbers); o++ {
			randomNumbers = append(randomNumbers, numbers[o])
		}
		// 预计算每个factor的分配顺序
		factorAllocations := make(map[string][]int)
		for _, factor := range factors {
			if factorRatio != nil && len(factorRatio) > 0 {
				ratios, found := slice.Find(factorRatio, func(index int, item []models.FactorRatio) bool {
					return item[0].FactorName == factor.Name
				})

				if found && (*ratios)[0].Ratio != nil {
					// 创建按比例分配的索引列表
					var allocations []int
					for i, r := range *ratios {
						count := *r.Ratio
						for j := 0; j < count; j++ {
							allocations = append(allocations, i)
						}
					}
					// 打乱顺序
					rand.Shuffle(len(allocations), func(i, j int) {
						allocations[i], allocations[j] = allocations[j], allocations[i]
					})
					factorAllocations[factor.Name] = allocations
				}
			}
		}
		//构建模拟的受试者
		var subjects []models.SimulateSubject
		for j := 0; j < simulateRandom.SimulateRandomInfo.SubjectQuantity; j++ {
			var infos []models.Info
			infos = append(infos, models.Info{
				Name:  "shortname",
				Value: fmt.Sprintf("sbj_%d", j+1),
			})
			for _, factor := range factors {
				var selectedOption models.Option
				// 检查是否需要使用1:1随机选择
				if factorRatio == nil || len(factorRatio) == 0 {
					selectedOption = factor.Options[rand.Intn(len(factor.Options))]
				} else {
					ratios, found := slice.Find(factorRatio, func(index int, item []models.FactorRatio) bool {
						return item[0].FactorName == factor.Name
					})

					if found && (*ratios)[0].Ratio == nil {
						selectedOption = factor.Options[rand.Intn(len(factor.Options))]
					} else {
						allocations, _ := factorAllocations[factor.Name]
						selectedOption = factor.Options[allocations[j]]
					}
				}

				infos = append(infos, models.Info{
					Name:  factor.Name,
					Value: selectedOption.Value,
				})
			}
			siteName := sites[rand.Intn(len(sites))]
			find, _ := slice.Find(simulateSites, func(index int, item models.SimulateSite) bool {
				return item.SiteName == siteName
			})
			countryName := find.CountryName
			regionName := find.RegionName
			subjects = append(subjects, models.SimulateSubject{
				ID:               primitive.NewObjectID(),
				Name:             fmt.Sprintf("sbj_%d", j+1),
				SimulateRandomID: simulateRandom.ID,
				SiteName:         siteName,
				CountryName:      countryName,
				RegionName:       regionName,
				Info:             infos,
				RunCount:         i + 1,
			})
		}
		randomNumberCount := len(randomNumbers)
		if attribute.AttributeInfo.IsRandom || attribute.AttributeInfo.IsCountryRandom || attribute.AttributeInfo.IsRegionRandom {
			//不分层或中心分层
			if !attribute.AttributeInfo.CountryLayered && !attribute.AttributeInfo.RegionLayered {
				sort.Sort(models.SortBySimulateCountry(randomNumbers))
			} else if attribute.AttributeInfo.CountryLayered {
				sort.Sort(models.SortBySimulateCountry(randomNumbers))
			} else if attribute.AttributeInfo.RegionLayered {
				sort.Sort(models.SortBySimulateRegion(randomNumbers))
			}
		} else {
			sort.Sort(models.SortBySimulateSite(randomNumbers))
		}

		subjects = random(subjects)
		for k := 0; k < len(subjects); k++ {
			var result models.RandomNumberReturn
			for _, randomList := range randomLists {
				result = extractSimulateRandomNumber(ctx, subjects[k], subjects, attribute, randomList, randomNumbers, randomNumberCount)
				if result.RandomNumber == "" {
					continue
				} else {
					// 1. 修改登记表的受试者状态为已随机
					subjects[k].RandomNumber = result.RandomNumber
					subjects[k].GroupName = result.Group
					subjects[k].ParName = result.ParName
					subjects[k].SubName = result.SubName
					subjects[k].Done = true
					subjects[k].RandomListID = result.RandomListID
					subjects[k].PlanNumber = result.RandomNumberModel.PlanNumber
					subjects[k].GroupValue = result.RandomNumberModel.GroupValue
					allSubject = append(allSubject, subjects[k])

					// 2.修改随机号状态等
					if randomList.Design.Type == 1 {
						// 剔除已命中的随机号
						for l := 0; l < len(randomNumbers); l++ {
							if randomNumbers[l].ID == result.ID {
								randomNumbers = append(randomNumbers[:l], randomNumbers[l+1:]...)
								break
							}
						}

						var factors []models.Factors
						if result.LayeredSign { // 为true表示要写入分层因素
							for _, factor := range randomList.Design.Factors {
								value := ""
								text := ""
								for _, info := range subjects[k].Info {
									if factor.Name == info.Name {
										value = info.Value.(string)
										for _, option := range factor.Options {
											if option.Value == value {
												text = option.Label
											}
										}
									}
								}
								factors = append(factors, models.Factors{
									Name:  factor.Name,
									Label: factor.Label,
									Value: value,
									Text:  text,
								})
							}
						}

						for m, _ := range randomNumbers {
							// 判断是否需要占区组
							if !attribute.AttributeInfo.IsRandom &&
								attribute.AttributeInfo.InstituteLayered &&
								randomNumbers[m].RandomListID == result.RandomListID &&
								randomNumbers[m].Block == result.BlockNumber &&
								randomNumbers[m].SimulateSite == "" {
								randomNumbers[m].SimulateSite = subjects[k].SiteName
								randomNumbers[m].SimulateCountry = subjects[k].CountryName
								randomNumbers[m].SimulateRegion = subjects[k].RegionName
							}
							if !attribute.AttributeInfo.IsRandom &&
								attribute.AttributeInfo.RegionLayered &&
								randomNumbers[m].RandomListID == result.RandomListID &&
								randomNumbers[m].Block == result.BlockNumber &&
								randomNumbers[m].SimulateSite == "" {
								randomNumbers[m].SimulateRegion = subjects[k].RegionName
							}
							if !attribute.AttributeInfo.IsRandom &&
								attribute.AttributeInfo.CountryLayered &&
								randomNumbers[m].RandomListID == result.RandomListID &&
								randomNumbers[m].Block == result.BlockNumber &&
								randomNumbers[m].SimulateSite == "" {
								randomNumbers[m].SimulateCountry = subjects[k].CountryName
							}
							if result.LayeredSign &&
								randomNumbers[m].RandomListID == randomList.ID &&
								randomNumbers[m].Block == result.BlockNumber { // 为true表示要写入分层因素
								randomNumbers[m].Factors = factors
							}
						}
					} else {
						//最小化随机
						// 剔除已命中的随机号
						for l := 0; l < len(randomNumbers); l++ {
							if randomNumbers[l].ID == result.ID {
								randomNumbers = append(randomNumbers[:l], randomNumbers[l+1:]...)
								break
							}
						}
					}
					break
				}

			}
			if result.RandomNumber == "" {
				return tools.BuildServerError(ctx, "subject_no_random")
			}
		}
	}
	_, err = tools.Database.Collection("simulate_subject").InsertMany(nil, allSubject)
	if err != nil {
		return errors.WithStack(err)
	}
	//项目日志
	marks := []models.Mark{}
	marks = append(marks, models.Mark{
		Label: "operation_log.label.simulate_random_name",
		Value: simulateRandom.SimulateRandomInfo.Name,
		Blind: false,
	})
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.random_design",
		TranKey: "operation_log.simulateRandom.run",
		Old: models.OperationLogField{
			Type:  1,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  1,
			Value: "1",
		},
	})
	err = tools.SaveOperation(ctx, nil, "operation_log.module.simulate_random", historyOID, 4, OperationLogFieldGroups, marks, simulateRandom.ID)
	if err != nil {
		return errors.WithStack(err)
	}
	//return nil, nil
	//}

	//err := tools.Transaction(callback)
	//if err != nil {
	//	return err
	//}
	return nil
}

// 生成一个按给定例数分配并随机打乱的选项列表
func generateShuffledOptionsByCounts(options []models.Option, ratios []int) []models.Option {
	if len(options) != len(ratios) {
		// 如果选项和数量不匹配，回退到纯随机
		return options
	}

	// 生成选项列表（如 [A, A, ..., B, B, ...]）
	shuffled := make([]models.Option, 0)
	for i, opt := range options {
		for j := 0; j < ratios[i]; j++ {
			shuffled = append(shuffled, opt)
		}
	}

	// Fisher-Yates 洗牌算法
	rand.Shuffle(len(shuffled), func(i, j int) {
		shuffled[i], shuffled[j] = shuffled[j], shuffled[i]
	})

	return shuffled
}

func random(obj []models.SimulateSubject) []models.SimulateSubject { //打乱顺序

	for i := len(obj) - 1; i > 0; i-- {
		num := rand.Intn(i + 1)
		obj[i], obj[num] = obj[num], obj[i]
	}

	newObj := make([]models.SimulateSubject, 0)
	for i := 0; i < len(obj); i++ {
		newObj = append(newObj, obj[i])
	}
	return newObj
}

// 提取随机号
func extractSimulateRandomNumber(ctx *gin.Context, subject models.SimulateSubject, subjects []models.SimulateSubject, attribute models.Attribute,
	randomList models.RandomList, randomNumbers []models.RandomNumber, randomNumberCount int) models.RandomNumberReturn {
	numbers := make([]models.RandomNumber, 0)
	var randomNumberReturn models.RandomNumberReturn // 返回的数据
	switch randomList.Design.Type {
	// 区组或者分层区组随机
	case 1:
		{
			// 中心/国家/区域没有随机号不可以随机入组
			if attribute.AttributeInfo.IsRandom || attribute.AttributeInfo.IsCountryRandom || attribute.AttributeInfo.IsRegionRandom {
				numbers = simulateFindRandomNumbers(attribute, subject, randomNumbers, randomList)
				if numbers != nil && len(numbers) > 0 { // 根据中心ID查到了随机号
					randomNumberReturn = simulateRandomNumberScreening(subject, randomList, numbers)
				}
			} else {
				numbers = simulateFindRandomNumberNoSite(subject, randomList, randomNumbers)
				if numbers != nil && len(numbers) > 0 { // 根据中心ID查到了随机号
					randomNumberReturn = simulateRandomNumberScreening(subject, randomList, numbers)
				}
			}
		}
		// 最小化随机
	case 2:
		{
			// 查询随机号
			numbers = simulateFindRandomNumber("", randomList, randomNumbers)
			var subjectList []models.SimulateSubject
			for _, s := range subjects {
				if s.Done {
					subjectList = append(subjectList, s)
				}
			}
			randomNumberReturn = simulateObtainNumber(ctx, attribute, randomList, numbers, subject, subjectList, randomNumberCount)
		}
	}

	return randomNumberReturn
}

func simulateFindRandomNumbers(attribute models.Attribute, subject models.SimulateSubject, numbers []models.RandomNumber, randomList models.RandomList) []models.RandomNumber {
	// RandomNumber
	randomNumbers := numbers
	if attribute.AttributeInfo.IsRandom { //中心入组限制
		randomNumbers = slice.Filter(numbers, func(index int, item models.RandomNumber) bool {
			return item.SimulateSite == subject.SiteName && (item.SimulateCountry == "" || item.SimulateCountry == subject.CountryName) && (item.SimulateRegion == "" || item.SimulateRegion == subject.RegionName)
		})
	} else if attribute.AttributeInfo.IsCountryRandom {
		randomNumbers = slice.Filter(numbers, func(index int, item models.RandomNumber) bool {
			return (item.SimulateSite == subject.SiteName || item.SimulateSite == "") && (item.SimulateCountry == subject.CountryName) && (item.SimulateRegion == "" || item.SimulateRegion == subject.RegionName)
		})
	} else if attribute.AttributeInfo.IsRegionRandom {
		randomNumbers = slice.Filter(numbers, func(index int, item models.RandomNumber) bool {
			return (item.SimulateSite == subject.SiteName || item.SimulateSite == "") && (item.SimulateCountry == "" || item.SimulateCountry == subject.CountryName) && (item.SimulateRegion == subject.RegionName)
		})
	} else {
		return []models.RandomNumber{}
	}

	return randomNumbers
}
func simulateFindRandomNumber(subjectSite string, randomList models.RandomList, randomNumbers []models.RandomNumber) []models.RandomNumber {
	var hitNumbers []models.RandomNumber
	for _, number := range randomNumbers {
		if randomList.ID == number.RandomListID && subjectSite == number.SimulateSite {
			hitNumbers = append(hitNumbers, number)
		}

	}
	return hitNumbers
}

func simulateFindRandomNumberNoSite(subject models.SimulateSubject, randomList models.RandomList, randomNumbers []models.RandomNumber) []models.RandomNumber {
	hitNumbers := make([]models.RandomNumber, 0, len(randomNumbers))
	for _, number := range randomNumbers {
		if randomList.ID.Hex() == number.RandomListID.Hex() &&
			(number.SimulateSite == "" && number.SimulateCountry == "" && number.SimulateRegion == "") ||
			(number.SimulateSite == subject.SiteName && number.SimulateCountry == subject.CountryName && number.SimulateRegion == subject.RegionName) ||
			(number.SimulateSite == "" && number.SimulateCountry == "" && number.SimulateRegion == subject.RegionName) ||
			(number.SimulateSite == "" && number.SimulateCountry == subject.CountryName && number.SimulateRegion == "") {
			hitNumbers = append(hitNumbers, number)
		}
	}
	return hitNumbers
}

// 模拟随机号筛选
func simulateRandomNumberScreening(subject models.SimulateSubject, randomList models.RandomList, randomNumbers []models.RandomNumber) models.RandomNumberReturn {
	var randomNumberReturn models.RandomNumberReturn                             // 返回的数据
	if randomList.Design.Factors != nil || len(randomList.Design.Factors) != 0 { // 带分层因素
		info := make([]models.Info, 0)
		// 得到subject表单里填写的分层因素
		for _, designFactor := range randomList.Design.Factors {
			if designFactor.Status == nil || *designFactor.Status == 1 {
				for _, subjectInfo := range subject.Info {
					if designFactor.Name == subjectInfo.Name {
						info = append(info, subjectInfo)
						break
					}
				}
			}
		}
		factorCount := slice.Count(randomList.Design.Factors, func(index int, item models.RandomFactor) bool {
			return item.Status == nil || *item.Status == 1
		})
		// 筛选(外部引用)
		for _, randomNumber := range randomNumbers {
			if factorMatching(info, randomNumber.Factors, factorCount) {
				return models.RandomNumberReturn{
					ID:           randomNumber.ID,
					RandomListID: randomNumber.RandomListID,
					RandomNumber: randomNumber.Number,
					Group:        randomNumber.Group,
					ParName:      randomNumber.ParName,
					SubName:      randomNumber.SubName,
					BlockNumber:  randomNumber.Block,
					OccupySign:   false,
					LayeredSign:  false,
				}
			}
		}

		// 走到这里说明还是没有筛选到，此时应该排除分层因素继续筛选
		for _, randomNumber := range randomNumbers {
			if randomNumber.Factors == nil || len(randomNumber.Factors) == 0 {
				return models.RandomNumberReturn{
					ID:           randomNumber.ID,
					RandomListID: randomNumber.RandomListID,
					RandomNumber: randomNumber.Number,
					Group:        randomNumber.Group,
					ParName:      randomNumber.ParName,
					SubName:      randomNumber.SubName,
					BlockNumber:  randomNumber.Block,
					OccupySign:   false,
					LayeredSign:  true,
				}
			}
		}
	} else { // 不带分层因素
		for _, randomNumber := range randomNumbers {
			return models.RandomNumberReturn{
				ID:           randomNumber.ID,
				RandomListID: randomNumber.RandomListID,
				RandomNumber: randomNumber.Number,
				Group:        randomNumber.Group,
				ParName:      randomNumber.ParName,
				SubName:      randomNumber.SubName,
				BlockNumber:  randomNumber.Block,
				OccupySign:   false,
				LayeredSign:  false,
			}
		}
	}
	return randomNumberReturn
}

// 最小化随机提取随机号
func simulateObtainNumber(ctx *gin.Context, attribute models.Attribute, random models.RandomList, randomNumberList []models.RandomNumber, subject models.SimulateSubject, subjectList []models.SimulateSubject, randomNumberCount int) models.RandomNumberReturn {
	var randomNumberReturn models.RandomNumberReturn
	// 包含外部引用暂时注释
	for _, randomNumber := range randomNumberList {
		return simulateMinimizingRandom(ctx, attribute, random, randomNumber, subjectList, subject, randomNumberCount)
	}
	return randomNumberReturn
}

// 包含外部引用暂时注释
func simulateMinimizingRandom(ctx *gin.Context, attribute models.Attribute, random models.RandomList, randomNumber models.RandomNumber, subjectList []models.SimulateSubject, subject models.SimulateSubject, randomNumberCount int) models.RandomNumberReturn {

	// 判断每组已入组人数是否达到比例要求（假设A组的入组人数已经达到要求，就要把A组以及对应的比例剔除）
	nodesNumberList := obtainMinimizeNumber(random, randomNumberCount)
	var groups []models.Group
	var groupRatio []int

	for i, node := range nodesNumberList {
		count := 0
		for _, subject := range subjectList {
			if subject.GroupName == random.Design.Groups[i].Name {
				count++
			}
		}
		if count < node {
			groups = append(groups, models.Group{
				Group:   random.Design.Groups[i].Name,
				ParName: random.Design.Groups[i].ParName,
				SubName: random.Design.Groups[i].SubName,
			})
			groupRatio = append(groupRatio, random.Design.Groups[i].Ratio)
		}
	}

	// 强制随机
	if len(groups) == 1 {
		return simulateMinimizingGetRandomNumber(groups[0], random, randomNumber, subject, nil, groups)
	}

	// 判断是否已有病人入组
	if subjectList == nil || len(subjectList) == 0 { // 没有受试者入组
		return simulateDistributeGroup(random, randomNumber, subject, nil, groups)
	}
	// 有受试者入组
	randomNumberReturn, err := simulateDistributionGroups(ctx, attribute, random, randomNumber, subjectList, subject, groups, groupRatio)
	if err != nil {
		errors.WithStack(err)
	}
	return randomNumberReturn
}

/**
* 等概率分配组别信息
 */
func simulateDistributeGroup(random models.RandomList, randomNumber models.RandomNumber, subject models.SimulateSubject, gValueList []float64, groups []models.Group) models.RandomNumberReturn {
	var randomNumberReturn models.RandomNumberReturn

	// 包含外部引用暂时注释
	minimizeGroupCode := obtainGroupNode(groups, 1.0)
	for _, groupCode := range minimizeGroupCode {
		if randomNumber.PlanNumber <= groupCode.GroupNode {
			randomNumberReturn = simulateMinimizingGetRandomNumber(models.Group{
				Group:   groupCode.Group,
				ParName: groupCode.ParName,
				SubName: groupCode.SubName,
			}, random, randomNumber, subject, gValueList, groups)
		}
	}
	return randomNumberReturn
}

// 在Map中提取随机号(最小化随机)
func simulateMinimizingGetRandomNumber(group models.Group, random models.RandomList, randomNumber models.RandomNumber, subject models.SimulateSubject, gValueList []float64, groups []models.Group) models.RandomNumberReturn {
	randomNumber.Group = group.Group     // 返回组别
	randomNumber.ParName = group.ParName // 返回组别
	randomNumber.SubName = group.SubName // 返回组别
	if len(groups) == 1 {
		randomNumber.ActualNumber = 0.001
	} else if len(groups) > 1 {

		var groupValues []models.GroupValue
		for index, g := range groups {
			if gValueList == nil || len(gValueList) == 0 {
				groupValues = append(groupValues, models.GroupValue{
					Group:   g.Group,
					ParName: g.ParName,
					SubName: g.SubName,
					Value:   0.5,
				})
			} else {
				groupValues = append(groupValues, models.GroupValue{
					Group:   g.Group,
					ParName: g.ParName,
					SubName: g.SubName,
					Value:   gValueList[index],
				})
			}
		}
		randomNumber.ActualNumber = randomNumber.PlanNumber // 实际随机数
		randomNumber.GroupValue = groupValues               // 各组别G值
	}

	// 返回分层和分层因素
	var factors []models.Factors
	for _, factor := range random.Design.Factors {
		for _, option := range factor.Options {
			for _, sbjInfo := range subject.Info {
				if factor.Name == sbjInfo.Name && option.Value == sbjInfo.Value.(string) {
					factors = append(factors, models.Factors{
						Name:  factor.Name,
						Label: factor.Label,
						Value: option.Value,
						Text:  option.Label,
					})
				}
			}
		}
	}
	randomNumber.Factors = factors

	var randomNumberReturn models.RandomNumberReturn
	randomNumberReturn.ID = randomNumber.ID
	randomNumberReturn.RandomListID = randomNumber.RandomListID
	randomNumberReturn.RandomNumber = randomNumber.Number
	randomNumberReturn.Group = randomNumber.Group
	randomNumberReturn.ParName = randomNumber.ParName
	randomNumberReturn.SubName = randomNumber.SubName
	randomNumberReturn.BlockNumber = randomNumber.Block
	randomNumberReturn.OccupySign = false
	randomNumberReturn.RandomNumberModel = randomNumber
	return randomNumberReturn
}

/**
* 组别分配
 */
func simulateDistributionGroups(ctx *gin.Context, attribute models.Attribute, random models.RandomList, randomNumber models.RandomNumber, subjectList []models.SimulateSubject, subject models.SimulateSubject, groups []models.Group, groupRatio []int) (models.RandomNumberReturn, error) {

	acceptList := simulateCalculationGroupLayeredDeviation(attribute, random, subjectList, subject, groups, groupRatio)
	if acceptList == nil || len(acceptList) == 0 {
		return models.RandomNumberReturn{}, tools.BuildServerError(ctx, "minimize_layered_tips")
	}

	var min = acceptList[0] // 最大值
	var max = min           // 最小值

	for _, accept := range acceptList {
		if min > accept {
			min = accept
		}
		if max < accept {
			max = accept
		}
	}

	if max == min {
		return simulateDistributeGroup(random, randomNumber, subject, acceptList, groups), nil
	}

	randomNumberReturn, err := simulateObtainBiasGroupNode(ctx, random, acceptList, min, randomNumber, subject, groups)
	if err != nil {
		return models.RandomNumberReturn{}, err
	} else {
		return randomNumberReturn, nil
	}
}

/**
* 根据各组已入组人数计算组别分层偏差
 */
func simulateCalculationGroupLayeredDeviation(attribute models.Attribute, random models.RandomList, subjectList []models.SimulateSubject, subject models.SimulateSubject, groups []models.Group, groupRatio []int) []float64 {
	// 获取组别总比例数的和
	var groupsRatioSum = 0
	for _, ratio := range groupRatio {
		groupsRatioSum += ratio
	}
	// 返回list
	var returnList []float64
	for groupOuterLayerIndex := range groups {
		var layerList []float64
		// 循环分层因素
		for _, factor := range random.Design.Factors {
			// 存放各组已入组人数List
			var groupNumberList []int
			// 记录指定分层因素下各个组别已入组人数的总和
			sum := 0
			// 内层第二次循环组别
			for _, groupInnerLayer := range groups {
				// 获取当前组别和本层分层因因素下已入组人数
				count := 0
				for _, sbj := range subjectList {
					for _, info1 := range sbj.Info {
						if sbj.GroupName == groupInnerLayer.Group && factor.Name == info1.Name {
							for _, info2 := range subject.Info {
								if factor.Name == info2.Name && info1.Value == info2.Value {
									count++
								}
							}
						}
					}
				}
				// 获取指定分层因素下各个组别已入组人数的总和
				sum += count
				// 记录当前组已入组人数
				groupNumberList = append(groupNumberList, count)
			}

			// 计算当前层权重因子
			var n = 0.0
			for groupNumberIndex, groupNumber := range groupNumberList {
				// 计算第N层预期值
				var expectNumber = float64(sum+1) * float64(groupRatio[groupNumberIndex]) / float64(groupsRatioSum)

				if groupNumberIndex == groupOuterLayerIndex {
					n += math.Pow((float64(groupNumber)+1)-expectNumber, 2.0)
				} else {
					n += math.Pow(float64(groupNumber)-expectNumber, 2.0)
				}
			}

			// 获取当前层权重因子
			factor := n * 1 / float64(len(groups))
			layerList = append(layerList, factor)
		}
		if attribute.AttributeInfo.InstituteLayered || attribute.AttributeInfo.CountryLayered || attribute.AttributeInfo.RegionLayered {
			// 存放各组已入组人数List
			var groupNumberList []int
			// 记录指定分层因素下各个组别已入组人数的总和
			sum := 0
			// 内层第二次循环组别
			for _, groupInnerLayer := range groups {
				// 获取当前组别和本层分层因因素下已入组人数
				count := 0
				for _, sbj := range subjectList {
					if sbj.GroupName == groupInnerLayer.Group {
						if attribute.AttributeInfo.InstituteLayered && sbj.SiteName == subject.SiteName {
							count++
						} else if attribute.AttributeInfo.CountryLayered && sbj.CountryName == subject.CountryName {
							count++
						} else if attribute.AttributeInfo.RegionLayered && sbj.RegionName == subject.RegionName {
							count++
						}
					}
				}
				// 获取指定分层因素下各个组别已入组人数的总和
				sum += count
				// 记录当前组已入组人数
				groupNumberList = append(groupNumberList, count)
			}

			// 计算当前层权重因子
			var n = 0.0
			for groupNumberIndex, groupNumber := range groupNumberList {
				// 计算第N层预期值
				var expectNumber = float64(sum+1) * float64(groupRatio[groupNumberIndex]) / float64(groupsRatioSum)

				if groupNumberIndex == groupOuterLayerIndex {
					n += math.Pow((float64(groupNumber)+1)-expectNumber, 2.0)
				} else {
					n += math.Pow(float64(groupNumber)-expectNumber, 2.0)
				}
			}

			// 获取当前层权重因子
			factorV := n * 1 / float64(len(groups))
			layerList = append(layerList, factorV)
		}
		// 计算结果
		var result = 0.0
		for layerIndex, layer := range layerList {
			if len(random.Design.Factors) > layerIndex {
				result += layer * float64(random.Design.Factors[layerIndex].Ratio)
			} else {
				result += layer * float64(random.Design.Ratio)
			}
		}

		// 返回list(G值四舍五入保留三位小数)
		resultFloat, _ := strconv.ParseFloat(fmt.Sprintf("%.3f", result), 64)
		returnList = append(returnList, resultFloat)
	}
	return returnList
}

/**
 * 根据偏倚概率分配组别
 */
func simulateObtainBiasGroupNode(ctx *gin.Context, random models.RandomList, acceptList []float64, min float64, randomNumber models.RandomNumber, subject models.SimulateSubject, groups []models.Group) (models.RandomNumberReturn, error) {

	var randomNumberReturn models.RandomNumberReturn

	if random.Config.Probability == 0 {
		return models.RandomNumberReturn{}, tools.BuildServerError(ctx, "minimize_bias_probability_tips")
	}

	// 找出最小值将偏倚概率0.9分配给他(多个最小值相同按比例将偏倚概率0.9进行分配)
	// 声明最小值和大于最小值组别以及集合比例集合
	var minGroup []models.Group
	var maxGroup []models.Group

	for index, accept := range acceptList {
		if min == accept {
			minGroup = append(minGroup, groups[index])
		} else {
			maxGroup = append(maxGroup, groups[index])
		}
	}

	minNumberNode := obtainGroupNode(minGroup, random.Config.Probability)
	maxNumberNode := obtainGroupNode(maxGroup, 1-random.Config.Probability)
	// 先找是否符合最小值
	for _, minNode := range minNumberNode {
		if randomNumber.PlanNumber <= minNode.GroupNode {
			return simulateMinimizingGetRandomNumber(models.Group{
				Group:   minNode.Group,
				ParName: minNode.ParName,
				SubName: minNode.SubName,
			}, random, randomNumber, subject, acceptList, groups), nil
		}
	}

	// 后找是否符合最大值
	for _, maxNode := range maxNumberNode {
		nodeFloat, _ := strconv.ParseFloat(fmt.Sprintf("%.3f", random.Config.Probability+maxNode.GroupNode), 64)
		if randomNumber.PlanNumber <= nodeFloat {
			return simulateMinimizingGetRandomNumber(models.Group{
				Group:   maxNode.Group,
				ParName: maxNode.ParName,
				SubName: maxNode.SubName,
			}, random, randomNumber, subject, acceptList, groups), nil
		}
	}

	return randomNumberReturn, nil
}

func (s *SimulateRandomService) GetGroups(ctx *gin.Context, id string) ([]string, error) {
	oId, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var simulateRandom models.SimulateRandom
	err = tools.Database.Collection("simulate_random").FindOne(nil, bson.M{"_id": oId}).Decode(&simulateRandom)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	filter := bson.M{"customer_id": simulateRandom.CustomerID, "project_id": simulateRandom.ProjectID, "env_id": simulateRandom.EnvironmentID}
	if simulateRandom.CohortID != primitive.NilObjectID {
		filter = bson.M{"customer_id": simulateRandom.CustomerID, "project_id": simulateRandom.ProjectID, "env_id": simulateRandom.EnvironmentID, "cohort_id": simulateRandom.CohortID}
	}
	var groups []string
	var randomDesign models.RandomDesign
	err = tools.Database.Collection("random_design").FindOne(nil, filter).Decode(&randomDesign)
	if randomDesign.ID.IsZero() {
		return groups, nil
	}
	randomDesign.Info.Groups = slice.Filter(randomDesign.Info.Groups, func(index int, item models.RandomGroup) bool {
		return item.Status == nil || *item.Status == 1
	})
	for _, group := range randomDesign.Info.Groups {
		if group.SubGroup != nil && len(group.SubGroup) > 0 {
			for _, subGroup := range group.SubGroup {
				groups = append(groups, group.Name+" "+subGroup.Name)
			}
		} else {
			groups = append(groups, group.Name)
		}
	}
	return groups, nil
}

func (s *SimulateRandomService) List(ctx *gin.Context) ([]models.SimulationRandomView, error) {
	views, err := GetSimulateRandomList(ctx, ctx.Query("customerId"), ctx.Query("projectId"), ctx.Query("envId"))
	if err != nil {
		return views, errors.WithStack(err)
	}
	return views, nil
}

func GetSimulateRandomList(ctx *gin.Context, customerId string, projectId string, envId string) ([]models.SimulationRandomView, error) {
	customerID, _ := primitive.ObjectIDFromHex(customerId)
	projectID, _ := primitive.ObjectIDFromHex(projectId)
	envID, _ := primitive.ObjectIDFromHex(envId)
	views := make([]models.SimulationRandomView, 0)
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectID}).Decode(&project)
	if err != nil {
		return views, errors.WithStack(err)
	}
	envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == envID
	})

	filter := bson.M{"customer_id": customerID, "project_id": projectID, "env_id": envID}
	var simulateRandoms []models.SimulateRandom
	cursor, err := tools.Database.Collection("simulate_random").Find(nil, filter)
	if err != nil {
		return views, errors.WithStack(err)
	}
	err = cursor.All(nil, &simulateRandoms)
	if err != nil {
		return views, errors.WithStack(err)
	}

	// 查询随机列表
	var randomList []models.RandomList
	randomListCursor, err := tools.Database.Collection("random_list").Find(nil, bson.M{"customer_id": customerID, "project_id": projectID, "env_id": envID})
	if err != nil {
		return views, errors.WithStack(err)
	}
	err = randomListCursor.All(nil, &randomList)
	if err != nil {
		return views, errors.WithStack(err)
	}

	for _, simulateRandom := range simulateRandoms {
		var cohort models.Cohort
		cohortP, b := slice.Find(envP.Cohorts, func(index int, item models.Cohort) bool {
			return item.ID == simulateRandom.CohortID
		})
		if b {
			cohort = *cohortP
		}
		var simulateRandomList []models.RandomList
		if simulateRandom.SimulateRandomInfo.RandomListIds != nil && len(simulateRandom.SimulateRandomInfo.RandomListIds) > 0 {
			for _, rsr := range simulateRandom.SimulateRandomInfo.RandomListIds {
				for _, rl := range randomList {
					randomListID, _ := primitive.ObjectIDFromHex(rsr)
					if randomListID == rl.ID {
						simulateRandomList = append(simulateRandomList, rl)
					}
				}
			}
		}
		randomListNames := slice.Map(simulateRandomList, func(index int, item models.RandomList) string {
			return item.Name
		})
		randomResult, err := getDetail(simulateRandom.ID)
		if err != nil {
			return nil, err
		}
		plaintext := []byte(simulateRandom.ID.Hex())
		aes := tools.ShuffleString(plaintext)
		// Base64编码
		encryptedData := base64.StdEncoding.EncodeToString(aes)
		view := models.SimulationRandomView{
			ID:                     simulateRandom.ID,
			CohortID:               simulateRandom.CohortID,
			Cohort:                 cohort,
			Name:                   simulateRandom.SimulateRandomInfo.Name,
			RandomListIds:          simulateRandom.SimulateRandomInfo.RandomListIds,
			SimulateRandomList:     randomListNames,
			SiteQuantity:           simulateRandom.SimulateRandomInfo.SiteQuantity,
			CountryQuantity:        simulateRandom.SimulateRandomInfo.CountryQuantity,
			RegionQuantity:         simulateRandom.SimulateRandomInfo.RegionQuantity,
			RunQuantity:            simulateRandom.SimulateRandomInfo.RunQuantity,
			SubjectQuantity:        simulateRandom.SimulateRandomInfo.SubjectQuantity,
			SimulationRandomResult: randomResult,
			OnlyID:                 encryptedData,
			FactorRatio:            simulateRandom.SimulateRandomInfo.FactorRatio,
		}
		views = append(views, view)
	}
	return views, nil
}

func (s *SimulateRandomService) GetDetail(ctx *gin.Context, id string) (models.SimulationRandomResult, error) {
	oId, _ := primitive.ObjectIDFromHex(id)
	randomResult, err := getDetail(oId)
	if err != nil {
		return randomResult, err
	}
	return randomResult, nil
}

func getDetail(oId primitive.ObjectID) (models.SimulationRandomResult, error) {
	result := models.SimulationRandomResult{}
	var subjects []models.SimulateSubject
	cursor, err := tools.Database.Collection("simulate_subject").Find(nil, bson.M{"simulate_random_id": oId})
	if err != nil {
		return result, errors.WithStack(err)
	}
	err = cursor.All(nil, &subjects)
	if err != nil {
		return result, errors.WithStack(err)
	}
	var simulateRandom models.SimulateRandom
	err = tools.Database.Collection("simulate_random").FindOne(nil, bson.M{"_id": oId}).Decode(&simulateRandom)
	if err != nil {
		return result, errors.WithStack(err)
	}
	filter := bson.M{"customer_id": simulateRandom.CustomerID, "project_id": simulateRandom.ProjectID, "env_id": simulateRandom.EnvironmentID}
	if simulateRandom.CohortID != primitive.NilObjectID {
		filter = bson.M{"customer_id": simulateRandom.CustomerID, "project_id": simulateRandom.ProjectID, "env_id": simulateRandom.EnvironmentID, "cohort_id": simulateRandom.CohortID}
	}
	var randomDesign models.RandomDesign
	err = tools.Database.Collection("random_design").FindOne(nil, filter).Decode(&randomDesign)
	if err != nil {
		return result, errors.WithStack(err)
	}
	randomDesign.Info.Groups = slice.Filter(randomDesign.Info.Groups, func(index int, item models.RandomGroup) bool {
		return item.Status == nil || *item.Status == 1
	})
	var groups []string
	for _, group := range randomDesign.Info.Groups {
		if group.SubGroup != nil && len(group.SubGroup) > 0 {
			for _, subGroup := range group.SubGroup {
				groups = append(groups, group.Name+" "+subGroup.Name)
			}
		} else {
			groups = append(groups, group.Name)
		}
	}
	result.Groups = groups
	randomDesign.Info.Factors = slice.Filter(randomDesign.Info.Factors, func(index int, item models.RandomFactor) bool {
		return item.Status == nil || *item.Status == 1
	})
	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute)
	if err != nil {
		return result, errors.WithStack(err)
	}

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": simulateRandom.ProjectID}).Decode(&project)
	if err != nil {
		return result, errors.WithStack(err)
	}

	randomLists := make([]models.RandomList, 0)
	randomListIds := slice.Map(simulateRandom.SimulateRandomInfo.RandomListIds, func(index int, item string) primitive.ObjectID {
		id, _ := primitive.ObjectIDFromHex(item)
		return id
	})
	cursor, err = tools.Database.Collection("random_list").Find(nil, bson.M{"_id": bson.M{"$in": randomListIds}})
	if err != nil {
		return result, errors.WithStack(err)
	}
	err = cursor.All(nil, &randomLists)
	if err != nil {
		return result, errors.WithStack(err)
	}
	if len(randomListIds) == 0 {
		return result, nil
	}
	factorSlices := make([][]models.Factors, 0)
	for _, randomList := range randomLists {
		if randomList.Design.Combination != nil && len(randomList.Design.Combination) > 0 {
			for _, combination := range randomList.Design.Combination {
				if combination.LayeredFactors != nil && len(combination.LayeredFactors) > 0 {
					factorSlices = append(factorSlices, combination.LayeredFactors)
				}
			}
		}

	}
	//均数标准差总览
	avgSDOverview := models.AvgSDOverview{}
	{
		//均数标准差项目总览
		projectAvgSDOverView := models.AvgSD{}
		avgSDS := make([]models.AvgSDItem, 0)
		for _, group := range groups {
			var hitSubjects []models.SimulateSubject
			for _, subject := range subjects {
				if subject.GroupName == group {
					hitSubjects = append(hitSubjects, subject)
				}
			}
			avgSDS = getAvgSDs(simulateRandom, hitSubjects, group, avgSDS)
		}
		projectAvgSDOverView.AvgSDs = avgSDS
		projectAvgSDOverView.Name = project.Number
		avgSDOverview.ProjectOverView = projectAvgSDOverView
	}
	{
		//均数标准差中心总览
		siteAvgSDOverView := make([]models.AvgSD, 0)
		for i := 0; i < simulateRandom.SimulateRandomInfo.SiteQuantity; i++ {
			avgSD := models.AvgSD{}
			site := fmt.Sprintf("site_%d", i+1)
			avgSD.Name = site
			avgSDS := make([]models.AvgSDItem, 0)
			for _, group := range groups {
				var hitSubjects []models.SimulateSubject
				for _, subject := range subjects {
					if subject.GroupName == group && subject.SiteName == site {
						hitSubjects = append(hitSubjects, subject)
					}
				}
				avgSDS = getAvgSDs(simulateRandom, hitSubjects, group, avgSDS)
			}
			avgSD.AvgSDs = avgSDS
			siteAvgSDOverView = append(siteAvgSDOverView, avgSD)
		}
		avgSDOverview.SiteOverView = siteAvgSDOverView
	}
	{
		//均数标准差区域总览
		regionAvgSDOverView := make([]models.AvgSD, 0)
		for i := 0; i < simulateRandom.SimulateRandomInfo.RegionQuantity; i++ {
			avgSD := models.AvgSD{}
			region := fmt.Sprintf("region_%d", i+1)
			avgSD.Name = region
			avgSDS := make([]models.AvgSDItem, 0)
			for _, group := range groups {
				var hitSubjects []models.SimulateSubject
				for _, subject := range subjects {
					if subject.GroupName == group && subject.RegionName == region {
						hitSubjects = append(hitSubjects, subject)
					}
				}
				avgSDS = getAvgSDs(simulateRandom, hitSubjects, group, avgSDS)
			}
			avgSD.AvgSDs = avgSDS
			regionAvgSDOverView = append(regionAvgSDOverView, avgSD)
		}
		avgSDOverview.RegionOverView = regionAvgSDOverView
	}
	{
		//均数标准差国家总览
		countryAvgSDOverView := make([]models.AvgSD, 0)
		for i := 0; i < simulateRandom.SimulateRandomInfo.CountryQuantity; i++ {
			avgSD := models.AvgSD{}
			country := fmt.Sprintf("country_%d", i+1)
			avgSD.Name = country
			avgSDS := make([]models.AvgSDItem, 0)
			for _, group := range groups {
				var hitSubjects []models.SimulateSubject
				for _, subject := range subjects {
					if subject.GroupName == group && subject.CountryName == country {
						hitSubjects = append(hitSubjects, subject)
					}
				}
				avgSDS = getAvgSDs(simulateRandom, hitSubjects, group, avgSDS)
			}
			avgSD.AvgSDs = avgSDS
			countryAvgSDOverView = append(countryAvgSDOverView, avgSD)
		}
		avgSDOverview.CountryOverView = countryAvgSDOverView
	}
	{
		//均数标准差分层总览
		factorAvgSDOverView := make([]models.AvgSD, 0)
		for _, factor := range randomDesign.Info.Factors {
			ops := factor.Options
			for _, option := range ops {
				avgSD := models.AvgSD{}
				avgSDS := make([]models.AvgSDItem, 0)
				label := fmt.Sprintf("%s(%s)", factor.Label, option.Label)
				avgSD.Name = label
				for _, group := range groups {
					var hitSubjects []models.SimulateSubject
					for _, subject := range subjects {
						if subject.GroupName == group {
							for _, info := range subject.Info {
								if info.Name == factor.Name && info.Value == option.Value {
									hitSubjects = append(hitSubjects, subject)
								}
							}
						}
					}
					avgSDS = getAvgSDs(simulateRandom, hitSubjects, group, avgSDS)
				}
				avgSD.AvgSDs = avgSDS
				factorAvgSDOverView = append(factorAvgSDOverView, avgSD)
			}
		}
		avgSDOverview.FactorOverView = factorAvgSDOverView
	}
	{
		//均数标准差组合分层总览
		combinationFactorAvgSDOverView := make([]models.AvgSDCombinationFactor, 0)
		for _, factors := range factorSlices {
			if attribute.AttributeInfo.InstituteLayered {
				for i := 0; i < simulateRandom.SimulateRandomInfo.SiteQuantity; i++ {
					avgSD := models.AvgSDCombinationFactor{}
					avgSDS := make([]models.AvgSDItem, 0)
					site := fmt.Sprintf("site_%d", i+1)
					labels := make([]string, 0)
					labels = append(labels, site)
					for _, factor := range factors {
						factorLabel := fmt.Sprintf("%s:%s", factor.Label, factor.Text)
						labels = append(labels, factorLabel)
					}
					avgSD.Labels = labels
					for _, group := range groups {
						var hitSubjects []models.SimulateSubject
						for _, subject := range subjects {
							if subject.GroupName == group && subject.SiteName == site {
								matchs := slice.Map(factors, func(index int, item models.Factors) bool {
									_, b := slice.Find(subject.Info, func(index int, info models.Info) bool {
										return info.Name == item.Name && info.Value == item.Value
									})
									return b
								})
								//全部分层都匹配才算
								everyMatch := slice.Every(matchs, func(index int, item bool) bool {
									return item
								})
								if everyMatch {
									hitSubjects = append(hitSubjects, subject)
								}
							}
						}
						avgSDS = getAvgSDs(simulateRandom, hitSubjects, group, avgSDS)
					}
					avgSD.AvgSDs = avgSDS
					combinationFactorAvgSDOverView = append(combinationFactorAvgSDOverView, avgSD)
				}
			} else if attribute.AttributeInfo.CountryLayered {
				for i := 0; i < simulateRandom.SimulateRandomInfo.CountryQuantity; i++ {
					avgSD := models.AvgSDCombinationFactor{}
					avgSDS := make([]models.AvgSDItem, 0)
					country := fmt.Sprintf("country_%d", i+1)
					labels := make([]string, 0)
					labels = append(labels, country)
					for _, factor := range factors {
						factorLabel := fmt.Sprintf("%s:%s", factor.Label, factor.Text)
						labels = append(labels, factorLabel)
					}
					avgSD.Labels = labels
					for _, group := range groups {
						var hitSubjects []models.SimulateSubject
						for _, subject := range subjects {
							if subject.GroupName == group && subject.CountryName == country {
								matchs := slice.Map(factors, func(index int, item models.Factors) bool {
									_, b := slice.Find(subject.Info, func(index int, info models.Info) bool {
										return info.Name == item.Name && info.Value == item.Value
									})
									return b
								})
								//全部分层都匹配才算
								everyMatch := slice.Every(matchs, func(index int, item bool) bool {
									return item
								})
								if everyMatch {
									hitSubjects = append(hitSubjects, subject)
								}
							}
						}
						avgSDS = getAvgSDs(simulateRandom, hitSubjects, group, avgSDS)
					}
					avgSD.AvgSDs = avgSDS
					combinationFactorAvgSDOverView = append(combinationFactorAvgSDOverView, avgSD)
				}
			} else if attribute.AttributeInfo.RegionLayered {
				for i := 0; i < simulateRandom.SimulateRandomInfo.RegionQuantity; i++ {
					avgSD := models.AvgSDCombinationFactor{}
					avgSDS := make([]models.AvgSDItem, 0)
					region := fmt.Sprintf("region_%d", i+1)
					labels := make([]string, 0)
					labels = append(labels, region)
					for _, factor := range factors {
						factorLabel := fmt.Sprintf("%s:%s", factor.Label, factor.Text)
						labels = append(labels, factorLabel)
					}
					avgSD.Labels = labels
					for _, group := range groups {
						var hitSubjects []models.SimulateSubject
						for _, subject := range subjects {
							if subject.GroupName == group && subject.RegionName == region {
								matchs := slice.Map(factors, func(index int, item models.Factors) bool {
									_, b := slice.Find(subject.Info, func(index int, info models.Info) bool {
										return info.Name == item.Name && info.Value == item.Value
									})
									return b
								})
								//全部分层都匹配才算
								everyMatch := slice.Every(matchs, func(index int, item bool) bool {
									return item
								})
								if everyMatch {
									hitSubjects = append(hitSubjects, subject)
								}
							}
						}
						avgSDS = getAvgSDs(simulateRandom, hitSubjects, group, avgSDS)
					}
					avgSD.AvgSDs = avgSDS
					combinationFactorAvgSDOverView = append(combinationFactorAvgSDOverView, avgSD)
				}
			} else {
				avgSD := models.AvgSDCombinationFactor{}
				avgSDS := make([]models.AvgSDItem, 0)
				labels := make([]string, 0)
				for _, factor := range factors {
					factorLabel := fmt.Sprintf("%s:%s", factor.Label, factor.Text)
					labels = append(labels, factorLabel)
				}
				avgSD.Labels = labels
				for _, group := range groups {
					var hitSubjects []models.SimulateSubject
					for _, subject := range subjects {
						if subject.GroupName == group {
							matchs := slice.Map(factors, func(index int, item models.Factors) bool {
								_, b := slice.Find(subject.Info, func(index int, info models.Info) bool {
									return info.Name == item.Name && info.Value == item.Value
								})
								return b
							})
							//全部分层都匹配才算
							everyMatch := slice.Every(matchs, func(index int, item bool) bool {
								return item
							})
							if everyMatch {
								hitSubjects = append(hitSubjects, subject)
							}
						}
					}
					avgSDS = getAvgSDs(simulateRandom, hitSubjects, group, avgSDS)
				}
				avgSD.AvgSDs = avgSDS
				combinationFactorAvgSDOverView = append(combinationFactorAvgSDOverView, avgSD)
			}
		}
		avgSDOverview.CombinationFactorOverView = combinationFactorAvgSDOverView
	}
	result.AvgSDOverview = avgSDOverview

	//最小值总览
	minOverview := models.MinOverview{}
	{
		//最小值项目总览
		projectAvgSDOverView := models.Min{}
		mins := make([]models.MinItem, 0)
		for _, group := range groups {
			minItem := models.MinItem{}
			var hitSubjects []models.SimulateSubject
			for _, subject := range subjects {
				if subject.GroupName == group {
					hitSubjects = append(hitSubjects, subject)
				}
			}
			runCountMap := make(map[int]int)
			for i := 0; i < simulateRandom.SimulateRandomInfo.RunQuantity; i++ {
				run := i + 1
				runCountMap[run] = 0
			}
			for _, subject := range hitSubjects {
				runCountMap[subject.RunCount]++
			}
			if len(runCountMap) > 0 {
				minCount := mathutil.Min(maputil.Values(runCountMap)...)
				minItem.Group = group
				minItem.Min = minCount
				mins = append(mins, minItem)
			}
		}
		projectAvgSDOverView.Mins = mins
		projectAvgSDOverView.Name = project.Number
		minOverview.ProjectOverView = projectAvgSDOverView
	}
	{
		//最小值中心总览
		siteMinOverView := make([]models.Min, 0)
		for i := 0; i < simulateRandom.SimulateRandomInfo.SiteQuantity; i++ {
			minStruct := models.Min{}
			site := fmt.Sprintf("site_%d", i+1)
			minStruct.Name = site
			mins := make([]models.MinItem, 0)
			for _, group := range groups {
				minItem := models.MinItem{}
				var hitSubjects []models.SimulateSubject
				for _, subject := range subjects {
					if subject.GroupName == group && subject.SiteName == site {
						hitSubjects = append(hitSubjects, subject)
					}
				}
				mins = getMins(hitSubjects, minItem, group, mins, simulateRandom.SimulateRandomInfo.RunQuantity)
			}
			minStruct.Mins = mins
			siteMinOverView = append(siteMinOverView, minStruct)
		}
		minOverview.SiteOverView = siteMinOverView
	}
	{
		//最小值国家总览
		countryMinOverView := make([]models.Min, 0)
		for i := 0; i < simulateRandom.SimulateRandomInfo.CountryQuantity; i++ {
			minStruct := models.Min{}
			country := fmt.Sprintf("country_%d", i+1)
			minStruct.Name = country
			mins := make([]models.MinItem, 0)
			for _, group := range groups {
				minItem := models.MinItem{}
				var hitSubjects []models.SimulateSubject
				for _, subject := range subjects {
					if subject.GroupName == group && subject.CountryName == country {
						hitSubjects = append(hitSubjects, subject)
					}
				}
				mins = getMins(hitSubjects, minItem, group, mins, simulateRandom.SimulateRandomInfo.RunQuantity)
			}
			minStruct.Mins = mins
			countryMinOverView = append(countryMinOverView, minStruct)
		}
		minOverview.CountryOverView = countryMinOverView
	}
	{
		//最小值区域总览
		regionMinOverView := make([]models.Min, 0)
		for i := 0; i < simulateRandom.SimulateRandomInfo.RegionQuantity; i++ {
			minStruct := models.Min{}
			region := fmt.Sprintf("region_%d", i+1)
			minStruct.Name = region
			mins := make([]models.MinItem, 0)
			for _, group := range groups {
				minItem := models.MinItem{}
				var hitSubjects []models.SimulateSubject
				for _, subject := range subjects {
					if subject.GroupName == group && subject.RegionName == region {
						hitSubjects = append(hitSubjects, subject)
					}
				}
				mins = getMins(hitSubjects, minItem, group, mins, simulateRandom.SimulateRandomInfo.RunQuantity)
			}
			minStruct.Mins = mins
			regionMinOverView = append(regionMinOverView, minStruct)
		}
		minOverview.RegionOverView = regionMinOverView
	}
	{
		//最小值分层总览
		factorMinOverView := make([]models.Min, 0)
		for _, factor := range randomDesign.Info.Factors {
			ops := factor.Options
			for _, option := range ops {
				minStruct := models.Min{}
				label := fmt.Sprintf("%s(%s)", factor.Label, option.Label)
				minStruct.Name = label
				mins := make([]models.MinItem, 0)
				for _, group := range groups {
					minItem := models.MinItem{}
					var hitSubjects []models.SimulateSubject
					for _, subject := range subjects {
						if subject.GroupName == group {
							for _, info := range subject.Info {
								if info.Name == factor.Name && info.Value == option.Value {
									hitSubjects = append(hitSubjects, subject)
								}
							}
						}
					}
					mins = getMins(hitSubjects, minItem, group, mins, simulateRandom.SimulateRandomInfo.RunQuantity)
				}
				minStruct.Mins = mins
				factorMinOverView = append(factorMinOverView, minStruct)
			}
		}
		minOverview.FactorOverView = factorMinOverView
	}
	{
		//最小值组合分层总览
		combinationFactorMinOverView := make([]models.MinCombinationFactor, 0)
		for _, factors := range factorSlices {
			if attribute.AttributeInfo.InstituteLayered {
				for i := 0; i < simulateRandom.SimulateRandomInfo.SiteQuantity; i++ {
					minStruct := models.MinCombinationFactor{}
					mins := make([]models.MinItem, 0)
					site := fmt.Sprintf("site_%d", i+1)
					labels := make([]string, 0)
					labels = append(labels, site)
					for _, factor := range factors {
						factorLabel := fmt.Sprintf("%s:%s", factor.Label, factor.Text)
						labels = append(labels, factorLabel)
					}
					minStruct.Labels = labels
					for _, group := range groups {
						minItem := models.MinItem{}
						var hitSubjects []models.SimulateSubject
						for _, subject := range subjects {
							if subject.GroupName == group && subject.SiteName == site {
								matchs := slice.Map(factors, func(index int, item models.Factors) bool {
									_, b := slice.Find(subject.Info, func(index int, info models.Info) bool {
										return info.Name == item.Name && info.Value == item.Value
									})
									return b
								})
								//全部分层都匹配才算
								everyMatch := slice.Every(matchs, func(index int, item bool) bool {
									return item
								})
								if everyMatch {
									hitSubjects = append(hitSubjects, subject)
								}
							}
						}
						mins = getMins(hitSubjects, minItem, group, mins, simulateRandom.SimulateRandomInfo.RunQuantity)
					}
					minStruct.Mins = mins
					combinationFactorMinOverView = append(combinationFactorMinOverView, minStruct)
				}
			} else if attribute.AttributeInfo.CountryLayered {
				for i := 0; i < simulateRandom.SimulateRandomInfo.CountryQuantity; i++ {
					minStruct := models.MinCombinationFactor{}
					mins := make([]models.MinItem, 0)
					country := fmt.Sprintf("country_%d", i+1)
					labels := make([]string, 0)
					labels = append(labels, country)
					for _, factor := range factors {
						factorLabel := fmt.Sprintf("%s:%s", factor.Label, factor.Text)
						labels = append(labels, factorLabel)
					}
					minStruct.Labels = labels
					for _, group := range groups {
						minItem := models.MinItem{}
						var hitSubjects []models.SimulateSubject
						for _, subject := range subjects {
							if subject.GroupName == group && subject.CountryName == country {
								matchs := slice.Map(factors, func(index int, item models.Factors) bool {
									_, b := slice.Find(subject.Info, func(index int, info models.Info) bool {
										return info.Name == item.Name && info.Value == item.Value
									})
									return b
								})
								//全部分层都匹配才算
								everyMatch := slice.Every(matchs, func(index int, item bool) bool {
									return item
								})
								if everyMatch {
									hitSubjects = append(hitSubjects, subject)
								}
							}
						}
						mins = getMins(hitSubjects, minItem, group, mins, simulateRandom.SimulateRandomInfo.RunQuantity)
					}
					minStruct.Mins = mins
					combinationFactorMinOverView = append(combinationFactorMinOverView, minStruct)
				}
			} else if attribute.AttributeInfo.RegionLayered {
				for i := 0; i < simulateRandom.SimulateRandomInfo.RegionQuantity; i++ {
					minStruct := models.MinCombinationFactor{}
					mins := make([]models.MinItem, 0)
					region := fmt.Sprintf("region_%d", i+1)
					labels := make([]string, 0)
					labels = append(labels, region)
					for _, factor := range factors {
						factorLabel := fmt.Sprintf("%s:%s", factor.Label, factor.Text)
						labels = append(labels, factorLabel)
					}
					minStruct.Labels = labels
					for _, group := range groups {
						minItem := models.MinItem{}
						var hitSubjects []models.SimulateSubject
						for _, subject := range subjects {
							if subject.GroupName == group && subject.RegionName == region {
								matchs := slice.Map(factors, func(index int, item models.Factors) bool {
									_, b := slice.Find(subject.Info, func(index int, info models.Info) bool {
										return info.Name == item.Name && info.Value == item.Value
									})
									return b
								})
								//全部分层都匹配才算
								everyMatch := slice.Every(matchs, func(index int, item bool) bool {
									return item
								})
								if everyMatch {
									hitSubjects = append(hitSubjects, subject)
								}
							}
						}
						mins = getMins(hitSubjects, minItem, group, mins, simulateRandom.SimulateRandomInfo.RunQuantity)
					}
					minStruct.Mins = mins
					combinationFactorMinOverView = append(combinationFactorMinOverView, minStruct)
				}
			} else {
				minStruct := models.MinCombinationFactor{}
				mins := make([]models.MinItem, 0)
				labels := make([]string, 0)
				for _, factor := range factors {
					factorLabel := fmt.Sprintf("%s:%s", factor.Label, factor.Text)
					labels = append(labels, factorLabel)
				}
				minStruct.Labels = labels
				for _, group := range groups {
					minItem := models.MinItem{}
					var hitSubjects []models.SimulateSubject
					for _, subject := range subjects {
						if subject.GroupName == group {
							matchs := slice.Map(factors, func(index int, item models.Factors) bool {
								_, b := slice.Find(subject.Info, func(index int, info models.Info) bool {
									return info.Name == item.Name && info.Value == item.Value
								})
								return b
							})
							//全部分层都匹配才算
							everyMatch := slice.Every(matchs, func(index int, item bool) bool {
								return item
							})
							if everyMatch {
								hitSubjects = append(hitSubjects, subject)
							}
						}
					}
					mins = getMins(hitSubjects, minItem, group, mins, simulateRandom.SimulateRandomInfo.RunQuantity)
				}
				minStruct.Mins = mins
				combinationFactorMinOverView = append(combinationFactorMinOverView, minStruct)
			}
		}
		minOverview.CombinationFactorOverView = combinationFactorMinOverView
	}
	result.MinOverview = minOverview

	//详情

	randomList := randomLists[0]
	//构建组别比例map
	ratioTotal := 0
	for _, group := range randomList.Design.Groups {
		ratioTotal = ratioTotal + group.Ratio
	}
	ratioMap := map[string]int{}
	for _, group := range randomList.Design.Groups {
		ratioMap[group.Name] = group.Ratio
	}
	//详情
	unbalancedDetails := make([]models.UnbalancedDetail, 0)
	for i := 0; i < simulateRandom.SimulateRandomInfo.RunQuantity; i++ {
		run := i + 1
		detail := models.UnbalancedDetail{}
		detail.Run = run
		groupCounts := make([]models.GroupCount, 0)
		for _, group := range groups {
			count := slice.Count(subjects, func(index int, item models.SimulateSubject) bool {
				return item.RunCount == run && item.GroupName == group
			})
			groupCount := models.GroupCount{
				Group: group,
				Run:   run,
				Count: count,
			}
			groupCounts = append(groupCounts, groupCount)
		}
		detail.GroupCounts = groupCounts
		detail.Groups = groups
		{
			//项目
			unbalanced := models.Unbalanced{}
			unbalanceds := make([]models.UnbalancedItem, 0)
			for _, group := range groups {
				unbalancedItem := models.UnbalancedItem{}
				totalCount := slice.Count(subjects, func(index int, item models.SimulateSubject) bool {
					return item.RunCount == run
				})
				count := slice.Count(subjects, func(index int, item models.SimulateSubject) bool {
					return item.RunCount == run && item.GroupName == group
				})
				unbalanced.Total = totalCount
				unbalancedItem.Group = group
				unbalancedItem.Count = count
				u := strconv.FormatFloat(math.Abs(float64(totalCount)*(float64(ratioMap[group])/float64(ratioTotal))-float64(totalCount)), 'f', 1, 64)
				unbalancedItem.Unbalanced = u
				unbalanceds = append(unbalanceds, unbalancedItem)
			}
			unbalanced.Unbalanceds = unbalanceds
			unbalanced.Name = project.Number
			detail.ProjectDetail = unbalanced
		}
		{
			//中心
			unbalancedDetail := make([]models.Unbalanced, 0)
			for i := 0; i < simulateRandom.SimulateRandomInfo.SiteQuantity; i++ {
				unbalanced := models.Unbalanced{}
				site := fmt.Sprintf("site_%d", i+1)
				unbalanced.Name = site
				totalCount := slice.Count(subjects, func(index int, item models.SimulateSubject) bool {
					return item.RunCount == run && item.SiteName == site
				})
				unbalanceds := make([]models.UnbalancedItem, 0)
				for _, group := range groups {
					unbalancedItem := models.UnbalancedItem{}
					count := slice.Count(subjects, func(index int, item models.SimulateSubject) bool {
						return item.RunCount == run && item.SiteName == site && item.GroupName == group
					})
					unbalanced.Total = totalCount
					unbalancedItem.Group = group
					unbalancedItem.Count = count
					u := strconv.FormatFloat(math.Abs(float64(totalCount)*(float64(ratioMap[group])/float64(ratioTotal))-float64(count)), 'f', 1, 64)
					unbalancedItem.Unbalanced = u
					unbalanceds = append(unbalanceds, unbalancedItem)
				}
				unbalanced.Unbalanceds = unbalanceds
				unbalancedDetail = append(unbalancedDetail, unbalanced)

			}
			detail.SiteDetail = unbalancedDetail
		}
		{
			//区域
			unbalancedDetail := make([]models.Unbalanced, 0)
			for i := 0; i < simulateRandom.SimulateRandomInfo.RegionQuantity; i++ {
				unbalanced := models.Unbalanced{}
				region := fmt.Sprintf("region_%d", i+1)
				unbalanced.Name = region
				totalCount := slice.Count(subjects, func(index int, item models.SimulateSubject) bool {
					return item.RunCount == run && item.RegionName == region
				})
				unbalanceds := make([]models.UnbalancedItem, 0)
				for _, group := range groups {
					unbalancedItem := models.UnbalancedItem{}
					count := slice.Count(subjects, func(index int, item models.SimulateSubject) bool {
						return item.RunCount == run && item.RegionName == region && item.GroupName == group
					})
					unbalanced.Total = totalCount
					unbalancedItem.Group = group
					unbalancedItem.Count = count
					u := strconv.FormatFloat(math.Abs(float64(totalCount)*(float64(ratioMap[group])/float64(ratioTotal))-float64(count)), 'f', 1, 64)
					unbalancedItem.Unbalanced = u
					unbalanceds = append(unbalanceds, unbalancedItem)
				}
				unbalanced.Unbalanceds = unbalanceds
				unbalancedDetail = append(unbalancedDetail, unbalanced)

			}
			detail.RegionDetail = unbalancedDetail
		}
		{
			//国家
			unbalancedDetail := make([]models.Unbalanced, 0)
			for i := 0; i < simulateRandom.SimulateRandomInfo.CountryQuantity; i++ {
				unbalanced := models.Unbalanced{}
				country := fmt.Sprintf("country_%d", i+1)
				unbalanced.Name = country
				totalCount := slice.Count(subjects, func(index int, item models.SimulateSubject) bool {
					return item.RunCount == run && item.CountryName == country
				})
				unbalanceds := make([]models.UnbalancedItem, 0)
				for _, group := range groups {
					unbalancedItem := models.UnbalancedItem{}
					count := slice.Count(subjects, func(index int, item models.SimulateSubject) bool {
						return item.RunCount == run && item.CountryName == country && item.GroupName == group
					})
					unbalanced.Total = totalCount
					unbalancedItem.Group = group
					unbalancedItem.Count = count
					u := strconv.FormatFloat(math.Abs(float64(totalCount)*(float64(ratioMap[group])/float64(ratioTotal))-float64(count)), 'f', 1, 64)
					unbalancedItem.Unbalanced = u
					unbalanceds = append(unbalanceds, unbalancedItem)
				}
				unbalanced.Unbalanceds = unbalanceds
				unbalancedDetail = append(unbalancedDetail, unbalanced)

			}
			detail.CountryDetail = unbalancedDetail
		}
		{
			//分层
			unbalancedDetail := make([]models.Unbalanced, 0)
			for _, factor := range randomDesign.Info.Factors {
				ops := factor.Options
				for _, option := range ops {
					unbalanced := models.Unbalanced{}
					label := fmt.Sprintf("%s(%s)", factor.Label, option.Label)
					unbalanced.Name = label
					totalCount := slice.Count(subjects, func(index int, item models.SimulateSubject) bool {
						_, b := slice.Find(item.Info, func(index int, info models.Info) bool {
							return info.Name == factor.Name && info.Value == option.Value
						})
						return item.RunCount == run && b
					})
					unbalanceds := make([]models.UnbalancedItem, 0)
					for _, group := range groups {
						unbalancedItem := models.UnbalancedItem{}
						count := slice.Count(subjects, func(index int, item models.SimulateSubject) bool {
							_, b := slice.Find(item.Info, func(index int, info models.Info) bool {
								return info.Name == factor.Name && info.Value == option.Value
							})
							return item.RunCount == run && b && item.GroupName == group
						})
						unbalanced.Total = totalCount
						unbalancedItem.Group = group
						unbalancedItem.Count = count
						u := strconv.FormatFloat(math.Abs(float64(totalCount)*(float64(ratioMap[group])/float64(ratioTotal))-float64(count)), 'f', 1, 64)
						unbalancedItem.Unbalanced = u
						unbalanceds = append(unbalanceds, unbalancedItem)
					}
					unbalanced.Unbalanceds = unbalanceds
					unbalancedDetail = append(unbalancedDetail, unbalanced)
				}
			}
			detail.FactorDetail = unbalancedDetail
		}
		{
			//组合分层
			unbalancedDetail := make([]models.UnbalancedCombinationFactor, 0)
			for _, factors := range factorSlices {
				if attribute.AttributeInfo.InstituteLayered {
					for i := 0; i < simulateRandom.SimulateRandomInfo.SiteQuantity; i++ {
						unbalanced := models.UnbalancedCombinationFactor{}
						unbalanceds := make([]models.UnbalancedItem, 0)
						site := fmt.Sprintf("site_%d", i+1)
						labels := make([]string, 0)
						labels = append(labels, site)
						for _, factor := range factors {
							factorLabel := fmt.Sprintf("%s:%s", factor.Label, factor.Text)
							labels = append(labels, factorLabel)
						}
						totalCount := slice.Count(subjects, func(index int, subject models.SimulateSubject) bool {
							matchs := slice.Map(factors, func(index int, item models.Factors) bool {
								_, b := slice.Find(subject.Info, func(index int, info models.Info) bool {
									return info.Name == item.Name && info.Value == item.Value
								})
								return b
							})
							//全部分层都匹配才算
							everyMatch := slice.Every(matchs, func(index int, item bool) bool {
								return item
							})
							return subject.RunCount == run && subject.SiteName == site && everyMatch
						})
						unbalanced.Labels = labels
						for _, group := range groups {
							unbalancedItem := models.UnbalancedItem{}
							count := slice.Count(subjects, func(index int, subject models.SimulateSubject) bool {
								matchs := slice.Map(factors, func(index int, item models.Factors) bool {
									_, b := slice.Find(subject.Info, func(index int, info models.Info) bool {
										return info.Name == item.Name && info.Value == item.Value
									})
									return b
								})
								//全部分层都匹配才算
								everyMatch := slice.Every(matchs, func(index int, item bool) bool {
									return item
								})
								return subject.RunCount == run && subject.SiteName == site && everyMatch && subject.GroupName == group
							})
							unbalanced.Total = totalCount
							unbalancedItem.Group = group
							unbalancedItem.Count = count
							u := strconv.FormatFloat(math.Abs(float64(totalCount)*(float64(ratioMap[group])/float64(ratioTotal))-float64(count)), 'f', 1, 64)
							unbalancedItem.Unbalanced = u
							unbalanceds = append(unbalanceds, unbalancedItem)
						}
						unbalanced.Unbalanceds = unbalanceds
						unbalancedDetail = append(unbalancedDetail, unbalanced)
					}
				} else if attribute.AttributeInfo.CountryLayered {
					for i := 0; i < simulateRandom.SimulateRandomInfo.CountryQuantity; i++ {
						unbalanced := models.UnbalancedCombinationFactor{}
						unbalanceds := make([]models.UnbalancedItem, 0)
						country := fmt.Sprintf("country_%d", i+1)
						labels := make([]string, 0)
						labels = append(labels, country)
						for _, factor := range factors {
							factorLabel := fmt.Sprintf("%s:%s", factor.Label, factor.Text)
							labels = append(labels, factorLabel)
						}
						totalCount := slice.Count(subjects, func(index int, subject models.SimulateSubject) bool {
							matchs := slice.Map(factors, func(index int, item models.Factors) bool {
								_, b := slice.Find(subject.Info, func(index int, info models.Info) bool {
									return info.Name == item.Name && info.Value == item.Value
								})
								return b
							})
							//全部分层都匹配才算
							everyMatch := slice.Every(matchs, func(index int, item bool) bool {
								return item
							})
							return subject.RunCount == run && subject.CountryName == country && everyMatch
						})
						unbalanced.Labels = labels
						for _, group := range groups {
							unbalancedItem := models.UnbalancedItem{}
							count := slice.Count(subjects, func(index int, subject models.SimulateSubject) bool {
								matchs := slice.Map(factors, func(index int, item models.Factors) bool {
									_, b := slice.Find(subject.Info, func(index int, info models.Info) bool {
										return info.Name == item.Name && info.Value == item.Value
									})
									return b
								})
								//全部分层都匹配才算
								everyMatch := slice.Every(matchs, func(index int, item bool) bool {
									return item
								})
								return subject.RunCount == run && subject.CountryName == country && everyMatch && subject.GroupName == group
							})
							unbalanced.Total = totalCount
							unbalancedItem.Group = group
							unbalancedItem.Count = count
							u := strconv.FormatFloat(math.Abs(float64(totalCount)*(float64(ratioMap[group])/float64(ratioTotal))-float64(count)), 'f', 1, 64)
							unbalancedItem.Unbalanced = u
							unbalanceds = append(unbalanceds, unbalancedItem)
						}
						unbalanced.Unbalanceds = unbalanceds
						unbalancedDetail = append(unbalancedDetail, unbalanced)
					}
				} else if attribute.AttributeInfo.RegionLayered {
					for i := 0; i < simulateRandom.SimulateRandomInfo.RegionQuantity; i++ {
						unbalanced := models.UnbalancedCombinationFactor{}
						unbalanceds := make([]models.UnbalancedItem, 0)
						region := fmt.Sprintf("region_%d", i+1)
						labels := make([]string, 0)
						labels = append(labels, region)
						for _, factor := range factors {
							factorLabel := fmt.Sprintf("%s:%s", factor.Label, factor.Text)
							labels = append(labels, factorLabel)
						}
						totalCount := slice.Count(subjects, func(index int, subject models.SimulateSubject) bool {
							matchs := slice.Map(factors, func(index int, item models.Factors) bool {
								_, b := slice.Find(subject.Info, func(index int, info models.Info) bool {
									return info.Name == item.Name && info.Value == item.Value
								})
								return b
							})
							//全部分层都匹配才算
							everyMatch := slice.Every(matchs, func(index int, item bool) bool {
								return item
							})
							return subject.RunCount == run && subject.RegionName == region && everyMatch
						})
						unbalanced.Labels = labels
						for _, group := range groups {
							unbalancedItem := models.UnbalancedItem{}
							count := slice.Count(subjects, func(index int, subject models.SimulateSubject) bool {
								matchs := slice.Map(factors, func(index int, item models.Factors) bool {
									_, b := slice.Find(subject.Info, func(index int, info models.Info) bool {
										return info.Name == item.Name && info.Value == item.Value
									})
									return b
								})
								//全部分层都匹配才算
								everyMatch := slice.Every(matchs, func(index int, item bool) bool {
									return item
								})
								return subject.RunCount == run && subject.RegionName == region && everyMatch && subject.GroupName == group
							})
							unbalanced.Total = totalCount
							unbalancedItem.Group = group
							unbalancedItem.Count = count
							u := strconv.FormatFloat(math.Abs(float64(totalCount)*(float64(ratioMap[group])/float64(ratioTotal))-float64(count)), 'f', 1, 64)
							unbalancedItem.Unbalanced = u
							unbalanceds = append(unbalanceds, unbalancedItem)
						}
						unbalanced.Unbalanceds = unbalanceds
						unbalancedDetail = append(unbalancedDetail, unbalanced)
					}
				} else {
					unbalanced := models.UnbalancedCombinationFactor{}
					unbalanceds := make([]models.UnbalancedItem, 0)
					labels := make([]string, 0)
					for _, factor := range factors {
						factorLabel := fmt.Sprintf("%s:%s", factor.Label, factor.Text)
						labels = append(labels, factorLabel)
					}
					totalCount := slice.Count(subjects, func(index int, subject models.SimulateSubject) bool {
						matchs := slice.Map(factors, func(index int, item models.Factors) bool {
							_, b := slice.Find(subject.Info, func(index int, info models.Info) bool {
								return info.Name == item.Name && info.Value == item.Value
							})
							return b
						})
						//全部分层都匹配才算
						everyMatch := slice.Every(matchs, func(index int, item bool) bool {
							return item
						})
						return subject.RunCount == run && everyMatch
					})
					unbalanced.Labels = labels
					for _, group := range groups {
						unbalancedItem := models.UnbalancedItem{}
						count := slice.Count(subjects, func(index int, subject models.SimulateSubject) bool {
							matchs := slice.Map(factors, func(index int, item models.Factors) bool {
								_, b := slice.Find(subject.Info, func(index int, info models.Info) bool {
									return info.Name == item.Name && info.Value == item.Value
								})
								return b
							})
							//全部分层都匹配才算
							everyMatch := slice.Every(matchs, func(index int, item bool) bool {
								return item
							})
							return subject.RunCount == run && everyMatch && subject.GroupName == group
						})
						unbalanced.Total = totalCount
						unbalancedItem.Group = group
						unbalancedItem.Count = count
						u := strconv.FormatFloat(math.Abs(float64(totalCount)*(float64(ratioMap[group])/float64(ratioTotal))-float64(count)), 'f', 1, 64)
						unbalancedItem.Unbalanced = u
						unbalanceds = append(unbalanceds, unbalancedItem)
					}
					unbalanced.Unbalanceds = unbalanceds
					unbalancedDetail = append(unbalancedDetail, unbalanced)
				}
			}
			detail.CombinationFactorDetail = unbalancedDetail
		}
		unbalancedDetails = append(unbalancedDetails, detail)
	}
	result.UnbalancedDetails = unbalancedDetails

	//不均衡运行次数
	unbalancedRunCountOverview := models.UnbalancedRunCountOverview{}
	{
		//项目
		unbalancedRunCount := models.UnbalancedRunCount{}
		count := 0
		for _, detail := range result.UnbalancedDetails {
			match := false
			for _, item := range detail.ProjectDetail.Unbalanceds {
				f, _ := strconv.ParseFloat(item.Unbalanced, 64)
				if f >= 2 {
					match = true
				}
			}
			if match {
				count++
			}
		}
		unbalancedRunCount.Count = count
		unbalancedRunCount.Name = project.Number
		unbalancedRunCountOverview.ProjectOverView = unbalancedRunCount
	}
	{
		//中心
		unbalancedRunCounts := make([]models.UnbalancedRunCount, 0)
		if result.UnbalancedDetails != nil && len(result.UnbalancedDetails) > 0 {
			tempDetail := result.UnbalancedDetails[0]
			if tempDetail.SiteDetail != nil && len(tempDetail.SiteDetail) > 0 {
				for _, site := range tempDetail.SiteDetail {
					name := site.Name
					unbalancedRunCount := models.UnbalancedRunCount{}
					count := 0
					for _, detail := range result.UnbalancedDetails {
						for _, unbalanced := range detail.SiteDetail {
							if unbalanced.Name == name {
								match := false
								for _, item := range unbalanced.Unbalanceds {
									f, _ := strconv.ParseFloat(item.Unbalanced, 64)
									if f >= 2 {
										match = true
									}
								}
								if match {
									count++
								}
							}
						}

					}
					unbalancedRunCount.Name = name
					unbalancedRunCount.Count = count
					unbalancedRunCounts = append(unbalancedRunCounts, unbalancedRunCount)
				}
			}
		}
		unbalancedRunCountOverview.SiteOverView = unbalancedRunCounts
	}
	{
		//国家
		unbalancedRunCounts := make([]models.UnbalancedRunCount, 0)
		if result.UnbalancedDetails != nil && len(result.UnbalancedDetails) > 0 {
			tempDetail := result.UnbalancedDetails[0]
			if tempDetail.CountryDetail != nil && len(tempDetail.CountryDetail) > 0 {
				for _, country := range tempDetail.CountryDetail {
					name := country.Name
					unbalancedRunCount := models.UnbalancedRunCount{}
					count := 0
					for _, detail := range result.UnbalancedDetails {
						for _, unbalanced := range detail.CountryDetail {
							if unbalanced.Name == name {
								match := false
								for _, item := range unbalanced.Unbalanceds {
									f, _ := strconv.ParseFloat(item.Unbalanced, 64)
									if f >= 2 {
										match = true
									}
								}
								if match {
									count++
								}
							}
						}

					}
					unbalancedRunCount.Name = name
					unbalancedRunCount.Count = count
					unbalancedRunCounts = append(unbalancedRunCounts, unbalancedRunCount)
				}
			}
		}
		unbalancedRunCountOverview.CountryOverView = unbalancedRunCounts
	}
	{
		//区域
		unbalancedRunCounts := make([]models.UnbalancedRunCount, 0)
		if result.UnbalancedDetails != nil && len(result.UnbalancedDetails) > 0 {
			tempDetail := result.UnbalancedDetails[0]
			if tempDetail.RegionDetail != nil && len(tempDetail.RegionDetail) > 0 {
				for _, region := range tempDetail.RegionDetail {
					name := region.Name
					unbalancedRunCount := models.UnbalancedRunCount{}
					count := 0
					for _, detail := range result.UnbalancedDetails {
						for _, unbalanced := range detail.RegionDetail {
							if unbalanced.Name == name {
								match := false
								for _, item := range unbalanced.Unbalanceds {
									f, _ := strconv.ParseFloat(item.Unbalanced, 64)
									if f >= 2 {
										match = true
									}
								}
								if match {
									count++
								}
							}
						}

					}
					unbalancedRunCount.Name = name
					unbalancedRunCount.Count = count
					unbalancedRunCounts = append(unbalancedRunCounts, unbalancedRunCount)
				}
			}
		}
		unbalancedRunCountOverview.RegionOverView = unbalancedRunCounts
	}
	{
		//分层
		unbalancedRunCounts := make([]models.UnbalancedRunCount, 0)
		if result.UnbalancedDetails != nil && len(result.UnbalancedDetails) > 0 {
			tempDetail := result.UnbalancedDetails[0]
			if tempDetail.FactorDetail != nil && len(tempDetail.FactorDetail) > 0 {
				for _, factor := range tempDetail.FactorDetail {
					name := factor.Name
					unbalancedRunCount := models.UnbalancedRunCount{}
					count := 0
					for _, detail := range result.UnbalancedDetails {
						for _, unbalanced := range detail.FactorDetail {
							if unbalanced.Name == name {
								match := false
								for _, item := range unbalanced.Unbalanceds {
									f, _ := strconv.ParseFloat(item.Unbalanced, 64)
									if f >= 2 {
										match = true
									}
								}
								if match {
									count++
								}
							}
						}

					}
					unbalancedRunCount.Name = name
					unbalancedRunCount.Count = count
					unbalancedRunCounts = append(unbalancedRunCounts, unbalancedRunCount)
				}
			}
		}
		unbalancedRunCountOverview.FactorOverView = unbalancedRunCounts
	}
	{
		//组合分层
		unbalancedRunCounts := make([]models.UnbalancedRunCountCombinationFactor, 0)
		if result.UnbalancedDetails != nil && len(result.UnbalancedDetails) > 0 {
			tempDetail := result.UnbalancedDetails[0]
			if tempDetail.CombinationFactorDetail != nil && len(tempDetail.CombinationFactorDetail) > 0 {
				for _, factor := range tempDetail.CombinationFactorDetail {
					name := fmt.Sprintf("%v", factor.Labels)
					unbalancedRunCount := models.UnbalancedRunCountCombinationFactor{}
					count := 0
					for _, detail := range result.UnbalancedDetails {
						for _, unbalanced := range detail.CombinationFactorDetail {
							if fmt.Sprintf("%v", unbalanced.Labels) == name {
								match := false
								for _, item := range unbalanced.Unbalanceds {
									f, _ := strconv.ParseFloat(item.Unbalanced, 64)
									if f >= 2 {
										match = true
									}
								}
								if match {
									count++
								}
							}
						}

					}
					unbalancedRunCount.Labels = factor.Labels
					unbalancedRunCount.Count = count
					unbalancedRunCounts = append(unbalancedRunCounts, unbalancedRunCount)
				}
			}
		}
		unbalancedRunCountOverview.CombinationFactorOverView = unbalancedRunCounts
	}
	result.UnbalancedRunCountOverview = unbalancedRunCountOverview
	return result, nil
}

func getMins(hitSubjects []models.SimulateSubject, minItem models.MinItem, group string, mins []models.MinItem, runQuantity int) []models.MinItem {
	runCountMap := make(map[int]int)
	for i := 0; i < runQuantity; i++ {
		run := i + 1
		runCountMap[run] = 0
	}
	for _, subject := range hitSubjects {
		runCountMap[subject.RunCount]++
	}
	if len(runCountMap) > 0 {
		minCount := mathutil.Min(maputil.Values(runCountMap)...)
		minItem.Group = group
		minItem.Min = minCount
		mins = append(mins, minItem)
	}
	return mins
}

func getAvgSDs(simulateRandom models.SimulateRandom, hitSubjects []models.SimulateSubject, group string, avgSDS []models.AvgSDItem) []models.AvgSDItem {
	xs := make([]float64, simulateRandom.SimulateRandomInfo.RunQuantity)
	for i := 0; i < simulateRandom.SimulateRandomInfo.RunQuantity; i++ {
		var hitCountSubjects []models.SimulateSubject
		for _, subject := range hitSubjects {
			if subject.RunCount == i+1 {
				hitCountSubjects = append(hitCountSubjects, subject)
			}
		}
		xs[i] = float64(len(hitCountSubjects))
	}
	avgSDItem := models.AvgSDItem{}
	avgSDItem.Group = group
	avg := tools.Average(xs)
	avgSDItem.Avg = strconv.FormatFloat(avg, 'f', 2, 64)
	sd := tools.StandardDeviation(xs)
	avgSDItem.SD = strconv.FormatFloat(sd, 'f', 2, 64)
	avgSDS = append(avgSDS, avgSDItem)
	return avgSDS
}
func (s *SimulateRandomService) GetSimulateDetail(ctx *gin.Context, id string) ([]map[string]interface{}, error) {
	oId, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var simulateSubject []models.SimulateSubject
	cursor, err := tools.Database.Collection("simulate_subject").Find(nil, bson.M{"simulate_random_id": oId})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &simulateSubject)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var datas []map[string]interface{}
	if len(simulateSubject) == 0 {
		return datas, nil
	}
	filter := bson.M{"_id": simulateSubject[0].RandomListID}
	var randomList models.RandomList
	err = tools.Database.Collection("random_list").FindOne(nil, filter).Decode(&randomList)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	subjectRunGroups := splitSlice4RunCount(simulateSubject)
	for _, group := range subjectRunGroups {
		data := map[string]interface{}{}
		data["runCount"] = group[0].RunCount
		for _, randomGroup := range randomList.Design.Groups {
			var nameGroup []models.SimulateSubject
			for _, subject := range group {
				if subject.GroupName == randomGroup.Name {
					nameGroup = append(nameGroup, subject)
				}
			}
			data[randomGroup.Name] = len(nameGroup)
		}
		datas = append(datas, data)
	}
	return datas, nil
}

func (s *SimulateRandomService) GetSiteDetail(ctx *gin.Context, id string, count int) ([]map[string]interface{}, error) {
	oId, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var simulateRandom models.SimulateRandom
	err = tools.Database.Collection("simulate_random").FindOne(nil, bson.M{"_id": oId}).Decode(&simulateRandom)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// RandomList
	randomFilter := bson.M{"customer_id": simulateRandom.CustomerID, "project_id": simulateRandom.ProjectID, "env_id": simulateRandom.EnvironmentID, "status": 1}
	if simulateRandom.CohortID != primitive.NilObjectID {
		randomFilter = bson.M{"customer_id": simulateRandom.CustomerID, "project_id": simulateRandom.ProjectID, "env_id": simulateRandom.EnvironmentID, "status": 1, "cohort_id": simulateRandom.CohortID}
	}
	var randomLists []models.RandomList
	cursor, err := tools.Database.Collection("random_list").Find(nil, randomFilter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &randomLists)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(randomLists) == 0 {
		return nil, tools.BuildServerError(ctx, "subject_no_random")
	}
	randomList := randomLists[0]
	var simulateSubjects []models.SimulateSubject
	cursor, err = tools.Database.Collection("simulate_subject").Find(nil, bson.M{"simulate_random_id": oId})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	cursor.All(nil, &simulateSubjects)
	siteQuantity := simulateRandom.SimulateRandomInfo.SiteQuantity
	//构建组别比例map
	ratioTotal := 0
	for _, group := range randomList.Design.Groups {
		ratioTotal = ratioTotal + group.Ratio
	}
	ratioMap := map[string]int{}
	for _, group := range randomList.Design.Groups {
		ratioMap[group.Name] = group.Ratio
	}
	var datas []map[string]interface{}
	for i := 0; i < siteQuantity; i++ {
		siteName := fmt.Sprintf("site_%d", i+1)
		data := map[string]interface{}{}
		var subjects []models.SimulateSubject
		for _, subject := range simulateSubjects {
			if subject.SiteName == siteName && subject.RunCount == count {
				subjects = append(subjects, subject)
			}
		}
		data["siteName"] = siteName
		data["total"] = len(subjects)
		if len(subjects) == 0 {
			for _, group := range randomList.Design.Groups {
				data[group.Name] = 0
				data[group.Name+"unbalanced"] = 0
			}
		} else {
			for _, group := range randomList.Design.Groups {
				var ss []models.SimulateSubject
				for _, subject := range subjects {
					if subject.GroupName == group.Name {
						ss = append(ss, subject)
					}
				}
				data[group.Name] = len(ss)
				unbalanced := strconv.FormatFloat(math.Abs(float64(len(subjects))*(float64(ratioMap[group.Name])/float64(ratioTotal))-float64(len(ss))), 'f', 1, 64)
				data[group.Name+"unbalanced"] = unbalanced
			}
		}
		datas = append(datas, data)
	}
	return datas, nil
}

func splitSlice4RunCount(list []models.SimulateSubject) [][]models.SimulateSubject {
	sort.Sort(models.SortByRunCount(list))
	returnData := make([][]models.SimulateSubject, 0)
	i := 0
	var j int
	for {
		if i >= len(list) {
			break
		}
		for j = i + 1; j < len(list) && list[i].RunCount == list[j].RunCount; j++ {
		}

		returnData = append(returnData, list[i:j])
		i = j
	}
	return returnData
}

func (s *SimulateRandomService) GetSiteOverview(ctx *gin.Context, id string) ([]map[string]interface{}, error) {
	var datas []map[string]interface{}
	oId, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var subjects []models.SimulateSubject
	cursor, err := tools.Database.Collection("simulate_subject").Find(nil, bson.M{"simulate_random_id": oId})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &subjects)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(subjects) == 0 {
		return datas, nil
	}
	var simulateRandom models.SimulateRandom
	err = tools.Database.Collection("simulate_random").FindOne(nil, bson.M{"_id": oId}).Decode(&simulateRandom)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	filter := bson.M{"_id": subjects[0].RandomListID}
	var randomList models.RandomList
	err = tools.Database.Collection("random_list").FindOne(nil, filter).Decode(&randomList)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	groups, err := s.GetGroups(ctx, id)
	if err != nil {
		return nil, err
	}
	for i := 0; i < simulateRandom.SimulateRandomInfo.SiteQuantity; i++ {
		data := map[string]interface{}{}
		site := fmt.Sprintf("site_%d", i+1)
		data["siteName"] = site
		for _, group := range groups {
			var hitSubjects []models.SimulateSubject
			for _, subject := range subjects {
				if subject.GroupName == group && subject.SiteName == site {
					hitSubjects = append(hitSubjects, subject)
				}
			}
			xs := make([]float64, simulateRandom.SimulateRandomInfo.RunQuantity)
			for j := 0; j < simulateRandom.SimulateRandomInfo.RunQuantity; j++ {
				var hitCountSubjects []models.SimulateSubject
				for _, subject := range hitSubjects {
					if subject.RunCount == j+1 {
						hitCountSubjects = append(hitCountSubjects, subject)
					}
				}
				xs[j] = float64(len(hitCountSubjects))
			}
			avg := tools.Average(xs)
			data[group+"Avg"] = strconv.FormatFloat(avg, 'f', 2, 64)
			sd := tools.StandardDeviation(xs)
			data[group+"SD"] = strconv.FormatFloat(sd, 'f', 2, 64)
		}
		datas = append(datas, data)
	}
	return datas, nil
}

func (s *SimulateRandomService) GetLayeredOverview(ctx *gin.Context, id string) ([]map[string]interface{}, error) {
	var datas []map[string]interface{}
	oId, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var subjects []models.SimulateSubject
	cursor, err := tools.Database.Collection("simulate_subject").Find(nil, bson.M{"simulate_random_id": oId})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &subjects)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(subjects) == 0 {
		return datas, nil
	}
	filter := bson.M{"_id": subjects[0].RandomListID}
	var randomList models.RandomList
	err = tools.Database.Collection("random_list").FindOne(nil, filter).Decode(&randomList)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(randomList.Design.Factors) == 0 {
		return datas, nil
	}
	var simulateRandom models.SimulateRandom
	err = tools.Database.Collection("simulate_random").FindOne(nil, bson.M{"_id": oId}).Decode(&simulateRandom)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	randomList.Design.Factors = slice.Filter(randomList.Design.Factors, func(index int, item models.RandomFactor) bool {
		if item.Status == nil {
			return true
		}
		return *item.Status != 2
	})
	for _, factor := range randomList.Design.Factors {
		ops := factor.Options
		for _, option := range ops {
			data := map[string]interface{}{}
			data["label"] = factor.Label
			data["factors"] = option.Label
			for _, group := range randomList.Design.Groups {
				var hitSubjects []models.SimulateSubject
				for _, subject := range subjects {
					if subject.GroupName == group.Name {
						for _, info := range subject.Info {
							if info.Name == factor.Name && info.Value == option.Value {
								hitSubjects = append(hitSubjects, subject)
							}
						}
					}
				}
				xs := make([]float64, simulateRandom.SimulateRandomInfo.RunQuantity)
				for i := 0; i < simulateRandom.SimulateRandomInfo.RunQuantity; i++ {
					var hitCountSubjects []models.SimulateSubject
					for _, subject := range hitSubjects {
						if subject.RunCount == i+1 {
							hitCountSubjects = append(hitCountSubjects, subject)
						}
					}
					xs[i] = float64(len(hitCountSubjects))
				}
				avg := tools.Average(xs)
				data[group.Name+"Avg"] = strconv.FormatFloat(avg, 'f', 2, 64)
				sd := tools.StandardDeviation(xs)
				data[group.Name+"SD"] = strconv.FormatFloat(sd, 'f', 2, 64)
			}
			datas = append(datas, data)
		}
	}
	return datas, nil
}

func (s *SimulateRandomService) DownloadList(ctx *gin.Context, id string) error {
	OID, _ := primitive.ObjectIDFromHex(id)
	var simulateSubjects []models.SimulateSubject
	cursor, _ := tools.Database.Collection("simulate_subject").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"simulate_random_id": OID}}},
		{{Key: "$sort", Value: bson.D{{"name", 1}}}},
	})

	var simulateRandom models.SimulateRandom
	err := tools.Database.Collection("simulate_random").FindOne(nil, bson.M{"_id": OID}).Decode(&simulateRandom)
	if err != nil {
		return errors.WithStack(err)
	}

	cursor.All(nil, &simulateSubjects)
	var randomList models.RandomList
	if len(simulateSubjects) > 0 {
		tools.Database.Collection("random_list").FindOne(nil, bson.M{"_id": simulateSubjects[0].RandomListID}).Decode(&randomList)
	}
	title := []interface{}{
		locales.Tr(ctx, "simulated.random.list.runCount"),
		locales.Tr(ctx, "simulated.random.list.subject"),
	}
	if len(randomList.Design.Factors) > 0 {
		for _, factor := range randomList.Design.Factors {
			title = append(title, fmt.Sprintf("%s(%s)", locales.Tr(ctx, "simulated.random.list.factor"), factor.Label))
		}
	}
	title = append(title, locales.Tr(ctx, "simulated.random.list.group"))
	title = append(title, locales.Tr(ctx, "simulated.random.list.number"))
	title = append(title, locales.Tr(ctx, "simulated.random.list.site"))

	var context [][]interface{}
	for _, subject := range simulateSubjects {
		var tmp []interface{}
		tmp = append(tmp, strconv.Itoa(subject.RunCount))
		tmp = append(tmp, subject.Name)
		if len(subject.Info) > 1 {
			for _, factor := range subject.Info {
				if factor.Name != "shortname" {
					for _, text := range randomList.Design.Factors {
						if factor.Name == text.Name {
							for _, option := range text.Options {
								if option.Value == factor.Value {
									tmp = append(tmp, option.Label)
								}
							}
						}
					}
				}
			}
		}
		tmp = append(tmp, subject.GroupName)
		tmp = append(tmp, subject.RandomNumber)
		tmp = append(tmp, subject.SiteName)
		context = append(context, tmp)
	}

	var projectInfo []map[string]interface{}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": simulateRandom.ProjectID}}},
		{{Key: "$unwind", Value: "$envs"}},
		{{Key: "$match", Value: bson.M{"envs.id": simulateRandom.EnvironmentID}}},
		{
			{
				Key: "$project",
				Value: bson.M{
					"_id":      0,
					"number":   "$info.number",
					"name":     "$info.name",
					"env":      "$envs.name",
					"timeZone": "$info.timeZoneStr",
				},
			},
		},
	}
	cursor, err = tools.Database.Collection("project").Aggregate(nil, pipeline)
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &projectInfo)
	if err != nil {
		return errors.WithStack(err)
	}

	var timeZone float64
	if projectInfo[0]["timeZone"] != nil {
		//timeZone = projectInfo[0]["timeZone"].(int32)
		timeZone, _ = strconv.ParseFloat(projectInfo[0]["timeZone"].(string), 64)
	} else {
		timeZone = float64(8)
	}
	hours := time.Duration(timeZone)
	minutes := time.Duration((timeZone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute

	number := ""
	env := ""
	if len(projectInfo) > 0 {
		number = projectInfo[0]["number"].(string)
		env = projectInfo[0]["env"].(string)
	}

	now := time.Now().UTC().Add(duration).Format("20060102")
	fileName := fmt.Sprintf("%s[%s]-%s-%s.xlsx", number, env, locales.Tr(ctx, "simulated.random.list.name"), now)

	err = tools.ExportExcelStream(ctx, fileName, title, context)
	if err != nil {
		return err
	}
	return nil
}

func (s *SimulateRandomService) GetFactor(ctx *gin.Context) (interface{}, error) {
	envID := ctx.Query("envId")
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortID := ctx.Query("cohortId")

	filter := bson.M{"env_id": envOID}
	if cohortID != "" {
		cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
		if !cohortOID.IsZero() {
			filter["cohort_id"] = cohortOID
		}
	}
	var randomDesign models.RandomDesign
	err := tools.Database.Collection("random_design").FindOne(nil, filter).Decode(&randomDesign)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	factors := slice.Filter(randomDesign.Info.Factors, func(index int, factor models.RandomFactor) bool {
		if factor.Status == nil {
			return true
		}
		return *factor.Status != 2
	})
	return factors, nil
}
