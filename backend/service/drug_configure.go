package service

import (
	"clinflash-irt/database"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/task"
	"clinflash-irt/tools"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/wxnacy/wgo/arrays"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DrugConfigureService struct {
	medicineService MedicineService
}

func (s *DrugConfigureService) ListVisit(ctx *gin.Context, customerID string, projectID string, envID string, cohortID string, roleID string) (models.VisitCycle, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if cohortID != "" && cohortOID != primitive.NilObjectID {
		filter["cohort_id"] = cohortOID
	}

	var visitCycle models.VisitCycle
	var attribute models.Attribute
	err := tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute)
	if err != nil {
		return visitCycle, errors.WithStack(err)
	}
	var randomDesign models.RandomDesign
	err = tools.Database.Collection("random_design").FindOne(nil, filter).Decode(&randomDesign)
	if err != nil && err != mongo.ErrNoDocuments {
		return visitCycle, errors.WithStack(err)
	}
	isBlindedRole, err := tools.IsBlindedRole(roleID)
	err = tools.Database.Collection("visit_cycle").FindOne(nil, filter).Decode(&visitCycle)
	if err != nil && err != mongo.ErrNoDocuments {
		return models.VisitCycle{}, errors.WithStack(err)
	}
	if isBlindedRole {
		blinds := make([]models.GroupBlind, 0)
		for _, group := range randomDesign.Info.Groups {
			if group.SubGroup != nil && len(group.SubGroup) > 0 {
				for _, subGroup := range group.SubGroup {
					blinds = append(blinds, models.GroupBlind{
						Group:    group.Name + " " + subGroup.Name,
						ParName:  group.Name,
						SubName:  subGroup.Name,
						SubBlind: subGroup.Blind,
					})
				}
			} else {
				blinds = append(blinds, models.GroupBlind{
					Group:    group.Name,
					ParName:  group.Name,
					SubName:  "",
					SubBlind: false,
				})
			}
		}
		for i, info := range visitCycle.Infos {
			for j := range info.Group {
				gP, b := slice.Find(blinds, func(index int, item models.GroupBlind) bool {
					return item.Group == visitCycle.Infos[i].Group[j]
				})
				if b {
					g := *gP
					if g.SubName != "" {
						if attribute.AttributeInfo.Blind && g.SubBlind {
							visitCycle.Infos[i].Group[j] = tools.BlindData + " " + tools.BlindData
						} else if attribute.AttributeInfo.Blind && !g.SubBlind {
							visitCycle.Infos[i].Group[j] = tools.BlindData + " " + g.SubName
						} else if !attribute.AttributeInfo.Blind && g.SubBlind {
							visitCycle.Infos[i].Group[j] = g.ParName + " " + tools.BlindData
						}
					} else {
						if attribute.AttributeInfo.Blind {
							visitCycle.Infos[i].Group[j] = tools.BlindData
						}
					}
				} else {
					if attribute.AttributeInfo.Blind {
						visitCycle.Infos[i].Group[j] = tools.BlindData
					}
				}
			}
		}
		for i, info := range visitCycle.HistoryInfo {
			for j, cycleInfo := range info.Infos {
				for n := range cycleInfo.Group {
					gP, b := slice.Find(blinds, func(index int, item models.GroupBlind) bool {
						return item.Group == visitCycle.HistoryInfo[i].Infos[j].Group[n]
					})
					if b {
						g := *gP
						if g.SubName != "" {
							if attribute.AttributeInfo.Blind && g.SubBlind {
								visitCycle.HistoryInfo[i].Infos[j].Group[n] = tools.BlindData + " " + tools.BlindData
							} else if attribute.AttributeInfo.Blind && !g.SubBlind {
								visitCycle.HistoryInfo[i].Infos[j].Group[n] = tools.BlindData + " " + g.SubName
							} else if !attribute.AttributeInfo.Blind && g.SubBlind {
								visitCycle.HistoryInfo[i].Infos[j].Group[n] = g.ParName + " " + tools.BlindData
							}
						} else {
							if attribute.AttributeInfo.Blind {
								visitCycle.HistoryInfo[i].Infos[j].Group[n] = tools.BlindData
							}
						}
					} else {
						if attribute.AttributeInfo.Blind {
							visitCycle.HistoryInfo[i].Infos[j].Group[n] = tools.BlindData
						}
					}
				}
			}
		}
		for i, info := range visitCycle.ConfigInfo.Infos {
			for n := range info.Group {
				gP, b := slice.Find(blinds, func(index int, item models.GroupBlind) bool {
					return item.Group == visitCycle.ConfigInfo.Infos[i].Group[n]
				})
				if b {
					g := *gP
					if g.SubName != "" {
						if attribute.AttributeInfo.Blind && g.SubBlind {
							visitCycle.ConfigInfo.Infos[i].Group[n] = tools.BlindData + " " + tools.BlindData
						} else if attribute.AttributeInfo.Blind && !g.SubBlind {
							visitCycle.ConfigInfo.Infos[i].Group[n] = tools.BlindData + " " + g.SubName
						} else if !attribute.AttributeInfo.Blind && g.SubBlind {
							visitCycle.ConfigInfo.Infos[i].Group[n] = g.ParName + " " + tools.BlindData
						}
					} else {
						if attribute.AttributeInfo.Blind {
							visitCycle.ConfigInfo.Infos[i].Group[n] = tools.BlindData
						}
					}
				} else {
					if attribute.AttributeInfo.Blind {
						visitCycle.ConfigInfo.Infos[i].Group[n] = tools.BlindData
					}
				}
			}
		}
	}
	return visitCycle, nil
}

func (s *DrugConfigureService) DeleteVisit(ctx *gin.Context, id string, visitID string, visitName string, envID string) error {
	envOID, _ := primitive.ObjectIDFromHex(envID)
	visitOID, _ := primitive.ObjectIDFromHex(visitID)
	OID, _ := primitive.ObjectIDFromHex(id)
	filter := bson.M{"_id": OID}
	update := bson.M{
		"$pull": bson.M{
			"update_infos.infos": bson.M{
				"id": visitOID,
			},
		},
		"$set": bson.M{"update_infos.meta.updated_at": time.Duration(time.Now().Unix())},
	}
	callback := func(sctx mongo.SessionContext) (interface{}, error) {

		// 校验访视是否发药过 随机过
		err := checkDispensingVisit(ctx, sctx, OID, visitOID)
		if err != nil {
			return nil, err
		}

		_, err = tools.Database.Collection("visit_cycle").UpdateOne(sctx, filter, update)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		var visitCycle models.VisitCycle
		err = tools.Database.Collection("visit_cycle").FindOne(sctx, bson.M{"_id": OID}).Decode(&visitCycle)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		var OperationLogFieldGroups []models.OperationLogFieldGroup
		marks := []models.Mark{}
		marks = append(marks, models.Mark{
			Label: "operation_log.label.visitCycle",
			Value: visitName,
			Blind: false,
		})
		logOID := envOID
		if !visitCycle.CohortID.IsZero() {
			logOID = visitCycle.CohortID
		}
		err = tools.SaveOperation(ctx, sctx, "operation_log.module.visitCycle", logOID, 3, OperationLogFieldGroups, marks, OID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil

}

func checkDispensingVisit(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, visitOID primitive.ObjectID) error {
	var visitCycle models.VisitCycle
	err := tools.Database.Collection("visit_cycle").FindOne(sctx, bson.M{"_id": OID}).Decode(&visitCycle)
	if err != nil {
		return errors.WithStack(err)
	}
	infos := models.VisitCycleInfo{}
	for _, info := range visitCycle.ConfigInfo.Infos {
		if info.ID == visitOID {
			infos = info
			break
		}
	}
	if infos.Random {
		// 查询是否已经有随机的受试者 已经有随机的不给删除修改
		documents, err := tools.Database.Collection("subject").CountDocuments(sctx, bson.M{"group": bson.M{"$ne": ""}, "env_id": visitCycle.EnvironmentID, "cohort_id": visitCycle.CohortID, "status": bson.M{"$in": bson.A{3, 4, 5, 6}}})
		if err != nil {
			return errors.WithStack(err)
		}
		if documents > 0 {
			return tools.BuildServerError(ctx, "visit_cycle_duplicated_random3")

		}
	} else {
		// 查询是否已经有发药的访视 已经有发药的不给删除修改
		documents, err := tools.Database.Collection("dispensing").CountDocuments(sctx, bson.M{"env_id": visitCycle.EnvironmentID, "cohort_id": visitCycle.CohortID, "visit_info.visit_cycle_info_id": visitOID, "status": bson.M{"$ne": 1}})
		if err != nil {
			return errors.WithStack(err)
		}
		if documents > 0 {
			return tools.BuildServerError(ctx, "visit_cycle_duplicated_random3")
		}
	}

	return nil
}

func (s *DrugConfigureService) UpdateVisit(ctx *gin.Context, projectID string, envID string, id string, visitID string, visitCycle models.VisitInfos) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		projectOID, _ := primitive.ObjectIDFromHex(projectID)
		envOID, _ := primitive.ObjectIDFromHex(envID)
		OID, _ := primitive.ObjectIDFromHex(id)
		visitOID, _ := primitive.ObjectIDFromHex(visitID)
		filter := bson.M{"_id": OID}
		visitCycleInfo := visitCycle.Infos[0]
		//判断该访视编号是否已存在
		var visitCycles models.VisitCycle
		err := tools.Database.Collection("visit_cycle").FindOne(sctx, bson.M{"_id": OID}).Decode(&visitCycles)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		logOID := envOID
		if !visitCycles.CohortID.IsZero() {
			logOID = visitCycles.CohortID

		}
		var oldVisitCycleInfo models.VisitCycleInfo
		oldVisitCycleInfoP, _ := slice.Find(visitCycles.ConfigInfo.Infos, func(index int, item models.VisitCycleInfo) bool {
			return item.ID == visitOID
		})
		if oldVisitCycleInfoP != nil {
			oldVisitCycleInfo = *oldVisitCycleInfoP
		}

		_, ok := slice.Find(visitCycles.ConfigInfo.Infos, func(index int, item models.VisitCycleInfo) bool {
			return visitCycleInfo.Number == item.Number && item.ID != visitOID
		})
		if ok {
			return nil, tools.BuildServerError(ctx, "visit_cycle_duplicated_number")
		}

		if visitCycleInfo.DTP && len(visitCycleInfo.DTPType) == 0 {
			return nil, tools.BuildServerError(ctx, "visit_cycle_dtp_required")
		}
		group := []string{}
		oldGroup := []string{}
		for _, item := range visitCycleInfo.Group {
			group = append(group, item.(string))
		}
		for _, item := range oldVisitCycleInfo.Group {
			oldGroup = append(oldGroup, item.(string))
		}
		sort.Strings(group)
		sort.Strings(oldGroup)

		// 校验是否存在随机发药过的访视 校验  访视编号、访视名称、组别、允许随机 不允许修改
		if visitCycleInfo.Number != oldVisitCycleInfo.Number ||
			visitCycleInfo.Name != oldVisitCycleInfo.Name ||
			visitCycleInfo.Random != oldVisitCycleInfo.Random ||
			strings.Join(group, "") != strings.Join(oldGroup, "") {
			err = checkDispensingVisit(ctx, sctx, OID, visitOID)
			if err != nil {
				return nil, err
			}
		}

		var drugConfigure models.DrugConfigure
		err = tools.Database.Collection("drug_configure").FindOne(nil, bson.M{"env_id": envOID, "cohort_id": visitCycles.CohortID}).Decode(&drugConfigure)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		err = checkDrugConfigureVisit(ctx, drugConfigure, visitCycleInfo)
		if err != nil {
			return nil, err
		}
		//查询项目信息，获取项目类型，验证是否随机
		var project models.Project
		projectOpts := &options.FindOneOptions{
			Projection: bson.M{"meta": 0},
		}
		if err := tools.Database.Collection("project").FindOne(sctx, bson.M{"_id": projectOID}, projectOpts).Decode(&project); err != nil {
			return nil, errors.WithStack(err)
		}
		//projectType := project.Type
		isRandomFilter := bson.M{"_id": OID, "project_id": projectOID, "env_id": envOID}
		pipeline := mongo.Pipeline{
			{{Key: "$match", Value: isRandomFilter}},
			{{
				Key: "$project",
				Value: bson.M{
					"update_infos.infos": bson.M{
						"$filter": bson.M{
							"input": "$update_infos.infos",
							"as":    "infos",
							"cond":  bson.M{"$and": bson.A{bson.M{"$eq": bson.A{"$$infos.random", true}}, bson.M{"$ne": bson.A{"$$infos.id", visitOID}}}},
						},
					},
				},
			}},
			{{Key: "$unwind", Value: "$update_infos.infos"}},
		}
		var data []map[string]interface{}
		cursor, err := tools.Database.Collection("visit_cycle").Aggregate(sctx, pipeline)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(sctx, &data)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		isRandomCount := len(data)
		if visitCycleInfo.Random {
			isRandomCount++
		}

		if isRandomCount > 1 {
			return nil, tools.BuildServerError(ctx, "visit_cycle_duplicated_random1")
		}
		//
		//if projectType == 3 { //再随机
		//	//可以设置2条随机访视
		//	if isRandomCount > 2 {
		//		return nil, tools.BuildServerError(ctx, "visit_cycle_duplicated_random2")
		//	}
		//} else {
		//	//可以设置1条随机访视
		//
		//}

		update := bson.M{
			"$set": bson.M{
				"update_infos.infos.$[info]":   visitCycleInfo,
				"update_infos.meta.updated_at": time.Duration(time.Now().Unix()),
			},
		}

		opts := &options.UpdateOptions{
			ArrayFilters: &options.ArrayFilters{
				Filters: bson.A{
					bson.M{"info.id": visitOID},
				},
			},
		}

		queryPipeline := mongo.Pipeline{
			{{Key: "$match", Value: isRandomFilter}},
			{{Key: "$unwind", Value: "$update_infos.infos"}},
			{{Key: "$match", Value: bson.M{"update_infos.infos.id": visitOID}}},
			{{"$project", bson.M{"info": "$update_infos.infos"}}},
		}
		visitCursor, err := tools.Database.Collection("visit_cycle").Aggregate(nil, queryPipeline)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		type ConfigInfo struct {
			ConfigInfo models.VisitCycleInfo `bson:"info"` // 配置页面上的访视 以及历史保存生效的

		}
		var visitData []ConfigInfo
		err = visitCursor.All(nil, &visitData)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var oldVisitInfo models.VisitCycleInfo
		oldInfos := visitData[0]
		if !oldInfos.ConfigInfo.ID.IsZero() {
			interval := oldInfos.ConfigInfo.Interval
			period_min := oldInfos.ConfigInfo.PeriodMin
			period_max := oldInfos.ConfigInfo.PeriodMax
			unit := oldInfos.ConfigInfo.Unit
			oldVisitInfo = models.VisitCycleInfo{
				Number:         oldInfos.ConfigInfo.Number,
				Name:           oldInfos.ConfigInfo.Name,
				Dispensing:     oldInfos.ConfigInfo.Dispensing,
				Random:         oldInfos.ConfigInfo.Random,
				EndDays:        oldInfos.ConfigInfo.EndDays,
				StartDays:      oldInfos.ConfigInfo.StartDays,
				Interval:       interval,
				PeriodMin:      period_min,
				PeriodMax:      period_max,
				Unit:           unit,
				Group:          oldInfos.ConfigInfo.Group,
				DTPType:        oldInfos.ConfigInfo.DTPType,
				DTP:            oldInfos.ConfigInfo.DTP,
				Replace:        oldInfos.ConfigInfo.Replace,
				DoseAdjustment: oldInfos.ConfigInfo.DoseAdjustment,
			}
		}

		_, err = tools.Database.Collection("visit_cycle").UpdateOne(sctx, filter, update, opts)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		insertVisitCycleLog(ctx, sctx, logOID, 2, oldVisitInfo, visitCycleInfo, OID, project, "")
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

// insertVisitSettingLog 访视设置
func insertVisitSettingLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, old models.VisitSetting, new models.VisitSetting, operID primitive.ObjectID, copyEnv string) error {
	// 保存项目日志
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	if types != 3 {
		if copyEnv != "" {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.visitSetting",
				TranKey: "operation_log.visitSetting.env_name",
				Old: models.OperationLogField{
					Type:  2,
					Value: nil,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: copyEnv,
				},
			})
		}

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitSetting",
			TranKey: "operation_log.visitSetting.unscheduled_visit",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.IsOpen,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.IsOpen,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitSetting",
			TranKey: "operation_log.visitSetting.name_zh",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.NameZh,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.NameZh,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitSetting",
			TranKey: "operation_log.visitSetting.name_en",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.NameEn,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.NameEn,
			},
		})

	}
	marks := []models.Mark{}

	err := tools.SaveOperation(ctx, sctx, "operation_log.module.visitSetting", OID, types, OperationLogFieldGroups, marks, operID)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func insertVisitCycleLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, old models.VisitCycleInfo, new models.VisitCycleInfo, operID primitive.ObjectID, project models.Project, copyEnv string) error {
	// 保存项目日志
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	if types != 3 {
		if copyEnv != "" {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.visitCycle",
				TranKey: "operation_log.visitCycle.env_name",
				Old: models.OperationLogField{
					Type:  2,
					Value: nil,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: copyEnv,
				},
			})
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitCycle",
			TranKey: "operation_log.visitCycle.number",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.Number,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.Number,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitCycle",
			TranKey: "operation_log.visitCycle.name",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.Name,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.Name,
			},
		})
		oldGroup := []string{}
		for _, item := range old.Group {
			oldGroup = append(oldGroup, item.(string))
		}
		newGroup := []string{}
		for _, item := range new.Group {
			newGroup = append(newGroup, item.(string))
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitCycle",
			TranKey: "operation_log.visitCycle.group",
			Old: models.OperationLogField{
				Type:  12,
				Value: strings.Join(oldGroup, ","),
			},
			New: models.OperationLogField{
				Type:  12,
				Value: strings.Join(newGroup, ","),
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitCycle",
			TranKey: "operation_log.visitCycle.interval",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.Interval,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.Interval,
			},
		})
		oldPeriodMin := ""
		oldPeriodMax := ""
		newPeriodMin := ""
		newPeriodMax := ""
		if old.PeriodMin != nil {
			oldPeriodMin = convertor.ToString(*old.PeriodMin)
		}
		if old.PeriodMax != nil {
			oldPeriodMax = convertor.ToString(*old.PeriodMax)
		}
		if new.PeriodMin != nil {
			newPeriodMin = convertor.ToString(*new.PeriodMin)
		}
		if new.PeriodMax != nil {
			newPeriodMax = convertor.ToString(*new.PeriodMax)
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitCycle",
			TranKey: "operation_log.visitCycle.period",
			Old: models.OperationLogField{
				Type:  2,
				Value: oldPeriodMin + "~" + oldPeriodMax,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newPeriodMin + "~" + newPeriodMax,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitCycle",
			TranKey: "operation_log.visitCycle.random",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.Random,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.Random,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitCycle",
			TranKey: "operation_log.visitCycle.dispensing",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.Dispensing,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.Dispensing,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitCycle",
			TranKey: "operation_log.visitCycle.dtp",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.DTP,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.DTP,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitCycle",
			TranKey: "operation_log.visitCycle.replace",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.Replace,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.Replace,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitCycle",
			TranKey: "operation_log.visitCycle.DTPMode",
			Old: models.OperationLogField{
				Type:  5,
				Value: old.DTPType,
			},
			New: models.OperationLogField{
				Type:  5,
				Value: new.DTPType,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitCycle",
			TranKey: "operation_log.visitCycle.doseAdjustment",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.DoseAdjustment,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.DoseAdjustment,
			},
		})

	}
	marks := []models.Mark{}
	if types != 1 {
		marks = append(marks,
			models.Mark{
				Label: "operation_log.label.visitCycle",
				Value: new.Name,
				Blind: false,
			})
	}

	err := tools.SaveOperation(ctx, sctx, "operation_log.module.visitCycle", OID, types, OperationLogFieldGroups, marks, operID)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (s *DrugConfigureService) AddVisit(ctx *gin.Context, customerID string, projectID string, envID primitive.ObjectID, cohortID primitive.ObjectID, visitCycleInfo models.VisitCycleInfo, types int) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		customerOID, _ := primitive.ObjectIDFromHex(customerID)
		projectOID, _ := primitive.ObjectIDFromHex(projectID)
		collection := tools.Database.Collection("visit_cycle")
		filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envID}
		var project models.Project
		projectErr := tools.Database.Collection("project").FindOne(sctx, bson.M{"_id": projectOID}).Decode(&project)
		if projectErr != nil {
			return errors.WithStack(projectErr), nil
		}
		OID := envID
		if !cohortID.IsZero() {
			filter["cohort_id"] = cohortID
			OID = cohortID
		}

		var visitCycle models.VisitCycle
		err := collection.FindOne(nil, filter).Decode(&visitCycle)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		var drugConfigure models.DrugConfigure
		err = tools.Database.Collection("drug_configure").FindOne(nil, filter).Decode(&drugConfigure)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		err = checkDrugConfigureVisit(ctx, drugConfigure, visitCycleInfo)
		if err != nil {
			return nil, err
		}
		//判断访视是否存在，如果已存在，push field
		visitCycleInfo.ID = primitive.NewObjectID()
		if count, _ := collection.CountDocuments(sctx, filter); count > 0 {
			//判断该访视编号是否已存在
			checkFilter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envID, "update_infos.infos.number": visitCycleInfo.Number}
			if !cohortID.IsZero() {
				checkFilter["cohort_id"] = cohortID
			}
			if checkNumber, _ := collection.CountDocuments(sctx, checkFilter); checkNumber > 0 {
				return nil, tools.BuildServerError(ctx, "visit_cycle_duplicated_number")
			}

			if visitCycleInfo.DTP && len(visitCycleInfo.DTPType) == 0 {
				return nil, tools.BuildServerError(ctx, "visit_cycle_dtp_required")
			}

			err := checkVisitRandom(ctx, sctx, projectOID, envID, cohortID, visitCycleInfo)
			if err != nil {
				return nil, err
			}
			visitCycleInfo.ID = primitive.NewObjectID()
			update := bson.M{
				"$push": bson.M{
					"update_infos.infos": visitCycleInfo,
				},
				"$set": bson.M{"update_infos.meta.updated_at": time.Duration(time.Now().Unix())},
			}
			if len(visitCycle.ConfigInfo.Infos) == 0 {
				update = bson.M{
					"$set": bson.M{"update_infos.meta.updated_at": time.Duration(time.Now().Unix()), "update_infos.infos": []interface{}{visitCycleInfo}},
				}
			}

			_, err = collection.UpdateOne(sctx, filter, update)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = insertVisitCycleLog(ctx, sctx, OID, types, models.VisitCycleInfo{}, visitCycleInfo, visitCycle.ID, project, "")
			if err != nil {
				return nil, err
			}

		} else {
			id := primitive.NewObjectID()
			visitCycle := models.VisitCycle{
				ID:            id,
				CustomerID:    customerOID,
				ProjectID:     projectOID,
				EnvironmentID: envID,
				ConfigInfo: models.VisitInfos{
					Infos: []models.VisitCycleInfo{visitCycleInfo},
					Meta: models.Meta{
						CreatedAt: time.Duration(time.Now().Unix()),
						UpdatedAt: time.Duration(time.Now().Unix()),
					},
				},
			}
			if !cohortID.IsZero() {
				visitCycle.CohortID = cohortID
			}
			if _, err := collection.InsertOne(sctx, visitCycle); err != nil {
				return nil, errors.WithStack(err)
			}
			err := insertVisitCycleLog(ctx, sctx, OID, 1, models.VisitCycleInfo{}, visitCycleInfo, id, project, "")
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *DrugConfigureService) GetVisitDtpRule(ctx *gin.Context, customerID string, projectID string, envID string, cohortID string) (int, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)

	filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if cohortID != "" {
		filter["cohort_id"] = cohortOID
	}

	var attribute models.Attribute
	err := tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute)
	if err != nil {
		return 0, errors.WithStack(err)
	}

	return attribute.AttributeInfo.DtpRule, nil
}

func checkVisitRandom(ctx *gin.Context, sctx mongo.SessionContext, projectOID, envID, cohortID primitive.ObjectID, visitCycleInfo models.VisitCycleInfo) error {
	//查询项目信息，获取项目类型，验证是否随机
	var project models.Project
	opts := &options.FindOneOptions{
		Projection: bson.M{"meta": 0},
	}
	if err := tools.Database.Collection("project").FindOne(sctx, bson.M{"_id": projectOID}, opts).Decode(&project); err != nil {
		return errors.WithStack(err)
	}
	//projectType := project.Type
	isRandomFilter := bson.M{"customer_id": project.CustomerID, "project_id": projectOID, "env_id": envID}
	if !cohortID.IsZero() {
		isRandomFilter["cohort_id"] = cohortID
	}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: isRandomFilter}},
		{{
			Key: "$project",
			Value: bson.M{
				"update_infos.infos": bson.M{
					"$filter": bson.M{
						"input": "$update_infos.infos",
						"as":    "infos",
						"cond":  bson.M{"$eq": bson.A{"$$infos.random", true}},
					},
				},
			},
		}},
		{{Key: "$unwind", Value: "$update_infos.infos"}},
	}
	var data []map[string]interface{}
	cursor, err := tools.Database.Collection("visit_cycle").Aggregate(sctx, pipeline)
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(sctx, &data)
	if err != nil {
		return errors.WithStack(err)
	}
	isRandomCount := len(data)
	if visitCycleInfo.Random {
		isRandomCount++
	}
	if isRandomCount > 1 {
		return tools.BuildServerError(ctx, "visit_cycle_duplicated_random1")
	}
	//if projectType == 3 { //再随机
	//	//可以设置2条随机访视
	//	if isRandomCount > 2 {
	//		return tools.BuildServerError(ctx, "visit_cycle_duplicated_random2")
	//	}
	//} else {
	//	//可以设置1条随机访视
	//
	//}
	return nil
}

func (s *DrugConfigureService) List(ctx *gin.Context, customerID string, envID string, cohortID string, roleId string) (models.DrugConfigure, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	filter := bson.M{"customer_id": customerOID, "env_id": envOID}
	if cohortID != "" {
		filter = bson.M{"customer_id": customerOID, "env_id": envOID, "cohort_id": cohortOID}
	}
	var drugConfigure models.DrugConfigure
	err := tools.Database.Collection("drug_configure").FindOne(nil, filter).Decode(&drugConfigure)
	if err != nil && err != mongo.ErrNoDocuments {
		return drugConfigure, errors.WithStack(err)
	}
	var visitCycle models.VisitCycle
	err = tools.Database.Collection("visit_cycle").FindOne(nil, filter).Decode(&visitCycle)
	if err != nil && err != mongo.ErrNoDocuments {
		return drugConfigure, errors.WithStack(err)
	}

	var randomDesign models.RandomDesign
	err = tools.Database.Collection("random_design").FindOne(nil, filter).Decode(&randomDesign)
	if err != nil && err != mongo.ErrNoDocuments {
		return drugConfigure, errors.WithStack(err)
	}
	if roleId != "" {
		var attribute models.Attribute
		err := tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute)
		if err != nil && err != mongo.ErrNoDocuments {
			return models.DrugConfigure{}, err
		}

		isBlindedRole, err := tools.IsBlindedRole(roleId)
		if err != nil {
			return models.DrugConfigure{}, err
		}
		for i := 0; i < len(drugConfigure.Configures); i++ {
			for j := 0; j < len(drugConfigure.Configures[i].Values); j++ {
				drugName := drugConfigure.Configures[i].Values[j].DrugName
				isBlindedDrug, _ := tools.IsBlindedDrug(envOID, drugName)
				if isBlindedDrug && isBlindedRole {
					drugConfigure.Configures[i].Values[j].DrugName = tools.BlindData
				}
				if drugConfigure.Configures[i].Values[j].CustomDispensingNumber == "" {
					drugConfigure.Configures[i].Values[j].CustomDispensingNumber = convertor.ToString(drugConfigure.Configures[i].Values[j].DispensingNumber)
				}
			}
			drugConfigure.Configures[i].VisitCycles = slice.Filter(drugConfigure.Configures[i].VisitCycles, func(index int, ID primitive.ObjectID) bool {
				_, ok := slice.Find(visitCycle.Infos, func(index int, item models.VisitCycleInfo) bool {
					return item.ID == ID
				})
				return ok || ID == visitCycle.SetInfo.Id
			})
		}
		type subGroup struct {
			group    string
			subGroup string
			blind    bool
		}
		blindSubGroups := make([]subGroup, 0)
		for _, group := range randomDesign.Info.Groups {
			if group.SubGroup != nil && len(group.SubGroup) > 0 {
				for _, sg := range group.SubGroup {
					blindSubGroups = append(blindSubGroups, subGroup{
						group:    group.Name,
						subGroup: sg.Name,
						blind:    sg.Blind,
					})
				}
			}
		}

		for i, configure := range drugConfigure.Configures {
			if configure.SubName != "" {
				sP, b := slice.Find(blindSubGroups, func(index int, item subGroup) bool {
					return item.group == configure.ParName && item.subGroup == configure.SubName
				})
				if isBlindedRole && b {
					ss := *sP
					if attribute.AttributeInfo.Blind && ss.blind {
						drugConfigure.Configures[i].Group = tools.BlindData + " " + tools.BlindData
						drugConfigure.Configures[i].ParName = tools.BlindData
						drugConfigure.Configures[i].SubName = tools.BlindData
					} else if !attribute.AttributeInfo.Blind && ss.blind {
						drugConfigure.Configures[i].Group = configure.ParName + " " + tools.BlindData
						drugConfigure.Configures[i].ParName = configure.ParName
						drugConfigure.Configures[i].SubName = tools.BlindData
					} else if attribute.AttributeInfo.Blind && !ss.blind {
						drugConfigure.Configures[i].Group = tools.BlindData + " " + configure.SubName
						drugConfigure.Configures[i].ParName = tools.BlindData
						drugConfigure.Configures[i].SubName = configure.SubName
					}
				}
			} else {
				if attribute.AttributeInfo.Blind && isBlindedRole {
					for i := 0; i < len(drugConfigure.Configures); i++ {
						drugConfigure.Configures[i].Group = tools.BlindData
						drugConfigure.Configures[i].ParName = tools.BlindData
					}
				} else {
					for i := 0; i < len(drugConfigure.Configures); i++ {
						if drugConfigure.Configures[i].ParName == "" {
							drugConfigure.Configures[i].ParName = drugConfigure.Configures[i].Group
						}
					}
				}
			}
		}

		if attribute.AttributeInfo.Blind {
			isBlindedRoomRole, err := tools.IsBlindedRoomRole(roleId)
			if err != nil {
				return models.DrugConfigure{}, err
			}
			if isBlindedRoomRole {
				for i := 0; i < len(drugConfigure.Configures); i++ {
					drugConfigure.Configures[i].Group = tools.BlindData
					for j := 0; j < len(drugConfigure.Configures[i].RoomNumbers); j++ {
						drugConfigure.Configures[i].RoomNumbers[j] = tools.BlindData
					}
				}
			}
		}

	}

	if drugConfigure.Configures != nil && len(drugConfigure.Configures) > 0 {
		drugConfigureInfoList := make([]models.DrugConfigureInfo, 0)
		for _, configure := range drugConfigure.Configures {
			plaintext := []byte(configure.ID.Hex())
			aes := tools.ShuffleString(plaintext)
			// Base64编码
			encryptedData := base64.StdEncoding.EncodeToString(aes)
			configure.OnlyID = encryptedData
			drugConfigureInfoList = append(drugConfigureInfoList, configure)
		}
		drugConfigure.Configures = drugConfigureInfoList
	}

	return drugConfigure, nil
}

func (s *DrugConfigureService) Delete(ctx *gin.Context, id string, configureID string, envID string, name string) error {
	configureOID, _ := primitive.ObjectIDFromHex(configureID)
	OID, _ := primitive.ObjectIDFromHex(id)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	filter := bson.M{"_id": OID}

	drugConfigureData := make([]models.UpdateDrugConfigure, 0)
	queryPipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$unwind", Value: "$configures"}},
		{{Key: "$match", Value: bson.M{"configures.id": configureOID}}},
	}
	drugConfigureCursor, drugErr := tools.Database.Collection("drug_configure").Aggregate(nil, queryPipeline)
	if drugErr != nil {
		return errors.WithStack(drugErr)
	}
	drugErr = drugConfigureCursor.All(nil, &drugConfigureData)
	if drugErr != nil {
		return errors.WithStack(drugErr)
	}

	//判断是否已经有库存
	var drugConfigures []models.DrugConfigure
	cursor, _ := tools.Database.Collection("drug_configure").Find(nil, bson.M{"env_id": envOID})
	err := cursor.All(nil, &drugConfigures)
	if err != nil {
		return errors.WithStack(err)
	}

	existDrugNames := make(map[string]string, 0)
	for _, drugConfig := range drugConfigures {
		configures := drugConfig.Configures
		for _, configure := range configures {
			if configure.ID != configureOID {
				for _, v := range configure.Values {
					existDrugNames[v.DrugName] = v.DrugName
				}
			}
		}
	}

	for _, drugConfigure := range drugConfigureData {
		configures := drugConfigure.Configures
		valuse := configures.Values
		for _, v := range valuse {
			//判断其它配置里面是否有该药物
			_, exist := existDrugNames[v.DrugName]
			if !exist {
				if v.IsOther { //未编号
					total, err := tools.Database.Collection("medicine_others").CountDocuments(ctx, bson.M{
						"name":   v.DrugName,
						"env_id": envOID,
					})
					if err != nil {
						return errors.WithStack(err)
					}
					if total > 0 {
						return tools.BuildServerError(ctx, "medicine_drug_configure_check")
					}
				} else { //有编号
					medicineFilter := bson.M{"env_id": envOID, "name": v.DrugName}
					total, err := tools.Database.Collection("medicine").CountDocuments(ctx, medicineFilter)
					if err != nil {
						return errors.WithStack(err)
					}
					if total > 0 {
						return tools.BuildServerError(ctx, "medicine_drug_configure_check")
					}
				}
			}
		}
	}

	update := bson.M{
		"$pull": bson.M{
			"configures": bson.M{
				"id": configureOID,
			},
		},
	}
	_, err = tools.Database.Collection("drug_configure").UpdateOne(nil, filter, update)
	if err != nil {
		return errors.WithStack(err)
	}
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.drug_configure",
		TranKey: "operation_log.drug_configure.onlyID",
		Old: models.OperationLogField{
			Type:  2,
			Value: configureOID,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: nil,
		},
	})
	marks := []models.Mark{}
	marks = append(marks, models.Mark{
		Label: "operation_log.label.drugConfigure",
		Value: name,
		Blind: true,
	})
	err = tools.SaveOperation(ctx, nil, "operation_log.module.drug_configure", envOID, 3, OperationLogFieldGroups, marks, OID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *DrugConfigureService) GetDrugPackageConfigure(ctx *gin.Context, customerID string, projectID string, envID string) (models.DrugPackageConfigureShow, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	var drugPackageConfigure models.DrugPackageConfigureShow
	_ = tools.Database.Collection("drug_package_configure").FindOne(nil, filter).Decode(&drugPackageConfigure)

	//判断有对应产品的订单产生，比值锁定不可再修改
	if len(drugPackageConfigure.MixedPackage) > 0 {
		var packageDrugNames []string
		for _, mixedPackage := range drugPackageConfigure.MixedPackage {
			for _, packageConfig := range mixedPackage.PackageConfig {
				packageDrugNames = append(packageDrugNames, packageConfig.Name)
			}
		}

		//判断药物是否已经生成订单
		match := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "status": bson.M{"$ne": 5},
			"medicines_package": bson.M{
				"$elemMatch": bson.M{
					"name":           bson.M{"$in": packageDrugNames},
					"package_method": true,
				},
			},
		}
		queryPipeline := mongo.Pipeline{
			{{Key: "$match", Value: match}},
			{{Key: "$unwind", Value: "$medicines_package"}},
			{{Key: "$match", Value: bson.M{"medicines_package.package_method": true}}},
			{{Key: "$group", Value: bson.M{"_id": "$medicines_package.name"}}},
			{{Key: "$project", Value: bson.M{"_id": 0, "id": "$_id"}}},
		}
		medicineOrderCursor, medicineOrderErr := tools.Database.Collection("medicine_order").Aggregate(nil, queryPipeline)
		if medicineOrderErr != nil {
			return drugPackageConfigure, errors.WithStack(medicineOrderErr)
		}
		var medicineOrderData []map[string]string
		medicineOrderErr = medicineOrderCursor.All(nil, &medicineOrderData)
		if medicineOrderErr != nil {
			return drugPackageConfigure, errors.WithStack(medicineOrderErr)
		}

		orderDrugs := make(map[string]bool)
		for _, orderDrug := range medicineOrderData {
			drugName := orderDrug["id"]
			orderDrugs[drugName] = true
		}

		var packageConfigs []models.MixedPackageShow
		for _, mixedPackage := range drugPackageConfigure.MixedPackage {
			mixedPackage.Locked = "false"
			for _, packageConfig := range mixedPackage.PackageConfig {
				if orderDrugs[packageConfig.Name] {
					mixedPackage.Locked = "true"
				}
			}
			packageConfigs = append(packageConfigs, mixedPackage)
		}

		//按照药物有效期批次号排序
		sort.SliceStable(packageConfigs, func(i int, j int) bool {
			groupDatai := packageConfigs[i]
			groupDataj := packageConfigs[j]
			return groupDatai.Locked > groupDataj.Locked
		})

		drugPackageConfigure.MixedPackage = packageConfigs

	}

	return drugPackageConfigure, nil
}

func (s *DrugConfigureService) GetDrugPackageIsOpen(ctx *gin.Context, envID string) (bool, error) {
	envOID, _ := primitive.ObjectIDFromHex(envID)

	isOpenPackage, _, _, _, _, _ := tools.IsOpenPackage(envOID)

	return isOpenPackage, nil
}

func (s *DrugConfigureService) UpdatePackage(ctx *gin.Context, updatePackage models.DrugPackageConfigure) (map[string]interface{}, error) {
	OID := updatePackage.ID
	var drugPackageConfigure models.DrugPackageConfigure
	if OID != primitive.NilObjectID {
		_ = tools.Database.Collection("drug_package_configure").FindOne(nil, bson.M{"_id": OID}).Decode(&drugPackageConfigure)
	}

	//包装运输打开时，判断配置中的药物是否已经在 研究产品列表中上传
	//新增的配置,研究列表中是否已经上传数据
	uploaded := false
	//是否有新增的配置
	addSetting := false
	addHave := false
	//是否有新增的配置
	delSetting := false
	//删除的配置,研究列表中是否已经上传数据
	delUploaded := false
	//判断原有的值，比值修改了且有数据
	specific := false
	otherSpecific := ""
	otherSpecificCount := 0
	//研究产品列表存在未上传药物列表的研究产品配置
	needUpload := false

	//未编号研究产品
	isOtherDrugMap, err := tools.IsOtherDrugMap(updatePackage.EnvironmentID)
	if err != nil {
		return map[string]interface{}{"addSetting": addSetting, "uploaded": uploaded, "delSetting": delSetting, "delUploaded": delUploaded, "specific": specific, "addHave": addHave, "needUpload": needUpload}, errors.WithStack(err)
	}

	if updatePackage.IsOpen {
		//对比修改前和修改后的配置产品
		var newMedicineNames []string
		updateConfigure := make(map[string]int)
		oldConfigure := make(map[string]int)
		for _, mixedPackage := range updatePackage.MixedPackage {
			newMedicineNamesPackage := ""
			number := 0
			for _, packageConfig := range mixedPackage.PackageConfig {
				number = number + packageConfig.Number
			}
			for _, packageConfig := range mixedPackage.PackageConfig {
				newMedicineNamesPackage = newMedicineNamesPackage + fmt.Sprintf("%s;", packageConfig.Name)
				updateConfigure[packageConfig.Name] = number
			}
			newMedicineNames = append(newMedicineNames, newMedicineNamesPackage)
		}
		var oldMedicineNames []string
		for _, mixedPackage := range drugPackageConfigure.MixedPackage {
			oldMedicineNamesPackage := ""
			for _, packageConfig := range mixedPackage.PackageConfig {
				oldMedicineNamesPackage = oldMedicineNamesPackage + fmt.Sprintf("%s;", packageConfig.Name)
				oldConfigure[packageConfig.Name] = packageConfig.Number
			}
			oldMedicineNames = append(oldMedicineNames, oldMedicineNamesPackage)
		}

		//新增包装
		addMedicinePackage := slice.Difference(newMedicineNames, oldMedicineNames)
		//减少包装
		delMedicinePackage := slice.Difference(oldMedicineNames, newMedicineNames)

		if len(addMedicinePackage) > 0 {
			for _, addMedicine := range addMedicinePackage {
				var QName []string
				names := strings.Split(addMedicine, ";")
				for _, name := range names {
					if name != "" {
						QName = append(QName, name)
					}
				}
				if len(QName) == 1 && isOtherDrugMap[QName[0]] { //判断是否是未编号
					drugName := QName[0]
					newPackageNumber := updateConfigure[drugName]
					var groupOtherMedicineData []map[string]interface{}
					//判断药物列表是否有数据
					groupPipeline := mongo.Pipeline{
						{{Key: "$match", Value: bson.M{"project_id": updatePackage.ProjectID, "env_id": updatePackage.EnvironmentID, "name": drugName, "status": 1, "storehouse_id": bson.M{"$nin": [2]interface{}{nil, ""}}}}},
						{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "batchNumber": "$batch_number", "expirationDate": "$expiration_date", "storehouseId": "$storehouse_id"}, "availableCount": bson.M{"$sum": 1}, "otherIds": bson.M{"$push": "$_id"}}}},
					}
					cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, groupPipeline)
					err = cursor.All(ctx, &groupOtherMedicineData)
					if err != nil {
						return map[string]interface{}{"addSetting": addSetting, "uploaded": uploaded, "delSetting": delSetting, "delUploaded": delUploaded, "specific": specific, "addHave": addHave, "needUpload": needUpload}, errors.WithStack(err)
					}
					for _, groupOther := range groupOtherMedicineData {
						other := groupOther["_id"].(map[string]interface{})
						availableCount := groupOther["availableCount"].(int32)
						if int(availableCount)%newPackageNumber != 0 {
							infoOtherName := fmt.Sprintf("%s/%s/%s", other["name"], other["batchNumber"], other["expirationDate"])
							errorInfo := fmt.Sprintf("%s%s%s%s%v%s", locales.Tr(ctx, "common.update.failed"), locales.Tr(ctx, "medicine_drug_configure_other_check2"), infoOtherName, locales.Tr(ctx, "medicine_drug_configure_other_check"), groupOther["availableCount"].(int32), locales.Tr(ctx, "medicine_drug_configure_other_check1"))
							return nil, tools.BuildCustomError(errorInfo)
						}
					}
					for _, groupOther := range groupOtherMedicineData {
						var updateIds []primitive.ObjectID
						otherIds := groupOther["otherIds"].(primitive.A)
						for _, id := range otherIds {
							updateIds = append(updateIds, id.(primitive.ObjectID))
						}
						//如果包装开关打开，查询项目环境下最大的包装号
						packageNumbers, err := s.GetPackageNumber(ctx, updatePackage.CustomerID, updatePackage.ProjectID, updatePackage.EnvironmentID, len(updateIds)/newPackageNumber, newPackageNumber)
						if err != nil {
							return map[string]interface{}{"addSetting": addSetting, "uploaded": uploaded, "delSetting": delSetting, "delUploaded": delUploaded, "specific": specific, "addHave": addHave, "needUpload": needUpload}, errors.WithStack(err)
						}
						for index, packageNumber := range packageNumbers {
							packageUpdateIds := updateIds[index*newPackageNumber : (index+1)*newPackageNumber]
							medicineFilter := bson.M{"_id": bson.M{"$in": packageUpdateIds}}
							medicineUpdate := bson.M{
								"$set": bson.M{
									"package_number":        packageNumber,
									"package_serial_number": packageNumber,
								},
							}
							if _, err := tools.Database.Collection("medicine_others").UpdateMany(nil, medicineFilter, medicineUpdate); err != nil {
								return map[string]interface{}{"addSetting": addSetting, "uploaded": uploaded, "delSetting": delSetting, "delUploaded": delUploaded, "specific": specific, "addHave": addHave, "needUpload": needUpload}, errors.WithStack(err)
							}
						}

					}
				} else {
					addSetting = true
					var count []map[string]interface{}
					countPipeline := mongo.Pipeline{
						{{Key: "$match", Value: bson.M{"project_id": updatePackage.ProjectID, "env_id": updatePackage.EnvironmentID, "name": bson.M{"$in": QName}, "package_number": bson.M{"$nin": [2]interface{}{nil, ""}}}}},
						{{Key: "$group", Value: bson.M{"_id": bson.M{"package_number": "$package_number"}, "availableCount": bson.M{"$sum": 1}}}},
						{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$availableCount", updateConfigure[addMedicine]}}}}},
						{{Key: "$count", Value: "count"}},
					}
					cursor, err := tools.Database.Collection("medicine").Aggregate(ctx, countPipeline)
					if err != nil {
						return map[string]interface{}{"addSetting": addSetting, "uploaded": uploaded, "delSetting": delSetting, "delUploaded": delUploaded, "specific": specific, "addHave": addHave, "needUpload": needUpload}, errors.WithStack(err)
					}
					err = cursor.All(nil, &count)
					if err != nil {
						return map[string]interface{}{"addSetting": addSetting, "uploaded": uploaded, "delSetting": delSetting, "delUploaded": delUploaded, "specific": specific, "addHave": addHave, "needUpload": needUpload}, errors.WithStack(err)
					}
					total := 0
					if len(count) > 0 {
						total = int(count[0]["count"].(int32))
						if total > 0 {
							uploaded = true
						}
					} else {
						filter := bson.M{"project_id": updatePackage.ProjectID, "env_id": updatePackage.EnvironmentID, "name": bson.M{"$in": QName}}
						total, err := tools.Database.Collection("medicine").CountDocuments(nil, filter)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						if total > 0 {
							addHave = true
						}
					}
				}

			}
		}

		if len(delMedicinePackage) > 0 {
			var delName []string
			for _, v := range delMedicinePackage {
				names := strings.Split(v, ";")
				for _, name := range names {
					if name != "" {
						delName = append(delName, name)
					}
				}
			}
			delSetting = true
			countPipeline := mongo.Pipeline{
				{{Key: "$match", Value: bson.M{"project_id": updatePackage.ProjectID, "env_id": updatePackage.EnvironmentID, "name": bson.M{"$in": delName}, "package_number": bson.M{"$nin": [2]interface{}{nil, ""}}}}},
				{{Key: "$count", Value: "count"}},
			}
			cursor, err := tools.Database.Collection("medicine").Aggregate(nil, countPipeline)
			var count []map[string]interface{}
			err = cursor.All(nil, &count)
			if err != nil {
				return map[string]interface{}{"addSetting": addSetting, "uploaded": uploaded, "delSetting": delSetting, "delUploaded": delUploaded, "specific": specific, "addHave": addHave, "needUpload": needUpload}, errors.WithStack(err)
			}
			total := 0
			if len(count) > 0 {
				total = int(count[0]["count"].(int32))
				if total > 0 {
					delUploaded = true
				}
			}
		}

		//判断原有的值，比值是否修改
		if len(addMedicinePackage) == 0 && len(delMedicinePackage) == 0 { //说明配置的研究产品没动，只是修改了包装的数量
			for drugName, newCount := range updateConfigure {
				oldCount, _ := oldConfigure[drugName]
				if newCount != oldCount {
					//判断是否是未编号研究产品，如果是自动编号研究产品，自动更新库存
					isOtherDrug, _ := isOtherDrugMap[drugName]
					if isOtherDrug {
						var groupOtherMedicineData []map[string]interface{}
						//判断药物列表是否有数据
						groupPipeline := mongo.Pipeline{
							{{Key: "$match", Value: bson.M{"project_id": updatePackage.ProjectID, "env_id": updatePackage.EnvironmentID, "name": drugName, "status": 1, "storehouse_id": bson.M{"$nin": [2]interface{}{nil, ""}}}}},
							{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "batchNumber": "$batch_number", "expirationDate": "$expiration_date", "storehouseId": "$storehouse_id"}, "availableCount": bson.M{"$sum": 1}, "otherIds": bson.M{"$push": "$_id"}}}},
						}
						cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, groupPipeline)
						err = cursor.All(ctx, &groupOtherMedicineData)
						if err != nil {
							return map[string]interface{}{"addSetting": addSetting, "uploaded": uploaded, "delSetting": delSetting, "delUploaded": delUploaded, "specific": specific, "addHave": addHave, "needUpload": needUpload}, errors.WithStack(err)
						}
						for _, groupOther := range groupOtherMedicineData {
							other := groupOther["_id"].(map[string]interface{})
							availableCount := groupOther["availableCount"].(int32)
							if int(availableCount)%newCount != 0 {
								infoOtherName := fmt.Sprintf("%s/%s/%s", other["name"], other["batchNumber"], other["expirationDate"])
								errorInfo := fmt.Sprintf("%s%s%s%s%v%s", locales.Tr(ctx, "common.update.failed"), locales.Tr(ctx, "medicine_drug_configure_other_check2"), infoOtherName, locales.Tr(ctx, "medicine_drug_configure_other_check"), groupOther["availableCount"].(int32), locales.Tr(ctx, "medicine_drug_configure_other_check1"))
								return nil, tools.BuildCustomError(errorInfo)
							}
						}
						for _, groupOther := range groupOtherMedicineData {
							var updateIds []primitive.ObjectID
							otherIds := groupOther["otherIds"].(primitive.A)
							for _, id := range otherIds {
								updateIds = append(updateIds, id.(primitive.ObjectID))
							}
							//other := groupOther["_id"].(map[string]interface{})
							// var otherMedicineData []map[string]interface{}
							// //判断药物列表是否有数据
							// countPipeline := mongo.Pipeline{
							// 	{{Key: "$match", Value: bson.M{"project_id": updatePackage.ProjectID, "env_id": updatePackage.EnvironmentID, "name": other["name"],
							// 		"storehouse_id": other["storehouseId"].(primitive.ObjectID), "batch_number": other["batchNumber"], "expiration_date": other["expirationDate"], "status": 1}}},
							// 	{{Key: "$group", Value: bson.M{"_id": bson.M{"package_number": "$package_number"}, "availableCount": bson.M{"$sum": 1}, "otherIds": bson.M{"$push": "$_id"}}}},
							// 	//{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$availableCount", oldCount}}}}},
							// }
							// cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, countPipeline)
							// err = cursor.All(nil, &otherMedicineData)
							// if err != nil {
							// 	return map[string]interface{}{"addSetting": addSetting, "uploaded": uploaded, "delSetting": delSetting, "delUploaded": delUploaded, "specific": specific, "addHave": addHave, "needUpload": needUpload}, errors.WithStack(err)
							// }
							// var updateIds []primitive.ObjectID
							// for _, otherMedicine := range otherMedicineData {
							// 	otherIds := otherMedicine["otherIds"].(primitive.A)
							// 	for _, id := range otherIds {
							// 		updateIds = append(updateIds, id.(primitive.ObjectID))
							// 	}
							// }
							// if len(updateIds)%newCount != 0 {
							// 	otherSpecificCount = len(updateIds)
							// 	return nil, tools.BuildCustomError(locales.Tr(ctx, "common.update.failed") + locales.Tr(ctx, "medicine_drug_configure_other_check2") + drugName + locales.Tr(ctx, "medicine_drug_configure_other_check") + strconv.Itoa(otherSpecificCount) + locales.Tr(ctx, "medicine_drug_configure_other_check1"))
							// } else {
							//如果包装开关打开，查询项目环境下最大的包装号
							packageNumbers, err := s.GetPackageNumber(ctx, updatePackage.CustomerID, updatePackage.ProjectID, updatePackage.EnvironmentID, len(updateIds)/newCount, newCount)
							if err != nil {
								return map[string]interface{}{"addSetting": addSetting, "uploaded": uploaded, "delSetting": delSetting, "delUploaded": delUploaded, "specific": specific, "addHave": addHave, "needUpload": needUpload}, errors.WithStack(err)
							}
							for index, packageNumber := range packageNumbers {
								packageUpdateIds := updateIds[index*newCount : (index+1)*newCount]
								medicineFilter := bson.M{"_id": bson.M{"$in": packageUpdateIds}}
								medicineUpdate := bson.M{
									"$set": bson.M{
										"package_number":        packageNumber,
										"package_serial_number": packageNumber,
									},
								}
								if _, err := tools.Database.Collection("medicine_others").UpdateMany(nil, medicineFilter, medicineUpdate); err != nil {
									return map[string]interface{}{"addSetting": addSetting, "uploaded": uploaded, "delSetting": delSetting, "delUploaded": delUploaded, "specific": specific, "addHave": addHave, "needUpload": needUpload}, errors.WithStack(err)
								}
							}
							// }
						}
					} else {
						var count []map[string]interface{}
						//判断药物列表是否有数据
						countPipeline := mongo.Pipeline{
							{{Key: "$match", Value: bson.M{"project_id": updatePackage.ProjectID, "env_id": updatePackage.EnvironmentID, "name": drugName}}},
							{{Key: "$count", Value: "count"}},
						}
						cursor, err := tools.Database.Collection("medicine").Aggregate(nil, countPipeline)
						err = cursor.All(nil, &count)
						if err != nil {
							return map[string]interface{}{"addSetting": addSetting, "uploaded": uploaded, "delSetting": delSetting, "delUploaded": delUploaded, "specific": specific, "addHave": addHave, "needUpload": needUpload}, errors.WithStack(err)
						}
						total := 0
						if len(count) > 0 {
							total = int(count[0]["count"].(int32))
							if total > 0 {
								specific = true
								break
							}
						} else {
							needUpload = true
							break
						}
					}
				}
			}
		}
	}

	operType := 2
	if drugPackageConfigure.ID != primitive.NilObjectID {
		update := bson.M{
			"$set": bson.M{
				"is_open":                  updatePackage.IsOpen,
				"mixed_package":            updatePackage.MixedPackage,
				"is_open_un_provide_date":  updatePackage.IsOpenUnProvideDate,
				"un_provide_date_config":   updatePackage.UnProvideDateConfig,
				"is_open_application":      updatePackage.IsOpenApplication,
				"is_supply_ratio":          updatePackage.SupplyRatio,
				"order_application_config": updatePackage.OrderApplicationConfig,
				"order_approval_name":      updatePackage.OrderApprovalName,
			},
		}
		if _, err := tools.Database.Collection("drug_package_configure").UpdateByID(nil, OID, update); err != nil {
			return map[string]interface{}{"addSetting": false, "uploaded": false, "delSetting": false, "delUploaded": false}, errors.WithStack(err)
		}
	} else {
		operType = 1
		drugPackageConfigure.CustomerID = updatePackage.CustomerID
		drugPackageConfigure.EnvironmentID = updatePackage.EnvironmentID
		drugPackageConfigure.ProjectID = updatePackage.ProjectID
		drugPackageConfigure.IsOpen = updatePackage.IsOpen
		drugPackageConfigure.MixedPackage = updatePackage.MixedPackage
		drugPackageConfigure.UnProvideDateConfig = updatePackage.UnProvideDateConfig
		drugPackageConfigure.IsOpenUnProvideDate = updatePackage.IsOpenUnProvideDate
		drugPackageConfigure.IsOpenApplication = updatePackage.IsOpenApplication
		drugPackageConfigure.SupplyRatio = updatePackage.SupplyRatio
		drugPackageConfigure.OrderApplicationConfig = updatePackage.OrderApplicationConfig
		drugPackageConfigure.ID = primitive.NewObjectID()
		if _, err := tools.Database.Collection("drug_package_configure").InsertOne(nil, drugPackageConfigure); err != nil {
			return map[string]interface{}{"addSetting": false, "uploaded": false, "delSetting": false, "delUploaded": false}, errors.WithStack(err)
		}
	}

	//保存项目日志
	logOID := updatePackage.EnvironmentID
	insertDrugPackageConfigureLog(ctx, nil, logOID, operType, drugPackageConfigure, updatePackage, OID)
	return map[string]interface{}{"addSetting": addSetting, "uploaded": uploaded, "delSetting": delSetting, "delUploaded": delUploaded, "specific": specific, "otherSpecific": otherSpecific, "otherSpecificCount": otherSpecificCount, "addHave": addHave, "needUpload": needUpload}, nil
}

func (s *DrugConfigureService) Update(ctx *gin.Context, updateInfo models.UpdateDrugConfigureInfo) error {
	OID := updateInfo.ID
	configureOID := updateInfo.ConfigureID
	filter := bson.M{"_id": OID}
	drugConfigureData := make([]models.UpdateDrugConfigure, 0)
	queryPipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$unwind", Value: "$configures"}},
		{{Key: "$match", Value: bson.M{"configures.id": configureOID}}},
	}
	drugConfigureCursor, drugErr := tools.Database.Collection("drug_configure").Aggregate(nil, queryPipeline)
	if drugErr != nil {
		return errors.WithStack(drugErr)
	}
	drugErr = drugConfigureCursor.All(nil, &drugConfigureData)
	if drugErr != nil {
		return errors.WithStack(drugErr)
	}

	envOID := drugConfigureData[0].EnvironmentID

	var visitCycle models.VisitCycle
	tools.Database.Collection("visit_cycle").FindOne(nil, bson.M{"env_id": drugConfigureData[0].EnvironmentID, "cohort_id": drugConfigureData[0].CohortID}).Decode(&visitCycle)
	err := checkDrugConfigureInfo(ctx, updateInfo.DrugConfigureInfo, visitCycle)
	if err != nil {
		return err
	}

	drugConfigureInfo := models.DrugConfigureInfo{}
	queryConfigure := drugConfigureData[0].Configures
	if !queryConfigure.ID.IsZero() {
		infoRoomNumbers := []string{}
		if queryConfigure.RoomNumbers != nil && len(queryConfigure.RoomNumbers) > 0 {
			roomNumbers := queryConfigure.RoomNumbers
			for _, roomNumber := range roomNumbers {
				infoRoomNumbers = append(infoRoomNumbers, roomNumber)
			}
		}

		var values []models.DrugValue
		for _, item := range queryConfigure.Values {
			var drugValue models.DrugValue
			name := item.DrugName
			number := item.DispensingNumber
			drugSpec := item.DrugSpec
			if item.PkgSpec != "" {
				pkgSpec := item.PkgSpec
				drugValue.PkgSpec = pkgSpec
			}
			drugValue.DrugName = name
			drugValue.DispensingNumber = int(number)
			drugValue.DrugSpec = drugSpec
			drugValue.CustomDispensingNumber = item.CustomDispensingNumber
			drugValue.IsOther = item.IsOther
			drugValue.IsCount = item.IsCount
			drugValue.IsOpen = item.IsOpen
			drugValue.CalculationInfo = item.CalculationInfo
			drugValue.Label = item.Label
			if queryConfigure.OpenSetting == 1 {
				drugValue.IsOpen = false
			}
			if queryConfigure.OpenSetting == 2 {
				drugValue.IsOpen = true
			}
			values = append(values, drugValue)
		}
		var infoVisitCycles []primitive.ObjectID
		visitCycles := queryConfigure.VisitCycles
		for _, visitCycle := range visitCycles {
			infoVisitCycles = append(infoVisitCycles, visitCycle)
		}
		drugConfigureInfo.ID = queryConfigure.ID
		drugConfigureInfo.VisitCycles = infoVisitCycles
		drugConfigureInfo.RoomNumbers = infoRoomNumbers
		drugConfigureInfo.Values = values
		drugConfigureInfo.Group = queryConfigure.Group
		drugConfigureInfo.ParName = queryConfigure.ParName
		drugConfigureInfo.SubName = queryConfigure.SubName
		drugConfigureInfo.Label = queryConfigure.Label
		drugConfigureInfo.OpenSetting = queryConfigure.OpenSetting
		drugConfigureInfo.CalculationType = queryConfigure.CalculationType
		drugConfigureInfo.CustomerCalculation = queryConfigure.CustomerCalculation
		drugConfigureInfo.CustomerCalculationSpec = queryConfigure.CustomerCalculationSpec
		drugConfigureInfo.IsFormula = queryConfigure.IsFormula
		drugConfigureInfo.KeepDecimal = queryConfigure.KeepDecimal
		drugConfigureInfo.RoutineVisitMappingList = queryConfigure.RoutineVisitMappingList
	}
	//
	var getDrugConfigure models.DrugConfigure
	err = tools.Database.Collection("drug_configure").FindOne(nil, filter).Decode(&getDrugConfigure)
	if err != nil {
		return err
	}
	configure := updateInfo.DrugConfigureInfo
	for i := range updateInfo.DrugConfigureInfo.Values {
		if updateInfo.DrugConfigureInfo.OpenSetting == 1 {
			updateInfo.DrugConfigureInfo.Values[i].IsOpen = false
		}
		if updateInfo.DrugConfigureInfo.OpenSetting == 2 {
			updateInfo.DrugConfigureInfo.Values[i].IsOpen = true
		}
	}
	//判断是否已经有库存
	var drugConfigures []models.DrugConfigure
	cursor, _ := tools.Database.Collection("drug_configure").Find(nil, bson.M{"env_id": envOID})
	err = cursor.All(nil, &drugConfigures)
	if err != nil {
		return errors.WithStack(err)
	}

	existDrugNames := make(map[string]string, 0)
	for _, drugConfig := range drugConfigures {
		configures := drugConfig.Configures
		for _, configure := range configures {
			if configure.ID != configureOID {
				for _, v := range configure.Values {
					existDrugNames[v.DrugName] = v.DrugName
				}
			}
		}
	}
	//编辑的时候，校验，研究产品是否删除或者修改名称

	for _, oldValue := range queryConfigure.Values {
		exist := false
		for _, newValue := range configure.Values {
			if oldValue.DrugName == newValue.DrugName {
				exist = true
				break
			}
		}
		//判断删除的研究产品，不存在，说明这个研究产品删除或者修改了名称
		if !exist {
			//判断其它配置中是否存在
			_, isOtherExist := existDrugNames[oldValue.DrugName]
			if !isOtherExist {
				if oldValue.IsOther { //未编号
					total, err := tools.Database.Collection("medicine_others").CountDocuments(ctx, bson.M{
						"name":   oldValue.DrugName,
						"env_id": envOID,
					})
					if err != nil {
						return errors.WithStack(err)
					}
					if total > 0 {
						return tools.BuildServerError(ctx, "medicine_drug_configure_check")
					}
				} else { //有编号
					medicineFilter := bson.M{"env_id": envOID, "name": oldValue.DrugName}
					total, err := tools.Database.Collection("medicine").CountDocuments(ctx, medicineFilter)
					if err != nil {
						return errors.WithStack(err)
					}
					if total > 0 {
						return tools.BuildServerError(ctx, "medicine_drug_configure_check")
					}
				}
			}
		}
	}

	config, err := checkUniqueDrugConfig(ctx, nil, filter, configure, getDrugConfigure.ProjectID, getDrugConfigure.EnvironmentID, getDrugConfigure.CohortID)
	if err != nil {
		return err
	}

	if !config {
		return tools.BuildServerError(ctx, "medicine_other_repeat")
	}

	// 公式计算校验
	var drugConfig models.DrugConfigure
	drugConfig.Configures = append(drugConfig.Configures, configure)
	err = checkFormulaUnique(ctx, getDrugConfigure.EnvironmentID, getDrugConfigure.CohortID, drugConfig)
	if err != nil {
		return err
	}

	// 开放药物标签校验
	err = checkOpenMedicineLabel(ctx, getDrugConfigure, configure)
	if err != nil {
		return err
	}

	routineVisitMappingList := make([]models.RoutineVisitMapping, 0)
	if configure.RoutineVisitMappingList != nil && len(configure.RoutineVisitMappingList) > 0 {
		for _, routineVisitMapping := range configure.RoutineVisitMappingList {
			rvm := routineVisitMapping
			if routineVisitMapping.ID == primitive.NilObjectID {
				rvm.ID = primitive.NewObjectID()
			}
			drugList := make([]models.Drug, 0)
			if routineVisitMapping.DrugList != nil && len(routineVisitMapping.DrugList) > 0 {
				for _, drug := range routineVisitMapping.DrugList {
					d := drug
					if drug.ID == primitive.NilObjectID {
						d.ID = primitive.NewObjectID()
					}
					drugList = append(drugList, d)
				}
			}
			rvm.DrugList = drugList
			routineVisitMappingList = append(routineVisitMappingList, rvm)
		}
	}
	configure.RoutineVisitMappingList = routineVisitMappingList

	update := bson.M{
		"$set": bson.M{
			"configures.$[configure]": configure,
		},
	}

	opts := &options.UpdateOptions{
		ArrayFilters: &options.ArrayFilters{
			Filters: bson.A{
				bson.M{"configure.id": configureOID},
			},
		},
	}

	_, updateErr := tools.Database.Collection("drug_configure").UpdateOne(nil, filter, update, opts)
	if updateErr != nil {
		return errors.WithStack(updateErr)
	}
	//更新研究产品配置操作轨迹
	var drugConfigure models.DrugConfigure
	fErr := tools.Database.Collection("drug_configure").FindOne(nil, filter).Decode(&drugConfigure)
	if fErr != nil {
		return errors.WithStack(fErr)
	}

	//保存项目日志
	logOID := drugConfigure.EnvironmentID
	if drugConfigure.CohortID != primitive.NilObjectID {
		logOID = drugConfigure.CohortID
	}
	drugErr = insertDrugConfigureLog(ctx, nil, logOID, 2, drugConfigureInfo, updateInfo.DrugConfigureInfo, OID, drugConfigure.EnvironmentID, drugConfigure.CohortID, "", models.VisitCycle{})
	if drugErr != nil {
		return errors.WithStack(fErr)
	}
	return nil
}

func (s *DrugConfigureService) UpdateDrugConfigureVerify(ctx *gin.Context, updateInfo models.UpdateDrugConfigureInfo) error {
	OID := updateInfo.ID
	configureOID := updateInfo.ConfigureID
	filter := bson.M{"_id": OID}
	drugConfigureData := make([]models.UpdateDrugConfigure, 0)
	queryPipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$unwind", Value: "$configures"}},
		{{Key: "$match", Value: bson.M{"configures.id": configureOID}}},
	}
	drugConfigureCursor, drugErr := tools.Database.Collection("drug_configure").Aggregate(nil, queryPipeline)
	if drugErr != nil {
		return errors.WithStack(drugErr)
	}
	drugErr = drugConfigureCursor.All(nil, &drugConfigureData)
	if drugErr != nil {
		return errors.WithStack(drugErr)
	}

	drugErr = verify(ctx, updateInfo.ConfigureID, drugConfigureData[0].CustomerID, drugConfigureData[0].ProjectID, drugConfigureData[0].EnvironmentID,
		drugConfigureData[0].CohortID, updateInfo.DrugConfigureInfo)
	if drugErr != nil {
		return errors.WithStack(drugErr)
	}

	return nil
}

// verify 【访视外发药配置】同一个组别+同一个研究产品+同一常规访视应该不能被多次配置，
func verify(ctx *gin.Context, id primitive.ObjectID, customerOID primitive.ObjectID, projectOID primitive.ObjectID, envOID primitive.ObjectID,
	cohortOID primitive.ObjectID, drugConfigureInfo models.DrugConfigureInfo) error {

	filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if cohortOID != primitive.NilObjectID {
		filter["cohort_id"] = cohortOID
	}

	var drugConfigure models.DrugConfigure
	err := tools.Database.Collection("drug_configure").FindOne(ctx, filter).Decode(&drugConfigure)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	routineVisitDrugNameList := make([]string, 0)
	if drugConfigure.Configures != nil && len(drugConfigure.Configures) > 0 {
		for _, configure := range drugConfigure.Configures {
			if id != primitive.NilObjectID && id == configure.ID {
				continue
			} else {
				if configure.Group == drugConfigureInfo.Group {
					if configure.RoutineVisitMappingList != nil && len(configure.RoutineVisitMappingList) > 0 {
						for _, mapping := range configure.RoutineVisitMappingList {
							if mapping.VisitList != nil && len(mapping.VisitList) > 0 {
								for _, visitId := range mapping.VisitList {
									if mapping.DrugList != nil && len(mapping.DrugList) > 0 {
										for _, drug := range mapping.DrugList {
											index := arrays.ContainsString(routineVisitDrugNameList, visitId.Hex()+drug.DrugName)
											if index == -1 {
												routineVisitDrugNameList = append(routineVisitDrugNameList, visitId.Hex()+drug.DrugName)
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	//visitDrugMap := make(map[string]models.VisitDrug)
	visitDrugList := make([]models.VisitDrug, 0)
	for _, visitMapping := range drugConfigureInfo.RoutineVisitMappingList {
		if visitMapping.VisitList != nil && len(visitMapping.VisitList) > 0 {
			for _, visitId := range visitMapping.VisitList {
				if visitMapping.DrugList != nil && len(visitMapping.DrugList) > 0 {
					for _, drug := range visitMapping.DrugList {
						index := arrays.ContainsString(routineVisitDrugNameList, visitId.Hex()+drug.DrugName)
						if index != -1 {
							visitDrug := models.VisitDrug{
								Group:    drugConfigureInfo.Group,
								VisitId:  visitId,
								DrugName: drug.DrugName,
							}
							//visitDrugMap[visitId.Hex()+drug.DrugName] = visitDrug
							visitDrugList = append(visitDrugList, visitDrug)
						}
					}
				}
			}
		}
		//每一组返回一次
		if visitDrugList != nil && len(visitDrugList) > 0 {

			var visitCycle models.VisitCycle
			err = tools.Database.Collection("visit_cycle").FindOne(nil, filter).Decode(&visitCycle)
			if err != nil && err != mongo.ErrNoDocuments {
				return errors.WithStack(err)
			}

			visitNameList := make([]string, 0)
			drugNameList := make([]string, 0)
			if visitCycle.Infos != nil && len(visitCycle.Infos) > 0 {
				for _, info := range visitCycle.Infos {
					for _, visitDrug := range visitDrugList {
						if visitDrug.VisitId == info.ID {
							visitNameList = append(visitNameList, "["+info.Name+"]")
							index := arrays.ContainsString(drugNameList, "["+visitDrug.DrugName+"]")
							if index == -1 {
								drugNameList = append(drugNameList, "["+visitDrug.DrugName+"]")
							}
						}
					}
				}
			}

			result := ""
			if locales.Lang(ctx) == "zh" {
				result = "\"" + drugConfigureInfo.Group + "\"下，常规访视映射中\"" + strings.Join(visitNameList, "") + "\"存在\"" + strings.Join(drugNameList, "") + "\"相同配置，请重新输入。"
			} else if locales.Lang(ctx) == "en" {
				result = "Common Visit Mapping \"" + strings.Join(visitNameList, "") + "\"has the \"" + strings.Join(drugNameList, "") + "\"same configuration under group\"" + drugConfigureInfo.Group + "\", please re-enter."
			}
			return tools.BuildCustomError(result)
			//return tools.BuildServerError(ctx, "medicine_other_repeat")
		}
	}

	return nil
}

func (s *DrugConfigureService) Add(ctx *gin.Context, drugConfigure models.DrugConfigure) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		customerOID := drugConfigure.CustomerID
		projectID := drugConfigure.ProjectID
		envOID := drugConfigure.EnvironmentID
		cohortID := drugConfigure.CohortID
		configure := drugConfigure.Configures[0]
		OID := envOID
		filter := bson.M{"customer_id": customerOID, "env_id": envOID}
		if !cohortID.IsZero() {
			filter["cohort_id"] = cohortID
			OID = cohortID
		}
		//判断研究产品配置是否存在，如果已存在，push field
		configure.ID = primitive.NewObjectID()
		if configure.VisitCycles == nil {
			configure.VisitCycles = []primitive.ObjectID{}
		}
		if configure.RoomNumbers == nil {
			configure.RoomNumbers = []string{}
		}

		var drugConfigureCohort models.DrugConfigure
		err := tools.Database.Collection("drug_configure").FindOne(sctx, filter).Decode(&drugConfigureCohort)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		// 校验唯一性 标签访视 组别 + 配置 + 房间号 唯一 、 开放药物 组别 + name 唯一
		config, err := checkUniqueDrugConfig(ctx, sctx, filter, configure, projectID, envOID, cohortID)
		if err != nil {
			return nil, err
		}
		if !config {
			return nil, tools.BuildServerError(ctx, "medicine_other_repeat")
		}

		// 公式计算校验
		err = checkFormulaUnique(ctx, envOID, cohortID, drugConfigure)
		if err != nil {
			return nil, err
		}

		// 开放药物标签校验
		err = checkOpenMedicineLabel(ctx, drugConfigureCohort, configure)
		if err != nil {
			return nil, err
		}

		var visitCycle models.VisitCycle
		tools.Database.Collection("visit_cycle").FindOne(nil, filter).Decode(&visitCycle)
		err = checkDrugConfigureInfo(ctx, configure, visitCycle)
		if err != nil {
			return nil, err
		}

		routineVisitMappingList := make([]models.RoutineVisitMapping, 0)
		if configure.RoutineVisitMappingList != nil && len(configure.RoutineVisitMappingList) > 0 {
			for _, routineVisitMapping := range configure.RoutineVisitMappingList {
				rvm := routineVisitMapping
				if routineVisitMapping.ID == primitive.NilObjectID {
					rvm.ID = primitive.NewObjectID()
				}
				drugList := make([]models.Drug, 0)
				if routineVisitMapping.DrugList != nil && len(routineVisitMapping.DrugList) > 0 {
					for _, drug := range routineVisitMapping.DrugList {
						d := drug
						if drug.ID == primitive.NilObjectID {
							d.ID = primitive.NewObjectID()
						}
						drugList = append(drugList, d)
					}
				}
				rvm.DrugList = drugList
				routineVisitMappingList = append(routineVisitMappingList, rvm)
			}
		}
		configure.RoutineVisitMappingList = routineVisitMappingList

		if count, _ := tools.Database.Collection("drug_configure").CountDocuments(sctx, filter); count > 0 {

			update := bson.M{
				"$push": bson.M{
					"configures": configure,
				},
			}
			_, err := tools.Database.Collection("drug_configure").UpdateOne(sctx, filter, update)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		} else {
			drugConfigure := models.DrugConfigure{
				ID:            primitive.NewObjectID(),
				CustomerID:    customerOID,
				ProjectID:     projectID,
				EnvironmentID: envOID,
				CohortID:      cohortID,
				Configures:    []models.DrugConfigureInfo{configure},
			}
			if !cohortID.IsZero() {
				drugConfigure.CohortID = cohortID
			}
			if _, err := tools.Database.Collection("drug_configure").InsertOne(sctx, drugConfigure); err != nil {
				return nil, errors.WithStack(err)
			}
		}

		err = insertDrugConfigureLog(ctx, nil, OID, 1, models.DrugConfigureInfo{}, configure, OID, envOID, cohortID, "", models.VisitCycle{})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}

	return nil
}

func (s *DrugConfigureService) AddDrugConfigureVerify(ctx *gin.Context, drugConfigure models.DrugConfigure) error {
	customerOID := drugConfigure.CustomerID
	projectOID := drugConfigure.ProjectID
	envOID := drugConfigure.EnvironmentID
	cohortOID := drugConfigure.CohortID
	configure := drugConfigure.Configures[0]
	filter := bson.M{"customer_id": customerOID, "env_id": envOID}
	if !cohortOID.IsZero() {
		filter["cohort_id"] = cohortOID
	}

	if count, _ := tools.Database.Collection("drug_configure").CountDocuments(ctx, filter); count > 0 {
		drugErr := verify(ctx, configure.ID, customerOID, projectOID, envOID,
			cohortOID, configure)
		if drugErr != nil {
			return errors.WithStack(drugErr)
		}
	}

	return nil
}

func checkOpenMedicineLabel(ctx *gin.Context, configures models.DrugConfigure, configure models.DrugConfigureInfo) error {
	if configure.OpenSetting == 2 {
		for _, value := range configure.Values {
			_, err := strconv.Atoi(value.CustomDispensingNumber)
			if err != nil && configure.Label != "" && len(configure.Values) > 1 {
				return tools.BuildServerError(ctx, "medicine_open_setting_repeat4")
			}
		}
		//for _, info := range configures.Configures {
		//	if info.ID == configure.ID {
		//		continue
		//	} else {
		//		for _, value := range info.Values {
		//			configureValueP, ok := slice.Find(configure.Values, func(index int, item models.DrugValue) bool {
		//				return item.DrugName == value.DrugName
		//			})
		//			if ok {
		//				configureValue := *configureValueP
		//				if info.OpenSetting == 2 {
		//					if (configure.Label == "" && info.Label != "") || (configure.Label != "" && info.Label == "") {
		//						return tools.BuildCustomError(locales.Tr(ctx, "medicine_open_setting_repeat5") + `""` + configureValue.DrugName + `"` + locales.Tr(ctx, "medicine_open_setting_repeat2"))
		//					}
		//				}
		//			}
		//		}
		//	}
		//}
	}

	return nil
}

func (s *DrugConfigureService) GetAppDrugNamesByRole(ctx *gin.Context) (map[string]interface{}, error) {
	workTaskID := ctx.Query("workTaskId")
	roleID := ctx.Query("roleId")
	workTaskOID, _ := primitive.ObjectIDFromHex(workTaskID)
	roleOID, _ := primitive.ObjectIDFromHex(roleID)
	var workTask models.WorkTask
	err := tools.Database.Collection("work_task").FindOne(nil, bson.M{"_id": workTaskOID}).Decode(&workTask)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return getDrugsByRole(workTask.CustomerID, workTask.EnvironmentID, workTask.CohortID, roleOID.Hex())
}

func getDrugsByRole(customerOID primitive.ObjectID, envOID primitive.ObjectID, cohortOID primitive.ObjectID, roleID string) (map[string]interface{}, error) {
	isBlindedRole := false
	if roleID != "" {
		isBlindedRole, _ = tools.IsBlindedRole(roleID)
	}
	filter := bson.M{"customer_id": customerOID, "env_id": envOID}
	// if cohortOID != primitive.NilObjectID {
	// 	filter = bson.M{"customer_id": customerOID, "env_id": envOID, "cohort_id": cohortOID}
	// }
	haveDrug := false
	haveOtherDrug := false

	var drugNames []map[string]interface{}
	drugSpecs := make(map[string][]string)
	drugSpecsMap := make(map[string]map[string]string)
	// var drugConfigure []models.DrugConfigure
	// if err := tools.Database.Collection("drug_configure").FindOne(nil, filter).Decode(&drugConfigure); err != nil {
	// 	return map[string]interface{}{"drugNames": drugNames, "drugSpecs": drugSpecs, "haveOtherDrug": haveOtherDrug, "haveDrug": haveDrug}, nil
	// }
	var drugConfigures []models.DrugConfigure
	cursor, _ := tools.Database.Collection("drug_configure").Find(nil, filter)
	err := cursor.All(nil, &drugConfigures)
	if err != nil {
		return map[string]interface{}{"drugNames": drugNames, "drugSpecs": drugSpecs, "haveOtherDrug": haveOtherDrug, "haveDrug": haveDrug}, nil
	}

	if len(drugConfigures) <= 0 {
		return map[string]interface{}{"drugNames": drugNames, "drugSpecs": drugSpecs, "haveOtherDrug": haveOtherDrug, "haveDrug": haveDrug}, nil
	}

	// var project models.Project
	// err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": drugConfigures[0].ProjectID}).Decode(&project)
	// if err != nil {
	// 	return nil, errors.WithStack(err)
	// }
	// envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
	// 	return item.ID == envOID
	// })
	// env := *envP
	for _, drugConfigure := range drugConfigures {
		// if project.Type != 1 {
		// 	_, ok := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
		// 		return (item.Status == 2 || item.Status == 5) && item.ID == drugConfigure.CohortID
		// 	})
		// 	if !ok {
		// 		continue
		// 	}
		// } else {
		// 	if !(*env.Status == 2 || *env.Status == 5) {
		// 		continue
		// 	}
		// }
		var configures = drugConfigure.Configures
		if configures != nil {
			for i := 0; i < len(configures); i++ {
				var configure = configures[i]
				var drugValues = configure.Values
				if drugValues != nil {
					for j := 0; j < len(drugValues); j++ {
						var drugName = drugValues[j].DrugName
						var drugSpec = drugValues[j].DrugSpec
						var isOther = drugValues[j].IsOther
						if isOther {
							haveOtherDrug = true
						} else {
							haveDrug = true
						}
						existDrugSpec, ok := drugSpecsMap[drugName]
						if ok { //已存在
							existDrugSpec[drugSpec] = drugSpec
						} else {
							specs := make(map[string]string)
							specs[drugSpec] = drugSpec
							drugSpecsMap[drugName] = specs
						}
					}
				}
			}
		}
	}

	//nameMap, _, err := matchCloseCohortMedicine(envOID)

	isBlindDrugMap, err := tools.IsBlindDrugMap(envOID)
	if err != nil {
		return map[string]interface{}{"drugNames": drugNames, "drugSpecs": drugSpecs, "haveOtherDrug": haveOtherDrug, "haveDrug": haveDrug}, nil
	}

	for drugName := range drugSpecsMap {
		//判断是否是盲态药物
		isBlindedDrug := isBlindDrugMap[drugName]
		processDrugName := ""
		drugName := drugName
		salt := ""
		// mixPage := nameMap[drugName]
		// if mixPage == 0 {
		// 	continue
		// }
		if isBlindedRole && isBlindedDrug {
			processDrugName, salt = tools.Encrypt(drugName)
			drugName = tools.BlindData
		}
		drugNames = append(drugNames, map[string]interface{}{
			"drugName":      drugName,
			"saltDrugName":  processDrugName,
			"salt":          salt,
			"isBlindedDrug": isBlindedDrug,
			//"mixPage":       mixPage,
		})
	}
	return map[string]interface{}{"drugNames": drugNames, "haveOtherDrug": haveOtherDrug, "haveDrug": haveDrug}, nil
}

func (s *DrugConfigureService) GetDrugNamesByType(ctx *gin.Context, customerID string, envID string, cohortID string, queryType string) (map[string]interface{}, error) {

	var singleDrugNames []string
	var packageDrugNames []string
	var blindDrugNames []string
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	filter := bson.M{"customer_id": customerOID, "env_id": envOID}
	if cohortOID != primitive.NilObjectID {
		filter = bson.M{"customer_id": customerOID, "env_id": envOID, "cohort_id": cohortOID}
	}

	drugSpecs := make(map[string][]string)
	drugSpecsMap := make(map[string]map[string]string)

	//盲态药物
	isBlindDrugMap, _ := tools.IsBlindDrugMap(envOID)
	if isBlindDrugMap != nil && len(isBlindDrugMap) > 0 {
		for key, _ := range isBlindDrugMap {
			blindDrugNames = append(blindDrugNames, key)
		}
	}

	opts := &options.FindOptions{Sort: bson.D{{"_id", 1}}}
	var drugConfigures []models.DrugConfigure
	cursor, _ := tools.Database.Collection("drug_configure").Find(nil, filter, opts)
	err := cursor.All(nil, &drugConfigures)
	if err != nil {
		return map[string]interface{}{"singleDrugNames": singleDrugNames, "packageDrugNames": packageDrugNames, "drugSpecs": drugSpecs, "blindDrugNames": blindDrugNames}, nil
	}

	//包装的研究产品
	isOpenPackage, packageAllDrugNames, _, _, mixedPackages, err := tools.IsOpenPackage(envOID)
	if err != nil {
		return map[string]interface{}{"singleDrugNames": singleDrugNames, "packageDrugNames": packageDrugNames, "drugSpecs": drugSpecs, "blindDrugNames": blindDrugNames}, nil
	}

	//未编号药物
	otherDrugNames := make(map[string]bool, 0)

	//单品
	for _, drugConfigure := range drugConfigures {
		var configures = drugConfigure.Configures
		if configures != nil {
			for i := 0; i < len(configures); i++ {
				var configure = configures[i]
				var drugValues = configure.Values
				if drugValues != nil {
					for j := 0; j < len(drugValues); j++ {
						if drugValues[j].IsOther {
							otherDrugNames[drugValues[j].DrugName] = drugValues[j].IsOther
						}
						//queryType == 2,查询
						if queryType == "2" && drugValues[j].IsOther {
							continue
						}
						var drugName = drugValues[j].DrugName
						var drugSpec = drugValues[j].DrugSpec
						existDrugSpec, ok := drugSpecsMap[drugName]
						if ok { //已存在
							existDrugSpec[drugSpec] = drugSpec
						} else {
							specs := make(map[string]string)
							specs[drugSpec] = drugSpec
							drugSpecsMap[drugName] = specs
							//判断是否是混合包装里面的配置药物
							_, isPackageDrug := packageAllDrugNames[drugName]
							if !isPackageDrug {
								singleDrugNames = append(singleDrugNames, drugName)
							}
						}
					}
				}
			}
			for drugName, specs := range drugSpecsMap {
				var specsV []string
				for _, v := range specs {
					specsV = append(specsV, v)
				}
				drugSpecs[drugName] = specsV
			}
		}
	}

	//包装配置
	if isOpenPackage && len(mixedPackages) > 0 {
		if len(mixedPackages) > 0 {
			for _, mixedPackage := range mixedPackages {
				var packageConfigNames []string
				var countinueFlag = false
				for _, packageConfig := range mixedPackage.PackageConfig {
					packageConfigNames = append(packageConfigNames, packageConfig.Name)
					if queryType == "2" && otherDrugNames[packageConfig.Name] {
						countinueFlag = true
					}
				}
				if countinueFlag {
					continue
				}
				packageDrugNames = append(packageDrugNames, strings.Join(packageConfigNames, "、"))
			}
		}
	}

	return map[string]interface{}{"singleDrugNames": singleDrugNames, "packageDrugNames": packageDrugNames, "drugSpecs": drugSpecs, "blindDrugNames": blindDrugNames}, nil
}

func (s *DrugConfigureService) GetDrugNamesByRole(ctx *gin.Context, customerID string, envID string, cohortID string, roleID string) (map[string]interface{}, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	return getDrugsByRole(customerOID, envOID, cohortOID, roleID)
}

func (s *DrugConfigureService) GetDrugNamesApp(ctx *gin.Context) (map[string]interface{}, error) {
	workTaskID := ctx.Query("workTaskId")
	workTaskOID, _ := primitive.ObjectIDFromHex(workTaskID)
	var workTask models.WorkTask
	err := tools.Database.Collection("work_task").FindOne(ctx, bson.M{"_id": workTaskOID}).Decode(&workTask)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	drugs, _ := getDrugs(workTask.CustomerID, workTask.EnvironmentID, workTask.CohortID, "0")
	return drugs, nil
}

func getDrugs(customerOID primitive.ObjectID, envOID primitive.ObjectID, cohortOID primitive.ObjectID, types string) (map[string]interface{}, error) {
	filter := bson.M{"customer_id": customerOID, "env_id": envOID}
	if cohortOID != primitive.NilObjectID {
		filter = bson.M{"customer_id": customerOID, "env_id": envOID, "cohort_id": cohortOID}
	}
	var drugNames []string
	var openDrugNames []string
	var otherDrugNames []string
	drugSpecs := make(map[string][]string)
	drugSpecsMap := make(map[string]map[string]string)

	opts := &options.FindOptions{Sort: bson.D{{"_id", 1}}}
	var drugConfigures []models.DrugConfigure
	cursor, _ := tools.Database.Collection("drug_configure").Find(nil, filter, opts)
	err := cursor.All(nil, &drugConfigures)
	if err != nil {
		return map[string]interface{}{"drugNames": drugNames, "drugSpecs": drugSpecs}, nil
	}

	var blindDrugNames []string
	blindDrug, _ := tools.IsBlindDrugMap(envOID)
	for key, _ := range blindDrug {
		blindDrugNames = append(blindDrugNames, key)
	}

	for _, drugConfigure := range drugConfigures {
		var configures = drugConfigure.Configures
		if configures != nil {
			for i := 0; i < len(configures); i++ {
				var configure = configures[i]
				var drugValues = configure.Values
				if drugValues != nil {
					for j := 0; j < len(drugValues); j++ {
						//types "1",只查询研究产品为“未编号”的研究产品
						if types == "1" && !drugValues[j].IsOther {
							continue
						}

						if types == "2" && drugValues[j].IsOther {
							continue
						}
						var drugName = drugValues[j].DrugName
						var drugSpec = drugValues[j].DrugSpec
						existDrugSpec, ok := drugSpecsMap[drugName]
						if ok { //已存在
							existDrugSpec[drugSpec] = drugSpec
						} else {
							specs := make(map[string]string)
							specs[drugSpec] = drugSpec
							drugSpecsMap[drugName] = specs
							drugNames = append(drugNames, drugName)
							if !drugValues[j].IsOther {
								openDrugNames = append(openDrugNames, drugName)
							} else {
								otherDrugNames = append(otherDrugNames, drugName)
							}
						}
					}
				}
			}
			for drugName, specs := range drugSpecsMap {
				var specsV []string
				for _, v := range specs {
					specsV = append(specsV, v)
				}
				drugSpecs[drugName] = specsV
			}
		}
	}

	return map[string]interface{}{"drugNames": drugNames, "drugSpecs": drugSpecs, "blindDrugNames": blindDrugNames, "openDrugNames": openDrugNames, "otherDrugNames": otherDrugNames}, nil
}

func (s *DrugConfigureService) GetDrugNames(ctx *gin.Context, customerID string, envID string, cohortID string, types string) (map[string]interface{}, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	return getDrugs(customerOID, envOID, cohortOID, types)
}

func (s *DrugConfigureService) ListOtherMedicine(ctx *gin.Context, customerID string, projectID string, envID string, roleID string, start int, limit int) (map[string]interface{}, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)

	//判断药物是否已经生成订单
	match := bson.M{"$and": bson.A{
		bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "status": 1},
		bson.M{"storehouse_id": bson.M{"$ne": nil}},
		bson.M{"storehouse_id": bson.M{"$ne": ""}},
		bson.M{"storehouse_id": bson.M{"$ne": primitive.NilObjectID}},
	}}
	//查询数据
	var data []map[string]interface{}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "batchNumber": "$batch_number", "expirationDate": "$expiration_date", "storehouseId": "$storehouse_id"},
			"packageNumbers": bson.M{"$addToSet": "$package_number"}, "count": bson.M{"$sum": 1}}}},
		{{Key: "$sort", Value: bson.D{{"_id", 1}}}},
		{{Key: "$project", Value: bson.M{"_id": 0,
			"storehouseId":   "$_id.storehouseId",
			"name":           "$_id.name",
			"batchNumber":    "$_id.batchNumber",
			"expirationDate": "$_id.expirationDate",
			"count":          1,
			"packageNumbers": bson.M{"$filter": bson.M{"input": "$packageNumbers", "as": "pNumbers", "cond": bson.M{"$ne": bson.A{"$$pNumbers", ""}}}},
			//"orderIds":       bson.M{"$filter": bson.M{"input": "$orderIds", "as": "hobby", "cond": bson.M{"$ne": bson.A{"$$hobby", primitive.NilObjectID}}}},
		}}},
		{{Key: "$project", Value: bson.M{
			"storehouseId":   1,
			"name":           1,
			"batchNumber":    1,
			"expirationDate": 1,
			"count":          1,
			"packageCount":   bson.M{"$size": "$packageNumbers"},
			//"orderIdsCount":  bson.M{"$size": "$orderIds"},
		}}},
		{{Key: "$sort", Value: bson.D{{"name", 1}, {"batchNumber", 1}, {"expirationDate", 1}}}},
	}
	cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	isBlindedRole := false
	if roleID != "" {
		isBlindedRole, _ = tools.IsBlindedRole(roleID)
	}
	isBlindDrugMap, err := tools.IsBlindDrugMap(envOID)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	end := start + limit
	if len(data) < end {
		end = len(data)
	}
	reslut := data[start:end]

	for _, otherMedicine := range reslut {
		otherIds := make([]primitive.ObjectID, 0)
		var otherDatas []map[string]interface{}
		pipeline := mongo.Pipeline{
			{{Key: "$match", Value: bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID,
				"name": otherMedicine["name"].(string), "expiration_date": otherMedicine["expirationDate"].(string), "batch_number": otherMedicine["batchNumber"].(string)}}},
			{{Key: "$project", Value: bson.M{
				"_id": 1,
			}}},
		}
		cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, pipeline)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &otherDatas)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		for _, data := range otherDatas {
			otherIds = append(otherIds, data["_id"].(primitive.ObjectID))
		}
		//判断药物是否已经生成订单，判断id是否存在medicine_order中
		orderMatch := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "other_medicines_new": bson.M{"$in": otherIds}}
		orderCount, err := tools.Database.Collection("medicine_order").CountDocuments(ctx, orderMatch)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		otherMedicine["orderIdsCount"] = orderCount

		//判断是否是盲态药物
		if isBlindedRole {
			isBlindedDrug := isBlindDrugMap[otherMedicine["name"].(string)]
			if isBlindedDrug {
				otherMedicine["name"] = tools.BlindData
			}
		}
	}

	// var reslut []map[string]interface{}
	// if data != nil && len(data) > 0 {
	// 	for _, m := range data {
	// 		// 尝试将 interface{} 类型的值转换为 primitive.ObjectID 类型
	// 		var objectID primitive.ObjectID
	// 		switch v := m["id"].(type) {
	// 		case primitive.ObjectID:
	// 			objectID = v
	// 			fmt.Println("ObjectID:", objectID)
	// 			plaintext := []byte(v.Hex())
	// 			aes := tools.ShuffleString(plaintext)
	// 			// Base64编码
	// 			encryptedData := base64.StdEncoding.EncodeToString(aes)
	// 			m["onlyId"] = encryptedData
	// 			reslut = append(reslut, m)
	// 		}
	// 	}

	// }

	packageIsOpen, _, _, _, _, _ := tools.IsOpenPackage(envOID)
	return map[string]interface{}{"isOpenPackage": packageIsOpen, "total": len(data), "items": reslut}, nil

}

func (s *DrugConfigureService) GetOtherMedicineList(ctx *gin.Context, orderInfo models.QueryOrderInfo) ([]map[string]interface{}, error) {
	var data []map[string]interface{}
	//混包药物，包装数量
	drugPackageCount := make(map[string]int)
	projectOID := orderInfo.ProjectID
	customerOID := orderInfo.CustomerID
	envOID := orderInfo.EnvID
	sendOID := orderInfo.SendID
	//供应计划
	supplyPlanOID := orderInfo.SupplyPlanID
	//补充方式
	mode := orderInfo.Mode
	//订单类型
	orderType := orderInfo.OrderType
	status := bson.A{1}
	if orderType == 3 || orderType == 4 {
		status = bson.A{1, 6, 7}
	}
	match := bson.M{"customer_id": customerOID, "env_id": envOID, "project_id": projectOID, "status": bson.M{"$in": status}}
	packageFilter := bson.M{"project_id": projectOID, "customer_id": customerOID, "env_id": envOID, "status": bson.M{"$in": status}, "package_number": bson.M{"$nin": [2]interface{}{nil, ""}}}
	sendId := "site_id"
	if sendOID != primitive.NilObjectID {
		//sendOId, _ := primitive.ObjectIDFromHex(sendId)
		//判断起运地是中心还是仓库
		storehouseMatch := bson.M{"_id": sendOID}
		storehouseCount, _ := tools.Database.Collection("project_storehouse").CountDocuments(nil, storehouseMatch)
		if storehouseCount > 0 {
			sendId = "storehouse_id"
			match["storehouse_id"] = sendOID
			packageFilter["storehouse_id"] = sendOID
		} else {
			match["site_id"] = sendOID
			packageFilter["site_id"] = sendOID
		}
	}

	isBlindedRole := true
	if orderInfo.RoleID != "" {
		isBlindedRole, _ = tools.IsBlindedRole(orderInfo.RoleID)
	}

	//查询哪些药物是按照包装运输
	packageIsOpen, _, packageDrugNames, packageConfigs, _, _ := tools.IsOpenPackage(envOID)
	var packDrugNames []string
	if packageIsOpen && len(packageDrugNames) > 0 {
		for key, _ := range packageDrugNames {
			packDrugNames = append(packDrugNames, key)
		}
		match["name"] = bson.M{"$nin": packDrugNames}
	}

	if len(orderInfo.DrugNames) > 0 {
		//match["name"] = bson.M{"$in": drugNames}
	} else {
		if orderInfo.Mode != 1 && orderInfo.Mode != 6 && orderInfo.OrderType == 1 {
			return data, nil
		}
	}

	if mode != 1 && mode != 6 {
		if len(orderInfo.DrugNames) > 0 {
			allDrugMap, err := tools.AllDrugMap(orderInfo.EnvID)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			drugNames := orderInfo.DrugNames
			for i, drugName := range orderInfo.DrugNames {
				_, ok := allDrugMap[drugName]
				if !ok && len(orderInfo.DrugNamesWithSalts) > 0 && orderInfo.DrugNamesWithSalts[i].SaltName != "" && orderInfo.DrugNamesWithSalts[i].Salt != "" {
					drugName = tools.Decrypt(orderInfo.DrugNamesWithSalts[i].SaltName, orderInfo.DrugNamesWithSalts[i].Salt)
				}
				drugNames[i] = drugName
			}

			//查询这个供应计划下，这个药物是否都设置了
			var supplyPlanMedicines []models.SupplyPlanMedicine
			supplyFilter := bson.M{
				"env_id":             orderInfo.EnvID,
				"customer_id":        orderInfo.CustomerID,
				"supply_plan_id":     supplyPlanOID,
				"info.medicine_name": bson.M{"$in": drugNames},
			}
			cursor, err := tools.Database.Collection("supply_plan_medicine").Find(nil, supplyFilter)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &supplyPlanMedicines)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			var mapSupply = make(map[string]models.SupplyPlanMedicine)
			for _, supply := range supplyPlanMedicines {
				mapSupply[supply.Medicine.MedicineName] = supply
			}

			matchInstitute := bson.M{"customer_id": orderInfo.CustomerID, "env_id": orderInfo.EnvID, "project_id": orderInfo.ProjectID}

			if orderInfo.ReceiveID != primitive.NilObjectID {
				//判断接受地是中心还是仓库
				storehouseMatch := bson.M{"_id": orderInfo.ReceiveID}
				storehouseCount, _ := tools.Database.Collection("project_storehouse").CountDocuments(nil, storehouseMatch)
				if storehouseCount > 0 {
					matchInstitute["storehouse_id"] = orderInfo.ReceiveID
				} else {
					matchInstitute["site_id"] = orderInfo.ReceiveID
				}
			}

			if packageIsOpen {
				for _, drugName := range drugNames {
					//如果该药物是按照包装发
					packageNumber, exist := packageDrugNames[drugName]
					if exist {
						count := 0
						//请确认选中的研究产品是否都在接收单位下设置了供应计划
						if _, ok := mapSupply[drugName]; ok {
							supplyPlanMedicine := mapSupply[drugName]
							switch mode {
							case 2: //再供应量
								count = supplyPlanMedicine.Medicine.SecondSupply
							case 3: //最大缓冲量
								//查询该中心现有库存量
								var bufferStatus = []int{1, 2, 3}
								existFilter := bson.M{
									"customer_id": customerOID,
									"env_id":      envOID,
									"site_id":     orderInfo.ReceiveID,
									"name":        drugName,
									"status":      bson.M{"$in": bufferStatus},
								}

								total, err := tools.Database.Collection("medicine_others").CountDocuments(ctx, existFilter)
								if err != nil {
									return nil, errors.WithStack(err)
								}
								count = supplyPlanMedicine.Medicine.Buffer - int(total)
							}
						}
						syCount := count / packageNumber
						if count%packageNumber > 0 {
							syCount = count/packageNumber + 1
						}
						drugPackageCount[drugName] = syCount
					}
				}
				for _, drugName := range drugNames {
					maxCount := drugPackageCount[drugName]
					durgNames := packageConfigs[drugName]
					for _, v := range durgNames {
						if maxCount < drugPackageCount[v] {
							maxCount = drugPackageCount[v]
							drugPackageCount[drugName] = maxCount
						}
					}
				}
			}

			otherDrugMap, _ := tools.IsOtherDrugMap(envOID)
			for _, drugName := range drugNames {
				//判断是否是未编号药物
				if otherDrugMap[drugName] {
					//请确认选中的研究产品是否都在接收单位下设置了供应计划
					if _, ok := mapSupply[drugName]; ok {
						supplyPlanMedicine := mapSupply[drugName]
						count := 0
						switch mode {
						case 2: //再供应量
							count = supplyPlanMedicine.Medicine.SecondSupply
						case 3: //最大缓冲量
							//计算库存数量
							matchInstitute["name"] = drugNames
							matchInstitute["status"] = bson.M{"$in": []int{1, 2, 3}}
							drugTotal, err := tools.Database.Collection("medicine_others").CountDocuments(nil, matchInstitute)
							if err != nil {
								return nil, errors.WithStack(err)
							}
							if supplyPlanMedicine.Medicine.Buffer > int(drugTotal) {
								count = supplyPlanMedicine.Medicine.Buffer - int(drugTotal)
							}
						}

						//供应计划，不配送天数
						timeZone, err := tools.GetTimeZone(projectOID)
						if err != nil {
							return nil, errors.WithStack(err)

						}
						unDistributionDate := supplyPlanMedicine.Medicine.UnDistributionDate
						hour := time.Duration(timeZone)
						minute := time.Duration((timeZone - float64(hour)) * 60)
						duration := hour*time.Hour + minute*time.Minute
						unDistributionDateStr := time.Now().AddDate(0, 0, unDistributionDate).UTC().Add(duration).Format("2006-01-02")

						queryFilter := bson.M{"project_id": projectOID, "customer_id": customerOID, "env_id": envOID, "status": 1}
						if sendOID != primitive.NilObjectID {
							queryFilter[sendId] = sendOID
						}
						queryFilter["name"] = drugName
						queryFilter["$or"] = bson.A{
							bson.M{"expiration_date": ""},
							bson.M{"expiration_date": nil},
							bson.M{"expiration_date": bson.M{"$gt": unDistributionDateStr}},
						}

						packageNumber, exist := packageDrugNames[drugName]

						if count > 0 { //说明需要发药
							//如果该药物是按照包装发，判断count数量是否是整盒
							if packageIsOpen && exist {
								count = drugPackageCount[drugName] * packageNumber
								queryFilter["package_number"] = bson.M{"$nin": [2]interface{}{nil, ""}}
							}
						}

						//获取分组数据
						groupData := []map[string]interface{}{}

						groupPipepine := mongo.Pipeline{
							{{Key: "$match", Value: queryFilter}},
							{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "batchNumber": "$batch_number", "expirationDate": "$expiration_date"}, "availableCount": bson.M{"$sum": 1}}}},
							{{Key: "$project", Value: bson.M{"_id": "$_id", "availableCount": "$availableCount", "packageMethod": "false"}}},
							{{Key: "$project", Value: bson.M{"_id": 0, "name": "$_id.name", "batchNumber": "$_id.batchNumber", "expirationDate": "$_id.expirationDate", "availableCount": "$availableCount", "packageMethod": "false"}}},
						}
						if packageIsOpen && exist {
							groupPipepine = mongo.Pipeline{
								{{Key: "$match", Value: queryFilter}},
								{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "package_number": "$package_number", "batch_number": "$batch_number", "expiration_date": "$expiration_date"}, "availableCount": bson.M{"$sum": 1}}}},
								{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$availableCount", packageNumber}}}}},
								{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$_id.name", "batchNumber": "$_id.batch_number", "expirationDate": "$_id.expiration_date"}, "availableCount": bson.M{"$sum": 1}}}},
								{{Key: "$project", Value: bson.M{"_id": "$_id", "availableCount": "$availableCount", "packageMethod": "true"}}},
								{{Key: "$project", Value: bson.M{"_id": 0, "name": "$_id.name", "batchNumber": "$_id.batchNumber", "expirationDate": "$_id.expirationDate", "availableCount": "$availableCount", "packageMethod": "true"}}},
							}
						}

						groupCursor, groupErr := tools.Database.Collection("medicine_others").Aggregate(nil, groupPipepine)
						if groupErr != nil {
							return nil, errors.WithStack(groupErr)
						}
						groupErr = groupCursor.All(nil, &groupData)
						if groupErr != nil {
							return nil, errors.WithStack(groupErr)
						}

						//按照药物有效期批次号排序
						sort.SliceStable(groupData, func(i int, j int) bool {
							groupDatai := groupData[i]
							groupDataj := groupData[j]
							if groupDatai["name"].(string) < groupDataj["name"].(string) {
								return true
							}

							if groupDatai["name"].(string) > groupDataj["name"].(string) {
								return false
							}

							if groupDatai["expirationDate"] != nil && groupDatai["expirationDate"] != "" && groupDataj["expirationDate"] != nil && groupDataj["expirationDate"] != "" {
								if groupDatai["expirationDate"].(string) < groupDataj["expirationDate"].(string) {
									return true
								}

								if groupDatai["expirationDate"].(string) > groupDataj["expirationDate"].(string) {
									return false
								}
							} else {
								if (groupDatai["expirationDate"] == nil || groupDatai["expirationDate"] == "") && (groupDataj["expirationDate"] != nil && groupDataj["expirationDate"] != "") {
									return false
								} else if (groupDataj["expirationDate"] == nil || groupDataj["expirationDate"] == "") && (groupDatai["expirationDate"] != nil && groupDatai["expirationDate"] != "") {
									return true
								} else {
									if groupDatai["batchNumber"] != nil && groupDatai["batchNumber"] != "" && groupDataj["batchNumber"] != nil && groupDataj["batchNumber"] != "" {
										if groupDatai["batchNumber"].(string) < groupDataj["batchNumber"].(string) {
											return true
										}

										if groupDatai["batchNumber"].(string) > groupDataj["batchNumber"].(string) {
											return false
										}
									} else {
										if (groupDatai["batchNumber"] == nil || groupDatai["batchNumber"] == "") && (groupDataj["batchNumber"] != nil && groupDataj["batchNumber"] != "") {
											return false
										} else {
											return true
										}
									}
								}
							}

							return groupDatai["batchNumber"].(string) < groupDataj["batchNumber"].(string)
						})

						if count <= 0 {
							for index, medicineGroupData := range groupData {
								if index == 0 {
									medicineGroupData["useCount"] = 0
								}
								medicineGroupData["isOpen"] = true
								data = append(data, medicineGroupData)
							}
						} else {
							needCount := count
							if packageIsOpen && exist {
								needCount = drugPackageCount[drugName]
							}
							for _, medicineGroupData := range groupData {
								availableCount := int(medicineGroupData["availableCount"].(int32))

								if needCount > 0 {
									if availableCount >= needCount {
										medicineGroupData["useCount"] = needCount
										needCount = 0
									} else {
										medicineGroupData["useCount"] = availableCount
										needCount = needCount - availableCount
									}
								}

								medicineGroupData["isOpen"] = true
								data = append(data, medicineGroupData)

							}
						}
					}
				}

			}

		}
	} else {
		groupId := bson.M{"name": "$name", "batchNumber": "$batch_number", "expirationDate": "$expiration_date"}
		projectPip := bson.M{"_id": 0, "name": "$_id.name", "batchNumber": "$_id.batchNumber", "expirationDate": "$_id.expirationDate", "availableCount": "$availableCount", "packageMethod": "false"}
		if orderType == 3 || orderType == 4 {
			groupId["status"] = "$status"
			projectPip["status"] = "$_id.status"
		}
		//单品运输
		pipepine := mongo.Pipeline{
			{{Key: "$match", Value: match}},
			{{Key: "$group", Value: bson.M{"_id": groupId, "availableCount": bson.M{"$sum": 1}}}},
			{{Key: "$project", Value: projectPip}},
			{{Key: "$sort", Value: bson.D{{"name", 1}, {"expirationDate", 1}}}},
		}
		cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, pipepine)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &data)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 包装运输
		if packageIsOpen && len(packageDrugNames) > 0 {
			packageGroupId := bson.M{"name": "$name", "package_number": "$package_number", "batchNumber": "$batch_number", "expirationDate": "$expiration_date"}
			packageGroupId2 := bson.M{"name": "$_id.name", "batchNumber": "$_id.batchNumber", "expirationDate": "$_id.expirationDate"}
			packageProjectPip := bson.M{"_id": 0, "name": "$_id.name", "batchNumber": "$_id.batchNumber", "expirationDate": "$_id.expirationDate", "availableCount": "$availableCount", "packageMethod": "true"}
			if orderType == 3 || orderType == 4 {
				packageGroupId["status"] = "$status"
				packageGroupId2["status"] = "$_id.status"
				packageProjectPip["status"] = "$_id.status"
			}
			for drugName, count := range packageDrugNames {
				packageFilter["name"] = bson.M{"$in": []string{drugName}}
				packagePipepine := mongo.Pipeline{
					{{Key: "$match", Value: packageFilter}},
					{{Key: "$group", Value: bson.M{"_id": packageGroupId, "availableCount": bson.M{"$sum": 1}}}},
					{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$availableCount", count}}}}},
					{{Key: "$group", Value: bson.M{"_id": packageGroupId2, "availableCount": bson.M{"$sum": 1}}}},
					{{Key: "$project", Value: packageProjectPip}},
				}
				packageCursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, packagePipepine)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				var packageData []map[string]interface{}
				err = packageCursor.All(nil, &packageData)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				data = append(data, packageData...)
			}
		}
	}

	//按照药物有效期批次号排序
	sort.SliceStable(data, func(i int, j int) bool {
		groupDatai := data[i]
		groupDataj := data[j]
		if groupDatai["name"].(string) < groupDataj["name"].(string) {
			return true
		}

		if groupDatai["name"].(string) > groupDataj["name"].(string) {
			return false
		}

		if groupDatai["expirationDate"] != nil && groupDatai["expirationDate"] != "" && groupDataj["expirationDate"] != nil && groupDataj["expirationDate"] != "" {
			if groupDatai["expirationDate"].(string) < groupDataj["expirationDate"].(string) {
				return true
			}

			if groupDatai["expirationDate"].(string) > groupDataj["expirationDate"].(string) {
				return false
			}
		} else {
			if (groupDatai["expirationDate"] == nil || groupDatai["expirationDate"] == "") && (groupDataj["expirationDate"] != nil && groupDataj["expirationDate"] != "") {
				return false
			} else if (groupDataj["expirationDate"] == nil || groupDataj["expirationDate"] == "") && (groupDatai["expirationDate"] != nil && groupDatai["expirationDate"] != "") {
				return true
			} else {
				if groupDatai["batchNumber"] != nil && groupDatai["batchNumber"] != "" && groupDataj["batchNumber"] != nil && groupDataj["batchNumber"] != "" {
					if groupDatai["batchNumber"].(string) < groupDataj["batchNumber"].(string) {
						return true
					}

					if groupDatai["batchNumber"].(string) > groupDataj["batchNumber"].(string) {
						return false
					}
				} else {
					if (groupDatai["batchNumber"] == nil || groupDatai["batchNumber"] == "") && (groupDataj["batchNumber"] != nil && groupDataj["batchNumber"] != "") {
						return false
					} else {
						return true
					}
				}
			}
		}

		return groupDatai["batchNumber"].(string) < groupDataj["batchNumber"].(string)
	})

	//nameMap, _, err := matchCloseCohortMedicine(orderInfo.EnvID)
	res := []map[string]interface{}{}
	for _, value := range data {
		name := value["name"].(string)
		// if nameMap[name] == 0 {
		// 	continue
		// }

		if value["expirationDate"] != nil {
			if value["expirationDate"].(string) == "" {
				value["expirationDate"] = "-"
			}
		}

		if value["batchNumber"] != nil {
			if value["batchNumber"].(string) == "" {
				value["batchNumber"] = "-"
			}
		}

		isBlindedDrug, _ := tools.IsBlindedDrug(orderInfo.EnvID, name)
		if isBlindedRole && isBlindedDrug {
			encrypted, salt := tools.Encrypt(name)
			value["name"] = tools.BlindData
			value["salt"] = salt
			value["saltName"] = encrypted
		}
		res = append(res, value)
	}

	return res, nil
}

// 未编号包装号
func (s *DrugConfigureService) GetPackageNumber(ctx *gin.Context, customerOID primitive.ObjectID, projectOID primitive.ObjectID, envOID primitive.ObjectID, count int, packageCount int) ([]string, error) {
	var data []models.OtherPackageNumber
	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}}},
		{{Key: "$sort", Value: bson.D{{"package_number", -1}}}},
		{{Key: "$limit", Value: 1}},
	}
	cursor, err := tools.Database.Collection("other_package_number").Aggregate(nil, pipepine)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var packageNumber string
	if data != nil {
		packageNumber = strconv.Itoa(data[0].PackageNumber)
	} else {
		packageNumber = "00000000"
	}

	packageNumbers := make([]string, 0)
	packageNumberDatas := make([]interface{}, 0)
	for i := 0; i < count; i++ {
		maxNumber, _ := strconv.Atoi(packageNumber)
		packageNumberNew := maxNumber + i + 1
		packageNumbers = append(packageNumbers, fmt.Sprintf("%08s", strconv.Itoa(packageNumberNew)))

		var packageNumberData models.OtherPackageNumber
		packageNumberData.ID = primitive.NewObjectID()
		packageNumberData.CustomerID = customerOID
		packageNumberData.EnvironmentID = envOID
		packageNumberData.ProjectID = projectOID
		packageNumberData.PackageNumber = packageNumberNew
		packageNumberData.PackageSerialNumber = packageNumberNew
		packageNumberData.PackageCount = packageCount
		packageNumberDatas = append(packageNumberDatas, packageNumberData)
	}
	if _, err := tools.Database.Collection("other_package_number").InsertMany(nil, packageNumberDatas); err != nil {
		return nil, errors.WithStack(err)
	}

	return packageNumbers, nil
}

func (s *DrugConfigureService) AddOtherMedicine(ctx *gin.Context, data models.AddMedicineOther) error {
	otherMedicines := make([]interface{}, 0)

	//混合包装是否打开、混包的药物
	isOpenPackage, _, packageDrugNames, _, _, _ := tools.IsOpenPackage(data.EnvironmentID)
	count, isPackageDrug := packageDrugNames[data.Name]
	//如果包装开关打开，查询项目环境下最大的包装号
	packageNumbers := []string{}
	if isOpenPackage {
		if isPackageDrug && data.PackageCount > 0 {
			packageNumbers, _ = s.GetPackageNumber(ctx, data.CustomerID, data.ProjectID, data.EnvironmentID, data.PackageCount, count)
		}
	}

	updateTime := time.Duration(time.Now().Unix())
	for i := 0; i < data.Count; i++ {
		var otherMedicine models.OtherMedicine
		otherMedicine.ID = primitive.NewObjectID()
		otherMedicine.CustomerID = data.CustomerID
		otherMedicine.ProjectID = data.ProjectID
		otherMedicine.EnvironmentID = data.EnvironmentID
		otherMedicine.StorehouseID = data.StorehouseID
		otherMedicine.Name = data.Name
		otherMedicine.BatchNumber = data.BatchNumber
		otherMedicine.ExpirationDate = data.ExpirationDate
		otherMedicine.Spec = data.Spec
		otherMedicine.Status = 1
		otherMedicine.UpdatedAt = updateTime
		//判断是否是包装药物，包装数量
		if isOpenPackage && data.PackageCount > 0 {
			if isPackageDrug {
				packageNumber := packageNumbers[i/count]
				otherMedicine.PackageNumber = packageNumber
				otherMedicine.PackageSerialNumber = packageNumber
			}
		}
		otherMedicines = append(otherMedicines, otherMedicine)
	}
	if _, err := tools.Database.Collection("medicine_others").InsertMany(nil, otherMedicines); err != nil {
		return errors.WithStack(err)
	}
	OID := data.EnvironmentID
	err := insertOtherMedicineLog(ctx, data, OID, 1, "")
	if err != nil {
		return errors.WithStack(err)
	}

	// updateMedicines := models.OtherMedicineFreeze{
	// 	CustomerID:    data.CustomerID,
	// 	ProjectID:     data.ProjectID,
	// 	EnvironmentID: data.EnvironmentID,
	// 	InstituteID:   data.StorehouseID,
	// 	InstituteType: 2,
	// }
	// otherMedicineInfo := models.FreezeOtherMedicines{
	// 	Name:       data.Name,
	// 	ExpireDate: data.ExpirationDate,
	// 	Batch:      data.BatchNumber,
	// }
	// s.medicineService.getOtherMedicineKey(ctx, updateMedicines, otherMedicineInfo, primitive.NilObjectID)
	// InsertData := models.MedicineOther{
	// 	ID:            primitive.NewObjectID(),
	// 	CustomerID:    data.CustomerID,
	// 	ProjectID:     data.ProjectID,
	// 	EnvironmentID: data.EnvironmentID,
	// 	StorehouseID:  data.StorehouseID,
	// 	Edit:          false,
	// 	Info:          data.Info,
	// }
	// //记录生成时的药物总数，以便判断是否有库存变化
	// filter := bson.M{"customer_id": data.CustomerID, "project_id": data.ProjectID, "env_id": data.EnvironmentID, "storehouse_id": data.StorehouseID,
	// 	"info.name": data.Info.Name, "info.batch": data.Info.Batch, "info.expire_date": data.Info.ExpireDate}
	// var oldOther models.MedicineOther
	// err := tools.Database.Collection("medicine_other_institute").FindOne(nil, filter).Decode(&oldOther)
	// if err != nil && err != mongo.ErrNoDocuments {
	// 	return errors.WithStack(err)
	// }
	// if !oldOther.ID.IsZero() {
	// 	otherMedicineUpdate := bson.M{
	// 		"$set": bson.M{
	// 			"edit":       true,
	// 			"info.count": oldOther.Info.Count + data.Info.Count,
	// 		},
	// 	}
	// 	_, err = tools.Database.Collection("medicine_other_institute").UpdateOne(nil, bson.M{"_id": oldOther.ID}, otherMedicineUpdate)
	// 	if err != nil {
	// 		return errors.WithStack(err)
	// 	}
	// } else {
	// 	if _, err := tools.Database.Collection("medicine_other_institute").InsertOne(nil, InsertData); err != nil {
	// 		return errors.WithStack(err)
	// 	}
	// }

	return nil
}

func (s *DrugConfigureService) UpdateOtherMedicine(ctx *gin.Context, data models.UpdateMedicineOtherInfo) error {
	match := bson.M{"status": 1, "env_id": data.EnvironmentID, "storehouse_id": data.OldStorehouseID, "name": data.OldName,
		"batch_number": data.OldBatchNumber, "expiration_date": data.OldExpirationDate}

	var oldMedicineOther models.OtherMedicine
	err := tools.Database.Collection("medicine_others").FindOne(nil, match).Decode(&oldMedicineOther)
	if err != nil {
		return errors.WithStack(err)
	}

	//混合包装是否打开、混包的药物
	isPackageDrug := false
	isOpenPackage, _, packageDrugNames, _, _, _ := tools.IsOpenPackage(data.EnvironmentID)
	if isOpenPackage {
		_, isPackageDrug = packageDrugNames[data.Name]
	}

	limit := 1
	if data.OldCount > data.Count {
		limit = data.OldCount - data.Count
	}
	delIds := make([]primitive.ObjectID, 0)

	if data.OldCount > data.Count { //删除
		if !isPackageDrug { // 非包装药物
			var oldMedicineOthers []models.OtherMedicine
			pipepine := mongo.Pipeline{
				{{Key: "$match", Value: match}},
				{{Key: "$sort", Value: bson.D{{"_id", -1}}}},
				{{Key: "$limit", Value: limit}},
			}
			cursor, err := tools.Database.Collection("medicine_others").Aggregate(ctx, pipepine)
			if err != nil {
				return errors.WithStack(err)
			}
			err = cursor.All(nil, &oldMedicineOthers)
			if err != nil {
				return errors.WithStack(err)
			}
			for _, del := range oldMedicineOthers {
				delIds = append(delIds, del.ID)
			}
		} else { //包装
			oldMedicineOthers := []map[string]interface{}{}
			limit = data.OldPackageCount - data.PackageCount
			if limit > 0 {
				pipepine := mongo.Pipeline{
					{{Key: "$match", Value: match}},
					{{Key: "$group", Value: bson.M{"_id": bson.M{"package_number": "$package_number"}, "ids": bson.M{"$addToSet": "$_id"}}}},
					{{Key: "$sort", Value: bson.D{{"_id.package_number", -1}}}},
					{{Key: "$limit", Value: limit}},
				}
				cursor, err := tools.Database.Collection("medicine_others").Aggregate(ctx, pipepine)
				if err != nil {
					return errors.WithStack(err)
				}
				err = cursor.All(nil, &oldMedicineOthers)
				if err != nil {
					return errors.WithStack(err)
				}
				for _, drug := range oldMedicineOthers {
					drugs := drug["ids"].(primitive.A)
					for _, id := range drugs {
						delIds = append(delIds, id.(primitive.ObjectID))
					}
				}
			}
		}
		_, err := tools.Database.Collection("medicine_others").DeleteMany(nil, bson.M{"_id": bson.M{"$in": delIds}})
		if err != nil {
			return errors.WithStack(err)
		}
	}

	if data.OldCount < data.Count { //新增
		updateTime := time.Duration(time.Now().Unix())
		otherMedicines := make([]interface{}, 0)
		count := data.Count - data.OldCount
		//如果包装开关打开，查询项目环境下最大的包装号
		packageCount := data.PackageCount - data.OldPackageCount
		packageNumbers := []string{}
		pCount, isPackageDrug := packageDrugNames[data.Name]
		if isOpenPackage && packageCount > 0 && isPackageDrug {
			packageNumbers, _ = s.GetPackageNumber(ctx, oldMedicineOther.CustomerID, oldMedicineOther.ProjectID, data.EnvironmentID, packageCount, pCount)
			for i := 0; i < count; i++ {
				var otherMedicine models.OtherMedicine
				otherMedicine.ID = primitive.NewObjectID()
				otherMedicine.CustomerID = oldMedicineOther.CustomerID
				otherMedicine.ProjectID = oldMedicineOther.ProjectID
				otherMedicine.EnvironmentID = data.EnvironmentID
				otherMedicine.StorehouseID = data.StorehouseID
				otherMedicine.Name = data.Name
				otherMedicine.BatchNumber = data.BatchNumber
				otherMedicine.ExpirationDate = data.ExpirationDate
				otherMedicine.Status = 1
				otherMedicine.UpdatedAt = updateTime
				//判断是否是包装药物，包装数量
				if isOpenPackage && pCount > 0 {
					if isPackageDrug {
						packageNumber := packageNumbers[i/pCount]
						otherMedicine.PackageNumber = packageNumber
						otherMedicine.PackageSerialNumber = packageNumber
					}
				}
				otherMedicines = append(otherMedicines, otherMedicine)
			}
			if _, err := tools.Database.Collection("medicine_others").InsertMany(nil, otherMedicines); err != nil {
				return errors.WithStack(err)
			}
		} else {
			for i := 0; i < count; i++ {
				var otherMedicine models.OtherMedicine
				otherMedicine.ID = primitive.NewObjectID()
				otherMedicine.CustomerID = oldMedicineOther.CustomerID
				otherMedicine.ProjectID = oldMedicineOther.ProjectID
				otherMedicine.EnvironmentID = data.EnvironmentID
				otherMedicine.StorehouseID = data.StorehouseID
				otherMedicine.Name = data.Name
				otherMedicine.BatchNumber = data.BatchNumber
				otherMedicine.ExpirationDate = data.ExpirationDate
				otherMedicine.Status = 1
				otherMedicines = append(otherMedicines, otherMedicine)
			}
			if _, err := tools.Database.Collection("medicine_others").InsertMany(nil, otherMedicines); err != nil {
				return errors.WithStack(err)
			}
		}
	}

	//更新
	update := bson.M{
		"$set": bson.M{
			"storehouse_id":   data.StorehouseID,
			"name":            data.Name,
			"batch_number":    data.BatchNumber,
			"expiration_date": data.ExpirationDate,
		},
	}
	if oldMedicineOther.ExpirationDate != data.ExpirationDate {
		update = bson.M{
			"$set": bson.M{
				"storehouse_id":   data.StorehouseID,
				"name":            data.Name,
				"batch_number":    data.BatchNumber,
				"expiration_date": data.ExpirationDate,
				"updated_at":      time.Duration(time.Now().Unix()),
			},
		}
	}
	_, err = tools.Database.Collection("medicine_others").UpdateMany(nil, match, update)
	if err != nil {
		return errors.WithStack(err)
	}
	//轨迹
	OID := data.EnvironmentID
	var old models.UpdateMedicineOtherInfo
	old.Name = oldMedicineOther.Name
	old.BatchNumber = oldMedicineOther.BatchNumber
	old.ExpirationDate = oldMedicineOther.ExpirationDate
	old.StorehouseID = oldMedicineOther.StorehouseID
	old.Count = data.OldCount
	old.PackageCount = data.OldPackageCount

	err = UpdateOtherMedicineLog(ctx, data, old, OID, 2)
	if err != nil {
		return errors.WithStack(err)
	}

	updateMedicines := models.OtherMedicineFreeze{
		CustomerID:    oldMedicineOther.CustomerID,
		ProjectID:     oldMedicineOther.ProjectID,
		EnvironmentID: oldMedicineOther.EnvironmentID,
		//InstituteID:   data.StorehouseID,
		InstituteType: 2,
	}
	otherMedicineInfo := models.FreezeOtherMedicines{
		Name:         data.Name,
		ExpireDate:   data.ExpirationDate,
		Batch:        data.BatchNumber,
		StorehouseID: data.StorehouseID,
	}
	s.medicineService.getOtherMedicineKey(ctx, updateMedicines, otherMedicineInfo)

	return nil
}

func (s *DrugConfigureService) DelOtherMedicine(ctx *gin.Context, envID string, storehouseId string, name string, batchNumber string, expirationDate string, count string) error {
	envOID, _ := primitive.ObjectIDFromHex(envID)
	storehouseOID, _ := primitive.ObjectIDFromHex(storehouseId)

	var old models.UpdateMedicineOtherInfo
	old.Name = name
	old.BatchNumber = batchNumber
	old.ExpirationDate = expirationDate

	if batchNumber == "-" {
		batchNumber = ""
	}
	if expirationDate == "-" {
		expirationDate = ""
	}
	match := bson.M{"status": 1, "env_id": envOID, "storehouse_id": storehouseOID, "name": name, "batch_number": batchNumber, "expiration_date": expirationDate}

	updateResult, err := tools.Database.Collection("medicine_others").DeleteMany(nil, match)
	if err != nil {
		return errors.WithStack(err)
	}

	logOID := envOID
	var new models.UpdateMedicineOtherInfo
	old.StorehouseID = storehouseOID
	old.Count = int(updateResult.DeletedCount)
	err = UpdateOtherMedicineLog(ctx, new, old, logOID, 3)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func getDrugOidValue(factor map[string]interface{}, oid string) string {
	if factor[oid] != nil {
		return factor[oid].(string)
	} else {
		return ""
	}
}

// checkUniqueDrugConfig
func checkUniqueDrugConfig(ctx *gin.Context, sctx mongo.SessionContext, filter bson.M, drugConfigureInfo models.DrugConfigureInfo, projectOID, envOID, cohortID primitive.ObjectID) (bool, error) {

	// 自身名称重复校验
	selfDrugName := map[string]bool{}
	for _, value := range drugConfigureInfo.Values {
		if selfDrugName[value.DrugName] {
			return false, nil
		}
		selfDrugName[value.DrugName] = true
	}

	var drugConfigures []models.DrugConfigure
	cursor, _ := tools.Database.Collection("drug_configure").Find(nil, bson.M{"env_id": envOID})
	err := cursor.All(nil, &drugConfigures)
	if err != nil {
		return false, errors.WithStack(err)
	}
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return false, errors.WithStack(err)
	}
	var cohorts []models.Cohort
	if project.Type != 1 {
		for _, env := range project.Environments {
			if env.ID == envOID {
				cohorts = env.Cohorts
			}
		}
	}

	for _, drugConfigure := range drugConfigures {
		cohortName := ""
		if project.Type != 1 && drugConfigure.CohortID != primitive.NilObjectID {
			for _, cohort := range cohorts {
				if cohort.ID == drugConfigure.CohortID {
					cohortName = "(" + cohort.Name + ")"
				}
			}
		}
		for _, configure := range drugConfigure.Configures {
			for _, value := range configure.Values {
				if drugConfigureInfo.OpenSetting != configure.OpenSetting {
					for _, drugValue := range drugConfigureInfo.Values {
						if drugConfigure.CohortID == cohortID && configure.OpenSetting != configure.OpenSetting && value.DrugName == drugValue.DrugName && configure.ID != drugConfigureInfo.ID {
							// 同个cohort下 发放方式不一致
							return false, tools.BuildServerError(ctx, "medicine_open_setting_repeat3")
						}
					}
				}
				// 药物名称 是否开放一致
				for _, drugValue := range drugConfigureInfo.Values {
					if value.DrugName == drugValue.DrugName &&
						!(configure.ID == drugConfigureInfo.ID && drugConfigure.CohortID == cohortID) { // 复制的cohort id一直问题 bug
						drugConfigureInfoOpenSetting := drugConfigureInfo.OpenSetting
						configureOpenSetting := configure.OpenSetting
						if drugConfigureInfo.OpenSetting == 3 {
							if drugValue.IsOpen {
								drugConfigureInfoOpenSetting = 2

							} else {
								drugConfigureInfoOpenSetting = 1
							}
						}

						if configure.OpenSetting == 3 {
							if value.IsOpen {
								configureOpenSetting = 2

							} else {
								configureOpenSetting = 1
							}
						}

						if drugConfigureInfoOpenSetting != configureOpenSetting {
							openSettingTr := ""
							if configureOpenSetting == 2 {
								openSettingTr = "medicine_open_setting_openTrue" //开放
							} else {
								openSettingTr = "medicine_open_setting_openFlase" //不开放
							}
							return false, tools.BuildCustomError("\"" + value.DrugName + "\"" + locales.Tr(ctx, "medicine_open_setting_repeat1") + locales.Tr(ctx, openSettingTr) + cohortName + locales.Tr(ctx, "medicine_open_setting_repeat2"))
						}
					}
				}
			}
		}
	}

	var drugConfigure models.DrugConfigure
	err = tools.Database.Collection("drug_configure").FindOne(sctx, filter).Decode(&drugConfigure)
	if err != nil && err != mongo.ErrNoDocuments {
		return false, errors.WithStack(err)
	}
	uniqueBool := true

	if drugConfigureInfo.OpenSetting == 1 {
		hash := map[string]bool{}
		for _, configure := range drugConfigure.Configures {
			if configure.OpenSetting != 1 {
				continue
			}
			// 更新时 去重过滤对应数据
			if configure.ID == drugConfigureInfo.ID {
				continue
			}
			// 研究配置排序后合成唯一hash   组别-名称-数量-规格
			{
				labels := []string{}
				for _, value := range configure.Values {
					labels = append(labels, value.DrugName+"-"+value.CustomDispensingNumber+"-"+value.DrugSpec)
				}
				sort.Sort(sort.StringSlice(labels))
				hash[configure.CustomerCalculation+configure.CustomerCalculationSpec+configure.Group+"-"+strings.Join(labels, "")+configure.Label] = true
			}

		}
		// 研究配置排序后校验hash是否存在
		labels := []string{}
		for _, value := range drugConfigureInfo.Values {
			labels = append(labels, value.DrugName+"-"+value.CustomDispensingNumber+"-"+value.DrugSpec)
		}
		sort.Sort(sort.StringSlice(labels))
		label := drugConfigureInfo.CustomerCalculation + drugConfigureInfo.CustomerCalculationSpec + drugConfigureInfo.Group + "-" + strings.Join(labels, "") + drugConfigureInfo.Label
		if hash[label] {
			uniqueBool = false
		}
	} else if drugConfigureInfo.OpenSetting == 2 {
		hash := map[string]bool{}
		// 研究配置唯一hash   组别-名称-
		for _, configure := range drugConfigure.Configures {
			if configure.OpenSetting != 2 {
				continue
			}
			if configure.ID == drugConfigureInfo.ID || configure.Label != "" {
				continue
			}
			for _, value := range configure.Values {
				for _, cycle := range configure.VisitCycles {
					other := convertor.ToString(value.IsOther)
					hash[configure.CustomerCalculation+configure.CustomerCalculationSpec+configure.Group+"-"+value.CustomDispensingNumber+value.DrugName+other+cycle.Hex()] = true
				}
			}
		}
		// 研究配置唯一hash   组别-名称 + 访视
		for _, value := range drugConfigureInfo.Values {
			other := convertor.ToString(value.IsOther)
			for _, cycle := range drugConfigureInfo.VisitCycles {
				if hash[drugConfigureInfo.CustomerCalculation+drugConfigureInfo.CustomerCalculationSpec+drugConfigureInfo.Group+"-"+value.CustomDispensingNumber+value.DrugName+other+cycle.Hex()] {
					uniqueBool = false
				} else {
					// 自身去重
					hash[drugConfigureInfo.CustomerCalculation+drugConfigureInfo.CustomerCalculationSpec+drugConfigureInfo.Group+"-"+value.CustomDispensingNumber+value.DrugName+other+cycle.Hex()] = true
				}
			}

		}
	} else if drugConfigureInfo.OpenSetting == 3 {

		hash := map[string]bool{}
		// 研究配置唯一hash   组别-名称-
		for _, configure := range drugConfigure.Configures {
			if configure.OpenSetting != 3 {
				continue
			}
			if configure.ID == drugConfigureInfo.ID {
				continue
			}
			for _, value := range configure.Values {
				for _, cycle := range configure.VisitCycles {
					other := convertor.ToString(value.IsOther)
					hash[configure.Group+"-"+value.DrugName+other+cycle.Hex()] = true
				}
			}
		}
		// 研究配置唯一hash   组别-名称 + 访视
		sort.Slice(drugConfigureInfo.VisitCycles, func(i, j int) bool {
			return drugConfigureInfo.VisitCycles[i].String() < drugConfigureInfo.VisitCycles[j].String()
		})

		for _, value := range drugConfigureInfo.Values {
			for _, cycle := range drugConfigureInfo.VisitCycles {
				other := convertor.ToString(value.IsOther)
				if hash[drugConfigureInfo.CustomerCalculation+drugConfigureInfo.CustomerCalculationSpec+drugConfigureInfo.Group+"-"+value.DrugName+other+cycle.Hex()] {
					uniqueBool = false
				} else {
					// 自身去重
					hash[drugConfigureInfo.CustomerCalculation+drugConfigureInfo.CustomerCalculationSpec+drugConfigureInfo.Group+"-"+value.DrugName+other+cycle.Hex()] = true
				}
			}
		}
	}
	return uniqueBool, nil
}

func (s *DrugConfigureService) GetGroupConfig(ctx *gin.Context, envID string, cohortID string, roleID string) ([]models.SaltGroup, error) {
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	match := bson.M{"env_id": envOID}
	if !cohortOID.IsZero() {
		match = bson.M{"env_id": envOID, "cohort_id": cohortOID}

	}
	var randomDesign models.RandomDesign
	var attribute models.Attribute
	err := tools.Database.Collection("random_design").FindOne(nil, match).Decode(&randomDesign)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	err = tools.Database.Collection("attribute").FindOne(nil, match).Decode(&attribute)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	isBlindedRole, err := tools.IsBlindedRole(roleID)
	var rep []models.SaltGroup
	key := 0
	naSaltName := base64.StdEncoding.EncodeToString([]byte("N/A"))

	rep = append(rep, models.SaltGroup{
		Key:         key,
		Group:       "N/A",
		AllSaltName: naSaltName,
	})

	for _, group := range randomDesign.Info.Groups {
		key++
		if group.SubGroup != nil && len(group.SubGroup) > 0 {
			for _, subGroup := range group.SubGroup {
				fmt.Println(group.Name + " " + subGroup.Name)
				allSaltName := base64.StdEncoding.EncodeToString([]byte(group.Name + " " + subGroup.Name))
				if isBlindedRole && subGroup.Blind {
					subSaltName, subSalt := tools.Encrypt(subGroup.Name)
					if isBlindedRole && attribute.AttributeInfo.Blind {
						saltName, salt := tools.Encrypt(group.Name)
						rep = append(rep, models.SaltGroup{
							Key:         key,
							Group:       tools.BlindData,
							Salt:        &salt,
							SaltName:    &saltName,
							SubGroup:    tools.BlindData,
							SubSaltName: &subSaltName,
							SubSalt:     &subSalt,
							AllSaltName: allSaltName,
						})
					} else {
						rep = append(rep, models.SaltGroup{
							Key:         key,
							Group:       group.Name,
							SubGroup:    tools.BlindData,
							SubSaltName: &subSaltName,
							SubSalt:     &subSalt,
							AllSaltName: allSaltName,
						})
					}
				} else {
					if isBlindedRole && attribute.AttributeInfo.Blind {
						saltName, salt := tools.Encrypt(group.Name)
						rep = append(rep, models.SaltGroup{
							Key:         key,
							Group:       tools.BlindData,
							Salt:        &salt,
							SaltName:    &saltName,
							SubGroup:    subGroup.Name,
							AllSaltName: allSaltName,
						})
					} else {
						rep = append(rep, models.SaltGroup{
							Key:         key,
							Group:       group.Name,
							SubGroup:    subGroup.Name,
							AllSaltName: allSaltName,
						})
					}
				}
			}
		} else {
			allSaltName := base64.StdEncoding.EncodeToString([]byte(group.Name))
			if isBlindedRole && attribute.AttributeInfo.Blind {
				saltName, salt := tools.Encrypt(group.Name)
				rep = append(rep, models.SaltGroup{
					Key:         key,
					Group:       tools.BlindData,
					Salt:        &salt,
					SaltName:    &saltName,
					AllSaltName: allSaltName,
				})
			} else {
				rep = append(rep, models.SaltGroup{
					Key:         key,
					Group:       group.Name,
					AllSaltName: allSaltName,
				})
			}
		}
	}
	return rep, nil
}

func (s *DrugConfigureService) UpdateVisitType(ctx *gin.Context, visitCycle models.VisitCycle) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		oldVisitCycle := models.VisitCycle{}
		if visitCycle.ID.IsZero() {
			visitCycle.ID = primitive.NewObjectID()
			oldVisitCycle.ID = visitCycle.ID
			visitCycle.Meta.UpdatedAt = time.Duration(time.Now().Unix())
			_, err := tools.Database.Collection("visit_cycle").InsertOne(sctx, visitCycle)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = insertVisitTypeLog(ctx, sctx, visitCycle.EnvironmentID, 1, oldVisitCycle, visitCycle, oldVisitCycle.ID)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		} else {

			subjectRandom, err := tools.Database.Collection("subject").CountDocuments(sctx, bson.M{"env_id": visitCycle.EnvironmentID, "cohort_id": visitCycle.CohortID, "group": bson.M{"$ne": ""}, "deleted": bson.M{"$ne": true}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if subjectRandom > 0 {

			}
			subjectDispensing, err := tools.Database.Collection("dispensing").CountDocuments(sctx, bson.M{"env_id": visitCycle.EnvironmentID, "cohort_id": visitCycle.CohortID, "status": bson.M{"$ne": 1}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if subjectRandom > 0 || subjectDispensing > 0 {

				return nil, tools.BuildServerError(ctx, "visit_cycle_duplicated_random3")

			}
			err = tools.Database.Collection("visit_cycle").FindOneAndUpdate(sctx, bson.M{"_id": visitCycle.ID},
				bson.M{"$set": bson.M{
					"update_infos.visit_type":      visitCycle.VisitType,
					"update_infos.meta.updated_at": time.Duration(time.Now().Unix()),
				}}).Decode(&oldVisitCycle)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			visitCycle.ConfigInfo.VisitType = visitCycle.VisitType
			if oldVisitCycle.ConfigInfo.VisitType != visitCycle.VisitType {
				err = insertVisitTypeLog(ctx, sctx, visitCycle.EnvironmentID, 2, oldVisitCycle, visitCycle, oldVisitCycle.ID)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}

		}

		// TODO 写入日志

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil

}

func (s *DrugConfigureService) UpdateVisitBaseCohort(ctx *gin.Context, visitCycle models.VisitCycle) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		oldVisitCycle := models.VisitCycle{}
		if visitCycle.ID.IsZero() {
			visitCycle.ID = primitive.NewObjectID()
			oldVisitCycle.ID = visitCycle.ID
			visitCycle.Meta.UpdatedAt = time.Duration(time.Now().Unix())
			_, err := tools.Database.Collection("visit_cycle").InsertOne(sctx, visitCycle)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = insertVisitTypeLog(ctx, sctx, visitCycle.EnvironmentID, 1, oldVisitCycle, visitCycle, oldVisitCycle.ID)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		} else {
			err := tools.Database.Collection("visit_cycle").FindOneAndUpdate(sctx, bson.M{"_id": visitCycle.ID},
				bson.M{"$set": bson.M{
					"update_infos.base_cohort":     visitCycle.BaseCohort,
					"update_infos.meta.updated_at": time.Duration(time.Now().Unix()),
				}}).Decode(&oldVisitCycle)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			visitCycle.ConfigInfo.BaseCohort = visitCycle.BaseCohort
			if oldVisitCycle.ConfigInfo.BaseCohort != visitCycle.BaseCohort {
				var project models.Project
				tools.Database.Collection("project").FindOne(nil, bson.M{"_id": oldVisitCycle.ProjectID}).Decode(&project)
				_, oldCohort := database.GetEnvCohortInfo(project, oldVisitCycle.EnvironmentID, oldVisitCycle.ConfigInfo.BaseCohort)
				_, newCohort := database.GetEnvCohortInfo(project, oldVisitCycle.EnvironmentID, visitCycle.BaseCohort)
				err = insertBaseCohortLog(ctx, sctx, visitCycle.EnvironmentID, 2, oldVisitCycle, visitCycle, oldVisitCycle.ID, oldCohort.Name, newCohort.Name)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}

		}

		// TODO 写入日志

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil

}

func (s *DrugConfigureService) PushVisit(ctx *gin.Context, ID string, version string, roleId string) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		OID, _ := primitive.ObjectIDFromHex(ID)
		now := time.Now().Unix()
		// 查询当前访视
		var visitCycle models.VisitCycle
		err := tools.Database.Collection("visit_cycle").FindOne(sctx, bson.M{"_id": OID}).Decode(&visitCycle)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 修改受试者的访视计划
		err = updateSubjectVisit(ctx, sctx, visitCycle)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 组别修改研究产品配置的访视字段
		cancelVistit := make(map[primitive.ObjectID][]string)
		newInfos := visitCycle.ConfigInfo.Infos
		for _, oldInfo := range visitCycle.Infos {
			for _, newInfo := range newInfos {
				if oldInfo.ID == newInfo.ID {
					oldStringSlice := make([]string, 0)
					for _, v := range oldInfo.Group {
						if str, ok := v.(string); ok {
							oldStringSlice = append(oldStringSlice, str)
						}
					}
					newStringSlice := make([]string, 0)
					for _, v := range newInfo.Group {
						if str, ok := v.(string); ok {
							newStringSlice = append(newStringSlice, str)
						}
					}
					cancelGroup := slice.Difference(oldStringSlice, newStringSlice)
					if len(cancelGroup) > 0 {
						cancelVistit[oldInfo.ID] = cancelGroup
					}
				}
			}
		}

		//查询研究产品配置哪些配置了这个组别并且配置了这个访视的
		for visitId, groups := range cancelVistit {
			match := bson.M{
				"project_id":  visitCycle.ProjectID,
				"env_id":      visitCycle.EnvironmentID,
				"customer_id": visitCycle.CustomerID,
			}
			if visitCycle.CohortID != primitive.NilObjectID {
				match = bson.M{"customer_id": visitCycle.CustomerID, "project_id": visitCycle.ProjectID, "env_id": visitCycle.EnvironmentID, "cohort_id": visitCycle.CohortID}
			}
			var drugConfigure models.DrugConfigure
			err = tools.Database.Collection("drug_configure").FindOne(nil, match).Decode(&drugConfigure)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}

			for i, drugConfig := range drugConfigure.Configures {
				index := arrays.ContainsString(groups, drugConfig.Group)
				vIndex := arrays.Contains(drugConfig.VisitCycles, visitId)
				if index != -1 && vIndex != -1 { //组别下，取消这个访视
					drugConfig.VisitCycles = RemoveElement(drugConfig.VisitCycles, visitId)
					drugConfigure.Configures[i] = drugConfig
				}
			}
			update := bson.M{
				"$set": bson.M{
					"configures": drugConfigure.Configures,
				},
			}
			_, updateErr := tools.Database.Collection("drug_configure").UpdateOne(nil, bson.M{"_id": drugConfigure.ID}, update)
			if updateErr != nil {
				return nil, errors.WithStack(updateErr)
			}

		}

		// 旧的配置写到历史配置
		for _, infos := range visitCycle.HistoryInfo {
			if infos.Version == version {
				return nil, tools.BuildServerError(ctx, "visit_cycle_duplicated_version")
			}
		}
		var visitInfos models.VisitInfos
		visitInfos.VisitType = visitCycle.ConfigInfo.VisitType
		visitInfos.Infos = visitCycle.ConfigInfo.Infos
		visitInfos.Version = version
		visitInfos.Meta = visitCycle.ConfigInfo.Meta
		visitInfos.BaseCohort = visitCycle.ConfigInfo.BaseCohort
		visitInfos.Meta.UpdatedAt = time.Duration(now) // 最后push的时间

		visitCycle.HistoryInfo = append(visitCycle.HistoryInfo, visitInfos)
		// 更新成配置上的数据
		visitCycle.VisitType = visitCycle.ConfigInfo.VisitType
		visitCycle.Version = version
		visitCycle.Infos = visitCycle.ConfigInfo.Infos
		visitCycle.BaseCohort = visitCycle.ConfigInfo.BaseCohort
		visitCycle.Meta.CreatedAt = visitCycle.ConfigInfo.Meta.UpdatedAt
		visitCycle.Meta.UpdatedAt = time.Duration(now)

		_, err = tools.Database.Collection("visit_cycle").UpdateOne(sctx, bson.M{"_id": OID}, bson.M{"$set": visitCycle})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		logOID := visitCycle.EnvironmentID
		if !visitCycle.CohortID.IsZero() {
			logOID = visitCycle.CohortID
		}
		err = insertVisitPushLog(ctx, sctx, visitCycle, logOID, visitCycle.ID)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		err = task.UpdateNotice(sctx, 2, visitCycle.EnvironmentID, visitCycle.CohortID, primitive.NilObjectID, primitive.NilObjectID)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil

}

func RemoveElement(arr []primitive.ObjectID, element primitive.ObjectID) []primitive.ObjectID {
	var result []primitive.ObjectID
	for _, value := range arr {
		if value != element {
			result = append(result, value)
		}
	}
	return result
}

func updateSubjectVisit(ctx *gin.Context, sctx mongo.SessionContext, visitCycle models.VisitCycle) error {
	/*
		for 当前版本
		if Vn 发过药或者随机过
		    continue
		if 发布版本.find(Vn) 存在
		    if 配置相等 顺序相等
		        continue
		    for subject.group
		        if 存在发布版本.group == subject.group || subject.group == ""
		        	update_subject_id append
		        else 修改其中一个组别 删除对应组别受试者这个访视
		            if subject.group != ""
						remove_subject_id append
		    remove_subject_id remove
			update_subject_id update
		else 不存在
		    remove
		for 发布版本
		if 发布版本.find(Vn) 不存在
		    for subject.group
		        if 存在发布版本.group == subject.group || subject.group == ""
		            subject_id append
		        insert subject_id Vn
	*/

	// 获取所有subject_id

	type SubjectGroupRegister struct {
		ID            primitive.ObjectID `bson:"_id"`
		Group         string             `bson:"group"`
		RegisterGroup string             `bson:"register_group"`
	}
	var subjectGroupRegister []SubjectGroupRegister

	cursor, err := tools.Database.Collection("subject").Aggregate(sctx, mongo.Pipeline{
		{{"$match", bson.M{"env_id": visitCycle.EnvironmentID, "cohort_id": visitCycle.CohortID, "deleted": bson.M{"$ne": true}}}},
		//{{Key: "$group", Value: bson.M{"_id": "$group", "subject_id": bson.M{"$push": "$_id"}}}},
	})
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &subjectGroupRegister)
	if err != nil {
		return errors.WithStack(err)
	}

	for i, item := range subjectGroupRegister {
		if item.RegisterGroup != "" {
			subjectGroupRegister[i].Group = item.RegisterGroup
		}
	}

	groupSubject := slice.GroupWith(subjectGroupRegister, func(item SubjectGroupRegister) string {
		return item.Group
	})

	subjectGroups := []models.SubjectGroup{}

	for key, item := range groupSubject {
		IDs := make([]primitive.ObjectID, 0)
		slice.ForEach(item, func(index int, item SubjectGroupRegister) {
			IDs = append(IDs, item.ID)
		})
		subjectGroups = append(subjectGroups, models.SubjectGroup{
			Group:     key,
			SubjectID: IDs,
		})

	}

	for j, info := range visitCycle.Infos {
		// 检查发药随机过的访视
		err := checkDispensingVisit(ctx, sctx, visitCycle.ID, info.ID)
		if err != nil {
			continue
		}

		index := -1
		for i, cycleInfo := range visitCycle.ConfigInfo.Infos {
			if info.Number == cycleInfo.Number {
				index = i
				break
			}
		}
		if index > -1 {
			currentVisit := visitCycle.ConfigInfo.Infos[index]
			configDeep := reflect.DeepEqual(currentVisit, info)

			if !configDeep || index != j { //配置修改过， 顺序修改过 更新受试者访视
				updateSubjectIDS := []primitive.ObjectID{}
				insertSubjectIDS := []primitive.ObjectID{}
				removeSubjectIDS := []primitive.ObjectID{}
				for _, group := range subjectGroups {
					_, ok := slice.Find(currentVisit.Group, func(index int, item interface{}) bool {
						return group.Group == item || group.Group == ""
					})
					if ok {
						insertSubjectIDStmp, updateSubjectIDStmp, err := returnUpdateAndInsertSubject(sctx, visitCycle, group, currentVisit)
						if err != nil {
							return errors.WithStack(err)
						}
						updateSubjectIDS = append(updateSubjectIDS, updateSubjectIDStmp...)
						insertSubjectIDS = append(insertSubjectIDS, insertSubjectIDStmp...)
					} else {
						removeSubjectIDS = append(removeSubjectIDS, group.SubjectID...)
					}
				}
				{ // 存在发布版本.group == subject.group || subject.group == ""
					err = updateSubjectDispensing(sctx, updateSubjectIDS, currentVisit, index, visitCycle)
					if err != nil {
						return errors.WithStack(err)
					}

				}
				{ //之前组别上没有 配置加上 需要insert
					err = insertSubjectDispensing(sctx, insertSubjectIDS, currentVisit, index, visitCycle)
					if err != nil {
						return errors.WithStack(err)
					}
				}
				{ //修改其中一个组别 删除对应组别受试者这个访视
					err = removeSubjectDispensing(sctx, removeSubjectIDS, currentVisit, visitCycle, false)
					if err != nil {
						return errors.WithStack(err)
					}

				}

			}
		} else {
			// 删除访视
			err = removeSubjectDispensing(sctx, []primitive.ObjectID{}, info, visitCycle, true)
			if err != nil {
				return errors.WithStack(err)
			}

		}
	}
	for i, info := range visitCycle.ConfigInfo.Infos {
		_, ok := slice.Find(visitCycle.Infos, func(index int, item models.VisitCycleInfo) bool {
			return info.Number == item.Number
		})
		if !ok { // TODO之前配置上没有 插入访视
			//dispensingVisit 查询不同组别的受试者
			insertSubjectIDS := []primitive.ObjectID{}
			for _, group := range subjectGroups {
				_, ok := slice.Find(info.Group, func(index int, item interface{}) bool {
					return group.Group == item || group.Group == ""
				})
				if ok {
					insertSubjectIDS = append(insertSubjectIDS, group.SubjectID...)
				}
			}
			err = insertSubjectDispensing(sctx, insertSubjectIDS, info, i, visitCycle)
			if err != nil {
				return errors.WithStack(err)
			}
		}
	}

	return nil
}

func removeSubjectDispensing(sctx mongo.SessionContext, removeSubjectIDS []primitive.ObjectID, currentVisit models.VisitCycleInfo, visitCycle models.VisitCycle, allRemove bool) error {
	if allRemove {
		_, err := tools.Database.Collection("dispensing").DeleteMany(sctx,
			bson.M{"env_id": visitCycle.EnvironmentID, "cohort_id": visitCycle.CohortID, "visit_info.number": currentVisit.Number, "status": 1})
		if err != nil {
			return errors.WithStack(err)
		}
	} else {
		if len(removeSubjectIDS) > 0 {
			_, err := tools.Database.Collection("dispensing").DeleteMany(sctx,
				bson.M{"status": 1, "env_id": visitCycle.EnvironmentID, "cohort_id": visitCycle.CohortID, "visit_info.number": currentVisit.Number, "subject_id": bson.M{"$in": removeSubjectIDS}})
			if err != nil {
				return errors.WithStack(err)
			}
		}
	}

	return nil
}

func updateSubjectDispensing(sctx mongo.SessionContext, updateSubjectIDS []primitive.ObjectID, currentVisit models.VisitCycleInfo, index int, visitCycle models.VisitCycle) error {
	if len(updateSubjectIDS) > 0 {
		VisitInfo := models.VisitInfo{
			VisitCycleInfoID: currentVisit.ID,
			Number:           currentVisit.Number,
			InstanceRepeatNo: "0",
			BlockRepeatNo:    "0",
			Name:             currentVisit.Name,
			Random:           currentVisit.Random,
			Dispensing:       currentVisit.Dispensing,
		}
		_, err := tools.Database.Collection("dispensing").UpdateMany(sctx,
			bson.M{"status": 1, "env_id": visitCycle.EnvironmentID, "cohort_id": visitCycle.CohortID, "subject_id": bson.M{"$in": updateSubjectIDS}, "visit_info.number": currentVisit.Number},
			bson.M{"$set": bson.M{
				"visit_info":    VisitInfo,
				"serial_number": (index + 1) * 100,
			}})
		if err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

func insertSubjectDispensing(sctx mongo.SessionContext, insertSubjectIDS []primitive.ObjectID, info models.VisitCycleInfo, i int, visitCycle models.VisitCycle) error {
	dispensing := []interface{}{}

	for _, subjectID := range insertSubjectIDS {
		dispensing = append(dispensing, models.Dispensing{
			ID:            primitive.NewObjectID(),
			CustomerID:    visitCycle.CustomerID,
			ProjectID:     visitCycle.ProjectID,
			EnvironmentID: visitCycle.EnvironmentID,
			CohortID:      visitCycle.CohortID,
			SubjectID:     subjectID,
			VisitInfo: models.VisitInfo{
				VisitCycleInfoID: info.ID,
				Number:           info.Number,
				InstanceRepeatNo: "0",
				BlockRepeatNo:    "0",
				Name:             info.Name,
				Random:           info.Random,
				Dispensing:       info.Dispensing,
			},
			SerialNumber: (i + 1) * 100,
			VisitSign:    false,
			Status:       1,
			Reasons:      []models.Reason{},
		})
	}
	if len(dispensing) > 0 {
		_, err := tools.Database.Collection("dispensing").InsertMany(sctx, dispensing)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

func returnUpdateAndInsertSubject(sctx mongo.SessionContext, visitCycle models.VisitCycle, group models.SubjectGroup, currentVisit models.VisitCycleInfo) ([]primitive.ObjectID, []primitive.ObjectID, error) {
	updateSubjectIDS := []primitive.ObjectID{}
	insertSubjectIDS := []primitive.ObjectID{}
	var dispensing []models.Dispensing
	cursor, err := tools.Database.Collection("dispensing").Find(sctx, bson.M{"env_id": visitCycle.EnvironmentID, "cohort_id": visitCycle.CohortID, "subject_id": bson.M{"$in": group.SubjectID}, "visit_info.number": currentVisit.Number})
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &dispensing)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}
	subjectMap := map[primitive.ObjectID]bool{}
	for _, item := range dispensing {
		subjectMap[item.SubjectID] = true
	}

	for _, item := range group.SubjectID {
		if subjectMap[item] {
			updateSubjectIDS = append(updateSubjectIDS, item)
		} else {
			insertSubjectIDS = append(insertSubjectIDS, item)
		}
	}
	return insertSubjectIDS, updateSubjectIDS, nil
}

func (s *DrugConfigureService) DragSort(ctx *gin.Context, ID string, sortIDs []interface{}) error {
	OID, _ := primitive.ObjectIDFromHex(ID)
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var visitCycle models.VisitCycle
		var visitCycleConfigs []models.VisitCycleInfo
		err := tools.Database.Collection("visit_cycle").FindOne(sctx, bson.M{"_id": OID}).Decode(&visitCycle)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		for i, info := range sortIDs {
			newIndex := i
			visitCycleInfo := models.VisitCycleInfo{}

			for j, item := range visitCycle.ConfigInfo.Infos {
				if info.(string) == item.ID.Hex() {
					newIndex = j
					visitCycleInfo = item
					break
				}
			}

			if !visitCycleInfo.ID.IsZero() {
				if i != newIndex { // 改变过顺序的访视 校验下是否发过药 发过药不给改顺序
					err = checkDispensingVisit(ctx, sctx, OID, visitCycleInfo.ID)
					if err != nil {
						return nil, err
					}
				}
				visitCycleConfigs = append(visitCycleConfigs, visitCycleInfo)
			}

		}
		_, err = tools.Database.Collection("visit_cycle").UpdateOne(sctx,
			bson.M{"_id": OID},
			bson.M{"$set": bson.M{
				"update_infos.infos":           visitCycleConfigs,
				"update_infos.meta.updated_at": time.Duration(time.Now().Unix()),
			}})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		newVisitCycle := models.VisitCycle{}
		newVisitCycle.ConfigInfo.Infos = visitCycleConfigs
		logOID := visitCycle.EnvironmentID
		if !visitCycle.CohortID.IsZero() {
			logOID = visitCycle.CohortID
		}

		err = insertVisitDragLog(ctx, sctx, logOID, 2, visitCycle, newVisitCycle, visitCycle.ID)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil

}

func (s *DrugConfigureService) VisitHistory(ctx *gin.Context, envID, cohortID primitive.ObjectID, date []int32, version *string, limit, skip int) (interface{}, error) {
	var visitInfos []models.VisitInfos
	//isBlindedRole, err := tools.IsBlindedRole(roleID)
	//if err != nil {
	//	return nil, err
	//}
	match := bson.M{"env_id": envID, "cohort_id": cohortID}
	pipeline := mongo.Pipeline{
		{{"$match", match}},
		{{"$unwind", "$history_info"}},
	}
	if len(date) > 0 {
		pipeline = append(pipeline, bson.D{{"$match", bson.M{"history_info.meta.updated_at": bson.M{"$gte": date[0]}}}})
		pipeline = append(pipeline, bson.D{{"$match", bson.M{"history_info.meta.updated_at": bson.M{"$lte": date[1]}}}})
	}
	if version != nil {
		pipeline = append(pipeline, bson.D{{"$match", bson.M{"history_info.version": version}}})
	}

	pipeline = append(pipeline, bson.D{{"$project", bson.M{
		"visit_type": 1,
		"version":    "$history_info.version",
		"infos":      "$history_info.infos",
		"meta":       "$history_info.meta",
	}}})
	pipeline = append(pipeline, bson.D{{"$sort", bson.M{"meta.updated_at": -1}}})
	pipeline = append(pipeline, bson.D{{"$limit", limit}})
	pipeline = append(pipeline, bson.D{{"$skip", skip}})

	cursor, err := tools.Database.Collection("visit_cycle").Aggregate(nil, pipeline)

	if err != nil {
		return nil, errors.WithStack(err)
	}

	err = cursor.All(nil, &visitInfos)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, match).Decode(&attribute)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var randomDesign models.RandomDesign
	err = tools.Database.Collection("random_design").FindOne(nil, match).Decode(&randomDesign)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}

	//if isBlindedRole {
	//
	//}

	for n, visitCycle := range visitInfos {
		for i, info := range visitCycle.Infos {
			for j, group := range info.Group {

				allSaltName := base64.StdEncoding.EncodeToString([]byte(group.(string)))
				visitInfos[n].Infos[i].Group[j] = allSaltName
			}
		}
	}
	return map[string]interface{}{"total": len(visitInfos), "data": visitInfos}, nil
}

func checkDrugConfigureInfo(ctx *gin.Context, updateDrugConfigureInfo models.DrugConfigureInfo, visitCycle models.VisitCycle) error {
	randomDispensing := false

	for _, info := range visitCycle.ConfigInfo.Infos {
		if info.Random && info.Dispensing {
			randomDispensing = true
		}
	}

	if updateDrugConfigureInfo.CalculationType == 4 {
		// 校验自定义公式
		if !tools.CustomFormulaValidation(updateDrugConfigureInfo.CustomerCalculation) {
			return tools.BuildServerError(ctx, "visit_cycle_formula_customer_error", tools.DrugFormulaError)
		}
	}
	if updateDrugConfigureInfo.OpenSetting == 3 {
		for _, value := range updateDrugConfigureInfo.Values {
			if updateDrugConfigureInfo.CalculationType == 1 || updateDrugConfigureInfo.CalculationType == 2 {
				for _, formula := range value.Formulas {
					if !tools.RangeFormulaValidation(formula.Expression) {
						return tools.BuildServerError(ctx, "visit_cycle_formula_error")
					}
				}
			}

			if !randomDispensing {
				if value.ComparisonType != nil && *value.ComparisonType == 3 {
					return tools.BuildServerError(ctx, "visit_cycle_formula_visit_error")
				}
				if value.CurrentComparisonType != nil && *value.CurrentComparisonType == 3 {
					return tools.BuildServerError(ctx, "visit_cycle_visit_formula_error")
				}
			}
		}
	}
	return nil

}

func checkDrugConfigureVisit(ctx *gin.Context, drugConfigure models.DrugConfigure, visitCycle models.VisitCycleInfo) error {
	randomUnDispensing := false
	if visitCycle.Random && !visitCycle.Dispensing {
		randomUnDispensing = true
	}

	for _, configure := range drugConfigure.Configures {
		for _, value := range configure.Values {
			if randomUnDispensing {
				if value.ComparisonType != nil && *value.ComparisonType == 3 {
					return tools.BuildServerError(ctx, "visit_cycle_formula_visit_error")
				}
				if value.CurrentComparisonType != nil && *value.CurrentComparisonType == 3 {
					return tools.BuildServerError(ctx, "visit_cycle_visit_formula_error")
				}
			}
		}
	}
	return nil
}

func checkFormulaUnique(ctx *gin.Context, envOID, cohortOID primitive.ObjectID, drugConfigureInfos models.DrugConfigure) error {
	drugConfigureInfo := drugConfigureInfos.Configures[0]
	var drugConfigure models.DrugConfigure
	tools.Database.Collection("drug_configure").FindOne(nil, bson.M{"env_id": envOID, "cohort_id": cohortOID}).Decode(&drugConfigure)
	// 当前 药物名称 + 方式 唯一
	infos := map[string]int{}
	// 当前 药物名称 + 公式类型 唯一
	formulaUnique := map[string]int{}

	for _, configure := range drugConfigure.Configures {
		if drugConfigureInfo.ID == configure.ID && drugConfigure.CohortID == cohortOID {
			continue // 不与自身比较 按标签的不比较
		}
		for _, value := range configure.Values {
			infos[value.DrugName] = configure.OpenSetting
			if configure.OpenSetting == 3 {
				formulaUnique[value.DrugName] = configure.CalculationType
			}
		}
	}
	for _, value := range drugConfigureInfo.Values {
		if infos[value.DrugName] != 0 && drugConfigureInfo.OpenSetting != infos[value.DrugName] {
			return tools.BuildServerError(ctx, "medicine_open_setting_repeat3")
		}

		if formulaUnique[value.DrugName] != 0 && formulaUnique[value.DrugName] != drugConfigureInfo.CalculationType {
			return tools.BuildServerError(ctx, "medicine_other_repeat_formula")
		}
	}

	return nil
}

func (s *DrugConfigureService) GetSetUpDrugConfigure(ctx *gin.Context, customerID string, projectID string, envID string, cohortID string, roleID string) (models.DrugConfigureSetting, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)

	drugConfigureSetting := models.DrugConfigureSetting{}

	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return drugConfigureSetting, errors.WithStack(err)
	}

	filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if cohortID != "" {
		filter["cohort_id"] = cohortOID
	}

	err = tools.Database.Collection("drug_configure_setting").FindOne(nil, filter).Decode(&drugConfigureSetting)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return drugConfigureSetting, errors.WithStack(err)
	}

	var drugConfigure models.DrugConfigure
	err = tools.Database.Collection("drug_configure").FindOne(nil, filter).Decode(&drugConfigure)
	if err != nil && err != mongo.ErrNoDocuments {
		return drugConfigureSetting, errors.WithStack(err)
	}

	if isBlindedRole {
		//DTP研究产品
		dtpIpList := make([]models.DtpIp, 0)
		if drugConfigureSetting.DtpIpList != nil && len(drugConfigureSetting.DtpIpList) > 0 {
			for _, dtpIp := range drugConfigureSetting.DtpIpList {
				isBlindedDrug, _ := tools.IsBlindedDrug(drugConfigureSetting.EnvironmentID, dtpIp.IP)
				if isBlindedDrug {
					dtpIp.IP = tools.BlindData
				}
				dtpIpList = append(dtpIpList, dtpIp)
			}
		}
		drugConfigureSetting.DtpIpList = dtpIpList

		//剂量水平
		if drugConfigureSetting.DoseLevelList != nil && len(drugConfigureSetting.DoseLevelList) > 0 {
			doseLevelList := make([]models.DoseLevel, 0)
			for _, level := range drugConfigureSetting.DoseLevelList {
				doseLevel := level
				//if doseLevel.Group != nil && len(doseLevel.Group) > 0 {
				//	grouplList := make([]string, 0)
				//	for _, gp := range doseLevel.Group {
				//		gp = tools.BlindData
				//		grouplList = append(grouplList, gp)
				//	}
				//	doseLevel.Group = grouplList
				//}
				if doseLevel.DoseDistribution != nil && len(doseLevel.DoseDistribution) > 0 {
					doseDistributionList := make([]string, 0)
					for _, db := range doseLevel.DoseDistribution {
						doseDistribution := db
						var jsonObject map[string]interface{}
						json.Unmarshal([]byte(db), &jsonObject)
						id := jsonObject["id"].(string)
						OID, _ := primitive.ObjectIDFromHex(id)
						name := jsonObject["name"].(string)

						drugConfigureInfo, a := slice.Find(drugConfigure.Configures, func(index int, item models.DrugConfigureInfo) bool {
							return item.ID == OID
						})
						if a {
							if drugConfigureInfo.OpenSetting == 1 {
								doseDistribution = tools.BlindData
							} else if drugConfigureInfo.OpenSetting == 3 {
								if drugConfigureInfo.Values != nil && len(drugConfigureInfo.Values) > 0 {
									drugValue, b := slice.Find(drugConfigureInfo.Values, func(index int, item models.DrugValue) bool {
										return item.DrugName == name
									})
									if b {
										if !drugValue.IsOpen {
											if len(drugValue.DrugSpec) != 0 {
												doseDistribution = tools.BlindData + "//" + drugValue.DrugSpec
											} else {
												doseDistribution = tools.BlindData + "//"
											}
										}
									}
								}
							}
						}
						doseDistributionList = append(doseDistributionList, doseDistribution)
					}
					doseLevel.DoseDistribution = doseDistributionList
				}
				doseLevelList = append(doseLevelList, doseLevel)
			}
			drugConfigureSetting.DoseLevelList = doseLevelList
		}

		//访视判断
		if drugConfigureSetting.VisitJudgmentList != nil && len(drugConfigureSetting.VisitJudgmentList) > 0 {
			visitJudgmentList := make([]models.VisitJudgment, 0)
			for _, vj := range drugConfigureSetting.VisitJudgmentList {
				visitJudgment := vj
				//if vj.Group != nil && len(vj.Group) > 0 {
				//	grouplList := make([]string, 0)
				//	for _, gp := range vj.Group {
				//		gp = tools.BlindData
				//		grouplList = append(grouplList, gp)
				//	}
				//	visitJudgment.Group = grouplList
				//}
				if vj.DoseDistribution != nil && len(vj.DoseDistribution) > 0 {
					doseDistributionList := make([]string, 0)
					for _, db := range vj.DoseDistribution {
						doseDistribution := db
						var jsonObject map[string]interface{}
						json.Unmarshal([]byte(db), &jsonObject)
						id := jsonObject["id"].(string)
						OID, _ := primitive.ObjectIDFromHex(id)
						name := jsonObject["name"].(string)

						drugConfigureInfo, a := slice.Find(drugConfigure.Configures, func(index int, item models.DrugConfigureInfo) bool {
							return item.ID == OID
						})
						if a {
							if drugConfigureInfo.OpenSetting == 1 {
								doseDistribution = tools.BlindData
							} else if drugConfigureInfo.OpenSetting == 3 {
								if drugConfigureInfo.Values != nil && len(drugConfigureInfo.Values) > 0 {
									drugValue, b := slice.Find(drugConfigureInfo.Values, func(index int, item models.DrugValue) bool {
										return item.DrugName == name
									})
									if b {
										if !drugValue.IsOpen {
											if len(drugValue.DrugSpec) != 0 {
												doseDistribution = tools.BlindData + "//" + drugValue.DrugSpec
											} else {
												doseDistribution = tools.BlindData + "//"
											}
										}
									}
								}
							}
						}
						doseDistributionList = append(doseDistributionList, doseDistribution)
					}
					visitJudgment.DoseDistribution = doseDistributionList
				}
				visitJudgmentList = append(visitJudgmentList, visitJudgment)
			}
			drugConfigureSetting.VisitJudgmentList = visitJudgmentList
		}
	}

	return drugConfigureSetting, nil
}

// IntToStringSlice 将 []int 转换为 []string
func IntToStringSlice(intSlice []int) []string {
	stringSlice := make([]string, len(intSlice))
	for i, v := range intSlice {
		stringSlice[i] = strconv.Itoa(v)
	}
	return stringSlice
}

func (s *DrugConfigureService) SetUpDrugConfigure(ctx *gin.Context, drugConfigureSetting models.DrugConfigureSetting) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		customerOID := drugConfigureSetting.CustomerID
		projectID := drugConfigureSetting.ProjectID
		envOID := drugConfigureSetting.EnvironmentID
		cohortID := drugConfigureSetting.CohortID
		collection := tools.Database.Collection("drug_configure_setting")
		OID := envOID
		filter := bson.M{"customer_id": customerOID, "project_id": projectID, "env_id": envOID}
		if !cohortID.IsZero() {
			filter["cohort_id"] = cohortID
			OID = cohortID
		}

		if drugConfigureSetting.DtpIpList != nil && len(drugConfigureSetting.DtpIpList) > 0 {
			var drugConfigure models.DrugConfigure
			err := tools.Database.Collection("drug_configure").FindOne(sctx, filter).Decode(&drugConfigure)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}

			// TODO
			// 创建map，用于快速查找
			labelDrugNameMap := make(map[string]([]string))
			if drugConfigure.Configures != nil && len(drugConfigure.Configures) > 0 {
				for _, configure := range drugConfigure.Configures {
					if &configure.Label != nil {
						if configure.Values != nil && len(configure.Values) > 0 {
							drugNameList := make([]string, 0)
							for _, value := range configure.Values {
								drugNameList = append(drugNameList, value.DrugName)
							}
							labelDrugNameMap[configure.Label] = drugNameList
						}
					}
				}
			}

			for _, v := range labelDrugNameMap {
				dtpTypeList := make([]string, 0)
				for _, dtpIp := range drugConfigureSetting.DtpIpList {
					index := arrays.ContainsString(v, dtpIp.IP)
					if index != -1 {
						if dtpIp.DtpTypeList != nil && len(dtpIp.DtpTypeList) > 0 {
							// 使用自定义的 IntToStringSlice 函数进行转换
							stringSlice := IntToStringSlice(dtpIp.DtpTypeList)
							typeList := strings.Join(stringSlice, ",")
							typeIndex := arrays.ContainsString(dtpTypeList, typeList)
							if typeIndex == -1 {
								dtpTypeList = append(dtpTypeList, typeList)
								if len(dtpTypeList) > 1 {
									return nil, tools.BuildServerError(ctx, "combined_dispensation_label_duplicated")
								}
							}
						}
					}
				}
			}

		}

		var drugConfigureSettingCohortOld models.DrugConfigureSetting
		err := tools.Database.Collection("drug_configure_setting").FindOne(sctx, filter).Decode(&drugConfigureSettingCohortOld)
		if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errors.WithStack(err)
		}

		old := drugConfigureSettingCohortOld

		if count, _ := collection.CountDocuments(sctx, filter); count > 0 {
			edit := bson.M{"dtp_ip_list": drugConfigureSetting.DtpIpList}
			edit["dtp_ip_list"] = drugConfigureSetting.DtpIpList
			if drugConfigureSetting.IsOpen {
				edit["is_open"] = true
				edit["select_type"] = drugConfigureSetting.SelectType
				edit["dose_form_id"] = drugConfigureSetting.DoseFormId

				doseLevelList := make([]models.DoseLevel, 0)
				visitJudgmentList := make([]models.VisitJudgment, 0)
				if drugConfigureSetting.SelectType == 1 {
					//清空访视判断表单
					edit["visit_judgment_list"] = visitJudgmentList
					edit["group_type"] = bson.A{}

					edit["is_first_initial"] = drugConfigureSetting.IsFirstInitial
					edit["is_dose_reduction"] = drugConfigureSetting.IsDoseReduction
					edit["frequency"] = drugConfigureSetting.Frequency
					if drugConfigureSetting.DoseLevelList != nil && len(drugConfigureSetting.DoseLevelList) > 0 {
						for _, doseLevel := range drugConfigureSetting.DoseLevelList {
							if doseLevel.ID == primitive.NilObjectID {
								doseLevel.ID = primitive.NewObjectID()
							}
							doseLevelList = append(doseLevelList, doseLevel)
						}
					}
					edit["dose_level_list"] = doseLevelList

				} else if drugConfigureSetting.SelectType == 2 {
					//清空剂量水平表单
					edit["dose_level_list"] = doseLevelList
					edit["is_first_initial"] = false
					edit["is_dose_reduction"] = false
					edit["frequency"] = nil

					if drugConfigureSetting.VisitJudgmentList != nil && len(drugConfigureSetting.VisitJudgmentList) > 0 {
						for _, visitJudgment := range drugConfigureSetting.VisitJudgmentList {
							if visitJudgment.ID == primitive.NilObjectID {
								visitJudgment.ID = primitive.NewObjectID()
							}
							visitJudgmentList = append(visitJudgmentList, visitJudgment)
						}
					}
					edit["visit_judgment_list"] = visitJudgmentList
					edit["group_type"] = drugConfigureSetting.GroupType
				}

				isExistence := false
				formRecordList := make([]models.FormRecord, 0)
				if drugConfigureSetting.FormRecordList != nil && len(drugConfigureSetting.FormRecordList) > 0 {
					for _, formRecord := range drugConfigureSetting.FormRecordList {
						if formRecord.SelectType == drugConfigureSetting.SelectType && formRecord.DoseFormId == drugConfigureSetting.DoseFormId {

							//old.SelectType = formRecord.SelectType
							//old.DoseFormId = formRecord.DoseFormId
							old.IsFirstInitial = formRecord.IsFirstInitial
							old.IsDoseReduction = formRecord.IsDoseReduction
							old.Frequency = formRecord.Frequency
							old.DoseLevelList = formRecord.DoseLevelList
							old.VisitJudgmentList = formRecord.VisitJudgmentList

							isExistence = true
							formRecord.IsFirstInitial = drugConfigureSetting.IsFirstInitial
							formRecord.IsDoseReduction = drugConfigureSetting.IsDoseReduction
							formRecord.Frequency = drugConfigureSetting.Frequency
							formRecord.DoseLevelList = doseLevelList
							formRecord.VisitJudgmentList = visitJudgmentList
							formRecord.GroupType = drugConfigureSetting.GroupType
						}
						formRecordList = append(formRecordList, formRecord)
					}
				}

				if !isExistence {

					old.IsFirstInitial = false
					old.IsDoseReduction = false
					old.Frequency = nil
					old.DoseLevelList = make([]models.DoseLevel, 0)
					old.VisitJudgmentList = make([]models.VisitJudgment, 0)

					var formRecord models.FormRecord
					formRecord.SelectType = drugConfigureSetting.SelectType
					formRecord.DoseFormId = drugConfigureSetting.DoseFormId
					formRecord.IsFirstInitial = drugConfigureSetting.IsFirstInitial
					formRecord.IsDoseReduction = drugConfigureSetting.IsDoseReduction
					formRecord.Frequency = drugConfigureSetting.Frequency
					formRecord.DoseLevelList = doseLevelList
					formRecord.VisitJudgmentList = visitJudgmentList
					formRecordList = append(formRecordList, formRecord)
				}

				edit["form_record_list"] = formRecordList
			} else {
				edit["is_open"] = false
				edit["select_type"] = 1
				edit["dose_form_id"] = ""
				//清空访视判断表单
				visitJudgmentList := make([]models.VisitJudgment, 0)
				edit["visit_judgment_list"] = visitJudgmentList

				edit["is_first_initial"] = drugConfigureSetting.IsFirstInitial
				edit["is_dose_reduction"] = drugConfigureSetting.IsDoseReduction
				edit["frequency"] = drugConfigureSetting.Frequency
				//清空剂量水平表单
				doseLevelList := make([]models.DoseLevel, 0)
				edit["dose_level_list"] = doseLevelList
			}

			update := bson.M{"$set": edit}
			_, err := collection.UpdateOne(sctx, filter, update)
			if err != nil {
				return nil, errors.WithStack(err)
			}

		} else {

			if drugConfigureSetting.IsOpen {
				drugConfigureSetting.ID = primitive.NewObjectID()
				doseLevelList := make([]models.DoseLevel, 0)
				if drugConfigureSetting.DoseLevelList != nil && len(drugConfigureSetting.DoseLevelList) > 0 {
					for _, doseLevel := range drugConfigureSetting.DoseLevelList {
						doseLevel.ID = primitive.NewObjectID()
						doseLevelList = append(doseLevelList, doseLevel)
					}
				}
				drugConfigureSetting.DoseLevelList = doseLevelList

				visitJudgmentList := make([]models.VisitJudgment, 0)
				if drugConfigureSetting.VisitJudgmentList != nil && len(drugConfigureSetting.VisitJudgmentList) > 0 {
					for _, visitJudgment := range drugConfigureSetting.VisitJudgmentList {
						visitJudgment.ID = primitive.NewObjectID()
						visitJudgmentList = append(visitJudgmentList, visitJudgment)
					}
				}
				drugConfigureSetting.VisitJudgmentList = visitJudgmentList

				isExistence := false
				formRecordList := make([]models.FormRecord, 0)
				if drugConfigureSetting.FormRecordList != nil && len(drugConfigureSetting.FormRecordList) > 0 {
					for _, formRecord := range drugConfigureSetting.FormRecordList {
						if formRecord.SelectType == drugConfigureSetting.SelectType && formRecord.DoseFormId == drugConfigureSetting.DoseFormId {

							//old.SelectType = formRecord.SelectType
							//old.DoseFormId = formRecord.DoseFormId
							old.IsFirstInitial = formRecord.IsFirstInitial
							old.IsDoseReduction = formRecord.IsDoseReduction
							old.Frequency = formRecord.Frequency
							old.DoseLevelList = formRecord.DoseLevelList
							old.VisitJudgmentList = formRecord.VisitJudgmentList

							isExistence = true
							formRecord.IsFirstInitial = drugConfigureSetting.IsFirstInitial
							formRecord.IsDoseReduction = drugConfigureSetting.IsDoseReduction
							formRecord.Frequency = drugConfigureSetting.Frequency
							formRecord.DoseLevelList = doseLevelList
							formRecord.VisitJudgmentList = visitJudgmentList
						}
						formRecordList = append(formRecordList, formRecord)
					}
				}

				if !isExistence {

					old.IsFirstInitial = false
					old.IsDoseReduction = false
					old.Frequency = nil
					old.DoseLevelList = make([]models.DoseLevel, 0)
					old.VisitJudgmentList = make([]models.VisitJudgment, 0)

					var formRecord models.FormRecord
					formRecord.SelectType = drugConfigureSetting.SelectType
					formRecord.DoseFormId = drugConfigureSetting.DoseFormId
					formRecord.IsFirstInitial = drugConfigureSetting.IsFirstInitial
					formRecord.IsDoseReduction = drugConfigureSetting.IsDoseReduction
					formRecord.Frequency = drugConfigureSetting.Frequency
					formRecord.DoseLevelList = doseLevelList
					formRecord.VisitJudgmentList = visitJudgmentList
					formRecordList = append(formRecordList, formRecord)
				}

				drugConfigureSetting.FormRecordList = formRecordList

			} else {
				drugConfigureSetting.ID = primitive.NewObjectID()
				drugConfigureSetting.SelectType = 1
				drugConfigureSetting.DoseFormId = ""
				drugConfigureSetting.IsFirstInitial = false
				drugConfigureSetting.IsDoseReduction = false
				drugConfigureSetting.Frequency = nil
				//清空剂量水平表单
				doseLevelList := make([]models.DoseLevel, 0)
				drugConfigureSetting.DoseLevelList = doseLevelList
				//清空访视判断表单
				visitJudgmentList := make([]models.VisitJudgment, 0)
				drugConfigureSetting.VisitJudgmentList = visitJudgmentList

			}

			if _, err := collection.InsertOne(sctx, drugConfigureSetting); err != nil {
				return nil, errors.WithStack(err)
			}

		}

		//保存项目日志
		logOID := drugConfigureSetting.EnvironmentID
		if drugConfigureSetting.CohortID != primitive.NilObjectID {
			logOID = drugConfigureSetting.CohortID
		}
		err = insertDrugConfigureSettingLog(ctx, nil, logOID, 2, old, drugConfigureSetting, OID, drugConfigureSetting.EnvironmentID, drugConfigureSetting.CohortID, "")
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}

	return nil
}

func (s *DrugConfigureService) GetSetUpDrugConfigureLabel(ctx *gin.Context, drugConfigureSettingLabel models.DrugConfigureSettingLabel) ([]models.DoseSettingLabel, error) {
	doseSettingLabels := make([]models.DoseSettingLabel, 0)
	customerOID := drugConfigureSettingLabel.CustomerID
	projectID := drugConfigureSettingLabel.ProjectID
	envOID := drugConfigureSettingLabel.EnvironmentID
	cohortID := drugConfigureSettingLabel.CohortID
	filter := bson.M{"customer_id": customerOID, "project_id": projectID, "env_id": envOID}
	if !cohortID.IsZero() {
		filter["cohort_id"] = cohortID
	}
	var drugConfigure models.DrugConfigure
	err := tools.Database.Collection("drug_configure").FindOne(nil, filter).Decode(&drugConfigure)
	if err != nil && err != mongo.ErrNoDocuments {
		return doseSettingLabels, errors.WithStack(err)
	}
	var randomDesign models.RandomDesign
	err = tools.Database.Collection("random_design").FindOne(nil, filter).Decode(&randomDesign)
	if err != nil && err != mongo.ErrNoDocuments {
		return doseSettingLabels, errors.WithStack(err)
	}
	if drugConfigureSettingLabel.RoleID != "" {
		var attribute models.Attribute
		err := tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute)
		if err != nil && err != mongo.ErrNoDocuments {
			return doseSettingLabels, err
		}

		isBlindedRole, err := tools.IsBlindedRole(drugConfigureSettingLabel.RoleID)
		if err != nil {
			return doseSettingLabels, err
		}
		for i := 0; i < len(drugConfigure.Configures); i++ {
			for j := 0; j < len(drugConfigure.Configures[i].Values); j++ {
				drugName := drugConfigure.Configures[i].Values[j].DrugName
				isBlindedDrug, _ := tools.IsBlindedDrug(envOID, drugName)
				if isBlindedDrug && isBlindedRole {
					drugConfigure.Configures[i].Values[j].DrugName = tools.BlindData
				}
				if drugConfigure.Configures[i].OpenSetting == 1 && drugConfigure.Configures[i].Values[j].CustomDispensingNumber == "" {
					drugConfigure.Configures[i].Values[j].CustomDispensingNumber = convertor.ToString(drugConfigure.Configures[i].Values[j].DispensingNumber)
				}
			}
		}
		type subGroup struct {
			group    string
			subGroup string
			blind    bool
		}
		blindSubGroups := make([]subGroup, 0)
		for _, group := range randomDesign.Info.Groups {
			if group.SubGroup != nil && len(group.SubGroup) > 0 {
				for _, sg := range group.SubGroup {
					blindSubGroups = append(blindSubGroups, subGroup{
						group:    group.Name,
						subGroup: sg.Name,
						blind:    sg.Blind,
					})
				}
			}
		}

		for i, configure := range drugConfigure.Configures {
			if configure.SubName != "" {
				sP, b := slice.Find(blindSubGroups, func(index int, item subGroup) bool {
					return item.group == configure.ParName && item.subGroup == configure.SubName
				})
				if isBlindedRole && b {
					ss := *sP
					if attribute.AttributeInfo.Blind && ss.blind {
						drugConfigure.Configures[i].Group = tools.BlindData + " " + tools.BlindData
						drugConfigure.Configures[i].ParName = tools.BlindData
						drugConfigure.Configures[i].SubName = tools.BlindData
					} else if !attribute.AttributeInfo.Blind && ss.blind {
						drugConfigure.Configures[i].Group = configure.ParName + " " + tools.BlindData
						drugConfigure.Configures[i].ParName = configure.ParName
						drugConfigure.Configures[i].SubName = tools.BlindData
					} else if attribute.AttributeInfo.Blind && !ss.blind {
						drugConfigure.Configures[i].Group = tools.BlindData + " " + configure.SubName
						drugConfigure.Configures[i].ParName = tools.BlindData
						drugConfigure.Configures[i].SubName = configure.SubName
					}
				}
			} else {
				if attribute.AttributeInfo.Blind && isBlindedRole {
					for i := 0; i < len(drugConfigure.Configures); i++ {
						drugConfigure.Configures[i].Group = tools.BlindData
						drugConfigure.Configures[i].ParName = tools.BlindData
					}
				} else {
					for i := 0; i < len(drugConfigure.Configures); i++ {
						if drugConfigure.Configures[i].ParName == "" {
							drugConfigure.Configures[i].ParName = drugConfigure.Configures[i].Group
						}
					}
				}
			}

			if drugConfigureSettingLabel.Groups != nil && len(drugConfigureSettingLabel.Groups) > 0 {
				for _, gp := range drugConfigureSettingLabel.Groups {
					if gp == drugConfigure.Configures[i].Group {
						if drugConfigure.Configures[i].OpenSetting == 1 {
							//按标签配置
							if len(drugConfigure.Configures[i].Label) != 0 {
								//组合标签
								isRange := false
								if drugConfigure.Configures[i].Values != nil && len(drugConfigure.Configures[i].Values) != 0 {
									for _, drugValue := range drugConfigure.Configures[i].Values {
										if strings.Contains(drugValue.CustomDispensingNumber, "~") {
											isRange = true
										}
									}
								}
								var doseSettingLabel models.DoseSettingLabel
								var doseLabel models.DoseLabel
								doseLabel.ID = drugConfigure.Configures[i].ID
								doseLabel.Name = drugConfigure.Configures[i].Label

								jsonString, e := json.Marshal(doseLabel)
								if e != nil {
									return doseSettingLabels, errors.WithStack(e)
								}
								doseSettingLabel.ID = string(jsonString)
								doseSettingLabel.Name = drugConfigure.Configures[i].Group + "/" + drugConfigure.Configures[i].Label
								doseSettingLabel.Group = drugConfigure.Configures[i].Group
								if isRange {
									doseSettingLabel.IsLabel = true
								}
								doseSettingLabels = append(doseSettingLabels, doseSettingLabel)
							} else {
								if drugConfigure.Configures[i].Values != nil && len(drugConfigure.Configures[i].Values) != 0 {
									for _, drugValue := range drugConfigure.Configures[i].Values {
										if len(drugValue.Label) != 0 {
											var doseSettingLabel models.DoseSettingLabel
											var doseLabel models.DoseLabel
											doseLabel.ID = drugConfigure.Configures[i].ID
											doseLabel.Name = drugValue.Label

											jsonString, e := json.Marshal(doseLabel)
											if e != nil {
												return doseSettingLabels, errors.WithStack(e)
											}
											doseSettingLabel.ID = string(jsonString)
											doseSettingLabel.Name = drugConfigure.Configures[i].Group + "/" + drugValue.Label + "/" + drugValue.CustomDispensingNumber
											doseSettingLabel.Group = drugConfigure.Configures[i].Group
											if !strings.Contains(drugValue.CustomDispensingNumber, "~") {
												doseSettingLabel.IsLabel = false
											} else {
												doseSettingLabel.IsLabel = true
											}
											doseSettingLabels = append(doseSettingLabels, doseSettingLabel)
										}
									}
								}
							}
						} else if drugConfigure.Configures[i].OpenSetting == 2 {
							//开放配置
							if len(drugConfigure.Configures[i].Label) != 0 {
								isRange := false
								if drugConfigure.Configures[i].Values != nil && len(drugConfigure.Configures[i].Values) != 0 {
									for _, drugValue := range drugConfigure.Configures[i].Values {
										if strings.Contains(drugValue.CustomDispensingNumber, "~") {
											isRange = true
										}
									}
								}
								var doseSettingLabel models.DoseSettingLabel
								var doseLabel models.DoseLabel
								doseLabel.ID = drugConfigure.Configures[i].ID
								doseLabel.Name = drugConfigure.Configures[i].Label

								jsonString, e := json.Marshal(doseLabel)
								if e != nil {
									return doseSettingLabels, errors.WithStack(e)
								}
								doseSettingLabel.ID = string(jsonString)
								doseSettingLabel.Name = drugConfigure.Configures[i].Group + "/" + drugConfigure.Configures[i].Label
								doseSettingLabel.Group = drugConfigure.Configures[i].Group
								if isRange {
									doseSettingLabel.IsLabel = true
								}
								doseSettingLabels = append(doseSettingLabels, doseSettingLabel)
							} else {
								if drugConfigure.Configures[i].Values != nil && len(drugConfigure.Configures[i].Values) != 0 {
									for _, drugValue := range drugConfigure.Configures[i].Values {
										drug := ""
										if len(drugValue.DrugSpec) != 0 {
											drug = drugValue.DrugName + "/" + drugValue.CustomDispensingNumber + "/" + drugValue.DrugSpec
										} else {
											drug = drugValue.DrugName + "/" + drugValue.CustomDispensingNumber + "/"
										}
										var doseSettingLabel models.DoseSettingLabel
										var doseLabel models.DoseLabel
										doseLabel.ID = drugConfigure.Configures[i].ID
										doseLabel.Name = drug

										jsonString, e := json.Marshal(doseLabel)
										if e != nil {
											return doseSettingLabels, errors.WithStack(e)
										}
										doseSettingLabel.ID = string(jsonString)
										doseSettingLabel.Name = drugConfigure.Configures[i].Group + "/" + drug
										doseSettingLabel.Group = drugConfigure.Configures[i].Group
										if !strings.Contains(drugValue.CustomDispensingNumber, "~") {
											doseSettingLabel.IsLabel = false
										} else {
											doseSettingLabel.IsLabel = true
										}
										doseSettingLabels = append(doseSettingLabels, doseSettingLabel)
									}
								}
							}
						} else if drugConfigure.Configures[i].OpenSetting == 3 {
							//按公式计算
							if drugConfigure.Configures[i].Values != nil && len(drugConfigure.Configures[i].Values) != 0 {
								for _, drugValue := range drugConfigure.Configures[i].Values {
									drug := ""
									if len(drugValue.DrugSpec) != 0 {
										drug = drugValue.DrugName + "//" + drugValue.DrugSpec
									} else {
										drug = drugValue.DrugName + "//"
									}
									var doseSettingLabel models.DoseSettingLabel
									var doseLabel models.DoseLabel
									doseLabel.ID = drugConfigure.Configures[i].ID
									doseLabel.Name = drug

									jsonString, e := json.Marshal(doseLabel)
									if e != nil {
										return doseSettingLabels, errors.WithStack(e)
									}
									doseSettingLabel.ID = string(jsonString)
									doseSettingLabel.Name = drugConfigure.Configures[i].Group + "/" + drug
									doseSettingLabel.Group = drugConfigure.Configures[i].Group
									if !strings.Contains(drugValue.CustomDispensingNumber, "~") {
										doseSettingLabel.IsLabel = false
									} else {
										doseSettingLabel.IsLabel = true
									}
									doseSettingLabels = append(doseSettingLabels, doseSettingLabel)
								}
							}
						}
					}
				}
			}

		}
	}
	return doseSettingLabels, nil
}

func (s *DrugConfigureService) GetSetUpDrugConfigureVisitList(ctx *gin.Context, customerID string, projectID string, envID string, cohortID string, doseFormID string) ([]models.DoseVisitJudgment, error) {
	doseVisitJudgmentList := make([]models.DoseVisitJudgment, 0)

	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	doseFormOID, _ := primitive.ObjectIDFromHex(doseFormID)
	filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if cohortID != "" {
		filter["cohort_id"] = cohortOID
	}
	var form models.Form
	err := tools.Database.Collection("form").FindOne(nil, filter).Decode(&form)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return doseVisitJudgmentList, errors.WithStack(err)
	}
	if form.Fields != nil && len(form.Fields) > 0 {
		for _, field := range form.Fields {
			if field.ApplicationType != nil {
				apt := *field.ApplicationType
				status := *field.Status
				if apt == 3 && status != 2 && doseFormOID == field.ID {
					if field.Options != nil && len(field.Options) > 0 {
						for _, option := range field.Options {
							var doseVisitJudgment models.DoseVisitJudgment
							doseVisitJudgment.ID = option.Value
							optionLabel := option.Label
							if option.Label == "form.control.type.options.one" {
								optionLabel = locales.Tr(ctx, "form.control.type.options.one")
							} else if option.Label == "form.control.type.options.two" {
								optionLabel = locales.Tr(ctx, "form.control.type.options.two")
							} else if option.Label == "form.control.type.options.three" {
								optionLabel = locales.Tr(ctx, "form.control.type.options.three")
							}
							doseVisitJudgment.Name = field.Label + " : " + optionLabel
							doseVisitJudgmentList = append(doseVisitJudgmentList, doseVisitJudgment)
						}
					}
				}
			}
		}
	}
	return doseVisitJudgmentList, nil
}

func (s *DrugConfigureService) GetSetUpDrugConfigureDtpRule(ctx *gin.Context, customerID string, projectID string, envID string, cohortID string, roleID string) (models.DtpRules, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)

	filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if cohortID != "" {
		filter["cohort_id"] = cohortOID
	}

	var attribute models.Attribute
	err := tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute)
	if err != nil {
		return models.DtpRules{}, errors.WithStack(err)
	}

	var drugConfigure models.DrugConfigure
	err = tools.Database.Collection("drug_configure").FindOne(nil, filter).Decode(&drugConfigure)
	if err != nil && err != mongo.ErrNoDocuments {
		return models.DtpRules{}, errors.WithStack(err)
	}

	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil && err != mongo.ErrNoDocuments {
		return models.DtpRules{}, errors.WithStack(err)
	}

	var dtpRules models.DtpRules
	dtpRules.Type = attribute.AttributeInfo.DtpRule
	drugNameList := make([]string, 0)
	if drugConfigure.Configures != nil && len(drugConfigure.Configures) > 0 {
		for _, configure := range drugConfigure.Configures {
			if configure.Values != nil && len(configure.Values) > 0 {
				for _, value := range configure.Values {
					index := arrays.ContainsString(drugNameList, value.DrugName)
					if index == -1 {
						drugName := value.DrugName
						isBlindedDrug, _ := tools.IsBlindedDrug(envOID, drugName)
						if isBlindedDrug && isBlindedRole {
							drugName = tools.BlindData
						}
						drugNameList = append(drugNameList, drugName)
					}
				}
			}
		}
	}
	dtpRules.DrugNameList = drugNameList
	return dtpRules, nil
}

func (s *DrugConfigureService) GetIsGroup(ctx *gin.Context, customerID string, projectID string, envID string, cohortID string, category string, roleID string) (string, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if cohortID != "" && cohortOID != primitive.NilObjectID {
		filter["cohort_id"] = cohortOID
	}

	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return "", errors.WithStack(err)
	}

	groups := make([]string, 0)

	var randomDesign models.RandomDesign
	err = tools.Database.Collection("random_design").FindOne(nil, filter).Decode(&randomDesign)
	if err != nil && err != mongo.ErrNoDocuments {
		return "", errors.WithStack(err)
	}

	if randomDesign.Info.Groups != nil && len(randomDesign.Info.Groups) > 0 {
		for _, group := range randomDesign.Info.Groups {
			if group.SubGroup != nil && len(group.SubGroup) > 0 {
				for _, subGroup := range group.SubGroup {
					groups = append(groups, group.Name+" "+subGroup.Name)
				}
			} else {
				groups = append(groups, group.Name)
			}
		}
	}

	if category == "1" {
		//进入“访视管理”主页面时，报错如下：组别已修改，请重新映射访视与组别的关系。
		var visitCycle models.VisitCycle
		err = tools.Database.Collection("visit_cycle").FindOne(nil, filter).Decode(&visitCycle)
		if err != nil && err != mongo.ErrNoDocuments {
			return "", errors.WithStack(err)
		}

		if visitCycle.Infos != nil && len(visitCycle.Infos) > 0 {
			for _, info := range visitCycle.Infos {
				if info.Group != nil && len(info.Group) > 0 {
					for _, group := range info.Group {
						// 使用类型断言将interface{}转换为string
						if gp, ok := group.(string); ok {
							index := arrays.ContainsString(groups, gp)
							if index == -1 && gp != "N/A" {
								//-1说明不存在
								if !isBlindedRole {
									return "visit.cycle.management.is.group", errors.WithStack(err)
								}
							}
						}
					}
				}
			}
		}

	} else if category == "2" {
		//进入“研究产品配置”主页面时，报错如下：组别已修改，请重新映射研究产品名称与组别的关系。
		var drugConfigure models.DrugConfigure
		err = tools.Database.Collection("drug_configure").FindOne(nil, filter).Decode(&drugConfigure)
		if err != nil && err != mongo.ErrNoDocuments {
			return "", errors.WithStack(err)
		}

		if drugConfigure.Configures != nil && len(drugConfigure.Configures) > 0 {
			for _, configure := range drugConfigure.Configures {
				if len(configure.Group) > 0 {
					index := arrays.ContainsString(groups, configure.Group)
					if index == -1 && configure.Group != "N/A" {
						//-1说明不存在
						if !isBlindedRole {
							return "visit.cycle.management.drug.configure.is.group", errors.WithStack(err)
						}
					}
				}
			}
		}
	}

	return "", nil
}

func (s *DrugConfigureService) GetIsLabel(ctx *gin.Context, customerID string, projectID string, envID string, cohortID string, roleID string) (string, error) {

	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return "", errors.WithStack(err)
	}

	groupList, err := s.GetAllocationGroup(ctx, customerID, projectID, envID, cohortID)
	if err != nil && err != mongo.ErrNoDocuments {
		return "", errors.WithStack(err)
	}

	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if cohortID != "" && cohortOID != primitive.NilObjectID {
		filter["cohort_id"] = cohortOID
	}

	var drugConfigure models.DrugConfigure
	err = tools.Database.Collection("drug_configure").FindOne(nil, filter).Decode(&drugConfigure)
	if err != nil && err != mongo.ErrNoDocuments {
		return "", errors.WithStack(err)
	}

	var drugConfigureSetting models.DrugConfigureSetting
	err = tools.Database.Collection("drug_configure_setting").FindOne(nil, filter).Decode(&drugConfigureSetting)
	if err != nil && err != mongo.ErrNoDocuments {
		return "", errors.WithStack(err)
	}

	if !isBlindedRole {

		deletedDoseLevelLabelList := make([]string, 0)
		//剂量水平集
		if drugConfigureSetting.DoseLevelList != nil && len(drugConfigureSetting.DoseLevelList) > 0 {
			for _, doseLevel := range drugConfigureSetting.DoseLevelList {
				newGroupList := make([]string, 0)
				if doseLevel.Group != nil && len(doseLevel.Group) > 0 {
					for _, gp := range doseLevel.Group {
						groupIndex := arrays.ContainsString(groupList, gp)
						if groupIndex == -1 {
							//-1说明不存在
						} else {
							newGroupList = append(newGroupList, gp)
						}
					}

					var drugConfigureSettingLabel models.DrugConfigureSettingLabel
					drugConfigureSettingLabel.CustomerID = customerOID
					drugConfigureSettingLabel.ProjectID = projectOID
					drugConfigureSettingLabel.EnvironmentID = envOID
					drugConfigureSettingLabel.CohortID = cohortOID
					drugConfigureSettingLabel.RoleID = roleID
					drugConfigureSettingLabel.Groups = newGroupList
					drugConfigureSettingLabelList, err := s.GetSetUpDrugConfigureLabel(ctx, drugConfigureSettingLabel)
					if err != nil && err != mongo.ErrNoDocuments {
						return "", errors.WithStack(err)
					}

					labelList := make([]string, 0)
					if drugConfigureSettingLabelList != nil && len(drugConfigureSettingLabelList) > 0 {
						for _, label := range drugConfigureSettingLabelList {
							labelList = append(labelList, label.ID)
						}
					}

					newDoseLevelLabelList := make([]string, 0)
					if doseLevel.DoseDistribution != nil && len(doseLevel.DoseDistribution) > 0 {
						for _, doseDistribution := range doseLevel.DoseDistribution {
							index := arrays.ContainsString(labelList, doseDistribution)
							if index == -1 {
								//-1说明不存在
								deletedDoseLevelLabelList = append(deletedDoseLevelLabelList, doseDistribution)
							} else {
								newDoseLevelLabelList = append(newDoseLevelLabelList, doseDistribution)
							}
						}
					}

					//更新
					opts := &options.UpdateOptions{
						ArrayFilters: &options.ArrayFilters{
							Filters: bson.A{bson.M{"element._id": doseLevel.ID}},
						},
					}
					_, err = tools.Database.Collection("drug_configure_setting").UpdateOne(ctx, bson.M{"env_id": envOID, "cohort_id": cohortOID},
						bson.M{
							"$set": bson.M{
								"dose_level_list.$[element].group":             newGroupList,
								"dose_level_list.$[element].dose_distribution": newDoseLevelLabelList,
							},
						},
						opts)
					if err != nil {
						return "", errors.WithStack(err)
					}
				}

			}
		}

		deletedVisitJudgmentLabelList := make([]string, 0)
		//访视判断
		if drugConfigureSetting.VisitJudgmentList != nil && len(drugConfigureSetting.VisitJudgmentList) > 0 {
			for _, visitJudgment := range drugConfigureSetting.VisitJudgmentList {
				newGroupList := make([]string, 0)

				if visitJudgment.Group != nil && len(visitJudgment.Group) > 0 {
					for _, gp := range visitJudgment.Group {
						groupIndex := arrays.ContainsString(groupList, gp)
						if groupIndex == -1 {
							//-1说明不存在
						} else {
							newGroupList = append(newGroupList, gp)
						}
					}

					var drugConfigureSettingLabel models.DrugConfigureSettingLabel
					drugConfigureSettingLabel.CustomerID = customerOID
					drugConfigureSettingLabel.ProjectID = projectOID
					drugConfigureSettingLabel.EnvironmentID = envOID
					drugConfigureSettingLabel.CohortID = cohortOID
					drugConfigureSettingLabel.RoleID = roleID
					drugConfigureSettingLabel.Groups = newGroupList
					drugConfigureSettingLabelList, err := s.GetSetUpDrugConfigureLabel(ctx, drugConfigureSettingLabel)
					if err != nil && err != mongo.ErrNoDocuments {
						return "", errors.WithStack(err)
					}

					labelList := make([]string, 0)
					if drugConfigureSettingLabelList != nil && len(drugConfigureSettingLabelList) > 0 {
						for _, label := range drugConfigureSettingLabelList {
							labelList = append(labelList, label.ID)
						}
					}

					newVisitJudgmentLabelList := make([]string, 0)
					if visitJudgment.DoseDistribution != nil && len(visitJudgment.DoseDistribution) > 0 {
						for _, doseDistribution := range visitJudgment.DoseDistribution {
							index := arrays.ContainsString(labelList, doseDistribution)
							if index == -1 {
								//-1说明不存在
								deletedVisitJudgmentLabelList = append(deletedVisitJudgmentLabelList, doseDistribution)
							} else {
								newVisitJudgmentLabelList = append(newVisitJudgmentLabelList, doseDistribution)
							}
						}
					}

					//更新
					opts := &options.UpdateOptions{
						ArrayFilters: &options.ArrayFilters{
							Filters: bson.A{bson.M{"element._id": visitJudgment.ID}},
						},
					}
					_, err = tools.Database.Collection("drug_configure_setting").UpdateOne(ctx, bson.M{"env_id": envOID, "cohort_id": cohortOID},
						bson.M{
							"$set": bson.M{
								"dose_level_list.$[element].group":                 newGroupList,
								"visit_judgment_list.$[element].dose_distribution": newVisitJudgmentLabelList,
							},
						},
						opts)
					if err != nil {
						return "", errors.WithStack(err)
					}

				}
			}
		}

		if len(deletedDoseLevelLabelList) > 0 || len(deletedVisitJudgmentLabelList) > 0 {
			return "visit.cycle.management.drug.configure.is.group.label", errors.WithStack(err)
		}
	}

	//for _, settingLabel := range settingLabelList {
	//	index := arrays.ContainsString(labelList, settingLabel)
	//	if index == -1 {
	//		//-1说明不存在
	//		return "visit.cycle.management.drug.configure.is.group.label", errors.WithStack(err)
	//	}
	//}

	return "", nil
}

func (s *DrugConfigureService) GetAllocationGroup(ctx *gin.Context, customerID string, projectID string, envID string, cohortID string) ([]string, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if cohortID != "" && cohortOID != primitive.NilObjectID {
		filter["cohort_id"] = cohortOID
	}

	groups := make([]string, 0)

	//进入“研究产品配置”主页面时，报错如下：组别已修改，请重新映射研究产品名称与组别的关系。
	var drugConfigure models.DrugConfigure
	err := tools.Database.Collection("drug_configure").FindOne(nil, filter).Decode(&drugConfigure)
	if err != nil && err != mongo.ErrNoDocuments {
		return groups, errors.WithStack(err)
	}

	if drugConfigure.Configures != nil && len(drugConfigure.Configures) > 0 {
		for _, configure := range drugConfigure.Configures {
			if len(configure.Group) > 0 {
				index := arrays.ContainsString(groups, configure.Group)
				if index == -1 {
					//-1说明不存在
					groups = append(groups, configure.Group)
				}
			}
		}
	}

	return groups, nil
}

func (s *DrugConfigureService) GetIsBlindedRole(ctx *gin.Context, roleID string) (bool, error) {
	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return false, errors.WithStack(err)
	}
	return isBlindedRole, nil
}

func (s *DrugConfigureService) GetVisitSettings(ctx *gin.Context, customerID string, projectID string, envID string, cohortID string, roleID string) (models.VisitSetting, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	filter := bson.M{"project_id": projectOID, "env_id": envOID}
	if cohortID != "" && cohortOID != primitive.NilObjectID {
		filter["cohort_id"] = cohortOID
	}

	visitSetting := models.VisitSetting{}

	var visitCycle models.VisitCycle
	err := tools.Database.Collection("visit_cycle").FindOne(nil, filter).Decode(&visitCycle)
	if err != nil && err != mongo.ErrNoDocuments {
		return visitSetting, errors.WithStack(err)
	}
	visitSetting = visitCycle.SetInfo
	return visitSetting, nil
}

func (s *DrugConfigureService) SetUpVisitSettings(ctx *gin.Context, customerID string, projectID string, envID string, cohortID string, roleID string, visitSetting models.VisitSetting) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		customerOID, _ := primitive.ObjectIDFromHex(customerID)
		projectOID, _ := primitive.ObjectIDFromHex(projectID)
		envOID, _ := primitive.ObjectIDFromHex(envID)
		cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
		logOID := envOID
		filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
		if cohortID != "" && cohortOID != primitive.NilObjectID {
			filter["cohort_id"] = cohortOID
			logOID = cohortOID
		}

		user, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		//判断是否存在数据
		if count, err := tools.Database.Collection("visit_cycle").CountDocuments(sctx, filter); count > 0 && err != mongo.ErrNoDocuments {
			var visitCycle models.VisitCycle
			err = tools.Database.Collection("visit_cycle").FindOne(sctx, filter).Decode(&visitCycle)

			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}
			if visitCycle.SetInfo.Id == primitive.NilObjectID {
				visitCycle.SetInfo.Id = primitive.NewObjectID()
			}
			visitSetting.Id = visitCycle.SetInfo.Id
			update := bson.M{
				"$set": bson.M{"set_info": visitSetting},
			}
			_, err = tools.Database.Collection("visit_cycle").UpdateOne(sctx, filter, update)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			err = insertVisitSettingLog(ctx, sctx, logOID, 2, visitCycle.SetInfo, visitSetting, visitCycle.ID, "")
			if err != nil {
				return nil, errors.WithStack(err)
			}

		} else {
			visitSetting.Id = primitive.NewObjectID()
			visitCycle := models.VisitCycle{
				ID:            primitive.NewObjectID(),
				CustomerID:    primitive.NewObjectID(),
				ProjectID:     primitive.NewObjectID(),
				EnvironmentID: primitive.NewObjectID(),
				CohortID:      primitive.NewObjectID(),
				SetInfo:       visitSetting,
				Meta: models.Meta{
					CreatedBy: user.ID,
					CreatedAt: time.Duration(time.Now().Unix()),
				},
			}
			_, err := tools.Database.Collection("visit_cycle").InsertOne(sctx, visitCycle)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			err = insertVisitSettingLog(ctx, sctx, logOID, 2, models.VisitSetting{}, visitSetting, visitCycle.ID, "")
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}
