package service

import (
	"clinflash-irt/config"
	"clinflash-irt/data"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"fmt"
	"github.com/wxnacy/wgo/arrays"
	"strconv"
	"time"

	"github.com/duke-git/lancet/v2/slice"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type ApprovalProcessService struct {
	subjectService SubjectService
}

func (s *ApprovalProcessService) GetApprovalNumber(ctx *gin.Context, projectOID primitive.ObjectID) (string, error) {
	timeZone, err := tools.GetTimeZone(projectOID)
	if err != nil {
		return "", errors.WithStack(err)
	}
	hours := time.Duration(timeZone)
	minutes := time.Duration((timeZone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute
	now := time.Now().UTC().Add(duration).Format("20060102")
	var approvalNumber string
	var data []models.ApprovalNumber
	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"number": bson.M{"$regex": "^" + now}}}},
		{{Key: "$sort", Value: bson.D{{"number", -1}}}},
		{{Key: "$limit", Value: 1}},
	}
	cursor, err := tools.Database.Collection("approval_number").Aggregate(nil, pipepine)
	if err != nil {
		return "", errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return "", errors.WithStack(err)
	}

	if data != nil {
		maxApprovalNumber := data[0].Number
		if maxApprovalNumber[:8] == now {
			maxNumber, _ := strconv.Atoi(maxApprovalNumber[9:])
			number := strconv.Itoa(maxNumber + 1)
			formatNumber := fmt.Sprintf("%04s", number)
			approvalNumber = now + formatNumber
		} else {
			approvalNumber = now + "0001"
		}
	} else {
		approvalNumber = now + "0001"
	}

	return approvalNumber, nil
}

func (s *ApprovalProcessService) GetApprovalProcessList(ctx *gin.Context, start int, limit int) (map[string]interface{}, error) {
	var approvalProcesssShow []models.OrderAddTaskShow

	customerId := ctx.Query("customerId")
	customerOID, _ := primitive.ObjectIDFromHex(customerId)
	projectId := ctx.Query("projectId")
	projectOID, _ := primitive.ObjectIDFromHex(projectId)
	envId := ctx.Query("envId")
	envOID, _ := primitive.ObjectIDFromHex(envId)
	//cohortID, _ := primitive.ObjectIDFromHex(ctx.Query("cohortId"))
	status := ctx.Query("status")
	roleId := ctx.Query("roleId")
	roleOID, _ := primitive.ObjectIDFromHex(roleId)

	packageIsOpen, _, _, _, _, _ := tools.IsOpenPackage(envOID)

	match := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == envOID
	})
	env := *envP

	//当前系统操作人
	user, err := tools.Me(ctx)
	isApprovalPermission, err := tools.IsRoleHasPermission(roleId, "operation.supply.shipment.approval")
	if err != nil {
		return nil, err
	}
	isUnblindingApprovalPermission, err := tools.IsRoleHasPermission(roleId, "operation.subject.unblinding-approval")
	if err != nil {
		return nil, err
	}
	isPvApprovalPermission, err := tools.IsRoleHasPermission(roleId, "operation.subject.unblinding-pv-approval")
	if err != nil {
		return nil, err
	}
	isIPApprovalPermission, err := tools.IsRoleHasPermission(roleId, "operation.subject.unblinding-ip-approval")
	if err != nil {
		return nil, err
	}
	//判断当前的人是否有审批流程权限,如果没有只可以查看自己的数据
	var typeQuery []int
	if isApprovalPermission {
		typeQuery = append(typeQuery, 1)
	}
	if isUnblindingApprovalPermission {
		typeQuery = append(typeQuery, 2)
	}
	if isPvApprovalPermission {
		typeQuery = append(typeQuery, 3)
	}
	if isIPApprovalPermission {
		typeQuery = append(typeQuery, 4)
	}
	if len(typeQuery) >= 4 {
		match["type"] = bson.M{"$in": typeQuery}
	} else {
		if len(typeQuery) > 0 && len(typeQuery) < 4 {
			match["$or"] = bson.A{
				bson.M{"type": bson.M{"$in": typeQuery}},
				bson.M{"$and": bson.A{
					bson.M{"type": bson.M{"$ne": typeQuery}},
					bson.M{"application_by": user.ID},
					bson.M{"application_role_id": roleOID},
				}},
			}
		} else { //三个审批权限都没有，就只查看自己申请的数据
			match["application_by"] = user.ID
			match["application_role_id"] = roleOID
		}
	}

	// 查询当前角色是不是
	var projectRolePermission models.ProjectRolePermission
	err = tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{"_id": roleOID}).Decode(&projectRolePermission)
	if err != nil {
		return nil, err
	}
	if projectRolePermission.Scope == "site" {
		// 查询当前用户角色下绑定了那些中心
		userSiteList := make([]models.UserSite, 0)
		userSiteCursor, err := tools.Database.Collection("user_site").Find(nil, bson.M{"env_id": envOID, "user_id": user.ID})
		if err != nil {
			return nil, err
		}
		err = userSiteCursor.All(nil, &userSiteList)
		if err != nil {
			return nil, err
		}
		var siteListId []primitive.ObjectID
		for _, userSite := range userSiteList {
			siteListId = append(siteListId, userSite.SiteID)
		}
		if siteListId != nil && len(siteListId) > 0 {
			match["$or"] = bson.A{
				bson.M{"project_site_id": primitive.NilObjectID},
				bson.M{"project_site_id": nil},
				bson.M{"project_site_id": bson.M{"$in": siteListId}},
			}
		} else {
			match["$or"] = bson.A{
				bson.M{"project_site_id": primitive.NilObjectID},
				bson.M{"project_site_id": nil},
			}
		}
	}

	total, _ := tools.Database.Collection("approval_process").CountDocuments(nil, match)

	statusInt, _ := strconv.Atoi(status)
	if statusInt != 100 {
		match["status"] = statusInt
	}

	currentTotal, _ := tools.Database.Collection("approval_process").CountDocuments(nil, match)

	var approvalProcesss []models.OrderAddTask
	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$sort", Value: bson.D{{"number", -1}}}},
		{{Key: "$skip", Value: start}},
		{{Key: "$limit", Value: limit}},
	}
	cursor, err := tools.Database.Collection("approval_process").Aggregate(nil, pipepine)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &approvalProcesss)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	//如果是盲法项目，判断他是否有权限查看，如果没有权限查看，打码显示
	isBlindedRole, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return map[string]interface{}{"currentTotal": 0, "total": 0, "items": approvalProcesss, "packageIsOpen": packageIsOpen}, err
	}

	for aindex, approvalTask := range approvalProcesss {
		var cohort models.Cohort
		cohortP, b := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
			return item.ID == approvalTask.CohortID
		})
		if b {
			cohort = *cohortP
		}

		if approvalTask.Type == 1 { //订单申请
			if isBlindedRole {
				bindDrugNames := make([]string, len(approvalTask.Data.DrugNames))
				for x, drugName := range approvalTask.Data.DrugNames {
					// isOpenDrug, _ := tools.IsOpenDrug(approvalTask.EnvironmentID, approvalTask.CohortID, drugName)
					// isOtherDrug, _ := tools.IsOtherDrug(approvalTask.EnvironmentID, drugName)
					// if !isOpenDrug && !isOtherDrug {
					isBlindedDrug, _ := tools.IsBlindedDrug(approvalTask.EnvironmentID, drugName)
					if isBlindedDrug {
						bindDrugNames[x] = tools.BlindData
					} else {
						bindDrugNames[x] = drugName
					}
					approvalTask.Data.DrugNames = bindDrugNames
					approvalProcesss[aindex] = approvalTask
				}
				approvalTask.Data.DrugNames = bindDrugNames
			}
			detailDatas := make([]models.DetailOrderAddInfoShow, len(approvalTask.Data.DetailData))
			for y, detailData := range approvalTask.Data.DetailData {
				detailDataShow := models.DetailOrderAddInfoShow{
					Name:           detailData.Name,
					BatchNumber:    detailData.BatchNumber,
					ExpirationDate: detailData.ExpirationDate,
					PackageMethod:  detailData.PackageMethod,
					Count:          strconv.Itoa(detailData.Count),
					UseCount:       strconv.Itoa(detailData.UseCount),
				}
				isBlindedDrug, _ := tools.IsBlindedDrug(approvalTask.EnvironmentID, detailData.Name)
				// isOpenDrug, _ := tools.IsOpenDrug(approvalTask.EnvironmentID, approvalTask.CohortID, detailData.Name)
				// isOtherDrug, _ := tools.IsOtherDrug(approvalTask.EnvironmentID, detailData.Name)
				//if blind && isBlindedRole && !isOpenDrug && !isOtherDrug {
				if isBlindedDrug && isBlindedRole {
					detailDataShow.Name = tools.BlindData
					detailDataShow.Count = tools.BlindData
					detailDataShow.UseCount = tools.BlindData
				}
				detailDatas[y] = detailDataShow
			}
			otherDetailDatas := make([]models.DetailOrderAddInfoShow, len(approvalTask.Data.OtherDetailData))
			for y, detailData := range approvalTask.Data.OtherDetailData {
				detailDataShow := models.DetailOrderAddInfoShow{
					Name:           detailData.Name,
					BatchNumber:    detailData.BatchNumber,
					ExpirationDate: detailData.ExpirationDate,
					Count:          strconv.Itoa(detailData.Count),
					UseCount:       strconv.Itoa(detailData.UseCount),
					PackageMethod:  detailData.PackageMethod,
				}
				isBlindedDrug, _ := tools.IsBlindedDrug(approvalTask.EnvironmentID, detailData.Name)
				if isBlindedDrug && isBlindedRole {
					detailDataShow.Name = tools.BlindData
				}
				otherDetailDatas[y] = detailDataShow
			}
			approvalTaskShow := models.OrderAddTaskShow{
				ID:              approvalTask.ID,
				CustomerID:      approvalTask.CustomerID,
				ProjectID:       approvalTask.ProjectID,
				EnvironmentID:   approvalTask.EnvironmentID,
				ApprovalProcess: approvalTask.ApprovalProcess,
				Cohort:          cohort,
				Data: models.OrderAddInfoShow{
					SendID:              approvalTask.Data.SendID,
					ReceiveID:           approvalTask.Data.ReceiveID,
					Mode:                approvalTask.Data.Mode,
					DrugNames:           approvalTask.Data.DrugNames,
					SupplyID:            approvalTask.Data.SupplyID,
					SupplyCount:         approvalTask.Data.SupplyCount,
					Contacts:            approvalTask.Data.Contacts,
					Phone:               approvalTask.Data.Phone,
					Email:               approvalTask.Data.Email,
					Address:             approvalTask.Data.Address,
					DetailData:          detailDatas,
					OtherDetailData:     otherDetailDatas,
					BlindCount:          approvalTask.Data.BlindCount,
					OpenDrugCount:       approvalTask.Data.OpenDrugCount,
					ExpectedArrivalTime: approvalTask.Data.ExpectedArrivalTime,
				},
			}
			approvalProcesssShow = append(approvalProcesssShow, approvalTaskShow)
		} else {
			approvalTaskShow := models.OrderAddTaskShow{
				ID:              approvalTask.ID,
				CustomerID:      approvalTask.CustomerID,
				ProjectID:       approvalTask.ProjectID,
				EnvironmentID:   approvalTask.EnvironmentID,
				ApprovalProcess: approvalTask.ApprovalProcess,
				Cohort:          cohort,
				UnblindingData: models.UnblindingDataInfoShow{
					SubjectID:           approvalTask.UnblindingData.SubjectID,
					SubjectNumber:       approvalTask.UnblindingData.SubjectNumber,
					SubjectRandomNumber: approvalTask.UnblindingData.SubjectRandomNumber,
					MedicineNumber:      approvalTask.UnblindingData.MedicineNumber,
					Number:              approvalTask.UnblindingData.Number,
					Status:              approvalTask.UnblindingData.Status,
					ApprovalType:        approvalTask.UnblindingData.ApprovalType,
					ApplicationTime:     approvalTask.UnblindingData.ApplicationTime,
					ApplicationBy:       approvalTask.UnblindingData.ApplicationBy,
					ApprovalTime:        approvalTask.UnblindingData.ApprovalTime,
					ApprovalBy:          approvalTask.UnblindingData.ApprovalBy,
					ApplicationByEmail:  approvalTask.UnblindingData.ApplicationByEmail,
					ApprovalByEmail:     approvalTask.UnblindingData.ApprovalByEmail,
					ReasonStr:           approvalTask.UnblindingData.ReasonStr,
					Remark:              approvalTask.UnblindingData.Remark,
					RejectReason:        approvalTask.UnblindingData.RejectReason},
			}

			//查询受试者的项目属性
			var subject models.Subject
			err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": approvalTask.UnblindingData.SubjectID}).Decode(&subject)
			if err != nil {
				return map[string]interface{}{"currentTotal": 0, "total": 0, "items": approvalProcesss, "packageIsOpen": packageIsOpen}, err
			}

			var medicine models.Medicine
			if !approvalTask.UnblindingData.MedicineID.IsZero() {
				err = tools.Database.Collection("medicine").FindOne(nil, bson.M{"_id": approvalTask.UnblindingData.MedicineID}).Decode(&medicine)
				if err != nil {
					return map[string]interface{}{"currentTotal": 0, "total": 0, "items": approvalProcesss, "packageIsOpen": packageIsOpen}, errors.WithStack(err)
				}
			}

			match := bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID}
			if subject.CohortID != primitive.NilObjectID {
				match = bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}
			}

			var attribute models.Attribute
			err = tools.Database.Collection("attribute").FindOne(nil, match).Decode(&attribute)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			// 不展示随机号
			if !attribute.AttributeInfo.IsRandomNumber {
				approvalTaskShow.UnblindingData.SubjectRandomNumber = tools.BlindData
			}

			approvalTaskShow.AttributeInfo = attribute.AttributeInfo

			// 查询该环境关联的所有用户
			userProjectEnvironmentList := make([]models.UserProjectEnvironment, 0)
			userProjectEnvironmentCursor, err := tools.Database.Collection("user_project_environment").Find(nil, bson.M{"env_id": subject.EnvironmentID})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = userProjectEnvironmentCursor.All(nil, &userProjectEnvironmentList)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			var userIDs []primitive.ObjectID
			for _, upel := range userProjectEnvironmentList {
				userIDs = append(userIDs, upel.UserID)
			}
			// 查询user
			var userList []models.User
			userCursor, err := tools.Database.Collection("user").Find(nil, bson.M{"_id": bson.M{"$in": userIDs}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = userCursor.All(nil, &userList)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			// 查询项目角色权限
			projectRolePermissionList := make([]models.ProjectRolePermission, 0)
			projectRolePermissionCursor, err := tools.Database.Collection("project_role_permission").Find(nil, bson.M{"project_id": subject.ProjectID, "name": bson.M{"$ne": "Project-Admin"}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = projectRolePermissionCursor.All(nil, &projectRolePermissionList)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			// 查询用户关联的角色
			userSiteList := make([]models.UserSite, 0)
			userSiteCursor, err := tools.Database.Collection("user_site").Find(nil, bson.M{"env_id": subject.EnvironmentID})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = userSiteCursor.All(nil, &userSiteList)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			// 盲法项目查询紧急揭盲和pv揭盲权限的用户
			if attribute.AttributeInfo.Blind {
				unblindingApprovalUser, pvUnblindingApprovalUser, ipUnblindingApprovalUser := queryApprovalUser(userProjectEnvironmentList, userList, projectRolePermissionList, userSiteList, subject.ProjectSiteID)
				if approvalTask.Type == 2 {
					approvalTaskShow.UnblindingData.ApprovalUser = unblindingApprovalUser
				} else if approvalTask.Type == 3 {
					approvalTaskShow.UnblindingData.ApprovalUser = pvUnblindingApprovalUser
				} else if approvalTask.Type == 4 {
					approvalTaskShow.UnblindingData.ApprovalUser = ipUnblindingApprovalUser
				}
			}

			// 紧急揭盲
			if subject.UrgentUnblindingApprovals != nil && len(subject.UrgentUnblindingApprovals) > 0 {
				for _, uua := range subject.UrgentUnblindingApprovals {
					if uua.Number == approvalTask.UnblindingData.Number {
						// 筛选申请人
						for _, u := range userList {
							if u.ID == uua.ApplicationBy {
								approvalTaskShow.UnblindingData.ApplicationByEmail = u.Name + "/" + u.Phone
								break
							}
						}
						// 筛选审批人
						for _, u := range userList {
							if u.ID == uua.ApprovalBy {
								approvalTaskShow.UnblindingData.ApprovalByEmail = u.Name + "/" + u.Phone
								break
							}
						}
					}
				}
			}

			// pv揭盲
			if subject.PvUrgentUnblindingApprovals != nil && len(subject.PvUrgentUnblindingApprovals) > 0 {
				for _, pua := range subject.PvUrgentUnblindingApprovals {
					if pua.Number == approvalTask.UnblindingData.Number {
						// 筛选申请人
						for _, u := range userList {
							if u.ID == pua.ApplicationBy {
								approvalTaskShow.UnblindingData.ApplicationByEmail = u.Name + "/" + u.Phone
								break
							}
						}

						// 筛选审批人
						for _, u := range userList {
							if u.ID == pua.ApprovalBy {
								approvalTaskShow.UnblindingData.ApprovalByEmail = u.Name + "/" + u.Phone
								break
							}
						}
					}
				}
			}

			// pv揭盲
			for _, pua := range medicine.UnblindingApprovals {
				if pua.Number == approvalTask.UnblindingData.Number {
					// 筛选申请人
					for _, u := range userList {
						if u.ID == pua.ApplicationBy {
							approvalTaskShow.UnblindingData.ApplicationByEmail = u.Name + "/" + u.Phone
							break
						}
					}

					// 筛选审批人
					for _, u := range userList {
						if u.ID == pua.ApprovalBy {
							approvalTaskShow.UnblindingData.ApprovalByEmail = u.Name + "/" + u.Phone
							break
						}
					}
				}
			}

			approvalProcesssShow = append(approvalProcesssShow, approvalTaskShow)
		}
	}

	return map[string]interface{}{"currentTotal": currentTotal, "total": total, "items": approvalProcesssShow, "packageIsOpen": packageIsOpen}, nil
}

func (s *ApprovalProcessService) GetApprovalProcessDetail(ctx *gin.Context, ID string, roleID string) (models.OrderAddTaskShow, error) {
	OID, _ := primitive.ObjectIDFromHex(ID)
	filter := bson.M{"_id": OID}

	orderAddTask := models.OrderAddTask{}
	if err := tools.Database.Collection("approval_process").FindOne(nil, filter).Decode(&orderAddTask); err != nil {
		return models.OrderAddTaskShow{}, nil
	}

	//如果是盲法项目，判断他是否有权限查看，如果没有权限查看，打码显示
	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return models.OrderAddTaskShow{}, err
	}

	approvalTaskShow := models.OrderAddTaskShow{}

	if orderAddTask.Type == 1 {
		bindDrugNames := make([]string, len(orderAddTask.Data.DrugNames))
		for index, drugName := range orderAddTask.Data.DrugNames {
			isBlindedDrug, _ := tools.IsBlindedDrug(orderAddTask.EnvironmentID, drugName)
			if isBlindedDrug && isBlindedRole {
				bindDrugNames[index] = tools.BlindData
			} else {
				bindDrugNames[index] = drugName
			}
		}
		orderAddTask.Data.DrugNames = bindDrugNames
		detailDatas := make([]models.DetailOrderAddInfoShow, len(orderAddTask.Data.DetailData))
		for y, detailData := range orderAddTask.Data.DetailData {
			detailDataShow := models.DetailOrderAddInfoShow{
				Name:           detailData.Name,
				BatchNumber:    detailData.BatchNumber,
				ExpirationDate: detailData.ExpirationDate,
				PackageMethod:  detailData.PackageMethod,
				Count:          strconv.Itoa(detailData.Count),
				UseCount:       strconv.Itoa(detailData.UseCount),
			}
			isBlindedDrug, _ := tools.IsBlindedDrug(orderAddTask.EnvironmentID, detailData.Name)
			if isBlindedDrug && isBlindedRole {
				detailDataShow.Name = tools.BlindData
				detailDataShow.Count = tools.BlindData
				detailDataShow.UseCount = tools.BlindData
			}
			detailDatas[y] = detailDataShow
		}
		otherDetailDatas := make([]models.DetailOrderAddInfoShow, len(orderAddTask.Data.OtherDetailData))
		for y, detailData := range orderAddTask.Data.OtherDetailData {
			detailDataShow := models.DetailOrderAddInfoShow{
				Name:           detailData.Name,
				BatchNumber:    detailData.BatchNumber,
				ExpirationDate: detailData.ExpirationDate,
				Count:          strconv.Itoa(detailData.Count),
				UseCount:       strconv.Itoa(detailData.UseCount),
			}
			isBlindedDrug, _ := tools.IsBlindedDrug(orderAddTask.EnvironmentID, detailData.Name)
			if isBlindedDrug && isBlindedRole {
				detailDataShow.Name = tools.BlindData
				detailDataShow.Count = tools.BlindData
				detailDataShow.UseCount = tools.BlindData
			}
			otherDetailDatas[y] = detailDataShow
		}

		approvalTaskShow = models.OrderAddTaskShow{
			ID:              orderAddTask.ID,
			CustomerID:      orderAddTask.CustomerID,
			ProjectID:       orderAddTask.ProjectID,
			EnvironmentID:   orderAddTask.EnvironmentID,
			ApprovalProcess: orderAddTask.ApprovalProcess,
			Data: models.OrderAddInfoShow{
				SendID:              orderAddTask.Data.SendID,
				ReceiveID:           orderAddTask.Data.ReceiveID,
				Mode:                orderAddTask.Data.Mode,
				DrugNames:           orderAddTask.Data.DrugNames,
				SupplyID:            orderAddTask.Data.SupplyID,
				SupplyCount:         orderAddTask.Data.SupplyCount,
				Contacts:            orderAddTask.Data.Contacts,
				Phone:               orderAddTask.Data.Phone,
				Email:               orderAddTask.Data.Email,
				Address:             orderAddTask.Data.Address,
				DetailData:          detailDatas,
				OtherDetailData:     otherDetailDatas,
				BlindCount:          orderAddTask.Data.BlindCount,
				OpenDrugCount:       orderAddTask.Data.OpenDrugCount,
				ExpectedArrivalTime: orderAddTask.Data.ExpectedArrivalTime,
			},
		}
	} else {
		approvalTaskShow = models.OrderAddTaskShow{
			ID:              orderAddTask.ID,
			CustomerID:      orderAddTask.CustomerID,
			ProjectID:       orderAddTask.ProjectID,
			EnvironmentID:   orderAddTask.EnvironmentID,
			ApprovalProcess: orderAddTask.ApprovalProcess,
			UnblindingData: models.UnblindingDataInfoShow{
				SubjectID:           orderAddTask.UnblindingData.SubjectID,
				SubjectNumber:       orderAddTask.UnblindingData.SubjectNumber,
				SubjectRandomNumber: orderAddTask.UnblindingData.SubjectRandomNumber,
				Number:              orderAddTask.UnblindingData.Number,
				Status:              orderAddTask.UnblindingData.Status,
				ApprovalType:        orderAddTask.UnblindingData.ApprovalType,
				ApplicationTime:     orderAddTask.UnblindingData.ApplicationTime,
				ApplicationBy:       orderAddTask.UnblindingData.ApplicationBy,
				ApprovalTime:        orderAddTask.UnblindingData.ApprovalTime,
				ApprovalBy:          orderAddTask.UnblindingData.ApprovalBy,
				ApplicationByEmail:  orderAddTask.UnblindingData.ApplicationByEmail,
				ApprovalByEmail:     orderAddTask.UnblindingData.ApprovalByEmail,
				ReasonStr:           orderAddTask.UnblindingData.ReasonStr,
				Remark:              orderAddTask.UnblindingData.Remark,
				RejectReason:        orderAddTask.UnblindingData.RejectReason,
			},
		}

		//查询受试者的项目属性
		var subject models.Subject
		err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": orderAddTask.UnblindingData.SubjectID}).Decode(&subject)
		if err != nil {
			return models.OrderAddTaskShow{}, errors.WithStack(err)
		}

		match := bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID}
		if subject.CohortID != primitive.NilObjectID {
			match = bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}
		}

		var attribute models.Attribute
		err = tools.Database.Collection("attribute").FindOne(nil, match).Decode(&attribute)
		if err != nil {
			return models.OrderAddTaskShow{}, errors.WithStack(err)
		}

		// 盲法项目查询紧急揭盲和pv揭盲权限的用户
		if attribute.AttributeInfo.Blind {
			// 查询该环境关联的所有用户
			userProjectEnvironmentList := make([]models.UserProjectEnvironment, 0)
			userProjectEnvironmentCursor, err := tools.Database.Collection("user_project_environment").Find(nil, bson.M{"env_id": subject.EnvironmentID})
			if err != nil {
				return models.OrderAddTaskShow{}, errors.WithStack(err)
			}
			err = userProjectEnvironmentCursor.All(nil, &userProjectEnvironmentList)
			if err != nil {
				return models.OrderAddTaskShow{}, errors.WithStack(err)
			}
			var userIDs []primitive.ObjectID
			for _, upel := range userProjectEnvironmentList {
				userIDs = append(userIDs, upel.UserID)
			}
			// 查询user
			var userList []models.User
			userCursor, err := tools.Database.Collection("user").Find(nil, bson.M{"_id": bson.M{"$in": userIDs}})
			if err != nil {
				return models.OrderAddTaskShow{}, errors.WithStack(err)
			}
			err = userCursor.All(nil, &userList)
			if err != nil {
				return models.OrderAddTaskShow{}, errors.WithStack(err)
			}

			// 查询项目角色权限
			projectRolePermissionList := make([]models.ProjectRolePermission, 0)
			projectRolePermissionCursor, err := tools.Database.Collection("project_role_permission").Find(nil, bson.M{"project_id": subject.ProjectID, "name": bson.M{"$ne": "Project-Admin"}})
			if err != nil {
				return models.OrderAddTaskShow{}, errors.WithStack(err)
			}
			err = projectRolePermissionCursor.All(nil, &projectRolePermissionList)
			if err != nil {
				return models.OrderAddTaskShow{}, errors.WithStack(err)
			}
			// 查询用户关联的角色
			userSiteList := make([]models.UserSite, 0)
			userSiteCursor, err := tools.Database.Collection("user_site").Find(nil, bson.M{"env_id": subject.EnvironmentID})
			if err != nil {
				return models.OrderAddTaskShow{}, errors.WithStack(err)
			}
			err = userSiteCursor.All(nil, &userSiteList)
			if err != nil {
				return models.OrderAddTaskShow{}, errors.WithStack(err)
			}
			unblindingApprovalUser, pvUnblindingApprovalUser, _ := findApprovalUser(userProjectEnvironmentList, userList, projectRolePermissionList, userSiteList, subject.ProjectSiteID)
			if orderAddTask.Type == 2 {
				approvalTaskShow.UnblindingData.ApprovalUser = unblindingApprovalUser
			} else if orderAddTask.Type == 3 {
				approvalTaskShow.UnblindingData.ApprovalUser = pvUnblindingApprovalUser
			}
		}

	}

	return approvalTaskShow, nil
}

func (s *ApprovalProcessService) UpdateAppApprovalProcess(ctx *gin.Context, workTaskUpdateOrderReq models.WorkTaskUpdateOrderReq) error {
	var workTask models.WorkTask
	err := tools.Database.Collection("work_task").FindOne(ctx, bson.M{"_id": workTaskUpdateOrderReq.WorkTaskID}).Decode(&workTask)
	if err != nil {
		return errors.WithStack(err)
	}

	//判断任务状态
	if workTask.Info.Status == 1 {
		return tools.BuildServerError(ctx, "work.task.error")
	}

	var updateOrderAddTask models.OrderAddTask
	updateOrderAddTask.ID = workTask.Info.ApprovalProcessID
	updateOrderAddTask.ApprovalStatus = workTaskUpdateOrderReq.Status
	updateOrderAddTask.Reason = workTaskUpdateOrderReq.Reason

	return s.UpdateApprovalProcess(ctx, updateOrderAddTask)
}

func (s *ApprovalProcessService) UpdateApprovalProcess(ctx *gin.Context, updateOrderAddTask models.OrderAddTask) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var operHistories []models.History
		user, err := tools.Me(ctx)
		if err != nil {
			return nil, err
		}
		var orderAddTask models.OrderAddTask
		tools.Database.Collection("approval_process").FindOne(nil, bson.M{"_id": updateOrderAddTask.ID}).Decode(&orderAddTask)
		now := time.Now()
		//判断流程状态
		if orderAddTask.Status == 1 {
			return nil, tools.BuildServerError(ctx, "approval_task_error")
		}

		//研究中心订单申请
		if orderAddTask.Type == 1 {
			var medicinesData []models.Medicine
			medicineMatch := bson.M{"approval_process_number": orderAddTask.Number, "env_id": orderAddTask.EnvironmentID, "status": 20}
			cursor, err := tools.Database.Collection("medicine").Find(sctx, medicineMatch)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(sctx, &medicinesData)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			key := ""
			orderNumber := ""
			if updateOrderAddTask.ApprovalStatus == 1 { //已通过，创建订单

				// 获取订单号
				orderNumber, err = getOrderNumber(sctx, orderAddTask.ProjectID)
				if err != nil {
					return "", errors.WithStack(err)
				}

				//判断起运地是仓库还是中心
				orderType := 1
				match := bson.M{"_id": orderAddTask.Data.SendID}
				siteCount, _ := tools.Database.Collection("project_site").CountDocuments(sctx, match)
				if siteCount > 0 {
					orderType = 2
				}

				var medicines []primitive.ObjectID

				for _, medicine := range medicinesData {
					medicines = append(medicines, medicine.ID)
				}

				// var otherMedicineCounts []models.OtherMedicineCount
				// for _, other := range orderAddTask.Data.OtherDetailData {
				// 	otherMedicineCount := models.OtherMedicineCount{
				// 		ID:         primitive.NewObjectID(),
				// 		Name:       other.Name,
				// 		Batch:      other.BatchNumber,
				// 		ExpireDate: other.ExpirationDate,
				// 		UseCount:   other.Count,
				// 	}
				// 	otherMedicineCounts = append(otherMedicineCounts, otherMedicineCount)

				// 	//更新未编号研究产品状态数量
				// 	var medicineOtherInstitute models.MedicineOtherInstitute
				// 	err := tools.Database.Collection("medicine_other_institute").FindOne(nil, bson.M{"env_id": orderAddTask.EnvironmentID, "storehouse_id": orderAddTask.Data.SendID, "info.name": other.Name, "info.batch": other.BatchNumber, "info.expire_date": other.ExpirationDate}).Decode(&medicineOtherInstitute)
				// 	if err != nil {
				// 		return nil, errors.WithStack(err)
				// 	}
				// 	//更新
				// 	otherMedicineUpdate := bson.M{
				// 		"$set": bson.M{
				// 			"edit":              true,
				// 			"info.locked_count": medicineOtherInstitute.Info.LockedCount - other.Count,
				// 		},
				// 	}
				// 	_, err = tools.Database.Collection("medicine_other_institute").UpdateOne(sctx, bson.M{"_id": medicineOtherInstitute.ID}, otherMedicineUpdate)
				// 	if err != nil {
				// 		return nil, errors.WithStack(err)
				// 	}
				// 	var receiveMedicineOtherInstitute models.MedicineOtherInstitute
				// 	if rerr := tools.Database.Collection("medicine_other_institute").FindOne(nil, bson.M{"env_id": orderAddTask.EnvironmentID, "institute_id": orderAddTask.Data.ReceiveID, "info.name": other.Name, "info.batch": other.BatchNumber, "info.expire_date": other.ExpirationDate}).Decode(&receiveMedicineOtherInstitute); rerr != nil {
				// 		other := models.MedicineOtherInstitute{
				// 			ID:            primitive.NewObjectID(),
				// 			CustomerID:    orderAddTask.CustomerID,
				// 			ProjectID:     orderAddTask.ProjectID,
				// 			EnvironmentID: orderAddTask.EnvironmentID,
				// 			InstituteID:   orderAddTask.Data.ReceiveID,
				// 			Info: models.MedicineOtherInfo{
				// 				Name:             other.Name,
				// 				Batch:            other.BatchNumber,
				// 				ExpireDate:       other.ExpirationDate,
				// 				ToBeConfirmCount: other.Count,
				// 				//ToBeSendCount: otherMedicine.UseCount,
				// 			},
				// 		}
				// 		_, err := tools.Database.Collection("medicine_other_institute").InsertOne(sctx, other)
				// 		if err != nil {
				// 			return nil, errors.WithStack(err)
				// 		}
				// 	} else {
				// 		toBeConfirmCount := receiveMedicineOtherInstitute.Info.ToBeConfirmCount + other.Count
				// 		update := bson.M{
				// 			"$set": bson.M{
				// 				"edit":                     true,
				// 				"info.to_be_confirm_count": toBeConfirmCount,
				// 			},
				// 		}
				// 		if _, err := tools.Database.Collection("medicine_other_institute").UpdateOne(sctx, bson.M{"_id": receiveMedicineOtherInstitute.ID}, update); err != nil {
				// 			return nil, errors.WithStack(err)
				// 		}
				// 	}

				// }

				var otherMedicinesData []models.OtherMedicine
				otherMedicineMatch := bson.M{"approval_process_number": orderAddTask.Number, "env_id": orderAddTask.EnvironmentID, "status": 20}
				cursor, err := tools.Database.Collection("medicine_others").Find(sctx, otherMedicineMatch)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = cursor.All(sctx, &otherMedicinesData)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				var otherMedicines []primitive.ObjectID
				for _, medicine := range otherMedicinesData {
					otherMedicines = append(otherMedicines, medicine.ID)
				}

				orderId := primitive.NewObjectID()
				order := models.MedicineOrder{
					ID:               orderId,
					CustomerID:       orderAddTask.CustomerID,
					ProjectID:        orderAddTask.ProjectID,
					EnvironmentID:    orderAddTask.EnvironmentID,
					SendID:           orderAddTask.Data.SendID,
					ReceiveID:        orderAddTask.Data.ReceiveID,
					Status:           6,
					SortIndex:        1,
					Mode:             orderAddTask.Data.Mode,
					Medicines:        medicines,
					MedicinesHistory: []models.MedicinesHistory{},
					//OtherMedicines:      otherMedicineCounts,
					OtherMedicinesNew:   otherMedicines,
					OrderNumber:         orderNumber,
					Type:                orderType,
					TaskID:              orderAddTask.ID,
					Contacts:            orderAddTask.Data.Contacts,
					Phone:               orderAddTask.Data.Phone,
					Email:               orderAddTask.Data.Email,
					Address:             orderAddTask.Data.Address,
					App:                 orderAddTask.App,
					ExpectedArrivalTime: orderAddTask.Data.ExpectedArrivalTime,
					MedicinesPackage:    orderAddTask.Data.MedicinesPackage,
					Meta: models.Meta{
						CreatedBy: user.ID,
						CreatedAt: time.Duration(time.Now().Unix()),
					},
				}
				result, err := tools.Database.Collection("medicine_order").InsertOne(sctx, order)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				//创建待确认的订单任务
				permissions := []string{"operation.supply.shipment.cancel", "operation.supply.shipment.confirm"}
				siteOrStoreIDs := []primitive.ObjectID{orderAddTask.Data.SendID, orderAddTask.Data.ReceiveID}
				userIds, err := tools.GetPermissionUserIds(sctx, permissions, orderAddTask.ProjectID, orderAddTask.EnvironmentID, siteOrStoreIDs...)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				if len(userIds) > 0 {
					workTask := models.WorkTask{
						ID:            primitive.NewObjectID(),
						CustomerID:    orderAddTask.CustomerID,
						ProjectID:     orderAddTask.ProjectID,
						EnvironmentID: orderAddTask.EnvironmentID,
						CohortID:      orderAddTask.CohortID,
						UserIDs:       userIds,
						Info: models.WorkTaskInfo{
							WorkType:        2,
							Status:          0,
							CreatedTime:     time.Duration(time.Now().Unix()),
							Deadline:        time.Duration(time.Now().AddDate(0, 0, 3).Unix()),
							MedicineIDs:     []primitive.ObjectID{},
							MedicineOrderID: orderId,
							DispensingID:    primitive.NilObjectID,
						},
					}
					_, err = tools.Database.Collection("work_task").InsertOne(sctx, workTask)
					if err != nil {
						return nil, errors.WithStack(err)
					}
				}

				//更新研究产品状态
				if len(medicines) > 0 {
					medicineFilter := bson.M{"_id": bson.M{"$in": medicines}}
					update := bson.M{
						"$set": bson.M{
							"status":   11,
							"order_id": result.InsertedID,
							// "site_id":       orderAddTask.Data.ReceiveID,
							// "storehouse_id": nil,
						},
					}
					if _, err := tools.Database.Collection("medicine").UpdateMany(sctx, medicineFilter, update); err != nil {
						return nil, errors.WithStack(err)
					}
				}

				if len(otherMedicines) > 0 {
					medicineFilter := bson.M{"_id": bson.M{"$in": otherMedicines}}
					update := bson.M{
						"$set": bson.M{
							"status":   11,
							"order_id": result.InsertedID,
							// "site_id":       orderAddTask.Data.ReceiveID,
							// "storehouse_id": nil,
						},
					}
					if _, err := tools.Database.Collection("medicine_others").UpdateMany(sctx, medicineFilter, update); err != nil {
						return nil, errors.WithStack(err)
					}
				}

				// //发送邮件
				// confirmOrder := models.RecevieOrder{
				// 	Medicines: medicines,
				// }
				// err = SendOrderMail(ctx, order, confirmOrder)
				// if err != nil {
				// 	return nil, errors.WithStack(err)
				// }
				approvalTaskHistory := models.History{
					Key:  "history.order.approval",
					Data: bson.M{"orderNumber": orderNumber},
					OID:  orderId,
					Time: time.Duration(time.Now().Unix()),
					UID:  user.ID,
					User: user.Name,
				}
				operHistories = append(operHistories, approvalTaskHistory)
			} else if updateOrderAddTask.ApprovalStatus == 2 { //已拒绝，更新药物状态
				key = "history.medicine.canUse"
				//更新审批任务
				match := bson.M{"approval_process_number": orderAddTask.Number, "env_id": orderAddTask.EnvironmentID, "status": 20}
				update := bson.M{
					"$set": bson.M{
						"status": 1,
					},
				}
				_, err := tools.Database.Collection("medicine").UpdateMany(sctx, match, update)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				_, err = tools.Database.Collection("medicine_others").UpdateMany(sctx, match, update)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				// for _, other := range orderAddTask.Data.OtherDetailData {
				// 	//更新未编号研究产品状态数量
				// 	var medicineOtherInstitute models.MedicineOtherInstitute
				// 	err := tools.Database.Collection("medicine_other_institute").FindOne(nil, bson.M{"env_id": orderAddTask.EnvironmentID, "storehouse_id": orderAddTask.Data.SendID, "info.name": other.Name, "info.batch": other.BatchNumber, "info.expire_date": other.ExpirationDate}).Decode(&medicineOtherInstitute)
				// 	if err != nil {
				// 		return nil, errors.WithStack(err)
				// 	}
				// 	//更新
				// 	otherMedicineUpdate := bson.M{
				// 		"$set": bson.M{
				// 			"edit":              true,
				// 			"info.count":        medicineOtherInstitute.Info.Count + other.Count,
				// 			"info.locked_count": medicineOtherInstitute.Info.LockedCount - other.Count,
				// 		},
				// 	}
				// 	_, err = tools.Database.Collection("medicine_other_institute").UpdateOne(sctx, bson.M{"_id": medicineOtherInstitute.ID}, otherMedicineUpdate)
				// 	if err != nil {
				// 		return nil, errors.WithStack(err)
				// 	}

				// }
			}

			//更新app的work_task
			workTaskFilter := bson.M{"info.approval_process_id": orderAddTask.ID, "info.work_type": 7}
			var workTask models.WorkTask
			err = tools.Database.Collection("work_task").FindOne(sctx, workTaskFilter).Decode(&workTask)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}
			if !workTask.ID.IsZero() && workTask.Info.Status == 0 {
				workTaskUpdate := bson.M{"$set": bson.M{
					"info.status":         1,
					"info.finish_time":    time.Duration(time.Now().Unix()),
					"info.finish_user_id": user.ID,
					//"info.finish_role_id": workTaskUpdateOrderReq.RoleID,
					//"info.medicine_ids":   order.Medicines,
				}}
				_, err = tools.Database.Collection("work_task").UpdateOne(sctx, workTaskFilter, workTaskUpdate)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}

			//审批任务拒绝，发送邮件通知
			state, err := tools.MailCustomContentState(orderAddTask.EnvironmentID, "order.approval.failed-title")
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if updateOrderAddTask.ApprovalStatus == 2 && state {
				var project models.Project
				if err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": orderAddTask.ProjectID}).Decode(&project); err != nil {
					return nil, errors.WithStack(err)
				}
				envName := ""
				for _, environment := range project.Environments {
					if environment.ID == orderAddTask.EnvironmentID {
						envName = environment.Name
					}
				}

				//获取发送方名称
				match := bson.M{"_id": orderAddTask.Data.SendID}
				siteCount, _ := tools.Database.Collection("project_site").CountDocuments(sctx, match)
				var sendData []map[string]interface{}
				matchSite := bson.M{"_id": orderAddTask.Data.SendID}
				document := "project_site"
				pipeline := mongo.Pipeline{}
				if siteCount > 0 {
					pipeline = mongo.Pipeline{
						{{Key: "$match", Value: matchSite}},
						{{Key: "$project", Value: bson.M{
							"_id":    0,
							"id":     "$_id",
							"number": "$number",

							"name":     models.ProjectSiteNameBsonLang("zh"),
							"nameEn":   models.ProjectSiteNameBsonLang("en"),
							"email":    "$email",
							"contacts": "$contacts",
							"phone":    "$phone",
							"address":  "$address",
						}}},
					}
				} else {
					pipeline = mongo.Pipeline{
						{{Key: "$match", Value: matchSite}},
						{{Key: "$lookup", Value: bson.M{
							"from":         "storehouse",
							"localField":   "storehouse_id",
							"foreignField": "_id",
							"as":           "detail",
						}}},
						{{Key: "$unwind", Value: "$detail"}},
						{{Key: "$project", Value: bson.M{
							"_id":      0,
							"id":       "$_id",
							"number":   "$detail.number",
							"name":     "$detail.name",
							"nameEn":   "$detail.name",
							"email":    "$detail.email",
							"contacts": "$detail.contacts",
							"phone":    "$detail.phone",
							"address":  "$detail.address",
						}}},
					}
					document = "project_storehouse"
				}
				sendCursor, err := tools.Database.Collection(document).Aggregate(nil, pipeline)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = sendCursor.All(nil, &sendData)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				//获取接收方名称
				matchSite = bson.M{"_id": orderAddTask.Data.ReceiveID}
				projectMongo := bson.M{
					"_id":      0,
					"id":       "$_id",
					"number":   "$number",
					"name":     models.ProjectSiteNameBsonLang("zh"),
					"nameEn":   models.ProjectSiteNameBsonLang("en"),
					"email":    "$email",
					"contacts": "$contacts",
					"phone":    "$phone",
					"address":  "$address",
				}
				pipeline = mongo.Pipeline{
					{{Key: "$match", Value: matchSite}},
					{{Key: "$project", Value: projectMongo}},
				}
				var siteData []map[string]interface{}

				siteCursor, err := tools.Database.Collection("project_site").Aggregate(nil, pipeline)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = siteCursor.All(nil, &siteData)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				var supplyPlan models.SupplyPlan
				if orderAddTask.Data.SupplyID != primitive.NilObjectID {
					err = tools.Database.Collection("supply_plan").FindOne(sctx, bson.M{"_id": orderAddTask.Data.SupplyID}).Decode(&supplyPlan)
					if err != nil {
						return nil, errors.WithStack(err)
					}
				}

				contentData := map[string]interface{}{
					"projectNumber": project.Number,
					"projectName":   project.Name,
					"envName":       envName,
					"start":         fmt.Sprintf("%s %s", sendData[0]["number"], sendData[0]["name"]),
					"startEn":       fmt.Sprintf("%s %s", sendData[0]["number"], sendData[0]["nameEn"]),
					"destination":   fmt.Sprintf("%s", siteData[0]["name"]),
					"destinationEn": fmt.Sprintf("%s", siteData[0]["nameEn"]),
					"IP":            orderAddTask.Data.DrugNames,
					"supplyMode":    data.GetMedicineOrderModeLang("zh", orderAddTask.Data.Mode),
					"supplyModeEn":  data.GetMedicineOrderModeLang("en", orderAddTask.Data.Mode),
					"supplyPlan":    supplyPlan.SupplyPlanInfo.Name,
					"supplyShow":    orderAddTask.Data.Mode != 5,
					"blindCount":    orderAddTask.Data.BlindCount,
					"openDrugCount": orderAddTask.Data.OpenDrugCount,
					"Link":          config.CLOUD,
				}
				if siteCount > 0 {
					contentData["start"] = fmt.Sprintf("%s %s", sendData[0]["number"], sendData[0]["name"])
					contentData["startEn"] = fmt.Sprintf("%s %s", sendData[0]["number"], sendData[0]["nameEn"])
				} else {
					contentData["start"] = fmt.Sprintf("%s", sendData[0]["name"])
					contentData["startEn"] = fmt.Sprintf("%s", sendData[0]["nameEn"])
				}

				mailBodyContet, err := tools.MailBodyContent(ctx, orderAddTask.EnvironmentID, "notice.medicine.order")
				for key, v := range mailBodyContet {
					contentData[key] = v
				}
				if err != nil {
					return nil, errors.WithStack(err)
				}

				var noticeConfig models.NoticeConfig
				err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": orderAddTask.EnvironmentID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
				if err != nil && err != mongo.ErrNoDocuments {
					return nil, errors.WithStack(err)
				}

				langList := make([]string, 0)
				html := "medicine_order_approval_failed_new_zh_en.html"
				if noticeConfig.Manual != 0 {
					if noticeConfig.Manual == 1 {
						langList = append(langList, "zh")
						html = "medicine_order_approval_failed_new_zh.html"
					} else if noticeConfig.Manual == 2 {
						langList = append(langList, "en")
						html = "medicine_order_approval_failed_new_en.html"
					} else if noticeConfig.Manual == 3 {
						langList = append(langList, "zh")
						langList = append(langList, "en")
						html = "medicine_order_approval_failed_new_zh_en.html"
					}
				} else {
					langList = append(langList, ctx.GetHeader("Accept-Language"))
					if locales.Lang(ctx) == "zh" {
						html = "medicine_order_approval_failed_new_zh.html"
					} else if locales.Lang(ctx) == "en" {
						html = "medicine_order_approval_failed_new_en.html"
					}
				}

				var nc models.NoticeConfig
				err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": orderAddTask.EnvironmentID, "key": "notice.medicine.order"}).Decode(&nc)
				if err != nil && err != mongo.ErrNoDocuments {
					return nil, err
				}

				isExists := false
				if nc.ExcludeRecipientList != nil && len(nc.ExcludeRecipientList) > 0 {
					index := arrays.ContainsString(nc.ExcludeRecipientList, orderAddTask.ApplicationByEmail)
					if index != -1 { //存在
						isExists = true
					}
				}

				if !isExists {
					var mails []models.Mail
					mails = append(mails, models.Mail{
						ID:           primitive.NewObjectID(),
						Subject:      "order.approval.failed-title",
						SubjectData:  map[string]interface{}{"projectNumber": project.Number, "projectName": project.Name, "envName": envName},
						HTML:         html,
						ContentData:  contentData,
						To:           []string{orderAddTask.ApplicationByEmail},
						Lang:         ctx.GetHeader("Accept-Language"),
						LangList:     langList,
						Status:       0,
						CreatedTime:  time.Duration(time.Now().Unix()),
						ExpectedTime: time.Duration(time.Now().Unix()),
						SendTime:     time.Duration(time.Now().Unix()),
					})
					ctx.Set("MAIL", mails)
					if len(mails) > 0 {
						var envs []models.MailEnv
						for _, m := range mails {
							envs = append(envs, models.MailEnv{
								ID:         primitive.NewObjectID(),
								MailID:     m.ID,
								CustomerID: orderAddTask.CustomerID,
								ProjectID:  orderAddTask.ProjectID,
								EnvID:      orderAddTask.EnvironmentID,
							})
						}
						ctx.Set("MAIL-ENV", envs)
					}
				}

			}

			//药物轨迹
			for _, medicine := range medicinesData {
				operHistory := models.History{
					Key:  key,
					Data: bson.M{"packNumber": medicine.Number, "order": orderNumber},
					OID:  medicine.ID,
					Time: time.Duration(time.Now().Unix()),
					UID:  user.ID,
					User: user.Name,
				}
				operHistories = append(operHistories, operHistory)
			}
			ctx.Set("HISTORY", operHistories)
		} else if orderAddTask.Type == 2 || orderAddTask.Type == 3 {
			s.subjectService.UnbindingApproval(ctx, sctx, orderAddTask.ProjectID, orderAddTask.EnvironmentID, orderAddTask.UnblindingData.SubjectID, orderAddTask.UnblindingData.Number, strconv.Itoa(orderAddTask.Type), updateOrderAddTask.ApprovalStatus, updateOrderAddTask.Reason, updateOrderAddTask.RoleID, time.Now())
		} else if orderAddTask.Type == 4 {

			var subject models.Subject
			err := tools.Database.Collection("subject").FindOne(sctx, bson.M{"_id": orderAddTask.UnblindingData.SubjectID}).Decode(&subject)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			_, err = UnbindingApproval(ctx, sctx, subject, orderAddTask.UnblindingData.MedicineID, orderAddTask.UnblindingData.DispensingID, orderAddTask.UnblindingData.Number, updateOrderAddTask.ApprovalStatus, updateOrderAddTask.Reason, updateOrderAddTask.RoleID, now)
			if err != nil {
				return nil, errors.WithStack(err)
			}

		}

		if orderAddTask.Type != 4 { //更新审批任务
			match := bson.M{"_id": orderAddTask.ID}
			if err != nil {
				return nil, errors.WithStack(err)
			}
			update := bson.M{
				"$set": bson.M{
					"approval_status":                   updateOrderAddTask.ApprovalStatus,
					"approval_time":                     time.Duration(now.Unix()),
					"approval_by":                       user.ID,
					"approval_by_email":                 user.Email,
					"reason":                            updateOrderAddTask.Reason,
					"status":                            1,
					"unblinding_data.approval_time":     time.Duration(time.Now().Unix()),
					"unblinding_data.approval_by":       user.ID,
					"unblinding_data.approval_by_email": user.Email,
					"unblinding_data.reason":            updateOrderAddTask.Reason,
					"unblinding_data.status":            updateOrderAddTask.ApprovalStatus,
				},
			}
			_, err = tools.Database.Collection("approval_process").UpdateOne(sctx, match, update)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}
