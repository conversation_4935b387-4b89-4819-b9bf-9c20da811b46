package service

import (
	"clinflash-irt/data"
	"clinflash-irt/database"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/multilingual"
	"clinflash-irt/tools"
	"context"
	"fmt"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/maputil"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/pkg/errors"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// 中心单品统计报表导出
func ExportSiteIPStatisticsExport(ctx *gin.Context, projectID string, envID string, roleId string, now time.Time, templateId string) (string, []byte, error) {
	//判断当前角色数据权限
	role, err := tools.GetRole(roleId)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)

	var parameter map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&parameter, binding.JSON)

	siteID := ""
	if parameter["siteId"] != nil {
		siteID = parameter["siteId"].(string)
	}

	medicine := make([]map[string]interface{}, 0)
	otherMedicines := make([]map[string]interface{}, 0)
	var template models.CustomTemplate
	if templateId != "" {
		templateOID, _ := primitive.ObjectIDFromHex(templateId)
		err := tools.Database.Collection("custom_template").FindOne(context.Background(), bson.M{
			"_id": templateOID,
		}).Decode(&template)
		if err != nil {
			return "", nil, err
		}
	}

	if role.Scope != "depot" {
		//编码药物统计
		filter := bson.M{
			"project_site.deleted": 2,
			"project_id":           projectOID,
			"env_id":               envOID,
		}
		match := bson.A{}
		if siteID != "" {
			siteOID, _ := primitive.ObjectIDFromHex(siteID)
			filter["site_id"] = siteOID
		} else {
			match = append(match, bson.M{"$and": bson.A{
				bson.M{"site_id": bson.M{"$ne": primitive.NilObjectID}},
				bson.M{"site_id": bson.M{"$ne": nil}},
			}})
		}
		match = append(match, filter)
		project := bson.M{
			"id":              "$_id",
			"_id":             0,
			"name":            1,
			"siteNumber":      "$project_site.number",
			"siteName":        models.ProjectSiteNameLookUpBson(ctx),
			"status":          "$status",
			"package_number":  1,
			"spec":            1,
			"expiration_date": 1,
			"batch_number":    1,
		}

		mpipeline := mongo.Pipeline{
			{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "site_id", "foreignField": "_id", "as": "project_site"}}},
			{{Key: "$unwind", Value: "$project_site"}},
			{{Key: "$match", Value: bson.M{"$and": match}}},
			{{Key: "$project", Value: project}},
		}

		if role.Scope == "site" {
			user, err := tools.Me(ctx)
			if err != nil {
				return "", nil, errors.WithStack(err)
			}
			filter["user_site.user_id"] = user.ID
			mpipeline = mongo.Pipeline{
				{{Key: "$lookup", Value: bson.M{"from": "user_site", "localField": "site_id", "foreignField": "site_id", "as": "user_site"}}},
				{{Key: "$unwind", Value: "$user_site"}},
				{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "user_site.site_id", "foreignField": "_id", "as": "project_site"}}},
				{{Key: "$unwind", Value: "$project_site"}},
				{{Key: "$match", Value: bson.M{"$and": match}}},
				{{Key: "$project", Value: project}},
			}
		}

		mall, err := tools.Database.Collection("medicine").Aggregate(nil, mpipeline)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = mall.All(nil, &medicine)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}

		//未编码药物统计
		pipeline := mongo.Pipeline{
			{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "site_id", "foreignField": "_id", "as": "project_site"}}},
			{{Key: "$unwind", Value: "$project_site"}},
			{{Key: "$match", Value: bson.M{"$and": match}}},
			{{Key: "$project", Value: project}},
		}

		if role.Scope == "site" {
			user, err := tools.Me(ctx)
			if err != nil {
				return "", nil, errors.WithStack(err)
			}
			filter["user_site.user_id"] = user.ID
			pipeline = mongo.Pipeline{
				{{Key: "$lookup", Value: bson.M{"from": "user_site", "localField": "site_id", "foreignField": "site_id", "as": "user_site"}}},
				{{Key: "$unwind", Value: "$user_site"}},
				{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "user_site.site_id", "foreignField": "_id", "as": "project_site"}}},
				{{Key: "$unwind", Value: "$project_site"}},
				{{Key: "$match", Value: bson.M{"$and": match}}},
				{{Key: "$project", Value: project}},
			}
		}

		all, err := tools.Database.Collection("medicine_others").Aggregate(nil, pipeline)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = all.All(nil, &otherMedicines)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}

		var attribute models.Attribute
		attributeMatch := bson.M{
			"project_id": projectOID,
			"env_id":     envOID,
		}
		if parameter["cohortId"] != nil {
			cohortID := parameter["cohortId"].(string)
			cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
			if cohortOID != primitive.NilObjectID {
				attributeMatch["cohort_id"] = cohortOID
			}
		}

		err = tools.Database.Collection("attribute").FindOne(nil, attributeMatch).Decode(&attribute)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		isBlindedRole, err := tools.IsBlindedRole(roleId)
		if err != nil {
			return "", nil, err
		}
		blindData := make([]string, 0)
		if isBlindedRole {
			blindDataMap, err := tools.IsBlindDrugMap(envOID)
			if err != nil {
				return "", nil, errors.WithStack(err)
			}
			for key, _ := range blindDataMap {
				blindData = append(blindData, key)
			}
		}
	}
	//包装
	type otherPackageNumber struct {
		ID            primitive.ObjectID `bson:"_id"`
		PackageNumber int                `bson:"package_number"`
		PackageCount  int                `bson:"package_count"`
	}

	otherPackageNumbers := make([]otherPackageNumber, 0)
	packageMatch := bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
	}
	packagePipepine := mongo.Pipeline{
		{{Key: "$match", Value: packageMatch}},
		{{
			Key: "$project",
			Value: bson.M{
				"_id":            1,
				"package_number": 1,
				"package_count":  1,
			},
		}},
	}
	otherPackageAll, err := tools.Database.Collection("other_package_number").Aggregate(nil, packagePipepine)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = otherPackageAll.All(nil, &otherPackageNumbers)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	otherPackageNumberMap := make(map[int]int, len(otherPackageNumbers))
	for _, number := range otherPackageNumbers {
		otherPackageNumberMap[number.PackageNumber] = number.PackageCount
	}
	isBlindedRole, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return "", nil, err
	}
	bindDrugMap, err := tools.IsBlindDrugMap(envOID)
	var attributes []models.Attribute
	match := bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
	}
	cursor, err := tools.Database.Collection("attribute").Find(ctx, match)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	if err = cursor.All(ctx, &attributes); err != nil {
		return "", nil, errors.WithStack(err)
	}

	//写入excel文件
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return "", nil, errors.WithStack(err)
	}

	title := []any{locales.TrStash(ctx, "report.attributes.project.number")}
	title = append(title, locales.TrStash(ctx, "report.attributes.project.name"))
	title = append(title, locales.TrStash(ctx, "site.number"))
	title = append(title, locales.TrStash(ctx, "site.name"))
	title = append(title, locales.TrStash(ctx, "medicine_name"))
	medicineData := getSiteIPStatistics(template, medicine, otherMedicines, otherPackageNumberMap, envOID)
	content := make([][]interface{}, len(medicineData))
	if templateId != "" {

		for _, field := range template.Fields {
			if field == "report.attributes.research.expireDate" {
				title = append(title, locales.TrStash(ctx, "medicine_download_expiredDate"))
			} else if field == "report.attributes.research.batch" {
				title = append(title, locales.TrStash(ctx, "medicine_download_batch"))
			} else if field == "report.attributes.research.spec" {
				title = append(title, locales.TrStash(ctx, "report.attributes.research.spec"))
			}
		}

		title = append(title, locales.TrStash(ctx, "medicine.status.available"))
		title = append(title, locales.TrStash(ctx, "medicine.status.toBeConfirmed"))
		title = append(title, locales.TrStash(ctx, "medicine.status.delivered"))
		title = append(title, locales.TrStash(ctx, "medicine.status.sending"))
		title = append(title, locales.TrStash(ctx, "medicine.status.quarantine"))
		title = append(title, locales.TrStash(ctx, "medicine.status.used"))
		title = append(title, locales.TrStash(ctx, "medicine.status.lose"))
		title = append(title, locales.TrStash(ctx, "medicine.status.expired"))
		title = append(title, locales.TrStash(ctx, "medicine.status.frozen"))
		title = append(title, locales.TrStash(ctx, "medicine.status.locked"))

		if attributes[0].AttributeInfo.ConnectAli {
			title = append(title, locales.TrStash(ctx, "medicine.status.receive"))
			title = append(title, locales.TrStash(ctx, "medicine.status.return"))
			title = append(title, locales.TrStash(ctx, "medicine.status.destroy"))
		}

		for i := 0; i < len(medicineData); i++ {
			item := medicineData[i]
			row := []any{project.ProjectInfo.Number}
			row = append(row, project.ProjectInfo.Name)
			row = append(row, item["siteNumber"])
			row = append(row, item["siteName"])
			if isBlindedRole && bindDrugMap[item["name"].(string)] {
				item["name"] = tools.BlindData
			}
			row = append(row, item["name"])

			for _, field := range template.Fields {
				if field == "report.attributes.research.expireDate" {
					row = append(row, item["expiration_date"])
				} else if field == "report.attributes.research.batch" {
					row = append(row, item["batch_number"])
				} else if field == "report.attributes.research.spec" {
					row = append(row, item["spec"])
				}
			}

			row = append(row, item["count"])
			row = append(row, item["to_be_confirm_count"])
			row = append(row, item["to_be_send_count"])
			row = append(row, item["in_transit_count"])
			row = append(row, item["quarantined_count"])
			row = append(row, item["used_count"])
			row = append(row, item["lost_count"])
			row = append(row, item["expired_count"])
			row = append(row, item["frozen_count"])
			row = append(row, item["locked_count"])

			if project.ProjectInfo.Type == 1 {
				if attributes[0].AttributeInfo.ConnectAli {
					row = append(row, item["receive_count"])
					row = append(row, item["return_count"])
					row = append(row, item["destroy_count"])
				}
			}

			content[i] = row
		}

	} else {
		title = append(title, locales.TrStash(ctx, "medicine_download_expiredDate"))
		title = append(title, locales.TrStash(ctx, "medicine_download_batch"))
		title = append(title, locales.TrStash(ctx, "report.attributes.research.spec"))
		title = append(title, locales.TrStash(ctx, "medicine.status.available"))
		title = append(title, locales.TrStash(ctx, "medicine.status.toBeConfirmed"))
		title = append(title, locales.TrStash(ctx, "medicine.status.delivered"))
		title = append(title, locales.TrStash(ctx, "medicine.status.sending"))
		title = append(title, locales.TrStash(ctx, "medicine.status.quarantine"))
		title = append(title, locales.TrStash(ctx, "medicine.status.used"))
		title = append(title, locales.TrStash(ctx, "medicine.status.lose"))
		title = append(title, locales.TrStash(ctx, "medicine.status.expired"))
		title = append(title, locales.TrStash(ctx, "medicine.status.frozen"))
		title = append(title, locales.TrStash(ctx, "medicine.status.locked"))

		if attributes[0].AttributeInfo.ConnectAli {
			title = append(title, locales.TrStash(ctx, "medicine.status.receive"))
			title = append(title, locales.TrStash(ctx, "medicine.status.return"))
			title = append(title, locales.TrStash(ctx, "medicine.status.destroy"))
		}

		for i := 0; i < len(medicineData); i++ {
			item := medicineData[i]
			row := []any{project.ProjectInfo.Number}
			row = append(row, project.ProjectInfo.Name)
			row = append(row, item["siteNumber"])
			row = append(row, item["siteName"])
			if isBlindedRole && bindDrugMap[item["name"].(string)] {
				item["name"] = tools.BlindData
			}
			row = append(row, item["name"])
			row = append(row, item["expiration_date"])
			row = append(row, item["batch_number"])
			row = append(row, item["spec"])
			row = append(row, item["count"])
			row = append(row, item["to_be_confirm_count"])
			row = append(row, item["to_be_send_count"])
			row = append(row, item["in_transit_count"])
			row = append(row, item["quarantined_count"])
			row = append(row, item["used_count"])
			row = append(row, item["lost_count"])
			row = append(row, item["expired_count"])
			row = append(row, item["frozen_count"])
			row = append(row, item["locked_count"])

			if project.ProjectInfo.Type == 1 {
				if attributes[0].AttributeInfo.ConnectAli {
					row = append(row, item["receive_count"])
					row = append(row, item["return_count"])
					row = append(row, item["destroy_count"])
				}
			}

			content[i] = row
		}

	}

	envCode := getEnvName(project, envOID)
	fileName := fmt.Sprintf("%s[%s]SiteIPStatisticsReport_%s.xlsx", project.Number, envCode, now.Format("20060102150405"))

	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	streamWriter, _ := f.NewStreamWriter("Sheet1")
	titleTr := multilingual.TrBatch(ctx, title)
	t := make([]interface{}, len(titleTr))
	for i, item := range titleTr {
		t[i] = excelize.Cell{Value: item}
	}
	_ = streamWriter.SetRow("A1", t)
	for i := 1; i <= len(content); i++ {
		r := make([]interface{}, len(content[i-1]))
		for j := 0; j < len(content[i-1]); j++ {
			r[j] = excelize.Cell{Value: content[i-1][j]}
		}
		cell, _ := excelize.CoordinatesToCellName(1, i+1)
		_ = streamWriter.SetRow(cell, r)
	}
	_ = streamWriter.Flush()
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	return fileName, buffer.Bytes(), nil
}

// 库房单品统计导出
func ExportDepotIPStatisticsExport(ctx *gin.Context, projectID string, envID string, roleId string, now time.Time, templateId string) (string, []byte, error) {
	//判断当前角色数据权限
	role, err := tools.GetRole(roleId)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	file := excelize.NewFile()
	file.SetDefaultFont("黑体")
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return "", nil, errors.WithStack(err)
	}

	var parameter map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&parameter, binding.JSON)

	storehouseID := ""
	if parameter["storehouseId"] != nil {
		storehouseID = parameter["storehouseId"].(string)
	}

	user, err := tools.Me(ctx)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	isBlindedRole, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return "", nil, err
	}
	blindData := make([]string, 0)
	if isBlindedRole {
		blindDataMap, err := tools.IsBlindDrugMap(envOID)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		for key, _ := range blindDataMap {
			blindData = append(blindData, key)
		}
	}
	var attribute models.Attribute
	attributeMatch := bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
	}
	err = tools.Database.Collection("attribute").FindOne(nil, attributeMatch).Decode(&attribute)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	medicine := make([]map[string]interface{}, 0)
	otherMedicines := make([]map[string]interface{}, 0)
	if role.Scope != "site" {
		//编码药物统计
		filter := bson.M{
			"project_storehouse.deleted": 2,
			"project_id":                 projectOID,
			"env_id":                     envOID,
			"status":                     bson.M{"$ne": 0}, // 剔除待入仓的药物
		}
		match := bson.A{}
		if storehouseID != "" {
			storehouseOID, _ := primitive.ObjectIDFromHex(storehouseID)
			filter["storehouse_id"] = storehouseOID
		} else {
			match = append(match, bson.M{"$and": bson.A{
				bson.M{"storehouse_id": bson.M{"$ne": primitive.NilObjectID}},
				bson.M{"storehouse_id": bson.M{"$ne": nil}},
				bson.M{"storehouse_id": bson.M{"$ne": ""}},
			}})
		}
		match = append(match, filter)
		project := bson.M{
			"id":              "$_id",
			"_id":             0,
			"name":            1,
			"storehouseName":  "$storehouse.name",
			"status":          "$status",
			"package_number":  1,
			"spec":            1,
			"expiration_date": 1,
			"batch_number":    1,
		}

		mpipeline := mongo.Pipeline{
			{{Key: "$lookup", Value: bson.M{"from": "project_storehouse", "localField": "storehouse_id", "foreignField": "_id", "as": "project_storehouse"}}},
			{{Key: "$unwind", Value: "$project_storehouse"}},
			{{Key: "$lookup", Value: bson.M{"from": "storehouse", "localField": "project_storehouse.storehouse_id", "foreignField": "_id", "as": "storehouse"}}},
			{{Key: "$unwind", Value: "$storehouse"}},
			{{Key: "$match", Value: bson.M{"$and": match}}},
			{{Key: "$project", Value: project}},
		}

		if role.Scope == "depot" {
			filter["user_depot.user_id"] = user.ID
			mpipeline = mongo.Pipeline{
				{{Key: "$lookup", Value: bson.M{"from": "user_depot", "localField": "storehouse_id", "foreignField": "depot_id", "as": "user_depot"}}},
				{{Key: "$unwind", Value: "$user_depot"}},
				{{Key: "$lookup", Value: bson.M{"from": "project_storehouse", "localField": "user_depot.depot_id", "foreignField": "_id", "as": "project_storehouse"}}},
				{{Key: "$unwind", Value: "$project_storehouse"}},
				{{Key: "$lookup", Value: bson.M{"from": "storehouse", "localField": "project_storehouse.storehouse_id", "foreignField": "_id", "as": "storehouse"}}},
				{{Key: "$unwind", Value: "$storehouse"}},
				{{Key: "$match", Value: bson.M{"$and": match}}},
				{{Key: "$project", Value: project}},
			}
		}

		mall, err := tools.Database.Collection("medicine").Aggregate(nil, mpipeline)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = mall.All(nil, &medicine)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}

		//未编码药物统计
		//未编号
		otherPipepine := mongo.Pipeline{
			{{Key: "$lookup", Value: bson.M{"from": "project_storehouse", "localField": "storehouse_id", "foreignField": "_id", "as": "project_storehouse"}}},
			{{Key: "$unwind", Value: "$project_storehouse"}},
			{{Key: "$lookup", Value: bson.M{"from": "storehouse", "localField": "project_storehouse.storehouse_id", "foreignField": "_id", "as": "storehouse"}}},
			{{Key: "$unwind", Value: "$storehouse"}},
			{{Key: "$match", Value: bson.M{"$and": match}}},
			{{Key: "$project", Value: project}},
		}
		if role.Scope == "depot" {
			filter["user_depot.user_id"] = user.ID
			mpipeline = mongo.Pipeline{
				{{Key: "$lookup", Value: bson.M{"from": "user_depot", "localField": "storehouse_id", "foreignField": "depot_id", "as": "user_depot"}}},
				{{Key: "$unwind", Value: "$user_depot"}},
				{{Key: "$lookup", Value: bson.M{"from": "project_storehouse", "localField": "user_depot.depot_id", "foreignField": "_id", "as": "project_storehouse"}}},
				{{Key: "$unwind", Value: "$project_storehouse"}},
				{{Key: "$lookup", Value: bson.M{"from": "storehouse", "localField": "project_storehouse.storehouse_id", "foreignField": "_id", "as": "storehouse"}}},
				{{Key: "$unwind", Value: "$storehouse"}},
				{{Key: "$match", Value: bson.M{"$and": match}}},
				{{Key: "$project", Value: project}},
			}
		}
		otherMedicineAll, err := tools.Database.Collection("medicine_others").Aggregate(nil, otherPipepine)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = otherMedicineAll.All(nil, &otherMedicines)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}

		otherMedicine := make([]map[string]interface{}, 0)
		all, err := tools.Database.Collection("medicine_others").Aggregate(nil, mpipeline)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = all.All(nil, &otherMedicine)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
	}

	//包装
	type otherPackageNumber struct {
		ID            primitive.ObjectID `bson:"_id"`
		PackageNumber int                `bson:"package_number"`
		PackageCount  int                `bson:"package_count"`
	}

	otherPackageNumbers := make([]otherPackageNumber, 0)
	packageMatch := bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
	}
	packagePipepine := mongo.Pipeline{
		{{Key: "$match", Value: packageMatch}},
		{{
			Key: "$project",
			Value: bson.M{
				"_id":            1,
				"package_number": 1,
				"package_count":  1,
			},
		}},
	}
	otherPackageAll, err := tools.Database.Collection("other_package_number").Aggregate(nil, packagePipepine)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = otherPackageAll.All(nil, &otherPackageNumbers)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	otherPackageNumberMap := make(map[int]int, len(otherPackageNumbers))
	for _, number := range otherPackageNumbers {
		otherPackageNumberMap[number.PackageNumber] = number.PackageCount
	}
	bindDrugMap, err := tools.IsBlindDrugMap(envOID)
	//模板
	if templateId != "" {
		var template models.CustomTemplate
		templateOID, _ := primitive.ObjectIDFromHex(templateId)
		err := tools.Database.Collection("custom_template").FindOne(context.Background(), bson.M{
			"_id": templateOID,
		}).Decode(&template)
		if err != nil {
			return "", nil, err
		}

		// Sheet1 按照药物名称统计 Sheet2 按照库房药物名称统计
		for i := 1; i <= 2; i++ {
			medicineData := getDepotIPStatistics(i, template, medicine, otherMedicines, otherPackageNumberMap, envOID)

			title := []any{}
			title = append(title, locales.TrStash(ctx, "report.attributes.project.number"))
			title = append(title, locales.TrStash(ctx, "report.attributes.project.name"))
			if i == 2 {
				title = append(title, locales.TrStash(ctx, "user.depot"))
			}
			title = append(title, locales.TrStash(ctx, "medicine_name"))

			for _, field := range template.Fields {
				if field == "report.attributes.research.expireDate" {
					title = append(title, locales.TrStash(ctx, "medicine_download_expiredDate"))
				} else if field == "report.attributes.research.batch" {
					title = append(title, locales.TrStash(ctx, "medicine_download_batch"))
				} else if field == "report.attributes.research.spec" {
					title = append(title, locales.TrStash(ctx, "report.attributes.research.spec"))
				}
			}

			title = append(title, locales.TrStash(ctx, "medicine.status.available"))
			title = append(title, locales.TrStash(ctx, "medicine.status.toBeConfirmed"))
			title = append(title, locales.TrStash(ctx, "medicine.status.delivered"))
			title = append(title, locales.TrStash(ctx, "medicine.status.sending"))
			title = append(title, locales.TrStash(ctx, "medicine.status.quarantine"))
			title = append(title, locales.TrStash(ctx, "medicine.status.used"))
			title = append(title, locales.TrStash(ctx, "medicine.status.lose"))
			title = append(title, locales.TrStash(ctx, "medicine.status.expired"))
			title = append(title, locales.TrStash(ctx, "medicine.status.frozen"))
			title = append(title, locales.TrStash(ctx, "medicine.status.locked"))

			content := make([][]interface{}, len(medicineData))
			for j := 0; j < len(medicineData); j++ {
				item := medicineData[j]
				row := []any{project.ProjectInfo.Number}
				row = append(row, project.ProjectInfo.Name)
				if i == 2 {
					row = append(row, item["storehouseName"])
				}

				if isBlindedRole && bindDrugMap[item["name"].(string)] {
					item["name"] = tools.BlindData
				}
				row = append(row, item["name"])
				for _, field := range template.Fields {
					if field == "report.attributes.research.expireDate" {
						row = append(row, item["expiration_date"])
					} else if field == "report.attributes.research.batch" {
						row = append(row, item["batch_number"])
					} else if field == "report.attributes.research.spec" {
						row = append(row, item["spec"])
					}
				}
				row = append(row, item["count"])
				row = append(row, item["to_be_confirm_count"])
				row = append(row, item["to_be_send_count"])
				row = append(row, item["in_transit_count"])
				row = append(row, item["quarantined_count"])
				row = append(row, item["used_count"])
				row = append(row, item["lost_count"])
				row = append(row, item["expired_count"])
				row = append(row, item["frozen_count"])
				row = append(row, item["locked_count"])

				content[j] = row
			}
			//写入excel文件   Sheet1 按照药物名称统计 Sheet2 按照库房药物名称统计

			var streamWriter *excelize.StreamWriter
			if i == 1 {
				streamWriter, _ = file.NewStreamWriter("Sheet1")
			} else {
				file.NewSheet("Sheet2")
				streamWriter, _ = file.NewStreamWriter("Sheet2")
			}
			titleTr := multilingual.TrBatch(ctx, title)
			t := make([]interface{}, len(titleTr))
			for i, item := range titleTr {
				t[i] = excelize.Cell{Value: item}
			}
			_ = streamWriter.SetRow("A1", t)
			for i := 1; i <= len(content); i++ {
				r := make([]interface{}, len(content[i-1]))
				for j := 0; j < len(content[i-1]); j++ {
					r[j] = excelize.Cell{Value: content[i-1][j]}
				}
				cell, _ := excelize.CoordinatesToCellName(1, i+1)
				_ = streamWriter.SetRow(cell, r)
			}
			_ = streamWriter.Flush()
		}
	} else {
		// Sheet1 按照药物名称统计 Sheet2 按照库房药物名称统计
		for i := 1; i <= 2; i++ {
			medicineData := getDepotIPStatistics(i, models.CustomTemplate{}, medicine, otherMedicines, otherPackageNumberMap, envOID)

			title := []any{}
			title = append(title, locales.TrStash(ctx, "report.attributes.project.number"))
			title = append(title, locales.TrStash(ctx, "report.attributes.project.name"))
			if i == 2 {
				title = append(title, locales.TrStash(ctx, "user.depot"))
			}
			title = append(title, locales.TrStash(ctx, "medicine_name"))
			title = append(title, locales.TrStash(ctx, "medicine_download_expiredDate"))
			title = append(title, locales.TrStash(ctx, "medicine_download_batch"))
			title = append(title, locales.TrStash(ctx, "report.attributes.research.spec"))
			title = append(title, locales.TrStash(ctx, "medicine.status.available"))
			title = append(title, locales.TrStash(ctx, "medicine.status.toBeConfirmed"))
			title = append(title, locales.TrStash(ctx, "medicine.status.delivered"))
			title = append(title, locales.TrStash(ctx, "medicine.status.sending"))
			title = append(title, locales.TrStash(ctx, "medicine.status.quarantine"))
			title = append(title, locales.TrStash(ctx, "medicine.status.used"))
			title = append(title, locales.TrStash(ctx, "medicine.status.lose"))
			title = append(title, locales.TrStash(ctx, "medicine.status.expired"))
			title = append(title, locales.TrStash(ctx, "medicine.status.frozen"))
			title = append(title, locales.TrStash(ctx, "medicine.status.locked"))

			content := make([][]interface{}, len(medicineData))
			for j := 0; j < len(medicineData); j++ {
				item := medicineData[j]
				row := []any{project.ProjectInfo.Number}
				row = append(row, project.ProjectInfo.Name)
				if i == 2 {
					row = append(row, item["storehouseName"])
				}
				row = append(row, item["name"])
				row = append(row, item["expiration_date"])
				row = append(row, item["batch_number"])
				row = append(row, item["spec"])
				row = append(row, item["count"])
				row = append(row, item["to_be_confirm_count"])
				row = append(row, item["to_be_send_count"])
				row = append(row, item["in_transit_count"])
				row = append(row, item["quarantined_count"])
				row = append(row, item["used_count"])
				row = append(row, item["lost_count"])
				row = append(row, item["expired_count"])
				row = append(row, item["frozen_count"])
				row = append(row, item["locked_count"])

				content[j] = row
			}
			//写入excel文件   Sheet1 按照药物名称统计 Sheet2 按照库房药物名称统计

			var streamWriter *excelize.StreamWriter
			if i == 1 {
				streamWriter, _ = file.NewStreamWriter("Sheet1")
			} else {
				file.NewSheet("Sheet2")
				streamWriter, _ = file.NewStreamWriter("Sheet2")
			}
			titleTr := multilingual.TrBatch(ctx, title)
			t := make([]interface{}, len(titleTr))
			for i, item := range titleTr {
				t[i] = excelize.Cell{Value: item}
			}
			_ = streamWriter.SetRow("A1", t)
			for i := 1; i <= len(content); i++ {
				r := make([]interface{}, len(content[i-1]))
				for j := 0; j < len(content[i-1]); j++ {
					r[j] = excelize.Cell{Value: content[i-1][j]}
				}
				cell, _ := excelize.CoordinatesToCellName(1, i+1)
				_ = streamWriter.SetRow(cell, r)
			}
			_ = streamWriter.Flush()
		}
	}

	envCode := getEnvName(project, envOID)
	fileName := fmt.Sprintf("%s[%s]DepotIPStatisticsReport_%s.xlsx", project.Number, envCode, now.Format("20060102150405"))

	buffer, err := file.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	return fileName, buffer.Bytes(), nil
}

func getDepotIPStatistics(i int, template models.CustomTemplate, medicine []map[string]interface{}, otherMedicines []map[string]interface{}, otherPackageNumberMap map[int]int, envOID primitive.ObjectID) []map[string]interface{} {
	medicineData := make([]map[string]interface{}, 0)
	groupStorehouse := false
	groupExpirationDate := false
	groupBatchNumber := false
	if i == 2 {
		groupStorehouse = true
	}
	if !template.ID.IsZero() {
		for _, field := range template.Fields {
			if field == "report.attributes.research.expireDate" {
				groupExpirationDate = true
			} else if field == "report.attributes.research.batch" {
				groupBatchNumber = true
			}
		}
	} else {
		groupExpirationDate = true
		groupBatchNumber = true
	}
	//编号
	medicineMap := make(map[string]models.MedicineOtherPackageInfo, 0)
	medicineSpecMap := make(map[string]string, 0)
	for _, m := range medicine {
		name := m["name"].(string)
		spec := m["spec"].(string)
		expirationDate := m["expiration_date"].(string)
		batchNumber := m["batch_number"].(string)
		storehouseName := m["storehouseName"].(string)
		status := m["status"].(int32)
		key := fmt.Sprintf("%s_%s", name, spec)
		if groupStorehouse {
			key = fmt.Sprintf("%s_%s_%s", storehouseName, name, spec)
			if groupExpirationDate && !groupBatchNumber {
				key = fmt.Sprintf("%s_%s_%s_%s", storehouseName, name, expirationDate, spec)
			} else if groupBatchNumber && !groupExpirationDate {
				key = fmt.Sprintf("%s_%s_%s_%s", storehouseName, name, batchNumber, spec)
			} else if groupExpirationDate && groupBatchNumber {
				key = fmt.Sprintf("%s_%s_%s_%s_%s", storehouseName, name, expirationDate, batchNumber, spec)
			}
		} else {
			key = fmt.Sprintf("%s_%s", name, spec)
			if groupExpirationDate && !groupBatchNumber {
				key = fmt.Sprintf("%s_%s_%s", name, expirationDate, spec)
			} else if groupBatchNumber && !groupExpirationDate {
				key = fmt.Sprintf("%s_%s_%s", name, batchNumber, spec)
			} else if groupExpirationDate && groupBatchNumber {
				key = fmt.Sprintf("%s_%s_%s_%s", name, expirationDate, batchNumber, spec)
			}
		}
		mp, exist := medicineMap[key]
		medicineSpecMap[key] = name
		count := 0
		to_be_send_count := 0
		in_transit_count := 0
		quarantined_count := 0
		used_count := 0
		lost_count := 0
		expired_count := 0
		to_be_confirm_count := 0
		locked_count := 0
		frozen_count := 0
		switch status {
		case 1:
			count = 1
		case 2:
			to_be_send_count = 1
		case 3:
			in_transit_count = 1
		case 4:
			quarantined_count = 1
		case 5:
			used_count = 1
		case 6:
			lost_count = 1
		case 7:
			expired_count = 1
		case 11:
			to_be_confirm_count = 1
		case 20:
			locked_count = 1
		case 14:
			frozen_count = 1
		}

		if !exist {
			mp = models.MedicineOtherPackageInfo{}
		}
		mp.Count = mp.Count + count
		mp.ToBeSendCount = mp.ToBeSendCount + to_be_send_count
		mp.InTransitCount = mp.InTransitCount + in_transit_count
		mp.QuarantinedCount = mp.QuarantinedCount + quarantined_count
		mp.UsedCount = mp.UsedCount + used_count
		mp.LostCount = mp.LostCount + lost_count
		mp.ExpiredCount = mp.ExpiredCount + expired_count
		mp.ToBeConfirmCount = mp.ToBeConfirmCount + to_be_confirm_count
		mp.LockedCount = mp.LockedCount + locked_count
		mp.FrozenCount = mp.FrozenCount + frozen_count
		mp.Spec = spec
		mp.ExpireDate = expirationDate
		mp.Batch = batchNumber
		mp.StorehouseName = storehouseName
		medicineMap[key] = mp
	}
	for key, v := range medicineMap {
		data := map[string]interface{}{
			"name":                medicineSpecMap[key],
			"count":               v.Count,
			"to_be_send_count":    v.ToBeSendCount,
			"in_transit_count":    v.InTransitCount,
			"quarantined_count":   v.QuarantinedCount,
			"used_count":          v.UsedCount,
			"lost_count":          v.LostCount,
			"expired_count":       v.ExpiredCount,
			"to_be_confirm_count": v.ToBeConfirmCount,
			"locked_count":        v.LockedCount,
			"frozen_count":        v.FrozenCount,
			"spec":                v.Spec,
			"storehouseName":      v.StorehouseName,
			"expiration_date":     v.ExpireDate,
			"batch_number":        v.Batch,
		}
		medicineData = append(medicineData, data)
	}

	//未编号
	//包装

	otherMedicineMap := make(map[string]models.MedicineOtherPackageInfo, 0)
	packMap := slice.GroupWith(otherMedicines, func(m map[string]interface{}) string {
		name := m["name"].(string)
		spec := m["spec"].(string)
		expirationDate := m["expiration_date"].(string)
		batchNumber := m["batch_number"].(string)
		storehouseName := m["storehouseName"].(string)
		packageNumber := ""
		if m["package_number"] != nil {
			packageNumber = m["package_number"].(string)
		}
		status := m["status"].(int32)
		key := fmt.Sprintf("%s_%s_%s_%d", name, spec, packageNumber, status)
		if groupStorehouse {
			key = fmt.Sprintf("%s_%s_%s_%s_%d", storehouseName, name, spec, packageNumber, status)
			if groupExpirationDate && !groupBatchNumber {
				key = fmt.Sprintf("%s_%s_%s_%s_%s_%d", storehouseName, name, expirationDate, spec, packageNumber, status)
			} else if groupBatchNumber && !groupExpirationDate {
				key = fmt.Sprintf("%s_%s_%s_%s_%s_%d", storehouseName, name, batchNumber, spec, packageNumber, status)
			} else if groupExpirationDate && groupBatchNumber {
				key = fmt.Sprintf("%s_%s_%s_%s_%s_%s_%d", storehouseName, name, expirationDate, batchNumber, spec, packageNumber, status)
			}
		} else {
			key = fmt.Sprintf("%s_%s_%s_%d", name, spec, packageNumber, status)
			if groupExpirationDate && !groupBatchNumber {
				key = fmt.Sprintf("%s_%s_%s_%s_%d", name, expirationDate, spec, packageNumber, status)
			} else if groupBatchNumber && !groupExpirationDate {
				key = fmt.Sprintf("%s_%s_%s_%s_%d", name, batchNumber, spec, packageNumber, status)
			} else if groupExpirationDate && groupBatchNumber {
				key = fmt.Sprintf("%s_%s_%s_%s_%s_%d", name, expirationDate, batchNumber, spec, packageNumber, status)
			}
		}
		return key
	})

	for _, pm := range packMap {
		m := pm[0]
		name := m["name"].(string)
		spec := m["spec"].(string)
		expirationDate := m["expiration_date"].(string)
		batchNumber := m["batch_number"].(string)
		storehouseName := m["storehouseName"].(string)
		status := m["status"].(int32)
		packageNumber := ""
		if m["package_number"] != nil {
			packageNumber = m["package_number"].(string)
		}
		key := fmt.Sprintf("%s_%s", name, spec)
		if groupStorehouse {
			key = fmt.Sprintf("%s_%s_%s", storehouseName, name, spec)
			if groupExpirationDate && !groupBatchNumber {
				key = fmt.Sprintf("%s_%s_%s_%s", storehouseName, name, expirationDate, spec)
			} else if groupBatchNumber && !groupExpirationDate {
				key = fmt.Sprintf("%s_%s_%s_%s", storehouseName, name, batchNumber, spec)
			} else if groupExpirationDate && groupBatchNumber {
				key = fmt.Sprintf("%s_%s_%s_%s_%s", storehouseName, name, expirationDate, batchNumber, spec)
			}
		} else {
			key = fmt.Sprintf("%s_%s", name, spec)
			if groupExpirationDate && !groupBatchNumber {
				key = fmt.Sprintf("%s_%s_%s", name, expirationDate, spec)
			} else if groupBatchNumber && !groupExpirationDate {
				key = fmt.Sprintf("%s_%s_%s", name, batchNumber, spec)
			} else if groupExpirationDate && groupBatchNumber {
				key = fmt.Sprintf("%s_%s_%s_%s", name, expirationDate, batchNumber, spec)
			}
		}
		addPackageNumberCount := 0
		packageNumberCount := len(pm)
		if m["package_number"] != nil {
			pn, _ := strconv.ParseInt(m["package_number"].(string), 10, 64)
			allPackageNumberCount := otherPackageNumberMap[int(pn)]
			if packageNumberCount == allPackageNumberCount { //整包s
				addPackageNumberCount = 1
			}
		}
		mp, exist := otherMedicineMap[key]
		medicineSpecMap[key] = name
		count := 0
		package_count := 0
		to_be_send_count := 0
		to_be_send_package_count := 0
		in_transit_count := 0
		in_transit_package_count := 0
		quarantined_count := 0
		quarantined_package_count := 0
		used_count := 0
		used_package_count := 0
		lost_count := 0
		lost_package_count := 0
		expired_count := 0
		expired_package_count := 0
		to_be_confirm_count := 0
		to_be_confirm_package_count := 0
		locked_count := 0
		locked_package_count := 0
		frozen_count := 0
		frozen_package_count := 0
		packageNumberCountInt := packageNumberCount
		switch status {
		case 1:
			count = packageNumberCountInt
			package_count = addPackageNumberCount
		case 2:
			to_be_send_count = packageNumberCountInt
			to_be_send_package_count = addPackageNumberCount
		case 3:
			in_transit_count = packageNumberCountInt
			in_transit_package_count = addPackageNumberCount
		case 4:
			quarantined_count = packageNumberCountInt
			quarantined_package_count = addPackageNumberCount
		case 5:
			used_count = packageNumberCountInt
			used_package_count = addPackageNumberCount
		case 6:
			lost_count = packageNumberCountInt
			lost_package_count = addPackageNumberCount
		case 7:
			expired_count = packageNumberCountInt
			expired_package_count = addPackageNumberCount
		case 11:
			to_be_confirm_count = packageNumberCountInt
			to_be_confirm_package_count = addPackageNumberCount
		case 20:
			locked_count = packageNumberCountInt
			locked_package_count = addPackageNumberCount
		case 14:
			frozen_count = packageNumberCountInt
			frozen_package_count = addPackageNumberCount
		}

		if !exist {
			mp = models.MedicineOtherPackageInfo{}
		}
		mp.Count = mp.Count + count
		mp.ToBeSendCount = mp.ToBeSendCount + to_be_send_count
		mp.InTransitCount = mp.InTransitCount + in_transit_count
		mp.QuarantinedCount = mp.QuarantinedCount + quarantined_count
		mp.UsedCount = mp.UsedCount + used_count
		mp.LostCount = mp.LostCount + lost_count
		mp.ExpiredCount = mp.ExpiredCount + expired_count
		mp.ToBeConfirmCount = mp.ToBeConfirmCount + to_be_confirm_count
		mp.LockedCount = mp.LockedCount + locked_count
		mp.FrozenCount = mp.FrozenCount + frozen_count

		mp.PackageCount = mp.PackageCount + package_count
		mp.ToBeSendPackageCount = mp.ToBeSendPackageCount + to_be_send_package_count
		mp.InTransitPackageCount = mp.InTransitPackageCount + in_transit_package_count
		mp.QuarantinedPackageCount = mp.QuarantinedPackageCount + quarantined_package_count
		mp.UsedPackageCount = mp.UsedPackageCount + used_package_count
		mp.LostPackageCount = mp.LostPackageCount + lost_package_count
		mp.ExpiredPackageCount = mp.ExpiredPackageCount + expired_package_count
		mp.ToBeConfirmPackageCount = mp.ToBeConfirmPackageCount + to_be_confirm_package_count
		mp.LockedPackageCount = mp.LockedPackageCount + locked_package_count
		mp.FrozenPackageCount = mp.FrozenPackageCount + frozen_package_count
		mp.Spec = spec
		mp.ExpireDate = expirationDate
		mp.Batch = batchNumber
		mp.StorehouseName = storehouseName
		mp.PackageNumber = packageNumber
		otherMedicineMap[key] = mp
	}

	isOpenPackage, _, packageDrugNames, _, _, _ := tools.IsOpenPackage(envOID)
	for key, v := range otherMedicineMap {
		data := map[string]interface{}{
			"name":                medicineSpecMap[key],
			"count":               v.Count,
			"to_be_send_count":    v.ToBeSendCount,
			"in_transit_count":    v.InTransitCount,
			"quarantined_count":   v.QuarantinedCount,
			"used_count":          v.UsedCount,
			"lost_count":          v.LostCount,
			"expired_count":       v.ExpiredCount,
			"to_be_confirm_count": v.ToBeConfirmCount,
			"locked_count":        v.LockedCount,
			"frozen_count":        v.FrozenCount,
			"spec":                v.Spec,
			"storehouseName":      v.StorehouseName,
			"expiration_date":     v.ExpireDate,
			"batch_number":        v.Batch,
		}
		//判断研究产品是否是未编号包装的研究产品
		if isOpenPackage && packageDrugNames[medicineSpecMap[key]] > 0 {
			data = map[string]interface{}{
				"name":                medicineSpecMap[key],
				"count":               fmt.Sprintf("%v(%v)", v.Count, v.PackageCount),
				"to_be_send_count":    fmt.Sprintf("%v(%v)", v.ToBeSendCount, v.ToBeSendPackageCount),
				"in_transit_count":    fmt.Sprintf("%v(%v)", v.InTransitCount, v.InTransitPackageCount),
				"quarantined_count":   fmt.Sprintf("%v(%v)", v.QuarantinedCount, v.QuarantinedPackageCount),
				"used_count":          fmt.Sprintf("%v(%v)", v.UsedCount, v.UsedPackageCount),
				"lost_count":          fmt.Sprintf("%v(%v)", v.LostCount, v.LostPackageCount),
				"expired_count":       fmt.Sprintf("%v(%v)", v.ExpiredCount, v.ExpiredPackageCount),
				"to_be_confirm_count": fmt.Sprintf("%v(%v)", v.ToBeConfirmCount, v.ToBeConfirmPackageCount),
				"locked_count":        fmt.Sprintf("%v(%v)", v.LockedCount, v.LockedPackageCount),
				"frozen_count":        fmt.Sprintf("%v(%v)", v.FrozenCount, v.FrozenPackageCount),
				"spec":                v.Spec,
				"storehouseName":      v.StorehouseName,
				"expiration_date":     v.ExpireDate,
				"batch_number":        v.Batch,
			}
		}
		medicineData = append(medicineData, data)
	}
	return medicineData
}

func getSiteIPStatistics(template models.CustomTemplate, medicine []map[string]interface{}, otherMedicines []map[string]interface{}, otherPackageNumberMap map[int]int, envOID primitive.ObjectID) []map[string]interface{} {
	medicineData := make([]map[string]interface{}, 0)
	groupExpirationDate := false
	groupBatchNumber := false
	if !template.ID.IsZero() {
		for _, field := range template.Fields {
			if field == "report.attributes.research.expireDate" {
				groupExpirationDate = true
			} else if field == "report.attributes.research.batch" {
				groupBatchNumber = true
			}
		}
	} else {
		groupExpirationDate = true
		groupBatchNumber = true
	}
	//编号
	medicineMap := make(map[string]models.MedicineOtherPackageInfo, 0)
	medicineSpecMap := make(map[string]string, 0)
	for _, m := range medicine {
		name := m["name"].(string)
		spec := m["spec"].(string)
		expirationDate := m["expiration_date"].(string)
		batchNumber := m["batch_number"].(string)
		siteName := m["siteName"].(string)
		siteNumber := m["siteNumber"].(string)
		status := m["status"].(int32)
		key := fmt.Sprintf("%s_%s_%s", siteNumber, name, spec)
		if groupExpirationDate && !groupBatchNumber {
			key = fmt.Sprintf("%s_%s_%s_%s", siteNumber, name, expirationDate, spec)
		} else if groupBatchNumber && !groupExpirationDate {
			key = fmt.Sprintf("%s_%s_%s_%s", siteNumber, name, batchNumber, spec)
		} else if groupExpirationDate && groupBatchNumber {
			key = fmt.Sprintf("%s_%s_%s_%s_%s", siteNumber, name, expirationDate, batchNumber, spec)
		}
		mp, exist := medicineMap[key]
		medicineSpecMap[key] = name
		count := 0
		to_be_send_count := 0
		in_transit_count := 0
		quarantined_count := 0
		used_count := 0
		lost_count := 0
		expired_count := 0
		to_be_confirm_count := 0
		locked_count := 0
		frozen_count := 0
		switch status {
		case 1:
			count = 1
		case 2:
			to_be_send_count = 1
		case 3:
			in_transit_count = 1
		case 4:
			quarantined_count = 1
		case 5:
			used_count = 1
		case 6:
			lost_count = 1
		case 7:
			expired_count = 1
		case 11:
			to_be_confirm_count = 1
		case 20:
			locked_count = 1
		case 14:
			frozen_count = 1
		}

		if !exist {
			mp = models.MedicineOtherPackageInfo{}
		}
		mp.Count = mp.Count + count
		mp.ToBeSendCount = mp.ToBeSendCount + to_be_send_count
		mp.InTransitCount = mp.InTransitCount + in_transit_count
		mp.QuarantinedCount = mp.QuarantinedCount + quarantined_count
		mp.UsedCount = mp.UsedCount + used_count
		mp.LostCount = mp.LostCount + lost_count
		mp.ExpiredCount = mp.ExpiredCount + expired_count
		mp.ToBeConfirmCount = mp.ToBeConfirmCount + to_be_confirm_count
		mp.LockedCount = mp.LockedCount + locked_count
		mp.FrozenCount = mp.FrozenCount + frozen_count
		mp.Spec = spec
		mp.ExpireDate = expirationDate
		mp.Batch = batchNumber
		mp.SiteNumber = siteNumber
		mp.SiteName = siteName
		medicineMap[key] = mp
	}
	for key, v := range medicineMap {
		data := map[string]interface{}{
			"name":                medicineSpecMap[key],
			"count":               v.Count,
			"to_be_send_count":    v.ToBeSendCount,
			"in_transit_count":    v.InTransitCount,
			"quarantined_count":   v.QuarantinedCount,
			"used_count":          v.UsedCount,
			"lost_count":          v.LostCount,
			"expired_count":       v.ExpiredCount,
			"to_be_confirm_count": v.ToBeConfirmCount,
			"locked_count":        v.LockedCount,
			"frozen_count":        v.FrozenCount,
			"spec":                v.Spec,
			"storehouseName":      v.StorehouseName,
			"expiration_date":     v.ExpireDate,
			"batch_number":        v.Batch,
			"siteNumber":          v.SiteNumber,
			"siteName":            v.SiteName,
		}
		medicineData = append(medicineData, data)
	}

	//未编号
	//包装

	otherMedicineMap := make(map[string]models.MedicineOtherPackageInfo, 0)
	packMap := slice.GroupWith(otherMedicines, func(m map[string]interface{}) string {
		name := m["name"].(string)
		spec := m["spec"].(string)
		expirationDate := m["expiration_date"].(string)
		batchNumber := m["batch_number"].(string)
		packageNumber := m["package_number"].(string)
		siteNumber := m["siteNumber"].(string)
		status := m["status"].(int32)
		key := fmt.Sprintf("%s_%s_%s_%s_%d", siteNumber, name, spec, packageNumber, status)
		if groupExpirationDate && !groupBatchNumber {
			key = fmt.Sprintf("%s_%s_%s_%s_%s_%d", siteNumber, name, expirationDate, spec, packageNumber, status)
		} else if groupBatchNumber && !groupExpirationDate {
			key = fmt.Sprintf("%s_%s_%s_%s_%s_%d", siteNumber, name, batchNumber, spec, packageNumber, status)
		} else if groupExpirationDate && groupBatchNumber {
			key = fmt.Sprintf("%s_%s_%s_%s_%s_%s_%d", siteNumber, name, expirationDate, batchNumber, spec, packageNumber, status)
		}
		return key
	})

	for _, pm := range packMap {
		m := pm[0]
		name := m["name"].(string)
		spec := m["spec"].(string)
		expirationDate := m["expiration_date"].(string)
		batchNumber := m["batch_number"].(string)
		siteName := m["siteName"].(string)
		siteNumber := m["siteNumber"].(string)
		status := m["status"].(int32)
		packageNumber := m["package_number"].(string)
		key := fmt.Sprintf("%s_%s_%s", siteNumber, name, spec)
		if groupExpirationDate && !groupBatchNumber {
			key = fmt.Sprintf("%s_%s_%s_%s", siteNumber, name, expirationDate, spec)
		} else if groupBatchNumber && !groupExpirationDate {
			key = fmt.Sprintf("%s_%s_%s_%s", siteNumber, name, batchNumber, spec)
		} else if groupExpirationDate && groupBatchNumber {
			key = fmt.Sprintf("%s_%s_%s_%s_%s", siteNumber, name, expirationDate, batchNumber, spec)
		}
		addPackageNumberCount := 0
		packageNumberCount := len(pm)
		if m["package_number"] != nil {
			pn, _ := strconv.ParseInt(m["package_number"].(string), 10, 64)
			allPackageNumberCount := otherPackageNumberMap[int(pn)]
			if packageNumberCount == allPackageNumberCount { //整包s
				addPackageNumberCount = 1
			}
		}
		mp, exist := otherMedicineMap[key]
		medicineSpecMap[key] = name
		count := 0
		package_count := 0
		to_be_send_count := 0
		to_be_send_package_count := 0
		in_transit_count := 0
		in_transit_package_count := 0
		quarantined_count := 0
		quarantined_package_count := 0
		used_count := 0
		used_package_count := 0
		lost_count := 0
		lost_package_count := 0
		expired_count := 0
		expired_package_count := 0
		to_be_confirm_count := 0
		to_be_confirm_package_count := 0
		locked_count := 0
		locked_package_count := 0
		frozen_count := 0
		frozen_package_count := 0
		packageNumberCountInt := packageNumberCount
		switch status {
		case 1:
			count = packageNumberCountInt
			package_count = addPackageNumberCount
		case 2:
			to_be_send_count = packageNumberCountInt
			to_be_send_package_count = addPackageNumberCount
		case 3:
			in_transit_count = packageNumberCountInt
			in_transit_package_count = addPackageNumberCount
		case 4:
			quarantined_count = packageNumberCountInt
			quarantined_package_count = addPackageNumberCount
		case 5:
			used_count = packageNumberCountInt
			used_package_count = addPackageNumberCount
		case 6:
			lost_count = packageNumberCountInt
			lost_package_count = addPackageNumberCount
		case 7:
			expired_count = packageNumberCountInt
			expired_package_count = addPackageNumberCount
		case 11:
			to_be_confirm_count = packageNumberCountInt
			to_be_confirm_package_count = addPackageNumberCount
		case 20:
			locked_count = packageNumberCountInt
			locked_package_count = addPackageNumberCount
		case 14:
			frozen_count = packageNumberCountInt
			frozen_package_count = addPackageNumberCount
		}

		if !exist {
			mp = models.MedicineOtherPackageInfo{}
		}
		mp.Count = mp.Count + count
		mp.ToBeSendCount = mp.ToBeSendCount + to_be_send_count
		mp.InTransitCount = mp.InTransitCount + in_transit_count
		mp.QuarantinedCount = mp.QuarantinedCount + quarantined_count
		mp.UsedCount = mp.UsedCount + used_count
		mp.LostCount = mp.LostCount + lost_count
		mp.ExpiredCount = mp.ExpiredCount + expired_count
		mp.ToBeConfirmCount = mp.ToBeConfirmCount + to_be_confirm_count
		mp.LockedCount = mp.LockedCount + locked_count
		mp.FrozenCount = mp.FrozenCount + frozen_count

		mp.PackageCount = mp.PackageCount + package_count
		mp.ToBeSendPackageCount = mp.ToBeSendPackageCount + to_be_send_package_count
		mp.InTransitPackageCount = mp.InTransitPackageCount + in_transit_package_count
		mp.QuarantinedPackageCount = mp.QuarantinedPackageCount + quarantined_package_count
		mp.UsedPackageCount = mp.UsedPackageCount + used_package_count
		mp.LostPackageCount = mp.LostPackageCount + lost_package_count
		mp.ExpiredPackageCount = mp.ExpiredPackageCount + expired_package_count
		mp.ToBeConfirmPackageCount = mp.ToBeConfirmPackageCount + to_be_confirm_package_count
		mp.LockedPackageCount = mp.LockedPackageCount + locked_package_count
		mp.FrozenPackageCount = mp.FrozenPackageCount + frozen_package_count
		mp.Spec = spec
		mp.ExpireDate = expirationDate
		mp.Batch = batchNumber
		mp.SiteName = siteName
		mp.SiteNumber = siteNumber
		mp.PackageNumber = packageNumber
		otherMedicineMap[key] = mp
	}

	isOpenPackage, _, packageDrugNames, _, _, _ := tools.IsOpenPackage(envOID)
	for key, v := range otherMedicineMap {
		data := map[string]interface{}{
			"name":                medicineSpecMap[key],
			"count":               v.Count,
			"to_be_send_count":    v.ToBeSendCount,
			"in_transit_count":    v.InTransitCount,
			"quarantined_count":   v.QuarantinedCount,
			"used_count":          v.UsedCount,
			"lost_count":          v.LostCount,
			"expired_count":       v.ExpiredCount,
			"to_be_confirm_count": v.ToBeConfirmCount,
			"locked_count":        v.LockedCount,
			"frozen_count":        v.FrozenCount,
			"spec":                v.Spec,
			"storehouseName":      v.StorehouseName,
			"expiration_date":     v.ExpireDate,
			"batch_number":        v.Batch,
			"siteNumber":          v.SiteNumber,
			"siteName":            v.SiteName,
		}
		//判断研究产品是否是未编号包装的研究产品
		if isOpenPackage && packageDrugNames[medicineSpecMap[key]] > 0 {
			data = map[string]interface{}{
				"name":                medicineSpecMap[key],
				"count":               fmt.Sprintf("%v(%v)", v.Count, v.PackageCount),
				"to_be_send_count":    fmt.Sprintf("%v(%v)", v.ToBeSendCount, v.ToBeSendPackageCount),
				"in_transit_count":    fmt.Sprintf("%v(%v)", v.InTransitCount, v.InTransitPackageCount),
				"quarantined_count":   fmt.Sprintf("%v(%v)", v.QuarantinedCount, v.QuarantinedPackageCount),
				"used_count":          fmt.Sprintf("%v(%v)", v.UsedCount, v.UsedPackageCount),
				"lost_count":          fmt.Sprintf("%v(%v)", v.LostCount, v.LostPackageCount),
				"expired_count":       fmt.Sprintf("%v(%v)", v.ExpiredCount, v.ExpiredPackageCount),
				"to_be_confirm_count": fmt.Sprintf("%v(%v)", v.ToBeConfirmCount, v.ToBeConfirmPackageCount),
				"locked_count":        fmt.Sprintf("%v(%v)", v.LockedCount, v.LockedPackageCount),
				"frozen_count":        fmt.Sprintf("%v(%v)", v.FrozenCount, v.FrozenPackageCount),
				"spec":                v.Spec,
				"storehouseName":      v.StorehouseName,
				"expiration_date":     v.ExpireDate,
				"batch_number":        v.Batch,
				"siteNumber":          v.SiteNumber,
				"siteName":            v.SiteName,
			}
		}
		medicineData = append(medicineData, data)
	}
	return medicineData
}

func ExportSourceIPExport(ctx *gin.Context, projectID string, envID string, cohortID string, roleId string, templateId string, now time.Time) (string, []byte, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)

	var attribute models.Attribute
	match := bson.M{
		"env_id": envOID,
	}

	// 查询编码配置
	var barcodeRule models.BarcodeRule
	barcodeRuleFilter := bson.M{"project_id": projectOID, "env_id": envOID}

	if cohortID != "" {
		cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
		match = bson.M{
			"cohort_id": cohortOID,
		}
		barcodeRuleFilter["cohort_id"] = cohortOID
	}

	_ = tools.Database.Collection("attribute").FindOne(nil, match).Decode(&attribute)

	_ = tools.Database.Collection("barcode_rule").FindOne(nil, barcodeRuleFilter).Decode(&barcodeRule)

	userSites, err := new(ProjectSiteService).UserSites(ctx, "", "", envID, roleId)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	filter := bson.M{"env_id": envOID}
	var medicineData []struct {
		models.Medicine         `bson:",inline"`
		Site                    []models.ProjectSite        `bson:"site"`
		Subject                 []models.Info               `bson:"subject"`
		DispensingMedicines     []models.DispensingMedicine `bson:"dispensing_medicines"`
		RealDispensingMedicines []models.DispensingMedicine `bson:"real_dispensing_medicines"`
	}

	m := make(bson.M)
	for k, v := range models.MedicineProjectM {
		m[k] = v
	}
	m["site"] = 1
	m["subject"] = bson.M{"$ifNull": bson.A{"$subject.info", "$real_dispensing_subject.info"}}
	m["dispensing_medicines"] = "$dispensing_medicines.dispensing_medicines"
	m["real_dispensing_medicines"] = "$real_dispensing.real_dispensing_medicines"

	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "project_site",
			"localField":   "site_id",
			"foreignField": "_id",
			"as":           "site",
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "dispensing",
			"localField":   "_id",
			"foreignField": "dispensing_medicines.medicine_id",
			"as":           "dispensing_medicines",
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "dispensing",
			"localField":   "_id",
			"foreignField": "real_dispensing_medicines.medicine_id",
			"as":           "real_dispensing",
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "subject",
			"localField":   "dispensing_medicines.subject_id",
			"foreignField": "_id",
			"as":           "subject",
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "subject",
			"localField":   "real_dispensing.subject_id",
			"foreignField": "_id",
			"as":           "real_dispensing_subject",
		}}},
		{{Key: "$unwind", Value: bson.M{"path": "$dispensing_medicines", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$unwind", Value: bson.M{"path": "$real_dispensing", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$unwind", Value: bson.M{"path": "$subject", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$unwind", Value: bson.M{"path": "$real_dispensing_subject", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$project", Value: m}},
		{{Key: "$sort", Value: bson.D{{"serial_number", 1}}}},
	}
	optTrue := true
	cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pipepine, &options.AggregateOptions{AllowDiskUse: &optTrue})
	if err != nil {
		return "", nil, err
	}
	err = cursor.All(nil, &medicineData)
	if err != nil {
		return "", nil, err
	}
	//TODO 盲态角色不给下载
	isBlindedRole := false
	err = slice.SortByField(medicineData, "SerialNumber")
	isBlindedRole, err = tools.IsBlindedRole(roleId)
	if err != nil {
		return "", nil, err
	}

	if isBlindedRole {
		return "", nil, err
	}
	subjectReplaceText := ""
	if locales.Lang(ctx) == "zh" {
		if len(attribute.AttributeInfo.SubjectReplaceText) != 0 {
			subjectReplaceText = attribute.AttributeInfo.SubjectReplaceText
		} else {
			if len(attribute.AttributeInfo.SubjectReplaceTextEn) != 0 {
				subjectReplaceText = attribute.AttributeInfo.SubjectReplaceTextEn
			}
		}
	} else if locales.Lang(ctx) == "en" {
		if len(attribute.AttributeInfo.SubjectReplaceTextEn) != 0 {
			subjectReplaceText = attribute.AttributeInfo.SubjectReplaceTextEn
		} else {
			if len(attribute.AttributeInfo.SubjectReplaceText) != 0 {
				subjectReplaceText = attribute.AttributeInfo.SubjectReplaceText
			}
		}
	}

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{
		"_id": projectOID,
	}).Decode(&project)
	if err != nil {
		return "", nil, err
	}

	isBlindDrugMap, err := tools.IsBlindDrugMap(envOID)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	//写入excel文件
	var title []interface{}
	content := make([][]interface{}, len(medicineData))
	if templateId != "" {
		templateOID, _ := primitive.ObjectIDFromHex(templateId)
		var template models.CustomTemplate
		err := tools.Database.Collection("custom_template").FindOne(context.Background(), bson.M{
			"_id": templateOID,
		}).Decode(&template)
		if err != nil {
			return "", nil, err
		}

		title = append(title, locales.TrStash(ctx, "report.attributes.project.number"))
		title = append(title, locales.TrStash(ctx, "report.attributes.project.name"))
		if project.Type == 2 {
			title = append(title, locales.TrStash(ctx, "report.attributes.random.cohort"))
		} else if project.Type == 3 {
			title = append(title, locales.TrStash(ctx, "report.attributes.random.stage"))
		}

		for _, field := range template.Fields {
			if field == "report.attributes.research.medicine.name" {
				title = append(title, locales.TrStash(ctx, "export.medicine.ip_name"))
			} else if field == "report.attributes.research.medicine.serial-number" {
				title = append(title, locales.TrStash(ctx, "medicine_serial_number"))
			} else if field == "report.attributes.research.medicine.number" {
				title = append(title, locales.TrStash(ctx, "export.medicine.ip_number"))
			} else if field == "report.attributes.research.expireDate" {
				title = append(title, locales.TrStash(ctx, "export.medicine.expiration_date"))
			} else if field == "report.attributes.research.batch" {
				title = append(title, locales.TrStash(ctx, "export.medicine.batch_number"))
			} else if field == "report.attributes.research.packageNumber" {
				title = append(title, locales.TrStash(ctx, "export.medicine.package_number"))
			} else if field == "report.attributes.research.package.serialNumber" {
				title = append(title, locales.TrStash(ctx, "export.medicine.package_number_serialNumber"))
			} else if field == "report.attributes.research.status" {
				title = append(title, locales.TrStash(ctx, "medicine_status"))
			} else if field == "report.attributes.info.subject.number" {
				if subjectReplaceText != "" {
					title = append(title, subjectReplaceText)
				} else {
					title = append(title, locales.TrStash(ctx, "export.subject.number"))
				}
			} else if field == "report.attributes.info.site.number" {
				title = append(title, locales.TrStash(ctx, "export.dispensing.siteNumber"))
			} else if field == "report.attributes.info.site.name" {
				title = append(title, locales.TrStash(ctx, "export.dispensing.siteName"))
			} else if field == "report.attributes.dispensing.time" {
				title = append(title, locales.TrStash(ctx, "export.dispensing.operate_time"))
			} else if field == "report.attributes.dispensing.medicine.is.replace" {
				title = append(title, locales.TrStash(ctx, "export.dispensing.is_replace"))
			} else if field == "report.attributes.dispensing.medicine.is.real" {
				title = append(title, locales.TrStash(ctx, "export.dispensing.is_real"))
			}
		}
		for i := 0; i < len(medicineData); i++ {
			var row []interface{}
			row = append(row, project.ProjectInfo.Number)
			row = append(row, project.ProjectInfo.Name)
			// 发药时间
			//timeZone := fmt.Sprintf("UTC%+d", project.TimeZone)
			//intTimeZone := project.TimeZone
			timeZone, _ := tools.GetProjectLocationUtc(project.ProjectInfo.TimeZoneStr, project.ProjectInfo.Tz, "")
			intTimeZone, _ := tools.GetProjectLocation(project.ProjectInfo.TimeZoneStr, project.ProjectInfo.Tz, "")
			if len(medicineData[i].Site) > 0 && medicineData[i].Site[0].Tz != "" {
				//timeZone = medicineData[i].Site[0].TimeZone
				//intTimeZone, _ = strconv.Atoi(strings.Replace(medicineData[i].Site[0].TimeZone, "UTC", "", 1))

				timeZoneStr, e := tools.GetUTCOffsetString(medicineData[i].Site[0].Tz)
				if e != nil {
					return "", nil, e
				}
				timeZone = timeZoneStr

				//offsetStr, e := tools.GetLocationTimeZone(medicineData[i].Site[0].Tz)
				offsetStr, e := tools.GetProjectLocation("", medicineData[i].Site[0].Tz, "")
				if e != nil {
					return "", nil, e
				}
				intTimeZone = offsetStr

			}
			dispensingTime := ""
			hours := time.Duration(intTimeZone)
			minutes := time.Duration((intTimeZone - float64(hours)) * 60)
			duration := hours*time.Hour + minutes*time.Minute
			for _, medicine := range medicineData[i].DispensingMedicines {
				if medicine.Number == medicine.Number {
					dispensingTime = time.Unix(int64(medicine.Time), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
					dispensingTime = dispensingTime + "(" + timeZone + ")"
				}
			}
			for _, medicine := range medicineData[i].RealDispensingMedicines {
				if medicine.Number == medicine.Number {
					dispensingTime = time.Unix(int64(medicine.Time), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
					dispensingTime = dispensingTime + "(" + timeZone + ")"
				}
			}
			if project.Type == 2 || project.Type == 3 {
				var val string
				for _, env := range project.Environments {
					if env.ID == envOID {
						var names []string
						for _, cohort := range env.Cohorts {
							names = append(names, models.GetCohortReRandomName(cohort))
						}
						val = strings.Join(names, ",")
						break
					}
				}
				row = append(row, val)
			}

			//只显示用户绑定的中心受试者
			showSiteInfo := false
			if len(medicineData[i].Site) > 0 {
				for _, site := range userSites {
					if site["id"].(primitive.ObjectID) == medicineData[i].Site[0].ID {
						showSiteInfo = true
						break
					}
				}
			}

			for _, field := range template.Fields {

				if field == "report.attributes.research.medicine.name" {
					//isBlindedDrug, _ := tools.IsBlindedDrug(envOID, medicineData[i].Name)
					if isBlindedRole && isBlindDrugMap[medicineData[i].Name] {
						row = append(row, tools.BlindData)
					} else {
						row = append(row, medicineData[i].Name)
					}
				} else if field == "report.attributes.research.medicine.serial-number" {
					row = append(row, medicineData[i].SerialNumber)
				} else if field == "report.attributes.research.medicine.number" {
					row = append(row, medicineData[i].Number)
				} else if field == "report.attributes.research.expireDate" {
					row = append(row, medicineData[i].ExpirationDate)
				} else if field == "report.attributes.research.batch" {
					row = append(row, medicineData[i].BatchNumber)
				} else if field == "report.attributes.research.packageNumber" {
					row = append(row, medicineData[i].PackageNumber)
				} else if field == "report.attributes.research.package.serialNumber" {
					row = append(row, medicineData[i].PackageSerialNumber)
				} else if field == "report.attributes.research.status" {
					row = append(row, data.GetMedicineStatus(ctx, medicineData[i].Status))
				} else if field == "report.attributes.info.subject.number" {
					if len(medicineData[i].Subject) > 0 && showSiteInfo {
						for _, info := range medicineData[i].Subject {
							if info.Name == "shortname" {
								row = append(row, info.Value)
								break
							}
						}
					} else {
						row = append(row, "")
					}
				} else if field == "report.attributes.info.site.number" {
					if len(medicineData[i].Site) > 0 && showSiteInfo {
						row = append(row, medicineData[i].Site[0].Number)
					} else {
						row = append(row, "")
					}
				} else if field == "report.attributes.info.site.name" {
					if len(medicineData[i].Site) > 0 && showSiteInfo {
						row = append(row, models.GetProjectSiteName(ctx, medicineData[i].Site[0]))
					} else {
						row = append(row, "")
					}
				} else if field == "report.attributes.dispensing.time" {
					//TODO 发药操作时间
					row = append(row, dispensingTime)
				} else if field == "report.attributes.dispensing.medicine.is.replace" {
					if len(medicineData[i].DispensingMedicines) > 0 {
						flag := false
						for _, medicine := range medicineData[i].DispensingMedicines {
							if medicine.MedicineID == medicineData[i].ID && medicine.Type == 3 {
								flag = true
								break
							}
						}
						if flag {
							row = append(row, locales.TrStash(ctx, "common.yes"))
						} else {
							row = append(row, locales.TrStash(ctx, "common.no"))
						}
					} else {
						row = append(row, locales.TrStash(ctx, "common.no"))
					}
				} else if field == "report.attributes.dispensing.medicine.is.real" {
					if len(medicineData[i].RealDispensingMedicines) > 0 {
						flag := false
						for _, medicine := range medicineData[i].RealDispensingMedicines {
							if medicine.MedicineID == medicineData[i].ID {
								flag = true
								break
							}
						}
						if flag {
							row = append(row, locales.TrStash(ctx, "common.yes"))
						} else {
							row = append(row, locales.TrStash(ctx, "common.no"))
						}
					} else {
						row = append(row, locales.TrStash(ctx, "common.no"))
					}
				}
			}

			content[i] = row
		}
	} else {
		title = append(title, locales.TrStash(ctx, "report.attributes.project.number"))
		title = append(title, locales.TrStash(ctx, "report.attributes.project.name"))
		if barcodeRule.CodeRule == 0 { // 手动上传才下载序列号
			title = append(title, locales.TrStash(ctx, "export.medicine.serial_number"))
		}
		title = append(title, locales.TrStash(ctx, "export.medicine.ip_name"))
		title = append(title, locales.TrStash(ctx, "export.medicine.ip_number"))
		title = append(title, locales.TrStash(ctx, "export.medicine.expiration_date"))
		title = append(title, locales.TrStash(ctx, "export.medicine.batch_number"))
		title = append(title, locales.TrStash(ctx, "medicine_status"))
		if attribute.AttributeInfo.SubjectReplaceText != "" {
			title = append(title, attribute.AttributeInfo.SubjectReplaceText)
		} else {
			title = append(title, locales.TrStash(ctx, "export.subject.number"))
		}
		title = append(title, locales.TrStash(ctx, "export.dispensing.siteNumber"))
		title = append(title, locales.TrStash(ctx, "export.dispensing.siteName"))
		title = append(title, locales.TrStash(ctx, "export.dispensing.operate_time"))
		title = append(title, locales.TrStash(ctx, "export.dispensing.is_replace"))
		title = append(title, locales.TrStash(ctx, "export.dispensing.is_real"))
		if project.Type == 2 {
			title = append(title, locales.TrStash(ctx, "report.attributes.random.cohort"))
		} else if project.Type == 3 {
			title = append(title, locales.TrStash(ctx, "report.attributes.random.stage"))
		}
		for i := 0; i < len(medicineData); i++ {
			//只显示用户绑定的中心受试者
			showSiteInfo := false
			if len(medicineData[i].Site) > 0 {
				for _, site := range userSites {
					if site["id"].(primitive.ObjectID) == medicineData[i].Site[0].ID {
						showSiteInfo = true
						break
					}
				}
			}

			var row []interface{}
			row = append(row, project.ProjectInfo.Number)
			row = append(row, project.ProjectInfo.Name)

			if barcodeRule.CodeRule == 0 { // 手动上传才下载序列号
				row = append(row, medicineData[i].SerialNumber)
			}
			//isBlindedDrug, _ := tools.IsBlindedDrug(envOID, medicineData[i].Name)
			if isBlindedRole && isBlindDrugMap[medicineData[i].Name] {
				row = append(row, tools.BlindData)
			} else {
				row = append(row, medicineData[i].Name)
			}
			row = append(row, medicineData[i].Number)
			row = append(row, medicineData[i].ExpirationDate)
			row = append(row, medicineData[i].BatchNumber)
			row = append(row, data.GetMedicineStatus(ctx, medicineData[i].Status))

			if len(medicineData[i].Subject) > 0 && showSiteInfo {
				for _, info := range medicineData[i].Subject {
					if info.Name == "shortname" {
						row = append(row, info.Value)
						break
					}
				}
			} else {
				row = append(row, "")
			}
			if len(medicineData[i].Site) > 0 && showSiteInfo {
				row = append(row, medicineData[i].Site[0].Number)
				row = append(row, models.GetProjectSiteName(ctx, medicineData[i].Site[0]))
			} else {
				row = append(row, "", "")
			}

			//TODO 发药操作时间
			//timeZone := fmt.Sprintf("UTC%+d", project.TimeZone)
			//intTimeZone := project.TimeZone
			timeZone, _ := tools.GetProjectLocationUtc(project.ProjectInfo.TimeZoneStr, project.ProjectInfo.Tz, "")
			intTimeZone, _ := tools.GetProjectLocation(project.ProjectInfo.TimeZoneStr, project.ProjectInfo.Tz, "")
			if len(medicineData[i].Site) > 0 && medicineData[i].Site[0].Tz != "" {
				//timeZone = medicineData[i].Site[0].TimeZone
				//intTimeZone, _ = strconv.Atoi(strings.Replace(medicineData[i].Site[0].TimeZone, "UTC", "", 1))

				timeZoneStr, e := tools.GetUTCOffsetString(medicineData[i].Site[0].Tz)
				if e != nil {
					return "", nil, e
				}
				timeZone = timeZoneStr

				//offsetStr, e := tools.GetLocationTimeZone(medicineData[i].Site[0].Tz)
				offsetStr, e := tools.GetProjectLocation("", medicineData[i].Site[0].Tz, "")
				if e != nil {
					return "", nil, e
				}
				intTimeZone = offsetStr

			}
			hour := time.Duration(intTimeZone)
			minute := time.Duration((intTimeZone - float64(hour)) * 60)
			duration := hour*time.Hour + minute*time.Minute
			dispensingTime := ""
			for _, item := range medicineData[i].DispensingMedicines {
				if medicineData[i].Number == item.Number {
					dispensingTime = time.Unix(int64(item.Time), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
					dispensingTime = dispensingTime + "(" + timeZone + ")"
				}
			}
			for _, item := range medicineData[i].RealDispensingMedicines {
				if medicineData[i].Number == item.Number {
					dispensingTime = time.Unix(int64(item.Time), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
					dispensingTime = dispensingTime + "(" + timeZone + ")"
				}
			}

			row = append(row, dispensingTime)

			if len(medicineData[i].DispensingMedicines) > 0 {
				flag := false
				for _, medicine := range medicineData[i].DispensingMedicines {
					if medicine.MedicineID == medicineData[i].ID && medicine.Type == 3 {
						flag = true
						break
					}
				}
				if flag {
					row = append(row, locales.TrStash(ctx, "common.yes"))
				} else {
					row = append(row, locales.TrStash(ctx, "common.no"))
				}
			} else {
				row = append(row, locales.TrStash(ctx, "common.no"))
			}
			if len(medicineData[i].RealDispensingMedicines) > 0 {
				row = append(row, locales.TrStash(ctx, "common.yes"))
			} else {
				row = append(row, locales.TrStash(ctx, "common.no"))
			}
			if project.Type == 2 || project.Type == 3 {
				var val string
				for _, env := range project.Environments {
					if env.ID == envOID {
						var names []string
						for _, cohort := range env.Cohorts {
							names = append(names, cohort.Name)
						}
						val = strings.Join(names, ",")
						break
					}
				}
				row = append(row, val)
			}
			content[i] = row
		}
	}

	envCode := getEnvName(project, envOID)
	fileName := fmt.Sprintf("%s[%s]SourceIP_%s.xlsx", project.Number, envCode, now.Format("20060102150405"))
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	tools.ExportSheet(f, "Sheet1", multilingual.TrBatch(ctx, title), multilingual.TrDoubleBatch(ctx, content))
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, err
	}

	return fileName, buffer.Bytes(), nil
}

func ExportDepotItemReport(ctx *gin.Context, projectID string, envID string, cohortId string, roleID string, templateId string, storehouseIds []string, now time.Time) (string, []byte, error) {
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	cohortID, _ := primitive.ObjectIDFromHex(cohortId)

	role, err := tools.GetRole(roleID)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	var medicineData []map[string]interface{}
	type otherMedicineData struct {
		ID             primitive.ObjectID `bson:"_id"`
		StorehouseID   primitive.ObjectID `bson:"storehouse_id"`
		StorehouseName string             `bson:"storehouse_name"`
		Number         string             `bson:"number"`
		Name           string             `bson:"name"`
		BatchNumber    string             `bson:"batch_number"`
		Spec           string             `bson:"spec"`
		ExpirationDate string             `bson:"expiration_date"`
		PackageNumber  string             `bson:"package_number"`
		Status         int32              `bson:"status"`
	}
	otherMedicineDatas := make([]otherMedicineData, 0)
	var orderNumber = ""
	medicinesOrder, err := new(MedicineService).GetMedicineOrder(primitive.NilObjectID, projectOID, envOID, orderNumber)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	if role.Scope != "site" {
		var storehouseOIDs []primitive.ObjectID
		for _, id := range storehouseIds {
			oid, _ := primitive.ObjectIDFromHex(id)
			storehouseOIDs = append(storehouseOIDs, oid)
		}

		match := bson.A{}
		filter := bson.M{"project_storehouse.deleted": 2, "env_id": envOID}

		if len(storehouseOIDs) > 0 {
			filter["storehouse_id"] = bson.M{"$in": storehouseOIDs}

		} else {
			match = append(match, bson.M{"$and": bson.A{
				bson.M{"storehouse_id": bson.M{"$ne": primitive.NilObjectID}},
				bson.M{"storehouse_id": bson.M{"$ne": nil}},
			}})
		}
		match = append(match, filter)

		optTrue := true
		opt := &options.AggregateOptions{
			AllowDiskUse: &optTrue,
		}
		pagePipeline := mongo.Pipeline{
			{{Key: "$lookup", Value: bson.M{"from": "project_storehouse", "localField": "storehouse_id", "foreignField": "_id", "as": "project_storehouse"}}},
			{{Key: "$unwind", Value: "$project_storehouse"}},
			{{Key: "$lookup", Value: bson.M{"from": "storehouse", "localField": "project_storehouse.storehouse_id", "foreignField": "_id", "as": "storehouse"}}},
			{{Key: "$match", Value: bson.M{"$and": match}}},
			{{Key: "$sort", Value: bson.D{{"number", 1}}}},
			{{Key: "$unwind", Value: "$storehouse"}},
			{{
				Key: "$project",
				Value: bson.M{
					"id":             "$_id",
					"_id":            0,
					"storehouseName": "$storehouse.name",
					"number":         1,
					"name":           1,
					"batchNumber":    "$batch_number",
					"spec":           "$spec",
					"expirationDate": "$expiration_date",
					"packageNumber":  "$package_number",
					"status":         1,
				},
			}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "history",
				"localField":   "id",
				"foreignField": "oid",
				"as":           "history",
			}}},
		}

		otherPipeline := mongo.Pipeline{
			{{Key: "$lookup", Value: bson.M{"from": "project_storehouse", "localField": "storehouse_id", "foreignField": "_id", "as": "project_storehouse"}}},
			{{Key: "$unwind", Value: "$project_storehouse"}},
			{{Key: "$lookup", Value: bson.M{"from": "storehouse", "localField": "project_storehouse.storehouse_id", "foreignField": "_id", "as": "storehouse"}}},
			{{Key: "$match", Value: bson.M{"$and": match}}},
			{{Key: "$sort", Value: bson.D{{"number", 1}}}},
			{{Key: "$unwind", Value: "$storehouse"}},
			{{
				Key: "$project",
				Value: bson.M{
					"_id":             1,
					"storehouse_name": "$storehouse.name",
					"storehouse_id":   "$project_storehouse._id",
					"number":          1,
					"name":            1,
					"batch_number":    1,
					"spec":            1,
					"expiration_date": 1,
					"package_number":  1,
					"status":          1,
				},
			}},
		}

		if role.Scope == "depot" {
			user, _ := tools.Me(ctx)
			filter["user_depot.user_id"] = user.ID

			pagePipeline = mongo.Pipeline{
				{{Key: "$lookup", Value: bson.M{"from": "user_depot", "localField": "storehouse_id", "foreignField": "depot_id", "as": "user_depot"}}},
				{{Key: "$unwind", Value: "$user_depot"}},
				{{Key: "$lookup", Value: bson.M{"from": "project_storehouse", "localField": "user_depot.depot_id", "foreignField": "_id", "as": "project_storehouse"}}},
				{{Key: "$unwind", Value: "$project_storehouse"}},
				{{Key: "$lookup", Value: bson.M{"from": "storehouse", "localField": "project_storehouse.storehouse_id", "foreignField": "_id", "as": "storehouse"}}},
				{{Key: "$match", Value: bson.M{"$and": match}}},
				{{Key: "$sort", Value: bson.D{{"number", 1}}}},
				{{Key: "$unwind", Value: "$storehouse"}},
				{{
					Key: "$project",
					Value: bson.M{
						"id":             "$_id",
						"_id":            0,
						"storehouseName": "$storehouse.name",
						"number":         1,
						"name":           1,
						"batchNumber":    "$batch_number",
						"spec":           "$spec",
						"expirationDate": "$expiration_date",
						"packageNumber":  "$package_number",
						"status":         1,
					},
				}},
				{{Key: "$lookup", Value: bson.M{
					"from":         "history",
					"localField":   "id",
					"foreignField": "oid",
					"as":           "history",
				}}},
			}

			otherPipeline = mongo.Pipeline{
				{{Key: "$lookup", Value: bson.M{"from": "user_depot", "localField": "storehouse_id", "foreignField": "depot_id", "as": "user_depot"}}},
				{{Key: "$unwind", Value: "$user_depot"}},
				{{Key: "$lookup", Value: bson.M{"from": "project_storehouse", "localField": "storehouse_id", "foreignField": "_id", "as": "project_storehouse"}}},
				{{Key: "$unwind", Value: "$project_storehouse"}},
				{{Key: "$lookup", Value: bson.M{"from": "storehouse", "localField": "project_storehouse.storehouse_id", "foreignField": "_id", "as": "storehouse"}}},
				{{Key: "$match", Value: bson.M{"$and": match}}},
				{{Key: "$sort", Value: bson.D{{"number", 1}}}},
				{{Key: "$unwind", Value: "$storehouse"}},
				{{
					Key: "$project",
					Value: bson.M{
						"_id":             1,
						"storehouse_name": "$storehouse.name",
						"storehouse_id":   "$project_storehouse._id",
						"number":          1,
						"name":            1,
						"batch_number":    1,
						"spec":            1,
						"expiration_date": 1,
						"package_number":  1,
						"status":          1,
					},
				}},
			}
		}

		cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pagePipeline, opt)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &medicineData)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}

		otherCursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, otherPipeline, opt)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = otherCursor.All(nil, &otherMedicineDatas)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
	}
	//处理未编号药物
	otherDatas := make([]map[string]interface{}, 0)
	if len(otherMedicineDatas) > 0 {
		sIds := slice.Map(otherMedicineDatas, func(index int, item otherMedicineData) primitive.ObjectID {
			return item.StorehouseID
		})
		otherkeys := make([]models.MedicineOtherKey, 0)
		otherKeyMatch := bson.M{"env_id": envOID, "storehouse_id": bson.M{"$in": sIds}}
		cursor, err := tools.Database.Collection("medicine_other_key").Find(nil, otherKeyMatch)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &otherkeys)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		keyMap := slice.GroupWith(otherkeys, func(t models.MedicineOtherKey) string {
			key := fmt.Sprintf("%s_%s_%s_%s", t.StorehouseID.Hex(), t.Name, t.ExpireDate, t.Batch)
			return key
		})
		oids := make([]primitive.ObjectID, 0)
		for _, v := range keyMap {
			otherKey := v[0]
			_, b := slice.Find(otherMedicineDatas, func(index int, item otherMedicineData) bool {
				return item.StorehouseID == otherKey.StorehouseID && item.Name == otherKey.Name && item.BatchNumber == otherKey.Batch && item.ExpirationDate == otherKey.ExpireDate
			})
			if b {
				oids = append(oids, otherKey.ID)
			}

		}
		//包装
		type otherPackageNumber struct {
			ID            primitive.ObjectID `bson:"_id"`
			PackageNumber int                `bson:"package_number"`
			PackageCount  int                `bson:"package_count"`
		}

		otherPackageNumbers := make([]otherPackageNumber, 0)
		packageMatch := bson.M{
			"project_id": projectOID,
			"env_id":     envOID,
		}
		packagePipepine := mongo.Pipeline{
			{{Key: "$match", Value: packageMatch}},
			{{
				Key: "$project",
				Value: bson.M{
					"_id":            1,
					"package_number": 1,
					"package_count":  1,
				},
			}},
		}
		otherPackageAll, err := tools.Database.Collection("other_package_number").Aggregate(nil, packagePipepine)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = otherPackageAll.All(nil, &otherPackageNumbers)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		otherPackageNumberMap := make(map[int]int, len(otherPackageNumbers))
		for _, number := range otherPackageNumbers {
			otherPackageNumberMap[number.PackageNumber] = number.PackageCount
		}

		histories := make([]map[string]interface{}, 0)
		cursor, err = tools.Database.Collection("history").Find(nil, bson.M{"oid": bson.M{"$in": oids}})
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &histories)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}

		statusMap := slice.GroupWith(otherMedicineDatas, func(t otherMedicineData) string {
			key := fmt.Sprintf("%s+%s+%s+%s+%d", t.StorehouseName, t.Name, t.BatchNumber, t.ExpirationDate, t.Status)
			return key
		})
		keys := maputil.Keys(statusMap)
		slices.Sort(keys)
		for _, key := range keys {
			d := statusMap[key]
			packageData := slice.Filter(d, func(index int, item otherMedicineData) bool {
				return item.PackageNumber != ""
			})
			packageMap := slice.GroupWith(packageData, func(t otherMedicineData) string {
				return t.PackageNumber
			})
			otherData := make(map[string]interface{})
			otherData["storehouseName"] = d[0].StorehouseName
			otherData["name"] = d[0].Name
			otherData["batchNumber"] = d[0].BatchNumber
			otherData["expirationDate"] = d[0].ExpirationDate
			otherData["status"] = d[0].Status
			if len(maputil.Keys(packageMap)) > 0 {
				pc := 0
				for k, v := range packageMap {
					pn, _ := strconv.ParseInt(k, 10, 64)
					if otherPackageNumberMap[int(pn)] == len(v) {
						pc++
					}
				}
				number := fmt.Sprintf("%d(%d)", len(d), pc)
				otherData["number"] = number
			} else {
				number := fmt.Sprintf("%d", len(d))
				otherData["number"] = number
			}
			otherOrderMap := medicinesOrder["otherMedicineOrderMap"].(map[primitive.ObjectID][]string)
			ons := make([]string, 0)
			for _, i := range d {
				on := otherOrderMap[i.ID]
				ons = append(ons, on...)
			}
			ons = slice.Unique(ons)
			orderNumbers := strings.Join(ons, ",")
			otherData["orderNumbers"] = orderNumbers
			keyMapKey := fmt.Sprintf("%s_%s_%s_%s", d[0].StorehouseID.Hex(), d[0].Name, d[0].ExpirationDate, d[0].BatchNumber)
			otherKeys, ok := keyMap[keyMapKey]
			var a primitive.A
			if ok {
				history := slice.Filter(histories, func(index int, item map[string]interface{}) bool {
					return item["oid"] == otherKeys[0].ID
				})
				for _, h := range history {
					a = append(a, h)
				}
			}
			otherData["history"] = a
			otherDatas = append(otherDatas, otherData)
		}
	}

	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return "", nil, err
	}

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return "", nil, errors.WithStack(err)
	}
	cohortMap := map[primitive.ObjectID]string{}
	for _, environment := range project.Environments {
		if environment.ID == envOID {
			for _, cohort := range environment.Cohorts {
				cohortMap[cohort.ID] = models.GetCohortReRandomName(cohort)
			}
		}
	}

	isOpenPackage, _, _, _, _, _ := tools.IsOpenPackage(envOID)
	isBlindDrugMap, err := tools.IsBlindDrugMap(envOID)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	// 获得使用到的翻译字段
	trMap := multilingual.TrBatchMap(ctx, []interface{}{
		locales.TrStash(ctx, "medicine_download_operFree"),
		locales.TrStash(ctx, "medicine_download_operRelease"),
		locales.TrStash(ctx, "medicine_download_operLost"),
		locales.TrStash(ctx, "medicine_download_operUse"),
		locales.TrStash(ctx, "medicine_download_reason"),
	})
	//写入excel文件
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	// Sheet1 有编号研究产品 Sheet2 未编号研究产品
	for i := 1; i <= 2; i++ {
		mergeCol := []string{"A", "B"}
		fieldCol := map[string]string{}
		var col = int32(2)
		title := []any{locales.TrStash(ctx, "report.attributes.project.number"), locales.TrStash(ctx, "report.attributes.project.name")}
		historyTitle := []any{}
		infoFlag := false
		infoBegin := ""
		dataMedicine := make([]map[string]interface{}, 0)
		var streamWriter *excelize.StreamWriter
		if i == 1 {
			streamWriter, _ = f.NewStreamWriter("Sheet1")
			dataMedicine = medicineData
		} else {
			f.NewSheet("Sheet2")
			streamWriter, _ = f.NewStreamWriter("Sheet2")
			dataMedicine = otherDatas
		}
		content := make([][]interface{}, len(dataMedicine))
		if templateId != "" {
			templateOID, _ := primitive.ObjectIDFromHex(templateId)
			var template models.CustomTemplate
			err := tools.Database.Collection("custom_template").FindOne(context.Background(), bson.M{
				"_id": templateOID,
			}).Decode(&template)
			if err != nil {
				return "", nil, err
			}

			for _, field := range template.Fields {

				if field == "report.attributes.project.number" || field == "report.attributes.project.name" {
					continue
				}

				if field == "report.attributes.random.stage" && project.Type != 3 {
					continue
				}

				if field == "report.attributes.random.cohort" && project.Type != 2 {
					continue
				}

				if (field == "report.attributes.random.stage" || field == "report.attributes.random.cohort") && !isBlindedRole {
					continue
				}
				if i == 1 && field == "report.attributes.research.other" {
					continue
				}

				if i == 2 && (field == "report.attributes.research.packageNumber" || field == "report.attributes.research.medicine.number") {
					continue
				}

				//c、d、e……
				colIdx := fmt.Sprintf("%s", string('A'+col%26))
				if col > 25 {
					colIdx = "A" + colIdx
				}
				fieldCol[field] = colIdx

				col++
				switch field {
				case "report.attributes.random.cohort":
					//判断是否是盲态角色的下载，如果是非盲角色，不需要该字段信息
					if isBlindedRole && project.Type == 2 {
						title = append(title, locales.TrStash(ctx, "report.attributes.random.cohort"))
					}
					mergeCol = append(mergeCol, colIdx)
				case "report.attributes.random.stage":
					//判断是否是盲态角色的下载，如果是非盲角色，不需要该字段信息
					if isBlindedRole && project.Type == 3 {
						title = append(title, locales.TrStash(ctx, "report.attributes.random.stage"))
					}
					mergeCol = append(mergeCol, colIdx)
				case "report.attributes.info.storehouse.name":
					title = append(title, locales.TrStash(ctx, "medicine_download_storehouse"))
					mergeCol = append(mergeCol, colIdx)
				case "report.attributes.research.medicine.number":
					if i == 1 {
						title = append(title, locales.TrStash(ctx, "medicine_download_number"))
						mergeCol = append(mergeCol, colIdx)
					}
				case "report.attributes.research.medicine.name":
					title = append(title, locales.TrStash(ctx, "medicine_download_name"))
					mergeCol = append(mergeCol, colIdx)
				case "report.attributes.research.packageNumber":
					if i == 1 {
						title = append(title, locales.TrStash(ctx, "medicine_download_packageNumber"))
						mergeCol = append(mergeCol, colIdx)
					}
				case "report.attributes.research.batch":
					title = append(title, locales.TrStash(ctx, "medicine_download_batch"))
					mergeCol = append(mergeCol, colIdx)
				case "report.attributes.research.spec":
					title = append(title, locales.TrStash(ctx, "medicine_download_spec"))
					mergeCol = append(mergeCol, colIdx)
				case "report.attributes.research.expireDate":
					title = append(title, locales.TrStash(ctx, "medicine_download_expiredDate"))
					mergeCol = append(mergeCol, colIdx)
				case "report.attributes.research.other":
					if i == 2 {
						title = append(title, locales.TrStash(ctx, "medicineOrder_download_other"))
						mergeCol = append(mergeCol, colIdx)

					}
				case "report.attributes.research.order.number":
					title = append(title, locales.TrStash(ctx, "medicine_download_orderNumber"))
					mergeCol = append(mergeCol, colIdx)
				case "report.attributes.research.status":
					title = append(title, locales.TrStash(ctx, "medicine_download_status"))
					mergeCol = append(mergeCol, colIdx)
				case "report.attributes.research.reason":
					title = append(title, locales.TrStash(ctx, "medicine_download_reason"))
					historyTitle = append(historyTitle, field)
					infoFlag = true
					if infoBegin == "" {
						infoBegin = colIdx
					}
				case "report.attributes.research.operator":
					title = append(title, locales.TrStash(ctx, "medicine_download_operator"))
					historyTitle = append(historyTitle, field)
					infoFlag = true
					if infoBegin == "" {
						infoBegin = colIdx
					}
				case "report.attributes.research.time":
					title = append(title, locales.TrStash(ctx, "medicine_download_time"))
					historyTitle = append(historyTitle, field)
					infoFlag = true
					if infoBegin == "" {
						infoBegin = colIdx
					}
				}
			}

			//设置第一行title
			titleTr := multilingual.TrBatch(ctx, title)
			t := make([]any, len(titleTr))
			for i, item := range titleTr {
				t[i] = excelize.Cell{Value: item}
			}
			_ = streamWriter.SetRow("A1", t)

			axisIndex := 2

			userIds := make([]primitive.ObjectID, 0)
			for _, d := range medicineData {
				if d["history"] != nil {
					for _, h := range d["history"].(primitive.A) {
						userIds = append(userIds, h.(map[string]interface{})["uid"].(primitive.ObjectID))
					}
				}
			}
			userIds = slice.Unique(userIds)
			users := make([]models.User, 0)
			userMap := make(map[primitive.ObjectID]int32)
			if len(userIds) > 0 {
				cursor, err := tools.Database.Collection("user").Find(nil, bson.M{"_id": bson.M{"$in": userIds}})
				if err != nil {
					return "", nil, errors.WithStack(err)
				}
				err = cursor.All(nil, &users)
				if err != nil {
					return "", nil, errors.WithStack(err)
				}
				for _, u := range users {
					userMap[u.ID] = u.Unicode
				}
			}

			for d := 0; d < len(dataMedicine); d++ {
				item := dataMedicine[d]
				max := axisIndex
				_ = streamWriter.SetRow(fmt.Sprintf("%s%d", "A", axisIndex), []any{project.ProjectInfo.Number})
				_ = streamWriter.SetRow(fmt.Sprintf("%s%d", "B", axisIndex), []any{project.ProjectInfo.Name})

				for _, field := range template.Fields {
					var val any
					switch field {
					case "report.attributes.random.cohort":
						if !isBlindedRole {
							continue
						}
						val = cohortMap[cohortID]
					case "report.attributes.random.stage":
						if !isBlindedRole {
							continue
						}
						val = cohortMap[cohortID]
					case "report.attributes.info.storehouse.name":
						val = item["storehouseName"]
					case "report.attributes.research.medicine.number":
						if i == 1 {
							val = item["number"]
						}
					case "report.attributes.research.medicine.name":
						value, ok := isBlindDrugMap[item["name"].(string)]
						if isBlindedRole && (value || !ok) {
							val = tools.BlindData
						} else {
							val = item["name"]
						}
					case "report.attributes.research.packageNumber":
						if i == 1 {
							val = item["package_number"]
						}
					case "report.attributes.research.batch":
						val = item["batchNumber"]
					case "report.attributes.research.spec":
						val = item["spec"]
					case "report.attributes.research.expireDate":
						val = item["expirationDate"]
					case "report.attributes.research.other":
						if i == 2 {
							val = item["number"]
						}
					case "report.attributes.research.order.number":
						if i == 1 {
							orderNumber := medicinesOrder["medicineOrderMap"].(map[primitive.ObjectID][]string)[item["id"].(primitive.ObjectID)]
							val = strings.Join(orderNumber, ",")
						}
						if i == 2 {
							val = item["orderNumbers"]
						}
					case "report.attributes.research.status":
						if item["status"] != nil {
							val = data.GetMedicineStatus(ctx, int(item["status"].(int32)))
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{val})
				}

				if infoFlag {
					baseAxis := axisIndex
					if item["history"] != nil {
						for _, history := range item["history"].(primitive.A) {
							info := []any{}
							if history.(map[string]any)["key"] == "history.medicine.new-freeze" ||
								history.(map[string]any)["key"] == "history.medicine.new-lost.lost" ||
								history.(map[string]any)["key"] == "history.medicine.release-new.release" ||
								history.(map[string]any)["key"] == "history.medicine.releaseLost-new.release" ||
								history.(map[string]any)["key"] == "history.medicine.otherRelease-new.release" ||
								history.(map[string]any)["key"] == "history.medicine.otherReleaseLost-new.release" ||
								history.(map[string]any)["key"] == "history.medicine.use-new" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze-new.freeze" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze-new.lost" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze-new.release" ||
								history.(map[string]any)["key"] == "history.medicine.freeze" ||
								history.(map[string]any)["key"] == "history.medicine.freeze-new" ||
								history.(map[string]any)["key"] == "history.medicine.freeze-package-new" ||
								history.(map[string]any)["key"] == "history.medicine.otherFreeze" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.drugFreeze" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.drugFreeze-package" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.otherDrugFreeze" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.allDrugFreeze" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.allDrugFreeze-package" ||
								history.(map[string]any)["key"] == "history.medicine.release" ||
								history.(map[string]any)["key"] == "history.medicine.release-package" ||
								history.(map[string]any)["key"] == "history.medicine.otherRelease" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.release" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.release-package" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.otherRelease" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.allRelease" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.allRelease-package" ||
								history.(map[string]any)["key"] == "history.medicine.lost" ||
								history.(map[string]any)["key"] == "history.medicine.lost-new" ||
								history.(map[string]any)["key"] == "history.medicine.otherMedicineLost" ||
								history.(map[string]any)["key"] == "history.medicine.otherMedicineLost-new" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.lost" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.lost-package" ||
								history.(map[string]any)["key"] == "history.medicine.releaseLost" ||
								history.(map[string]any)["key"] == "history.medicine.releaseLost-package" ||
								history.(map[string]any)["key"] == "history.medicine.otherReleaseLost" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.otherLost" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.allLost" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.allLost-package" ||
								history.(map[string]any)["key"] == "history.medicine.use" ||
								history.(map[string]any)["key"] == "history.medicine.otherCanUse" ||
								history.(map[string]any)["key"] == "history.medicine.canUse" {
								for _, field := range historyTitle {
									val := ""
									if field == "report.attributes.research.reason" {
										if history.(map[string]any)["key"] == "history.medicine.new-freeze" ||
											history.(map[string]any)["key"] == "history.medicine.freeze" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze-new.freeze" ||
											history.(map[string]any)["key"] == "history.medicine.freeze-new" ||
											history.(map[string]any)["key"] == "history.medicine.freeze-package-new" ||
											history.(map[string]any)["key"] == "history.medicine.otherFreeze" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.drugFreeze" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.drugFreeze-package" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.otherDrugFreeze" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.allDrugFreeze" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.allDrugFreeze-package" {
											val = trMap["medicine_download_operFree"]
										} else if history.(map[string]any)["key"] == "history.medicine.release" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze-new.release" ||
											history.(map[string]any)["key"] == "history.medicine.release-new.release" ||
											history.(map[string]any)["key"] == "history.medicine.otherRelease-new.release" ||
											history.(map[string]any)["key"] == "history.medicine.release-package" ||
											history.(map[string]any)["key"] == "history.medicine.otherRelease" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.release" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.release-package" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.otherRelease" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.allRelease-package" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.allRelease" {
											val = trMap["medicine_download_operRelease"]
										} else if history.(map[string]any)["key"] == "history.medicine.lost" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze-new.lost" ||
											history.(map[string]any)["key"] == "history.medicine.new-lost.lost" ||
											history.(map[string]any)["key"] == "history.medicine.releaseLost-new.release" ||
											history.(map[string]any)["key"] == "history.medicine.otherReleaseLost-new.release" ||
											history.(map[string]any)["key"] == "history.medicine.lost-new" ||
											history.(map[string]any)["key"] == "history.medicine.otherMedicineLost" ||
											history.(map[string]any)["key"] == "history.medicine.otherMedicineLost-new" ||
											history.(map[string]any)["key"] == "history.medicine.releaseLost" ||
											history.(map[string]any)["key"] == "history.medicine.releaseLost-package" ||
											history.(map[string]any)["key"] == "history.medicine.otherReleaseLost" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.otherLost" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.allLost" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.allLost-package" {
											val = trMap["medicine_download_operLost"]
										} else if history.(map[string]any)["key"] == "history.medicine.use" ||
											history.(map[string]any)["key"] == "history.medicine.use-new.use" ||
											history.(map[string]any)["key"] == "history.medicine.otherCanUse" ||
											history.(map[string]any)["key"] == "history.medicine.canUse" {
											val = trMap["medicine_download_operUse"]
										}
										var reason any
										if history.(map[string]any)["data"] != nil {
											if history.(map[string]any)["data"].(map[string]any)["reason"] != nil {
												reason = history.(map[string]any)["data"].(map[string]any)["reason"].(string)
											}
											if history.(map[string]any)["data"].(map[string]any)["freezeReason"] != nil {
												reason = history.(map[string]any)["data"].(map[string]any)["freezeReason"].(string)
											}
										}
										if history.(map[string]any)["custom_temps"] != nil {
											customTemps := history.(map[string]any)["custom_temps"].(primitive.A)[0]
											if customTemps != nil {
												custom_temp_options := customTemps.(map[string]any)["custom_temp_options"].(primitive.A)
												for _, option := range custom_temp_options {
													key := option.(map[string]any)["key"].(string)
													if key == "history.medicine.new-freeze-label.reason" ||
														key == "history.medicine.releaseLost-new.reason" ||
														key == "history.medicine.otherRelease-new.reason" ||
														key == "history.medicine.otherReleaseLost-new.reason" ||
														key == "history.medicine.use-new.reason" ||
														key == "history.medicine.drugFreeze-new.reason" {
														data := option.(map[string]any)["data"].(map[string]any)
														reason = data["reason"].(string)
													}
												}
											}
										}
										if reason != nil {
											val = val + "," + trMap["medicine_download_reason"] + ":" + reason.(string)
										}
									} else if field == "report.attributes.research.operator" {
										val = fmt.Sprintf("%s(%d)", history.(map[string]any)["user"].(string), userMap[history.(map[string]any)["uid"].(primitive.ObjectID)])
									} else if field == "report.attributes.research.time" {
										//val = time.Unix(history.(map[string]any)["time"].(int64), 0).UTC().Add(time.Hour * time.Duration(project.ProjectInfo.TimeZone)).Format("2006-01-02 15:04:05")
										val = tools.FormatFloatTime(project.TimeZoneStr, project.Tz, "", history.(map[string]any)["time"].(int64), "2006-01-02 15:04:05")
										tz, _ := tools.GetProjectLocationUtc(project.ProjectInfo.TimeZoneStr, project.ProjectInfo.Tz, "")
										val = val + "(" + tz + ")"
										//if project.ProjectInfo.TimeZone >= 0 {
										//	val = val + fmt.Sprintf("(UTC+%d)", project.ProjectInfo.TimeZone)
										//} else {
										//	val = val + fmt.Sprintf("(UTC-%d)", project.ProjectInfo.TimeZone)
										//}
									}
									info = append(info, val)
								}
								_ = streamWriter.SetRow(fmt.Sprintf("%s%d", infoBegin, baseAxis), info)
								baseAxis++
								if baseAxis > max {
									max = baseAxis
								}
							}
						}
					}
				}

				if axisIndex == max {
					axisIndex++
				} else {
					for _, colIdx := range mergeCol {
						_ = streamWriter.MergeCell(fmt.Sprintf("%s%d", colIdx, axisIndex), fmt.Sprintf("%s%d", colIdx, max-1))
					}
					axisIndex = max
				}
			}
		} else {
			if isBlindedRole {
				if project.Type == 2 {
					title = append(title, locales.TrStash(ctx, "report.attributes.random.cohort"))
				}
				if project.Type == 3 {
					title = append(title, locales.TrStash(ctx, "report.attributes.random.stage"))
				}

			}
			title = append(title, locales.TrStash(ctx, "medicine_download_storehouse"))

			if i == 1 {
				title = append(title, locales.TrStash(ctx, "medicine_download_number"))
			}

			title = append(title, locales.TrStash(ctx, "medicine_download_name"))
			//判断项目是否按照包装运输
			if isOpenPackage && i == 1 {
				title = append(title, locales.TrStash(ctx, "medicine_download_packageNumber"))
			}
			title = append(title, locales.TrStash(ctx, "medicine_download_batch"))
			title = append(title, locales.TrStash(ctx, "medicine_download_spec"))
			title = append(title, locales.TrStash(ctx, "medicine_download_expiredDate"))
			if i == 2 {
				title = append(title, locales.TrStash(ctx, "medicineOrder_download_other"))
			}
			title = append(title, locales.TrStash(ctx, "medicine_download_orderNumber"))
			title = append(title, locales.TrStash(ctx, "medicine_download_status"))

			for d := 0; d < len(dataMedicine); d++ {
				item := dataMedicine[d]
				row := []any{project.ProjectInfo.Number, project.ProjectInfo.Name}
				if isBlindedRole && (project.Type == 2 || project.Type == 3) {
					var cohort any
					if cohortID != primitive.NilObjectID {
						cohort = cohortMap[cohortID]
					}
					row = append(row, cohort)
				}
				row = append(row, item["storehouseName"])
				if i == 1 {
					row = append(row, item["number"])
				}
				blind, ok := isBlindDrugMap[item["name"].(string)]
				if isBlindedRole && (blind || !ok) {
					row = append(row, tools.BlindData)
				} else {
					row = append(row, item["name"])
				}
				//判断项目是否按照包装运输
				if isOpenPackage && i == 1 {
					row = append(row, item["package_number"])
				}
				row = append(row, item["batchNumber"])
				row = append(row, item["spec"])
				row = append(row, item["expirationDate"])
				if i == 2 {
					row = append(row, item["number"])
				}
				val := ""
				if i == 1 {
					orderNumber := medicinesOrder["medicineOrderMap"].(map[primitive.ObjectID][]string)[item["id"].(primitive.ObjectID)]
					val = strings.Join(orderNumber, ",")
				}
				if i == 2 {
					val = item["orderNumbers"].(string)
				}
				row = append(row, val)
				if item["status"] != nil {
					row = append(row, data.GetMedicineStatus(ctx, int(item["status"].(int32))))
				}
				content[d] = row
			}
			titleTr := multilingual.TrBatch(ctx, title)
			t := make([]interface{}, len(titleTr))
			for i, item := range titleTr {
				t[i] = excelize.Cell{Value: item}
			}
			_ = streamWriter.SetRow("A1", t)
			for i := 1; i <= len(content); i++ {
				r := make([]interface{}, len(content[i-1]))
				for j := 0; j < len(content[i-1]); j++ {
					r[j] = excelize.Cell{Value: content[i-1][j]}
				}
				cell, _ := excelize.CoordinatesToCellName(1, i+1)
				_ = streamWriter.SetRow(cell, r)
			}
		}
		_ = streamWriter.Flush()
	}

	fileName := fmt.Sprintf("%s[%s]DepotItemReport_%s.xlsx", project.Number, getEnvName(project, envOID), now.Format("20060102150405"))

	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	return fileName, buffer.Bytes(), nil
}

func ExportSiteItemReport(ctx *gin.Context, projectID string, envID string, cohortId string, roleId string, templateId string, projectSiteIds []string, now time.Time) (string, []byte, error) {
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)

	role, err := tools.GetRole(roleId)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	var orderNumber = ""
	medicinesOrder, err := new(MedicineService).GetMedicineOrder(primitive.NilObjectID, projectOID, envOID, orderNumber)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	type otherMedicineData struct {
		ID             primitive.ObjectID `bson:"_id"`
		SiteId         primitive.ObjectID `bson:"site_id"`
		Number         string             `bson:"number"`
		Name           string             `bson:"name"`
		BatchNumber    string             `bson:"batch_number"`
		Spec           string             `bson:"spec"`
		ExpirationDate string             `bson:"expiration_date"`
		PackageNumber  string             `bson:"package_number"`
		Status         int32              `bson:"status"`
	}
	var medicineData []map[string]interface{}
	otherMedicineDatas := make([]otherMedicineData, 0)
	sites := make(map[primitive.ObjectID]map[string]interface{})
	if role.Scope != "depot" {
		//查询该项目下的中心
		var querySiteIds []primitive.ObjectID
		for _, v := range projectSiteIds {
			vOID, _ := primitive.ObjectIDFromHex(v)
			querySiteIds = append(querySiteIds, vOID)
		}

		var siteData []map[string]interface{}
		sitePipeline := mongo.Pipeline{
			{{Key: "$match", Value: bson.M{"_id": bson.M{"$in": querySiteIds}, "env_id": envOID}}},
			{{Key: "$lookup", Value: bson.M{"from": "region", "localField": "region_id", "foreignField": "_id", "as": "region"}}},
			{{Key: "$unwind", Value: bson.M{"path": "$region", "preserveNullAndEmptyArrays": true}}},
			{{
				Key: "$project",
				Value: bson.M{
					"id":         "$_id",
					"_id":        0,
					"tz":         "$tz",
					"siteNumber": "$number",
					"timeZone":   "$time_zone",
					"country":    bson.M{"$first": "$country"},
					"region":     "$region.name",
					"siteName":   models.ProjectSiteNameBson(ctx),
				},
			}},
		}
		siteCursor, err := tools.Database.Collection("project_site").Aggregate(nil, sitePipeline)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = siteCursor.All(nil, &siteData)
		if err != nil {
			return "", nil, errors.WithStack(err)

		}
		for _, site := range siteData {
			sites[site["id"].(primitive.ObjectID)] = site
		}

		otherMatch := bson.A{}
		match := bson.A{}
		filter := bson.M{"env_id": envOID}
		otherFilter := bson.M{"env_id": envOID}

		var siteIDs []primitive.ObjectID
		for _, siteID := range projectSiteIds {
			siteOID, _ := primitive.ObjectIDFromHex(siteID)
			siteIDs = append(siteIDs, siteOID)
		}
		filter["site_id"] = bson.M{
			"$in": siteIDs,
		}
		otherFilter["site_id"] = bson.M{
			"$in": siteIDs,
		}

		match = append(match, filter)
		otherMatch = append(otherMatch, otherFilter)

		pagePipeline := mongo.Pipeline{
			{{Key: "$match", Value: bson.M{"$and": match}}},
			{{Key: "$sort", Value: bson.D{{"site_id", 1}, {"number", 1}}}},
			{{
				Key: "$project",
				Value: bson.M{
					"id":             "$_id",
					"_id":            0,
					"siteId":         "$site_id",
					"number":         1,
					"name":           1,
					"batchNumber":    "$batch_number",
					"spec":           "$spec",
					"expirationDate": "$expiration_date",
					"packageNumber":  "$package_number",
					"status":         1,
				},
			}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "history",
				"localField":   "id",
				"foreignField": "oid",
				"as":           "history",
			}}},
		}

		otherPipeline := mongo.Pipeline{
			{{Key: "$match", Value: bson.M{"$and": otherMatch}}},
			{{Key: "$sort", Value: bson.D{{"site_id", 1}, {"info.name", 1}}}},
			{{
				Key: "$project",
				Value: bson.M{
					"_id":             1,
					"site_id":         1,
					"number":          1,
					"name":            1,
					"batch_number":    1,
					"spec":            1,
					"expiration_date": 1,
					"package_number":  1,
					"status":          1,
				},
			}},
		}

		if role.Scope == "site" {
			user, _ := tools.Me(ctx)
			filter["user_site.user_id"] = user.ID
			otherFilter["user_site.user_id"] = user.ID

			pagePipeline = mongo.Pipeline{
				{{Key: "$lookup", Value: bson.M{"from": "user_site", "localField": "site_id", "foreignField": "site_id", "as": "user_site"}}},
				{{Key: "$unwind", Value: "$user_site"}},
				{{Key: "$match", Value: bson.M{"$and": match}}},
				{{Key: "$sort", Value: bson.D{{"site_id", 1}, {"number", 1}}}},
				{{
					Key: "$project",
					Value: bson.M{
						"id":             "$_id",
						"_id":            0,
						"siteId":         "$site_id",
						"number":         1,
						"name":           1,
						"batchNumber":    "$batch_number",
						"spec":           "$spec",
						"expirationDate": "$expiration_date",
						"packageNumber":  "$package_number",
						"status":         1,
					},
				}},
				{{Key: "$lookup", Value: bson.M{
					"from":         "history",
					"localField":   "id",
					"foreignField": "oid",
					"as":           "history",
				}}},
			}

			otherPipeline = mongo.Pipeline{
				{{Key: "$lookup", Value: bson.M{"from": "user_site", "localField": "site_id", "foreignField": "site_id", "as": "user_site"}}},
				{{Key: "$unwind", Value: "$user_site"}},
				{{Key: "$match", Value: bson.M{"$and": otherMatch}}},
				{{Key: "$sort", Value: bson.D{{"site_id", 1}, {"name", 1}}}},
				{{
					Key: "$project",
					Value: bson.M{
						"_id":             1,
						"site_id":         1,
						"number":          1,
						"name":            1,
						"batch_number":    1,
						"spec":            1,
						"expiration_date": 1,
						"package_number":  1,
						"status":          1,
					},
				}},
			}
		}

		optTrue := true
		opt := &options.AggregateOptions{
			AllowDiskUse: &optTrue,
		}

		cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pagePipeline, opt)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &medicineData)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}

		otherCursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, otherPipeline, opt)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = otherCursor.All(nil, &otherMedicineDatas)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
	}

	//处理未编号药物
	otherDatas := make([]map[string]interface{}, 0)
	if len(otherMedicineDatas) > 0 {
		sIds := slice.Map(otherMedicineDatas, func(index int, item otherMedicineData) primitive.ObjectID {
			return item.SiteId
		})
		otherkeys := make([]models.MedicineOtherKey, 0)
		otherKeyMatch := bson.M{"env_id": envOID, "site_id": bson.M{"$in": sIds}}
		cursor, err := tools.Database.Collection("medicine_other_key").Find(nil, otherKeyMatch)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &otherkeys)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		keyMap := slice.GroupWith(otherkeys, func(t models.MedicineOtherKey) string {
			key := fmt.Sprintf("%s_%s_%s_%s", t.SiteID.Hex(), t.Name, t.ExpireDate, t.Batch)
			return key
		})
		oids := make([]primitive.ObjectID, 0)
		for _, v := range keyMap {
			otherKey := v[0]
			_, b := slice.Find(otherMedicineDatas, func(index int, item otherMedicineData) bool {
				return item.SiteId == otherKey.SiteID && item.Name == otherKey.Name && item.BatchNumber == otherKey.Batch && item.ExpirationDate == otherKey.ExpireDate
			})
			if b {
				oids = append(oids, otherKey.ID)
			}

		}

		//包装
		type otherPackageNumber struct {
			ID            primitive.ObjectID `bson:"_id"`
			PackageNumber int                `bson:"package_number"`
			PackageCount  int                `bson:"package_count"`
		}

		otherPackageNumbers := make([]otherPackageNumber, 0)
		packageMatch := bson.M{
			"project_id": projectOID,
			"env_id":     envOID,
		}
		packagePipepine := mongo.Pipeline{
			{{Key: "$match", Value: packageMatch}},
			{{
				Key: "$project",
				Value: bson.M{
					"_id":            1,
					"package_number": 1,
					"package_count":  1,
				},
			}},
		}
		otherPackageAll, err := tools.Database.Collection("other_package_number").Aggregate(nil, packagePipepine)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = otherPackageAll.All(nil, &otherPackageNumbers)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		otherPackageNumberMap := make(map[int]int, len(otherPackageNumbers))
		for _, number := range otherPackageNumbers {
			otherPackageNumberMap[number.PackageNumber] = number.PackageCount
		}

		histories := make([]map[string]interface{}, 0)
		cursor, err = tools.Database.Collection("history").Find(nil, bson.M{"oid": bson.M{"$in": oids}})
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &histories)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}

		statusMap := slice.GroupWith(otherMedicineDatas, func(t otherMedicineData) string {
			site := sites[t.SiteId]
			key := fmt.Sprintf("%s+%s+%s+%s+%d", site["siteNumber"].(string), t.Name, t.BatchNumber, t.ExpirationDate, t.Status)
			return key
		})
		keys := maputil.Keys(statusMap)
		slices.Sort(keys)
		for _, key := range keys {
			d := statusMap[key]
			packageData := slice.Filter(d, func(index int, item otherMedicineData) bool {
				return item.PackageNumber != ""
			})
			packageMap := slice.GroupWith(packageData, func(t otherMedicineData) string {
				return t.PackageNumber
			})
			otherData := make(map[string]interface{})
			otherData["siteId"] = d[0].SiteId
			otherData["name"] = d[0].Name
			otherData["batchNumber"] = d[0].BatchNumber
			otherData["expirationDate"] = d[0].ExpirationDate
			otherData["status"] = d[0].Status
			if len(maputil.Keys(packageMap)) > 0 {
				pc := 0
				for k, v := range packageMap {
					pn, _ := strconv.ParseInt(k, 10, 64)
					if otherPackageNumberMap[int(pn)] == len(v) {
						pc++
					}
				}
				number := fmt.Sprintf("%d(%d)", len(d), pc)
				otherData["number"] = number
			} else {
				number := fmt.Sprintf("%d", len(d))
				otherData["number"] = number
			}
			otherOrderMap := medicinesOrder["otherMedicineOrderMap"].(map[primitive.ObjectID][]string)
			ons := make([]string, 0)
			for _, i := range d {
				on := otherOrderMap[i.ID]
				ons = append(ons, on...)
			}
			ons = slice.Unique(ons)
			orderNumbers := strings.Join(ons, ",")
			otherData["orderNumbers"] = orderNumbers
			keyMapKey := fmt.Sprintf("%s_%s_%s_%s", d[0].SiteId.Hex(), d[0].Name, d[0].ExpirationDate, d[0].BatchNumber)
			otherKeys, ok := keyMap[keyMapKey]
			var a primitive.A
			if ok {
				history := slice.Filter(histories, func(index int, item map[string]interface{}) bool {
					return item["oid"] == otherKeys[0].ID
				})
				for _, h := range history {
					a = append(a, h)
				}
			}
			otherData["history"] = a
			otherDatas = append(otherDatas, otherData)
		}
	}

	isBlindedRole, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return "", nil, err
	}

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return "", nil, errors.WithStack(err)
	}
	cohortMap := map[primitive.ObjectID]string{}
	for _, environment := range project.Environments {
		if environment.ID == envOID {
			for _, cohort := range environment.Cohorts {
				cohortMap[cohort.ID] = cohort.Name
			}
		}
	}

	countries := bson.M{}
	isBlindDrugMap, err := tools.IsBlindDrugMap(envOID)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	isOpenPackage, _, _, _, _, _ := tools.IsOpenPackage(envOID)
	//写入excel文件
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	// 使用到的翻译字段
	trMap := multilingual.TrBatchMap(ctx, []interface{}{
		locales.TrStash(ctx, "medicine_download_operFree"),
		locales.TrStash(ctx, "medicine_download_operRelease"),
		locales.TrStash(ctx, "medicine_download_operLost"),
		locales.TrStash(ctx, "medicine_download_operUse"),
		locales.TrStash(ctx, "medicine_download_reason"),
	})
	// Sheet1 有编号研究产品 Sheet2 未编号研究产品
	for i := 1; i <= 2; i++ {
		mergeCol := []string{"A", "B"}
		historyTitle := []any{}
		infoFlag := false
		infoBegin := ""
		fieldCol := map[string]string{}
		var col = int32(2)
		title := []any{locales.TrStash(ctx, "report.attributes.project.number"), locales.TrStash(ctx, "report.attributes.project.name")}
		dataMedicine := make([]map[string]interface{}, 0)
		var streamWriter *excelize.StreamWriter
		if i == 1 {
			streamWriter, _ = f.NewStreamWriter("Sheet1")
			dataMedicine = medicineData
		} else {
			f.NewSheet("Sheet2")
			streamWriter, _ = f.NewStreamWriter("Sheet2")
			dataMedicine = otherDatas
		}
		content := make([][]interface{}, len(dataMedicine))
		if templateId != "" {
			templateOID, _ := primitive.ObjectIDFromHex(templateId)
			var template models.CustomTemplate
			err := tools.Database.Collection("custom_template").FindOne(context.Background(), bson.M{
				"_id": templateOID,
			}).Decode(&template)
			if err != nil {
				return "", nil, err
			}
			countries, err = database.GetCountries(ctx)

			for _, field := range template.Fields {
				if field == "report.attributes.project.number" || field == "report.attributes.project.name" {
					continue
				}

				if field == "report.attributes.random.stage" && project.Type != 3 {
					continue
				}

				if field == "report.attributes.random.cohort" && project.Type != 2 {
					continue
				}

				if i == 1 && field == "report.attributes.research.other" {
					continue
				}

				if i == 2 && (field == "report.attributes.research.packageNumber" || field == "report.attributes.research.medicine.number") {
					continue
				}

				//c、d、e……
				colIdx := fmt.Sprintf("%s", string('A'+col%26))
				if col > 25 {
					colIdx = "A" + colIdx
				}
				fieldCol[field] = colIdx
				col++

				switch field {
				// case "report.attributes.random.cohort":
				// 	title = append(title, locales.TrStash(ctx, "report.attributes.random.cohort"))
				// 	mergeCol = append(mergeCol, colIdx)
				// case "report.attributes.random.stage":
				// 	title = append(title, locales.TrStash(ctx, "report.attributes.random.stage"))
				// 	mergeCol = append(mergeCol, colIdx)
				case "report.attributes.info.site.country":
					title = append(title, locales.TrStash(ctx, "medicine_download_site_country"))
					mergeCol = append(mergeCol, colIdx)
				case "report.attributes.info.site.region":
					title = append(title, locales.TrStash(ctx, "medicine_download_site_region"))
					mergeCol = append(mergeCol, colIdx)
				case "report.attributes.info.site.name":
					title = append(title, locales.TrStash(ctx, "medicine_download_site"))
					mergeCol = append(mergeCol, colIdx)
				case "report.attributes.info.site.number":
					title = append(title, locales.TrStash(ctx, "medicine_download_site_number"))
					mergeCol = append(mergeCol, colIdx)
				case "report.attributes.research.medicine.number":
					if i == 1 {
						title = append(title, locales.TrStash(ctx, "medicine_download_number"))
						mergeCol = append(mergeCol, colIdx)
					}
				case "report.attributes.research.medicine.name":
					title = append(title, locales.TrStash(ctx, "medicine_download_name"))
					mergeCol = append(mergeCol, colIdx)
				case "report.attributes.research.packageNumber":
					if i == 1 {
						title = append(title, locales.TrStash(ctx, "medicine_download_packageNumber"))
						mergeCol = append(mergeCol, colIdx)
					}
				case "report.attributes.research.batch":
					title = append(title, locales.TrStash(ctx, "medicine_download_batch"))
					mergeCol = append(mergeCol, colIdx)
				case "report.attributes.research.spec":
					title = append(title, locales.TrStash(ctx, "medicine_download_spec"))
					mergeCol = append(mergeCol, colIdx)
				case "report.attributes.research.expireDate":
					title = append(title, locales.TrStash(ctx, "medicine_download_expiredDate"))
					mergeCol = append(mergeCol, colIdx)
				case "report.attributes.research.other":
					if i == 2 {
						title = append(title, locales.TrStash(ctx, "medicineOrder_download_other"))
						mergeCol = append(mergeCol, colIdx)

					}
				case "report.attributes.research.order.number":
					title = append(title, locales.TrStash(ctx, "medicine_download_orderNumber"))
					mergeCol = append(mergeCol, colIdx)
				case "report.attributes.research.status":
					title = append(title, locales.TrStash(ctx, "medicine_download_status"))
					mergeCol = append(mergeCol, colIdx)
				case "report.attributes.research.reason":
					title = append(title, locales.TrStash(ctx, "medicine_download_reason"))
					historyTitle = append(historyTitle, field)
					infoFlag = true
					if infoBegin == "" {
						infoBegin = colIdx
					}
				case "report.attributes.research.operator":
					title = append(title, locales.TrStash(ctx, "medicine_download_operator"))
					historyTitle = append(historyTitle, field)
					infoFlag = true
					if infoBegin == "" {
						infoBegin = colIdx
					}
				case "report.attributes.research.time":
					title = append(title, locales.TrStash(ctx, "medicine_download_time"))
					historyTitle = append(historyTitle, field)
					infoFlag = true
					if infoBegin == "" {
						infoBegin = colIdx
					}
				}
			}

			//设置第一行title
			titleTr := multilingual.TrBatch(ctx, title)
			t := make([]any, len(titleTr))
			for i, item := range titleTr {
				t[i] = excelize.Cell{Value: item}
			}
			_ = streamWriter.SetRow("A1", t)

			axisIndex := 2

			userIds := make([]primitive.ObjectID, 0)
			for _, d := range dataMedicine {
				if d["history"] != nil {
					for _, h := range d["history"].(primitive.A) {
						userIds = append(userIds, h.(map[string]interface{})["uid"].(primitive.ObjectID))
					}
				}
			}
			userIds = slice.Unique(userIds)
			users := make([]models.User, 0)
			userMap := make(map[primitive.ObjectID]int32)
			if len(userIds) > 0 {
				cursor, err := tools.Database.Collection("user").Find(nil, bson.M{"_id": bson.M{"$in": userIds}})
				if err != nil {
					return "", nil, errors.WithStack(err)
				}
				err = cursor.All(nil, &users)
				if err != nil {
					return "", nil, errors.WithStack(err)
				}
				for _, u := range users {
					userMap[u.ID] = u.Unicode
				}
			}

			for d := 0; d < len(dataMedicine); d++ {
				item := dataMedicine[d]
				max := axisIndex
				_ = streamWriter.SetRow(fmt.Sprintf("%s%d", "A", axisIndex), []any{project.ProjectInfo.Number})
				_ = streamWriter.SetRow(fmt.Sprintf("%s%d", "B", axisIndex), []any{project.ProjectInfo.Name})
				for _, field := range template.Fields {
					projectSite := sites[item["siteId"].(primitive.ObjectID)]
					var val any
					switch field {
					case "report.attributes.info.site.country":
						if projectSite["country"] != nil {
							val = countries[projectSite["country"].(string)]
						} else {
							val = ""
						}

					case "report.attributes.info.site.region":
						if projectSite["region"] != nil {
							val = projectSite["region"]
						} else {
							val = ""
						}
					case "report.attributes.info.site.name":
						val = projectSite["siteName"]
					case "report.attributes.info.site.number":
						val = projectSite["siteNumber"]
					case "report.attributes.research.medicine.number":
						if i == 1 {
							val = item["number"]
						}
					case "report.attributes.research.medicine.name":
						if isBlindedRole && isBlindDrugMap[item["name"].(string)] {
							val = tools.BlindData
						} else {
							val = item["name"]
						}
					case "report.attributes.research.packageNumber":
						if i == 1 {
							val = item["package_number"]
						}
					case "report.attributes.research.batch":
						val = item["batchNumber"]
					case "report.attributes.research.spec":
						val = item["spec"]
					case "report.attributes.research.expireDate":
						val = item["expirationDate"]
					case "report.attributes.research.other":
						if i == 2 {
							val = item["number"]
						}
					case "report.attributes.research.order.number":
						if i == 1 {
							orderNumber := medicinesOrder["medicineOrderMap"].(map[primitive.ObjectID][]string)[item["id"].(primitive.ObjectID)]
							val = strings.Join(orderNumber, ",")
						}
						if i == 2 {
							val = item["orderNumbers"]
						}
					case "report.attributes.research.status":
						if item["status"] != nil {
							val = data.GetMedicineStatus(ctx, int(item["status"].(int32)))
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{val})
				}

				if infoFlag {
					baseAxis := axisIndex
					if item["history"] != nil {
						for _, history := range item["history"].(primitive.A) {
							info := []any{}
							if history.(map[string]any)["key"] == "history.medicine.new-freeze" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze-new.freeze" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze-new.lost" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze-new.release" ||
								history.(map[string]any)["key"] == "history.medicine.new-lost.lost" ||
								history.(map[string]any)["key"] == "history.medicine.release-new.release" ||
								history.(map[string]any)["key"] == "history.medicine.releaseLost-new.release" ||
								history.(map[string]any)["key"] == "history.medicine.otherRelease-new.release" ||
								history.(map[string]any)["key"] == "history.medicine.otherReleaseLost-new.release" ||
								history.(map[string]any)["key"] == "history.medicine.use-new" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze-new" ||
								history.(map[string]any)["key"] == "history.medicine.freeze" ||
								history.(map[string]any)["key"] == "history.medicine.freeze-new" ||
								history.(map[string]any)["key"] == "history.medicine.freeze-package-new" ||
								history.(map[string]any)["key"] == "history.medicine.otherFreeze" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.drugFreeze" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.drugFreeze-package" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.otherDrugFreeze" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.allDrugFreeze" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.allDrugFreeze-package" ||
								history.(map[string]any)["key"] == "history.medicine.release" ||
								history.(map[string]any)["key"] == "history.medicine.release-package" ||
								history.(map[string]any)["key"] == "history.medicine.otherRelease" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.release" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.release-package" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.otherRelease" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.allRelease" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.allRelease-package" ||
								history.(map[string]any)["key"] == "history.medicine.lost" ||
								history.(map[string]any)["key"] == "history.medicine.lost-new" ||
								history.(map[string]any)["key"] == "history.medicine.otherMedicineLost" ||
								history.(map[string]any)["key"] == "history.medicine.otherMedicineLost-new" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.lost" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.lost-package" ||
								history.(map[string]any)["key"] == "history.medicine.releaseLost" ||
								history.(map[string]any)["key"] == "history.medicine.releaseLost-package" ||
								history.(map[string]any)["key"] == "history.medicine.otherReleaseLost" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.otherLost" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.allLost" ||
								history.(map[string]any)["key"] == "history.medicine.drugFreeze.allLost-package" ||
								history.(map[string]any)["key"] == "history.medicine.use" ||
								history.(map[string]any)["key"] == "history.medicine.otherCanUse" ||
								history.(map[string]any)["key"] == "history.medicine.canUse" {
								for _, field := range historyTitle {
									val := ""
									if field == "report.attributes.research.reason" {
										if history.(map[string]any)["key"] == "history.medicine.new-freeze" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze-new.freeze" ||
											history.(map[string]any)["key"] == "history.medicine.freeze" ||
											history.(map[string]any)["key"] == "history.medicine.freeze-new" ||
											history.(map[string]any)["key"] == "history.medicine.freeze-package-new" ||
											history.(map[string]any)["key"] == "history.medicine.otherFreeze" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.drugFreeze" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.drugFreeze-package" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.otherDrugFreeze" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.allDrugFreeze" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.allDrugFreeze-package" {
											val = trMap["medicine_download_operFree"]
										} else if history.(map[string]any)["key"] == "history.medicine.release" ||
											history.(map[string]any)["key"] == "history.medicine.release-new.release" ||
											history.(map[string]any)["key"] == "history.medicine.otherRelease-new.release" ||
											history.(map[string]any)["key"] == "history.medicine.release-package" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze-new.release" ||
											history.(map[string]any)["key"] == "history.medicine.otherRelease" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.release" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.release-package" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.otherRelease" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.allRelease-package" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.allRelease" {
											val = trMap["medicine_download_operRelease"]
										} else if history.(map[string]any)["key"] == "history.medicine.lost" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze-new.lost" ||
											history.(map[string]any)["key"] == "history.medicine.new-lost.lost" ||
											history.(map[string]any)["key"] == "history.medicine.releaseLost-new.release" ||
											history.(map[string]any)["key"] == "history.medicine.otherReleaseLost-new.release" ||
											history.(map[string]any)["key"] == "history.medicine.lost-new" ||
											history.(map[string]any)["key"] == "history.medicine.otherMedicineLost" ||
											history.(map[string]any)["key"] == "history.medicine.otherMedicineLost-new" ||
											history.(map[string]any)["key"] == "history.medicine.releaseLost" ||
											history.(map[string]any)["key"] == "history.medicine.releaseLost-package" ||
											history.(map[string]any)["key"] == "history.medicine.otherReleaseLost" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.otherLost" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.allLost-package" ||
											history.(map[string]any)["key"] == "history.medicine.drugFreeze.allLost" {
											val = trMap["medicine_download_operLost"]
										} else if history.(map[string]any)["key"] == "history.medicine.use" ||
											history.(map[string]any)["key"] == "history.medicine.use-new.use" ||
											history.(map[string]any)["key"] == "history.medicine.otherCanUse" ||
											history.(map[string]any)["key"] == "history.medicine.canUse" {
											val = trMap["medicine_download_operUse"]
										}
										var reason any
										if history.(map[string]any)["data"] != nil {
											if history.(map[string]any)["data"].(map[string]any)["reason"] != nil {
												reason = history.(map[string]any)["data"].(map[string]any)["reason"].(string)
											}
											if history.(map[string]any)["data"].(map[string]any)["freezeReason"] != nil {
												reason = history.(map[string]any)["data"].(map[string]any)["freezeReason"].(string)
											}
										}
										if history.(map[string]any)["custom_temps"] != nil {
											customTemps := history.(map[string]any)["custom_temps"].(primitive.A)[0]
											if customTemps != nil {
												custom_temp_options := customTemps.(map[string]any)["custom_temp_options"].(primitive.A)
												for _, option := range custom_temp_options {
													key := option.(map[string]any)["key"].(string)
													if key == "history.medicine.new-freeze-label.reason" ||
														key == "history.medicine.releaseLost-new.reason" ||
														key == "history.medicine.otherRelease-new.reason" ||
														key == "history.medicine.otherReleaseLost-new.reason" ||
														key == "history.medicine.use-new.reason" ||
														key == "history.medicine.drugFreeze-new.reason" {
														data := option.(map[string]any)["data"].(map[string]any)
														reason = data["reason"].(string)
													}
												}
											}
										}
										if reason != nil {
											val = val + "," + trMap["medicine_download_reason"] + ":" + reason.(string)
										}
									} else if field == "report.attributes.research.operator" {
										val = fmt.Sprintf("%s(%d)", history.(map[string]any)["user"].(string), userMap[history.(map[string]any)["uid"].(primitive.ObjectID)])
									} else if field == "report.attributes.research.time" {
										projectSite := sites[item["siteId"].(primitive.ObjectID)]
										if projectSite["tz"] != nil || projectSite["timeZone"] != nil {
											val = tools.FormatFloatTime(projectSite["timeZone"].(string), projectSite["tz"].(string), "", history.(map[string]any)["time"].(int64), "2006-01-02 15:04:05")
											tz, _ := tools.GetProjectLocationUtc(projectSite["timeZone"].(string), projectSite["tz"].(string), "")
											val = val + "(" + tz + ")"
										} else {
											val = tools.FormatFloatTime(project.TimeZoneStr, project.Tz, "", history.(map[string]any)["time"].(int64), "2006-01-02 15:04:05")
											tz, _ := tools.GetProjectLocationUtc(project.ProjectInfo.TimeZoneStr, project.ProjectInfo.Tz, "")
											val = val + "(" + tz + ")"
										}
										//val = time.Unix(history.(map[string]any)["time"].(int64), 0).UTC().Add(time.Hour * time.Duration(project.ProjectInfo.TimeZone)).Format("2006-01-02 15:04:05")
										//if project.ProjectInfo.TimeZone >= 0 {
										//	val = val + fmt.Sprintf("(UTC+%d)", project.ProjectInfo.TimeZone)
										//} else {
										//	val = val + fmt.Sprintf("(UTC-%d)", project.ProjectInfo.TimeZone)
										//}
									}
									info = append(info, val)
								}
								_ = streamWriter.SetRow(fmt.Sprintf("%s%d", infoBegin, baseAxis), info)
								baseAxis++
								if baseAxis > max {
									max = baseAxis
								}
							}
						}
					}
				}

				if axisIndex == max {
					axisIndex++
				} else {
					for _, colIdx := range mergeCol {
						_ = streamWriter.MergeCell(fmt.Sprintf("%s%d", colIdx, axisIndex), fmt.Sprintf("%s%d", colIdx, max-1))
					}
					axisIndex = max
				}
			}

		} else {
			title = append(title, locales.TrStash(ctx, "medicine_download_site"))
			title = append(title, locales.TrStash(ctx, "medicine_download_site_number"))
			if i == 1 {
				title = append(title, locales.TrStash(ctx, "medicine_download_number"))
			}

			title = append(title, locales.TrStash(ctx, "medicine_download_name"))
			//判断项目是否按照包装运输
			if isOpenPackage && i == 1 {
				title = append(title, locales.TrStash(ctx, "medicine_download_packageNumber"))
			}

			title = append(title, locales.TrStash(ctx, "medicine_download_batch"))
			title = append(title, locales.TrStash(ctx, "medicine_download_spec"))
			title = append(title, locales.TrStash(ctx, "medicine_download_expiredDate"))
			if i == 2 {
				title = append(title, locales.TrStash(ctx, "medicineOrder_download_other"))
			}
			title = append(title, locales.TrStash(ctx, "medicine_download_orderNumber"))
			title = append(title, locales.TrStash(ctx, "medicine_download_status"))

			for d := 0; d < len(dataMedicine); d++ {
				item := dataMedicine[d]
				row := []any{project.Number, project.ProjectInfo.Name}

				projectSite := sites[item["siteId"].(primitive.ObjectID)]

				row = append(row, projectSite["siteName"])
				row = append(row, projectSite["siteNumber"])
				if i == 1 {
					row = append(row, item["number"])
				}

				if isBlindedRole && isBlindDrugMap[item["name"].(string)] {
					row = append(row, tools.BlindData)
				} else {
					row = append(row, item["name"])
				}

				if isOpenPackage && i == 1 {
					row = append(row, item["package_number"])
				}
				row = append(row, item["batchNumber"])
				row = append(row, item["spec"])
				row = append(row, item["expirationDate"])
				if i == 2 {
					row = append(row, item["number"])
				}
				val := ""
				if i == 1 {
					orderNumber := medicinesOrder["medicineOrderMap"].(map[primitive.ObjectID][]string)[item["id"].(primitive.ObjectID)]
					val = strings.Join(orderNumber, ",")
				}
				if i == 2 {
					val = item["orderNumbers"].(string)
				}
				row = append(row, val)

				if item["status"] != nil {
					row = append(row, data.GetMedicineStatus(ctx, int(item["status"].(int32))))
				}

				content[d] = row
			}
			titleTr := multilingual.TrBatch(ctx, title)
			t := make([]interface{}, len(titleTr))
			for i, item := range titleTr {
				t[i] = excelize.Cell{Value: item}
			}
			_ = streamWriter.SetRow("A1", t)
			for i := 1; i <= len(content); i++ {
				r := make([]interface{}, len(content[i-1]))
				for j := 0; j < len(content[i-1]); j++ {
					r[j] = excelize.Cell{Value: content[i-1][j]}
				}
				cell, _ := excelize.CoordinatesToCellName(1, i+1)
				_ = streamWriter.SetRow(cell, r)
			}
		}
		_ = streamWriter.Flush()
	}
	fileName := fmt.Sprintf("%s[%s]SiteItemReport_%s.xlsx", project.Number, getEnvName(project, envOID), now.Format("20060102150405"))
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	return fileName, buffer.Bytes(), nil
}

func ExportSourceIPUploadHistory(ctx *gin.Context, projectId string, envId string, now time.Time) (string, []byte, error) {
	envOID, _ := primitive.ObjectIDFromHex(envId)
	projectOID, _ := primitive.ObjectIDFromHex(projectId)

	timeZone, err := tools.GetTimeZone(projectOID)

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return "", nil, errors.WithStack(err)
	}

	// sheet1
	MedicineUploadRecordViews := make([]models.MedicineUploadRecordView, 0)
	pipeline := mongo.Pipeline{
		{{"$match", bson.M{"env_id": envOID}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "user",
			"localField":   "user_id",
			"foreignField": "_id",
			"as":           "user",
		}}},
		{{"$unwind", "$user"}},
		{{"$sort", bson.D{{"create_time", -1}}}},
	}

	cursor, err := tools.Database.Collection("medicine_upload_record").Aggregate(nil, pipeline)
	err = cursor.All(nil, &MedicineUploadRecordViews)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	// sheet1标题
	sheet1Title := []interface{}{
		locales.TrStash(ctx, "source_ip_upload_history_name"),
		locales.TrStash(ctx, "medicine_download_operator"),
		locales.TrStash(ctx, "medicine_download_time"),
		locales.TrStash(ctx, "source_ip_upload_history_rows"),
		locales.TrStash(ctx, "medicine_download_status"),
	}
	// sheet1内容
	sheet1Content := make([][]interface{}, 0)
	//使用cloud查询个人时区
	for _, murv := range MedicineUploadRecordViews {
		ok := locales.TrData{}
		if murv.Status == 0 {
			ok = locales.TrStash(ctx, "source_ip_upload_history_rows_succeeded")
		} else {
			ok = locales.TrStash(ctx, "source_ip_upload_history_rows_failed")
		}

		var date string
		if timeZone >= 0 {
			date = time.Unix(int64(murv.Time.Nanoseconds()), 0).UTC().Add(time.Hour*time.Duration(timeZone)).Format("2006-01-02 15:04:05") + "(UTC+" + fmt.Sprint(timeZone) + ")"
		} else {
			date = time.Unix(int64(murv.Time.Nanoseconds()), 0).UTC().Add(time.Hour*time.Duration(timeZone)).Format("2006-01-02 15:04:05") + "(UTC" + fmt.Sprint(timeZone) + ")"
		}
		contentItem := []interface{}{
			murv.Name,
			murv.User.Name,
			date,
			murv.Row,
			ok,
		}
		sheet1Content = append(sheet1Content, contentItem)
	}

	// sheet2
	filter := bson.M{"env_id": envOID}
	medicineLookup := bson.M{"from": "medicine", "localField": "medicine_id", "foreignField": "_id", "as": "medicine"}
	userLookup := bson.M{"from": "user", "localField": "operator_id", "foreignField": "_id", "as": "user"}
	drugTrackData := make([]models.DrugTrackPage, 0)
	// var drugTrackData []map[string]interface{}
	drugTrackPipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$lookup", Value: medicineLookup}},
		{{Key: "$unwind", Value: "$medicine"}},
		{{Key: "$lookup", Value: userLookup}},
		{{Key: "$unwind", Value: "$user"}},
		{{Key: "$project", Value: bson.M{
			"id":                  "$_id",
			"serialNumber":        "$medicine.serial_number",
			"name":                "$medicine.name",
			"number":              "$medicine.number",
			"shortCode":           "$medicine.short_code",
			"expirationDate":      "$medicine.expiration_date",
			"batchNumber":         "$medicine.batch_number",
			"packageNumber":       "$medicine.package_number",
			"packageSerialNumber": "$medicine.package_serial_number",
			"status":              "$status",
			"remarks":             "$remarks",
			"operatorTime":        "$operator_time",
			"userName":            "$user.info.name",
			"userUnicode":         "$user.info.unicode",
		}}},
	}
	drugTrackCursor, err := tools.Database.Collection("drug_track").Aggregate(nil, drugTrackPipeline)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = drugTrackCursor.All(nil, &drugTrackData)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	// 查询是否是自动编码项目
	barCodeRules := make([]models.BarcodeRule, 0)
	barcodeRuleCursor, err := tools.Database.Collection("barcode_rule").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = barcodeRuleCursor.All(nil, &barCodeRules)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	// 自动编码标记
	autoCodeSign := false
	for _, bcr := range barCodeRules {
		if bcr.CodeRule == 1 { // 自动编码
			autoCodeSign = true
		}
	}

	// 查询打开包装扫码
	var drugPackageConfigure models.DrugPackageConfigure
	err = tools.Database.Collection("drug_package_configure").FindOne(nil, bson.M{"env_id": envOID}).Decode(&drugPackageConfigure)
	if err != nil && err != mongo.ErrNoDocuments {
		return "", nil, errors.WithStack(err)
	}
	isOpen := drugPackageConfigure.IsOpen

	// sheet2标题
	sheet2Title := []interface{}{
		locales.TrStash(ctx, "medicine_name"),
		locales.TrStash(ctx, "medicine_serial_number"),
	}
	//if !autoCodeSign {
	//	sheet2Title = append(sheet2Title, locales.TrStash(ctx, "medicine_serial_number"))
	//}
	sheet2Title = append(sheet2Title, locales.TrStash(ctx, "medicine_number"))
	if autoCodeSign {
		sheet2Title = append(sheet2Title, locales.TrStash(ctx, "medicine_code"))
	}
	sheet2Title = append(sheet2Title, locales.TrStash(ctx, "medicine_expiration_date"))
	sheet2Title = append(sheet2Title, locales.TrStash(ctx, "medicineOrder_download_batchNumber"))
	if isOpen {
		sheet2Title = append(sheet2Title, locales.TrStash(ctx, "medicineOrder_download_packageNumber"))
		sheet2Title = append(sheet2Title, locales.TrStash(ctx, "export.medicine.package_number_serialNumber"))
	}
	sheet2Title = append(sheet2Title, locales.TrStash(ctx, "export.dispensing.type"))
	sheet2Title = append(sheet2Title, locales.TrStash(ctx, "operator.time"))
	sheet2Title = append(sheet2Title, locales.TrStash(ctx, "operator.people"))
	sheet2Title = append(sheet2Title, locales.TrStash(ctx, "operator.content"))

	// sheet2内容
	sheet2Content := make([][]interface{}, 0)
	trMap := multilingual.TrBatchMap(ctx, []interface{}{
		locales.TrStash(ctx, "operation_log.project.descriptions"),
	})
	//使用cloud查询个人时区
	for _, dtd := range drugTrackData {
		statusItme := locales.TrData{}
		if dtd.Status == 1 {
			statusItme = locales.TrStash(ctx, "app_scan_notification_content")
		} else if dtd.Status == 2 {
			statusItme = locales.TrStash(ctx, "medicine_examine_uccess")
		} else if dtd.Status == 3 {
			statusItme = locales.TrStash(ctx, "medicine_examine_fail")
		} else if dtd.Status == 4 {
			statusItme = locales.TrStash(ctx, "medicine_examine_update")
		} else if dtd.Status == 5 {
			statusItme = locales.TrStash(ctx, "medicine_examine_release")
		} else {
			statusItme = locales.TrStash(ctx, "app_scan_notification_content")
		}

		var date string
		if timeZone >= 0 {
			date = time.Unix(int64(dtd.OperatorTime.Nanoseconds()), 0).UTC().Add(time.Hour*time.Duration(timeZone)).Format("2006-01-02 15:04:05") + "(UTC+" + fmt.Sprint(timeZone) + ")"
		} else {
			date = time.Unix(int64(dtd.OperatorTime.Nanoseconds()), 0).UTC().Add(time.Hour*time.Duration(timeZone)).Format("2006-01-02 15:04:05") + "(UTC" + fmt.Sprint(timeZone) + ")"
		}
		contentItem := []interface{}{
			dtd.Name,
		}

		//if !autoCodeSign {
		//	contentItem = append(contentItem, dtd.SerialNumber)
		//}
		if dtd.ShortCode != "" {
			contentItem = append(contentItem, "")
		} else {
			contentItem = append(contentItem, dtd.SerialNumber)
		}

		contentItem = append(contentItem, dtd.Number)

		if autoCodeSign {
			contentItem = append(contentItem, dtd.ShortCode)
		}
		contentItem = append(contentItem, dtd.ExpirationDate)
		contentItem = append(contentItem, dtd.BatchNumber)
		if isOpen {
			contentItem = append(contentItem, dtd.PackageNumber)
			contentItem = append(contentItem, dtd.PackageSerialNumber)
		}
		contentItem = append(contentItem, statusItme)
		contentItem = append(contentItem, date)
		contentItem = append(contentItem, dtd.UserName+"("+strconv.FormatInt(int64(dtd.UserUnicode), 10)+")")
		remark := dtd.Remarks
		if dtd.Status == 3 {
			remark = trMap["operation_log.project.descriptions"] + ":" + dtd.Remarks
		}
		contentItem = append(contentItem, remark)
		sheet2Content = append(sheet2Content, contentItem)
	}

	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	f.SetSheetName("Sheet1", "UploadHistory")
	tools.ExportSheet(f, "UploadHistory", multilingual.TrBatch(ctx, sheet1Title), multilingual.TrDoubleBatch(ctx, sheet1Content))
	f.NewSheet("SourceIPAudit")
	tools.ExportSheet(f, "SourceIPAudit", multilingual.TrBatch(ctx, sheet2Title), multilingual.TrDoubleBatch(ctx, sheet2Content))

	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	fileName := fmt.Sprintf("%s[%s]SourceIPUploadHistory_%s.xlsx", project.Number, getEnvName(project, envOID), now.Format("20060102150405"))
	return fileName, buffer.Bytes(), nil
}
