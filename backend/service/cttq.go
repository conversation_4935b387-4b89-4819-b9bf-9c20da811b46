package service

import (
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/wxnacy/wgo/arrays"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type CttqService struct{}

func (s *CttqService) GetSubjectDispensing(ctx *gin.Context, projectNumber string, envName string) (models.CttqInfo, error) {

	//cttqInfoList := make([]models.CttqInfo, 0)
	cttqInfo := models.CttqInfo{
		SubjectInfoList:    make([]models.SubjectInfo, 0),
		DispensingInfoList: make([]models.DispensingInfo, 0),
	}

	if projectNumber != "" {

		var settingConfig map[string]interface{}
		err := tools.Database.Collection("setting_config").FindOne(nil, bson.M{"key": "cttqWhiteList"}).Decode(&settingConfig)
		if err != nil && err != mongo.ErrNoDocuments {
			return cttqInfo, errors.WithStack(err)
		}

		if settingConfig != nil {
			projectNumberList := make([]string, 0)
			dataList := settingConfig["data"].(primitive.A)
			for _, element := range dataList {
				if item, ok := element.(string); ok {
					projectNumberList = append(projectNumberList, item)
				}
			}

			index := arrays.ContainsString(projectNumberList, projectNumber)
			if index != -1 {
				var project models.Project
				err = tools.Database.Collection("project").FindOne(nil, bson.M{"info.number": projectNumber}).Decode(&project)
				if err != nil && err != mongo.ErrNoDocuments {
					return cttqInfo, errors.WithStack(err)
				}

				envId := ""
				if envName != "" {
					if project.Environments != nil && len(project.Environments) > 0 {
						env, is := slice.Find(project.Environments, func(index int, item models.Environment) bool {
							return envName == item.Name
						})
						if is {
							envId = env.ID.Hex()
						} else {
							envId = "-1"
						}
					}
				}

				if envId != "-1" {
					attributeList, subjectList, subjectInfoList, err := GetSubjectInfo(project.ID.Hex(), envId, "")
					if err != nil {
						return cttqInfo, errors.WithStack(err)
					}
					cttqInfo.SubjectInfoList = subjectInfoList

					dispensingInfoList, err := GetDispensingInfo(project.ID.Hex(), envId, "", attributeList, subjectList)
					if err != nil {
						return cttqInfo, errors.WithStack(err)
					}
					cttqInfo.DispensingInfoList = dispensingInfoList
				} else {
					subjectInfoList := make([]models.SubjectInfo, 0)
					cttqInfo.SubjectInfoList = subjectInfoList
					dispensingInfoList := make([]models.DispensingInfo, 0)
					cttqInfo.DispensingInfoList = dispensingInfoList
				}
			}
		}
	}

	return cttqInfo, nil
}

// GetSubjectInfo ...获取受试者信息
func GetSubjectInfo(projectId string, envId string, cohortId string) ([]models.Attribute, []models.Subject, []models.SubjectInfo, error) {
	attributeList := make([]models.Attribute, 0)
	subjectList := make([]models.Subject, 0)
	subjectInfoList := make([]models.SubjectInfo, 0)

	projectOID, _ := primitive.ObjectIDFromHex(projectId)
	envOID, _ := primitive.ObjectIDFromHex(envId)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortId)

	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return attributeList, subjectList, subjectInfoList, errors.WithStack(err)
	}

	condition := bson.M{
		"project_id": projectOID,
	}
	if envOID != primitive.NilObjectID {
		condition["env_id"] = envOID
	}
	projectSiteList := make([]models.ProjectSite, 0)
	projectSiteCursor, err := tools.Database.Collection("project_site").Find(nil, condition)
	if err != nil {
		return attributeList, subjectList, subjectInfoList, errors.WithStack(err)
	}
	err = projectSiteCursor.All(nil, &projectSiteList)
	if err != nil && err != mongo.ErrNoDocuments {
		return attributeList, subjectList, subjectInfoList, errors.WithStack(err)
	}

	filter := bson.M{
		"project_id": projectOID,
	}
	if envOID != primitive.NilObjectID {
		filter["env_id"] = envOID
	}
	if cohortOID != primitive.NilObjectID {
		filter["cohort_id"] = cohortOID
	}

	formList := make([]models.Form, 0)
	formCursor, err := tools.Database.Collection("form").Find(nil, filter)
	if err != nil {
		return attributeList, subjectList, subjectInfoList, errors.WithStack(err)
	}
	err = formCursor.All(nil, &formList)
	if err != nil && err != mongo.ErrNoDocuments {
		return attributeList, subjectList, subjectInfoList, errors.WithStack(err)
	}

	randomDesignList := make([]models.RandomDesign, 0)
	randomDesignCursor, err := tools.Database.Collection("random_design").Find(nil, filter)
	if err != nil {
		return attributeList, subjectList, subjectInfoList, errors.WithStack(err)
	}
	err = randomDesignCursor.All(nil, &randomDesignList)
	if err != nil && err != mongo.ErrNoDocuments {
		return attributeList, subjectList, subjectInfoList, errors.WithStack(err)
	}

	attributeCursor, err := tools.Database.Collection("attribute").Find(nil, filter)
	if err != nil {
		return attributeList, subjectList, subjectInfoList, errors.WithStack(err)
	}
	err = attributeCursor.All(nil, &attributeList)
	if err != nil && err != mongo.ErrNoDocuments {
		return attributeList, subjectList, subjectInfoList, errors.WithStack(err)
	}

	subjectCursor, err := tools.Database.Collection("subject").Find(nil, filter)
	if err != nil {
		return attributeList, subjectList, subjectInfoList, errors.WithStack(err)
	}
	err = subjectCursor.All(nil, &subjectList)
	if err != nil && err != mongo.ErrNoDocuments {
		return attributeList, subjectList, subjectInfoList, errors.WithStack(err)
	}

	subjectIdList := make([]primitive.ObjectID, 0)
	if subjectList != nil && len(subjectList) > 0 {
		for _, subject := range subjectList {
			subjectIdList = append(subjectIdList, subject.ID)
		}
	}

	keyList := make([]string, 0)
	keyList = append(keyList, "history.subject.random")
	keyList = append(keyList, "history.subject.randomSub")
	keyList = append(keyList, "history.subject.randomNoNumber")
	keyList = append(keyList, "history.subject.randomNoNumberSub")
	keyList = append(keyList, "history.subject.at-random-random")
	keyList = append(keyList, "history.subject.label.random")
	keyList = append(keyList, "history.subject.label.at-random-random")
	keyList = append(keyList, "history.subject.label.randomSub")
	keyList = append(keyList, "history.subject.label.at-random-randomSub")
	keyList = append(keyList, "history.subject.label.randomNoNumber")
	keyList = append(keyList, "history.subject.label.at-random-randomNoNumber")
	keyList = append(keyList, "history.subject.label.randomNoNumberSub")
	keyList = append(keyList, "history.subject.label.at-random-randomNoNumberSub")
	historyFilter := bson.M{
		"oid": bson.M{"$in": subjectIdList},
		"key": bson.M{"$in": keyList},
	}
	opts := &options.FindOptions{
		Sort: bson.D{{"time", -1}},
	}
	historyList := make([]models.History, 0)
	historyCursor, err := tools.Database.Collection("history").Find(nil, historyFilter, opts)
	if err != nil && err != mongo.ErrNoDocuments {
		return attributeList, subjectList, subjectInfoList, errors.WithStack(err)
	}
	err = historyCursor.All(nil, &historyList)
	if err != nil && err != mongo.ErrNoDocuments {
		return attributeList, subjectList, subjectInfoList, errors.WithStack(err)
	}

	historyMap := make(map[primitive.ObjectID]models.History)
	if historyList != nil && len(historyList) > 0 {
		for _, history := range historyList {
			// 使用多重赋值判断键是否存在
			_, exists := historyMap[history.OID]
			if !exists {
				historyMap[history.OID] = history
			}
		}
	}

	userList := make([]models.User, 0)
	userCursor, err := tools.Database.Collection("user").Find(nil, bson.M{}, opts)
	if err != nil && err != mongo.ErrNoDocuments {
		return attributeList, subjectList, subjectInfoList, errors.WithStack(err)
	}
	err = userCursor.All(nil, &userList)
	if err != nil && err != mongo.ErrNoDocuments {
		return attributeList, subjectList, subjectInfoList, errors.WithStack(err)
	}

	userMap := make(map[primitive.ObjectID]models.User)
	if userList != nil && len(userList) > 0 {
		for _, user := range userList {
			// 使用多重赋值判断键是否存在
			_, exists := userMap[user.ID]
			if !exists {
				userMap[user.ID] = user
			}
		}
	}

	if subjectList != nil && len(subjectList) > 0 {
		for _, subject := range subjectList {

			var projectSite models.ProjectSite
			if projectSiteList != nil && len(projectSiteList) > 0 {
				for _, site := range projectSiteList {
					if site.ID == subject.ProjectSiteID {
						projectSite = site
					}
				}
			}

			cohort := ""
			if project.ProjectInfo.Type != 1 && subject.CohortID != primitive.NilObjectID {
				if project.Environments != nil && len(project.Environments) > 0 {
					env, is := slice.Find(project.Environments, func(index int, item models.Environment) bool {
						return envOID == item.ID
					})
					if is {
						if env.Cohorts != nil && len(env.Cohorts) > 0 {
							co, ex := slice.Find(env.Cohorts, func(i int, it models.Cohort) bool {
								return subject.CohortID == it.ID
							})
							if ex {
								cohort = co.Name
							}
						}
					}
				}
			}

			sexField := models.Field{}
			birthdayField := models.Field{}
			informedDateField := models.Field{}
			if formList != nil && len(formList) > 0 {
				for _, form := range formList {
					if form.EnvironmentID == subject.EnvironmentID && form.CohortID == subject.CohortID {
						if form.Fields != nil && len(form.Fields) > 0 {
							for _, f := range form.Fields {
								if f.Label == "性别" {
									if f.Type == "select" || f.Type == "radio" {
										sexField = f
									}
								} else if f.Label == "出生日期" {
									if f.Type == "datePicker" && f.DateFormat == "YYYY-MM-DD" {
										birthdayField = f
									}
								} else if f.Label == "知情同意书签署日期" || f.Label == "知情日期" {
									if f.Type == "datePicker" && f.DateFormat == "YYYY-MM-DD" {
										informedDateField = f
									}
								}
							}
						}
					}
				}
			}

			randomType := ""
			randomFactorList := make([]models.RandomFactor, 0)
			subGroupName := ""
			if randomDesignList != nil && len(randomDesignList) > 0 {
				for _, design := range randomDesignList {
					if design.EnvironmentID == subject.EnvironmentID && design.CohortID == subject.CohortID {
						if design.Info.Factors != nil && len(design.Info.Factors) > 0 {
							for _, factor := range design.Info.Factors {
								randomFactorList = append(randomFactorList, factor)
							}
						}
						if design.Info.Groups != nil && len(design.Info.Groups) > 0 {
							for _, group := range design.Info.Groups {
								if group.SubGroup != nil && len(group.SubGroup) > 0 {
									for _, subGroup := range group.SubGroup {
										if subGroup.Name == subject.SubGroupName {
											if subGroup.Blind {
												subGroupName = ""
											} else {
												subGroupName = subject.SubGroupName
											}
										}

									}
								}
							}
						}
						randomType = fmt.Sprintf("%d", design.Info.Type)
					}
				}
			}

			blind := false
			ab := models.Attribute{}
			if attributeList != nil && len(attributeList) > 0 {
				for _, attribute := range attributeList {
					if attribute.EnvironmentID == subject.EnvironmentID && attribute.CohortID == subject.CohortID {
						blind = attribute.AttributeInfo.Blind
						ab = attribute
					}
				}
			}

			subjectNumber := ""
			sex := ""
			birthday := ""
			informedDate := ""
			factorsInfoList := make([]models.FactorsInfo, 0)
			if subject.Info != nil && len(subject.Info) > 0 {
				for _, info := range subject.Info {
					if info.Name == "shortname" {
						// 将 interface{} 类型转换为 string
						str, ok := info.Value.(string)
						if ok {
							subjectNumber = str
						}
					} else {
						if sexField.Name != "" && sexField.Name == info.Name {
							// 将 interface{} 类型转换为 string
							str, ok := info.Value.(string)
							if ok {
								if sexField.Options != nil && len(sexField.Options) > 0 {
									for _, option := range sexField.Options {
										if option.Value == str {
											if option.Label == "男性" || option.Label == "女性" || option.Label == "男" || option.Label == "女" {
												sex = option.Label
											}
										}
									}
								}
							}
						}
						if birthdayField.Name != "" && birthdayField.Name == info.Name {
							// 将 interface{} 类型转换为 string
							str, ok := info.Value.(string)
							if ok {
								birthday = str
							}
						}
						if informedDateField.Name != "" && informedDateField.Name == info.Name {
							// 将 interface{} 类型转换为 string
							str, ok := info.Value.(string)
							if ok {
								informedDate = str
							}
						}

						if randomFactorList != nil && len(randomFactorList) > 0 {
							for _, factor := range randomFactorList {
								if factor.Name == info.Name {
									var factorsInfo models.FactorsInfo
									factorsInfo.Code = factor.Number
									factorsInfo.Name = factor.Label
									// 将 interface{} 类型转换为 string
									str, ok := info.Value.(string)
									if ok {
										factorsInfo.Value = str
									}
									if factor.Options != nil && len(factor.Options) > 0 {
										for _, option := range factor.Options {
											if str == option.Value {
												factorsInfo.SignValue = option.Label
											}
										}
									}
									factorsInfoList = append(factorsInfoList, factorsInfo)
								}
							}
						}

					}
				}
			}

			var subjectInfo models.SubjectInfo
			subjectInfo.ProjectNumber = project.ProjectInfo.Number
			subjectInfo.SiteNo = projectSite.Number
			subjectInfo.Site = projectSite.Name
			subjectInfo.SubjectNumber = subjectNumber
			subjectInfo.Cohort = cohort
			subjectInfo.Sex = sex
			subjectInfo.Birthday = birthday
			subjectInfo.InformedDate = informedDate
			if subject.RandomTime != 0 {
				timeStr, err := tools.GetLocationUtc(projectSite.Tz, int64(subject.RandomTime))
				if err != nil {
					return attributeList, subjectList, subjectInfoList, errors.WithStack(err)
				}
				subjectInfo.RandomTime = timeStr

				// 使用多重赋值判断键是否存在
				historyValue, historyExists := historyMap[subject.ID]
				if historyExists {
					userValue, userExists := userMap[historyValue.UID]
					if userExists {
						subjectInfo.RandomBy = fmt.Sprintf("%s(%d)", userValue.Name, userValue.Unicode)
					}
				}

			} else if subject.JoinTimeForTime != 0 {
				timeStr, err := tools.GetLocationUtc(projectSite.Tz, int64(subject.JoinTimeForTime))
				if err != nil {
					return attributeList, subjectList, subjectInfoList, errors.WithStack(err)
				}
				subjectInfo.RandomTime = timeStr

				// 使用多重赋值判断键是否存在
				historyValue, historyExists := historyMap[subject.ID]
				if historyExists {
					userValue, userExists := userMap[historyValue.UID]
					if userExists {
						subjectInfo.RandomBy = fmt.Sprintf("%s(%d)", userValue.Name, userValue.Unicode)
					}
				}
			}
			subjectInfo.RandomNumber = subject.RandomNumber
			//// 将切片编码为JSON格式
			//jsonData, err := json.Marshal(factorsInfoList)
			//if err != nil {
			//	return subjectInfoList, errors.WithStack(err)
			//}
			subjectInfo.Factors = factorsInfoList
			subjectInfo.Status = fmt.Sprintf("%d", subject.Status)
			if project.ProjectInfo.Type == 3 {
				if subject.Status == 1 {
					subjectInfo.Status = fmt.Sprintf("%d", 10)
				}
			}
			if ab.AttributeInfo.Dispensing && !ab.AttributeInfo.Random {
				if subject.Status == 7 {
					subjectInfo.Status = fmt.Sprintf("%d", 11)
				}
			}
			subjectInfo.RandomType = randomType
			if blind {
				subjectInfo.Group = ""
			} else {
				subjectInfo.Group = subject.ParGroupName
			}
			subjectInfo.SubGroup = subGroupName
			subjectInfoList = append(subjectInfoList, subjectInfo)
		}
	}

	return attributeList, subjectList, subjectInfoList, nil
}

// GetDispensingInfo ...获取药品发放信息
func GetDispensingInfo(projectId string, envId string, cohortId string, attributeList []models.Attribute, subjectList []models.Subject) ([]models.DispensingInfo, error) {
	dispensingInfoList := make([]models.DispensingInfo, 0)

	projectOID, _ := primitive.ObjectIDFromHex(projectId)
	envOID, _ := primitive.ObjectIDFromHex(envId)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortId)

	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return dispensingInfoList, errors.WithStack(err)
	}

	condition := bson.M{
		"project_id": projectOID,
	}
	if envOID != primitive.NilObjectID {
		condition["env_id"] = envOID
	}
	projectSiteList := make([]models.ProjectSite, 0)
	projectSiteCursor, err := tools.Database.Collection("project_site").Find(nil, condition)
	if err != nil {
		return dispensingInfoList, errors.WithStack(err)
	}
	err = projectSiteCursor.All(nil, &projectSiteList)
	if err != nil && err != mongo.ErrNoDocuments {
		return dispensingInfoList, errors.WithStack(err)
	}

	filter := bson.M{
		"project_id": projectOID,
	}
	if envOID != primitive.NilObjectID {
		filter["env_id"] = envOID
	}
	if cohortOID != primitive.NilObjectID {
		filter["cohort_id"] = cohortOID
	}

	dispensingList := make([]models.Dispensing, 0)
	dispensingCursor, err := tools.Database.Collection("dispensing").Find(nil, filter)
	if err != nil {
		return dispensingInfoList, errors.WithStack(err)
	}
	err = dispensingCursor.All(nil, &dispensingList)
	if err != nil && err != mongo.ErrNoDocuments {
		return dispensingInfoList, errors.WithStack(err)
	}

	if dispensingList != nil && len(dispensingList) > 0 {
		for _, dispensing := range dispensingList {

			subject := models.Subject{}
			if subjectList != nil && len(subjectList) > 0 {
				for _, sub := range subjectList {
					if sub.ID == dispensing.SubjectID {
						subject = sub
					}
				}
			}

			var projectSite models.ProjectSite
			if projectSiteList != nil && len(projectSiteList) > 0 {
				for _, site := range projectSiteList {
					if site.ID == subject.ProjectSiteID {
						projectSite = site
					}
				}
			}

			subjectNumber := ""
			if subject.Info != nil && len(subject.Info) > 0 {
				for _, info := range subject.Info {
					if info.Name == "shortname" {
						// 将 interface{} 类型转换为 string
						str, ok := info.Value.(string)
						if ok {
							subjectNumber = str
						}
					}
				}
			}

			var dispensingInfo models.DispensingInfo
			dispensingInfo.ProjectNumber = project.ProjectInfo.Number
			dispensingInfo.SiteNo = projectSite.Number
			dispensingInfo.SubjectNumber = subjectNumber
			dispensingInfo.VisitName = dispensing.VisitInfo.Name
			if dispensing.DispensingTime != 0 {
				timeStr, err := tools.GetLocationUtc(projectSite.Tz, int64(dispensing.DispensingTime))
				if err != nil {
					return dispensingInfoList, errors.WithStack(err)
				}
				dispensingInfo.DispensingTime = timeStr
			}

			if dispensing.DispensingMedicines != nil && len(dispensing.DispensingMedicines) > 0 {
				for _, medicine := range dispensing.DispensingMedicines {
					isBlindedDrug, _ := tools.IsBlindedDrug(dispensing.EnvironmentID, medicine.Name)
					if isBlindedDrug {
						dispensingInfo.MedicineName = tools.BlindData
					} else {
						dispensingInfo.MedicineName = medicine.Name
					}
					dispensingInfo.ExpirationDate = medicine.ExpirationDate
					dispensingInfo.MedicineNumber = medicine.Number
					dispensingInfo.Quantity = "1"

					dispensingInfoList = append(dispensingInfoList, dispensingInfo)
				}
			}

			if dispensing.OtherDispensingMedicines != nil && len(dispensing.OtherDispensingMedicines) > 0 {
				for _, otherMedicine := range dispensing.OtherDispensingMedicines {
					isBlindedDrug, _ := tools.IsBlindedDrug(dispensing.EnvironmentID, otherMedicine.Name)
					if isBlindedDrug {
						dispensingInfo.MedicineName = tools.BlindData
					} else {
						dispensingInfo.MedicineName = otherMedicine.Name
					}
					dispensingInfo.ExpirationDate = otherMedicine.ExpireDate
					dispensingInfo.MedicineNumber = ""
					dispensingInfo.Quantity = fmt.Sprintf("%d", otherMedicine.Count)

					dispensingInfoList = append(dispensingInfoList, dispensingInfo)
				}
			}

		}
	}

	return dispensingInfoList, nil
}
