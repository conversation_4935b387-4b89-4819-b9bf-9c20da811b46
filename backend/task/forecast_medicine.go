package task

import (
	"clinflash-irt/database"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"sort"
	"strconv"
	"time"
)

/*
for project_env //查询勾选库存预算的环境
    for project_site
        查询所有随机的受试者，关联dispensing    // 访视 + 药物名称 +中心 + 日期 ： 数量   （不同cohort下 同个中心 同种药物的需要叠加）
        var 日期+药物 数量
        for cohort
            for 受试者
                // 根据cohortMaxCount  同个日期内、同个名称的药 发的数量累加
                V2+xx组别
                    使用标签药物A：xx
                    使用开放药物B：xx
                    使用计算药物C：
                    if 药物C是公式计算药物：

                V2 -- 5号
                    a 调用函数计算要发多少种药各个药的数量是多少
                    同个日期同一种药 累加
                v3 -- 6号   ...
                    a ...
                    同个日期同一种药 累加
                v4 -- 7号   ...
                    a ...
                    同个日期同一种药 累加
        日期排序
            for 5号....n号 {
                if 库存 < N号 && N号<今天 {
                    N号感冒药不足够
                    N-1号是最后库存
                }
            }
        if 定时任务{
            读取配置提前多少天
            if N-5 > now (在这五天的范围内)
                写入邮件通知
                写入项目动态
        }else{ //前端请求
            结果是N号，响应回前端
        }

MaxCount:
    for attribute or cohort
        查询药物配置 对应访视发最大多少个药   //cohort下 访视 + 药物名称 - 数量
            V1 感冒药 3个
            V1 对照药 8个
            V2 感冒药 4个
            V2 对照药 6个
            ...
*/

type NameCount struct {
	Name  string
	Count int
}

type VisitCycleMaxCount struct {
	Name  string // 访视编号+"-"+药物名称
	Count int
}

// MedicineCount 哪个访视/日期 对应的药物数量
type MedicineCount struct {
	Medicine  string `bson:"medicine"`
	Other     bool   `bson:"other"`
	Count     int    `bson:"count"`
	IsFormula bool   `bson:"is_formula"`
}

// ForecastMedicineTask ...
func ForecastMedicineTask() error {
	_, err := ForecastMedicine(primitive.NilObjectID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func ForecastMedicine(envOID primitive.ObjectID, siteOIDs ...[]primitive.ObjectID) ([]models.NoticeSite, error) {
	// 查询所有配置了库存预测的环境
	webRequest := false
	// web返回信息 定时任务不需要用到
	noticeSite := make([]models.NoticeSite, 0)

	match := bson.M{"state": "order.forecast_title"}
	if !envOID.IsZero() {
		match["env_id"] = envOID
		webRequest = true

	}
	var noticeConfigs []models.NoticeConfig
	cursor, err := tools.Database.Collection("notice_config").Find(nil, match)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &noticeConfigs)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	for _, config := range noticeConfigs {
		var project models.Project
		err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": config.ProjectID}).Decode(&project)

		if err != nil {
			return nil, errors.WithStack(err)
		}
		envP, ok := slice.Find(project.Environments, func(index int, item models.Environment) bool {
			return item.ID == config.EnvironmentID
		})
		if !ok { //环境已经删除
			continue
		}
		env := *envP
		if env.Name != "PROD" && !webRequest {
			continue
		}
		var drugConfigures []models.DrugConfigure
		cursor, err = tools.Database.Collection("drug_configure").Find(nil, bson.M{"env_id": config.EnvironmentID})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &drugConfigures)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		var visitCycles []models.VisitCycle
		cursor, err = tools.Database.Collection("visit_cycle").Find(nil, bson.M{"env_id": config.EnvironmentID})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &visitCycles)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		subjectMap := make(map[string]models.Subject)
		for _, value := range visitCycles {
			if !value.BaseCohort.IsZero() && value.CohortID != value.BaseCohort {
				subjectMap, err = GetBaseCohortSubjectMap(nil, value.BaseCohort, primitive.NilObjectID, "")
			}
		}

		callback := func(sctx mongo.SessionContext) (interface{}, error) {
			var projectSites []models.ProjectSite
			siteMatch := bson.M{"env_id": config.EnvironmentID} //"_id": testOID

			if len(siteOIDs) > 0 {
				siteMatch["_id"] = bson.M{"$in": siteOIDs[0]}
			}
			cursor, err = tools.Database.Collection("project_site").Find(sctx, siteMatch, &options.FindOptions{
				Projection: bson.M{
					"_id":       1,
					"env_id":    1,
					"number":    1,
					"name":      models.ProjectSiteNameZhBson(),
					"name_en":   models.ProjectSiteNameEnBson(),
					"time_zone": 1,
				},
				Sort: bson.D{{"number", 1}},
			})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &projectSites)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			// 需要通知的中心 事务重跑时重置
			noticeSite = make([]models.NoticeSite, 0)
			for _, site := range projectSites {

				//strTimeZone := site.TimeZone
				strTimeZone, err := tools.GetSiteTimeZoneInfo(site)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				if strTimeZone == "" {
					//strTimeZone = fmt.Sprintf("UTC%+d", project.TimeZone)
					timeZone, _ := tools.GetProjectLocationUtc(project.ProjectInfo.TimeZoneStr, project.ProjectInfo.Tz, "")
					strTimeZone = timeZone
				}
				//intTimeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
				intTimeZone, _ := tools.ParseTimezoneOffset(strTimeZone)

				// 统计中心所有药物数量
				siteSkuMedicineCounts, err := siteSkuMedicineCountsFun(site.EnvironmentID, site.ID, intTimeZone)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				// 统计每个日期所需要的数量
				siteDateMedicineCount := map[string][]MedicineCount{}

				if len(env.Cohorts) > 0 {
					for _, cohort := range env.Cohorts {

						drugConfigureP, b := slice.Find(drugConfigures, func(index int, item models.DrugConfigure) bool {
							return item.CohortID == cohort.ID
						})
						if b {
							drugConfigure := *drugConfigureP

							visitCycleP, b2 := slice.Find(visitCycles, func(index int, item models.VisitCycle) bool {
								return item.CohortID == cohort.ID
							})
							if b2 {
								visitCycle := *visitCycleP

								VisitGroupCount := MaxCount(drugConfigure)

								attribute, err := database.GetAttributeWithEnvCohortID(nil, env.ID, cohort.ID)
								if err != nil {
									return nil, errors.WithStack(err)
								}

								subjectDispensing, err := GetSubjectDispensing(sctx, attribute, site.ID)
								if err != nil {
									return nil, err
								}
								for _, subject := range subjectDispensing {
									if subjectMap[subject.Info[0].Value.(string)].RandomTime != 0 {
										subject.RandomTime = subjectMap[subject.Info[0].Value.(string)].RandomTime
									}
									siteDateMedicineCount = dateDispensing(subject, VisitGroupCount, siteDateMedicineCount, drugConfigure, visitCycle, intTimeZone, attribute)
								}
							}
						}
					}
				} else {
					if len(drugConfigures) == 0 {
						break
					}
					drugConfigure := drugConfigures[0]
					if len(visitCycles) == 0 {
						break
					}
					visitCycle := visitCycles[0]
					attribute, err := database.GetAttributeWithEnvCohortID(nil, env.ID, primitive.NilObjectID)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					subjectDispensing, err := GetSubjectDispensing(sctx, attribute, site.ID)
					if err != nil {
						return nil, err
					}
					VisitGroupCount := MaxCount(drugConfigure)
					for _, subject := range subjectDispensing {
						dateDispensing(subject, VisitGroupCount, siteDateMedicineCount, drugConfigure, visitCycle, intTimeZone, attribute)
					}
				}

				// 时间key排序
				var dateKeys []string
				for key := range siteDateMedicineCount {
					dateKeys = append(dateKeys, key)
				}
				sort.Strings(dateKeys)

				// 所有cohort合并相同日期下药物数量/ 叠加之前日期数量
				siteDateMedicineCountGroupFun(siteDateMedicineCount, dateKeys)

				// 计算最晚可用时间结果
				siteNotice, date := forecastDateMedicine(siteDateMedicineCount, siteSkuMedicineCounts, config, intTimeZone, dateKeys)
				if siteNotice || webRequest {
					noticeSite = append(noticeSite, models.NoticeSite{
						ID:     site.ID,
						Number: site.Number,
						NameEn: site.NameEn,
						Name:   site.Name,
						Date:   date,
					})
				}
				// 所有日期
			}
			// 邮件通知
			if len(noticeSite) > 0 && !webRequest {
				err = sendMail(sctx, project, config, noticeSite)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = insertProjectDynamics(sctx, config.EnvironmentID, noticeSite)
				if err != nil {
					return nil, errors.WithStack(err)

				}
			}

			return nil, nil
		}
		err := tools.Transaction(callback)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		//return noticeSite, nil
	}

	return noticeSite, nil
}

func insertProjectDynamics(sctx mongo.SessionContext, envOID primitive.ObjectID, site []models.NoticeSite) error {
	now := time.Duration(time.Now().Unix())

	dynamics := models.ProjectDynamics{
		ID:          primitive.NewObjectID(),
		Operator:    primitive.NilObjectID,
		OID:         envOID,
		Time:        now,
		SceneTran:   "project_dynamics_scene_forecast",
		TypeTran:    "project_dynamics_type_forecast",
		ContentTran: "project_dynamics_content_forecast",
		ContentData: map[string]interface{}{
			"result": site,
		},
	}
	_, err := tools.Database.Collection("project_dynamics").InsertOne(sctx, dynamics)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func siteDateMedicineCountGroupFun(siteDateMedicineCount map[string][]MedicineCount, dateKeys []string) {
	medicineCount := map[string]int{}
	//同个药物名称 叠加之前日期的数量
	for _, key := range dateKeys {
		for i, count := range siteDateMedicineCount[key] {
			currentCount := siteDateMedicineCount[key][i].Count + medicineCount[count.Medicine+convertor.ToString(count.Other)]
			siteDateMedicineCount[key][i].Count = currentCount
			medicineCount[count.Medicine+convertor.ToString(count.Other)] = currentCount
		}
	}
}

func siteSkuMedicineCountsFun(envID, siteID primitive.ObjectID, zone float64) ([]MedicineCount, error) {
	expireDateKey, err := getExpireDateKey(envID, siteID, zone)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	status := bson.A{1, 14}

	matchMedicine := bson.A{}
	otherMatchMedicine := bson.A{}
	for key, value := range expireDateKey {
		matchMedicine = append(matchMedicine, bson.M{"name": key, "expiration_date": bson.M{"$gt": value}})
		otherMatchMedicine = append(otherMatchMedicine, bson.M{"name": key, "expiration_date": bson.M{"$gt": value}})
	}

	hour := time.Duration(zone)
	minute := time.Duration((zone - float64(hour)) * 60)
	duration := hour*time.Hour + minute*time.Minute
	date := time.Now().UTC().Add(duration).Format("2006-01-02")

	match := bson.M{"site_id": siteID, "env_id": envID, "status": bson.M{"$in": status}, "expiration_date": bson.M{"$gt": date}}
	if len(matchMedicine) > 0 {
		match["$or"] = matchMedicine
	}
	cursor, err := tools.Database.Collection("medicine").Aggregate(nil, mongo.Pipeline{
		{{"$match", match}},
		{{"$group", bson.M{"_id": "$name", "count": bson.M{"$sum": 1}}}},
		{{"$project", bson.M{
			"_id":      0,
			"medicine": "$_id",
			"count":    1,
		}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var medicineCount []MedicineCount
	err = cursor.All(nil, &medicineCount)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	matchOther := bson.M{"site_id": siteID, "env_id": envID}
	if len(otherMatchMedicine) > 0 {
		matchOther["$or"] = otherMatchMedicine
	}
	cursor, err = tools.Database.Collection("medicine_others").Aggregate(nil, mongo.Pipeline{
		{{"$match", matchOther}},
		{{"$group", bson.M{"_id": "$name", "count": bson.M{"$sum": 1}}}},
		{{"$project", bson.M{
			"_id":      0,
			"medicine": "$_id",
			"count":    1,
		}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)

	}
	var otherMedicineCount []MedicineCount
	err = cursor.All(nil, &otherMedicineCount)
	if err != nil {
		return nil, errors.WithStack(err)

	}
	for i, _ := range otherMedicineCount {
		otherMedicineCount[i].Other = true
	}
	allMedicineCount := []MedicineCount{}
	allMedicineCount = append(allMedicineCount, medicineCount...)
	allMedicineCount = append(allMedicineCount, otherMedicineCount...)
	return allMedicineCount, nil
}

// forecastDateMedicine
func forecastDateMedicine(siteDateMedicineCount map[string][]MedicineCount, siteSkuMedicineCounts []MedicineCount, config models.NoticeConfig, timeZone float64, dateKeys []string) (bool, string) {
	date := "-"
	needSend := false
	lenKey := len(dateKeys)
Loop:
	for i, key := range dateKeys {
		//count := bson.A{}
		for _, medicineInfo := range siteDateMedicineCount[key] {
			siteCount := 0
			siteSkuMedicineCountsP, ok := slice.Find(siteSkuMedicineCounts, func(index int, item MedicineCount) bool {
				return item.Medicine == medicineInfo.Medicine
			})
			if ok {
				siteSkuMedicineCount := *siteSkuMedicineCountsP
				siteCount = siteSkuMedicineCount.Count
			}
			if medicineInfo.Count > siteCount {
				needSend = true
			}
		}
		if needSend {
			break Loop
		}
		date = key
		if i+1 == lenKey {
			date = "-"
		}
	}

	siteNotice := false
	if needSend && config.ForecastTime != nil {
		if date != "-" {
			//now := time.Now().UTC().Add(time.Hour * time.Duration(timeZone)).Format("2006-01-02")
			now := tools.FormatFloatTime(fmt.Sprintf("%.10g", timeZone), "", "", time.Now().Unix(), "2006-01-02")
			// 将 timeZone(float64) 转为 Duration（支持小数小时）
			hours := int(timeZone)
			minutes := int((timeZone - float64(hours)) * 60)
			if minutes < 0 {
				minutes = -minutes
			}
			duration := time.Duration(hours)*time.Hour + time.Duration(minutes)*time.Minute
			converDate := time.Now().AddDate(0, 0, *config.ForecastTime).UTC().Add(duration).Format("2006-01-02")
			if converDate >= date && now <= date {
				siteNotice = true
			}
		}
		return siteNotice, date
	}
	return siteNotice, date
}

func sendMail(sctx mongo.SessionContext, project models.Project, config models.NoticeConfig, noticeSite []models.NoticeSite) error {
	if len(config.Roles) == 0 {
		return nil
	}
	type RoleScopeEmailSite struct {
		Scope string               `bson:"scope"`
		Email string               `bson:"email"`
		Sites []primitive.ObjectID `bson:"sites"`
	}
	var roleScopeEmailSites []RoleScopeEmailSite
	cursor, err := tools.Database.Collection("project_role_permission").Aggregate(nil, mongo.Pipeline{
		{{"$match", bson.M{"_id": bson.M{"$in": config.Roles}}}},
		{{"$lookup", bson.M{
			"from":         "user_project_environment",
			"localField":   "_id",
			"foreignField": "roles",
			"as":           "user_project_environment",
		}}},
		{{"$unwind", "$user_project_environment"}},
		{{"$match", bson.M{"user_project_environment.env_id": config.EnvironmentID}}},
		{{"$lookup", bson.M{
			"from":         "user",
			"localField":   "user_project_environment.user_id",
			"foreignField": "_id",
			"as":           "user",
		}}},
		{{"$lookup", bson.M{
			"from":         "user_site",
			"localField":   "user._id",
			"foreignField": "user_id",
			"as":           "user_site",
		}}},
		{{"$project", bson.M{
			"scope": 1,
			"email": bson.M{"$first": "$user.info.email"},
			"sites": "$user_site.site_id",
		}}},
		{{Key: "$sort", Value: bson.D{{"scope", -1}}}},
	})
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &roleScopeEmailSites)
	if err != nil {
		return errors.WithStack(err)
	}
	studyUser := map[string]bool{}
	roleScopeEmailSites = slice.Filter(roleScopeEmailSites, func(index int, item RoleScopeEmailSite) bool {
		_, ok := slice.Find(config.ExcludeRecipientList, func(index int, email string) bool {
			return email == item.Email
		})
		return !ok
	})
	var mails []interface{}
	_, projectNumberShow := slice.Find(config.FieldsConfig, func(index int, item string) bool {
		return item == "projectNumber"
	})
	_, projectNameShow := slice.Find(config.FieldsConfig, func(index int, item string) bool {
		return item == "projectName"
	})
	for _, info := range roleScopeEmailSites {
		contentData := bson.M{"projectNumber": project.Number, "projectName": project.Name, "envName": "PROD"}

		if projectNumberShow {
			contentData["projectNumberShow"] = true
		} else {
			contentData["projectNumberShow"] = false
		}

		if projectNameShow {
			contentData["projectNameShow"] = true
		} else {
			contentData["projectNameShow"] = false
		}
		contentData["envNameShow"] = true

		var noticeConfig models.NoticeConfig
		err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": config.EnvironmentID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
		if err != nil && err != mongo.ErrNoDocuments {
			return errors.WithStack(err)
		}

		langList := make([]string, 0)
		html := "forecast_new.html"
		if noticeConfig.Automatic != 0 {
			if noticeConfig.Automatic == 1 {
				langList = append(langList, "zh")
				html = "forecast_new_zh.html"
			} else if noticeConfig.Automatic == 2 {
				langList = append(langList, "en")
				html = "forecast_new_en.html"
			} else if noticeConfig.Automatic == 3 {
				langList = append(langList, "zh")
				langList = append(langList, "en")
				html = "forecast_new.html"
			}
		} else {
			langList = append(langList, "zh")
			langList = append(langList, "en")
		}

		if info.Scope == "study" {
			contentData["results"] = noticeSite
			studyUser[info.Email] = true
			mails = append(mails, models.Mail{
				ID:           primitive.NewObjectID(),
				Subject:      "order.forecast_title",
				SubjectData:  bson.M{"projectNumber": project.Number, "envName": "PROD"},
				ContentData:  contentData,
				To:           []string{info.Email},
				Lang:         "en-US",
				LangList:     langList,
				Status:       0,
				CreatedTime:  time.Duration(time.Now().Unix()),
				ExpectedTime: time.Duration(time.Now().Unix()),
				SendTime:     time.Duration(time.Now().Unix()),
				HTML:         html,
			})
		} else if !studyUser[info.Email] {

			// site dept 角色
			matchNoticeSite := slice.Filter(noticeSite, func(index int, item models.NoticeSite) bool {
				_, ok := slice.Find(info.Sites, func(index int, siteOID primitive.ObjectID) bool {
					return item.ID == siteOID
				})
				return ok
			})
			contentData["results"] = matchNoticeSite
			if len(matchNoticeSite) > 0 {
				mails = append(mails, models.Mail{
					ID:           primitive.NewObjectID(),
					Subject:      "order.forecast_title",
					SubjectData:  bson.M{"projectNumber": project.Number, "projectName": project.Name, "envName": "PROD"},
					ContentData:  contentData,
					To:           []string{info.Email},
					Lang:         "en-US",
					LangList:     langList,
					Status:       0,
					CreatedTime:  time.Duration(time.Now().Unix()),
					ExpectedTime: time.Duration(time.Now().Unix()),
					SendTime:     time.Duration(time.Now().Unix()),
					HTML:         html,
				})
			}
		}
	}
	if len(mails) > 0 {
		_, err = tools.Database.Collection("mail").InsertMany(sctx, mails)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

func MaxCount(drugConfigures models.DrugConfigure) map[string][]MedicineCount {
	cohortVisitCycle := make(map[string][]MedicineCount)

	for _, info := range drugConfigures.Configures {
		for _, cycle := range info.VisitCycles {
			for _, value := range info.Values {
				i := -1
				_, ok := slice.Find(cohortVisitCycle[cycle.Hex()+info.Group], func(index int, item MedicineCount) bool {
					i = index
					return item.Medicine == value.DrugName
				})
				if ok && cohortVisitCycle[cycle.Hex()+info.Group][i].Count < value.DispensingNumber {
					cohortVisitCycle[cycle.Hex()+info.Group][i].Count = value.DispensingNumber
				} else {
					cohortVisitCycle[cycle.Hex()+info.Group] = append(cohortVisitCycle[cycle.Hex()+info.Group], MedicineCount{
						Count:     value.DispensingNumber,
						IsFormula: info.OpenSetting == 3,
						Medicine:  value.DrugName,
						Other:     value.IsOther,
					})
				}
			}
		}
	}
	return cohortVisitCycle

}

func GetSubjectDispensing(sctx mongo.SessionContext, attribute models.Attribute, siteID primitive.ObjectID) ([]models.SubjectDispensing, error) {
	//attribute, err := database.GetAttributeWithEnvCohortID(nil, envID, cohortID)
	//if err != nil {
	//	return nil, errors.WithStack(err)
	//}

	match := bson.M{"env_id": attribute.EnvironmentID, "cohort_id": attribute.CohortID, "project_site_id": siteID}
	// 排除退出 替换的受试者
	match["status"] = bson.M{"$in": bson.A{3, 6}}

	if attribute.AttributeInfo.IsScreen {
		match["status"] = bson.M{"$in": bson.A{3, 6, 7}}
	}
	if !attribute.AttributeInfo.IsScreen {
		match["status"] = bson.M{"$in": bson.A{3, 6, 1}}
	}
	// 是否需要计算揭盲的
	if attribute.AttributeInfo.UnBlindingRestrictions {
		match["urgent_unblinding_status"] = 0
	}
	// 是否需要计算pv揭盲
	if attribute.AttributeInfo.PvUnBlindingRestrictions {
		match["pv_unblinding_status"] = 0
	}
	cursor, err := tools.Database.Collection("subject").Aggregate(sctx, mongo.Pipeline{
		{{"$match", match}},
		{{Key: "$lookup", Value: bson.M{
			"from": "dispensing",
			"let":  bson.M{"subject_id": "$_id"},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$subject_id", "$$subject_id"}}}},
				bson.M{"$sort": bson.D{{"serial_number", 1}}},
			},
			"as": "dispensing",
		}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var subjectDispensing []models.SubjectDispensing
	err = cursor.All(nil, &subjectDispensing)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return subjectDispensing, nil
}

func GetSubjectDispensingNoticeRecord(sctx mongo.SessionContext, attribute models.Attribute, siteID primitive.ObjectID, subjectOIDs []primitive.ObjectID) ([]models.SubjectDispensingVisitNotice, error) {
	//attribute, err := database.GetAttributeWithEnvCohortID(nil, envID, cohortID)
	//if err != nil {
	//	return nil, errors.WithStack(err)
	//}

	match := bson.M{"env_id": attribute.EnvironmentID, "cohort_id": attribute.CohortID, "project_site_id": siteID, "_id": bson.M{"$in": subjectOIDs}}
	// 排除退出 替换的受试者
	match["status"] = bson.M{"$in": bson.A{3, 6}}

	if attribute.AttributeInfo.IsScreen {
		match["status"] = bson.M{"$in": bson.A{3, 6, 7}}
	}
	if !attribute.AttributeInfo.IsScreen {
		match["status"] = bson.M{"$in": bson.A{3, 6, 1}}
	}
	// 是否需要计算揭盲的
	if attribute.AttributeInfo.UnBlindingRestrictions {
		match["urgent_unblinding_status"] = 0
	}
	// 是否需要计算pv揭盲
	if attribute.AttributeInfo.PvUnBlindingRestrictions {
		match["pv_unblinding_status"] = 0
	}
	cursor, err := tools.Database.Collection("subject").Aggregate(sctx, mongo.Pipeline{
		{{"$match", match}},
		{{Key: "$lookup", Value: bson.M{
			"from": "dispensing",
			"let":  bson.M{"subject_id": "$_id"},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$subject_id", "$$subject_id"}}}},
				bson.M{"$sort": bson.D{{"serial_number", 1}}},
				bson.M{"$lookup": bson.M{
					"from": "visit_notice",
					"let":  bson.M{"dispensing_id": "$_id"},
					"pipeline": bson.A{
						bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$dispensing_id", "$$dispensing_id"}}}},
						bson.M{"$lookup": bson.M{
							"from":         "user",
							"localField":   "_id",
							"foreignField": "_id",
							"as":           "push_user",
						}},
						bson.M{"$lookup": bson.M{
							"from":         "visit_notice",
							"localField":   "_id",
							"foreignField": "dispensing_id",
							"as":           "visit_notice",
						}},
					},
					"as": "visit_notice",
				}},
			},
			"as": "dispensing",
		}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var subjectDispensing []models.SubjectDispensingVisitNotice
	err = cursor.All(nil, &subjectDispensing)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return subjectDispensing, nil
}

func GetBaseCohortSubjectMap(sctx mongo.SessionContext, baseCohortOID, siteOID primitive.ObjectID, subjectNumber string) (map[string]models.Subject, error) {
	subjectMap := make(map[string]models.Subject)
	var subjects []models.Subject
	match := bson.M{"cohort_id": baseCohortOID}
	if !siteOID.IsZero() {
		match["project_site_id"] = siteOID
	}
	if subjectNumber != "" {
		match["info"] = bson.M{
			"$elemMatch": bson.M{
				"name":  "shortname",
				"value": subjectNumber,
			},
		}
	}
	cursor, err := tools.Database.Collection("subject").Find(sctx, match)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &subjects)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	for _, item := range subjects {
		subjectMap[item.Info[0].Value.(string)] = item
	}
	return subjectMap, nil
}

func dateDispensing(subject models.SubjectDispensing, visitGroupCount, siteDateMedicineCount map[string][]MedicineCount, drugConfigure models.DrugConfigure, visitCycle models.VisitCycle, intTimeZone float64, attribute models.Attribute) map[string][]MedicineCount {
	interval := float64(0)
	firstTime := time.Duration(0)
	for _, dispensing := range subject.Dispensing {
		visitInfoP, _ := slice.Find(visitCycle.Infos, func(index int, item models.VisitCycleInfo) bool {
			return dispensing.VisitInfo.VisitCycleInfoID == item.ID || dispensing.VisitInfo.Number == item.Number
		})
		visitInfo := *visitInfoP
		if dispensing.Status != 1 {
			if firstTime == 0 && dispensing.DispensingTime != 0 && visitInfo.Interval != nil && subject.RandomTime == 0 {
				subject.RandomTime = time.Duration(time.Unix(int64(dispensing.DispensingTime), 0).Add(time.Hour * time.Duration(-24**visitInfo.Interval)).Unix())
				firstTime = dispensing.DispensingTime
			}
			continue
		}

		// 获取当前访视具体的日期
		date := tools.HandleDate(visitCycle.VisitType, visitInfo, subject.RandomTime, subject.RandomTime, intTimeZone, &interval, attribute, subject.JoinTime)

		if subject.RandomTime != 0 && subject.Group == "" {
			subject.Group = "N/A"
		}

		if attribute.AttributeInfo.Random && subject.Group != "" && subject.RegisterGroup != "" {
			subject.Group = subject.RegisterGroup
		}

		for _, medicine := range visitGroupCount[dispensing.VisitInfo.VisitCycleInfoID.Hex()+subject.Group] {
			if medicine.IsFormula {
				forest, _ := ForecastMedicineCount(drugConfigure.Configures, subject, medicine.Medicine, dispensing.VisitInfo.VisitCycleInfoID)
				medicine.Count = forest
			}
			//map["2022-12-02"] 使用的药物数量
			i := -1
			_, ok := slice.Find(siteDateMedicineCount[date], func(index int, item MedicineCount) bool {
				find := item.Other == medicine.Other && item.Medicine == medicine.Medicine
				if find {
					i = index
				}
				return find
			})
			if ok { // 同个日期 同个药物存在 则 叠加
				siteDateMedicineCount[date][i].Count = siteDateMedicineCount[date][i].Count + medicine.Count
			} else { // 同个日期 同个药物不存在 则新增
				siteDateMedicineCount[date] = append(siteDateMedicineCount[date], medicine)
			}
		}
	}
	return siteDateMedicineCount
}

func ForecastMedicineCount(configure []models.DrugConfigureInfo, dispensing models.SubjectDispensing, medicineName string, visitOID primitive.ObjectID) (int, error) {
	var w *float64
	var h *float64
	var age *string
	tmp := make([]models.Dispensing, len(dispensing.Dispensing))
	copy(tmp, dispensing.Dispensing)
	_ = slice.SortByField(tmp, "DispensingTime")
	for _, d := range dispensing.Dispensing {
		if d.FormulaInfo.Weight != nil {
			w = d.FormulaInfo.Weight
		}
		if d.FormulaInfo.Height != nil {
			h = d.FormulaInfo.Height
		}
		if d.FormulaInfo.Height != nil {
			age = d.FormulaInfo.Age
		}
	}
	forest, err := formulaForest(configure, dispensing.Group, medicineName, visitOID, age, w, h)
	return forest, err
}

func handleDate(visitType int, info models.VisitCycleInfo, baseTime time.Duration, lastTime time.Duration, timeZoneInt int, interval *float64, attribute models.Attribute, joinTime string) string {
	var lineTimeStr string
	Unit, Interval, _ := tools.ReturnUnitIntervalPeriod(info.Unit, info.Interval, info.PeriodMin)
	converTimes := tools.ConvertTime(Unit, Interval, 0)
	if (visitType == 0 && (baseTime != 0 || joinTime != "")) || (visitType == 1 && lastTime != 0) {
		// 计算最小和最大时间
		var lineTime time.Time
		timeZone := time.Duration(timeZoneInt)
		if visitType == 0 { //基准日期
			if baseTime != 0 {
				lineTime = time.Unix(int64(baseTime), 0).Add(time.Hour * time.Duration(converTimes)).Add(time.Hour * timeZone)
			}
			if !attribute.AttributeInfo.Random && joinTime != "" {
				parse, _ := time.Parse("2006-01-02", joinTime)
				lineTime = parse.Add(time.Hour * time.Duration(converTimes))

			}
		} else { //上一次发药日期
			lineTime = time.Unix(int64(lastTime), 0).Add(time.Hour * time.Duration(*interval)).Add(time.Hour * time.Duration(converTimes)).Add(time.Hour * timeZone)
		}
		// 格式化时间
		lineTimeStr = lineTime.UTC().Format("2006-01-02")
	}
	// 间隔时间计算
	*interval = *interval + tools.ConvertTime(Unit, Interval, 0)

	return lineTimeStr
}

func getExpireDateKey(envID, siteID primitive.ObjectID, zone float64) (map[string]interface{}, error) {
	expireDateKey := make(map[string]interface{})

	// 查询研究产品配置的药物配置
	var drugConfigures []models.DrugConfigure
	cursor, err := tools.Database.Collection("drug_configure").Find(nil, bson.M{"env_id": envID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	cursor.All(nil, &drugConfigures)

	hour := time.Duration(zone)
	minute := time.Duration((zone - float64(hour)) * 60)
	duration := hour*time.Hour + minute*time.Minute
	times := time.Now().UTC().Add(duration).Format("2006-01-02")
	for _, drugConfigure := range drugConfigures {
		for _, config := range drugConfigure.Configures {
			for _, value := range config.Values {
				expireDateKey[value.DrugName] = times
			}
		}
	}

	// 查询研究产品配置的药物配置
	var drugPackageConfigure models.DrugPackageConfigure
	err = tools.Database.Collection("drug_package_configure").FindOne(nil, bson.M{"env_id": envID}).Decode(&drugPackageConfigure)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	for _, config := range drugPackageConfigure.UnProvideDateConfig {
		hours, _ := time.ParseDuration(fmt.Sprintf("%sh", strconv.Itoa(config.Number*24)))
		times := time.Now().UTC().Add(time.Hour * time.Duration(zone)).Add(hours).Format("2006-01-02")
		expireDateKey[config.Name] = times
	}

	// 查询中的供应计划
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": siteID}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "supply_plan_medicine",
			"localField":   "supply_plan_id",
			"foreignField": "supply_plan_id",
			"as":           "supply_plan_medicine",
		}}},
		{{Key: "$match", Value: bson.M{"supply_plan_id": bson.M{"$ne": primitive.NilObjectID}}}},

		{{Key: "$unwind", Value: "$supply_plan_medicine"}},
		{{Key: "$project", Value: bson.M{
			"_id":             0,
			"medicine_name":   "$supply_plan_medicine.info.medicine_name",
			"un_provide_date": "$supply_plan_medicine.info.un_provide_date",
		}}},
	}
	supplyPlanCursor, err := tools.Database.Collection("project_site").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var supplyPlan []map[string]interface{}
	err = supplyPlanCursor.All(nil, &supplyPlan)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	for _, item := range supplyPlan {
		hours, _ := time.ParseDuration(fmt.Sprintf("%sh", strconv.Itoa(int(item["un_provide_date"].(int32))*24)))
		if err != nil {
			return nil, err
		}
		times := time.Now().UTC().Add(time.Hour * time.Duration(zone)).Add(hours).Format("2006-01-02")
		expireDateKey[item["medicine_name"].(string)] = times
	}
	return expireDateKey, nil
}
