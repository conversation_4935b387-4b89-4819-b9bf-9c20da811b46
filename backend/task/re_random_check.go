package task

import (
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"fmt"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

// CheckReRandom 再随机项目 上一阶段已随机受试者所在组别与该阶段随即表上阶段组别不匹配
func CheckReRandom(needMail bool) (map[string]interface{}, error) {
	defer tools.DeferReturn("CheckReRandom")

	//查询出再随机的项目
	var projects []models.Project
	cursor, err := tools.Database.Collection("project").Find(nil, bson.M{"info.type": 3})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	cursor.All(nil, &projects)
	ss := make([]string, 0)
	for _, project := range projects {
		if len(project.Environments) > 0 {
			for _, env := range project.Environments {
				if env.Name == "PROD" {
					if len(env.Cohorts) > 1 {
						for _, cohort := range env.Cohorts {
							//查询出该阶段已随机的受试者所在组别
							sgs, err := tools.Database.Collection("subject").Distinct(nil, "group", bson.M{"cohort_id": cohort.ID, "status": 3})
							if err != nil {
								return nil, errors.WithStack(err)
							}
							if sgs != nil && len(sgs) > 0 {
								groups := make([]string, len(sgs))
								for i, data := range sgs {
									groups[i] = data.(string)
								}
								//查询第二阶段 一起用的random list 是否匹配这些组别
								for _, c := range env.Cohorts {
									if c.LastID == cohort.ID {
										rgs, err := tools.Database.Collection("random_list").Distinct(nil, "last_group", bson.M{"cohort_id": c.ID, "status": 1})
										if err != nil {
											return nil, errors.WithStack(err)
										}
										lastGroups := make([]string, len(rgs))
										for i, data := range rgs {
											lastGroups[i] = data.(string)
										}
										lackGroups := make([]string, 0)
										eqSize := false
										if len(groups) == len(lastGroups) {
											eqSize = true
										}
										if eqSize {
											for _, g := range groups {
												exist := false
												for _, lg := range lastGroups {
													if g == lg {
														exist = true
														break
													}
												}
												if !exist {
													lackGroups = append(lackGroups, g)
												}
											}
										}
										if !eqSize || len(lackGroups) > 0 {
											s := fmt.Sprintf("projectNumber[%s] projectName[%s] env[%s] 阶段[%s] 上一阶段已随机受试者的组别%s与该阶段已启用的随机表组别%s不匹配", project.Number, project.Name, env.Name, c.Name, groups, lastGroups)
											ss = append(ss, s)
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
	if len(ss) > 0 && needMail {
		var settingConfig map[string]interface{}
		tools.Database.Collection("setting_config").FindOne(nil, bson.M{"key": "corssCheck"}).Decode(&settingConfig)
		var mailArr []string
		for _, item := range settingConfig["data"].(map[string]interface{})["mail"].(primitive.A) {
			mailArr = append(mailArr, item.(string))
		}
		mails := models.Mail{
			ID:           primitive.NewObjectID(),
			Subject:      "cross.check.error",
			ContentData:  map[string]interface{}{"results": ss},
			To:           mailArr,
			Lang:         "en-US",
			Status:       0,
			CreatedTime:  time.Duration(time.Now().Unix()),
			ExpectedTime: time.Duration(time.Now().Unix()),
			SendTime:     time.Duration(time.Now().Unix()),
			HTML:         "layer_check_en-US_new.html",
		}
		_, err = tools.Database.Collection("mail").InsertOne(nil, mails)
	}
	data := map[string]interface{}{}
	data["info"] = ss
	return data, nil
}
