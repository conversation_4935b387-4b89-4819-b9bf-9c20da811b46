package task

import (
	"clinflash-irt/config"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"time"
)

// 背景：cloud禁用删除用户之后irt需要对用户的状态和deleted字段进行维护
// SynchronizeUserStatus .. 同步cloud删除/禁用用户之后的用户状态到irt
func SynchronizeUserStatus() error {
	defer tools.DeferReturn("SynchronizeUserStatus")
	//1.查询irt所有用户
	var users []models.User
	opt := &options.FindOptions{
		Sort: bson.D{{"_id", -1}},
	}
	//filter := bson.M{"env_id": eid, "project_id": oid, "user_id": user.ID}
	cursor, err := tools.Database.Collection("user").Find(nil, bson.M{}, opt)
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &users)
	if err != nil {
		return errors.WithStack(err)
	}

	irtCloudIds := make([]string, len(users))
	for i, user := range users {
		irtCloudIds[i] = user.CloudId.Hex()
	}

	//2.查询
	//ctx *gin.Context
	var userFetchResp []*models.UserData
	userFetchResp, err = tools.UserFetch(&models.UserFetchRequest{Ids: irtCloudIds}, "en")
	if err != nil {
		return errors.WithStack(err)
	}

	for _, user := range users {
		//exists := false
		for _, cloudUser := range userFetchResp {
			if user.CloudId.Hex() == cloudUser.Id {
				//exists = true
				if user.UserInfo.Status != int8(cloudUser.Status) {

					//禁用的用户
					if user.UserInfo.Status != int8(2) && cloudUser.Status == int32(2) {
						//if cloudUser.Status == int32(2) {
						//设置为已关闭状态
						if cloudUser.Customers != nil {
							customers := cloudUser.Customers
							for _, customer := range customers {
								if customer.Apps != nil {
									if stringInSlice(config.CLOUD_KEY, customer.Apps) {
										closed(user.ID.Hex(), customer.Id, 2)
									}
								}
							}
						}
					}

					//TODO:启用操作-由禁用变成启用
					if user.UserInfo.Status == int8(2) && cloudUser.Status != int32(2) {
						//if cloudUser.Status != int32(2){
						if cloudUser.Customers != nil {
							customers := cloudUser.Customers
							for _, customer := range customers {
								if customer.Apps != nil {
									if stringInSlice(config.CLOUD_KEY, customer.Apps) {
										id, _ := primitive.ObjectIDFromHex(customer.Id)
										_, err = tools.Database.Collection("user").UpdateOne(nil,
											bson.M{"_id": user.ID},
											bson.M{"$pull": bson.M{"close_customer": id}, "$set": bson.M{"info.status": int8(cloudUser.Status)}})
										if err != nil {
											return errors.WithStack(err)
										}
									}
								}
							}
						}
					}

				}

				//已经删除的用户
				if user.Deleted == false && cloudUser.Deleted == true {
					//if cloudUser.Deleted == true {
					//设置为已关闭状态
					if cloudUser.Customers != nil {
						customers := cloudUser.Customers
						for _, customer := range customers {
							if customer.Apps != nil {
								if stringInSlice(config.CLOUD_KEY, customer.Apps) {
									closed(user.ID.Hex(), customer.Id, 1)
								}
							}
						}
					}
				}
			}
		}
		//if !exists {
		//	update := bson.M{"$set": bson.M{
		//		"deleted": true,
		//	}}
		//	//如果irt中存在user,但是在cloud中不存在此user,则将irt中此user的deleted设置为true
		//	if _, err := tools.Database.Collection("user").UpdateOne(nil, bson.M{"_id": user.ID}, update); err != nil {
		//		return errors.WithStack(err)
		//	}
		//}
	}
	return nil
}

// 设置为已关闭状态
func closed(userId string, customerID string, cloudType int) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	userOID, _ := primitive.ObjectIDFromHex(userId)

	var us models.User
	if cloudType == 1 {
		_ = tools.Database.Collection("user").FindOneAndUpdate(nil, bson.M{"_id": userOID}, bson.M{"$addToSet": bson.M{"close_customer": customerOID},
			"$set": bson.M{"deleted": true}}).Decode(&us)
	} else if cloudType == 2 {
		_ = tools.Database.Collection("user").FindOneAndUpdate(nil, bson.M{"_id": userOID}, bson.M{"$addToSet": bson.M{"close_customer": customerOID},
			"$set": bson.M{"info.status": int8(2)}}).Decode(&us)
	}

	_, _ = tools.Database.Collection("user_project_environment").UpdateMany(nil,
		bson.M{"user_id": userOID, "customer_id": customerOID},
		bson.M{"$set": bson.M{"unbind": true}})

	//取消客户管理员
	//TODO 先判断是不是客户管理员
	_ = tools.RemoveCustomerAdmin(&models.CustomerAdminRequest{CustomerId: customerID, Email: us.Email}, "en")
	//取消该客户下的所有项目管理员
	_, _ = tools.Database.Collection("project").UpdateMany(nil, bson.M{"customer_id": customerOID}, bson.M{"$pull": bson.M{"administrators": userOID}})

	var userProjectEnvironments []models.UserProjectEnvironment
	opt := &options.FindOptions{
		Sort: bson.D{{"_id", -1}},
	}
	//filter := bson.M{"env_id": eid, "project_id": oid, "user_id": user.ID}
	cursor, _ := tools.Database.Collection("user_project_environment").Find(nil, bson.M{"user_id": userOID, "customer_id": customerOID}, opt)
	cursor.All(nil, &userProjectEnvironments)
	for _, userProjectEnvironment := range userProjectEnvironments {
		_ = insertUnbindInviteProjectUserLog(userProjectEnvironment.EnvID, 7, 1, 0, us.Email, userProjectEnvironment.ID, cloudType, userOID)
	}

}

func stringInSlice(str string, list []string) bool {
	for _, v := range list {
		if v == str {
			return true
		}
	}
	return false
}

// 人员管理日志——解绑/再次授权
func insertUnbindInviteProjectUserLog(OID primitive.ObjectID, types int, old int, new int, userNameEmail string, operatorID primitive.ObjectID, cloudType int, userOID primitive.ObjectID) error {

	OperationLogFieldGroups := make([]models.OperationLogFieldGroup, 0)

	if old != new {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.projectUser",
			TranKey: "operation_log.projectUser.status",
			Old: models.OperationLogField{
				Type:  6,
				Value: old,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new,
			},
		})
	}

	if OperationLogFieldGroups != nil && len(OperationLogFieldGroups) > 0 {
		marks := make([]models.Mark, 0)
		marks = append(marks, models.Mark{
			Label: "operation_log.label.projectUser",
			Value: userNameEmail,
			Blind: false,
		})
		err := SaveOperation("operation_log.module.projectUser", OID, types, OperationLogFieldGroups, marks, operatorID, cloudType, userOID)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

func SaveOperation(module string, OID primitive.ObjectID, types int, OperationLogFieldGroups []models.OperationLogFieldGroup, mark []models.Mark, operatorID primitive.ObjectID, cloudType int, userOID primitive.ObjectID) error {
	var operationLog models.OperationLog
	operationLog.ID = primitive.NewObjectID()
	operationLog.OID = OID
	operationLog.Operator = userOID
	operationLog.Module = module
	operationLog.Mark = mark
	operationLog.Time = time.Duration(time.Now().Unix())
	operationLog.Type = types
	operationLog.OperatorID = operatorID
	operationLog.Fields = OperationLogFieldGroups
	operationLog.CloudType = cloudType
	_, err := tools.Database.Collection("operation_log").InsertOne(nil, operationLog)
	return err
}
