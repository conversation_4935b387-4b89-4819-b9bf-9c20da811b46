package main

import (
	"clinflash-irt/middleware"
	"clinflash-irt/tools"
	"clinflash-irt/web"
	"clinflash-irt/ws"
	"github.com/gin-gonic/gin"
)

func routes(app *gin.Engine) {

	go ws.RunHub()
	_ws := app.Group("/ws")
	_ws.GET("/:token/connect", func(c *gin.Context) {
		ws.Connect(c.Writer, c.Request)
	})
	_ws.GET("/:token/disconnect", func(c *gin.Context) {
		ws.Disconnect(c.Writer, c.Request)
	})

	app.Use(tools.RequestId())
	app.Use(middleware.ErrorHandler)
	app.Use(middleware.History)
	app.Use(middleware.Mail)
	api := app.Group("/api")
	api.Use(middleware.Authentication)
	api.Use(middleware.Logger())
	//api.Use(middleware.PermissionHandler)
	api.Use(tools.Logger())
	{
		users := api.Group("/users")
		{
			users.POST("/verify", (&web.UserController{}).LoginWithToken)
			users.GET("/customers", (&web.UserController{}).ListCustomers)
			users.PATCH("", (&web.UserController{}).Update)
			users.PATCH("/update-user", (&web.UserController{}).UpdateUser)
			users.DELETE("", (&web.UserController{}).Delete)
			users.GET("/info", (&web.UserController{}).GetUserByEmail)
			users.GET("/info-from-cloud", (&web.UserController{}).GetUserFromCloud)
			users.GET("/roles/all", (&web.UserController{}).GetUserRoles)
			users.PATCH("/roles", (&web.UserController{}).SetUserRoles)
			users.POST("/customers/projects/envs/sites", (&web.UserController{}).UpdateUserSites)
			users.POST("/customers/projects/envs/storehouses", (&web.UserController{}).UpdateUserStorehouses)
			users.GET("/roles", (&web.UserController{}).GetProjectEnvironmentRoles)
			users.GET("/app/roles", (&web.UserController{}).GetProjectEnvironmentRolesByPermission)
			users.GET("/learn", (&web.UserController{}).GetLearnCourse)
			users.PATCH("/app-account", (&web.UserController{}).SwitchAppAccount)
			users.POST("/apps/user-info", (&web.UserController{}).GetUserInfo)
			users.POST("/apps/identifying-codes", (&web.UserController{}).GetIdentifyCode)
			users.POST("/apps/login", (&web.UserController{}).AppLogin)
			users.POST("/apps/login-with-password", (&web.UserController{}).AppLoginWithPassword)
			users.POST("/apps/forgot-password", (&web.UserController{}).AppForgotPassword)
			users.POST("/apps/logout", (&web.UserController{}).AppLogout)
			users.POST("/apps/language", (&web.UserController{}).AppLanguage)
			users.PATCH("/info", (&web.UserController{}).Edit)
			users.GET("/main-page-permission", (&web.UserController{}).GetMainRolePermission)
			users.GET("/project", (&web.UserController{}).UserProjectSwitch)

			users.POST("/update-user-status", (&web.UserController{}).UpdateUserStatus)

			users.POST("/customers/projects/envs/roles-sites-depots", (&web.UserController{}).UpdateUserRolesSitesDepots)

			users.POST("/unbind-roles", (&web.UserController{}).GetUserUnbindRoles)

			users.GET("/roles/permissions", (&web.UserController{}).GetProjectEnvironmentRolesPermissions)
		}
	}
	{
		customers := api.Group("/customers")
		{
			customers.GET("/users", (&web.CustomerController{}).ListCustomerUsers)
			customers.GET("/users/export", (&web.CustomerController{}).ListCustomerUsersExport)
			customers.POST("/users/export-with-ids", (&web.CustomerController{}).ListCustomerUsersExportWithIDs)
			customers.PATCH("/users/admin", (&web.CustomerController{}).UpdateUserCustomerAdmin)
			customers.POST("/users/close", (&web.CustomerController{}).CloseCustomerUser)
			customers.POST("/users/batch-close", (&web.CustomerController{}).BatchCloseCustomerUser)
			customers.POST("/users/invite-again", (&web.CustomerController{}).InviteAgainCustomerUser)
			customers.GET("/projects", (&web.CustomerController{}).ListCustomerProjects)
			customers.POST("/users", (&web.CustomerController{}).AddCustomerUser)
			customers.POST("/batch-users-verify", (&web.CustomerController{}).AddCustomerBatchUserVerify)
			customers.POST("/batch-users", (&web.CustomerController{}).AddCustomerBatchUser)
			customers.POST("/batch-users-roles", (&web.CustomerController{}).SetUsersRoles)
			customers.POST("/projects", (&web.CustomerController{}).AddCustomerProject)
			customers.GET("/roles/export", (&web.CustomerController{}).ListRolesPermissionExport)
		}
	}
	{
		barcodes := api.Group("/barcodes")
		{
			barcodes.GET("", (&web.BarcodeController{}).GetList)
			barcodes.GET("/groupRule", (&web.BarcodeController{}).GetBarcodeCodeGroupRule)
			barcodes.GET("/correlationIDs", (&web.BarcodeController{}).GetBarcodeTaskList)
			barcodes.GET("/correlationID", (&web.BarcodeController{}).GetBarcodeTask)
			barcodes.POST("", (&web.BarcodeController{}).Add)
			barcodes.GET("/config", (&web.BarcodeController{}).GetConfig)
			barcodes.GET("/codeRule", (&web.BarcodeController{}).GetCodeRule)
			barcodes.GET("/getBarcodeCodeRule", (&web.BarcodeController{}).GetBarcodeCodeRule)
			barcodes.POST("/config", (&web.BarcodeController{}).SaveConfig)
			barcodes.GET("/excels", (&web.BarcodeController{}).Download)
			barcodes.GET("/content", (&web.BarcodeController{}).GetContent)
			barcodes.POST("/feedback", (&web.BarcodeController{}).SubmitFeedback)
			barcodes.GET("/app/versions", (&web.BarcodeController{}).GetVersion)
			barcodes.GET("/app/download", (&web.BarcodeController{}).AppDownload)
		}
	}
	{
		barcodeLabel := api.Group("/barcode-label")
		{
			barcodeLabel.GET("", (&web.BarcodeLabelController{}).GetBarcodeLabelList)
			barcodeLabel.GET("/preview/:id", (&web.BarcodeLabelController{}).GetPreviewBarcodeLabel)
			barcodeLabel.GET("/:id", (&web.BarcodeLabelController{}).GetBarcodeLabelByID)
			barcodeLabel.POST("", (&web.BarcodeLabelController{}).AddBarcodeLabel)
			barcodeLabel.PUT("/:id", (&web.BarcodeLabelController{}).UpdateBarcodeLabel)
			barcodeLabel.PUT("/send/:id", (&web.BarcodeLabelController{}).SendBarcodeLabel)
			barcodeLabel.DELETE("/:id", (&web.BarcodeLabelController{}).DeleteBarcodeLabel)
			barcodeLabel.GET("/numbers", (&web.BarcodeLabelController{}).GetBarcodeLabelNumber)
		}
	}
	{
		checks := api.Group("/check")
		{
			checks.GET("/layer", (&web.CrossCheckController{}).LayerCheck)
			checks.GET("/re-random", (&web.CrossCheckController{}).ReRandomCheck)
			checks.POST("/dispensing", (&web.CrossCheckController{}).Dispensing)
			checks.GET("/projects", (&web.CrossCheckController{}).ListProjectsSite)
			checks.POST("/random", (&web.CrossCheckController{}).RandomList)
			checks.GET("/dispensing-check", (&web.CrossCheckController{}).DispensingCheck)
			checks.GET("/auth-user", (&web.CrossCheckController{}).AuthUserCheck)
		}
	}
	{
		countries := api.Group("/countries")
		{
			countries.GET("", (&web.CountryController{}).All)
		}
	}
	{
		dispensing := api.Group("/dispensing")
		{
			dispensing.GET("/visit", (&web.DispensingController{}).GetVisit)
			dispensing.POST("/visit", (&web.DispensingController{}).AddDispensingVisit)
			dispensing.GET("/subject/role", (&web.DispensingController{}).GetDispensing)
			//dispensing.GET("/subject/role/app/dispense-task", (&web.DispensingController{}).PatchAppDispenseTask)
			dispensing.PATCH("/subject/role/app/dispense-task-finish", (&web.DispensingController{}).PatchAppDispenseTaskFinish)
			dispensing.PATCH("/subject/role/app/dispense-task-voided", (&web.DispensingController{}).PatchAppDispenseTaskVoided)
			//dispensing.PATCH("/subject/role/app/dispense-task-again-push", (&web.DispensingController{}).PatchAppDispenseTaskAgainPush)
			dispensing.GET("/app/subject/role", (&web.DispensingController{}).GetAppDispensing)
			dispensing.GET("/no/build/subject", (&web.DispensingController{}).GetNonBlindDispensing)
			dispensing.POST("/replace-drug", (&web.DispensingController{}).ReplaceDrug)
			dispensing.POST("/reissue", (&web.DispensingController{}).ReissueDispensing)
			dispensing.DELETE("", (&web.DispensingController{}).Cancel)
			dispensing.GET("/export", (&web.DispensingController{}).ExportDispensing)
			dispensing.PATCH("/print", (&web.DispensingController{}).UpdatePrintStatus)
			dispensing.PATCH("/retrieval", (&web.DispensingController{}).RetrievalDrug)
			dispensing.PATCH("/invalid", (&web.DispensingController{}).InvalidDispensing)
			dispensing.PATCH("/real", (&web.DispensingController{}).RegisterDispensing)
			dispensing.GET("/subject/status-room", (&web.DispensingController{}).GetSubjectStatusAndRoom)
			dispensing.GET("/room/info", (&web.DispensingController{}).GetDispensingRoomInfo)
			dispensing.POST("/room/info", (&web.DispensingController{}).PostRecordRoomInfo)
			dispensing.GET("/export/room", (&web.DispensingController{}).ExportRoomRecord)
			dispensing.PATCH("/visit/dtp", (&web.DispensingController{}).UpdateDispensingVisitWithDTP)
			dispensing.POST("/visit/dtp", (&web.DispensingController{}).AddDispensingVisitWithDTP)
			dispensing.POST("/reissue/dtp", (&web.DispensingController{}).ReissueDispensingWithDTP)
			dispensing.GET("/reissue/medicine", (&web.DispensingController{}).GetReissueMedicineName)
			dispensing.GET("/formula", (&web.DispensingController{}).GetFormula)
			dispensing.POST("/formula/medicine", (&web.DispensingController{}).GetFormulaMedicine)
			dispensing.GET("/subject/visit", (&web.DispensingController{}).GetLabelMedicine)
			dispensing.GET("/subject/visit/web", (&web.DispensingController{}).GetWebLabelMedicines)
			dispensing.GET("/app/subject/visit/dtp-operation", (&web.DispensingController{}).GetAppAddDispensingOperationDTP)
			dispensing.GET("/resume", (&web.DispensingController{}).DispensingResume)
			dispensing.GET("/dose", (&web.DispensingController{}).DoseInfo)
			dispensing.POST("/dose-res", (&web.DispensingController{}).DoseInfoRes)
			dispensing.POST("/start/follow/up/visits", (&web.DispensingController{}).StartFollowUpVisits)
			dispensing.POST("/confirm-table", (&web.DispensingController{}).DispensingConfirmTable)
			dispensing.GET("/medicine/un-blind", (&web.DispensingController{}).GetMedicineUnBlind)
			dispensing.POST("/urgent-unblinding-application", (&web.DispensingController{}).IPUnblindingApplication)
			dispensing.POST("/urgent-unblinding-approval", (&web.DispensingController{}).UnblindingApproval)
		}
	}

	{
		subjectVisit := api.Group("/subject-visit")
		{
			subjectVisit.GET("", (&web.SubjectVisitController{}).GetSubjectVisit)
			subjectVisit.POST("", (&web.SubjectVisitController{}).PushNotice)
			subjectVisit.GET("/history", (&web.SubjectVisitController{}).GetNoticeHistory)
		}
	}

	{
		drugConfigure := api.Group("/drug/configure")
		{
			drugConfigure.GET("/visit", (&web.DrugConfigureController{}).GetVisit)
			drugConfigure.DELETE("/visit", (&web.DrugConfigureController{}).DeleteVisit)
			drugConfigure.PATCH("/visit", (&web.DrugConfigureController{}).UpdateVisit)
			drugConfigure.POST("/visit", (&web.DrugConfigureController{}).AddVisit)
			drugConfigure.GET("/visit/dtpRule", (&web.DrugConfigureController{}).GetVisitDtpRule)
			drugConfigure.GET("", (&web.DrugConfigureController{}).GetList)
			drugConfigure.DELETE("", (&web.DrugConfigureController{}).DeleteDrugConfigure)
			drugConfigure.PATCH("", (&web.DrugConfigureController{}).UpdateDrugConfigure)
			drugConfigure.PATCH("/verify", (&web.DrugConfigureController{}).UpdateDrugConfigureVerify)
			drugConfigure.POST("", (&web.DrugConfigureController{}).AddDrugConfigure)
			drugConfigure.POST("/verify", (&web.DrugConfigureController{}).AddDrugConfigureVerify)
			drugConfigure.GET("/drug-names", (&web.DrugConfigureController{}).GetDrugNames)
			drugConfigure.GET("/drug-names-by-role", (&web.DrugConfigureController{}).GetDrugNamesByRole)
			drugConfigure.GET("/drug-names-by-type", (&web.DrugConfigureController{}).GetDrugNamesByType)
			drugConfigure.POST("/medicine-other/all", (&web.DrugConfigureController{}).GetOtherMedicineList)
			drugConfigure.GET("/medicine-other", (&web.DrugConfigureController{}).ListOtherMedicine)
			drugConfigure.POST("/medicine-other", (&web.DrugConfigureController{}).AddOtherMedicine)
			drugConfigure.PATCH("/medicine-other", (&web.DrugConfigureController{}).UpdateOtherMedicine)
			drugConfigure.DELETE("/medicine-other", (&web.DrugConfigureController{}).DelOtherMedicine)
			drugConfigure.GET("/group", (&web.DrugConfigureController{}).GetGroupConfig)
			drugConfigure.PATCH("/type", (&web.DrugConfigureController{}).UpdateVisitType)
			drugConfigure.PATCH("/base-cohort", (&web.DrugConfigureController{}).UpdateVisitBaseCohort)
			drugConfigure.POST("/push", (&web.DrugConfigureController{}).PushVisit)
			drugConfigure.PATCH("/drag", (&web.DrugConfigureController{}).DragSort)
			drugConfigure.GET("/package", (&web.DrugConfigureController{}).GetDrugPackageConfigure)
			drugConfigure.PATCH("/package", (&web.DrugConfigureController{}).UpdateDrugPackage)
			drugConfigure.GET("/is-open-package", (&web.DrugConfigureController{}).GetDrugPackageIsOpen)
			drugConfigure.PATCH("/visit/history", (&web.DrugConfigureController{}).VisitHistory)

			drugConfigure.GET("/settings", (&web.DrugConfigureController{}).GetSetUpDrugConfigure)
			drugConfigure.POST("/settings", (&web.DrugConfigureController{}).SetUpDrugConfigure)
			drugConfigure.POST("/settings/label", (&web.DrugConfigureController{}).GetSetUpDrugConfigureLabel)
			drugConfigure.GET("/settings/visit", (&web.DrugConfigureController{}).GetSetUpDrugConfigureVisitList)
			drugConfigure.GET("/settings/dtpRule", (&web.DrugConfigureController{}).GetSetUpDrugConfigureDtpRule)

			drugConfigure.GET("/judge/group", (&web.DrugConfigureController{}).GetIsGroup)
			drugConfigure.GET("/judge/label", (&web.DrugConfigureController{}).GetIsLabel)
			drugConfigure.GET("/allocation/group", (&web.DrugConfigureController{}).GetAllocationGroup)

			drugConfigure.GET("/judge/role", (&web.DrugConfigureController{}).GetIsBlindedRole)

			drugConfigure.GET("/visit/settings", (&web.DrugConfigureController{}).GetVisitSettings)
			drugConfigure.POST("/visit/settings", (&web.DrugConfigureController{}).SetUpVisitSettings)

		}
	}
	{
		history := api.Group("/history")
		{
			history.GET("", (&web.HistoryController{}).GetList)
			history.GET("/key", (&web.HistoryController{}).GetKeyList)
		}
	}
	{
		notices := api.Group("/notices")
		{
			notices.GET("/configs", (&web.NoticeController{}).GetNoticeConfig)
			notices.GET("/configs/key", (&web.NoticeController{}).GetNoticeConfigByKey)
			notices.POST("/configs", (&web.NoticeController{}).SetNoticeConfig)

			notices.POST("/configs/verify", (&web.NoticeController{}).GetNoticeConfigVerify)
			notices.GET("/template-file", (&web.NoticeController{}).GetTemplateFile)
		}
	}

	{
		pageNotices := api.Group("/page-notices")
		{
			pageNotices.GET("/message-center", (&web.PageNoticeController{}).GetMessageCenter)
			pageNotices.POST("/read-operation", (&web.PageNoticeController{}).ReadOperation)
			pageNotices.POST("/system-update-notice", (&web.PageNoticeController{}).SystemUpdateNotice)
			pageNotices.POST("/send-again", (&web.PageNoticeController{}).SendAgain)

			pageNotices.GET("/mail-auth", (&web.PageNoticeController{}).GetMailAuth)

			pageNotices.GET("/message-quantity", (&web.PageNoticeController{}).GetMessageQuantity)
			pageNotices.GET("/message-system-quantity", (&web.PageNoticeController{}).GetMessageSystemQuantity)
			pageNotices.GET("/message-center-quantity", (&web.PageNoticeController{}).GetMessageCenterQuantity)
			pageNotices.GET("/message-single", (&web.PageNoticeController{}).GetMessageSingle)
			pageNotices.POST("/message-single-read", (&web.PageNoticeController{}).GetMessageSingleRead)
			pageNotices.POST("/message-all-read", (&web.PageNoticeController{}).GetMessageAllRead)
		}
	}

	{
		project := api.Group("/projects")
		{
			project.GET("", (&web.ProjectController{}).GetBy)
			project.PATCH("", (&web.ProjectController{}).Update)
			project.DELETE("", (&web.ProjectController{}).Delete)

			project.GET("/find", (&web.ProjectController{}).Find)

			project.POST("/administrators", (&web.ProjectController{}).AddAdministrator)
			project.DELETE("/administrators", (&web.ProjectController{}).RemoveAdministrator)

			project.POST("/envs", (&web.ProjectController{}).AddEnvironment)
			project.PATCH("/envs", (&web.ProjectController{}).EditEnvironment)
			project.DELETE("/envs", (&web.ProjectController{}).DeleteEnvironment)
			project.GET("/envs/blind", (&web.ProjectController{}).GetProjectEnvIsBlind)

			project.POST("/envs/cohorts", (&web.ProjectController{}).AddCohort)
			project.PATCH("/envs/cohorts", (&web.ProjectController{}).UpdateCohort)
			project.POST("/envs/cohorts/status", (&web.ProjectController{}).UpdateCohortStatus)
			project.DELETE("/envs/cohorts", (&web.ProjectController{}).DeleteCohort)
			project.PATCH("/envs/cohorts/copy", (&web.ProjectController{}).CopyCohort)
			project.GET("/envs/users/list", (&web.ProjectController{}).ProjectEnvironmentUsersSearchList)
			project.POST("/envs/users", (&web.ProjectController{}).AddProjectEnvironmentUser)
			project.POST("/envs/batch-users-verify", (&web.ProjectController{}).AddProjectEnvironmentBatchUserVerify)
			project.POST("/envs/batch-users", (&web.ProjectController{}).AddProjectEnvironmentBatchUser)
			project.PATCH("/envs/users/roles", (&web.ProjectController{}).SetProjectEnvironmentUserRoles)
			project.POST("/envs/users/unblind-codes", (&web.ProjectController{}).SetEnvUserUnblindingCode)
			project.POST("/envs/users/unbind", (&web.ProjectController{}).UnbindProjectEnvironmentUser)
			project.POST("/envs/users/batch-unbind", (&web.ProjectController{}).BatchUnbindProjectEnvironmentUser)
			project.POST("/envs/users/reauthorization", (&web.ProjectController{}).ReauthorizationProjectEnvironmentUser)
			project.GET("/envs/users/download", (&web.ProjectController{}).DownloadProjectEnvironmentUserData)
			project.POST("/users/resend-invite-email", (&web.ProjectController{}).ResendInviteEmail)

			project.POST("/envs/copy", (&web.ProjectController{}).CopyEnvironment)
			project.PATCH("/envs/update-lock-config", (&web.ProjectController{}).UpdateLockConfig)

			project.GET("/list", (&web.ProjectController{}).GetList)
			project.GET("/view-multiLanguage", (&web.ProjectController{}).GetViewMultiLanguage)
			project.GET("/home-list", (&web.ProjectController{}).GetHomeList)

			project.GET("/setting-list", (&web.ProjectController{}).GetSettingList)
			project.POST("/users", (&web.ProjectController{}).GetUsers)

			project.GET("/overview", (&web.ProjectController{}).GetOverview)

			project.GET("/cohort-status", (&web.ProjectController{}).GetCohortStatus)

			project.GET("/permissions", (&web.ProjectController{}).GetPermissions)

			project.PATCH("/type", (&web.ProjectController{}).UpdateProjectType)
			// 项目关注与取消关注
			project.POST("/user-focus", (&web.ProjectController{}).UserFocus)

			// 查询项目信息(项目卡片)
			project.GET("/card", (&web.ProjectController{}).Card)

			// 查询所有项目的eLearning对接情况（系统学习）
			project.GET("/user-project-elearning", (&web.ProjectController{}).GetUserProjectElearning)
			project.GET("/user-role-project-env", (&web.ProjectController{}).GetProjectEnvRole)
			project.GET("/notice", (&web.ProjectController{}).GetProjectNotice)
			project.POST("/notice", (&web.ProjectController{}).UpdateProjectNotice)
			project.GET("/getRoleNoticeUser", (&web.ProjectController{}).GetRoleNoticeUser)

			project.GET("/role/list", (&web.ProjectController{}).GetRoleList)
			project.GET("/role/user/list", (&web.ProjectController{}).GetRoleUserList)

			project.GET("/cohort", (&web.ProjectController{}).GetByCohort)

			project.GET("/time-zone", (&web.ProjectController{}).GetProjectTimeZone)

			project.GET("/copy/prod", (&web.ProjectController{}).GetCopyIsProd)
			project.GET("/copy/prod/data", (&web.ProjectController{}).GetCopyIsProdData)

			project.GET("/type", (&web.ProjectController{}).GetProjectType)

		}
	}

	{
		roles := api.Group("/roles")
		{
			roles.GET("", (&web.RoleController{}).Get)
			roles.POST("", (&web.RoleController{}).Post)
			roles.PATCH("", (&web.RoleController{}).Update)
			roles.DELETE("", (&web.RoleController{}).Delete)
			roles.GET("/role", (&web.RoleController{}).GetBy)
			roles.GET("/pool", (&web.RoleController{}).ListPool)
			roles.GET("/is-bind-role", (&web.RoleController{}).IsBindRole)
			roles.GET("/roleIsBind", (&web.RoleController{}).RoleIsBind)
		}
	}

	{
		projectsRoles := api.Group("/projects-roles")
		{
			projectsRoles.GET("", (&web.ProjectRoleController{}).Get)
			projectsRoles.POST("", (&web.ProjectRoleController{}).Post)
			projectsRoles.PATCH("", (&web.ProjectRoleController{}).Update)
			projectsRoles.DELETE("", (&web.ProjectRoleController{}).Delete)
			projectsRoles.GET("/role", (&web.ProjectRoleController{}).GetBy)
			projectsRoles.GET("/pool", (&web.ProjectRoleController{}).ListPool)
			projectsRoles.GET("/bind-user", (&web.ProjectRoleController{}).HasBindUser)
			projectsRoles.GET("/permission", (&web.ProjectRoleController{}).Permission)
		}
	}

	{
		projectSite := api.Group("/projects-sites")
		{
			projectSite.GET("", (&web.ProjectSiteController{}).GetProjectSiteById)
			projectSite.GET("/list", (&web.ProjectSiteController{}).List)
			projectSite.GET("/user-sites", (&web.ProjectSiteController{}).UserSites)
			projectSite.GET("/sites-and-storehouses", (&web.ProjectSiteController{}).GetSitesAndStorehouses)
			projectSite.GET("/get-user-sites-and-storehouses", (&web.ProjectSiteController{}).GetUserSitesAndStorehouses)
			projectSite.GET("/sites", (&web.ProjectSiteController{}).GetUserSites)
			projectSite.GET("/batch-sites", (&web.ProjectSiteController{}).GetBatchUserSites)
			projectSite.GET("/storehouses", (&web.ProjectSiteController{}).GetUserStorehouses)
			projectSite.GET("/batch-storehouses", (&web.ProjectSiteController{}).GetBatchUserStorehouses)
			projectSite.GET("/by/list", (&web.ProjectSiteController{}).SiteList)
			projectSite.POST("/add", (&web.ProjectSiteController{}).Add)
			projectSite.PATCH("/supply-plan", (&web.ProjectSiteController{}).UpdateSupplyPlan)
			projectSite.GET("/environments/store", (&web.ProjectSiteController{}).GetStoreListOption)
			projectSite.GET("/country", (&web.ProjectSiteController{}).GetCountry)
			projectSite.GET("/timeZone", (&web.ProjectSiteController{}).GetProjectSiteByTz)
			projectSite.GET("/batch-group", (&web.ProjectSiteController{}).GetBatchGroupNum)
		}
	}

	{
		medicineOrder := api.Group("/order")
		{
			medicineOrder.GET("/list", (&web.MedicineOrderController{}).GetOrderList)
			medicineOrder.GET("/subject", (&web.MedicineOrderController{}).GetSubjectGroup)
			medicineOrder.GET("/dtp/list", (&web.MedicineOrderController{}).GetDtpOrderList)
			medicineOrder.GET("", (&web.MedicineOrderController{}).GetOrder)
			medicineOrder.POST("", (&web.MedicineOrderController{}).AddOrder)
			medicineOrder.POST("/add-recovery-order", (&web.MedicineOrderController{}).AddRecoveryOrder)
			medicineOrder.PATCH("", (&web.MedicineOrderController{}).UpdateOrder)
			medicineOrder.PATCH("/update-recovery-order", (&web.MedicineOrderController{}).UpdateRecoveryOrder)
			medicineOrder.PATCH("/receive", (&web.MedicineOrderController{}).ReceiveOrder)
			medicineOrder.PATCH("/confirm", (&web.MedicineOrderController{}).ConfirmOrder)
			medicineOrder.PATCH("/recover-confirm", (&web.MedicineOrderController{}).RecoverConfirmOrder)
			medicineOrder.PATCH("/receive-recovery", (&web.MedicineOrderController{}).ReceiveRecoveryOrder)
			medicineOrder.GET("/download", (&web.MedicineOrderController{}).DownloadOrder)
			medicineOrder.GET("/dtp/download", (&web.MedicineOrderController{}).DownloadDtpOrder)
			medicineOrder.POST("/alarm-medicine", (&web.MedicineOrderController{}).AlarmMedicine)
			medicineOrder.PATCH("/change-medicines", (&web.MedicineOrderController{}).ChangeOrderMedicines)
			medicineOrder.GET("/change-records", (&web.MedicineOrderController{}).GetMedicineChangeRecord)

			medicineOrder.POST("update-expiration-single", (&web.MedicineOrderController{}).UpdateExpirationSingle)
			medicineOrder.POST("update-expiration-batch", (&web.MedicineOrderController{}).UpdateExpirationBatch)
		}
	}

	{
		projectStorehouse := api.Group("/projects-storehouses")
		{
			projectStorehouse.GET("/list", (&web.ProjectStorehouseController{}).List)
			projectStorehouse.GET("/list-medicine", (&web.ProjectStorehouseController{}).ListMedicine)
			projectStorehouse.GET("/user-storehouses", (&web.ProjectStorehouseController{}).UserStoreHouses)
			projectStorehouse.PATCH("/save", (&web.ProjectStorehouseController{}).Save)
			projectStorehouse.PATCH("/medicine-info", (&web.ProjectStorehouseController{}).SaveMedicineInfo)
			projectStorehouse.DELETE("", (&web.ProjectStorehouseController{}).Delete)
			projectStorehouse.GET("/query/one", (&web.ProjectStorehouseController{}).QueryOne)
			projectStorehouse.POST("/batch-group", (&web.ProjectStorehouseController{}).UpdateStoreBatchGroup)
			projectStorehouse.GET("/batch-group", (&web.ProjectStorehouseController{}).GetStoreBatchGroup)
		}
	}

	{
		simulateRandom := api.Group("/simulate-random")
		{
			simulateRandom.POST("", (&web.SimulateRandomController{}).Add)
			simulateRandom.PATCH("", (&web.SimulateRandomController{}).Update)
			simulateRandom.POST("/run", (&web.SimulateRandomController{}).Run)
			simulateRandom.GET("/list", (&web.SimulateRandomController{}).List)
			simulateRandom.GET("/detail", (&web.SimulateRandomController{}).GetDetail)
			simulateRandom.GET("/download", (&web.SimulateRandomController{}).DownloadList)
			simulateRandom.GET("/factor", (&web.SimulateRandomController{}).GetFactor)
		}
	}
	{
		subject := api.Group("subject")
		{
			subject.GET("/subject-number", (&web.SubjectController{}).GetSubjectNumber)
			subject.POST("/register/add", (&web.SubjectController{}).Add)
			subject.POST("/screen", (&web.SubjectController{}).Screen)
			subject.POST("/register/update", (&web.SubjectController{}).Update)
			subject.POST("/factor/random", (&web.SubjectController{}).FactorRandom)
			subject.POST("", (&web.SubjectController{}).GetList)
			subject.GET("/count", (&web.SubjectController{}).GetListCount)
			subject.GET("/list", (&web.SubjectController{}).GetAppList)
			subject.GET("/register", (&web.SubjectController{}).GetSubject)
			subject.DELETE("/register/delete", (&web.SubjectController{}).Delete)
			subject.POST("/register/random", (&web.SubjectController{}).SubjectRandom)
			subject.POST("/edc/verification", (&web.SubjectController{}).SubjectEdcVerification)
			subject.POST("/register/update/status", (&web.SubjectController{}).UpdateStatus)
			subject.POST("/register/replace", (&web.SubjectController{}).SubjectReplace)
			subject.POST("/register/replace/site", (&web.SubjectController{}).GetSubjectReplaceSite)
			subject.POST("/register/replace/factor", (&web.SubjectController{}).GetSubjectReplaceFactor)
			subject.POST("/register/replace/at/random", (&web.SubjectController{}).SubjectReplaceAtRandom)
			subject.POST("/transfer", (&web.SubjectController{}).Transfer)
			subject.GET("/register/replace/random-number", (&web.SubjectController{}).SubjectReplaceRandomNumber)
			subject.GET("/download-unblinding-data", (&web.SubjectController{}).DownloadUnblindingData)
			subject.GET("/download-random-data", (&web.SubjectController{}).DownloadRandomData)
			subject.POST("/urgent-unblinding-application", (&web.SubjectController{}).UrgentUnblindingApplication)
			subject.POST("/urgent-unblinding-approval", (&web.SubjectController{}).UrgentUnblindingApproval)
			subject.POST("/urgent-unblinding-application/resend-sms", (&web.SubjectController{}).ResendSms)
			subject.POST("/report", (&web.SubjectController{}).GetSubjectInfo)
			subject.GET("/reality-capacity", (&web.SubjectController{}).RealityCapacity)
			subject.POST("/join-time", (&web.SubjectController{}).UpdateSubjectJoinTime)
			subject.GET("/at/random/form", (&web.SubjectController{}).AtRandomForm)
			subject.POST("/actual-factor", (&web.SubjectController{}).UpdateActualFactor)
			subject.POST("/register/page", (&web.SubjectController{}).GetNewSubjectPage)
			subject.POST("/check-push-history", (&web.SubjectController{}).CheckPushHistory)
			subject.POST("/push-history", (&web.SubjectController{}).PushHistory)
			subject.GET("/list-invalid", (&web.SubjectController{}).GetInvalidList)
			subject.POST("/switch-cohort", (&web.SubjectController{}).SwitchCohort)
		}
	}

	{
		randomization := api.Group("/randomization")
		{
			randomization.GET("/region", (&web.RandomizationController{}).GetRegion)
			randomization.DELETE("/region", (&web.RandomizationController{}).DeleteRegion)
			randomization.POST("/region", (&web.RandomizationController{}).AddRegion)
			randomization.GET("/form", (&web.RandomizationController{}).GetForm)
			randomization.GET("/register/form", (&web.RandomizationController{}).GetRegisterForm)
			randomization.GET("/formula/form", (&web.RandomizationController{}).GetFormulaForm)
			randomization.DELETE("/form/fields", (&web.RandomizationController{}).DeleteForm)
			randomization.PATCH("/form/fields", (&web.RandomizationController{}).UpdateForm)
			randomization.POST("/form", (&web.RandomizationController{}).AddForm)
			randomization.GET("/attribute", (&web.RandomizationController{}).GetAttribute)
			randomization.GET("/attribute/randomization/type", (&web.RandomizationController{}).GetRandomizationType)
			randomization.GET("/attributes", (&web.RandomizationController{}).GetAttributes)
			randomization.GET("/attribute/list", (&web.RandomizationController{}).GetAttributeList)
			randomization.POST("/attribute", (&web.RandomizationController{}).UpdateAttribute)
			randomization.POST("/attribute/site/layered", (&web.RandomizationController{}).UpdateSiteLayered)
			randomization.GET("/configure", (&web.RandomizationController{}).GetRandomizationInfo)
			randomization.POST("/configure", (&web.RandomizationController{}).AddRandomizationInfo)
			randomization.PATCH("/configure", (&web.RandomizationController{}).UpdateRandomizationInfo)
			randomization.POST("/file", (&web.RandomizationController{}).UploadRandomizationFile)
			randomization.DELETE("/random-design/group", (&web.RandomizationController{}).DeleteRandomGroup)
			randomization.DELETE("/random-design/factor", (&web.RandomizationController{}).DeleteRandomFactor)
			randomization.POST("/random/generate", (&web.RandomizationController{}).GenerateRandomNumber)
			randomization.POST("/random/list/update", (&web.RandomizationController{}).UpdateRandomList)
			randomization.GET("/random/list", (&web.RandomizationController{}).GetRandomList)
			randomization.GET("/random/configure", (&web.RandomizationController{}).GetRandomListConfigure)
			randomization.POST("/blocks", (&web.RandomizationController{}).GetRandomNumberGroup)
			randomization.POST("/blocks/distribution-statistics", (&web.RandomizationController{}).GetGroupDistributionStatistics)
			randomization.GET("/random-list/factor/combination", (&web.RandomizationController{}).GetFactorCombination)
			randomization.PATCH("/customers/project/env/random/update/status", (&web.RandomizationController{}).UpdateRandomListStatus)
			randomization.PATCH("/customers/project/env/random/update/invalid", (&web.RandomizationController{}).UpdateRandomListInvalid)
			randomization.POST("/block/project-site", (&web.RandomizationController{}).GetBlock)
			randomization.GET("/project-site", (&web.RandomizationController{}).GetProjectSite)
			randomization.PATCH("/random-list", (&web.RandomizationController{}).UpdateProjectSite)
			randomization.GET("/template-file", (&web.RandomizationController{}).GetTemplateFile)
			randomization.PATCH("/random-list/factor", (&web.RandomizationController{}).UpdateFactorNumber)
			randomization.POST("/random-list/factor", (&web.RandomizationController{}).AddFactorNumber)
			randomization.DELETE("/random-list/delFactor", (&web.RandomizationController{}).DelFactorNumber)
			randomization.GET("/random/list/last", (&web.RandomizationController{}).GetLastGroup)
			randomization.GET("/random-list", (&web.RandomizationController{}).ExportRandomList)
			randomization.GET("/random-list/factor", (&web.RandomizationController{}).CalculateCombination)
			randomization.DELETE("/random-list/block", (&web.RandomizationController{}).CleanFactor)
			randomization.GET("/report", (&web.RandomizationController{}).DownloadReport)
			randomization.GET("/random-statistics/pie", (&web.RandomizationController{}).GetRandomStatisticsPie)
			randomization.GET("/random-statistics/bar", (&web.RandomizationController{}).GetRandomStatisticsBar)
			randomization.POST("/random-list/many", (&web.RandomizationController{}).GetRandomListMany)
			randomization.POST("/random-list/sync", (&web.RandomizationController{}).SyncRandomList)
			randomization.GET("/attribute/connect/ali", (&web.RandomizationController{}).GetAttributeConnectAli)
			randomization.GET("/attribute/connect", (&web.RandomizationController{}).GetAttributeConnect)
			randomization.GET("/form/type", (&web.RandomizationController{}).GetFormType)
			randomization.GET("/cohort/random", (&web.RandomizationController{}).GetRandomCohort)
			randomization.POST("/random-list/inactivate", (&web.RandomizationController{}).RandomNumberInactivate)
			randomization.POST("/random-list/blocks/activate-inactivate", (&web.RandomizationController{}).RandomNumberActivateInactivate)
		}
	}
	{
		site := api.Group("sites")
		{
			site.GET("/country", (&web.SiteController{}).GetCountry)
			site.GET("/config", (&web.SiteController{}).GetSiteConfig)
		}
	}
	{
		storehouse := api.Group("storehouses")
		{
			storehouse.PATCH("", (&web.StorehouseController{}).Update)
			storehouse.DELETE("", (&web.StorehouseController{}).Delete)
			storehouse.GET("", (&web.StorehouseController{}).Get)
			storehouse.POST("", (&web.StorehouseController{}).Add)
			storehouse.GET("/all", (&web.StorehouseController{}).GetAll)
		}
	}
	{
		supplyPlan := api.Group("/supply-plan")
		{
			supplyPlan.PATCH("", (&web.SupplyPlanController{}).UpdateSupplyPlan)
			supplyPlan.DELETE("/", (&web.SupplyPlanController{}).DeleteSupplyPlan)
			supplyPlan.GET("/", (&web.SupplyPlanController{}).SupplyPlanList)
			supplyPlan.POST("/add", (&web.SupplyPlanController{}).AddSupplyPlan)
			supplyPlan.PATCH("/medicine", (&web.SupplyPlanController{}).UpdateSupplyPlanMedicine)
			supplyPlan.DELETE("/medicine", (&web.SupplyPlanController{}).DeleteSupplyPlanMedicine)
			supplyPlan.GET("/medicine", (&web.SupplyPlanController{}).SupplyPlanMedicineList)
			supplyPlan.POST("/medicine/add", (&web.SupplyPlanController{}).AddSupplyPlanMedicine)
			supplyPlan.GET("/site/storehouse", (&web.SupplyPlanController{}).GetSiteAndStorehouse)
			supplyPlan.GET("/medicine-configures", (&web.SupplyPlanController{}).GetMedicineList)
			supplyPlan.GET("/medicine-configures/customer/project/env/site", (&web.SupplyPlanController{}).GetMedicineConfigures)
			supplyPlan.GET("/detail", (&web.SupplyPlanController{}).GetSupplyPlan)
			supplyPlan.POST("/check-site", (&web.SupplyPlanController{}).CheckSite)
			supplyPlan.GET("/site", (&web.SupplyPlanController{}).GetApplicableSiteSupplyPlan)
			supplyPlan.POST("/getSupplyIdCount", (&web.SupplyPlanController{}).GetSupplyCountById)

			supplyPlan.POST("/medicine/batch", (&web.SupplyPlanController{}).BatchSupplyPlanMedicines)
		}
	}
	{
		approvalProcess := api.Group("/approvalProcess")
		{
			approvalProcess.GET("/list", (&web.ApprovalProcessController{}).GetApprovalProcessList)
			approvalProcess.GET("/get-by-id", (&web.ApprovalProcessController{}).GetApprovalProcessDetail)
			approvalProcess.PATCH("/update", (&web.ApprovalProcessController{}).UpdateApprovalProcess)
		}
	}
	{
		medicines := api.Group("medicines")
		{
			medicines.DELETE("", (&web.MedicineController{}).DeleteData)
			medicines.GET("/template-file", (&web.MedicineController{}).GetTemplateFile)
			medicines.GET("/packlist-template-file", (&web.MedicineController{}).GetPacklistTemplateFile)
			medicines.PATCH("/update-medicines", (&web.MedicineController{}).UpdateMedicines)
			medicines.GET("/download-data", (&web.MedicineController{}).DownloadData)
			medicines.GET("/batch", (&web.MedicineController{}).GetBatchList)
			medicines.GET("/batch/group/status", (&web.MedicineController{}).GetBatchGroupStatus)
			medicines.PATCH("batch/group/mult", (&web.MedicineController{}).GetBatchGroupStatusMult)
			medicines.POST("/medicine-batch", (&web.MedicineController{}).GetMedicineBatchList)
			medicines.GET("/medicine-freeze", (&web.MedicineController{}).GetMedicineFreezeList)
			medicines.GET("/medicine-freeze-details", (&web.MedicineController{}).GetMedicineFreezeDetailsList)
			medicines.POST("/storehouse-sku", (&web.MedicineController{}).GetMedicineStorehouseSku)
			medicines.GET("/download-storehouse-sku", (&web.MedicineController{}).DownloadStorehouseSku)
			medicines.POST("/site-sku", (&web.MedicineController{}).GetMedicineSiteSku)
			medicines.GET("/dtp-sku", (&web.MedicineController{}).GetMedicineDtpSku)
			medicines.GET("/download-site-sku", (&web.MedicineController{}).DownloadSiteSku)
			medicines.GET("/download-dtp-sku", (&web.MedicineController{}).DownloadDtpSku)
			medicines.POST("/site-other-sku", (&web.MedicineController{}).GetOtherMedicineSiteSku)
			medicines.POST("/storehouse-other-sku", (&web.MedicineController{}).GetOtherMedicineStorehouseSku)
			medicines.GET("/all-medicine-storehouse-stat", (&web.MedicineController{}).GetAllMedicineStorehouseSTAT)
			medicines.GET("/all-medicine-site-stat", (&web.MedicineController{}).GetAllMedicineSiteSTAT)
			medicines.GET("/site-other-sku-dtp", (&web.MedicineController{}).GetOtherMedicineSiteSkuWithDTP)
			medicines.GET("", (&web.MedicineController{}).GetMedicineList)
			medicines.POST("/upload", (&web.MedicineController{}).UploadMedicines)
			medicines.POST("/upload-packlist", (&web.MedicineController{}).UploadMedicinesPacklist)
			medicines.POST("/medicine-summary-storehouse", (&web.MedicineController{}).GetSummaryStorehouse)
			medicines.POST("/medicine-summary-site", (&web.MedicineController{}).GetSummarySite)
			medicines.POST("/site-medicine", (&web.MedicineController{}).GetSiteMedicineList)
			medicines.PATCH("/update-batch", (&web.MedicineController{}).UpdateBatch)
			medicines.PATCH("/freeze-medicines", (&web.MedicineController{}).FreezeMedicines)
			medicines.PATCH("/freeze-other-medicines", (&web.MedicineController{}).FreezeOtherMedicines)
			medicines.PATCH("/other-medicines/update", (&web.MedicineController{}).UpdateOtherMedicine)
			medicines.GET("/other/count", (&web.MedicineController{}).GetOtherMedicineCount)
			medicines.PATCH("/release-medicines", (&web.MedicineController{}).ReleaseMedicines)
			medicines.PATCH("/medicines-for-order", (&web.MedicineController{}).GetMedicineForOrder)
			medicines.GET("/medicines-for-order-group", (&web.MedicineController{}).GetMedicineForOrderGroup)
			medicines.PATCH("/medicines-for-order-group-all", (&web.MedicineController{}).GetMedicineForOrderGroupAll)
			medicines.PATCH("/medicine-freeze-by-ids", (&web.MedicineController{}).GetMedicineFreezeByIds)
			medicines.GET("/site-forecast", (&web.MedicineController{}).SiteForecast)
			medicines.GET("/group/name-data-number", (&web.MedicineController{}).GetGroupNameDataNumber)
			medicines.PATCH("/to/examine/flow/path", (&web.MedicineController{}).ToExamineFlowPath)
			medicines.GET("/get/medicine/name", (&web.MedicineController{}).GetMedicineName)

			//批次管理选中研究产品(不包含未编号)
			medicines.POST("/batch/select", (&web.MedicineController{}).GetBatchSelectList)
		}
	}

	{
		workTasks := api.Group("/work-tasks")
		{
			workTasks.GET("/list", (&web.WorkTaskController{}).List)
			workTasks.GET("", (&web.WorkTaskController{}).Detail)
			workTasks.POST("", (&web.WorkTaskController{}).Add)
			workTasks.PATCH("", (&web.WorkTaskController{}).Update)
			workTasks.PATCH("/scan-code/confirm", (&web.WorkTaskController{}).ScanCodeConfirm)
			workTasks.PATCH("/package-scan-code/confirm", (&web.WorkTaskController{}).PackageScanConfirm)
			workTasks.GET("/scan-code/medicines", (&web.WorkTaskController{}).ScanMedicineInfo)
			workTasks.GET("/scan-code/package-number", (&web.WorkTaskController{}).ScanPackageNumberInfo)
			workTasks.PATCH("/scan-code/confirm/warehousing", (&web.WorkTaskController{}).ScanCodeWarehousing)
			workTasks.GET("/getAllDrugs", (&web.DrugConfigureController{}).GetDrugNamesApp)
			workTasks.GET("/getDrugs/by/roleId", (&web.DrugConfigureController{}).GetAppDrugNamesByRole)
			workTasks.GET("/order/projectInfo", (&web.UserController{}).GetProjectInfos)
			workTasks.GET("/subject/projectInfo", (&web.UserController{}).GetSubjectProjectInfos)
			workTasks.PATCH("/order/confirm", (&web.MedicineOrderController{}).ConfirmAppOrder)
			workTasks.PATCH("/order/update", (&web.MedicineOrderController{}).UpdateAppOrder)
			workTasks.PATCH("/order/receive", (&web.MedicineOrderController{}).ReceiveAppOrder)
			workTasks.PATCH("/order/approval", (&web.ApprovalProcessController{}).UpdateAppApprovalProcess)
			workTasks.POST("/unblinding/approval", (&web.SubjectController{}).UrgentUnblindingApproval)
			workTasks.GET("/query/list", (&web.WorkTaskController{}).QueryList)
		}
	}
	{
		appTaskNotice := api.Group("/app-task-notice")
		{
			appTaskNotice.GET("/overview", (&web.AppTaskNoticeController{}).Overview)
			appTaskNotice.GET("/list", (&web.AppTaskNoticeController{}).List)
			appTaskNotice.PATCH("/update", (&web.AppTaskNoticeController{}).Update)
		}
	}
	{
		aliCallback := api.Group("/ali-callback")
		{
			aliCallback.POST("/msg/unblinded-approval", (&web.AliMsgController{}).UnblindedApprovalMsg)
		}
	}
	{
		env := api.Group("/envs")
		{
			env.GET("/cloud", (&web.EnvController{}).GetCloudURL)
		}
	}
	{
		menus := api.Group("/menus-permissions")
		{
			menus.GET("", (&web.MenuPermissionController{}).All)
			menus.GET("/excel", (&web.MenuPermissionController{}).ExportExcel)
		}
	}
	{
		dynamics := api.Group("/project-dynamics")
		{
			dynamics.GET("", (&web.ProjectDynamicsController{}).All)
			dynamics.GET("/analysis", (&web.ProjectDynamicsController{}).Analysis)
			dynamics.GET("/subject-statistics", (&web.ProjectDynamicsController{}).SubjectStatistics)
			dynamics.GET("/subject-site-statistics", (&web.ProjectDynamicsController{}).SubjectSiteStatistics)
		}
	}
	{
		push := api.Group("/push")
		{
			push.GET("/", (&web.PushController{}).PushList)
			push.PATCH("/", (&web.PushController{}).PushSend)
			push.GET("/details", (&web.PushController{}).Details)
		}
	}

	edcApi := app.Group("/edc-api")
	edcApi.Use(middleware.EdcAuthentication)
	edcApi.Use(tools.Logger())

	{
		remoteSubject := edcApi.Group("/remote/subject")
		{
			remoteSubject.POST("/register", (&web.RemoteSubjectController{}).Register)
			remoteSubject.POST("/random", (&web.RemoteSubjectController{}).Random)
			remoteSubject.POST("/siteSynchronization", (&web.RemoteSubjectController{}).SiteSynchronization)
			remoteSubject.POST("/updateSubjectNo", (&web.RemoteSubjectController{}).UpdateSubjectNo)
		}
		remoteDispensing := edcApi.Group("/remote/drug")
		{
			remoteDispensing.POST("/dispensing", (&web.RemoteDispensingController{}).Dispensing)
		}
		remoteForm := edcApi.Group("/remote/field")
		{
			remoteForm.POST("/fieldList", (&web.RemoteFieldController{}).FieldList)
		}
		pushForm := edcApi.Group("/remote/push")
		{
			pushForm.POST("/update", (&web.PushController{}).Update)
		}
		remoteReport := edcApi.Group("/remote/report")
		{
			remoteReport.POST("/data", (&web.RemoteReportController{}).GetReportData)
		}
	}
	{
		operationLog := api.Group("/operation")
		{
			operationLog.GET("", (&web.OperationLogController{}).GetOperationList)
		}
	}

	openApi := app.Group("/open-api")
	openApi.Use(tools.Logger())
	{
		openApi.GET("/site", (&web.RemoteSiteController{}).SiteList)
		openApi.GET("/pi", (&web.RemoteSiteController{}).ResearcherInfo)
	}

	wmsApi := app.Group("/logistics-api")
	wmsApi.Use(middleware.LogisticsAuthentication)
	wmsApi.Use(tools.Logger())
	{
		wmsShipmentConfirm := wmsApi.Group("/order")
		{
			wmsShipmentConfirm.POST("/transport", (&web.WmsController{}).UpdateShipmentConfirm)
		}
	}

	report := api.Group("/reports")
	{
		report.GET("", (&web.ReportController{}).GetReports)
		report.POST("/export", (&web.ReportController{}).Export)

		template := report.Group("/templates")
		template.GET("", (&web.ReportController{}).GetTemplate)
		template.POST("", (&web.ReportController{}).AddTemplate)
		template.PATCH("", (&web.ReportController{}).UpdateTemplate)
		template.DELETE("", (&web.ReportController{}).DeleteTemplate)
		template.POST("/copy", (&web.ReportController{}).CopyTemplate)

		history := report.Group("/history")
		history.GET("", (&web.ReportController{}).GetHistory)
		history.GET("/download", (&web.ReportController{}).DownloadHistory)

		report.GET("/export-authority", (&web.ReportController{}).ExportAuthority)
		report.GET("/get-randomization-simulation", (&web.ReportController{}).ExportRandomizationSimulation)

		report.POST("/export/configure/pdf", (&web.ReportController{}).ExportConfigurePdf)

		report.POST("/export/configure/report", (&web.ReportController{}).ExportConfigureReport)

		report.POST("/export/subject/pdf", (&web.ReportController{}).ExportSubjectPdf)

		report.POST("/export/subject/report", (&web.ReportController{}).ExportSubjectReport)

		report.POST("/export/simulate-random/pdf", (&web.ReportController{}).ExportSimulateRandomPdf)

		report.POST("/export/simulate-random/report", (&web.ReportController{}).ExportSimulateRandomReport)

	}

	multiLanguage := api.Group("/multiLanguage")
	{
		multiLanguage.GET("/name", (&web.MultiLanguageController{}).GetName)
		multiLanguage.GET("/list", (&web.MultiLanguageController{}).GetList)
		multiLanguage.POST("/add", (&web.MultiLanguageController{}).Add)
		multiLanguage.POST("/update", (&web.MultiLanguageController{}).Update)
		multiLanguage.DELETE("/delete", (&web.MultiLanguageController{}).Delete)
		multiLanguage.POST("/translate/list", (&web.MultiLanguageController{}).GetTranslateList)
		multiLanguage.POST("/translate/map", (&web.MultiLanguageController{}).GetTranslateMap)
		multiLanguage.POST("/translate/update", (&web.MultiLanguageController{}).UpdateTranslate)
		multiLanguage.GET("/history", (&web.MultiLanguageController{}).GetHistoryList)
		multiLanguage.POST("/upload", (&web.MultiLanguageController{}).UploadTranslate)
	}

	rave := api.Group("/rave")
	{
		rave.POST("/add-subject", (&web.RaveController{}).CreateRaveSubject)
		rave.POST("/update-subject", (&web.RaveController{}).UpdateRaveSubject)

		rave.GET("/get-env-url", (&web.RaveController{}).GetEnvUrl)
		rave.GET("/get-cohort-visit-randomization", (&web.RaveController{}).GetCohortVisitRandomization)
	}
	logistics := api.Group("/logistics")
	{
		logistics.GET("", (&web.LogisticsController{}).GetLogistics)
		logistics.GET("/company-code", (&web.LogisticsController{}).GetLogisticsCompanyCode)
		logistics.GET("/company/code", (&web.LogisticsController{}).GetCompanyCode)
	}

	alihealthApi := app.Group("/ali-health-api")
	alihealthApi.Use(tools.Logger())
	{
		alihealth := alihealthApi.Group("/alihealth")
		{
			alihealth.POST("/", (&web.AliTraceabilityController{}).AliHealth)
			alihealth.POST("/bound", (&web.AliTraceabilityController{}).Bound)
			alihealth.POST("/code", (&web.AliTraceabilityController{}).GetCode)
			alihealth.POST("/patient", (&web.AliTraceabilityController{}).GetPatient)
			alihealth.POST("/code-status", (&web.AliTraceabilityController{}).CodeStatus)
			alihealth.POST("/transfer", (&web.AliTraceabilityController{}).Transfer)
			alihealth.POST("/in-stock-status", (&web.AliTraceabilityController{}).InStockStatus)
			alihealth.POST("/project-update", (&web.AliTraceabilityController{}).ProjectUpdate)
			alihealth.POST("/refill-medicine", (&web.AliTraceabilityController{}).RefillMedicine)
			alihealth.POST("/back-in", (&web.AliTraceabilityController{}).BackIn)

		}
	}

	cttqApi := app.Group("/cttq-api")
	cttqApi.Use(middleware.CttqAuthentication)
	cttqApi.Use(tools.Logger())
	{
		cttq := cttqApi.Group("/cttq")
		{
			cttq.GET("/get-subject-dispensing", (&web.CttqController{}).GetSubjectDispensing)
		}
	}

}
