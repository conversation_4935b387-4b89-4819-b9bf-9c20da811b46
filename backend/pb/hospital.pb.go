// protoc --go_out=. --go-grpc_out=. protos/*.proto
// protoc-go-inject-tag -input ./pb/*.pb.go    // 这条命令在上面那条之后执行

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.1
// source: protos/hospital.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HospitalList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*HospitalData `protobuf:"bytes,1,rep,name=data,proto3" json:"data"` // @gotags: json:"data"
}

func (x *HospitalList) Reset() {
	*x = HospitalList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_hospital_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HospitalList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HospitalList) ProtoMessage() {}

func (x *HospitalList) ProtoReflect() protoreflect.Message {
	mi := &file_protos_hospital_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HospitalList.ProtoReflect.Descriptor instead.
func (*HospitalList) Descriptor() ([]byte, []int) {
	return file_protos_hospital_proto_rawDescGZIP(), []int{0}
}

func (x *HospitalList) GetData() []*HospitalData {
	if x != nil {
		return x.Data
	}
	return nil
}

type HospitalPage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count int64           `protobuf:"varint,1,opt,name=count,proto3" json:"count"` // @gotags: json:"count"
	Items []*HospitalData `protobuf:"bytes,2,rep,name=items,proto3" json:"items"`  // @gotags: json:"items"
}

func (x *HospitalPage) Reset() {
	*x = HospitalPage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_hospital_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HospitalPage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HospitalPage) ProtoMessage() {}

func (x *HospitalPage) ProtoReflect() protoreflect.Message {
	mi := &file_protos_hospital_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HospitalPage.ProtoReflect.Descriptor instead.
func (*HospitalPage) Descriptor() ([]byte, []int) {
	return file_protos_hospital_proto_rawDescGZIP(), []int{1}
}

func (x *HospitalPage) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *HospitalPage) GetItems() []*HospitalData {
	if x != nil {
		return x.Items
	}
	return nil
}

type ListHospitalsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Start        int64  `protobuf:"varint,1,opt,name=start,proto3" json:"start,omitempty"`
	Limit        int64  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	HospitalName string `protobuf:"bytes,3,opt,name=hospitalName,proto3" json:"hospitalName,omitempty"`
	WithDisabled bool   `protobuf:"varint,4,opt,name=withDisabled,proto3" json:"withDisabled,omitempty"`
}

func (x *ListHospitalsRequest) Reset() {
	*x = ListHospitalsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_hospital_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListHospitalsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHospitalsRequest) ProtoMessage() {}

func (x *ListHospitalsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_hospital_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHospitalsRequest.ProtoReflect.Descriptor instead.
func (*ListHospitalsRequest) Descriptor() ([]byte, []int) {
	return file_protos_hospital_proto_rawDescGZIP(), []int{2}
}

func (x *ListHospitalsRequest) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *ListHospitalsRequest) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListHospitalsRequest) GetHospitalName() string {
	if x != nil {
		return x.HospitalName
	}
	return ""
}

func (x *ListHospitalsRequest) GetWithDisabled() bool {
	if x != nil {
		return x.WithDisabled
	}
	return false
}

type SearchHospitalNamesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HospitalName string `protobuf:"bytes,1,opt,name=hospitalName,proto3" json:"hospitalName,omitempty"`
}

func (x *SearchHospitalNamesRequest) Reset() {
	*x = SearchHospitalNamesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_hospital_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchHospitalNamesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchHospitalNamesRequest) ProtoMessage() {}

func (x *SearchHospitalNamesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_hospital_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchHospitalNamesRequest.ProtoReflect.Descriptor instead.
func (*SearchHospitalNamesRequest) Descriptor() ([]byte, []int) {
	return file_protos_hospital_proto_rawDescGZIP(), []int{3}
}

func (x *SearchHospitalNamesRequest) GetHospitalName() string {
	if x != nil {
		return x.HospitalName
	}
	return ""
}

type SearchHospitalNamesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result []*HospitalNameData `protobuf:"bytes,1,rep,name=result,proto3" json:"result"` // @gotags: json:"result"
}

func (x *SearchHospitalNamesResult) Reset() {
	*x = SearchHospitalNamesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_hospital_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchHospitalNamesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchHospitalNamesResult) ProtoMessage() {}

func (x *SearchHospitalNamesResult) ProtoReflect() protoreflect.Message {
	mi := &file_protos_hospital_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchHospitalNamesResult.ProtoReflect.Descriptor instead.
func (*SearchHospitalNamesResult) Descriptor() ([]byte, []int) {
	return file_protos_hospital_proto_rawDescGZIP(), []int{4}
}

func (x *SearchHospitalNamesResult) GetResult() []*HospitalNameData {
	if x != nil {
		return x.Result
	}
	return nil
}

type HospitalNameData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HospitalNames  []string `protobuf:"bytes,1,rep,name=hospitalNames,proto3" json:"hospitalNames"`   // @gotags: json:"hospitalNames"
	HospitalNameEN string   `protobuf:"bytes,2,opt,name=hospitalNameEN,proto3" json:"hospitalNameEN"` // @gotags: json:"hospitalNameEN"
}

func (x *HospitalNameData) Reset() {
	*x = HospitalNameData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_hospital_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HospitalNameData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HospitalNameData) ProtoMessage() {}

func (x *HospitalNameData) ProtoReflect() protoreflect.Message {
	mi := &file_protos_hospital_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HospitalNameData.ProtoReflect.Descriptor instead.
func (*HospitalNameData) Descriptor() ([]byte, []int) {
	return file_protos_hospital_proto_rawDescGZIP(), []int{5}
}

func (x *HospitalNameData) GetHospitalNames() []string {
	if x != nil {
		return x.HospitalNames
	}
	return nil
}

func (x *HospitalNameData) GetHospitalNameEN() string {
	if x != nil {
		return x.HospitalNameEN
	}
	return ""
}

type SearchDoctorsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HospitalName string `protobuf:"bytes,1,opt,name=hospitalName,proto3" json:"hospitalName,omitempty"`
	DoctorName   string `protobuf:"bytes,2,opt,name=doctorName,proto3" json:"doctorName,omitempty"`
}

func (x *SearchDoctorsRequest) Reset() {
	*x = SearchDoctorsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_hospital_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchDoctorsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchDoctorsRequest) ProtoMessage() {}

func (x *SearchDoctorsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_hospital_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchDoctorsRequest.ProtoReflect.Descriptor instead.
func (*SearchDoctorsRequest) Descriptor() ([]byte, []int) {
	return file_protos_hospital_proto_rawDescGZIP(), []int{6}
}

func (x *SearchDoctorsRequest) GetHospitalName() string {
	if x != nil {
		return x.HospitalName
	}
	return ""
}

func (x *SearchDoctorsRequest) GetDoctorName() string {
	if x != nil {
		return x.DoctorName
	}
	return ""
}

type SearchDoctorsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result []*DoctorData `protobuf:"bytes,1,rep,name=result,proto3" json:"result"` // @gotags: json:"result"
}

func (x *SearchDoctorsResult) Reset() {
	*x = SearchDoctorsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_hospital_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchDoctorsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchDoctorsResult) ProtoMessage() {}

func (x *SearchDoctorsResult) ProtoReflect() protoreflect.Message {
	mi := &file_protos_hospital_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchDoctorsResult.ProtoReflect.Descriptor instead.
func (*SearchDoctorsResult) Descriptor() ([]byte, []int) {
	return file_protos_hospital_proto_rawDescGZIP(), []int{7}
}

func (x *SearchDoctorsResult) GetResult() []*DoctorData {
	if x != nil {
		return x.Result
	}
	return nil
}

type HospitalData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             string            `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`                         // @gotags: json:"id"
	HospitalNames  []string          `protobuf:"bytes,2,rep,name=hospitalNames,proto3" json:"hospitalNames"`   // @gotags: json:"hospitalNames"
	HospitalNameEN string            `protobuf:"bytes,3,opt,name=hospitalNameEN,proto3" json:"hospitalNameEN"` // @gotags: json:"hospitalNameEN"
	Country        string            `protobuf:"bytes,4,opt,name=country,proto3" json:"country"`               // @gotags: json:"country"
	Province       string            `protobuf:"bytes,5,opt,name=province,proto3" json:"province"`             // @gotags: json:"province"
	City           string            `protobuf:"bytes,6,opt,name=city,proto3" json:"city"`                     // @gotags: json:"city"
	Timezone       string            `protobuf:"bytes,7,opt,name=timezone,proto3" json:"timezone"`             // @gotags: json:"timezone"
	Departments    []*DepartmentData `protobuf:"bytes,8,rep,name=departments,proto3" json:"departments"`       // @gotags: json:"departments"
	CountryCode    []string          `protobuf:"bytes,9,rep,name=countryCode,proto3" json:"countryCode"`       // @gotags: json:"countryCode"
	Postcode       string            `protobuf:"bytes,10,opt,name=postcode,proto3" json:"postcode"`            // @gotags: json:"postcode"
	Address        string            `protobuf:"bytes,11,opt,name=address,proto3" json:"address"`              // @gotags: json:"address"
	Disabled       bool              `protobuf:"varint,12,opt,name=disabled,proto3" json:"disabled,omitempty"`
	IsBeiAnSite    bool              `protobuf:"varint,13,opt,name=isBeiAnSite,proto3" json:"isBeiAnSite"` // @gotags: json:"isBeiAnSite"
	Type           string            `protobuf:"bytes,14,opt,name=type,proto3" json:"type"`                // @gotags: json:"type"
	Tz             string            `protobuf:"bytes,15,opt,name=tz,proto3" json:"tz"`                    // @gotags: json:"tz"
}

func (x *HospitalData) Reset() {
	*x = HospitalData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_hospital_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HospitalData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HospitalData) ProtoMessage() {}

func (x *HospitalData) ProtoReflect() protoreflect.Message {
	mi := &file_protos_hospital_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HospitalData.ProtoReflect.Descriptor instead.
func (*HospitalData) Descriptor() ([]byte, []int) {
	return file_protos_hospital_proto_rawDescGZIP(), []int{8}
}

func (x *HospitalData) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *HospitalData) GetHospitalNames() []string {
	if x != nil {
		return x.HospitalNames
	}
	return nil
}

func (x *HospitalData) GetHospitalNameEN() string {
	if x != nil {
		return x.HospitalNameEN
	}
	return ""
}

func (x *HospitalData) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *HospitalData) GetProvince() string {
	if x != nil {
		return x.Province
	}
	return ""
}

func (x *HospitalData) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *HospitalData) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

func (x *HospitalData) GetDepartments() []*DepartmentData {
	if x != nil {
		return x.Departments
	}
	return nil
}

func (x *HospitalData) GetCountryCode() []string {
	if x != nil {
		return x.CountryCode
	}
	return nil
}

func (x *HospitalData) GetPostcode() string {
	if x != nil {
		return x.Postcode
	}
	return ""
}

func (x *HospitalData) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *HospitalData) GetDisabled() bool {
	if x != nil {
		return x.Disabled
	}
	return false
}

func (x *HospitalData) GetIsBeiAnSite() bool {
	if x != nil {
		return x.IsBeiAnSite
	}
	return false
}

func (x *HospitalData) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *HospitalData) GetTz() string {
	if x != nil {
		return x.Tz
	}
	return ""
}

type DepartmentData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DepartmentName string        `protobuf:"bytes,1,opt,name=departmentName,proto3" json:"departmentName"` // @gotags: json:"departmentName"
	Doctors        []*DoctorData `protobuf:"bytes,2,rep,name=doctors,proto3" json:"doctors"`               // @gotags: json:"doctors"
	Id             string        `protobuf:"bytes,3,opt,name=id,proto3" json:"id"`                         // @gotags: json:"id"
}

func (x *DepartmentData) Reset() {
	*x = DepartmentData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_hospital_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepartmentData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepartmentData) ProtoMessage() {}

func (x *DepartmentData) ProtoReflect() protoreflect.Message {
	mi := &file_protos_hospital_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepartmentData.ProtoReflect.Descriptor instead.
func (*DepartmentData) Descriptor() ([]byte, []int) {
	return file_protos_hospital_proto_rawDescGZIP(), []int{9}
}

func (x *DepartmentData) GetDepartmentName() string {
	if x != nil {
		return x.DepartmentName
	}
	return ""
}

func (x *DepartmentData) GetDoctors() []*DoctorData {
	if x != nil {
		return x.Doctors
	}
	return nil
}

func (x *DepartmentData) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DoctorData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DoctorName     string `protobuf:"bytes,1,opt,name=doctorName,proto3" json:"doctorName"`         // @gotags: json:"doctorName"
	DepartmentName string `protobuf:"bytes,2,opt,name=departmentName,proto3" json:"departmentName,omitempty"` // 一些接口的返回值会把科室名称加上，这里就不加 @gotags 了
	Email          string `protobuf:"bytes,3,opt,name=email,proto3" json:"email"`                   // @gotags: json:"email"
	Mobile         string `protobuf:"bytes,4,opt,name=mobile,proto3" json:"mobile"`                 // @gotags: json:"mobile"
	Id             string `protobuf:"bytes,5,opt,name=id,proto3" json:"id"`                         // @gotags: json:"id"
	DepartmentId   string `protobuf:"bytes,6,opt,name=departmentId,proto3" json:"departmentId,omitempty"`     // 一些接口的返回值会把科室id加上，这里就不加 @gotags 了
}

func (x *DoctorData) Reset() {
	*x = DoctorData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_hospital_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoctorData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoctorData) ProtoMessage() {}

func (x *DoctorData) ProtoReflect() protoreflect.Message {
	mi := &file_protos_hospital_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoctorData.ProtoReflect.Descriptor instead.
func (*DoctorData) Descriptor() ([]byte, []int) {
	return file_protos_hospital_proto_rawDescGZIP(), []int{10}
}

func (x *DoctorData) GetDoctorName() string {
	if x != nil {
		return x.DoctorName
	}
	return ""
}

func (x *DoctorData) GetDepartmentName() string {
	if x != nil {
		return x.DepartmentName
	}
	return ""
}

func (x *DoctorData) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *DoctorData) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *DoctorData) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DoctorData) GetDepartmentId() string {
	if x != nil {
		return x.DepartmentId
	}
	return ""
}

var File_protos_hospital_proto protoreflect.FileDescriptor

var file_protos_hospital_proto_rawDesc = []byte{
	0x0a, 0x15, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x68, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61,
	0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x31, 0x0a, 0x0c, 0x48, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x48, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x49, 0x0a, 0x0c, 0x48, 0x6f, 0x73, 0x70, 0x69,
	0x74, 0x61, 0x6c, 0x50, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x48,
	0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x22, 0x8a, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x6f, 0x73, 0x70, 0x69,
	0x74, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x68, 0x6f, 0x73, 0x70, 0x69,
	0x74, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x68,
	0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x77,
	0x69, 0x74, 0x68, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0c, 0x77, 0x69, 0x74, 0x68, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22,
	0x40, 0x0a, 0x1a, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x48, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61,
	0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a,
	0x0c, 0x68, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x68, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0x46, 0x0a, 0x19, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x48, 0x6f, 0x73, 0x70, 0x69,
	0x74, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x29,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x48, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x60, 0x0a, 0x10, 0x48, 0x6f, 0x73,
	0x70, 0x69, 0x74, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x24, 0x0a,
	0x0d, 0x68, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x68, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x68, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x45, 0x4e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x68, 0x6f, 0x73,
	0x70, 0x69, 0x74, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x45, 0x4e, 0x22, 0x5a, 0x0a, 0x14, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x44, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x68, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x68, 0x6f, 0x73, 0x70, 0x69,
	0x74, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x6f, 0x63, 0x74, 0x6f,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63,
	0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x3a, 0x0a, 0x13, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x44, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x23,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x44, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0xbf, 0x03, 0x0a, 0x0c, 0x48, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x68, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x68, 0x6f, 0x73,
	0x70, 0x69, 0x74, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x68, 0x6f,
	0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x45, 0x4e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x68, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x45, 0x4e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x1a, 0x0a, 0x08,
	0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x31, 0x0a, 0x0b, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0b,
	0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x6f, 0x73, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x6f, 0x73, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12,
	0x20, 0x0a, 0x0b, 0x69, 0x73, 0x42, 0x65, 0x69, 0x41, 0x6e, 0x53, 0x69, 0x74, 0x65, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x42, 0x65, 0x69, 0x41, 0x6e, 0x53, 0x69, 0x74,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x7a, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x74, 0x7a, 0x22, 0x6f, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x25, 0x0a, 0x07, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x44, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x07, 0x64,
	0x6f, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0xb6, 0x01, 0x0a, 0x0a, 0x44, 0x6f, 0x63, 0x74, 0x6f,
	0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63, 0x74, 0x6f,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64,
	0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x64,
	0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x32,
	0xc7, 0x02, 0x0a, 0x08, 0x48, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x12, 0x39, 0x0a, 0x10,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x48, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x73,
	0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x0d, 0x2e, 0x48, 0x6f, 0x73, 0x70, 0x69,
	0x74, 0x61, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x48,
	0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x73, 0x12, 0x15, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x48,
	0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x0d, 0x2e, 0x48, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x67, 0x65, 0x12, 0x4e,
	0x0a, 0x13, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x48, 0x6f, 0x73, 0x70, 0x69, 0x74, 0x61, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x1b, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x48, 0x6f,
	0x73, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x48, 0x6f, 0x73, 0x70, 0x69,
	0x74, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3c,
	0x0a, 0x0d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x44, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x12,
	0x15, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x44, 0x6f, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x44,
	0x6f, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3b, 0x0a, 0x13,
	0x4d, 0x75, 0x6c, 0x74, 0x69, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x48, 0x6f, 0x73, 0x70, 0x69,
	0x74, 0x61, 0x6c, 0x12, 0x15, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x6f, 0x73, 0x70, 0x69, 0x74,
	0x61, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0d, 0x2e, 0x48, 0x6f, 0x73,
	0x70, 0x69, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x67, 0x65, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_protos_hospital_proto_rawDescOnce sync.Once
	file_protos_hospital_proto_rawDescData = file_protos_hospital_proto_rawDesc
)

func file_protos_hospital_proto_rawDescGZIP() []byte {
	file_protos_hospital_proto_rawDescOnce.Do(func() {
		file_protos_hospital_proto_rawDescData = protoimpl.X.CompressGZIP(file_protos_hospital_proto_rawDescData)
	})
	return file_protos_hospital_proto_rawDescData
}

var file_protos_hospital_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_protos_hospital_proto_goTypes = []interface{}{
	(*HospitalList)(nil),               // 0: HospitalList
	(*HospitalPage)(nil),               // 1: HospitalPage
	(*ListHospitalsRequest)(nil),       // 2: ListHospitalsRequest
	(*SearchHospitalNamesRequest)(nil), // 3: SearchHospitalNamesRequest
	(*SearchHospitalNamesResult)(nil),  // 4: SearchHospitalNamesResult
	(*HospitalNameData)(nil),           // 5: HospitalNameData
	(*SearchDoctorsRequest)(nil),       // 6: SearchDoctorsRequest
	(*SearchDoctorsResult)(nil),        // 7: SearchDoctorsResult
	(*HospitalData)(nil),               // 8: HospitalData
	(*DepartmentData)(nil),             // 9: DepartmentData
	(*DoctorData)(nil),                 // 10: DoctorData
	(*emptypb.Empty)(nil),              // 11: google.protobuf.Empty
}
var file_protos_hospital_proto_depIdxs = []int32{
	8,  // 0: HospitalList.data:type_name -> HospitalData
	8,  // 1: HospitalPage.items:type_name -> HospitalData
	5,  // 2: SearchHospitalNamesResult.result:type_name -> HospitalNameData
	10, // 3: SearchDoctorsResult.result:type_name -> DoctorData
	9,  // 4: HospitalData.departments:type_name -> DepartmentData
	10, // 5: DepartmentData.doctors:type_name -> DoctorData
	11, // 6: Hospital.ListAllHospitals:input_type -> google.protobuf.Empty
	2,  // 7: Hospital.ListHospitals:input_type -> ListHospitalsRequest
	3,  // 8: Hospital.SearchHospitalNames:input_type -> SearchHospitalNamesRequest
	6,  // 9: Hospital.SearchDoctors:input_type -> SearchDoctorsRequest
	2,  // 10: Hospital.MultiSearchHospital:input_type -> ListHospitalsRequest
	0,  // 11: Hospital.ListAllHospitals:output_type -> HospitalList
	1,  // 12: Hospital.ListHospitals:output_type -> HospitalPage
	4,  // 13: Hospital.SearchHospitalNames:output_type -> SearchHospitalNamesResult
	7,  // 14: Hospital.SearchDoctors:output_type -> SearchDoctorsResult
	1,  // 15: Hospital.MultiSearchHospital:output_type -> HospitalPage
	11, // [11:16] is the sub-list for method output_type
	6,  // [6:11] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_protos_hospital_proto_init() }
func file_protos_hospital_proto_init() {
	if File_protos_hospital_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_protos_hospital_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HospitalList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_hospital_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HospitalPage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_hospital_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListHospitalsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_hospital_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchHospitalNamesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_hospital_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchHospitalNamesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_hospital_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HospitalNameData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_hospital_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchDoctorsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_hospital_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchDoctorsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_hospital_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HospitalData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_hospital_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepartmentData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_hospital_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoctorData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_protos_hospital_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_protos_hospital_proto_goTypes,
		DependencyIndexes: file_protos_hospital_proto_depIdxs,
		MessageInfos:      file_protos_hospital_proto_msgTypes,
	}.Build()
	File_protos_hospital_proto = out.File
	file_protos_hospital_proto_rawDesc = nil
	file_protos_hospital_proto_goTypes = nil
	file_protos_hospital_proto_depIdxs = nil
}
