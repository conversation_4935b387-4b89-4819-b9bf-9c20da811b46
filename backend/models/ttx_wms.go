package models

import "go.mongodb.org/mongo-driver/bson/primitive"

// ReceiveConfirmData TTX入库单回传数据
type ReceiveConfirmData struct {
	Details []Details `json:"details"`
}

type Details struct {
	Containers []Containers `json:"containers"`
}

type Containers struct {
	ReceiptContainer ReceiptContainer `json:"receiptContainer"`
}

type ReceiptContainer struct {
	ProjectNo     string `json:"projectNo"`     //项目编号
	WarehouseCode string `json:"warehouseCode"` //仓库编码
	CompanyCode   string `json:"companyCode"`   // 货主编码
	ContainerCode string `json:"containerCode"` //箱号
	ContainerType string `json:"containerType"` //箱型
	ReceiptCode   string `json:"receiptCode"`   //入库单编号
	DrugCode      string `json:"drugCode"`      //药品编码
	ItemCode      string `json:"itemCode"`
	ItemName      string `json:"itemName"`     //药品名称
	Quantity      string `json:"quantity"`     //数量
	AgingDate     string `json:"agingDate"`    //入库日期
	InventorySts  string `json:"inventorySts"` //库存状态
}

type ReceiptContainerConfirm struct {
	ID            primitive.ObjectID `json:"id" bson:"_id"`
	ProjectNo     string             `json:"projectNo" bson:"project_no"`         //项目编号
	WarehouseCode string             `json:"warehouseCode" bson:"warehouse_code"` //仓库编码
	CompanyCode   string             `json:"companyCode" bson:"company_code"`     // 货主编码
	ContainerCode string             `json:"containerCode" bson:"container_code"` //箱号
	ContainerType string             `json:"containerType" bson:"container_type"` //箱型
	ReceiptCode   string             `json:"receiptCode" bson:"receipt_code"`     //入库单编号
	ItemCode      string             `json:"itemCode" bson:"item_code"`
	DrugCode      string             `json:"drugCode" bson:"drug_code"`            //药品编码
	ItemName      string             `json:"itemName" bson:"item_name"`            //药品名称
	Quantity      string             `json:"quantity" bson:"quantity"`             //数量
	AgingDate     string             `json:"agingDate" bson:"aging_date"`          //入库日期
	InventorySts  string             `json:"inventorySts" bson:"inventory_status"` //库存状态
	Meta          `json:"meta"`
}

// ShipmentConfirmData TTX出库单回传数据
type ShipmentConfirmData struct {
	ShipmentContainer ShipmentContainer `json:"container"`
}

type ShipmentContainer struct {
	ShipmentDetails []ShipmentDetail `json:"details"`
}

type ShipmentDetail struct {
	OrderNumber             string                  `json:"erpOrderCode"` //出库单
	ShippingContainerDetail ShippingContainerDetail `json:"shippingContainerDetail"`
}

type ShippingContainerDetail struct {
	ProjectNo     string `json:"projectNo"`     //项目编号
	WarehouseCode string `json:"warehouseCode"` //仓库编码
	CompanyCode   string `json:"companyCode"`   // 货主编码
	ShipmentCode  string `json:"shipmentCode"`  //出库单编码
	DrugCode      string `json:"drugCode"`      //药品编码
	ItemCode      string `json:"itemCode"`
	ItemName      string `json:"itemName"`     //药品名称
	Quantity      string `json:"quantity"`     //数量
	AgingDate     string `json:"agingDate"`    //入库日期
	InventorySts  string `json:"inventorySts"` //库存状态
}

type ShipmentDetailConfirm struct {
	ID            primitive.ObjectID `json:"id" bson:"_id"`
	OrderNumber   string             `json:"erpOrderCode" bson:"order_number"`    //出库单
	ProjectNo     string             `json:"projectNo" bson:"project_no"`         //项目编号
	WarehouseCode string             `json:"warehouseCode" bson:"warehouse_code"` //仓库编码
	CompanyCode   string             `json:"companyCode" bson:"company_code"`     // 货主编码
	ShipmentCode  string             `json:"shipmentCode" bson:"shipment_code"`   //出库单编码
	ItemCode      string             `json:"itemCode" bson:"item_code"`
	DrugCode      string             `json:"drugCode" bson:"drug_code"`            //药品编码
	ItemName      string             `json:"itemName" bson:"item_name"`            //药品名称
	Quantity      string             `json:"quantity" bson:"quantity"`             //数量
	AgingDate     string             `json:"agingDate" bson:"aging_date"`          //入库日期
	InventorySts  string             `json:"inventorySts" bson:"inventory_status"` //库存状态
	Meta          `json:"meta"`
}

// InventoryAdjustNotify 库存调整回传
type InventoryAdjustNotify struct {
	Rows []InventoryAdjustConfirm `json:"rows"`
}

type InventoryAdjustConfirm struct {
	ID              primitive.ObjectID `json:"id" bson:"_id"`
	Code            string             `json:"code" bson:"code"`                        //调整单号
	TransactionType string             `json:"transactionType" bson:"transaction_type"` //交易类型 STATUS_CHANGE状态调整、ADD_INVENTORY数量调整、QUANTITY_ADJUST添加库存、ATTRIBUTE_ADJUST属性调整（从A项目变到B项目）
	WarehouseCode   string             `json:"warehouseCode" bson:"warehouse_code"`     //仓库编码
	Zone            string             `json:"zone" bson:"zone"`                        //区域编码
	LocationCode    string             `json:"locationCode" bson:"location_code"`       //货位编码
	Lpn             string             `json:"lpn" bson:"lpn"`                          //LPN
	ItemCode        string             `json:"itemCode" bson:"item_code"`               //药品编码
	ItemName        string             `json:"itemName" bson:"item_name"`               //药品名称
	CompanyCode     string             `json:"companyCode" bson:"company_code"`         //货主编码
	Quantity        string             `json:"quantity" bson:"quantity"`                //数量
	BeforeSts       string             `json:"beforeSts" bson:"before_status"`          //调整前库存状态
	AfterSts        string             `json:"afterSts" bson:"after_status"`            //调整后库存状态
	Remark          string             `json:"remark" bson:"remark"`                    //备注
	Batch           string             `json:"batch" bson:"batch"`                      //批次
	Lot             string             `json:"lot" bson:"lot"`                          //批号
	ProjectNo       string             `json:"projectNo" bson:"project_no"`             //项目编号
	ManufactureDate string             `json:"manufactureDate" bson:"manufacture_date"` //生产日期
	ExpirationDate  string             `json:"expirationDate" bson:"expiration_date"`   //失效日期
	AgingDate       string             `json:"agingDate" bson:"aging_date"`             //入库日期
	Meta            `json:"meta"`
}
