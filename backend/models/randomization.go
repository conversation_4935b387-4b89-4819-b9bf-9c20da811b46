package models

import (
	"fmt"
	"time"

	"github.com/duke-git/lancet/v2/slice"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Attribute ..
type Attribute struct {
	ID            primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID    primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID     primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID primitive.ObjectID `json:"envId" bson:"env_id"`
	CohortID      primitive.ObjectID `json:"cohortId" bson:"cohort_id"`
	AttributeInfo AttributeInfo      `json:"info" bson:"info"`
}

// AttributeInfo ...
type AttributeInfo struct {
	Random                     bool   `json:"random"`                                                          // 是否随机
	RandomControl              bool   `json:"randomControl" bson:"random_control"`                             // 随机研究产品供应核查
	RandomControlRule          int    `json:"randomControlRule" bson:"random_control_rule"`                    // 随机研究产品供应核查方式 1:所有分组，有供应后允许随机 2:已分配分组，有供应后允许随机 3:强制随机到有供应的分组
	RandomControlGroup         int    `json:"randomControlGroup" bson:"random_control_group"`                  // 强制随机到有供应的分组的时候，至少需要多少个分组需有供应
	IsRandomNumber             bool   `json:"isRandomNumber" bson:"is_random_number"`                          // 是否展示随机号
	IsRandomSequenceNumber     bool   `json:"isRandomSequenceNumber" bson:"is_random_sequence_number"`         // 是否展示随机顺序号
	RandomSequenceNumberPrefix string `json:"randomSequenceNumberPrefix" bson:"random_sequence_number_prefix"` // 随机顺序号前缀
	RandomSequenceNumberDigit  int    `json:"randomSequenceNumberDigit" bson:"random_sequence_number_digit"`   // 随机顺序号位数
	RandomSequenceNumberStart  int    `json:"randomSequenceNumberStart" bson:"random_sequence_number_start"`   // 随机顺序号起始数
	Dispensing                 bool   `json:"dispensing"`                                                      // 是否发药
	DtpRule                    int    `json:"dtpRule" bson:"dtp_rule"`                                         // DTP规则 1研究产品 2访视流程 3不适用
	Blind                      bool   `json:"blind"`                                                           // 是否盲法
	CountryLayered             bool   `json:"countryLayered" bson:"country_layered"`                           // 国家分层
	InstituteLayered           bool   `json:"instituteLayered" bson:"institute_layered"`                       // 中心分层
	RegionLayered              bool   `json:"regionLayered" bson:"region_layered"`                             // 区域分层
	Prefix                     bool   `json:"prefix"`                                                          // 是否使用前缀
	SubjectNumberRule          int    `json:"subjectNumberRule" bson:"subject_number_rule"`                    //受试者号录入规则 1:自定义 2：自动递增且在项目中唯一 3：自动递增且在中心中唯一
	//SitePrefix               bool                     `json:"sitePrefix" bson:"site_prefix"`                              // 废弃 是否将中心作为前缀
	//PrefixConnector          string                   `json:"prefixConnector" bson:"prefix_connector"`                    // 废弃 前缀连接符
	//OtherPrefix              bool                     `json:"otherPrefix" bson:"other_prefix"`                            // 废弃 受试者号的其他前缀
	//OtherPrefixText          string                   `json:"otherPrefixText" bson:"other_prefix_text"`                   // 废弃 其他前缀文本
	PrefixExpression           string                   `json:"prefixExpression" bson:"prefix_expression"`                       // 前缀表达式
	SubjectReplaceText         string                   `json:"subjectReplaceText" bson:"subject_replace_text"`                  // 标签（受试者号替换文本）
	SubjectReplaceTextEn       string                   `json:"subjectReplaceTextEn" bson:"subject_replace_text_en"`             // 标签（受试者号替换文本(英文)）
	Digit                      int                      `json:"digit"`                                                           // 位数限制
	Accuracy                   int                      `json:"accuracy"`                                                        // 位数精确值(1:小于等于,2:等于)
	Field                      Field                    `json:"field"`                                                           // 受试者号拼接字段
	PrefixSymbol               string                   `json:"prefixSymbol" bson:"prefix_symbol"`                               // 中心号为受试者号前缀
	IsFreeze                   bool                     `json:"isFreeze" bson:"is_freeze"`                                       // 运行运送算法时将隔离的单品计算为研究机构重可用存货的一部分
	IsRandom                   bool                     `json:"isRandom" bson:"is_random"`                                       // 中心没有随机号不能随机
	IsCountryRandom            bool                     `json:"isCountryRandom" bson:"is_country_random"`                        // 国家没有随机号不能随机
	IsRegionRandom             bool                     `json:"isRegionRandom" bson:"is_region_random"`                          // 区域没有随机号不能随机
	EdcDrugConfigLabel         string                   `json:"edcDrugConfigLabel" bson:"edc_drug_config_label"`                 // EDC对接研究产品配置的label
	Segment                    bool                     `json:"segment" bson:"segment"`                                          //号段随机
	SegmentType                int                      `json:"segmentType" bson:"segment_type"`                                 //号段随机类别 0、序列号， 1，药物号
	UnblindingReasonConfig     []UnblindingReasonConfig `json:"unblindingReasonConfig" bson:"unblinding_reason_config"`          //揭盲原因自定义配置
	FreezeReasonConfig         []UnblindingReasonConfig `json:"freezeReasonConfig" bson:"freeze_reason_config"`                  //隔离原因自定义配置
	UnBlindingRestrictions     bool                     `json:"blindingRestrictions" bson:"blinding_restrictions"`               //紧急揭盲限制 0.关 1开
	PvUnBlindingRestrictions   bool                     `json:"pvUnBlindingRestrictions" bson:"pv_unblinding_restrictions"`      //PV揭盲限制  0.关 1开
	ReplaceRule                int                      `json:"replaceRule" bson:"replace_rule"`                                 //替换受试者随机号规则，0自定义 1系统自动生成
	ReplaceRuleNumber          int                      `json:"replaceRuleNumber" bson:"replace_rule_number"`                    //替换受试者随机号往后加多少
	IsScreen                   bool                     `json:"isScreen" bson:"is_screen"`                                       //受试者筛选流程 true开启 false关闭
	ConnectAli                 bool                     `json:"connectAli" bson:"connect_ali"`                                   // 1、对接 2、非对接
	AliProjectNO               string                   `json:"aliProjectNo" bson:"ali_project_no"`                              // 阿里健康码上放心对接项目编号
	AllowReplace               bool                     `json:"allowReplace" bson:"allow_replace"`                               // 允许替换受试者
	AllowRegisterGroup         bool                     `json:"allowRegisterGroup" bson:"allow_register_group"`                  // 允许变更登记替换组别
	MinimizeCalc               int                      `json:"minimizeCalc" bson:"minimize_calc"`                               // 最小化随机计算 0随机分层 1实际分层 2.不适用
	IPInheritance              bool                     `json:"IPInheritance" bson:"ip_inheritance"`                             // 继承访视
	RemainingVisit             *int                     `json:"remainingVisit" bson:"remaining_visit"`                           // 剩余访视周期
	CodeRule                   int                      `json:"codeRule" bson:"code_rule"`                                       //编码规则 0手动上传 1系统自动编码
	CodeConfigInit             bool                     `json:"codeConfigInit" bson:"code_config_init"`                          //编码配置是否已经初始化
	OverdueVisitApproval       bool                     `json:"overdueVisitApproval" bson:"overdue_visit_approval"`              // 超窗访视发放审批
	OverdueVisitSms            bool                     `json:"overdueVisitSms" bson:"overdue_visit_sms"`                        //超窗访视发放审批 短信
	OverdueVisitProcess        bool                     `json:"overdueVisitProcess" bson:"overdue_visit_process"`                //超窗访视发放审批 流程操作
	OverdueVisitMainVisit      bool                     `json:"overdueVisitMainVisit" bson:"overdue_visit_main_visit"`           // 超窗访视发放审批 主访视
	OverdueVisitReDispensation bool                     `json:"overdueVisitReDispensation" bson:"overdue_visit_re_dispensation"` // 超窗访视发放审批 补发
	OverdueVisitUnscheduled    bool                     `json:"overdueVisitUnscheduled" bson:"overdue_visit_unscheduled"`        // 超窗访视发放审批 计划外发放/独立计划外
}

type UnblindingReasonConfig struct {
	Reason      string `json:"reason" bson:"reason"`
	AllowRemark bool   `json:"allowRemark" bson:"allow_remark"`
}

type UnblindingReasonConfigOperationLog struct {
	Reason             string
	AllowRemarkTranKey string
}

// RandomDesign ...
type RandomDesign struct {
	ID            primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID    primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID     primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID primitive.ObjectID `json:"envId" bson:"env_id"`
	CohortID      primitive.ObjectID `json:"cohortId" bson:"cohort_id"`
	Info          RandomDesignInfo   `json:"info" bson:"info"`
	Version       float32            `json:"version"`
}

// RandomDesignInfo ...
type RandomDesignInfo struct {
	// 随机类型 1.区组随机 2.最小化随机
	Type int `json:"type"`
	// 随机分组
	Groups []RandomGroup `json:"groups"`
	// 分层因素
	Factors []RandomFactor `json:"factors"`
	//地区分层比例
	Ratio int `json:"ratio"`
	// 分层因素排列组合
	Combination []FactorsCombination `json:"combination"`
	// 记录上次生成随机号的最后一位数
	BlockNumber int `json:"blockNumber" bson:"block_number"`
}

type RandomListInfo struct {
	// 随机类型 1.区组随机 2.最小化随机
	Type int `json:"type"`
	// 随机分组
	Groups []RandomListGroup `json:"groups"`
	// 分层因素
	Factors []RandomFactor `json:"factors"`
	//地区分层比例
	Ratio int `json:"ratio"`
	// 分层因素排列组合
	Combination []FactorsCombination `json:"combination"`
	// 记录上次生成随机号的最后一位数
	BlockNumber int `json:"blockNumber" bson:"block_number"`
}

type RandomFactorGroupReq struct {
	ProjectID  primitive.ObjectID `json:"projectId"`
	EnvID      primitive.ObjectID `json:"envId"`
	CustomerID primitive.ObjectID `json:"customerId"`
	CohortID   primitive.ObjectID `json:"cohortId"`
	Type       *int               `json:"type"`
	Group      *struct {
		ID         primitive.ObjectID `json:"id" bson:"id"`
		Code       string             `json:"code" bson:"code"`
		Name       string             `json:"name" bson:"name"`
		Status     int                `json:"status" bson:"status"`
		IsCopyData bool               `json:"isCopyData" bson:"is_copy_data"`
		SubGroup   []SubGroupReq      `json:"subGroup" bson:"sub_group"`
	} `json:"group" bson:"group"`
	Factor *struct {
		ID             primitive.ObjectID `json:"id" bson:"id"`
		IsCalc         bool               `json:"isCalc" bson:"is_calc"`      //是否分层计算
		CalcType       *int               `json:"calcType" bson:"calc_type"`  //计算公式类型 0年龄 1BMI
		Precision      *int               `json:"precision" bson:"precision"` //保留小数位数
		Number         string             `json:"number" bson:"number"`
		DateFormat     *string            `json:"dateFormat" bson:"date_format"`         //日期格式类型
		CustomFormulas string             `json:"customFormulas" bson:"custom_formulas"` //自定义公式
		//InputLabel       *string            `json:"inputLabel" bson:"input_label"`
		//InputWeightLabel *string            `json:"inputWeightLabel" bson:"input_weight_label"`
		//InputHeightLabel *string            `json:"inputHeightLabel" bson:"input_height_label"`
		Round      int    `json:"round" bson:"round"` //1向上取整 2向下取整
		Label      string `json:"label" bson:"label"`
		Type       string `json:"type" bson:"type"`
		Name       string `json:"name" bson:"name"`
		Status     *int   `json:"status" bson:"status"`
		IsCopyData bool   `json:"isCopyData" bson:"is_copy_data"`
		Options    []struct {
			Label      string  `json:"label" bson:"label"`
			Value      string  `json:"value" bson:"value"`
			Formula    *string `json:"formula" bson:"formula"`
			IsCopyData bool    `json:"isCopyData" bson:"is_copy_data"`
		} `json:"options" bson:"options"`
	} `json:"factor" bson:"factor"`
}

type SubGroupReq struct {
	Name       string `json:"name" bson:"name"`
	Blind      bool   `json:"blind" bson:"blind"`
	IsCopyData bool   `json:"isCopyData" bson:"is_copy_data"`
}

// RandomGroup ...
type RandomGroup struct {
	ID primitive.ObjectID `json:"id" bson:"id"`
	// 代码
	Code string `json:"code"`
	// 名称
	Name string `json:"name"`
	// 比例
	Ratio int `json:"ratio"`
	// 描述
	Description string `json:"description"`
	// 号段随机长度
	SegmentLength int `json:"segmentLength" bson:"segment_length"`
	//子组别
	SubGroup []SubGroup `json:"subGroup" bson:"sub_group"`
	//状态
	Status *int `json:"status" bson:"status"` //1或者nil有效，2无效
	//是否被使用
	Used bool `json:"used" bson:"-"`
	//是否从PROD复制的且被使用
	IsCopyData bool `json:"isCopyData" bson:"is_copy_data"`
}

type RandomListGroup struct {
	ID primitive.ObjectID `json:"id" bson:"id"`
	// 代码
	Code string `json:"code"`
	// 名称
	Name string `json:"name"`
	//主组别名称
	ParName string `json:"parName" bson:"par_name"`
	//子组别名称
	SubName string `json:"subName" bson:"sub_name"`
	//盲态
	Blind bool `json:"blind" bson:"blind"`
	// 比例
	Ratio int `json:"ratio"`
	// 描述
	Description string `json:"description"`
	// 号段随机长度
	SegmentLength int `json:"segmentLength" bson:"segment_length"`
	//状态
	Status *int `json:"status" bson:"status"` //1或者nil有效，2无效
}
type SubGroup struct {
	Name          string `json:"name" bson:"name"`
	Blind         bool   `json:"blind" bson:"blind"`
	SegmentLength int    `json:"segmentLength" bson:"segment_length"`
	IsCopyData    bool   `json:"isCopyData" bson:"is_copy_data"`
}

// RandomFactor ...
type RandomFactor struct {
	ID     primitive.ObjectID `json:"id" bson:"id"`
	Number string             `json:"number"`
	Name   string             `json:"name" bson:"name"`
	Label  string             `json:"label" bson:"label"`
	//InputLabel       *string            `json:"inputLabel" bson:"input_label"`              //废弃
	//InputWeightLabel *string            `json:"inputWeightLabel" bson:"input_weight_label"` //废弃
	//InputHeightLabel *string            `json:"inputHeightLabel" bson:"input_height_label"` //废弃
	CustomFormulas string   `json:"customFormulas" bson:"custom_formulas"` //自定义公式
	Type           string   `json:"type" bson:"type"`                      //"select" "radio" "datePicker" "input"
	DateFormat     *string  `json:"dateFormat" bson:"date_format"`         //日期格式类型
	Precision      *int     `json:"precision" bson:"precision"`            //保留小数位数
	Round          int      `json:"round" bson:"round"`                    //1向上取整 2向下取整
	IsCalc         bool     `json:"isCalc" bson:"is_calc"`                 //是否分层计算
	CalcType       *int     `json:"calcType" bson:"calc_type"`             //计算公式类型 0年龄 1BMI
	Options        []Option `json:"options" bson:"options"`
	Modifiable     bool     `json:"modifiable" bson:"modifiable"` //是否修改：false否，true是
	Status         *int     `json:"status" bson:"status"`         //1或者nil有效，2无效
	Ratio          int      `json:"ratio"`                        // 分层因素权重比
	//是否从PROD复制的且被使用
	IsCopyData bool `json:"isCopyData" bson:"is_copy_data"`
}

// FactorsCombination ...
type FactorsCombination struct {
	ID primitive.ObjectID
	// 分层编号
	Number string `json:"number"`
	// 分层组合
	LayeredFactors []Factors `json:"layered" bson:"layered_factors"`
	// 预计录入人数
	EstimateNumber int `json:"estimateNumber" bson:"estimate_number"`
	// 警戒人数
	WarnNumber int `json:"warnNumber" bson:"warn_number"`
}

// FactorsCombinations ...
type FactorsCombinations struct {
	ID primitive.ObjectID
	// 分层编号
	Number string `json:"number"`
	// 分层组合
	LayeredFactors []Factors `json:"layered" bson:"layered_factors"`
	// 预计录入人数
	EstimateNumber int `json:"estimateNumber" bson:"estimate_number"`
	// 警戒人数
	WarnNumber int `json:"warnNumber" bson:"warn_number"`
}

// RandomList ...
type RandomList struct {
	ID            primitive.ObjectID   `json:"id" bson:"_id"`
	CustomerID    primitive.ObjectID   `json:"customerId" bson:"customer_id"`
	ProjectID     primitive.ObjectID   `json:"projectId" bson:"project_id"`
	EnvironmentID primitive.ObjectID   `json:"envId" bson:"env_id"`
	CohortID      primitive.ObjectID   `json:"cohortId" bson:"cohort_id"`
	SiteIds       []primitive.ObjectID `json:"siteIds" bson:"site_ids"`
	Name          string               `json:"name"`
	LastGroup     string               `json:"lastGroup" bson:"last_group"` // 再随机项目上一阶段的组别
	Design        RandomListInfo       `json:"design" bson:"design"`
	Config        RandomListConfig     `json:"config" bson:"config"`
	Status        int8                 `json:"status" bson:"status"`   // 1激活 2关闭 3作废(此字段一旦被作废就不可在修改)
	Version       float32              `json:"version" bson:"version"` // v1
	CustomHeaders []string             `json:"customHeaders" bson:"custom_headers"`
	Meta          Meta                 `json:"meta"`
}

// RandomListConfig ...
type RandomListConfig struct {
	InitialValue     int      `json:"initialValue" bson:"initial_value"`          // 初始值(随机号生成时的初始值)
	EndValue         int      `json:"endValue" bson:"end_value"`                  // 终止值
	Rule             int      `json:"rule" bson:"rule"`                           // 随机号生成规则 0顺序，1乱序
	BlocksRule       int      `json:"blocksRule" bson:"blocks_rule"`              // 区组规则，0顺序，1乱序
	Blocks           []Block  `json:"blocks"`                                     // 区组信息
	BlockSize        int      `json:"blockSize" bson:"block_size"`                // 区组大小
	Total            int      `json:"total"`                                      // 总数量
	NumberLength     int      `json:"numberLength" bson:"number_length"`          // 随机号长度
	Probability      float64  `json:"probability"`                                // 偏倚概率
	Seed             int64    `json:"seed"`                                       // 随机种子
	Prefix           string   `json:"prefix"`                                     // 随机号前缀
	SiteAllocation   bool     `json:"siteAllocation" bson:"site_allocation"`      // 中心是否预分配标记
	CheckSite        bool     `json:"checkSite" bson:"check_site"`                // 随机分段是否勾选中心
	CheckOtherFactor bool     `json:"checkOtherFactor" bson:"check_other_factor"` // 随机分段是否勾选其他分层
	BlockSizes       []string `json:"blockSizes" bson:"block_sizes"`              // 可接受的区组大小
}

// Block ...
type Block struct {
	BlockLength int `json:"blockLength"` // 区组长度
	BlockNumber int `json:"blockNumber"` // 区组数量
}

type BlockReq struct {
	ID           string             `json:"id"`
	RandomListID primitive.ObjectID `json:"randomListId"`
	RangeType    int32              `json:"rangeType"`
	Start        int                `json:"start"`
	End          int                `json:"end"`
	StartStr     string             `json:"startStr"`
	EndStr       string             `json:"endStr"`
	Begin        int                `json:"begin"`
	Limit        int                `json:"limit"`
	Status       []int32            `json:"status"`
	StatusType   int32              `json:"statusType"`
}

// RandomNumber 随机号
type RandomNumber struct {
	ID               primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID       primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID        primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID    primitive.ObjectID `json:"envId" bson:"env_id"`
	CohortID         primitive.ObjectID `json:"cohortId" bson:"cohort_id"`
	RandomListID     primitive.ObjectID `json:"randomListId" bson:"random_list_id"`
	ProjectSiteID    primitive.ObjectID `json:"projectSiteId" bson:"project_site_id"` // 中心ID(占用)
	RegionID         primitive.ObjectID `json:"regionId" bson:"region_id"`            // 区域ID(占用)
	SubjectID        primitive.ObjectID `json:"subjectId" bson:"subject_id"`          // 受试者ID
	Block            int                `json:"block"`
	Number           string             `json:"number"`
	Group            string             `json:"group"`
	ParName          string             `json:"parName" bson:"par_name"`
	SubName          string             `json:"subName" bson:"sub_name"`
	PlanNumber       float64            `json:"planNumber" bson:"plan_number"`     // 最小化随机的计划随机数
	ActualNumber     float64            `json:"actualNumber" bson:"actual_number"` // 最小化随机的实际随机数
	Factors          []Factors          `json:"factors"`                           // 分层因素等字段信息
	Status           int8               `json:"status"`                            // 1未使用 2 已使用 3 已作废 4 无效 （3、4统一都叫无效） 5 不可用
	GroupValue       []GroupValue       `json:"groupValue" bson:"group_value"`
	Country          string             `json:"country" bson:"country"`                     // 国家占用
	ReplaceSubjectID primitive.ObjectID `bson:"replace_subject_id" json:"replaceSubjectId"` // 替换受试者ID
	ReplaceSubject   string             `bson:"replace_subject" json:"replaceSubject"`      // 替换受试者号
	ReplaceNumber    string             `bson:"replace_number" json:"replaceNumber"`        // 替换随机号
	SimulateSite     string             //模拟随机时的中心名称
	SimulateCountry  string             //模拟随机时的国家名称
	SimulateRegion   string             //模拟随机时的区域名称
	Custom           map[string]string  `bson:"custom" json:"custom"` // 自定义列（列头->单元格值）
}

type SortBySimulateSite []RandomNumber

func (a SortBySimulateSite) Len() int      { return len(a) }
func (a SortBySimulateSite) Swap(i, j int) { a[i], a[j] = a[j], a[i] }
func (a SortBySimulateSite) Less(i, j int) bool {
	return a[i].SimulateSite < a[j].SimulateSite
}

type SortBySimulateCountry []RandomNumber

func (a SortBySimulateCountry) Len() int      { return len(a) }
func (a SortBySimulateCountry) Swap(i, j int) { a[i], a[j] = a[j], a[i] }
func (a SortBySimulateCountry) Less(i, j int) bool {
	//先按照中心降序，然后按照国家降序，最后按照区域降序
	if a[i].SimulateSite == a[j].SimulateSite {
		if a[i].SimulateCountry == a[j].SimulateCountry {
			return a[i].SimulateRegion > a[j].SimulateRegion
		}
		return a[i].SimulateCountry > a[j].SimulateCountry
	}
	return a[i].SimulateSite > a[j].SimulateSite
}

type SortBySimulateRegion []RandomNumber

func (a SortBySimulateRegion) Len() int      { return len(a) }
func (a SortBySimulateRegion) Swap(i, j int) { a[i], a[j] = a[j], a[i] }
func (a SortBySimulateRegion) Less(i, j int) bool {
	//先按照中心降序，然后按照区域降序，最后按照国家降序
	if a[i].SimulateSite == a[j].SimulateSite { // 如果年龄相同，按照名字排序
		if a[i].SimulateRegion == a[j].SimulateRegion {
			return a[i].SimulateCountry > a[j].SimulateCountry
		}
		return a[i].SimulateRegion > a[j].SimulateRegion
	}
	return a[i].SimulateSite > a[j].SimulateSite
}

// Factors ..
type Factors struct {
	Name  string `json:"name" bson:"name"`
	Label string `json:"label" bson:"label"`
	Value string `json:"value" bson:"value"`
	Text  string `json:"text" bson:"text"`
}
type SortByNameValue []Factors

func (p SortByNameValue) Len() int {
	return len(p)
}

func (p SortByNameValue) Swap(i, j int) {
	p[i], p[j] = p[j], p[i]
}

func (p SortByNameValue) Less(i, j int) bool {
	// 先比较名字，如果名字相同再比较值
	if p[i].Name == p[j].Name {
		return p[i].Value < p[j].Value
	}
	return p[i].Name < p[j].Name
}

// 用于比较两个 Person 切片是否相等
func (p SortByNameValue) String() string {
	return fmt.Sprintf("%v", p)
}

// Form ..
type Form struct {
	ID            primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID    primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID     primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID primitive.ObjectID `json:"envId" bson:"env_id"`
	CohortID      primitive.ObjectID `json:"cohortId" bson:"cohort_id"`
	Fields        []Field            `json:"fields" bson:"fields"`
}

type ReRandomForm struct {
	CohortID     primitive.ObjectID `json:"cohortId" bson:"cohort_id"`
	CohortName   string             `json:"cohortName" bson:"cohort_name"`
	ReRandomName string             `json:"reRandomName" `
	Fields       []ListField        `json:"fields" bson:"fields"`
}

// Field ..
type Field struct {
	ID             primitive.ObjectID `json:"id" bson:"id"`
	CohortId       primitive.ObjectID `json:"cohortId" bson:"cohort_id"`
	CohortName     string             `json:"cohortName" bson:"-"`
	IsCalc         bool               `json:"isCalc" bson:"is_calc"`     //是否分层计算
	CalcType       *int               `json:"calcType" bson:"calc_type"` //计算公式类型 0年龄 1BMI
	Name           string             `json:"name" bson:"name"`
	Label          string             `json:"label" bson:"label"`
	LabelEn        string             `json:"labelEn" bson:"label_en"`
	CustomFormulas string             `json:"customFormulas" bson:"custom_formulas"` //自定义公式
	//InputLabel       *string            `json:"inputLabel" bson:"input_label"`
	//InputWeightLabel *string            `json:"inputWeightLabel" bson:"input_weight_label"`
	//InputHeightLabel *string            `json:"inputHeightLabel" bson:"input_height_label"`
	Precision            *int       `json:"precision" bson:"precision"` //保留小数位数
	Round                int        `json:"round" bson:"round"`         //1向上取整 2向下取整
	Type                 string     `json:"type" bson:"type"`
	Status               *int       `json:"status" bson:"status"`                    //1或者nil有效，2无效
	ApplicationType      *int       `json:"applicationType" bson:"application_type"` //1或者nil受试者登记，2公式计算,3剂量调整
	Variable             string     `json:"variable" bson:"variable"`                //变量id
	Used                 bool       `json:"used" bson:"used"`                        //标识已使用，不能删除，只能改有效无效状态
	Options              []Option   `json:"options" bson:"options"`
	List                 bool       `json:"list" bson:"list"`                     //列表显示：false否，true是
	Modifiable           bool       `json:"modifiable" bson:"modifiable"`         //是否修改：false否，true是
	Required             bool       `json:"required" bson:"required"`             //是否必填：false否，true是
	Stratification       bool       `json:"stratification" bson:"stratification"` //是否是分层因素：false否，true是
	Digit                int        `json:"digit" bson:"digit"`                   //位数限制(只有type为Input才设置)
	Accuracy             int        `json:"accuracy" bson:"accuracy"`             //位数精确值(1:小于等于,2:等于)只有type为Input才设置
	DateFormat           string     `json:"dateFormat" bson:"date_format"`        //日期格式类型
	FormatType           string     `json:"formatType" bson:"formatType"`         //格式类型
	TimeFormat           string     `json:"timeFormat" bson:"time_format"`        //时间格式类型
	Length               *float64   `json:"length" bson:"length"`                 //变量长度
	Range                *Range     `json:"range" bson:"range"`                   //变量范围
	DateRange            *DateRange `json:"dateRange" bson:"date_range"`          //变量范围
	IsCustomFormulaField bool       `json:"isCustomFormula" bson:"-"`             //是否是分层计算分层自定义公式所包含的表单
}

// 受试者列表专用
type ListField struct {
	ID             primitive.ObjectID `json:"id"`
	Label          string             `json:"label"`
	LabelEn        string             `json:"labelEn"`
	Name           string             `json:"name"` //factor field shortname
	Status         *int               `json:"status"`
	Type           string             `json:"type"`
	Options        []Option           `json:"options"`
	InvalidDisplay string             `json:"invalidDisplay"`
	IsCalc         bool               `json:"isCalc"`
	CalcType       *int               `json:"calcType" ` //计算公式类型 0年龄 1BMI
	DateFormat     string             `json:"dateFormat"`
	TimeFormat     string             `json:"timeFormat"`
}

// Option ..
type Option struct {
	Label   string  `json:"label" bson:"label"`
	Value   string  `json:"value" bson:"value"`
	Formula *string `json:"formula" bson:"formula"`
	Disable bool    `json:"disable" bson:"-"`
	//是否从PROD复制的且被使用
	IsCopyData bool `json:"isCopyData" bson:"is_copy_data"`
}

// FactorHeader 分层因素排列组合表头
type FactorHeader struct {
	Key  string `json:"key"`  // 字段key
	Name string `json:"name"` // 字段名称
}

// GroupValue 最小化随机各组别G值
type GroupValue struct {
	Group   string  `json:"group"`
	ParName string  `json:"parName" bson:"par_name"`
	SubName string  `json:"subName" bson:"sub_name"`
	Value   float64 `json:"value"`
}

type Range struct {
	Min *float64 `json:"min" bson:"min"`
	Max *float64 `json:"max" bson:"max"`
}

type DateRange struct {
	Min string `json:"min" bson:"min"`
	Max string `json:"max" bson:"max"`
}

// Region 区域
type Region struct {
	ID         primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID  primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvID      primitive.ObjectID `json:"envId" bson:"env_id"`
	Name       string             `json:"name" bson:"name"`
	//Status     int                `json:"status" bson:"status"` //1有效 0无效
	Deleted bool `json:"deleted" bson:"deleted"`
	Disable bool `json:"disable" bson:"-"`
}

// *************** 以下是参数类 不合数据库做交互 ***************

// RandomListConfigPt 随机配置
type RandomListConfigPt struct {
	CustomerID       string            `json:"customerId"`
	ProjectID        string            `json:"projectId"`
	EnvID            string            `json:"envId"`
	CohortID         string            `json:"cohortId"`
	LastGroup        string            `json:"lastGroup"`        // 在随机项目上一阶段的组别
	Name             string            `json:"name"`             // 随机表名
	SiteIds          []string          `json:"siteIds"`          // 中心ID
	InitialValue     int               `json:"initialValue"`     // 初始值(随机号生成时的初始值)
	EndValue         int               `json:"endValue"`         // 终止值
	Groups           []RandomListGroup `json:"groups"`           // 组别配置
	RandomNumberRule int               `json:"randomNumberRule"` // 随机号规则 0顺序 1乱序
	Blocks           []Block           `json:"blocks"`           // 区组信息
	BlocksRule       int               `json:"blocksRule"`       // 0顺序 1乱序
	Factors          []RandomFactor    `json:"factors"`          // 分层因素配置
	Ratio            int               `json:"ratio"`            // 地区分层配置比
	Total            int               `json:"total"`            // 总数量
	NumberLength     int               `json:"numberLength"`     // 随机号长度
	Probability      float64           `json:"probability"`      // 偏倚概率
	Seed             int64             `json:"seed"`             // 随机种子
	Prefix           string            `json:"prefix"`           // 随机号前缀
	SiteAllocation   bool              `json:"siteAllocation"`   // 中心是否预分配标记
}
type RandomNumberInactivateReq struct {
	EnvID    primitive.ObjectID `json:"envId"`
	CohortID primitive.ObjectID `json:"cohortId"`
}

type RandomNumberActivateInactivateReq struct {
	RandomListID  primitive.ObjectID `json:"randomListId"`
	Block         int                `json:"block"`
	OperationType int                `json:"operationType"` // 0激活 1失活
	DataType      int                `json:"dataType"`      // 0区组 1随机号
	Number        string             `json:"number"`
	RangeType     int                `json:"rangeType"`
	StartStr      string             `json:"startStr"`
	EndStr        string             `json:"endStr"`
}

// GroupCode 系统生成随机号所需的组别和对应的组别数字节点
type GroupCode struct {
	// 组别
	Group string
	//主组别
	ParName string
	//子组别
	SubName string
	// 组别对应的节点
	GroupNode int
}
type Group struct {
	// 组别
	Group string
	//主组别
	ParName string
	//子组别
	SubName string
}

type GroupBlind struct {
	// 组别
	Group string
	//主组别
	ParName string
	//子组别
	SubName  string
	SubBlind bool
}

// FactorsParameter ..
type FactorsParameter struct {
	// 分层编号
	Number string `json:"number"`
	// 分层组合
	Layered map[string]string `json:"layered"`
}

// ExportNumber ..
type ExportNumber struct {
	Block                int
	Number               string
	Group                string
	ParName              string `json:"parName" bson:"par_name"`
	SubName              string `json:"subName" bson:"sub_name"`
	Factors              []Factors
	Subject              string
	Site                 string
	Status               int
	ReplaceSubject       string             `bson:"replace_subject"` // 替换受试者号
	ReplaceNumber        string             `bson:"replace_number"`  // 替换随机号
	Country              string             `bson:"country"`
	PlanNumber           *float64           `json:"planNumber" bson:"plan_number"` // 最小化随机的计划随机数
	GroupValue           []GroupValue       `json:"groupValue" bson:"group_value"`
	SiteNumber           string             `bson:"site_number"`
	RandomTime           int64              `bson:"random_time"`
	RandomListID         primitive.ObjectID `bson:"random_list_id"`
	History              []History          `bson:"history"`
	ReplaceSubjectStatus *int               `bson:"replace_subject_status"`
	CohortID             primitive.ObjectID `bson:"cohort_id"`
	TimeZone             string             `bson:"time_zone"`
	Tz                   string             `bson:"tz"`
	RegisterGroup        string             `bson:"register_group"`
	Custom               map[string]string  `bson:"custom" json:"custom"` // 自定义列（列头->单元格值）
}

// ExportSubjectNumber ..
type ExportSubjectNumber struct {
	ID                   primitive.ObjectID `bson:"_id"`
	ProjectID            primitive.ObjectID `bson:"project_id"`
	EnvironmentID        primitive.ObjectID `bson:"env_id"`
	ProjectSiteID        primitive.ObjectID `bson:"project_site_id"` // 中心ID(占用)
	SiteNumber           string             `bson:"site_number"`
	SiteName             string             `bson:"site_name"`
	RandomListID         primitive.ObjectID `bson:"random_list_id"`
	RegisterRandomListID primitive.ObjectID `bson:"register_random_list_id"` //随机之前，使用的随机表的id
	CohortID             primitive.ObjectID `bson:"cohort_id"`
	RandomNumber         string             `bson:"random_number"`
	RandomTime           int64              `bson:"random_time"`
	RegisterTime         int64              `bson:"register_time"`
	History              []History          `bson:"history"`
	Group                string
	ParGroupName         string `bson:"par_group_name"`
	SubGroupName         string `bson:"sub_group_name"`
	Number               string
	Status               int32
	TimeZone             string                 `bson:"time_zone"`
	Tz                   string                 `bson:"tz"`
	Subject              []Info                 `bson:"subject"`
	ActualInfo           []Info                 `bson:"actual_info"`
	ReplaceSubject       []Info                 `bson:"replace_subject"` // 替换受试者号
	ReplaceNumber        string                 `bson:"replace_number"`  // 替换随机号
	Country              string                 `bson:"country"`
	ReplaceSubjectStatus *int32                 `bson:"replace_subject_status"`
	ReplaceSubjectID     primitive.ObjectID     `bson:"replace_subject_id"` // 替换受试者ID
	Region               string                 `bson:"region"`
	IsScreen             *bool                  `bson:"is_screen"`          //是否筛选成功
	ScreenTime           string                 `bson:"screen_time"`        //筛选时间
	ICFTime              string                 `bson:"icf_time"`           //ICF签署时间
	FinishRemark         string                 `bson:"finish_remark"`      //ICF签署时间
	SignOutRealTime      string                 `bson:"sign_out_real_time"` // 实际停用时间
	SignOutPeople        primitive.ObjectID     `bson:"sign_out_people"`    // 退出（操作人）
	SignOutTime          int64                  `bson:"sign_out_time"`      // 停用时间
	SignOutReason        string                 `bson:"sign_out_reason"`    // 停用原因
	JoinTime             string                 `bson:"join_time"`
	RegisterGroup        string                 `bson:"register_group"`
	Dispensing           []DispensingSimpleInfo `bson:"dispensing"`
	RandomSequenceNumber string                 `bson:"random_sequence_number"` //随机顺序号
}

type DispensingSimpleInfo struct {
	Status         int           `bson:"status"`
	VisitNumber    string        `bson:"visit_number"`
	SerialNumber   int           `bson:"serial_number"`
	VisitSign      bool          `bson:"visit_sign"`
	DispensingTime time.Duration `bson:"dispensing_time"`
}

type CheckNumber struct {
	ID     primitive.ObjectID `bson:"_id"`
	Number string             `bson:"number"`
	Status int                `bson:"status"`
}

func DesignGroupToListGroup(listGroup []RandomListGroup, designGroup []RandomGroup) []RandomListGroup {
	groups := make([]RandomListGroup, 0)
	for _, dg := range designGroup {
		if dg.SubGroup != nil && len(dg.SubGroup) > 0 {
			for _, sg := range dg.SubGroup {
				sgP, b := slice.Find(listGroup, func(index int, item RandomListGroup) bool {
					return item.Name == dg.Name+" "+sg.Name
				})
				if b {
					sgg := *sgP
					groups = append(groups, RandomListGroup{
						ID:            dg.ID,
						Code:          dg.Code,
						Name:          sgg.Name,
						ParName:       sgg.ParName,
						SubName:       sgg.SubName,
						Blind:         sg.Blind,
						Ratio:         sgg.Ratio,
						Description:   dg.Description,
						SegmentLength: dg.SegmentLength,
					})
				}
			}
		} else {
			sgP, b := slice.Find(listGroup, func(index int, item RandomListGroup) bool {
				return item.Name == dg.Name && item.SubName == ""
			})
			if b {
				sgg := *sgP
				groups = append(groups, RandomListGroup{
					ID:            dg.ID,
					Code:          dg.Code,
					Name:          sgg.Name,
					ParName:       sgg.ParName,
					SubName:       "",
					Blind:         false,
					Ratio:         sgg.Ratio,
					Description:   dg.Description,
					SegmentLength: dg.SegmentLength,
				})
			}
		}
	}
	return groups
}
func RandomDesignToGroups(randomDesign RandomDesign, isBlindedRole bool, attribute Attribute) []string {
	var groups []string
	for _, group := range randomDesign.Info.Groups {
		if group.SubGroup != nil && len(group.SubGroup) > 0 {
			for _, subGroup := range group.SubGroup {
				if isBlindedRole {
					if attribute.AttributeInfo.Blind && subGroup.Blind {
						groups = append(groups, BlindData+" "+BlindData)
					} else if attribute.AttributeInfo.Blind && !subGroup.Blind {
						groups = append(groups, BlindData+" "+subGroup.Name)
					} else if !attribute.AttributeInfo.Blind && subGroup.Blind {
						groups = append(groups, group.Name+" "+BlindData)
					} else {
						groups = append(groups, group.Name+" "+subGroup.Name)
					}
				} else {
					groups = append(groups, group.Name+" "+subGroup.Name)
				}
			}
		} else {
			if isBlindedRole && attribute.AttributeInfo.Blind {
				groups = append(groups, BlindData)
			} else {
				groups = append(groups, group.Name)
			}
		}
	}
	return groups
}

func RandomDesignToGroupSub(randomDesign RandomDesign, isBlindedRole bool, attribute Attribute) []string {
	groups := make([]string, 0)
	for _, group := range randomDesign.Info.Groups {
		if group.SubGroup != nil && len(group.SubGroup) > 0 {
			for _, subGroup := range group.SubGroup {
				if isBlindedRole {
					if attribute.AttributeInfo.Blind && subGroup.Blind {
						groups = append(groups, BlindData+"("+BlindData+")")
					} else if attribute.AttributeInfo.Blind && !subGroup.Blind {
						groups = append(groups, BlindData+"("+subGroup.Name+")")
					} else if !attribute.AttributeInfo.Blind && subGroup.Blind {
						groups = append(groups, group.Name+"("+BlindData+")")
					} else {
						groups = append(groups, group.Name+"("+subGroup.Name+")")
					}
				} else {
					groups = append(groups, group.Name+"("+subGroup.Name+")")
				}
			}
		} else {
			if isBlindedRole && attribute.AttributeInfo.Blind {
				groups = append(groups, BlindData)
			} else {
				groups = append(groups, group.Name)
			}
		}
	}
	return groups
}

type MainGroup struct {
	// 代码
	Code string `json:"code"`
	// 名称
	Name string `json:"name"`
	//状态
	Status *int `json:"status" bson:"status"` //1或者nil有效，2无效
	//子组别
	ViceGroup []ViceGroup `json:"viceGroup" bson:"vice_group"`
}

type ViceGroup struct {
	Name  string `json:"name" bson:"name"`
	Blind bool   `json:"blind" bson:"blind"`
}
