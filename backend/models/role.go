package models

import "go.mongodb.org/mongo-driver/bson/primitive"

// Role ...
type Role struct {
	ID          primitive.ObjectID `json:"id" bson:"_id"`
	Name        string             `json:"name"`
	Scope       string             `json:"scope"`
	Description string             `json:"description"`
}

type RoleBlind struct {
	ID          primitive.ObjectID `json:"id" bson:"_id"`
	Name        string             `json:"name"`
	Scope       string             `json:"scope"`
	Description string             `json:"description"`
	Blind       bool               `json:"blind"`
}
