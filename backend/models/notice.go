package models

import "go.mongodb.org/mongo-driver/bson/primitive"

// Notice ..
type Notice struct {
}

// NoticeConfig ..
type NoticeConfig struct {
	ID                   primitive.ObjectID   `json:"id" bson:"_id"`
	CustomerID           primitive.ObjectID   `json:"customerId" bson:"customer_id"`
	ProjectID            primitive.ObjectID   `json:"projectId" bson:"project_id"`
	EnvironmentID        primitive.ObjectID   `json:"envId" bson:"env_id"`
	Key                  string               `json:"key" bson:"key"`               // 邮件通知类型
	Repeatable           bool                 `json:"repeatable" bson:"repeatable"` // 是否重复
	Cycle                string               `json:"cycle" bson:"cycle"`           // 重复触发的周期，如"1h", "60m", "3600s"
	Roles                []primitive.ObjectID `json:"roles" bson:"roles"`           // 发送目标角色
	TimeoutDays          int                  `json:"timeoutDays" bson:"timeout_days"`
	ForecastTime         *int                 `json:"forecastTime" bson:"forecast_time"`
	SendDays             int                  `json:"sendDays" bson:"send_days"`
	FieldsConfig         []string             `json:"fieldsConfig" bson:"fields_config" `                 //配置字段
	State                []string             `json:"state" bson:"state"`                                 //场景
	Automatic            int                  `json:"automatic" bson:"automatic"`                         //邮件语言-自动任务 1中文2英文3中英文
	Manual               int                  `json:"manual" bson:"manual"`                               //邮件语言-手动任务 1中文2英文3中英文
	ExcludeRecipientList []string             `json:"excludeRecipientList" bson:"exclude_recipient_list"` //排除收件人-邮箱
	UnbindEmailList      []string             `json:"unbindEmailList" bson:"unbind_email_list"`           //排除收件人中------解绑的用户邮箱
}
