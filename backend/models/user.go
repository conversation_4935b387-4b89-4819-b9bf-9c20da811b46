package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

const LoginTypeLogin = 1
const LoginTypeRefresh = 2
const LoginTypeUnlock = 3

type UserLoginReq struct {
	Token    string `json:"token"`
	Username string `json:"username,omitempty"`
	Password string `json:"password,omitempty"`
	Type     int    `json:"type"` //1登录 2刷新 3解锁
}

// User ...
type User struct {
	ID             primitive.ObjectID `json:"id" bson:"_id"`
	Group          int8               `json:"group"`
	Admin          bool               `json:"admin"`
	ProjectAdmin   bool               `json:"project_admin"`
	UserInfo       `json:"info" bson:"info"`
	Roles          []primitive.ObjectID `json:"roles"`
	Deleted        bool                 `json:"deleted" bson:"deleted"`
	CloudId        primitive.ObjectID   `json:"cloudId" bson:"cloud_id"`
	UserSettings   `json:"settings" bson:"settings"`
	CloseCustomer  []primitive.ObjectID `json:"closeCustomer" bson:"close_customer"`
	RegistrationId string               `json:"registrationId" bson:"registration_id"`
	AppLanguage    string               `json:"appLanguage" bson:"app_language"` // app语言
}

// UserInfo ...
type UserInfo struct {
	EmailLanguage *string `json:"emailLanguage" validate:"required,email_language"`
	Email         string  `json:"email" validate:"required,email"`
	Name          string  `json:"name" validate:"required"`
	Phone         string  `json:"phone" validate:"phone"`
	Company       string  `json:"company"`
	Description   string  `json:"description"`
	Version       int     `json:"version"`
	Status        int8    `json:"status"` // 0未激活 1已激活
	Unicode       int32   `json:"unicode" bson:"unicode"`
}

// UserInfoList ...
type UserInfoList struct {
	EmailLanguage *string              `json:"emailLanguage" validate:"required,email_language"`
	EmailList     []string             `json:"emailList" validate:"required,email_list"`
	Name          string               `json:"name" validate:"required"`
	Phone         string               `json:"phone" validate:"phone"`
	Company       string               `json:"company"`
	Description   string               `json:"description"`
	Version       int                  `json:"version"`
	Status        int8                 `json:"status"` // 0未激活 1已激活
	Unicode       int32                `json:"unicode" bson:"unicode"`
	Roles         []primitive.ObjectID `json:"roles"`
}

// ProjectRole ...
type ProjectRole struct {
	ID       primitive.ObjectID `json:"id" bson:"id"`
	Name     string             `json:"name" bson:"name"`
	Scope    string             `json:"scope" bson:"scope"`
	Template int                `json:"template" bson:"template"`
	Type     int                `json:"type" bson:"type"`
	Status   int                `json:"status" bson:"status"`
}

// BatchUser ...
type BatchUser struct {
	UserIdList []primitive.ObjectID `json:"userIdList"`
	ProjectId  primitive.ObjectID   `json:"projectId"`
}

// NotAvailableEmail ...
type NotAvailableEmail struct {
	Email  string `json:"email"`
	Reason string `json:"reason"`
}

// UserRoleEmail ...
type UserRoleEmail struct {
	UserId   primitive.ObjectID `json:"userId"`
	UserName string             `json:"userName"`
	Email    string             `json:"email"`
	RoleList []ProjectRole      `json:"roleList"`
}

// UserRole ...
type UserRole struct {
	UserIdList []primitive.ObjectID `json:"userIdList"`
	RoleIdList []primitive.ObjectID `json:"roleIdList"`
}

// UserCustomer ...
type UserCustomer struct {
	ID           primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID   primitive.ObjectID `json:"customerId" bson:"customer_id"`
	UserID       primitive.ObjectID `json:"userId" bson:"user_id"`
	Admin        bool               `json:"admin" bson:"admin"`                // 是否客户管理员
	ProjectAdmin bool               `json:"projectAdmin" bson:"project_admin"` // 是否项目管理员
}

// UserProjectEnvironment ...
type UserProjectEnvironment struct {
	ID             primitive.ObjectID   `json:"id" bson:"_id"`
	CustomerID     primitive.ObjectID   `json:"customerId" bson:"customer_id"`
	ProjectID      primitive.ObjectID   `json:"projectId" bson:"project_id"`
	EnvID          primitive.ObjectID   `json:"envId" bson:"env_id"`
	UserID         primitive.ObjectID   `json:"userId" bson:"user_id"`
	Roles          []primitive.ObjectID `json:"roles"`
	App            bool                 `json:"app" bson:"app"`
	EmailLanguage  *string              `json:"emailLanguage" bson:"email_language"`
	UnblindingCode UnblindingCode       `json:"unblindingCode" bson:"unblinding_code"`
	Unbind         bool                 `json:"unbind" bson:"unbind"` //有效、无效
	//FocusOnMark    int                  `json:"focusOnMark" bson:"focus_on_mark"` // 关注项目标记 0.未关注 1.已关注
}

// UserFocusProject ...
type UserFocusProject struct {
	ID         primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID  primitive.ObjectID `json:"projectId" bson:"project_id"`
	UserID     primitive.ObjectID `json:"userId" bson:"user_id"`
	CreateTime time.Duration      `json:"createTime" bson:"create_time"` // 记录创建时间(用于已关注项目的排序)
}

type UnblindingCode struct {
	AvailableCode []string `json:"availableCode" bson:"available_code"`
	UsedCode      []string `json:"usedCode" bson:"used_code"`
}

// Login ...
type Login struct {
	Token    string `json:"token" bson:"token"`
	User     `json:"user"`
	ExpireAt primitive.DateTime `json:"expireAt" bson:"expire_at"`
	App      int                `json:"app" bson:"app"`
}

type IdentifyCode struct {
	ID       primitive.ObjectID `json:"id" bson:"_id"`
	Phone    string             `json:"phone" bson:"phone"`
	Code     string             `json:"code" bson:"code"`
	ExpireAt primitive.DateTime `json:"expireAt" bson:"expire_at"`
}

// UserSite ...
type UserSite struct {
	ID         primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID  primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvID      primitive.ObjectID `json:"envId" bson:"env_id"`
	UserID     primitive.ObjectID `json:"userId" bson:"user_id"`
	SiteID     primitive.ObjectID `json:"siteId" bson:"site_id"`
}

// UserSites ..
type UserSites struct {
	CustomerID primitive.ObjectID   `json:"customerId"`
	ProjectID  primitive.ObjectID   `json:"projectId"`
	EnvID      primitive.ObjectID   `json:"envId"`
	UserID     primitive.ObjectID   `json:"userId"`
	Sites      []primitive.ObjectID `json:"sites"`
}

// UserDepot ...
type UserDepot struct {
	ID         primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID  primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvID      primitive.ObjectID `json:"envId" bson:"env_id"`
	UserID     primitive.ObjectID `json:"userId" bson:"user_id"`
	DepotID    primitive.ObjectID `json:"depotId" bson:"depot_id"`
}

// UserSites ..
type UserRolesSitesDepots struct {
	CustomerID          primitive.ObjectID    `json:"customerId"`
	ProjectID           primitive.ObjectID    `json:"projectId"`
	EnvID               primitive.ObjectID    `json:"envId"`
	RolesSitesDepotList []RolesSitesDepotList `json:"rolesSitesDepotList"`
}

// RolesSitesDepots ..
type RolesSitesDepotList struct {
	UserID    primitive.ObjectID   `json:"userId"`
	RoleList  []primitive.ObjectID `json:"roleList"`
	SiteList  []primitive.ObjectID `json:"siteList"`
	DepotList []primitive.ObjectID `json:"depotList"`
}

// UserDepots ...
type UserDepots struct {
	CustomerID primitive.ObjectID   `json:"customerId"`
	ProjectID  primitive.ObjectID   `json:"projectId"`
	EnvID      primitive.ObjectID   `json:"envId"`
	UserID     primitive.ObjectID   `json:"userId"`
	Depots     []primitive.ObjectID `json:"depots"`
}

type UserSettings struct {
	Timezone   string `json:"timezone,omitempty"`   // 时区
	Dateformat string `json:"dateformat,omitempty"` // 日期格式
	Tz         string `json:"tz,omitempty"`         // 时区
}

type RoleUserInfo struct {
	ID      primitive.ObjectID `json:"id" bson:"_id"`
	UserID  primitive.ObjectID `json:"userId" bson:"user_id"`
	CloudId primitive.ObjectID `json:"cloudId" bson:"cloud_id"`
	Unbind  bool               `json:"unbind" bson:"unbind"` //有效、无效
	Name    string             `json:"name" validate:"required"`
	Email   string             `json:"email" validate:"required,email"`
	Phone   string             `json:"phone" validate:"phone"`
}

type UserRegistrationIdAppLanguage struct {
	RegistrationId string `json:"registrationId"` // 推送的appID
	AppLanguage    string `json:"appLanguage"`    // app语言
}
