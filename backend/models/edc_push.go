package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// EdcPush ..
type EdcPush struct {
	ID             primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID     primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID      primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID  primitive.ObjectID `json:"envId" bson:"env_id"`
	CohortID       primitive.ObjectID `json:"cohortId" bson:"cohort_id"`
	ProjectSiteID  primitive.ObjectID `json:"projectSiteID" bson:"project_site_id"`
	OID            primitive.ObjectID `json:"oid" bson:"oid"`
	Content        Content            `json:"content" bson:"content"`                 // 推送内容
	PushMode       int                `json:"pushMode" bson:"push_mode"`              // 推送方式 1.自动 2.手动
	Source         int                `json:"source" bson:"source"`                   // 来源 1.随机 2.发药
	SourceType     int                `json:"sourceType" bson:"source_type"`          // 来源具体细分 0.未知 1.登记 2.修改 3.随机 4.发药 5.访视外发药 6.药物替换 7.药物补发 8.药物撤销 9.药物取回 10.实际用药 11.受试者替换 12.编辑实际分层 13.筛选 14.切换群组
	SourceTypeList []int              `json:"sourceTypeList" bson:"source_type_list"` // 推送历史数据时的数组，在前端展示该字段
	SerialNumber   int                `json:"serialNumber" bson:"serial_number"`      // 序列号
	Status         int                `json:"status" bson:"status"`                   // 推送状态 0.推送中 1.成功 2.失败 3.失效 4.处理中 5.不处理
	Retry          int                `json:"retry" bson:"retry"`                     // 重试次数共5次（1/2/3/4/5）
	SendTime       time.Duration      `json:"sendTime" bson:"send_time"`              // 发送时间
	CreateTime     time.Duration      `json:"createTime" bson:"create_time"`          // 创建时间
	Result         interface{}        `json:"result" bson:"result"`                   // 返回结果（包含成功失败）
	//HttpRspBody       HTTPRspBody        `json:"httpRspBody" bson:"http_rsp_body"`           // EDC返回结果Body v2.11 作废
	HttpRspBody       HTTPRspBodyNew `json:"httpRspBody" bson:"http_rsp_body"`            // TODO EDC返回结果Body v2.11新增
	ProcessingResults string         `json:"processingResults" bson:"processing_results"` // 处理结果
	Location          string         `bson:"location" json:"location"`                    //location时区
	EdcFields         []string       `json:"edcFields" bson:"edc_fields"`                 // EDC配置的字段
	EecErrorCode      int            `json:"edcErrorCode"  bson:"edc_error_code"`         // TODO v2.11新增
}

type Content struct {
	Env              string `json:"env" bson:"env"`
	Project          string `json:"project" bson:"project"`
	Sign             string `json:"sign" bson:"sign"`
	Site             string `json:"site" bson:"site"`
	IrtSubjectID     string `json:"irtSubjectId" bson:"irt_subject_id"`
	ReplaceSubjectID string `json:"replaceSubjectId" bson:"replace_subject_id"`
	ReplaceSubjectNo string `json:"replaceSubjectNo" bson:"replace_subject_no"`
	EdcPushId        string `json:"edcPushId" bson:"edc_push_id"`
	EdcPushLogId     string `json:"edcPushLogId" bson:"edc_push_log_id"`
	SubjectNo        string `json:"subjectNo" bson:"subject_no"`
	SubjectNoPrefix  string `json:"subjectNoPrefix" bson:"subject_no_prefix"`
	Timestamp        int64  `json:"timestamp" bson:"timestamp"`
	Type             string `json:"type" bson:"type"`
	//AsyncFlag    string `json:"asyncFlag" bson:"async_flag"`
	Remarks     string `json:"remarks" bson:"remarks"`
	Cohort      string `json:"cohort" bson:"cohort"`
	SubjectData []Data `json:"subjectData" bson:"subject_data"`
	OldCohort   string `json:"oldCohort" bson:"old_cohort"` // 切换cohort 时旧的cohort
}

type Data struct {
	Field                        string                        `json:"field" bson:"field"`
	Value                        interface{}                   `json:"value" bson:"value"`
	Visit                        string                        `json:"visit" bson:"visit"`
	InstanceRepeatNo             interface{}                   `json:"instanceRepeatNo" bson:"instance_repeat_no"`
	BlockRepeatNo                interface{}                   `json:"blockRepeatNo" bson:"block_repeat_no"`
	DispensingMedicines          []DispensingMedicine          `json:"dispensingMedicines"`
	OtherDispensingMedicines     []OtherDispensingMedicine     `json:"otherDispensingMedicines"`
	RealDispensingMedicines      []DispensingMedicine          `json:"realDispensingMedicines"`
	RealOtherDispensingMedicines []OtherDispensingMedicineInfo `json:"realOtherDispensingMedicines"`
}

// 推送对象
type PushContent struct {
	Env              string `json:"env" bson:"env"`
	Project          string `json:"project" bson:"project"`
	Sign             string `json:"sign" bson:"sign"`
	Site             string `json:"site" bson:"site"`
	IrtSubjectID     string `json:"irtSubjectId" bson:"irt_subject_id"`
	ReplaceSubjectID string `json:"replaceSubjectId" bson:"replace_subject_id"`
	ReplaceSubjectNo string `json:"replaceSubjectNo" bson:"replace_subject_no"`
	EdcPushId        string `json:"edcPushId" bson:"edc_push_id"`
	EdcPushLogId     string `json:"edcPushLogId" bson:"edc_push_log_id"`
	SubjectNo        string `json:"subjectNo" bson:"subject_no"`
	SubjectNoPrefix  string `json:"subjectNoPrefix" bson:"subject_no_prefix"`
	Timestamp        int64  `json:"timestamp" bson:"timestamp"`
	Type             string `json:"type" bson:"type"`
	//AsyncFlag    string     `json:"asyncFlag" bson:"async_flag"`
	Remarks     string     `json:"remarks" bson:"remarks"`
	Cohort      string     `json:"cohort" bson:"cohort"`
	SubjectData []PushData `json:"subjectData" bson:"subject_data"`
	OldCohort   string     `json:"oldCohort" bson:"old_cohort"` // 切换cohort 时旧的cohort
}

// 推送的数据
type PushData struct {
	Field            string      `json:"field" bson:"field"`
	Value            interface{} `json:"value" bson:"value"`
	Visit            string      `json:"visit" bson:"visit"`
	InstanceRepeatNo interface{} `json:"instanceRepeatNo" bson:"instance_repeat_no"`
	BlockRepeatNo    interface{} `json:"blockRepeatNo" bson:"block_repeat_no"`
}

// PushSend 重新发送
type PushSend struct {
	Type    int                  `json:"type"` // 1.单个发送 2.批量发送 3.全部发送
	PushId  primitive.ObjectID   `json:"pushId"`
	PushIds []primitive.ObjectID `json:"pushIds"`
}

type GroupPush struct {
	OID               primitive.ObjectID `json:"oid" bson:"oid"`
	MergedSourceTypes []int              `json:"mergedSourceTypes" bson:"merged_source_types"`
	SubjectNo         string             `json:"subjectNo" bson:"subject_no"`
	Source            int                `json:"source" bson:"source"` // 来源 1.随机 2.发药
}
