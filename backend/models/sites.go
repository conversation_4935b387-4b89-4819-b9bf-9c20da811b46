package models

import "go.mongodb.org/mongo-driver/bson/primitive"

// Site ...
//type Site struct {
//	ID         primitive.ObjectID `json:"id" bson:"_id"`
//	CustomerID primitive.ObjectID `json:"customerId" bson:"customer_id"`
//	Number     string             `json:"number"`
//	Name       string             `json:"name"`
//	Contacts   string             `json:"contacts"`
//	Phone      string             `json:"phone"`
//	Email      string             `json:"email"`
//	Address    string             `json:"address"`
//	Country    []string           `json:"country" bson:"country"`
//}

type CountryCode struct {
	Country string `json:"country" bson:"country"`
	State   string `json:"state" bson:"state"`
	City    string `json:"city" bson:"city"`
}

type CountryOption struct {
	Value    string          `json:"value" bson:"value"`
	Label    string          `json:"label" bson:"label"`
	CN       string          `json:"cn" bson:"cn"`
	Children []CountryOption `bson:"children" json:"children"`
}

type SitesConfig struct {
	Id          string   `json:"id"`
	Cn          string   `json:"cn"`
	En          string   `json:"en"`
	Country     string   `json:"country"`
	City        string   `json:"city" bson:"city"`
	TimeZone    string   `json:"timeZone" bson:"timeZone"`
	Tz          string   `json:"tz" bson:"tz"`
	CountryCode []string `json:"countryCode" bson:"countryCode"`
}

// EdcSynchronizationSite EDC中心同步
type EdcSynchronizationSite struct {
	Version             string                `json:"v"`
	ProjectNo           string                `json:"projectNo"`
	Env                 string                `json:"env"`
	SynchronizationSite []SynchronizationSite `json:"siteList"`
}

type SynchronizationSite struct {
	SiteNo    string `json:"siteNo"`
	SiteName  string `json:"siteName"`
	ShortName string `json:"shortName"` // 简称
}

type Site struct {
	ID string `bson:"id" json:"id"` //dmp_id
	Tz string `bson:"tz" json:"tz"`
}

type TimeZone struct {
	ID       primitive.ObjectID `json:"id" bson:"_id"`
	TimeZone string             `json:"timeZone" bson:"time_zone"` // 时区
	Tz       string             `bson:"tz" json:"tz"`
}
