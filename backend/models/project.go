package models

import (
	"github.com/duke-git/lancet/v2/slice"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	ProjectStatusProgress  = 0 //项目进行中
	ProjectStatusFinish    = 1 //项目已完成
	ProjectStatusClose     = 2 //项目已关闭
	ProjectStatusPause     = 3 //项目已暂停
	ProjectStatusTerminate = 4 //项目已终止
	CohortStatusDraft      = 1 //cohort草稿
	CohortStatusEnrollment = 2 //cohort入组
	CohortStatusComplete   = 3 //cohort完成
	CohortStatusStop       = 4 //cohort停止
)

// Project ..
type Project struct {
	ID             primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID     primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectInfo    `json:"info" bson:"info"`
	Environments   []Environment        `json:"envs" bson:"envs"`
	Administrators []primitive.ObjectID `json:"administrators" bson:"administrators"`
	Status         int                  `json:"status" bson:"status"` //0:进行中,1:已完成,2:已关闭,3:已暂停,4:已终止
	Meta           `json:"meta"`
}

// ProjectInfo ...
type ProjectInfo struct {
	Type                int                `json:"type" bson:"type"` // 1 基本研究项目 2 cohort项目 3 在随机项目
	Number              string             `json:"number" bson:"number"`
	Name                string             `json:"name" bson:"name"`
	Sponsor             string             `json:"sponsor" bson:"sponsor"`
	Phone               string             `json:"phone" bson:"phone"`
	Tz                  string             `json:"tz" bson:"tz"`
	TimeZoneStr         string             `json:"timeZoneStr" bson:"timeZoneStr"`
	TimeZone            int                `json:"timeZone" bson:"timeZone"`
	ConnectEdc          int                `json:"connectEdc" bson:"connect_edc"`                   // 1.对接 2.不对接
	EdcSupplier         int                `json:"edcSupplier" bson:"edc_supplier"`                 // EDC供应商: 1.Clinflash　EDC 2.Medidata Rave　EDC
	PushMode            int                `json:"pushMode" bson:"push_mode"`                       // EDC对接数据推送方式(1.EDC实时请求 2.IRT全量推送)
	SynchronizationMode int                `json:"synchronizationMode" bson:"synchronization_mode"` // 1.分步同步 2.一次全量同步
	EdcUrl              string             `json:"edcUrl" bson:"edc_url"`                           // EDC接受随机发药数据的节点
	PushTypeEdc         string             `json:"pushTypeEdc" bson:"push_type_edc"`                // 推送类型(OnlyRandom / RandomAndDrug / OnlyDrug) v2.11版本被作废
	PushRules           int                `json:"pushRules" bson:"push_rules"`                     // 推送规则 1.受试者号 2.受试者UID(EDC对接->IRT全量推送) v2.11版本新增字段
	PushScenario        PushScenario       `json:"pushScenario" bson:"push_scenario"`               // 推送场景(EDC对接->IRT全量推送) v2.11版本新增字段
	VisitRandomization  VisitRandomization `json:"visitRandomization" bson:"visit_Randomization"`   // rave访视、随机配置
	ConnectAli          int                `json:"connectAli" bson:"connect_ali"`                   // 1、对接 2、非对接
	Bound               int                `json:"bound" bson:"bound"`                              //  0 未绑定 1 以绑定 2 已解绑
	AliProjectID        string             `json:"aliProjectId" bson:"ali_project_id"`              // 阿里对接项目ID
	//ConnectAli           int      `json:"connectAli" bson:"connect_ali"`                   // 1、对接 2、非对接
	//Bound                int      `json:"bound" bson:"bound"`                              //  0 未绑定 1 已绑定 2 已解绑
	//AliProjectID         string   `json:"aliProjectId" bson:"ali_project_id"`              // 阿里对接项目ID
	//AliProjectNO         string   `json:"aliProjectNo" bson:"ali_project_No"`              // 阿里对接项目编号
	Description          string   `json:"description" bson:"description"`
	SystemCourses        int      `json:"systemCourses" bson:"system_courses"`                   // 1.开启 2.关闭（系统课程）
	NeedSystemCourses    int      `json:"needSystemCourses" bson:"need_system_courses"`          // 1.强制学习 2.非强制学习（系统课程）
	ConnectLearning      int      `json:"connectLearning" bson:"connect_learning"`               // 1.对接 2.不对接（此字段在v2.8版本里面 改为了项目课程 1.开启 2.关闭）
	NeedLearning         int      `json:"needLearning" bson:"need_learning"`                     // 1.强制学习 2.非强制学习（项目课程）
	NeedLearningEnv      []string `json:"needLearningEnv" bson:"need_learning_env"`              // 需要加的环境（项目课程）
	OrderCheck           int      `json:"orderCheck" bson:"order_check"`                         // 1.定时（包含手动核查） 2.实时 3.不适用
	OrderCheckDay        []int    `json:"orderCheckDay" bson:"order_check_day"`                  // 定时核查自定义日期，默认每天8点
	OrderCheckTime       string   `json:"orderCheckTime" bson:"order_check_time"`                // 定时核查自定义时间
	OrderConfirmation    int      `json:"orderConfirmation" bson:"order_confirmation"`           // 中心回收订单确认 0.关 1开
	DeIsolationApproval  int      `json:"deIsolationApproval" bson:"de_isolation_approval"`      // 解隔离审批 0.关 1开
	ResearchAttribute    int      `json:"researchAttribute" bson:"research_attribute"`           // 0.中心交付管理 1.直接面向患者（DTP）
	UnblindingControl    int      `json:"unblindingControl" bson:"unblinding_control"`           //揭盲控制 0.关 1开
	UnblindingType       int      `json:"unblindingType" bson:"unblinding_type"`                 //揭盲类型 紧急揭盲 0.关 1开
	UnblindingSms        int      `json:"unblindingSms" bson:"unblinding_sms"`                   //确认方式 短信 0.关 1开
	UnblindingProcess    int      `json:"unblindingProcess" bson:"unblinding_process"`           //确认方式 流程操作 0.关 1开
	UnblindingCode       int      `json:"unblindingCode" bson:"unblinding_code"`                 //揭盲码 0.关 1开
	PvUnblindingType     int      `json:"pvUnblindingType" bson:"pv_unblinding_type"`            //揭盲类型 pv揭盲 0.关 1开
	PvUnblindingSms      int      `json:"pvUnblindingSms" bson:"pv_unblinding_sms"`              //pv揭盲 确认方式 短信 0.关 1开
	PvUnblindingProcess  int      `json:"pvUnblindingProcess" bson:"pv_unblinding_process"`      //pv揭盲 确认方式 流程操作 0.关 1开
	IpUnblindingType     int      `json:"ipUnblindingType" bson:"ip_unblinding_type"`            //揭盲类型 pv揭盲 0.关 1开
	IpUnblindingSms      int      `json:"ipUnblindingSms" bson:"ip_unblinding_sms"`              //pv揭盲 确认方式 短信 0.关 1开
	IpUnblindingProcess  int      `json:"ipUnblindingProcess" bson:"ip_unblinding_process"`      //pv揭盲 确认方式 流程操作 0.关 1开
	OrderApprovalControl int      `json:"orderApprovalControl" bson:"order_approval_control"`    //研究中心订单申请审批控制 0.关 1开
	OrderApprovalSms     int      `json:"orderApprovalSms" bson:"order_approval_sms"`            //研究中心订单申请审批方式 短信 0.关 1开
	OrderApprovalProcess int      `json:"orderApprovalProcess" bson:"order_approval_process"`    //研究中心订单申请审批 流程操作 0.关 1开
	Room                 bool     `json:"room" bson:"room"`                                      // jira 2837 控制新的项目不显示房间号相关功能
	StartTime            int      `json:"startTime,omitempty" bson:"start_time,omitempty"`       //开始时间
	EndTime              int      `json:"endTime,omitempty" bson:"end_time,omitempty"`           //结束时间
	PlannedCases         *int     `json:"plannedCases,omitempty" bson:"planned_cases,omitempty"` //计划病例数
	StatusReason         string   `json:"statusReason" bson:"status_reason"`
}

// Environment ...
type Environment struct {
	ID                 primitive.ObjectID `json:"id" bson:"id"`
	Name               string             `json:"name"`
	LockConfig         bool               `json:"lockConfig"`
	Capacity           *int               `json:"capacity" bson:"capacity"`                      //入组上限 废弃
	ReminderThresholds *int               `json:"reminderThresholds" bson:"reminder_thresholds"` //提醒阈值 废弃
	AlertThresholds    []AlertThreshold   `json:"alertThresholds" bson:"alert_thresholds"`
	Status             *int               `json:"status" bson:"status"` // 1草稿 2入组 3完成入组 4停止 5入组已满
	Cohorts            []Cohort           `json:"cohorts"`
	IsCopy             bool               `json:"isCopy" bson:"is_copy"` //是否PROD复制
}
type AlertThreshold struct {
	Type       int  `json:"type" bson:"type"` //1已登记 7筛选成功 3入组
	Capacity   int  `json:"capacity" bson:"capacity"`
	Thresholds *int `json:"thresholds" bson:"thresholds"`
}

// Cohort ...
type Cohort struct {
	ID                 primitive.ObjectID `json:"id" bson:"id"`
	LastID             primitive.ObjectID `json:"lastId" bson:"last_id"`
	Type               int8               `json:"type" bson:"type"` //只有群组项目有用，0默认群组类型，1再随机类型
	Name               string             `json:"name"`
	ReRandomName       string             `json:"reRandomName" bson:"re_random_name"`            //群组项目再随机类型的阶段名称
	Capacity           int                `json:"capacity"`                                      //废弃
	ReminderThresholds *int               `json:"reminderThresholds" bson:"reminder_thresholds"` //提醒阈值 废弃
	AlertThresholds    []AlertThreshold   `json:"alertThresholds" bson:"alert_thresholds"`
	Factor             string             `json:"factor"` // 该字段用于对接
	Status             int                `json:"status"` // 1草稿 2入组 3完成入组 4停止 5入组已满
	CopyFrom           primitive.ObjectID `json:"copyFrom" bson:"copy_from"`
	IsCopyToProd       bool               `json:"isCopyToProd" bson:"is_copy_to_prod"` //是否单独新增并复制到PROD
	IsCopy             bool               `json:"isCopy" bson:"is_copy"`               //是否PROD复制
}

// 推送场景
type PushScenario struct {
	RegisterPush          bool `json:"registerPush" bson:"register_push"`                     // 登记是否推送
	UpdateRandomFrontPush bool `json:"updateRandomFrontPush" bson:"update_random_front_push"` // 修改 随机前是否推送
	UpdateRandomAfterPush bool `json:"updateRandomAfterPush" bson:"update_random_after_push"` // 修改 随机后是否推送
	RandomPush            bool `json:"randomPush" bson:"random_push"`                         // 随机是否推送
	RandomBlockPush       bool `json:"randomBlockPush" bson:"random_block_push"`              // 分层不一致是否阻断随机
	FormRandomBlockPush   bool `json:"formRandomBlockPush" bson:"form_random_block_push"`     // 表单不一致是否阻断随机
	CohortRandomBlockPush bool `json:"cohortRandomBlockPush" bson:"cohort_random_block_push"` // cohort不一致是否阻断随机
	DispensingPush        bool `json:"dispensingPush" bson:"dispensing_push"`                 // 发药是否推送
	ScreenPush            bool `json:"screenPush" bson:"screen_push"`                         // 筛选是否推送  v2.12版本新增字段
}

// UpdateCohort ...
type UpdateCohort struct {
	ID                 primitive.ObjectID `json:"id"`
	LastID             primitive.ObjectID `json:"lastId" bson:"last_id"`
	Type               int8               `json:"type"`
	Name               string             `json:"name"`
	ReRandomName       string             `json:"reRandomName" bson:"re_random_name"` //群组项目再随机类型的阶段名称
	Capacity           int                `json:"capacity"`
	ReminderThresholds *int               `json:"reminderThresholds"` //提醒阈值
	AlertThresholds    []AlertThreshold   `json:"alertThresholds" bson:"alert_thresholds"`
	Factor             string             `json:"factor"` // 该字段用于对接
	Status             int8               `json:"status"` // 1草稿 2入组 3完成入组 4停止
	Password           string             `json:"password"`
}

type UpdateCohortStatus struct {
	ProjectID primitive.ObjectID `json:"projectId"`
	EnvID     primitive.ObjectID `json:"envId"`
	CohortID  primitive.ObjectID `json:"cohortId"`
	Status    int                `json:"status"` // 1草稿 2入组 3完成入组 4停止
	Password  string             `json:"password"`
}

// ProjectEnvironmentUser ..
type ProjectEnvironmentUser struct {
	CustomerID               primitive.ObjectID   `json:"customerId" bson:"customer_id"`
	ProjectID                primitive.ObjectID   `json:"projectId" bson:"project_id"`
	EnvID                    primitive.ObjectID   `json:"envId" bson:"env_id"`
	EmailLanguage            *string              `json:"emailLanguage" bson:"email_language"`
	Email                    string               `json:"email" bson:"email"`
	Roles                    []primitive.ObjectID `json:"roles"`
	UnblindingAvailableCount int                  `json:"unblindingAvailableCount" bson:"unblinding_available_count"`
}

// ProjectEnvironmentUserList ..
type ProjectEnvironmentUserList struct {
	CustomerID               primitive.ObjectID   `json:"customerId" bson:"customer_id"`
	ProjectID                primitive.ObjectID   `json:"projectId" bson:"project_id"`
	EnvID                    primitive.ObjectID   `json:"envId" bson:"env_id"`
	EmailLanguage            *string              `json:"emailLanguage" bson:"email_language"`
	EmailList                []string             `json:"emailList" bson:"email_list"`
	Roles                    []primitive.ObjectID `json:"roles"`
	UnblindingAvailableCount int                  `json:"unblindingAvailableCount" bson:"unblinding_available_count"`
}

// ProjectAdministrator ..
type ProjectAdministrator struct {
	ProjectID primitive.ObjectID `json:"projectId" bson:"project_id"`
	UserID    primitive.ObjectID `json:"userId" bson:"user_id"`
}

type ProjectSettingResp struct {
	CustomerName  string `json:"customerName"`
	CustomerAdmin bool   `json:"customerAdmin"`
	Project       `json:",inline" bson:",inline"`
}

type ProjectOverview struct {
	ProjectStatus int            `json:"projectStatus" bson:"projectStatus"`              //0:进行中,1:已完成,2:已关闭,3:已暂停,4:已终止
	StartTime     int            `json:"startTime,omitempty" bson:"start_time,omitempty"` //开始时间
	EndTime       int            `json:"endTime,omitempty" bson:"end_time,omitempty"`     //结束时间
	SiteCount     int64          `json:"siteCount"`
	CohortsCases  []CohortsCases `json:"cohorts_cases"` //各cohort、再随机的计划病例数
	//StartTimeStr       string         `json:"startTimeStr,omitempty" bson:"start_time_str,omitempty"` //开始时间
	//EndTimeStr    	   string         `json:"endTimeStr,omitempty" bson:"end_time_str,omitempty"`     //结束时间
	ProjectTimeZone int `json:"projectTimeZone,omitempty" bson:"project_time_zone,omitempty"`
}

type CohortsCases struct {
	CohortName   string `json:"cohort_name,omitempty" bson:"cohort_name,omitempty"`    //计划病例数
	PlannedCases int    `json:"plannedCases,omitempty" bson:"planned_cases,omitempty"` //计划病例数
	ActualCases  int    `json:"actualCases,omitempty" bson:"actual_cases,omitempty"`   //实际病例数
	IsRandom     bool   `json:"is_random" bson:"is_random"`                            //是否随机
}

type ProjectEnvsRoles struct {
	CustomerID            primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID             primitive.ObjectID `json:"projectId" bson:"project_id"`
	ProjectName           string             `json:"projectName" bson:"project_name"`
	ProjectType           int                `json:"projectType" bson:"project_type"`
	ConnectEdc            int                `json:"connectEdc" bson:"connect_edc"`
	EdcSupplier           int                `json:"edcSupplier" bson:"edc_supplier"`
	PushMode              int                `json:"pushMode" bson:"push_mode"`
	RegisterPush          bool               `json:"registerPush" bson:"register_push"`                     // 登记是否推送
	RandomPush            bool               `json:"randomPush" bson:"random_push"`                         // 随机是否推送
	RandomBlockPush       bool               `json:"randomBlockPush" bson:"random_block_push"`              // 分层不一致是否阻断随机
	FormRandomBlockPush   bool               `json:"formRandomBlockPush" bson:"form_random_block_push"`     // 表单不一致是否阻断随机
	CohortRandomBlockPush bool               `json:"cohortRandomBlockPush" bson:"cohort_random_block_push"` // cohort不一致是否阻断随机
	DispensingPush        bool               `json:"dispensingPush" bson:"dispensing_push"`                 // 发药是否推送
	ScreenPush            bool               `json:"screenPush" bson:"screen_push"`                         // 筛选是否推送
	Envs                  []EnvRoles         `json:"envRoles" bson:"env_roles"`
}

type EnvRoles struct {
	EnvID        primitive.ObjectID `json:"envId" bson:"env_id"`
	EnvName      string             `json:"envName" bson:"env_name"`
	Roles        []RoleBlind        `json:"roles" bson:"roles"`
	Cohorts      []CohortInfo       `json:"cohorts" bson:"cohorts"`
	MixedPackage []MixedPackage     `json:"mixedPackage" bson:"mixed_package"` //包装信息配置
}

// CohortInfo ...
type CohortInfo struct {
	ID     primitive.ObjectID `json:"id" bson:"id"`
	Name   string             `json:"name"`
	Status int                `json:"status"` // 1草稿 2入组 3完成入组 4停止
}

type CohortMapping struct {
	OldCohortID primitive.ObjectID `json:"oldCohortID" bson:"old_cohort_id"`
	NewCohortID primitive.ObjectID `json:"newCohortID" bson:"new_cohort_id"`
}

func GetEnv(project Project, envID string) (e Environment, exist bool) {
	for _, env := range project.Environments {
		if env.ID.Hex() == envID {
			return env, true
		}
	}
	return Environment{}, false
}
func GetCohorts(project Project, envID string) (cs []Cohort, exist bool) {
	for _, env := range project.Environments {
		if env.ID.Hex() == envID {
			return env.Cohorts, true
		}
	}
	return []Cohort{}, false
}

func GetCohort(project Project, envID string, cohortID string) (cs Cohort, exist bool) {
	for _, env := range project.Environments {
		if env.ID.Hex() == envID {
			for _, cohort := range env.Cohorts {
				if cohort.ID.Hex() == cohortID {
					return cohort, true
				}
			}
		}
	}
	return Cohort{}, false
}

func GetCohortIds(project Project, envID string) (cIds []primitive.ObjectID, exist bool) {
	for _, env := range project.Environments {
		if env.ID.Hex() == envID {
			cohortIds := slice.Map(env.Cohorts, func(index int, item Cohort) primitive.ObjectID {
				return item.ID
			})
			return cohortIds, true
		}
	}
	return []primitive.ObjectID{}, false
}
func GetAllCohortReRandom(project Project, envID string) (cs []Cohort, exist bool) {
	for _, env := range project.Environments {
		if env.ID.Hex() == envID {
			cohorts := slice.Filter(env.Cohorts, func(index int, item Cohort) bool {
				return item.Type == 1
			})
			return cohorts, true
		}
	}
	return []Cohort{}, false
}
func GetReRandomCohortIds(project Project, envID string, cohortName string) (cIds []primitive.ObjectID, exist bool) {
	for _, env := range project.Environments {
		if env.ID.Hex() == envID {
			cohorts := slice.Filter(env.Cohorts, func(index int, item Cohort) bool {
				return item.Name == cohortName
			})
			cohortIds := slice.Map(cohorts, func(index int, item Cohort) primitive.ObjectID {
				return item.ID
			})
			return cohortIds, true
		}
	}
	return []primitive.ObjectID{}, false
}
func GetReRandomCohorts(project Project, envID string, cohortName string) (cs []Cohort, exist bool) {
	for _, env := range project.Environments {
		if env.ID.Hex() == envID {
			cohorts := slice.Filter(env.Cohorts, func(index int, item Cohort) bool {
				return item.Name == cohortName
			})
			return cohorts, true
		}
	}
	return []Cohort{}, false
}

func GetReRandomCohortFirstId(project Project, envID string, cohortName string) (cIds primitive.ObjectID, exist bool) {
	for _, env := range project.Environments {
		if env.ID.Hex() == envID {
			find, b := slice.Find(env.Cohorts, func(index int, item Cohort) bool {
				return item.Name == cohortName && item.LastID.IsZero()
			})
			if b {
				return find.ID, true
			}
		}
	}
	return primitive.ObjectID{}, false
}

func GetReRandomCohortFirstIds(project Project, envID string) (cIds []primitive.ObjectID, exist bool) {
	for _, env := range project.Environments {
		if env.ID.Hex() == envID {
			cohorts := slice.Filter(env.Cohorts, func(index int, item Cohort) bool {
				return item.Type == 1 && item.LastID.IsZero()
			})
			ids := slice.Map(cohorts, func(index int, item Cohort) primitive.ObjectID {
				return item.ID
			})
			return ids, true
		}
	}
	return []primitive.ObjectID{}, false
}

func GetReRandomCohortSecondId(project Project, envID string, cohortName string) (cIds primitive.ObjectID, exist bool) {
	for _, env := range project.Environments {
		if env.ID.Hex() == envID {
			find, b := slice.Find(env.Cohorts, func(index int, item Cohort) bool {
				return item.Name == cohortName && !item.LastID.IsZero()
			})
			if b {
				return find.ID, true
			}
		}
	}
	return primitive.ObjectID{}, false
}

func GetReRandomCohortSecondIds(project Project, envID string) (cIds []primitive.ObjectID, exist bool) {
	for _, env := range project.Environments {
		if env.ID.Hex() == envID {
			cohorts := slice.Filter(env.Cohorts, func(index int, item Cohort) bool {
				return item.Type == 1 && !item.LastID.IsZero()
			})
			ids := slice.Map(cohorts, func(index int, item Cohort) primitive.ObjectID {
				return item.ID
			})
			return ids, true
		}
	}
	return []primitive.ObjectID{}, false
}

func HaveCohortReRandom(project Project, envID string) (exist bool) {
	for _, env := range project.Environments {
		if env.ID.Hex() == envID {
			for _, cohort := range env.Cohorts {
				if cohort.Type == 1 {
					return true
				}
			}
		}
	}
	return false
}
func IsCohortReRandom(project Project, envID string, cohortName string) (exist bool) {
	for _, env := range project.Environments {
		if env.ID.Hex() == envID {
			for _, cohort := range env.Cohorts {
				if cohort.Type == 1 && cohort.Name == cohortName {
					return true
				}
			}
		}
	}
	return false
}

func HaveCohortReRandomInCohortIds(project Project, envID string, cohortIds []primitive.ObjectID) (exist bool) {
	for _, env := range project.Environments {
		if env.ID.Hex() == envID {
			for _, cohort := range env.Cohorts {
				if slice.Contain(cohortIds, cohort.ID) && cohort.Type == 1 {
					return true
				}
			}
		}
	}
	return false
}

func GetCohortReRandomName(cohort Cohort) string {
	if cohort.Type == 1 {
		return cohort.Name + " - " + cohort.ReRandomName
	}
	return cohort.Name
}

type UpdateCohortSortReq struct {
	ProjectOID primitive.ObjectID   `json:"projectId"`
	EnvOID     primitive.ObjectID   `json:"envId"`
	SortIDs    []primitive.ObjectID `json:"sortIds"`
}
