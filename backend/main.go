package main

import (
	"clinflash-irt/config"
	"clinflash-irt/edc_push_task"
	"clinflash-irt/locales"
	"clinflash-irt/logging"
	"clinflash-irt/service"
	"clinflash-irt/task"
	"clinflash-irt/tools"
	"fmt"
	"log"

	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
)

var g errgroup.Group

func init() {
	config.Init()
	locales.Init()
	tools.InitMongoDB()
	tools.InitDmpGrpcClientConn()
	if config.TASK == "ON" {
		task.Init()
	} else {
		log.Println("Tasks did not started...")
	}
	logging.Init()

	// 初始化EDC推送任务执行器
	edcExecutor := &service.EDCExecutor{}
	edc_push_task.SetExecutor(edcExecutor)
	edc_push_task.InitTaskProcessor()
}

func httpServer() error {
	defer tools.CloseMongoDB()
	defer tools.CloseDmpGrpcConn()
	defer edc_push_task.CloseAnts()
	gin.SetMode(gin.ReleaseMode)
	app := gin.New()
	app.Use(gzip.Gzip(gzip.DefaultCompression))
	spa(app, false)
	routes(app)
	return app.Run(fmt.Sprintf(":%v", config.PORT))
}

func main() {
	g.Go(httpServer)
	if err := g.Wait(); err != nil {
		log.Fatal(err)
	}
}
