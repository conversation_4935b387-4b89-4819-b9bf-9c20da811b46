package tools

import (
	"clinflash-irt/models"

	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// IsBlindedDrugOld 判断是否是盲态药物
func IsBlindedDrugOld(envOID primitive.ObjectID, drugName string) (bool, error) {
	//查询药物配置是否是盲态的
	match := bson.M{"env_id": envOID}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$unwind", Value: "$configures"}},
		{{Key: "$match", Value: bson.M{"configures.open_setting": bson.M{"$ne": true}, "configures.values.drugname": drugName}}},
		{{Key: "$project", Value: bson.M{
			"values": "$configures.values",
		}}},
	}
	drugOpenCursor, err := Database.Collection("drug_configure").Aggregate(nil, pipeline)
	if err != nil {
		return true, errors.WithStack(err)
	}
	var drugOpenData []models.DrugConfigureInfo
	err = drugOpenCursor.All(nil, &drugOpenData)
	if err != nil {
		return true, errors.WithStack(err)
	}

	if len(drugOpenData) > 0 {
		return true, nil
	}
	return false, nil
}

// IsOtherDrug 判断是否是未编号药物
func IsOtherDrugMap(envOID primitive.ObjectID) (map[string]bool, error) {
	// 获取该环境下
	data := map[string]bool{}
	match := bson.M{"env_id": envOID}
	type DrugConfigure struct {
		DrugValue models.DrugValue `bson:"drug_value"`
	}
	var drugConfigure []DrugConfigure
	cursor, err := Database.Collection("drug_configure").Aggregate(nil, mongo.Pipeline{
		{{"$match", match}},
		{{"$unwind", "$configures"}},
		{{"$unwind", "$configures.values"}},
		{{"$project", bson.M{
			"drug_value": "$configures.values",
		}}},
	})
	if err != nil {
		return data, errors.WithStack(err)
	}
	err = cursor.All(nil, &drugConfigure)
	if err != nil {
		return data, errors.WithStack(err)
	}

	for _, configure := range drugConfigure {
		if configure.DrugValue.IsOther {
			data[configure.DrugValue.DrugName] = true
		}
	}
	return data, err
}

// IsBlindDrugMapOld ...
func IsBlindDrugMapOld(envOID primitive.ObjectID) (map[string]bool, error) {
	// 获取该环境下
	data := map[string]bool{}
	match := bson.M{"env_id": envOID}
	type DrugConfigure struct {
		DrugValue   models.DrugValue `bson:"drug_value"`
		Other       []interface{}    `bson:"medicine_other_institute"`
		OpenSetting bool             `bson:"open_setting"` // 0 原始配置、 1、开放配置

	}
	var drugConfigure []DrugConfigure

	cursor, err := Database.Collection("drug_configure").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$unwind", Value: "$configures"}},
		{{Key: "$unwind", Value: "$configures.values"}},
		{{Key: "$project", Value: bson.M{
			"drug_value":   "$configures.values",
			"open_setting": "$configures.open_setting",
		}}},
	})
	if err != nil {
		return data, errors.WithStack(err)
	}

	err = cursor.All(nil, &drugConfigure)
	if err != nil {
		return data, errors.WithStack(err)
	}
	for _, configure := range drugConfigure {
		if !configure.OpenSetting {
			data[configure.DrugValue.DrugName] = true
		}
	}
	return data, err
}

// 判断包装运输是否开启，如果开启，药物包装的数量  返回结果1：是否开启包装运输，map[药物名称]整包包装数量，map[药物名称]药物单独包装数量,包装药物数组，包装药物
func IsOpenPackage(envOID primitive.ObjectID) (bool, map[string]int, map[string]int, map[string][]string, []models.MixedPackage, error) {
	packageIsOpen := false
	packageConfigs := make(map[string][]string)
	packageAllDrugNames := make(map[string]int)
	packageDrugNames := make(map[string]int)
	var drugPackageConfigure models.DrugPackageConfigure
	match := bson.M{"env_id": envOID}
	err := Database.Collection("drug_package_configure").FindOne(nil, match).Decode(&drugPackageConfigure)
	if err != nil && err != mongo.ErrNoDocuments {
		return packageIsOpen, nil, nil, nil, nil, err
	}
	if drugPackageConfigure.IsOpen {
		packageIsOpen = true
		if len(drugPackageConfigure.MixedPackage) > 0 {
			for _, mixedPackage := range drugPackageConfigure.MixedPackage {
				if len(mixedPackage.PackageConfig) > 0 { //混合包装
					packageArray := make([]string, len(mixedPackage.PackageConfig))
					number := 0
					for _, packageConfig := range mixedPackage.PackageConfig {
						number = number + packageConfig.Number
					}
					for i, packageConfig := range mixedPackage.PackageConfig {
						packageArray[i] = packageConfig.Name
						packageDrugNames[packageConfig.Name] = packageConfig.Number
						packageAllDrugNames[packageConfig.Name] = number
						packageConfigs[packageConfig.Name] = packageArray
					}
				}
			}
		}
	}

	return packageIsOpen, packageAllDrugNames, packageDrugNames, packageConfigs, drugPackageConfigure.MixedPackage, nil
}

// IsBlindedDrug...
func IsBlindedDrug(envOID primitive.ObjectID, drugName string) (bool, error) {
	//查询药物配置是否是盲态的
	match := bson.M{"env_id": envOID}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$unwind", Value: "$configures"}},
		{{Key: "$unwind", Value: "$configures.values"}},
		{{Key: "$match", Value: bson.M{"configures.values.drugname": drugName}}},
		{{Key: "$match", Value: bson.M{"$or": bson.A{bson.M{"configures.open_setting": bson.M{"$in": bson.A{1, 3}}, "configures.values.is_open": bson.M{"$ne": true}}}}}},
		{{Key: "$project", Value: bson.M{
			"values": "$configures.values",
		}}},
	}
	drugOpenCursor, err := Database.Collection("drug_configure").Aggregate(nil, pipeline)
	if err != nil {
		return true, errors.WithStack(err)
	}
	var drugOpenData []map[string]interface{}
	err = drugOpenCursor.All(nil, &drugOpenData)
	if err != nil {
		return true, errors.WithStack(err)
	}

	if len(drugOpenData) > 0 {
		return true, nil
	}
	return false, nil
}

// IsBlindDrugMap ...
func IsBlindDrugMap(envOID primitive.ObjectID) (map[string]bool, error) {
	// 获取该环境下
	data := map[string]bool{}
	match := bson.M{"env_id": envOID}
	type DrugConfigure struct {
		DrugValue   models.DrugValue `bson:"drug_value"`
		Other       []interface{}    `bson:"medicine_other_institute"`
		OpenSetting int              `bson:"open_setting"` // 1 原始配置、 2、开放配置 3、公式计算

	}
	var drugConfigure []DrugConfigure

	cursor, err := Database.Collection("drug_configure").Aggregate(nil, mongo.Pipeline{
		{{"$match", match}},
		{{"$unwind", "$configures"}},
		{{"$unwind", "$configures.values"}},
		{{"$project", bson.M{
			"drug_value":   "$configures.values",
			"open_setting": "$configures.open_setting",
		}}},
	})
	if err != nil {
		return data, errors.WithStack(err)
	}

	err = cursor.All(nil, &drugConfigure)
	if err != nil {
		return data, errors.WithStack(err)
	}
	for _, configure := range drugConfigure {
		if configure.OpenSetting != 2 && !configure.DrugValue.IsOpen {
			data[configure.DrugValue.DrugName] = true
		}
	}
	return data, err
}

// AllDrugMap ...
func AllDrugMap(envOID primitive.ObjectID) (map[string]bool, error) {
	// 获取该环境下
	data := map[string]bool{}
	match := bson.M{"env_id": envOID}
	type DrugConfigure struct {
		DrugValue   models.DrugValue `bson:"drug_value"`
		Other       []interface{}    `bson:"medicine_other_institute"`
		OpenSetting int              `bson:"open_setting"` // 1 原始配置、 2、开放配置 3、公式计算

	}
	var drugConfigure []DrugConfigure

	cursor, err := Database.Collection("drug_configure").Aggregate(nil, mongo.Pipeline{
		{{"$match", match}},
		{{"$unwind", "$configures"}},
		{{"$unwind", "$configures.values"}},
		{{"$project", bson.M{
			"drug_value":   "$configures.values",
			"open_setting": "$configures.open_setting",
		}}},
	})
	if err != nil {
		return data, errors.WithStack(err)
	}

	err = cursor.All(nil, &drugConfigure)
	if err != nil {
		return data, errors.WithStack(err)
	}
	for _, configure := range drugConfigure {
		if configure.OpenSetting != 2 && !configure.DrugValue.IsOpen {
			data[configure.DrugValue.DrugName] = true
		} else {
			data[configure.DrugValue.DrugName] = false
		}
	}
	return data, err
}

func GetOtherMedicineKey(otherMedicine models.OtherMedicineFreeze, otherMedicineInfo models.FreezeOtherMedicines) (models.MedicineOtherKey, error) {
	orderType := otherMedicine.InstituteType
	if otherMedicineInfo.ExpireDate == "-" {
		otherMedicineInfo.ExpireDate = ""
	}
	if otherMedicineInfo.Batch == "-" {
		otherMedicineInfo.Batch = ""
	}
	//查询otherKey
	var medicineOtherKey models.MedicineOtherKey
	otherKeyMatch := bson.M{"customer_id": otherMedicine.CustomerID, "project_id": otherMedicine.ProjectID,
		"env_id": otherMedicine.EnvironmentID, "name": otherMedicineInfo.Name, "expiration_date": otherMedicineInfo.ExpireDate, "batch_number": otherMedicineInfo.Batch}
	if orderType == 2 { //仓库
		otherKeyMatch["storehouse_id"] = otherMedicineInfo.StorehouseID
	} else {
		otherKeyMatch["site_id"] = otherMedicineInfo.SiteID
	}
	err := Database.Collection("medicine_other_key").FindOne(nil, otherKeyMatch).Decode(&medicineOtherKey)
	if err == mongo.ErrNoDocuments {
		//轨迹key表数据
		medicineOtherKey.ID = primitive.NewObjectID()
		medicineOtherKey.ProjectID = otherMedicine.ProjectID
		medicineOtherKey.EnvironmentID = otherMedicine.EnvironmentID
		medicineOtherKey.CustomerID = otherMedicine.CustomerID
		if orderType == 2 {
			medicineOtherKey.StorehouseID = otherMedicineInfo.StorehouseID
		} else {
			medicineOtherKey.SiteID = otherMedicineInfo.SiteID
		}
		medicineOtherKey.Name = otherMedicineInfo.Name
		medicineOtherKey.Batch = otherMedicineInfo.Batch
		medicineOtherKey.ExpireDate = otherMedicineInfo.ExpireDate
		//medicineOtherKey.Spec = otherMedicine.Spec
		_, err = Database.Collection("medicine_other_key").InsertOne(nil, medicineOtherKey)
		if err != nil {
			return medicineOtherKey, errors.WithStack(err)
		}
	}
	if err != nil && err != mongo.ErrNoDocuments {
		return medicineOtherKey, errors.WithStack(err)
	}
	return medicineOtherKey, nil
}

func GetProjectSiteLangName(projectSite models.ProjectSite, lang string) string {
	if projectSite.ShortName != "" {
		return projectSite.ShortName
	}
	name := projectSite.Name
	if lang == "zh" && projectSite.Name != "" {
		name = projectSite.Name
	}
	if lang == "en" && projectSite.NameEn != "" {
		name = projectSite.NameEn
	}
	return name
}
