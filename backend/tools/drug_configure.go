package tools

import (
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"github.com/dengsgo/math-engine/engine"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"math"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// 例：13.4<X<=15
var rangeFormula = "^((\\d+(.\\d+)?)(\\s*<\\s*|\\s*<=\\s*|=))?([xX])((\\s*<\\s*|\\s*<=\\s*|\\s*≤\\s*|=)(\\d+(.\\d+)?))?$"

func CustomFormulaValidation(f string) bool {
	f = strings.ReplaceAll(f, " ", "")
	f = strings.ToLower(f)
	if !strings.Contains(f, "h") && !strings.Contains(f, "w") {
		return false
	}
	if strings.Contains(f, "h") || strings.Contains(f, "w") {
		f = strings.ReplaceAll(f, "h", "100")
		f = strings.ReplaceAll(f, "w", "50")
		_, err := engine.ParseAndExec(f)
		return err == nil
	} else {
		return false
	}
}

func CustomMultipleFormulaValidation(f string, keys []string) (bool, float64) {
	f = strings.ReplaceAll(f, " ", "")
	f = strings.ToLower(f)
	for _, k := range keys {
		f = strings.ReplaceAll(f, k, strconv.FormatFloat(50.5, 'f', -1, 64))
	}
	r, err := engine.ParseAndExec(f)
	if err != nil {
		return false, 0
	}
	return true, r
}
func CustomMultipleFormulaExec(f string, formulas []models.CustomerFormula) (bool, float64) {
	f = strings.ReplaceAll(f, " ", "")
	f = strings.ToLower(f)
	for _, formula := range formulas {
		formulaKey := strings.ToLower(formula.Key)

		f = strings.ReplaceAll(f, formulaKey, strconv.FormatFloat(formula.Value, 'f', -1, 64))
	}
	r, err := engine.ParseAndExec(f)
	if err != nil {
		return false, 0
	}
	return true, r
}
func CustomMultipleFormulaExecNoToLower(f string, formulas []models.CustomerFormula) (bool, float64) {
	f = strings.ReplaceAll(f, " ", "")
	for _, formula := range formulas {
		f = strings.ReplaceAll(f, formula.Key, strconv.FormatFloat(formula.Value, 'f', -1, 64))
	}
	r, err := engine.ParseAndExec(f)
	if err != nil {
		return false, 0
	}
	return true, r
}
func CustomFormulaExec(f string, h string, w string) (bool, float64) {
	f = strings.ReplaceAll(f, " ", "")
	f = strings.ToLower(f)
	f = strings.ReplaceAll(f, "h", h)
	f = strings.ReplaceAll(f, "w", w)
	r, err := engine.ParseAndExec(f)
	if err != nil {
		return false, 0
	}
	return true, r
}
func RangeFormulaValidation(f string) bool {
	f = strings.ReplaceAll(f, " ", "")
	if len(f) == 1 {
		return false
	}
	re := regexp.MustCompile(rangeFormula)
	return re.MatchString(f)
}

func MatchRangeFormula(x string, f string) (bool, error) {
	f = strings.ReplaceAll(f, " ", "")
	re := regexp.MustCompile(rangeFormula)
	matches := re.FindAllStringSubmatch(f, -1)
	match := matches[0]
	b1 := true
	b2 := true
	var err error
	number1 := match[2]
	symbol1 := match[4]
	if number1 != "" && symbol1 != "" {
		b1, err = cal(number1, x, symbol1)
		if err != nil {
			return false, err
		}
	}
	symbol2 := match[7]
	number2 := match[8]
	if number2 != "" && symbol2 != "" {
		b2, err = cal(x, number2, symbol2)
		if err != nil {
			return false, err
		}
	}
	return b1 && b2, err
}

type Interval struct {
	Start            float64
	End              float64
	IsStartInclusive bool //包不包含左区间
	IsEndInclusive   bool //包不包含右区间
}

// 校验分层计算公式
func ValidateFactorFormula(ctx *gin.Context, formulas []string) error {
	//校验公式是否遵循范围表达式格式
	validations := slice.Map(formulas, func(index int, item string) bool {
		return RangeFormulaValidation(item)
	})
	_, b := slice.Find(validations, func(index int, item bool) bool {
		return !item
	})
	if b {
		return BuildServerError(ctx, "visit_cycle_formula_error")
	}

	//取出范围值的边界值
	boundaries := make([]string, 0)
	for _, formula := range formulas {
		boundary := getBoundary(formula)
		boundaries = append(boundaries, boundary...)
	}
	boundaries = slice.Unique(boundaries)
	for _, boundary := range boundaries {
		count := 0
		for _, formula := range formulas {
			matchRangeFormula, err := MatchRangeFormula(boundary, formula)
			if err != nil {
				return err
			}
			if matchRangeFormula {
				count++
			}
		}
		if count > 1 {
			return BuildServerError(ctx, "factor_calc_mapping_converge")
		}
	}
	return nil
}

func getBoundary(f string) []string {
	strs := make([]string, 0)
	f = strings.ReplaceAll(f, " ", "")
	re := regexp.MustCompile(rangeFormula)
	matches := re.FindAllStringSubmatch(f, -1)
	match := matches[0]
	number1 := match[2]
	number2 := match[8]
	if number1 != "" {
		strs = append(strs, number1)
	}
	if number2 != "" {
		strs = append(strs, number2)
	}
	return strs
}

func cal(l string, r string, s string) (bool, error) {
	ld, err := decimal.NewFromString(l)
	if err != nil {
		return false, errors.WithStack(err)
	}
	rd, err := decimal.NewFromString(r)
	if err != nil {
		return false, errors.WithStack(err)
	}
	switch s {
	case "<":
		return ld.LessThan(rd), nil
	case "<=":
		return ld.LessThanOrEqual(rd), nil
	case "≤":
		return ld.LessThanOrEqual(rd), nil
	case "=":
		return ld.Equal(rd), nil
	}
	return false, nil
}

func Formula(ctx *gin.Context, configure models.DrugConfigureInfo, value models.DrugValue, age *string, currentformulaWeight, height *float64) (float64, int, error) {
	number := 0
	bsaCount := 0.0
	var err error
	precision := 0
	if value.Precision != nil {
		precision = *value.Precision
	}
	switch configure.CalculationType {
	case 1:
		//  年龄计算
		if age != nil && *age != "" {
			now := time.Now()
			targetDate, err := time.Parse("2006-01-02", *age)
			if err != nil {
				return 0, 0, errors.WithStack(err)
			}
			// 计算日期差距
			days := int(now.Sub(targetDate).Hours() / 24)
			ageFloat := float64(days) / 365.25
			// 年龄转换  年龄 =  当天 - 出生日期 / 365.25  + 1
			ageFloat = TruncateDecimal(ageFloat, precision, 2) //ageFloat = math.Floor(ageFloat)
			number, err = matchFormulaValue(value, ageFloat)
			if err != nil {
				return 0, 0, errors.WithStack(err)

			}
		}

	case 2:
		//  体重计算
		if currentformulaWeight != nil {
			*currentformulaWeight = TruncateDecimal(*currentformulaWeight, precision, 1) //ageFloat = math.Floor(ageFloat)

			number, err = matchFormulaValue(value, *currentformulaWeight)
			if err != nil {
				return 0, 0, errors.WithStack(err)
			}
		}

	case 3:
		// BSA
		if currentformulaWeight != nil && height != nil {
			bsaCount, number, err = formulaBSA(*currentformulaWeight, *height, *value.UnitCalculationStandard, *value.Specifications.Value, precision)
			if err != nil {
				return 0, 0, errors.WithStack(err)
			}
		}

	case 4:
		// 自定义公式
		bsaCount, number, err = formulaCustomerBSA(ctx, configure.CustomerCalculation, currentformulaWeight, height, *value.UnitCalculationStandard, *value.Specifications.Value, &precision)
		if err != nil {
			return 0, 0, errors.WithStack(err)

		}
	}
	return bsaCount, number, nil
}

func matchFormulaValue(values models.DrugValue, weight float64) (int, error) {
	weightStr := convertor.ToString(weight)
	for _, formula := range values.Formulas {
		rangeFormula, err := MatchRangeFormula(weightStr, formula.Expression)
		if err != nil {
			return 0, errors.WithStack(err)
		}
		if rangeFormula {
			return formula.Value, nil
		}
	}
	return 0, nil
}

func formulaBSA(weight float64, height float64, unitCalculationStandard, specifications float64, precision int) (float64, int, error) {
	//        bsa := 身高*体重/3600 ^2
	//        单位计算标准 * bsa / 规格标准

	bsa := math.Sqrt((weight * height) / 3600)

	bsaCount := unitCalculationStandard * bsa
	count := math.Ceil(bsaCount / specifications)
	return bsaCount, int(count), nil
}

func formulaCustomerBSA(ctx *gin.Context, f string, weight *float64, height *float64, unitCalculationStandard, specifications float64, precision *int) (float64, int, error) {
	//        bsa := 身高*体重/3600 ^2
	//        单位计算标准 * bsa / 规格标准
	f = strings.ToLower(f)
	if (strings.Contains(f, "h") && height == nil) || (strings.Contains(f, "w") && weight == nil) {
		return 0, 0, nil
	}

	err, bsa := CustomFormulaExec(f, convertor.ToString(*height), convertor.ToString(*weight))
	if !err {
		return 0, 0, nil
	}
	bsa = TruncateOrRound(bsa, 14)
	bsaCount := unitCalculationStandard * bsa
	bsaCount = TruncateOrRound(bsaCount, 14)
	count := math.Ceil(bsaCount / specifications)
	return bsaCount, int(count), nil
}

func TruncateDecimal(num float64, decimalPlaces int, useType int) float64 {
	shift := math.Pow(10, float64(decimalPlaces))

	// 四舍五入
	roundedNum := math.Round(num * shift)

	// 对于小数位数为0且结果非整数的情况，向下取整  年龄
	if useType == 2 && decimalPlaces == 0 && roundedNum != math.Floor(num) {
		return math.Floor(num)
	}

	// 对于小数位数为0且结果非整数的情况，向上取整  体重
	if useType == 1 && decimalPlaces == 0 && roundedNum != math.Ceil(num) {
		return math.Ceil(num)
	}

	// 除以 shift 进行还原
	return roundedNum / shift
}

func TruncateOrRound(num float64, decimalPlaces int) float64 {
	shift := math.Pow(10, float64(decimalPlaces))

	// 四舍五入
	roundedNum := math.Round(num * shift)

	// 对于小数位数为0且结果非整数的情况，向下取整
	if decimalPlaces == 0 && roundedNum != math.Floor(num) {
		return math.Floor(num)
	}

	// 除以 shift 进行还原
	return roundedNum / shift
}

func Round(num float64, decimalPlaces int) float64 {
	factor := math.Pow(10, float64(decimalPlaces))
	return math.Round(num*factor) / factor
}

func GetDispensingTypeName(g string, dispensing models.Dispensing, visit models.VisitCycle) string {
	visitName := dispensing.VisitInfo.Name
	if dispensing.VisitSign {
		if dispensing.Reissue == 1 {
			visitName = visitName + "(" + locales.TrWithLang(g, "export.dispensing.reissue") + ")"
		} else {

			visitName = visitName + "(" + locales.TrWithLang(g, "export.dispensing.outVisit") + ")"

			if visit.SetInfo.IsOpen {
				if g == "zh" {
					visitName = visitName + "(" + visit.SetInfo.NameZh + ")"
				} else {
					visitName = visitName + "(" + visit.SetInfo.NameEn + ")"
				}
			}
		}
	}
	return visitName
}
