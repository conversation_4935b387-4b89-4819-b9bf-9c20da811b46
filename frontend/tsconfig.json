{
	"compilerOptions": {
		"baseUrl": "src/",
		"paths": {
			"~/*": ["src/*"]
		},
		// "target": "ES2015",
		// "module": "ES2015",
		// "lib": ["ES2015", "DOM", "DOM.Iterable"],
		"module": "esnext",
        "target": "es6",
        "lib": ["esnext", "dom"],
		"allowJs": true,
		"skipLibCheck": true,
		"esModuleInterop": true,
		"allowSyntheticDefaultImports": true,
		"strict": true,
		"forceConsistentCasingInFileNames": true,
		"noFallthroughCasesInSwitch": true,
		"moduleResolution": "Node",
		"resolveJsonModule": true,
		"isolatedModules": true,
		"noEmit": true,
		"jsx": "react-jsx",
	},
	"include": ["src"],
	"exclude": ["node_modules"],

}