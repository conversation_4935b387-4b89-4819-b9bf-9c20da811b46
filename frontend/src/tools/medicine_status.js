import {medicineStatus} from "../data/data";


export const medicineStatusData = (code_rule, research_attribute) => {
    let medicine_status = medicineStatus
    if (code_rule === 0) {
        medicine_status = medicineStatus.filter(item => (item.value !== 12 && item.value !== 21 && item.value !== 22 && item.value !== 23))
    }
    if (!research_attribute ||  research_attribute === 0) {
        medicine_status = medicine_status.filter(item => (item.value !== 13))
    }
    return medicine_status
}


export const medicineStatusAliData = (code_rule, research_attribute, ali) => {
    let medicine_status = medicineStatus
    if (code_rule === 0) {
        medicine_status = medicineStatus.filter(item => (item.value !== 0 && item.value !== 12 && item.value !== 21 && item.value !== 22 && item.value !== 23))
    }
    if (!research_attribute ||  research_attribute === 0) {
        medicine_status = medicine_status.filter(item => (item.value !== 13))
    }
    if (ali !== 1) {
        medicine_status = medicine_status.filter(item => (item.value !== 8 && item.value !== 9 && item.value !== 10))
    }
    return medicine_status
}