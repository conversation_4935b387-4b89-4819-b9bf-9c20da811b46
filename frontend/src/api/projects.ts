import { del, get, patch, post } from "utils/http";

export const switchAppAccount = (params: object = {}) => patch("/users/app-account", { params });
export const projectEnvironmentUsersSearchList = (params: object = {}) => get("/projects/envs/users/list", { params });
export const downloadProjectEnvironmentUserData = (params: object = {}) => get("/projects/envs/users/download", { params });
export const setProjectEnvironmentUserRoles = (params: object = {}, data: object = {}) => patch("/projects/envs/users/roles", { params, data });
export const setEnvUserUnblindingCode = (params: object = {}, data: object = {}) => post("/projects/envs/users/unblind-codes", { params, data });
export const resendInviteEmail = (params: object = {}) => post("/projects/users/resend-invite-email", { params });
export const addProjectEnvironmentUser = (params: object = {}, data: object = {}) => post("/projects/envs/users", { params, data });
export const unbindProjectEnvironmentUser = (params: object = {}) => post("/projects/envs/users/unbind", { params });
export const batchUnbindProjectEnvironmentUser = (params: object = {}, data: object = {}) => post("/projects/envs/users/batch-unbind", { params, data });
export const reauthorizationProjectEnvironmentUser = (params: object = {}) => post("/projects/envs/users/reauthorization", { params });
export const updateProject = (params: object = {}, data: object = {}) => patch("/projects", { params, data });
export const getProject = (params: object = {}) => get("/projects", { params });
export const getProjectEnvBlind = (params: object = {}) => get("/projects/envs/blind", { params });
export const addEnv = (data: object = {}) => post("/projects/envs", { data });
export const editEnv = (data: object = {}) => patch("/projects/envs", { data });
export const copyEnv = (data: object = {}) => post(`/projects/envs/copy`, { data });
export const updateLock = (params: object = {}, data: object = {}) => patch("/projects/envs/update-lock-config", { params, data });
export const addCohort = (params: object = {}, data: object = {}) => post("/projects/envs/cohorts", { params, data });
export const delCohort = (params: object = {}, data: object = {}) => del("/projects/envs/cohorts", { params, data });
export const updateCohort = (params: object = {}, data: object = {}) => patch("/projects/envs/cohorts", { params, data });
export const updateCohortStatus = (params: object = {}, data: object = {}) => post("/projects/envs/cohorts/status", { params, data });
export const getProjectList = () => get("/projects/list");
export const getViewMultiLanguage = () => get("/projects/view-multiLanguage");
export const getProjectHomeList = (params: object = {}) => get("/projects/home-list", { params });
export const getProjectSettingList = (params: object = {}) => get("/projects/setting-list", { params });
export const getProjectUsers = (params: object = {}, data: object = {}) => post("/projects/users", { params, data });

export const getProjectOverview = (params: object = {}) => get("/projects/overview", { params });
export const copyCohort = (params: object = {}, data: object = {}) => patch("/projects/envs/cohorts/copy", { params, data });

export const addProjectEnvironmentBatchUserVerify = (params: object = {}, data: object = {}) => post("/projects/envs/batch-users-verify", { params, data });
export const addProjectEnvironmentBatchUser = (params: object = {}, data: object = {}) => post("/projects/envs/batch-users", { params, data });



export const getProjectPermissions = (params: object = {}) => get("/projects/permissions", { params });

export const updateProjectType = (params: object = {}) => patch("/projects/type", { params });




export const findProject = (params: object = {}) => get("/projects/find", { params });

export const getCohortStatus = (params: object = {}) => get("/projects/cohort-status", { params });
export const focusOnProjects = (params: object = {}) => post("/projects/user-focus", { params });
export const projectCard = (params: object = {}) => get("/projects/card", { params });

export const getUserProjectElearning = (params: object = {}) => get("/projects/user-project-elearning", { params });
export const getProjectEnvRole = (params: object = {}) => get("/projects/user-role-project-env", { params });
export const getProjectNotice = (params: object = {}) => get("/projects/notice", { params });
export const postProjectNotice = (data: object = {}) => post("/projects/notice", { data });


export const getByCohort = (params: object = {}) => get(`/projects/cohort`, { params });
export const getProjectTimeZone = (params: object = {}) => get(`/projects/time-zone`, { params });

export const getCopyIsProd = (params: object = {}) => get(`/projects/copy/prod`, { params });
export const getCopyIsProdData = (params: object = {}) => get(`/projects/copy/prod/data`, { params });

export const getProjectType = (params: object = {}) => get(`/projects/type`, { params });
export const updateCohortSort = (data: object = {}) => post(`/projects/cohort/drag`, { data });

