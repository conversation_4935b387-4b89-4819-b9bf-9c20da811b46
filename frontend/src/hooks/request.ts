import {useRequest} from "ahooks";
import {useRequestx} from "../context/request";
import {Options, Plugin, Service} from "ahooks/lib/useRequest/src/types"

export const useFetch = (service: Service<unknown, any[]>, options?: Options<unknown, any[]> | undefined, plugins?: Plugin<unknown, any[]>[] | undefined) => {
    
    const ctx = useRequestx();

    return useRequest(service, {
        ...options,
        onError: (options?.onError)?options?.onError:ctx.onError,
    });
};