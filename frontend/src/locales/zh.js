export const message = {
    "error.unknown": "未知错误",
    "error.invalid-request": "请求无效",
    "error.not-logged-in": "未登录",
    "error.unauthorized": "未授权",
    "error.resource-not-found": "资源不存在",
    "common.options": "选项",
    "common.all-options": "全部选项",
    "common.collapse": "收起",
    "common.expand": "展开",
    "common.detail": "详情",
    "common.details": "详情",
    "common.pagination.tip.total": "共{total}条，",
    "common.pagination.tip.pagesize-prefix": "每页",
    "common.pagination.tip.pagesize-suffix": "条",
    "common.all": "全部",
    "common.customer": "客户",
    "common.selectUser": "选择客户",
    "common.add": "新增",
    "common.copy": "复制",
    "common.copy.add": "复制并新增",
    "common.view": "查看",
    "common.push": "发布",
    "common.delete": "删除",
    "common.unbinding": "解绑",
    "common.unbinding.batch": "批量解绑",
    "common.recover": "恢复",
    "common.save": "保存",
    "common.edit": "编辑",
    "common.edit.batch": "批量编辑",
    "common.modify": "修改",
    "common.sprint": "已打印",
    "common.submit": "提交",
    "common.search": "搜索",
    "common.reset": "重置",
    "common.select": "选择",
    "common.select.list.tips": "请先选中列表数据",
    "common.ok": "确定",
    "common.ok.sync": "确定并同步",
    "common.cancel": "取消",
    "common.next": "下一步",
    "common.open": "开启",
    "common.close": "关闭",
    "common.close.batch": "批量关闭",
    "common.operation": "操作",
    "common.enable": "启用",
    "common.active": "激活",
    "common.more": "更多",
    "common.status": "状态",
    "common.timezone": "时区",
    "common.yes": "是",
    "common.no": "否",
    "common.update": "更新",
    "common.update.count": "更新数量",
    "common.page": "页面",
    "common.type": "类型",
    "common.setting": "设置",
    "common.settings": "设置",
    "common.setting.batch": "批量设置",
    "common.print": "打印",
    "common.created.at": "创建时间",
    "common.created.by": "创建人",
    "common.updated.at": "修改时间",
    "common.updated.by": "修改人",
    "common.operator": "操作人",
    "common.operation.time": "操作时间",
    "common.operation.content": "操作内容",
    "common.operation.type": "操作方式",
    "common.operation.error": "操作失败！",
    "common.history": "轨迹",
    "common.register": "注册",
    "common.login": "登录",
    "common.logout": "退出登录",
    "common.username": "账号",
    "common.password": "密码",
    "common.remember": "记住密码",
    "common.forget": "忘记密码？",
    "common.user.info": "用户信息",
    "common.tips": "提示",
    "common.occasions": "次",
    "common.tips.expireDate_batch": "有效期/批次号为空，可在“批次管理”或DTP订单中更新。",
    "common.tips.expireDate_batch.know": "我知道了",
    "common.tips.save": "当前数据尚未保存，确定离开吗？",
    "common.random.tips": "是否确定随机?",
    "common.task.automatic": "自动任务",
    "common.task.manual": "手动任务",
    "common.email.language": "邮件语言",
    "common.email.language.zh": "中文",
    "common.email.language.en": "英文",
    "common.email.language.zh_en": "中英文",
    "common.email": "邮箱",
    "common.phone": "手机号",
    "common.number": "编号",
    "common.name": "名称",
    "common.name.current": "当前名称",
    "common.name.new": "新名称",
    "common.country": "国家/地区",
    "common.full_name": "姓名",
    "common.company": "公司",
    "common.language": "语言",
    "common.contacts": "联系人",
    "common.address": "地址",
    "common.description": "说明",
    "common.confirm.save": "确认保存吗？",
    "common.confirm.delete": "确定删除吗？",
    "common.confirm.cancel.edit": "确定取消编辑吗？",
    "common.confirm.is_delete": "是否确认删除？",
    "common.confirm.delete.unable.recover": "删除后，受试者数据无法恢复。",
    "common.confirm.copy": "确定复制吗？",
    "common.confirm.push": "确定发布吗？",
    "common.confirm.unbinding": "确定解绑吗?",
    "common.confirm.close": "确定关闭吗？",
    "common.confirm.inactivate.group": "确定无效该组别吗？",
    "common.administrator": "管理员",
    "common.administrator.project": "项目管理员",
    "common.all.role": "全部角色",
    "common.select.role": "选择角色",
    "common.role": "角色",
    "common.permission": "权限",
    "common.role.assigned": "系统检测到当前客户组已添加用户，但未分配角色及权限，请联系管理员设置。",
    "common.upload.fileSize": "上传的文件大小不可以超50MB!",
    "common.upload.fileSize.100": "上传的文件大小不可以超100MB!",
    "common.upload": "上传",
    "common.download": "下载",
    "common.download.data": "数据下载",
    "common.download.template": "下载模板",
    "common.generate": "生成",
    "common.sync": "同步",
    "common.inactivating": "失活",
    "common.site": "中心",
    "common.subject": "受试者",
    "common.depot": "仓库",
    "common.export": "导出",
    "common.between": "区间",
    "common.confirm.tip": "请确认下述信息。",
    "common.to.set": "去设置",
    "common.download.fail": "下载失败",
    "common.site.country.null": "匹配不到该中心对应的国家信息",
    "common.not-logged-in": "未登录或登录超时",
    "common.classification": "分类",
    "common.effective": "有效",
    "common.invalid": "无效",
    "common.menu": "菜单/模块",
    "common.role.setting": "权限设置",
    "common.serial": "序号",
    "common.common": "通用",
    "common.template": "模板",
    "common.addTo": "添加",
    "common.authorization.success": "授权成功",
    "common.success": "操作成功",
    "common.fail": "操作失败",
    "common.open.switch": "开",
    "common.close.switch": "关",
    "common.have": "有",
    "common.nothing": "无",
    "common.confirm": "确认",
    "common.previous": "上一步",
    "common.time": "时间",
    "common.all.select": "全选",
    "common.time.start": "开始时间",
    "common.time.end": "结束时间",
    "common.pagination.seleted": "已选",
    "common.pagination.record": "条",
    "common.pagination.all": "共",
    "common.pagination.empty": "清空",
    "common.lock": "锁定",
    "common.unlock": "解锁",
    "common.required.prefix": "请输入",
    "common.required.prefix.capping": "请输入入组上限",
    "common.required.prefix.factor": "请输入因素",
    "common.required.prefix.search": "请输入搜索",
    "common.required.prefix.name": "请输入名称",
    "common.required.prefix.new.name": "请输入新名称",
    "common.required.prefix.keyword": "请输入关键词",
    "common.required.positiveInteger": "请输入正整数",
    "common.required.positiveInteger.zero": "保留小数位必须为非负整数，请确认",
    "common.upload.add": "添加文件",
    "common.upload.excel.tip": "仅支持xlsx格式，格式可参考模版",
    "common.upload.excel.tip2": "仅支持xlsx格式",
    "common.upload.excel.tip3": "提示：",
    "common.upload.excel.tip4": "·默认读取前10列表头；",
    "common.upload.excel.tip5": "·系统提供的模版字段必填；",
    "common.upload.excel.tip6": "·尾列之前不允许存在空白列。",
    "common.confirm-change": "确定更改",
    "common.delete.confirm": "确认删除？",
    "common.delete.ok": "删除成功",
    "common.copy.ok": "复制成功",
    "common.create": "创建",
    "common.reason": "原因",
    "common.condition": "条件",
    "common.rest.assured": "码上放心",
    "common.please.confirm": "请确认！",
    "common.colon": "：",
    "common.update.fail": "修改失败",
    "common.stage.name": "阶段名称",
    "common.stage": "阶段",
    "common.remark": "备注",
    "common.current.time": "当前时间",
    "common.approval.confirm": "审批确认",
    "common.toBeApproved": "待审批",
    "common.ratio": "例数",
    "common.ip.info": "研究产品信息",
    "role.setting.operation": "操作权限",
    "role.operation.template": "权限模板",
    "role.name.enter": "请输入角色名称",
    "role.name": "角色名称",

    "common.statistics": "统计",

    "project.task": "项目任务",
    "project.task.status": "任务状态",
    "project.task.status.all": "全部",
    "project.task.status.notStarted": "未开始",
    "project.task.status.completed": "已完成",
    "project.task.approvalStatus.notStarted": "提交申请",
    "project.task.approvalStatus.passed": "已通过",
    "project.task.approvalStatus.rejected": "已拒绝",
    "project.task.approvalStatus.pass": "通过",
    "project.task.approvalStatus.reject": "拒绝",
    "project.task.estimatedCompletionTime": "预期完成",
    "project.task.approvalTime": "实际完成",
    "project.task.addOrder.title": "研究中心订单申请",
    "project.task.urgent-unblinding.title": "揭盲（紧急）申请",
    "project.task.pv-unblinding.title": "揭盲（PV）申请",
    "project.task.ip-unblinding.title": "揭盲（IP）申请",
    "project.task.approval.deadline": "截止时间",
    "project.statistics": "项目统计",
    "project.depot.ip.statistics": "库房单品统计",
    "project.depot.ip.statistics.info": "库房中所有研究产品的状态统计值",
    "project.site.ip.statistics": "中心单品统计",
    "project.site.ip.statistics.info": "中心中所有研究产品的状态统计值",
    "project.overview": "项目概览",
    "project.overview.timeZone": "取项目时区：",
    "project.overview.timeZone.home": "项目首页，时区计算标准：",
    "project.overview.progress.template": "项目已进行{startYear}年{startMonth}月{startDay}日，距离结束还有{endYear}年{endMonth}月{endDay}日",
    "project.random.statistics": "随机统计",
    "project.random.statistics.info": "按分层或时间统计项目实际随机情况",
    "project.visit.cycle.calendar": "访视日历",
    "project.visit.cycle.calendar.summary": "概览",
    "project.subject.statistics.info": "项目/中心受试者所有状态统计：",
    "project.subject.statistics.info.register": "已登记：登记的人数；",
    "project.subject.statistics.info.screenSuccess": "筛选成功：筛选成功的人数统计；",
    "project.subject.statistics.info.screenFail": "筛选失败：筛选失败的人数统计；",
    "project.subject.statistics.info.random": "已随机/已入组：随机入组的人数；",
    "project.subject.statistics.info.exit": "已停用：停止使用研究产品的人数；",
    "project.subject.statistics.info.unblinding": "已揭盲(紧急)：紧急揭盲的人数统计；",
    "project.subject.statistics.info.pvUnblinding": "已揭盲(PV)：PV揭盲的人数统计；",
    "project.subject.statistics.info.finish": "完成研究：完成研究的人数统计；",
    "project.subject.statistics.info.status.register": "已登记",
    "project.subject.statistics.info.status.screenSuccess": "筛选成功",
    "project.subject.statistics.info.status.screenFail": "筛选失败",
    "project.subject.statistics.info.status.random": "已随机",
    "project.subject.statistics.info.status.exit": "已停用",
    "project.subject.statistics.info.status.unblinding": "已揭盲(紧急)",
    "project.subject.statistics.info.status.pv": "已揭盲(pv)",
    "project.subject.statistics.info.status.finish": "完成研究",
    "project.subject.statistics": "受试者统计",
    "project.analysis": "异常分析",
    "project.analysis.info": "项目中异常订单、任务统计",
    "project.analysis.order.timeout": "超时订单",
    "project.analysis.order.all": "总订单",
    "project.analysis.task.timeout": "超期任务",
    "project.analysis.task.all": "总任务",
    "project.analysis.task.prepare": "待办任务",
    "project.analysis.subject.visit": "超窗访视",
    "project.analysis.subject.visit.outSizeNotCompleted": "未完成",
    "project.analysis.subject.visit.outSizeCompleted": "已完成",
    "project.attribute.subjectNumberPrex.err": "受试者前缀必须已{siteNO}开始",
    "project.attribute.randomSubject.err": "请选择随机研究产品供应核查规则",
    "project.attribute.subject_number_rule.info2": "受试者筛选流程需保持一致，已同步其他群组配置。",
    "project.attribute.subject_number_rule.info3": "受试者筛选流程需保持一致，已同步其它阶段配置。",
    "project.attribute.subject_number_rule.info4": "受试者号规则需保持一致，已同步其他群组配置。",
    "project.attribute.subject_number_rule.info5": "受试者号规则需保持一致，已同步其它阶段配置。",
    "project.attribute.subject_number_rule.info6": "受试者筛选流程/受试者号规则需保持一致，已同步其他群组配置。",
    "project.attribute.subject_number_rule.info7": "受试者筛选流程/受试者号规则需保持一致，已同步其它阶段配置。",
    "project.attribute.freeze_reason.info1": "隔离原因需保持一致，已同步其他群组配置",
    "project.attribute.freeze_reason.info2": "隔离原因需保持一致，已同步其他阶段配置",
    "project.noPermission": "暂未分配项目内权限",
    "projects.all": "全部项目",
    "projects.all.admin": "全部管理员",
    "projects.add": "新增项目",
    "projects.edit": "编辑项目",
    "projects.number": "项目编号",
    "projects.name": "项目名称",
    "projects.name.enter": "请输入项目名称",
    "projects.status": "项目状态",
    "projects.status.progress": "进行中",
    "projects.status.finish": "已完成",
    "projects.status.close": "已关闭",
    "projects.status.pause": "已暂停",
    "projects.status.terminate": "已终止",
    "projects.edit.status.progress.tips": "确定修改为进行中吗？",
    "projects.edit.status.progress.content": "项目恢复为进行中后，项目数据可操作。",
    "projects.edit.status.progress.reason": "请描述导致项目恢复的原因...(必填)",
    "projects.edit.status.finish.tips": "确定修改为已完成吗？",
    "projects.edit.status.finish.content": "请确保项目所有执行工作及任务已完成。项目完成后，项目数据将不可操作。",
    "projects.edit.status.close.tips": "确定修改为已关闭吗？",
    "projects.edit.status.close.content": "项目关闭后，项目数据将冻结不可操作。",
    "projects.edit.status.pause.tips": "确定修改为已暂停吗？",
    "projects.edit.status.pause.content": "项目暂停后，项目数据将暂时限制不可操作。",
    "projects.edit.status.pause.reason": "请描述导致项目暂停的原因...(必填)",
    "projects.edit.status.terminate.tips": "确定修改为已终止吗？",
    "projects.edit.status.terminate.content": "请确保项目当前执行工作已完成。项目终止后，项目数据将冻结不可操作。",
    "projects.edit.status.terminate.reason": "请描述导致项目终止的原因...(必填)",
    "projects.description": "项目描述",
    "projects.sponsor": "申办方",
    "projects.sponsor.enter": "请输入申办方",
    "projects.connectEdc": "对接EDC",
    "projects.connectEdc.supplier": "EDC供应商",
    "projects.connectEdc.mapping.configuration": "EDC映射",
    "common.select.supplier": "请选择供应商",
    "projects.connectELearning": "对接eLearning",
    "projects.learning.need": "必须完成课程",
    "projects.learning.system.courses": "系统课程",
    "projects.learning.courses": "项目课程",
    "projects.learning.compulsory": "强制学习",
    "projects.no.system.learning": "进入项目失败，当前有未完成的系统课程。",
    "projects.no.learning": "进入项目失败，当前有未完成的项目课程。",
    "projects.learning": "去学习",
    "projects.traceability.code": "追溯码",
    "projects.env": "环境",
    "projects.orderCheck": "订单核查",
    "projects.timing": "定时",
    "projects.realTime": "实时",
    "projects.manualRun": "（包含手动核查）",
    "projects.notApplicable": "不适用",
    "projects.customTime": "自定义时间",
    "projects.synchronization.mode": "同步方式",
    "projects.orderCheckWeek.noEmpty": "订单核查时间不可为空",

    "projects.push.rules": "推送规则",
    "projects.subject.uid": "受试者UID",
    "projects.push.scene": "推送场景",
    "projects.push.scene.select": "请选择推送场景",
    "projects.push.scene.block.select": "请选择随机阻断选项",
    "projects.subject.update": "受试者修改",
    "projects.subject.update.front": "(随机前)",
    "projects.subject.update.after": "(随机后)",
    "projects.subject.random.block": "分层校验不一致，进行随机阻断",
    "projects.subject.stratification": "分层",
    "projects.subject.form": "表单",
    "projects.subject.cohortName": "群组名称",
    "projects.subject.stageName": "阶段名称",
    "projects.subject.block.str-before": "当",
    "projects.subject.block.str-after": "校验不一致时，进行随机阻断",
    "projects.subject.no.tip": "受试者号：通过受试者号作为唯一标识关联。受试者号修改时，会以受试者号唯一判断，进行数据覆盖或新增。",
    "projects.subject.uid.tip": "受试者UID：通过数据库中受试者唯一ID进行关联。受试者号修改时，会以优先绑定关系判断，进行数据覆盖或新增。",

    "projects.recycling.confirmation": "中心回收订单确认",
    "projects.recycling.confirmation.tip": "开启后，中心回收订单，需先审批确认后才可流转后续运送、接收等节点。",
    "projects.step.by.synchronization": "受试者筛选时同步",
    "projects.one.time.full.synchronization": "受试者随机时同步",
    "projects.connectAli": "对接码上放心",
    "projects.connectAliID": "对接码上放心ID",
    "projects.type": "项目类型",
    "projects.timezone": "时区",
    "projects.types.first": "基本研究",
    "projects.types.second": "群组研究",
    "projects.types.third": "再随机研究",
    "projects.administrators": "项目管理员",
    "projects.envs": "项目环境",
    "projects.envs.all": "全部环境",
    "projects.envs.create": "创建环境",
    "projects.envs.add": "添加环境",
    "projects.envs.edit": "编辑环境",
    "projects.envs.delete": "删除此环境",
    "projects.envs.empty": "环境名称不能为空",
    "projects.envs.duplicate.name": "环境名称已存在",
    "projects.envs.cohorts.capacity": "上限",
    "projects.envs.cohorts.capacity.value": "上限值",
    "projects.envs.cohorts.reminder.thresholds": "提醒阈值",
    "projects.envs.cohorts.reminder.thresholds.toplimit": "阈值上限",
    "projects.envs.cohorts.factor": "因素",
    "projects.envs.cohorts.stage": "上一阶段",
    "projects.envs.cohorts.status1": "草稿",
    "projects.envs.cohorts.status2": "入组",
    "projects.envs.cohorts.status3": "完成",
    "projects.envs.cohorts.status4": "停止",
    "projects.envs.cohorts.status5": "入组已满",
    "projects.envs.copy": "复制环境",
    "projects.envs.the.copy": "复制该环境",
    "projects.envs.oper.lock.title": "确定锁定吗？",
    "projects.envs.oper.unlock.title": "确定解锁吗？",
    "projects.envs.oper.lock.message": "锁定后，部分页面的数据需要解锁后才允许编辑。",
    "projects.envs.oper.unlock.message": "解锁后，项目数据可被重新配置。",
    "projects.envs.lock": "锁定配置",
    "projects.envs.unlock": "解锁配置",
    "projects.envs.name": "环境名称",
    "projects.envs.cohort.type": "群组类型",
    "projects.envs.cohort.type.cohort": "普通群组",
    "projects.envs.cohort.type.reRandom": "再随机群组",
    "projects.envs.current": "当前环境",
    "projects.envs.new": "新环境",
    "projects.users": "项目人员",
    "projects.users.all": "全部人员",
    "projects.users.add": "添加人员",
    "projects.users.edit": "编辑人员",
    "customers.add": "新增客户",
    "customers.edit": "编辑客户",
    "customers.name": "客户名称",
    "roles.add": "新增角色",
    "roles.edit": "编辑角色",
    "roles.name": "角色名称",
    "roles.scope": "分类",
    "projects.plannedCases": "计划入组数",
    "projects.cycle": "项目周期",
    "projects.plannedCases.enter": "请输入计划入组数",
    "projects.plannedCases.enter.min": "计划入组数必须大于0",
    "projects.second": "群组",
    "projects.third": "再随机",
    "projects.third.name": "再随机名称",
    "projects.current.stage": "当前阶段",
    "projects.startDate": "开始日期",
    "projects.endDate": "结束日期",
    "projects.startDateTooltip": "开始日期不能大于结束日期",
    "projects.endDateTooltip": "结束日期不能小于开始日期",

    "drug.configure.drugNumber.limit": "发放数量限制",
    "drug.configure.open.tip.one": "开放配置：勾选后，可直接选择研究产品名称和数量进行发放，支持自定义数量发放；",
    "drug.configure.open.tip.two": "按标签：支持通过选择研究产品组合标签进行，或自定义数量发放；",
    "drug.configure.open.tip.three": "按公式计算：可按照公式计算后的建议值进行发放。",
    "drug.configure.open.tip.four": "自定义数量发放，则配置“发放数量”时，使用英文逗号隔开不同数字，使用“~”连接数字范围。",
    "drug.configure.open.tip.six": "发放数量：3,7~9，表示可以发放3,7,8,9规格的研究产品；",
    "drug.configure.open.tip.seven": "发放数量：2，表示仅发放2规格的研究产品。",
    "drug.configure.open.config": "开放配置",
    "drug.configure.formula.config": "按公式计算",
    "drug.configure.label.config": "按标签",
    "drug.configure.label.config.error": "配置冲突，组合标签仅适用于固定数量的研究产品发放。",
    "drug.configure.label.config.duplicate.error": "提交失败，发放标签为空，请重新确认。",
    "drug.configure.formula": "公式",
    "drug.configure.formula.tip": "计算公式如下 ：",
    "drug.configure.formula.tip.one": "年龄：按365.25天/年进行计算；",
    "drug.configure.formula.tip.two": "简易体表面积BSA = [体重 (kg) x 身高 (cm)/3600]1/2；",
    "drug.configure.formula.tip.three": "其他体表面积BSA，支持自定义输入公式计算。",
    "drug.configure.formula.weight": "体重",
    "drug.configure.formula.weight.last": "上次访视计算体重",
    "drug.configure.formula.weight.actual": "上次访视实际体重",
    "drug.configure.formula.weight.random": "随机访视体重",
    "drug.configure.formula.standard": "单位计算标准",
    "drug.configure.formula.unit": "计算单位",
    "drug.configure.formula.unit.capacity": "单位容量",
    "drug.configure.formula.weight.range": "体重范围",
    "drug.configure.formula.height": "身高",
    "drug.configure.formula.age": "年龄",
    "drug.configure.formula.age.name": "周岁",
    "drug.configure.formula.age.range": "年龄范围",
    "drug.configure.formula.date": "出生日期",
    "drug.configure.formula.bsa": "简易体表面积BSA",
    "drug.configure.formula.other": "其他体表面积BSA",
    "drug.configure.formula.customer": "自定义公式",
    "drug.configure.package.setting.error": "盲态研究产品与开放研究产品不允许同一包装，请重新确认。",
    "drug.configure.package.setting.error1": "保存失败，混合包装需配置多个研究产品。",
    "drug.configure.package.setting.error2": "未编号研究产品不允许配置混合包装，请重新确认。",
    "drug.configure.package.setting.error3": "已配置成单品，并产生库存，请重新确认数据后再操作。",
    "drug.list.serialNumber": "序列号",
    "drug.batch.management.update.info": "批量更新，仅支持同一个批次有效期研究产品操作。",
    "user.all": "全部用户",
    "user.add": "新增用户",
    "user.edit": "编辑用户",
    "user.name": "姓名",
    "user.allocationRole": "分配角色",
    "user.allocationRole.info": "请先选择需要分配的角色",
    "user.allocationProject": "分配项目",
    "user.list": "用户列表",
    "user.no.exist": "用户不存在",
    "user.status.notActive": "未激活",
    "user.status.active": "已激活",
    "user.status.open": "已开启",
    "user.status.close": "已关闭",
    "user.status.unauthorized": "未授权",
    "user.resend.invite.email": "重发",
    "user.resend.invite.email.info": "是否确定重新发送激活邮件？",
    "user.customer.admin.add": "是否确定给该账号分配admin权限？",
    "user.customer.admin.remove": "是否确定取消该账号的admin权限？",
    "user.customer.learn": "开始学习",
    "user.customer.learnTrain": "培训学习",
    "user.customer.unLearn": "您暂未完成学习！！！",
    "user.customer.isLearn": "是否已完成学习?",
    "user.customer.learnTip": "培训学习提示",
    "user.customer.learnContext": "该项目环境下存在未完成的课程，是否确认参加培训学习",
    "user.customer.cancel": "返回登录，我想稍后完成",
    "user.customer.welcome": "欢迎登录",
    "user.customer.invite": "管理员邀请您完成系统培训学习",
    "user.app.account": "APP账号",
    "user.settings.invite-again": "再次邀请",
    "user.settings.reauthorization": "再次授权",
    "user.settings.batch-close.tip": "仅支持已激活/已开启状态的用户",
    "form.onlyID": "表单ID",
    "form.setting": "表单配置",
    "form.preview": "预览",
    "form.field.name": "变量名称",
    "form.field.label": "字段名称",
    "form.application.type": "应用类型",
    "form.application.type.register": "受试者登记",
    "form.application.type.formula": "公式计算",
    "form.application.type.doseAdjustment": "剂量调整",
    "form.application.type.variable": "变量ID",
    "form.modify": "可修改",
    "form.list.modify": "是否可修改",
    "form.required": "必填",
    "form.control.type": "控件类型",
    "form.control.type.input": "输入框",
    "form.control.type.inputNumber": "数字输入框",
    "form.control.type.textArea": "多行文本框",
    "form.control.type.checkbox": "复选框",
    "form.control.type.radio": "单选框",
    "form.control.type.switch": "开关",
    "form.control.type.date": "日期选择框",
    "form.control.type.dateTime": "时间选择框",
    "form.control.type.select": "下拉框",
    "form.control.type.maximum": "受试者号位数",
    "form.control.type.exact": "受试者号位数精确值",
    "form.control.type.le": "小于等于",
    "form.control.type.eq": "等于",
    "form.control.type.options": "选项值",
    "form.control.type.label": "名称",
    "form.control.type.value": "值",
    "form.control.type.format": "格式类型",
    "form.control.type.format.characterLength": "字符长度",
    "form.control.type.format.numberLength": "数字长度",
    "form.control.type.format.decimalLength": "小数（整数+小数点+小数）",
    "form.control.type.format.checkbox": "多选框",
    "form.control.type.format.limit": "字符长度限制{length}，请重新输入",
    "form.control.type.variableFormat": "变量格式",
    "form.control.type.variableFormat.tip1": "字符长度：数值表明可输入的文本长度，例：200，表示支持输入200字符以内的字符串",
    "form.control.type.variableFormat.tip2": "数字长度：数值表示可输入的整数数字位数，例：3，表示可输入3位以内的整数数字",
    "form.control.type.variableFormat.tip3": "小数：表示可输入的数字的长度与小数部分的位数，例：8.3表示数字长度总位数8位包含小数点，小数部分为3位",
    "form.control.type.variableRange": "变量范围",
    "form.control.type.variableRange.min": "请输入min值",
    "form.control.type.variableRange.max": "请输入max值",
    "form.control.type.variableRange.validate": "max值须大于等于min值，请重新输入",
    "form.control.type.variableRange.validate.range": "输入错误，超出变量格式范围。",
    "form.control.type.options.lack": "缺少选项",
    "form.validate.info": "分层因素字段不能设置为可修改",
    "form.picker.max.tip": "系统会自动计算约束，最大值选择为当前时间。",
    "projects.attributes.random": "是否随机",
    "projects.attributes.isRandom": "随机化",
    "projects.attributes.randomYes": "随机",
    "projects.attributes.randomNo": "非随机化",
    "projects.attributes.random.registerGroup": "实际使用研究产品组别",
    "projects.attributes.random.registerGroup.info": "开启后，当随机后首次登记的实际研究产品组别与随机组别不一致时，允许后续研究产品根据实际研究产品组别进行发放。仅适用于访视流程一致的项目。",
    "projects.attributes.random.control": "随机研究产品供应核查",
    "projects.attributes.random.control.info": "开启后，当中心/仓库库存不足或研究产品未配置时，不允许继续随机。",
    "projects.attributes.random.control.rule1": "所有的分组，有供应后允许随机",
    "projects.attributes.random.control.rule1Info": "选择后，系统会在随机前强制进行库存核查。仅当研究中心内拥有足以发放给受试者可能随机到分组的库存时，才能允许随机。",
    "projects.attributes.random.control.rule2": "已分配的分组，有供应后允许随机",
    "projects.attributes.random.control.rule2Info": "选择后，需保证受试者所在分组有足够库存，允许随机。",
    "projects.attributes.random.control.rule3": "强制随机到有供应的分组",
    "projects.attributes.random.control.rule3Info": "受试者可强制随机到研究中心有库存的分组。请注意，频繁执行可能会导致入组不均衡的情况。",
    "projects.attributes.random.control.rule3Input1": "至少",
    "projects.attributes.random.control.rule3Input2": "个分组需有供应",
    "projects.attributes.subject.number.rules": "受试者号录入规则",
    "projects.attributes.subject.number.rule1": "自定义",
    "projects.attributes.subject.number.rule2": "自动递增且在项目中唯一",
    "projects.attributes.subject.number.rule3": "自动递增且在中心中唯一",
    "projects.attributes.is.random.number": "是否展示随机号",
    "projects.attributes.is.random.showNumber": "随机号展示",
    "projects.attributes.is.random.showSequenceNumber": "随机顺序号展示",
    "projects.attributes.is.random.sequenceNumberPrefix": "随机顺序号前缀",
    "projects.attributes.is.random.sequenceNumberDigit": "随机顺序号位数",
    "projects.attributes.is.random.sequenceNumberStartNumber": "随机顺序号起始数",
    "projects.attributes.is.random.sequenceNumber.tip": "随机顺序号位数包含前缀，例：前缀为A，位数为3，则生成顺序号为A01、A02、A03。",
    "projects.attributes.is.random.number.show": "展示",
    "projects.attributes.is.random.number.notShow": "不展示",
    "projects.attributes.dispensing": "是否发药",
    "projects.attributes.dispensingDesign": "发药设计",
    "projects.attributes.dispensing.yes": "发药",
    "projects.attributes.dispensing.no": "不发药",
    "projects.attributes.dtp.rule": "DTP规则",
    "projects.attributes.dtp.rule.ip": "研究产品",
    "projects.attributes.dtp.rule.ip.tooltip": "在项目维度，添加确认使用DTP方式运送的研究产品 ，可应用在受试者发放时。",
    "projects.attributes.dtp.rule.visitFlow": "访视流程",
    "projects.attributes.dtp.rule.notApplicable": "不适用",
    "projects.attributes.dispensing.noInfo": "保存后，系统将自动隐藏发药相关菜单及功能，重新登录即可生效",
    "projects.attributes.blind": "是否盲法",
    "projects.attributes.isBlind": "盲法",
    "projects.attributes.isBlind.blind": "盲态",
    "projects.attributes.isBlind.open": "开放",
    "projects.attributes.isScreen": "受试者筛选流程",
    "projects.attributes.random.minimize.calc": "最小化随机计算",
    "projects.attributes.visit.inheritance": "研究产品继承",
    "projects.attributes.visit.inheritance.tip": "开启后，允许受试者访视继承使用首次发放的批次有效期的研究产品。",
    "projects.attributes.visit.inheritance.enough": "剩余访视周期",
    "projects.attributes.random.minimize.calc.factor": "随机分层",
    "projects.attributes.random.minimize.calc.actual.factor": "实际分层",
    "projects.attributes.random.minimize.calc.notApplicable": "不适用",
    "projects.attributes.random.minimize.calc.tip": "优先使用实际分层，如未登记实际分层，则使用随机分层计算。",
    "projects.attributes.Layered": "地区分层",
    "projects.attributes.countryLayered": "国家分层",
    "projects.attributes.country": "国家",
    "projects.attributes.instituteLayered": "中心",
    "projects.attributes.regionLayered": "区域",
    "projects.attributes.instituteLayer": "中心分层",
    "projects.attributes.isFreeze": "隔离单品计算规则",
    "projects.attributes.isFreeze.info": "开启后，运行运送算法时，将隔离单品默认计算为研究中心的可用库存。",
    "projects.attributes.edc.label": "EDC对接研究产品配置标签",
    "projects.attributes.systemRules": "系统配置",
    "projects.attributes.subject.numberRules": "受试者号规则",
    "projects.attributes.other.numberRules": "其它规则",
    "projects.attributes.blind.rules": "设盲限制",
    "projects.attributes.blind.rules.stop": "停用非盲受试者",
    "projects.attributes.blind.rules.stop.open": "开启后，紧急揭盲的受试者，不再允许继续发放。",
    "projects.attributes.rest.assured.stop.open": "开启后，项目支持与阿里健康的追溯码对接。",
    "projects.attributes.blind.rules.stop.pv": "包含PV揭盲的受试者",
    "projects.attributes.blind.rules.stop.pv.notApplicable": "不适用",
    "projects.attributes.prefix.bool": "受试者前缀",
    "projects.attributes.prefix": "受试者号前缀",
    "projects.attributes.prefix.tooltip1": "中心编号表达式:{siteNO}，连接符支持-、~、/等。",
    "projects.attributes.prefix.tooltip2": "举例: 设置规则为{siteNO}-A，则实际受试者编号为09-A001，09为中心编号，001为受试者顺序号。",
    "projects.attributes.subject.type.site.numberPrefix": "是否将中心作为前缀",
    "projects.attributes.subject.prefixConnector": "前缀连接符",
    "projects.attributes.subject.other.prefix": "受试者号的其他前缀",
    "projects.attributes.subject.prefix.text": "前缀文本",
    "projects.attributes.subject.allow.replace.tip": "发药项目，请同时配置对应访视是否允许替换。",
    "projects.attributes.subject.replace.text": "受试者号替换文本",
    "projects.attributes.subject.replace.text.en": "受试者号替换文本(英文)",
    "projects.attributes.subject.isRandom": "中心没有分配随机号不能入组",
    "projects.attributes.subject.isRandom.title": "入组限制",
    "projects.attributes.subject.isRandom.tips": "项目维度，国家/中心/区域未分配随机号，则限制受试者入组。",
    "projects.attributes.code.config.all": "全部编码配置",
    "projects.attributes.code.config": "编码配置",
    "projects.attributes.code.config.method": "编码方式",
    "projects.attributes.code.rule": "编码规则",
    "projects.attributes.code.rule.manual": "手动编码上传",
    "projects.attributes.code.rule.auto": "系统自动编码",
    "projects.attributes.code.rule.software.informed": "软件知情",
    "projects.attributes.code.rule.information":
        "用户使用IRT APP时，对其相关功能以及信息具备一定的知情权的说明，常见有《用户隐私协议》等用户账户相关的内容说明。开启后，将在项目使用APP扫描条形码功能时，支持用户签署知情流程，开启后暂不支持修改。",
    "projects.attributes.code.rule.informed.agreement":
        "请输入公司法务确认的扫码知情协议内容，内容只可配置一次，确认后保存。",
    "projects.attributes.code.rule.confirm.title": "是否确定保存？",
    "projects.attributes.code.rule.confirm.msg": "确认后，配置无法再更改。",
    "projects.attributes.segment": "号段随机",
    "projects.attributes.segment.type": "计算规则",
    "projects.attributes.segment.length": "号段长度",
    "projects.attributes.groupsInfo": "系统未检测到组别，请先至随机配置中设置组别",
    "projects.attributes.saveInfo": "项目已选择“不发药”，项目内关于发药的菜单及权限将自动隐藏，重新登录即可生效。",
    "projects.attributes.unblindingReason": "揭盲原因（单选）",
    "projects.attributes.freezeReason": "隔离原因（单选）",
    "projects.attributes.freezeReason.info": "隔离原因（单选）",
    "projects.attributes.allowed.remark": "允许备注",
    "projects.attributes.subject.replace": "替换受试者随机号",
    "projects.attributes.subject.replace.customize": "自定义",
    "projects.attributes.subject.replace.auto": "系统自动生成",
    "projects.attributes.subject.replace.tip": "请确认，计算后的替换随机号不在当前随机表范围内，否则将替换失败。",
    "projects.attributes.subject.replace.original": "原随机号",
    "projects.randomization.configure": "随机配置",
    "projects.randomization.list": "随机列表",
    "projects.randomization.factor": "分层因素",
    "projects.randomization.separate": "随机分段",
    "projects.randomization.type": "随机类型",
    "projects.randomization.blockRandom": "区组随机",
    "projects.randomization.factorBlock": "分层区组随机",
    "projects.randomization.minimize": "最小化随机",
    "projects.randomization.source": "盲底来源",
    "projects.randomization.upload": "系统上传",
    "projects.randomization.generate": "系统生成",
    "projects.randomization.group": "治疗组别",
    "projects.randomization.groupRatio": "组间比例",
    "projects.randomization.groupMapping": "组别映射",
    "projects.randomization.factorRatio": "分层因素权重比",
    "projects.randomization.probability": "偏倚概率",
    "projects.randomization.blockLength": "区组长度",
    "projects.randomization.total": "总例数",
    "projects.randomization.seed": "随机种子",
    "projects.randomization.prefix": "随机号前缀",
    "projects.randomization.count": "随机号数量",
    "projects.randomization.useCount": "随机号（已使用/总数量）",
    "projects.randomization.defaultNumber": "初始编号",
    "projects.randomization.numberLength": "号码长度",
    "projects.randomization.numberPrefix": "号码前缀",
    "projects.randomization.blockConfig": "区组配置",
    "projects.randomization.groupConfig": "组别配置",
    "projects.randomization.blockRule": "区组规则",
    "projects.randomization.blockRule.order": "顺序",
    "projects.randomization.blockRule.reverse": "乱序",
    "projects.randomization.randomNumberRule": "随机号规则",
    "projects.randomization.randomNumberRule.order": "顺序",
    "projects.randomization.randomNumberRule.reverse": "乱序",
    "projects.randomization.endNumber": "终止编号",
    "projects.randomization.weight": "权重",
    "projects.randomization.confirmUpload": "请上传文件",
    "projects.randomization.lockFactor": "缺少分层",
    "projects.randomization.design": "随机设计",
    "projects.randomization.form": "表单配置",
    "projects.randomization.allocation.factor": "清空其他分层",
    "projects.randomization.allocation.factorConfirm": "确定清空其他分层吗？",
    "projects.randomization.allocation.block.activate.tip": "确定激活该区组吗？",
    "projects.randomization.allocation.block.activate.content": "激活后，区组内不可用的随机号恢复可用，允许再随机使用。",
    "projects.randomization.allocation.block.Inactivate.tip": "确定失活该区组吗？",
    "projects.randomization.allocation.block.Inactivate.content": "失活后，区组内可用的随机号变更为不可用，不允许被随机使用。",
    "projects.randomization.allocation.randomNumber.activate.tip": "确定激活该随机号吗？",
    "projects.randomization.allocation.randomNumber.activate.content": "激活后，随机号恢复可用，允许随机使用。",
    "projects.randomization.allocation.randomNumber.Inactivate.tip": "确定失活该随机号吗？",
    "projects.randomization.allocation.randomNumber.Inactivate.content": "失活后，随机号不可用，不允许被随机使用。",
    "projects.site.list": "关联中心",
    "projects.site.no.supplyPlan": "请关联供应计划",
    "projects.site.no.supplyPlan.store": "请配置供应计划和仓库。",
    "projects.site.no.storehouse": "订单新增失败，中心对应的供应计划未绑定仓库，请重新确认。",
    "projects.site.supplyPlan.0init_supply": "初始发药量为0, 无法初始发放",
    "projects.site.send.medicine": "确认发放",
    "projects.site.not.enough.medicine": "研究产品({0})库存剩余量不足",
    "project.site.add_order_error": "该中心已被激活, 无法再次创建初始订单",
    "projects.site.medicine": "中心研究产品",
    "projects.storehouse.list": "仓库列表",
    "projects.storehouse.name": "仓库名称",
    "projects.storehouse.inStorehouse": "是否确定该仓库的研究产品入物流仓库?",
    "projects.site.name": "中心名称",
    "projects.site.name.standard": "中心标准名称",
    "projects.site.short.name": "中心简称",
    "projects.site.number": "中心编号",
    "projects.site.supply": "自动订单供应",
    "projects.site.open": "开启",
    "projects.site.close": "关闭",
    "projects.site.isDefault.contact": "设为默认联系人",
    "projects.drug.alert": "研究产品库存警戒",
    "projects.drug.alert.check": "请选择研究产品名称或者警戒值",
    "visit.cycle.management": "访视管理",
    "visit.cycle.push": "发布",
    "visit.cycle.push.visit": "发布访视",
    "visit.cycle.name": "访视名称",
    "visit.cycle.window": "访视窗",
    "visit.cycle.group": "组别",
    "visit.cycle.period": "窗口期",
    "visit.cycle.interval": "间隔时长",


    "visit.cycle.push.tip": "发布后，访视部分配置将无法再修改。",
    "visit.cycle.type": "访视偏倚类型",
    "visit.cycle.type.tip": "访视偏倚类型，",
    "visit.cycle.version.number": "访视版本号",
    "visit.cycle.version": "访视版本",
    "visit.cycle.push.date": "发布日期",
    "visit.cycle.push.record": "发布记录",
    "visit.cycle.push.record.view": "查看",
    "visit.cycle.type.tip.baseline": "Baseline：以随机日期为基准计算后续访视的计划日期；",
    "visit.cycle.type.tip.lastdate": "Lastdate：以访视的研究产品实际发放时间为基准，计算后续访视的计划日期。",
    "visit.cycle.day.tip": "负偏移量的间隔时长，则系统视此访视为随机前访视。随机前访视以首次随机前访视的实际发放时间开始计算，始终计算至随机访视。",
    "visit.cycle.setting.unscheduled_visit": "计划外访视",
    "visit.cycle.setting.nameZh": "中文名称",
    "visit.cycle.setting.nameEn": "英文名称",
    "visit.cycle.visitNumber": "访视编号",
    "visit.cycle.visitName": "访视名称",
    "visit.cycle.startDays": "起始天数",
    "visit.cycle.endDays": "结束天数",
    "visit.cycle.dispensing": "允许发放",
    "visit.cycle.replace": "允许受试者替换",
    "visit.cycle.random": "允许随机",
    "visit.cycle.dtp": "允许DTP",
    "visit.cycle.doseAdjustment": "剂量调整",
    "drug.configure": "研究产品配置",
    "drug.configure.group": "组别",
    "drug.configure.subGroup": "子组别",
    "drug.configure.drugName": "研究产品名称",
    "drug.isolation.quantity": "隔离数量",
    "drug.isolation.single.quantity": "隔离数量(单品数)",
    "drug.isolation.package.quantity": "隔离数量(包装数)",
    "drug.configure.visitName": "访视名称",
    "drug.configure.roomNumber": "房间号",
    "drug.configure.drugNumber": "发放数量",
    "drug.configure.drugLabel": "组合发放标签",
    "drug.configure.showAll": "发放展示全部",
    "drug.configure.routine.visit.mapping": "常规访视映射",
    "drug.configure.drugLabel.config": "(组合)发放标签",
    "drug.configure.drugSpecifications": "研究产品规格",
    "drug.configure.PkgSpecifications": "包装规格",
    "drug.configure.spec": "规格",
    "drug.configure.other.tip": "勾选后，则是未编号研究产品。",
    "drug.configure.delete": "删除研究产品配置信息，可能会影响研究产品列表的信息，是否确认删除?",
    "drug.configure.sub-labels.tip": "同个访视配置多个子标签，允许同时全部选择发放。",
    "drug.configure.labels.tip": "同个访视配置多个组合标签，只允许单选发放。",
    "drug.configure.ip.tip": "同个访视配置多个研究产品，允许同时全部选择发放。",
    "drug.configure.visitCycles.name.err": "按公式计算不适用计划外访视规则，请重新选择。",
    "drug.configure.visitCycles.config.error": "配置冲突。",

    "drug.list": "研究产品列表",
    "drug.list.name": "研究产品名称",
    "drug.list.batchMag": "批次管理",
    "batchMag.type.depot": "仓库",
    "batchMag.type.site": "中心",
    "batchMag.type.order": "订单",
    "drug.list.packlist": "包装清单",
    "drug.list.isolation": "隔离",
    "drug.list.release": "解隔离",
    "drug.list.lost": "丢失/作废",
    "drug.list.setUse": "设为可用",
    "drug.list.drugNumber": "研究产品编号",
    "drug.list.expireDate": "有效期",
    "drug.list.entryTime": "入仓时间",
    "drug.list.batch": "批次号",
    "drug.list.status": "状态",
    "drug.list.current.status": "当前状态",
    "drug.list.verificationCode": "研究产品验证码",
    "drug.list.delete": "批量删除",
    "drug.list.delete.info": "请勾选需要删除的研究产品",
    "drug.list.availableCount": "可用库存",
    "drug.list.number.placeholder": "请输入产品研究编号",
    "drug.batch.management.stock": "库存批次号",
    "drug.batch.management.status": "研究产品状态",
    "drug.batch.management.update": "更新批次号",
    "drug.batch.management.selectStatus": "请选择研究产品状态",
    "drug.batch.management.updateExpireDate": "更新有效期",
    "drug.batch.management.updateError": "输入错误，单品数量与包装比例关系不匹配，请重新输入。",
    "drug.batch.management.storeGroup.add": "添加库房组",
    "drug.batch.management.group.add": "添加批次号组",
    "drug.batch.management.group.del": "删除批次号组",
    "drug.upload.uploadDrug": "上传研究产品编号",
    "drug.other": "未编号研究产品",
    "drug.medicine": "编号研究产品",
    "drug.medicine.upload": "上传研究产品",
    "drug.other.name": "研究产品名称",
    "drug.other.count": "数量",
    "drug.other.singleCount": "单品数量",
    "drug.other.packageCount": "包装数量",
    "drug.other.add.error": "单品数量与包装比例不匹配，请重新确认。",
    "drug.medicine.packlist": "包装号",
    "drug.medicine.packlist.settings": "设置",
    "drug.medicine.package.serial_number": "包装序列号",
    "drug.medicine.packlist.upload": "上传包装清单",
    "drug.medicine.packlist.downtemplate": "包装清单模板下载",
    "drug.medicine.packlist.select.file": "请选择你需要上传的包装清单文件。",
    "drug.medicine.package.setting.isOpen": "按包装运输",
    "drug.medicine.package.mixed": "混合包装",
    "drug.medicine.package.setting.info": "开启后，订单新增等会按照包装运输。",
    "drug.medicine.order.supply.ratio": "供应比例",
    "drug.medicine.package.setting": "包装配置",
    "drug.medicine.package.reupload": "请重新上传包含包装号的研究产品列表。",
    "drug.medicine.package.upload": "请上传包含包装号的研究产品列表。",
    "drug.medicine.package.message": "已上传研究产品列表中的包装号功能将不可用，新增订单将按照单品计算库存。",
    "drug.medicine.packlist.setting.unProvideDate": "不发放天数",
    "drug.medicine.setting.unProvideDate": "不发放天数配置",
    "medicine.but.examine": "审核",
    "medicine.but.update": "修改",
    "medicine.but.release": "放行",
    "medicine.status.dsm": "待扫码",
    "medicine.status.dsh": "待审核",
    "medicine.status.shsb": "审核失败",
    "medicine.status.examine.tip": "请勾选需要审核的研究产品。",
    "medicine.status.update.tip": "请勾选需要修改的研究产品，修改后，研究产品状态将更新为待审核。",
    "medicine.status.release.tip": "请勾选需要放行的研究产品，放行后，研究产品将分配到对应仓库中。",
    "medicine.status.examine.confirm": "审核确认",
    "drug.medicine.setting.application.err1": "仅添加单一盲态研究产品申请，可能会导致破盲",
    "drug.medicine.setting.application.err2": "保存失败，仅开放研究产品不适用于供应比例配置。",
    "drug.medicine.setting.application.err3": "按包装运输的研究产品，需与包装比例一致。",
    "drug.medicine.setting.application.err4": "保存失败，不允许仅添加单一盲态研究产品控制。",
    "drug.medicine.setting.application.err5": "保存失败，请设置有效的默认联系人",
    "medicine.status.toBeWarehoused": "待入仓",
    "medicine.status.available": "可用",
    "medicine.status.delivered": "已确认",
    "medicine.status.sending": "已运送",
    "medicine.status.quarantine": "已隔离",
    "medicine.status.used": "已使用",
    "medicine.status.transit": "已运送",
    "medicine.status.lose": "丢失/作废",
    "medicine.status.expired": "已过期",
    "medicine.status.receive": "已领药",
    "medicine.status.return": "已退货",
    "medicine.status.destroy": "已销毁",
    "medicine.status.InOrder": "待确认",
    "medicine.status.stockPending": "待入库",
    "medicine.status.inStorage": "入库中",
    "medicine.status.apply": "已申请",
    "medicine.status.frozen": "已冻结",
    "medicine.status.fzn": "冻结",
    "medicine.status.toBeApproved": "待审批",
    "medicine.status.under.approval": "解隔离审批中",
    "medicine.status.locked": "锁定",
    "validator.message.phone": "非有效的手机号",
    "validator.message.replace.medicine": "请选择替换研究产品",
    "validator.message.site": "请输入1-10位的数字/字母",
    "randomization.config.type": "随机类型",
    "randomization.config.group": "治疗组别",
    "randomization.config.subGroup": "子组别",
    "randomization.config.factorName": "分层名称",
    "randomization.config.factor": "分层因素",
    "randomization.config.factors": "分层",
    "randomization.config.region": "区域因素",
    "randomization.config.region.duplicate": "区域因素已存在",
    "randomization.config.randomList": "随机列表",
    "randomization.config.groupAdd": "添加-治疗组别",
    "randomization.config.groupEdit": "编辑-治疗组别",
    "randomization.config.factorAdd": "添加-分层因素",
    "randomization.config.factorEdit": "编辑-分层因素添加",
    "randomization.config.factor.addError": "保存失败，无法同时勾选同一个分层的不同选项值。",
    "randomization.config.desc": "描述",
    "randomization.config.groupSize": "区组大小",
    "randomization.config.groupSize.placeholder": '请输入，多个区组用","隔开',
    "randomization.config.number": "字段编号",
    "randomization.config.factor.calc": "分层计算",
    "randomization.config.factor.calc.type.age": "年龄",
    "randomization.config.factor.calc.type.bmi": "BMI",
    "randomization.config.factor.calc.formula": "公式类型",
    "randomization.config.factor.calc.formula.customer": "自定义公式",
    "randomization.config.factor.calc.formula.round.up": "向上取整",
    "randomization.config.factor.calc.formula.round.down": "向下取整",
    "randomization.config.factor.calc.formula.keep.decimal.places": "保留小数位",
    "randomization.config.factor.calc.formula.title1": "当前时间的变量ID为{CurrentTime}。",
    "randomization.config.factor.calc.formula.title2": "输入公式仅支持如下符号:",
    "randomization.config.factor.calc.formula.footer1": "自定义公式举例：",
    "randomization.config.factor.calc.formula.footer2": "({CurrentTime}-{时间变量ID})/365",
    "randomization.config.factor.calc.formula.tip": "支持引用表单配置中字段，设置自定义公式进行计算。如：",
    "randomization.config.factor.calc.formula.tip.age": "年龄：按365.25天/年进行计算；",
    "randomization.config.factor.calc.formula.tip.bmi": "BMI：体重÷(身高)²，其单位为kg和m。",
    "randomization.config.input.label": "录入字段名称",
    "randomization.config.input.label.weight": "录入体重字段名称",
    "randomization.config.input.label.weight.placeholder": "请输入体重字段名称，计算单位：kg",
    "randomization.config.input.label.height": "录入身高字段名称",
    "randomization.config.input.label.height.placeholder": "请输入身高字段名称，计算单位：kg",
    "randomization.config.factor.label": "分层名称",
    "randomization.config.factor.label.option.mapping": "分层选项映射",
    "randomization.config.factor.label.option.value.range": "分层值范围",
    "randomization.config.factor.label.option.value": "分层选项",
    "randomization.config.code": "组别代码",
    "randomization.config.subGroup.duplicate": "子组别名称重复",
    "randomization.config.subGroup.duplicate.all": "全部子组别",
    "randomization.config.group.duplicate.all": "全部组别",
    "randomization.random.list.invalid": "作废",
    "randomization.random.number.invalid": "无效",
    "randomization.random.number.used": "已使用",
    "randomization.random.number.unused": "可用",
    "randomization.random.number.not.available": "不可用",
    "randomization.random.number.block.all.usable": "全部可用",
    "randomization.random.number.block.partially.usable": "部分可用",
    "randomization.random.number.block.used": "已使用",
    "randomization.random.number.block.invalid": "无效",
    "randomization.random.number.block.unavailable": "不可用",
    "randomization.random.number.block.status.tip.1": "区组状态说明：",
    "randomization.random.number.block.status.tip.2": "全部可用：区组内随机号均为可用状态；",
    "randomization.random.number.block.status.tip.3": "部分可用：区组内有可用的随机号；",
    "randomization.random.number.block.status.tip.4": "已使用：区组内所有随机号均已被使用；",
    "randomization.random.number.block.status.tip.5": "无效：区组内有无效状态的随机号；",
    "randomization.random.number.block.status.tip.6": "不可用：区组内有不可用状态的随机号。",
    "randomization.random.number.block.status.tip.7": "随机号状态说明：",
    "randomization.random.number.block.status.tip.8": "可用：随机号可用，允许被随机；",
    "randomization.random.number.block.status.tip.9": "不可用：随机号不可用，不允许被随机；",
    "randomization.random.number.block.status.tip.10": "无效：组别无效或随机表作废后，随机号为无效状态；",
    "randomization.random.number.block.status.tip.11": "已使用：随机号已被使用。",
    // "randomization.random.number.block.status": "区组状态",
    "randomization.random.list.invalid.doubt.title": "确定作废吗？",
    "randomization.random.list.invalid.doubt": "作废后，该随机表将无法恢复。",
    "randomization.random.list.invalid.doubt.info": "系统检测出",
    "randomization.random.list.invalid.doubt.first": "已有受试者完成随机/发放操作，",
    "randomization.random.list.invalid.doubt.second": "作废后，将无法恢复。",
    "projects.randomization.randomizationSubTab": "随机列表属性",
    "projects.randomization.randomNumberTab": "随机号区组",
    "projects.randomization.block": "区组",
    "projects.randomization.randomNumber": "随机号",
    "projects.randomization.randomSequenceNumber": "随机顺序号",
    "projects.randomization.groupList": "组别",
    "projects.randomization.planNumber": "计划随机数",
    "projects.randomization.randomCount": "随机号数量",
    "projects.randomization.actualPeople": "实际人数",
    "projects.randomization.planPeople": "预计人数",
    "projects.randomization.alertPeople": "受试者警戒人数",
    "projects.randomization.upperLimitPeople": "受试者上限人数",
    "projects.randomization.alertSetFactor": "随机列表未分配分层",
    "projects.randomization.settingPeople": "设置人数",
    "projects.randomization.settingRandom": "当前为非随机项目，请进行属性配置",
    "projects.randomization.settingFactor": "随机列表暂未分配分层因素",
    "projects.randomization.selectBlock": "请选择区组",
    "projects.randomization.nullFactor": "未选择分层因素",
    "projects.randomization.nullSite": "错误, 分配中心为空，请重新确认。",
    "projects.randomization.nullCountry": "错误，分配国家为空，请重新确认。",
    "projects.randomization.nullRegion": "错误，分配区域为空，请重新确认。",
    "projects.randomization.distribution": "分配",
    "projects.randomization.distributions": "区组分配",
    "projects.randomization.distributionCenter": "区组分配给中心",
    "projects.randomization.distributionRegion": "区组分配给区域",
    "projects.randomization.distributionCountry": "区组分配给国家",
    "projects.randomization.distributionFactor": "区组分配给分层",
    "projects.randomization.distributionConfirm": "确定进行分配吗？",
    "projects.randomization.distributionContent": "当前{target}将分配{currentCount}个随机号，累计已分配{totalCount}个随机号。",
    "projects.randomization.sourceSite": "分配机构",
    "projects.randomization.targetSite": "接收机构",
    "projects.randomization.enterSourceSite": "请选择分配机构",
    "projects.randomization.enterTargetSite": "请选择接收机构",
    "projects.randomization.selectFactor": "选择分层",
    "projects.randomization.enterFactor": "请选择分层",
    "projects.randomization.factorRules": "分层规则",
    "projects.randomization.otherFactor": "其他分层",
    "projects.randomization.generateVerificationTips1": "区组长度必须是比例之和的倍数",
    "projects.randomization.generateVerificationTips2": "各区组长度必须是比例之和的倍数",
    "projects.randomization.endValueTips": "可用随机号范围无法满足当前区组配置随机号，请重新配置。",
    "projects.randomization.generateGroupWeightRatio": "组别配置(权重比)",
    "projects.randomization.generateWeightRatio": "权重比",
    "projects.randomization.generateBlockNumber": "区组数",
    "projects.randomization.generateLayeredWeightRatio": "分层配置(权重比)",
    "projects.randomization.last.group": "上阶段组别",
    "storehouse.name": "库房",
    "storehouse.all": "全部库房",
    "projects.statistics.site": "中心",
    "projects.statistics.selectField": "请选择依据",
    "projects.storehouse.statistics.summary": "概览",
    "projects.storehouse.statistics.sku": "单品管理",
    "projects.storehouse.statistics.other.sku": "未编号单品管理",
    "projects.storehouse.statistics.material.forecast": "物资预测",
    "projects.material.forecast.name": "物资名称",
    "projects.material.forecast.need.transport": "是否需要运送",
    "projects.material.forecast.current.stock": "当前库存",
    "projects.material.forecast.predicted.consumption": "预测消耗",
    "common.date.range": "日期区间",
    "common.start.date": "开始日期",
    "common.end.date": "结束日期",
    "material.forecast.settings.project.enrollment.plan": "项目入组计划",
    "material.forecast.settings.site.enrollment.plan": "中心入组计划",
    "material.forecast.settings.enrollment.period": "入组周期",
    "material.forecast.settings.target.count": "入组目标",
    "material.forecast.settings.monthly.enrollment.plan": "月入组计划",
    "material.forecast.settings.weekly.enrollment.plan": "周入组计划",
    "material.forecast.settings.search.site": "请输入中心",
    "material.forecast.settings.batch.settings": "批量设置",
    "material.forecast.settings.planned.enrollment": "计划入组数",
    "material.forecast.settings.actual.enrollment": "实际入组数",
    "material.forecast.settings.period": "周期",
    "projects.statistics.sku.expirationDate": "有效期",
    "projects.statistics.sku.place": "位置",
    "projects.statistics.sku.status": "状态",
    "projects.other.package.info": "库存数量显示中，括号()内为包装数量。",
    "projects.other.sku.freeze.package": "已隔离包装数",
    "projects.other.sku.freeze.single": "已隔离单品数",
    "projects.other.sku.freeze.count": "已隔离数",
    "projects.other.sku.lost.count": "丢失/作废数",
    "projects.other.sku.lost.package": "丢失/作废包装数",
    "projects.other.sku.lost.single": "丢失/作废单品数",
    "projects.other.sku.freeze.info": "请选择可用/冻结数量大于0的数据进行隔离操作",
    "projects.other.sku.lost.info": "请选择可用/已过期数量大于0的数据进行丢失/作废操作",
    "projects.storehouse.statistics.send.unit": "发送单位",
    "projects.storehouse.no.storehouse": "请关联库房",
    "projects.storehouse.bind": "关联库房",
    "projects.storehouse.connected": "是否对接",
    "projects.sitePharmacy.forecast": "当前库存最晚可用时间",
    "projects.sitePharmacy.medicineReceive": "研究产品接收",
    "projects.sitePharmacy.storehouseMedicine": "库房研究产品",
    "projects.sitePharmacy.availableMedicine": "可用研究产品",
    "projects.sitePharmacy.usedMedicine": "已用研究产品",
    "projects.sitePharmacy.isolateMedicine": "隔离研究产品",
    "projects.sitePharmacy.wasteMedicine": "丢失/作废研究产品",
    "projects.sitePharmacy.orderHistory": "历史订单",
    "projects.sitePharmacy.order.status": "订单状态",
    "projects.sitePharmacy.order.medicineQuantity": "研究产品数量",
    "projects.sitePharmacy.order.receiver": "接收者",
    "projects.sitePharmacy.order.receiveDate": "接收日期",
    "projects.sitePharmacy.no.site": "请关联中心",
    "drug.freeze.release": "研究产品解隔离",
    "drug.freeze.reason": "原因",
    "drug.freeze.form-item.reason": "即将隔离原因",
    "drug.freeze.selectData": "请先勾选需要隔离的研究产品",
    "drug.freeze.number": "隔离编号",
    "drug.freeze.startDate": "隔离日期",
    "drug.freeze.operator": "操作人",
    "drug.freeze.endDate": "关闭日期",
    "drug.freeze.count": "数量",
    "drug.freeze.receive.count": "接收数量",
    "drug.freeze.confirm.count": "确认数量",
    "drug.freeze.receiveFreeze.count": "隔离数量",
    "drug.freeze.institute": "隔离位置",
    "drug.freeze.ipNumber": "隔离产品编号",
    "drug.freeze.all": "全部隔离管理",
    "shipment.number.interval": "序列号区间",
    "shipment.supply.count": "供应量总和",
    "shipment.supply": "当前供应计划",
    "shipment.expectedArrivalTime": "期望送达时间",
    "shipment.actualReceiptTime": "实际接收时间",
    "shipment.blindCount": "盲态研究产品数量",
    "shipment.orderNumber": "订单号",
    "shipment.send": "起运地",
    "shipment.receive": "目的地",
    "shipment.status": "状态",
    "shipment.medicine": "研究产品",
    "shipment.supplement.mode": "补充方式",
    "shipment.other.drug": "未编号研究产品",
    "shipment.approval.confirm.title": "审批确认",
    "shipment.approval.confirm": "审批",
    "shipment.basic.information": "基本信息",
    "shipment.drug": "研究产品",
    "shipment.supply.info": "如选择非默认供应计划，审批通过后将自动创建订单。",
    "shipment.mode.set": "发放数量",
    "shipment.mode.reSupply": "再供应量",
    "shipment.mode.max": "最大缓冲量",
    "shipment.mode.forecast": "最低预测",
    "shipment.mode.supplyRatio": "供应比例",
    "shipment.mode.serialNo": "序列号",
    "shipment.cancal.order": "取消订单",
    "shipment.transit.order": "是否确认运送该订单?",
    "shipment.transit.work.task.title": "是否确定发送运送订单确认任务?",
    "shipment.transit.work.task.msg": "确认后，请登录APP完成任务。",
    "shipment.transit.confirm": "确认发送",
    "shipment.transit.skip": "跳过，已人工确认",
    "shipment.transit.artificial.confirm": "已人工线下核对订单，确认无误。",
    "shipment.lose.order": "订单丢失",
    "shipment.end.order": "终止订单",
    "shipment.close.order": "关闭订单",
    "shipment.transit": "运送",
    "shipment.end": "终止",
    "shipment.lose": "丢失",
    "shipment.received": "接收",
    "shipment.approvalTask": "审批记录",
    "shipment.order.contacts.detail": "详情",
    "shipment.order.all": "全部订单",
    "shipment.order.all-no": "全部订单号",
    "shipment.order.fail.title": "提交失败",
    "shipment.order.page.fail": "无法创建订单，“{name}”为包装运输，同包装研究产品“{otherName}”所属群组状态为”已完成/草稿/已终止“，请重新确认。",
    "shipment.order.success.title": "审批通过后将自动创建订单。",
    "shipment.order.received": "订单接收",
    "shipment.order.create.modeMax.info": "目的地库存已大于最大缓冲量，研究产品订单不生成",
    "shipment.order.received.info": "请勾选可用的研究产品，未勾选的研究产品，将自动隔离并生成隔离记录",
    "single.freeze.info": "按包装运输的研究产品，需按包装进行隔离。",
    "single.lost.info": "按包装运输的研究产品，需按包装进行丢失/作废。",
    "single.use.info": "按包装运输的研究产品，需按包装设置可用。",
    "single.freeze.lost.info": "丢失/作废仅限于“可用”、“已过期”状态的研究产品使用，共{total}条",
    "single.freeze.freeze.info": "隔离仅限于“可用”、“冻结”状态的研究产品使用，共{total}条",
    "single.freeze.setUse.info": "设为可用仅限于“丢失/作废”状态的研究产品使用，共{total}条",
    "single.freeze.lost.info.hint": "丢失/作废请勾选“可用、已过期”状态的研究产品",
    "shipment.order.medicines": "研究产品数量",
    "shipment.order.medicines.changeCount": "更换数量",
    "shipment.order.medicines.change": "更换",
    "shipment.order.mediciens.changeRecords": "更换研究产品",
    "shipment.order.medicines.change.records": "更换记录",
    "shipment.order.old.medicine": "原研究产品",
    "shipment.order.new.medicine": "更换后研究产品",
    "shipment.order.change.operTime": "更换时间",
    "logistics.info.details": "详情",
    "shipment.order.sendAndReceive.info": "起运地与目的地不能重复",
    "shipment.order.supplyUnset.info": "目的地未绑定供应计划，请联系项目管理员配置。",
    "shipment.order.medicine.change.info": "请确认列表单品，更换后，系统将重新分配研究产品。",
    "shipment.order.medicine.batch.info": "确定后，将自动更新对应单品的有效期和批次。",
    "shipment.order.medicine.change.success.info": "研究产品更换成功，系统重新分配的研究产品如下：",
    "shipment.order.change.success": "更换成功",
    "shipment.order.change.records": "更换记录",
    "shipment.order.change.count.info": "请填写更换数量",
    "shipment.order.create.info": "请选择研究产品",
    "shipment.order.create.count.info": "请填写数量",
    "shipment.order.availableCount": "库存数量",
    "shipment.order.package.method": "运送方式",
    "shipment.order.packageMethod.package": "包装",
    "shipment.order.packageMethod.single": "单品",
    "shipment.status.requested": "已确认",
    "shipment.status.transit": "已运送",
    "shipment.status.received": "已接收",
    "shipment.status.lose": "已丢失",
    "shipment.status.cancelled": "已取消",
    "shipment.status.toBeConfirm": "待确认",
    "shipment.status.apply": "已申请",
    "shipment.status.end": "已终止",
    "shipment.status.timeout": "已超时",
    "shipment.receive.work.task.title": "是否确定发送研究产品接收任务?",
    "shipment.receive.reason": "未接收研究产品",
    "shipment.receive.user": "接收人",
    "shipment.receive.time": "接收时间",
    "shipment.canceller": "取消人",
    "shipment.cancel.time": "取消时间",
    "shipment.list.role": "无权限查看",
    "shipment.store.alarm": "库存核查",
    "shipment.confirm.order": "确认订单",
    "shipment.oper.confirm.order": "确认",
    "shipment.receive.confirm.order": "即将接收研究产品",
    "shipment.isolation.confirm.order": "即将隔离研究产品",
    "shipment.cancel.order": "取消订单",
    "shipment.confirm.select": "提交失败，请确认研究产品数量后提交。",
    "shipment.medicine.availableCount": "库存",
    "projects.supplyPlan.all": "全部供应计划",
    "projects.supplyPlan.warning": "订单警戒值",
    "projects.supplyPlan.warning.site": "中心库存警戒值",
    "projects.supplyPlan.warning.dispensing": "受试者发放警戒值",
    "projects.supplyPlan.buffer": "最大缓冲量",
    "projects.supplyPlan.forecast": "预测窗口期",

    "projects.supplyPlan.secondSupply.tip": "再供应量：中心可用库存低于警戒值时，按照再供应量值自动触发中心订单；",
    "projects.supplyPlan.forecast.tip": "最低预测：系统将按照最小窗口期进行研究产品使用量预测，当中心可用库存不满足预期使用时，则会按照最大窗口期使用量触发订单。",
    "projects.supplyPlan.buffer.tip": "最大缓冲量：中心库存达到警戒值时，按照最大缓冲量值与中心库存计算后自动触发中心订单。",
    "projects.supplyPlan.na.tip": "NA：系统将无条件匹配，去触发中心自动订单。",
    "projects.supplyPlan.na.alert": "系统将无条件匹配，去触发中心自动订单。",
    "projects.supplyPlan.max.alert": "最大窗口期须大于或等于最小窗口期",

    "projects.supplyPlan.warning.tip": "中心库存警戒值，中心内对应的研究产品低于警戒值时，会触发警戒邮件通知到相关角色用户。",
    "projects.supplyPlan.warning.dispensing.tip": "受试者发放警戒值，研究产品库存低于对应受试者发放警戒值时，将限制发放。",

    "projects.supplyPlan.order.fail": "自动订单将创建失败。",
    "projects.supplyPlan.order.no.auto": "系统将无条件匹配，去触发中心自动订单。",
    "projects.supplyPlan.unDistributionDate.err": "不运送天数须大于或等于不计入天数与不发放天数之和。",
    "projects.supplyPlan.unDistributionDate.tip": "不运送天数，指距离研究产品过期多少天，即不从库房运送研究产品。",
    "projects.supplyPlan.notCountedDate.tip": "不计入天数，指研究产品运送到中心的天数。",
    "projects.supplyPlan.unProvideDate.tip": "不发放天数，指距离研究产品过期多少天，即不发放给受试者。",

    "projects.supplyPlan.secondSupply": "再供应量",
    "projects.supplyPlan.initSupply": "初始发放量",
    "projects.supplyPlan.unDistributionDate": "不运送天数",
    "projects.supplyPlan.notCountedDate": "不计入天数",
    "projects.supplyPlan.unProvideDate": "不发放天数",
    "projects.supplyPlan.validityReminder": "有效期提醒",
    "projects.supplyPlan.autoSupply": "自动配药",
    "projects.supplyPlan.autoSupplySize": "自动配给方式",
    "projects.supplyPlan.supplyMode": "补充方式",
    "projects.supplyPlan.name": "计划名称",
    "projects.supplyPlan.site": "研究中心",
    "projects.supplyPlan.storehouse": "药房库房",
    "projects.supplyPlan.description": "计划说明",
    "projects.supplyPlan.allSupply": "全研究产品补充",
    "projects.supplyPlan.singleSupply": "单研究产品补充",
    "projects.supplyPlan.allSupplyAndMedicine": "全研究产品补充+1个随机研究产品编号/包装号",
    "projects.supplyPlan.singleSupplyAndMedicine": "单研究产品补充+1个随机研究产品编号/包装号",
    "projects.supplyPlan.control": "供应计划控制",
    "projects.supplyPlan.control.tips": "供应计划控制：开启后，可独立配置不同属性的研究产品，应用不同的中心库存校验方式；\n" +
        "中心库存警戒：根据Plan中配置的“中心库存警戒值”判断，如达到条件会触发中心库存警戒邮件通知；\n" +
        "自动供应：中心开启自动供应，根据自动订单核查的触发条件校验库存，库存不足时将触发创建自动订单；\n" +
        "盲态研究产品最低预测自动供应停用：受限于盲态研究产品自动配给方式必须保持一致，当不需要盲态产品自动供应时选中此选项，且相应的窗口期配置为0-0。",
    "projects.supplyPlan.siteWarning": "中心库存警戒",
    "projects.supplyPlan.auto": "自动供应",
    "projects.supplyPlan.drugBlind": "盲态研究产品",
    "projects.supplyPlan.drugOpen": "开放研究产品",
    "projects.supplyPlan.drugBlindAuto": "盲态研究产品最低预测自动供应停用",

    "projects.subject.selectSite": "未选择中心或无权限查看",
    "subject.registration.success": "登记成功",
    "subject.status.registered": "已登记",
    "subject.status.to.be.random": "待随机",
    "subject.status.filtered": "已筛选",
    "subject.status.random": "已随机",
    "subject.status.blinded.urgent": "已揭盲(紧急)",
    "subject.status.blinded.pv": "已揭盲(pv)",
    "subject.status.exited": "已停用",
    "subject.status.replace": "已替换",
    "subject.status.screen.success": "筛选成功",
    "subject.status.screen.fail": "筛选失败",
    "subject.status.finish": "完成研究",
    "export.random.toBeRandom" :"待随机",
    "subject.status.invalid": "无效",
    "subject.status.join": "已入组",
    "subject.confirm.random": "随机成功后，影响受试者随机结果的相关信息无法再更改。",
    "subject.check.random.form": "所有表单必须填写",
    "subject.confirm.exited": "是否确定停用？",
    "subject.confirm.transport": "是否确认转运中心?",
    "subject.confirm.transport.content": "转运后，受试者数据只在新中心查看。",
    "subject.confirm.switch.cohort": "是否确认切换群组?",
    "subject.confirm.switch.cohort.content": "切换后，受试者数据将转移至新群组。",
    "subject.confirm.finish": "是否确定完成研究？",
    "subject.random": "随机",
    "subject.update": "修改",
    "subject.exited": "停用",
    "subject.screen": "筛选",
    "subject.finish": "完成研究",
    "subject.transport": "转运",
    "subject.switch.cohort": "切换群组",
    "subject.screen.title": "受试者筛选",
    "subject.finish.title": "受试者完成研究",
    "subject.screen.field": "是否筛选成功",
    "subject.screen.time.field": "筛选日期",
    "subject.screen.ICF.field": "ICF签署日期",
    "subject.stop.time": "实际停用日期",
    "subject.unblinding.urgent": "揭盲(紧急)",
    "subject.unblinding.pv": "揭盲(pv)",
    "subject.unblinding.ip": "揭盲(研究产品)",
    "subject.unblinding.sponsor": "是否已经通知申办方",
    "subject.trail": "轨迹",
    "subject.register": "登记",
    "subject.current.site": "当前中心",
    "subject.new.site": "新中心",
    "subject.current.cohort": "当前群组",
    "subject.new.cohort": "新群组",
    "subject.register.add": "受试者登记",
    "subject.register.update": "受试者修改",
    "subject.register.edit": "受试者编辑",
    "subject.reason": "停用原因",
    "subject.finish.remark": "备注",
    "subject.unblinding.remark": "备注",
    "subject.unblinding.reason": "揭盲原因",
    "subject.unblinding.time": "揭盲时间",
    "subject.unblinding.real.title": "请确认受试者实际使用与系统随机发放研究产品一致性！",
    "subject.unblinding.real.true": "一致或已登记，",
    "subject.unblinding.real.false": "不一致，请登记实际使用研究产品，以备后续跟踪，",
    "subject.unblinding.real.continue": "继续揭盲>>",
    "subject.unblinding.real.register": "去登记>>",
    "subject.sign.out": "受试者停用",
    "subject.replace": "替换",
    "subject.register.replace": "受试者替换",
    "subject.replace.button": "确认随机",
    "subject.replace.site.tip.title": "是否确认替换？",
    "subject.replace.site.tip.content": "替换受试者与当前受试者不在同一中心下，可能会存在中心下比例不均衡的风险。",
    "subject.replace.factor.tip.content": "替换受试者与当前受试者不在同一分层下，可能会存在中心下比例不均衡的风险。",
    "subject.number": "受试者号",
    "subject.number.replace": "替换{label}",
    "subject.number.random.replace": "替换受试者随机号",
    "subject.number.no.empty": "必须填写",
    "subject.already.unblinding": "已揭盲",
    "subject.Unblinded": "未揭盲",
    "subject.brokenBlinded": "揭盲",
    "subject.number.digit.no": "输入长度不等于实际设置的长度",
    "subject.unblinding.download": "揭盲报表下载",
    "subject.dispensing.download": "发放下载",
    "subject.random.download": "随机下载",
    "subject.unblinding.confirm": "是否确认对以下受试者进行揭盲？",
    "subject.unblinding.confirmTip": "揭盲确认",
    "subject.invalid.list": "无效列表",

    "subject.dispensing.subject.detail": "受试者信息详情查看",
    "subject.dispensing.subjectInfo": "受试者发放信息",
    "subject.dispensing.info": "发放信息",
    "subject.replace.info.old": "原受试者信息",
    "subject.dispensing.apply.subjectInfo.apply": "受试者发放申请详情",
    "subject.dispensing.apply.time": "申请时间",
    "subject.dispensing.apply.order.tip": "研究产品订单申请成功，请及时通知供应商提供产品。",
    "subject.dispensing.apply.order.tip.apply": "订单号状态为“已申请” ，可至“研究产品订单”页面查看。",
    "subject.dispensing.apply.order.tip.to_send": "订单号状态为“待确认” ，可至“研究产品订单”页面查看。",
    "subject.dispensing.subject": "受试者信息",
    "subject.dispensing.record": "发放记录",
    "subject.dispensing.time": "操作时间",
    "subject.dispensing.drugNumber": "研究产品编号",
    "subject.dispensing.drugNumber.system": "研究产品系统编号",
    "subject.dispensing.realDispensing": "实际使用研究产品",
    "subject.dispensing.registration.success": "登记成功！",
    "subject.dispensing.realDispensing.res": "系统发现登记研究产品组别与随机组别不一致，后续发放组别为本次实际登记的研究产品组别。",
    "subject.dispensing.realDispensingTip": "确定登记吗？",
    "subject.dispensing.realDispensingConfirm": "登记后，该研究产品状态更新为已使用。",
    "subject.dispensing.realDispensingConfirmDTP": "登记后，该DTP研究产品将触发新的订单，状态为已确认。",
    "subject.dispensing.selectDrugNumber": "请选择研究产品",
    "subject.dispensing.selectDrugNumber.label": "请选择研究产品标签",
    "subject.dispensing.selectedRepeatedly": "研究产品不可重复选择",
    "subject.dispensing.label.selectedRepeatedly": "研究产品标签不可重复选择",
    "subject.dispensing.label.typeSelectedRepeatedly": "组合标签，不允许多选发放",
    "subject.dispensing.NotFilledDrug": "研究产品名称或数量不完整",
    "subject.dispensing.formula.dispensing": "公式发放",
    "subject.dispensing.label.dispensing": "标签发放",
    "subject.dispensing.drugName.dispensing": "研究产品发放",
    "subject.dispensing.drugName": "研究产品名称",
    "subject.dispensing.drugLabel": "研究产品标签",
    "subject.dispensing.dose.tip": "上一次发放水平：{last}，本次发放水平：{current}。",
    "subject.dispensing.dispensing": "发放",
    "subject.dispensing.reissue": "补发",
    "subject.dispensing.replace": "受试者研究产品替换",
    "subject.dispensing.replace.reason": "替换原因",
    "subject.dispensing.cancel": "撤销",
    "subject.dispensing.visitSign": "计划外",
    "subject.dispensing.visitSignDispensing": "计划外发放",
    "subject.dispensing.visitSignDispensingReason": "计划外发放原因",
    "subject.dispensing.reissue.reason": "补发原因",
    "subject.dispensing.room": "房间号",
    "subject.dispensing.retrieval": "取回",
    "subject.dispensing.invalid": "不参加访视",
    "subject.dispensing.invalidTip": "是否确认不参加访视？",
    "subject.dispensing.resumeTip": "是否确认恢复访视？",
    "subject.dispensing.confirmInvalid": "不参加后，该次访视将跳过发放环节。",
    "subject.dispensing.confirmRetrieval": "确定取回吗？",
    "subject.dispensing.retrievalMessage": "取回后，研究产品将更新为可用或冻结。",
    "subject.dispensing.retrieve.freeze": "取回后，研究产品将更新为可用或冻结。",
    "subject.dispensing.number.register": "登记实际使用研究产品",
    "subject.dispensing.form.number": "系统发放研究产品",
    "subject.dispensing.form.realNumber": "实际使用研究产品",
    "subject.dispensing.medicine.available.frozen": "可用/冻结",
    "subject.dispensing.medicine.validator": "最大数量限制{data}，请重新输入。",
    "subject.dispensing.outsize": "超窗",
    "subject.dispensing.no.join": "未参加",
    "subject.dispensing.plan.time": "计划访视",
    "subject.dispensing.actual.time": "实际发放",
    "subject.dispensing.outsize.reason": "发放已超窗，请备注原因。",
    "subject.dispensing.open.visit": "开启后续阶段访视",
    "subject.dispensing.open.visit.tip1": "开启后，将关闭{atThisStage}发放功能，允许{nextStage}研究产品发放。",
    "subject.dispensing.open.visit.tip2": "开启后，将关闭{atThisStage}发放功能",
    "subject.dispensing.placeholder.input.count": "请输入数量",
    "subject.dispensing.placeholder.input.expiration": "请输入有效期",
    "subject.dispensing.placeholder.input.batch": "请输入批次",
    "subject.dispensing.placeholder.input.dispensing.count": "请输入发放数量",
    "subject.dispensing.apply": "发放申请",
    "subject.apply": "申请",
    "subject.dispensing.visitSignDispensing.apply": "计划外发放申请",
    "subject.dispensing.reissue.apply": "补发申请",
    "subject.dispensing.visitSignDispensingReason.apply": "访视外发放申请原因",
    "subject.dispensing.order.view": "查看订单",
    "subject.dispensing.detail": "返回详情",
    "subject.dispensing.apply.success": "申请成功",
    "subject.dispensing.dtp.success": "DTP订单创建成功",
    "subject.random.confirm": "随机确认",
    "subject.random.select.confirm": "是否确认随机？",
    "subject.dispensing.confirm": "系统将按照提交信息进行研究产品分配，请确认下述信息。",
    "subject.dispensing.confirm.replace": "系统将按照提交信息进行研究产品替换，请确认下述信息。",
    "subject.dispensing.replace.info": "替换研究产品",
    "subject.dispensing.replace.select": "选择替换研究产品",
    "subject.dispensing.replace.confirm": "受试者研究产品替换确认",
    "subject.dispensing.available.frozen": "可用/冻结",
    "subject.random.info": "随机信息",
    "subject.dispensing.drug.limit": "超过最大发放限制数，请重新输入",

    "subject.dispensing.drug.formula.tip": "系统建议 {number} {spec}  (单位研究产品：{specifications}{unit}/{spec})，实际用量 {count}{unit} 。",
    "subject.dispensing.drug.formula.tip.start": "系统建议 ",
    "subject.dispensing.drug.formula.tip.end": "{spec}  (单位研究产品：{specifications}{unit}/{spec})，实际用量 ",
    "subject.dispensing.drug.formula.weight.tip": "系统建议 {number} {spec}。",
    "subject.dispensing.drug.formula.weight.tip.start": "系统建议 ",
    "subject.dispensing.drug.formula.two.tip": "较{comparisonType}，变化> {radio}% ，本次计算体重为{currentComparisonType}。",
    "subject.dispensing.drug.formula.two.tip.start": "较{comparisonType}，变化{comparisonSymbols} ",
    "subject.dispensing.drug.formula.two.tip.end": " ，本次计算体重为{currentComparisonType}。",
    "subject.dispensing.drug.customer.formula.tip": "{unit}  (单位研究产品：{spec})。 ",
    "subject.dispensing.drug.input.error": "输入数量，不在设置范围内，请重新输入。",
    "subject.dispensing.drug.input.error.age": "无法匹配年龄/体重计算范围，请重新确认。",
    "subject.dispensing.drug.formula.error": "计算错误，计算值无法匹配发放数量。",
    "subject.dispensing.form.factor.title": "表单/分层因素名称",
    "subject.dispensing.factor.title": "值（随机分层/登记表单）",
    "subject.dispensing.actual.factor.title": "值（实际分层）",

    "subject.random.success": "随机成功",
    "subject.random.fail": "随机失败",
    "subject.random.all": "全部受试者",
    "subject.confirm.random.button": "确定随机",
    "subject.confirm.dispensing.button": "确定发放",
    "subject.random.section1": "是否已完成",
    "subject.random.section2": ",确定进行",
    "subject.random.section3": "的再随机吗",

    "notice.type": "通知类型",
    "notice.basic.settings": "基本设置",
    "notice.subject.add": "受试者登记",
    "notice.subject.random": "受试者随机",
    "notice.subject.signOut": "受试者停用",
    "notice.subject.replace": "受试者替换",
    "notice.subject.screen": "受试者筛选",
    "notice.subject.update": "受试者修改",
    "notice.subject.dispensing": "受试者发放",
    "notice.subject.alarm": "受试者警戒",
    "notice.subject.unblinding.type": "揭盲类型",
    "notice.subject.unblinding": "紧急揭盲",
    "notice.subject.pv.unblinding": "PV揭盲",
    "notice.subject.ip.unblinding": "研究产品揭盲",
    "notice.medicine.isolation": "研究产品隔离",
    "notice.medicine.order": "研究产品订单",
    "notice.medicine.retrieve": "研究产品回收",
    "notice.medicine.reminder": "研究产品有效期",
    "notice.medicine.alarm": "中心库存警戒",
    "notice.storehouse.alarm": "库房库存警戒",
    "notice.order.timeout": "订单超时",
    "notice.subject.medicine.alarm": "受试者库房/中心警戒提醒",
    "notice.subject.medicine.capping": "受试者库房/中心上限提醒",
    "notice.medicine.un_provide_date": "不发放天数",
    "notice.subject.alert.threshold": "受试者上限设置",
    "notice.email.content": "邮件内容",
    "notice.email.bodyContent": "正文配置",
    "notice.config.content": "内容配置",
    "notice.config.content.group": "非盲角色明文展示，盲态角色会展示***",
    "notice.config.state": "场景",
    "notice.content.group": "组别",
    "notice.content.number": "随机号",
    "notice.state.dispensing": "发放",
    "notice.state.unscheduled": "计划外发放",
    "notice.state.re.dispensing": "补发",
    "notice.state.replace": "研究产品替换",
    "notice.state.retrieval": "研究产品取回",
    "notice.state.register": "登记实际使用研究产品",
    "notice.state.not-attend": "不参加访视",
    "notice.state.form_factor": "表单/分层信息",
    "notice.state.screen": "筛选信息",
    "notice.state.stop": "停用信息",
    "notice.state.finish": "完成研究信息",
    "notice.order.scene.apply": "申请",
    "notice.order.scene.applyFail": "申请审批失败",
    "notice.order.scene.create": "创建(手动订单)",
    "notice.order.scene.confirm": "确认",
    "notice.order.scene.cancel": "取消",
    "notice.order.scene.close": "关闭",
    "notice.order.scene.transport": "运送",
    "notice.order.scene.receive": "接收",
    "notice.order.scene.stop": "终止",
    "notice.order.scene.lost": "丢失",
    "notice.order.scene.autoCreate": "创建(自动订单)",
    "notice.order.scene.autoCreateFail": "创建失败(自动订单)",
    "notice.order.scene.autoAlarm": "自动订单警戒",
    "notice.order.scene.change": "研究产品更换",
    "notice.order.scene.batch": "有效期和批次更新",
    "notice.order.tip": "仅适用研究产品订单",
    "notice.order.scene.isolation": "隔离",
    "notice.order.scene.release": "解隔离",
    "notice.alarm.scene.stock": "研究产品库存",
    "notice.alarm.scene.forecast": "库存使用时间预测",
    "notice.alarm.scene.forecast.time": "预测提醒提前天数",
    "order.timeout.days": "订单确认起天数",
    "order.send.days": "订单运送起天数",
    "tips.processing": "正在处理中......",
    "tips.search.permission": "正在获取用户权限......",
    "history.randomization": "随机配置轨迹",
    "history.order": "订单轨迹",
    "history.dispensing": "发放轨迹",
    "history.subject": "受试者轨迹",
    "history.medicine": "研究产品轨迹",
    "history.users": "人员管理轨迹",
    "history.medicine.drugFreeze": "轨迹",
    "history.supply-plan": "供应计划轨迹",
    "history.supply-plan-medicine": "供应计划研究产品配置轨迹",
    "history.sign": "研究者签名",
    "history.date": "签署日期",
    "history.user.search": "请输入操作人",
    "history.all": "全部轨迹",
    "barcode.isPackage.tip": "包装条行码，开启后，生成的条形码列表中，会包含研究产品条形码和包装条形码，适用于按包装运输的研究产品条形码场景。",
    "barcode.count": "研究产品码个数",
    "barcode.rule": "研究产品条形码规则",
    "barcode.package": "包装条形码",
    "barcode.package.rule": "包装条形码规则",
    "package.count": "包装码个数",
    "barcode.add": "生成条形码",
    "barcode.available-count": "可用个数",
    "barcode.list": "条形码列表",
    "barcode.scan": "扫码入仓",
    "barcode.correlationID": "任务ID",
    "barcode.taskIDs": "关联条形码任务",
    "shortCode.prefix": "短码前缀",
    "barcode.scan.confirm": "是否确定发送扫码入仓任务？",
    "barcode.scan.confirm.msg": "确认后，请登录APP完成任务。",
    "barcode": "条形码",
    "packageBarcode": "包装码",
    "workTask.add.success": "操作成功，任务已发送",
    "workTask.system.add.success": "系统已自动发送发放确认任务，请及时登录APP完成。",
    "projects.storehouse.logistics.supplier": "物流供应商",
    "projects.storehouse.logistics.shengsheng": "生生物流",
    "projects.storehouse.logistics.catalent": "catalent",
    "projects.storehouse.logistics.baicheng": "佰诚物流",
    "projects.storehouse.logistics.eDRUG": "eDRUG平台",
    "projects.storehouse.not.included": "订单中不包含\n隔离研究产品",
    "storehouse.country.region": "所属国家地区",
    "simulate_random.name": "名称",
    "simulate_random.all": "全部模拟随机",
    "simulate_random.site.count": "中心数",
    "simulate_random.country.count": "国家数",
    "simulate_random.region.count": "区域数",
    "simulate_random.run.count": "运行次数",
    "simulate_random.subject.count": "受试者数",
    "simulate_random.factor.ratio": "分层例数",
    "simulate_random.detail": "详情",
    "simulate_random.detail.unbalanced": "详情(不均衡数)",
    "simulate_random.site.details": "中心详情",
    "simulate_random.site.disequilibrium-number": "中心[不均衡数]",
    "simulate_random.site.detail.total": "总计",
    "simulate_random.site.detail.people.count": "人数",
    "simulate_random.site.detail.unbalanced": "不均衡",
    "simulate_random.run": "运行",
    "simulate_random.overview": "总览",
    "simulate_random.overview.avgsd": "总览(均数±标准差)",
    "simulate_random.overview.min": "总览(最小值)",
    "simulate_random.overview.unbalanced.run.count": "总览(不均衡运行次数)",
    "simulate_random.unbalanced.run.count": "不均衡运行次数",
    "simulate_random.project": "项目",
    "simulate_random.project.overview": "项目总览",
    "simulate_random.site.overview": "中心总览",
    "simulate_random.layered.overview": "分层总览",
    "simulate_random.country.overview": "国家总览",
    "simulate_random.region.overview": "区域总览",
    "simulate_random.combination.factor.overview": "组合分层总览",
    "simulate_random.combination.factor": "组合分层",
    "simulate_random.detail.meanStandard": "均数±标准差",
    "simulate_random.subject.count.min": "受试者例数最小值",
    "simulate_random.average.value": "均值",
    "simulate_random.standard.deviation": "标准差",
    "page_notice.system_update": "系统更新",
    "page_notice.system_update.current": "当前版本",
    "page_notice.email_error": "邮件异常",
    "page_notice.show_detail": "查看详情",
    "page_notice.fail_reason": "失败原因",
    "page_notice.mail_detail": "邮件详情",
    "page_notice.mail_resend": "重新发送",
    "page_notice.mail_resend_email": "重新发送邮件",
    "page_notice.message_center_all": "全部",
    "page_notice.message_center_read": "未读",
    "page_notice.message_center_all_read": "全部已读",


    "check.select.project": "选择项目",
    "check.select.site": "选择中心",
    "check.select.time": "入组时间",
    "check.select.unlimited": "不限",
    "dynamic.monitoring.total.random": "总随机",
    "dynamic.monitoring.random.error": "随机错误",
    "check.affiliated.site": "所属中心",
    "check.random.group": "随机组别",
    "check.blind.random.group": "盲底组别",
    "check.blind.random.number": "盲底随机号",
    "check.random.time": "随机时间",
    "check.search": "查询",
    "check.dispensing.medicine": "研究产品编号/名称",
    "check.dispensing.medicine.blind": "研究产品盲底",
    "check.dispensing.group": "研究产品组别",
    "check.dispensing.time": "发放时间",
    "check.dispensing.error": "发放错误",
    "check.subject.search": "请输入受试者号搜索",
    "check.system": "系统监控",
    "check.cohort": "群组/阶段",
    "group.cohort": "阶段/组别",
    "common.sites": "全部中心",
    "subject.room.download": "房间号导出",
    "subject.with.room": "是否包含房间号",
    "subject.dispensing.room.download": "房间号查看记录下载",
    "tips.locked": "屏幕已锁定，请重新输入密码",
    "user.status.not.active": "未激活",
    "user.status.enable": "已启用",
    "user.status.disable": "已禁用",
    "menu.back": "返回主页面",
    'tips.user.password-changed': "密码已修改请输入新密码。",
    'tips.user.disabled': "账号已禁止访问，请联系管理员。",
    'tips.user.deleted': "账号已失效请重新登录。",
    "menu.projects.project.subject.screening": "筛选",
    "menu.projects.project.subject.register": "登记",
    "menu.projects.project.subject.randomization": "随机",
    "menu.projects.project.subject.unblinding": "揭盲",
    "menu.projects.project.subject.dispensing": "发放",
    "menu.projects.project.supply.drug_upload": "研究产品上传",
    "menu.projects.project.supply.drug_freeze": "研究产品隔离",
    "menu.projects.project.build.careDesign": "治疗设计",

    //---------------------------------菜单部分----------------------------------------
    "menu.projects.project.supply.storehouse.no_number": "未编号单品管理",
    "menu.projects.project.build.push": "推送统计",
    "menu.home": "工作台",
    "menu.settings": "设置",
    "menu.settings.storehouse": "仓库",
    "menu.settings.roles": "角色权限",
    "menu.settings.users": "用户",
    "menu.projects": "项目",
    "menu.projects.main": "项目",
    "menu.projects.main.env": "项目环境",
    "menu.projects.main.setting": "项目设置",
    "menu.projects.main.setting.base": "基本信息",
    "menu.projects.main.setting.project": "项目信息",
    "menu.projects.main.setting.function": "业务功能",
    "menu.projects.main.setting.docking": "外部对接",
    "menu.projects.main.setting.permission": "项目权限",
    "menu.projects.main.setting.notice": "项目通知",
    "menu.projects.main.setting.custom": "自定义流程",
    "menu.projects.project": "项目详情",
    "menu.projects.project.overview": "概览",
    "menu.projects.project.status": "状态",
    "menu.projects.project.task": "项目任务",
    "menu.projects.project.random.statistics": "随机统计",
    "menu.projects.project.subject.statistics": "受试者统计",
    "menu.projects.project.depot.ip.statistics": "库房单品统计",
    "menu.projects.project.site.ip.statistics": "中心单品统计",
    "menu.projects.project.analysis": "异常分析",
    "menu.projects.project.dynamics": "项目动态",
    "menu.projects.project.home": "首页",
    "menu.projects.project.sub": "受试者",
    "menu.projects.project.subject": "受试者列表",
    "menu.projects.project.subject.urgent-unblinding": "揭盲（紧急）",
    "menu.projects.project.subject.urgent-unblinding.unblinding": "揭盲（紧急）",
    "menu.projects.project.subject.urgent-unblinding.approval-log": "审批记录",
    "menu.projects.project.subject.urgent-unblinding-pv": "揭盲（pv）",
    "menu.projects.project.subject.urgent-unblinding.unblinding-pv": "揭盲（pv）",
    "menu.projects.project.subject.urgent-unblinding.approval-log-pv": "审批记录",
    "menu.projects.project.subject.urgent-unblinding-ip": "揭盲（研究产品）",
    "menu.projects.project.subject.urgent-unblinding.unblinding-ip": "揭盲（研究产品）",
    "menu.projects.project.subject.urgent-unblinding.approval-log-ip": "审批记录",
    "menu.projects.project.subject.visit.cycle": "访视管理",
    "menu.projects.project.supply": "供应管理",
    "menu.projects.project.supply.storehouse": "库房统计",
    "menu.projects.project.supply.storehouse.summary": "概览",
    "menu.projects.project.supply.storehouse.single": "单品管理",
    "menu.projects.project.supply.site": "中心药房",
    "menu.projects.project.supply.site.summary": "概览",
    "menu.projects.project.supply.site.single": "单品管理",
    "menu.projects.project.supply.site.no_number": "未编号单品管理",
    "menu.projects.project.supply.shipment": "研究产品订单",
    "menu.projects.project.supply.shipment.approval": "审批记录",
    "menu.projects.project.supply.shipment.logistics": "物流",
    "menu.projects.project.supply.drug_recovery": "回收订单管理",
    "menu.projects.project.supply.drug_recovery.logistics": "物流",
    "menu.projects.project.supply.release-record": "隔离管理",
    "menu.projects.project.supply.drug": "研究产品",
    "menu.projects.project.supply.drug.order": "研究产品订单",
    "menu.projects.project.supply.drug.single": "单品管理",
    "menu.projects.project.supply.drug.no_number": "未编号单品管理",
    "menu.projects.project.build": "项目构建",
    "menu.projects.project.build.storehouse": "库房管理",
    "menu.projects.project.build.site": "中心管理",
    "menu.projects.project.build.site.supply-plan": "供应计划",
    "menu.projects.project.build.attributes": "属性配置",
    "menu.projects.project.build.code_rule": "编码配置",
    "menu.projects.project.build.simulate_random": "模拟随机",
    "menu.projects.project.build.randomization": "随机配置",
    "menu.projects.project.build.randomization.design": "随机设计",
    "menu.projects.project.build.randomization.design.type": "随机类型",
    "menu.projects.project.build.randomization.design.group": "治疗组别",
    "menu.projects.project.build.randomization.design.factor": "分层因素",
    "menu.projects.project.build.randomization.design.list": "随机列表",
    "menu.projects.project.build.randomization.design.attribute": "随机列表属性",
    "menu.projects.project.build.randomization.design.block": "随机号区组",
    "menu.projects.project.build.randomization.design.factor-in": "分层因素",
    "menu.projects.project.build.randomization.form": "表单配置",
    "menu.projects.project.build.randomization.tooltip": "请先选中列表数据",
    "menu.projects.project.build.drug": "研究产品管理",
    "menu.projects.project.build.drug.visit": "访视管理",
    "menu.projects.project.build.drug.visit.setting": "设置",
    "menu.projects.project.build.drug.config": "研究产品配置",
    "menu.projects.project.build.drug.config.setting": "设置",
    "menu.projects.project.build.drug.list": "研究产品列表",
    "menu.projects.project.build.drug.no_number": "未编号研究产品",
    "menu.projects.project.build.drug.batch": "批次管理",
    "menu.projects.project.build.drug.barcode": "条形码列表",
    "menu.projects.project.build.drug.barcode_label": "标签管理",
    "menu.projects.project.build.plan": "供应计划",
    "menu.projects.project.build.plan.config": "供应计划管理",
    "menu.projects.project.build.history": "项目日志",
    "menu.projects.project.settings": "其它设置",
    "menu.projects.project.settings.notice": "通知设置",
    "menu.projects.project.settings.user": "人员管理",
    "menu.projects.project.settings.export": "配置导出",
    "menu.projects.project.settings.role": "角色权限",
    "menu.projects.project.settings.config": "项目配置",
    "menu.projects.project.monitor": "动态监控",

    "menu.projects.project.info": "项目信息",
    "operation.projects.project.info.view": "查看",
    "menu.projects.project.basic.information": "基本信息",
    "operation.projects.project.basic.information.view": "查看",
    "menu.projects.project.basic.environment": "项目环境",
    "operation.projects.project.basic.environment.view": "查看",
    "menu.projects.project.business.functions": "业务功能",
    "operation.projects.project.business.functions.view": "查看",
    "menu.projects.project.external.docking": "外部对接",
    "operation.projects.project.external.docking.view": "查看",
    "menu.projects.project.custom.process": "自定义流程",
    "operation.projects.project.custom.process.view": "查看",
    "menu.projects.project.permissions": "项目权限",
    "operation.projects.project.permissions.view": "查看",
    "menu.projects.notice.permissions": "项目通知",
    "operation.projects.notice.permissions.view": "查看",


    "menu.projects.project.subject.medicine.trail": "轨迹",
    "menu.projects.project.subject.trail": "轨迹",
    "menu.projects.project.storehouse.sku.trail": "轨迹",
    "menu.projects.project.drug.sku.trail": "轨迹",
    "menu.projects.project.site.sku.trail": "轨迹",
    "menu.projects.project.site.no_number.trail": "轨迹",
    "menu.projects.project.storehouse.no_number.trail": "轨迹",
    "menu.projects.project.supply.shipment.trail": "轨迹",
    "menu.projects.project.recovery.trail": "轨迹",
    "menu.projects.project.freeze.trail": "轨迹",
    "menu.projects.project.randomization.list.trail": "轨迹",
    "menu.projects.project.medicine.upload.trail": "轨迹",
    "menu.projects.project.supply.order.trail": "轨迹",
    "menu.report": "报表",
    "menu.report.userRoleStatus": "用户角色状态报表",
    "menu.report.userRoleAssignHistory": "用户角色分配记录",
    "menu.report.configureReport": "配置报告",
    "menu.report.auditTrailExport": "稽查轨迹",
    "menu.report.auditTrailExport.build": "项目构建",
    "menu.report.auditTrailExport.settings": "项目设置",
    "menu.report.auditTrailExport.release-record": "隔离管理",
    "menu.report.auditTrailExport.order": "研究产品订单",
    "menu.report.auditTrailExport.drug_recovery": "研究产品回收",
    "menu.report.auditTrailExport.subject": "受试者",
    "menu.report.auditTrailExport.dispensing": "受试者发放",
    "menu.report.auditTrailExport.ip": "研究产品",
    "menu.report.auditTrailExport.userLoginHistory": "用户登录历史记录",
    "menu.report.auditTrailExport.userRoleAssignHistory": "用户角色分配记录",
    "menu.report.projectPermissionConfigurationExport": "项目权限配置报表",
    "menu.report.ProjectNotificationsConfigurationReportExport": "项目通知配置报表",
    "menu.report.randomizeReport": "受试者明细报表",
    "menu.report.dispenseReport": "受试者发放报表",
    "menu.report.unblindingReport": "揭盲报表",
    "menu.report.depotItemReport": "库房单品报表",
    "menu.report.sourceRandomizationListExport": "随机盲底",
    "menu.report.sourceIPExport": "研究产品盲底",
    "menu.report.siteItemReport": "中心药房单品报表",
    "menu.report.sourceIpUploadHistory": "研究产品盲底上传记录",
    "menu.report.randomizationSimulationResult": "模拟随机结果",
    "menu.report.returnOrdersReport": "回收订单报表",
    "menu.report.shipmentOrdersReport": "研究产品订单报表",
    "menu.report.siteIPStatisticsExport": "中心单品统计报表",
    "menu.report.randomizationStatisticsExport": "随机统计报表",
    "menu.report.subjectStatisticsExport": "受试者统计报表",
    "menu.report.depotIPStatisticsExport": "库房单品统计报表",
    "menu.report.userLoginHistory": "用户登录历史记录",
    "menu.report.RandomizationSimulationPDFExport": "模拟随机报告",
    "menu.report.forecastingPrediction": "研究产品预测报表",
    "menu.report.visitForecast": "访视统计报表",

    //多语言
    "menu.projects.project.multiLanguage": "多语言",
    "menu.projects.project.multiLanguage.details": "语言详情",

    //---------------------------------菜单部分----------------------------------------
    //---------------------------------权限部分----------------------------------------
    //首页 设置 角色权限
    "operation.settings.roles.view": "查看",
    "operation.settings.roles.add": "添加",
    "operation.settings.roles.edit": "编辑",
    "operation.settings.roles.config": "权限配置",
    "operation.settings.roles.export": "导出",
    //首页 设置 用户
    "operation.settings.users.view": "查看",
    "operation.settings.users.add": "新增",
    "operation.settings.users.setting": "设置",
    "operation.settings.users.edit": "编辑",
    "operation.settings.users.close": "关闭",
    "operation.settings.users.close.batch": "批量关闭",
    "operation.settings.users.invite-again": "再次邀请",
    "operation.settings.users.cancel": "取消",
    "operation.settings.users.export": "导出",
    //首页 设置 库房
    "operation.settings.storehouse.view": "查看",
    "operation.settings.storehouse.add": "新增",
    "operation.settings.storehouse.edit": "编辑",
    "operation.settings.storehouse.delete": "删除",
    //项目 项目
    "operation.projects.main.view": "查看",
    "operation.projects.main.create": "创建项目",
    //项目 项目 项目环境
    "operation.projects.main.config.view": "查看",
    "operation.projects.main.config.create": "添加环境",
    "operation.projects.main.config.copy": "复制环境",
    "operation.projects.main.config.edit_env": "编辑环境",
    "operation.projects.main.config.unlock": "解锁",
    "operation.projects.main.config.lock": "锁定",
    "operation.projects.main.config.add": "添加",
    "operation.projects.main.config.edit": "编辑",
    "operation.projects.main.config.delete": "删除",
    "operation.projects.main.config.copy_cohort": "复制",
    // "operation.projects.main.config.edit": "管理",

    //项目 项目 项目设置
    "operation.projects.main.setting.view": "查看",
    //项目 项目 项目设置 基本信息
    "operation.projects.main.setting.base.view": "查看",
    "operation.projects.main.setting.base.edit": "编辑",
    "operation.projects.main.setting.base.modify": "修改",
    //项目 项目 项目设置 业务功能
    "operation.projects.main.setting.function.view": "查看",
    "operation.projects.main.setting.function.edit": "编辑",
    "operation.projects.main.setting.function.admin": "管理员启用/禁用",
    //项目 项目 项目设置 外部对接
    "operation.projects.main.setting.docking.view": "查看",
    "operation.projects.main.setting.docking.edit": "编辑",
    //项目 项目 项目设置 自定义流程
    "operation.projects.main.setting.custom.view": "查看",
    "operation.projects.main.setting.custom.edit": "编辑",
    //项目 项目 项目设置 项目权限
    "operation.projects.main.setting.permission.view": "查看",
    "operation.projects.main.setting.permission.add": "添加",
    "operation.projects.main.setting.permission.edit": "编辑",
    "operation.projects.main.setting.permission.setting": "权限设置",
    // "operation.projects.main.setting.permission.export": "导出",
    //项目 项目 项目设置 项目通知
    "operation.projects.main.setting.notice.view": "查看",
    "operation.projects.main.setting.notice.add": "添加",
    "operation.projects.main.setting.notice.delete": "删除",
    "operation.projects.main.setting.notice.edit": "编辑",
    //项目 项目 项目首页 查看
    "operation.projects.home.view": "查看",
    "operation.project.status.view": "查看",
    "operation.project.task.view": "查看",
    "operation.project.analysis.view": "查看",
    "operation.project.dynamics.view": "查看",
    "operation.project.site.IPStatistics.view": "查看",
    "operation.project.site.IPStatistics.download": "导出",
    "operation.project.depot.IPStatistics.view": "查看",
    "operation.project.depot.IPStatistics.download": "导出",
    "operation.project.subject.view": "查看",
    "operation.project.subject.download": "导出",
    "operation.project.random.view": "查看",
    "operation.project.random.download": "导出",
    //项目详情 受试者管理
    // "operation.subject.view-list": "查看受试者列表",
    // "operation.subject.random": "受试者随机",
    // "operation.subject.replace": "受试者替换",
    // "operation.subject.trail": "受试者轨迹",
    // "operation.subject.unblinding-pv": "受试者揭盲(pv)",
    // "operation.subject.medicine.view-dispensing": "查看发放",
    // "operation.subject.medicine.trail": "发放轨迹",
    // "operation.subject.medicine.dispensing": "发放研究产品",
    // "operation.subject.medicine.reissue": "补发研究产品",
    // "operation.subject.medicine.replace": "替换研究产品",
    // "operation.subject.unblinding": "受试者揭盲",
    // "operation.subject.download": "下载揭盲报表",
    // "operation.subject.medicine.export": "发放下载",
    // "operation.subject.download-random": "下载随机报表",
    // 换成数据权限
    // "operation.subject.medicine.view-dispensing-blind": "查看发放 (盲态）",
    // "operation.subject.medicine.export-blind": "发放下载（盲态）",
    // "operation.subject.download-random-blinded": "下载随机报表（盲态）",
    // "operation.subject.view-list-blinded": "查看受试者列表（盲态）",
    // "operation.subject.registered": "登记受试者",
    // "operation.subject.update": "受试者修改",
    // "operation.subject.delete": "删除受试者",
    // "operation.subject.medicine.room": "查看房间号",
    // "operation.subject.medicine.retrieval": "取回研究产品",
    // "operation.subject.medicine.register": "登记实际使用药物",
    // "operation.subject.medicine.out-visit-dispensing": "访视外发放",
    // "operation.subject.medicine.invalid": "不参加访视",
    // "operation.subject.medicine.export-room": "发放下载(包含房间号)",
    // "operation.subject.medicine.room-download": "下载房间号查看记录",
    // "operation.subject.secede": "停用",
    "operation.subject.view-list": "查看",
    "operation.subject.random": "随机",
    "operation.subject.replace": "受试者替换",
    "operation.subject.trail": "查看",
    "operation.subject.unblinding-pv": "揭盲(pv)",
    "operation.subject.medicine.view-dispensing": "受试者信息详情查看",
    "operation.subject.medicine.transport": "转运",
    "operation.subject.medicine.trail": "查看",
    "operation.subject.medicine.dispensing": "发放",
    "operation.subject.medicine.reissue": "补发",
    "operation.subject.medicine.replace": "研究产品替换",
    "operation.subject.medicine.resume": "恢复发放",
    "operation.subject.medicine.formula.update": "修改",
    "operation.subject.medicine.joinTime": "编辑",
    //"operation.subject.unblinding": "揭盲(紧急)",
    "operation.subject.unblinding": "查看",
    "operation.subject.unblinding-application": "揭盲(紧急)申请",
    "operation.subject.unblinding-approval": "揭盲(紧急)审批",
    "operation.subject.unblinding-log": "查看",
    "operation.subject.unblinding-sms": "发送短信",
    "operation.subject.unblinding-print": "打印",
    "operation.subject.unblinding-pv-view": "查看",
    "operation.subject.unblinding-pv-application": "揭盲(pv)申请",
    "operation.subject.unblinding-pv-approval": "揭盲(pv)审批",
    "operation.subject.unblinding-pv-log": "查看",
    "operation.subject.unblinding-pv-sms": "发送短信",
    "operation.subject.unblinding-pv-print": "打印",
    "operation.subject.unblinding-ip-view": "查看",
    "operation.subject.unblinding-ip-application": "揭盲(研究产品)申请",
    "operation.subject.unblinding-ip-approval": "揭盲(研究产品)审批",
    "operation.subject.unblinding-ip-log": "查看",
    "operation.subject.unblinding-ip-sms": "发送短信",
    "operation.subject.unblinding-ip-print": "打印",
    // "operation.subject.download": "揭盲报表下载",
    // "operation.subject.medicine.export": "发放下载",
    // "operation.subject.download-random": "随机下载",
    "operation.subject.registered": "登记",
    "operation.subject.invalid-list": "无效列表",
    "operation.subject.switch.cohort": "切换群组",
    "operation.subject.update": "受试者修改",
    "operation.subject.delete": "受试者删除",
    "operation.subject.medicine.room": "房间号查看",
    "operation.subject.medicine.retrieval": "取回",
    "operation.subject.medicine.out-visit-dispensing": "计划外发放",
    "operation.subject.medicine.invalid": "不参加访视",
    "operation.subject.medicine.room-download": "房间号查看记录下载",
    "operation.subject.medicine.register": "登记实际使用研究产品",
    "operation.subject.medicine.setUp": "设置",
    "operation.subject.secede": "停用（随机）",
    "operation.subject.secede-registered": "停用（登记/筛选成功）",
    "operation.subject.print": "打印",
    "operation.subject.screen": "筛选",
    "operation.subject.finish": "完成研究",
    "operation.subject.medicine.trail.print": "打印",
    "operation.subject.cohort.status": "状态修改",
    "operation.subject.cohort.status.no.permission": "无权限，请联系管理员分配操作权限。",
    //项目详情 受试者管理(DTP）
    "operation.subject-dtp.view-list": "查看",
    "operation.subject-dtp.random": "随机",
    "operation.subject-dtp.replace": "受试者替换",
    "operation.subject-dtp.trail": "受试者轨迹",
    "operation.subject-dtp.unblinding-pv": "揭盲(pv)",
    "operation.subject-dtp.medicine.view-dispensing": "发放申请详情",
    "operation.subject-dtp.medicine.trail": "发放轨迹",
    "operation.subject-dtp.medicine.dispensing": "申请",
    "operation.subject-dtp.medicine.transport": "转运",
    "operation.subject-dtp.medicine.reissue": "补发申请",
    "operation.subject-dtp.medicine.replace": "研究产品替换",
    // "operation.subject-dtp.unblinding": "揭盲(紧急)",
    "operation.subject-dtp.unblinding": "查看",
    "operation.subject-dtp.unblinding-application": "揭盲(紧急)申请",
    "operation.subject-dtp.unblinding-approval": "揭盲(紧急)审批",
    "operation.subject-dtp.unblinding-log": "查看",
    "operation.subject-dtp.unblinding-sms": "发送短信",
    "operation.subject-dtp.download": "揭盲报表下载",
    "operation.subject-dtp.medicine.export": "发放下载",
    "operation.subject-dtp.download-random": "随机下载",
    "operation.subject-dtp.registered": "登记",
    "operation.subject-dtp.update": "受试者修改",
    "operation.subject-dtp.delete": "受试者删除",
    "operation.subject-dtp.medicine.room": "房间号查看",
    "operation.subject-dtp.medicine.out-visit-dispensing": "计划外发放申请",
    "operation.subject-dtp.medicine.invalid": "不参加本次访视",
    "operation.subject-dtp.medicine.export-room": "发放下载(包含房间号)",
    "operation.subject-dtp.medicine.room-download": "房间号查看记录下载",
    "operation.subject-dtp.medicine.register": "登记实际使用的研究产品",
    "operation.subject-dtp.secede": "停用（随机）",
    "operation.subject-dtp.secede-registered": "停用（登记/筛选成功）",
    "operation.subject-dtp.print": "打印受试者轨迹",
    "operation.subject-dtp.medicine.print": "打印发放轨迹",
    "operation.subject-dtp.screen": "筛选",
    "operation.subject-dtp.finish": "完成研究",
    "operation.project.subject.visit.cycle.view": "查看",
    "operation.project.subject.visit.cycle.notice.view": "通知-查看",
    "operation.project.subject.visit.cycle.send.notice": "发送通知",
    //项目详情 供应管理	库房统计	概览
    // "operation.supply.storehouse.medicine.summary": "查看库房研究产品概览",
    // "operation.supply.storehouse.medicine.summary-blind": "查看库房研究产品概览（盲态）",
    "operation.supply.storehouse.medicine.summary": "查看",
    //项目详情 供应管理	库房统计	单品管理
    // "operation.supply.storehouse.medicine.singe": "查看库房研究产品单品",
    // "operation.supply.storehouse.medicine.singe-blind": "查看库房研究产品单品（盲态）",
    // "operation.supply.storehouse.medicine.use": "研究产品设为可用（库房统计）",
    // "operation.supply.storehouse.medicine.freeze": "研究产品隔离（库房统计）",
    // "operation.supply.storehouse.medicine.history": "轨迹（库房统计）",
    // "operation.supply.storehouse.medicine.download": "下载库房统计报表",
    "operation.supply.storehouse.medicine.singe": "查看",
    "operation.supply.storehouse.medicine.use": "设为可用",
    "operation.supply.storehouse.medicine.freeze": "隔离",
    "operation.supply.storehouse.medicine.lost": "丢失/作废",
    "operation.supply.storehouse.medicine.history": "查看",
    "operation.supply.storehouse.medicine.print": "打印",
    // "operation.supply.storehouse.medicine.download": "数据下载",
    "operation.supply.storehouse.no_number.view": "查看",
    "operation.supply.storehouse.no_number.freeze": "隔离",
    "operation.supply.storehouse.no_number.lost": "丢失/作废",
    "operation.supply.storehouse.no_number.history": "查看",
    "operation.supply.storehouse.no_number.print": "打印",
    //项目详情	供应管理	中心药房	概览
    // "operation.supply.site.medicine.summary": "查看中心研究产品概览",
    // "operation.supply.site.medicine.summary-blind": "查看中心研究产品概览（盲态）",
    "operation.supply.site.medicine.summary": "查看",
    "operation.supply.site.medicine.summary.formula": "库存使用时间预测",
    //项目详情	供应管理	中心药房	单品管理
    // "operation.supply.site.medicine.singe": "查看中心研究产品单品",
    // "operation.supply.site.medicine.singe-blind": "查看中心研究产品单品（盲态）",
    // "operation.supply.site.medicine.use": "研究产品设为可用（中心药房）",
    // "operation.supply.site.medicine.freeze": "研究产品隔离（中心药房）",
    // "operation.supply.site.medicine.history": "轨迹（中心药房）",
    // "operation.supply.site.medicine.download": "下载中心药房报表",
    "operation.supply.site.medicine.singe": "查看",
    "operation.supply.site.medicine.use": "设为可用",
    "operation.supply.site.medicine.freeze": "隔离",
    "operation.supply.site.medicine.lost": "丢失/作废",
    "operation.supply.site.medicine.history": "查看",
    "operation.supply.site.medicine.print": "打印",
    // "operation.supply.site.medicine.download": "数据下载",
    //项目详情	供应管理	中心药房 未编号单品管理
    "operation.supply.site.no_number.view": "查看",
    "operation.supply.site.no_number.freeze": "隔离",
    "operation.supply.site.no_number.lost": "丢失/作废",
    "operation.supply.site.no_number.history": "查看",
    "operation.supply.site.no_number.print": "打印",
    //项目详情 供应管理 研究产品 研究产品订单
    "operation.supply.drug.order.list": "查看",
    "operation.supply.drug.order.send": "运送",
    "operation.supply.drug.order.receive": "接收",
    "operation.supply.drug.order.end": "终止",
    "operation.supply.drug.order.cancel": "取消",
    "operation.supply.drug.order.reason": "原因",
    "operation.supply.drug.order.download": "下载",
    "operation.supply.drug.order.confirm": "确认",
    "operation.supply.drug.order.close": "关闭",
    "operation.supply.drug.order.history": "查看",
    "operation.supply.drug.order.print": "打印",
    //项目详情 供应管理 研究产品 单品管理
    "operation.supply.drug.single.sku": "查看",
    "operation.supply.drug.single.history": "查看",
    "operation.supply.drug.single.print": "打印",
    "operation.supply.drug.single.download": "数据下载",
    "operation.supply.drug.single.delete": "丢失/作废",
    "operation.supply.drug.single.use": "设为可用",
    //项目详情	供应管理	研究产品 未编号单品管理
    "operation.supply.drug.no_number.view": "查看",
    //项目详情	供应管理	研究产品订单
    // "operation.supply.shipment.create": "创建研究产品订单",
    // "operation.supply.shipment.cancel": "取消研究产品订单",
    // "operation.supply.shipment.send": "发送研究产品订单",
    // "operation.supply.shipment.lose": "研究产品订单丢失",
    // "operation.supply.shipment.list": "查看研究产品订单列表",
    // "operation.supply.shipment.list-blind": "查看研究产品订单列表(盲态)",
    // "operation.supply.shipment.receive": "接收研究产品订单",
    // "operation.supply.shipment.download": "下载研究产品订单报表",
    // "operation.supply.shipment.alarm": "库房核查",
    // "operation.supply.shipment.history": "轨迹（研究产品订单）",
    "operation.supply.shipment.create": "新增",
    "operation.supply.shipment.cancel": "通用-取消",
    "operation.supply.shipment.send": "运送",
    "operation.supply.shipment.lose": "丢失",
    "operation.supply.shipment.list": "查看",
    "operation.supply.shipment.receive": "接收",
    "operation.supply.shipment.alarm": "库房核查",
    "operation.supply.shipment.history": "查看",
    "operation.supply.shipment.print": "打印",
    "operation.supply.shipment.confirm": "通用-确认",
    "operation.supply.shipment.reason": "原因",
    "operation.supply.shipment.approval": "研究中心订单申请审批",
    "operation.supply.shipment.approval.view": "查看",
    "operation.supply.shipment.approval.print": "打印",
    "menu.projects.project.supply.shipment.detail": "研究产品详情",
    "operation.supply.shipment.detail.change": "更换",
    "operation.supply.shipment.detail.changeRecord": "更换记录",
    "operation.supply.shipment.detail.edit": "编辑",
    "menu.projects.project.supply.recovery.detail": "研究产品详情",
    "operation.supply.recovery.detail.change": "更换",
    "operation.supply.recovery.detail.changeRecord": "更换记录",
    "operation.supply.shipment.close": "通用-关闭",
    "operation.supply.shipment.terminated": "通用-终止",
    "operation.supply.shipment.logistics.view": "查看",
    "operation.supply.shipment.logistics.edit": "编辑",
    "operation.supply.drug_recovery.logistics.view": "查看",
    "operation.supply.drug_recovery.logistics.edit": "编辑",
    "operation.supply.shipment.detail.view": "详情",
    "operation.supply.drug_recovery.detail.view": "详情",
    "operation.supply.shipment.contacts": "联系人",
    "operation.supply.shipment.confirm-dtp": "DTP-确认",
    "operation.supply.shipment.cancel-dtp": "DTP-取消",
    "operation.supply.shipment.close-dtp": "DTP-关闭",
    "operation.supply.shipment.terminated-dtp": "DTP-终止",
    //项目详情	供应管理	研究产品回收
    // "operation.supply.recovery.list": "查看研究产品回收订单列表",
    // "operation.supply.recovery.list-blind": "查看研究产品订单列表(盲态)",
    // "operation.supply.recovery.receive": "接收研究产品回收订单",
    // "operation.supply.recovery.add": "创建研究产品回收订单",
    // "operation.supply.recovery.confirm": "运送研究产品回收订单",
    // "operation.supply.recovery.cancel": "取消研究产品回收订单",
    // "operation.supply.recovery.lose": "标记研究产品回收订单丢失",
    // "operation.supply.recovery.download": "下载研究产品回收报表",
    "operation.supply.recovery.detail.view": "详情",
    "operation.supply.recovery.list": "查看",
    "operation.supply.recovery.add": "新增",
    "operation.supply.recovery.receive": "接收",
    "operation.supply.recovery.confirm": "运送",
    "operation.supply.recovery.cancel": "取消",
    "operation.supply.recovery.lose": "丢失",
    // "operation.supply.recovery.download": "下载",
    "operation.supply.recovery.reason": "原因",
    "operation.supply.recovery.history": "查看",
    "operation.supply.recovery.print": "打印",
    "operation.supply.recovery.determine": "确认",
    "operation.supply.recovery.close": "关闭",
    "operation.supply.recovery.end": "终止",
    //项目详情	供应管理	隔离记录
    // "operation.supply.freeze.list": "查看研究产品隔离列表",
    // "operation.supply.freeze.release": "解隔离研究产品",
    "operation.supply.freeze.list": "查看",
    "operation.supply.freeze.release": "解隔离",
    "operation.supply.freeze.delete": "丢失/作废",
    "operation.supply.freeze.history": "查看",
    "operation.supply.freeze.print": "打印",
    "operation.supply.freeze.approval": "解隔离审批",
    //项目详情	项目构建	库房管理
    // "operation.build.storehouse.add": "添加库房管理",
    // "operation.build.storehouse.delete": "删除库房管理",
    // "operation.build.storehouse.edit": "编辑库房管理",
    // "operation.build.storehouse.notice": "库房管理有效期提醒设置",
    // "operation.build.storehouse.view": "查看库房管理信息",
    "operation.build.storehouse.add": "新增",
    "operation.build.storehouse.delete": "删除",
    "operation.build.storehouse.edit": "编辑",
    "operation.build.storehouse.notice": "设置",
    "operation.build.storehouse.view": "查看",
    "operation.build.storehouse.alarm": "研究产品库存警戒",
    //项目详情 项目构建 中心管理
    // "operation.build.site.active": "激活/禁用中心",
    // "operation.build.site.disable": "启用/禁用中心",
    // "operation.build.site.first-dispensing": "首次发放",
    // "operation.build.site.relate-plan": "关联供应计划",
    "operation.build.site.view": "查看",
    "operation.build.site.edit": "编辑",
    "operation.build.site.add": "添加",
    "operation.build.site.dispensing": "发放",
    "operation.build.site.supply-plan.view": "查看",
    "operation.build.site.supply-plan.edit": "编辑",
    //项目详情 项目构建 属性配置
    // "operation.build.attribute.view": "查看属性配置",
    // "operation.build.attribute.edit": "修改属性配置",
    // "operation.build.attribute.history": "查看属性配置轨迹",
    "operation.build.attribute.view": "查看",
    "operation.build.attribute.edit": "编辑",
    "operation.build.attribute.history": "查看轨迹",
    //项目详情 项目构建 编码配置
    // "operation.build.code-rule.view": "查看编码配置",
    // "operation.build.code-rule.edit": "编辑编码配置",
    "operation.build.code-rule.view": "查看",
    "operation.build.code-rule.edit": "编辑",
    //项目详情 项目构建 模拟随机
    // "operation.build.simulate-random.view": "查看模拟随机",
    // "operation.build.simulate-random.edit": "编辑模拟随机",
    "operation.build.simulate-random.view": "查看",
    "operation.build.simulate-random.edit": "编辑",
    "operation.build.simulate-random.add": "新增",
    "operation.build.simulate-random.run": "运行",
    "operation.build.simulate-random.site": "总览",
    "operation.build.simulate-random.factor": "详情",
    // "operation.build.simulate-random.download": "数据下载",
    //项目详情 项目构建 随机配置 随机设计 随机类型
    // "operation.build.randomization.type.view": "查看随机类型",
    // "operation.build.randomization.type.edit": "修改随机类型",
    "operation.build.randomization.type.view": "查看",
    "operation.build.randomization.type.edit": "编辑",
    //项目详情 项目构建 随机配置 随机设计 治疗组别
    // "operation.build.randomization.group.add": "新增组别",
    // "operation.build.randomization.group.delete": "删除组别",
    // "operation.build.randomization.group.edit": "设置组别",
    // "operation.build.randomization.group.view": "查看组别",
    // "operation.build.randomization.edc.mapping": "edc映射",
    "operation.build.randomization.group.add": "新增",
    "operation.build.randomization.group.delete": "删除",
    "operation.build.randomization.group.edit": "编辑",
    "operation.build.randomization.group.view": "查看",
    "operation.build.randomization.group.inactivating": "同步",
    //项目详情 项目构建 随机配置 随机设计 分层因素
    // "operation.build.randomization.factor.add": "新增分层",
    // "operation.build.randomization.factor.country": "国家分层",
    // "operation.build.randomization.factor.delete": "删除分层",
    // "operation.build.randomization.factor.edit": "设置分层",
    // "operation.build.randomization.factor.page.view": "查看分层因素",
    // "operation.build.randomization.factor.set-toplimit": "设置分层预警、上限人数",
    // "operation.build.randomization.factor.site": "中心分层",
    // "operation.build.randomization.factor.view": "查看分层",
    "operation.build.randomization.factor.add": "添加",
    "operation.build.randomization.factor.view": "查看",
    "operation.build.randomization.factor.delete": "删除",
    "operation.build.randomization.factor.edit": "编辑",
    "operation.build.randomization.factor.set-toplimit": "设置分层",
    //项目详情 项目构建 随机配置 随机设计 随机列表
    // "operation.build.randomization.list.view-summary": "查看随机列表概览",
    // "operation.build.randomization.list.upload": "上传随机表",
    // "operation.build.randomization.list.generate": "生成随机表",
    // "operation.build.randomization.list.active": "激活/禁用随机列表",
    // "operation.build.randomization.list.export": "导出随机列表",
    // "operation.build.randomization.list.invalid": "随机表作废",
    "operation.build.randomization.list.view-summary": "查看",
    "operation.build.randomization.list.upload": "上传",
    "operation.build.randomization.list.generate": "生成",
    "operation.build.randomization.list.sync": "同步",
    "operation.build.randomization.list.active": "激活/禁用随机列表",
    // "operation.build.randomization.list.export": "导出",
    "operation.build.randomization.list.invalid": "作废",
    "operation.build.randomization.list.edit": "编辑",
    "operation.build.randomization.list.history": "查看",
    "operation.build.randomization.list.print": "打印",
    //项目详情 项目构建 随机配置 随机设计 随机列表属性
    "operation.build.randomization.list.attribute": "查看",
    //项目详情 项目构建 随机配置 随机设计 随机号区组
    // "operation.build.randomization.list.segmentation": "随机分段",
    "operation.build.randomization.list.segmentation.view": "查看",
    "operation.build.randomization.list.segmentation.clear": "清空其他分层",
    "operation.build.randomization.list.segmentation.site": "区组分配给中心",
    "operation.build.randomization.list.segmentation.region": "区组分配给区域",
    "operation.build.randomization.list.segmentation.country": "区组分配给国家",
    "operation.build.randomization.list.segmentation.factor": "区组分配给分层",
    "operation.build.randomization.list.segmentation.activate": "激活",
    "operation.build.randomization.list.segmentation.deactivate": "失活",
    //项目详情 项目构建 随机配置 随机设计 分层因素2
    "operation.build.randomization.factor-in.view": "查看",
    "operation.build.randomization.factor-in.add": "添加",
    "operation.build.randomization.factor-in.set-people": "编辑",
    "operation.build.randomization.factor-in.delete": "删除",
    //项目详情 项目构建 随机配置 表单配置
    // "operation.build.randomization.form.add": "新增表单配置",
    // "operation.build.randomization.form.delete": "删除表单配置",
    // "operation.build.randomization.form.edit": "编辑表单配置",
    // "operation.build.randomization.form.list": "查看表单配置",
    "operation.build.randomization.form.add": "新增",
    "operation.build.randomization.form.delete": "删除",
    "operation.build.randomization.form.edit": "编辑",
    "operation.build.randomization.form.list": "查看",
    "operation.build.randomization.form.preview": "预览",
    //项目详情	项目构建	研究产品管理	访视管理
    // "operation.build.medicine.visit.add": "新增访视周期",
    // "operation.build.medicine.visit.delete": "删除访视周期",
    // "operation.build.medicine.visit.edit": "编辑访视周期",
    // "operation.build.medicine.visit.list": "查看访视周期",
    "operation.build.medicine.visit.update": "修改",
    "operation.build.medicine.visit.drag": "排序",
    "operation.build.medicine.visit.copy": "复制新增",
    "operation.build.medicine.visit.push.record": "发布记录",
    "operation.build.medicine.visit.push": "发布",
    "operation.build.medicine.visit.add": "新增",
    "operation.build.medicine.visit.delete": "删除",
    "operation.build.medicine.visit.edit": "编辑",
    "operation.build.medicine.visit.list": "查看",
    "operation.build.medicine.visit.setting.edit": "编辑",
    "operation.build.medicine.visit.setting.list": "查看",

    //项目详情	项目构建	研究产品管理	研究产品配置
    // "operation.build.medicine.configuration.add": "新增研究产品配置",
    // "operation.build.medicine.configuration.delete": "删除研究产品配置",
    // "operation.build.medicine.configuration.edit": "编辑研究产品配置",
    // "operation.build.medicine.configuration.list": "查看研究产品配置",
    "operation.build.medicine.configuration.add": "新增",
    "operation.build.medicine.configuration.delete": "删除",
    "operation.build.medicine.configuration.edit": "编辑",
    "operation.build.medicine.configuration.list": "查看",
    "operation.build.medicine.configuration.setting.add": "新增",
    "operation.build.medicine.configuration.setting.delete": "删除",
    "operation.build.medicine.configuration.setting.edit": "编辑",
    "operation.build.medicine.configuration.setting.list": "查看",
    //项目详情	项目构建	研究产品配置	研究产品列表
    // "operation.build.medicine.upload.list": "查看研究产品列表",
    // "operation.build.medicine.upload.upload": "上传研究产品列表",
    // "operation.build.medicine.upload.downtemplate": "研究产品列表模板下载",
    // "operation.build.medicine.upload.downdata": "研究产品列表数据下载",
    // "operation.build.medicine.packlist.upload": "上传包装清单",
    // "operation.build.medicine.packlist.downtemplate": "包装清单模板下载",
    // "operation.build.medicine.upload.delete": "研究产品列表批量删除",
    // "operation.build.medicine.upload.uploadHistory": "上传研究产品列表轨迹",
    "operation.build.medicine.upload.list": "查看",
    "operation.build.medicine.upload.upload": "上传研究产品",
    // "operation.build.medicine.upload.downdata": "数据下载",
    "operation.build.medicine.packlist.upload": "上传包装清单",
    "operation.build.medicine.upload.delete": "批量删除",
    "operation.build.medicine.upload.uploadHistory": "查看",
    "operation.build.medicine.upload.print": "打印",
    "operation.build.medicine.package.setting": "设置",
    "operation.build.medicine.batch.setting": "设置",
    "operation.build.medicine.examine": "审核",
    "operation.build.medicine.update": "修改",
    "operation.build.medicine.release": "放行",

    //项目详情	项目构建	研究产品配置	未编号研究产品
    // "operation.build.medicine.otherm.add": "新增未编号研究产品",
    // "operation.build.medicine.otherm.delete": "删除未编号研究产品",
    // "operation.build.medicine.otherm.edit": "编辑未编号研究产品",
    // "operation.build.medicine.otherm.list": "查看未编号研究产品",
    "operation.build.medicine.otherm.add": "新增",
    "operation.build.medicine.otherm.delete": "删除",
    "operation.build.medicine.otherm.edit": "编辑",
    "operation.build.medicine.otherm.list": "查看",
    //项目详情	项目构建	研究产品配置	批次管理
    // "operation.build.medicine.batch.list": "查看批次管理",
    // "operation.build.medicine.batch.edit": "编辑批次管理",
    "operation.build.medicine.batch.list": "查看",
    "operation.build.medicine.batch.edit": "编辑",
    "operation.build.medicine.batch.update": "更新",
    //项目详情	项目构建	研究产品配置	条形码列表
    // "operation.build.medicine.barcode.add": "生成条形码",
    // "operation.build.medicine.barcode.scan": "扫码入仓",
    "operation.build.medicine.barcode.view": "查看",
    "operation.build.medicine.barcode.add": "生成条形码",
    "operation.build.medicine.barcode.scan": "扫码入仓",
    "operation.build.medicine.barcode.scanPackage": "包装扫码",
    "operation.build.medicine.barcode.export": "导出",
    // 项目详情	项目构建	研究产品配置	标签管理

    "operation.build.medicine.barcode_label.view": "查看",
    "operation.build.medicine.barcode_label.add": "新增",
    "operation.build.medicine.barcode_label.preview": "预览",
    "operation.build.medicine.barcode_label.send": "发送",

    //项目详情 项目构建 供应计划
    // "operation.build.supply-plan.add": "新增供应计划",
    // "operation.build.supply-plan.delete": "删除供应计划",
    // "operation.build.supply-plan.edit": "编辑供应计划",
    // "operation.build.supply-plan.view": "查看供应计划",
    "operation.build.supply-plan.add": "新增",
    "operation.build.supply-plan.delete": "删除",
    "operation.build.supply-plan.edit": "编辑",
    "operation.build.supply-plan.view": "查看",
    "operation.build.supply-plan.history": "轨迹",
    //项目详情 项目构建 供应计划 计划管理
    // "operation.build.supply-plan.medicine.add": "新增供应计划研究产品配置",
    // "operation.build.supply-plan.medicine.delete": "删除供应计划研究产品配置",
    // "operation.build.supply-plan.medicine.edit": "编辑供应计划研究产品配置",
    // "operation.build.supply-plan.medicine.view": "查看供应计划研究产品配置",
    "operation.build.supply-plan.medicine.add": "新增",
    "operation.build.supply-plan.medicine.delete": "删除",
    "operation.build.supply-plan.medicine.edit": "编辑",
    "operation.build.supply-plan.medicine.view": "查看",
    "operation.build.supply-plan.medicine.history": "轨迹",
    //项目详情 项目构建 项目日志
    // "operation.build.history.view": "查看项目日志",
    "operation.build.history.view": "查看",
    "operation.build.history.print": "打印",
    //项目详情 项目构建 推送统计
    "operation.build.push.view": "查看",
    "operation.build.push.all.send": "全部推送",
    "operation.build.push.batch.send": "批量发送",
    "operation.build.push.history": "历史数据推送",
    "operation.build.push.send": "重新发送",
    "operation.build.push.details": "详情",
    //项目详情 项目设置 通知设置
    // "operation.build.settings.notice": "通知设置",
    "operation.build.settings.notice.view": "查看",
    "operation.build.settings.notice.edit": "编辑",
    //项目详情 项目设置 人员管理
    // "operation.build.settings.user": "人员管理",
    "operation.build.settings.user.view": "查看",
    "operation.build.settings.user.add": "新增",
    //"operation.build.settings.user.edit": "操作管理",
    "operation.build.settings.user.role": "分配角色",
    "operation.build.settings.user.site": "分配中心",
    "operation.build.settings.user.depot": "分配仓库",
    "operation.build.settings.user.app": "APP帐号启用/禁用",
    "operation.build.settings.user.unbind": "解绑",
    "operation.build.settings.user.unbind.batch": "批量解绑",
    "operation.build.settings.users.invite-again": "再次邀请",
    "operation.build.settings.user.reauthorization": "再次授权",
    // "operation.build.settings.user.download": "下载",
    "operation.build.settings.user.history": "轨迹",
    "operation.build.settings.user.print": "打印",
    //项目详情 项目设置 项目配置
    // "operation.build.randomization.info.view":"查看配置报表",
    // "operation.build.randomization.info.export":"导出配置报表",
    "operation.build.randomization.info.view": "查看",
    // "operation.build.randomization.info.export": "导出",
    //项目详情 动态监控
    // "operation.projects.mail-error": "邮件异常",
    // "operation.check": "系统监测",
    "operation.monitor.view": "查看",
    "operation.monitor.edit": "管理",
    //报表
    "operation.report.userRoleStatus.download": "下载",
    "operation.build.settings.user.download": "下载",
    "operation.report.userRoleAssignHistory.download": "下载",
    "operation.build.randomization.info.export": "下载",
    "operation.report.auditTrailExport.download": "下载",
    "operation.report.auditTrailExport.build": "项目构建",
    "operation.report.auditTrailExport.settings": "项目设置",
    "operation.report.auditTrailExport.release-record": "隔离管理",
    "operation.report.auditTrailExport.order": "研究产品订单",
    "operation.report.auditTrailExport.drug_recovery": "研究产品回收",
    "operation.report.auditTrailExport.subject": "受试者",
    "operation.report.auditTrailExport.dispensing": "受试者发放",
    "operation.report.auditTrailExport.ip": "研究产品",
    "operation.report.auditTrailExport.userLoginHistory": "下载",
    "operation.report.auditTrailExport.userRoleAssignHistory": "下载",
    "operation.report.siteIPStatisticsExport.download": "下载",
    "operation.report.siteIPStatisticsExport.download.template": "自定义模板",
    "operation.report.randomizationStatisticsExport.download": "下载",
    "operation.report.subjectStatisticsExport.download": "下载",
    "operation.report.forecastingPrediction.download": "下载",
    "operation.report.visitForecast.download": "下载",
    "operation.report.depotIPStatisticsExport.download": "下载",
    "operation.report.depotIPStatisticsExport.download.template": "自定义模板",
    "operation.report.userLoginHistory.download": "下载",
    "operation.projects.main.setting.permission.export": "下载",
    "operation.subject.download-random": "下载",
    "operation.subject.download-random.template": "自定义模板",
    "operation.subject.medicine.export": "下载",
    "operation.subject.medicine.export.template": "自定义模板",
    "operation.subject.download": "下载",
    "operation.subject.download.template": "自定义模板",
    "operation.build.simulate-random.pdf.download": "下载",
    "operation.build.projectNotificationsConfigurationReport.download": "下载",
    // "operation.subject-dtp.download": "下载",
    // "operation.subject-dtp.download.template": "自定义模板",
    // "operation.subject-dtp.medicine.export": "下载",
    // "operation.subject-dtp.medicine.export.template": "自定义模板",
    // "operation.subject-dtp.download-random": "下载",
    // "operation.subject-dtp.download-random.template": "自定义模板",
    "operation.supply.storehouse.medicine.download": "下载",
    "operation.supply.storehouse.medicine.download.template": "自定义模板",
    "operation.build.randomization.list.export": "下载",
    "operation.build.medicine.upload.downdata": "下载",
    "operation.build.medicine.upload.downdata.template": "自定义模板",
    "operation.source.ip.upload.history.downdata": "下载",
    "operation.supply.site.medicine.download": "下载",
    "operation.supply.site.medicine.download.template": "自定义模板",
    "operation.build.simulate-random.download": "下载",
    "operation.supply.recovery.download": "下载",
    "operation.supply.recovery.download.template": "自定义模板",
    "operation.supply.shipment.download": "下载",
    "operation.supply.shipment.download.template": "自定义模板",
    "operation.report.visitForecast.download.template": "自定义模板",

    //多语言
    "operation.projects.project.multiLanguage.view": "查看",
    "operation.projects.project.multiLanguage.add": "添加",
    "operation.projects.project.multiLanguage.edit": "编辑",
    "operation.projects.project.multiLanguage.delete": "删除",
    "operation.projects.project.multiLanguage.trail": "轨迹",
    "operation.projects.project.multiLanguage.details.view": "查看",
    "operation.projects.project.multiLanguage.details.edit": "编辑",
    "operation.projects.project.multiLanguage.details.preview": "预览",
    "operation.projects.project.multiLanguage.details.downloadTemplate": "下载模板",
    "operation.projects.project.multiLanguage.details.batchExport": "导入",

    //---------------------------------权限部分----------------------------------------
    "operation.subject.replace.confirm": "确定替换",
    "operation.subject.replace.confirm.is": "受试者替换需符合方案规定，系统将按照提交信息进行替换，请确认下述信息。",
    "operation.subject.beReplace.info": "替换受试者信息",
    "projects.research.attribute": "研究属性",
    "projects.contact.information": "联系方式",
    "projects.research.site": "中心交付管理",
    "projects.research.dtp": "直接面向患者（DTP）",
    "projects.research.site.describe": "中心交付管理：研究产品发放由临床中心管理并交付给到受试者。",
    "projects.research.dtp.describe":
        "直接面向患者（DTP）：研究产品发放从仓库直接分发至受试者，不需要经过临床中心管理。",
    "projects.status.progress.describe": "进行中：项目已开始工作且正在执行；",
    "projects.status.finish.describe": "已完成：项目所有的入组/发放等执行工作已完成；",
    "projects.status.close.describe": "已关闭：项目全部工作完结，经项目管理审核后关闭；",
    "projects.status.pause.describe": "已暂停：项目由于异常而需要暂时停止。",
    "projects.status.terminate.describe": "已终止：项目由于异常而需要终止执行；",

    "projects.customer": "所属客户",
    "projects.attribute": "项目属性",
    "projects.brief": "项目简介",
    "placeholder.select.common": "请选择",
    "placeholder.select.common.email.language": "请选择邮件语言",
    "placeholder.select.common.stage": "请选择上一阶段",
    "placeholder.select.search": "请选择或输入搜索",
    "placeholder.input.common": "请输入",
    "placeholder.input.order": "请输入订单号",
    "placeholder.input.contact": "请输入，多个号码可用英文逗号隔开",
    "placeholder.input.email": "请输入邮箱",
    "placeholder.input.name": "请输入姓名",
    "input.error.common": "输入错误，请重新输入",
    "model.note.title.common": "请注意！",
    "model.note.content.research": "选择并保存“直接面向患者（DTP）”后，将无法再修改，请慎重配置。",
    "model.note.title.edc": "确定开启“对接EDC”吗？",
    "model.note.start.success": "开启成功",
    "model.note.content.edc": "开启后，无法再更改。",
    "model.note.title.medicine.use": "是否确认设为可用？",
    "model.note.title.medicine.lose": "是否确认丢失/作废？",
    "model.note.title.order.dtp.receive": "是否确认接收？",
    "model.note.content.order.dtp.receive1": "当前订单未运送，",
    "model.note.content.order.dtp.receive2": "接收后将无法再操作运送状态。",
    "button.update": "修改",
    "message.save.success": "保存成功",
    "tool.tip.timezone": "适用于：项目中心在一个时区内，应用于与外部对接时的时间计算及用户系统时间的计算。",
    "tool.tip.edc": "开启后，EDC可触发同步研究中的操作数据，开启后不可关闭。",
    "tool.tip.elearning": "开启后，可配置项目维度对用户学习eLearning课程的要求。",
    "tool.tip.elearning.yes.tp": "是：要求用户必须完成学习课程后，才能进入项目工作；",
    "tool.tip.elearning.no.tp": "否：可点击忽略学习流程，进入项目工作。",
    "tool.tip.elearning.config": "可配置项目维度对用户学习eLearning课程的要求。",
    "tool.tip.elearning.config.yes": "是：要求用户必须完成学习课程后，才能进入系统/项目工作。",
    "tool.tip.elearning.config.no": "否：可点击忽略学习流程，进入系统/项目工作。",
    "suppy.drug.order.visitNumber": "访视号",
    "input.error.only.number.letter": "输入错误，请输入1-10位的数字/字母。",
    "project.setting.divider.approval.control": "研究中心订单申请",
    "project.setting.divider.approval.control.tip": "设置盲态角色需要申请中心订单的研究产品名称，或需按供应比例申请订单的比例。",
    "project.setting.switch.approval.control": "审批控制",
    "project.setting.switch.approval.control.tip": "开启后，须在研究产品管理中配置需进行订单(或按供应比例)申请的研究产品。",
    "project.setting.approval.method": "审批方式",
    "tool.tip.approval.control": "开启后，盲态项目中盲态角色申请中心订单时，需经过审批通过后才能触发订单创建。",
    "project.setting.divider.unblind.control": "揭盲控制",
    "project.setting.switch.unblind.control": "揭盲控制",
    "project.setting.unblind.error1": "请选择揭盲类型",
    "project.setting.unblind.error2": "请选择紧急揭盲的控制方式",
    "project.setting.unblind.error3": "请选择PV揭盲的控制方式",
    "project.de.isolation.approval": "解隔离审批",
    "project.de.isolation.approval.tip": "开启后，研究产品解隔离环节，需先审批确认后才可完成。",
    "project.reason.for.de.isolation": "解隔离原因",
    "project.setting.unblind.method": "控制方式",
    "project.setting.checkbox.approval": "审批确认",
    "project.setting.checkbox.unblinded-code": "揭盲码",
    "tool.tip.unblind.control": "开启后，揭盲操作需经过审批通过或揭盲码确认正确后才能允许继续揭盲。",
    "project.setting.unblind.confirm": "确认方式",
    "project.setting.checkbox.unblind.sms": "短信",
    "project.setting.checkbox.unblind.process": "流程操作",
    "project.setting.msg.unblind.sms": "审批角色的用户，需配置有效手机号（+86）,短信审批仅支持中国大陆运营商。",
    "validator.msg.required": "必填",
    "validator.msg.number": "数字范围为0-999之间的整数，请重新输入。",
    "modal.title.user.role": "角色维护",
    "form.label.unblinding.code.available": "可用",
    "form.label.unblinding.code.used": "已用",
    "form.label.unblinding.code.indivual": "个",
    "tag.copy": "复制",
    "subject.urgentUnblindingApproval.reason.other": "其他",
    "subject.urgentUnblindingApproval.reason.sae": "SAE",
    "subject.urgentUnblindingApproval.reason.pregnancy": "妊娠",
    "subject.urgentUnblindingApproval.reason.policy": "政策要求",
    "subject.urgentUnblindingApproval.pending": "提交申请",
    "subject.urgentUnblindingApproval.agree": "已通过",
    "subject.urgentUnblindingApproval.reject": "已拒绝",
    "subject.unblinding.reason.remark": "备注",
    "subject.unblinding.application": "揭盲申请",
    "subject.unblinding.approval": "揭盲审批",
    "subject.unblinding.application.alert": "如实际发放的研究产品与系统随机发放不一致，请及时登记",
    "subject.unblinding.application.type": "申请方式",
    "subject.unblinding.unblinded.code.confirm": "揭盲码确认",
    "subject.unblinding.application.result.title": "揭盲（紧急）申请",
    "subject.unblinding.application.result.title.pv": "揭盲（pv）申请",
    "subject.unblinding.application.result.title.ip": "揭盲（研究产品）申请",
    "subject.unblinding.approval.result.title": "揭盲（紧急）审批",
    "subject.unblinding.application.success": "申请成功",
    "subject.unblinding.approval.success": "审批完成",
    "subject.unblinding.application.info": "揭盲(紧急)申请已提交成功，请等待审批人审批确认。",
    "subject.unblinding.application.info.pv": "揭盲(pv)申请已提交成功，请等待审批人审批确认。",
    "subject.unblinding.application.info.ip": "揭盲(研究产品)申请已提交成功，请等待审批人审批确认。",
    "subject.unblinding.application.alert.ip": "研究产品揭盲后，将标记为已揭盲，并可能从试验中剔除，请谨慎操作。",
    "subject.unblinding.success": "揭盲成功",
    "subject.unblinding.approval.number": "审批编号",
    "subject.unblinding.approval.reason": "原因",
    "subject.unblinding.approval.agree": "通过",
    "subject.unblinding.approval.reject": "拒绝",
    "subject.unblinding.application.resend": "发送短信",
    "subject.edc.verification.factor": "分层因素",
    "subject.edc.verification.source": "数据来源",
    "subject.edc.verification.return": "EDC返回",
    "subject.edc.verification.enter": "IRT录入",
    "subject.edc.return.modification": "返回修改",
    "subject.edc.continue.submitting": "继续随机",
    "subject.edc.continue.dispense": "继续发放",
    "subject.edc.continue.replace": "继续替换",
    "subject.edc.continue.register": "继续登记",
    "subject.edc.continue.update": "继续修改",
    "subject.edc.continue.screen": "继续筛选",
    "subject.edc.confirm.lamination1": "EDC返回分层/表单信息为空，需确认分层/表单信息录入是否正确。",
    "subject.edc.confirm.lamination2": "EDC返回分层信息为空，需确认分层信息录入是否正确。",
    "subject.edc.confirm.lamination3": "EDC返回表单信息为空，需确认表单信息录入是否正确。",
    "subject.edc.interface.error": "EDC接口响应异常，无法校验EDC中受试者状态",
    "subject.edc.interface.site.error": "EDC接口响应异常,无法校验EDC中心",
    "subject.edc.inconsistent.information": "IRT录入分层信息与EDC系统返回不一致，请确认分层信息是否正确。",
    "subject.edc.inconsistent.information1": "IRT录入{label}信息与EDC系统返回不一致，请确认{label}信息是否正确。",
    "subject.edc.random.failure.filter.failed": "系统校验受试者状态为筛选失败，不允许继续随机。",
    "subject.edc.random.failure.screen.failed": "系统校验受试者状态为筛选中，不允许继续随机。",
    "subject.edc.random.failure.exit": "系统校验受试者状态为已退出，不允许继续随机。",
    "subject.edc.random.failure.complete.the.study": "系统校验受试者状态为完成研究，不允许继续随机。",
    "subject.edc.dispensing.failure.filter.failed": "系统校验受试者状态为筛选失败，不允许继续发药。",
    "subject.edc.dispensing.failure.exit": "系统校验受试者状态为已退出，不允许继续发药。",
    "subject.edc.dispensing.failure.complete.the.study": "系统校验受试者状态为完成研究，不允许继续发药。",
    "subject.edc.replace.inconsistent.information": "IRT录入替换受试者号分层信息，与EDC系统返回不一致，需确认分层信息录入是否正确。",
    "subject.edc.replace.register.information.stratification": "分层",
    "subject.edc.replace.register.information.form": "表单",
    "subject.edc.replace.register.information.screen": "筛选",
    "subject.edc.replace.register.information.subject.no": "受试者号",
    "subject.edc.replace.register.information.cohort": "群组",
    "subject.edc.replace.register.information.stage": "阶段",
    "subject.edc.replace.register.information.a1": "IRT录入",
    "subject.edc.replace.register.information.a2": "信息，与EDC系统返回不一致，需确认",
    "subject.edc.replace.register.information.a3": "信息录入是否正确。",
    "subject.edc.replace.register.information.b": "信息，与EDC系统返回一致。",

    "subject.edc.subject": "EDC返回受试者状态为",
    "subject.edc.subject.exited": "已退出",
    "subject.edc.subject.screen.no.random": "筛选中-不可随机",
    "subject.edc.subject.continue.update": "，请确认。",
    "subject.edc.push.centre": "EDC系统该受试者数据处理中，如继续修改，EDC系统将稍后完成数据覆盖操作。",
    "subject.edc.register.push.centre": "EDC系统数据处理中，如继续修改，EDC系统将稍后完成数据写入操作。",
    "subject.edc.no.subject": "EDC系统中无该受试者",
    "subject.edc.no.subject.push.centre": "，如继续修改，将稍后在EDC系统中自动创建该受试者。",

    "subject.edc.replace.register.information1": "IRT录入分层/表单信息，与EDC系统返回不一致，需确认分层/表单信息录入是否正确。",
    "subject.edc.replace.register.information2": "IRT录入分层信息，与EDC系统返回不一致，需确认分层信息录入是否正确。",
    "subject.edc.replace.register.information3": "IRT录入表单信息，与EDC系统返回不一致，需确认表单信息录入是否正确。",
    "subject.edc.register.no.subject": "，如继续登记，将在EDC系统中自动创建该受试者。",
    "subject.edc.update.no.subject": "，如继续修改，将在EDC系统中自动创建该受试者。",
    "subject.edc.screen.no.subject": "，如继续筛选，将在EDC系统中自动创建该受试者。",
    "subject.edc.update.handle.subject": "EDC系统数据处理中，如继续修改，EDC系统将稍后完成数据写入操作。",
    "subject.edc.update.cover.subject": "EDC系统该受试者数据处理中，如继续修改，EDC系统将稍后完成数据覆盖操作。",
    "subject.edc.cover.tip": "操作成功，EDC系统将在IRT系统数据推送后，自动完成数据覆盖",
    "subject.edc.no.mapping": "EDC未配置映射关系，IRT将不做数据推送。",
    "subject.edc.complete.registration": "EDC系统中已有受试者：{subjectNo}，与IRT系统核对一致。如继续登记，将在IRT系统中完成受试者登记。",
    "subject.edc.complete.cohort.registration": "EDC系统中已有受试者：{subjectNo}，群组：{cohortName}，与IRT系统核对不一致。如继续登记，将在IRT系统中完成受试者登记。",
    "subject.edc.complete.cohort.update": "EDC系统中已有受试者：{subjectNo}，群组：{cohortName}，与IRT系统核对不一致。如继续修改，将在IRT系统中完成受试者修改。",
    "subject.edc.complete.cohort.random": "EDC系统中已有受试者：{subjectNo}，群组：{cohortName}，与IRT系统核对不一致。如继续随机，将在IRT系统中完成受试者随机。",
    "subject.edc.complete.cohort.screen": "EDC系统中已有受试者：{subjectNo}，群组：{cohortName}，与IRT系统核对不一致。如继续筛选，将在IRT系统中完成受试者筛选。",
    "subject.edc.create.subject": "EDC未创建受试者，返回信息为空。",
    "subject.edc.replace.error": "替换失败",
    "subject.edc.replace.failure.filter.failed": "系统校验受试者状态为筛选失败，不允许继续替换。",
    "subject.edc.replace.failure.exit": "系统校验受试者状态为已退出，不允许继续替换。",
    "subject.edc.replace.failure.not-registered": "系统校验受试者状态为未登记，不允许继续替换。",
    "subject.edc.replace.failure.complete.the.study": "系统校验受试者状态为完成研究，不允许继续替换。",
    "subject.edc.replace.failure.screen.no.random": "系统校验受试者状态为筛选中，不允许继续替换。",
    "subject.edc.site.empty": "EDC的中心信息获取失败，请人工判断当前受试者在IRT和EDC所在中心是否一致。",
    "subject.edc.site.inconsistent": "EDC返回受试者所在中心不一致，请确认所在IRT中心是否正确。",
    "tip.site.disable.title": "确定无效吗？",
    "tip.site.disable": "无效后，中心绑定用户将自动失活。",
    "tip.user.disable": "删除后，用户关联的中心/库房等数据自动解除绑定。",
    "tip.user.unbind": "解绑后，用户无法再进入项目。",
    "tip.user.close": "关闭后，用户将无法进入客户下所有的项目。",
    "tip.sync.title": "确定同步吗？",
    "tip.copy.title": "是否确定复制？",
    "tip.copy.title2": "确定复制吗？",
    "tip.sync.content": "同步后，将自动适配最新的分层因素使用随机表。",
    "toast.copy.success": "复制成功",

    "env.user.edit.section1": "是否确认分配非盲角色等",
    "env.user.edit.section2": "当前项目为盲态项目",
    "env.user.edit.section3": "确定分配非盲角色吗？",
    "env.user.edit.section4": "非盲角色（",
    "env.user.edit.section5": "）。",

    "order.logistics.sf": "顺丰",
    "order.logistics.ems": "EMS",
    "order.logistics.jd": "京东",
    "order.logistics.yt": "圆通",
    "order.logistics.yd": "韵达",
    "order.logistics.zt": "中通",
    "order.logistics.st": "申通",
    "order.logistics.jt": "极兔",
    "order.logistics.other": "其他",

    "logistics.confirm.deliver": "确认运送",
    "logistics.confirm.info": "物流供应商",
    "logistics.info.isTransit": "是否确认运送",
    "logistics.info.isTransit.ok": "确定后，订单状态将更新为已运送。",
    "logistics.info.name": "物流名称",
    "logistics.other.vendor": "其他物流",
    "logistics.number": "物流单号",
    "logistics.info": "物流",
    "logistics.dispensing.dtpIp": "DTP研究产品",
    "logistics.dispensing.method": "发放方式",
    "logistics.send.site": "中心（中心库存）",
    "logistics.send.site.info": "研究产品从中心发放，受试者去中心拿取。",
    "logistics.send.site.subject": "中心（直接寄送受试者）",
    "logistics.send.site.subject.info": "中心将研究产品采用快递的方式，直接寄送至受试者。",
    "logistics.send.depot.subject": "库房（直接寄送受试者）",
    "logistics.send.depot.subject.info": "库房将研究产品采用快递的方式，直接寄送至受试者。",

    "project.statistics.mode": "数据推送方式",
    "project.statistics.select.mode": "请选择数据推送方式",
    "project.statistics.real": "EDC实时请求",
    "project.statistics.active": "IRT全量推送",
    "project.statistics.mode.real.tp": "EDC实时请求：随机/发放请求由EDC发起，实时请求IRT完成后，数据进行传输；",
    "project.statistics.mode.active.tp": "IRT全量推送：EDC与IRT均可登记受试者，在IRT完成随机/发放操作后，IRT实时推送数据给EDC，IRT可追溯推送过程日志。",
    "project.statistics.url": "请输入EDC的URL地址",
    "project.statistics.projectNo": "请输入项目编号",
    "project.statistics.folder.oid": "文件夹OID",
    "project.statistics.form.oid": "表单OID",
    "project.statistics.field.oid": "字段OID",
    "project.statistics.dispensing.mapping": "EDC发放数据映射",
    "project.statistics.randomize.mapping": "EDC随机数据映射",
    "project.statistics.randomize.number.oid": "随机号OID",
    "project.statistics.randomize.group.oid": "随机组别OID",
    "project.statistics.randomize.time.oid": "随机时间OID",
    "project.statistics.dispensing.number.oid": "发放研究产品OID",
    "project.statistics.dispensing.time.oid": "发放时间OID",
    "project.statistics.mapping": "EDC映射",
    "project.statistics.all": "全部推送",
    "project.statistics.pushing": "推送中",
    "project.statistics.succeeded": "成功",
    "project.statistics.failed": "推送失败",
    "project.statistics.lose": "无效",
    "project.received.failed": "入库失败",
    "project.edc.failed": "失败",
    "project.edc.processing": "处理中",
    "project.edc.return": "EDC返回",
    "project.statistics.enter.number": "请输入{label}",
    "project.statistics.batch.send": "批量发送",
    "project.statistics.push.history": "历史数据推送",
    "project.statistics.push.history.empty": "推送失败，系统未检测到历史数据。",
    "project.statistics.push.history.confirm": "确定进行历史数据推送吗？",
    "project.statistics.push.history.scenario": "历史数据场景包含：",
    "project.statistics.select.data": "请先勾选要发送的数据",
    "project.statistics.subject.number": "受试者号",
    "project.statistics.push.item": "推送内容",
    "project.statistics.randomize": "随机",
    "project.statistics.dispense": "发放",
    "project.statistics.push.mode": "推送方式",
    "project.statistics.manual.push": "手动推送",
    "project.statistics.system.auto": "系统自动",
    "project.statistics.push.time": "推送时间",
    "project.statistics.retry.times": "重试次数",
    "project.statistics.ls.resend": "确定重新发送吗？",
    "project.statistics.operation.succeed": "确定后，系统将在30s内推送至EDC。",
    "project.statistics.resend": "重新发送",
    "project.statistics.details": "详情",
    "project.statistics.randomization.number": "随机号",
    "project.statistics.group": "组别",
    "project.statistics.randomization.time": "随机时间",
    "project.statistics.return.edc": "EDC返回",
    "project.statistics.visit.number": "访视号",
    "project.statistics.dispensing.time": "发放时间",
    "project.statistics.registered": "登记",
    "project.statistics.update": "修改",
    "project.statistics.out-visit-dispensing": "计划外发放",
    "project.statistics.replace": "研究产品替换",
    "project.statistics.reissue": "补发",
    "project.statistics.cancel": "研究产品撤销",
    "project.statistics.retrieval": "研究产品取回",
    "project.statistics.realDispensing": "实际用药",
    "project.statistics.unknown": "未知",
    "project.statistics.screen": "筛选",

    "supply.plan.applicable.site": "适用中心",
    "supply.plan.all.site": "全部中心",
    "supply.plan.status.invalid.title": "确定无效化该供应计划吗？",
    "supply.plan.status.invalid.content": "中心已关联供应计划，无效后，中心将无对应供应计划。",
    "supply.plan.status.applicable.site.title": "是否确认修改？",
    "supply.plan.status.applicable.site.content": "系统检测到部分中心无绑定的供应计划。",
    "supply.plan.current": "当前供应计划",
    "storehouse.alert": "警戒值",
    "permission.scope.study": "Study:项目/试验维度的数据权限角色",
    "permission.scope.depot": "Depot:库房维度的数据权限角色",
    "permission.scope.site": "Site:研究中心维度 的数据权限角色",
    "project.role.invalid.title": "是否确认无效角色？",
    "project.role.invalid.content": "无效后，将自动取消已分配用户的角色使用。",
    "project.user.status.Valid": "有效：项目授权成功，且用户已激活成功；",
    "project.user.status.Inactivated": "已开启：项目授权成功，但用户账号未激活；",
    "project.user.status.Invalid": "无效：用户已解绑项目。",
    "customer.user.status.Enabled": "已开启：系统已添加用户并成功发送账号激活邮件；",
    "customer.user.status.Activated": "已激活：用户账号已激活成功；",
    "customer.user.status.Closed": "已关闭：用户账号已关闭。关闭后，用户无法进入客户下所有项目。",
    "random.factor.alert": "分层因素已更新，请及时同步随机表。",
    "subject.list.alert": "草稿态不允许登记，需修改群组状态至“入组”。",
    "subject.list.alert.modify": "立即修改",
    "cohort.status": "群组状态",

    "report": "报表",
    "report.role": "对象角色",
    "report.projectEnv": "项目/环境",
    "report.latestDownloadTime": "最近下载时间",
    "report.customTemplate": "自定义模板",
    "report.history": "历史记录",
    "report.template": "模板",
    "report.template.name": "模板名称",
    "report.template.name.required": "请输入模板名称",
    "report.template.default": "引用默认模板",
    "report.template.name.default": "默认模板",
    "report.attributes": "字段库",
    "report.attributes.unit": "项",
    "report.custom": "自定义",
    "report.filename": "文件名称",
    "report.filesize": "大小",
    "report.exportTime": "导出时间",
    "report.filename.search.required": "请输入文件名称",
    "report.storehouse": "库房",

    "report.randomizationStatisticsExport": "随机统计报表",
    "report.subjectStatisticsExport": "受试者统计报表",
    "report.siteIPStatisticsExport": "中心单品统计报表",
    "report.depotIPStatisticsExport": "库房单品统计报表",
    "report.userLoginHistory": "用户登录历史记录",
    "report.userRoleAssignHistory": "用户角色分配记录",
    "report.userRoleStatus": "用户角色状态报表",
    "report.auditTrailExport": "稽查轨迹",
    "report.RandomizationSimulationPDFExport": "模拟随机报告",
    "report.forecastingPrediction": "研究产品预测报表",
    "report.visitForecast": "访视统计报表",

    "report.projectPermissionConfigurationExport": "项目权限配置报表",
    "report.ProjectNotificationsConfigurationReport": "项目通知配置报表",
    "report.configureReport": "配置报告",
    "report.sourceIPExport": "研究产品盲底",
    "report.sourceRandomizationListExport": "随机盲底",
    "report.randomizationSimulationReport": "模拟随机报告",
    "report.randomizeReport": "受试者明细报表",
    "report.dispenseReport": "受试者发放报表",
    "report.unblindingReport": "揭盲报表",
    "report.roomNumberViewHistory": "房间号查看记录",
    "report.depotItemReport": "库房单品报表",
    "report.siteItemReport": "中心药房单品报表",
    "report.shipmentOrdersReport": "研究产品订单报表",
    "report.returnOrdersReport": "回收订单报表",
    "report.sourceIPUploadHistoryExport": "研究产品盲底上传记录",
    "report.randomizationSimulationResult": "模拟随机结果",
    "report.site.warning": "未选择中心",
    "report.role.warning": "未选择角色",
    "report.subject.warning": "未选择受试者",
    "report.random.list.warning": "未选择随机列表",
    "report.stage.warning": "请选择再随机",
    "report.cohort.warning": "请选择群组",
    "report.audit.trail.type.warning": "未选择稽查轨迹类型",
    "report.audit.trail.type.secret.warning": "未填写密码",
    "report.template.all": "全部模板",
    "report.template.delete.confirm": "确定删除吗?",
    "report.template.create.success": "创建成功",
    "report.template.edit.success": "编辑成功",

    "report.attributes.project": "项目",
    "report.attributes.project.number": "项目编号",
    "report.attributes.project.name": "项目名称",
    "report.attributes.info": "基本信息",
    "report.attributes.info.user.name": "姓名",
    "report.attributes.info.user.email": "邮箱",
    "report.attributes.info.user.role": "角色",
    "report.attributes.info.project.name": "项目",
    "report.attributes.info.country": "国家（分层属性）",
    "report.attributes.info.region": "区域（分层属性）",
    "report.attributes.info.site.country": "国家",
    "report.attributes.info.site.region": "区域",
    "report.attributes.info.site.number": "中心编号",
    "report.attributes.info.site.name": "中心名称",
    "report.attributes.info.subject.number": "受试者号",
    "report.attributes.info.status": "状态",
    "report.attributes.info.storehouse.name": "库房",
    "report.attributes.random": "受试者随机",
    "report.attributes.random.factor": "分层因素",
    "report.attributes.random.factor.calc": "录入分层字段（页面）",
    "report.attributes.random.actual.factor": "实际分层",
    "report.attributes.random.g": "G值",
    "report.attributes.random.time": "随机/入组时间",
    "report.attributes.random.group": "随机组别",
    "report.attributes.random.sub.group": "随机子组别",
    "report.attributes.random.subject.number.replace": "替换受试者号",
    "report.attributes.random.number": "随机号",
    "report.attributes.random.register.time": "登记时间",
    "report.attributes.random.register.operator": "登记操作人",
    "report.attributes.random.operator": "随机操作人",
    "report.attributes.random.form": "表单配置",
    "report.attributes.random.subject.replace.status": "替换受试者状态",
    "report.attributes.random.cohort": "群组",
    "report.attributes.random.stage": "阶段",
    "report.attributes.random.subject.replace.time": "替换时间",
    "report.attributes.random.subject.replace.number": "替换随机号",
    "report.attributes.random.config.code": "组别代码",
    "report.attributes.random.sign.out.operator": "停用操作人",
    "report.attributes.random.sign.out.time": "停用操作时间",
    "report.attributes.random.sign.out.real.time": "实际停用日期",
    "report.attributes.random.sign.out.reason": "停用原因",
    "report.attributes.random.screen.time": "筛选日期",
    "report.attributes.random.icf.time": "ICF签署日期",
    "report.attributes.random.finish.remark": "完成研究-备注",
    "report.attributes.random.plan.time": "计划随机/入组时间",
    "report.attributes.random.sequence.number": "随机顺序号",
    "report.attributes.dispensing": "受试者发放",
    "report.attributes.dispensing.room": "房间号",
    "report.attributes.dispensing.cycle.name": "访视名称",
    "report.attributes.dispensing.type": "发放操作类型",
    "report.attributes.dispensing.planTime": "计划访视",
    "report.attributes.dispensing.time": "操作时间",
    "report.attributes.dispensing.dose": "发放水平",
    "report.attributes.dispensing.outsize": "是否超窗",
    "report.attributes.dispensing.doseFormulas": "剂量调整表单",
    "report.attributes.dispensing.medicine": "研究产品编号",
    "report.attributes.dispensing.drug.name": "研究产品名称",
    "report.attributes.dispensing.label": "发放标签",
    "report.attributes.dispensing.medicine.replace": "被替换研究产品",
    "report.attributes.dispensing.medicine.real": "系统发放研究产品",
    "report.attributes.dispensing.medicine.real.group": "实际使用研究产品组别",
    "report.attributes.dispensing.drug.other.number": "未编号研究产品数量",
    "report.attributes.dispensing.useFormulas": "公式计算表单",
    "report.attributes.dispensing.operator": "发放操作人",
    "report.attributes.dispensing.remark": "发放-备注",
    "report.attributes.dispensing.out-visit-dispensing.reason": "计划外发放原因",
    "report.attributes.dispensing.reissue.reason": "补发原因",
    "report.attributes.dispensing.reissue.remark": "补发-备注",
    "report.attributes.dispensing.out-visit-dispensing.remark": "计划外发放-备注",
    "report.attributes.dispensing.replace.remark": "研究产品替换-备注",
    "report.attributes.dispensing.retrieval.remark": "取回-备注",
    "report.attributes.dispensing.register.remark": "登记实际使用研究产品-备注",
    "report.attributes.dispensing.invalid.remark": "不参加访视-备注",
    "report.attributes.dispensing.send.type": "发放方式",
    "report.attributes.dispensing.logistics.info": "物流",
    "report.attributes.dispensing.logistics.remark": "物流-备注",
    "report.attributes.dispensing.medicine.real.number": "实际使用研究产品编号",
    "report.attributes.unblinding": "受试者揭盲",
    "report.attributes.unblinding.sponsor": "是否已经通知申办方",
    "report.attributes.unblinding.mark": "揭盲-备注",
    "report.attributes.unblinding.reason": "揭盲原因",
    "report.attributes.unblinding.reason.mark": "揭盲原因-备注",
    "report.attributes.unblinding.operator": "揭盲操作人",
    "report.attributes.unblinding.time": "揭盲操作时间",
    "report.attributes.research": "研究产品",
    "report.attributes.research.medicine.serial-number": "序列号",
    "report.attributes.research.medicine.number": "研究产品编号",
    "report.attributes.research.medicine.code": "可识别短码",
    "report.attributes.research.medicine.type": "操作类型",
    "report.attributes.research.medicine.name": "研究产品名称",
    "report.attributes.research.batch": "批次号",
    "report.attributes.research.spec": "规格",
    "report.attributes.research.expireDate": "有效期",
    "report.attributes.research.packageNumber": "包装号",
    "report.attributes.research.package.serialNumber": "包装序列号",
    "report.attributes.research.packageMethod": "运送方式",
    "report.attributes.research.place": "位置",
    "report.attributes.research.order.number": "订单号",
    "report.attributes.research.status": "状态",
    "report.attributes.research.reason": "原因",
    "report.attributes.research.operator": "操作人",
    "report.attributes.research.time": "操作时间",
    "report.attributes.research.freeze.reason": "隔离原因",
    "report.attributes.research.freeze.operator": "隔离操作人",
    "report.attributes.research.freeze.time": "隔离操作时间",
    "report.attributes.research.release.reason": "解隔离原因",
    "report.attributes.research.release.operator": "解隔离操作人",
    "report.attributes.research.release.time": "解隔离操作时间",
    "report.attributes.research.lost.reason": "丢失/作废原因",
    "report.attributes.research.lost.operator": "丢失/作废操作人",
    "report.attributes.research.lost.time": "丢失/作废操作时间",
    "report.attributes.research.use.reason": "设为可用原因",
    "report.attributes.research.use.operator": "设为可用操作人",
    "report.attributes.research.use.time": "设为可用操作时间",
    "report.attributes.research.other": "数量",
    "report.attributes.order": "研究产品订单",
    "report.attributes.order.detail": "研究产品订单详情",
    "report.attributes.order.number": "订单号",
    "report.attributes.order.status": "状态",
    "report.attributes.order.send": "起运地",
    "report.attributes.order.receive": "目的地",
    "report.attributes.order.medicineQuantity": "研究产品数量",
    "report.attributes.order.create.by": "创建人",
    "report.attributes.order.create.time": "创建时间",
    "report.attributes.order.cancel.by": "取消人",
    "report.attributes.order.cancel.time": "取消时间",
    "report.attributes.order.cancel.reason": "取消原因",
    "report.attributes.order.confirm.by": "确认人",
    "report.attributes.order.confirm.time": "确认时间",
    "report.attributes.order.close.by": "关闭人",
    "report.attributes.order.close.time": "关闭时间",
    "report.attributes.order.close.reason": "关闭原因",
    "report.attributes.order.send.by": "运送人",
    "report.attributes.order.send.time": "运送时间",
    "report.attributes.order.receive.by": "接收人",
    "report.attributes.order.receive.time": "接收时间",
    "report.attributes.order.lost.by": "丢失人",
    "report.attributes.order.lost.time": "丢失时间",
    "report.attributes.order.lost.reason": "丢失原因",
    "report.attributes.order.end.by": "终止人",
    "report.attributes.order.end.time": "终止时间",
    "report.attributes.order.end.reason": "终止原因",
    "report.attributes.order.supplier": "物流供应商",
    "report.attributes.order.supplier.other": "其他物流",
    "report.attributes.order.supplier.number": "物流单号",
    "report.attributes.order.expectedArrivalTime": "期望送达时间",
    "report.attributes.order.actualReceiptTime": "实际接收时间",

    "report.user.role.assign.name": "姓名",
    "report.user.role.assign.email": "邮箱",
    "report.user.role.assign.operType": "操作",
    "report.user.role.assign.content": "操作内容",
    "report.user.role.assign.oper": "操作人",
    "report.user.role.assign.operTime": "操作时间",

    "report.ip.statistics.status.available": "可用",
    "report.ip.statistics.status.toBeConfirmed": "待确认",
    "report.ip.statistics.status.delivered": "已确认",
    "report.ip.statistics.status.sending": "已运送",
    "report.ip.statistics.status.quarantine": "已隔离",
    "report.ip.statistics.status.used": "已使用",
    "report.ip.statistics.status.lose": "丢失/作废",
    "report.ip.statistics.status.expired": "已过期",
    "report.ip.statistics.status.frozen": "冻结",
    "report.ip.statistics.status.locked": "锁定",
    "report.user.login.ip": "IP",
    "report.user.login.time": "操作时间",
    "report.user.login.success": "登录是否成功",
    "report.simulate.random.name": "名称",
    "report.simulate.random.block": "区组",
    "report.simulate.random.group": "组别",
    "report.random.statistics.month": "每月/累计",
    "report.random.statistics.week": "每周/累计",
    "report.subject.unblinding.urgent": "已揭盲(紧急)",
    "report.subject.unblinding.pv": "已揭盲(PV)",
    "report.forecast.period": "计划发放窗口期",
    "report.forecast.depot": "库房名称",
    "simulated.random.list.runCount": "运行次数",




    "report.download.success": "下载成功，历史记录中支持再次下载。",
    "project_dynamics": "项目动态",
    "project.number.name": "请输入编号或名称",
    "project.focus.on": "仅显示关注项目",
    "project.focus.on.cancel": "取消关注",
    "project.focus.on.cancel.success": "取消关注成功",
    "project.focus.on.ok": "关注",
    "project.focus.on.ok.success": "关注成功",
    "no.permission": "当前账号/角色暂无权限跳转",
    "random.statistics.total": "总随机",
    "random.statistics.country": "按国家",
    "random.statistics.region": "按区域",
    "random.statistics.site": "按中心",
    "random.statistics.factor": "按分层因素",
    "random.statistics.month": "每月",
    "random.statistics.week": "每周",
    "random.statistics.current-month": "当月",
    "random.statistics.current-week": "当周",
    "random.statistics.cumulative": "累计",

    "project.overview.carried.out": "项目已进行",
    "project.overview.still.far.from.the.end": "距离结束还有",
    "project.overview.year": "年",
    "project.overview.month": "月",
    "project.overview.day": "天",
    "project.overview.week": "周",
    "project.overview.hour": "时",

    "project.overview.time": "项目时间",
    "project.overview.site.number": "中心总数",
    "project.overview.random.number": "入组数",
    "menu.message.center": "消息中心",
    "menu.message.center.single": "消息",
    "project.report.locus.select.time": "时间",
    "project.report.locus.checkbox.password": "解压密码",
    "project.report.locus.password": "密码",
    "system.message.center.see.more": "查看更多",

    "project.setting.cohort.copy.contain": "包含项目配置",
    "operation.settings.users.system.administrator": "系统管理员",
    "common.export.success": "导出成功",

    "report.export.randomization.simulation.select.name": "请选择名称",
    "report.export.forecast.date": "预测范围",
    "report.export.history.prompt": "仅保存近30天内的下载报表内容。",

    "report.visit.forecast.visit.status": "访视状态",
    "report.visit.forecast.notice.content": "通知内容",
    "report.visit.forecast.notice.time": "推送时间",
    "report.visit.forecast.notice.user": "推送人",
    "report.visit.forecast.notice.type": "推送方式",
    "report.visit.forecast.notice.email": "通知对象",

    "project.external.edc.rave.mapping.visitCode": "IRT访视编号",
    "project.external.edc.rave.mapping.edcOid": "EDC OID",
    "project.external.edc.rave.mapping.folderOid": "文件夹OID",
    "project.external.edc.rave.mapping.formOid": "表单OID",
    "project.external.edc.rave.mapping.ipNumberOid": "研究产品编号OID",
    "project.external.edc.rave.mapping.dispenseTimeOid": "发放时间OID",


    "project.external.edc.rave.mapping.randomizationField": "IRT随机字段",
    "project.external.edc.rave.mapping.randomizationCode": "随机号",
    "project.external.edc.rave.mapping.randomizationTime": "随机时间",
    "project.external.edc.rave.mapping.group": "组别",
    "project.external.edc.rave.mapping.factor": "分层因素值",
    "project.external.edc.rave.mapping.cohort": "群组/再随机",
    "project.external.edc.rave.mapping.fieldOid": "字段OID",


    "placeholder.select.common.project.type": "请选择项目类型",


    "export.notifications.configuration.report.type": "邮件类型",
    "export.notifications.configuration.report.role": "角色勾选",
    "export.notifications.configuration.report.content.configuration": "内容配置勾选",
    "export.notifications.configuration.report.scene": "场景勾选",



    "drug.batch.treatmentDesign.openSetting": "请选择发放方式",
    "drug.batch.treatmentDesign.formula": "请选择公式",

    "visit.cycle.version.number.after.publishing": "发布后，访视部分配置将无法再修改。",

    "drug.batch.treatmentDesign.formula.weight.last.calculation": "上次访视计算体重",
    "drug.batch.treatmentDesign.formula.weight.last.actual": "上次访视实际体重",
    "drug.batch.treatmentDesign.formula.weight.random": "随机访视体重",

    "drug.batch.treatmentDesign.treatment.design.open.ip": "开放研究产品",
    "drug.batch.treatmentDesign.treatment.design.automatic.recode": "自动赋值",
    "drug.batch.treatmentDesign.treatment.design.keep.decimal.places": "保留小数位",
    "drug.batch.treatmentDesign.treatment.design.weight.comparison.calculation": "体重比较计算",
    "drug.batch.treatmentDesign.treatment.design.weight.comparison.calculation.compared.with": "较",
    "drug.batch.treatmentDesign.treatment.design.weight.comparison.calculation.the.change.is": "变化",
    "drug.batch.treatmentDesign.treatment.design.weight.comparison.calculation.is.used.for.this.calculation": "本次计算体重为",



    "drug.batch.treatmentDesign.treatment.design.custom.formula": "自定义公式",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.title": "变量因素仅支持身高、体重，输入公式仅支持如下符号:",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.symbol": "符号",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation": "说明",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.eg": "举例",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.footer": "混合运算举例: 0.0061*{身高}+0.0124*{体重}-0.0090",

    "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.plus": "加",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.sub": "减",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.multiply": "乘",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.division": "除",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.remainder": "取余",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.integer.power": "整数次方",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.brackets": "括号",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.square.root": "平方根",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.cube.root": "立方根",

    "drug.batch.treatmentDesign.treatment.design.custom.formula.completion.instructions": "填写说明",
    "project.bound.repeat": "项目绑定重复，请重新确认。",

    "drug.batch.treatmentDesign.treatment.design.weight.title1": "输入符号仅支持<、≤、=，待计算的",
    "drug.batch.treatmentDesign.treatment.design.weight.title2": "值用X替换。",


    "visit.management.allowed.to.randomize": "以Baseline计算的随机访视，不需要配置间隔时长和窗口期。",

    "visit.management.allowed.to.showDoseAdjustment": "开启后，允许访视维度上，控制剂量水平或访视判断规则。",

    "report.attributes.dispensing.medicine.is.replace": "是否是替换研究产品编号",
    "report.attributes.dispensing.medicine.is.real": "是否是实际使用研究产品",


    "only.one.row.can.be.edited.simultaneously": "只能允许同时编辑一行",

    "source.ip.upload.history.name": "盲底名称",
    "source.ip.upload.history.rows": "行数",


    "subject.visit.outsize": "超窗",
    "subject.visit.outsize.undone": "未完成：",
    "subject.visit.outsize.completed": "已完成：",
    "subject.visit.in.progress": "进行中",
    "subject.visit.completed.on.schedule": "按期已完成",
    "subject.visit.has.not.started": "未开始",

    "subject.visit.cycle.item": "访视事项",
    "subject.visit.cycle.item.site": "中心：",
    "subject.visit.cycle.item.plan": "计划：",
    "subject.visit.cycle.item.actual": "实际：",



    "subject.visit.item.outsize": "超窗",
    "subject.visit.item.undone": "未完成",
    "subject.visit.item.in.progress": "进行中",
    "subject.visit.item.completed": "已完成",
    "subject.visit.item.has.not.started": "未开始",
    "subject.visit.item.on.schedule": "按期",


    "calendar.button.today": "今天",
    "calendar.button.site.visit.matter.notice": "通知",
    "calendar.button.site.visit.matter.view": "查看",

    "calendar.button.site.visit.matter.app.notice": "APP通知",
    "calendar.button.site.visit.matter.text.message": "短信",
    "calendar.button.site.visit.matter.recipient": "收件人",

    "calendar.button.site.visit.matter.notice.send": "发送通知",
    "calendar.button.site.visit.matter.notice.history": "历史记录",
    "calendar.button.site.visit.matter.notice.history.content": "通知内容",
    "calendar.button.site.visit.matter.notice.history.time": "推送时间",
    "calendar.button.site.visit.matter.notice.history.people": "推送人",
    "calendar.button.site.visit.matter.notice.history.way": "推送方式",
    "calendar.button.site.visit.matter.notice.history.object": "通知对象",

    "projects.notice.rules": "通知规则",
    "projects.notice.object": "通知对象",


    "project.visit.cycle.calendar.summary.outsize.completed": "超窗已完成：不在窗口期内完成的访视；",
    "project.visit.cycle.calendar.summary.outsize.undone": "超窗未完成：超过窗口期且未完成的访视；",
    "project.visit.cycle.calendar.summary.in.progress": "进行中：在窗口期内的未完成访视；",
    "project.visit.cycle.calendar.summary.completed.on.schedule": "按期已完成：在窗口内完成的访视；",
    "project.visit.cycle.calendar.summary.has.not.started": "未开始：当前日期小于窗口期最小值的未来访视。",

    "projects.notice.rules.template.a": "模版a",
    "projects.notice.rules.template.a.bracket": "{ }",
    "projects.notice.rules.template.a.content": "天后您有访视任务，请记得提前安排好！",
    "projects.notice.rules.template.b": "模版b",
    "projects.notice.rules.template.b.content": "明天您有访视任务，请记得安排好！",
    "projects.notice.rules.template.c": "模版c",
    "projects.notice.rules.template.c.content": "今天您有访视任务，请记得去检查！",
    "projects.notice.rules.template.d": "模版d",
    "projects.notice.rules.template.d.content": "今天是不是忘记访视安排？进来看一下呢！",

    "projects.notice.rules.template.title": "规则",
    "projects.notice.rules.template.minimum": "：最小随访窗口期前",
    "projects.notice.rules.template.day": "天，",
    "projects.notice.rules.template.select": "推送，选择",


    "projects.notice.visit.notification": "访视通知",
    "projects.notice.visit.notification.required.prefix": "请输入其他收件人手机号",
    "projects.notice.visit.notification.required.prefix.recipient": "请选择收件用户",
    "projects.notice.visit.notification.required.prefix.rules": "请完成规则填写",

    "configuration.export.treatment.design.shipment.other.drug": "*为未编号研究产品",

    "form.control.type.options.duplicate": "选项值已存在",
    "form.control.type.options.tip.one": "剂量水平：选项值仅支持系统提供的枚举应用；",
    "form.control.type.options.tip.two": "访视判断：支持自定义或系统提供的枚举应用。",
    "form.control.type.options.one": "初始剂量",
    "form.control.type.options.two": "维持上一次的剂量",
    "form.control.type.options.three": "剂量减少，选择将受试者当前剂量降低一个剂量级别",

    "form.control.type.options.selectedRepeatedly": "选项值不可重复选择",

    "drug.configure.setting.dose.form": "剂量表单",
    "drug.configure.setting.dose.form.doseAdjustment": "剂量调整",
    "drug.configure.setting.dose.form.type": "剂量选择",
    "drug.configure.setting.dose.level": "剂量水平",
    "drug.configure.setting.dose.visit.judgment": "访视判断",
    "drug.configure.setting.dose.level.set": "剂量水平集",
    "drug.configure.setting.dose.level.set.tip1": "剂量水平集的设置，均按以下方法对其排序：首先输入最低剂量，最后输入最高剂量。最低剂量应显示于表格顶部，而最高剂量应显示于表格底部。发放剂量仅支持固定数量发放的研究产品。",
    "drug.configure.setting.dose.level.desc": "允许设置受试者治疗设计上剂量下调的次数",
    "drug.configure.setting.dose.visit.judgment.desc": "允许设置受试者在访视维度进行判断规则确认后续发放水平",
    "drug.configure.setting.dose.form.list.isFirstInitial": "首次访视启用初始剂量",
    "drug.configure.setting.dose.form.list.isDoseReduction": "允许受试者剂量下调的次数",
    "drug.configure.setting.dose.form.list.name": "名称/值",
    "drug.configure.setting.dose.form.list.group": "组别",
    "drug.configure.setting.dose.form.list.dose.distribution": "发放剂量",
    "drug.configure.setting.dose.form.list.initial.dose": "初始剂量",
    "drug.configure.setting.dose.form.list.visit.inheritance": "后续访视继承",
    "drug.configure.setting.dose.form.list.visit.inheritance.dispensing": "访视继承发放",
    "drug.configure.setting.dose.form.list.visit.inheritance.stop": "停止发放",
    "drug.configure.setting.dose.form.list.visit.inheritance.main": "主访视",
    "drug.configure.setting.dose.form.list.name.selectedRepeatedly": "名称/值不能重复",

    "visit.cycle.management.is.group": "组别已修改，请重新映射访视与组别的关系。",
    "visit.cycle.management.drug.configure.is.group": "组别已修改，请重新映射研究产品名称与组别的关系。",
    "visit.cycle.management.drug.configure.is.group.label": "研究产品配置已修改，请重新配置发放剂量。",
    "random.list.inactivating.tips": "无效后，可通过同步操作将无效组别同步已激活随机表，且失效随机号不能再次激活。",
    "random.list.inactivating.success": "同步成功，随机表已跳过无效组别随机号规则。",


    "common.export.format": "导出格式",

    "visit.cycle.management.is.group.visit.save.release.unpublished": "存在未发布的访视",

    "visit.cycle.management.is.group.visit.save": "操作成功，访视计划需发布后生效，",
    "visit.cycle.management.is.group.visit.save.release": "立即发布",

    "visit.cycle.management.is.group.visit.release.save": "发布成功，研究产品配置需重新关联确认访视名称，",
    "visit.cycle.management.is.group.visit.release.save.treatmentDesign": "去关联",

    "visit.cycle.management.is.group.visit.batch.save": "批次号已更新，请重新确认研究产品继承规则。",


    "env.copy.prod.isCopy.prompt.language": "PROD环境未产生受试者/订单相关数据，可直接进行配置复制。",

    "env.copy.prod.isCopy.prompt.language.tips": "复制后，环境内部分核心配置不允许删除/修改。",

    "user.batch.add.email.required": "请输入邮箱，多个用回车分隔",
    "user.batch.add.email.tip1": "列表邮箱无法新增。",
    "user.batch.add.email.tip2": "列表邮箱无法新增，点击忽略并继续将跳过列表邮箱发送邀请邮件。",
    "common.back.revise": "返回修改",
    "common.ignore.continue": "忽略并继续",
    "common.user.add.success": "用户新增成功",
    "common.user.add.configure.permissions": "配置权限",
    "common.user.add.configure.permissions.role": "配置角色",
    "common.user.add.configure.permissions.tip": "如需为新增用户批量分配权限，可点击配置权限按钮。",
    "common.user.add.configure.permissions.role.tip": "如需为新增用户批量分配角色，可点击配置角色按钮。",

    "notice.exclude_recipient_list.email": "排除收件人",
    "notice.exclude_recipient_list.email.batch": "批量导入",
    "notice.exclude_recipient_list.email.batch.tip": "仅支持上传Excel格式文件",
    "notice.exclude_recipient_list.email.account": "邮箱账户",
    "notice.exclude_recipient_list.email.tip1": "列表邮箱均非项目用户，请重新输入。",
    "notice.exclude_recipient_list.email.tip2": "列表邮箱非项目用户，点击忽略并继续代表操作仅对剩余邮箱生效。",
    "notice.exclude_recipient_list.email.tip3": "删除线标识邮箱表示该用户已解绑",

    "week.all": "每天",
    "week.monday": "周一",
    "week.tuesday": "周二",
    "week.wednesday": "周三",
    "week.thursday": "周四",
    "week.friday": "周五",
    "week.saturday": "周六",
    "week.sunday": "周日",
    "time.hour": "时",
    "time.minute": "分",

    "multiLanguage.allLanguage": "全部语言",
    "multiLanguage.enable.status": "启用状态",
    "multiLanguage.translation.progress": "翻译进度",
    "multiLanguage.shared.system.library": "共享系统库",
    "multiLanguage.language.library": "语言库",
    "multiLanguage.language.library.system": "项目库/系统库",
    "multiLanguage.language.library.construct": "项目库/项目构建",
    "multiLanguage.language.library.eIRT": "eIRT库",
    "multiLanguage.page.path": "页面路径",
    "multiLanguage.translation.status": "翻译状态",
    "multiLanguage.translation.status.yes": "已翻译",
    "multiLanguage.translation.status.no": "未翻译",
    "multiLanguage.translation.export": "导入",
    "multiLanguage.translation.no.data": "输入查询条件后，在此查看结果",
    "multiLanguage.translation.downloadTemplate.downloadCheckbox": "下载勾选项",
    "multiLanguage.translation.downloadTemplate.downloadAll": "下载全部",

    "upload.click": "点击此处",
    "upload.drag": "或拖拽上传文件",
    "upload.excel.reminder": "上传的文件格式支持csv/xls/xlsx，大小不超过100M",
    "upload.import.success": "保存/导入成功",
    "upload.excel.header.error": "检测到导入文件表头与页面所需内容不匹配，请确认后重新上传。",
    "upload.excel.key.not-match": "检测到文件内存在非当前表单的key值，请确认后重新上传。",

    "projects.remark": "备注",

    "tips.user.invalid": "操作失败，当前用户为无效状态，请联系管理员确认。",
    "tips.user.Permission": "权限已变更，请联系管理员确认。",
    "common.select.all": "全选所有",
    "common.select.invert.currentPage": "反选当页",
    "common.select.clearAll": "清空所有",
    "barcode.label.selectDrug.info": "请选择研究产品",
    "barcode.label": "标签",
    "barcode.label.number": "标签编号",
    "barcode.label.printCount": "打印数量",
    "barcode.label.printPaperSize": "打印纸张大小",
    "barcode.label.size": "标签大小",
    "barcode.label.baseComponent": "基础组件",
    "barcode.label.send": "发送",
    "barcode.label.send.confirm": "是否确定发送？",
    "barcode.label.codeManual": "手动",
    "barcode.label.codeAuto": "自动",
    "barcode.label.addSuccessTips": "新增标签成功",
    "barcode.label.preview": "标签预览",
    "barcode.label.printPreview": "打印预览",
    "barcode.label.component.delete": "删除组件？",
    "barcode.label.component.textBox": "文本框",
    "barcode.label.sendSuccess": "发送成功",
    "barcode.label.component.smartAlign": "智能对齐",
    "barcode.label.attribute.textAlignLeft": "居左",
    "barcode.label.attribute.textAlignCenter": "居中",
    "barcode.label.attribute.textAlignRight": "居右",
    "barcode.label.attribute.fontFamilyMicrosoftYaHei": "微软雅黑",
    "barcode.label.attribute.fontFamilySong": "宋体",
    "barcode.label.attribute.fontFamilyArial": "Arial",
    "barcode.label.attribute.fontFamilyTimesNewRoman": "TimesNewRoman",
    "barcode.label.attribute.text": "文本",
    "barcode.label.attribute.position": "位置",
    "barcode.label.attribute.fontFamily": "字体",
    "barcode.label.attribute.width": "宽度",
    "barcode.label.attribute.height": "高度",
    "barcode.label.attribute.marginTop": "上边距",
    "barcode.label.attribute.marginLeft": "左边距",
    "barcode.label.template.select": "选择模板",
    "barcode.label.template.name.default": "默认模板",
    "barcode.label.template.linkTips": "确定后将直接覆盖当前已有内容。",
    "barcode.label.template.emptyTips": "将基础组件拖拽至此处",

};
