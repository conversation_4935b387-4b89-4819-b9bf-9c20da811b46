import React from "react";
import {Col, Input, Row, Table} from "antd";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";

import {useSafeState} from "ahooks";
import {useAuth} from "../../../../context/auth";
import {useFetch} from "../../../../hooks/request";
import {otherMedicineSiteSkuWithDTP} from "../../../../api/medicine";
import {PaginationView} from "../../../../components/pagination";
import {usePage} from "../../../../context/page"

export const OtherMedicineSku = (props) => {

    const intl = useTranslation();
    const {formatMessage} = intl;

    const [subject, setSubject] = useSafeState(undefined);
    const [summaryList, setSummaryList] = useSafeState([]);
    const [tableHeight, setTableHeight] = useSafeState(300);
    const [selecteds, setSelecteds] = useSafeState([]);
    const [selectedIds, setSelectedIds] = useSafeState([]);
    const page = usePage()

    const auth = useAuth()
    const envId = auth.env ? auth.env.id : null;
    const projectId = auth.project.id;
    const customerId = auth.customerId;

    const {runAsync: otherMedicineSiteSkuWithDTPSku, loading} = useFetch(otherMedicineSiteSkuWithDTP, {manual: true})


    const search = () => {
        otherMedicineSiteSkuWithDTPSku({
            customerId: customerId,
            projectId: projectId,
            envId: envId,
            subject : subject,
            roleId: auth.project.permissions.role_id,
            start: (page.currentPage - 1) * page.pageSize,
            limit: page.pageSize,
        }).then((result) => {
            let response = result.data
            setSummaryList(response.item)
            page.setTotal(response.total)
        });
    }

    React.useEffect(
        () => {
            setTableHeight(document.documentElement.clientHeight - 250);
        },
        []
    );

    React.useEffect(search, [subject, page.currentPage, page.pageSize])

    const rowSelection = {
        type: "checkbox",
        onChange: (selectedRowKeys, selectedRows) => {
            setSelecteds(selectedRows);
            setSelectedIds(selectedRowKeys)
        },
        selectedRows: selecteds,
        selectedRowKeys: selectedIds,
        getCheckboxProps: (record) => ({
            disabled: record.count <= 0
        }),
        preserveSelectedRowKeys: true,
    };

    const resetSelected = () => {
        setSelecteds([])
        setSelectedIds([])
    }

    const refreshList = () => {
        search();
        resetSelected()
    };

    const {Search} = Input;

    const onSearch = (e) => {
        setSubject(e)
    }

    return (
        <React.Fragment>
            <Row gutter={8}>

                <Col xs={24} sm={24} md={12} lg={6} className="mar-top-5">

                    <Search placeholder={auth.attribute?.info?.subjectReplaceText} onSearch={onSearch}
                            style={{width: 200}}/>
                </Col>
            </Row>
            <Table
                className="mar-top-10"
                loading={loading}
                dataSource={summaryList}
                style={{maxHeight: tableHeight, overflowY: "auto"}}
                size="small"
                pagination={false}
                rowKey={(record) => (record._id)}
                rowSelection={rowSelection}
            >
                <Table.Column title={auth.attribute?.info?.subjectReplaceText}
                              dataIndex="subject"
                              key="subject" ellipsis width={220}/>
                <Table.Column
                    title={<FormattedMessage id="drug.configure.drugName"/>}
                    dataIndex="name" key="name" ellipsis width={220}
                />
                <Table.Column
                    title={<FormattedMessage id="drug.list.batch"/>}
                    dataIndex="batch"
                    key="batch" ellipsis width={220}/>
                <Table.Column title={<FormattedMessage id="projects.statistics.sku.expirationDate"/>}
                              dataIndex="expire_date"
                              key="expire_date" ellipsis width={120}/>
                <Table.Column title={<FormattedMessage id="common.site"/>}
                              dataIndex="site"
                              key="site" ellipsis width={220}/>
                <Table.Column title={<FormattedMessage id="medicine.status.apply"/>}
                              dataIndex="apply_count" key="apply_count" width={100}
                />
                <Table.Column title={<FormattedMessage id="medicine.status.transit"/>}
                              dataIndex="in_transit_count" key="in_transit_count" width={100}
                />
                <Table.Column title={<FormattedMessage id="medicine.status.used"/>}
                              dataIndex="used_count" key="used_count" width={100}
                />
            </Table>
            <PaginationView mode="SELECTABLE" selectedNumber={selectedIds?.length} clearDisplay={true} refresh={refreshList} />
        </React.Fragment>
    )
};
