import React, { useEffect } from "react";
import {
  Badge,
  Button,
  Col,
  DatePicker,
  Input,
  message,
  Row,
  Select,
  Table,
} from "antd";
import { FormattedMessage, useTranslation } from "../../../common/multilingual/component";
import { HistoryList } from "../../../common/history-list.jsx";
import { permissions } from "../../../../tools/permission";
import { useSafeState } from "ahooks";
import { medicineStatusColors } from "../../../../data/data";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import {
  downloadDtpSku,
  medicineDtpSku,
  updateMedicineStatus,
} from "../../../../api/medicine";
import { medicineStatusData } from "../../../../tools/medicine_status";
import { PaginationView } from "../../../../components/pagination";
import Footer from "../../../main/layout/footer";
import { usePage } from "../../../../context/page";
import { CustomConfirmModal } from "../../../../components/modal";

export const SkuMain = (props) => {
  const intl = useTranslation();
  const { formatMessage } = intl;
  const auth = useAuth();
  const page = usePage();
  const [field, setField] = useSafeState(undefined);
  const [fieldDate, setFieldDate] = useSafeState(null);
  const [fieldValue, setFieldValue] = useSafeState(null);
  const [status, setStatus] = useSafeState(undefined);
  const [summaryList, setSummaryList] = useSafeState([]);
  const [tableHeight, setTableHeight] = useSafeState(300);

  const envId = auth.env ? auth.env.id : null;
  const projectId = auth.project.id;
  const customerId = auth.customerId;
  const searchInputRef = React.useRef();
  const [doSearch, setDoSearch] = useSafeState(0);
  const [number, setNumber] = useSafeState(null);
  const timeZone =
    (auth.project.info.timeZoneStr !== undefined && auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "") ? Number(auth.project.info.timeZoneStr) : 8;
  const history_ref = React.useRef();
  const fields = [
    { id: "number", name: formatMessage({ id: "drug.list.drugNumber" }) },
    { id: "name", name: formatMessage({ id: "drug.configure.drugName" }) },
    { id: "batch_number", name: formatMessage({ id: "drug.list.batch" }) },
    {
      id: "expiration_date",
      name: formatMessage({ id: "projects.statistics.sku.expirationDate" }),
    },
    { id: "order_number", name: formatMessage({ id: "shipment.orderNumber" }) },
  ];
  const { runAsync: run_medicineDtpSku, loading } = useFetch(medicineDtpSku, {
    manual: true,
  });
  const { runAsync: run_updateMedicineStatus } = useFetch(
    updateMedicineStatus,
    { manual: true }
  );
  const search = () => {
    run_medicineDtpSku({
      customerId: customerId,
      projectId: projectId,
      envId: envId,
      status: status,
      field: field,
      fieldValue: fieldValue,
      subjectNumber: number,
      roleId: auth.project.permissions.role_id,
      start: (page.currentPage - 1) * page.pageSize,
      limit: page.pageSize,
    }).then((result) => {
      let response = result.data;
      setSummaryList(response.items);
      page.setTotal(response.total);
    });
  };

  React.useEffect(() => {
    setTableHeight(document.documentElement.clientHeight - 250);
  }, []);

  React.useEffect(search, [doSearch, status, page.currentPage, page.pageSize]);

  const showHistory = (id) => {
    history_ref.current.show("history.medicine", id, timeZone);
  };

  const onUpdateStatus = (id, status) => {
    let title;
    if (status === 1) {
      title = formatMessage({ id: "model.note.title.medicine.use" });
    } else if (status === 6) {
      title = formatMessage({ id: "model.note.title.medicine.lose" });
    }
    CustomConfirmModal({
      title: title,
      okText: formatMessage({ id: "common.ok" }),
      onOk: () =>
        run_updateMedicineStatus({ medicineIds: [id], status: status }).then(
          () => {
            search();
          }
        ),
    });
  };

  const { runAsync: run_downloadDtpSku, loading: downloadLoading } = useFetch(
    downloadDtpSku,
    { manual: true }
  );

  function downloadData() {
    run_downloadDtpSku({
      customerId: customerId,
      projectId: projectId,
      envId: envId,
      subjectNumber: number,
      status: status,
      field: field,
      fieldValue: fieldValue,
      roleId: auth.project.permissions.role_id,
    }).catch(() => {
      message
        .error(props.intl.formatMessage({ id: "common.download.fail" }))
        .then();
    });
  }

  useEffect(() => search(), []);

  return (
    <React.Fragment>
      <Row gutter={8}>
        <Col xs={24} sm={12} md={6} lg={4} className="mar-ver-5">
          <Input.Search
            placeholder={auth.attribute?.info?.subjectReplaceText}
            value={number}
            onChange={(e) => setNumber(e.target.value)}
            allowClear
            onSearch={() => {
              page.setCurrentPage(1);
              searchInputRef?.current?.blur();
              setDoSearch(doSearch + 1);
            }}
          />
        </Col>
        <Col xs={24} sm={24} md={12} lg={5} className="mar-top-5">
          <Select
            style={{ width: "100%" }}
            allowClear
            value={status}
            onChange={setStatus}
            placeholder={<FormattedMessage id="drug.batch.management.status" />}
          >
            {medicineStatusData(
              auth.codeRule,
              auth.project.info.research_attribute
            ).map((it) => (
              <Select.Option key={it.value} value={it.value}>
                {it.label}
              </Select.Option>
            ))}
          </Select>
        </Col>
        <Col xs={24} sm={24} md={12} lg={9} className="mar-top-5">
          <Input.Group compact>
            <Select
              style={{ width: "204px" }}
              placeholder={
                <FormattedMessage id="projects.statistics.selectField" />
              }
              allowClear
              value={field}
              onChange={(value) => {
                setFieldValue(null);
                setField(value);
              }}
            >
              {fields.map((it) => (
                <Select.Option key={it.id} value={it.id}>
                  {it.name}
                </Select.Option>
              ))}
            </Select>
            {field === "expiration_date" ? (
              <DatePicker
                style={{ width: "calc(100% - 204px)" }}
                allowClear
                value={fieldDate}
                onChange={(e) => {
                  setFieldDate(e);
                  setFieldValue(e ? e.format("YYYY-MM-DD") : null);
                }}
              />
            ) : (
              <Input.Search
                ref={searchInputRef}
                style={{ width: "calc(100% - 204px)" }}
                value={fieldValue}
                onChange={(e) => {
                  setFieldValue(e.target.value);
                }}
                allowClear
                onSearch={() => {
                  searchInputRef?.current?.blur();
                  page.setCurrentPage(1);
                  setDoSearch(doSearch + 1);
                }}
              />
            )}
          </Input.Group>
        </Col>
        <Col xs={24} sm={24} md={12} lg={6} className="pad-top-5">
          {permissions(
            auth.project.permissions,
            "operation.supply.drug.single.download"
          ) ? (
            <Button loading={downloadLoading} onClick={() => downloadData()}>
              <FormattedMessage id="common.download.data" />
            </Button>
          ) : null}
        </Col>
      </Row>
      <Table
        className="mar-top-10"
        loading={loading}
        dataSource={summaryList}
        style={{ maxHeight: tableHeight, overflowY: "auto" }}
        size="small"
        pagination={false}
        rowKey={(record) => record.id}
      >
        <Table.Column
          title={
            auth.attribute
              ? auth.attribute.info.subjectReplaceText
              : formatMessage({ id: "subject.number" })
          }
          width={200}
          dataIndex="subjectNumber"
          key="subjectNumber"
          ellipsis
        />
        <Table.Column
          title={<FormattedMessage id="drug.list.drugNumber" />}
          dataIndex="number"
          key="number"
          ellipsis
          width={220}
        />
        <Table.Column
          title={<FormattedMessage id="drug.configure.drugName" />}
          dataIndex="name"
          key="name"
          ellipsis
          width={220}
        />
        <Table.Column
          title={<FormattedMessage id="suppy.drug.order.visitNumber" />}
          width={200}
          dataIndex="visitNumber"
          key="visitNumber"
          ellipsis
        />
        <Table.Column
          title={<FormattedMessage id="drug.list.batch" />}
          dataIndex="batchNumber"
          key="batchNumber"
          ellipsis
          width={220}
        />
        <Table.Column
          title={
            <FormattedMessage id="projects.statistics.sku.expirationDate" />
          }
          dataIndex="expirationDate"
          key="expirationDate"
          ellipsis
          width={120}
        />
        <Table.Column
          title={<FormattedMessage id="shipment.orderNumber" />}
          dataIndex="orderNumber"
          key="orderNumber"
          width={220}
          ellipsis
        />
        <Table.Column
          title={<FormattedMessage id="common.site" />}
          dataIndex="siteName"
          key="siteName"
          ellipsis
          width={220}
        />
        <Table.Column
          title={<FormattedMessage id="projects.statistics.sku.status" />}
          dataIndex="status"
          width={150}
          key="status"
          ellipsis
          render={(text, record, index) => {
            return (
              <Badge
                color={medicineStatusColors[text]}
                text={
                  medicineStatusData(
                    auth.codeRule,
                    auth.project.info.research_attribute
                  ).find((it) => it.value === text).label
                }
              />
            );
          }}
        />
        <Table.Column
          title={<FormattedMessage id="common.operation" />}
          align="center"
          width={220}
          render={(value, record, index) => (
            <React.Fragment>
              {permissions(
                auth.project.permissions,
                "operation.supply.drug.single.history"
              ) && (
                <Button
                  size="small"
                  type="link"
                  onClick={() => showHistory(record.id)}
                >
                  <FormattedMessage id="common.history" />
                </Button>
              )}
              {record.status === 6 &&
              permissions(
                auth.project.permissions,
                "operation.supply.drug.single.use"
              ) ? (
                <Button
                  size="small"
                  type="link"
                  onClick={() => onUpdateStatus(record.id, 1)}
                >
                  <FormattedMessage id="drug.list.setUse" />
                </Button>
              ) : null}
              {record.status === 1 &&
              permissions(
                auth.project.permissions,
                "operation.supply.drug.single.delete"
              ) ? (
                <Button
                  size="small"
                  type="link"
                  onClick={() => onUpdateStatus(record.id, 6)}
                >
                  <FormattedMessage id="medicine.status.lose" />
                </Button>
              ) : null}
            </React.Fragment>
          )}
        />
      </Table>
      <Footer>
        <PaginationView />
      </Footer>
      <HistoryList
        bind={history_ref}
        permission={permissions(
          auth.project.permissions,
          "operation.supply.drug.single.print"
        )}
      />
    </React.Fragment>
  );
};
