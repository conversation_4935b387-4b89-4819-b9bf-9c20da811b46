import React from "react";
import { Col, Form, Input, message, Modal, Row, Table } from "antd";
import { FormattedMessage, useIntl } from "react-intl";
import { useFetch } from "../../../../hooks/request";
import { getMedicineFreezeByIds, updateMedicineStatus } from "../../../../api/medicine";
import { useSafeState } from "ahooks";
import { useAuth } from "../../../../context/auth";
import { Title } from "components/title";
import { combineRow } from "../../../../utils/merge_cell";
import { medicineStatusData } from "../../../../tools/medicine_status";

export const MakeAvailable = (props) => {
    const [visible, setVisible] = useSafeState(false);
    const [medicineIds, setMedicineIds] = useSafeState([]);
    const [medicines, setMedicines] = useSafeState([]);
    const [form] = Form.useForm();
    const intl = useIntl();
    const { formatMessage } = intl;
    const [submitting, setSubmitting] = useSafeState(false);
    const [packageIsOpen, setPackageIsOpen] = useSafeState(false);
    const [storehouses, setStorehouses] = useSafeState([]);

    const auth = useAuth()
    const projectId = auth.project ? auth.project.id : null;
    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId;

    const show = (data, ids, instituteType, packageIsOpen, toastFlag, storehouses) => {
        setStorehouses(storehouses)
        form.resetFields()
        setVisible(true)
        setPackageIsOpen(packageIsOpen)
        if (data) {
            setMedicines(data);
        }
        if (ids) {
            setMedicineIds(ids);
            //根据ids查询数据,重新赋值数据
            if (packageIsOpen) {
                search(ids)
            } else {
                setMedicineIds(ids);
            }
        }
        var length = 0;
        if (ids != null && ids.length > 0) {
            length = ids.length
        }
        if (toastFlag) {
            message.warn(formatMessage({ id: "single.freeze.setUse.info" }, { total: length }))
        }
    };

    const { runAsync: run_getMedicineFreezeByIds } = useFetch(getMedicineFreezeByIds, { manual: true })
    const search = (ids) => {
        run_getMedicineFreezeByIds({
            customerId: customerId,
            projectId: projectId,
            envId: envId,
            medicineIds: ids,
            status: 6
        }).then((result) => {
            const data = result.data
            setMedicineIds(data.ids)
            var tableData = combineRow(data.medicines, "packageNumber", "package_number", true)
            tableData = combineRow(data.medicines, "name", "name", false)
            setMedicines(tableData)
        });
    }

    const hide = () => {
        setVisible(false);
        setSubmitting(false);
        // setMedicineIds([]);
        form.resetFields();
        props.search();
    };
    const { runAsync: run_updateMedicineStatus } = useFetch(updateMedicineStatus, { manual: true });
    const confirmAvailable = () => {
        form.validateFields().then(values => {
            setSubmitting(true);
            run_updateMedicineStatus({ instituteType: 2, medicineIds: medicineIds, status: 1, ...values }).then((resp) => {
                message.success(resp.msg);
                hide();
                props.refresh();
            }, () => {
                setSubmitting(false);
            });
        }).catch(errorInfo => {
            // 验证失败时的操作
            setSubmitting(false);
            if (errorInfo.errorFields.length > 0) {
              // 获取第一个出错字段的名字
              const firstErrorFieldName = errorInfo.errorFields[0].name.join('.');
              scrollToField(firstErrorFieldName);
            }
        });
    }


    const scrollToField = fieldKey => {
        const labelNode = document.querySelector(`label[for="${fieldKey}"]`);
        if (labelNode) {
          labelNode.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    };

    const { TextArea } = Input;

    React.useImperativeHandle(props.bind, () => ({ show }));

    return (
        <Modal
            className='custom-large-modal'
            title={formatMessage({ id: 'operation.supply.storehouse.medicine.use' })}
            visible={visible}
            onCancel={hide}

            maskClosable={false}
            centered
            destroyOnClose
            okButtonProps={{ loading: submitting }}
            okText={formatMessage({ id: 'common.ok' })}
            onOk={confirmAvailable}
        >
            <Row gutter={[16, 16]}>
                <Col span={24}>
                    <div style={{ marginBottom: 12 }}>
                        <Title name={formatMessage({ id: "drug.list.drugNumber" })} />
                    </div>
                    <Table
                        style={{ marginTop: 12 }}
                        size="small"
                        dataSource={medicines}
                        pagination={false}
                        rowKey={(record) => (record.id)}
                    >
                        {/* <Table.Column title={<FormattedMessage id="common.serial"/>} 
                            dataIndex="#" key="#" width={70}
                            render={(text, record, index) => (index + 1)}   
                            onCell={(_, index) => {
                                return {rowSpan : medicines[index].package_numberRowSpan}
                            }}/> */}

                        <Table.Column title={<FormattedMessage id="common.serial" />} dataIndex="index" key="index" width={70}
                            onCell={(_, index) => {
                                return { rowSpan: medicines[index].package_numberRowSpan }
                            }}
                            render={(text, record, index) => packageIsOpen ? record.index : index + 1} />
                        {packageIsOpen ?
                            <Table.Column title={<FormattedMessage id="storehouse.name" />}
                                dataIndex="storehouseId"
                                key="storehouseId" ellipsis
                                render={(value) => storehouses.find(it => it.id === value)?.name}
                            />
                            : <Table.Column title={<FormattedMessage id="storehouse.name" />}
                                dataIndex="storehouseName"
                                key="storehouseName" ellipsis
                            />
                        }
                        {packageIsOpen ?
                            <Table.Column
                                title={<FormattedMessage id="drug.configure.drugName" />}
                                dataIndex={"name"}
                                key="name"
                                ellipsis
                                align="left"
                                onCell={(_, index) => {
                                    return { rowSpan: medicines[index].package_numberRowSpan }
                                }}
                            />
                            :
                            <Table.Column
                                title={<FormattedMessage id="drug.configure.drugName" />}
                                dataIndex={"name"}
                                key="name"
                                ellipsis
                                align="left" />
                        }
                        {packageIsOpen && <Table.Column
                            title={<FormattedMessage id="drug.medicine.packlist" />}
                            dataIndex={"packageNumber"}
                            key="packageNumber"
                            ellipsis
                            align="left"
                            render={(value, record, index) => {
                                let newValue = ""
                                if (value !== undefined && value !== "") {
                                    newValue = value
                                } else {
                                    newValue = "-"
                                }
                                let obj = {
                                    children: newValue,
                                    props: { rowSpan: medicines[index].package_numberRowSpan }
                                }
                                return obj
                            }}
                        />}
                        <Table.Column title={formatMessage({ id: 'drug.list.drugNumber' })} key="number" width={150}
                            dataIndex="number" align="left" ellipsis />
                        <Table.Column title={formatMessage({ id: 'drug.list.expireDate' })} key="expirationDate" width={100}
                            dataIndex="expirationDate" align="left" ellipsis />
                        <Table.Column title={formatMessage({ id: 'drug.list.batch' })} key="batchNumber" width={150}
                            dataIndex="batchNumber" align="left" ellipsis />
                        <Table.Column title={formatMessage({ id: 'drug.list.status' })} key="status" width={150}
                            dataIndex="status" align="left" ellipsis
                            render={(_, record) => (
                                medicineStatusData(auth.codeRule, auth.project.info.research_attribute)
                                    .find(it => it.value === record.status)?.label
                            )}
                        />
                    </Table>
                </Col>
                <Col span={24}>
                    <Form form={form} layout="horizontal">
                        <Form.Item label={formatMessage({ id: 'drug.freeze.reason' })} rules={[{ required: true }]} name="reason"
                            className="mar-ver-5">
                            <TextArea placeholder={formatMessage({ id: 'placeholder.input.common' })} allowClear showCount maxLength={500} />
                        </Form.Item>
                    </Form>
                </Col>
            </Row>
        </Modal>
    );
};


