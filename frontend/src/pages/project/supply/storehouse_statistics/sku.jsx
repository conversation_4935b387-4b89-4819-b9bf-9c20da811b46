import React, { useRef } from "react";
import { Badge, Col, DatePicker, Form, Input, message, Popover, Row, Select, Space, Spin, Table } from "antd";
import { HistoryList } from "../../../common/history-list.jsx";
import { permissions } from "../../../../tools/permission";
import Barcode from "react-barcode";
import { useSafeState } from "ahooks";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import { Reason } from "../site_pharmacy/reason";
import { SelectedFreeze } from "./selected_freeze";
import { MakeAvailable } from "./make_medicine_available.jsx";
import { downloadStorehouseSku, medicineStorehouseSku } from "../../../../api/medicine";
import { medicineStatusColors } from "../../../../data/data";
import { medicineStatusAliData } from "../../../../tools/medicine_status";
import { usePage } from "../../../../context/page";
import { PaginationView } from "../../../../components/pagination";
import { fillTableCellEmptyPlaceholder } from "../../../../components/table";
import { InsertDivider } from "components/divider";
import { TitledPopover } from "../../../../components/popover";
import Footer from "../../../main/layout/footer";
import { userStoreHouses } from "../../../../api/project_storehouse";
import { getBarcodeRule } from "../../../../api/barcode";
import { getProjectAttributeConnect } from "../../../../api/randomization";
import { FormattedMessage, useTranslation } from "../../../common/multilingual/component";
import { AuthButton } from "../../../common/auth-wrap";
import { VirtualTable } from "../../../../components/virtual-tabel";
import { PageContextProvider } from "../../../../context/page";

export const Sku = (props) => {
  const intl = useTranslation();
  const { formatMessage } = intl;
  const page = usePage();
  const [field, setField] = useSafeState(undefined);
  const [fieldValue, setFieldValue] = useSafeState("");
  const searchInputRef = useRef();
  const [doSearch, setDoSearch] = useSafeState(0);
  const [isOpenPackage, setIsOpenPackage] = useSafeState(false);
  const [fieldDate, setFieldDate] = useSafeState(null);
  const [storehouse, setStorehouse] = useSafeState(undefined);
  const [status, setStatus] = useSafeState(undefined);
  const [summaryList, setSummaryList] = useSafeState([]);
  const [tableHeight, setTableHeight] = useSafeState(300);
  const [storehouses, setStorehouses] = useSafeState([]);
  const [selecteds, setSelecteds] = useSafeState([]);
  const [selectedIds, setSelectedIds] = useSafeState([]);
  const [fields, setFields] = useSafeState([]);
  const [paramStorehouses, setParamStorehouses] = useSafeState([])
  const [paramStorehousesOp, setParamStorehousesOp] = useSafeState(1)
  const [storehouseMode, setStorehouseMode] = useSafeState({})
  const [storehouseOpen, setStorehouseOpen] = useSafeState(false)
  const selected_freeze = React.useRef()
  const make_available = React.useRef()
  const reason_ref = React.useRef()
  const auth = useAuth()
  const envId = auth.env ? auth.env.id : null;
  const projectId = auth.project.id;
  const customerId = auth.customerId;
  const timeZone = (auth.project.info.timeZoneStr !== undefined && auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "") ? Number(auth.project.info.timeZoneStr) : 8;
  const history_ref = React.useRef();
  const projectStatus = auth.project.status ? auth.project.status : 0
  const [codeRule, setCodeRule] = useSafeState(null);

  const { runAsync: getBarcodeRun } = useFetch(getBarcodeRule, { manual: true })

  const barcodeCodeRule = () => {
    getBarcodeRun({
      customerId: customerId,
      projectId: projectId,
      envId: envId,
    }).then((response) => {
      if (response) {
        let data = response.data;
        setCodeRule(data.codeRule1);
      }
    });
  };

  //const codeRule = auth.codeRule;
  // const fields = [
  //     { id: 'number', name: formatMessage({ id: 'drug.list.drugNumber' }) },
  //     { id: 'name', name: formatMessage({ id: 'drug.configure.drugName' }) },
  //     { id: 'batch_number', name: formatMessage({ id: 'drug.list.batch' }) },
  //     { id: 'expiration_date', name: formatMessage({ id: 'projects.statistics.sku.expirationDate' }) },
  //     { id: 'order_number', name: formatMessage({ id: 'shipment.orderNumber' }) },
  //     { id: 'package_number', name: formatMessage({ id: 'drug.medicine.packlist' }) },
  // ]

  const [ali, setAli] = useSafeState(0);

  const {
    runAsync: getProjectAttributeConnectRun,
    loading: getProjectAttributeConnectLoading,
  } = useFetch(getProjectAttributeConnect, { manual: true });

  const { runAsync: run_medicineStorehouseSku, loading } = useFetch(
    medicineStorehouseSku,
    { manual: true }
  );

  function escapeRegexSpecialChars(str) {
    // 正则表达式特殊字符
    var specialChars = /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g;

    // 使用replace方法和函数作为第二个参数来转义每个匹配项
    return str?.replace(specialChars, '\\$&');
  }
  const search = () => {
    getProjectAttributeConnectRun({ projectId: projectId, envId: envId }).then(
      (res) => {
        if (res.data) {
          if (res.data.info.connectAli) {
            setAli(1);
          }
        }
      }
    );
    if (field !== undefined && field != null && field !== "") {
      // let key = escapeRegexSpecialChars(fieldValue);
      let key = fieldValue;
      if (fieldValue !== null && fieldValue !== undefined) {
        key = fieldValue.trim();
      }
      run_medicineStorehouseSku({
        customerId: customerId,
        projectId: projectId,
        envId: envId,
        storehouseIds: paramStorehouses,
        status: String(status),
        field: field,
        fieldValue: key,
        roleId: auth.project.permissions.role_id,
        start: (page.currentPage - 1) * page.pageSize,
        limit: page.pageSize,
      }).then((result) => {
        const response = result.data;
        setSummaryList(
          fillTableCellEmptyPlaceholder(response.items ? response.items : [])
        );
        page.setTotal(response.total);
        setIsOpenPackage(response.isOpenPackage);
        if (response.isOpenPackage) {
          //fields.push( { id: 'package_number', name: formatMessage({ id: 'drug.medicine.packlist' }) },)
          setFields([
            {
              id: "number",
              name: formatMessage({ id: "drug.list.drugNumber", allowComponent: true }),
            },
            {
              id: "name",
              name: formatMessage({ id: "drug.configure.drugName", allowComponent: true }),
            },
            {
              id: "batch_number",
              name: formatMessage({ id: "drug.list.batch", allowComponent: true }),
            },
            {
              id: "expiration_date",
              name: formatMessage({
                id: "projects.statistics.sku.expirationDate", allowComponent: true
              }),
            },
            {
              id: "order_number",
              name: formatMessage({ id: "shipment.orderNumber", allowComponent: true }),
            },
            {
              id: "package_number",
              name: formatMessage({ id: "drug.medicine.packlist", allowComponent: true }),
            },
          ]);
          if (field === undefined || field === null || field === "") {
            setField("number");
          }
        } else {
          setFields([
            {
              id: "number",
              name: formatMessage({ id: "drug.list.drugNumber", allowComponent: true }),
            },
            {
              id: "name",
              name: formatMessage({ id: "drug.configure.drugName", allowComponent: true }),
            },
            {
              id: "batch_number",
              name: formatMessage({ id: "drug.list.batch", allowComponent: true }),
            },
            {
              id: "expiration_date",
              name: formatMessage({
                id: "projects.statistics.sku.expirationDate", allowComponent: true
              }),
            },
            {
              id: "order_number",
              name: formatMessage({ id: "shipment.orderNumber", allowComponent: true }),
            },
          ]);
          if (field === undefined || field === null || field === "") {
            setField("number");
          }
        }
      });
    }
  };

  const { runAsync: run_userStoreHouses } = useFetch(userStoreHouses, {
    manual: true,
  });

  React.useEffect(
    () => {
      barcodeCodeRule()
      run_userStoreHouses({
        customerId: customerId,
        projectId: projectId,
        envId: envId,
        roleId: auth.project.permissions.role_id
      }).then((response) => {
        let list = [];
        if (response) {
          let data = response.data
          if (data !== null) {
            data.forEach(item => {
              list.push({ id: item.id, name: item.name })
            });
            if (list && list.length > 0) {
              let sh = list[0];
              setStorehouse(sh.id + '__' + sh.name);
            }
          }
        }
        setStorehouses(list);
      });
      setField((fields && fields.length > 0) ? fields[0].id : "number")
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  // eslint-disable-next-line
  React.useEffect(search, [
    storehouse,
    status,
    doSearch,
    page.currentPage,
    page.pageSize,
    isOpenPackage,
  ]);

  const showHistory = (id) => {
    history_ref.current.show("history.medicine", id, timeZone);
  };

  const rowSelection = {
    type: "checkbox",
    onChange: (selectedRowKeys, selectedRows) => {
      setSelecteds(selectedRows);
      setSelectedIds(selectedRowKeys);
    },
    selectedRows: selecteds,
    selectedRowKeys: selectedIds,
    getCheckboxProps: (record) => ({
      disabled:
        record.status !== 1 && record.status !== 6 && record.status !== 7 && record.status !== 14,
    }),
    preserveSelectedRowKeys: true,
  };

  const resetSelected = () => {
    setSelecteds([]);
    setSelectedIds([]);
  };

  function freezeMedicine(status) {
    var medicines = [];
    var medicineIds = [];
    //status 4隔离 6丢失/作废
    if (status === 4) { //隔离操作只隔离状态为1可用的药物
      selecteds.forEach((it) => {
        if (it.status === 1 || it.status === 14) {//隔离-可用和已冻结
          medicines.push(it);
          medicineIds.push(it.id);
        }
      });
    } else if (status === 6) {//丢失/作为操作只能操作状态为过期的药物
      selecteds.forEach((it) => {
        if (it.status === 1 || it.status === 7) {//丢失/作废-可用和已过期
          medicines.push(it);
          medicineIds.push(it.id);
        }
      });
    } else if (status === 1) { //设置为可用只能操作丢失/作废的药物
      selecteds.forEach((it) => {
        if (it.status === 6) {//可用-丢失/作废
          medicines.push(it);
          medicineIds.push(it.id);
        }
      });
    }
    if (medicineIds.length <= 0) {
      //message.warn(formatMessage({id: "menu.projects.project.build.randomization.tooltip"}))
      if (status === 4) {
        message.warn(formatMessage({ id: "single.freeze.freeze.info" }, { total: 0 }))
      } else if (status === 6) {
        message.warn(formatMessage({ id: "single.freeze.lost.info" }, { total: 0 }))
        // message.warn(formatMessage({ id: "single.freeze.lost.info.hint" }))
      } if (status === 1) {
        message.warn(formatMessage({ id: "single.freeze.setUse.info" }, { total: 0 }))
      }
    } else {
      //判断是否需要toast提示
      var toastFlag = false;
      if (selecteds.length > medicines.length) {
        toastFlag = true;
      }
      if (status === 1) { //可用
        make_available.current.show(medicines, medicineIds, 2, isOpenPackage, toastFlag, storehouses);
      } else { //丢失/作废  、隔离
        selected_freeze.current.show(medicines, medicineIds, 2, paramStorehouses, isOpenPackage, status, toastFlag, storehouses);
      }

    }
  }

  function canUseMedicine(record) {
    make_available.current.show([record], [record.id], 2, isOpenPackage, false, storehouses);
  }

  const { runAsync: run_downloadStorehouseSku, loading: downloadLoading } =
    useFetch(downloadStorehouseSku, { manual: true });

  function download() {
    // let key = escapeRegexSpecialChars(fieldValue);
    let key = fieldValue;
    if (fieldValue !== null && fieldValue !== undefined) {
      key = fieldValue.trim();
    }
    run_downloadStorehouseSku({
      customerId: customerId,
      projectId: projectId,
      envId: envId,
      storehouseId: storehouse ? storehouse.split("__")[0] : null,
      status: status,
      field: field,
      fieldValue: key,
      roleId: auth.project.permissions.role_id,
    }).then();
  }

  const refreshList = () => {
    search();
    resetSelected();
  };

  return (
    <React.Fragment>
      <Spin spinning={loading}>
        <Row justify="space-between" wrap={false}>
          <Col span={18}>
            <Form layout="inline">
              <Form.Item label={formatMessage({ id: "storehouse.name", allowComponent: true })}>
                <Select
                  value={paramStorehousesOp}
                  style={{ width: 300 }}
                  {...storehouseMode}
                  maxTagCount={1}
                  maxTagTextLength={12}
                  open={storehouseOpen}
                  onBlur={() => {
                    setStorehouseOpen(false)
                    if (paramStorehousesOp !== null) {
                      setDoSearch(doSearch + 1)
                    }
                  }

                  }
                  showArrow={true}
                  showSearch={false}
                  onDropdownVisibleChange={(visible) => setStorehouseOpen(visible)}
                  onChange={(value) => {
                    if (value === 1 || (Array.isArray(value) && (value.find((i) => i === 1) || value.length === 0))) {
                      if (storehouseMode.mode != null) {
                        setStorehouseMode({})
                        setStorehouseOpen(true)
                      }
                      setParamStorehouses([])
                      setParamStorehousesOp(1)
                      setDoSearch(doSearch + 1)
                      setStorehouseOpen(false)
                    } else {
                      setStorehouseOpen(true)
                      if (storehouseMode.mode !== "multiple") {
                        setStorehouseMode({ mode: "multiple", })
                        setDoSearch(doSearch + 1)
                        setStorehouseOpen(true)
                      }
                      if (!Array.isArray(value)) {
                        let siteIds = [value];
                        setParamStorehouses(siteIds)
                        setParamStorehousesOp(siteIds)
                        setDoSearch(doSearch + 1)
                      } else {
                        setParamStorehouses(value)
                        setParamStorehousesOp(value)
                        setDoSearch(doSearch + 1)
                      }
                    }
                  }}
                >
                  <Select.Option value={1}>{formatMessage({ id: "common.all", allowComponent: true })}</Select.Option>
                  {
                    storehouses.map(
                      it => (
                        <Select.Option key={it.id} value={it.id} >
                          {it.name}
                        </Select.Option>
                      )
                    )
                  }
                </Select>
                {/* <Select
                  style={{ minWidth: 160, maxWidth: 200 }}
                  dropdownStyle={{ maxWidth: 400 }}
                  dropdownMatchSelectWidth={false}
                  filterOption={(input, option) => {
                    const childrenText = option.props.children;
                    if (typeof childrenText === 'string') {
                      return childrenText.trim().toLowerCase().indexOf(input.trim().toLowerCase()) >= 0;
                    }
                    return false;
                  }}
                  allowClear
                  showSearch
                  value={storehouse}
                  onChange={(value) => {
                    page.setCurrentPage(1);
                    setStorehouse(value);
                    setSelectedIds([]);
                  }}
                >
                  {storehouses.map((it) => (
                    <Select.Option key={it.id} value={it.id + "__" + it.name}>
                      {it.name}
                    </Select.Option>

                  ))}
                </Select> */}
              </Form.Item>
              <Form.Item label={<FormattedMessage id={"common.status"} />}>
                <Select
                  style={{ width: 135 }}
                  allowClear
                  value={status}
                  onChange={(value) => {
                    page.setCurrentPage(1);
                    setStatus(value);
                  }}
                  placeholder={
                    <span style={{ color: "#1D2129" }}>
                      {formatMessage({ id: "common.all" })}
                    </span>
                  }
                  left={6}
                >
                  {medicineStatusAliData(
                    auth.codeRule,
                    auth.project.info.research_attribute,
                    ali
                  )
                    .filter((msd) => msd.value !== 0)
                    .map((it) => (
                      <Select.Option key={it.value} value={it.value}>
                        {it.label}
                      </Select.Option>
                    ))}
                </Select>
              </Form.Item>

              <Col span={8}>
                <Input.Group compact style={{ display: "flex" }}>
                  <Select
                    style={{ width: 130 }}
                    placeholder={
                      <FormattedMessage id="projects.statistics.selectField" />
                    }
                    value={field}
                    onChange={(value) => {
                      setFieldValue(null);
                      setFieldDate(null);
                      setField(value);
                    }}
                  >
                    {fields.map((it) => (
                      <Select.Option key={it.id} value={it.id}>
                        {it.name}
                      </Select.Option>
                    ))}
                  </Select>
                  {field === "expiration_date" ? (
                    <DatePicker
                      allowClear
                      value={fieldDate}
                      onChange={(e) => {
                        setFieldDate(e);
                        setFieldValue(e ? e.format("YYYY-MM-DD") : null);
                        page.setCurrentPage(1);
                        setDoSearch(doSearch + 1);
                      }}
                    />
                  ) : (
                    <Input.Search
                      placeholder={formatMessage({
                        id: "placeholder.input.common",
                      })}
                      ref={searchInputRef}
                      style={{ flex: 1 }}
                      value={fieldValue}
                      onChange={(e) => {
                        setFieldValue(e.target.value);
                      }}
                      allowClear
                      onSearch={() => {
                        searchInputRef?.current?.blur();
                        page.setCurrentPage(1);
                        setDoSearch(doSearch + 1);
                      }}
                    />
                  )}
                </Input.Group>
              </Col>
            </Form>
          </Col>
          <Space size={6}>
            {permissions(
              auth.project.permissions,
              "operation.supply.storehouse.medicine.download"
            ) && auth.project.info.research_attribute === 1 ? (
              <AuthButton loading={downloadLoading} onClick={() => download()}>
                <FormattedMessage id="common.download.data" />
              </AuthButton>
            ) : null}
            {permissions(
              auth.project.permissions,
              "operation.supply.storehouse.medicine.use"
            ) &&
              paramStorehouses != null &&
              paramStorehouses !== undefined &&
              projectStatus !== 2 ? (
              <AuthButton onClick={() => freezeMedicine(1)}>
                <FormattedMessage id="drug.list.setUse" />
              </AuthButton>
            ) : null}
            {permissions(
              auth.project.permissions,
              "operation.supply.storehouse.medicine.lost"
            ) &&
              paramStorehouses != null &&
              paramStorehouses !== undefined &&
              projectStatus !== 2 ? (
              <AuthButton onClick={() => freezeMedicine(6)}>
                <FormattedMessage id="medicine.status.lose" />
              </AuthButton>
            ) : null}
            {permissions(
              auth.project.permissions,
              "operation.supply.storehouse.medicine.freeze"
            ) &&
              paramStorehouses != null &&
              paramStorehouses !== undefined &&
              projectStatus !== 2 ? (
              <AuthButton type="primary" onClick={() => freezeMedicine(4)}>
                <FormattedMessage id="drug.list.isolation" />
              </AuthButton>
            ) : null}
          </Space>
        </Row>

        <VirtualTable
          className="mar-top-10"
          dataSource={summaryList}
          pagination={false}
          scroll={{ x: summaryList.length > 0 ? "100%" : false }}
          sticky
          style={{ maxHeight: "calc(100vh - 245px)", overflowY: "auto" }}
          rowKey={(record) => record.id}
          rowSelection={rowSelection}
          height={() => document.documentElement.clientHeight - 295}
        >
          {/*先去掉，影响数据加载速度 <Table.Column title={<FormattedMessage id="storehouse.name" />}
                        dataIndex="storehouseName"
                        key="storehouseName" 
                        //ellipsis 
                        width={220} 
                        render={(text,record,index) =>{
                            return  <TooltipParagraph outStyle={{ width: 200 }} tooltip={text}>
                                        {text}
                                </TooltipParagraph>;
                        }}/> */}
          <Table.Column title={<FormattedMessage id="storehouse.name" />}
            className="table-column-padding-left-16-1"
            dataIndex="storehouseName"
            key="storehouseName" ellipsis
          />
          {
            isOpenPackage &&
            <Table.Column title={<FormattedMessage id="drug.medicine.packlist" />}
              key="packageNumber"
              dataIndex="packageNumber"
              ellipsis
              render={(text, record, index) => {
                return (
                  record["packageNumber"] ?
                    record["packageNumber"]
                    :
                    "-"
                );
              }} />
          }
          <Table.Column
            title={<FormattedMessage id="drug.list.drugNumber" />}
            dataIndex="number" key="number" ellipsis
            className="table-column-padding-left-32"
          />
          <Table.Column
            title={<FormattedMessage id="drug.configure.drugName" />}
            dataIndex="name" key="name" ellipsis
            className="table-column-padding-left-32"
          />
          <Table.Column
            title={<FormattedMessage id="drug.list.batch" />}
            dataIndex="batchNumber"
            key="batchNumber" ellipsis
            className="table-column-padding-left-32"
          />
          <Table.Column title={<FormattedMessage id="projects.statistics.sku.expirationDate" />}
            dataIndex="expirationDate"
            key="expirationDate" ellipsis width={122}
            className="table-column-padding-left-32" />
          {/*<Table.Column title={<FormattedMessage id="projects.statistics.sku.place" />}*/}
          {/*dataIndex="place" key="place" width={300}*/}
          {/*ellipsis />*/}
          <Table.Column title={<FormattedMessage id="shipment.orderNumber" />}
            dataIndex="orderNumber"
            width={152}
            className="table-column-padding-left-32"
            ellipsis render={(_, record) =>
              <TitledPopover
                title={formatMessage({ id: 'shipment.order.all-no' })}
                data={record.orderNumber}
                iconLink='icon-quanbudingdanhao'
                contentLineHeight='32px'
              />
            } />
          <Table.Column title={<FormattedMessage id="projects.statistics.sku.status" />}
            dataIndex="status"
            width={146}
            className="table-column-padding-left-32"
            key="status" ellipsis render={(_, record) => {
              return <Badge color={medicineStatusColors[record.status]}
                text={medicineStatusAliData(auth.codeRule, auth.project.info.research_attribute, ali).find(it => (it.value === record.status))?.label} />;
            }} />
          {codeRule &&
            <>
              <Table.Column title={<FormattedMessage id="barcode" />} key="number"
                dataIndex="number" align="center" ellipsis width={150}
                render={(value, record) => {
                  return (
                    <Popover content={
                      <>
                        <Barcode value={value} displayValue={false} height={60} width={1}
                          format="CODE128" />
                        <br />
                        <span>&nbsp;&nbsp;{value}</span>
                      </>
                    } trigger="click">
                      {
                        record.shortCode != "-" ?
                          <AuthButton
                            style={{ padding: 0, marginRight: '12px' }}
                            size="small"
                            type="link">
                            <FormattedMessage id="form.preview" />
                          </AuthButton>
                          :
                          "-"
                      }
                    </Popover>
                  )
                }} />
              <Table.Column title={<FormattedMessage id="packageBarcode" />} key="packageNumber"
                dataIndex="packageNumber" align="center" ellipsis width={150}
                render={(value, record) => {
                  return (
                    <Popover content={
                      <>
                        <Barcode value={value} displayValue={false} height={60} width={1}
                          format="CODE128" />
                        <br />
                        <span>&nbsp;&nbsp;{value}</span>
                      </>
                    } trigger="click">
                      {
                        record.shortCode !== "-" && record.packageNumber !== "-" ?
                          <AuthButton
                            style={{ padding: 0, marginRight: '12px' }}
                            size="small"
                            type="link">
                            <FormattedMessage id="form.preview" />
                          </AuthButton>
                          :
                          "-"
                      }
                    </Popover>
                  )
                }} />
            </>}
          <Table.Column
            title={<FormattedMessage id="common.operation" />}
            width={200}
            className="table-column-padding-left-16-2"
            fixed="right"
            render={
              (_, record) => {
                const btns = [];
                if (permissions(auth.project.permissions, "operation.supply.storehouse.medicine.history")) {
                  btns.push(
                    <AuthButton
                      style={{ padding: 0 }}
                      size="small"
                      type="link"
                      onClick={() => showHistory(record.id)}>
                      <FormattedMessage id="common.history" />
                    </AuthButton>
                  )
                }
                if (record.status === 6 && permissions(auth.project.permissions, "operation.supply.storehouse.medicine.use")) {
                  btns.push(<AuthButton
                    style={{ padding: 0 }}
                    size="small"
                    type="link"
                    onClick={() => canUseMedicine(record)}>
                    <FormattedMessage id="drug.list.setUse" />
                  </AuthButton>)
                }
                return InsertDivider(btns)

              }
            }
          />
        </VirtualTable>
        {
          summaryList?.length ?
            <Footer>
              <PaginationView mode="SELECTABLE" selectedNumber={selectedIds?.length} clearDisplay={true} refresh={refreshList} />
            </Footer> : null
        }
      </Spin>
      <HistoryList bind={history_ref} permission={permissions(auth.project.permissions, "operation.supply.storehouse.medicine.print")} />
      <PageContextProvider>
        <SelectedFreeze bind={selected_freeze} search={search} refresh={refreshList} />
      </PageContextProvider>
      <MakeAvailable bind={make_available} search={search} refresh={refreshList} />
      <Reason bind={reason_ref} refresh={refreshList} />
    </React.Fragment>
  )
}
