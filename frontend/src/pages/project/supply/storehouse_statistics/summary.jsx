import React from "react";
import { Col, Collapse, Empty, Form, Row, Select, Spin, Table, Tag, Tooltip } from "antd";
import { useSafeState } from "ahooks";
import { useFetch } from "../../../../hooks/request";
import { medicineSummaryStorehouse } from "../../../../api/medicine";
import { useAuth } from "../../../../context/auth";
import { medicineStatusAliData } from "../../../../tools/medicine_status";
import { PaginationView } from "../../../../components/pagination";
import { usePage } from "../../../../context/page";
import { CaretDownOutlined, CaretRightOutlined } from '@ant-design/icons';
import { getProjectAttributeConnect } from "../../../../api/randomization";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";


export const Summary = (props) => {

    const page = usePage();
    const [loading, setLoading] = useSafeState(false);
    const [storehouseList, setStorehouseList] = useSafeState([]);
    const [defaultKeys, setDefaultKeys] = useSafeState([]);
    const [tableHeight, setTableHeight] = useSafeState(300);
    const [selectStorehouse, setSelectStorehouse] = useSafeState([]);
    const [ali, setAli] = useSafeState(0);
    const auth = useAuth()
    const envId = auth.env ? auth.env.id : null;
    const projectId = auth.project.id;
    const customerId = auth.customerId;
    const { runAsync: run_medicineSummaryStorehouse } = useFetch(medicineSummaryStorehouse, { manual: true })
    const { runAsync: getProjectAttributeConnectRun, loading: getProjectAttributeConnectLoading } = useFetch(getProjectAttributeConnect, { manual: true })
    const {formatMessage} = useTranslation()

    const search = () => {
        setLoading(true);
        run_medicineSummaryStorehouse(
            {
                customerId: customerId,
                projectId: projectId,
                envId: envId,
                storehouseId: selectStorehouse,
                roleId: auth.project.permissions.role_id,
                start: (page.currentPage - 1) * page.pageSize,
                limit: page.pageSize
            }).then((result) => {
                let response = result.data
                let list = []
                if (response.items) {
                    page.setTotal(response.total);
                    response.items.forEach(storehouse => {
                        let drugs = [];
                        let batchMaps = {};
                        let otherDrugs = [];
                        let tmpStorehouse = { id: storehouse._id, name: storehouse.storehouse.name }
                        storehouse.medicine_others.forEach(info => {
                            let item = info._id;
                            let findIndex = otherDrugs.findIndex(drug => drug.name === item.name);
                            if (findIndex !== -1) {
                                otherDrugs[findIndex][item.status] = otherDrugs[findIndex][item.status] ? { count: otherDrugs[findIndex][item.status].count + info.count } : { count: info.count };;
                            } else {
                                let tmp = { name: item.name, id: "other", spec: item?.spec, "isOpenPackage": item.isOpenPackage }
                                tmp[item.status] = { count: info.count };
                                otherDrugs.push(tmp);
                            }

                            let batchs = []
                            if (!batchMaps[item.name + "other"]) {
                                batchMaps[item.name + "other"] = batchs;
                            } else {
                                batchs = batchMaps[item.name + "other"];
                            }
                            findIndex = batchs.findIndex(batch => batch.name === item.name && batch.batchNumber === item.batch_number && batch.id === "other");
                            if (findIndex !== -1) {
                                batchs[findIndex][item.status] = batchs[findIndex][item.status] ? { count: batchs[findIndex][item.status].count + info.count } : { count: info.count };
                            } else {
                                let tmp = { name: item.name, id: "other", batchNumber: item.batch_number, "isOpenPackage": item.isOpenPackage }
                                tmp[item.status] = { count: info.count };
                                batchs.push(tmp);
                            }
                        });
                        storehouse.medicine.forEach(medicine => {
                            let item = medicine._id
                            let findIndex = drugs.findIndex(drug => drug.name === item.name && drug.spec === item.spec);
                            if (findIndex !== -1) {
                                drugs[findIndex][item.status] = drugs[findIndex][item.status] ? { count: drugs[findIndex][item.status].count + medicine.count } : { count: medicine.count };
                            } else {
                                let tmp = { name: item.name, id: "", spec: item?.spec }
                                tmp[item.status] = { count: medicine.count };
                                drugs.push(tmp);
                            }
                            let batchs = []
                            if (!batchMaps[item.name + " " + item.spec]) {
                                batchMaps[item.name + " " + item.spec] = batchs;
                            } else {
                                batchs = batchMaps[item.name + " " + item.spec];
                            }
                            findIndex = batchs.findIndex(batch => batch.name === item.name && batch.batchNumber === item.batch_number && batch?.spec === item?.spec);
                            if (findIndex !== -1) {
                                batchs[findIndex][item.status] = batchs[findIndex][item.status] ? { count: batchs[findIndex][item.status].count + medicine.count } : { count: medicine.count };
                            } else {
                                let tmp = { name: item.name, id: "", batchNumber: item.batch_number, spec: item?.spec }
                                tmp[item.status] = { count: medicine.count };
                                batchs.push(tmp);
                            }
                        });
                        drugs.sort((a, b) => a.name.localeCompare(b.name, 'zh-CN'))
                        tmpStorehouse.drugs = drugs.concat(otherDrugs);
                        tmpStorehouse.batchs = batchMaps;
                        list.push(tmpStorehouse);
                    });
                    setLoading(false);
                }
                setDefaultKeys(list.map(e => e.id));
                setStorehouseList(list);
            });

        // if(auth.project.info.type === 1){
        getProjectAttributeConnectRun({ projectId: projectId, envId: envId }).then((res) => {
            if (res.data) {
                if (res.data.info.connectAli) {
                    setAli(1)
                }
            }
        });
        // }
    }

    // const setOtherDrugList = (listData, findIndex, item, batchNumber) => {
    //     if (findIndex !== -1) {
    //         listData[findIndex][1] = listData[findIndex][1] ? listData[findIndex][1] + item.count : item.count;
    //         listData[findIndex][2] = listData[findIndex][2] ? listData[findIndex][2] + item.to_be_send_count : item.to_be_send_count;
    //         listData[findIndex][3] = listData[findIndex][3] ? listData[findIndex][3] + item.in_transit_count : item.in_transit_count;
    //         listData[findIndex][4] = listData[findIndex][4] ? listData[findIndex][4] + item.quarantined_count : item.quarantined_count;
    //         listData[findIndex][5] = listData[findIndex][5] ? listData[findIndex][5] + item.used_count : item.used_count;
    //         listData[findIndex][6] = listData[findIndex][6] ? listData[findIndex][6] + item.lost_count : item.lost_count;
    //         listData[findIndex][7] = listData[findIndex][7] ? listData[findIndex][7] + item.expired_count : item.expired_count;
    //         listData[findIndex][11] = listData[findIndex][11] ? listData[findIndex][11] + item.to_be_confirm_count : item.to_be_confirm_count;
    //         listData[findIndex][14] = listData[findIndex][14] ? listData[findIndex][14] + item.frozen_count : item.frozen_count;
    //         listData[findIndex][20] = listData[findIndex][20] ? listData[findIndex][20] + item.locked_count : item.locked_count;
    //     } else {
    //         let tmp = { name: item.name, id: "other" }
    //         if (batchNumber) {
    //             tmp.batchNumber = batchNumber
    //         }
    //         tmp[1] = item.count;
    //         tmp[2] = item.to_be_send_count;
    //         tmp[3] = item.in_transit_count;
    //         tmp[4] = item.quarantined_count;
    //         tmp[5] = item.used_count;
    //         tmp[6] = item.lost_count;
    //         tmp[7] = item.expired_count;
    //         tmp[11] = item.to_be_confirm_count;
    //         tmp[14] = item.frozen_count;
    //         tmp[20] = item.locked_count;
    //         listData.push(tmp);
    //     }
    // }

    React.useEffect(
        () => {
            setTableHeight(document.documentElement.clientHeight - 330);
        },
        []
    );

    // eslint-disable-next-line
    React.useEffect(
        search,
        // [selectStorehouse]
        [selectStorehouse, page.currentPage, page.pageSize]
    );





    const onExpandDetail = (it, drug) => {
        <Table
            dataSource={it.batchs[drug.name + (drug.id !== "" ? drug.id : " " + drug.spec)]}
            // size="small"
            width="100%"
            pagination={false}
            // style={{
            //     maxHeight: tableHeight,
            //     overflowX: "scroll"
            // }}
            className="ant-custom-table"
            style={{ overflowX: "auto", marginLeft: "12px" }}
            rowKey={(record) => (record.id)}
        >
            <Table.Column
                title={<FormattedMessage id="drug.list.batch" />}
                dataIndex="batchNumber"
                key="batchNumber"
                ellipsis
                width={250}
                render={(v) => (v ? v : "-")}
            />
            {
                medicineStatusAliData(auth.codeRule, auth.project.info.research_attribute, ali).map(status =>
                (
                    <Table.Column
                        title={status.label}
                        dataIndex={status.value}
                        key={status.value}
                        width={120}
                        render={(value, record, index) =>
                            record[status.value] ? record[status.value].count : 0
                        }
                    />
                )
                )
            }
        </Table>
    }


    const expandedDetail = (it, drug) => {
        return <Spin spinning={false}>
            <Table
                dataSource={it.batchs[drug.name + (drug.id !== "" ? drug.id : " " + drug.spec)]}
                // size="small"
                width="100%"
                pagination={false}
                // style={{
                //     maxHeight: tableHeight,
                //     overflowX: "scroll"
                // }}
                className="ant-custom-table"
                style={{ overflowX: "auto", marginLeft: "12px" }}
                rowKey={(record) => (record.id)}
            >
                <Table.Column
                    title={<FormattedMessage id="drug.list.batch" />}
                    dataIndex="batchNumber"
                    key="batchNumber"
                    ellipsis
                    width={250}
                    render={(v) => (v ? v : "-")}
                />
                <Table.Column
                    title={<FormattedMessage id="drug.configure.spec" />}
                    dataIndex="spec"
                    key="spec"
                    ellipsis
                    width={140}
                />
                {
                    medicineStatusAliData(auth.codeRule, auth.project.info.research_attribute, ali).map(status =>
                    (
                        <Table.Column
                            title={status.label}
                            dataIndex={status.value}
                            key={status.value}
                            width={120}
                            render={(value, record, index) =>
                                record[status.value] ? record[status.value].count : 0
                            }
                        />
                    )
                    )
                }
            </Table>
        </Spin>

            ;
    }

    return (
        <React.Fragment>
            <Row gutter={8}>
                <Col xs={24} sm={24} md={24} lg={24} className="mar-ver-5">
                    <Form.Item label={<span style={{ color: '#1D2129' }}><FormattedMessage id="storehouse.name" /></span>}>
                        <Select style={{ width: "200px", }}
                            dropdownStyle={{ maxWidth: 400 }}
                            dropdownMatchSelectWidth={false}
                            allowClear showSearch={true} mode="multiple" value={selectStorehouse}
                            // 如果发现下拉菜单跟随页面滚动，或者需要在其他弹层中触发 Select，请尝试使用 getPopupContainer={triggerNode => triggerNode.parentElement} 将下拉弹层渲染节点固定在触发器的父元素中。
                            getPopupContainer={(triggerNode) => triggerNode.parentElement}
                            filterOption={(input, option) => {
                                const childrenText = option.props.children;
                                if (typeof childrenText === 'string') {
                                    return childrenText.trim().toLowerCase().indexOf(input.trim().toLowerCase()) >= 0;
                                }
                                return false;
                            }}
                            optionFilterProp="children"
                            onChange={setSelectStorehouse}
                            showArrow={true}
                            placeholder={<span style={{ color: "#1D2129" }}>{formatMessage({id: 'common.all'})}</span>}
                        >
                            {
                                props.storehouses.map(
                                    it => (
                                        <Select.Option key={it.id} value={it.id}>
                                            {/* <Tooltip title={it.name}>{it.name}</Tooltip> */}
                                            {it.name}
                                        </Select.Option>
                                    )
                                )
                            }
                        </Select>
                    </Form.Item>
                </Col>
                {
                    storehouseList.length ?
                        <React.Fragment>
                            <Col xs={24} sm={24} md={24} lg={24}>
                                <Spin spinning={loading}>
                                    <div className="custom-collapse">


                                        <Collapse key="storehouses"
                                            // defaultActiveKey={defaultKeys}
                                            activeKey={defaultKeys}
                                            // expandIconPosition={false}
                                            expandIcon={() => null}
                                            accordion={false}
                                            bordered={false}
                                            className="ant-collapse ant-custom-item"
                                        >
                                            {
                                                storehouseList.map(it => (
                                                    <Collapse.Panel
                                                        header={it.name}
                                                        key={it.id}
                                                        defaultActive={false}
                                                        className="custom-collapse"

                                                    >
                                                        {
                                                            // it.drugs && it.drugs.length ?

                                                            <Table
                                                                dataSource={(it.drugs && it.drugs.length) ? it.drugs : []}
                                                                // size="small"
                                                                width="100%"
                                                                pagination={false}
                                                                // style={{
                                                                //     maxHeight: tableHeight,
                                                                //     overflowX: "scroll"
                                                                // }}
                                                                className="ant-custom-thead"
                                                                style={{ overflowX: "auto" }}
                                                                rowKey={(record) => (it.id + record.name + " " + record.spec)}
                                                                expandable={{
                                                                    expandedRowRender: record => expandedDetail(it, record),
                                                                    expandIcon: ({ expanded, onExpand, record }) =>
                                                                        expanded ? (
                                                                            <CaretDownOutlined onClick={e => onExpand(record, e)} />
                                                                        ) : (
                                                                            <CaretRightOutlined onClick={e => onExpand(record, e)} />
                                                                        ),
                                                                    onExpand: (expanded, record) => onExpandDetail(it, record),
                                                                    columnWidth: 30,
                                                                    // fixed:"left"
                                                                }}
                                                            >
                                                                <Table.Column
                                                                    title={<FormattedMessage id="drug.list.name" />}
                                                                    dataIndex="name"
                                                                    key="name"
                                                                    ellipsis
                                                                    width={250}
                                                                />
                                                                <Table.Column
                                                                    title={<FormattedMessage id="drug.configure.spec" />}
                                                                    dataIndex="spec"
                                                                    key="spec"
                                                                    ellipsis
                                                                    width={140}
                                                                />
                                                                {
                                                                    medicineStatusAliData(auth.codeRule, auth.project.info.research_attribute, ali).map(item =>
                                                                    (
                                                                        <Table.Column
                                                                            title={item.label}
                                                                            dataIndex={item.value}
                                                                            key={item.value}
                                                                            width={140}
                                                                            render={(value, record, index) =>
                                                                                record[item.value] ? record[item.value].count : 0
                                                                            }
                                                                        />
                                                                    )
                                                                    )
                                                                }
                                                            </Table>
                                                        }
                                                    </Collapse.Panel>
                                                ))
                                            }
                                        </Collapse>
                                    </div>
                                </Spin>
                            </Col>
                        </React.Fragment>
                        :
                        <Col xs={24} sm={24} md={24} lg={24}>
                            <Empty
                                image={Empty.PRESENTED_IMAGE_SIMPLE}
                                description={<FormattedMessage id="projects.storehouse.no.storehouse" />}
                            />
                        </Col>
                }
            </Row>
            <PaginationView />

        </React.Fragment>
    )
};