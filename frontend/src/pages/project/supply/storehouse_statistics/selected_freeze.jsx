import React from "react";
import { Col, Form, Input, message, Modal, Row, Table, Select } from "antd";
import { FormattedMessage, useIntl } from "react-intl";
import { useFetch } from "../../../../hooks/request";
import { getMedicineFreezeByIds, updateMedicineStatus, updateStatus } from "../../../../api/medicine";
import { useSafeState } from "ahooks";
import { useAuth } from "../../../../context/auth";
import { Title } from "components/title";
import { combineRow } from "../../../../utils/merge_cell";
import { InfoTips } from "../../../../components/tips";
import { medicineStatusData } from "../../../../tools/medicine_status";
import { fillTableCellEmptyPlaceholder } from "../../../../components/table";
import { getProjectAttribute } from "../../../../api/randomization";

export const SelectedFreeze = (props) => {
    const [visible, setVisible] = useSafeState(false);
    const [medicineIds, setMedicineIds] = useSafeState([]);
    const [medicines, setMedicines] = useSafeState([]);
    const [instituteType, setInstituteType] = useSafeState(null);
    const [instituteId, setInstituteId] = useSafeState(null);
    const [storehouses, setStorehouses] = useSafeState([]);
    const [form] = Form.useForm();
    const intl = useIntl();
    const { formatMessage } = intl;
    const [submitting, setSubmitting] = useSafeState(false);
    const [packageIsOpen, setPackageIsOpen] = useSafeState(false);
    const [title, setTitle] = useSafeState("");
    const [content, setContent] = useSafeState("");
    const [operStatus, setOperStatus] = useSafeState(null);
    const [attribute, setAttribute] = useSafeState(null);
    const [reasonType, setReasonType] = useSafeState(-1);
    const auth = useAuth()
    const projectId = auth.project ? auth.project.id : null;
    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId;

    const show = (data, ids, instituteType, instituteId, packageIsOpen, status, toastFlag, storehouses) => {
        form.resetFields()
        setStorehouses(storehouses)
        setVisible(true)
        setPackageIsOpen(packageIsOpen)
        if (data.length > 0) {
            setMedicines(data);
        }
        var length = 0;
        if (ids != null && ids.length > 0) {
            length = ids.length
        }
        if (status === 4) {
            setTitle(formatMessage({ id: 'drug.list.isolation' }))
            setContent(formatMessage({ id: "single.freeze.info" }))
            if (toastFlag) {
                message.warn(formatMessage({ id: "single.freeze.freeze.info" }, { total: length }))
            }
        } else {
            setTitle(formatMessage({ id: 'medicine.status.lose' }))
            setContent(formatMessage({ id: "single.lost.info" }))
            if (toastFlag) {
                message.warn(formatMessage({ id: "single.freeze.lost.info" }, { total: length }))
            }
        }
        setOperStatus(status);
        if (ids.length > 0) {
            //根据ids查询数据,重新赋值数据
            if (packageIsOpen) {
                search(ids, status)
            } else {
                setMedicineIds(ids);
            }
        }
        if (instituteType) {
            setInstituteType(instituteType);
        }
        if (instituteId) {
            setInstituteId(instituteId);
        }
        getAttribute(projectId, envId, "", customerId);
    };

    const getAttribute = (projectId, envId, cohortId, customerId) => {
        getProjectAttributeRun({ projectId, env: envId, cohort: cohortId, customer: customerId }).then(
            (result) => {
                setAttribute(result.data);
            }
        )
    }
    const { runAsync: getProjectAttributeRun } = useFetch(getProjectAttribute, { manual: true })
    const { runAsync: run_getMedicineFreezeByIds } = useFetch(getMedicineFreezeByIds, { manual: true })
    const search = (ids, operStatus) => {
        var status = 1
        if (operStatus === 6) {
            status = 7
        }
        run_getMedicineFreezeByIds({
            customerId: customerId,
            projectId: projectId,
            envId: envId,
            medicineIds: ids,
            status: status
        }).then((result) => {
            const data = result.data
            setMedicineIds(data.ids)
            var tableData = combineRow(data.medicines, "packageNumber", "package_number", true)
            tableData = combineRow(data.medicines, "name", "name", false)
            tableData = fillTableCellEmptyPlaceholder(tableData)
            setMedicines(tableData)
        });
    }

    const hide = () => {
        setVisible(false);
        setSubmitting(false);
        setReasonType(-1);
        form.resetFields();
        props.search();
    };


    const save = () => {
        if (operStatus === 4) {
            confirmFreeze();
        } else if (operStatus === 6) {
            confirmLost();
        }
    }

    const { runAsync: run_updateStatus } = useFetch(updateStatus, { manual: true });
    const confirmFreeze = () => {
        form.validateFields().then(values => {
            setSubmitting(true);
            run_updateStatus(
                {
                    customerId: customerId,
                    projectId: projectId,
                    envId: envId,
                    instituteType: instituteType,
                    //instituteId: instituteId,
                    medicineIds: medicineIds,
                    reason: values.reason,
                    status: 4,
                    remark: values.remark,
                }).then((resp) => {
                    message.success(resp.msg);
                    hide();
                    props.refresh();
                }, () => {
                    setSubmitting(false);
                });
        }).catch(errorInfo => {
            // 验证失败时的操作
            setSubmitting(false);
            console.log('Validation failed:', errorInfo);
            if (errorInfo.errorFields.length > 0) {
              // 获取第一个出错字段的名字
              const firstErrorFieldName = errorInfo.errorFields[0].name.join('.');
              scrollToField(firstErrorFieldName);
            }
        })
    }

    const scrollToField = fieldKey => {
        const labelNode = document.querySelector(`label[for="${fieldKey}"]`);
        if (labelNode) {
          labelNode.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    };

    const { runAsync: run_updateMedicineStatus } = useFetch(updateMedicineStatus, { manual: true })
    //丢失/作废
    const confirmLost = () => {
        form.validateFields().then(values => {
            setSubmitting(true);
            run_updateMedicineStatus(
                {
                    customerId: customerId,
                    projectId: projectId,
                    envId: envId,
                    instituteType: instituteType,
                    //   instituteId: instituteId,
                    medicineIds: medicineIds,
                    reason: values.reason,
                    status: 6
                }).then((resp) => {
                    message.success(resp.msg);
                    hide();
                    props.refresh();
                }, () => {
                    setSubmitting(false);
                });
        }).catch(errorInfo => {
            // 验证失败时的操作
            setSubmitting(false);
            console.log('Validation failed:', errorInfo);
            if (errorInfo.errorFields.length > 0) {
              // 获取第一个出错字段的名字
              const firstErrorFieldName = errorInfo.errorFields[0].name.join('.');
              scrollToField(firstErrorFieldName);
            }
        })
    }

    const { TextArea } = Input;

    React.useImperativeHandle(props.bind, () => ({ show }));

    return (
        <Modal
            className='custom-large-modal'
            title={title}
            visible={visible}
            onCancel={hide}
            maskClosable={false}
            centered
            destroyOnClose
            okButtonProps={{ loading: submitting }}
            okText={formatMessage({ id: 'common.ok' })}
            onOk={save}
        >
            <Row gutter={[16, 16]}>
                <Col span={24}>
                    <div style={{ marginBottom: 12 }}>
                        <Title name={formatMessage({ id: "drug.list.drugNumber" })} />
                    </div>
                    <InfoTips style={{ marginBottom: 24 }} content={content} />
                    <Table
                        style={{ marginTop: 12 }}
                        size="small"
                        dataSource={medicines}
                        pagination={false}
                        rowKey={(record) => (record.id)}
                    >
                        {packageIsOpen ?
                            <Table.Column title={<FormattedMessage id="storehouse.name" />}
                                dataIndex="storehouseId"
                                key="storehouseId" ellipsis
                                render={(value) => storehouses.find(it => it.id === value)?.name}
                            />
                            : <Table.Column title={<FormattedMessage id="storehouse.name" />}
                                dataIndex="storehouseName"
                                key="storehouseName" ellipsis
                            />
                        }

                        {/* <Table.Column title={<FormattedMessage id="common.serial"/>} 
                            dataIndex="#" key="#" width={70}
                            render={(text, record, index) => (index + 1)}   
                            onCell={(_, index) => {
                                return {rowSpan : medicines[index].package_numberRowSpan}
                            }}/> */}
                        {packageIsOpen ?
                            <Table.Column
                                title={<FormattedMessage id="drug.configure.drugName" />}
                                dataIndex={"name"}
                                key="name"
                                ellipsis
                                align="left"
                                onCell={(_, index) => {
                                    return { rowSpan: medicines[index].nameRowSpan }
                                }}
                            />
                            :
                            <Table.Column
                                title={<FormattedMessage id="drug.configure.drugName" />}
                                dataIndex={"name"}
                                key="name"
                                ellipsis
                                align="left" />
                        }
                        {packageIsOpen && <Table.Column
                            title={<FormattedMessage id="drug.medicine.packlist" />}
                            dataIndex={"packageNumber"}
                            key="packageNumber"
                            ellipsis
                            align="left"
                            render={(value, record, index) => {
                                let newValue = ""
                                if (value !== undefined && value !== "") {
                                    newValue = value
                                } else {
                                    newValue = "-"
                                }
                                let obj = {
                                    children: newValue,
                                    props: { rowSpan: medicines[index].package_numberRowSpan }
                                }
                                return obj
                            }}
                        />}
                        <Table.Column title={formatMessage({ id: 'drug.list.drugNumber' })} key="number" width={150}
                            dataIndex="number" align="left" ellipsis />
                        <Table.Column title={formatMessage({ id: 'drug.list.expireDate' })} key="expirationDate" width={100}
                            dataIndex="expirationDate" align="left" ellipsis />
                        <Table.Column title={formatMessage({ id: 'drug.list.batch' })} key="batchNumber" width={150}
                            dataIndex="batchNumber" align="left" ellipsis />
                        <Table.Column title={formatMessage({ id: 'drug.list.status' })} key="status" width={150}
                            dataIndex="status" align="left" ellipsis
                            render={(_, record) => (
                                medicineStatusData(auth.codeRule, auth.project.info.research_attribute)
                                    .find(it => it.value === record.status)?.label
                            )}
                        />
                    </Table>
                </Col>
                <Col span={24}>
                    <Form form={form} layout="horizontal">
                        <Form.Item label={formatMessage({ id: 'drug.freeze.reason' })} rules={[{ required: true }]} name="reason"
                            className="mar-ver-5">
                            {operStatus === 4 && attribute?.info?.freezeReasonConfig !== null && attribute?.info?.freezeReasonConfig.length > 0 ?
                                <Select onChange={(e) => {
                                    setReasonType(e)
                                }}>
                                    {attribute?.info?.freezeReasonConfig.map((v) => <Select.Option key={v.reason} value={v.reason}>{v.reason}</Select.Option>)}
                                </Select> :
                                <TextArea placeholder={formatMessage({ id: 'placeholder.input.common' })} allowClear showCount maxLength={500} />
                            }
                        </Form.Item>
                        {
                            operStatus === 4 && attribute?.info?.freezeReasonConfig !== null && attribute?.info?.freezeReasonConfig.find((e) => e.reason === reasonType)?.allowRemark ?
                                <Form.Item label={formatMessage({ id: 'subject.unblinding.reason.remark' })} name="remark" rules={[{ required: true }]} >
                                    <Input.TextArea allowClear className="full-width" ></Input.TextArea>
                                </Form.Item> : null
                        }
                    </Form>
                </Col>
            </Row>
        </Modal>
    );
};


