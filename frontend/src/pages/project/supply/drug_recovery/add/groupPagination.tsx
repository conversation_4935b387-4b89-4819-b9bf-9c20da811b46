import { But<PERSON>, Pagination, Select } from "antd";
import { useMemo } from "react";
import {useTranslation} from "../../../../common/multilingual/component";
import { usePage } from "../../../../../context/page";
import styled from "@emotion/styled";
import { useSafeState } from "ahooks";
import { InsertDivider } from "../../../../../components/divider";
import React from "react";

type MODE = "NORMAL" | "SELECTABLE"
type PaginationViewProp = {
	mode?: MODE;
	// 只有在SELECTABLE下起效
	selectedNumber?: number;
	// 页码器的大小
	pageSizeOptions?: number[];

	// 分组总数
	groupTotal: number;

	empty: boolean;
	setEmpty: any
}
export const GroupPaginationView = (prop: PaginationViewProp) => {
	const ctx = usePage();
	const intl = useTranslation();
	const [dropDownVisible, setDropDownVisible] = useSafeState(false);
	const pageOptions = useMemo(() => {
		let opts = [10, 20, 50, 100];
		if (prop.pageSizeOptions) {
			opts = prop.pageSizeOptions;
		}
		return (
			<Select
				style={{ fontSize: 12 }}
				value={ctx.pageSize}
				suffixIcon={dropDownVisible ? <UpTriangleIcon /> : <DownTriangleIcon />}
				onDropdownVisibleChange={v => setDropDownVisible(v)}
				onSelect={(v: number) => {
					ctx.setCurrentPage(1);
					ctx.setPageSize(v);
				}}
				size={"small"}
			>
				{opts.map(item => <Select.Option style={{ fontSize: 12 }} key={item} value={item}>{item}</Select.Option>)}
			</Select>
		)
	}, [ctx, dropDownVisible, setDropDownVisible])
	// 页码切换
	const pageSizeEl = useMemo(() => {
		return (
			<span style={{ color: "#666666", fontSize: 12 }}>
				{!prop.mode &&
					<span>{intl.formatMessage({ id: "common.pagination.tip.total" }, { total: ctx.total })}</span>}
				<span>
					{intl.formatMessage({ id: "common.pagination.tip.pagesize-prefix" })}
					<span style={{ margin: "0px 4px" }}>{pageOptions}</span>
					<span>{intl.formatMessage({ id: "common.pagination.tip.pagesize-suffix" })}</span>
				</span>
			</span>)
	}, [pageOptions, ctx.total, intl, prop.mode]);
	// 已选中的条数
	const selectedMsgEl = useMemo(() => {
		if (prop.mode !== "SELECTABLE") return <></>;
		if (!prop.selectedNumber) return <></>;
		const selectedNum = prop.selectedNumber ? prop.selectedNumber : 0
		return InsertDivider([
			<div style={{ color: "#666666", fontSize: 12 }}>
				{intl.formatMessage({ id: "common.pagination.seleted" })}<span
					style={{
						color: "#1D2129",
						fontWeight: 400
					}}> {selectedNum} </span>{intl.formatMessage({ id: "common.pagination.record" })}，{intl.formatMessage({ id: "common.pagination.all" })}<span
						style={{
							color: "#1D2129",
							fontWeight: 400
						}}> {prop.groupTotal} </span> {intl.formatMessage({ id: "common.pagination.record" })}
			</div>,
			<Button
				size={"small"}
				type={"link"}
				style={{ paddingLeft: 0, fontSize: 12 }}
				onClick={() => {
					prop.setEmpty(!prop.empty);
				}}
			>{intl.formatMessage({ id: "common.pagination.empty" })}</Button>
		])


	}, [prop.mode, prop.selectedNumber, ctx.total])
	return (
		<div style={{ marginTop: 8, display: "flex", alignItems: "center" }}>
			{selectedMsgEl}
			{ctx.total > 10 && <div style={{ display: "flex", alignItems: "center", marginLeft: "auto", }}>
				<span style={{ marginRight: 4 }}>{pageSizeEl}</span>
				<StyledPagination
					hideOnSinglePage={false}
					showSizeChanger={false}
					showQuickJumper={true}
					className="text-right"
					current={ctx.currentPage}
					pageSize={ctx.pageSize}
					total={ctx.total}
					onChange={(page, pageSize) => {
						ctx.setCurrentPage(page);
					}}
				/>
			</div>}
		</div>
	);
};

const StyledPagination = styled(Pagination)`
  .ant-pagination-options {
    margin-left: 4px;
  }

  .ant-pagination-options > .ant-pagination-options-quick-jumper {
    font-size: 12px;
    color: #666666;
  }

  .ant-pagination-item, .ant-pagination-prev, .ant-pagination-next {
    min-width: 24px;
    height: 24px;
    line-height: 24px;
  }

  .ant-pagination-options-quick-jumper input {
    height: 24px;
    width: 36px;
  }
`

const DownTriangleIcon = styled.div`
  width: 0;
  height: 0;
  border-top: 6px solid #323233;
  border-right: 6px solid transparent;
  border-left: 6px solid transparent;
`

const UpTriangleIcon = styled.div`
  width: 0;
  height: 0;
  border-bottom: 6px solid #323233;
  border-right: 6px solid transparent;
  border-left: 6px solid transparent;
`
