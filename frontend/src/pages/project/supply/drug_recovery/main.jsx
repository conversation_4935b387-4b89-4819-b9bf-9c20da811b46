import React from "react";
import { FormattedMessage, useTranslation } from "../../../common/multilingual/component";
import { Badge, Button, Col, Dropdown, Input, Menu, message, Row, Select, Spin, Table, Tooltip } from "antd";
import { Add } from "./add/add";
import { Receive } from "./receive";
import { ConfirmOrder } from "./confirmOrder";
import { Medicines } from "./medicines";
import { ReasonOrder } from "./reasonOrder";
import { shipmentStatus } from "../../../../data/data";
import { permissions } from '../../../../tools/permission';
import { useSafeState } from "ahooks";
import { useAuth } from "../../../../context/auth";
import { PaginationView } from "../../../../components/pagination";
import { useFetch } from "../../../../hooks/request";
import { getUserSitesAndStorehouses, sitesAndStorehouses } from "../../../../api/project_site";
import { downloadOrder, getOrderList, updateOrderStatus } from "../../../../api/order";
import { getDrugNamesByRoleId } from "../../../../api/drug";
import { usePage } from "../../../../context/page";
import { HistoryList } from "../../../common/history-list";
import { CustomConfirmModal, CustomInfoModal } from "components/modal";
import { InsertDivider } from "components/divider";
import styled from "@emotion/styled";
import { TooltipParagraph } from "components/TooltipParagraph";
import { useGlobal } from "../../../../context/global";
import { Logistics } from "../drug_dtp/logistics";
import { RecoveryDetail } from "./recoveryDetail";
import { PageContextProvider } from "../../../../context/page";
import {AuthButton, authButtonFilter, AuthDropdown} from "../../../common/auth-wrap";

export const Main = (props) => {
    const g = useGlobal()
    const auth = useAuth()
    const customerId = auth.customerId;
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const projectStatus = auth.project.status ? auth.project.status : 0
    const timeZone = (auth.project.info.timeZoneStr !== undefined && auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "") ? Number(auth.project.info.timeZoneStr) : 8
    const page = usePage()
    const intl = useTranslation();
    const { formatMessage } = intl;
    const [loading, setLoading] = useSafeState(false);
    const [orderNumber, setOrderNumber] = useSafeState("");
    const [doSearch, setDoSearch] = useSafeState(0);
    const searchInputRef = React.useRef();
    const [receiveId, setReceiveId] = useSafeState(undefined);
    const [sendId, setSendId] = useSafeState(undefined);
    const [status, setStatus] = useSafeState(undefined);
    const [storehouses, setStorehouses] = useSafeState([]);
    const [sites, setSites] = useSafeState([]);
    const [institutes, setInstitutes] = useSafeState([]);
    const [packageIsOpen, setPackageIsOpen] = useSafeState(false);
    const [haveDrug, setHaveDrug] = useSafeState(true);
    const [haveOtherDrug, setHaveOtherDrug] = useSafeState(true);
    const [height, setHeight] = useSafeState(window.innerHeight - 210);

    const { runAsync: run_getProjectUserSites } = useFetch(getUserSitesAndStorehouses, { manual: true })
    const { runAsync: run_sitesAndStorehouses } = useFetch(sitesAndStorehouses, { manual: true })
    const { runAsync: run_getOrderList } = useFetch(getOrderList, { manual: true })
    const { runAsync: run_download, loading: downloadLoading } = useFetch(downloadOrder, { manual: true })
    const { runAsync: run_updateOrderStatus } = useFetch(updateOrderStatus, { manual: true })
    //获取研究产品名称
    const { runAsync: run_getDrugNames } = useFetch(getDrugNamesByRoleId, { manual: true });
    const add_ref = React.useRef();
    const receive_ref = React.useRef();
    const confirm_order_ref = React.useRef();
    const medicines_ref = React.useRef();
    const reason_ref = React.useRef();
    const history_ref = React.useRef();
    const logistics_ref = React.useRef();
    const detailRef = React.useRef();

    const handleResize = () => {
        setHeight(window.innerHeight - 210);
    };
    React.useEffect(
        () => {
            // 监听
            window.addEventListener("resize", handleResize);
            // 销毁
            return () => window.removeEventListener("resize", handleResize);
        }
    );

    function escapeRegexSpecialChars(str) {
        // 正则表达式特殊字符
        var specialChars = /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g;

        // 使用replace方法和函数作为第二个参数来转义每个匹配项
        return str.replace(specialChars, '\\$&');
    }

    const list = () => {
        let scope = auth.project.permissions.scope
        setLoading(true);
        if (scope === "site") {//中心
            run_getProjectUserSites(
                {
                    customerId: customerId,
                    projectId: projectId,
                    envId: envId,
                    roleId: auth.project.permissions.role_id
                }).then(
                    (result) => {
                        const data = result.data
                        if (data != null && data.site != null) {
                            setSites(data.site)
                            setInstitutes(data.site)
                        }
                    }
                )
        } else if (scope === "depot") {//仓库
            run_getProjectUserSites(
                {
                    customerId: customerId,
                    projectId: projectId,
                    envId: envId,
                    roleId: auth.project.permissions.role_id
                }).then(
                    (result) => {
                        const data = result.data
                        if (data != null && data.storehouse != null) {
                            setStorehouses(data.storehouse)
                        }
                    }
                )
        }

        run_sitesAndStorehouses(
            {
                customerId: customerId,
                projectId: projectId,
                envId: envId,
            }).then(
                (result) => {
                    const data = result.data
                    if (data != null) {
                        let institutes = [];
                        if (scope === "site") {
                            if (data.storehouse != null) {
                                setStorehouses(data.storehouse)
                            }
                        } else {
                            if (data.storehouse != null) {
                                data.storehouse.forEach((value) => {
                                    institutes.push(value)
                                });
                                if (scope === "study") {
                                    setStorehouses(data.storehouse)
                                }
                            }
                            if (data.site != null) {
                                setSites(data.site)
                                data.site.forEach((value) => {
                                    institutes.push(value)
                                });
                            }
                            setInstitutes(institutes)
                        }
                    }
                    // let key = escapeRegexSpecialChars(orderNumber);
                    let key = orderNumber;
                    if(orderNumber !== null && orderNumber !== undefined){
                        key = orderNumber.trim();
                    }
                    //订单列表查询
                    run_getOrderList(
                        {
                            customerId: customerId,
                            projectId: projectId,
                            envId: envId,
                            roleId: auth.project.permissions.role_id,
                            sendId: sendId,
                            receiveId: receiveId,
                            status: status,
                            orderNumber: key.trim(),
                            orderType: "storehouse",
                            start: (page.currentPage - 1) * page.pageSize,
                            limit: page.pageSize
                        }).then((result) => {
                            const data = result.data
                            if (data != null) {
                                page.setTotal(data.total);
                                page.setData(data.items);
                                setPackageIsOpen(data.packageIsOpen);
                            }
                            setLoading(false);
                        }
                        );
                }
            );

        run_getDrugNames({
            customerId: customerId,
            envId: envId,
            roleId: auth.project.permissions.role_id,
        }).then((result) => {
            const data = result.data;
            if (data.haveDrug != null) {
                setHaveDrug(data.haveDrug)
            }
            if (data.haveOtherDrug != null) {
                setHaveOtherDrug(data.haveOtherDrug)
            }
        });
    }

    const add = () => {
        add_ref.current.show(institutes, storehouses, packageIsOpen, haveDrug, haveOtherDrug);
    };

    //下载
    const download = () => {
        // let key = escapeRegexSpecialChars(orderNumber);
        let key = orderNumber;
        if(orderNumber !== null && orderNumber !== undefined){
            key = orderNumber.trim();
        }
        run_download(
            {
                customerId: customerId,
                projectId: projectId,
                envId: envId,
                roleId: auth.project.permissions.role_id,
                sendId: sendId,
                receiveId: receiveId,
                status: status,
                orderNumber: key.trim(),
                orderType: "storehouse"
            }).catch(error => message.error(props.intl.formatMessage({ id: 'common.download.fail' })))
    };

    const updateOrder = (record, status, content) => {
        if (status === 2) {
            logistics_ref.current.show(record, 1)
            return
        }
        CustomConfirmModal({
            title: formatMessage({ id: 'common.tips' }),
            content: content,
            okText: formatMessage({ id: 'common.ok' }),
            onOk: () => {
                run_updateOrderStatus({ id: record._id, status: status, roleId: auth.project.permissions.role_id, }).then((data) => { message.success(data.msg); list() });
            }
        });
    };

    const receiveOrder = (record) => {
        receive_ref.current.show(record, institutes);
    };

    const confirmOrder = (record) => {
        confirm_order_ref.current.show(record, institutes)
    }

    const orderMedicines = (record) => {
        medicines_ref.current.show(record);
    };

    function renderSend(record) {
        if (!record) {
            return null;
        }
        if (record.type === 3) {
            const storehouse = storehouses.find(it => (record.send_id === it.value));
            return <TooltipParagraph outStyle={{ width: 200 }} tooltip={storehouse?.label || "-"}>
                {storehouse?.label || "-"}
            </TooltipParagraph>;
        } else {
            const site = sites.find(it => (record.send_id === it.value));
            return <TooltipParagraph outStyle={{ width: 200 }} tooltip={site?.label || "-"}>
                {site?.label || "-"}
            </TooltipParagraph>;
        }
    }

    function renderReceive(value) {
        if (!value) {
            return null;
        }
        const match = storehouses.find(it => (it.value === value));
        return <TooltipParagraph outStyle={{ width: 200 }} tooltip={match?.label || "-"}>
            {match?.label || "-"}
        </TooltipParagraph>;
    }

    const showReasonModal = (id, status, orderNumber) => {
        reason_ref.current.show(id, status, orderNumber);
    };

    const showReasonViewModal = (id, status, reason) => {
        let title = ""
        if (status) {
            if (status === 5) {//取消
                title = formatMessage({ id: 'shipment.cancel.order' })
            } else if (status === 4) {//丢失订单
                title = formatMessage({ id: 'shipment.lose.order' })
            } else if (status === 8) {//终止订单
                title = formatMessage({ id: 'shipment.end.order' })
            } else if (status === 9) {//关闭订单
                title = formatMessage({ id: 'shipment.close.order' })
            }
        }
        CustomInfoModal({
            title: title,
            content: reason,
            centered: true,
        })
    };

    //轨迹
    const showHistory = (oid) => {
        history_ref.current.show("history.order", oid, timeZone);
    }

    function renderStatus(value) {
        const colors = ["", "geekblue", "orange", "cyan", "red", "purple", "yellow", "volcano", "gold", "magenta"];
        return <Badge color={colors[value]} text={shipmentStatus.find(it => (it.value === value)).label} />;
    }

    const operationButtons = (record) => {
        const allBtns = []
        //　取消
        allBtns.push({
            key: 'cancel',
            label: "common.cancel",
            onClick: () => showReasonModal(record._id, 5, record.order_number),
            show: record.status === 6 && projectStatus !== 2 && permissions(auth.project.permissions, "operation.supply.recovery.cancel")
        })
        // 确认
        allBtns.push({
            key: 'confirm',
            label: "common.confirm",
            onClick: () => confirmOrder(record),
            show: record.status === 6 && permissions(auth.project.permissions, "operation.supply.recovery.determine") && projectStatus !== 2
        })

        allBtns.push({
            key: 'close',
            label: "common.close",
            onClick: () => showReasonModal(record._id, 9, record.order_number),
            show: record.status === 1 && projectStatus !== 2 && permissions(auth.project.permissions, "operation.supply.recovery.close")
        })

        allBtns.push({
            key: 'transit',
            label: "shipment.transit",
            onClick: () => updateOrder(record, 2, formatMessage({ id: 'shipment.transit.order' })),
            show: record.status === 1 && permissions(auth.project.permissions, "operation.supply.recovery.confirm") && projectStatus !== 2
        })

        allBtns.push({
            key: 'received',
            label: "shipment.received",
            onClick: () => receiveOrder(record),
            show: (record.status === 1 || record.status === 2) && permissions(auth.project.permissions, "operation.supply.recovery.receive") && projectStatus !== 2
        })

        allBtns.push({
            key: 'end',
            label: "shipment.end",
            onClick: () => showReasonModal(record._id, 8, record.order_number),
            show: record.status === 2 && projectStatus !== 2 && permissions(auth.project.permissions, "operation.supply.recovery.end")
        })

        allBtns.push({
            key: 'lose',
            label: "shipment.lose",
            onClick: () => showReasonModal(record._id, 4, record.order_number),
            show: (record.status === 1 || record.status === 2) && permissions(auth.project.permissions, "operation.supply.recovery.lose") && projectStatus !== 2
        })


        allBtns.push({
            key: 'history',
            label: "common.history",
            onClick: () => showHistory(record._id),
            show: permissions(auth.project.permissions, "operation.supply.recovery.history")
        })

        // if (((record.status === 4 || record.status === 5 || record.status === 8 || record.status === 9) && permissions(auth.project.permissions, "operation.supply.recovery.reason"))) {
        //     btns.length < 3 ? btns.push(<Button size="small" type="link" style={{ padding: 0 }}
        //         onClick={() => showReasonViewModal(record._id, record.status, record.reason)}><FormattedMessage
        //             id="drug.freeze.reason" /></Button>)
        //             :
        //             moreOperationsMeunItems.push(<Menu.Item key="reason" onClick={() => showReasonViewModal(record._id, record.status, record.reason)}><FormattedMessage id="drug.freeze.reason" /></Menu.Item>)
        // }

        //详情
        allBtns.push({
            key: 'detail',
            label: "common.detail",
            onClick: () => {
                const type = status === 2 && (record.type === 5 || record.type === 6) ? 1 : 2;
                detailRef.current.show(record, type);
            },
            show: permissions(auth.project.permissions, "operation.supply.drug_recovery.logistics.view") ||
                ((record.status === 4 || record.status === 5 || record.status === 8 || record.status === 9) &&
                    permissions(auth.project.permissions, "operation.supply.recovery.reason"))
        })

        const allButtons = authButtonFilter(allBtns)
        const btns = allButtons.slice(0, 3).map((it) => {
            return <AuthButton size="small" type="link" style={{ padding: 0 }} onClick={it.onClick}>
                <FormattedMessage id={it.label} />
            </AuthButton>
        })
        const moreItems = allButtons.slice(3).map((it) => {
            return {key: it.key, label: <FormattedMessage id={it.label} />, onClick: it.onClick}
        })

        return <div style={{ display: 'flex', alignItems: 'center' }}>
            {InsertDivider(btns)}
            <AuthDropdown show={moreItems.length > 0} menu={{items:moreItems}}>
                <span style={{ marginLeft: 12 }} className='dropdown-button'><i className='iconfont icon-gengduo-changgui' /></span>
            </AuthDropdown>
        </div>
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
    React.useEffect(list, [doSearch, sendId, receiveId, status, page.currentPage, page.pageSize, g.lang]);

    return (
        <React.Fragment>
            <Spin spinning={loading}>
                <Row>
                    <svg className="iconfont" width={16} height={16}>
                        <use xlinkHref="#icon-quanbudingdan"></use>
                    </svg>
                    <span style={{
                        color: "#1D2129",
                        fontWeight: 600,
                        paddingLeft: "8px"
                    }}>{formatMessage({ id: 'shipment.order.all' })}</span>
                </Row>
                <Row justify='space-between' className='mar-top-15 order-filter'>
                    <Col span={18}>
                        <Row gutter={[12, 0]} align="middle">
                            <Col span={5} className="mar-rgt-12">
                                <Tooltip
                                    placement="top"
                                    title={formatMessage({ id: 'common.required.prefix' }) + formatMessage({ id: 'shipment.orderNumber' })}
                                >
                                    <Input.Search
                                        style={{ width: "100%" }}
                                        placeholder={formatMessage({ id: 'common.required.prefix' }) + formatMessage({ id: 'shipment.orderNumber' })}
                                        ref={searchInputRef}
                                        value={orderNumber}
                                        onChange={e => {
                                            setOrderNumber(e.target.value)
                                        }}
                                        allowClear onSearch={() => {
                                            searchInputRef?.current?.blur();
                                            page.setCurrentPage(1);
                                            setDoSearch(doSearch + 1);
                                        }}
                                    />
                                </Tooltip>
                            </Col>
                            {formatMessage({ id: 'shipment.send' })}:
                            <Col span={4} className="mar-rgt-12">
                                <Tooltip
                                    placement="top"
                                    // title={formatMessage({ id: 'placeholder.select.search' })}
                                >
                                    <Select
                                        style={{ width: "100%" }}
                                        placeholder={formatMessage({ id: 'placeholder.select.search' })}
                                        value={sendId}
                                        onChange={e => {
                                            page.setCurrentPage(1);
                                            setSendId(e)
                                        }}
                                        allowClear
                                        showSearch
                                        dropdownStyle={{ maxWidth: 400 }}
                                        dropdownMatchSelectWidth={false}
                                        optionFilterProp="label"
                                        filterOption={(input, option) => {
                                            const childrenText = option.props.label;
                                            if (typeof childrenText === 'string') {
                                                return childrenText.trim().toLowerCase().indexOf(input.trim().toLowerCase()) >= 0;
                                            }
                                            return false;
                                        }}
                                    >
                                        {
                                            institutes?.map((it) => (
                                                    <Select.Option key={it.value} value={it.value}>
                                                        {/* <Tooltip title={it.label}>{it.label}</Tooltip> */}
                                                        {it.label}
                                                    </Select.Option>
                                                ))
                                        }

                                    </Select>
                                </Tooltip>
                            </Col>
                            {formatMessage({ id: 'shipment.receive' })}:
                            <Col span={4} className="mar-rgt-12">
                                <Tooltip
                                    placement="top"
                                    // title={formatMessage({ id: 'placeholder.select.search' })}
                                >
                                    <Select
                                        style={{ width: "100%" }}
                                        placeholder={formatMessage({ id: 'placeholder.select.search' })}
                                        value={receiveId}
                                        onChange={e => { page.setCurrentPage(1); setReceiveId(e) }}
                                        allowClear
                                        showSearch
                                        dropdownStyle={{ maxWidth: 400 }}
                                        dropdownMatchSelectWidth={false}
                                        optionFilterProp="label"
                                        filterOption={(input, option) => {
                                            const childrenText = option.props.label;
                                            if (typeof childrenText === 'string') {
                                                return childrenText.trim().toLowerCase().indexOf(input.trim().toLowerCase()) >= 0;
                                            }
                                            return false;
                                        }}
                                    >
                                        {
                                            storehouses?.map((it) => (
                                                <Select.Option key={it.value} value={it.value}>
                                                    {/* <Tooltip title={it.label}>{it.label}</Tooltip> */}
                                                    {it.label}
                                                </Select.Option>
                                            ))
                                        }

                                    </Select>
                                </Tooltip>
                            </Col>
                            {formatMessage({ id: 'shipment.status' })}:
                            <Col span={4}>
                                <Select
                                    style={{ width: "100%" }}
                                    placeholder={formatMessage({ id: 'placeholder.select.common' })}
                                    value={status}
                                    onChange={e => { page.setCurrentPage(1); setStatus(e) }}
                                    allowClear
                                    options={[...shipmentStatus, { value: 10, label: <FormattedMessage id="shipment.status.timeout" /> }]}
                                />
                            </Col>
                        </Row>
                    </Col>
                    <Col>
                        {permissions(auth.project.permissions, "operation.supply.recovery.download") && auth.project.info.research_attribute === 1 ?
                            <Button loading={downloadLoading} className="mar-rgt-12"
                                onClick={() => download()}><FormattedMessage
                                    id="common.download" /></Button> : null}
                        {permissions(auth.project.permissions, "operation.supply.recovery.add") && projectStatus !== 2 ?
                            <AuthButton type="primary" onClick={() => add()}><FormattedMessage
                                id="common.add" /></AuthButton> : null}
                    </Col>
                </Row>
                <Table
                    className="mar-top-16"
                    //scroll={{x: page.data && page.data.length > 0 ? "100%" : false}}
                    scroll={{ x: 'max-content', y: 'calc(100vh - 270px)' }}
                    dataSource={page.data}
                    sticky
                    //style={{ maxHeight: "calc(100vh - 245px)", overflowY: "auto", }}
                    pagination={false}
                    rowKey={(record) => (record.id)}
                >
                    <Table.Column
                        title={<FormattedMessage id={"common.serial"} />}
                        dataIndex="#"
                        key="#"
                        width={70}
                        render={(text, record, index) => ((page.currentPage - 1) * page.pageSize + index + 1)} />
                    <Table.Column title={<FormattedMessage id="shipment.orderNumber" />} width={200}
                        dataIndex="order_number" key="order_number" ellipsis />
                    <Table.Column
                        title={<FormattedMessage id="shipment.send" />}
                        dataIndex="send_id"
                        key="send_id"
                        //llipsis
                        width={200}
                        render={(value, record, index) => (renderSend(record))}
                    />
                    <Table.Column
                        title={<FormattedMessage id="shipment.receive" />}
                        dataIndex="receive_id"
                        key="receive_id"
                        //ellipsis
                        width={200}
                        render={(value, record, index) => (renderReceive(value))}
                    />
                    <Table.Column
                        title={<FormattedMessage id="shipment.status" />}
                        dataIndex="status"
                        key="status"
                        ellipsis
                        width={180}
                        render={(value, record, index) => (renderStatus(value))}
                    />
                    <Table.Column
                        title={<FormattedMessage id="projects.sitePharmacy.order.medicineQuantity" />}
                        dataIndex="medicines"
                        key="medicines"
                        ellipsis
                        width={135}
                        render={
                            (value, record, index) => (
                                <span className="link-btn" onClick={() => orderMedicines(record)}>{record.count}</span>
                            )
                        }
                    />
                    {/* <Table.Column
                            title={<FormattedMessage id="common.created.by" />}
                            dataIndex={["meta", "createdUser", [0], "info", "name"]}
                            key={["meta", "createdUser", [0], "info", "name"]}
                            ellipsis
                            width={100}
                        />
                        <Table.Column
                            title={<FormattedMessage id="common.created.at" />}
                            key="operationTime"
                            dataIndex="operationTime"
                            ellipsis
                            width={200}
                            render={
                                (value, record, index) => (
                                    (record.meta.created_at === null || record.meta.created_at === 0) ? '' : moment.unix(record.meta.created_at).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss')
                                )
                            }
                        />
                        <Table.Column
                            title={<FormattedMessage id="shipment.receive.user" />}
                            dataIndex={["receiveUser", [0], "info", "name"]}
                            key={["receiveUser", [0], "info", "name"]}
                            ellipsis
                            width={100}
                        />
                        <Table.Column
                            title={<FormattedMessage id="shipment.receive.time" />}
                            key="receive_at"
                            dataIndex="receive_at"
                            ellipsis
                            width={200}
                            render={
                                (value, record, index) => (
                                    (record.receive_at === null || record.receive_at === 0) ? '' : moment.unix(record.receive_at).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss')
                                )
                            }
                        />
                        <Table.Column
                            title={<FormattedMessage id="shipment.canceller" />}
                            dataIndex={["cancellerUser", [0], "info", "name"]}
                            key={["cancellerUser", [0], "info", "name"]}
                            ellipsis
                            width={100}
                        />
                        <Table.Column
                            title={<FormattedMessage id="shipment.cancel.time" />}
                            key="canceller_at"
                            dataIndex="canceller_at"
                            ellipsis
                            width={200}
                            render={
                                (value, record, index) => (
                                    (record.canceller_at === null || record.canceller_at === 0) ? '' : moment.unix(record.canceller_at).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss')
                                )
                            }
                        /> */}
                    <Table.Column
                        title={<FormattedMessage id="common.operation" />}
                        width={220}
                        fixed="right"
                        render={
                            (value, record, index) => operationButtons(record)
                        }
                    />
                </Table>
                <PaginationView />
            </Spin>
            <Logistics bind={logistics_ref} refresh={list} />
            <Add bind={add_ref} refresh={list} />
            <PageContextProvider>
                <Receive bind={receive_ref} refresh={list} />
            </PageContextProvider>
            <PageContextProvider>
                <ConfirmOrder bind={confirm_order_ref} refresh={list} />
            </PageContextProvider>
            <Medicines bind={medicines_ref} refresh={list} />
            <ReasonOrder bind={reason_ref} refresh={list} />
            <HistoryList bind={history_ref} permission={permissions(auth.project.permissions, "operation.supply.recovery.print")} />
            <RecoveryDetail ref={detailRef} refresh={list} />
        </React.Fragment>


    )
};

const CustomSearch = styled(Input.Search)`
    .ant-input-search-button {
        height: 31px !important;
    }
`
