import { Button, Popover, Table } from "antd";
import { PaginationView } from "components/pagination";
import { PageContextProvider } from "context/page";
import { useCacheTable } from "hooks/cache-rowSpan-table";
import { useMemo } from "react";
import { FormattedMessage } from "react-intl";
import Barcode from "react-barcode";
import React from "react";
import _ from "lodash";

interface SelectableOrderTableProp {
  // 当前订单的缓存数据
  dataSource: any[];
  selectedIds: any[];
  rowSelection: any;
  clearDisplay?: boolean;
  // 清空函数
  refresh?: any;
  //是否按照包装运送
  packageIsOpen?: boolean;
  // 总计
  amount?: number;
  // 是否有未编号
  isHasOtherDataSource?: boolean;
  // 是否有tip
  isHasTip?: boolean;
  showGroup?: boolean;
  searchValue?: any;
}

// 可勾选的Table
export const SelectableOrderTable = (props: SelectableOrderTableProp) => {
  return (
    <OrderTable {...props}></OrderTable>
  );
};

interface UnSelectableOrderTableProp {
  dataSource: any[];
  //是否按照包装运送
  packageIsOpen?: boolean;
  //是否有自动编码规则
  codeRule?: boolean;
  // 是否有未编号
  isHasOtherDataSource?: boolean;
  // 是否有tip
  isHasTip?: boolean;
}
// 不可勾选的Table
export const UnSelectableOrderTable = (props: UnSelectableOrderTableProp) => {
  return (
    <UnOrderTable {...props}></UnOrderTable>
  );
};

const OrderTable = (props: any) => {
  const tableData = useCacheTable(props);
  const pageMode = useMemo(() => {
    if (props.rowSelection) {
      return "SELECTABLE";
    } else {
      return undefined;
    }
  }, [props.rowSelection]);
  var selectedNumber = useMemo(() => {
    if (!props.selectedIds) {
      return 0;
    } else {
      //包装号的特殊处理
      if (
        props.packageIsOpen &&
        props.dataSource.length === props.selectedIds.length
      ) {
        //全选
        var selectIds: any = [];
        props.dataSource.forEach((it: any) => {
          if (
            !props.packageIsOpen ||
            (props.packageIsOpen && it.package_numberRowSpan !== 0)
          ) {
            selectIds.push(it._id);
          }
        });
        return selectIds.length;
      }
    }
    return props.selectedIds.length;
  }, [props.selectedIds]);

  if (props.searchValue !== undefined && props.searchValue !== null && props.searchValue.length > 0) {
    selectedNumber = 0
  }


  var groupInfo: any = new Map();
  var groupCount: any = [];
  var selectedData: any = []

  selectedData = _.filter(props.dataSource, function (o) { return props.selectedIds.includes(o._id) });

  var groupName = _.groupBy(selectedData, "name")
  _.forEach(groupName, function (value, drugName) {
    var groupBatch = _.groupBy(value, "batch_number")
    _.forEach(groupBatch, function (batchVal, batch) {
      groupInfo.set(drugName + "/" + batch, batchVal.length)
    });
  });
  groupCount = Array.from(groupInfo);

  return (
    <div>
      <div>
        <Table
          className="mar-top-10"
          size="small"
          scroll={{ x: true }}
          pagination={false}
          rowKey={(record) => record._id}
          dataSource={tableData}
          rowSelection={{
            ...props.rowSelection,
            renderCell: (
              checked: any,
              record: any,
              index: any,
              originNode: any,
              package_numberRowSpan: any
            ) => {
              return {
                children: originNode,
                props: {
                  rowSpan:
                    tableData[index] !== undefined
                      ? tableData[index]["package_numberRowSpan"]
                      : 1,
                },
              };
            },
          }}
        >
          {props.packageIsOpen ? (
            <Table.Column
              title={<FormattedMessage id="drug.configure.drugName" />}
              dataIndex={"name"}
              key="name"
              ellipsis
              onCell={(_, index: any) => {
                return {
                  rowSpan: tableData[index].nameRowSpan,
                };
              }}
              render={(value, record, index) =>
                <div style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'normal',
                  wordBreak: 'break-word',
                }}>
                  {value}
                </div>
              }
            />
          ) : (
            <Table.Column
              title={<FormattedMessage id="drug.configure.drugName" />}
              dataIndex={"name"}
              key="name"
              ellipsis
              render={(value, record, index) =>
                <div style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'normal',
                  wordBreak: 'break-word',
                }}>
                  {value}
                </div>
              }
            />
          )}
          {props.packageIsOpen && (
            <Table.Column
              title={<FormattedMessage id="drug.medicine.packlist" />}
              dataIndex={"package_number"}
              key="package_number"
              ellipsis
              width={180}
              render={(value: any, record: any, index: any) => {
                let newValue = "";
                if (value !== undefined && value !== "") {
                  newValue = value;
                } else {
                  newValue = "-";
                }
                let obj = {
                  children: newValue,
                  props: {
                    rowSpan: tableData[index].package_numberRowSpan,
                  },
                };
                return obj;
              }}
            />
          )}
          <Table.Column
            width={140}
            title={<FormattedMessage id="drug.list.drugNumber" />}
            dataIndex={"number"}
            key="number"
            ellipsis
          />
          <Table.Column
            width={120}
            title={<FormattedMessage id="drug.list.expireDate" />}
            dataIndex={"expiration_date"}
            key="expiration_date"
            ellipsis
          />
          <Table.Column
            title={<FormattedMessage id="drug.list.batch" />}
            dataIndex={"batch_number"}
            key="batch_number"
            width={140}
            ellipsis
          />
          {props.codeRule && (
            <Table.Column
              title={<FormattedMessage id="barcode" />}
              key="number"
              dataIndex="number"
              width={120}
              ellipsis
              render={(value, record, index) => {
                return (
                  <Popover
                    content={
                      <>
                        <Barcode
                          value={value}
                          displayValue={false}
                          height={60}
                          width={1}
                          format="CODE128"
                        />
                        <br />
                        <span>&nbsp;&nbsp;{value}</span>
                      </>
                    }
                    trigger="click"
                  >
                    <Button
                      style={{ padding: 0, marginRight: "12px" }}
                      size="small"
                      type="link"
                    >
                      <FormattedMessage id="form.preview" />
                    </Button>
                    {/*<Button><FormattedMessage id="form.preview" /></Button>*/}
                  </Popover>
                );
              }}
            />
          )}
        </Table>
      </div>
      <div style={{ marginTop: 12 }}>
        <PaginationView
          mode={pageMode}
          selectedNumber={selectedNumber}
          clearDisplay={true}
          refresh={props.refresh}
        />
        {props.showGroup && groupCount.length > 0 &&
          <div style={{ marginTop: 5 }}>
            <div className="triangle">
              <div className="triangle_inner"></div>
            </div>
            <div className="rectangle" >
              <span style={{ marginLeft: 8, marginTop: 8, marginBottom: 8, marginRight: 8 }}>
                {
                  groupCount.map(
                    (it: any) => (
                      <>
                        <span style={{ fontSize: 12, fontFamily: "PingFang SC", fontWeight: 400, color: "#677283" }}>{it[0]}：</span>
                        <span style={{ fontSize: 12, fontFamily: "PingFang SC", fontWeight: 400, color: "#1D2129" }}>{it[1]}；</span>
                      </>
                    )
                  )
                }
              </span>
            </div>
          </div>
        }

      </div>
    </div>
  );
};





const UnOrderTable = (props: any) => {
  const tableData = useCacheTable(props);
  const pageMode = useMemo(() => {
    if (props.rowSelection) {
      return "SELECTABLE";
    } else {
      return undefined;
    }
  }, [props.rowSelection]);
  const selectedNumber = useMemo(() => {
    if (!props.selectedIds) {
      return 0;
    } else {
      //包装号的特殊处理
      if (
        props.packageIsOpen &&
        props.dataSource.length === props.selectedIds.length
      ) {
        //全选
        var selectIds: any = [];
        props.dataSource.forEach((it: any) => {
          if (
            !props.packageIsOpen ||
            (props.packageIsOpen && it.package_numberRowSpan !== 0)
          ) {
            selectIds.push(it._id);
          }
        });
        return selectIds.length;
      }
    }
    return props.selectedIds.length;
  }, [props.selectedIds]);
  return (
    <div>
      <div>
        <Table
          className="mar-top-10"
          size="small"
          scroll={{
            y: `calc(100vh - 409px - ${props.isHasTip ? "48px" : "22px"} - ${props.isHasOtherDataSource ? "90px" : "68px"
              })`,
          }}
          pagination={false}
          rowKey={(record) => record._id}
          dataSource={tableData}
        >
          {props.packageIsOpen ? (
            <Table.Column
              title={<FormattedMessage id="drug.configure.drugName" />}
              dataIndex={"name"}
              key="name"
              ellipsis
              render={
                (value, record, index) => (
                  value !== null && value !== "" ?
                    <div style={{
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'normal',
                      wordBreak: 'break-word',
                    }}>
                      {value}
                    </div>
                    :
                    "-"
                )
              }
              onCell={(_, index: any) => {
                return {
                  rowSpan: tableData[index].nameRowSpan,
                };
              }}
            />
          ) : (
            <Table.Column
              title={<FormattedMessage id="drug.configure.drugName" />}
              dataIndex={"name"}
              key="name"
              ellipsis
              render={
                (value, record, index) => (
                  value !== null && value !== "" ?
                    <div style={{
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'normal',
                      wordBreak: 'break-word',
                    }}>
                      {value}
                    </div>
                    :
                    "-"
                )
              }
            />
          )}
          {props.packageIsOpen && (
            <Table.Column
              title={<FormattedMessage id="drug.medicine.packlist" />}
              dataIndex={"package_number"}
              key="package_number"
              ellipsis
              width={180}
              render={(value: any, record: any, index: any) => {
                let newValue = "";
                if (value !== undefined && value !== "") {
                  newValue = value;
                } else {
                  newValue = "-";
                }
                let obj = {
                  children: newValue,
                  props: {
                    rowSpan: tableData[index].package_numberRowSpan,
                  },
                };
                return obj;
              }}
            />
          )}
          <Table.Column
            width={140}
            title={<FormattedMessage id="drug.list.drugNumber" />}
            dataIndex={"number"}
            key="number"
            ellipsis
          />
          <Table.Column
            width={120}
            title={<FormattedMessage id="drug.list.expireDate" />}
            dataIndex={"expiration_date"}
            key="expiration_date"
            ellipsis
          />
          <Table.Column
            title={<FormattedMessage id="drug.list.batch" />}
            dataIndex={"batch_number"}
            key="batch_number"
            width={140}
            ellipsis
          />
          {props.codeRule && (
            <>
              <Table.Column title={<FormattedMessage id="barcode" />} key="number"
                dataIndex="number" align="center" ellipsis width={150}
                render={(value, record: any) => {
                  return (
                    <Popover content={
                      <>
                        <Barcode value={value} displayValue={false} height={60} width={1}
                          format="CODE128" />
                        <br />
                        <span>&nbsp;&nbsp;{value}</span>
                      </>
                    } trigger="click">
                      {
                        record.short_code !== "-" && record.short_code !== "" ?
                          <Button
                            style={{ padding: 0, marginRight: '12px' }}
                            size="small"
                            type="link">
                            <FormattedMessage id="form.preview" />
                          </Button>
                          :
                          "-"
                      }


                      {/*<Button><FormattedMessage id="form.preview" /></Button>*/}
                    </Popover>
                  )
                }} />
              <Table.Column title={<FormattedMessage id="packageBarcode" />} key="package_number"
                dataIndex="package_number" align="center" ellipsis width={150}
                render={(value, record: any) => {
                  return (
                    <Popover content={
                      <>
                        <Barcode value={value} displayValue={false} height={60} width={1}
                          format="CODE128" />
                        <br />
                        <span>&nbsp;&nbsp;{value}</span>
                      </>
                    } trigger="click">
                      {
                        record.short_code !== "" && record.short_code !== "-" && record.package_number !== "" && record.package_number !== "-" ?
                          <Button
                            style={{ padding: 0, marginRight: '12px' }}
                            size="small"
                            type="link">
                            <FormattedMessage id="form.preview" />
                          </Button>
                          :
                          "-"
                      }
                    </Popover>
                  )
                }} />
            </>
          )}
        </Table>
      </div>
      <div style={{ marginTop: 12 }}>
        <PaginationView
          mode={pageMode}
          selectedNumber={selectedNumber}
          clearDisplay={true}
          refresh={props.refresh}
        />
      </div>
    </div>
  );
};
