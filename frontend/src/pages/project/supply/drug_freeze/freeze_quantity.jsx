import React from "react";
import { Form, Modal, Table, Empty } from "antd";
import { FormattedMessage, useTranslation } from "../../../common/multilingual/component";
import { useAuth } from "../../../../context/auth";
import { useSafeState } from "ahooks";
import { useFetch } from "../../../../hooks/request";
import { medicineFreezeDetailsList } from "../../../../api/medicine";
import { medicineStatusAliData } from "../../../../tools/medicine_status";
import { usePage } from "../../../../context/page";
import { UnSelectableOrderTable } from "../shipment/order-table";
import { SelectEmbeddeTitle } from "components/select";
import { Title } from "components/title";
import { useGlobal } from "../../../../context/global";
import { combineRow } from "../../../../utils/merge_cell";
import { getProjectAttributeConnect } from "../../../../api/randomization";
import { getBarcodeRule } from "../../../../api/barcode";
import { fillTableCellEmptyPlaceholder } from "../../../../components/table";

export const FreezeQuantity = (props) => {
    const g = useGlobal()
    const [visible, setVisible] = useSafeState(false);
    const [otherTableData, setOtherTableData] = useSafeState([]);
    const [medicinesIds, setMedicinesIds] = useSafeState([]);
    const [otherIds, setOtherIds] = useSafeState([]);
    const intl = useTranslation();
    const { formatMessage } = intl;
    const [id, setId] = useSafeState("");
    const [status, setStatus] = useSafeState(4);
    const [isolationTime, setIsolationTime] = useSafeState(0);
    const [packageIsOpen, setPackageIsOpen] = useSafeState(false);
    const [instituteType, setInstituteType] = useSafeState(0);
    const [orderType, setOrderType] = useSafeState(0);
    const [havePacakge, setHavePackage] = useSafeState(false);
    const [haveSingle, setHaveSingle] = useSafeState(false);
    const [codeRule, setCodeRule] = useSafeState(null);
    const son_page = usePage();
    const auth = useAuth();
    const [ali, setAli] = useSafeState(0);
    const envId = auth.env ? auth.env.id : null;
    const projectId = auth.project.id;
    const customerId = auth.customerId;


    const show = (data) => {
        setInstituteType(data.institute_type);
        setOrderType(data.order_type);
        setOtherTableData([]);
        setMedicinesIds([]);
        setOtherIds([]);
        son_page.setTotal(0);
        son_page.setData([]);
        setStatus(4);
        isAli();
        barcodeCodeRule();
        if (data) {
            setId(data._id);
            setMedicinesIds(data.history);
            setOtherIds(data.other_history_new);
            setIsolationTime(data.meta.created_at);
            setVisible(true);
            getMedicineFreezeDetailsList(data._id, data.history);


            //setOtherTableData(fillTableCellEmptyPlaceholder(data.other_medicines ? data.other_medicines : []));
        }
    };

    const isAli = () => {
        getProjectAttributeConnectRun({ projectId: projectId, envId: envId }).then((res) => {
            if (res.data) {
                if (res.data.info.connectAli) {
                    setAli(1)
                }
            }
        });
    };

    const hide = () => {
        setVisible(false);
        setOtherTableData([]);
        setMedicinesIds([]);
        setStatus(4);
        setIsolationTime(0);
        setId("");
        setInstituteType(0);
        setOrderType(0);
        setHavePackage(false);
        setHaveSingle(false);
        // isAli();
    };
    const { runAsync: run_medicineFreezeDetailsList, loading } = useFetch(medicineFreezeDetailsList, { manual: true })
    const { runAsync: getProjectAttributeConnectRun, loading: getProjectAttributeConnectLoading } = useFetch(getProjectAttributeConnect, { manual: true })

    // function renderStatus(value) {
    //     if (value !== undefined) {
    //         return medicineStatusAliData(auth.codeRule, auth.project.info.research_attribute, ali).find(it => (it.value === value)).label;
    //     } else {
    //         return ""
    //     }
    // }
    const { runAsync: getBarcodeRun } = useFetch(getBarcodeRule, { manual: true })

    const barcodeCodeRule = () => {
        getBarcodeRun({
            customerId: customerId,
            projectId: projectId,
            envId: envId,
        }).then((response) => {
            if (response) {
                let data = response.data;
                setCodeRule(data.codeRule1);
            }
        });
    };

    const getMedicineFreezeDetailsList = (oid, mesIds) => {
        setOtherTableData([]);
        let fId = oid;
        if (id !== undefined && id !== "") {
            fId = id;
        }
        if (fId !== undefined && fId !== "") {
            run_medicineFreezeDetailsList({
                id: fId,
                start: (son_page.currentPage - 1) * son_page.pageSize,
                limit: son_page.pageSize,
                //attributeId: auth.attribute.id,
                roleId: auth.project.permissions.role_id,
                status: status,         // 状态
                sign: 1,                // sign 1 查询所有隔离的药物 2 查询待解隔离或者待审批的药物
            }).then(
                (result) => {
                    setPackageIsOpen(result.data.packageIsOpen);
                    if (result.data.items != null) {
                        son_page.setTotal(result.data.total);
                        if (result.data.packageIsOpen) {
                            var tableData = combineRow(result.data.items, "package_number", "package_number", false)
                            tableData = combineRow(result.data.items, "name", "name", false)
                            son_page.setData(fillTableCellEmptyPlaceholder(tableData ? tableData : []));
                        } else {
                            son_page.setData(fillTableCellEmptyPlaceholder(result.data.items ? result.data.items : []));
                        }
                    } else {
                        son_page.setTotal(0);
                        son_page.setData([]);
                    }
                    if (result.data.otherData != null) {
                        setOtherTableData(fillTableCellEmptyPlaceholder(result.data.otherData ? result.data.otherData : []));
                        for (let i = 0; i < result.data.otherData.length; i++) {
                            if (result.data.otherData[i].package_method) {
                                setHavePackage(true)
                            } else {
                                setHaveSingle(true)
                            }
                        }
                    }
                }
            )
        }
    };


    function renderPackageMethod(packageMethod) {
        return packageMethod === true && (instituteType !== 1 || orderType === 1) ? (
            <FormattedMessage id="shipment.order.packageMethod.package" />
        ) : (
            <FormattedMessage id="shipment.order.packageMethod.single" />
        );
    }

    // function renderOrder(record) {
    //     return <span style={{ color: record.isolation_approval_sign === 1 ? "#BBBBBB" : "" }}>{record.name} {
    //         (record.isolation_approval_sign === 1) &&
    //         <Tooltip placement="top" title={formatMessage({ id: 'medicine.status.under.approval' })}>
    //             <svg className="iconfont" width={13} height={13}>
    //                 <use xlinkHref="#icon-jiegelishenpizhong" />
    //             </svg>
    //         </Tooltip>
    //     }</span>
    // }

    React.useImperativeHandle(props.bind, () => ({ show }));
    React.useEffect(getMedicineFreezeDetailsList, [son_page.currentPage, son_page.pageSize, status]);
    return (
        <Modal
            className="custom-blarge-modal"
            title={formatMessage({ id: 'drug.isolation.quantity' })}
            visible={visible}
            onCancel={hide}
            centered
            maskClosable={false}
            footer={null}
        >
            <Form >
                {(medicinesIds != null && medicinesIds.length > 0) || (otherIds != null && otherIds.length > 0) ?
                    <SelectEmbeddeTitle
                        style={{ width: g.lang === "en" ? 200 : 150, marginTop: "12px", marginBottom: "12px" }}
                        _title={formatMessage({ id: 'common.status' })}
                        loading={loading}
                        placeholder={formatMessage({ id: 'placeholder.select.common' })}
                        showSearch
                        allowClear
                        optionFilterProp="children"
                        value={status}
                        onChange={(value) => { setStatus(value) }}
                        options={medicineStatusAliData(auth.codeRule, auth.project.info.research_attribute, ali)}
                    />
                    :
                    null
                }

                {(son_page.data == null || son_page.data.length === 0) && (otherTableData == null || otherTableData.length === 0) &&
                    < Empty
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                    />
                }
                {son_page.data != null && son_page.data.length > 0 &&
                    <>
                        <Title name={formatMessage({ id: 'shipment.medicine' })} />
                        <UnSelectableOrderTable dataSource={son_page.data} packageIsOpen={packageIsOpen} codeRule={codeRule} />
                    </>
                }
                {otherTableData != null && otherTableData.length > 0 && (status === undefined || status === 1 || status === 4 || status === 6) ?
                    <>
                        <div style={{ marginTop: 12, marginBottom: 12 }}>
                            <Title name={formatMessage({ id: 'shipment.other.drug' })}></Title>
                        </div>
                        <Table
                            className="mar-top-10"
                            size="small"
                            dataSource={otherTableData}
                            pagination={false}
                            rowKey={(record) => (record.id)}
                        >
                            <Table.Column
                                title={<FormattedMessage id="drug.configure.drugName" />}
                                dataIndex={"name"}
                                key="name"
                                ellipsis
                                render={(value, record, index) =>
                                    <div style={{
                                        display: '-webkit-box',
                                        WebkitLineClamp: 2,
                                        WebkitBoxOrient: 'vertical',
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        whiteSpace: 'normal',
                                        wordBreak: 'break-word',
                                    }}>
                                        {value}
                                    </div>
                                }
                            />
                            <Table.Column
                                title={<FormattedMessage id="drug.list.expireDate" />}
                                dataIndex={"expirationDate"}
                                key="expirationDate"
                                width={120}
                                ellipsis
                            />
                            <Table.Column
                                title={<FormattedMessage id="drug.list.batch" />}
                                dataIndex={"batchNumber"}
                                key="batchNumber"
                                width={140}
                                ellipsis
                            />

                            {packageIsOpen && (
                                <Table.Column
                                    title={intl.formatMessage({
                                        id: "shipment.order.package.method",
                                    })}
                                    width={110}
                                    dataIndex="package_method"
                                    key="package_method"
                                    ellipsis
                                    render={(value, record, index) =>
                                        renderPackageMethod(value)
                                    }
                                />
                            )}
                            <Table.Column
                                title={(instituteType === 1 && orderType !== 1) ? <FormattedMessage id="drug.isolation.single.quantity" /> : havePacakge && !haveSingle ? <FormattedMessage id="drug.isolation.package.quantity" /> : <FormattedMessage id="drug.isolation.quantity" />}
                                dataIndex={"count"}
                                key="count"
                                width={90}
                                ellipsis
                                render={(value, record, index) => {
                                    return (instituteType === 1 && orderType !== 1) ? value : record.package_method ? record.allCount + "(" + value + ")" : value;
                                }
                                }
                            />
                        </Table>
                    </>
                    : null
                }
            </Form>
        </Modal>

    );
};

