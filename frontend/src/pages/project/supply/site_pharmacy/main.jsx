import React from "react";
import { Tabs } from "antd";
import { FormattedMessage } from "react-intl";
import { Summary } from "./summary";
import { OtherMedicineSku } from "./other_medicine_sku";
import { Sku } from "./sku";
import { MaterialForecast } from "./material_forecast";
import { permissions } from "../../../../tools/permission";
import { useAuth } from "../../../../context/auth";
import { usePage } from "../../../../context/page";
import { useSafeState } from "ahooks";
import { useFetch } from "../../../../hooks/request";
import { userSites } from "../../../../api/project_site";
import { PageContextProvider } from "../../../../context/page";
import styled from "@emotion/styled";

export const Main = (props) => {

    const [sites, setSites] = useSafeState([]);
    const auth = useAuth()
    const page = usePage();
    const envId = auth.env ? auth.env.id : null;
    const projectId = auth.project.id;
    const customerId = auth.customerId;
    const { runAsync: run_userSites } = useFetch(userSites, { manual: true });

    const tabChange = () => {
        page.setCurrentPage(1);
    };

    React.useEffect(
        () => {
            run_userSites({
                projectId: projectId,
                customerId: customerId,
                envId: envId,
                roleId: auth.project.permissions.role_id
            }).then((result) => {
                const response = result.data
                let list = [];
                if (response) {
                    response.forEach(item => {
                        list.push({ id: item.id, name: item.name, number: item.number })
                    });
                }
                setSites(list);
            });

        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [projectId, customerId, envId]
    );

    return (
        <CustomTabs tabBarStyle={{ height: "32px" }} size="small" defaultActiveKey="1" tabPosition="top" destroyInactiveTabPane={true} onChange={tabChange}>
            {
                permissions(auth.project.permissions, "operation.supply.site.medicine.summary") ?
                    <Tabs.TabPane tab={<FormattedMessage id="projects.storehouse.statistics.summary" />} key="1">
                        <Summary sites={sites} />
                    </Tabs.TabPane>
                    :
                    null
            }
            {
                permissions(auth.project.permissions, "operation.supply.site.medicine.singe") ?
                    <Tabs.TabPane tab={<FormattedMessage id="projects.storehouse.statistics.sku" />} key="2">
                        <Sku sites={sites} />
                    </Tabs.TabPane>
                    :
                    null
            }
            {
                permissions(auth.project.permissions, "operation.supply.site.no_number.view") ?
                    <Tabs.TabPane tab={<FormattedMessage id="projects.storehouse.statistics.other.sku" />}
                        key="3">
                        <OtherMedicineSku sites={sites} />
                    </Tabs.TabPane>
                    :
                    null
            }
            {
                permissions(auth.project.permissions, "operation.supply.site.medicine.forecast") ?
                    <Tabs.TabPane tab={<FormattedMessage id="projects.storehouse.statistics.material.forecast" />}
                        key="4">
                        <MaterialForecast sites={sites} />
                    </Tabs.TabPane>
                    :
                    null
            }
        </CustomTabs>
    )
};

const CustomTabs = styled(Tabs)`
    .ant-tabs-nav .ant-tabs-tab {
        padding: unset;
        padding-bottom: 12px;
    }
`
