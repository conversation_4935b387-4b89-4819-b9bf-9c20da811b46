import React from "react";
import { Modal, Form, Input, DatePicker, Button, Table, Space, message, Select, InputNumber } from "antd";
import { useSafeState } from "ahooks";
import { useAuth } from "../../../../context/auth";
import { FormattedMessage } from "../../../common/multilingual/component";
import { SearchOutlined, CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { Title as CustomTitle } from "../../../../components/title";
import { PaginationView } from "../../../../components/pagination";
import { usePage } from "../../../../context/page";
import moment from "moment";

const { RangePicker } = DatePicker;
const { Option } = Select;

export const MaterialForecastSettings = React.forwardRef((props, ref) => {
    const [visible, setVisible] = useSafeState(false);
    const [loading, setLoading] = useSafeState(false);
    const [planType, setPlanType] = useSafeState("monthly"); // monthly 或 weekly

    // 项目入组计划数据
    const [projectEnrollmentPeriod, setProjectEnrollmentPeriod] = useSafeState([]);
    const [projectTargetCount, setProjectTargetCount] = useSafeState(500);

    // 中心入组计划数据
    const [siteSearchText, setSiteSearchText] = useSafeState("");
    const [editingKey, setEditingKey] = useSafeState('');
    const [editingRow, setEditingRow] = useSafeState({});
    const [siteEnrollmentData, setSiteEnrollmentData] = useSafeState([
        {
            key: 1,
            sequence: 1,
            siteId: "site_001",
            siteName: "22-华西医院",
            plannedEnrollment: 88,
            actualEnrollment: 88,
            period: planType === "monthly" ? "2020-09" : "20200201-20200207"
        },
        {
            key: 2,
            sequence: 2,
            siteId: "site_002",
            siteName: "中心编号-中心名称",
            plannedEnrollment: 66,
            actualEnrollment: 66,
            period: planType === "monthly" ? "2020-10" : "20200208-20200214"
        }
    ]);

    // 可选择的中心列表
    const [availableSites] = useSafeState([
        { id: "site_001", name: "22-华西医院" },
        { id: "site_002", name: "中心编号-中心名称" },
        { id: "site_003", name: "北京协和医院" },
        { id: "site_004", name: "上海瑞金医院" }
    ]);

    const auth = useAuth();
    const page = usePage();

    const show = () => {
        setVisible(true);
    };

    const hide = () => {
        setVisible(false);
        // 页面关闭后恢复编辑状态
        setEditingKey('');
        setEditingRow({});
    };

    const handleSave = () => {
        setLoading(true);
        // 这里添加保存逻辑
        setTimeout(() => {
            setLoading(false);
            message.success("设置保存成功");
            hide();
        }, 1000);
    };

    // 保存行编辑
    const handleRowSave = (key) => {
        const newData = [...siteEnrollmentData];
        const index = newData.findIndex(item => key.toString() === item.key.toString());

        if (index > -1) {
            const item = newData[index];
            newData.splice(index, 1, { ...item, ...editingRow });
            setSiteEnrollmentData(newData);
            setEditingKey('');
            setEditingRow({});
        }
    };

    // 判断是否正在编辑某行
    const isEditing = (record) => record.key.toString() === editingKey;

    // 开始编辑
    const handleEdit = (record) => {
        setEditingKey(record.key.toString());
        setEditingRow({ ...record });
    };

    // 取消编辑
    const handleCancel = () => {
        setEditingKey('');
        setEditingRow({});
    };


    // 处理编辑字段变化

    const handleFieldChange = (value, field, record) => {
        const newEditingRow = { ...editingRow };
        newEditingRow[field] = value;
        setEditingRow(newEditingRow);
    };

    // 切换计划类型
    const handlePlanTypeChange = (type) => {
        setPlanType(type);
        // 根据计划类型更新周期格式
        const newData = siteEnrollmentData.map(item => ({
            ...item,
            period: type === "monthly"
                ? moment(item.period.includes('-') ? item.period.split('-')[0] : item.period).format('YYYY-MM')
                : item.period.includes('-')
                    ? item.period
                    : `${moment(item.period).startOf('month').format('YYYYMMDD')}-${moment(item.period).endOf('month').format('YYYYMMDD')}`
        }));
        setSiteEnrollmentData(newData);
    };

    // 中心入组计划表格列定义
    const siteColumns = [
        {
            title: <FormattedMessage id="common.serial" />,
            dataIndex: "sequence",
            key: "sequence",
            width: 80,
        },
        {
            title: <FormattedMessage id="common.site" />,
            dataIndex: "siteName",
            key: "siteName",
            width: 200,
            render: (text, record) => {
                const editing = isEditing(record);
                return editing ? (
                    <Select
                        value={editingRow.siteId || record.siteId}
                        onChange={(value) => {
                            const selectedSite = availableSites.find(site => site.id === value);
                            handleFieldChange(value, 'siteId', record);
                            handleFieldChange(selectedSite?.name || '', 'siteName', record);
                        }}
                        style={{ width: '100%' }}
                    >
                        {availableSites.map(site => (
                            <Option key={site.id} value={site.id}>
                                {site.name}
                            </Option>
                        ))}
                    </Select>
                ) : text;
            }
        },
        {
            title: <FormattedMessage id="material.forecast.settings.planned.enrollment" />,
            dataIndex: "plannedEnrollment",
            key: "plannedEnrollment",
            width: 120,
            render: (text, record) => {
                const editing = isEditing(record);
                return editing ? (
                    <InputNumber
                        value={editingRow.plannedEnrollment || record.plannedEnrollment}
                        onChange={(value) => handleFieldChange(value, 'plannedEnrollment', record)}
                        min={0}
                        style={{ width: '100%' }}
                    />
                ) : text;
            }
        },
        {
            title: <FormattedMessage id="material.forecast.settings.actual.enrollment" />,
            dataIndex: "actualEnrollment",
            key: "actualEnrollment",
            width: 120,
            render: (text, record) => {
                const editing = isEditing(record);
                return editing ? (
                    <InputNumber
                        value={editingRow.actualEnrollment || record.actualEnrollment}
                        onChange={(value) => handleFieldChange(value, 'actualEnrollment', record)}
                        min={0}
                        style={{ width: '100%' }}
                    />
                ) : text;
            }
        },
        {
            title: <FormattedMessage id="material.forecast.settings.period" />,
            dataIndex: "period",
            key: "period",
            width: 200,
            render: (text, record) => {
                const editing = isEditing(record);
                if (!editing) return text;

                return planType === "monthly" ? (
                    <DatePicker
                        picker="month"
                        value={editingRow.period ? moment(editingRow.period) : moment(record.period)}
                        onChange={(date) => handleFieldChange(date ? date.format('YYYY-MM') : '', 'period', record)}
                        style={{ width: '100%' }}
                        format="YYYY-MM"
                    />
                ) : (
                    <RangePicker
                        value={editingRow.period ?
                            [moment(editingRow.period.split('-')[0]), moment(editingRow.period.split('-')[1])] :
                            [moment(record.period.split('-')[0]), moment(record.period.split('-')[1])]
                        }
                        onChange={(dates) => {
                            const periodValue = dates && dates.length === 2 ?
                                `${dates[0].format('YYYYMMDD')}-${dates[1].format('YYYYMMDD')}` : '';
                            handleFieldChange(periodValue, 'period', record);
                        }}
                        style={{ width: '100%' }}
                        format="YYYYMMDD"
                    />
                );
            }
        },
        {
            title: <FormattedMessage id="common.operation" />,
            key: "operation",
            width: 120,
            render: (_, record) => {
                const editing = isEditing(record);
                return editing ? (
                    <Space>
                        <Button
                            type="link"
                            size="small"
                            icon={<CheckOutlined />}
                            onClick={() => handleRowSave(record.key)}
                            style={{ padding: 0, color: '#52c41a' }}
                        />
                        <Button
                            type="link"
                            size="small"
                            icon={<CloseOutlined />}
                            onClick={handleCancel}
                            style={{ padding: 0, color: '#ff4d4f' }}
                        />
                    </Space>
                ) : (
                    <Button
                        type="link"
                        size="small"
                        onClick={() => handleEdit(record)}
                        style={{ padding: 0 }}
                    >
                        <FormattedMessage id="common.edit" />
                    </Button>
                );
            },
        },
    ];

    // 过滤中心数据
    const filteredSiteData = siteEnrollmentData.filter(item =>
        item.siteName.toLowerCase().includes(siteSearchText.toLowerCase())
    );

    React.useImperativeHandle(ref, () => ({
        show,
        hide
    }));

    return (
        <Modal
            title={<FormattedMessage id="common.setting" />}
            open={visible}
            onCancel={hide}
            onOk={handleSave}
            confirmLoading={loading}
            width={900}
            okText={<FormattedMessage id="common.save" />}
            cancelText={<FormattedMessage id="common.cancel" />}
            bodyStyle={{ maxHeight: '70vh', overflowY: 'auto' }}
        >
            <div>
                {/* 项目入组计划 */}
                <CustomTitle
                    name={<FormattedMessage id="material.forecast.settings.project.enrollment.plan" />}
                />
                <Form layout="vertical" style={{ marginBottom: 24 }}>
                    <Form.Item label={<FormattedMessage id="material.forecast.settings.enrollment.period" />}>
                        <Space>
                            <RangePicker
                                value={projectEnrollmentPeriod}
                                onChange={setProjectEnrollmentPeriod}
                                format="YYYY-MM-DD"
                                placeholder={[
                                    <FormattedMessage id="common.time.start" />,
                                    <FormattedMessage id="common.time.end" />
                                ]}
                            />
                            <span style={{ marginLeft: 16 }}>
                                <FormattedMessage id="material.forecast.settings.target.count" />:
                            </span>
                            <Input
                                type="number"
                                value={projectTargetCount}
                                onChange={(e) => setProjectTargetCount(e.target.value)}
                                style={{ width: 100 }}
                            />
                        </Space>
                    </Form.Item>
                </Form>

                {/* 中心入组计划 */}
                <CustomTitle
                    name={<FormattedMessage id="material.forecast.settings.site.enrollment.plan" />}
                />
                <div style={{ marginBottom: 16 }}>
                    <Space style={{ marginBottom: 16 }}>
                        <Button
                            type={planType === "monthly" ? "primary" : "default"}
                            onClick={() => handlePlanTypeChange("monthly")}
                        >
                            <FormattedMessage id="material.forecast.settings.monthly.enrollment.plan" />
                        </Button>
                        <Button
                            type={planType === "weekly" ? "primary" : "default"}
                            onClick={() => handlePlanTypeChange("weekly")}
                        >
                            <FormattedMessage id="material.forecast.settings.weekly.enrollment.plan" />
                        </Button>
                    </Space>

                    <div style={{ marginBottom: 16 }}>
                        <Input
                            placeholder={<FormattedMessage id="material.forecast.settings.search.site" />}
                            value={siteSearchText}
                            onChange={(e) => setSiteSearchText(e.target.value)}
                            prefix={<SearchOutlined />}
                            style={{ width: 300 }}
                            allowClear
                        />
                        <Button
                            type="link"
                            style={{ float: 'right' }}
                        >
                            <FormattedMessage id="common.setting.batch" />
                        </Button>
                    </div>

                    <Table
                        columns={siteColumns}
                        dataSource={filteredSiteData}
                        pagination={false}
                        size="small"
                        scroll={{ y: 300 }}
                    />
                    <PaginationView />
                </div>
            </div>
        </Modal>
    );
});
