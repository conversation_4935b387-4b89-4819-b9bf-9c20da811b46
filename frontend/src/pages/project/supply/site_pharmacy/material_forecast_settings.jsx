import React from "react";
import { Modal, Form, Input, DatePicker, Button, Table, Space, message } from "antd";
import { useSafeState } from "ahooks";
import { useAuth } from "../../../../context/auth";
import { FormattedMessage } from "../../../common/multilingual/component";
import { SearchOutlined } from '@ant-design/icons';
import { Title as CustomTitle } from "../../../../components/title";
import moment from "moment";

const { RangePicker } = DatePicker;

export const MaterialForecastSettings = React.forwardRef((props, ref) => {
    const [visible, setVisible] = useSafeState(false);
    const [loading, setLoading] = useSafeState(false);
    
    // 项目入组计划数据
    const [projectEnrollmentPeriod, setProjectEnrollmentPeriod] = useSafeState([]);
    const [projectTargetCount, setProjectTargetCount] = useSafeState(500);
    
    // 中心入组计划数据
    const [siteSearchText, setSiteSearchText] = useSafeState("");
    const [siteEnrollmentData, setSiteEnrollmentData] = useSafeState([
        {
            key: 1,
            sequence: 1,
            siteName: "22-华西医院",
            plannedEnrollment: 88,
            actualEnrollment: 88,
            period: "2020-09"
        },
        {
            key: 2,
            sequence: 2,
            siteName: "中心编号-中心名称",
            plannedEnrollment: 66,
            actualEnrollment: 66,
            period: "20200201-20200207"
        }
    ]);

    const auth = useAuth();

    const show = () => {
        setVisible(true);
    };

    const hide = () => {
        setVisible(false);
    };

    const handleSave = () => {
        setLoading(true);
        // 这里添加保存逻辑
        setTimeout(() => {
            setLoading(false);
            message.success("设置保存成功");
            hide();
        }, 1000);
    };

    const handleEdit = (record) => {
        // 编辑逻辑
        console.log("编辑记录:", record);
    };

    // 中心入组计划表格列定义
    const siteColumns = [
        {
            title: <FormattedMessage id="common.serial" />,
            dataIndex: "sequence",
            key: "sequence",
            width: 80,
        },
        {
            title: <FormattedMessage id="common.site" />,
            dataIndex: "siteName",
            key: "siteName",
            width: 200,
        },
        {
            title: <FormattedMessage id="material.forecast.settings.planned.enrollment" />,
            dataIndex: "plannedEnrollment",
            key: "plannedEnrollment",
            width: 120,
        },
        {
            title: <FormattedMessage id="material.forecast.settings.actual.enrollment" />,
            dataIndex: "actualEnrollment",
            key: "actualEnrollment",
            width: 120,
        },
        {
            title: <FormattedMessage id="material.forecast.settings.period" />,
            dataIndex: "period",
            key: "period",
            width: 150,
        },
        {
            title: <FormattedMessage id="common.operation" />,
            key: "operation",
            width: 100,
            render: (_, record) => (
                <Button 
                    type="link" 
                    size="small" 
                    onClick={() => handleEdit(record)}
                    style={{ padding: 0 }}
                >
                    <FormattedMessage id="common.edit" />
                </Button>
            ),
        },
    ];

    // 过滤中心数据
    const filteredSiteData = siteEnrollmentData.filter(item =>
        item.siteName.toLowerCase().includes(siteSearchText.toLowerCase())
    );

    React.useImperativeHandle(ref, () => ({
        show,
        hide
    }));

    return (
        <Modal
            title={<FormattedMessage id="common.setting" />}
            open={visible}
            onCancel={hide}
            onOk={handleSave}
            confirmLoading={loading}
            width={900}
            okText={<FormattedMessage id="common.save" />}
            cancelText={<FormattedMessage id="common.cancel" />}
            bodyStyle={{ maxHeight: '70vh', overflowY: 'auto' }}
        >
            <div>
                {/* 项目入组计划 */}
                <CustomTitle
                    name={<FormattedMessage id="material.forecast.settings.project.enrollment.plan" />}
                />
                <Form layout="vertical" style={{ marginBottom: 24 }}>
                    <Form.Item label={<FormattedMessage id="material.forecast.settings.enrollment.period" />}>
                        <Space>
                            <RangePicker
                                value={projectEnrollmentPeriod}
                                onChange={setProjectEnrollmentPeriod}
                                format="YYYY-MM-DD"
                                placeholder={[
                                    <FormattedMessage id="common.time.start" />,
                                    <FormattedMessage id="common.time.end" />
                                ]}
                            />
                            <span style={{ marginLeft: 16 }}>
                                <FormattedMessage id="material.forecast.settings.target.count" />:
                            </span>
                            <Input
                                type="number"
                                value={projectTargetCount}
                                onChange={(e) => setProjectTargetCount(e.target.value)}
                                style={{ width: 100 }}
                            />
                        </Space>
                    </Form.Item>
                </Form>

                {/* 中心入组计划 */}
                <CustomTitle
                    name={<FormattedMessage id="material.forecast.settings.site.enrollment.plan" />}
                />
                <div style={{ marginBottom: 16 }}>
                    <Space style={{ marginBottom: 16 }}>
                        <Button type="primary">
                            <FormattedMessage id="material.forecast.settings.monthly.enrollment.plan" />
                        </Button>
                        <Button>
                            <FormattedMessage id="material.forecast.settings.weekly.enrollment.plan" />
                        </Button>
                    </Space>

                    <div style={{ marginBottom: 16 }}>
                        <Input
                            placeholder={<FormattedMessage id="material.forecast.settings.search.site" />}
                            value={siteSearchText}
                            onChange={(e) => setSiteSearchText(e.target.value)}
                            prefix={<SearchOutlined />}
                            style={{ width: 300 }}
                            allowClear
                        />
                        <Button
                            type="link"
                            style={{ float: 'right' }}
                        >
                            <FormattedMessage id="common.setting.batch" />
                        </Button>
                    </div>

                    <Table
                        columns={siteColumns}
                        dataSource={filteredSiteData}
                        pagination={{
                            current: 1,
                            pageSize: 20,
                            total: filteredSiteData.length,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: (total, range) =>
                                `已选中${range[0]}, 共 ${total} 条记录`,
                            pageSizeOptions: ['20', '50', '100'],
                        }}
                        size="small"
                        scroll={{ y: 300 }}
                    />
                </div>
            </div>
        </Modal>
    );
});
