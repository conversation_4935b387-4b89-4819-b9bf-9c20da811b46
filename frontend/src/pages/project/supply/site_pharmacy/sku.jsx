import React, { useEffect } from "react";
import { Badge, Col, DatePicker, Form, Input, message, Popover, Row, Select, Space, Spin, Table } from "antd";
import { HistoryList } from "../../../common/history-list.jsx";
import { SelectedFreeze } from "./selected_freeze.jsx";
import { Reason } from "./reason";
import { permissions } from '../../../../tools/permission'
import Barcode from "react-barcode";
import { useSafeState } from "ahooks";
import { medicineStatusColors } from "../../../../data/data";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import { downloadSiteSku, medicineSiteSku } from "../../../../api/medicine";
import { medicineStatusAliData } from "../../../../tools/medicine_status";
import { PaginationView } from "../../../../components/pagination";
import { usePage } from "../../../../context/page";
import { fillTableCellEmptyPlaceholder } from "../../../../components/table";
import { TitledPopover } from "../../../../components/popover";
import Footer from "../../../main/layout/footer";
import { userSites } from "../../../../api/project_site";
import { getProjectAttributeConnect } from "../../../../api/randomization";
import { getBarcodeRule } from "../../../../api/barcode";
import { MakeAvailable } from "./make_medicine_available.jsx";
import { FormattedMessage, useTranslation } from "../../../common/multilingual/component";
import { AuthButton } from "../../../common/auth-wrap";
import { VirtualTable } from "../../../../components/virtual-tabel";
import { PageContextProvider } from "../../../../context/page";

export const Sku = (props) => {

    const intl = useTranslation();
    const { formatMessage } = intl;
    const auth = useAuth()
    const page = usePage();

    const [field, setField] = useSafeState(undefined);
    const [fieldDate, setFieldDate] = useSafeState(null);
    const [fieldValue, setFieldValue] = useSafeState("");
    const searchInputRef = React.useRef();
    const [doSearch, setDoSearch] = useSafeState(0);
    const [site, setSite] = useSafeState(undefined);
    const [status, setStatus] = useSafeState(undefined);
    const [summaryList, setSummaryList] = useSafeState([]);
    const [selecteds, setSelecteds] = useSafeState([]);
    const [selectedIds, setSelectedIds] = useSafeState([]);
    const [isOpenPackage, setIsOpenPackage] = useSafeState(false);
    const [sites, setSites] = useSafeState([]);
    const selected_freeze = React.useRef()
    const make_available = React.useRef()
    const [codeRule, setCodeRule] = useSafeState(null);
    const [paramSites, setParamSites] = useSafeState([])
    const [paramSitesOp, setParamSitesOp] = useSafeState(1)
    const [siteMode, setSiteMode] = useSafeState({})
    const [siteOpen, setSiteOpen] = useSafeState(false)
    const reason_ref = React.useRef()
    const envId = auth.env ? auth.env.id : null;
    const projectId = auth.project.id;
    const customerId = auth.customerId;
    const projectStatus = auth.project.status ? auth.project.status : 0
    const history_ref = React.useRef();
    // const codeRule = auth.codeRule;
    // const fields = [
    //     { id: 'number', name: formatMessage({ id: 'drug.list.drugNumber' }) },
    //     { id: 'name', name: formatMessage({ id: 'drug.configure.drugName' }) },
    //     { id: 'batch_number', name: formatMessage({ id: 'drug.list.batch' }) },
    //     { id: 'expiration_date', name: formatMessage({ id: 'projects.statistics.sku.expirationDate' }) },
    //     // { id: 'site.address', name: formatMessage({ id: 'projects.statistics.sku.place' }) },
    //     { id: 'order_number', name: formatMessage({ id: 'shipment.orderNumber' }) },
    // ]
    const [fields, setFields] = useSafeState([]);
    const [ali, setAli] = useSafeState(0);

    const { runAsync: getProjectAttributeConnectRun, loading: getProjectAttributeConnectLoading } = useFetch(getProjectAttributeConnect, { manual: true });
    const { runAsync: run_medicineSiteSku, loading } = useFetch(medicineSiteSku, { manual: true })
    const { runAsync: run_userSites } = useFetch(userSites, { manual: true });
    const { runAsync: getBarcodeRun } = useFetch(getBarcodeRule, { manual: true })



    const barcodeCodeRule = () => {
        getBarcodeRun({
            customerId: customerId,
            projectId: projectId,
            envId: envId,
        }).then((response) => {
            if (response) {
                let data = response.data;
                setCodeRule(data.codeRule1);
            }
        });
    };

    // function escapeRegexSpecialChars(str) {
    //     if (str) {
    //         // 正则表达式特殊字符
    //         var specialChars = /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g;

    //         // 使用replace方法和函数作为第二个参数来转义每个匹配项
    //         return str.replace(specialChars, '\\$&');
    //     }

    //     return str

    // }

    const search = () => {
        getProjectAttributeConnectRun({ projectId: projectId, envId: envId }).then((res) => {
            if (res.data) {
                if (res.data.info.connectAli) {
                    setAli(1)
                }
            }
        });
        //if (field !== undefined && field != null && field !== "") {
        // let key = escapeRegexSpecialChars(fieldValue);
        let key = fieldValue;
        if (fieldValue !== null && fieldValue !== undefined) {
            key = fieldValue.trim();
        }
        run_medicineSiteSku({
            customerId: customerId,
            projectId: projectId,
            envId: envId,
            //siteId: site ? site.split("__")[0] : null,
            siteIds: paramSites,
            status: String(status),
            field: field,
            fieldValue: key,
            roleId: auth.project.permissions.role_id,
            start: (page.currentPage - 1) * page.pageSize,
            limit: page.pageSize,
        }).then((result) => {
            let response = result.data
            setSummaryList(fillTableCellEmptyPlaceholder(response.items ? response.items : []))
            page.setTotal(response.total)
            setIsOpenPackage(response.isOpenPackage)
            if (response.isOpenPackage) {
                setFields([
                    { id: 'number', name: formatMessage({ id: 'drug.list.drugNumber', allowComponent: true }) },
                    { id: 'name', name: formatMessage({ id: 'drug.configure.drugName', allowComponent: true }) },
                    { id: 'batch_number', name: formatMessage({ id: 'drug.list.batch', allowComponent: true }) },
                    { id: 'expiration_date', name: formatMessage({ id: 'projects.statistics.sku.expirationDate', allowComponent: true }) },
                    { id: 'order_number', name: formatMessage({ id: 'shipment.orderNumber', allowComponent: true }) },
                    { id: 'package_number', name: formatMessage({ id: 'drug.medicine.packlist', allowComponent: true }) },
                ])
                if (field === undefined || field === null || field === "") {
                    setField("number")
                }
            } else {
                setFields([
                    { id: 'number', name: formatMessage({ id: 'drug.list.drugNumber', allowComponent: true }) },
                    { id: 'name', name: formatMessage({ id: 'drug.configure.drugName', allowComponent: true }) },
                    { id: 'batch_number', name: formatMessage({ id: 'drug.list.batch', allowComponent: true }) },
                    { id: 'expiration_date', name: formatMessage({ id: 'projects.statistics.sku.expirationDate', allowComponent: true }) },
                    { id: 'order_number', name: formatMessage({ id: 'shipment.orderNumber', allowComponent: true }) },
                ])
                if (field === undefined || field === null || field === "") {
                    setField("number")
                }
            }
        })
        //}
    }

    useEffect(
        () => {
            setField((fields && fields.length > 0) ? fields[0].id : "number")
            run_userSites({
                projectId: projectId,
                customerId: customerId,
                envId: envId,
                roleId: auth.project.permissions.role_id
            }).then((result) => {
                const response = result.data
                let list = [];
                if (response) {
                    response.forEach(item => {
                        list.push({ id: item.id, name: item.name, number: item.number })
                    });
                    // if (list && list.length > 0) {
                    //     let s = list[0];
                    //     setSite(s.id + '__' + s.name);
                    // }
                }
                setSites(list);
            });
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );


    useEffect(search, [paramSites, status, doSearch, page.currentPage, page.pageSize])

    useEffect(barcodeCodeRule, [])

    const showHistory = (id, timeZone, tz) => {
        if (timeZone && (timeZone !== "-")) {
            let str = timeZone.replace('UTC', '');
            str = str.replace('+', '');
            history_ref.current.show("history.medicine", id, null, str, null, tz);
        } else {
            history_ref.current.show("history.medicine", id, timeZone, null, null, tz);

        }
    };

    // const showReasonModal = (id, status) => {
    //     reason_ref.current.show(id, status);
    // };

    function canUseMedicine(record) {
        make_available.current.show([record], [record.id], 2, isOpenPackage, false, sites);
    }

    const rowSelection = {
        type: "checkbox",
        onChange: (selectedRowKeys, selectedRows) => {
            setSelecteds(selectedRows);
            setSelectedIds(selectedRowKeys);
        },
        selectedRows: selecteds,
        selectedRowKeys: selectedIds,
        getCheckboxProps: (record) => ({
            disabled: record.status !== 1 && record.status !== 6 && record.status !== 14 && record.status !== 7
        }),
        preserveSelectedRowKeys: true,
    };

    const resetSelected = () => {
        setSelecteds([])
        setSelectedIds([])
    }

    function freezeMedicine(status) {
        var medicines = [];
        var medicineIds = [];
        //status 4隔离 6丢失/作废
        if (status === 4) { //隔离操作只隔离状态为1可用14冻结的药物
            selecteds.forEach((it) => {
                if (it.status === 1 || it.status === 14) {
                    medicines.push(it);
                    medicineIds.push(it.id);
                }
            });
        } else if (status === 6) {//丢失/作为操作只能操作状态为过期的药物
            selecteds.forEach((it) => {
                if (it.status === 1 || it.status === 7) {
                    medicines.push(it);
                    medicineIds.push(it.id);
                }
            });
        } else if (status === 1) {//设为可用操作只能操作状态为丢失/作废的药物
            selecteds.forEach((it) => {
                if (it.status === 6) {
                    medicines.push(it);
                    medicineIds.push(it.id);
                }
            });
        }
        if (medicineIds.length <= 0) {
            //message.error(formatMessage({ id: 'drug.freeze.selectData' }))
            // message.warn(formatMessage({id: "menu.projects.project.build.randomization.tooltip"}))
            if (status === 4) {
                message.warn(formatMessage({ id: "single.freeze.freeze.info" }, { total: 0 }))
            } else if (status === 6) {
                message.warn(formatMessage({ id: "single.freeze.lost.info" }, { total: 0 }))
                // message.warn(formatMessage({ id: "single.freeze.lost.info.hint" }))
            } else if (status === 1) {
                message.warn(formatMessage({ id: "single.freeze.setUse.info" }, { total: 0 }))
            }
        } else {
            //判断是否需要toast提示
            var toastFlag = false;
            if (selecteds.length > medicines.length) {
                toastFlag = true;
            }
            if (status === 1) {
                make_available.current.show(medicines, medicineIds, 2, isOpenPackage, toastFlag, sites);
            } else {
                selected_freeze.current.show(medicines, medicineIds, 1, paramSites, isOpenPackage, status, toastFlag);
            }

        }
    }

    const { runAsync: run_downloadSiteSku, loading: downloadLoading } = useFetch(downloadSiteSku, { manual: true })

    function downloadData() {
        // let key = escapeRegexSpecialChars(fieldValue);
        let key = fieldValue;
        if (fieldValue !== null && fieldValue !== undefined) {
            key = fieldValue.trim();
        }
        run_downloadSiteSku(
            {
                customerId: customerId,
                projectId: projectId,
                envId: envId,
                siteId: site ? site.split("__")[0] : null,
                status: status,
                field: field,
                fieldValue: key,
                roleId: auth.project.permissions.role_id
            }).catch(
                () => {
                    message.error(props.intl.formatMessage({ id: 'common.download.fail' })).then()
                }
            )
    }


    const refreshList = () => {
        search();
        resetSelected()
    };

    return (
        <React.Fragment>
            <Form>
                <Spin spinning={loading}>
                    <Row justify="space-between" wrap={false}>
                        <Col span={18}>
                            <Form layout="inline">
                                <Form.Item label={formatMessage({ id: 'common.site', allowComponent: true })}>
                                    {/* <Select style={{ minWidth: 160, maxWidth: 200 }}
                                        dropdownStyle={{ maxWidth: 400 }}
                                        dropdownMatchSelectWidth={false}
                                        allowClear showSearch value={site} onChange={(value) => { page.setCurrentPage(1); setSite(value); setSelectedIds([]); }}
                                        // placeholder={<span style={{color: "#1D2129"}}>{formatMessage({id: 'common.all'})}</span>}
                                        left={6}
                                        filterOption={(input, option) => {
                                            const childrenText = option.props.children;
                                            if (typeof childrenText === 'string') {
                                                return childrenText.trim().toLowerCase().indexOf(input.trim().toLowerCase()) >= 0;
                                            }
                                            return false;
                                        }}
                                    > */}
                                    <Select
                                        value={paramSitesOp}
                                        style={{ width: 300 }}
                                        {...siteMode}
                                        maxTagCount={1}
                                        maxTagTextLength={12}
                                        open={siteOpen}
                                        onBlur={() => {
                                            setSiteOpen(false)
                                            if (paramSitesOp !== null) {
                                                setDoSearch(doSearch + 1)
                                            }
                                        }

                                        }
                                        showArrow={true}
                                        showSearch={false}
                                        onDropdownVisibleChange={(visible) => setSiteOpen(visible)}
                                        onChange={(value) => {
                                            if (value === 1 || (Array.isArray(value) && (value.find((i) => i === 1) || value.length === 0))) {
                                                if (siteMode.mode != null) {
                                                    setSiteMode({})
                                                    setSiteOpen(true)
                                                }
                                                setParamSites([])
                                                setParamSitesOp(1)
                                                setDoSearch(doSearch + 1)
                                                setSiteOpen(false)
                                            } else {
                                                setSiteOpen(true)
                                                if (siteMode.mode !== "multiple") {
                                                    setSiteMode({ mode: "multiple", })
                                                    setDoSearch(doSearch + 1)
                                                    setSiteOpen(true)
                                                }
                                                if (!Array.isArray(value)) {
                                                    let siteIds = [value];
                                                    setParamSites(siteIds)
                                                    setParamSitesOp(siteIds)
                                                    setDoSearch(doSearch + 1)
                                                } else {
                                                    setParamSites(value)
                                                    setParamSitesOp(value)
                                                    setDoSearch(doSearch + 1)
                                                }
                                            }
                                        }}
                                    >
                                        <Select.Option value={1}>{formatMessage({ id: "common.all", allowComponent: true })}</Select.Option>
                                        {
                                            props.sites.map(
                                                it => (
                                                    <Select.Option key={it.id} value={it.id} >
                                                        {/* <Tooltip title={it.number + "-" + it.name}>{it.number + "-" + it.name}</Tooltip> */}
                                                        {(it.number ? it.number + "-" : "") + it.name}
                                                    </Select.Option>
                                                )
                                            )
                                        }
                                    </Select>
                                </Form.Item>

                                <Form.Item label={formatMessage({ id: 'common.status', allowComponent: true })}>
                                    <Select style={{ width: 135 }}
                                        allowClear value={status} onChange={(value) => { page.setCurrentPage(1); setStatus(value) }}
                                        placeholder={<span style={{ color: "#1D2129" }}>{formatMessage({ id: 'common.all' })}</span>}
                                        left={6}
                                    >
                                        {
                                            medicineStatusAliData(auth.codeRule, auth.project.info.research_attribute, ali).map(
                                                it => (
                                                    <Select.Option key={it.value} value={it.value}>
                                                        {it.label}
                                                    </Select.Option>
                                                )
                                            )
                                        }
                                    </Select>
                                </Form.Item>
                                <Col span={8}>
                                    <Input.Group compact style={{ display: "flex" }}>
                                        <Select style={{ width: 130 }}
                                            placeholder={<FormattedMessage id="projects.statistics.selectField" />}
                                            value={field} onChange={value => {
                                                setFieldValue(null);
                                                setFieldDate(null);
                                                setField(value);
                                            }}>
                                            {
                                                fields.map(
                                                    it => (
                                                        <Select.Option key={it.id} value={it.id}>
                                                            {it.name}
                                                        </Select.Option>
                                                    )
                                                )
                                            }
                                        </Select>
                                        {
                                            field === "expiration_date" ?
                                                <DatePicker style={{ width: '200px' }} allowClear value={fieldDate}
                                                    onChange={e => {
                                                        setFieldDate(e);
                                                        setFieldValue(e ? e.format("YYYY-MM-DD") : null);
                                                        page.setCurrentPage(1);
                                                        setDoSearch(doSearch + 1);
                                                    }} />
                                                :
                                                <Input.Search
                                                    ref={searchInputRef}
                                                    style={{ width: '200px' }}
                                                    value={fieldValue}
                                                    onChange={e => { setFieldValue(e.target.value) }}
                                                    allowClear onSearch={() => {
                                                        searchInputRef?.current?.blur();
                                                        page.setCurrentPage(1);
                                                        setDoSearch(doSearch + 1);
                                                    }}
                                                    placeholder={formatMessage({ id: "placeholder.input.common" })}

                                                />
                                        }
                                    </Input.Group>
                                </Col>
                            </Form>

                        </Col>
                        <Space size={6}>
                            {permissions(auth.project.permissions, "operation.supply.site.medicine.download") && auth.project.info.research_attribute === 1 ?
                                <AuthButton loading={downloadLoading} onClick={() => downloadData()}><FormattedMessage
                                    id="common.download.data" /></AuthButton>
                                : null
                            }
                            {permissions(auth.project.permissions, "operation.supply.site.medicine.use") && paramSites != null && paramSites !== undefined && projectStatus !== 2 ?
                                <AuthButton className="mar-lft-5" onClick={() => freezeMedicine(1)}>
                                    <FormattedMessage id="drug.list.setUse" />
                                </AuthButton>
                                : null
                            }
                            {permissions(auth.project.permissions, "operation.supply.site.medicine.lost") && paramSites != null && paramSites !== undefined && projectStatus !== 2 ?
                                <AuthButton className="mar-lft-5" onClick={() => freezeMedicine(6)}>
                                    <FormattedMessage id="medicine.status.lose" />
                                </AuthButton>
                                : null
                            }
                            {permissions(auth.project.permissions, "operation.supply.site.medicine.freeze") && paramSites != null && paramSites !== undefined && projectStatus !== 2 ?
                                <AuthButton type='primary' className="mar-lft-5" onClick={() => freezeMedicine(4)}>
                                    <FormattedMessage id="drug.list.isolation" />
                                </AuthButton>
                                : null
                            }
                        </Space>
                    </Row>
                    <VirtualTable
                        className="mar-top-10"
                        dataSource={summaryList}
                        scroll={{ x: summaryList.length > 0 ? "100%" : false }}
                        sticky
                        style={{ maxHeight: "calc(100vh - 245px)", overflowY: "auto", }}
                        // scroll={{y: 'calc(100vh - 280px)',x: summaryList.length > 0 ? "100%" : false}}
                        //size="small"
                        pagination={false}
                        rowKey={(record) => (record.id)}
                        rowSelection={rowSelection}
                        height={() => document.documentElement.clientHeight - 295}
                    >
                        <Table.Column title={<FormattedMessage id="common.site" />}
                            dataIndex="siteName"
                            className="table-column-padding-left-16-1"
                            key="siteName"
                        />
                        {
                            isOpenPackage &&
                            <Table.Column title={<FormattedMessage id="drug.medicine.packlist" />}
                                key="packageNumber"
                                dataIndex="packageNumber"
                                ellipsis
                                render={(text, record, index) => {
                                    return (
                                        record["packageNumber"] ?
                                            record["packageNumber"]
                                            :
                                            "-"
                                    );
                                }} />
                        }
                        <Table.Column
                            title={<FormattedMessage id="drug.list.drugNumber" />}
                            className="table-column-padding-left-32"
                            dataIndex="number" key="number" ellipsis />
                        <Table.Column
                            title={<FormattedMessage id="drug.configure.drugName" />}
                            className="table-column-padding-left-32"
                            dataIndex="name" key="name" ellipsis
                        />
                        <Table.Column
                            title={<FormattedMessage id="drug.list.batch" />}
                            className="table-column-padding-left-32"
                            dataIndex="batchNumber"
                            key="batchNumber" ellipsis />
                        <Table.Column title={<FormattedMessage id="projects.statistics.sku.expirationDate" />}
                            className="table-column-padding-left-32"
                            dataIndex="expirationDate"
                            key="expirationDate" ellipsis width={122} />
                        {/*<Table.Column title={<FormattedMessage id="projects.statistics.sku.place" />}*/}
                        {/*dataIndex="place" key="place" width={300}*/}
                        {/*ellipsis />*/}
                        <Table.Column title={<FormattedMessage id="shipment.orderNumber" />}
                            className="table-column-padding-left-32"
                            dataIndex="orderNumber" width={152} render={(_, record) =>
                                <TitledPopover
                                    title={formatMessage({ id: 'shipment.order.all-no', allowComponent: true })}
                                    data={record.orderNumber}
                                    iconlink='#icon-quanbudingdanhao'
                                    contentLineHeight='32px'
                                />
                            }
                        />
                        <Table.Column title={<FormattedMessage id="projects.statistics.sku.status" />}
                            className="table-column-padding-left-32"
                            dataIndex="status" width={146}
                            key="status" ellipsis render={(text, record) => {
                                return <Badge color={medicineStatusColors[record.status]}
                                    text={medicineStatusAliData(auth.codeRule, auth.project.info.research_attribute, ali).find(it => (it.value === record.status)).label} />;
                            }} />
                        {codeRule &&
                            <>
                                <Table.Column title={<FormattedMessage id="barcode" />} key="number"
                                    dataIndex="number" align="center" ellipsis width={150}
                                    render={(value, record) => {
                                        return (
                                            <Popover content={
                                                <>
                                                    <Barcode value={value} displayValue={false} height={60} width={1}
                                                        format="CODE128" />
                                                    <br />
                                                    <span>&nbsp;&nbsp;{value}</span>
                                                </>
                                            } trigger="click">
                                                {
                                                    record.shortCode != "-" ?
                                                        <AuthButton
                                                            style={{ padding: 0, marginRight: '12px' }}
                                                            size="small"
                                                            type="link">
                                                            <FormattedMessage id="form.preview" />
                                                        </AuthButton>
                                                        :
                                                        "-"
                                                }


                                                {/*<Button><FormattedMessage id="form.preview" /></Button>*/}
                                            </Popover>
                                        )
                                    }} />
                                <Table.Column title={<FormattedMessage id="packageBarcode" />} key="packageNumber"
                                    dataIndex="packageNumber" align="center" ellipsis width={150}
                                    render={(value, record) => {
                                        return (
                                            <Popover content={
                                                <>
                                                    <Barcode value={value} displayValue={false} height={60} width={1}
                                                        format="CODE128" />
                                                    <br />
                                                    <span>&nbsp;&nbsp;{value}</span>
                                                </>
                                            } trigger="click">
                                                {
                                                    record.packageNumber != "-" && record.shortCode != "-" ?
                                                        <AuthButton
                                                            style={{ padding: 0, marginRight: '12px' }}
                                                            size="small"
                                                            type="link">
                                                            <FormattedMessage id="form.preview" />
                                                        </AuthButton>
                                                        :
                                                        "-"
                                                }
                                            </Popover>
                                        )
                                    }} />
                            </>}
                        <Table.Column
                            title={<FormattedMessage id="common.operation" />}
                            width={146}
                            className="table-column-padding-left-16-2"
                            fixed="right"
                            render={
                                (_, record) => (
                                    <React.Fragment>
                                        {
                                            permissions(auth.project.permissions, "operation.supply.site.medicine.history") ?
                                                <AuthButton size="small" type="link" onClick={() => showHistory(record.id, record.timeZone, record.tz)} style={{ padding: '0 14px 0 0' }}>
                                                    <FormattedMessage id="common.history" /></AuthButton>
                                                : null
                                        }
                                        {
                                            record.status === 6 && permissions(auth.project.permissions, "operation.supply.site.medicine.use") ?
                                                <AuthButton size="small" type="link" style={{ padding: '0 14px 0 0' }}
                                                    // onClick={() => showReasonModal(record.id, 1)}
                                                    onClick={() => canUseMedicine(record)}
                                                >
                                                    <FormattedMessage id="drug.list.setUse" /></AuthButton>
                                                : null
                                        }
                                    </React.Fragment>)
                            }
                        />
                    </VirtualTable>
                    {/*<Row justify="space-between">*/}
                    {/*    <Col />*/}
                    {/*    <Col className="pad-top-10">*/}
                    {/*        <Pagination*/}
                    {/*            hideOnSinglePage={false}*/}
                    {/*            className="text-right"*/}
                    {/*            current={currentPage}*/}
                    {/*            pageSize={pageSize}*/}
                    {/*            pageSizeOptions={['10', '20', '50', '100']}*/}
                    {/*            total={total}*/}
                    {/*            showSizeChanger*/}
                    {/*            showTotal={(total, range) => (`${range[0]} - ${range[1]} / ${total}`)}*/}
                    {/*            onChange={(page, pageSize) => {*/}
                    {/*                page.setCurrentPage(page);*/}
                    {/*            }}*/}
                    {/*            onShowSizeChange={(current, size) => {*/}
                    {/*                page.setCurrentPage(1);*/}
                    {/*                page.setPageSize(size);*/}
                    {/*            }}*/}
                    {/*        />*/}
                    {/*    </Col>*/}
                    {/*</Row>*/}

                    {
                        summaryList?.length ?
                            <Footer>
                                <PaginationView mode="SELECTABLE" selectedNumber={selectedIds?.length} clearDisplay={true} refresh={refreshList} />
                            </Footer>
                            : null
                    }

                </Spin>
            </Form>

            <HistoryList bind={history_ref} permission={permissions(auth.project.permissions, "operation.supply.site.medicine.print")} />
            <PageContextProvider>
                <SelectedFreeze bind={selected_freeze} search={search} refresh={refreshList} />
            </PageContextProvider>
            <MakeAvailable bind={make_available} search={search} refresh={refreshList} />
            <Reason bind={reason_ref} refresh={refreshList} />
        </React.Fragment >
    )
}
