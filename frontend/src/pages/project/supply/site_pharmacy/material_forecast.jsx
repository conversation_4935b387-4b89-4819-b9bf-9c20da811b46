import React from "react";
import { <PERSON><PERSON>, Col, DatePicker, Form, Input, Row, Select, Table, Spin } from "antd";
import { useFetch } from "../../../../hooks/request";
import { getSiteForecast } from "../../../../api/medicine";
import { useSafeState } from "ahooks";
import { useAuth } from "../../../../context/auth";
import { usePage } from "../../../../context/page";
import { PaginationView } from "../../../../components/pagination";
import { FormattedMessage } from "../../../common/multilingual/component";
import { SettingOutlined } from '@ant-design/icons';
import { MaterialForecastSettings } from "./material_forecast_settings";
import moment from "moment";

const { RangePicker } = DatePicker;

export const MaterialForecast = (props) => {
    const [tableHeight, setTableHeight] = useSafeState(300);
    const [selectSite, setSelectSite] = useSafeState([]);
    const [dateRange, setDateRange] = useSafeState([]);
    const [materialName, setMaterialName] = useSafeState("");
    const [forecastData, setForecastData] = useSafeState([]);
    const [loading, setLoading] = useSafeState(false);

    const auth = useAuth();
    const settingsRef = React.useRef();
    const envId = auth.env ? auth.env.id : null;
    const projectId = auth.project.id;
    const customerId = auth.customerId;
    const page = usePage();

    React.useEffect(() => {
        setTableHeight(document.documentElement.clientHeight - 330);
    }, []);

    // 使用真实的API调用
    const { runAsync: getMaterialForecast, loading: forecastLoading } = useFetch(getSiteForecast, { manual: true });

    const search = () => {
        setLoading(true);
        // 使用getSiteForecast API，它返回的是预测数据
        getMaterialForecast({
            envId: envId,
            roleId: auth.project.permissions.role_id
        }).then((result) => {
            const response = result.data;
            if (response && Array.isArray(response)) {
                // 转换数据格式以适配表格显示
                const transformedData = response.map((item, index) => ({
                    id: item.id || index,
                    materialName: item.name || item.name_en || '-',
                    needTransport: Math.random() > 0.5, // 临时随机值，实际应从API获取
                    currentStock: Math.floor(Math.random() * 200) + 50, // 临时随机值
                    predictedConsumption: Math.floor(Math.random() * 100) + 20, // 临时随机值
                    forecastDate: item.date || '-'
                }));

                // 根据筛选条件过滤数据
                let filteredData = transformedData;

                if (selectSite && selectSite.length > 0) {
                    filteredData = filteredData.filter(item =>
                        selectSite.includes(item.id)
                    );
                }

                if (materialName) {
                    filteredData = filteredData.filter(item =>
                        item.materialName.toLowerCase().includes(materialName.toLowerCase())
                    );
                }

                page.setTotal(filteredData.length);
                setForecastData(filteredData);
            }
        }).finally(() => {
            setLoading(false);
        });
    };

    const handleSettings = () => {
        // 打开设置弹窗
        settingsRef.current?.show();
    };

    React.useEffect(() => {
        search();
    }, [selectSite, dateRange, materialName, page.currentPage, page.pageSize]);

    const columns = [
        {
            title: <FormattedMessage id="projects.material.forecast.name" />,
            dataIndex: "materialName",
            key: "materialName",
            width: 200,
            ellipsis: true,
        },
        {
            title: <FormattedMessage id="projects.material.forecast.need.transport" />,
            dataIndex: "needTransport",
            key: "needTransport",
            width: 150,
            render: (value) => (
                <span style={{ color: value ? '#52c41a' : '#ff4d4f' }}>
                    <FormattedMessage id={value ? "common.yes" : "common.no"} />
                </span>
            ),
        },
        {
            title: <FormattedMessage id="projects.material.forecast.current.stock" />,
            dataIndex: "currentStock",
            key: "currentStock",
            width: 150,
            render: (value) => (
                <span style={{ fontWeight: 'bold' }}>{value}</span>
            ),
        },
        {
            title: <FormattedMessage id="projects.material.forecast.predicted.consumption" />,
            dataIndex: "predictedConsumption",
            key: "predictedConsumption",
            width: 180,
            render: (value) => (
                <span style={{ color: '#1890ff', fontWeight: 'bold' }}>{value}</span>
            ),
        },
    ];

    return (
        <React.Fragment>
            <Row gutter={16} style={{ marginBottom: 16 }}>
                <Col xs={24} sm={12} md={6} lg={6}>
                    <Form.Item label={<FormattedMessage id="common.date.range" />}>
                        <RangePicker
                            style={{ width: "100%" }}
                            value={dateRange}
                            onChange={setDateRange}
                            format="YYYY-MM-DD"
                            placeholder={[
                                <FormattedMessage id="common.start.date" />,
                                <FormattedMessage id="common.end.date" />
                            ]}
                        />
                    </Form.Item>
                </Col>
                
                <Col xs={24} sm={12} md={6} lg={6}>
                    <Form.Item label={<FormattedMessage id="common.site" />}>
                        <Select
                            style={{ width: "100%" }}
                            allowClear
                            showSearch
                            mode="multiple"
                            value={selectSite}
                            onChange={setSelectSite}
                            placeholder={<FormattedMessage id="common.all" />}
                            filterOption={(input, option) => {
                                const childrenText = option.props.children;
                                if (typeof childrenText === 'string') {
                                    return childrenText.trim().toLowerCase().indexOf(input.trim().toLowerCase()) >= 0;
                                }
                                return false;
                            }}
                        >
                            {props.sites.map(site => (
                                <Select.Option key={site.id} value={site.id}>
                                    {(site.number ? site.number + "-" : "") + site.name}
                                </Select.Option>
                            ))}
                        </Select>
                    </Form.Item>
                </Col>

                <Col xs={24} sm={12} md={6} lg={6}>
                    <Form.Item label={<FormattedMessage id="projects.material.forecast.name" />}>
                        <Input
                            style={{ width: "100%" }}
                            value={materialName}
                            onChange={(e) => setMaterialName(e.target.value)}
                            placeholder={<FormattedMessage id="placeholder.input.common" />}
                            allowClear
                        />
                    </Form.Item>
                </Col>

                <Col xs={24} sm={12} md={6} lg={6}>
                    <Form.Item label=" ">
                        <Button
                            type="link"
                            icon={<SettingOutlined />}
                            onClick={handleSettings}
                        >
                            <FormattedMessage id="common.setting" />
                        </Button>
                    </Form.Item>
                </Col>
            </Row>

            <Spin spinning={loading || forecastLoading}>
                <Table
                    dataSource={forecastData}
                    columns={columns}
                    rowKey="id"
                    pagination={false}
                    scroll={{ y: tableHeight }}
                    style={{ overflowX: "auto" }}
                />
            </Spin>

            <PaginationView />

            <MaterialForecastSettings ref={settingsRef} />
        </React.Fragment>
    );
};
