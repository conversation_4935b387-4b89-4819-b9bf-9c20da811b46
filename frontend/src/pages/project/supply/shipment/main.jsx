import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Col,
  Dropdown,
  Form,
  Input,
  Menu,
  message,
  Row,
  Select,
  Spin,
  Table,
  Tooltip,
} from "antd";
import { Add } from "./add";
import { Medicines } from "./medicines.jsx";
import { useAuth } from "../../../../context/auth";
import { PaginationView } from "../../../../components/pagination";
import { usePage } from "../../../../context/page";
import { useSafeState, useUpdateEffect } from "ahooks";
import { permissions } from "../../../../tools/permission";
import { nilObjectId, shipmentStatus } from "../../../../data/data";
import { useFetch } from "../../../../hooks/request";
import {
  getUserSitesAndStorehouses,
  sitesAndStorehouses,
} from "../../../../api/project_site";
import {
  alarmMedicine,
  downloadOrder,
  getOrderList,
  getOrderSubject,
  updateOrderStatus,
} from "../../../../api/order";
import { HistoryList } from "../../../common/history-list";
import { Receive } from "./receive";
import { Confirm } from "./confirm";
import { Approval } from "./approval";
import { ReasonOrder } from "./reasonOrder";
import { CustomConfirmModal } from "components/modal";
import { InsertDivider } from "components/divider";
import { Logistics } from "../drug_dtp/logistics";
import { roleIsBind } from "../../../../api/roles";
import { getDrugNamesByRoleId } from "../../../../api/drug";
import { useProject } from "../../context";
import { TooltipParagraph } from "components/TooltipParagraph";
import { ShipDetail } from "./ShipDetail";
import { useGlobal } from "../../../../context/global";
import { PageContextProvider } from "../../../../context/page";
import { FormattedMessage, useTranslation } from "../../../common/multilingual/component";
import { AuthButton, AuthMenuItem, AuthSpan } from "../../../common/auth-wrap";

export const Main = (props) => {
  const [form] = Form.useForm();
  const auth = useAuth();
  const page = usePage();
  const project = useProject();
  const [storehouses, setStorehouses] = useSafeState([]);
  const [sites, setSites] = useSafeState([]);
  const [subjects, setSubjects] = useSafeState([]);
  const timeZone = (auth.project.info.timeZoneStr !== undefined && auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "") ? Number(auth.project.info.timeZoneStr) : 8;
  const customerId = auth.customerId;
  const projectId = auth.project.id;
  const envId = auth.env ? auth.env.id : null;
  const projectStatus = auth.project.status ? auth.project.status : 0;
  const orderCheck = auth.project.info.order_check
    ? auth.project.info.order_check
    : 1;
  const researchAttribute = auth.project.info.research_attribute ? auth.project.info.research_attribute : 0;
  const statusArr = researchAttribute === 0 ? shipmentStatus.filter((it) => it.value !== 7) : shipmentStatus
  const g = useGlobal();
  const intl = useTranslation();
  const { formatMessage } = intl;
  const [loading, setLoading] = useSafeState(false);
  const [orderNumber, setOrderNumber] = useSafeState("");
  const [doSearch, setDoSearch] = useSafeState(0);
  const searchInputRef = React.useRef();
  const [receiveId, setReceiveId] = useSafeState(undefined);
  const [sendId, setSendId] = useSafeState(undefined);
  const [status, setStatus] = useSafeState(undefined);
  const [isBlind, setIsBlind] = useSafeState(true);
  const [institutes, setInstitutes] = useSafeState([]);
  const [drugNameDefaultValue, setDrugNameDefaultValue] = useSafeState([]);
  const [drugData, setDrugData] = useSafeState([]);
  const [packageIsOpen, setPackageIsOpen] = useSafeState(false);
  const [haveDrug, setHaveDrug] = useSafeState(true);
  const [haveOtherDrug, setHaveOtherDrug] = useSafeState(true);
  const [mixPackage, setMixPackage] = useSafeState(null);
  const [height, setHeight] = useSafeState(window.innerHeight - 210);
  const add_ref = React.useRef();
  const receive_ref = React.useRef();
  const medicines_ref = React.useRef();
  const reason_ref = React.useRef();
  const confirm_ref = React.useRef();
  const history_ref = React.useRef();
  const logistics_ref = React.useRef();
  const approval_ref = React.useRef();
  const detailRef = React.useRef();

  const handleResize = () => {
    setHeight(window.innerHeight - 210);
  };
  React.useEffect(() => {
    // 监听
    window.addEventListener("resize", handleResize);
    // 销毁
    return () => window.removeEventListener("resize", handleResize);
  });

  const { runAsync: run_getProjectUserSites } = useFetch(
    getUserSitesAndStorehouses,
    { manual: true }
  );
  const { runAsync: run_sitesAndStorehouses } = useFetch(sitesAndStorehouses, {
    manual: true,
  });
  const { runAsync: run_getOrderList } = useFetch(getOrderList, {
    manual: true,
  });
  const { runAsync: run_getOrderSubject } = useFetch(getOrderSubject, {
    manual: true,
  });
  const { runAsync: run_download, loading: downloading } = useFetch(
    downloadOrder,
    { manual: true }
  );
  const { runAsync: run_updateOrderStatus, loading: updateOrderStatusLoading } =
    useFetch(updateOrderStatus, { manual: true });
  //const { runAsync: run_addWorkTask, loading: addWorkTaskLoading } = useFetch(addWorkTask, { manual: true })

  const { runAsync: run_alarmMedicine } = useFetch(alarmMedicine, {
    manual: true,
  });

  function escapeRegexSpecialChars(str) {
    // 正则表达式特殊字符
    var specialChars = /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g;

    // 使用replace方法和函数作为第二个参数来转义每个匹配项
    return str.replace(specialChars, '\\$&');
  }

  const list = () => {
    const scope = auth.project.permissions.scope;
    setLoading(true);
    run_getOrderSubject({
      customerId: customerId,
      projectId: projectId,
      envId: envId,
      roleId: auth.project.permissions.role_id,
    }).then((result) => {
      setSubjects(result.data ? result.data : []);
    });
    if (scope === "site") {
      //中心
      run_getProjectUserSites({
        customerId: customerId,
        projectId: projectId,
        envId: envId,
        roleId: auth.project.permissions.role_id,
      }).then((result) => {
        const data = result.data;
        if (data != null && data.site != null) {
          setSites(data.site);
        }
      });
    } else if (scope === "depot") {
      //仓库
      run_getProjectUserSites({
        customerId: customerId,
        projectId: projectId,
        envId: envId,
        roleId: auth.project.permissions.role_id,
      }).then((result) => {
        const data = result.data;
        if (data != null && data.storehouse != null) {
          setStorehouses(data.storehouse);
          setInstitutes(data.storehouse);
        }
      });
    }
    isBindRoleBool();
    run_sitesAndStorehouses({
      customerId: customerId,
      projectId: projectId,
      envId: envId,
    }).then((result) => {
      const data = result.data;
      if (data != null) {
        let institutes = [];
        if (scope === "depot") {
          if (data.site != null) {
            setSites(data.site);
          }
        } else {
          if (data.storehouse != null) {
            setStorehouses(data.storehouse);
            data.storehouse.forEach((value) => {
              if (value.deleted !== 1) {
                //过滤删除掉的仓库
                institutes.push(value);
              }
            });
          }
          if (data.site != null) {
            data.site.forEach((value) => {
              institutes.push(value);
            });
            if (scope === "study") {
              setSites(data.site);
            }
          }
          setInstitutes(institutes);
        }
      }
      // let key = escapeRegexSpecialChars(orderNumber);
      let key = orderNumber;
      if (orderNumber !== null && orderNumber !== undefined) {
        key = orderNumber.trim();
      }
      //订单列表查询
      run_getOrderList({
        customerId: customerId,
        projectId: projectId,
        envId: envId,
        roleId: auth.project.permissions.role_id,
        sendId: sendId,
        receiveId: receiveId,
        status: status,
        orderNumber: key.trim(),
        orderType: "site",
        start: (page.currentPage - 1) * page.pageSize,
        limit: page.pageSize,
      }).then((result) => {
        const data = result.data;
        if (data != null) {
          page.setTotal(data.total);
          page.setData(data.items);
          setPackageIsOpen(data.packageIsOpen);
          setMixPackage(data.mixPackage);
        }
        setLoading(false);
      });
    });
  };

  //获取研究产品名称
  const { runAsync: run_getDrugNames } = useFetch(getDrugNamesByRoleId, {
    manual: true,
  });
  const onGetDrugNames = (isBind) => {
    setDrugNameDefaultValue([]);
    run_getDrugNames({
      customerId: customerId,
      envId: envId,
      roleId: auth.project.permissions.role_id,
    }).then((result) => {
      const data = result.data;
      if (data.drugNames != null) {
        if (isBind) {
          var df = [];
          var options = [];
          data.drugNames.forEach((it) => {
            var value =
              it.saltDrugName !== ""
                ? it.saltDrugName + "/" + it.salt
                : it.drugName;
            options.push({
              label: it.drugName,
              value: value,
              // disabled:!it.isOther && !it.isOpen,
              disabled: it.isBlindedDrug,
            });
            if (!it.isOther) {
              df.push(value);
            }
          });
          setDrugData(options);
          setDrugNameDefaultValue(df);
        } else {
          const options = data.drugNames.map((it) => ({
            label: it.drugName,
            value:
              it.saltDrugName !== ""
                ? it.saltDrugName + "/" + it.salt
                : it.drugName,
          }));
          setDrugData(options);
        }
      }
      if (data.haveDrug != null) {
        setHaveDrug(data.haveDrug)
      }
      if (data.haveOtherDrug != null) {
        setHaveOtherDrug(data.haveOtherDrug)
      }
    });
  };

  const { runAsync: run_isBlindRole } = useFetch(roleIsBind, { manual: true });
  const isBindRoleBool = () => {
    run_isBlindRole({
      customerId: customerId,
      envId: envId,
      projectId: projectId,
      roleId: auth.project.permissions.role_id,
    }).then((result) => {
      const data = result.data;
      setIsBlind(data);
      //获取研究产品名称
      onGetDrugNames(data);
    });
  };

  const add = () => {
    //获取研究产品名称
    add_ref.current.show(
      institutes,
      sites,
      storehouses,
      isBlind,
      drugData,
      drugNameDefaultValue,
      packageIsOpen,
      haveDrug,
      haveOtherDrug,
      mixPackage
    );
  };

  //下载
  const download = () => {
    // let key = escapeRegexSpecialChars(orderNumber);
    let key = orderNumber;
    if (orderNumber !== null && orderNumber !== undefined) {
      key = orderNumber.trim();
    }
    run_download({
      customerId: customerId,
      projectId: projectId,
      envId: envId,
      roleId: auth.project.permissions.role_id,
      sendId: sendId,
      receiveId: receiveId,
      status: status,
      orderNumber: key.trim(),
      orderType: "site",
    }).catch((error) =>
      message.error(props.intl.formatMessage({ id: "common.download.fail" }))
    );
  };

  const updateOrder = (record, status) => {
    if (status === 2) {
      logistics_ref.current.show(record, 1);
      return;
    }

    CustomConfirmModal({
      title: formatMessage({ id: "shipment.transit.order" }),
      okText: formatMessage({ id: "common.ok" }),
      onOk: () => {
        run_updateOrderStatus({ id: record._id, status: status }).then(
          (resp) => {
            message.success(resp.msg);
            list();
          }
        );
      },
    });
    // }
  };

  const receiveOrder = (record) => {
    receive_ref.current.show(
      record._id,
      record.order_number,
      record.status,
      record.type,
      record
    );
  };

  const showReasonModal = (id, status, orderNumber) => {
    reason_ref.current.show(id, status, orderNumber);
  };

  // const showReasonViewModal = (id, status, reason) => {
  //   let title = "";
  //   if (status) {
  //     if (status === 5) {
  //       //取消
  //       title = formatMessage({ id: "shipment.cancel.order" });
  //     } else if (status === 4) {
  //       //丢失订单
  //       title = formatMessage({ id: "shipment.lose.order" });
  //     } else if (status === 9) {
  //       //关闭订单
  //       title = formatMessage({ id: "shipment.close.order" });
  //     } else if (status === 8) {
  //       //终止订单
  //       title = formatMessage({ id: "shipment.end.order" });
  //     }
  //   }
  //   return title;
  //   // CustomInfoModal({
  //   //     title: title,
  //   //     content: reason,
  //   //     centered: true,
  //   // })
  // };

  function renderSend(record) {
    if (!record) {
      return null;
    }
    if (record.type === 1 || record.type === 5) {
      const storehouse = storehouses.find((it) => record.send_id === it.value);
      return (
        <TooltipParagraph
          outStyle={{ width: 200 }}
          tooltip={storehouse?.label || "-"}
        >
          {storehouse?.label || "-"}
        </TooltipParagraph>
      );
    } else {
      const site = institutes.find((it) => record.send_id === it.value);
      return (
        <TooltipParagraph
          outStyle={{ width: 200 }}
          tooltip={site?.label || "-"}
        >
          {site?.label || "-"}
        </TooltipParagraph>
      );
    }
  }

  function renderOrder(record) {
    return (
      <span>
        {record.order_number}{" "}
        {(record.type === 5 || record.type === 6) && (
          <svg className="iconfont" width={12} height={12}>
            <use xlinkHref="#icon-DTP"></use>
          </svg>
        )}
      </span>
    );
  }

  function renderReceive(value, record) {
    if (record.subject) {
      return record.subject.info[0]?.value;
    }
    if (!value) {
      return null;
    }
    const match = sites.find((it) => it.value === value);
    return (
      <TooltipParagraph outStyle={{ width: 200 }} tooltip={match?.label || "-"}>
        {match ? match.label : "-"}
      </TooltipParagraph>
    );
  }

  function renderStatus(value) {
    const colors = [
      "",
      "geekblue",
      "orange",
      "cyan",
      "red",
      "purple",
      "yellow",
      "volcano",
      "gold",
      "magenta",
    ];
    return (
      <TooltipParagraph
        outStyle={{ width: 140 }}
        tooltip={statusArr.find((it) => it.value === value).label}
      >
        <Badge
          color={colors[value]}
          text={statusArr.find((it) => it.value === value).label}
        />
      </TooltipParagraph>
    );
  }

  const orderMedicines = (record) => {
    // medicines_ref.current.show(record._id, record.order_number);
    if ((record.type === 5 || record.type === 6) && (record.status === 2 || record.status === 3)) {
      // DTP订单且是已运送、已接收状态
      medicines_ref.current.show(record._id, record.order_number, 1);
    } else {
      medicines_ref.current.show(record._id, record.order_number, 0);
    }
  };

  //轨迹
  const showHistory = (oid) => {
    history_ref.current.show("history.order", oid, timeZone);
  };

  //审批记录
  const showApprovalRecord = (record) => {
    approval_ref.current.show(record, sites, storehouses, packageIsOpen);
  };

  //确认订单
  const confirmOrder = (record) => {
    confirm_ref.current.show(record);
  };

  // 库存核查
  const onAlarmMedicine = () => {
    run_alarmMedicine({ envId: envId }).then((resp) => {
      message.success(resp.msg);
      list();
    });
  };
  // 操作列渲染
  const operationButtons = (record) => {
    const btns = [];
    const moreOperationsMeunItems = [];
    if (
      permissions(
        auth.project.permissions,
        "operation.supply.shipment.cancel"
      ) &&
      record.status === 6 && record.dtp_cancel && !(record.type === 5 || record.type === 6)
    ) {
      btns.push(
        <AuthButton
          style={{ padding: 0 }}
          size="small"
          type="link"
          onClick={() => showReasonModal(record._id, 5, record.order_number)}
        >
          <FormattedMessage id="common.cancel" />
        </AuthButton>
      );
    }
    if (
      permissions(
        auth.project.permissions,
        "operation.supply.shipment.cancel-dtp"
      ) &&
      record.status === 6 && record.dtp_cancel && (record.type === 5 || record.type === 6)
    ) {
      btns.push(
        <AuthButton
          style={{ padding: 0 }}
          size="small"
          type="link"
          onClick={() => showReasonModal(record._id, 5, record.order_number)}
        >
          <FormattedMessage id="common.cancel" />
        </AuthButton>
      );
    }
    if (
      permissions(
        auth.project.permissions,
        "operation.supply.shipment.confirm"
      ) &&
      record.status === 6 &&
      projectStatus !== 2 && !(record.type === 5 || record.type === 6)
    ) {
      btns.push(
        <AuthButton
          size="small"
          type="link"
          style={{ padding: 0 }}
          onClick={() => confirmOrder(record)}
        >
          <FormattedMessage id="shipment.oper.confirm.order" />
        </AuthButton>
      );
    }

    if (
      permissions(
        auth.project.permissions,
        "operation.supply.shipment.confirm-dtp"
      ) &&
      record.status === 6 &&
      projectStatus !== 2 && (record.type === 5 || record.type === 6)
    ) {
      btns.push(
        <AuthButton
          size="small"
          type="link"
          style={{ padding: 0 }}
          onClick={() => confirmOrder(record)}
        >
          <FormattedMessage id="shipment.oper.confirm.order" />
        </AuthButton>
      );
    }

    // 这里要加关闭权限
    if (
      permissions(
        auth.project.permissions,
        "operation.supply.shipment.close"
      ) &&
      record.status === 1 &&
      projectStatus !== 2 && record.dtp_cancel && !(record.type === 5 || record.type === 6)
    ) {
      btns.push(
        <AuthButton
          style={{ padding: 0 }}
          size="small"
          type="link"
          onClick={() => showReasonModal(record._id, 9, record.order_number)}
        >
          <FormattedMessage id="common.close" />
        </AuthButton>
      );
    }
    if (
      permissions(
        auth.project.permissions,
        "operation.supply.shipment.close-dtp"
      ) &&
      record.status === 1 &&
      projectStatus !== 2 && record.dtp_cancel && (record.type === 5 || record.type === 6) && !record.is_register
    ) {
      btns.push(
        <AuthButton
          style={{ padding: 0 }}
          size="small"
          type="link"
          onClick={() => showReasonModal(record._id, 9, record.order_number)}
        >
          <FormattedMessage id="common.close" />
        </AuthButton>
      );
    }
    if (
      record.status === 1 &&
      permissions(auth.project.permissions, "operation.supply.shipment.send") &&
      projectStatus !== 2
    ) {
      btns.length < 3
        ? btns.push(
          <AuthButton
            style={{ padding: 0 }}
            size="small"
            type="link"
            onClick={() => updateOrder(record, 2)}
          >
            <FormattedMessage id="shipment.transit" />
          </AuthButton>
        )
        : moreOperationsMeunItems.push(
          <AuthMenuItem key={"transit"} onClick={() => updateOrder(record, 2)}>
            <FormattedMessage id="shipment.transit" />
          </AuthMenuItem>
        );
    }

    if (
      (record.status === 1 || record.status === 2) &&
      permissions(
        auth.project.permissions,
        "operation.supply.shipment.receive"
      ) &&
      projectStatus !== 2
    ) {
      btns.length < 3
        ? btns.push(
          <AuthButton
            size="small"
            type="link"
            style={{ padding: 0 }}
            onClick={() => receiveOrder(record)}
          >
            <FormattedMessage id="shipment.received" />
          </AuthButton>
        )
        : moreOperationsMeunItems.push(
          <AuthMenuItem key={"received"} onClick={() => receiveOrder(record)}>
            <FormattedMessage id="shipment.received" />
          </AuthMenuItem>
        );
    }

    // 这里要加终止权限
    if (
      permissions(
        auth.project.permissions,
        "operation.supply.shipment.terminated"
      ) &&
      record.status === 2 &&
      projectStatus !== 2 && record.dtp_cancel && !(record.type === 5 || record.type === 6)
    ) {
      btns.length < 3
        ? btns.push(
          <AuthButton
            style={{ padding: 0 }}
            size="small"
            type="link"
            onClick={() => showReasonModal(record._id, 8, record.order_number)}
          >
            <FormattedMessage id="shipment.end" />
          </AuthButton>
        )
        : moreOperationsMeunItems.push(
          <AuthMenuItem
            key={"end"}
            onClick={() => showReasonModal(record._id, 8, record.order_number)}
          >
            <FormattedMessage id="shipment.end" />
          </AuthMenuItem>
        );
    }

    if (
      permissions(
        auth.project.permissions,
        "operation.supply.shipment.terminated-dtp"
      ) &&
      record.status === 2 &&
      projectStatus !== 2 && record.dtp_cancel && (record.type === 5 || record.type === 6) && !record.is_register
    ) {
      btns.length < 3
        ? btns.push(
          <AuthButton
            style={{ padding: 0 }}
            size="small"
            type="link"
            onClick={() => showReasonModal(record._id, 8, record.order_number)}
          >
            <FormattedMessage id="shipment.end" />
          </AuthButton>
        )
        : moreOperationsMeunItems.push(
          <AuthMenuItem
            key={"end"}
            onClick={() => showReasonModal(record._id, 8, record.order_number)}
          >
            <FormattedMessage id="shipment.end-dtp" />
          </AuthMenuItem>
        );
    }

    //丢失
    if (
      (record.status === 1 || record.status === 2) &&
      permissions(auth.project.permissions, "operation.supply.shipment.lose") &&
      projectStatus !== 2
      &&
      record.dtp_cancel && !record.is_register
    ) {
      btns.length < 3
        ? btns.push(
          <AuthButton
            size="small"
            type="link"
            style={{ padding: 0 }}
            onClick={() => showReasonModal(record._id, 4, record.order_number)}
          >
            <FormattedMessage id="shipment.lose" />
          </AuthButton>
        )
        : moreOperationsMeunItems.push(
          <AuthMenuItem
            key={"lose"}
            onClick={() => showReasonModal(record._id, 4, record.order_number)}
          >
            <FormattedMessage id="shipment.lose" />
          </AuthMenuItem>
        );
    }

    if (
      (record.status === 1 || record.status === 2) &&
      permissions(auth.project.permissions, "operation.supply.shipment.lose-dtp") &&
      projectStatus !== 2
      &&
      record.dtp_cancel && (record.type === 5 || record.type === 6)
    ) {
      btns.length < 3
        ? btns.push(
          <AuthButton
            size="small"
            type="link"
            style={{ padding: 0 }}
            onClick={() => showReasonModal(record._id, 4, record.order_number)}
          >
            <FormattedMessage id="shipment.lose-dtp" />
          </AuthButton>
        )
        : moreOperationsMeunItems.push(
          <AuthMenuItem
            key={"lose"}
            onClick={() => showReasonModal(record._id, 4, record.order_number)}
          >
            <FormattedMessage id="shipment.lose-dtp" />
          </AuthMenuItem>
        );
    }


    if (
      permissions(auth.project.permissions, "operation.supply.shipment.history")
    ) {
      btns.length < 3
        ? btns.push(
          <AuthButton
            size="small"
            type="link"
            style={{ padding: 0 }}
            onClick={() => showHistory(record._id)}
          >
            {" "}
            <FormattedMessage id="common.history" />
          </AuthButton>
        )
        : moreOperationsMeunItems.push(
          <AuthMenuItem key={"history"} onClick={() => showHistory(record._id)}>
            <FormattedMessage id="common.history" />
          </AuthMenuItem>
        );
    }
    //原因
    // if ((record.status === 4 || record.status === 5 || record.status === 8 || record.status === 9) && permissions(auth.project.permissions, "operation.supply.shipment.reason")) {
    //     btns.length < 3 ? btns.push(
    //         <Popover content={record.reason} title={showReasonViewModal(record._id, record.status, record.reason)}>
    //             <Button size="small" type="link" style={{ padding: 0 }}><FormattedMessage id="drug.freeze.reason" /></Button>
    //         </Popover>
    //         )
    //         :
    //         moreOperationsMeunItems.push(<Popover content={record.reason} title={showReasonViewModal(record._id, record.status, record.reason)}><Menu.Item key={"freeze"} ><FormattedMessage id="drug.freeze.reason" /></Menu.Item></Popover>)
    // }
    //物流
    // if ((record.type === 5 || record.type === 6) && permissions(auth.project.permissions, "operation.supply.shipment.logistics.view")) {
    // //     btns.length < 3 ? btns.push(<Button size="small" type="link" key={"logistics"} style={{ padding: 0 }}
    // //                     onClick={() => logistics_ref.current.show(record, 2)}><FormattedMessage id="logistics.info"/></Button>)
    // //         :
    // //         moreOperationsMeunItems.push(<Menu.Item key={"logistics"} onClick={() => logistics_ref.current.show(record, 2)}><FormattedMessage id="logistics.info"/></Menu.Item>)
    // // }
    // // if (permissions(auth.project.permissions, "operation.supply.shipment.logistics.view")) {
    //     btns.length < 3 ? btns.push(<Button size="small" type="link" key={"logistics"} style={{ padding: 0 }}
    //                     onClick={() => logistics_ref.current.show(record, 2)}><FormattedMessage id="logistics.info"/></Button>)
    //         :
    //         moreOperationsMeunItems.push(<Menu.Item key={"logistics"} onClick={() => logistics_ref.current.show(record, 2)}><FormattedMessage id="logistics.info"/></Menu.Item>)
    // }

    if (
      record.task_id !== undefined &&
      record.task_id !== "" &&
      record.task_id !== nilObjectId &&
      permissions(
        auth.project.permissions,
        "operation.supply.shipment.approval.view"
      )
    ) {
      btns.length < 3
        ? btns.push(
          <AuthButton
            size="small"
            type="link"
            style={{ padding: 0 }}
            onClick={() => showApprovalRecord(record)}
          >
            {" "}
            <FormattedMessage id="shipment.approvalTask" />
          </AuthButton>
        )
        : moreOperationsMeunItems.push(
          <AuthMenuItem
            key={"approvalTask"}
            onClick={() => showApprovalRecord(record)}
          >
            {" "}
            <FormattedMessage id="shipment.approvalTask" />
          </AuthMenuItem>
        );
    }

    //详情
    if (
      permissions(
        auth.project.permissions,
        "operation.supply.shipment.logistics.view"
      ) ||
      permissions(
        auth.project.permissions,
        "operation.supply.shipment.contacts"
      ) ||
      ((record.status === 4 ||
        record.status === 5 ||
        record.status === 8 ||
        record.status === 9) &&
        permissions(
          auth.project.permissions,
          "operation.supply.shipment.reason"
        ))
    ) {
      btns.length < 3
        ? btns.push(
          <AuthButton
            size="small"
            type="link"
            style={{ padding: 0 }}
            onClick={() => {
              const type =
                status === 2 && (record.type === 5 || record.type === 6)
                  ? 1
                  : 2;
              detailRef.current.show(record, type);
            }}
          >
            <FormattedMessage id="common.detail" />
          </AuthButton>
        )
        : moreOperationsMeunItems.push(
          <AuthMenuItem
            key={"contactsDetail"}
            onClick={() => {
              const type =
                status === 2 && (record.type === 5 || record.type === 6)
                  ? 1
                  : 2;
              detailRef.current.show(record, type);
            }}
          >
            <FormattedMessage id="common.detail" />
          </AuthMenuItem>
        );
    }

    // return InsertDivider(btns)
    return (
      <div style={{ display: "flex", alignItems: "center" }}>
        {InsertDivider(btns)}
        {moreOperationsMeunItems.length !== 0 && (
          <Dropdown
            overlay={<Menu>{moreOperationsMeunItems.map((item) => item)}</Menu>}
          >
            <span style={{ marginLeft: 12 }} className="dropdown-button">
              <i className="iconfont icon-gengduo-changgui" />
            </span>
          </Dropdown>
        )}
      </div>
    );
  };
  React.useEffect(() => {
    if (project.orderStatus !== 0) {
      setStatus(project.orderStatus);
      project.setOrderStatus(0);
    } else {
      setDoSearch(doSearch + 1);
    }
  }, []);

  useUpdateEffect(list, [
    doSearch,
    sendId,
    receiveId,
    status,
    page.currentPage,
    page.pageSize,
    g.lang,
  ]);

  return (
    <>
      <Spin spinning={loading}>
        <Row>
          <Col>
            <div style={{ display: "flex", alignItems: "center" }}>
              <svg className="iconfont" width={16} height={16}>
                <use xlinkHref="#icon-quanbudingdan" />
              </svg>
              <span
                style={{
                  color: "#1D2129",
                  fontWeight: 600,
                  paddingLeft: "8px",
                }}
              >
                {formatMessage({ id: "shipment.order.all", allowComponent: true })}
              </span>
            </div>
          </Col>
        </Row>
        <Row justify="space-between" className="mar-top-15 order-filter">
          <Col span={20}>
            <Row gutter={[12, 0]} align="middle">
              <Col span={5} className="mar-rgt-12">
                <Tooltip
                  placement="top"
                  title={
                    formatMessage({ id: "common.required.prefix" }) +
                    formatMessage({ id: "shipment.orderNumber" })
                  }
                >
                  <Input.Search
                    placeholder={
                      formatMessage({ id: "common.required.prefix" }) +
                      formatMessage({ id: "shipment.orderNumber" })
                    }
                    ref={searchInputRef}
                    value={orderNumber}
                    onChange={(e) => {
                      setOrderNumber(e.target.value);
                    }}
                    allowClear
                    onSearch={() => {
                      searchInputRef?.current?.blur();
                      page.setCurrentPage(1);
                      setDoSearch(doSearch + 1);
                    }}
                  />
                </Tooltip>
              </Col>
              {formatMessage({ id: "shipment.send", allowComponent: true })}:
              <Col span={4} className="mar-rgt-12">
                <Tooltip
                  placement="top"
                  title={formatMessage({ id: "placeholder.select.search" })}
                >
                  <Select
                    style={{ width: "100%" }}
                    placeholder={formatMessage({
                      id: "placeholder.select.search",
                    })}
                    value={sendId}
                    onChange={(e) => {
                      page.setCurrentPage(1);
                      setSendId(e);
                    }}
                    dropdownStyle={{ maxWidth: 400 }}
                    dropdownMatchSelectWidth={false}
                    optionFilterProp="label"
                    showSearch
                    allowClear
                    options={institutes}
                    filterOption={(input, option) => {
                      const childrenText = option.props.label;
                      if (typeof childrenText === 'string') {
                        return childrenText.trim().toLowerCase().indexOf(input.trim().toLowerCase()) >= 0;
                      }
                      return false;
                    }}
                  />
                </Tooltip>
              </Col>
              {formatMessage({ id: "shipment.receive", allowComponent: true })}:
              <Col span={4} className="mar-rgt-12">

                <Select
                  style={{ width: "100%" }}
                  placeholder={formatMessage({
                    id: "placeholder.select.search",
                  })}
                  value={receiveId}
                  onChange={(e) => {
                    page.setCurrentPage(1);
                    setReceiveId(e);
                  }}
                  showSearch
                  dropdownStyle={{ maxWidth: 400 }}
                  dropdownMatchSelectWidth={false}
                  optionFilterProp="label"
                  allowClear
                  filterOption={(input, option) => {
                    const childrenText = option.props.label;
                    if (typeof childrenText === 'string') {
                      return childrenText.trim().toLowerCase().indexOf(input.trim().toLowerCase()) >= 0;
                    }
                    return false;
                  }}
                >
                  {
                    sites?.map(
                      it => (
                        <Select.Option key={it.value} value={it.value}>
                          {/* <Tooltip title={it.label}>{it.label}</Tooltip> */}
                          {it.label}
                        </Select.Option>
                      )
                    )
                  }
                  {
                    subjects?.map(
                      it => (
                        <Select.Option key={it.value} value={it.value}>
                          {/* <Tooltip title={it.label}>{it.label}</Tooltip> */}
                          {it.label}
                        </Select.Option>
                      )
                    )
                  }

                </Select>
              </Col>
              {formatMessage({ id: "shipment.status", allowComponent: true })}:
              <Col span={5}>
                <Select
                  style={{ width: "100%" }}
                  placeholder={formatMessage({
                    id: "placeholder.select.common",
                  })}
                  value={status}
                  onChange={(e) => {
                    page.setCurrentPage(1);
                    setStatus(e);
                  }}
                  allowClear
                  options={[
                    ...statusArr,
                    {
                      value: 10,
                      label: <FormattedMessage id="shipment.status.timeout" />,
                    },
                  ]}
                />
              </Col>
            </Row>
          </Col>
          <Col>
            {permissions(
              auth.project.permissions,
              "operation.supply.shipment.alarm"
            ) &&
              projectStatus !== 2 &&
              orderCheck === 1 ? (
              <AuthButton className="mar-rgt-12" onClick={onAlarmMedicine}>
                <FormattedMessage id="shipment.store.alarm" />
              </AuthButton>
            ) : null}
            {permissions(
              auth.project.permissions,
              "operation.supply.shipment.download"
            ) && auth.project.info.research_attribute === 1 ? (
              <AuthButton
                loading={downloading}
                className="mar-rgt-12"
                onClick={() => download()}
              >
                <FormattedMessage id="common.download" />
              </AuthButton>
            ) : null}
            {permissions(
              auth.project.permissions,
              "operation.supply.shipment.create"
            ) && projectStatus !== 2 ? (
              <AuthButton type="primary" onClick={() => add()}>
                <FormattedMessage id="common.add" />
              </AuthButton>
            ) : null}
          </Col>
        </Row>
        <Table
          className="mar-top-10"
          dataSource={page.data}
          pagination={false}
          //scroll={{ x: !!page.data?.length ? "max-content" : false }}
          scroll={{ x: "max-content", y: "calc(100vh - 270px)" }}
          sticky
          //style={{ maxHeight: "calc(100vh - 225px)", overflowY: "auto", }}
          rowKey={(record) => record._id}
        >
          <Table.Column
            title={intl.formatMessage({ id: "common.serial", allowComponent: true })}
            className="table-column-padding-left-16-1"
            dataIndex="#"
            key="#"
            width={48}
            render={(text, record, index) =>
              (page.currentPage - 1) * page.pageSize + index + 1
            }
          />
          <Table.Column
            title={<FormattedMessage id="shipment.orderNumber" />}
            className="table-column-padding-left-16-1"
            width={136}
            dataIndex="order_number"
            key="order_number"
            ellipsis
            render={(value, record, index) => renderOrder(record)}
          />
          <Table.Column
            title={<FormattedMessage id="shipment.send" />}
            className="table-column-padding-left-32"
            dataIndex="send_id"
            key="send_id"
            //ellipsis
            // width={200}
            render={(value, record, index) => renderSend(record)}
          />
          <Table.Column
            title={<FormattedMessage id="shipment.receive" />}
            className="table-column-padding-left-32"
            dataIndex="receive_id"
            key="receive_id"
            //ellipsis
            // width={200}
            render={(value, record, index) => renderReceive(value, record)}
          />
          <Table.Column
            title={<FormattedMessage id="shipment.status" />}
            className="table-column-padding-left-32"
            dataIndex="status"
            key="status"
            ellipsis
            width={162}
            render={(value, record, index) => renderStatus(value)}
          />
          <Table.Column
            title={
              <FormattedMessage id="projects.sitePharmacy.order.medicineQuantity" />
            }
            className="table-column-padding-left-32"
            dataIndex="medicines"
            key="medicines"
            width={122}
            render={(value, record, index) => (
              <AuthSpan
                style={{ color: "#165DFF" }}
                className="mouse"
                onClick={() => orderMedicines(record)}
              >
                {record.count}
              </AuthSpan>
            )}
          />
          <Table.Column
            title={<FormattedMessage id="common.operation" />}
            className="table-column-padding-left-16-2"
            fixed="right"
            width={216}
            render={(value, record, index) => {
              return operationButtons(record);
            }}
          />
        </Table>
        <PaginationView />
      </Spin>
      <Logistics bind={logistics_ref} refresh={list} />
      <Add bind={add_ref} refresh={list} />
      <PageContextProvider>
        <Receive bind={receive_ref} refresh={list} />
      </PageContextProvider>
      <Medicines bind={medicines_ref} refresh={list} />
      <ReasonOrder bind={reason_ref} refresh={list} />
      <PageContextProvider>
        <Confirm bind={confirm_ref} refresh={list} />
      </PageContextProvider>
      <HistoryList
        bind={history_ref}
        permission={permissions(
          auth.project.permissions,
          "operation.supply.shipment.print"
        )}
      />
      <Approval bind={approval_ref} />
      <ShipDetail ref={detailRef} refresh={list} />
    </>
  );
};
