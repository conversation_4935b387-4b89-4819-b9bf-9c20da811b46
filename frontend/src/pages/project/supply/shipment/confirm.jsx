import React from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { Form, Spin, Button, Col, Modal, InputNumber, message, Row, Table, Space, Input, DatePicker } from "antd";
import { Title } from "components/title";
import { useAuth } from "../../../../context/auth";
import { useSafeState, useUpdateEffect } from "ahooks";
import { useFetch } from "../../../../hooks/request";
import { confirmOrder, getOrderMedicines } from "../../../../api/order";
import { permissions } from "../../../../tools/permission";
import { combineRow } from "../../../../utils/merge_cell";
import { SearchOutlined } from "@ant-design/icons";
import { SelectableOrderTable, UnSelectableOrderTable } from "./order-table";
import { useRowSelection } from "../../../../hooks/row-selection";
import { useCacheTable } from "hooks/cache-rowSpan-table";
import { fillTableCellEmptyPlaceholder } from "../../../../components/table";
import { CustomDateTimePicker } from "../../../../components/CustomDateTimePicker";
import moment from "moment";




export const Confirm = (props) => {
    const DatePickers = DatePicker;
    const intl = useIntl();
    const { formatMessage } = intl;
    const auth = useAuth()
    const [searchValue, setSearchValue] = useSafeState(null);
    const [visible, setVisible] = useSafeState(false);
    const [submitting, setSubmitting] = useSafeState(false);
    const [otherTableData, setOtherTableData] = useSafeState([]);
    const [tableData, setTableData] = useSafeState([]);
    const data = useCacheTable({ dataSource: tableData });
    //const [receiveMedicines, setReceiveMedicines] = useSafeState([]);
    //const [receiveMedicineIds, setReceiveMedicineIds] = useSafeState([]);
    const [expectedArrivalTimeValue, setExpectedArrivalTimeValue] = useSafeState(null);
    const [orderId, setOrderId] = useSafeState(null);
    const [record, setRecord] = useSafeState({});
    const [orderNumber, setOrderNumber] = useSafeState(null);
    const [allSelected, setAllSelected] = useSafeState(true);
    const [packageIsOpen, setPackageIsOpen] = useSafeState(false);
    const envId = auth.env ? auth.env.id : null;
    const [form] = Form.useForm();

    const { runAsync: run_getOrderMedicines, loading } = useFetch(getOrderMedicines, { manual: true })
    const show = (record) => {
        setRecord(record)
        setVisible(true);
        setOrderId(record._id ? record._id : record.id);
        setOrderNumber(record.order_number);
        setSearchValue("");
        setAllSelected(true);
        setOtherTableData([]);
        getOrderMedicineList();
        if (record.expected_arrival_time !== undefined && record.expected_arrival_time !== "") {
            // 将字符串日期解析为Date对象
            // var parsedDate = moment(record.expected_arrival_time, 'YYYY-MM-DD HH:mm:ss');
            form.setFieldValue("expectedArrivalTime", record.expected_arrival_time)
            setExpectedArrivalTimeValue(record.expected_arrival_time)
        }
    };


    const getOrderMedicineList = () => {
        if (orderId != null) {
            run_getOrderMedicines({
                id: orderId,
                envId: envId,
                roleId: auth.project.permissions.role_id,
                searchValue: searchValue,
            }).then(
                (result) => {
                    const data = result.data
                    if (data.order != null) {
                        let order = data.order[0];
                        if (data.packageIsOpen) {
                            var tableData = combineRow(order.medicines, "package_number", "package_number", false)
                            tableData = combineRow(order.medicines, "name", "name", false)
                            setTableData(fillTableCellEmptyPlaceholder(tableData ? tableData : []));
                        } else {
                            setTableData(fillTableCellEmptyPlaceholder(order.medicines ? order.medicines : []));
                        }
                        setPackageIsOpen(data.packageIsOpen);
                        setAllSelected(false);
                        //setReceiveMedicines([]);
                        // setReceiveMedicineIds([]);
                        //resetSelectedIds()
                        if (order.other_medicines != null && order.other_medicines.length > 0) {
                            order.other_medicines.forEach((otherMedicine) => {
                                otherMedicine.receive_count = otherMedicine.use_count;
                            });
                            //setOtherTableData(order.other_medicines)
                            setOtherTableData(fillTableCellEmptyPlaceholder(order.other_medicines ? order.other_medicines : []));
                        }
                    } else {
                        setTableData([]);
                    }
                }
            )
        }
    }


    const hide = () => {
        setVisible(false);
        setSubmitting(false);
        setOrderId(null);
        setExpectedArrivalTimeValue(null);
        setTableData([]);
        //setReceiveMedicines([]);
        //setReceiveMedicineIds([]);
        resetSelectedIds();
        setOtherTableData([]);
        setSearchValue(null);
        form.resetFields();
        props.refresh();
    };

    const { runAsync: run_confirmOrder } = useFetch(confirmOrder, { manual: true })
    const save = () => {
        //其它研究产品
        let count = 0;
        if (otherTableData != null) {
            otherTableData.forEach((value) => {
                if (value.receive_count !== undefined && value.receive_count != null) {
                    count = count + value.receive_count
                }
            });
        }
        count = count + receiveMedicineIds.length
        if (count <= 0) {
            message.error(formatMessage({ id: 'shipment.confirm.select' })).then();
            setSubmitting(false);
            return false;
        }

        setSubmitting(true);
        // if (receiveMedicineIds.length <= 0) {
        //     message.error(formatMessage({id: 'shipment.confirm.select'})).then();
        //     setSubmitting(false);
        //     return false;
        // }
        form.validateFields()
            .then((values) => {
                // let expectedArrivalTime = values.expectedArrivalTime
                //     ? moment(values.expectedArrivalTime).format("YYYY-MM-DD HH:mm:ss")
                //     : "";
                let expectedArrivalTime = (values.expectedArrivalTime !== null && values.expectedArrivalTime !== undefined && values.expectedArrivalTime !== "-") ? values.expectedArrivalTime : "";
                run_confirmOrder(
                    {
                        id: orderId,
                        roleId: auth.project.permissions.role_id,
                        medicines: receiveMedicineIds,
                        otherMedicines: otherTableData,
                        expectedArrivalTime: expectedArrivalTime
                    }).then(
                        (data) => {
                            message.success(data.msg)
                            props.refresh();
                            hide();
                        }, (data) => {
                            setSubmitting(false);
                        });
            })
            .catch((errors) => {
                setSubmitting(false);
            });


    };

    const { selectedIds: receiveMedicineIds, selectedRows: receiveMedicines, resetSelectedIds, rowSelection } =
        useRowSelection({
            dataSource: data,
            allData: tableData,
            key: "_id",
            packageIsOpen: packageIsOpen,
            allSelected: allSelected,
            changeKey: null,
            operation: null,
            mixPackage: null,
            groupInfo: null,
            onChange: (v) => {

            },
            renderCell: (checked, record, index, originNode, package_numberRowSpan) => {
                return {
                    children: originNode,
                    props: {
                        rowSpan: data[index] !== undefined ? data[index]["package_numberRowSpan"] : 1
                    }
                }
            }
        })


    // const rowSelection = {
    //     type: "checkbox",
    //     onChange: (selectedRowKeys, selectedRows) => {
    //         var receiveMedicines = [];
    //         var receiveMedicineIds = [];
    //         selectedRows.forEach((record) => {
    //             if (!packageIsOpen || (packageIsOpen && record.package_numberRowSpan !== 0) ) {
    //                 receiveMedicines.push(record)
    //                 receiveMedicineIds.push(record._id)
    //             }
    //         })
    //         setReceiveMedicines(receiveMedicines);
    //         setReceiveMedicineIds(receiveMedicineIds);
    //     },
    //     onSelectAll: (selected, records, changeRows) => {
    // 		if (selected) {
    //             var receiveMedicines = [];
    //             var receiveMedicineIds = [];
    // 			changeRows.forEach((record) => {
    // 				if (!packageIsOpen || (packageIsOpen && record.package_numberRowSpan !== 0) ) {
    //                     receiveMedicines.push(record)
    //                     receiveMedicineIds.push(record._id)
    //                 }
    // 			})
    //             setReceiveMedicines(receiveMedicines);
    //             setReceiveMedicineIds(receiveMedicineIds);
    // 		} else {
    //             //处理勾选了包装的数据
    //             setReceiveMedicines([]);
    //             setReceiveMedicineIds([]);
    // 		}
    // 	},
    //     selectedRowKeys: receiveMedicineIds,
    //     selectedRows: receiveMedicines,
    //     renderCell: (checked, record, index, originNode) => {
    //         return {
    //             children: originNode,
    //             props: {
    //                 rowSpan: tableData[index] !== undefined ? tableData[index]["package_numberRowSpan"] : 1 
    //             }
    //         }
    //     }
    // };

    const otherHandleChange = (value, record) => {
        const _data = [...otherTableData];
        _data.forEach(it => {
            if (it.id === record.id) {
                it.receive_count = value["receive_count"]
            }
        });
        setOtherTableData(_data)
    }

    const refresh = () => {
        //setReceiveMedicines([]);
        //setReceiveMedicineIds([]);
        resetSelectedIds();
    }

    const disabledDate = (current) => {
        return current && current < moment().startOf('day');
    };


    function renderPackageMethod(packageMethod) {
        return packageMethod === true ? (
            <FormattedMessage id="shipment.order.packageMethod.package" />
        ) : (
            <FormattedMessage id="shipment.order.packageMethod.single" />
        );
    }

    React.useImperativeHandle(props.bind, () => ({ show }));

    useUpdateEffect(getOrderMedicineList, [orderId, searchValue]);

    //const [ vt, set_components ] = useVT(() => ({ scroll: { y: document.documentElement.clientHeight - 300 } }), [])
    return (
        <React.Fragment>
            <Modal
                className="custom-blarge-modal"
                title={formatMessage({ id: "shipment.confirm.order" }) + " - " + orderNumber}
                centered={true}
                destroyOnClose={true}
                visible={visible}
                onCancel={hide}
                maskClosable={false}
                footer={
                    <Row>
                        <Col span={24} style={{ textAlign: 'right' }}>
                            <Space direction='horizontal'>
                                <Button onClick={() => { hide(); }} >
                                    {formatMessage({ id: "common.cancel" })}
                                </Button>
                                {
                                    (permissions(auth.project.permissions, "operation.supply.shipment.confirm") || permissions(auth.project.permissions, "operation.supply.drug.order.confirm") || permissions(auth.project.permissions, "operation.supply.shipment.confirm-dtp")) &&

                                    <Button onClick={save} type="primary" loading={submitting}>
                                        <FormattedMessage id="common.ok" />
                                    </Button>
                                }
                            </Space>
                        </Col>
                    </Row>
                }
            >
                <Spin spinning={loading}>
                    <Form form={form}  >
                        <Title
                            name={formatMessage({
                                id: "shipment.basic.information",
                            })}
                        ></Title>
                        <Row gutter={32}>
                            <Col span={12}>
                                <Form.Item
                                    label={formatMessage({
                                        id: "shipment.expectedArrivalTime",
                                    })}
                                    name="expectedArrivalTime"
                                    className="mar-ver-5"
                                >
                                    {/* <DatePickers
                                        placeholder={formatMessage({
                                            id: "placeholder.select.common",
                                        })}
                                        className={"full-width"}
                                        format={"YYYY-MM-DD HH:mm:ss"}
                                        showTime={{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }}
                                    ></DatePickers> */}
                                    <CustomDateTimePicker
                                        disabledDate={disabledDate}
                                        disabledTime={"disBefore"}
                                        value={expectedArrivalTimeValue}
                                        onChange={setExpectedArrivalTimeValue}
                                        ph={'placeholder.select.common'}>
                                    </CustomDateTimePicker>
                                </Form.Item>
                            </Col>
                        </Row>


                        {(tableData != null && tableData.length > 0 || searchValue) && (
                            <>
                                <div style={{ marginBottom: 12, marginTop: 12 }}>
                                    <Title name={formatMessage({ id: 'drug.medicine' })}></Title>
                                </div>
                                {
                                    !(record.type === 5 || record.type === 6) &&
                                    <Row gutter={8} justify="space-between" style={{ marginBottom: 12 }}>
                                        <Col xs={12} sm={12} md={12} lg={4} style={{ paddingRight: 0 }}>
                                            <Input
                                                placeholder={formatMessage({ id: "common.required.prefix" })}
                                                onChange={e => {
                                                    setSearchValue(e.target.value)
                                                }}
                                                value={searchValue}
                                                style={{ width: 220, marginRight: 12 }}
                                                suffix={<SearchOutlined style={{ color: "#BFBFBF" }} />}
                                                allowClear
                                            />
                                        </Col>
                                    </Row>
                                }

                                {
                                    record.type === 5 || record.type === 6 ?
                                        <UnSelectableOrderTable
                                            dataSource={tableData}
                                            packageIsOpen={packageIsOpen}
                                            isHasOtherDataSource={
                                                otherTableData != null && otherTableData.length > 0
                                            }
                                            isHasTip={false}
                                        />
                                        :
                                        <SelectableOrderTable
                                            dataSource={tableData}
                                            selectedIds={receiveMedicineIds}
                                            rowSelection={rowSelection}
                                            clearDisplay={true}
                                            refresh={refresh}
                                            packageIsOpen={packageIsOpen}
                                            isHasOtherDataSource={
                                                otherTableData != null && otherTableData.length > 0
                                            }
                                            isHasTip={false}
                                            showGroup={true && (searchValue == null || searchValue.length <= 0)}
                                            searchValue={searchValue}
                                        />
                                }
                            </>
                        )}

                        {otherTableData !== null && otherTableData.length > 0 &&
                            <><div style={{ marginTop: 12, marginBottom: 12 }}>
                                <Title name={formatMessage({ id: 'shipment.other.drug' })}></Title>
                            </div>
                                <Table
                                    className="mar-top-10"
                                    size="small"
                                    dataSource={otherTableData}
                                    pagination={false}
                                    rowKey={(record) => (record.id)}
                                >
                                    <Table.Column
                                        title={<FormattedMessage id="drug.configure.drugName" />}
                                        dataIndex={"name"}
                                        key="name"
                                        ellipsis
                                        render={(value, record, index) =>
                                            <div style={{
                                                display: '-webkit-box',
                                                WebkitLineClamp: 2,
                                                WebkitBoxOrient: 'vertical',
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                whiteSpace: 'normal',
                                                wordBreak: 'break-word',
                                            }}>
                                                {value}
                                            </div>
                                        }
                                    />
                                    <Table.Column
                                        title={<FormattedMessage id="drug.list.expireDate" />}
                                        dataIndex={"expire_date"}
                                        key="expire_date"
                                        ellipsis
                                        width={120}
                                    />
                                    <Table.Column
                                        title={<FormattedMessage id="drug.list.batch" />}
                                        dataIndex={"batch"}
                                        key="batch"
                                        ellipsis
                                        width={140}
                                    />
                                    {packageIsOpen && (
                                        <Table.Column
                                            title={intl.formatMessage({
                                                id: "shipment.order.package.method",
                                            })}
                                            dataIndex="package_method"
                                            key="package_method"
                                            ellipsis
                                            width={110}
                                            render={(value, record, index) =>
                                                renderPackageMethod(value)
                                            }
                                        />
                                    )}
                                    <Table.Column
                                        title={<FormattedMessage id="drug.freeze.count" />}
                                        dataIndex={"use_count"}
                                        key="use_count"
                                        width={90}
                                        ellipsis
                                    />
                                    <Table.Column
                                        title={<FormattedMessage id="drug.freeze.confirm.count" />}
                                        dataIndex={"receive_count"}
                                        key="receive_count"
                                        ellipsis
                                        width={120}
                                        render={
                                            (value, record, index) => (
                                                <InputNumber precision={0} min={0} step={1} defaultValue={record.receive_count}
                                                    max={record.use_count}
                                                    onChange={(e) => otherHandleChange({ receive_count: e }, record)} />
                                            )
                                        }
                                    />
                                </Table></>
                        }

                    </Form>
                </Spin>
            </Modal>
        </React.Fragment >
    )
};

