import React, { useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { Badge, Button, Col, Form, Input, message, Modal, Popover, Row, Space, Spin, Table, Tooltip, } from "antd";
import Barcode from "react-barcode";
import { useAuth } from "../../../../context/auth";
import { useSafeState, useUpdateEffect } from "ahooks";
import { useFetch } from "../../../../hooks/request";
import { getOrderMedicines, updateExpirationSingle } from "../../../../api/order";
import { medicineStatusData } from "../../../../tools/medicine_status";
import { medicineStatusColors } from "../../../../data/data";
import { Title } from "components/title";
import { combineRow } from "../../../../utils/merge_cell";
import { getBarcodeRule } from "../../../../api/barcode";
import { SearchOutlined } from "@ant-design/icons";
import { ChangeMedicines } from "./change_medicines";
import { BatchEditExpiration } from "./batch_edit_expiration";
import { permissions } from "../../../../tools/permission";
import { ChangeRecords } from "./change_records";
import { fillTableCellEmptyPlaceholder } from "../../../../components/table";
import { CustomDateTimePicker } from "../../../../components/CustomDateTimePicker";
import { CustomDateTimePickerCopy } from "../../../../components/CustomDateTimePickerCopy";
import { CloseOutlined, } from "@ant-design/icons";
import _ from "lodash";
import { PaginationView } from "components/pagination";
import { usePage } from "../../../../context/page";

export const Medicines = (props) => {
    const intl = useIntl();
    const { formatMessage } = intl;
    const [visible, setVisible] = useSafeState(false);
    const [medicineData, setMedicineData] = useSafeState([]);
    const [tableData, setTableData] = useSafeState([]);
    const [otherTableData, setOtherTableData] = useSafeState([]);
    const [orderNumber, setOrderNumber] = useSafeState(null);
    const [isDtpStatus, setIsDtpStatus] = useSafeState(0);
    const [packageIsOpen, setPackageIsOpen] = useSafeState(false);
    const [orderStatus, setOrderStatus] = useSafeState(null);
    const [codeRule, setCodeRule] = useSafeState(null);
    const [orderType, setOrderType] = useSafeState(null);
    const [searchValue, setSearchValue] = useSafeState(null);
    const [upperNumber, setUpperNumber] = useState("");
    const [lowerNumber, setLowerNumber] = useState("");
    const [orderId, setOrderId] = useSafeState(null);
    var [updateMedicines, setUpdateMedicines] = useSafeState([]);
    var [updateMedicineIds, setUpdateMedicinesIds] = useSafeState([]);
    const [updateOtherMedicines, setUpdateOtherMedicines] = useSafeState([]);
    const [groupCount, setGroupCount] = useSafeState([]);
    const [selectedGroupCount, setSelectedGroupCount] = useSafeState([]);
    const auth = useAuth()
    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId;
    const projectId = auth.project.id;
    const change_medicines_ref = React.useRef();
    const change_records_ref = React.useRef();
    const batch_edit_expiration_ref = React.useRef();
    const page = usePage()
    const [expirationDateForm] = Form.useForm();
    const [batchNumberForm] = Form.useForm();
    const [expirationDateOpen, setExpirationDateOpen] = useSafeState(new Map());
    const [expirationDateIconColor, setExpirationDateIconColor] = useSafeState(new Map());
    const [batchNumberOpen, setBatchNumberOpen] = useSafeState(new Map());
    const [batchNumberIconColor, setBatchNumberIconColor] = useSafeState(new Map());

    const [otherExpirationDateForm] = Form.useForm();
    const [otherBatchNumberForm] = Form.useForm();
    const [otherExpirationDateOpen, setOtherExpirationDateOpen] = useSafeState(new Map());
    const [otherExpirationDateIconColor, setOtherExpirationDateIconColor] = useSafeState(new Map());
    const [otherBatchNumberOpen, setOtherBatchNumberOpen] = useSafeState(new Map());
    const [otherBatchNumberIconColor, setOtherBatchNumberIconColor] = useSafeState(new Map());

    const { runAsync: run_getOrderMedicines, loading } = useFetch(getOrderMedicines, { manual: true })
    const { runAsync: getBarcodeRun } = useFetch(getBarcodeRule, { manual: true })


    const { runAsync: updateExpirationSingleRun, loading: updateExpirationSingleLoading } = useFetch(updateExpirationSingle, { manual: true });

    const show = (id, orderNumber, isDtpStatus) => {
        setVisible(true);
        setOrderNumber(orderNumber);
        setIsDtpStatus(isDtpStatus);
        barcodeCodeRule();
        setOrderId(id);
        getOrderMedicineList();
        page.setCurrentPage(1);
        page.setPageSize(20);
    };

    const updateField = (type, index, newValue) => {
        if (type === "expirationDateOpen") {
            setExpirationDateOpen((preExpirationDateOpen) => {
                const newExpirationDateOpen = new Map(preExpirationDateOpen);
                newExpirationDateOpen.set(index, newValue);
                return newExpirationDateOpen;
            });
        } else if (type === "expirationDateIconColor") {
            setExpirationDateIconColor((preExpirationDateIconColor) => {
                const newExpirationDateIconColor = new Map(preExpirationDateIconColor);
                newExpirationDateIconColor.set(index, newValue);
                return newExpirationDateIconColor;
            });
        } else if (type === "batchNumberOpen") {
            setBatchNumberOpen((preBatchNumberOpen) => {
                const newBatchNumberOpen = new Map(preBatchNumberOpen);
                newBatchNumberOpen.set(index, newValue);
                return newBatchNumberOpen;
            });
        } else if (type === "batchNumberIconColor") {
            setBatchNumberIconColor((preBatchNumberIconColor) => {
                const newBatchNumberIconColor = new Map(preBatchNumberIconColor);
                newBatchNumberIconColor.set(index, newValue);
                return newBatchNumberIconColor;
            });
        } else if (type === "otherExpirationDateOpen") {
            setOtherExpirationDateOpen((preOtherExpirationDateOpen) => {
                const newOtherExpirationDateOpen = new Map(preOtherExpirationDateOpen);
                newOtherExpirationDateOpen.set(index, newValue);
                return newOtherExpirationDateOpen;
            });
        } else if (type === "otherExpirationDateIconColor") {
            setOtherExpirationDateIconColor((preOtherExpirationDateIconColor) => {
                const newOtherExpirationDateIconColor = new Map(preOtherExpirationDateIconColor);
                newOtherExpirationDateIconColor.set(index, newValue);
                return newOtherExpirationDateIconColor;
            });
        } else if (type === "otherBatchNumberOpen") {
            setOtherBatchNumberOpen((preOtherBatchNumberOpen) => {
                const newOtherBatchNumberOpen = new Map(preOtherBatchNumberOpen);
                newOtherBatchNumberOpen.set(index, newValue);
                return newOtherBatchNumberOpen;
            });
        } else if (type === "otherBatchNumberIconColor") {
            setOtherBatchNumberIconColor((preOtherBatchNumberIconColor) => {
                const newOtherBatchNumberIconColor = new Map(preOtherBatchNumberIconColor);
                newOtherBatchNumberIconColor.set(index, newValue);
                return newOtherBatchNumberIconColor;
            });
        }
    };

    const singleUpdateExpiration = (record, isOther, isPackage, idList, orderID, otherList, expirationDate, batchNumber, index, key) => {
        updateExpirationSingleRun({
            customerId: record.customer_id,
            projectId: record.project_id,
            envId: record.env_id,
            isOther: isOther,
            isPackage: isPackage,
            idList: idList,
            orderID: orderID,
            otherList: otherList,
            expirationDate: expirationDate,
            batchNumber: batchNumber,
        }).then(() => {
            if (key === 1) {
                message.success(formatMessage({ id: "common.success" }));
                getOrderMedicineList();
                updateField("expirationDateOpen", index, false);
                expirationDateForm.resetFields();
                props.refresh();
            } else if (key === 2) {
                message.success(formatMessage({ id: "common.success" }));
                getOrderMedicineList();
                updateField("batchNumberOpen", index, false);
                batchNumberForm.resetFields();
                props.refresh();
            } else if (key === 3) {
                message.success(formatMessage({ id: "common.success" }));
                getOrderMedicineList();
                updateField("otherExpirationDateOpen", index, false);
                otherExpirationDateForm.resetFields();
                props.refresh();
            } else if (key === 4) {
                message.success(formatMessage({ id: "common.success" }));
                getOrderMedicineList();
                updateField("otherBatchNumberOpen", index, false);
                otherBatchNumberForm.resetFields();
                props.refresh();
            }
        });
    }

    const singleUpdate = (record, number, index, field) => {
        let expirationDate = null;
        let batchNumber = null;
        let isOther = false;
        let idList = [];
        let orderID = orderId;
        let otherList = [];
        let isPackage = false;
        if (number === 1) {
            //编号研究产品
            isOther = false;
            if (record.packageDrug) {
                //包装
                isPackage = true;
                if (tableData !== null && tableData.length > 0) {
                    let ids = tableData.filter((obj) => obj.package_number === record.package_number).map(obj => obj._id);
                    idList = ids;
                }
            } else {
                //非包装
                idList.push(record._id);
            }
            if (field === "e") {
                expirationDateForm.validateFields().then((value) => {
                    if (value.expiration_date !== undefined && value.expiration_date !== null && value.expiration_date !== "" && value.expiration_date !== "-") {
                        expirationDate = value.expiration_date;
                        singleUpdateExpiration(record, isOther, isPackage, idList, orderID, otherList, expirationDate, batchNumber, index, 1);
                    }
                })
            } else if (field === "b") {
                batchNumberForm.validateFields().then((value) => {
                    if (value.batch_number !== undefined && value.batch_number !== null && value.batch_number !== "" && value.batch_number !== "-") {
                        batchNumber = value.batch_number;
                        singleUpdateExpiration(record, isOther, isPackage, idList, orderID, otherList, expirationDate, batchNumber, index, 2);
                    }
                })
            }
        } else if (number === 2) {
            //未编号研究产品
            isOther = true;
            otherList = [record];
            // otherList = _.cloneDeep([record]);
            isPackage = record.package_method;
            // // 假设 otherList 是您的数组
            // otherList.forEach(item => {
            //     if (item.batch === "-") {
            //         item.batch = ""; // 将满足条件的元素的 batch 属性转换为空字符串
            //     }
            //     if (item.expire_date === "-") {
            //         item.expire_date = ""; // 将满足条件的元素的 expire_date 属性转换为空字符串
            //     }
            // });

            if (field === "e") {
                otherExpirationDateForm.validateFields().then((value) => {
                    if (value.expire_date !== undefined && value.expire_date !== null && value.expire_date !== "" && value.expire_date !== "-") {
                        expirationDate = value.expire_date;
                        singleUpdateExpiration(record, isOther, isPackage, idList, orderID, otherList, expirationDate, batchNumber, index, 3);
                    }
                })
            } else if (field === "b") {
                otherBatchNumberForm.validateFields().then((value) => {
                    if (value.batch !== undefined && value.batch !== null && value.batch !== "" && value.batch !== "-") {
                        batchNumber = value.batch;
                        singleUpdateExpiration(record, isOther, isPackage, idList, orderID, otherList, expirationDate, batchNumber, index, 4);
                    }
                })
            }
        }

    };


    const rowSelection = {
        type: "checkbox",
        onChange: (selectedRowKeys, selectedRows) => {
            setUpdateMedicines(selectedRows);
            setUpdateMedicinesIds(selectedRowKeys);
            var groupInfo = new Map();
            var groupName = _.groupBy(selectedRows, "name")
            _.forEach(groupName, function (value, drugName) {
                var groupBatch = _.groupBy(value, "batch_number")
                _.forEach(groupBatch, function (batchVal, batch) {
                    groupInfo.set(drugName + "/" + batch, batchVal.length)
                });
            });
            setSelectedGroupCount(Array.from(groupInfo));
        },
        selectedRows: updateMedicines,
        selectedRowKeys: updateMedicineIds,
        renderCell: (checked, record, index, originNode, package_numberRowSpan) => {
            return {
                children: originNode,
                props: {
                    rowSpan: tableData[index] !== undefined ? tableData[index]["package_numberRowSpan"] : 1
                }
            }
        }
    };

    const rowOtherSelection = {
        type: "checkbox",
        onChange: (selectedRowKeys, selectedRows) => {
            setUpdateOtherMedicines(selectedRows);
        },
        selectedRows: updateOtherMedicines,
    }

    const barcodeCodeRule = () => {
        getBarcodeRun({
            customerId: customerId,
            projectId: projectId,
            envId: envId,
        }).then((response) => {
            if (response) {
                let data = response.data;
                setCodeRule(data.codeRule1);
            }
        });
    };

    //更换
    const changeMedicines = () => {
        if (updateMedicines.length <= 0 && updateOtherMedicines.length <= 0) {
            message.warning(formatMessage({ id: 'menu.projects.project.build.randomization.tooltip' })).then();
            return false;
        } else {
            if (packageIsOpen) {
                var rows = [];
                var keys = [];
                updateMedicines.forEach((item) => {
                    if (item.package_numberRowSpan !== 0) {
                        rows.push(item)
                        keys.push(item._id)
                    }
                });
                updateMedicines = rows
                updateMedicineIds = keys
            }
            //处理数据
            // 保证有序性
            let set = new Set(updateMedicineIds);
            //判断是否有按照包装接收的数据
            const receiveDataMap = new Map();
            const selectedData = [];

            if (orderType !== 5 && orderType !== 6) {
                medicineData.forEach((item) => {
                    if (set.has(item._id)) {
                        selectedData.push(item);
                        var packageNumber = item.package_number;
                        if (packageIsOpen && packageNumber !== undefined && packageNumber !== null && packageNumber !== "" && packageNumber !== "-") {
                            receiveDataMap.set(packageNumber, packageNumber);
                        }
                    }
                });
                medicineData.forEach((item) => {
                    if (
                        packageIsOpen && item.package_number !== undefined && item.package_number !== null && item.packageNumber !== "-" && item.package_number !== "" &&
                        item.package_number === receiveDataMap.get(item.package_number) &&
                        !set.has(item._id)
                    ) {
                        selectedData.push(item);
                        updateMedicineIds.push(item._id);
                        set.add(item._id);
                    }
                });
            } else {
                tableData.forEach((item) => {
                    selectedData.push(item);
                });
            }
            setUpdateMedicines(selectedData)
            change_medicines_ref.current.show(orderId, orderNumber, packageIsOpen, codeRule, selectedData, updateOtherMedicines);
        }
    };

    //批量编辑-有效期、批次号
    const batchEditExpiration = () => {
        if (updateMedicines.length <= 0 && updateOtherMedicines.length <= 0) {
            message.warning(formatMessage({ id: 'menu.projects.project.build.randomization.tooltip' })).then();
            return false;
        } else {
            if (packageIsOpen) {
                var rows = [];
                var keys = [];
                updateMedicines.forEach((item) => {
                    if (item.package_numberRowSpan !== 0) {
                        rows.push(item)
                        keys.push(item._id)
                    }
                });
                updateMedicines = rows
                updateMedicineIds = keys
            }
            //处理数据
            // 保证有序性
            let set = new Set(updateMedicineIds);
            //判断是否有按照包装接收的数据
            const receiveDataMap = new Map();
            const selectedData = [];

            if (isDtpStatus === 1) {
                medicineData.forEach((item) => {
                    if (set.has(item._id)) {
                        selectedData.push(item);
                        var packageNumber = item.package_number;
                        if (packageIsOpen && packageNumber !== undefined && packageNumber !== null && packageNumber !== "" && packageNumber !== "-") {
                            receiveDataMap.set(packageNumber, packageNumber);
                        }
                    }
                });
                medicineData.forEach((item) => {
                    if (
                        packageIsOpen && item.package_number !== undefined && item.package_number !== null && item.packageNumber !== "-" && item.package_number !== "" &&
                        item.package_number === receiveDataMap.get(item.package_number) &&
                        !set.has(item._id)
                    ) {
                        selectedData.push(item);
                        updateMedicineIds.push(item._id);
                        set.add(item._id);
                    }
                });
            } else {
                tableData.forEach((item) => {
                    selectedData.push(item);
                });
            }

            // console.log(isDtpStatus);
            // console.log(selectedData.length);
            setUpdateMedicines(selectedData)
            batch_edit_expiration_ref.current.show(orderId, orderNumber, packageIsOpen, codeRule, selectedData, updateOtherMedicines);
        }
    };

    const hide = () => {
        setPackageIsOpen(false);
        setVisible(false);
        setTableData([]);
        setOtherTableData([]);
        setUpdateMedicinesIds([]);
        setUpdateMedicines([]);
        setUpdateOtherMedicines([]);
        setSearchValue(null);
        setUpperNumber("");
        setLowerNumber("");
        setOrderId(null);
        setOrderNumber("");
        setIsDtpStatus(0);
        setGroupCount([]);
        setSelectedGroupCount([]);
        props.refresh();
        page.setTotal(0);
        page.setCurrentPage(1);
        page.setData([]);
        page.setPageSize(20);
    };


    const batchHide = () => {
        getOrderMedicineList();
    };

    function renderStatus(value, shortCode) {
        let code_rule = 1           // 自动编码
        if (shortCode === "") {
            code_rule = 0           // 手动上传
        }
        return <Badge color={medicineStatusColors[value]} text={medicineStatusData(code_rule, auth.project.info.research_attribute).find(it => (it.value === value))?.label} />;
    }

    const getOrderMedicineList = () => {
        if (orderId != null) {
            run_getOrderMedicines({
                id: orderId,
                envId: envId,
                roleId: auth.project.permissions.role_id,
                searchValue: searchValue,
                upperNumber: upperNumber,
                lowerNumber: lowerNumber,
            }).then(
                (result) => {
                    const data = result.data
                    if (data.order != null) {
                        let order = data.order[0];
                        if (data.packageIsOpen) {
                            var medicineData = combineRow(order.medicines, "package_number", "package_number", false)
                            medicineData = combineRow(order.medicines, "name", "name", false)
                            medicineData = combineRow(order.medicines, "expiration_date", "expiration_date", false)
                            medicineData = combineRow(order.medicines, "batch_number", "batch_number", false)
                            medicineData = fillTableCellEmptyPlaceholder(medicineData ? medicineData : [])
                            setTableData(medicineData);
                            page.setTotal(medicineData.length)
                            page.setData(medicineData.slice((page.currentPage - 1) * page.pageSize, page.currentPage * page.pageSize))
                            console.log(medicineData)
                        } else {
                            var orderMedicineData = fillTableCellEmptyPlaceholder(order.medicines ? order.medicines : [])
                            setTableData(orderMedicineData);
                            page.setTotal(orderMedicineData.length)
                            page.setData(orderMedicineData.slice((page.currentPage - 1) * page.pageSize, page.currentPage * page.pageSize))
                        }
                        if (searchValue == null || searchValue.length <= 0) {
                            setMedicineData(order.medicines)
                            if (order.medicines.length > 0) {
                                var groupInfo = new Map();
                                var groupName = _.groupBy(order.medicines, "name")
                                _.forEach(groupName, function (value, drugName) {
                                    var groupBatch = _.groupBy(value, "batch_number")
                                    _.forEach(groupBatch, function (batchVal, batch) {
                                        groupInfo.set(drugName + "/" + batch, batchVal.length)
                                    });
                                });
                                setGroupCount(Array.from(groupInfo));
                            }
                        }
                        setPackageIsOpen(data.packageIsOpen);
                        if (order.other_medicines != null && order.other_medicines.length > 0) {
                            setOtherTableData(fillTableCellEmptyPlaceholder(order.other_medicines ? order.other_medicines : []));
                        }
                        setOrderStatus(order._id.status)
                        setOrderType(order._id.type)
                    } else {
                        setTableData([]);
                        page.setData([]);
                        page.setTotal(0);
                    }
                }
            )
        }
    }

    function renderPackageMethod(packageMethod) {
        return packageMethod === true ? (
            <FormattedMessage id="shipment.order.packageMethod.package" />
        ) : (
            <FormattedMessage id="shipment.order.packageMethod.single" />
        );
    }


    const getChangeRecords = () => {
        change_records_ref.current.show(orderId, orderNumber, packageIsOpen, codeRule);
    }


    React.useImperativeHandle(props.bind, () => ({ show }));

    useUpdateEffect(getOrderMedicineList, [orderId, searchValue, upperNumber, lowerNumber]);

    const list = () => {
        var curmedicines = tableData.slice((page.currentPage - 1) * page.pageSize, page.currentPage * page.pageSize);
        page.setData(curmedicines);
    }

    React.useEffect(list, [page.currentPage, page.pageSize]);

    return (
        <React.Fragment>
            <Modal
                className="custom-blarge-modal"
                title={<>{formatMessage({ id: "shipment.order.medicines" })}-<span style={{ fontWeight: 400 }}>{orderNumber}</span></>}
                open={visible}
                onCancel={hide}
                maskClosable={false}
                centered={true}
                bodyStyle={{ padding: 24 }}
                footer={
                    ((!(orderType === 5 || orderType === 6) && (orderStatus === 1 || orderStatus === 6) && permissions(
                        auth.project.permissions,
                        "operation.supply.shipment.detail.change"
                    )) || (!(orderType === 5 || orderType === 6) && permissions(
                        auth.project.permissions,
                        "operation.supply.shipment.detail.changeRecord"
                    )) || (isDtpStatus === 1 && permissions(
                        auth.project.permissions,
                        "operation.supply.shipment.detail.edit"))
                    ) ?

                        <Row justify={"end"}>
                            <Space>
                                <Button
                                    onClick={() => {
                                        hide();
                                    }}
                                >
                                    {formatMessage({ id: "common.cancel" })}
                                </Button>
                                {!(orderType === 5 || orderType === 6) && isDtpStatus !== 1 && permissions(
                                    auth.project.permissions,
                                    "operation.supply.shipment.detail.changeRecord"
                                ) && <Button
                                    onClick={(e) => {
                                        getChangeRecords()
                                    }}
                                >
                                        <FormattedMessage id="shipment.order.medicines.change.records" />
                                    </Button>
                                }
                                {(!(orderType === 5 || orderType === 6) && (orderStatus === 1 || orderStatus === 6) && isDtpStatus !== 1 && permissions(
                                    auth.project.permissions,
                                    "operation.supply.shipment.detail.change"
                                )) && <Button type="primary"
                                    onClick={(e) => {
                                        changeMedicines()
                                    }}
                                >
                                        <FormattedMessage id="shipment.order.medicines.change" />
                                    </Button>}
                                {
                                    (isDtpStatus === 1 && permissions(
                                        auth.project.permissions,
                                        "operation.supply.shipment.detail.edit"
                                    )) ? <Button type="primary"
                                        onClick={(e) => {
                                            batchEditExpiration()
                                        }}
                                    >
                                        <FormattedMessage id="common.edit.batch" />
                                    </Button> : null
                                }
                            </Space>
                        </Row>
                        : null}
            >
                <Spin spinning={loading}>
                    <>
                        {medicineData != null && medicineData.length > 0 &&
                            <div style={{ marginBottom: isDtpStatus === 1 ? -20 : 12 }}>
                                <Title name={formatMessage({ id: 'drug.medicine' })}></Title>
                                {
                                    !(orderType === 5 || orderType === 6) && isDtpStatus !== 1 &&
                                    <Row gutter={8} justify="space-between" style={{ marginBottom: 12, marginTop: 12 }}>
                                        <Col xs={12} sm={12} md={12} lg={4} style={{ paddingRight: 0 }}>
                                            <Input
                                                placeholder={formatMessage({ id: "common.required.prefix" })}
                                                onChange={e => {
                                                    setSearchValue(e.target.value)
                                                }} marginTop
                                                value={searchValue}
                                                style={{ width: 220, marginRight: 12 }}
                                                suffix={<SearchOutlined style={{ color: "#BFBFBF" }} />}
                                                allowClear
                                            />
                                        </Col>

                                    </Row>
                                }
                                {
                                    (isDtpStatus === 1 && permissions(
                                        auth.project.permissions,
                                        "operation.supply.shipment.detail.edit"
                                    )) ?
                                        <Row>
                                            <div style={{ textAlign: "left", display: "inline-block", marginTop: "16px", marginBottom: "16px" }}>
                                                <Col style={{ textAlign: "left" }}>
                                                    {
                                                        formatMessage({
                                                            id: "drug.list.drugNumber",
                                                        }) + "："
                                                    }
                                                    <Input
                                                        placeholder={formatMessage({
                                                            id: "common.required.prefix",
                                                        })}
                                                        onChange={(e) => {
                                                            setUpperNumber(e.target.value);
                                                        }}
                                                        value={upperNumber}
                                                        style={{ width: 100, marginRight: 12 }}
                                                        allowClear
                                                    />
                                                    ～
                                                    <Input
                                                        placeholder={formatMessage({
                                                            id: "common.required.prefix",
                                                        })}
                                                        onChange={(e) => {
                                                            setLowerNumber(e.target.value);
                                                        }}
                                                        value={lowerNumber}
                                                        style={{ width: 100, marginLeft: 12 }}
                                                        allowClear
                                                    />
                                                </Col>
                                            </div>
                                        </Row> : null
                                }
                            </div>
                        }
                        {tableData != null && tableData.length > 0 &&
                            <>
                                {groupCount.length > 0 &&
                                    <div style={{ marginTop: 0, marginBottom: 12 }}>
                                        <div className="rectangle" style={{ fontSize: 12, fontFamily: "PingFang SC", fontWeight: 400, color: "#677283" }} >
                                            <span style={{ marginLeft: 8, marginTop: 8, marginBottom: 8, marginRight: 8 }}>
                                                {formatMessage({ id: 'common.statistics' })}：{
                                                    groupCount.map(
                                                        (it) => (
                                                            <>
                                                                <span style={{ fontSize: 12, fontFamily: "PingFang SC", fontWeight: 400, color: "#677283" }}>{it[0]}：</span>
                                                                <span style={{ fontSize: 12, fontFamily: "PingFang SC", fontWeight: 400, color: "#1D2129" }}>{it[1]}；</span>
                                                            </>
                                                        )
                                                    )
                                                }
                                            </span>
                                        </div>
                                    </div>
                                }
                                <div>
                                    <Table
                                        size="large"
                                        dataSource={page.data}
                                        pagination={false}
                                        scroll={{ x: 450 }}
                                        rowKey={(record) => (record._id)}
                                        rowSelection={((!(orderType === 5 || orderType === 6) && (orderStatus === 1 || orderStatus === 6) && permissions(
                                            auth.project.permissions,
                                            "operation.supply.shipment.detail.change"
                                        )) || (isDtpStatus === 1 && permissions(
                                            auth.project.permissions,
                                            "operation.supply.shipment.detail.edit"
                                        ))) ? rowSelection : null}
                                    >
                                        {packageIsOpen && <Table.Column
                                            title={formatMessage({ id: 'drug.medicine.packlist' })}
                                            dataIndex="package_number"
                                            key="package_number"
                                            ellipsis
                                            width={180}
                                            onCell={(_, index) => {
                                                return { rowSpan: tableData[index].package_numberRowSpan }
                                            }}
                                        //width={125}
                                        // render={(value, record, index) => {
                                        //     let newValue = ""
                                        //     if (value !== undefined && value !== "") {
                                        //         newValue = value
                                        //     } else {
                                        //         newValue = "-"
                                        //     }
                                        //     let obj = {
                                        //         children: newValue,
                                        //         props: { rowSpan: tableData[index].package_numberRowSpan }
                                        //     }
                                        //     return obj
                                        // }}
                                        />}
                                        {packageIsOpen ?
                                            <Table.Column
                                                title={formatMessage({ id: 'drug.configure.drugName' })}
                                                dataIndex="name"
                                                key="name"
                                                ellipsis
                                                render={(value, record, index) =>
                                                    <div style={{
                                                        display: '-webkit-box',
                                                        WebkitLineClamp: 2,
                                                        WebkitBoxOrient: 'vertical',
                                                        overflow: 'hidden',
                                                        textOverflow: 'ellipsis',
                                                        whiteSpace: 'normal',
                                                        wordBreak: 'break-word',
                                                    }}>
                                                        {value}
                                                    </div>
                                                }
                                            // width={175}
                                            // onCell={(_, index) => {
                                            //     return { rowSpan: tableData[index].nameRowSpan }
                                            // }}
                                            />
                                            :
                                            <Table.Column
                                                title={formatMessage({ id: 'drug.configure.drugName' })}
                                                dataIndex="name"
                                                key="name"
                                                ellipsis
                                                render={(value, record, index) =>
                                                    <div style={{
                                                        display: '-webkit-box',
                                                        WebkitLineClamp: 2,
                                                        WebkitBoxOrient: 'vertical',
                                                        overflow: 'hidden',
                                                        textOverflow: 'ellipsis',
                                                        whiteSpace: 'normal',
                                                        wordBreak: 'break-word',
                                                    }}>
                                                        {value}
                                                    </div>
                                                }
                                            //width={175}
                                            />
                                        }

                                        <Table.Column
                                            title={formatMessage({ id: 'drug.list.drugNumber' })}
                                            dataIndex="number"
                                            key="number"
                                            ellipsis
                                            width={140}
                                        />
                                        <Table.Column
                                            title={formatMessage({ id: 'drug.list.expireDate' })}
                                            dataIndex="expiration_date"
                                            key="expiration_date"
                                            ellipsis
                                            width={120}
                                            onCell={(_, index) => {
                                                return {
                                                    rowSpan: tableData[index].expiration_dateRowSpan
                                                }
                                            }}
                                            render={(value, record, index) => {
                                                return <>
                                                    <Col>
                                                        {value}
                                                        {(isDtpStatus === 1 && permissions(
                                                            auth.project.permissions,
                                                            "operation.supply.shipment.detail.edit"
                                                        )) ?
                                                            <Popover
                                                                placement="top"
                                                                open={(expirationDateOpen && expirationDateOpen.has(index)) ? expirationDateOpen.get(index) : false}
                                                                title={
                                                                    <div>
                                                                        {formatMessage({
                                                                            id: "common.edit",
                                                                        })}
                                                                        <Button
                                                                            onClick={() => {
                                                                                updateField("expirationDateOpen", index, false);
                                                                                expirationDateForm.resetFields();
                                                                                props.refresh();
                                                                            }}
                                                                            type="text"
                                                                            icon={
                                                                                <CloseOutlined
                                                                                    style={{
                                                                                        color: "#666666",
                                                                                    }}
                                                                                />
                                                                            }
                                                                            style={{
                                                                                float: "right",
                                                                                marginRight: -4,
                                                                                width: "15px",
                                                                                height: "15px",
                                                                            }}
                                                                        />
                                                                    </div>
                                                                }
                                                                content={
                                                                    <div>
                                                                        <Form form={expirationDateForm}>
                                                                            <Form.Item
                                                                                name="expiration_date"
                                                                                rules={[
                                                                                    {
                                                                                        required: true,
                                                                                        message: formatMessage({ id: "placeholder.select.common" })
                                                                                    },
                                                                                ]}
                                                                                label={
                                                                                    <span
                                                                                        style={{
                                                                                            fontFamily: "PingFang SC",
                                                                                            fontSize: "14px",
                                                                                            fontWeight: 400,
                                                                                            lineHeight: "20px",
                                                                                            letterSpacing: "0px",
                                                                                            textAlign: "left",
                                                                                            color: "#677283",
                                                                                        }}
                                                                                    >
                                                                                        {formatMessage({
                                                                                            id: "drug.list.expireDate",
                                                                                        })}
                                                                                    </span>
                                                                                }
                                                                            >
                                                                                {
                                                                                    tableData != null && index < 3 ?
                                                                                        <CustomDateTimePicker></CustomDateTimePicker> :
                                                                                        <CustomDateTimePickerCopy ></CustomDateTimePickerCopy>
                                                                                }
                                                                            </Form.Item>
                                                                        </Form>
                                                                        <Row justify={"end"}>
                                                                            <Space>
                                                                                <Button
                                                                                    onClick={() => {
                                                                                        updateField("expirationDateOpen", index, false);
                                                                                        expirationDateForm.resetFields();
                                                                                        props.refresh();
                                                                                    }}
                                                                                    size="small"
                                                                                >
                                                                                    {formatMessage({
                                                                                        id: "common.cancel",
                                                                                    })}
                                                                                </Button>
                                                                                <Button
                                                                                    onClick={(e) => {
                                                                                        singleUpdate(record, 1, index, "e")
                                                                                    }}
                                                                                    loading={updateExpirationSingleLoading}
                                                                                    type={"primary"}
                                                                                    size="small"
                                                                                >
                                                                                    {formatMessage({
                                                                                        id: "common.ok",
                                                                                    })}
                                                                                </Button>
                                                                            </Space>
                                                                        </Row>
                                                                    </div>
                                                                }
                                                                style={{
                                                                    height: "180px",
                                                                    width: "260px !important",
                                                                    left: "496px",
                                                                    top: "155px",
                                                                    borderRadius: "2px",
                                                                    marginTop: "16px",
                                                                    marginLeft: "12px",
                                                                }}
                                                                trigger="click"
                                                                onOpenChange={(e) => {
                                                                    let date = (value !== null && value !== undefined && value !== "-") ? value : null;
                                                                    expirationDateForm.setFieldsValue({ "expiration_date": date });
                                                                    if (e != null) {
                                                                        if (e === true) {
                                                                            updateField("expirationDateIconColor", index, 1);
                                                                        } else {
                                                                            updateField("expirationDateIconColor", index, 0);
                                                                        }
                                                                    }
                                                                    updateField("expirationDateOpen", index, e);
                                                                }}
                                                            >
                                                                <Tooltip
                                                                    title={formatMessage({
                                                                        id: "common.edit",
                                                                    })}
                                                                >
                                                                    <i
                                                                        style={{
                                                                            marginLeft: 8,
                                                                            cursor: "pointer",
                                                                            color: (((expirationDateIconColor && expirationDateIconColor.has(index)) ? expirationDateIconColor.get(index) : 0) === 0) ? "#999999" : "#165DFF",
                                                                        }}
                                                                        className="iconfont icon-bianji"
                                                                        onMouseEnter={() => {
                                                                            updateField("expirationDateIconColor", index, 1);
                                                                        }}
                                                                        onMouseLeave={() => {
                                                                            updateField("expirationDateIconColor", index, 0);
                                                                        }}
                                                                    ></i>
                                                                </Tooltip>
                                                            </Popover> : null
                                                        }
                                                    </Col>
                                                </>
                                            }}
                                        //width={120}
                                        />
                                        <Table.Column
                                            title={formatMessage({ id: 'drug.list.batch' })}
                                            dataIndex="batch_number"
                                            key="batch_number"
                                            width={140}
                                            ellipsis
                                            onCell={(_, index) => {
                                                return { rowSpan: tableData[index].batch_numberRowSpan }
                                            }}
                                            render={(value, record, index) => {
                                                return <>
                                                    <Col>
                                                        {value}
                                                        {(isDtpStatus === 1 && permissions(
                                                            auth.project.permissions,
                                                            "operation.supply.shipment.detail.edit"
                                                        )) ?
                                                            <Popover
                                                                placement="top"
                                                                open={(batchNumberOpen && batchNumberOpen.has(index)) ? batchNumberOpen.get(index) : false}
                                                                title={
                                                                    <div>
                                                                        {formatMessage({
                                                                            id: "common.edit",
                                                                        })}
                                                                        <Button
                                                                            onClick={() => {
                                                                                updateField("batchNumberOpen", index, false);
                                                                                batchNumberForm.resetFields();
                                                                                props.refresh();
                                                                            }}
                                                                            type="text"
                                                                            icon={
                                                                                <CloseOutlined
                                                                                    style={{
                                                                                        color: "#666666",
                                                                                    }}
                                                                                />
                                                                            }
                                                                            style={{
                                                                                float: "right",
                                                                                marginRight: -4,
                                                                                width: "15px",
                                                                                height: "15px",
                                                                            }}
                                                                        />
                                                                    </div>
                                                                }
                                                                content={
                                                                    <div>
                                                                        <Form form={batchNumberForm}>
                                                                            <Form.Item
                                                                                name="batch_number"
                                                                                rules={[
                                                                                    {
                                                                                        required: true,
                                                                                        message: formatMessage({ id: "placeholder.input.common" })
                                                                                    },
                                                                                ]}
                                                                                label={
                                                                                    <span
                                                                                        style={{
                                                                                            fontFamily: "PingFang SC",
                                                                                            fontSize: "14px",
                                                                                            fontWeight: 400,
                                                                                            lineHeight: "20px",
                                                                                            letterSpacing: "0px",
                                                                                            textAlign: "left",
                                                                                            color: "#677283",
                                                                                        }}
                                                                                    >
                                                                                        {formatMessage({
                                                                                            id: "drug.list.batch",
                                                                                        })}
                                                                                    </span>
                                                                                }
                                                                            >
                                                                                <Input placeholder={formatMessage({ id: 'placeholder.input.common' })} className="full-width" />
                                                                            </Form.Item>
                                                                        </Form>
                                                                        <Row justify={"end"}>
                                                                            <Space>
                                                                                <Button
                                                                                    onClick={() => {
                                                                                        updateField("batchNumberOpen", index, false);
                                                                                        batchNumberForm.resetFields();
                                                                                        props.refresh();
                                                                                    }}
                                                                                    size="small"
                                                                                >
                                                                                    {formatMessage({
                                                                                        id: "common.cancel",
                                                                                    })}
                                                                                </Button>
                                                                                <Button
                                                                                    onClick={(e) => {
                                                                                        singleUpdate(record, 1, index, "b")
                                                                                    }}
                                                                                    loading={updateExpirationSingleLoading}
                                                                                    type={"primary"}
                                                                                    size="small"
                                                                                >
                                                                                    {formatMessage({
                                                                                        id: "common.ok",
                                                                                    })}
                                                                                </Button>
                                                                            </Space>
                                                                        </Row>
                                                                    </div>
                                                                }
                                                                style={{
                                                                    height: "180px",
                                                                    width: "260px !important",
                                                                    left: "496px",
                                                                    top: "155px",
                                                                    borderRadius: "2px",
                                                                    marginTop: "16px",
                                                                    marginLeft: "12px",
                                                                }}
                                                                trigger="click"
                                                                onOpenChange={(e) => {
                                                                    let date = (value !== null && value !== undefined && value !== "-") ? value : null;
                                                                    batchNumberForm.setFieldsValue({ "batch_number": date });
                                                                    if (e != null) {
                                                                        if (e === true) {
                                                                            updateField("batchNumberIconColor", index, 1);
                                                                        } else {
                                                                            updateField("batchNumberIconColor", index, 0);
                                                                        }
                                                                    }
                                                                    updateField("batchNumberOpen", index, e);
                                                                }}
                                                            >
                                                                <Tooltip
                                                                    title={formatMessage({
                                                                        id: "common.edit",
                                                                    })}
                                                                >
                                                                    <i
                                                                        style={{
                                                                            marginLeft: 8,
                                                                            cursor: "pointer",
                                                                            color: (((batchNumberIconColor && batchNumberIconColor.has(index)) ? batchNumberIconColor.get(index) : 0) === 0) ? "#999999" : "#165DFF",
                                                                        }}
                                                                        className="iconfont icon-bianji"
                                                                        onMouseEnter={() => {
                                                                            updateField("batchNumberIconColor", index, 1);
                                                                        }}
                                                                        onMouseLeave={() => {
                                                                            updateField("batchNumberIconColor", index, 0);
                                                                        }}
                                                                    ></i>
                                                                </Tooltip>
                                                            </Popover> : null
                                                        }
                                                    </Col>
                                                </>
                                            }}
                                        />
                                        <Table.Column
                                            title={formatMessage({ id: "shipment.status" })}
                                            dataIndex="status"
                                            key="status"
                                            ellipsis
                                            width={100}
                                            render={(value, record, index) => (renderStatus(value, record.shortCode))}
                                        />
                                        {codeRule &&
                                            <>
                                                <Table.Column title={<FormattedMessage id="barcode" />} key="number"
                                                    dataIndex="number" align="center" ellipsis
                                                    render={(value, record, index) => {
                                                        return (
                                                            <Popover content={
                                                                <>
                                                                    <Barcode value={value} displayValue={false} height={60} width={1}
                                                                        format="CODE128" />
                                                                    <br />
                                                                    <span>&nbsp;&nbsp;{value}</span>
                                                                </>
                                                            } trigger="click">
                                                                {
                                                                    record.short_code !== "" && record.short_code !== "-" ?
                                                                        <Button size="small"
                                                                            type="link">
                                                                            <FormattedMessage id="form.preview" />
                                                                        </Button>
                                                                        :
                                                                        "-"
                                                                }
                                                            </Popover>
                                                        )
                                                    }} />
                                                {packageIsOpen && <Table.Column title={<FormattedMessage id="packageBarcode" />} key="package_number"
                                                    dataIndex="package_number" align="center" ellipsis
                                                    render={(value, record, index) => {
                                                        return (
                                                            <Popover content={
                                                                <>
                                                                    <Barcode value={value} displayValue={false} height={60} width={1}
                                                                        format="CODE128" />
                                                                    <br />
                                                                    <span>&nbsp;&nbsp;{value}</span>
                                                                </>
                                                            } trigger="click">
                                                                {
                                                                    record.short_code !== "" && record.short_code !== "-" && record.package_number !== "-" && record.package_number !== "" ?
                                                                        <Button size="small"
                                                                            type="link">
                                                                            <FormattedMessage id="form.preview" />
                                                                        </Button>
                                                                        :
                                                                        "-"
                                                                }
                                                            </Popover>
                                                        )
                                                    }} />}
                                            </>
                                        }
                                    </Table>
                                    <PaginationView />
                                </div>
                                {selectedGroupCount.length > 0 &&
                                    isDtpStatus === 0 ?
                                    <div style={{ marginTop: 12, marginBottom: 0 }}>
                                        <div style={{ fontSize: 12, fontFamily: "PingFang SC", fontWeight: 400, color: "#677283" }} >
                                            <span style={{ marginLeft: 8, marginTop: 8, marginBottom: 8, marginRight: 8 }}>
                                                {formatMessage({ id: 'common.statistics' })}：{
                                                    selectedGroupCount.map(
                                                        (it) => (
                                                            <>
                                                                <span style={{ fontSize: 12, fontFamily: "PingFang SC", fontWeight: 400, color: "#677283" }}>{it[0]}：</span>
                                                                <span style={{ fontSize: 12, fontFamily: "PingFang SC", fontWeight: 400, color: "#1D2129" }}>{it[1]}；</span>
                                                            </>
                                                        )
                                                    )
                                                }
                                            </span>
                                        </div>
                                    </div> : null
                                }
                            </>
                        }
                    </>

                    {otherTableData != null && otherTableData.length > 0 &&
                        <><div style={{ marginTop: 12, marginBottom: 12 }}>
                            <Title name={formatMessage({ id: 'shipment.other.drug' })}></Title>
                        </div>
                            <Table
                                className="mar-top-10"
                                size="large"
                                dataSource={otherTableData}
                                pagination={false}
                                scroll={{ x: 450 }}
                                rowKey={(record) => (record.name + record.expire_data + record.batch)}
                                rowSelection={((!(orderType === 5 || orderType === 6) && (orderStatus === 1 || orderStatus === 6) && permissions(
                                    auth.project.permissions,
                                    "operation.supply.shipment.detail.change"
                                )) || (isDtpStatus === 1 && permissions(
                                    auth.project.permissions,
                                    "operation.supply.shipment.detail.edit"
                                ))) ? rowOtherSelection : null}
                            >
                                <Table.Column
                                    title={<FormattedMessage id="drug.configure.drugName" />}
                                    dataIndex="name"
                                    key="name"
                                    ellipsis
                                    render={(value, record, index) =>
                                        <div style={{
                                            display: '-webkit-box',
                                            WebkitLineClamp: 2,
                                            WebkitBoxOrient: 'vertical',
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis',
                                            whiteSpace: 'normal',
                                            wordBreak: 'break-word',
                                        }}>
                                            {value}
                                        </div>
                                    }
                                />
                                <Table.Column
                                    title={<FormattedMessage id="drug.list.expireDate" />}
                                    dataIndex="expire_date"
                                    key="expire_date"
                                    ellipsis
                                    width={120}
                                    render={(value, record, index) => {
                                        return <>
                                            <Col>
                                                {value}
                                                {(isDtpStatus === 1 && permissions(
                                                    auth.project.permissions,
                                                    "operation.supply.shipment.detail.edit"
                                                )) ?
                                                    <Popover
                                                        placement="top"
                                                        open={(otherExpirationDateOpen && otherExpirationDateOpen.has(index)) ? otherExpirationDateOpen.get(index) : false}
                                                        title={
                                                            <div>
                                                                {formatMessage({
                                                                    id: "common.edit",
                                                                })}
                                                                <Button
                                                                    onClick={() => {
                                                                        updateField("otherExpirationDateOpen", index, false);
                                                                        otherExpirationDateForm.resetFields();
                                                                        props.refresh();
                                                                    }}
                                                                    type="text"
                                                                    icon={
                                                                        <CloseOutlined
                                                                            style={{
                                                                                color: "#666666",
                                                                            }}
                                                                        />
                                                                    }
                                                                    style={{
                                                                        float: "right",
                                                                        marginRight: -4,
                                                                        width: "15px",
                                                                        height: "15px",
                                                                    }}
                                                                />
                                                            </div>
                                                        }
                                                        content={
                                                            <div>
                                                                <Form form={otherExpirationDateForm}>
                                                                    <Form.Item
                                                                        name="expire_date"
                                                                        rules={[
                                                                            {
                                                                                required: true,
                                                                                message: formatMessage({ id: "placeholder.select.common" })
                                                                            },
                                                                        ]}
                                                                        label={
                                                                            <span
                                                                                style={{
                                                                                    fontFamily: "PingFang SC",
                                                                                    fontSize: "14px",
                                                                                    fontWeight: 400,
                                                                                    lineHeight: "20px",
                                                                                    letterSpacing: "0px",
                                                                                    textAlign: "left",
                                                                                    color: "#677283",
                                                                                }}
                                                                            >
                                                                                {formatMessage({
                                                                                    id: "drug.list.expireDate",
                                                                                })}
                                                                            </span>
                                                                        }
                                                                    >
                                                                        {
                                                                            ((tableData != null && otherTableData != null && tableData.length + otherTableData.length < 2) ||
                                                                                ((tableData == null || tableData.length === 0) && otherTableData != null && index < 3)) ?
                                                                                <CustomDateTimePicker></CustomDateTimePicker> :
                                                                                <CustomDateTimePickerCopy ></CustomDateTimePickerCopy>
                                                                        }
                                                                    </Form.Item>
                                                                </Form>
                                                                <Row justify={"end"}>
                                                                    <Space>
                                                                        <Button
                                                                            onClick={() => {
                                                                                updateField("otherExpirationDateOpen", index, false);
                                                                                otherExpirationDateForm.resetFields();
                                                                                props.refresh();
                                                                            }}
                                                                            size="small"
                                                                        >
                                                                            {formatMessage({
                                                                                id: "common.cancel",
                                                                            })}
                                                                        </Button>
                                                                        <Button
                                                                            onClick={(e) => {
                                                                                singleUpdate(record, 2, index, "e")
                                                                            }}
                                                                            loading={updateExpirationSingleLoading}
                                                                            type={"primary"}
                                                                            size="small"
                                                                        >
                                                                            {formatMessage({
                                                                                id: "common.ok",
                                                                            })}
                                                                        </Button>
                                                                    </Space>
                                                                </Row>
                                                            </div>
                                                        }
                                                        style={{
                                                            height: "180px",
                                                            width: "260px !important",
                                                            left: "496px",
                                                            top: "155px",
                                                            borderRadius: "2px",
                                                            marginTop: "16px",
                                                            marginLeft: "12px",
                                                        }}
                                                        trigger="click"
                                                        onOpenChange={(e) => {
                                                            let date = (value !== null && value !== undefined && value !== "-") ? value : null;
                                                            otherExpirationDateForm.setFieldsValue({ "expire_date": date });
                                                            if (e != null) {
                                                                if (e === true) {
                                                                    updateField("otherExpirationDateIconColor", index, 1);
                                                                } else {
                                                                    updateField("otherExpirationDateIconColor", index, 0);
                                                                }
                                                            }
                                                            updateField("otherExpirationDateOpen", index, e);
                                                        }}
                                                    >
                                                        <Tooltip
                                                            title={formatMessage({
                                                                id: "common.edit",
                                                            })}
                                                        >
                                                            <i
                                                                style={{
                                                                    marginLeft: 8,
                                                                    cursor: "pointer",
                                                                    color: (((otherExpirationDateIconColor && otherExpirationDateIconColor.has(index)) ? otherExpirationDateIconColor.get(index) : 0) === 0) ? "#999999" : "#165DFF",
                                                                }}
                                                                className="iconfont icon-bianji"
                                                                onMouseEnter={() => {
                                                                    updateField("otherExpirationDateIconColor", index, 1);
                                                                }}
                                                                onMouseLeave={() => {
                                                                    updateField("otherExpirationDateIconColor", index, 0);
                                                                }}
                                                            ></i>
                                                        </Tooltip>
                                                    </Popover> : null
                                                }
                                            </Col>
                                        </>
                                    }}
                                />
                                <Table.Column
                                    title={<FormattedMessage id="drug.list.batch" />}
                                    dataIndex="batch"
                                    key="batch"
                                    ellipsis
                                    width={140}
                                    render={(value, record, index) => {
                                        return <>
                                            <Col>
                                                {value}
                                                {(isDtpStatus === 1 && permissions(
                                                    auth.project.permissions,
                                                    "operation.supply.shipment.detail.edit"
                                                )) ?
                                                    <Popover
                                                        placement="top"
                                                        open={(otherBatchNumberOpen && otherBatchNumberOpen.has(index)) ? otherBatchNumberOpen.get(index) : false}
                                                        title={
                                                            <div>
                                                                {formatMessage({
                                                                    id: "common.edit",
                                                                })}
                                                                <Button
                                                                    onClick={() => {
                                                                        updateField("otherBatchNumberOpen", index, false);
                                                                        otherBatchNumberForm.resetFields();
                                                                        props.refresh();
                                                                    }}
                                                                    type="text"
                                                                    icon={
                                                                        <CloseOutlined
                                                                            style={{
                                                                                color: "#666666",
                                                                            }}
                                                                        />
                                                                    }
                                                                    style={{
                                                                        float: "right",
                                                                        marginRight: -4,
                                                                        width: "15px",
                                                                        height: "15px",
                                                                    }}
                                                                />
                                                            </div>
                                                        }
                                                        content={
                                                            <div>
                                                                <Form form={otherBatchNumberForm}>
                                                                    <Form.Item
                                                                        name="batch"
                                                                        rules={[
                                                                            {
                                                                                required: true,
                                                                                message: formatMessage({ id: "placeholder.input.common" })
                                                                            },
                                                                        ]}
                                                                        label={
                                                                            <span
                                                                                style={{
                                                                                    fontFamily: "PingFang SC",
                                                                                    fontSize: "14px",
                                                                                    fontWeight: 400,
                                                                                    lineHeight: "20px",
                                                                                    letterSpacing: "0px",
                                                                                    textAlign: "left",
                                                                                    color: "#677283",
                                                                                }}
                                                                            >
                                                                                {formatMessage({
                                                                                    id: "drug.list.batch",
                                                                                })}
                                                                            </span>
                                                                        }
                                                                    >
                                                                        <Input placeholder={formatMessage({ id: 'placeholder.input.common' })} className="full-width" />
                                                                    </Form.Item>
                                                                </Form>
                                                                <Row justify={"end"}>
                                                                    <Space>
                                                                        <Button
                                                                            onClick={() => {
                                                                                updateField("otherBatchNumberOpen", index, false);
                                                                                otherBatchNumberForm.resetFields();
                                                                                props.refresh();
                                                                            }}
                                                                            size="small"
                                                                        >
                                                                            {formatMessage({
                                                                                id: "common.cancel",
                                                                            })}
                                                                        </Button>
                                                                        <Button
                                                                            onClick={(e) => {
                                                                                singleUpdate(record, 2, index, "b")
                                                                            }}
                                                                            loading={updateExpirationSingleLoading}
                                                                            type={"primary"}
                                                                            size="small"
                                                                        >
                                                                            {formatMessage({
                                                                                id: "common.ok",
                                                                            })}
                                                                        </Button>
                                                                    </Space>
                                                                </Row>
                                                            </div>
                                                        }
                                                        style={{
                                                            height: "180px",
                                                            width: "260px !important",
                                                            left: "496px",
                                                            top: "155px",
                                                            borderRadius: "2px",
                                                            marginTop: "16px",
                                                            marginLeft: "12px",
                                                        }}
                                                        trigger="click"
                                                        onOpenChange={(e) => {
                                                            let date = (value !== null && value !== undefined && value !== "-") ? value : null;
                                                            otherBatchNumberForm.setFieldsValue({ "batch": date });
                                                            if (e != null) {
                                                                if (e === true) {
                                                                    updateField("otherBatchNumberIconColor", index, 1);
                                                                } else {
                                                                    updateField("otherBatchNumberIconColor", index, 0);
                                                                }
                                                            }
                                                            updateField("otherBatchNumberOpen", index, e);
                                                        }}
                                                    >
                                                        <Tooltip
                                                            title={formatMessage({
                                                                id: "common.edit",
                                                            })}
                                                        >
                                                            <i
                                                                style={{
                                                                    marginLeft: 8,
                                                                    cursor: "pointer",
                                                                    color: (((otherBatchNumberIconColor && otherBatchNumberIconColor.has(index)) ? otherBatchNumberIconColor.get(index) : 0) === 0) ? "#999999" : "#165DFF",
                                                                }}
                                                                className="iconfont icon-bianji"
                                                                onMouseEnter={() => {
                                                                    updateField("otherBatchNumberIconColor", index, 1);
                                                                }}
                                                                onMouseLeave={() => {
                                                                    updateField("otherBatchNumberIconColor", index, 0);
                                                                }}
                                                            ></i>
                                                        </Tooltip>
                                                    </Popover> : null
                                                }
                                            </Col>
                                        </>
                                    }}
                                />
                                {!(orderType === 5 || orderType === 6) && packageIsOpen && (
                                    <Table.Column
                                        title={intl.formatMessage({
                                            id: "shipment.order.package.method",
                                        })}
                                        dataIndex="package_method"
                                        key="package_method"
                                        ellipsis
                                        width={110}
                                        render={(value, record, index) =>
                                            renderPackageMethod(value)
                                        }
                                    />
                                )}
                                <Table.Column
                                    title={<FormattedMessage id="drug.freeze.count" />}
                                    dataIndex="use_count"
                                    key="use_count"
                                    ellipsis
                                    width={90}
                                />
                                {orderStatus !== 3 ?
                                    <Table.Column
                                        title={<FormattedMessage id="drug.freeze.receive.count" />}
                                        dataIndex="receive_count"
                                        key="receive_count"
                                        ellipsis
                                        width={90}
                                        render={
                                            (value, record, index) => "-"
                                        }
                                    /> :
                                    <Table.Column
                                        title={<FormattedMessage id="drug.freeze.receive.count" />}
                                        dataIndex="use_count"
                                        key="receive_count"
                                        ellipsis
                                        width={90}
                                    />
                                }
                            </Table>
                        </>
                    }
                </Spin>
            </Modal>
            <ChangeMedicines bind={change_medicines_ref} refresh={hide} />
            <BatchEditExpiration bind={batch_edit_expiration_ref} refresh={batchHide} />
            <ChangeRecords bind={change_records_ref} />
        </React.Fragment >
    )
};


