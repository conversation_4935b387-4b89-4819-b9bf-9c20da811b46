import React from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {<PERSON><PERSON>, Modal, Table} from "antd";
import {useSafeState} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {getPushListDetails} from "../../../../api/push";
import {useAuth} from "../../../../context/auth";
import {pushLogStatus, pushLogStatusColors, pushSource} from "../../../../data/data";
import moment from "moment";
import {useGlobal} from "../../../../context/global";
import _ from "lodash";

export const Details = (props) => {
    const auth = useAuth();
    const g = useGlobal();
    const intl = useIntl();
    const {formatMessage} = intl;
    const timeZone = (auth.project.info.timeZoneStr !== undefined && auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "") ? Number(auth.project.info.timeZoneStr) : 8;

    const [visible, setVisible] = useSafeState(false);
    const [fieldsData, setFieldsData] = useSafeState([]);
    const [data, setData] = useSafeState([]);
    const [title, setTitle] = useSafeState("");

    const {runAsync, loading} = useFetch(getPushListDetails, {manual: true});

    const getSourceTypeListStr = (sourceTypeList) => {
        sourceTypeList = _.uniq(sourceTypeList)
        const tmp = sourceTypeList.map(sourceType => {
            if (sourceType === 5) {
                return props.outVisit; // 如果是 5，使用 outVisit 的值
            }
            return pushSource.find(it => it.value === sourceType)?.label;
        });

        return (
            <>
                {tmp.map((label, index) => (
                    <span key={index}>
                        {g.lang==="zh"?"历史":"History "}
                        {label}
                        {index !== tmp.length - 1 && (g.lang==="zh"? "、":",")}
                    </span>
                ))}
            </>
        );

    }

    const show = (info) => {
        setVisible(true);
        if (info) {
            if (info.sourceTypeList != null && info.sourceTypeList.length > 0) {
                let str = getSourceTypeListStr(info.sourceTypeList);
                setTitle(str)
            } else {
                setTitle(info.sourceType === 5? props.outVisit : pushSource.find(it => (it.value === info.sourceType)).label);
            }
            runAsync({
                id: info.id,
                roleId: auth.project.permissions.role_id
            }).then((result) => {
                let pushLogData = [];
                let resultData = result.data;
                setFieldsData(resultData.formData);

                // 处理返回数据
                resultData.data.forEach((rd) => {
                    if (rd.status === 1 || rd.status === 5){
                        rd["edcResult"] = rd.edcResult;
                    } else {
                        rd["edcResult"] = JSON.stringify(rd.edcResult)+"";
                    }
                    pushLogData.push(rd);
                });
                setData(pushLogData);
            });
        }
    };

    const hide = () => {
        setVisible(false);
        setFieldsData([]);
        setData([]);
        setTitle("");
    };

    function formatTimezoneOffset(offset) {
        const negative = offset < 0;
        offset = Math.abs(offset);
        const hours = Math.floor(offset);
        const minutes = Math.round((offset - hours) * 60); // 四舍五入处理 .5

        const sign = negative ? "-" : "";
        const hh = String(hours).padStart(2, '0');
        const mm = String(minutes).padStart(2, '0');

        return `${sign}${hh}:${mm}`;
    }

    React.useImperativeHandle(props.bind, () => ({show}));
    return (
        <React.Fragment>
            <Modal
                centered
                width={1500}
                title={title}
                open={visible}
                
                maskClosable={false}
                onCancel={hide}
                footer={null}
            >
                <Table
                    loading={loading}
                    style={{paddingBottom:50}}
                    className="mar-top-10"
                    dataSource={data}
                    pagination={false}
                    rowKey={(record) => (record.id)}
                >
                    <Table.Column
                        title= {formatMessage({id: 'project.statistics.subject.number'})}
                        key="subjectNo"
                        dataIndex="subjectNo"
                        ellipsis
                        width={150}
                        render={(value) => {
                            if (!value) {
                                return <>-</>
                            }
                            return <>{value}</>
                        }}
                    />

                    {fieldsData != null ? fieldsData.map((field) => (
                        field.name !== "" ?
                            <Table.Column
                                title={field.label}
                                dataIndex={field.name}
                                key={field.name}
                                ellipsis
                                width={120}
                                render={(value) => {
                                    if (!value) {
                                        return <>-</>
                                    }
                                    return <>{value}</>
                                }}
                            />
                            : null
                    )) : null}

                    <Table.Column
                        title= {formatMessage({id: 'common.status'})}
                        key="status"
                        dataIndex="status"
                        ellipsis
                        width={170}
                        render={(value) => {
                            return <Badge color={pushLogStatusColors[value]}
                                        text={pushLogStatus.find(it => (it.value === value)).label} />;
                        }}/>

                    <Table.Column
                        title={<FormattedMessage id="project.statistics.push.time"/>}
                        key="sendTime"
                        dataIndex="sendTime"
                        align="left"
                        width={210}
                        render={(value) => {
                            if (!value) {
                                return <>-</>
                            }
                            return <> {(value === null || value === 0) ? '' : (moment.unix(value).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss') + (timeZone >= 0 ? "(UTC+" : "(UTC") + formatTimezoneOffset(timeZone) + ")")}</>
                        }}/>

                    <Table.Column
                        title= {<FormattedMessage id="project.edc.return"/>}
                        key="edcResult"
                        dataIndex="edcResult"
                        width={120}
                        ellipsis
                        render={(value) => {
                            if (!value) {
                                return <>-</>
                            }
                            return <>{value}</>
                        }}/>
                </Table>
            </Modal>
        </React.Fragment>
    )
};