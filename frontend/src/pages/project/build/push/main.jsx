import React, {useEffect, useState} from "react";
import {Badge, Button, Col, Dropdown, Form, Input, Menu, message, Row, Select, Space, Table} from "antd";
import moment from "moment";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import { useSafeState } from "ahooks";
import { getPushList, pushSend } from "../../../../api/push";
import { usePage } from "../../../../context/page";
import DatePicker from "../../../../components/DatePicker";
import { InsertDivider } from "components/divider";
import { fillTableCellEmptyPlaceholder } from "../../../../components/table";
import { pushMode, pushSource, pushStatus, pushStatusColors } from "../../../../data/data";
import { CustomConfirmModal } from "components/modal";
import { Details } from "./details";
import {permissions, permissionsCohort} from "../../../../tools/permission";
import { getProjectAttribute } from "../../../../api/randomization";
import {getVisitSettings} from "../../../../api/visit";
import {useGlobal} from "../../../../context/global";
import {checkPushHistory, pushHistory} from "../../../../api/subject";
import _ from "lodash";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {AuthButton, AuthDropdown, btnFilter} from "../../../common/auth-wrap";

export const PushListMain = (props) => {
    const auth = useAuth();
    const page = usePage();
    const intl = useTranslation();
    const searchInputRef = React.useRef();
    const g = useGlobal();

    const { formatMessage } = intl;
    const timeZone = (auth.project.info.timeZoneStr !== undefined && auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "") ? Number(auth.project.info.timeZoneStr) : 8;
    const DatePickers = DatePicker;
    const customerId = auth.customerId;
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const cohortId = props.cohort ? props.cohort.id : null;
    const [attribute, setAttribute] = useSafeState(null);
    const [startTime, setStartTime] = useSafeState(null);
    const [endTime, setEndTime] = useSafeState(null);
    const [queryPushStatus, setQueryPushStatus] = useSafeState(2);
    const [subjectNo, setSubjectNo] = useSafeState("");
    const [selectedIds, setSelectedIds] = useSafeState([]);
    const [doSearch, setDoSearch] = useSafeState(0);
    const [afootCount, setAfootCount] = useSafeState(0);
    const [handleCount, setHandleCount] = useSafeState(0);
    const [successCount, setSuccessCount] = useSafeState(0);
    const [failCount, setFailCount] = useSafeState(0);
    const [loseCount, setLoseCount] = useSafeState(0);
    const [outVisit, setOutVisit] = useSafeState("");
    const [visitSettings, setVisitSettings] = useSafeState(null);

    const pushDetails = React.useRef();
    const { runAsync, loading } = useFetch(getPushList, { manual: true });
    const { runAsync: run_pushSend } = useFetch(pushSend, { manual: true });
    const { runAsync: getProjectAttributeRun } = useFetch(getProjectAttribute, { manual: true });
    const { runAsync: checkPushHistoryRun, loading: checkPushHistoryRunLoading, } = useFetch(checkPushHistory, { manual: true });
    const { runAsync: pushHistoryRun, loading: pushHistoryRunLoading } = useFetch(pushHistory, { manual: true });

    const projectStatus = auth.project.status ? auth.project.status : 0;

    const operationButtons = (record) => {
        const allBtns = [];
        //　重新发送
        allBtns.push({
            key: "resend",
            label: "project.statistics.resend",
            onClick: () => send(record.id),
            show: (record.status === 2) && permissionsCohort(auth.project.permissions, "operation.build.push.send",props.cohort?.status) && projectStatus !== 2,
        });
        // 详情
        allBtns.push({
            key: "details",
            label: "common.details",
            onClick: () => details(record),
            show: permissions(auth.project.permissions, "operation.build.push.details"),
        })
        const {btns, moreItems} = btnFilter(allBtns)

        return <div style={{ display: 'flex', alignItems: 'center' }}>
            {InsertDivider(btns.map(it => (<AuthButton
                size="small" type="link" style={{ padding: 0 }}
                onClick={it.onClick}
            >
                <FormattedMessage id={it.label} />
            </AuthButton>)))}
            <AuthDropdown
                show={moreItems.length > 0}
                menu={{items:moreItems.map(it => ({
                    key: it.key,
                    label: <FormattedMessage id={it.label} />,
                    onClick: it.onClick,
                }))}}
            >
                <span style={{ marginLeft: 12 }} className='dropdown-button'><i className='iconfont icon-gengduo-changgui' /></span>
            </AuthDropdown>
        </div>
    };

    const details = (data) => {
        pushDetails.current.show(data);
    };

    function formatTimezoneOffset(offset) {
        const negative = offset < 0;
        offset = Math.abs(offset);
        const hours = Math.floor(offset);
        const minutes = Math.round((offset - hours) * 60); // 四舍五入处理 .5

        const sign = negative ? "-" : "";
        const hh = String(hours).padStart(2, '0');
        const mm = String(minutes).padStart(2, '0');

        return `${sign}${hh}:${mm}`;
    }

    const send = (id) => {
        CustomConfirmModal({
            title: formatMessage({ id: 'project.statistics.ls.resend' }),
            content: formatMessage({ id: 'project.statistics.operation.succeed' }),
            okText: formatMessage({ id: 'common.ok' }) + " ",
            cancelText: formatMessage({ id: "common.cancel" }) + " ",
            onOk: () => {
                run_pushSend({ type: 1, pushId: id }).then((result) => {
                    message.success(result.msg).then();
                    getList();
                    setSelectedIds([]);
                }
                )
            }
        });
    };

    const batchSend = () => {
        if (selectedIds.length > 0) {
            CustomConfirmModal({
                title: formatMessage({ id: 'project.statistics.ls.resend' }),
                content: formatMessage({ id: 'project.statistics.operation.succeed' }),
                okText: formatMessage({ id: 'common.ok' }) + " ",
                cancelText: formatMessage({ id: "common.cancel" }) + " ",
                onOk: () => {
                    run_pushSend({ type: 2, pushIds: selectedIds }).then((result) => {
                        message.success(result.msg).then();
                        getList();
                        setSelectedIds([]);
                    }
                    )
                }
            });
        } else {
            message.error(formatMessage({ id: 'project.statistics.select.data' }));
        }
    };

    const push_history = () => {
        checkPushHistoryRun({
            projectId: projectId,
            envId: envId,
            cohortId: cohortId,
            roleId: auth.project.permissions.role_id,
        }).then((result) => {
            let data = result.data;
            if (data.subjectCount === 0) {
                message.error(formatMessage({ id: "project.statistics.push.history.empty" }));
            } else {
                // 拼接场景
                let contentStr = getHistoryScenarioStr(data.pushScenario);
                CustomConfirmModal({
                    title: formatMessage({ id: "project.statistics.push.history.confirm" }),
                    content: contentStr,
                    okText: formatMessage({ id: 'common.ok' }) + " ",
                    cancelText: formatMessage({ id: "common.cancel" }) + " ",
                    onOk: () => {
                        pushHistoryRun({
                            projectId: projectId,
                            envId: envId,
                            cohortId: cohortId,
                            roleId: auth.project.permissions.role_id,
                        }).then((result) => {
                            message.success(result.msg)
                            getList();
                        })
                    }
                });
            }
        })
    }

    const getHistoryScenarioStr = (scenario) => {
        let list = [];
        if (scenario.registerPush) {
            list.push(formatMessage({ id: "subject.register" }));
        }
        if (scenario.updateRandomFrontPush) {
            list.push(formatMessage({ id: "projects.subject.update" }) + formatMessage({ id: "projects.subject.update.front" }));
        }
        if (scenario.updateRandomAfterPush) {
            list.push(formatMessage({ id: "projects.subject.update" }) + formatMessage({ id: "projects.subject.update.after" }));
        }
        if (scenario.screenPush) {
            list.push(formatMessage({ id: "subject.screen" }));
        }
        if (scenario.dispensingPush) {
            list.push(formatMessage({ id: "projects.attributes.dispensing.yes" }));
        }
        if (scenario.randomPush) {
            list.push(formatMessage({ id: "subject.random" }));
        }

        if (g.lang === "zh") {
            return formatMessage({ id: "project.statistics.push.history.scenario" }) + list.join("、") + "。";
        } else {
            return formatMessage({ id: "project.statistics.push.history.scenario" }) + list.join(",") + ".";
        }

    }

    const allSend = () => {
        CustomConfirmModal({
            title: formatMessage({ id: 'project.statistics.ls.resend' }),
            content: formatMessage({ id: 'project.statistics.operation.succeed' }),
            okText: formatMessage({ id: 'common.ok' }) + " ",
            cancelText: formatMessage({ id: "common.cancel" }) + " ",
            onOk: () => {
                run_pushSend({ type: 3 }).then((result) => {
                    message.success(result.msg).then();
                    getList();
                    setSelectedIds([]);
                }
                )
            }
        });
    };

    const getList = () => {
        runAsync({
            start: (page.currentPage - 1) * page.pageSize,
            limit: page.pageSize,
            startTime: startTime,
            endTime: endTime,
            status: queryPushStatus,
            subjectNo: subjectNo.trim(),
            customerId: customerId,
            projectId: projectId,
            envId: envId,
            cohortId: cohortId,
        }).then((result) => {
            let data = result.data;
            page.setTotal(data.total);
            page.setData(fillTableCellEmptyPlaceholder(data.items));
            setAfootCount(data.afootCount);
            setHandleCount(data.handleCount);
            setSuccessCount(data.successCount);
            setFailCount(data.failCount);
            setLoseCount(data.loseCount);
        });
        getProjectAttributeRun({
            projectId,
            env: envId,
            cohort: props.cohortId ? props.cohortId : null,
            customer: customerId,
        }).then((result) => {
            let da = result.data;
            setAttribute(da);
        });
        getVisitSettingsRun({
            customerId,
            projectId,
            envId,
            cohortId: cohortId,
            roleId: auth.project.permissions.role_id,
        }).then((result) => {
            setVisitSettings(result.data);
            if (result.data.isOpen){
                setOutVisit(g.lang === "zh"?result.data.nameZh:result.data.nameEn);

            }else{
                setOutVisit(formatMessage({ id: "project.statistics.out-visit-dispensing" }));

            }
        });
    };

    useEffect(() => {
        if (visitSettings !== null) {
            if (visitSettings.isOpen) {
                setOutVisit(g.lang === "zh"?visitSettings.nameZh:visitSettings.nameEn);

            }else{
                setOutVisit(formatMessage({ id: "project.statistics.out-visit-dispensing" }));

            }
        }
    }, [g.lang]);

    const onChange = (type, dateString) => {
        let offset_GMT = new Date().getTimezoneOffset();
        let time = moment(dateString).valueOf() / 1000 - offset_GMT * 60 - timeZone * 60 * 60;
        if (type === "start") {
            setStartTime(time);
        }
        if (type === "end") {
            setEndTime(time);
        }
    };
    const {runAsync: getVisitSettingsRun, loading: getVisitSettingsLoading} = useFetch(getVisitSettings, {manual: true});

    const rowSelection = {
        type: "checkbox",
        onChange: (selectedRowKeys, selectedRows) => {
            setSelectedIds(selectedRowKeys);
        },
        selectedRowKeys: selectedIds,
        getCheckboxProps: (record) => ({
            disabled: record.status === 0 || record.status === 1 || record.status === 3 || record.status === 4
        }),
        preserveSelectedRowKeys: true,
    };

    const getSourceTypeListStr = (sourceTypeList) => {
        sourceTypeList = _.uniq(sourceTypeList)
        const tmp = sourceTypeList.map(sourceType => {
            if (sourceType === 5) {
                return outVisit; // 如果是 5，使用 outVisit 的值
            }
            return pushSource.find(it => it.value === sourceType)?.label;
        });

        return (
            <>
                {tmp.map((label, index) => (
                    <span key={index}>
                        {g.lang==="zh"?"历史":"History "}
                        {label}
                        {index !== tmp.length - 1 && (g.lang==="zh"? "、":",")}
                    </span>
                ))}
            </>
        );

    }

    React.useEffect(getList, [page.currentPage, page.pageSize, startTime, endTime, queryPushStatus, doSearch, customerId, projectId, envId, cohortId]);
    return (
        <>
            <Row >
                <Col>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                        <svg className="iconfont" width={16} height={16}>
                            <use xlinkHref="#icon-quanbutuisong" />
                        </svg>
                        <span style={{ color: "#1D2129", fontWeight: 600, paddingLeft: "8px" }}>{formatMessage({ id: 'project.statistics.all', allowComponent: true })}</span>
                    </div>
                </Col>
            </Row>
            <Row justify="space-between" className='mar-top-15'>
                <Col>
                    <Row>
                        <Col>
                            <Form.Item label={<FormattedMessage id={"project.statistics.push.time"} />}>
                                <DatePickers
                                    showTime
                                    placeholder={formatMessage({ id: "common.time.start" })}
                                    style={{ width: "180px" }}
                                    onChange={
                                        (value, dateString) => {
                                            onChange("start", dateString, value)
                                        }
                                    } />
                                <span style={{ padding: "4px" }}>~</span>
                                <DatePickers
                                    showTime
                                    placeholder={formatMessage({ id: "common.time.end" })}
                                    style={{ width: "180px" }}
                                    onChange={
                                        (value, dateString) => {
                                            onChange("end", dateString)
                                        }
                                    } />
                            </Form.Item>
                        </Col>
                        <Col>
                            <Form.Item style={{ marginLeft: "12px" }} label={<FormattedMessage id={"common.status"} />}>
                                <Select defaultValue={queryPushStatus} allowClear
                                    placeholder={intl.formatMessage({ id: "placeholder.select.common" })}
                                    style={{ width: "100px" }} onChange={(value) => {
                                        setQueryPushStatus(value)
                                    }}>
                                    <Select.Option value={0} key="0"><FormattedMessage id="project.statistics.pushing" /></Select.Option>
                                    <Select.Option value={1} key="1"><FormattedMessage
                                        id="project.statistics.succeeded" /></Select.Option>
                                    <Select.Option value={2} key="2"><FormattedMessage
                                        id="project.edc.failed" /></Select.Option>
                                    <Select.Option value={3} key="3"><FormattedMessage
                                        id="project.statistics.lose" /></Select.Option>
                                    <Select.Option value={4} key="4"><FormattedMessage
                                        id="project.edc.processing" /></Select.Option>
                                </Select>
                            </Form.Item>
                        </Col>
                        <Col>
                            <Input.Search
                                style={{ width: "200px", marginLeft: "12px" }}
                                placeholder={formatMessage({ id: "project.statistics.enter.number" }, { label: attribute?.info?.subjectReplaceText })}
                                ref={searchInputRef}
                                value={subjectNo}
                                onChange={e => setSubjectNo(e.target.value)}
                                allowClear
                                onSearch={() => {
                                    searchInputRef?.current?.blur();
                                    page.setCurrentPage(1);
                                    setDoSearch(doSearch + 1);
                                }}
                            />
                        </Col>
                    </Row>
                </Col>

                <Col>
                    <Space>
                        {
                            permissionsCohort(auth.project.permissions, "operation.build.push.history",props.cohort?.status) && projectStatus !== 2 &&
                            <AuthButton onClick={push_history}
                                    loading={checkPushHistoryRunLoading || pushHistoryRunLoading}>
                                <FormattedMessage id="project.statistics.push.history" />
                            </AuthButton>
                        }
                        {
                            permissionsCohort(auth.project.permissions, "operation.build.push.batch.send",props.cohort?.status) && projectStatus !== 2 &&
                            <AuthButton type="primary" onClick={batchSend}><FormattedMessage
                                id="project.statistics.batch.send" /></AuthButton>
                        }
                    </Space>
                </Col>

            </Row>
            <Row>
                <span style={{ color: '#888888' }}><FormattedMessage id="project.statistics.pushing" />:<span style={{ color: "#1D2129"}}>{afootCount}</span></span>&nbsp;&nbsp;&nbsp;
                <span style={{ color: '#888888' }}><FormattedMessage id="project.edc.processing" />:<span style={{ color: "#1D2129"}}>{handleCount}</span></span>&nbsp;&nbsp;&nbsp;
                <span style={{ color: '#888888' }}><FormattedMessage id="project.statistics.succeeded" />:<span style={{ color: "#1D2129"}}>{successCount}</span></span> &nbsp;&nbsp;&nbsp;
                <span style={{ color: '#888888' }}><FormattedMessage id="project.edc.failed" />:<span style={{ color: "#1D2129"}}>{failCount}</span></span>&nbsp;&nbsp;&nbsp;
                <span style={{ color: '#888888' }}><FormattedMessage id="project.statistics.lose" />:<span style={{ color: "#1D2129"}}>{loseCount}</span></span>&nbsp;&nbsp;&nbsp;
            </Row>
            <Table
                loading={loading}
                className="mar-top-10"
                dataSource={page.data}
                scroll={{ x: true }}
                pagination={false}
                rowKey={(record) => (record.id)}
                rowSelection={rowSelection}
            >
                <Table.Column title={<FormattedMessage id="common.serial" />} dataIndex="#" key="#" width={70}
                    render={(text, record, index) => ((page.currentPage - 1) * page.pageSize + index + 1)} />
                <Table.Column
                    title={<FormattedMessage id="project.statistics.subject.number" />}
                    key="subjectNo"
                    dataIndex="subjectNo"
                    width={100}
                    align="left"
                    ellipsis
                    render={
                        (text, record) => {
                            return record.subjectNo;
                        }
                    }
                />
                <Table.Column
                    title={<FormattedMessage id="project.statistics.push.item" />}
                    key="source"
                    dataIndex="source"
                    width={100}
                    align="left"
                    ellipsis
                    render={
                        (text, record) => {
                            if (record.sourceTypeList != null && record.sourceTypeList.length > 0) {
                                return getSourceTypeListStr(record.sourceTypeList);
                            } else {
                                if (record.sourceType === 5) {
                                    return outVisit
                                }
                                return pushSource.find(it => (it.value === record.sourceType))?.label;
                            }
                        }
                    }
                />
                <Table.Column
                    title={<FormattedMessage id="project.statistics.push.mode" />}
                    dataIndex="pushMode"
                    width={150}
                    key="pushMode"
                    ellipsis
                    render={
                        (text, record) => {
                            return pushMode.find(it => (it.value === record.pushMode))?.label;
                        }
                    }
                />
                <Table.Column
                    title={<FormattedMessage id="project.statistics.push.time" />}
                    key="sendTime"
                    dataIndex="sendTime"
                    align="left"
                    width={200}
                    ellipsis
                    render={
                        (value, record, index) => (
                            (value === null || value === 0) ? '' : (moment.unix(value).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss') + (timeZone >= 0 ? "(UTC+" : "(UTC") + formatTimezoneOffset(timeZone) + ")")
                        )
                    } />
                <Table.Column
                    title={<FormattedMessage id={'common.status'} />}
                    dataIndex="status"
                    width={150}
                    key="status"
                    ellipsis
                    render={
                        (text, record) => {
                            return <Badge color={pushStatusColors[record.status]}
                                text={pushStatus.find(it => (it.value === record.status))?.label} />;
                        }
                    }
                />
                <Table.Column title={<FormattedMessage id={'project.statistics.retry.times'} />} key="count" dataIndex="count" align="left"
                    ellipsis width={150} />

                <Table.Column
                    title={<FormattedMessage id="common.operation" />}
                    width={220}
                    fixed="right"
                    render={
                        (value, record, index) => operationButtons(record)
                    }
                />
            </Table>
            <Details bind={pushDetails} refresh={getList} outVisit={outVisit}/>
        </>
    );
};