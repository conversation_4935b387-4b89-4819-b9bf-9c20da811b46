import React from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {Button, Form, FormProps, Input, InputNumber, message, Modal, Radio, Select, Space} from "antd";
import {MinusCircleFilled, PlusOutlined} from '@ant-design/icons';
import {useAuth} from "../../../../context/auth";
import {useSafeState} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {generateRandomNumber, getLastGroup} from "../../../../api/randomization";
import {useGlobal} from "../../../../context/global";
import {nilObjectId} from "../../../../data/data";
import styled from "@emotion/styled";
import {userSites} from "../../../../api/subject";

export const RandomGenerate = (props:any) => {

    const g = useGlobal()
    const auth = useAuth();
    const intl = useIntl();

    const {formatMessage} = intl;

    const [visible, setVisible] = useSafeState<any>(false);
    const [blockBl, setBlockBl] = useSafeState<any>(true);
    const [randomType, setRandomType] = useSafeState<any>(0);
    const [lastGroups, setLastGroups] = useSafeState<any>([]);
    const [sites, setSites] = useSafeState([]);
    const [paramSites, setParamSites] = useSafeState<any>([])
    const [siteMode, setSiteMode] = useSafeState<any>({});
    const [siteOpen, setSiteOpen] = useSafeState(false);
    const [paramSitesOp, setParamSitesOp] = useSafeState<any>(1);
    const [search,setSearch] = useSafeState(0);

    const [form] = Form.useForm();
    const [randomNumberRule,setRandomNumberRule] = useSafeState<any>(0                )
    const customerId :any= auth.customerId;
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const lastId = props.cohort ? props.cohort.lastId : null;
    const projectType = auth.project.info.type;
    const attribute = props.attribute;

    const {runAsync: getLastGroupRun} = useFetch(getLastGroup, {manual: true})
    const {runAsync: generateRandomNumberRun, loading:generateRandomNumberLoading} = useFetch(generateRandomNumber, {manual: true})
    const { runAsync: userSitesRun, loading: userSitesLoading } = useFetch(
        userSites,
        { manual: true }
    );

    const show = (group:any, factor:any, randomType:any) => {
        setVisible(true);
        // 组别配置
        let groups :any = [];
        if(group != null && group.length > 0){
            group.filter((field:any) => field.status !== 2).forEach((value:any)=>{
                if (value.subGroup != null && value.subGroup.length > 0){
                    for (let i = 0; i < value.subGroup.length; i++) {
                        groups.push({
                            name: value.name + " " + value.subGroup[i].name,
                            parName:value.name,
                            subName:value.subGroup[i].name,
                            ratio: 1,
                        })
                    }
                }else {
                    groups.push({
                        name: value.name,
                        parName:value.name,
                        ratio: 1,
                    })
                }
            });
        }

        // 区组配置
        let blocks = [];
        for (let i = 2; i <= 4; i++){
            blocks.push({blockLength: groups.length * i});
        }

        // 最小化随机分层因素权重比展示
        if(randomType === 2){
            let factors :any = [];
            if(factor != null && factor.length > 0){
                factor.filter((value:any)=>value.status !== 2).forEach((value:any)=>{
                    factors.push({
                        label: value.label,
                        name: value.name,
                        ratio: 1,
                    });
                });
            }
            form.setFieldsValue({groups: groups, factors: factors, blocks: blocks});
        }else{
            form.setFieldsValue({groups: groups, blocks: blocks});
        }

        // 接受类型
        setRandomType(randomType);

        // 如果是在随机项目获取上一阶段组别信息
        if((projectType === 3 || (projectType === 2 && props.cohort?.type === 1)) && lastId != null && lastId !== nilObjectId){
            getLastGroupRun({projectId, envId, lastId}).then(
                (result:any) => {
                    let data :any = result.data
                    const options :any = [];
                    if (data != null) {
                        data.forEach((it:any) => {
                            options.push({
                                label: it.name,
                                value: it.name
                            });
                        });
                    }
                    setLastGroups(options);
                }
            )
        };

        // 查询中心
        userSitesRun({
            projectId: projectId,
            customerId: customerId,
            envId: envId,
            roleId: auth.project.permissions.role_id,
        }).then((result: any) => {
            setSites(result.data);
        });
    };

    const hide = () => {
        setRandomNumberRule(0)
        setVisible(false);
        setParamSitesOp(1);
        setParamSites([]);
        setSiteMode({});
        form.resetFields();
    };

    const onChangeRatio = (value:any) =>{
        if(randomType === 1){           // 区组随机才执行此函数
            let blocks = [];
            let gus = form.getFieldsValue().groups;
            let blk = form.getFieldsValue().blocks;
            let ratioSum = 0;
            if(gus != null && gus.length > 0){
                gus.forEach((value:any)=>{
                    ratioSum += value.ratio
                });
            }

            if(blk != null && blk.length > 0){
                for (let i = 0; i < blk.length; i++){
                    blocks.push({blockLength: ratioSum * (i+1)})
                }
            }
            form.setFieldsValue({blocks:blocks});
        }
    };

    const onChangeBlockLength = (value:any) =>{
        setBlockBl(true);
        let gus = form.getFieldsValue().groups;
        let ratioSum = 0;
        if(gus != null && gus.length > 0){
            gus.forEach((value:any)=>{
                ratioSum += value.ratio
            });
        }
        if(value % ratioSum !== 0){
            setBlockBl(false);
            message.error(formatMessage({ id: 'projects.randomization.generateVerificationTips1' }));
        }
    };

    const save = () => {
        if(blockBl){
            form.validateFields()
                .then(values => {
                    values["customerId"] = auth.customerId;
                    values["projectId"] = auth.project.id;
                    values["envId"] = auth.env.id;
                    values["siteIds"] = paramSites;
                    if (props.cohort != null) {
                        values["cohortId"] = props.cohort.id;
                    }
                    generateRandomNumberRun(values).then(
                        () => {
                            props.refresh();
                            hide();
                        }
                    )
                }).catch(() => {})
        }else{
            message.error(formatMessage({ id: 'projects.randomization.generateVerificationTips2' }));
        }
    }

    React.useImperativeHandle(props.bind, () => ({ show }));

    const formItemLayout = {
        labelCol: { style: {   width: g.lang === "en"? "195px": "140px" } }
    }

    const validateEndValue = (rule:any, value:any) => {
        const initialValue = form.getFieldValue('initialValue');
        let count = 0
        if (randomType === 1){
            form.getFieldValue('blocks').forEach((block:any) => {
                count += block.blockLength * block.blockNumber
            })
        }else if (randomType === 2){
            count = form.getFieldValue('total')
        }
        if (value && value < initialValue + count -1) {
            return Promise.reject(formatMessage({id:"projects.randomization.endValueTips"}));
        }
        return Promise.resolve();
    };

    return (
        <React.Fragment>
            <Modal
                title={<FormattedMessage id="projects.randomization.generate" />}
                open={visible}
                onCancel={hide}
                
                maskClosable={false}
                centered
                destroyOnClose
                className='custom-small-modal'
                okButtonProps={{ loading: generateRandomNumberLoading }}
                onOk={save}
                okText={formatMessage({id: 'common.ok'})}
            >
                <CustomForm lang={g.lang} form={form} layout="horizontal" {...formItemLayout}>
                    {
                        (projectType === 3 || projectType === 2 && props.cohort?.type === 1) && lastId != null && lastId !== nilObjectId?
                            <Form.Item label={formatMessage({ id: 'projects.randomization.last.group' })} name="lastGroup" rules={[{ required: true }]} >
                                <Select placeholder={formatMessage({id: 'placeholder.select.common'})} className="full-width"  options={lastGroups}>
                                </Select>
                            </Form.Item>
                        : null
                    }
                    <Form.Item label={formatMessage({ id: 'common.name' })} name="name" rules={[{ required: true }]} >
                        <Input placeholder={formatMessage({id: 'placeholder.input.common'})} allowClear />
                    </Form.Item>
                    <Form.Item label={<span style={{color:'#1D2129'}}><FormattedMessage id="common.site"/></span>} rules={[{ required: true }]}>
                        <Select
                            value={paramSitesOp}
                            {...siteMode}
                            open={siteOpen}
                            onBlur={() => {
                                setSiteOpen(false);
                                if (paramSitesOp !== null){
                                    setSearch(search + 1)};
                            }
                            }
                            onDropdownVisibleChange={(visible: boolean) => setSiteOpen(visible)}
                            onChange={(value: any) => {
                                if (value === 1 || (Array.isArray(value) && (value.find((i: any) => i === 1)|| value.length === 0))) {
                                    if (siteMode.mode != null){
                                        setSiteMode({});
                                        setSiteOpen(true);
                                    }
                                    setParamSites([]);
                                    setParamSitesOp(1);
                                    setSearch(search + 1);
                                    setSiteOpen(false);
                                } else {
                                    setSiteOpen(true);
                                    if (siteMode.mode !==  "multiple"){
                                        setSiteMode({mode: "multiple"});
                                        setSearch(search + 1);
                                        setSiteOpen(true);
                                    }
                                    if (!Array.isArray(value)) {
                                        let siteIds = [value];
                                        setParamSites(siteIds);
                                        setParamSitesOp(siteIds);
                                    }else{
                                        setParamSites(value);
                                        setParamSitesOp(value);
                                    }
                                }
                            }}
                        >
                            <Select.Option value={1}>{formatMessage({id: "supply.plan.all.site"})}</Select.Option>
                            {
                                sites?.map((item: any) => {
                                    return <Select.Option value={item.id}>{item.number+"-"+item.name}</Select.Option>
                                })
                            }
                        </Select>
                    </Form.Item>
                    <Form.Item label={formatMessage({id: 'projects.randomization.defaultNumber'})} name="initialValue" rules={[{ required: true }]} >
                        <InputNumber placeholder={formatMessage({id: 'placeholder.input.common'})} min={1} className="full-width" />
                    </Form.Item>

                    <Form.Item className='custom-eng-groups' style={{marginBottom:"0px"}} label={formatMessage({id: 'projects.randomization.generateGroupWeightRatio'})} name="groups" rules={[{required: true}]} >
                        <Form.List name="groups">
                            {(fields) => (
                                <>
                                    {
                                        fields.map((field:any) => (
                                            <Space key={field.key} style={{ display: 'flex' }} align="baseline" size={0}>
                                                <Form.Item
                                                    {...field}
                                                    name={[field.name, 'name']}
                                                    fieldKey={[field.fieldKey, 'name']}
                                                    rules={[{ required: true, message: formatMessage({ id: 'projects.randomization.groupList' }) }]}
                                                    initialValue = {field.group}
                                                    style={{ width: "150px"}}
                                                >
                                                    <Input disabled placeholder={formatMessage({id: 'projects.randomization.groupList'})} className="full-width"/>
                                                </Form.Item>
                                                <Form.Item
                                                    {...field}
                                                    name={[field.name, 'ratio']}
                                                    fieldKey={[field.fieldKey, 'ratio']}
                                                    rules={[{ required: true, message: formatMessage({ id: 'projects.randomization.generateWeightRatio' }) }]}
                                                    initialValue={field.ratio}
                                                    style={{width: g.lang === "en"? "207px": "262px"}}
                                                >
                                                    <InputNumber min="1" placeholder={formatMessage({id: 'projects.randomization.generateWeightRatio'})} onChange={ onChangeRatio } className="full-width"/>
                                                </Form.Item>
                                            </Space>
                                        ))
                                    }
                                </>
                            )}
                        </Form.List>
                    </Form.Item>

                    {
                        randomType === 2?
                            <CustomFormItem className='custom-eng-stratification'  style={{marginBottom:"0px"}} label= {formatMessage({id: 'projects.randomization.generateLayeredWeightRatio'})} name="factors" rules={[{required: true}]}  >
                                <Form.List name="factors">
                                    {(fields) => (
                                        <>
                                            {
                                                fields.map((field:any) => (
                                                    <Space key={field.key} style={{ display: 'flex' }} align="baseline" size={0}>
                                                        <Form.Item
                                                            {...field}
                                                            name={[field.name, 'name']}
                                                        />
                                                        <Form.Item
                                                            {...field}
                                                            name={[field.name, 'label']}
                                                            rules={[{ required: true, message: formatMessage({ id: 'randomization.config.factors' }) }]}
                                                            initialValue = {field.group}
                                                            style={{ width: "150px"}}
                                                        >
                                                            <Input disabled placeholder={formatMessage({id: 'randomization.config.factors'})} className="full-width"/>
                                                        </Form.Item>
                                                        <Form.Item
                                                            {...field}
                                                            name={[field.name, 'ratio']}
                                                            rules={[{ required: true, message: formatMessage({ id: 'projects.randomization.generateWeightRatio' }) }]}
                                                            initialValue={field.ratio}
                                                            style={{width: g.lang === "en"? "207px": "262px"}}
                                                        >
                                                            <InputNumber min="1" placeholder={formatMessage({id: 'projects.randomization.generateWeightRatio'})} className="full-width"/>
                                                        </Form.Item>
                                                    </Space>
                                                ))
                                            }
                                        </>
                                    )}
                                </Form.List>
                                {
                                    (attribute.info.instituteLayered || attribute.info.countryLayered|| attribute.info.regionLayered)?
                                        <>
                                        <Space  style={{ display: 'flex' }} align="baseline" size={0}>
                                            <Form.Item
                                                rules={[{ required: true, message: formatMessage({ id: 'randomization.config.factors' }) }]}
                                                style={{ width: "150px"}}
                                            >
                                                {
                                                    attribute.info.instituteLayered?
                                                        <Input disabled placeholder={formatMessage({id: 'common.site'})} className="full-width"/>
                                                        :null
                                                }
                                                {
                                                    attribute.info.countryLayered?
                                                        <Input disabled placeholder={formatMessage({id: 'projects.attributes.country'})} className="full-width"/>
                                                        :null
                                                }
                                                {
                                                    attribute.info.regionLayered?
                                                        <Input disabled placeholder={formatMessage({id: 'projects.attributes.regionLayered'})} className="full-width"/>
                                                        :null
                                                }
                                            </Form.Item>
                                            <Form.Item name={'ratio'}
                                                       rules={[{ required: true, message: formatMessage({ id: 'projects.randomization.generateWeightRatio' }) }]}
                                                       style={{width: g.lang === "en"? "207px": "262px"}}
                                                       initialValue={1}
                                            >
                                                <InputNumber min="1" placeholder={formatMessage({id: 'projects.randomization.generateWeightRatio'})} className="full-width"/>
                                            </Form.Item>
                                        </Space>
                                        </>
                                        :null
                                }
                            </CustomFormItem>
                        : null
                    }

                    {
                        randomType === 1?
                            <Form.Item label={formatMessage({id: 'projects.randomization.blockConfig'})} name="blocks" rules={[{required: true}]}  >
                                <Form.List name="blocks">
                                    {(fields, { add, remove }) => (
                                        <>
                                            {fields.map((field:any) => (
                                                <Space key={field.key} style={{ display: 'flex' }} align="baseline" >
                                                    <Form.Item
                                                        {...field}
                                                        name={[field.name, 'blockLength']}
                                                        fieldKey={[field.fieldKey, 'blockLength']}
                                                        rules={[{ required: true, message: formatMessage({ id: 'projects.randomization.blockLength' }) }]}
                                                        
                                                    >
                                                        <InputNumber min="1" placeholder={formatMessage({id: 'projects.randomization.blockLength'})} onChange={ onChangeBlockLength } className="full-width"/>
                                                    </Form.Item>
                                                    <Form.Item
                                                        {...field}
                                                        name={[field.name, 'blockNumber']}
                                                        fieldKey={[field.fieldKey, 'blockNumber']}
                                                        rules={[{ required: true, message: formatMessage({ id: 'projects.randomization.generateBlockNumber' }) }]}
                                                    >
                                                        <InputNumber min="1" placeholder={formatMessage({id: 'projects.randomization.generateBlockNumber'})} className="full-width"/>
                                                    </Form.Item>
                                                    <MinusCircleFilled style={{color: "#F96964", marginLeft: "24px"}}   onClick={() => remove(field.name)}/>
                                                </Space>
                                            ))}
                                            <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}><FormattedMessage id={'common.addTo'} /></Button>
                                        </>
                                    )}
                                </Form.List>
                            </Form.Item>
                        : null
                    }

                    {/*{*/}
                        {/*randomType === 1 && factorCount > 0?*/}
                            {/*<Input prefix={tips} suffix="总数:***" disabled />*/}
                        {/*: null*/}
                    {/*}*/}

                    {
                        randomType === 2?
                            <Form.Item label={formatMessage({id: 'projects.randomization.probability'})} name="probability" rules={[{ required: true }]} >
                                <InputNumber placeholder={formatMessage({id: 'placeholder.input.common'})} min="0" max="0.99" className="full-width"/>
                            </Form.Item>
                        : null
                    }

                    {
                        randomType === 2?
                            <Form.Item label={formatMessage({id: 'projects.randomization.total'})} name="total" rules={[{ required: true }]} >
                                <InputNumber placeholder={formatMessage({id: 'placeholder.input.common'})} min="1" className="full-width"/>
                            </Form.Item>
                        : null
                    }

                    <Form.Item label={formatMessage({id: 'projects.randomization.numberLength'})} name="numberLength" rules={[{ required: true }]} >
                        <InputNumber min="1" placeholder={formatMessage({id: 'placeholder.input.common'})} className="full-width"/>
                    </Form.Item>
                    {
                        randomType === 1?
                            <Form.Item label={formatMessage({id: 'projects.randomization.blockRule'})} initialValue={1} name="blocksRule" rules={[{ required: true }]} >
                                <Radio.Group>
                                    <Radio value={0}>{formatMessage({id: "projects.randomization.blockRule.order"})}</Radio>
                                    <Radio value={1}>{formatMessage({id: "projects.randomization.blockRule.reverse"})}</Radio>
                                </Radio.Group>
                            </Form.Item>
                            :null
                    }
                    <Form.Item label={formatMessage({id: 'projects.randomization.randomNumberRule'})} initialValue={0} name="randomNumberRule" rules={[{ required: true }]} >
                        <Radio.Group onChange={(e) => { setRandomNumberRule(e.target.value) }}>
                            <Radio value={0}>{formatMessage({id: "projects.randomization.blockRule.order"})}</Radio>
                            <Radio value={1}>{formatMessage({id: "projects.randomization.blockRule.reverse"})}</Radio>
                        </Radio.Group>
                    </Form.Item>
                    {
                        randomNumberRule === 1 ?
                            <Form.Item label={formatMessage({id: 'projects.randomization.endNumber'})} name="endValue" rules={[{ required: true ,validator: validateEndValue}]} >
                                <InputNumber placeholder={formatMessage({id: 'placeholder.input.common'})} className="full-width"/>
                            </Form.Item>
                            :null
                    }
                    <Form.Item label={formatMessage({id: 'projects.randomization.seed'})} name="seed" rules={[{ required: true }]} >
                        <InputNumber min="1" placeholder={formatMessage({id: 'placeholder.input.common'})} className="full-width"/>
                    </Form.Item>
                    <Form.Item label={formatMessage({id: 'projects.randomization.numberPrefix'})} name="prefix" >
                        <Input placeholder={formatMessage({id: 'placeholder.input.common'})} allowClear/>
                    </Form.Item>
                </CustomForm>
            </Modal>
        </React.Fragment>
    )
};

interface CustomFormProps extends FormProps {
    lang: string;
}

class TmpForm<T extends FormProps> extends React.Component<T, {}> { }

class TempForm extends TmpForm<CustomFormProps> {
    render() {
        return <Form {...this.props} />
    }
}

// https://github.com/ant-design/ant-design/issues/5285
const CustomForm = styled(TempForm)`
    .ant-form-item-label {
        white-space: normal;
    }
    .ant-form-item-label label {
        text-align: right;
    }

    ${({ lang }) => lang === 'en' && `
        .custom-eng-groups .ant-form-item-required::before {
            margin-left: 12px;
        }

        .custom-eng-stratification .ant-form-item-required::before {
            margin-left: 12px;
        }
    `}
`

const CustomFormItem = styled(Form.Item)`
    .ant-form-item-label label {
        height: auto !important;
      padding-top: 5px !important;
    }
`