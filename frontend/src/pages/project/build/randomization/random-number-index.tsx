import React from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {Drawer, Tabs} from "antd";
import {permissions} from '../../../../tools/permission'
import {useSafeState} from "ahooks";
import {RandomNumberAllocation} from "./random-number-allocation";
import {RandomNumberConfigure} from "./random-number-configure";
import {RandomizationFactor} from "./randomization-factor";
import {useAuth} from "../../../../context/auth";
import styled from "@emotion/styled";


export const RandomNumberIndex = (props: any) => {
    const {TabPane} = Tabs;
    const auth = useAuth();

    const intl = useIntl();
    const {formatMessage} = intl;

    const [visible, setVisible] = useSafeState<any>(false);
    const [record, setRecord] = useSafeState<any>(false);
    const [recordData, setRecordData] = useSafeState<any>(null);
    const [title, setTitle] = useSafeState<any>("");


    const show = (random: any) => {
        setRecord(random._id)
        setRecordData(random)
        setTitle(formatMessage({id: 'projects.randomization.list'}) + (random ? ` - ${random.name}` : ''));
        setVisible(true);
    };

    const hide = () => {
        setVisible(false);
    };


    React.useImperativeHandle(props.bind, () => ({show}));


    return (
        <React.Fragment>
            {
                <Drawer
                    // className="drawer-width-percent"
                    title={title}
                    width={"100%"}
                    open={visible}
                    destroyOnClose={true}
                    onClose={hide}
                    footer={null}
                    
                    maskClosable={false}
                    bodyStyle={{padding: '16px 24px'}}
                >
                    <NoTopTabs destroyInactiveTabPane={true} defaultActiveKey="1">
                        {
                            permissions(auth.project.permissions, "operation.build.randomization.list.segmentation.view") ?
                                <TabPane tab={<FormattedMessage id="projects.randomization.randomNumberTab"/>}
                                         key="1">
                                    <RandomNumberAllocation record={record} cohort={props.cohort} attribute={props.attribute}/>
                                </TabPane> : null
                        }
                        {
                            permissions(auth.project.permissions, "operation.build.randomization.list.attribute") ?
                                <TabPane tab={<FormattedMessage id="projects.randomization.randomizationSubTab"/>}
                                         key="2">
                                    <RandomNumberConfigure record={record} recordData={recordData} cohort={props.cohort} attribute={props.attribute}/>
                                </TabPane> : null
                        }
                        {
                            permissions(auth.project.permissions, "operation.build.randomization.factor-in.view") ?
                                <TabPane tab={<FormattedMessage id="randomization.config.factor"/>} key="4">
                                    <RandomizationFactor record={record} cohort={props.cohort}/>
                                </TabPane>
                                :
                                null
                        }
                    </NoTopTabs>
                </Drawer>
            }
        </React.Fragment>
    )
};

const NoTopTabs = styled(Tabs)`
    .ant-tabs-tab {
        padding-top: 0;
    }

    .ant-tabs-nav {
        margin-bottom: 16px !important;
    }
`
