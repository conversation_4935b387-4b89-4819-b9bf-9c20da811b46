import React, {useState} from "react";
import {
    <PERSON><PERSON>,
    <PERSON>ton,
    Col,
    Form,
    Input,
    InputNumber,
    Modal,
    Radio,
    Row,
    Select,
    Space,
    Switch,
    Table,
    Tooltip
} from "antd";
import {FormattedMessage, useIntl} from "react-intl";
import {CloseOutlined, MinusCircleFilled, PlusOutlined, QuestionCircleFilled} from "@ant-design/icons";
import {useFetch} from "../../../../hooks/request";
import {addRandomization, updateRandomization} from "../../../../api/randomization";
import {useSafeState} from "ahooks";
import {useAuth} from "../../../../context/auth";
import {useGlobal} from "../../../../context/global";
import _ from "lodash";
import Draggable from "react-draggable";
import {FormulaData} from "../../../../data/data";
import styled from "@emotion/styled";


export const RandomListFactor = (props:any) => {
    const [isModalFactorVisible, setIsModalFactorVisible] = useSafeState<any>(false);
    const [factorDetail, setFactorDetail] = useSafeState<any>(null)
    const [factorForm] = Form.useForm();
    const [isEdit, setIsEdit] = useSafeState(false);
    const [isCopyData, setIsCopyData] = useSafeState<any>(false);
    const [send, setSend] = useSafeState(true);
    const [oldData, setOldData] = useSafeState(null);

    const auth = useAuth();
    const intl = useIntl();
    const [title, setTitle] = useSafeState("");
    const {formatMessage} = intl;
    const g = useGlobal()

    const [factorCalc, setFactorCalc] = useSafeState<any>(false);

    const formItemLayout = {
        labelCol: { style: {   width: g.lang === "en"?"190px":"106px"} },
    };
    const [footer, setFooter] = useSafeState<any>({});
    const [fields, setFields] = useSafeState([]);

    const {runAsync: addRandomizationRun, loading:addRandomizationLoading} = useFetch(addRandomization, {manual: true})
    const {runAsync: updateRandomizationRun, loading:updateRandomizationLoading} = useFetch(updateRandomization, {manual: true})



    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const cohortId = props.cohort ? props.cohort.id : null;
    const customerId = auth.customerId;
    const connectEdc = auth.project ? auth.project.info.connect_edc : null;
    const pushMode = auth.project ? auth.project.info.push_mode : null;
    const [iconColor, setIconColor] = useSafeState("#999999");
    const [open, setOpen] = useState(false);
    const [precision, setPrecision] = useState(0);
    // 展示对话框
    const handleFactorCancel = () => {
        setOldData(null);
        setSend(true);
        setIsEdit(false);
        setIsCopyData(false);
        factorForm.setFieldsValue({})
        setIsModalFactorVisible(false);
    };
    const validateFieldsForm = () => {
        factorForm.validateFields().then(values => {
            if (values.options != null) {
                let options = values.options
                let optionNumber = 1;
                if (factorDetail != null && options.length > 0) {
                    let options = factorDetail.options
                    let str = options[options.length - 1].value;
                    optionNumber = parseInt(str) + 1;
                }
                values.options.forEach((option:any) => {
                    if (option.value == null) {
                        let optionValue = optionNumber;
                        option.value = optionValue + ""
                        optionNumber++
                    }
                });
            }
            values.isCopyData = isCopyData
            const data = {
                "projectId": projectId,
                "envId": envId,
                "cohortId": cohortId,
                "customerId": customerId,
                "factor": { ...values },
            }
            if (factorDetail != null) {
                data["factor"] = { "name": factorDetail.name,...values, "id":factorDetail.id }
                if (values.isCalc === true && values.calcType === 0) {
                    data["factor"]["type"] = "datePicker"
                }else if (values.isCalc === true && values.calcType === 1){
                    data["factor"]["type"] = "input"
                }
                updateRandomizationRun({...data}).then(
                    () => {
                        handleFactorCancel();
                        props.refresh()
                    }
                )
            } else {
                if (values.isCalc === true && values.calcType === 0) {
                    data["factor"]["type"] = "datePicker"
                }else if (values.isCalc === true && values.calcType === 1){
                    data["factor"]["type"] = "input"
                }
                addRandomizationRun({...data}).then(
                    () => {
                        handleFactorCancel()
                        props.refresh()
                    }
                )
            }
            }
        )
    }
    const show = (fields:any, factorDetail:any) => {
        setOpen(false);
        factorForm.resetFields()
        setFactorDetail(null)
        setFields(fields)
        setFooter({})
        setFactorCalc(false);
        setIsEdit(false);
        setIsCopyData(false);
        setTitle(formatMessage({ id: 'randomization.config.factorAdd' }))
        setSend(true);
        setPrecision(0)
        factorForm.setFieldsValue({calcType: 0 });
        if (factorDetail !== undefined) {
            setOldData(factorDetail);
            factorForm.setFieldsValue({ ...factorDetail })
            setFactorDetail(factorDetail)
            setFactorCalc(factorDetail.isCalc)
            setIsEdit(true);
            setSend(true);
            setIsCopyData(factorDetail?.isCopyData);
            setTitle(formatMessage({ id: 'randomization.config.factorEdit' }))
            factorForm.setFieldsValue({calcType: factorDetail.calcType });
        }
        setIsModalFactorVisible(true)

    }


    const formChange = () => {
        if(isEdit){
            const a = oldData;;
            // console.log("1===" + JSON.stringify(a)); 
            let b = factorForm.getFieldsValue();
            // console.log("2===" + JSON.stringify(b)); 
            if (!compareObjects(a, b)) {
                setSend(false);
            } else {
                setSend(true);
            }
        }
    };

    //比较两个JavaScript对象是否相同
    function compareObjects(obj1: any, obj2: any) {
        for (let key in obj1) { 
            if (obj2.hasOwnProperty(key) && obj2[key] !== undefined) {
                if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
                if (!arraysAreEqual(obj1[key], obj2[key])) {
                    return false;
                }
                } else if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
                    if (!compareObjects(obj1[key], obj2[key])) {
                        return false;
                    }
                } else {
                    if (obj1[key] !== obj2[key]) {
                        return false;
                    }
                }
            }
        }
        return true;
    }
    const changeType = (type:any) => {
        if (type !== "select" && type !== "checkbox" && type !== "radio") {
            factorForm.setFieldsValue({ "options": [] });
        }
    };
    //比较两个数组是否相同
    function arraysAreEqual(arr1: any, arr2: any) {
        // 检查数组长度是否相同
        if (arr1.length !== arr2.length) {
            return false;
        }

        const a = _.cloneDeep(arr1);
        const b = _.cloneDeep(arr2);
        // 将数组转换为字符串并比较
        const str1 = JSON.stringify(a.sort());
        const str2 = JSON.stringify(b.sort());

        return str1 === str2;
    }


    const handleMouseEnter = () => {
        setIconColor("#165DFF");
    };

    const handleMouseLeave = () => {
        setIconColor("#999999");
    };
    const radioStyle = {
        display: 'block',
        height: '30px',
        lineHeight: '30px',
    };
    React.useImperativeHandle(props.bind, () => ({ show }));
    return (
        <>
            {
                <Modal  className="custom-small-modal" confirmLoading={updateRandomizationLoading || addRandomizationLoading} {...footer} destroyOnClose={true}
                    title={title} open={isModalFactorVisible} onOk={validateFieldsForm} okText={formatMessage({ id: 'common.ok' })} onCancel={handleFactorCancel} centered
                    okButtonProps={{ disabled: isEdit?send:false }}
                >
                    {fields.length!==0 || factorDetail != undefined ?<Alert style={{ marginBottom: 16 }} showIcon message={formatMessage({id: 'random.factor.alert'})} type="warning" />:null}
                    <Form form={factorForm} onValuesChange={formChange} {...formItemLayout} >
                        <Form.Item initialValue={factorCalc} label={formatMessage({ id: 'randomization.config.factor.calc' })} name="isCalc"  valuePropName="checked">
                            <Switch
                                onChange={(v) => {
                                    setFactorCalc(v);
                                }}
                                size="small"
                                disabled={factorDetail != null || (isEdit?isCopyData:false) }
                            />
                        </Form.Item>
                        <Form.Item label={formatMessage({id: 'randomization.config.number'})} name="number"
                                   rules={[{required: true}]}>
                            <Input disabled={factorDetail != null || (isEdit?isCopyData:false) }
                                   placeholder={formatMessage({id: 'placeholder.input.common'})} allowClear
                                   className="full-width"/>
                        </Form.Item>
                        {
                            factorCalc ?
                                <>
                                    <Form.Item label={formatMessage({id: 'randomization.config.factor.calc.formula'})}
                                               tooltip={{
                                                   title: (
                                                       <>
                                                           <Row>
                                                               {formatMessage({
                                                                   id: "randomization.config.factor.calc.formula.tip",
                                                               })}
                                                           </Row>
                                                           <Row>
                                                               {formatMessage({
                                                                   id: "randomization.config.factor.calc.formula.tip.age",
                                                               })}
                                                           </Row>
                                                           <Row>
                                                               {formatMessage({
                                                                   id: "randomization.config.factor.calc.formula.tip.bmi",
                                                               })}
                                                           </Row>
                                                       </>
                                                   ),
                                                   icon: (
                                                       <QuestionCircleFilled
                                                           style={{
                                                               color: "#D0D0D0",
                                                               cursor: "pointer",
                                                           }}
                                                       />
                                                   ),
                                                   overlayStyle: {
                                                       whiteSpace: "pre-wrap",
                                                       maxWidth: "600px",
                                                       width: "auto",
                                                   },
                                               }}
                                               name="calcType" >
                                        <Select disabled={factorDetail != null || (isEdit?isCopyData:false) }>
                                            <Select.Option
                                                value={0}>{formatMessage({id: 'drug.configure.formula.age'})}</Select.Option>
                                            <Select.Option
                                                value={1}>{formatMessage({id: 'randomization.config.factor.calc.type.bmi'})}</Select.Option>
                                        </Select>
                                    </Form.Item>
                                    <Form.Item
                                        style={{marginBottom: 0}}
                                        label={formatMessage({
                                            id: "drug.batch.treatmentDesign.treatment.design.custom.formula",
                                        })}
                                    >
                                        <Form.Item
                                            name="customFormulas"
                                            rules={[
                                                {
                                                    required: true,
                                                    message: formatMessage({
                                                        id: "common.required.prefix",
                                                    }),
                                                },
                                            ]}
                                        >
                                            <Input
                                                disabled={factorDetail != null || (isEdit?isCopyData:false) }
                                                placeholder={formatMessage({
                                                    id: "common.required.prefix",
                                                })}
                                                // value={inputValue}
                                                // onChange={handleChange}
                                                className={
                                                    iconColor === "#999999" && !open ? "" : "custom-input"
                                                }
                                                addonAfter={
                                                    <div
                                                        onMouseEnter={handleMouseEnter}
                                                        onMouseLeave={handleMouseLeave}
                                                        style={{
                                                            width: "100%",
                                                            height: "100%",
                                                            display: "flex",
                                                            alignItems: "center",
                                                            justifyContent: "center",
                                                            cursor: "pointer",
                                                        }}
                                                    >
                                                        <Tooltip
                                                            title={formatMessage({
                                                                id: "drug.batch.treatmentDesign.treatment.design.custom.formula.completion.instructions",
                                                            })}
                                                        >
                                                            <i
                                                                style={{
                                                                    cursor: "pointer",
                                                                }}
                                                                onMouseEnter={handleMouseEnter}
                                                                onMouseLeave={handleMouseLeave}
                                                                onClick={() => {
                                                                    setIconColor("#165DFF");
                                                                    setOpen(true);
                                                                }}
                                                            >
                                                                <svg
                                                                    className="iconfont"
                                                                    width={16}
                                                                    height={16}
                                                                    style={{marginBottom: "-3px"}}
                                                                    fill={"#165DFF"}
                                                                >
                                                                    <use
                                                                        xlinkHref="#icon-zidingyigongshitianxieshuoming"></use>
                                                                </svg>
                                                            </i>
                                                        </Tooltip>
                                                    </div>
                                                }
                                            />
                                            {/* </Tooltip> */}
                                        </Form.Item>
                                    </Form.Item>
                                    <Row>
                                        <Col>
                                    <Form.Item
                                        name={"precision"}
                                        label={formatMessage({
                                            id: "drug.batch.treatmentDesign.treatment.design.keep.decimal.places",
                                        })}
                                        rules={[{required: true}]}
                                    >
                                        <InputNumber
                                            onChange={(e) => {
                                                setPrecision(e)
                                            }}
                                            disabled={factorDetail != null || (isEdit?isCopyData:false) }
                                            placeholder={formatMessage({
                                                id: "common.required.prefix",
                                            })}
                                            min={0}
                                            style={{
                                                width: 100,
                                            }}
                                        ></InputNumber>
                                    </Form.Item>
                                        </Col>
                                        {
                                            precision === 0 ?
                                                <Col>
                                                    <Form.Item
                                                        style={{marginLeft:20}}
                                                        name={"round"}
                                                        rules={[{required: true}]}
                                                        initialValue={1}
                                                    >
                                                        <Radio.Group  disabled={factorDetail != null || (isEdit?isCopyData:false) }>
                                                            <Radio  value={1}><FormattedMessage
                                                                id="randomization.config.factor.calc.formula.round.up"/></Radio>
                                                            <Radio  value={2}><FormattedMessage
                                                                id="randomization.config.factor.calc.formula.round.down"/></Radio>
                                                        </Radio.Group>
                                                    </Form.Item>
                                                </Col>
                                                :null
                                        }
                                    </Row>
                                    <Form.Item label={formatMessage({id: 'randomization.config.factor.label'})}
                                               name="label"
                                               rules={[{required: true}]}>
                                        <Input disabled={factorDetail != null || (isEdit?isCopyData:false) }
                                               placeholder={formatMessage({id: 'placeholder.input.common'})}
                                               allowClear
                                               className="full-width"/>
                                    </Form.Item>
                                    <Form.Item name="option" label={formatMessage({id: 'randomization.config.factor.label.option.mapping'})}
                                               style={{marginBottom: 0}}>
                                        <Form.List
                                            name="options"
                                            rules={[
                                                {
                                                    validator: async (_, names) => {
                                                        if (!names || names.length < 1) {
                                                            return Promise.reject(new Error(formatMessage({id: 'projects.randomization.lockFactor'})));
                                                        }
                                                    },
                                                },
                                            ]}
                                        >
                                            {(opt, {add, remove}, {errors}) => (
                                                <>
                                                    {opt.map((field, index) => (
                                                        <Space key={field.name} align="baseline">
                                                            <Form.Item
                                                                {...field}
                                                                name={[field.name, 'formula']}
                                                                key={field.name}
                                                                validateTrigger={['onChange', 'onBlur']}
                                                                rules={[{
                                                                    required: true,
                                                                    message: formatMessage({id: 'randomization.config.factor.label.option.value.range'})
                                                                }]}
                                                            >
                                                                {
                                                                    factorDetail && factorDetail?.options[index]?.disable === true ?
                                                                        (factorDetail?.options[index]?.formula?
                                                                            <div style={{
                                                                                width: g.lang === "en"? 163: 209,
                                                                                marginLeft: 12,
                                                                                marginRight: -12,
                                                                            }}>{factorDetail?.options[index]?.formula}</div> :null)
                                                                        :
                                                                        <Input
                                                                            style={{
                                                                                width: g.lang === "en"? 163: 209
                                                                            }}
                                                                            placeholder={formatMessage({id: 'randomization.config.factor.label.option.value.range'})}/>
                                                                }
                                                            </Form.Item>
                                                            <span>-</span>
                                                            <Form.Item
                                                                {...field}
                                                                name={[field.name, 'label']}
                                                                key={field.name}
                                                                validateTrigger={['onChange', 'onBlur']}
                                                                rules={[{
                                                                    required: true,
                                                                    message: formatMessage({id: 'randomization.config.factor.label.option.value'})
                                                                }]}
                                                            >
                                                                {
                                                                    factorDetail && factorDetail?.options[index]?.disable === true ?
                                                                        (factorDetail?.options[index]?.label?
                                                                            <div style={{
                                                                            width: g.lang === "en"? 163: 209,
                                                                            marginLeft: 12,
                                                                            marginRight: -12,
                                                                        }}>{factorDetail?.options[index]?.label}</div>:null)
                                                                        :
                                                                        <Input
                                                                            style={{
                                                                                width: g.lang === "en"? 163: 209
                                                                            }}
                                                                            placeholder={formatMessage({id: 'randomization.config.factor.label.option.value'})}/>
                                                                }
                                                            </Form.Item>
                                                            {
                                                                factorDetail && factorDetail?.options[index]?.disable === true ? null :
                                                                    <MinusCircleFilled
                                                                        style={{color: "#F96964", marginLeft: "24px"}}
                                                                        onClick={() => {
                                                                            remove(field.name);
                                                                        }}/>
                                                            }
                                                        </Space>
                                                    ))}
                                                    <Form.Item style={{marginBottom: 0}}>
                                                        {
                                                            opt.length > 0 ?
                                                                <Button type="dashed" onClick={() => add()}
                                                                        style={{width: g.lang === "en" ?"322px":"430px"}} block
                                                                        icon={<PlusOutlined/>}
                                                                        className="mar-ver-5"><FormattedMessage
                                                                    id="common.addTo"/></Button>
                                                                : <Button type="dashed" onClick={() => add()} block
                                                                          icon={<PlusOutlined/>}
                                                                          className="mar-ver-5"><FormattedMessage
                                                                    id="common.addTo"/></Button>
                                                        }
                                                        <Form.ErrorList errors={errors}/>
                                                    </Form.Item>
                                                </>
                                            )}
                                        </Form.List>
                                    </Form.Item>
                                </>
                                :
                                <>
                                    <Form.Item label={formatMessage({id: 'form.field.label'})} name="label"
                                               rules={[{required: true}]}>
                                        <Input disabled={factorDetail != null || (isEdit?isCopyData:false) }
                                               placeholder={formatMessage({id: 'placeholder.input.common'})} allowClear
                                               className="full-width"/>
                                    </Form.Item>
                                    {connectEdc === 1 && pushMode === 1 ?
                                        <Form.Item label={formatMessage({id: 'form.field.name'})} name="name"
                                                   rules={[{required: true}]}>
                                            <Input placeholder={formatMessage({id: 'placeholder.input.common'})}
                                                   allowClear className="full-width"/>
                                        </Form.Item>
                                        : null
                                    }
                                    <Form.Item label={formatMessage({id: 'form.control.type'})} name="type"
                                               rules={[{required: true}]}>
                                        <Radio.Group onChange={e => changeType(e.target.value)}>
                                            <Radio disabled={isEdit?isCopyData:false} value={"select"} style={{marginRight: "50px"}}><FormattedMessage
                                                id="form.control.type.select"/></Radio>
                                            <Radio disabled={isEdit?isCopyData:false} value={"radio"}><FormattedMessage
                                                id="form.control.type.radio"/></Radio>
                                        </Radio.Group>
                                    </Form.Item>
                                    <Form.Item name="option" label={formatMessage({id: 'form.control.type.options'})}
                                               style={{marginBottom: 0}}>
                                        <Form.List
                                            name="options"
                                            rules={[
                                                {
                                                    validator: async (_, names) => {
                                                        if (!names || names.length < 1) {
                                                            return Promise.reject(new Error(formatMessage({id: 'projects.randomization.lockFactor'})));
                                                        }
                                                    },
                                                },
                                            ]}
                                        >
                                            {(opt, {add, remove}, {errors}) => (
                                                <>
                                                    {opt.map((field, index) => (
                                                        <Space key={field.name} align="baseline">
                                                            <Form.Item
                                                                {...field}
                                                                name={[field.name, 'label']}
                                                                validateTrigger={['onChange', 'onBlur']}
                                                                rules={[{
                                                                    required: true,
                                                                    message: formatMessage({id: 'form.control.type.label'})
                                                                }]}
                                                            >
                                                                {
                                                                    factorDetail && factorDetail?.options[index]?.disable === true ?
                                                                        <div style={{
                                                                            marginLeft: 12,
                                                                            width: 439,
                                                                        }}>{factorDetail?.options[index]?.label}</div>
                                                                        :
                                                                        <Input
                                                                            placeholder={formatMessage({id: 'placeholder.input.common'})}
                                                                            style={{width: 439}}/>
                                                                }
                                                            </Form.Item>
                                                            {
                                                                factorDetail && factorDetail?.options[index]?.disable === true ? null :
                                                                    <MinusCircleFilled
                                                                        style={{color: "#F96964",  marginLeft:24}}
                                                                        onClick={() => remove(field.name)}/>
                                                            }
                                                        </Space>
                                                    ))}
                                                    <Form.Item style={{marginBottom: 0}}>
                                                        {
                                                            opt.length > 0 ?
                                                                <Button type="dashed" onClick={() => add()}
                                                                        style={{width: "calc(100% - 40px)"}} block
                                                                        icon={<PlusOutlined/>}
                                                                        className="mar-ver-5"><FormattedMessage
                                                                    id="common.addTo"/></Button>
                                                                : <Button type="dashed" onClick={() => add()} block
                                                                          icon={<PlusOutlined/>}
                                                                          className="mar-ver-5"><FormattedMessage
                                                                    id="common.addTo"/></Button>
                                                        }
                                                        <Form.ErrorList errors={errors}/>
                                                    </Form.Item>
                                                </>
                                            )}
                                        </Form.List>
                                    </Form.Item>
                                </>
                        }
                        <Form.Item label={formatMessage({id: 'common.status'})} name="status" className="mar-ver-5"
                                   initialValue={1}>
                            <Radio.Group>
                                <Radio disabled={isEdit?isCopyData:false} value={1}>{formatMessage({id: "common.effective"})}</Radio>
                                <Radio disabled={isEdit?isCopyData:false} value={2}>{formatMessage({id: "common.invalid"})}</Radio>
                            </Radio.Group>
                        </Form.Item>
                    </Form>
                    {open && (
                        <Draggable>
                            <DragWrap
                                style={{
                                    top: 50,
                                    left: 620,
                                }}
                            >
                                <RowHeader justify="space-between" align="middle">
                                    <Col style={{ fontWeight: 500, lineHeight: "24px" }}>
                                        {formatMessage({
                                            id: "drug.batch.treatmentDesign.treatment.design.custom.formula.completion.instructions",
                                        })}
                                    </Col>
                                    <CloseOutlined
                                        style={{ cursor: "pointer" }}
                                        onClick={() => setOpen(false)}
                                    />
                                </RowHeader>
                                <div style={{ padding: "12px 12px 16px 12px" }}>
                                    <Row
                                        style={{
                                            color: "#677283",
                                            fontSize: "10px",
                                        }}
                                    >
                                        {formatMessage({
                                            id: "randomization.config.factor.calc.formula.title1",
                                        })}
                                    </Row>
                                    <Row
                                        style={{
                                            color: "#677283",
                                            fontSize: "10px",
                                            marginBottom: 8,
                                        }}
                                    >
                                        {formatMessage({
                                            id: "randomization.config.factor.calc.formula.title2",
                                        })}
                                    </Row>
                                    <MyTable dataSource={FormulaData} pagination={false}>
                                        <Table.Column
                                            title={formatMessage({
                                                id: "drug.batch.treatmentDesign.treatment.design.custom.formula.symbol",
                                            })}
                                            dataIndex={"symbol"}
                                            key="symbol"
                                        />
                                        <Table.Column
                                            title={formatMessage({
                                                id: "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation",
                                            })}
                                            dataIndex={"illustrate"}
                                            key="illustrate"
                                        />
                                        <Table.Column
                                            title={formatMessage({
                                                id: "drug.batch.treatmentDesign.treatment.design.custom.formula.eg",
                                            })}
                                            dataIndex={"eg"}
                                            key="eg"
                                        />
                                    </MyTable>
                                    <Row
                                        style={{
                                            color: "#677283",
                                            fontSize: "10px",
                                            marginTop: "10px",
                                        }}
                                    >
                                        {formatMessage({
                                            id: "randomization.config.factor.calc.formula.footer1",
                                        })}
                                    </Row>
                                    <Row
                                        style={{
                                            color: "#677283",
                                            fontSize: "10px",
                                        }}
                                    >
                                        {formatMessage({
                                            id: "randomization.config.factor.calc.formula.footer2",
                                        })}
                                    </Row>
                                </div>
                            </DragWrap>
                        </Draggable>
                    )}
                </Modal>
            }
        </>
    );
};

const DragWrap = styled.div`
  position: absolute;
  z-index: 1000;
  width: 350px;
  cursor: move;
  background: #fff;
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.2);
  border-radius: 2px;
`;

const RowHeader = styled(Row)`
  border-bottom: 1px solid #e3e4e6;
  padding: 8px 12px;
`;

const MyTable = styled(Table)`
  .ant-table-thead {
    height: 24px;
    line-height: 24px;

    th {
      line-height: 24px !important;
      padding: 0 0 0 10px !important;
      font-size: 10px !important;
    }
  }

  tbody.ant-table-tbody > tr > td {
    line-height: 24px !important;
    font-size: 10px;
    color: #677283;
    padding: 0 0 0 10px !important;
  }
`;



