import React, {use<PERSON>allback, useEffect} from "react";
import {
    Badge,
    Button,
    Form,
    Input,
    List,
    message,
    Popover,
    Radio,
    Result,
    Row,
    Spin,
    Switch,
    Table,
    Tooltip,
    Typography,
    notification,
} from "antd";
import {CloseOutlined, PlusOutlined, CloseCircleFilled} from "@ant-design/icons";
import moment from "moment";
import {permissions, permissionsCohort} from "../../../../tools/permission";
import {useAuth} from "../../../../context/auth";
import useSafeState from "ahooks/lib/useSafeState";
import {useProjectRandomization} from "./context";
import {useFetch} from "../../../../hooks/request";
import {
    addRandomization,
    addRegion,
    delRandomizationDesignFactor,
    delRandomizationDesignGroup,
    delRegion,
    downloadRandomList,
    getProjectAttribute,
    getRandomization,
    getRandomList,
    getRegion,
    randomListInactivate,
    randomListSync,
    updateRandomListInvalid,
    updateRandomListStatus,
    updateSiteLayered,
} from "../../../../api/randomization";
import {HistoryList} from "../../../common/history-list";
import {RandomUpload} from "./random-upload";
import {RandomGenerate} from "./random-generate";
import {RandomListGroup} from "./random-list-group";
import {RandomListFactor} from "./random-list-factor";
import {RandomNumberIndex} from "./random-number-index";
import {CustomConfirmModal} from "components/modal";
import {Title} from "components/title";
import styled from "@emotion/styled";
import {TableCellPopover} from "components/popover";
import {InsertDivider} from "components/divider";
import {nilObjectId} from "../../../../data/data";
import {exportReport} from "../../../../api/report";
import MoreIcon from "../../../../images/more.svg";
import {RandomListUpdate} from "./random-list-update";
import {useGlobal} from "../../../../context/global";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {AuthButton, AuthPopover, AuthRadio, AuthSwitch} from "../../../common/auth-wrap";
import {attributeRefreshAtom} from "./ctx";
import {useAtom} from "jotai";

export const RandomList = (props: any) => {
    const ctx = useProjectRandomization();
    const auth = useAuth();
    const intl = useTranslation();

    const { formatMessage } = intl;
    const [form] = Form.useForm();
    const { Text, Paragraph } = Typography;

    const history_ref: any = React.useRef();
    const group_ref = React.useRef();
    const factor_ref = React.useRef();
    const random_list = React.useRef<any>();
    const random_upload = React.useRef();
    const random_list_update = React.useRef();
    const random_generate = React.useRef();

    const [oldRandomType, setOldRandomType] = useSafeState<any>(0);
    const [randomType, setRandomType] = useSafeState<any>(0);
    const [random, setRandom] = useSafeState<any>(true);
    const [group, setGroup] = useSafeState<any>([]);
    const [factor, setFactor] = useSafeState<any>([]);
    const [exportList, setExportList] = useSafeState<any>(false);
    const [attributeId, setAttributeId] = useSafeState<any>("");
    const [attribute,setAttribute] = useSafeState<any>(null);
    const [layers, setLayers] = useSafeState<any>(0);
    const [randomList, setRandomList] = useSafeState<any>([]);
    const [regions, setRegions] = useSafeState<any>([]);
    const [regionRefresh, setRegionRefresh] = useSafeState<any>(0);
    const [attributeRefresh, setAttributeRefresh] = useAtom(attributeRefreshAtom)
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const lockConfig = auth.env.lockConfig ? auth.env.lockConfig : false;
    const projectStatus = auth.project.status ? auth.project.status : 0;
    const cohortId = props.cohort ? props.cohort.id : null;
    const customerId = auth.customerId;
    const lastId = props.cohort ? props.cohort.last_id : null;
    const projectType = auth.project.info.type;
    const timeZone =
        (auth.project.info.timeZoneStr !== undefined && auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "")
            ? Number(auth.project.info.timeZoneStr)
            : 8;

    const showGroupModal = () => {
        // @ts-ignore
        group_ref.current.show();
    };
    const showFactorModal = () => {
        // @ts-ignore
        factor_ref.current.show(factor);
    };
    const showFactorModalId = (factorDetail: any) => {
        // @ts-ignore
        factor_ref.current.show(factor, factorDetail);
    };
    const showGroupModalId = (groupDetail: any) => {
        // @ts-ignore
        group_ref.current.show(groupDetail);
    };
    const upload = () => {
        // @ts-ignore
        random_upload.current.show();
    };

    const randomListUpdate = (item: any) => {
        // @ts-ignore
        random_list_update.current.show(item);
    };

    const generate = () => {
        // @ts-ignore
        random_generate.current.show(group, factor, randomType);
    };
    const { runAsync: randomListSyncRun, loading: randomListSyncLoading } =
        useFetch(randomListSync, { manual: true });
    const sync = () => {
        CustomConfirmModal({
            title: formatMessage({ id: "tip.sync.title" }),
            content: formatMessage({ id: "tip.sync.content" }),
            cancelText:formatMessage({ id: "common.cancel" }),
            okText: formatMessage({ id: "common.ok" }),
            okButtonProps: { loading: randomListSyncLoading },
            onOk: () =>
                randomListSyncRun({
                    envId: envId,
                    cohortId: cohortId,
                }).then((result: any) => {
                    message
                        .success(formatMessage({ id: "message.save.success" }))
                        .then(() => {});
                    list();
                }),
        });
    };

    const { runAsync: getRandomizationRun, loading: getRandomizationLoading } =
        useFetch(getRandomization, { manual: true });
    const { runAsync: addRandomizationRun } = useFetch(
        addRandomization,         { 
            manual: true,
            onError: (err: any) => {
                form.resetFields();
                form.setFieldValue("type", oldRandomType);
                err.json().then((data: any) =>
                    notification.open({
                        message: (
                            <div
                                style={{
                                    fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                    fontSize: "14px",
                                }}
                            >
                                <CloseCircleFilled
                                    style={{
                                        color: "#F96964",
                                        paddingRight: "8px",
                                    }}
                                />
                                {data.msg}
                                
                            </div>
                        ),
                        // description: (
                        //     <div
                        //         style={{
                        //             paddingLeft: "20px",
                        //             color: "#646566",
                        //         }}
                        //     >
                        //         {data.msg}
                        //     </div>
                        // ),
                        duration: 5,
                        placement: "top",
                        style: {
                            width: "720px",
                            // height: "88px",
                            background: "#FEF0EF",
                            borderRadius: "4px",
                        },
                    })
                );
            },
          }
    );
    const { runAsync: delRandomizationDesignGroupRun } = useFetch(
        delRandomizationDesignGroup,
        { manual: true }
    );
    const { runAsync: updateSiteLayeredRun } = useFetch(updateSiteLayered, {
        manual: true,
    });
    const { runAsync: delRandomizationDesignFactorRun } = useFetch(
        delRandomizationDesignFactor,
        { manual: true }
    );
    const { runAsync: updateRandomListStatusRun } = useFetch(
        updateRandomListStatus,
        { manual: true }
    );
    const {
        runAsync: getProjectAttributeRun,
        loading: getProjectAttributeLoading,
    } = useFetch(getProjectAttribute, { manual: true });
    const { runAsync: getRandomListRun, loading: getRandomListLoading } =
        useFetch(getRandomList, { manual: true });
    const { runAsync: updateRandomListInvalidRun } = useFetch(
        updateRandomListInvalid,
        { manual: true }
    );
    const {
        runAsync: downloadRandomListRun,
        loading: downloadRandomListLoading,
    } = useFetch(downloadRandomList, { manual: true });
    const { runAsync: getRegionRun, loading: getRegionLoading } = useFetch(
        getRegion,
        { manual: true }
    );

    const regionList = () => {
        getRegionRun({
            envId: envId,
            cohortId: cohortId,
        }).then((result: any) => setRegions(result.data));
    };
    useEffect(
        () => regionList,
        [attribute,envId, cohortId, projectId, regionRefresh]
    );
    const updateType = (e: any) => {
        setRandomType(e);
        addRandomizationRun({
            projectId: projectId,
            envId: envId,
            cohortId: cohortId,
            customerId: customerId,
            type: e,
        }).then((result: any) => {
                message.success(result.msg).then(() => {});
            })
            .catch();
    };

    const delete_group = (id: any) => {
        CustomConfirmModal({
            title: formatMessage({ id: "common.confirm.delete" }),
            okText: formatMessage({ id: "common.ok" }),
            cancelText: formatMessage({ id: "common.cancel" }),
            onOk: () => {
                const deleting = message.loading("Deleting...", 0);
                delRandomizationDesignGroupRun({
                    projectId: projectId,
                    envId: envId,
                    cohortId: cohortId,
                    customerId: customerId,
                    groupId: id,
                }).then(() => {
                    deleting();
                    list();
                });
            },
        });
    };

    // 中心分层设置
    const centerLayeredMarker = (e: any) => {
        updateSiteLayeredRun({ id: attributeId, value: e, type: 1 }).then(
            (resp: any) => {
                getRandomArray();
                message.success(resp.msg).then();
            }
        );
    };

    const delete_factor = (name: any) => {
        CustomConfirmModal({
            title: formatMessage({ id: "common.confirm.delete" }),
            okText: formatMessage({ id: "common.ok" }),
            cancelText: formatMessage({ id: "common.cancel" }),
            onOk: () => {
                const deleting = message.loading("Deleting...", 0);
                delRandomizationDesignFactorRun({
                    projectId: projectId,
                    envId: envId,
                    cohortId: cohortId,
                    customerId: customerId,
                    factorName: name,
                })
                    .then(() => {
                        deleting();
                        list();
                    })
                    .catch(() => {
                        deleting();
                        list();
                    });
            },
        });
    };

    const update_random_list_status = (id: any, value: any) => {
        let status = 1;
        if (value) {
            status = 2;
        }
        updateRandomListStatusRun({
            id: id,
            projectId: projectId,
            envId: envId,
            cohortId: cohortId,
            customerId: customerId,
            status: status,
        }).then(() => {
            getRandomArray();
        });
    };
    

    const getRandomArray = () => {
        getProjectAttributeRun({
            projectId: projectId,
            env: envId,
            cohort: cohortId,
            customer: customerId,
        }).then((result: any) => {
            setAttributeId(result.data.id);
            setAttribute(result.data);
            getRandomListData();
            result = result.data.info;
            if (result.instituteLayered) {
                setLayers(2);
            } else if (result.countryLayered) {
                setLayers(1);
            } else if (result.regionLayered) {
                setLayers(3);
            }
            setRandom(result.random);
        });
    }

    useEffect(() => {
        getRandomArray()
    }, [envId, cohortId, customerId, projectId,attributeRefresh]);

    const list = React.useCallback(() => {
        setExportList(
            permissions(
                auth.project.permissions,
                "operation.build.randomization.list.export"
            )
        );
        //获取实验组信息
        getRandomizationRun({
            projectId: projectId,
            env: envId,
            cohort: cohortId,
            customer: customerId,
            roleId: auth.project.permissions.role_id,
        }).then((result: any) => {
            form.setFieldsValue({ ...result.data });
            setRandomType(result.data.type);
            setOldRandomType(result.data.type);
            setFactor(result.data.factors === null ? [] : result.data.factors);
            setGroup(result.data.groups === null ? [] : result.data.groups);
        });
    }, [form, envId, cohortId, customerId, projectId]);

    const getRandomListData =() => {
        getRandomListRun({
            projectId: projectId,
            env: envId,
            cohort: cohortId,
            customer: customerId,
            roleId: auth.project.permissions.role_id,
        }).then((result: any) => {
            setRandomList(result.data);
        });
    }

    const invalid = (record: any) => {
        let id = record._id;
        let status = record.status;
        if (record.use === 0) {
            CustomConfirmModal({
                title: formatMessage({
                    id: "randomization.random.list.invalid.doubt.title",
                }),
                content: formatMessage({
                    id: "randomization.random.list.invalid.doubt",
                }),
                okText: formatMessage({ id: "common.ok" }),
                cancelText: formatMessage({ id: "common.cancel" }),
                onOk: () => {
                    updateRandomListInvalidRun({
                        id: id,
                        projectId: projectId,
                        envId: envId,
                        cohortId: cohortId,
                        customerId: customerId,
                        status: status,
                    }).then((data: any) => {
                        message.success(data.msg);
                        getRandomArray();
                    });
                },
            });
        } else {
            CustomConfirmModal({
                title: formatMessage({
                    id: "randomization.random.list.invalid.doubt.title",
                }),
                content: (
                    <Paragraph>
                        {" "}
                        {formatMessage({
                            id: "randomization.random.list.invalid.doubt.info",
                        })}
                        <Text type="danger">
                            {formatMessage({
                                id: "randomization.random.list.invalid.doubt.first",
                            })}
                        </Text>{" "}
                        {formatMessage({
                            id: "randomization.random.list.invalid.doubt.second",
                        })}
                    </Paragraph>
                ),
                okText: formatMessage({ id: "common.ok" }),
                cancelText: formatMessage({ id: "common.cancel" }),
                onOk: () => {
                    updateRandomListInvalidRun({
                        id: id,
                        projectId: projectId,
                        envId: envId,
                        cohortId: cohortId,
                        customerId: customerId,
                        status: status,
                    }).then((data: any) => {
                        message.success(data.msg);
                        getRandomArray();
                    });
                },
            });
        }
    };

    const showHistory = (id: any) => {
        history_ref.current.show("history.randomization", id, timeZone, null, cohortId, null);
    };

    const downloadRandomArray = (id: any) => {
        downloadRandomListRun({ id: id })
            .then()
            .catch(() => {
                message
                    .error(formatMessage({ id: "common.download.fail" }))
                    .then(() => {});
            });
    };
    const { runAsync: exportReportRun, loading: exportReportLoading } =
        useFetch(exportReport, { manual: true });
    const downloadRandomArrayNew = (id: any) => {
        const params = {
            type: 12,
            projectId: auth.project.id,
            envId: envId,
            roleId: auth.project.permissions.role_id,
            cohortIds: cohortId ? [cohortId] : [],
            randomList: [id],
        };
        exportReportRun(params)
            .then(() => {
                message.success(
                    formatMessage({ id: "report.download.success" })
                );
            })
            .catch(() => {
                message
                    .error(formatMessage({ id: "common.download.fail" }))
                    .then(() => {});
            });
    };

    const layersClick = (e: any, click: any) => {
        // 双击
        if (click) {
            // 选择 选中
            if (e === layers) {
                // 取消选择
                setLayers(0);
                centerLayeredMarker(0);
            }
        } else {
            if (e !== layers) {
                // 重新选择
                setLayers(e);
                centerLayeredMarker(e);
            }
        }
    };
    const showUseCOunt = (item: any) => {
        random_list?.current?.show(item);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
    React.useEffect(list, [list]);

    if (!random) {
        return (
            <Result
                status="warning"
                title={formatMessage({
                    id: "projects.randomization.settingRandom",
                })}
            />
        );
    }
    //表单配置
    return (
        <React.Fragment>
            <Spin
                spinning={
                    getRandomizationLoading ||
                    getRandomListLoading ||
                    getProjectAttributeLoading
                }
                size="large"
            >
                <div style={{ padding: 8 }}>
                    {/* 随机类型 */}
                    {permissions(
                        auth.project.permissions,
                        "operation.build.randomization.type.view"
                    ) && (
                        <ModuleContainer>
                            <div style={{ marginBottom: 12 }}>
                                <Title
                                    name={formatMessage({
                                        id: "randomization.config.type", allowComponent: true
                                    })}
                                ></Title>
                            </div>
                            <Form form={form}>
                                <Form.Item
                                    name="type"
                                    rules={[{ required: true }]}
                                    className="mar-ver-5"
                                    initialValue={randomType}
                                >
                                    <Radio.Group
                                        onChange={(e) =>
                                            updateType(e.target.value)
                                        }
                                        disabled={
                                            !(
                                                permissionsCohort(
                                                    auth.project.permissions,
                                                    "operation.build.randomization.type.edit",props.cohort?.status
                                                ) && !lockConfig
                                            ) || projectStatus === 2
                                        }
                                    >
                                        <AuthRadio value={1}>
                                            <FormattedMessage id="projects.randomization.blockRandom" />
                                        </AuthRadio>
                                        <AuthRadio value={2}>
                                            <FormattedMessage id="projects.randomization.minimize" />
                                        </AuthRadio>
                                    </Radio.Group>
                                </Form.Item>
                            </Form>
                        </ModuleContainer>
                    )}
                    {randomType !== 0 && (
                        <React.Fragment>
                            {permissions(
                                auth.project.permissions,
                                "operation.build.randomization.group.view"
                            ) && (
                                    <GroupProfile
                                        randomList={randomList}
                                        cohort={props.cohort}
                                        dataSource={group}
                                        rowEdit={showGroupModalId}
                                        rowAdd={showGroupModal}
                                        rowDelete={delete_group}
                                        rowEditable={
                                            permissionsCohort(
                                                auth.project.permissions,
                                                "operation.build.randomization.group.edit",
                                                props.cohort?.status
                                            ) &&
                                            !lockConfig &&
                                            projectStatus !== 2
                                        }
                                        rowDeleteable={
                                            permissionsCohort(
                                                auth.project.permissions,
                                                "operation.build.randomization.group.delete",
                                                props.cohort?.status
                                            ) &&
                                            !lockConfig &&
                                            projectStatus !== 2
                                        }
                                        rowAddable={
                                            permissionsCohort(
                                                auth.project.permissions,
                                                "operation.build.randomization.group.add",
                                                props.cohort?.status
                                            ) &&
                                            !lockConfig &&
                                            projectStatus !== 2
                                        }
                                        inactivatingAble={
                                            permissionsCohort(
                                                auth.project.permissions,
                                                "operation.build.randomization.group.inactivating",
                                                props.cohort?.status
                                            ) &&
                                            !lockConfig &&
                                            projectStatus !== 2 &&
                                            randomType !== 2
                                        }
                                    />
                            )}
                            {permissions(
                                auth.project.permissions,
                                "operation.build.randomization.factor.view"
                            ) && (
                                <FactorProfile
                                    regionRefresh={regionRefresh}
                                    setRegionRefresh={setRegionRefresh}
                                    regions={regions}
                                    getRegionLoading={getRegionLoading}
                                    cohort={props.cohort}
                                    // addRegionLoading={addRegionLoading}
                                    // delRegionLoading={delRegionLoading}
                                    syncable={
                                        permissionsCohort(
                                            auth.project.permissions,
                                            "operation.build.randomization.list.sync",
                                            props.cohort?.status
                                        ) &&
                                        !lockConfig &&
                                        projectStatus !== 2 &&
                                        randomType !== 2
                                    }
                                    sync={sync}
                                    layers={layers}
                                    layersClick={layersClick}
                                    layersClickable={
                                        permissionsCohort(
                                            auth.project.permissions,
                                            "operation.build.randomization.factor.set-toplimit",
                                            props.cohort?.status
                                        ) &&
                                        !lockConfig &&
                                        projectStatus !== 2
                                    }
                                    dataSource={factor}
                                    rowEditable={
                                        permissionsCohort(
                                            auth.project.permissions,
                                            "operation.build.randomization.factor.edit",
                                            props.cohort?.status
                                        ) &&
                                        !lockConfig &&
                                        projectStatus !== 2
                                    }
                                    rowEdit={showFactorModalId}
                                    rowDeleteable={
                                        permissionsCohort(
                                            auth.project.permissions,
                                            "operation.build.randomization.factor.delete",
                                            props.cohort?.status
                                        ) &&
                                        !lockConfig &&
                                        projectStatus !== 2
                                    }
                                    rowDelete={delete_factor}
                                    rowAddable={
                                        permissionsCohort(
                                            auth.project.permissions,
                                            "operation.build.randomization.factor.add",
                                            props.cohort?.status
                                        ) &&
                                        !lockConfig &&
                                        projectStatus !== 2
                                    }
                                    rowAdd={showFactorModal}
                                />
                            )}

                            {group.length !== 0 &&
                                permissions(
                                    auth.project.permissions,
                                    "operation.build.randomization.list.view-summary"
                                ) && (
                                    <RandomListProfile
                                        timeZone={timeZone}
                                        uploadable={
                                            randomType === 1 &&
                                            permissionsCohort(
                                                auth.project.permissions,
                                                "operation.build.randomization.list.upload",
                                                props.cohort?.status
                                            ) &&
                                            // !lockConfig &&
                                            projectStatus !== 2
                                        }
                                        upload={upload}
                                        randomListUpdate={randomListUpdate}
                                        editPermissions={
                                            permissionsCohort(
                                                auth.project.permissions,
                                                "operation.build.randomization.list.edit",
                                                props.cohort?.status
                                            ) &&
                                            // !lockConfig &&
                                            projectStatus !== 2
                                        }
                                        generatable={
                                            permissionsCohort(
                                                auth.project.permissions,
                                                "operation.build.randomization.list.generate",
                                                props.cohort?.status
                                            ) &&
                                            projectStatus !== 2
                                        }
                                        generate={generate}
                                        dataSource={randomList}
                                        displayLastGroupColumn={
                                            projectType === 3 &&
                                            lastId != null &&
                                            lastId !== nilObjectId
                                        }
                                        showUseCount={showUseCOunt}
                                        statusChangeable={
                                            (permissionsCohort(
                                                auth.project.permissions,
                                                "operation.build.randomization.list.active",
                                                    props.cohort?.status
                                            )) ||
                                            projectStatus === 2
                                        }
                                        statusChage={update_random_list_status}
                                        invalidable={
                                            permissionsCohort(
                                                auth.project.permissions,
                                                "operation.build.randomization.list.invalid",
                                                props.cohort?.status
                                            ) &&
                                            projectStatus !== 2
                                        }
                                        invalid={invalid}
                                        historyShowable={permissions(
                                            auth.project.permissions,
                                            "operation.build.randomization.list.history"
                                        )}
                                        showHistory={showHistory}
                                        downloadable={exportList}
                                        downloadRandomArray={
                                            downloadRandomArray
                                        }
                                        downloadRandomArrayLoading={
                                            downloadRandomListLoading
                                        }
                                        downloadRandomArrayNew={
                                            downloadRandomArrayNew
                                        }
                                        downloadRandomArrayNewLoading={
                                            exportReportLoading
                                        }
                                    />
                                )}
                        </React.Fragment>
                    )}
                    <HistoryList
                        bind={history_ref}
                        permission={permissions(
                            auth.project.permissions,
                            "operation.build.randomization.list.print"
                        )}
                    />
                    <RandomUpload
                        bind={random_upload}
                        refresh={getRandomArray}
                        cohort={props.cohort}
                    />
                    <RandomListUpdate
                        bind={random_list_update}
                        refresh={getRandomArray}
                    />
                    <RandomGenerate
                        bind={random_generate}
                        refresh={getRandomArray}
                        cohort={props.cohort}
                        attribute={attribute}
                    />
                    <RandomListGroup bind={group_ref} refresh={list} cohort={props.cohort}/>
                    <RandomListFactor bind={factor_ref} refresh={list} cohort={props.cohort}/>
                    <RandomNumberIndex bind={random_list} refresh={list} cohort={props.cohort}  attribute={attribute}/>
                </div>
            </Spin>
        </React.Fragment>
    );
};

// 组别治疗
interface GroupProfileProp {
    randomList:any;
    cohort:any;
    dataSource: any[];
    inactivatingAble: boolean;
    rowEditable: boolean;
    rowEdit: (record: any) => void;
    rowDeleteable: boolean;
    rowDelete: (record: any) => void;
    rowAddable: boolean;
    rowAdd: () => any;
}
const GroupProfile = (props: GroupProfileProp) => {
    const intl = useTranslation();
    const auth = useAuth()
    const cohortId = props.cohort ? props.cohort.id : null
    const { formatMessage } = intl;
    const { runAsync: randomListInactivateRun, loading: randomListInactivateLoading } = useFetch(
        randomListInactivate,
        { manual: true }
    );

    const operationBtns = useCallback(
        (record: any) => {
            const results: any[] = [];
            if (props.rowEditable) {
                results.push(
                    <AuthButton
                        style={{ padding: "0px" }}
                        size="small"
                        type="link"
                        onClick={() => {
                            props.rowEdit(record);
                        }}
                    >
                        <FormattedMessage id="common.edit" />
                    </AuthButton>
                );
            }
            if (props.rowDeleteable && !record.used) {
                results.push(
                    <AuthButton
                        style={{ padding: "0px" }}
                        size="small"
                        type="link"
                        onClick={() => {
                            props.rowDelete(record.id);
                        }}
                    >
                        <FormattedMessage id="common.delete" />
                    </AuthButton>
                );
            }
            return InsertDivider(results);
        },
        [props.rowDelete, props.rowDeleteable, props.rowEdit, props.rowEditable]
    );

    return (
        <>
                <ModuleContainer>
                    <div style={{marginBottom: 12}}>
                        <Title
                            name={formatMessage({
                                id: "projects.randomization.group", allowComponent: true

                            })}
                            extra={
                                <React.Fragment>
                                    {
                                        props.inactivatingAble && props.dataSource.find((item:any) => item.status === 2) ? (
                                            <AuthButton
                                                style={{marginRight: 12}}
                                                onClick={(e: any) => {
                                                    CustomConfirmModal({
                                                        title: formatMessage({id: 'tip.sync.title'}),
                                                        // content: formatMessage({id: 'common.confirm.is_delete'}),
                                                        okText: formatMessage({id: 'common.ok'}),
                                                        cancelText: formatMessage({id: 'common.cancel'}),
                                                        onOk: () => {
                                                            randomListInactivateRun({envId: auth.env.id,cohortId:cohortId}).then(
                                                                () => {
                                                                    message.success(formatMessage({id: 'random.list.inactivating.success' }))
                                                                }
                                                            )
                                                        }
                                                    })
                                                }}
                                            >
                                                <FormattedMessage id="common.sync"/>
                                            </AuthButton>
                                        ) : null
                                    }
                                </React.Fragment>
                            }
                        ></Title>
                    </div>
                    <Table
                        dataSource={props.dataSource}
                        pagination={false}
                        rowKey={(record) => record.id}
                    >
                        <Table.Column
                            title={formatMessage({id: "randomization.config.code", allowComponent: true})}
                            dataIndex="code"
                            ellipsis
                            //TODO render 超长文本处理
                        />
                        <Table.Column
                            title={formatMessage({id: "common.name", allowComponent: true})}
                            dataIndex="name"
                            ellipsis
                            //TODO render 超长文本处理
                        />
                        {props.dataSource &&
                        props.dataSource.findIndex(
                            (it: any) => it.subGroup && it.subGroup.length > 0
                        ) !== -1 ? (
                            <Table.Column
                                title={intl.formatMessage({
                                    id: "randomization.config.subGroup", allowComponent: true
                                })}
                                dataIndex="subGroup"
                                ellipsis
                                render={(value) => {
                                    if (!value) return <></>;
                                    const items: string[] = [];
                                    (value as []).forEach((it: any) => {
                                        items.push(it.name);
                                    });
                                    return (
                                        <TableCellPopover
                                            title={
                                                <span
                                                    style={{
                                                        display: "flex",
                                                        alignItems: "center",
                                                    }}
                                                >
                                            <svg
                                                className="iconfont"
                                                width={16}
                                                height={16}
                                            >
                                                <use xlinkHref="#icon-quanbuxuanxing"></use>
                                            </svg>
                                            <span style={{marginLeft: 8}}>
                                                {intl.formatMessage({
                                                    id: "randomization.config.subGroup.duplicate.all",  allowComponent: true
                                                })}
                                            </span>
                                        </span>
                                            }
                                            items={items}
                                        />
                                    );
                                }}
                            />
                        ) : null}
                        <Table.Column
                            title={<FormattedMessage id="common.status" />}
                            key="status"
                            dataIndex="status"
                            ellipsis
                            width={200}
                            render={(value) => {
                                if (value === 2) {
                                    return (
                                        <div>
                                            <Badge color={"#F96964"} style={{marginRight:8}}/>
                                            {formatMessage({ id: "common.invalid", allowComponent: true })}
                                        </div>
                                    );
                                } else if (value === 1) {
                                    return (
                                        <div>
                                            {" "}
                                            <Badge color={"#41CC82"}  style={{marginRight:8}}/>
                                            {formatMessage({ id: "common.effective", allowComponent: true })}
                                        </div>
                                    );
                                }
                            }}
                        />
                        <Table.Column
                            width={150}
                            title={formatMessage({id: "common.operation", allowComponent: true})}
                            render={(_, record: any) => {
                                return operationBtns(record);
                            }}
                        />
                    </Table>
                    {props.rowAddable && (
                        <AuthButton
                            block
                            type="dashed"
                            style={{height: 36}}
                            className="mar-top-10"
                            icon={<PlusOutlined/>}
                            onClick={props.rowAdd}
                        >
                    <span style={{fontSize: 12}}>
                        {intl.formatMessage({id: "common.addTo", allowComponent: true})}
                    </span>
                        </AuthButton>
                    )}
                </ModuleContainer>
        </>
);
};

interface FactorProfileProp {
    layers: any;
    layersClick: (num: number, click: boolean) => void;
    // 能否选择分层
    layersClickable: boolean;
    cohort:any;
    dataSource: any[];
    rowEditable: boolean;
    rowEdit: (item: any) => void;
    rowDeleteable: boolean;
    rowDelete: (item: any) => void;
    rowAddable: boolean;
    rowAdd: () => void;
    regions: any[];
    getRegionLoading: boolean;
    regionRefresh: any;
    setRegionRefresh: (v: any) => void;
    syncable: boolean;
    sync: () => void;
    // addRegionLoading: boolean;
    // delRegionLoading: boolean;
}

// 分层因素
const FactorProfile = (props: FactorProfileProp) => {
    const intl = useTranslation();
    const auth = useAuth();
    const { formatMessage } = intl;
    const { runAsync: addRegionRun, loading: addRegionLoading } = useFetch(
        addRegion,
        { manual: true }
    );
    const { runAsync: delRegionRun, loading: delRegionLoading } = useFetch(
        delRegion,
        { manual: true }
    );
    const [form] = Form.useForm();
    const onDisable = (id: any) => {
        delRegionRun({
            envId: auth.env.id,
            cohortId: props.cohort ? props.cohort.id : null,
            id: id,
        }).then((result: any) => {
            message.success(result.msg).then();
            props.setRegionRefresh(props.regionRefresh + 1);
        });
    };

    const onAddRegion = () => {
        form.validateFields().then((result: any) => {
            let name = result.name;
            addRegionRun({
                customerId: auth.customerId,
                projectId: auth.project.id,
                envId: auth.env.id,
                cohortId: props.cohort ? props.cohort.id : null,
                name: name,
            }).then((result: any) => {
                form.resetFields();
                message.success(result.msg).then();
                props.setRegionRefresh(props.regionRefresh + 1);
            });
        });
    };
    const validator = {
        validator: (_: any, value: any) => {
            if (
                value &&
                value.trim().length > 0 &&
                props.regions.findIndex((item) => item.name === value) === -1
            ) {
                return Promise.resolve();
            } else if (!value || value.trim().length === 0) {
                return Promise.reject(
                    (formatMessage({ id: "common.required.prefix" }) as string) +
                    (formatMessage({ id: "randomization.config.region" }) as string)
                )
            } else {
                return Promise.reject(
                    formatMessage({
                        id: "randomization.config.region.duplicate",
                    })
                );
            }
        },
    };
    return (
        <ModuleContainer>
            <div style={{ marginBottom: 12 }}>
                <Title
                    name={formatMessage({ id: "randomization.config.factor", allowComponent: true })}
                ></Title>
            </div>
            {/* 地区分层 */}
            <Form form={form}>
                <Form.Item
                    label={formatMessage({ id: "projects.attributes.Layered", allowComponent: true })}
                >
                    <Radio.Group
                        style={{ marginTop: "6px" }}
                        value={props.layers}
                        disabled={!props.layersClickable}
                    >
                        <Row>
                            <div
                                onDoubleClick={() => {
                                    props.layersClick(1, true);
                                }}
                            >
                                <AuthRadio
                                    onClick={() => {
                                        props.layersClick(1, false);
                                    }}
                                    value={1}
                                >
                                    <FormattedMessage id="projects.attributes.country" />
                                </AuthRadio>
                            </div>
                            <div
                                onDoubleClick={() => {
                                    props.layersClick(2, true);
                                }}
                            >
                                <AuthRadio
                                    onClick={() => {
                                        props.layersClick(2, false);
                                    }}
                                    value={2}
                                >
                                    <FormattedMessage id="common.site" />
                                </AuthRadio>
                            </div>
                            <div
                                onDoubleClick={() => {
                                    props.layersClick(3, true);
                                }}
                            >
                                <AuthRadio
                                    onClick={() => {
                                        props.layersClick(3, false);
                                    }}
                                    value={3}
                                >
                                    <FormattedMessage id="projects.attributes.regionLayered" />
                                    <AuthPopover
                                        previewProps={{hideOnPreview: true}}
                                        placement="bottom"
                                        overlayClassName="area-pop"
                                        content={
                                            <div>
                                                <List
                                                    loading={
                                                        props.getRegionLoading ||
                                                        addRegionLoading ||
                                                        delRegionLoading
                                                    }
                                                    dataSource={props.regions}
                                                    renderItem={(item) => (
                                                        <List.Item
                                                            actions={
                                                                !item.disable &&
                                                                props.rowDeleteable
                                                                    ? [
                                                                          <CloseOutlined
                                                                              key="delete"
                                                                              onClick={() =>
                                                                                  onDisable(
                                                                                      item.id
                                                                                  )
                                                                              }
                                                                          />,
                                                                      ]
                                                                    : []
                                                            }
                                                        >
                                                            <div>
                                                                {item.name}
                                                            </div>
                                                        </List.Item>
                                                    )}
                                                />
                                                {props.rowAddable && (
                                                    <Row className="input-row">
                                                        <Form.Item
                                                            name="name"
                                                            rules={[validator]}
                                                        >
                                                            <Input
                                                                placeholder={formatMessage(
                                                                    {
                                                                        id: "placeholder.input.common",
                                                                    }
                                                                ) as string}
                                                            />
                                                        </Form.Item>
                                                        <AuthButton
                                                            type={"link"}
                                                            onClick={
                                                                onAddRegion
                                                            }
                                                        >
                                                            +
                                                            <FormattedMessage id="common.addTo" />
                                                        </AuthButton>
                                                    </Row>
                                                )}
                                            </div>
                                        }
                                        title={formatMessage({
                                            id: "randomization.config.region", allowComponent: true
                                        })}
                                        trigger="click"
                                    >
                                        <img
                                            style={{
                                                marginLeft: 5,
                                                cursor: "poniter",
                                                position: "relative",
                                                bottom: "1px",
                                            }}
                                            src={MoreIcon}
                                            alt=""
                                            width={14}
                                        />
                                    </AuthPopover>
                                </AuthRadio>
                            </div>
                        </Row>
                    </Radio.Group>
                    <>
                        {props.syncable && (
                            <div style={{ marginTop: "-6px", float: "right" }}>
                                <AuthButton
                                    onClick={(e: any) => {
                                        e.stopPropagation();
                                        props.sync();
                                    }}
                                >
                                    <FormattedMessage id="common.sync" />
                                </AuthButton>
                            </div>
                        )}
                    </>
                </Form.Item>
            </Form>

            {/* 分层因素 */}
            <Table
                dataSource={props.dataSource}
                pagination={false}
                rowKey={(record) => record.id}
            >
                <Table.Column
                    title={intl.formatMessage({ id: "common.name", allowComponent: true })}
                    dataIndex="label"
                    ellipsis
                />
                <Table.Column
                    title={intl.formatMessage({ id: "common.options", allowComponent: true })}
                    dataIndex="options"
                    ellipsis
                    render={(value) => {
                        if (!value) return <></>;
                        const items: string[] = [];
                        (value as []).forEach((it: any) => {
                            items.push(it.label);
                        });
                        return (
                            <TableCellPopover
                                title={
                                    <span
                                        style={{
                                            display: "flex",
                                            alignItems: "center",
                                        }}
                                    >
                                        <svg
                                            className="iconfont"
                                            width={16}
                                            height={16}
                                        >
                                            <use xlinkHref="#icon-quanbuxuanxing"></use>
                                        </svg>
                                        <span style={{ marginLeft: 8 }}>
                                            {intl.formatMessage({
                                                id: "common.all-options", allowComponent: true
                                            })}
                                        </span>
                                    </span>
                                }
                                items={items}
                            />
                        );
                    }}
                />
                <Table.Column
                    title={<FormattedMessage id="common.status" />}
                    key="status"
                    dataIndex="status"
                    ellipsis
                    width={200}
                    render={(value) => {
                        if (value === 2) {
                            return (
                                <div>
                                    <Badge color={"#F96964"} style={{marginRight:8}}/>
                                    {formatMessage({ id: "common.invalid", allowComponent: true })}
                                </div>
                            );
                        } else if (value === 1) {
                            return (
                                <div>
                                    {" "}
                                    <Badge color={"#41CC82"} style={{marginRight:8}}/>
                                    {formatMessage({ id: "common.effective", allowComponent: true })}
                                </div>
                            );
                        }
                    }}
                />
                <Table.Column
                    width={150}
                    title={intl.formatMessage({ id: "common.operation", allowComponent: true })}
                    render={(_, record: any) => {
                        const results: any[] = [];
                        if (props.rowEditable) {
                            results.push(
                                <AuthButton
                                    style={{ padding: "0px" }}
                                    size="small"
                                    type="link"
                                    onClick={() => props.rowEdit(record)}
                                >
                                    <FormattedMessage id="common.edit" />
                                </AuthButton>
                            );
                        }
                        if (props.rowDeleteable && record.status === 2 && !record.isCopyData) {
                            results.push(
                                <AuthButton
                                    style={{ padding: "0px" }}
                                    size="small"
                                    type="link"
                                    onClick={() => props.rowDelete(record.name)}
                                >
                                    <FormattedMessage id="common.delete" />
                                </AuthButton>
                            );
                        }
                        return InsertDivider(results);
                    }}
                />
            </Table>
            {props.rowAddable && (
                <AuthButton
                    block
                    type="dashed"
                    style={{ height: 36 }}
                    className="mar-top-10"
                    icon={<PlusOutlined />}
                    onClick={props.rowAdd}
                >
                    <span style={{ fontSize: 12 }}>
                        {" "}
                        {intl.formatMessage({ id: "common.addTo", allowComponent: true })}
                    </span>
                </AuthButton>
            )}
        </ModuleContainer>
    );
};
interface RandomListProfileProp {
    timeZone: any;
    uploadable: boolean;
    upload: () => void;
    randomListUpdate: (item: any) => void;
    editPermissions: boolean;
    generatable: boolean;
    generate: () => void;
    dataSource: any[];
    displayLastGroupColumn: boolean;

    showUseCount: (item: any) => void;
    statusChangeable: boolean;
    statusChage: (id: string, checked: boolean) => void;
    // 操作相关
    invalidable: boolean;
    invalid: (item: any) => void;
    historyShowable: boolean;
    showHistory: (item: any) => void;
    downloadable: boolean;
    downloadRandomArray: (item: any) => void;
    downloadRandomArrayLoading: boolean;
    downloadRandomArrayNew: (item: any) => void;
    downloadRandomArrayNewLoading: boolean;
}
const RandomListProfile = (props: RandomListProfileProp) => {
    const intl = useTranslation();
    const { formatMessage } = intl;
    const auth = useAuth();
    const g = useGlobal();
    const researchAttribute = auth.project.info.research_attribute
        ? auth.project.info.research_attribute
        : 0;
    const [loadingData, setLoadingData] = useSafeState(null);
    const operationBtns = (record: any) => {
        const btns: any[] = [];

        //编辑按钮
        if (props.editPermissions) {
            btns.push(
                <AuthButton
                    type="link"
                    style={{ padding: "0" }}
                    disabled={record.status === 3}
                    onClick={() => {
                        props.randomListUpdate(record);
                    }}
                >
                    <FormattedMessage id="common.edit" />
                </AuthButton>
            );
        }
        if (props.invalidable) {
            btns.push(
                <AuthButton
                    type="link"
                    style={{ padding: "0" }}
                    disabled={record.status === 3}
                    onClick={() => {
                        props.invalid(record);
                    }}
                >
                    <FormattedMessage id="randomization.random.list.invalid" />
                </AuthButton>
            );
        }
        if (props.historyShowable) {
            btns.push(
                <AuthButton
                    type="link"
                    style={{ padding: "0" }}
                    onClick={() => {
                        props.showHistory(record._id);
                    }}
                >
                    <FormattedMessage id="common.history" />
                </AuthButton>
            );
        }

        if (props.downloadable) {
            btns.push(
                <AuthButton
                    loading={
                        loadingData == record._id &&
                        (props.downloadRandomArrayNewLoading ||
                            props.downloadRandomArrayLoading)
                    }
                    disabled={
                        loadingData != record._id &&
                        (props.downloadRandomArrayNewLoading ||
                            props.downloadRandomArrayLoading)
                    }
                    type="link"
                    style={{ padding: "0" }}
                    onClick={() => {
                        setLoadingData(record._id);
                        if (researchAttribute === 0) {
                            props.downloadRandomArrayNew(record._id);
                        } else {
                            props.downloadRandomArray(record._id);
                        }
                    }}
                >
                    <FormattedMessage id="common.export" />
                </AuthButton>
            );
        }
        return InsertDivider(btns);
    };

    const  getSiteName = (record: any, type: any) => {
        if (g.lang === "en"){   // 英文
            if(record.shortName != "" && record.shortName != null && record.shortName != undefined){
                if(type === 1){
                    return <span>{record.number +"-"+ record.shortName}...</span>
                }else{
                    return <span>{record.number +"-"+ record.shortName}</span>
                }
            }else{
                if (record.nameEn != "" && record.nameEn != null && record.nameEn != undefined){
                    if(type === 1){
                        return <span>{record.number +"-"+ record.nameEn}...</span>
                    }else{
                        return <span>{record.number +"-"+ record.nameEn}</span>
                    }
                }else{
                    if(type === 1){
                        return <span>{record.number +"-"+ record.name}...</span>
                    }else{
                        return <span>{record.number +"-"+ record.name}</span>
                    }
                }
            }
        }else{                  // 中文
            if (record.shortName != "" && record.shortName != null && record.shortName != undefined){
                if(type === 1){
                    return <span>{record.number +"-"+ record.shortName}...</span>
                }else{
                    return <span>{record.number +"-"+ record.shortName}</span>
                }
            }else{
                if(type === 1){
                    return <span>{record.number +"-"+ record.name}...</span>
                }else{
                    return <span>{record.number +"-"+ record.name}</span>
                }
            }
        }
    }

    return (
        <ModuleContainer>
            <div style={{ marginBottom: 12 }}>
                <Title
                    name={formatMessage({
                        id: "projects.randomization.list", allowComponent: true
                    })}
                    extra={
                        <React.Fragment>
                            {props.uploadable && (
                                <AuthButton
                                    style={{ marginRight: 12 }}
                                    onClick={(e: any) => {
                                        e.stopPropagation();
                                        props.upload();
                                    }}
                                >
                                    <FormattedMessage id="common.upload" />
                                </AuthButton>
                            )}
                            {props.generatable && (
                                <AuthButton
                                    type="primary"
                                    onClick={(e: any) => {
                                        e.stopPropagation();
                                        props.generate();
                                    }}
                                >
                                    <FormattedMessage id="common.generate" />
                                </AuthButton>
                            )}
                        </React.Fragment>
                    }
                />
            </div>

            <Table
                className="mar-top-10"
                scroll={{ x: "max-content" }}
                dataSource={props.dataSource}
                pagination={false}
                rowKey={(record) => record._id}
            >
                <Table.Column
                    title={intl.formatMessage({ id: "common.serial", allowComponent: true })}
                    dataIndex="#"
                    key="#"
                    width={60}
                    render={(value, record, index) => index + 1}
                />
                <Table.Column
                    title={<FormattedMessage id="common.name" />}
                    dataIndex="name"
                    key="name"
                    width={130}
                    ellipsis
                />
                <Table.Column
                    title={<FormattedMessage id="common.site" />}
                    dataIndex="projectSiteData"
                    key="projectSiteData"
                    width={130}
                    render={(value, record, index) => {
                        if (value === null || value.length == 0) {
                            return <span className={'.ant-steps-step-description'}><FormattedMessage id="supply.plan.all.site" /></span>
                        } else if (value.length === 1) {
                            return (
                                <span className={'.ant-steps-step-description'}>
                                    {
                                        // value[0].number+"-"+ (value[0].shortName!=""? value[0].shortName:value[0].name)
                                        getSiteName(value[0], 0)
                                    }
                                </span>
                            );
                        }else{
                            return (
                                <>
                                    <Tooltip
                                        placement="top"
                                        title={
                                            <>
                                                {
                                                    value.map((v: any) => {
                                                        return <div>{getSiteName(v, 0)}</div>
                                                    })
                                                }
                                            </>
                                        }
                                    >
                                        <span className={'.ant-steps-step-description'}>
                                            {
                                                getSiteName(value[0], 1)
                                            }
                                        </span>
                                    </Tooltip>
                                </>
                            )
                        }
                    }}
                />
                {props.displayLastGroupColumn && (
                    <Table.Column
                        title={
                            <FormattedMessage id="projects.randomization.last.group" />
                        }
                        dataIndex="last_group"
                        key="last_group"
                        ellipsis
                    />
                )}
                <Table.Column
                    title={<FormattedMessage id="common.operator" />}
                    // @ts-ignore
                    dataIndex={["meta", "createdUser", [0], "info", "name"]}
                    // @ts-ignore
                    key={["meta", "createdUser", [0], "info", "name"]}
                    width={100}
                    ellipsis
                />
                <Table.Column
                    title={<FormattedMessage id="common.operation.time" />}
                    key="operationTime"
                    dataIndex="operationTime"
                    ellipsis
                    width={180}
                    render={(value, record: any, index) =>
                        record.meta.created_at === null ||
                        record.meta.created_at === 0
                            ? ""
                            : moment
                                  .unix(record.meta.created_at)
                                  .utc()
                                  .add(props.timeZone, "hour")
                                  .format("YYYY-MM-DD HH:mm:ss")
                    }
                />
                <Table.Column
                    title={
                        <FormattedMessage id="projects.randomization.useCount" />
                    }
                    dataIndex="count"
                    key="count"
                    width={g.lang==="zh"?190:230}
                    fixed={"right"}
                    render={(value, record: any, index) => (
                        <AuthButton
                            type="link"
                            // @ts-ignore
                            onClick={() => props.showUseCount(record)}
                        >
                            {record.use}/{value}
                        </AuthButton>
                    )}
                />
                <Table.Column
                    title={<FormattedMessage id="common.status" />}
                    dataIndex="status"
                    key="status"
                    width={60}
                    fixed={"right"}
                    render={(value, record: any, index) => (
                        <AuthSwitch
                            size="small"
                            onClick={(checked: boolean) =>
                                props.statusChage(record._id, checked)
                            }
                            defaultChecked={value === 1}
                            disabled={
                                !props.statusChangeable ||
                                record.status === 3 ||
                                auth.project.status !== 0
                            }
                        />
                    )}
                />
                <Table.Column
                    title={<FormattedMessage id="common.operation" />}
                    dataIndex="trail"
                    key="trail"
                    width={240}
                    fixed={"right"}
                    render={(value, record: any, index) =>
                        operationBtns(record)
                    }
                />
            </Table>
        </ModuleContainer>
    );
};

const ModuleContainer = styled.div`
    padding-bottom: 24px;
`;
