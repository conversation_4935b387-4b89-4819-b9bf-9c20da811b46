import { ProjectRandomizationProvider } from "./context";
import { RandomList } from "./main";
import { Tabs } from "antd";
import { FormattedMessage } from "react-intl";
import { useAuth } from "../../../../context/auth";
import { permissions } from "../../../../tools/permission";
import { FormIndex } from "./form-index";
import {getProjectCohortRandom} from "../../../../api/randomization";
import {useFetch} from "../../../../hooks/request";
import {useEffect} from "react";
import {useSafeState} from "ahooks";

export const Index = (props: any) => {
  const auth = useAuth();
  const allCohorts = auth.env?.cohorts;
  const projectType = auth.project.info.type;
  const [cohorts, setCohorts] = useSafeState<any>([]);
  const [tabKey, setTabKey] = useSafeState<any>("1")

    const {
        runAsync: getProjectCohortRandomRun,
        loading: getProjectCohortRandomLoading,
    } = useFetch(getProjectCohortRandom, { manual: true });

    useEffect(() => {
        getProjectCohortRandomRun({"projectId":auth.project.id, "envId":auth.env.id}).then(
            (data:any) => {
                setCohorts(data.data)
            }
        )
    },[])

    useEffect(() => {
        if (!!props.tabKey) setTabKey(props.tabKey)
    }, [props.tabKey])

  return (
    <ProjectRandomizationProvider>
        {
            auth.isRandomDispensing.random != null ?
                <Tabs
                    destroyInactiveTabPane={true}
                    size="small"
                    defaultActiveKey="1"
                    tabPosition="top"
                    activeKey={tabKey}
                    onChange={setTabKey}
                >
                    {   auth.isRandomDispensing.random &&
                    (
                        permissions(auth.project.permissions,"operation.build.randomization.type.view") ||
                        permissions(auth.project.permissions,"operation.build.randomization.group.view") ||
                        permissions(auth.project.permissions,"operation.build.randomization.factor.view") ||
                        permissions(auth.project.permissions,"operation.build.randomization.list.view-summary")
                        //  ||
                        // permissions(auth.project.permissions,"operation.build.randomization.factor-in.view")
                    )?
                        <Tabs.TabPane
                            tab={<FormattedMessage id="projects.randomization.design" />}
                            key="1"
                        >
                            {projectType === 1 ? (
                                <RandomList />
                            ) : (
                                <Tabs
                                    destroyInactiveTabPane={true}
                                    size="small"
                                    className="block-tab"
                                    defaultActiveKey="1"
                                    tabPosition="top"
                                    items={cohorts.map((cohort: any, cohort_index: number) => {
                                        const id = cohort.id;
                                        return {
                                            label: cohort.type === 1?cohort.name + " - " + cohort.reRandomName:cohort.name,
                                            key: id,
                                            children: <RandomList cohort={cohort} />,
                                        };
                                    })}
                                ></Tabs>
                            )}
                        </Tabs.TabPane>:null
                    }

                    {/*{permissions(auth.project.permissions,"operation.build.randomization.factor.page.view")?*/}
                    {/*    <Tabs.TabPane tab={<FormattedMessage id="randomization.config.factor" />} key="2">*/}
                    {/*        {tabKey === "2" ? <RandomizationFactor/> : null}*/}
                    {/*    </Tabs.TabPane>*/}
                    {/*    :null*/}
                    {/*}*/}
                    {permissions(
                        auth.project.permissions,
                        "operation.build.randomization.form.list"
                    ) ? (
                        <Tabs.TabPane
                            tab={<FormattedMessage id="projects.randomization.form" />}
                            key="3"
                        >
                            {projectType === 1 ? (
                                <FormIndex />
                            ) : (
                                <Tabs
                                    destroyInactiveTabPane={true}
                                    size="small"
                                    defaultActiveKey="1"
                                    tabPosition="top"
                                    className="block-tab"
                                    items={allCohorts.map((cohort: any, cohort_index: number) => {
                                        const id = cohort.id;
                                        return {
                                            label: cohort.type === 1?cohort.name + " - " + cohort.re_random_name:cohort.name,
                                            key: id,
                                            children: <FormIndex cohort={cohort} />,
                                        };
                                    })}
                                ></Tabs>
                            )}
                        </Tabs.TabPane>
                    ) : null}
                </Tabs>
                :null
        }
    </ProjectRandomizationProvider>
  );
};
