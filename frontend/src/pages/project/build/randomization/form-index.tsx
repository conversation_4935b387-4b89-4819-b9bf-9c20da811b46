import React from "react";
import {Badge, Button, Col, Row, Spin, Table} from "antd";
import {permissions, permissionsCohort} from "../../../../tools/permission";
import {useAuth} from "../../../../context/auth";
import {useSafeState} from "ahooks";
import {boolStatus, controlTypes} from "../../../../data/data";
import {FormAdd} from "./form-add";
import {useFetch} from "../../../../hooks/request";
import {deleteForm, getForm} from "../../../../api/form";
import {FormView} from "./form-view";
import {CustomConfirmModal} from "components/modal";
import {InsertDivider} from "components/divider";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {AuthButton} from "../../../common/auth-wrap";

export const FormIndex = (props: any) => {
    const auth = useAuth();
    const intl = useTranslation();
    const { formatMessage } = intl;

    const [data, setData] = useSafeState<any>([]);
    const [id, setId] = useSafeState<any>(null);
    const field_add: any = React.useRef();
    const field_view: any = React.useRef();

    const connectEdc = auth.project ? auth.project.connectEdc : null;
    const lockConfig = auth.env.lockConfig ? auth.env.lockConfig : false;
    const projectStatus = auth.project.status ? auth.project.status : 0;

    const { runAsync: getFormRun, loading: getFormLoading } = useFetch(
        getForm,
        { manual: true }
    );
    const { runAsync: deleteFormRun, loading: deleteFormLoading } = useFetch(
        deleteForm,
        { manual: true }
    );

    const get_form = () => {
        let cohortId = null;
        if (props.cohort != null) {
            cohortId = props.cohort.id;
        }
        getFormRun({
            customerId: auth.customerId,
            envId: auth.env.id,
            cohortId: cohortId,
        }).then((result: any) => {
            setId(result.data.id);
            setData(result.data.fields);
        });
    };

    const add = () => {
        field_add.current.show(data, id);
    };

    const edit = (field: any) => {
        field_add.current.show(data, id, field);
    };

    const remove = (fieldId: any) => {
        CustomConfirmModal({
            title: formatMessage({ id: "common.tips" }),
            content: formatMessage({ id: "common.confirm.delete" }),
            okText: formatMessage({ id: "common.ok" }),
            onOk: () =>
                deleteFormRun({ id, fieldId }).then(() => {
                    get_form();
                }),
        });
    };

    const preview = () => {
        field_view.current.show(data);
    };

    function renderStatus(value: boolean, record:any, types :any) {
        if (!(record.applicationType === null || record.applicationType === 1 || record.applicationType === 4) && types === 1) {
            return "-"
        }
        // @ts-ignore

        return boolStatus.find((it: any) => it.value === String(value)).label;
    }

    function renderControlType(type: any) {
        // @ts-ignore
        return controlTypes.find((it: any) => it.value === type).label;
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
    React.useEffect(get_form, []);

    return (
        <React.Fragment>
            <Row gutter={8} justify="space-between">
                <Col></Col>
                <Col>
                    {permissions(
                        auth.project.permissions,
                        "operation.build.randomization.form.preview"
                    ) && (
                        <AuthButton
                            type="default"
                            onClick={preview}
                            style={{ marginRight: "12px" }}
                        >
                            <FormattedMessage id="form.preview" />
                        </AuthButton>
                    )}
                    {permissionsCohort(
                        auth.project.permissions,
                        "operation.build.randomization.form.add",
                        props.cohort?.status
                    ) &&
                    !lockConfig &&
                    projectStatus !== 2 ? (
                        <AuthButton
                            type="primary"
                            onClick={() => {
                                add();
                            }}
                        >
                            {formatMessage({ id: "common.add", allowComponent: true })}
                        </AuthButton>
                    ) : null}
                </Col>
            </Row>
            <Spin spinning={getFormLoading}>
                <Table
                    className="mar-top-10"
                    dataSource={data}
                    pagination={false}
                    scroll={{ x: true }}
                    rowKey={(record) => record.id}
                >
                    <Table.Column
                        title={<FormattedMessage id="common.serial" />}
                        dataIndex="#"
                        key="#"
                        width={110}
                        render={(text, record, index) => index + 1}
                    />
                    <Table.Column
                        title={<FormattedMessage id="form.field.label" />}
                        key="label"
                        dataIndex="label"
                        ellipsis
                        width={200}
                    />
                    {connectEdc === 1 ? (
                        <Table.Column
                            title={<FormattedMessage id="form.field.name" />}
                            key="name"
                            dataIndex="name"
                            ellipsis
                        />
                    ) : null}
                    <Table.Column
                        title={<FormattedMessage id="form.list.modify" />}
                        key="modifiable"
                        dataIndex="modifiable"
                        width={200}
                        render={(value, record, index) => renderStatus(value, record, 1)}
                    />
                    <Table.Column
                        title={<FormattedMessage id="form.required" />}
                        key="required"
                        dataIndex="required"
                        width={150}
                        render={(value, record, index) => renderStatus(value, record, 2)}
                    />
                    <Table.Column
                        title={<FormattedMessage id="form.control.type" />}
                        key="type"
                        dataIndex="type"
                        ellipsis
                        width={150}
                        render={(value, record, index) =>
                            renderControlType(value)
                        }
                    />
                    <Table.Column
                        title={<FormattedMessage id="common.status" />}
                        key="status"
                        dataIndex="status"
                        ellipsis
                        width={200}
                        render={(value) => {
                            if (value === 2) {
                                return <div >
                                    <Badge style={{ marginRight: "8px" }} color={"#F96964"}/>
                                    {formatMessage({id: 'common.invalid', allowComponent: true})}
                                </div>
                            } else if (value === 1) {
                                return (
                                    <div>
                                        {" "}
                                        <Badge style={{ marginRight: "8px" }} color={"#41CC82"} />
                                        {formatMessage({
                                            id: "common.effective", allowComponent: true
                                        })}
                                    </div>
                                );
                            }
                        }}
                    />
                    <Table.Column
                        title={<FormattedMessage id="form.application.type" />}
                        key="applicationType"
                        dataIndex="applicationType"
                        ellipsis
                        width={200}
                        render={(value) => {
                            if (value === null || value === 1) {
                                return formatMessage({id: 'form.application.type.register', allowComponent: true})
                            } else if (value === null || value === 2) {
                                return formatMessage({id: 'form.application.type.formula', allowComponent: true})
                            } else if (value === null || value === 3){
                                return formatMessage({id: 'drug.configure.setting.dose.form.doseAdjustment', allowComponent: true})
                            }else {
                                return formatMessage({id: 'randomization.config.factor.calc', allowComponent: true})
                            }
                        }}
                    />
                    {lockConfig || projectStatus === 2 ? null :(
                        <Table.Column
                            title={<FormattedMessage id="common.operation" />}
                            width={200}
                            render={(value, record: any, index) => {
                                const btns = [];
                                if (
                                    permissionsCohort(
                                        auth.project.permissions,
                                        "operation.build.randomization.form.edit",
                                        props.cohort?.status
                                    )
                                ) {
                                    btns.push(
                                        <AuthButton
                                            style={{ padding: 0 }}
                                            size="small"
                                            type="link"
                                            onClick={() => edit(record)}
                                        >
                                            <FormattedMessage id="common.edit" />
                                        </AuthButton>
                                    );
                                }
                                if (
                                    permissionsCohort(
                                        auth.project.permissions,
                                        "operation.build.randomization.form.delete",
                                        props.cohort?.status
                                    ) &&
                                    record.status === 2 && !record.used
                                ) {
                                    btns.push(
                                        <AuthButton
                                            style={{ padding: 0 }}
                                            loading={deleteFormLoading}
                                            size="small"
                                            type="link"
                                            onClick={() => remove(record.id)}
                                        >
                                            <FormattedMessage id="common.delete" />
                                        </AuthButton>
                                    );
                                }
                                return InsertDivider(btns);
                            }}
                        />
                    )}
                </Table>
            </Spin>
            <FormAdd bind={field_add} refresh={get_form} cohort={props.cohort}/>
            <FormView bind={field_view} />
        </React.Fragment>
    );
};
