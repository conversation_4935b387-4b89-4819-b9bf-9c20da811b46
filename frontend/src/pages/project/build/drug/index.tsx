import {DrugProvider} from "./context";
import {Main} from "./main";
import {PageContextProvider} from "../../../../context/page";
import React from "react";
import styled from "@emotion/styled";

export const Index = (props: any) => {
    return (
        <DrugProvider>
            <PageContextProvider>
                {props.hideTabs ? <HideTabs><Main {...props} /></HideTabs> : <Main {...props} />}
            </PageContextProvider>
        </DrugProvider>
    )
};

const HideTabs = styled.div`
    .ant-tabs-nav {
      // height: 0 !important;
      // visibility: hidden;
    }
`