import React, { useState } from "react";
import {
  Button,
  Col,
  Form,
  Modal,
  Popover,
  Row,
  Select,
  Spin,
  Table,
  Tooltip,
  notification, Divider,
} from "antd";
import {
  CloseOutlined,
  QuestionCircleFilled,
  CloseCircleTwoTone,
} from "@ant-design/icons";
import { VisitSetting } from "./visit-setting";
import { VisitAdd } from "./visit-add";
import { permissions, permissionsCohort } from "../../../../tools/permission";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import {
  addVisit,
  deleteVisit,
  dragVisit,
  getVisitList, updateVisitBaseCohort,
  updateVisitType,
  visitHistory,
} from "../../../../api/visit";
import { getIsGroup } from "../../../../api/drug";
import { useSafeState } from "ahooks";
import { CustomConfirmModal } from "../../../../components/modal";
import styled from "@emotion/styled";
import { VisitView } from "./visit-view";
import { arrayMove } from "@dnd-kit/sortable";
import { DragEndEvent } from "@dnd-kit/core";
import { useDrug } from "./context";
import { DateType } from "../../../../data/data";
import { TableSortable } from "../../../../components/TableSortable";
import { TableSortableRow } from "../../../../components/TableSortableRow";
import DatePicker from "../../../../components/DatePicker";
import moment from "moment/moment";
import { PaginationView } from "../../../../components/pagination";
import { usePage } from "../../../../context/page";
import { useGlobal } from "../../../../context/global";
import Bell from "../../../../images/bell.png";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {AuthButton, AuthTooltip, clickFilter} from "../../../common/auth-wrap";

export const VisitIndex = (props: any) => {
  const drug = useDrug();
  const auth = useAuth();
  const intl = useTranslation();
  const g = useGlobal();
  const { formatMessage } = intl;
  const [form] :any = Form.useForm();

  const [visit, setVisit]: any = useSafeState({});
  const [visitVersion, setVisitVersion]: any = useSafeState("");
  const visit_setting = React.useRef();
  const visit_add = React.useRef();
  const [iconColor, setIconColor] = useSafeState(0);
  const [cohortBaseIconColor, setCohortBaseIconColor] = useSafeState(0);

  const { runAsync: getVisitListRun, loading } = useFetch(getVisitList, {
    manual: true,
  });
  const { runAsync: deleteVisitRun } = useFetch(deleteVisit, {
    manual: true,
  });
  const { runAsync: copyVisitRun } = useFetch(addVisit, { manual: true });

  const { runAsync: getIsGroupRun, loading: getIsGroupLoading } = useFetch(
    getIsGroup,
    { manual: true }
  );

  const envId = auth.env ? auth.env.id : null;
  const cohortId = props.cohort ? props.cohort.id : null;
  const customerId = auth.customerId;
  const projectId = auth.project.id;
  const lockConfig = auth.env.lockConfig ? auth.env.lockConfig : false;
  const projectStatus = auth.project.status ? auth.project.status : 0;
  const researchAttribute = auth.project.info.research_attribute
    ? auth.project.info.research_attribute
    : 0;
  const [open, setOpen] = useSafeState<any>(false);
  const [cohortOpen, setCohortOpen] = useSafeState<any>(false);
  const [showBaseCohort, setShowBaseCohort] = useSafeState<any>(false);
  const [cohortSelect, setCohortSelect] = useSafeState<any>("");

  const hide = () => {
    setOpen(false);
    setCohortOpen(false);
    setIconColor(0);
    setCohortBaseIconColor(0);
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (newOpen != null) {
      if (newOpen === true) {
        setIconColor(1);
      } else {
        setIconColor(0);
      }
    }
    setOpen(newOpen);
  };

  const handleMouseEnter = () => {
    setIconColor(1);
  };

  const handleMouseLeave = () => {
    setIconColor(0);
  };

  const handleCohortOpenChange = (newOpen: boolean) => {
    if (newOpen != null) {
      if (newOpen === true) {
        setCohortBaseIconColor(1);
      } else {
        setCohortBaseIconColor(0);
      }
    }
    setCohortOpen(newOpen);
  };

  const handleCohortMouseEnter = () => {
    setCohortBaseIconColor(1);
  };

  const handleCohortMouseLeave = () => {
    setCohortBaseIconColor(0);
  };


  const getJudgeGroup = () => {
    getIsGroupRun({
      customerId,
      projectId,
      envId,
      cohortId: cohortId,
      category: "1",
      roleId: auth.project.permissions.role_id,
    }).then((result: any) => {
      var visits = [];
      if (result.data !== null && result.data.length > 0) {
        notification.open({
          message: (
            <div
              style={{
                fontFamily:
                  "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                fontSize: "14px",
              }}
            >
              <CloseCircleTwoTone
                twoToneColor="#FF0000"
                style={{ paddingRight: "8px" }}
              />
              {formatMessage({ id: result.data })}
            </div>
          ),
          // duration: 5,
          placement: "top",
          style: {
            width: "720px",
            height: "55px",
            background: "#fff2f1",
            borderStyle: "solid",
            border: "1px",
            borderColor: "#41CC82",
            borderRadius: "4px",
          },
          // closeIcon: null, // 隐藏关闭按钮，用户必须手动关闭
          duration: null, // 设置为null，让通知框不自动关闭
        });
      }
    });
  };

  const getList = () => {
    getVisitListRun({
      customerId,
      projectId,
      envId,
      cohortId: cohortId,
      roleId: auth.project.permissions.role_id,
    }).then((result: any) => {
      drug.setId(result.data.id);
      drug.setData(
        result.data.update_infos.infos ? result.data.update_infos.infos : []
      );
      drug.setCanPush(
        result.data.createdAt === result.data.update_infos.updatedAt
      );

      setVisit(result.data.update_infos);
      setVisitVersion(result.data.version);
      setCohortSelect(
          auth.env.cohorts?.find((it:any)=> it.id === result.data.update_infos.baseCohort)?
              auth.env.cohorts?.find((it:any)=> it.id === result.data.update_infos.baseCohort).name
              :
              auth.env.cohorts?.find((it:any)=> it.id === cohortId).name
      )
      drug.setVisitType(result.data.update_infos.visit_type);
      form.setFieldsValue({
        visit_type: result.data.update_infos.visit_type,
      });
      if (auth.project.info.type == 3 && auth.env.cohorts[0]?.id !== cohortId ){
        form.setFieldsValue({
          visit_type: result.data.update_infos.visit_type,
          baseCohort: result.data.update_infos.baseCohort === auth.env.cohorts[0]?.id?auth.env.cohorts[0]?.id:cohortId,
        });
        if (result.data.update_infos.visit_type === 0) {
          setShowBaseCohort(true)

        }
      }
    });
  };

  const setting = () => {
    // @ts-ignore
    visit_setting.current.show(drug.id);
  };

  const add = () => {
    // @ts-ignore
    visit_add.current.show(drug.id);
  };

  const push_visit = () => {
    drug.setVisitView(true);
  };

  const view_visit = () => {
    drug.setRecordVisible(true);
  };

  const edit = (info: any, oper: any) => {
    // @ts-ignore
    visit_add.current.show(drug.id, info, oper);
  };

  const remove = (infoId: any, visitName: string) => {
    CustomConfirmModal({
      title: formatMessage({ id: "common.confirm.delete" }),
      okText: formatMessage({ id: "common.ok" }),
      cancelText: formatMessage({ id: "common.cancel" }),
      onOk: () =>
        deleteVisitRun({
          id: drug.id,
          visitId: infoId,
          visitName: visitName,
          envId: envId,
        }).then(() => {
          getList();
        }),
    });
  };


  // eslint-disable-next-line react-hooks/exhaustive-deps
  React.useEffect(() => {
    getList();
    getJudgeGroup();
  }, []);

  const { runAsync: updateVisitTypeRun, loading: updateVisitTypeLoading } =
    useFetch(updateVisitType, { manual: true });
  const { runAsync: dragVisitRun, loading: dragVisitLoading } = useFetch(
    dragVisit,
    { manual: true }
  );
  const { runAsync: updateVisitBaseCohortRun, loading: updateVisitBaseCohortLoading } =
      useFetch(updateVisitBaseCohort, { manual: true });
  const update_visit_type = () => {
    form.validateFields().then((value: any) => {
      updateVisitTypeRun({
        id: drug.id,
        customerId,
        projectId,
        envId,
        cohortId,
        visit_type: value.visit_type,
      }).then(() => {
        getList();
        drug.setVisitTypeView(false);
        setIconColor(0);
        setShowBaseCohort(value.visit_type === 0)
      });
    });
  };


  const update_visit_base_cohort = () => {
    form.validateFields().then((value: any) => {
      updateVisitBaseCohortRun({
        id: drug.id,
        customerId,
        projectId,
        envId,
        cohortId,
        baseCohort: value.baseCohort,
      }).then(() => {
        // getList();
        setCohortOpen(false)
        setCohortSelect(auth.env.cohorts?.find((it:any)=> it.id === value.baseCohort).name)
        getList();

      });
    });
  };

  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      let prevData = drug.data;
      const oldIndex = prevData.findIndex((item: any) => item.id === active.id);
      const newIndex = prevData.findIndex((item: any) => item.id === over?.id);
      const newData = arrayMove(prevData, oldIndex, newIndex);

      dragVisitRun({
        id: drug.id,
        sortIds: newData.map((value: any) => value.id),
      }).then((result: any) => {
        drug.setData(newData);
        drug.setCanPush(false);
      });
    }
  };

  const columns: any = [
    {
      title: <FormattedMessage id={"common.serial"} />,
      dataIndex: "serial",
      key: "serial",
      width: 70,
      render: (val: any, record: any, index: number) => index + 1,
    },
    {
      title: <FormattedMessage id="visit.cycle.visitNumber" />,
      dataIndex: "number",
      key: "number",
      ellipsis: true,
    },
    {
      title: <FormattedMessage id="visit.cycle.visitName" />,
      dataIndex: "name",
      key: "name",
      ellipsis: true,
      width: 80,
    },
    {
      title: <FormattedMessage id="visit.cycle.window" />,
      dataIndex: "window",
      key: "window",
      ellipsis: true,
      render: (value: any, record: any) =>
        !record.interval && !record.PeriodMin && !record.periodMax ? (
          "-"
        ) : (
          <>
            <Row>
              {record.interval}&nbsp;
              <span>{DateType.find((it: any) => it.value === record.unit)?.label}</span>
            </Row>
            <Row>
              {record.PeriodMin} ~ {record.periodMax}&nbsp;
              <span>{DateType.find((it: any) => it.value === record.unit)?.label}</span>
            </Row>
          </>
        ),
    },
    {
      title: <FormattedMessage id="visit.cycle.group" />,
      dataIndex: "group",
      key: "group",
      render: (value: any) =>
        value?.map((item: any) => <Row style={{ marginRight: 5 }}>{item}</Row>),
    },
    {
      title: <FormattedMessage id="visit.cycle.dispensing" />,
      dataIndex: "dispensing",
      key: "dispensing",
      ellipsis: true,
      render: (value: any) =>
        value ? (
          <OpenDiv>{formatMessage({ id: "common.yes", allowComponent: true })}</OpenDiv>
        ) : (
          <CloseDiv>{formatMessage({ id: "common.no", allowComponent: true })}</CloseDiv>
        ),
    },
    {
      title: <FormattedMessage id="visit.cycle.random" />,
      dataIndex: "random",
      key: "random",
      ellipsis: true,
      render: (value: any) =>
        value ? (
          <OpenDiv>{formatMessage({ id: "common.yes", allowComponent: true })}</OpenDiv>
        ) : (
          <CloseDiv>{formatMessage({ id: "common.no", allowComponent: true })}</CloseDiv>
        ),
    },
    researchAttribute !== 1
      ? {
          title: <FormattedMessage id="visit.cycle.dtp" />,
          dataIndex: "dtp",
          key: "dtp",
          ellipsis: true,
          render: (value: any) =>
            value ? (
              <OpenDiv>{formatMessage({ id: "common.yes", allowComponent: true })}</OpenDiv>
            ) : (
              <CloseDiv>{formatMessage({ id: "common.no", allowComponent: true })}</CloseDiv>
            ),
        }
      : false,
    {
      title: <FormattedMessage id="visit.cycle.replace" />,
      dataIndex: "replace",
      key: "replace",
      ellipsis: true,
      render: (value: any) =>
        value ? (
          <OpenDiv>{formatMessage({ id: "common.yes", allowComponent: true })}</OpenDiv>
        ) : (
          <CloseDiv>{formatMessage({ id: "common.no", allowComponent: true })}</CloseDiv>
        ),
    },
    {
      title: <FormattedMessage id="drug.configure.setting.dose.form.doseAdjustment" />,
      dataIndex: "doseAdjustment",
      key: "doseAdjustment",
      ellipsis: true,
      render: (value: any) =>
        value ? (
          <OpenDiv>{formatMessage({ id: "common.yes", allowComponent: true })}</OpenDiv>
        ) : (
          <CloseDiv>{formatMessage({ id: "common.no", allowComponent: true })}</CloseDiv>
        ),
    },


    (permissionsCohort(
        auth.project.permissions,
        "operation.build.medicine.visit.delete",
        props.cohort?.status
    ) || permissionsCohort(
            auth.project.permissions,
            "operation.build.medicine.visit.delete",
            props.cohort?.status
        ) || permissionsCohort(
            auth.project.permissions,
            "operation.build.medicine.visit.copy",
            props.cohort?.status
        )
    ) && {
      title: <FormattedMessage id="common.operation" />,
      dataIndex: "actions",
      key: "actions",
      fixed: "right",
      width: 220,
      render: (value: any, record: any) => (
        <>
          {permissionsCohort(
            auth.project.permissions,
            "operation.build.medicine.visit.edit",
            props.cohort?.status
          ) && (
            <AuthButton
              style={{ padding: 0, marginRight: "12px" }}
              size="small"
              type="link"
              onClick={() => edit(record, null)}
            >
              <FormattedMessage id="common.edit" />
            </AuthButton>
          )}
          {permissionsCohort(
            auth.project.permissions,
            "operation.build.medicine.visit.delete",
            props.cohort?.status
          ) &&
            !record.isUsed && (
              <AuthButton
                style={{ padding: 0, marginRight: "12px" }}
                size="small"
                type="link"
                onClick={() => remove(record.id, record.name)}
              >
                <FormattedMessage id="common.delete" />
              </AuthButton>
            )}
          {permissionsCohort(
            auth.project.permissions,
            "operation.build.medicine.visit.copy",
            props.cohort?.status
          ) && (
            <AuthButton
              style={{ padding: 0 }}
              size="small"
              type="link"
              onClick={() => edit(record, 1)}
            >
              <FormattedMessage id="common.copy" />
            </AuthButton>
          )}
        </>
      ),
    },
  ].filter(Boolean);

  return (
    <React.Fragment>
      <Spin spinning={loading}>
        <Col>
          <Form.Item
            style={{ marginBottom: 0, marginLeft: 12 }}
            label={formatMessage({ id: "visit.cycle.version", allowComponent: true })}
          >
            {visitVersion !== "" ? visitVersion : "-"}
          </Form.Item>
        </Col>
        <Row justify="space-between" style={{ marginTop: 6, marginBottom: 16 }}>
          <Col>
            <Row>
              <Col>
                {
                  <Form.Item
                    style={{
                      marginBottom: 0,
                      marginLeft: 12,
                    }}
                    label={formatMessage({
                      id: "visit.cycle.type", allowComponent: true
                    })}
                    tooltip={{
                      title: (
                        <>
                          {/* <Row>{formatMessage({id:"visit.cycle.type.tip"})}</Row> */}
                          <Row>
                            {formatMessage({
                              id: "visit.cycle.type.tip.baseline", allowComponent: true, dark: true
                            })}
                          </Row>
                          <Row>
                            {formatMessage({
                              id: "visit.cycle.type.tip.lastdate", allowComponent: true, dark: true
                            })}
                          </Row>
                        </>
                      ),
                      icon: (
                        <QuestionCircleFilled
                          style={{
                            color: "#D0D0D0",
                            cursor: "pointer",
                          }}
                        />
                      ),
                      overlayStyle: {
                        whiteSpace: "pre-wrap",
                        maxWidth: "600px",
                        width: "auto",
                      },
                    }}
                  >
                    {!visit.visit_type || visit.visit_type === 0
                      ? "Baseline"
                      : "Lastdate"}
                    {permissionsCohort(
                      auth.project.permissions,
                      "operation.build.medicine.visit.update",
                      props.cohort?.status
                    ) &&
                      !lockConfig &&
                      projectStatus !== 2 && (
                        <Popover
                          placement="bottom"
                          open={open}
                          title={
                            <div>
                              {formatMessage({
                                id: "common.modify", allowComponent: true, dark: true
                              })}
                              <AuthButton
                                onClick={hide}
                                type="text"
                                icon={
                                  <CloseOutlined
                                    style={{
                                      color: "#666666",
                                    }}
                                  />
                                }
                                style={{
                                  float: "right",
                                  marginRight: -4,
                                  width: "15px",
                                  height: "15px",
                                }}
                              />
                            </div>
                          }
                          content={
                            <div
                              style={{
                                width: "228px",
                                height: "116px",
                              }}
                            >
                              <Form layout={"vertical"} form={form}>
                                <Form.Item
                                  name="visit_type"
                                  rules={[
                                    {
                                      required: true,
                                    },
                                  ]}
                                  // label={formatMessage({id:"visit.cycle.type"})}
                                  label={
                                    <span
                                      style={{
                                        fontFamily: "PingFang SC",
                                        fontSize: "14px",
                                        fontWeight: 400,
                                        lineHeight: "20px",
                                        letterSpacing: "0px",
                                        textAlign: "left",
                                        color: "#677283",
                                      }}
                                    >
                                      {formatMessage({
                                        id: "visit.cycle.type", allowComponent: true
                                      })}
                                    </span>
                                  }
                                >
                                  <Select>
                                    <Select value={0}>Baseline</Select>
                                    <Select value={1}>Lastdate</Select>
                                  </Select>
                                </Form.Item>
                              </Form>
                              <AuthButton
                                onClick={update_visit_type}
                                type={"primary"}
                                className="full-width"
                              >
                                {formatMessage({
                                  id: "common.ok", allowComponent: true
                                })}
                              </AuthButton>
                            </div>
                          }
                          style={{
                            height: "180px",
                            width: "260px !important",
                            left: "496px",
                            top: "155px",
                            borderRadius: "2px",
                            marginTop: "16px",
                            marginLeft: "12px",
                          }}
                          trigger="click"
                          onOpenChange={handleOpenChange}
                        >
                          <AuthTooltip
                            title={formatMessage({
                              id: "common.modify", allowComponent: true, dark: true
                            })}
                          >
                            <i
                              // onClick={()=>drug.setVisitTypeView(true)}
                              style={{
                                marginLeft: 8,
                                cursor: "pointer",
                                color: iconColor === 0 ? "#999999" : "#165DFF",
                              }}
                              className="iconfont icon-bianji"
                              onMouseEnter={handleMouseEnter}
                              onMouseLeave={handleMouseLeave}
                            ></i>
                          </AuthTooltip>
                        </Popover>
                      )}
                  </Form.Item>
                }



              </Col>
              {
                  auth.project.info.type == 3 && auth.env.cohorts[0]?.id !== cohortId &&
                  showBaseCohort &&
                  <>
                    <Col>
                      <Divider type="vertical" style={{border: "0.1px solid #C8C9CC", marginTop:8,marginLeft:16 }} />
                    </Col>


                    <Col style={{width:500}}>

                      <Form.Item
                          style={{
                            marginBottom: 0,
                            marginLeft: 12,
                          }}
                          label={formatMessage({
                            id: "common.stage.name", allowComponent: true
                          })}
                      >
                        {
                          cohortSelect
                        }
                        {

                          <Popover
                              placement="bottom"
                              open={cohortOpen}
                              title={
                                <div>
                                  {formatMessage({
                                    id: "common.modify", allowComponent: true
                                  })}
                                  <AuthButton
                                      onClick={hide}
                                      type="text"
                                      icon={
                                        <CloseOutlined
                                            style={{
                                              color: "#666666",
                                            }}
                                        />
                                      }
                                      style={{
                                        float: "right",
                                        marginRight: -4,
                                        width: "15px",
                                        height: "15px",
                                      }}
                                  />
                                </div>
                              }
                              content={
                                <div
                                    style={{
                                      width: "288px",
                                      height: "116px",
                                    }}
                                >
                                  <Form layout={"vertical"} form={form}>
                                    <Form.Item
                                        name="baseCohort"
                                        rules={[
                                          {
                                            required: true,
                                          },
                                        ]}
                                        // label={formatMessage({id:"visit.cycle.type"})}
                                        label={
                                          <span
                                              style={{
                                                fontFamily: "PingFang SC",
                                                fontSize: "14px",
                                                fontWeight: 400,
                                                lineHeight: "20px",
                                                letterSpacing: "0px",
                                                textAlign: "left",
                                                color: "#677283",
                                              }}
                                          >
                                      {formatMessage({
                                        id: "common.stage.name", allowComponent: true
                                      })}
                                    </span>
                                        }
                                    >
                                      <Select>
                                        {
                                          auth.env.cohorts.map((item:any)=>
                                              <Select.Option key={item.id} value={item.id}>{item.name}</Select.Option>
                                          )
                                        }
                                      </Select>
                                    </Form.Item>
                                  </Form>
                                  <AuthButton
                                      onClick={update_visit_base_cohort}
                                      type={"primary"}
                                      className="full-width"
                                  >
                                    {formatMessage({
                                      id: "common.ok", allowComponent: true
                                    })}
                                  </AuthButton>
                                </div>
                              }
                              style={{
                                height: "180px",
                                width: "260px !important",
                                left: "496px",
                                top: "155px",
                                borderRadius: "2px",
                                marginTop: "16px",
                                marginLeft: "12px",
                              }}
                              trigger="click"
                              onOpenChange={handleCohortOpenChange}
                          >
                            <Tooltip
                                title={formatMessage({
                                  id: "common.modify", allowComponent: true
                                })}
                            >
                              <i
                                  style={{
                                    marginLeft: 8,
                                    cursor: "pointer",
                                    color: iconColor === 0 ? "#999999" : "#165DFF",
                                  }}
                                  className="iconfont icon-bianji"
                                  onMouseEnter={handleCohortMouseEnter}
                                  onMouseLeave={handleCohortMouseLeave}
                              ></i>
                            </Tooltip>
                          </Popover>
                        }
                      </Form.Item>
                    </Col>
                  </>
              }
            </Row>
          </Col>

          <Col>
            {permissions(
              auth.project.permissions,
              "operation.build.medicine.visit.push.record"
            ) &&
              !lockConfig &&
              projectStatus !== 2 && (
                <AuthButton
                  type={"link"}
                  onClick={() => {
                    view_visit();
                  }}
                  style={{ marginRight: -3 }}
                >
                  <span>
                    {intl.formatMessage({
                      id: "visit.cycle.push.record", allowComponent: true
                    })}
                  </span>
                </AuthButton>
              )}

            {permissionsCohort(
              auth.project.permissions,
              "operation.build.medicine.visit.push",
              props.cohort?.status
            ) &&
              !lockConfig &&
              projectStatus !== 2 && (
                <Tooltip
                  zIndex={2}
                  align={{ offset: [0, 5] }}
                  getPopupContainer={(trigger) =>
                    trigger.parentNode as HTMLElement
                  }
                  title={
                    drug.canPush ? (
                      ""
                    ) : (
                      <span>
                        <img src={Bell} alt="" width={18} />
                        {intl.formatMessage({
                          id: "visit.cycle.management.is.group.visit.save.release.unpublished", allowComponent: true
                        })}
                      </span>
                    )
                  }
                  placement="topRight"
                  open={!drug.canPush}
                  overlayClassName="custom-tooltip"
                >
                  <AuthButton
                    disabled={drug.canPush}
                    onClick={() => {
                      push_visit();
                    }}
                    style={{ marginRight: 12 }}
                  >
                    <span>
                      {intl.formatMessage({
                        id: "visit.cycle.push", allowComponent: true
                      })}
                    </span>
                  </AuthButton>
                </Tooltip>
              )}
            {permissionsCohort(
              auth.project.permissions,
              "operation.build.medicine.visit.setting.list",
              props.cohort?.status
            ) &&
              !lockConfig &&
              projectStatus !== 2 && (
                <AuthButton
                  onClick={() => {
                    setting();
                  }}
                  // type={"primary"}
                >
                  <span>
                    {intl.formatMessage({
                      id: "common.setting", allowComponent: true
                    })}
                  </span>
                </AuthButton>
              )}
            {permissionsCohort(
              auth.project.permissions,
              "operation.build.medicine.visit.add",
              props.cohort?.status
            ) &&
              !lockConfig &&
              projectStatus !== 2 && (
                <AuthButton
                  style={{marginLeft:"16px"}}
                  onClick={() => {
                    add();
                  }}
                  type={"primary"}
                >
                  <span>
                    {intl.formatMessage({
                      id: "common.add", allowComponent: true
                    })}
                  </span>
                </AuthButton>
              )}
          </Col>
        </Row>
        <TableSortable
          columns={columns}
          dataSource={drug.data || []}
          rowKey="id"
          onDragEnd={clickFilter(onDragEnd)}
          onDragStart={() => {}}
        >
          <Table
            dataSource={drug.data || []}
            scroll={{ x: "max-content" }}
            loading={false}
            columns={columns}
            components={{
              body: {
                row: (props: any) => (
                  <TableSortableRow
                    disabled={
                      !(
                        permissionsCohort(
                          auth.project.permissions,
                          "operation.build.medicine.visit.drag",
                          props.cohort?.status
                        ) &&
                        !lockConfig &&
                        projectStatus !== 2
                      )
                    }
                    {...props}
                  />
                ),
              },
            }}
            rowKey={(record: any) => record.id}
            pagination={false}
          />
        </TableSortable>
      </Spin>
      <VisitSetting bind={visit_setting} refresh={getList} cohort={props.cohort}/>
      <VisitAdd bind={visit_add} refresh={getList} cohort={props.cohort} />
      <VisitView refresh={getList} cohort={props.cohort} />
      <VisitRecord cohort={props.cohort} />
      <Modal
        open={drug.visitTypeView}
        title={formatMessage({ id: "common.modify" })}
        footer={null}
        width={260}
        onCancel={() => {
          drug.setVisitTypeView(false);
        }}
        destroyOnClose={true}
      >
        <Form layout={"vertical"} form={form}>
          <Form.Item
            name="visit_type"
            rules={[{ required: true }]}
            label={formatMessage({ id: "visit.cycle.type" })}
          >
            <Select>
              <Select value={0}>Baseline</Select>
              <Select value={1}>Lastdate</Select>
            </Select>
          </Form.Item>
        </Form>
        <AuthButton
          onClick={update_visit_type}
          type={"primary"}
          className="full-width"
        >
          {formatMessage({ id: "common.ok", allowComponent: true })}
        </AuthButton>
      </Modal>
    </React.Fragment>
  );
};

const VisitRecord = (props: any) => {
  const auth = useAuth();
  const timeZone =
    (auth.project.info.timeZoneStr !== undefined && auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "") ? Number(auth.project.info.timeZoneStr) : 8;
  const [optionVersion, setOptionVersion]: any = useSafeState([]);
  const drug = useDrug();
  const { formatMessage } = useTranslation();
  const { RangePicker }: any = DatePicker;

  const envId = auth.env ? auth.env.id : null;
  const cohortId = props.cohort ? props.cohort.id : null;

  const view = (record: any) => {
    drug.setVisitView(true);
    drug.setPushViewType(1);
    drug.setHistoryData(record.infos);
  };
  const [form] = Form.useForm();

  function formatTimezoneOffset(offset: any) {
      const negative = offset < 0;
      offset = Math.abs(offset);
      const hours = Math.floor(offset);
      const minutes = Math.round((offset - hours) * 60); // 四舍五入处理 .5

      const sign = negative ? "-" : "";
      const hh = String(hours).padStart(2, '0');
      const mm = String(minutes).padStart(2, '0');

      return `${sign}${hh}:${mm}`;
  }

  const columns: any = [
    {
      title: <FormattedMessage id={"common.serial"} />,
      dataIndex: "serial",
      key: "serial",
      width: 70,
      render: (val: any, record: any, index: number) => index + 1,
    },
    {
      title: <FormattedMessage id={"visit.cycle.version.number"} />,
      dataIndex: "version",
      key: "version",
      width: 200,
      render: (value: any) => (value !== "" ? value : "-"),
    },
    {
      title: <FormattedMessage id={"visit.cycle.push.date"} />,
      dataIndex: "updatedAt",
      key: "updatedAt",
      render: (value: any, record: any, index: any) =>
        value === null || value === 0
          ? "-"
          : moment
              .unix(value)
              .utc()
              .add(timeZone, "hour")
              .format("YYYY-MM-DD HH:mm:ss") +
            (timeZone >= 0 ? "(UTC+" : "(UTC") +
            formatTimezoneOffset(timeZone) +
            ")",
    },
    {
      title: <FormattedMessage id={"common.operation"} />,
      key: "operation",
      fixed: "right",
      width: 70,
      render: (value: any, record: any, index: any) => (
        <>
          <AuthButton
            style={{ paddingLeft: 0 }}
            type={"link"}
            onClick={() => {
              view(record);
            }}
          >
            <FormattedMessage id={"common.view"} />
          </AuthButton>
        </>
      ),
    },
  ];

  const page = usePage();

  const hide = () => {
    form.resetFields();
    drug.setPushViewType(0);
    drug.setRecordVisible(false);
    setOptionVersion([]);
  };
  const { runAsync: visitHistoryRun, loading } = useFetch(visitHistory, {
    manual: true,
  });

  const getVersionHistory = () => {
    if (drug.recordVisible) {
      form.validateFields().then((value: any) => {
        value.start = (page.currentPage - 1) * page.pageSize;
        value.limit = page.pageSize;
        if (value.date) {
          const startDate: any = new Date(value.date[0]); // 起始日期
          startDate.setHours(0, 0, 0, 0); // 设置时间为午夜（00:00:00）
          const startTimeStamp = Date.parse(startDate);

          const endDate: any = new Date(value.date[1]); // 起始日期
          endDate.setHours(23, 59, 59, 59); // 设置时间为午夜（00:00:00）
          const endTimeStamp = Date.parse(endDate);

          value.date = [startTimeStamp / 1000, endTimeStamp / 1000];
        }
        visitHistoryRun({ ...value, envId, cohortId }).then((value: any) => {
          page.setTotal(value.data.total);
          page.setData(value.data.data);
          if (optionVersion.length === 0) {
            let options: any = [];
            value.data?.data?.map((it: any) => {
              if (!options.find((item: any) => item.value === it.version)) {
                return options.push({
                  label: it.version !== "" ? it.version : "-",
                  value: it.version,
                });
              }
            });
            setOptionVersion(options);
          }
        });
      });
    }
  };
  React.useEffect(getVersionHistory, [
    drug.recordVisible,
    page.currentPage,
    page.pageSize,
  ]);
  return (
    <Modal
      zIndex={800}
      open={drug.recordVisible}
      className={"custom-medium-modal"}
      title={formatMessage({ id: "visit.cycle.push.record", allowComponent: true })}
      centered
      footer={null}
      destroyOnClose={true}
      onCancel={hide}
      maskClosable={false}
    >
      <Form form={form}>
        <Row>
          <Form.Item
            name={"date"}
            label={
              <span style={{ color: "#677283" }}>
                {formatMessage({ id: "visit.cycle.push.date" })}
              </span>
            }
          >
            <RangePicker format="YYYY-MM-DD" onChange={getVersionHistory} />
          </Form.Item>
          <Form.Item
            name={"version"}
            style={{ marginLeft: 12, width: 300 }}
            label={
              <span style={{ color: "#677283" }}>
                {formatMessage({
                  id: "visit.cycle.version.number",
                })}
              </span>
            }
          >
            <Select
              allowClear
              onChange={getVersionHistory}
              options={optionVersion}
              placeholder={formatMessage({
                id: "placeholder.select.common",
              })}
            />
          </Form.Item>
        </Row>
      </Form>

      <Table
        rowKey={(record: any, index: any) => record.updatedAt}
        loading={loading}
        columns={columns}
        dataSource={page.data ? page.data : []}
        pagination={false}
      ></Table>
      <PaginationView />
    </Modal>
  );
};

const OpenDiv = styled.div`
  height: 20px;
  width: 34px;
  border-radius: 2px;
  font-family: PingFang SC;
  font-size: 12px;
  font-weight: 400;
  line-height: 17px;
  letter-spacing: 0px;
  text-align: center;
  background: #41cc821a;
  color: #41cc82;
`;
const CloseDiv = styled.div`
  height: 20px;
  width: 34px;
  border-radius: 2px;
  font-family: PingFang SC;
  font-size: 12px;
  font-weight: 400;
  line-height: 17px;
  letter-spacing: 0px;
  text-align: center;
  background: #f969641a;
  color: #f96964;
`;
