import React, { useState } from 'react';
import styled from "@emotion/styled";
import { Table, Badge, } from "antd";
import { useEffect } from "react";
import { useIntl } from "react-intl";
import { useCacheTableBatch } from "../../../../hooks/cache-rowSpan-table-batch";
import { PaginationView } from "../../../../components/pagination_batch";
import { medicineStatusColors } from "../../../../data/data";
import { useAuth } from "../../../../context/auth";
import { medicineStatusData } from "../../../../tools/medicine_status";
import { useSafeState } from "ahooks";
import _ from 'lodash';
import { usePage } from "../../../../context/page";
import { useDrug } from "./context";


interface ProjectNoticeInterface {
  data: any;
  selectedRowKeys: any;
  selectedNumber: any;
  selectedPackageNumber: any;
  selectedPackageTotal: any;
  isExistsPackageNumber: any;
  selectedTotal: any;
  medicineListOpen: any;
}

export const MedicineDataView = (props: ProjectNoticeInterface) => {
  const tableData = useCacheTableBatch({ dataSource: props.data });
  const intl = useIntl();
  const { formatMessage } = intl;
  const auth = useAuth();
  const ud = useDrug();
  
  const [selectedRowKeys, setSelectedRowKeys] = useSafeState<any>(props.selectedRowKeys);
  const [selectedNumber, setSelectedNumber] = useSafeState(props.selectedNumber);
  const [selectedPackageNumber, setSelectedPackageNumber] = useSafeState((props.data ? props.data : []).length);
  const [selectedData, setSelectedData] = useSafeState<any>(props.isExistsPackageNumber);

  const [isExistsPackageNumber, setIsExistsPackageNumber] = useSafeState(false); //是否存在包装

  const [groupCount, setGroupCount] = useSafeState<any>([]);



  function renderStatus(value: any, shortCode: any) {
    let code_rule = 1; // 自动编码
    if (shortCode === "") {
        code_rule = 0; // 手动上传
    }
    // @ts-ignore
    return (
        <Badge
            color={medicineStatusColors[value]}
            text={
                medicineStatusData(
                    code_rule,
                    auth.project.info.research_attribute
                ).find((it: any) => it.value === value)?.label ?? "-"
            }
        />
    );
  }

  const handleRowSelectionChange = (srk: any, sr: any) => {
    let old = _.cloneDeep(selectedRowKeys);
    let idList = srk;
    if (!isExistsPackageNumber) {
        const deleteDifference = old.filter((item: any) => !srk.includes(item));
        const addDifference = srk.filter((item: any) => !old.includes(item));

        let did: any[] = [];
        let aid: any[] = [];
        if (tableData !== null && tableData.length > 0) {
            for (const item of tableData) {
                // eslint-disable-next-line no-loop-func
                deleteDifference.forEach((it: any) => {
                    if (item.id === it) {
                      if (!did.includes(item.id)) {
                        did.push(item.id);
                      }
                    }
                });
                // eslint-disable-next-line no-loop-func
                addDifference.forEach((it: any) => {
                    if (item.id === it) {
                      if (!aid.includes(item.id)) {
                        aid.push(item.id);
                      }
                    }
                });
            }
        }

        let dpnId: any[] = [];
        if (old !== null && old.length > 0) {
            if (did !== null && did.length > 0) {
                let list = old.filter((obj: any) => !did.includes(obj));
                dpnId = list;
            } else {
                let list = Array.from(new Set(dpnId.concat(old)));
                dpnId = list;
            }
        }

        if (aid !== null && aid.length > 0) {
          aid.forEach((element: any) => {
            dpnId.push(element);
          });
        }

        setGroupCount(Array.from(dpnId));
        setSelectedRowKeys(dpnId);

        idList = dpnId;

    } else {
        const deleteDifference = old.filter((item: any) => !srk.includes(item));
        const addDifference = srk.filter((item: any) => !old.includes(item));

        let dpn: any[] = [];
        let did: any[] = [];
        let apn: any[] = [];
        let aid: any[] = [];
        if (tableData !== null && tableData.length > 0) {
            for (const item of tableData) {
                // eslint-disable-next-line no-loop-func
                deleteDifference.forEach((it: any) => {
                    if (item.id === it) {
                        if (item.packageNumber !== null && item.packageNumber !== undefined && item.packageNumber !== "" && item.packageNumber !== "-") {
                            if (!dpn.includes(item.packageNumber)) {
                                dpn.push(item.packageNumber);
                            }
                        } else {
                            if (!did.includes(item.id)) {
                                did.push(item.id);
                            }
                        }
                    }
                });
                // eslint-disable-next-line no-loop-func
                addDifference.forEach((it: any) => {
                    if (item.id === it) {
                        if (item.packageNumber !== null && item.packageNumber !== undefined && item.packageNumber !== "" && item.packageNumber !== "-") {
                            if (!apn.includes(item.packageNumber)) {
                                apn.push(item.packageNumber);
                            }
                        } else {
                            if (!aid.includes(item.id)) {
                                aid.push(item.id);
                            }
                        }
                    }
                });
            }
        }

        if (props.data !== null && props.data.length > 0) {
          let d = props.data.filter((obj: any) => ((obj.packageNumber !== null && obj.packageNumber !== undefined && obj.packageNumber !== "" && obj.packageNumber !== "-" && dpn.includes(obj.packageNumber)))).map((obj: any) => obj.id);
          did = Array.from(new Set(did.concat(d)));

          let a = props.data.filter((obj: any) => ((obj.packageNumber !== null && obj.packageNumber !== undefined && obj.packageNumber !== "" && obj.packageNumber !== "-" && apn.includes(obj.packageNumber)))).map((obj: any) => obj.id);
          aid = Array.from(new Set(aid.concat(a)));

        }

        let idArr: any[] = [];
        if (old !== null && old.length > 0) {
            if (did !== null && did.length > 0) {
                let list = old.filter((obj: any) => !did.includes(obj));
                idArr = list;
            } else {
                let list = Array.from(new Set(idArr.concat(old)));
                idArr = list;
            }
        }

        if (aid !== null && aid.length > 0) {
          aid.forEach((element: any) => {
            idArr.push(element);
          });
        }

        setGroupCount(Array.from(idArr));
        setSelectedRowKeys(idArr);

        idList = idArr;
        
    }

    ud.setSelectedRowKeys(idList);

    let sn = 0;
    let pn: any[] = [];
    if (props.data !== null && props.data.length > 0) {
        for (const item of props.data) {
            // eslint-disable-next-line no-loop-func
            idList.forEach((it: any) => {
                if (item.id === it) {
                    if (item.packageNumber !== null && item.packageNumber !== undefined && item.packageNumber !== "" && item.packageNumber !== "-") {
                        if (!pn.includes(item.packageNumber)) {
                            pn.push(item.packageNumber);
                            sn = sn + 1;
                        }
                    } else {
                        sn = sn + 1;
                    }
                }
            });
        }
    }
    setSelectedNumber(idList.length);
    setSelectedPackageNumber(sn);

  };


  const refresh = () => {
    setSelectedPackageNumber(0);
    setSelectedNumber(0);
    setSelectedRowKeys([]);
    ud.setSelectedRowKeys([]);
  };

  useEffect(() => {
    if(props.medicineListOpen){
      ud.setSelectedRowKeys(props.selectedRowKeys);
      setSelectedRowKeys(props.selectedRowKeys);
      setSelectedNumber((props.selectedRowKeys).length);
      setSelectedPackageNumber(props.selectedPackageNumber);
      setIsExistsPackageNumber(props.isExistsPackageNumber);
    }
  }, [props.medicineListOpen]);

  // useEffect(() => {
  // }, []);

  return (
    <Wrap>
      <Table
        className="mar-top-10"
        size="small"
        // scroll={{
        //   y: `calc(100vh - 409px - ${props.isHasTip ? "48px" : "0px"} - ${props.isHasOtherDataSource ? "90px" : "68px"
        //     })`,
        // }}
        pagination={false}
        rowKey={(record) => record.id}
        dataSource={tableData}
        rowSelection={{
          // ...props.rowSelection,
          // rowSelection,
          // selections:false,
          // type:"checkbox",
          selectedRowKeys,
          onChange: handleRowSelectionChange,
          renderCell: (
            checked: any,
            record: any,
            index: any,
            originNode: any,
          ) => {
            return {
              children: originNode,
              props: {
                rowSpan:
                tableData[index] !== undefined
                    ? tableData[index]["packageNumberRowSpan"]
                    : 1,
              },
            };
          },
        }}
      >
        {(
          <Table.Column
            title={formatMessage({ id: "drug.medicine.packlist" })}
            dataIndex={"packageNumber"}
            key="packageNumber"
            ellipsis
            width={130}
            // onCell={(_, index) => {
            //     return handlerRowSpan(tableData, "packageNumber", index)
            // }}
            render={(value: any, record: any, index: any) => {
              let newValue = "";
              if (value !== undefined && value !== "") {
                newValue = value;
              } else {
                newValue = "-";
              }
              let obj = {
                children: newValue,
                props: {
                  rowSpan: tableData[index] !== undefined
                    ? tableData[index].packageNumberRowSpan
                    : 1,
                  // rowSpan: medicineTableData[index].packageNumberRowSpan,
                },
              };
              return obj;
            }}
          />
        )}
        <Table.Column
          width={200}
          title={formatMessage({ id: "drug.list.serialNumber" })}
          dataIndex={"serialNumber"}
          key="serialNumber"
          ellipsis
        />
        <Table.Column
          width={200}
          title={formatMessage({ id: "drug.list.drugNumber" })}
          dataIndex={"number"}
          key="number"
          ellipsis
        />
        <Table.Column
          width={150}
          title={formatMessage({ id: "drug.list.status" })}
          dataIndex={"status"}
          key="status"
          ellipsis
          render={(value: any, record: any, index: any) =>
            renderStatus(value, record.shortCode)
          }
        />
      </Table>
      {/* <PaginationView /> */}
      <PaginationView
          mode={"SELECTABLE"}
          // mode={undefined}
          selectedNumber={selectedNumber}
          selectedPackageNumber={selectedPackageNumber}
          isExistsPackageNumber={props.isExistsPackageNumber}
          selectedPackageTotal={props.selectedPackageTotal}
          selectedTotal={props.selectedTotal}
          clearDisplay={true}
          refresh={refresh}
      />
    </Wrap>
  );
};

const Wrap = styled.div`
  .control-wrap {
    margin-left: 8px;
    display: flex;
    align-items: center;

    .line {
      background: #e0e1e2;
      width: 1px;
      height: 40px;
    }

    .control-items {
      margin-left: 16px;
    }
  }
  .ant-checkbox-wrapper {
    line-height: 32px;
  }
`;
