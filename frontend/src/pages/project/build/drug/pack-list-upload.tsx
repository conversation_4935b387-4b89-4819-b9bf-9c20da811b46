import React from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {Form, message, Modal} from "antd";
import {useAuth} from "../../../../context/auth";
import {useSafeState} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {downloadPacklistTemplate, uploadMedicinesPacklist} from "../../../../api/medicine";
import {SingleUpload} from "components/SingleUpload";
import {useGlobal} from "../../../../context/global";



export const MedicinePacklistUpload = (props: any) => {
    const singleUpload = React.useRef<any>(null) 

    const g = useGlobal()
    const auth = useAuth()
    const intl = useIntl();
    const {formatMessage} = intl;

    const [visible, setVisible] = useSafeState<any>(false);

    const [form] = Form.useForm();
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const customerId: any = auth.customerId;


    const {
        runAsync: uploadMedicinesPackListRun,
        loading: uploadMedicinesPackListLoading
    } = useFetch(uploadMedicinesPacklist, {manual: true})
    const { runAsync: downloadPacklistTemplateRun } = useFetch(downloadPacklistTemplate, { manual: true })

    const show = () => {
        setVisible(true);
    };

    const hide = () => {
        setVisible(false);
        form.resetFields();
        singleUpload.current.reset()
    };

    const formData = () => {
        const form_data = new FormData();
        form_data.append('envId', envId);
        form_data.append('projectId', projectId);
        form_data.append('customerId', customerId);
        form_data.append('file', singleUpload.current.originFile);
        return form_data;
    }

    const save = () => {
        if (!singleUpload.current.originFile) {
            message.error(formatMessage({id: 'projects.randomization.confirmUpload'}));
            return false;
        }
        form.validateFields().then(() => {
            uploadMedicinesPackListRun(formData()).then(
                (resp:any) => {
                    message.success(resp.msg)
                    props.refresh();
                    hide();
                }
            )
        }).catch(() => {
        })
    };

    React.useImperativeHandle(props.bind, () => ({show}));

    return (
        <React.Fragment>
            <Modal
                title={<FormattedMessage id={"drug.medicine.packlist.upload"}/>}
                visible={visible}
                onCancel={hide}
                
                maskClosable={false}
                centered
                destroyOnClose
                className='custom-small-modal'
                okButtonProps={{loading: uploadMedicinesPackListLoading}}
                onOk={save}
                okText={formatMessage({id: 'common.ok'})}
            >
                <Form form={form} layout="horizontal">
                    <Form.Item label={formatMessage({id: 'common.upload'})} name="file" required>
                        <SingleUpload
                            bind={singleUpload}
                            tips={
                                <div style={{fontSize: 12, color: '#ADB2BA'}} onClick={e => e.stopPropagation()}>
                                    <span>{formatMessage({id: 'common.upload.excel.tip'})}{g.lang === 'zh' ? ', ' : ' '}</span><a onClick={() => downloadPacklistTemplateRun().then()}>{formatMessage({ id: 'common.download.template' })}</a>{g.lang === 'zh' ? '。' : '.'}
                                </div>
                            }
                            accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                            width={485}
                        />
                    </Form.Item>
                </Form>
            </Modal>
        </React.Fragment>
    )
};