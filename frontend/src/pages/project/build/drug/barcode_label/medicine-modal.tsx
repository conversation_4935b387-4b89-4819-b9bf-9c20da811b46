import React, { useEffect, useImperative<PERSON>andle } from "react"
import { Button, Col, Modal, Row, Input, Select } from "antd";
import { Title } from "../../../../../components/title";
import { MedicineDataView } from "./medicine-data";
import { useIntl } from "react-intl";
import { useSafeState } from "ahooks";
import { useDrug } from "../context";
import { useFetch } from "../../../../../hooks/request";
import { getBarcodeTask } from "../../../../../api/barcode"
import { useAuth } from "../../../../../context/auth";

export const MedicineSelectModal = (props: any) => {
    const { formatMessage } = useIntl()
    const auth = useAuth()
    const [open, setOpen] = useSafeState<boolean>(false)
    const ud = useDrug()
    const [selectedRowKeys, setSelectedRowKeys] = useSafeState<string[]>([])
    const [medicineData, setMedicineData] = useSafeState<any>([]);
    const [searchValue, setSearchValue] = useSafeState("");
    const [searchStatus, setSearchStatus] = useSafeState(null);
    // const envId = auth.env ? auth.env.id : null;


    useImperativeHandle(props.bind, () => ({ show }))

    const show = () => {
        // page.setCurrentPage(1)
        // page.setTotal(props.medicineData.length)
        setMedicineData(props.medicineData)
        setSelectedRowKeys(props.selectedRowKeys)
        ud.setSelectedRowKeys(props.selectedRowKeys)
        setOpen(true)
    }

    const onClose = () => {
        setMedicineData([]);
        setSearchStatus(null);
        setSearchValue("")
        setOpen(false)
    }
    const onConfirm = () => {
        props.onConfirm && props.onConfirm(ud.selectedRowKeys)
        setOpen(false)
    }


    // // 根据自动编码的任务ID,查询生成的研究产品
    // const { runAsync: getBarcodeTaskRun } = useFetch(getBarcodeTask, { manual: true })
    // const getBarcodeTaskData = () => {
    //     getBarcodeTaskRun({
    //         id: props.correlationId,
    //         envId: envId,
    //         roleId: auth.project.permissions.role_id,
    //         searchValue: searchValue,
    //         searchStatus: searchStatus,
    //     }).then((result: any) => {
    //         setMedicineData(result.data?.datas || [])
    //     })
    // }

    // useEffect(() => {
    //  getBarcodeTaskData();
    // }, [searchValue, searchStatus]);

    const getShowData = () => {
        if (!searchValue && searchStatus !== 0 && searchStatus !== 1) return medicineData
        return medicineData
            .filter((it: any) => !searchValue || it['number'].includes(searchValue))
            .filter((it: any) => (searchStatus !== 0 && searchStatus !== 1) || it['status'] === searchStatus)
    }

    return <Modal
        forceRender={true}
        className="custom-medium-modal-batch"
        style={{ width: "1200px" }}
        title={<span
            style={{
                fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                fontSize: "16px",
                fontWeight: 500,
                lineHeight: "24px",
                textAlign: "left",
            }}
        >
            {formatMessage({ id: "common.select" })}
        </span>}
        open={open}
        onCancel={onClose}
        destroyOnClose={true}
        centered
        maskClosable={false}
        footer={
            <Row justify="end">
                <Col style={{ marginRight: "16px" }}>
                    <Button onClick={onClose}>
                        {formatMessage({ id: "common.cancel" })}
                    </Button>
                </Col>
                <Col>
                    <Button
                        type={"primary"}
                        className="full-width"
                        onClick={onConfirm}
                    >
                        {formatMessage({ id: "common.ok" })}
                    </Button>
                </Col>
            </Row>
        }
    >
        <div>
            <div>
                <Title name={formatMessage({ id: "drug.medicine" })}></Title>
                <Row>
                    <div style={{ textAlign: "left", display: "inline-block", marginTop: "16px", marginBottom: "16px" }}>
                        <Col style={{ textAlign: "left" }}>
                            {
                                formatMessage({
                                    id: "drug.list.drugNumber",
                                }) + "："
                            }
                            <Input
                                placeholder={formatMessage({
                                    id: "common.required.prefix",
                                })}
                                onChange={(e) => {
                                    setSearchValue(e.target.value);
                                }}
                                value={searchValue}
                                style={{ width: 200, marginRight: 12 }}
                                allowClear
                            />
                            {
                                formatMessage({
                                    id: "common.status",
                                }) + "："
                            }
                            <Select placeholder={formatMessage({ id: 'placeholder.select.common' })}
                                onChange={(e) => {
                                    setSearchStatus(e);
                                }}
                                value={searchStatus}
                                allowClear
                                style={{ width: 200, marginRight: 12 }}
                            >
                                <Select.Option value={1}>{formatMessage({ id: "medicine.status.available" })}</Select.Option>
                                <Select.Option value={0}>{formatMessage({ id: "medicine.status.toBeWarehoused" })}</Select.Option>
                            </Select>
                        </Col>
                    </div>
                </Row>
                <MedicineDataView
                    data={getShowData()}
                    selectedRowKeys={selectedRowKeys}
                    selectedNumber={0}
                    selectedTotal={props.medicineData.length}
                    medicineListOpen={open}
                    selectedPackageNumber={0}
                    selectedPackageTotal={props.medicineData.length}
                    isExistsPackageNumber={false}
                />
            </div>
        </div>
    </Modal>
}