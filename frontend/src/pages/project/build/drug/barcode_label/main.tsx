import React, {useEffect, useRef} from "react"
import {FormattedMessage, useTranslation} from "../../../../common/multilingual/component"
import {Badge, Button, Dropdown, Input, message, Modal, Space, Spin, Table} from "antd";
import {useSafeState} from "ahooks";
import {BarcodeLabelAdd} from "./add";
import {PageContextProvider, usePage} from "context/page";
import {useFetch} from "../../../../../hooks/request";
import {getBarcodeLabelList, getPreviewBarcodeLabel, sendBarcodeLabel} from "../../../../../api/barcode_label";
import {useAuth} from "../../../../../context/auth";
import {PaginationView} from "../../../../../components/pagination_batch";
import {permissions} from "../../../../../tools/permission";
import {PreviewLabel, PreviewPrintLabel} from "./board/preview";
import {LabelExport} from "./board/label-export";
import {getBarcodeTask, getBarcodeTaskList} from "../../../../../api/barcode";
import {InfoCircleFilled, SearchOutlined} from "@ant-design/icons";
import {enrichFieldData} from "./board/util";
import {useAtom} from "jotai";
import {barcodeLabelLoadingAtom} from "./context";
import {AuthButton, AuthDropdown} from "../../../../common/auth-wrap";


export const BarcodeLabel = (props: any) => {

    const {formatMessage} = useTranslation()
    const auth = useAuth()
    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId
    const customerName = auth.project.customerName
    const projectId = auth.project.id;
    const pageCtx = usePage()

    const [search, setSearch] = useSafeState<any>({})

    const addRef = useRef<any>()
    const [data, setData] = useSafeState<any[]>([])
    const [allLabel, setAllLabel] = useSafeState<any[]>([])

    const [taskOption, setTaskOption] = useSafeState<any>([])
    const [allTask, setAllTask] = useSafeState<any>([])

    const previewRef = useRef<any>()
    const labelPreviewRef = useRef<any>()
    const labelExportRef = useRef<any>()

    // 加载状态
    const [labelLoading, setLabelLoading] = useAtom(barcodeLabelLoadingAtom)

    const codeMethodOption = [
        {value: 1, label: <FormattedMessage id={'barcode.label.codeManual'} />},
        {value: 2, label: <FormattedMessage id={'barcode.label.codeAuto'} />},
    ]
    const statusOption = [
        {value: 1, label: <FormattedMessage id={'common.effective'} />, color: 'rgba(65, 204, 130, 1)'},
        {value: 2, label: <FormattedMessage id={'common.invalid'} />, color: 'rgba(192, 196, 204, 1)'},
    ]

    useEffect(() => {
        setSearch({})
    }, [])


    useFetch(() => getBarcodeTaskList({customerId, projectId, envId}), {
        onSuccess: (resp: any) => {
            const data = resp.data?.datas || []
            const options = data.map((it: any) => ({
                label: it.correlationId,
                value: it.id
            }));
            setTaskOption(options)
        }
    })

    useFetch(() => getBarcodeTask({
        id: "", envId: envId, roleId: auth.project.permissions.role_id,
    }), {
        onSuccess: (resp: any) => {
            setAllTask(resp.data?.datas || [])
        }
    })


    const columns = [
        {
            key: '#', width: 60, title: <FormattedMessage id={"common.serial"}/>, ellipsis: true,
            render: (value: any, record: any, index: number) => (pageCtx.currentPage - 1) * pageCtx.pageSize + index + 1
        },
        {key: 'labelNumber', dataIndex: 'labelNumber', width: 120, title: <FormattedMessage id={"barcode.label.number"}/>, ellipsis: true},
        {
            key: 'codeMethod',
            dataIndex: 'codeMethod',
            width: 120,
            title: <FormattedMessage id={"projects.attributes.code.config.method"}/>,
            ellipsis: true,
            render: (text: number) => codeMethodOption.find(it => it.value === text)?.label || '-'
        },
        {
            key: 'correlationName',
            dataIndex: 'correlationName',
            width: 120,
            title: <FormattedMessage id={"barcode.taskIDs"}/>,
            ellipsis: true,
            render: (text: string) => text || '-'
        },
        {
            key: 'productCount',
            dataIndex: 'productCount',
            width: 120,
            title: <FormattedMessage id={"barcode.label.printCount"}/>,
            ellipsis: true,
            render: (text: number, record: any) => text
        },
        {
            key: 'status',
            dataIndex: 'status',
            width: 120,
            title: <FormattedMessage id={"common.status"}/>,
            render: (text: any) => {
                const op = statusOption.find(it => it.value === text)
                return <Badge text={op?.label} color={op?.color} />
            }
        },
        {
            key: 'operator', width: 120, title: <FormattedMessage id={"common.operation"}/>,
            render: (_: any, record: any) => <Space size={'middle'}>
                {permissions(auth.project.permissions, "operation.build.medicine.barcode_label.preview") && <AuthDropdown
                    menu={{items: [
                        {key: '1', label: <FormattedMessage id={'barcode.label.preview'} />, onClick: () => previewLabel(record.id)},
                        {key: '2', label: <FormattedMessage id={'barcode.label.printPreview'} />, onClick: () => preview(record.id)},
                        /*{key: '3', label: <FormattedMessage id={'common.export'} />, onClick: () => exportToExcel(record.id)}*/
                    ]}}
                >
                    <div>
                        <AuthButton type={'link'} style={{padding: '4px 0'}}>
                            <FormattedMessage id={'form.preview'} />
                        </AuthButton>
                    </div>
                </AuthDropdown>}
                {permissions(auth.project.permissions, "operation.build.medicine.barcode_label.send") && record.sendMeta?.status !== 1 && <AuthButton
                    type={'link'} style={{padding: '4px 0'}}
                    onClick={() => send(record.id)}
                >
                    <FormattedMessage id={'barcode.label.send'} />
                </AuthButton>}
            </Space>
        },
    ]

    const {run: getData, loading} = useFetch(() => getBarcodeLabelList({
            ...search,
            envId, projectId, customerId, cohortId: "",
            skip: (pageCtx.currentPage - 1) * pageCtx.pageSize, limit: pageCtx.pageSize
        }), {
        refreshDeps: [pageCtx.currentPage, pageCtx.pageSize, search],
        onSuccess: ((resp: any) => {
            if (resp && resp.code === 0) {
                const data = resp.data
                setData(data.data || [])
                setAllLabel(data.allLabel || [])
                pageCtx.setTotal(data.total || 0)
            }
        })
    })

    const {runAsync: getPreviewData, loading: previewLoading} = useFetch((id) => getPreviewBarcodeLabel(id, {
        envId, projectId, customerId, cohortId: ""
    }), {manual: true})

    const {runAsync: sendLabel, loading: sendLoading} = useFetch((id) => sendBarcodeLabel(id, {
        envId, projectId, customerId, cohortId: ""
    }), {manual: true})

    const add = () => {
        addRef?.current?.show()
    }

    const preview = (id: string) => {
        getPreviewData(id).then((resp: any) => {
            if (resp && resp.code === 0) {
                const {fields, printSize, labelSize} = resp.data.template
                const productList = resp.data.productList
                const printData = enrichFieldData(fields, productList, customerName)
                const printInfo = {printSize: printSize, labelSize: labelSize}
                setLabelLoading(true)
                previewRef.current?.preview(printData, printInfo)
            }
        })
    }

    const previewLabel = (id: string) => {
        getPreviewData(id).then((resp: any) => {
            if (resp && resp.code === 0) {
                const template = resp.data.template
                labelPreviewRef?.current?.show(template.fields, template.labelSize)
            }
        })
    }

    const exportToExcel = (id: string) => {
        getPreviewData(id).then((resp: any) => {
            if (resp && resp.code === 0) {
                const {fields, printSize, labelSize} = resp.data.template
                const productList = resp.data.productList
                const printData = enrichFieldData(fields, productList, customerName)
                const printInfo = {printSize: printSize, labelSize: labelSize}
                labelExportRef.current?.show(printData, printInfo)
            }
        })
    }

    const send = (id: string) => {
        Modal.confirm({
            centered: true,
            title: formatMessage({id: "barcode.label.send.confirm"}),
            icon: <InfoCircleFilled style={{color: "#FFAE00"}}/>,
            okText: formatMessage({id: 'common.ok'}),
            cancelText: formatMessage({id: 'common.cancel'}),
            onOk: () => {
                sendLabel(id).then((resp: any) => {
                    if (resp && resp.code === 0) {
                        message.success(formatMessage({id: 'barcode.label.sendSuccess'}))
                        getData()
                    }
                })
            }
        })
    }


    return <React.Fragment>
        <Spin spinning={loading || sendLoading || previewLoading || labelLoading}>
            <div style={{display: 'flex', alignItems: 'center', justifyContent: 'space-between', margin: '12px 0'}}>
                <Input
                    style={{width: '220px'}}
                    placeholder={formatMessage({id: 'common.required.prefix'}) + formatMessage({id: 'barcode.label.number'})}
                    value={search.labelNumber}
                    onChange={e => setSearch({...search, labelNumber: e.target.value})}
                    suffix={<SearchOutlined style={{ color: "#BFBFBF" }} />}
                />
                {permissions(auth.project.permissions, "operation.build.medicine.barcode_label.add") && <div>
                    <AuthButton
                        onClick={add}
                        type={"primary"}
                    >
                        <FormattedMessage id={'common.add'}/>
                    </AuthButton>
                </div>}
            </div>
            <Table
                dataSource={data}
                rowKey={(record: any) => (record.id)}
                columns={columns}
                pagination={false}
                scroll={{y: `calc(100vh - 292px)`}}
            />
            <PaginationView refresh={getData} />
        </Spin>
        <PageContextProvider>
            <BarcodeLabelAdd
                bind={addRef}
                refresh={getData}
                taskOption={taskOption}
                allTask={allTask}
                allLabel={allLabel}
            />
        </PageContextProvider>
        <PreviewPrintLabel bind={previewRef} />
        <PreviewLabel bind={labelPreviewRef} />
        <LabelExport bind={labelExportRef} />
    </React.Fragment>

}