import {Modal} from "antd";
import {FormattedMessage} from "../../../../../common/multilingual/component";
import React, {useEffect, useImperativeHandle, useRef} from "react";
import {useSafeState} from "ahooks";
import {useReactToPrint} from "react-to-print";
import './index.less'
import Draggable from "react-draggable";
import {mm2px} from "./util";
import {DragInput} from "./drag-input";
import {useAtom} from "jotai";
import {barcodeLabelLoadingAtom} from "../context";

export const PreviewLabel = (props: any) => {
    const [visible, setVisible] = useSafeState<boolean>(false)
    const [fields, setFields] = useSafeState<any[]>([])
    const [labelSize, setLabelSize] = useSafeState<any>({})

    useImperativeHandle(props.bind, () => ({show}))

    const show = (fields: any[], labelSize: any) => {
        setFields(fields)
        setLabelSize(labelSize)
        setVisible(true)
    }
    const hide = () => {
        setFields([])
        setVisible(false)
    }

    return <Modal
        open={visible}
        centered
        onCancel={hide}
        onOk={hide}
        title={<FormattedMessage id={'barcode.label.preview'}/>}
        closable
        forceRender
        className="custom-medium-modal"
        bodyStyle={{height: `calc(${labelSize?.height}mm + 48px)`, minHeight: '250px'}}
        maskClosable={false}
        footer={null}
    >
        <div style={{height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
            <LabelPreview fields={fields} labelSize={labelSize} />
        </div>
    </Modal>
}

export const PreviewPrintLabel = (props: any) => {
    const previewPrintRef = useRef<any>()
    const [visible, setVisible] = useSafeState<boolean>(false)

    useImperativeHandle(props.bind, () => ({preview}))

    const preview = (printData: any[], printInfo: any) => {
        setVisible(true)
        setTimeout(() => {previewPrintRef?.current?.print(printData, printInfo)}, 1)
        setTimeout(() => {setVisible(false)}, 200)
    }

    return <>
        {visible && <PreviewPrint bind={previewPrintRef} />}
    </>

}

const PreviewPrint = (props: any) => {
    const [contentRef, setContentRef] = useSafeState<any>()
    const [printData, setPrintData] = useSafeState<any[]>([])
    const [printInfo, setPrintInfo] = useSafeState<any>({printSize: {}, labelSize: {}})

    const [, setLabelLoading] = useAtom(barcodeLabelLoadingAtom)

    useEffect(() => {
        if (!printInfo.printSize?.width) return
        const printStyle = `
           @media print {
             @page {
               size: auto;
               margin: 1cm 0 1cm 0;
               width: ${printInfo.printSize.width}mm;
               height: ${printInfo.printSize.height}mm;
               @top-center {
                content: "";
               }
               @bottom-center {
                content: "";
               }
             }
           }
         `;
        const newStyleElement = document.createElement('style')
        newStyleElement.media = 'print'
        newStyleElement.appendChild(document.createTextNode(printStyle))
        document.head.appendChild(newStyleElement)
    }, [printInfo])

    const reactToPrintFn = useReactToPrint({
        content: () => contentRef,
        onBeforePrint: () => {
            setLabelLoading(false)
        },
        onAfterPrint: () => {
            setLabelLoading(false)
            removeMediaPrintStyle()
            setPrintData([])
        },
        onPrintError: () => {
            setLabelLoading(false)
        }
    })

    useImperativeHandle(props.bind, () => ({print}))

    const removeMediaPrintStyle = () => {
        // 销毁所有head中的print样式
        const printStyles = document.head.querySelectorAll('style[media="print"]')
        printStyles.forEach(style => document.head.removeChild(style))
    }

    const print = (printData: any[], printInfo: any) => {
        setPrintData([])
        setPrintInfo(printInfo)
        setTimeout(() => {
            setPrintData(printData)
            reactToPrintFn()
        }, 20)
    }

    return (
        <div style={{position: 'absolute', zIndex: -1, top: 0, height: 0, overflow: "auto"}}>
            <div ref={setContentRef}>
                <div style={{display: 'flex', flexWrap: 'wrap'}}>
                    {printData.map((fields, index) =>
                        <div key={index} style={{margin: '2px 0 3mm 3mm'}}>
                            <LabelPreview fields={fields} labelSize={printInfo.labelSize} />
                        </div>
                    )}
                </div>
            </div>
        </div>
    )
}

export const LabelPreview = (props: any) => {
    return <div className={'preview-print'}>
        <div className={'preview-label'}>
            <div
                className={'board-label'}
                style={{
                    position: 'relative',
                    width: `calc(${props.labelSize.width}mm)`,
                    height: `calc(${props.labelSize.height}mm)`,
                    border:"1px solid rgba(235, 237, 240, 1)",
                }}
            >
                {props.fields.map((it: any) =>
                    <Draggable
                        key={it.key}
                        bounds="parent"
                        defaultPosition={{x: mm2px(it.marginLeft), y: mm2px(it.marginTop)}}
                        disabled
                    >
                        <div
                            key={it.key}
                            data-key={it.key}
                            style={{position: 'absolute', width: `${it.width}mm`, height: `${it.height}mm`}}
                            className={'drag-block'}
                        >
                            <DragInput
                                field={it}
                                defaultSize={{width: it.width, height: it.height}}
                                range={{x: [0, 0], y: [0, 0]}}
                            />
                        </div>
                    </Draggable>
                )}
            </div>
        </div>
    </div>
}