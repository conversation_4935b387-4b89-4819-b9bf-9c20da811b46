import {AutoComplete, Col, Form, InputNumber, Radio, Row, Select} from "antd";
import React, {useEffect} from "react";
import {useDebounceFn, useSafeState} from "ahooks";
import {Align<PERSON>enterOutlined, AlignLeftOutlined, AlignRightOutlined} from "@ant-design/icons";
import {ColorPicker} from "@arco-design/web-react";
import {fontFamilyOptions, mm2px, px2mm} from "./util";
import {FormattedMessage} from "../../../../../common/multilingual/component";

export const FieldAttribute = (props: any) => {

    const [form] = Form.useForm()
    const [maxRange, setMaxRange] = useSafeState<any>({width: 0, height: 0, marginLeft: 0, marginTop: 0})
    const getField = () => {
        return {
            backgroundColor: '#FFF',
            ...props.fieldsInfo[props.fieldKey]
        }
    }

    useEffect(() => {
        const {x: rangeX, y: rangeY} = props.range
        const field = getField()
        if (!rangeX || !rangeY || !field) return
        setMaxRange({
            width: px2mm(rangeX[1]),
            height: px2mm(rangeY[1]),
            marginLeft: px2mm(rangeX[1] - mm2px(field.width)),
            marginTop: px2mm(rangeY[1] - mm2px(field.height)),
        })
    }, [props.range, props.fieldKey, props.fieldsInfo])

    useEffect(() => {
        if (!props.fieldKey) form.resetFields()
        form.setFieldsValue(getField() || {})
    }, [props.fieldKey, props.fieldsInfo])

    const onFormChange = (otherValues?: any) => {
        if (props.disabled) return
        form.validateFields().then(values => {
            props.onChange && props.onChange(props.fieldKey, {...values, ...(otherValues || {})})
        })
    }
    const {run: formChange} = useDebounceFn(() => {
        onFormChange()
    }, {wait: 200})

    const {run: colorChange} = useDebounceFn((key: string, color: string) => {
        onFormChange({[key]: color})
    }, {wait: 200})

    const {run: textAlignChange} = useDebounceFn((textAlign: string) => {
        onFormChange({textAlign: textAlign})
    }, {wait: 200})

    const manualSign: any = {width: 'manualWidth', height: 'manualHeight'}
    const {run: sizeChange} = useDebounceFn((label: string, size: number) => {
        const other = !manualSign[label] ? {} : {[manualSign[label]]: size}
        onFormChange({[label]: size, ...other})
    }, {wait: 200})

    const fontWeightChange = () => {
        const fontWeigh = form.getFieldValue('fontWeight') === 'bold' ? 'normal' : 'bold'
        form.setFieldValue('fontWeight', fontWeigh)
        form.validateFields().then(values => {
            props.onChange && props.onChange(props.fieldKey, {...values, fontWeigh: fontWeigh})
        })
    }

    const positionOptions = [
        {
            label: <FormattedMessage id={'barcode.label.attribute.textAlignLeft'}/>,
            value: 'left',
            icon: <AlignLeftOutlined/>
        },
        {
            label: <FormattedMessage id={'barcode.label.attribute.textAlignCenter'}/>,
            value: 'center',
            icon: <AlignCenterOutlined/>
        },
        {
            label: <FormattedMessage id={'barcode.label.attribute.textAlignRight'}/>,
            value: 'right',
            icon: <AlignRightOutlined/>
        },
    ]


    const disabledInput = () => {
        const field = getField() || {}
        // 条形码允许编辑
        if (field.type === 'barcode') return false
        return field.type !== 'input'
    }

    const fontTextAlign = () => {
        return getField()?.textAlign || 'left'
    }

    const getBindOptions = () => {
        if (!props.bindData || props.bindData.length === 0) return null
        const isBarcode = getField()?.type === 'barcode'
        const options = props.bindData.filter((it: any) => isBarcode ? it.key === 'barcodeNumber' : it.key !== 'barcodeNumber')
        return options.length > 0 ? options : null
    }


    return <Form form={form} onChange={formChange} disabled={props.disabled}>
        <Row gutter={12}>
            <Col span={24}>
                <Form.Item label={<FormattedMessage id={'barcode.label.attribute.text'}/>} name={'value'}>
                    <AutoComplete
                        disabled={disabledInput()}
                        options={getBindOptions()}
                        onChange={(value, option: any) => {
                            onFormChange({"bind": option.key})
                        }}
                    />
                </Form.Item>
            </Col>
            <Col span={12}>
                <div style={{display: 'flex'}}>
                    <Form.Item style={{flex: 1}} label={<FormattedMessage id={'barcode.label.attribute.fontFamily'}/>} name={'fontFamily'}>
                        <Select
                            options={fontFamilyOptions.map(it => ({...it, label: <FormattedMessage id={it.label} />}))}
                            onChange={formChange}
                        />
                    </Form.Item>
                    <Form.Item style={{margin: '0 8px'}} name={'fontSize'}>
                        <InputNumber
                            min={1}
                            style={{width: '60px'}}
                            onChange={formChange}
                            onKeyUp={e => e.stopPropagation()}
                        />
                    </Form.Item>
                    <Form.Item style={{marginRight: '8px'}} name={'fontWeight'}>
                        <Radio.Group defaultValue="bold" buttonStyle="solid">
                            <Radio.Button value="bold" onClick={fontWeightChange}>
                                B
                            </Radio.Button>
                        </Radio.Group>
                    </Form.Item>
                    <Form.Item name={'fontColor'}>
                        <ColorPicker
                            defaultValue={'#1D2129'}
                            disabled={props.disabled}
                            onChange={(color: string) => colorChange('fontColor', color)}
                            renderFooter={() => <div className={'drag-color-picker'}>
                                {['#1D2129', '#1677FF', '#13C2C2', '#FA541C', '#F5222D', '#FAAD14', '#A0D911'].map(it =>
                                    <div
                                        className={'drag-color-preset'}
                                        style={{backgroundColor: it}}
                                        onClick={() => colorChange('fontColor', it)}
                                    />
                                )}
                            </div>}
                        />
                    </Form.Item>
                </div>
            </Col>
            <Col span={12}>
                <div style={{display: 'flex'}}>
                    <Form.Item label={<FormattedMessage id={'barcode.label'}/>} name={'backgroundColor'}>
                        <ColorPicker
                            defaultValue={'#FFF'}
                            disabled={props.disabled}
                            onChange={(color: string) => colorChange('backgroundColor', color)}
                            renderFooter={() => <div className={'drag-color-picker'}>
                                {['#1D2129', '#1677FF', '#13C2C2', '#FA541C', '#F5222D', '#FAAD14', '#A0D911'].map(it =>
                                    <div
                                        className={'drag-color-preset'}
                                        style={{backgroundColor: it}}
                                        onClick={() => colorChange('backgroundColor', it)}
                                    />
                                )}
                            </div>}
                        />
                    </Form.Item>
                    <div style={{
                        display: 'flex', alignItems: 'center',
                        justifyContent: 'space-around', width: '80px', height: '32px',
                        fontSize: '12px', color: '#ADB2BA', marginLeft: '8px',
                    }}>
                        {positionOptions.map(it => <span
                            key={it.value}
                            className={it.value === fontTextAlign() ? 'position-icon-active' : 'position-icon'}
                            style={{cursor: props.disabled ? 'not-allowed' : 'pointer'}}
                            onClick={() => textAlignChange(it.value)}
                        >
                           {it.icon}
                        </span>)}
                    </div>
                </div>

            </Col>
            <Col span={12}>
                <Form.Item label={<FormattedMessage id={'barcode.label.attribute.width'}/>} name={'width'}>
                    <InputNumber
                        style={{width: '100%'}} addonAfter={'mm'}
                        min={0} max={maxRange.width}
                        onChange={v => sizeChange('width', v)}
                        onKeyUp={e => e.stopPropagation()}
                    />
                </Form.Item>
                <Form.Item hidden name={'manualWidth'}/>
            </Col>
            <Col span={12}>
                <Form.Item label={<FormattedMessage id={'barcode.label.attribute.height'}/>} name={'height'}>
                    <InputNumber
                        style={{width: '100%'}} addonAfter={'mm'}
                        min={0} max={maxRange.height}
                        onChange={v => sizeChange('height', v)}
                        onKeyUp={e => e.stopPropagation()}
                    />
                </Form.Item>
                <Form.Item hidden name={'manualHeight'}/>
            </Col>
            <Col span={12}>
                <Form.Item label={<FormattedMessage id={'barcode.label.attribute.marginTop'}/>} name={'marginTop'}>
                    <InputNumber
                        style={{width: '100%'}} addonAfter={'mm'}
                        min={0} max={maxRange.marginTop}
                        onChange={v => sizeChange('marginTop', v)}
                        onKeyUp={e => e.stopPropagation()}
                    />
                </Form.Item>
            </Col>
            <Col span={12}>
                <Form.Item label={<FormattedMessage id={'barcode.label.attribute.marginLeft'}/>} name={'marginLeft'}>
                    <InputNumber
                        style={{width: '100%'}} addonAfter={'mm'}
                        min={0} max={maxRange.marginLeft}
                        onChange={v => sizeChange('marginLeft', v)}
                        onKeyUp={e => e.stopPropagation()}
                    />
                </Form.Item>
            </Col>
        </Row>

    </Form>
}