
export const defaultConfig = {
    width: 25, height: 8.5, fontSize: 12, fontColor: '#1D2129',
    fontWeight: 'normal', fontFamily: 'MicrosoftYaHei', backgroundColor: '#FFFFFF',
    textAlign: 'left',
}

export const defaultTemplate = {
    printSize: {width: 210, height: 297},
    labelSize: {width: 200, height: 50},
    fields: [
        {
            "width": 46,
            "height": 10,
            "fontSize": 14,
            "fontColor": "#1D2129",
            "fontWeight": "bold",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "center",
            "key": "a6532ff9-1584-4071-85e7-0478b9eaf4af",
            "value": "甲氧那明胶囊Ⅲ期临床用药",
            "marginLeft": 0,
            "marginTop": 0,
            "type": "input",
            "fontWeigh": "bold",
            "bind": "productName",
        },
        {
            "width": 46,
            "height": 5.03,
            "fontSize": 12,
            "fontColor": "#A3003B",
            "fontWeight": "bold",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "center",
            "key": "5920c87b-6939-48c2-82ea-7d0347209c9f",
            "value": "仅供临床研究用",
            "marginLeft": 0,
            "marginTop": 12.96,
            "type": "input",
            "fontWeigh": "bold"
        },
        {
            "width": 46,
            "height": 10.05,
            "fontSize": 12,
            "fontColor": "#1D2129",
            "fontWeight": "normal",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "center",
            "key": "3b5b87d7-5b15-491f-ad67-2275616787cb",
            "value": "嘉兴易迪希医药科技有限公司",
            "marginLeft": 0,
            "marginTop": 20.37,
            "type": "input",
            "bind": "customerName",
        },
        {
            "width": 46,
            "height": 16,
            "fontSize": 12,
            "fontColor": "#1D2129",
            "fontWeight": "normal",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "center",
            "key": "05f06c36-2a85-453d-a427-c104e8b5af55",
            "value": "82892910198833",
            "marginLeft": 0,
            "marginTop": 33.07,
            "type": "barcode",
            "bind": "barcodeNumber"
        },
        {
            "width": 14,
            "height": 8.47,
            "fontSize": 12,
            "fontColor": "#1D2129",
            "fontWeight": "normal",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "left",
            "key": "83a12ae9-48ef-449b-85ba-d06b44291f37",
            "value": "方案号：",
            "marginLeft": 49.74,
            "marginTop": 4.76,
            "type": "input"
        },
        {
            "width": 23,
            "height": 10,
            "fontSize": 12,
            "fontColor": "#1D2129",
            "fontWeight": "normal",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "left",
            "key": "12cdbafe-d5cf-42d3-996e-3c4af73391ab",
            "value": "研究产品名称：",
            "marginLeft": 48.95,
            "marginTop": 15.08,
            "type": "input"
        },
        {
            "width": 14,
            "height": 8.47,
            "fontSize": 12,
            "fontColor": "#1D2129",
            "fontWeight": "normal",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "left",
            "key": "fbf02e65-9517-41fc-8ac6-402fe8aece15",
            "value": "筛选号：",
            "marginLeft": 48.95,
            "marginTop": 29.9,
            "type": "input"
        },
        {
            "width": 17,
            "height": 8.47,
            "fontSize": 12,
            "fontColor": "#1D2129",
            "fontWeight": "normal",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "left",
            "key": "618d9bb4-116e-41b9-8c17-4b49f53d8e8c",
            "value": "发药日期：",
            "marginLeft": 48.95,
            "marginTop": 41.01,
            "type": "input"
        },
        {
            "width": 17,
            "height": 6.5,
            "fontSize": 12,
            "fontColor": "#1D2129",
            "fontWeight": "normal",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "left",
            "key": "cedfbbb6-b21d-4da7-9477-c0aab49caf4c",
            "value": "包装规格：",
            "marginLeft": 104.78,
            "marginTop": 6.09,
            "type": "input"
        },
        {
            "width": 17,
            "height": 6.5,
            "fontSize": 12,
            "fontColor": "#1D2129",
            "fontWeight": "normal",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "left",
            "key": "79479a13-5ecd-4b3e-a605-cf4b626ef110",
            "value": "用法用量：",
            "marginLeft": 104.78,
            "marginTop": 16.67,
            "type": "input"
        },
        {
            "width": 11,
            "height": 6.5,
            "fontSize": 12,
            "fontColor": "#1D2129",
            "fontWeight": "normal",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "left",
            "key": "8c1f3185-8bf2-4b15-a58f-68b2e6181566",
            "value": "批号：",
            "marginLeft": 157.43,
            "marginTop": 6.09,
            "type": "input"
        },
        {
            "width": 14,
            "height": 6.5,
            "fontSize": 12,
            "fontColor": "#1D2129",
            "fontWeight": "normal",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "left",
            "key": "f394ac79-ab84-4d8e-8593-7571035af7d3",
            "value": "有效期：",
            "marginLeft": 157.16,
            "marginTop": 14.82,
            "type": "input"
        },
        {
            "width": 17,
            "height": 6.5,
            "fontSize": 12,
            "fontColor": "#1D2129",
            "fontWeight": "normal",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "left",
            "key": "489613b2-fa39-4fcf-b8c5-4f479932bcfd",
            "value": "保存条件：",
            "marginLeft": 157.16,
            "marginTop": 22.23,
            "type": "input"
        },
        {
            "width": 25.14,
            "height": 6.5,
            "fontSize": 12,
            "fontColor": "#1D2129",
            "fontWeight": "normal",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "left",
            "key": "b477096f-114b-49aa-80e9-85e87a9b8fc2",
            "value": "2024.12",
            "marginLeft": 171.19,
            "marginTop": 14.55,
            "type": "input",
            "bind": "expirationDate"
        },
        {
            "width": 25.14,
            "height": 6.5,
            "fontSize": 12,
            "fontColor": "#1D2129",
            "fontWeight": "normal",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "left",
            "key": "5d4f8c31-3e6e-4f8e-bd6e-bb5df380aaf6",
            "value": "20210101",
            "marginLeft": 169.07,
            "marginTop": 6.35,
            "type": "input",
            "bind": "batchNumber",
        },
        {
            "width": 25.14,
            "height": 6.5,
            "fontSize": 12,
            "fontColor": "#1D2129",
            "fontWeight": "normal",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "left",
            "key": "789c4b59-cfdc-426b-a8d8-f6f3a2889de7",
            "value": "室温密闭保存",
            "marginLeft": 174.1,
            "marginTop": 22.49,
            "type": "input"
        },
        {
            "width": 26.14,
            "height": 6.5,
            "fontSize": 12,
            "fontColor": "#1D2129",
            "fontWeight": "normal",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "left",
            "key": "2c6be5ce-3ca9-4149-a673-4517fa3cbc3f",
            "value": "请置于儿童接触处",
            "marginLeft": 157.43,
            "marginTop": 30.43,
            "type": "input"
        },
        {
            "width": 32.14,
            "height": 16.84,
            "fontSize": 12,
            "fontColor": "#1D2129",
            "fontWeight": "normal",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "left",
            "key": "dac2a649-27bf-481c-9873-c737c15e33c6",
            "value": "口服，一次2粒，一日3次，根据研究研究医师的指示使用",
            "marginLeft": 120.65,
            "marginTop": 16.4,
            "type": "input"
        },
        {
            "width": 31.14,
            "height": 6.5,
            "fontSize": 12,
            "fontColor": "#1D2129",
            "fontWeight": "normal",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "left",
            "key": "30e73b28-8f04-4a6c-9f46-1a7d8feab985",
            "value": "12.5mg/粒，48粒/瓶",
            "marginLeft": 121.71,
            "marginTop": 6.35,
            "type": "input"
        },
        {
            "width": 25.14,
            "height": 8.47,
            "fontSize": 12,
            "fontColor": "#1D2129",
            "fontWeight": "normal",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "left",
            "key": "693d8931-05d2-48dd-a6cc-8a42f936b10c",
            "value": "Clinflash001",
            "marginLeft": 63.76,
            "marginTop": 4.76,
            "type": "input"
        },
        {
            "width": 32.14,
            "height": 10.05,
            "fontSize": 12,
            "fontColor": "#1D2129",
            "fontWeight": "normal",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "left",
            "key": "63357f74-fc03-47f6-80d8-87f55a6dd5d8",
            "value": "甲氧那明胶囊/安慰剂",
            "marginLeft": 71.7,
            "marginTop": 15.08,
            "type": "input",
            "bind": "productName",
        },
        {
            "width": 25.14,
            "height": 8.47,
            "fontSize": 12,
            "fontColor": "#1D2129",
            "fontWeight": "normal",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "left",
            "key": "7fecaf84-7956-433d-abd5-02a4007192df",
            "value": "001",
            "marginLeft": 64.03,
            "marginTop": 29.9,
            "type": "input"
        },
        {
            "width": 25.14,
            "height": 8.47,
            "fontSize": 12,
            "fontColor": "#1D2129",
            "fontWeight": "normal",
            "fontFamily": "MicrosoftYaHei",
            "textAlign": "left",
            "key": "3d7b3d78-0a1d-4919-87f8-8cfff61a6db4",
            "value": "2025.12",
            "marginLeft": 65.88,
            "marginTop": 41.01,
            "type": "input"
        }
    ]
}