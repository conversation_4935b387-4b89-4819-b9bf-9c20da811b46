import React, { useRef } from "react";
import {
  Badge,
  Button,
  Col,
  <PERSON><PERSON><PERSON>, DatePicker, Dropdown,
  Empty,
  Input,
  message,
  Popover,
  Row,
  Spin,
  Table,
  Menu,
  Space,
} from "antd";
import { MedicineUpload } from "./medicine-list-upload";
import { permissions } from "../../../../tools/permission";
import { HistoryList } from "../../../common/history-list.jsx";
import { MedicinePacklistSetting } from "./package-setting";
import moment from "moment";
import Barcode from "react-barcode";
import { useAuth } from "../../../../context/auth";
import { useSafeState } from "ahooks";
import { useFetch } from "../../../../hooks/request";
import {
  deleteData,
  downloadData,
  getMedicineList,
} from "../../../../api/medicine";
import { medicineStatusData } from "../../../../tools/medicine_status";
import { CustomConfirmModal } from "components/modal";
import styled from "@emotion/styled";
import { medicineStatusColors } from "../../../../data/data";
import { usePage } from "../../../../context/page";
import { PaginationView } from "../../../../components/pagination";
import { exportReport } from "../../../../api/report";

import EmptyImg from "images/empty.png";
import { MedicineFolwPath } from "./medicine-folw-path";
import { SearchOutlined, DownOutlined } from "@ant-design/icons";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {AuthButton, clickFilter} from "../../../common/auth-wrap";

export const MedicineList = (props) => {
  const auth = useAuth();
  const intl = useTranslation();
  const page = usePage();
  const RangePicker = DatePicker.RangePicker;

  const searchInput = useRef();
  const { formatMessage } = intl;
  const [drugNumber, setDrugNumber] = useSafeState(null);
  const searchInputRef = React.useRef();
  const [doSearch, setDoSearch] = useSafeState(0);
  const [deleteMedicines, setDeleteMedicines] = useSafeState([]);
  const [deleteMedicineIds, setDeleteMedicineIds] = useSafeState([]);
  const medicine_upload = React.useRef();
  const history_ref = React.useRef();
  const package_setting_ref = React.useRef();
  const folw_path_setting_ref = React.useRef();
  const envId = auth.env ? auth.env.id : null;
  const customerId = auth.customerId;
  const projectId = auth.project.id;
  const [timeZone, setTimeZone] = useSafeState(8);
  const codeRule0 = props.codeRule0;
  const codeRule1 = props.codeRule1;
  const projectStatus = auth.project.status ? auth.project.status : 0;
  const [isOpenPackage, setIsOpenPackage] = useSafeState(false);
  const [basicMovement, seBasicMovement] = useSafeState(true);
  const [batchNumber, setBatchNumber] = useSafeState("");
  const [startTime, setStartTime] = useSafeState("");
  const [endTime, setEndTime] = useSafeState("");


  const { runAsync: getMedicineListRun, loading: getMedicineListLoading } =
    useFetch(getMedicineList, { debounceWait: 300, manual: true });
  const { runAsync: downloadDataRun, loading: downloadDataLoading } = useFetch(
    downloadData,
    { manual: true }
  );
  const { runAsync: deleteDataRun, loading: deleteDataLoading } = useFetch(
    deleteData,
    { manual: true }
  );

  const getList = () => {
    if (
      auth.project.info.timeZoneStr === null ||
      auth.project.info.timeZoneStr === "" || 
      auth.project.info.timeZoneStr === null
    ) {
      setTimeZone(8);
    } else {
      setTimeZone(Number(auth.project.info.timeZoneStr));
    }

    getMedicineListRun({
      customerId,
      envId,
      projectId: projectId,
      drugNumber: drugNumber?.trim(),
      batch: batchNumber,
      startTime: startTime,
      endTime: endTime,
      start: (page.currentPage - 1) * page.pageSize,
      limit: page.pageSize,
      roleId: auth.project.permissions.role_id,
    }).then((result) => {
      page.setTotal(result.data.total);
      page.setData(result.data.items);
      setIsOpenPackage(result.data.packageIsOpen);
      seBasicMovement(result.data.basicMovement);
    });
    resetSelected();
  };
  const resetSelected = () => {
    setDeleteMedicines([]);
    setDeleteMedicineIds([]);
  };
  const packageSettingModal = () => {
    // @ts-ignore
    package_setting_ref.current.show();
  };

  // 审核
  const toExamineModal = () => {
    // @ts-ignore
    folw_path_setting_ref.current.show(1);
  };

  // 修改
  const updateModal = () => {
    // @ts-ignore
    folw_path_setting_ref.current.show(2);
  };

  // 放行
  const releaseModal = () => {
    // @ts-ignore
    folw_path_setting_ref.current.show(3);
  };

  const upload = () => {
    // @ts-ignore
    medicine_upload.current.show();
  };

  function renderStatus(value, shortCode) {
    let code_rule = 1; // 自动编码
    if (shortCode === "") {
      code_rule = 0; // 手动上传
    }
    // @ts-ignore
    return (
      <Badge
        color={medicineStatusColors[value]}
        text={
          medicineStatusData(
            code_rule,
            auth.project.info.research_attribute
          ).find((it) => it.value === value)?.label ?? "-"
        }
      />
    );
  }
  const { runAsync: exportReportRun, loading: exportReportLoading } = useFetch(
    exportReport,
    { manual: true }
  );
  const downloadDataFun = () => {
    if (auth.project.info.research_attribute === 1) {
      downloadDataRun({
        customerId,
        projectId,
        envId,
        roleId: auth.project.permissions.role_id,
      }).then(() => { });
    } else {
      const params = {
        type: 11,
        projectId: auth.project.id,
        envId: envId,
        roleId: auth.project.permissions.role_id,
        cohortIds: [],
      };
      exportReportRun(params)
        .then(() => {
          message.success(formatMessage({ id: "report.download.success" }));
        })
        .catch(() => {
          message
            .error(formatMessage({ id: "common.download.fail" }))
            .then(() => { });
        });
    }
  };

  const rowSelection = {
    type: "checkbox",
    onChange: (selectedRowKeys, selectedRows) => {
      setDeleteMedicines(selectedRows);
      setDeleteMedicineIds(selectedRowKeys);
    },
    selectedRowKeys: deleteMedicineIds,
    selectedRows: deleteMedicines,
    getCheckboxProps: (record) => ({
      disabled: record.status !== 1,
    }),
  };

  //批量删除
  const deleteUploadMedicines = () => {
    if (deleteMedicineIds.length <= 0) {
      message.warn(formatMessage({ id: "drug.list.delete.info" }));
    } else {
      CustomConfirmModal({
        title: formatMessage({ id: "common.tips" }),
        content: formatMessage({ id: "common.confirm.delete" }),
        okText: formatMessage({ id: "common.ok" }),
        onOk: () =>
          deleteDataRun({
            customerId,
            envId,
            projectId,
            deleteIds: deleteMedicineIds,
          }).then((resp) => {
            message.success(resp.msg);
            getList();
          }),
      });
    }
  };

  //轨迹
  const showHistory = () => {
    // @ts-ignore
    history_ref.current.show(
      "history.project.medicine.upload",
      envId,
      timeZone
    );
  };

  const getColumnSearchBatchNumber = (dataIndex) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm }) => (
      <div style={{ padding: 8 }}>
        <Input.Search
          ref={searchInput}
          placeholder={formatMessage({ id: 'subject.dispensing.placeholder.input.batch' })}
          value={batchNumber}
          onChange={(e) => setBatchNumber(e.target.value)}
          onSearch={value => {
            getList();
          }}
          onPressEnter={confirm}
          enterButton
        />
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined style={{ color: filtered ? '#1890ff' : '#677283' }} />
    ),
    onFilter: (value, record) =>
      record[dataIndex]
        .toString()
        .toLowerCase()
        .includes((value).toLowerCase()),
    onFilterDropdownOpenChange: (visible) => {
      if (visible) {
        setTimeout(() => searchInput.current?.select(), 100);
      }
    },
  });

  function timeFilterOnChange(dates, dateStrings) {
    let starTime = "";
    let endTime = "";
    if (dates !== null) {
      starTime = dateStrings[0];
      endTime = dateStrings[1];
    }
    setStartTime(starTime);
    setEndTime(endTime);
  }

  const getColumnSearchExpirationDate = (dataIndex) => ({
    filterDropdown: ({ }) => (
      <div style={{ padding: 8 }}>
        <RangePicker
          onChange={timeFilterOnChange}
        />
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined style={{ color: filtered ? '#1890ff' : '#677283' }} />
    ),
    onFilter: (value, record) =>
      record[dataIndex]
        .toString()
        .toLowerCase()
        .includes((value).toLowerCase()),
    onFilterDropdownOpenChange: (visible) => {
      if (visible) {
        setTimeout(() => searchInput.current?.select(), 100);
      }
    },
  });

  // 审核、修改、放行按钮文本
  let buttonText = "";
  if (basicMovement && permissions(auth.project.permissions, "operation.build.medicine.examine")) {
    buttonText = <FormattedMessage id="medicine.but.examine" />;
  } else if (basicMovement && permissions(auth.project.permissions, "operation.build.medicine.update")) {
    buttonText = <FormattedMessage id="medicine.but.update" />;
  } else if (permissions(auth.project.permissions, "operation.build.medicine.release")) {
    buttonText = <FormattedMessage id="medicine.but.release" />;
  } else {
    buttonText = "";
  }

  // 审核、修改、放行按钮权限
  const items = [];
  if (basicMovement && permissions(auth.project.permissions, "operation.build.medicine.examine")) {
    items.push({ label: <FormattedMessage id="medicine.but.examine" />, key: '1', });
  }
  if (basicMovement && permissions(auth.project.permissions, "operation.build.medicine.update")) {
    items.push({ label: <FormattedMessage id="medicine.but.update" />, key: '2', });
  }
  if (permissions(auth.project.permissions, "operation.build.medicine.release")) {
    items.push({ label: <FormattedMessage id="medicine.but.release" />, key: '3', });
  }

  // 审核、修改、放行按钮单击事件
  const buttonMenuClick = (e) => {
    if (e.key === "1") {
      toExamineModal();
    } else if (e.key === "2") {
      updateModal();
    } else {
      releaseModal();
    }
  };

  const menuProps = {
    items,
    onClick: clickFilter(buttonMenuClick),
  };


  // eslint-disable-next-line react-hooks/exhaustive-deps
  React.useEffect(getList, [doSearch, page.currentPage, page.pageSize, startTime, endTime]);

  // React.useImperativeHandle(props.bind, () => ({refresh: getList}));

  return (
    <React.Fragment>
      <Row gutter={8} justify="space-between">
        <Col xs={12} sm={12} md={12} lg={4} style={{ paddingRight: 0 }}>
          <CustomInputSearch
            placeholder={formatMessage({ id: "drug.list.drugNumber" })}
            ref={searchInputRef}
            value={drugNumber}
            onChange={(e) => {
              setDrugNumber(e.target.value);
            }}
            allowClear
            onSearch={() => {
              searchInputRef?.current?.blur();
              page.setCurrentPage(1);
              setDoSearch(doSearch + 1);
            }}
          />
        </Col>
        <MarLft12Col>
          {/* 审核 */}
          {/*{basicMovement && permissions(
            auth.project.permissions,
            "operation.build.medicine.examine"
            ) ? (
                <Button onClick={toExamineModal}>
                    <FormattedMessage id="medicine.but.examine" />
                </Button>
            ) : null}*/}

          {/* 修改 */}
          {/*{basicMovement && permissions(
            auth.project.permissions,
            "operation.build.medicine.update"
            ) ? (
                <Button onClick={updateModal}>
                  <FormattedMessage id="medicine.but.update" />
                </Button>
            ) : null}*/}
          {/* 放行 */}
          {/*{permissions(
            auth.project.permissions,
            "operation.build.medicine.release"
            ) ? (
                <Button onClick={releaseModal}>
                    <FormattedMessage id="medicine.but.release" />
                </Button>
            ) : null}*/}
          {/* {codeRule === 1 && permissions(auth.project.permissions, "operation.build.medicine.barcode.scan") && projectStatus !== 2 ?
                        <Button onClick={showScanModal}><FormattedMessage id="barcode.scan" /></Button>
                        : null}*/}
          {permissions(
            auth.project.permissions,
            "operation.build.medicine.upload.delete"
          ) && projectStatus !== 2 ? (
            <AuthButton loading={deleteDataLoading} onClick={deleteUploadMedicines}>
              <FormattedMessage id="drug.list.delete" />
            </AuthButton>
          ) : null}
          {permissions(
            auth.project.permissions,
            "operation.build.medicine.upload.downdata"
          ) ? (
            <AuthButton
              loading={downloadDataLoading || exportReportLoading}
              onClick={downloadDataFun}
            >
              <FormattedMessage id="common.download.data" />
            </AuthButton>
          ) : null}

          {/* 审核、修改、放行 按钮*/}
          {
            buttonText != "" && buttonText != null && buttonText != undefined ?
              <Dropdown menu={menuProps}>
                <AuthButton>
                  <Space>
                    {buttonText}
                    <DownOutlined />
                  </Space>
                </AuthButton>
              </Dropdown>
              :
              null
          }
          {permissions(
            auth.project.permissions,
            "operation.build.medicine.package.setting"
          ) ? (
            <AuthButton onClick={packageSettingModal}>
              <FormattedMessage id="menu.settings" />
            </AuthButton>
          ) : null
          }
          {/* {permissions(auth.project.permissions, "operation.build.medicine.upload.uploadHistory") ?
                        <Button onClick={showHistory}> <FormattedMessage
                            id="common.history" /></Button>
                        : null
                    } */}
          {codeRule0 &&
            permissions(
              auth.project.permissions,
              "operation.build.medicine.upload.upload"
            ) &&
            //  || permissions(auth.project.permissions, "operation.build.medicine.packlist.upload")
            projectStatus !== 2 && (
              <AuthButton onClick={upload}>
                <FormattedMessage id="common.upload" />
              </AuthButton>
            )}
        </MarLft12Col>
      </Row>
      <Spin spinning={getMedicineListLoading}>
        <ConfigProvider
          renderEmpty={() => {
            return (
              <Empty
                image={
                  <img src={EmptyImg} style={{ width: 300, height: 213 }} />
                }
                imageStyle={{
                  height: 240,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              />
            );
          }}
        >
          <Table
            className="mar-top-10"
            dataSource={page.data}
            pagination={false}
            rowKey={(record) => record.id}
            rowSelection={rowSelection}
            scroll={{ x: 1200, y: "calc(100vh - 270px)" }}
          >
            <Table.Column
              title={<FormattedMessage id={"common.serial"} />}
              dataIndex="#"
              key="#"
              width={70}
              render={(text, record, index) =>
                (page.currentPage - 1) * page.pageSize + index + 1
              }
            />
            <Table.Column
              title={<FormattedMessage id="drug.configure.drugName" />}
              key="name"
              dataIndex="name"
              ellipsis
              render={(text, record, index) => {
                return record["name"]
                  ?
                  record["name"]
                  :
                  "-"

              }}
            />
            {/*如果是系统自动编码，隐藏“序列号”字段的展示*/}
            {codeRule0 && (
              <Table.Column
                title={<FormattedMessage id="drug.list.serialNumber" />}
                key="serialNumber"
                dataIndex="serialNumber"
                width={200}
                ellipsis
                render={(text, record, index) => {
                  return record["shortCode"] == ""
                    ? record["serialNumber"]
                      ? record["serialNumber"]
                      : "-"
                    : "-";
                }}
              />
            )}
            <Table.Column
              title={<FormattedMessage id="drug.list.drugNumber" />}
              key="number"
              dataIndex="number"
              width={200}
              ellipsis
            />
            <Table.Column
              title={<FormattedMessage id="drug.list.expireDate" />}
              key="expirationDate"
              dataIndex="expirationDate"
              ellipsis
              {...getColumnSearchExpirationDate("expirationDate")}
              render={
                (value) => (
                  (value === undefined || value === null || value === "") ? '-' : value
                )}
            />
            <Table.Column
              title={<FormattedMessage id="drug.list.batch" />}
              key="batchNumber"
              dataIndex="batchNumber"
              ellipsis
              {...getColumnSearchBatchNumber("batchNumber")}
              render={
                (value) => (
                  (value === undefined || value === null || value === "") ? '-' : value
                )}
            />
            {isOpenPackage && (
              <>
                <Table.Column
                  title={<FormattedMessage id="drug.medicine.packlist" />}
                  key="packageNumber"
                  dataIndex="packageNumber"
                  ellipsis
                  render={(text, record, index) => {
                    return record["packageNumber"]
                      ? record["packageNumber"]
                      : "-";
                  }}
                />
                <Table.Column
                  title={<FormattedMessage id="drug.medicine.package.serial_number" />}
                  key="packageSerialNumber"
                  dataIndex="packageSerialNumber"
                  ellipsis
                  render={(text, record, index) => {
                    return record["packageSerialNumber"]
                      ? record["packageSerialNumber"]
                      : "-";
                  }}
                />
              </>
            )}
            {/* <Table.Column title={<FormattedMessage id="drug.medicine.packlist" />}
                        key="packlist"
                        dataIndex="packlist"
                        ellipsis
                        render={(text, record, index) => {
                            return (
                                record["packlist"] ?
                                    <React.Fragment>
                                        <Tooltip placement="top" title={record["packlist"].join(">")}>
                                            {record["packlist"].join(">")}
                                        </Tooltip>
                                    </React.Fragment>
                                    :
                                    "-"
                            );
                        }} /> */}
            {codeRule1 && (
              <Table.Column
                title={<FormattedMessage id="drug.list.entryTime" />}
                key="entryTime"
                dataIndex="entryTime"
                ellipsis
                render={(value, record, index) =>
                  value === null || value === 0
                    ? "-"
                    : moment
                      .unix(value)
                      .utc()
                      .add(timeZone, "hour")
                      .format("YYYY-MM-DD HH:mm")
                }
              />
            )}
            {codeRule1 && (
              <Table.Column
                title={<FormattedMessage id="barcode" />}
                key="number"
                dataIndex="number"
                ellipsis
                render={(value, record, index) => {
                  return (
                    <>
                      {record.shortCode != "" ? (
                        <Popover
                          content={
                            <>
                              <Barcode
                                value={value}
                                displayValue={false}
                                height={60}
                                width={1}
                                format="CODE128"
                              />
                              <br />
                              <span>&nbsp;&nbsp;{value}</span>
                            </>
                          }
                          trigger="click"
                        >
                          <AuthButton
                            style={{ padding: 0, marginRight: "12px" }}
                            size="small"
                            type="link"
                          >
                            <FormattedMessage id="form.preview" />
                          </AuthButton>
                        </Popover>
                      ) : (
                        "-"
                      )}
                    </>
                  );
                }}
              />
            )}
            {codeRule1 && (
              <Table.Column
                title={<FormattedMessage id="packageBarcode" />}
                key="packageNumber"
                dataIndex="packageNumber"
                ellipsis
                render={(value, record, index) => {
                  return (
                    <>
                      {record.shortCode != "" && record.packageNumber != "" ? (
                        <Popover
                          content={
                            <>
                              <Barcode
                                value={value}
                                displayValue={false}
                                height={60}
                                width={1}
                                format="CODE128"
                              />
                              <br />
                              <span>&nbsp;&nbsp;{value}</span>
                            </>
                          }
                          trigger="click"
                        >
                          <AuthButton
                            style={{ padding: 0, marginRight: "12px" }}
                            size="small"
                            type="link"
                          >
                            <FormattedMessage id="form.preview" />
                          </AuthButton>
                        </Popover>
                      ) : (
                        "-"
                      )}
                    </>
                  );
                }}
              />
            )}
            <Table.Column
              title={<FormattedMessage id="drug.list.status" />}
              key="status"
              dataIndex="status"
              ellipsis
              render={(value, record, index) =>
                renderStatus(value, record.shortCode)
              }
            />
          </Table>
        </ConfigProvider>
      </Spin>
      <PaginationView
        mode="SELECTABLE"
        selectedNumber={deleteMedicineIds?.length}
        clearDisplay={true}
        refresh={getList}
      />
      <MedicineUpload bind={medicine_upload} refresh={getList} />
      <MedicinePacklistSetting bind={package_setting_ref} refresh={getList} />
      <MedicineFolwPath bind={folw_path_setting_ref} refresh={getList} />
      <HistoryList
        bind={history_ref}
        permission={permissions(
          auth.project.permissions,
          "operation.build.medicine.upload.print"
        )}
      />
    </React.Fragment>
  );
};

const CustomInputSearch = styled(Input.Search)`
  .ant-input-search-button {
    height: 31.6px;
  }
`;
const MarLft12Col = styled(Col)`
  padding-left: 0 !important;

  & > * {
    margin-left: 12px;
  }
`;
