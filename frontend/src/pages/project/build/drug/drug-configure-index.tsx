import React from "react";
import {<PERSON><PERSON>, <PERSON>, message, notification, Row, Spin, Table,} from "antd";
import {DrugConfigureAdd} from "./drug-configure-add";
import {DrugConfigureSetting} from "./drug-configure-setting";
import {permissionsCohort} from "../../../../tools/permission";
import {useAuth} from "../../../../context/auth";
import {useFetch} from "../../../../hooks/request";
import {getVisitList} from "../../../../api/visit";
import {deleteConfigure, getConfigureList, getIsGroup, getIsLabel, getSetUpDrugConfigureDtpRule, } from "../../../../api/drug";
import {CloseCircleTwoTone} from "@ant-design/icons";
import {useSafeState} from "ahooks";
import {CustomConfirmModal} from "../../../../components/modal";
import {getVisitSettings} from "../../../../api/visit";
import { useGlobal } from "../../../../context/global";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {AuthButton} from "../../../common/auth-wrap";

export const DrugIndex = (props: any) => {
    const g = useGlobal();
    const auth = useAuth();
    const intl = useTranslation();
    const { formatMessage } = intl;

    const [data, setData] = useSafeState([]);
    const [id, setId] = useSafeState(null);
    const drug_configure_add = React.useRef();
    const drug_configure_setting = React.useRef();
    const [visitCycles, setVisitCycles] = useSafeState([]);
    const [vcList, setVcList] = useSafeState([]);

    const [dtpRules, setDtpRules] = useSafeState(0);
    const [drugNameList, setDrugNameList] = useSafeState<any[]>([]);

    const envId = auth.env ? auth.env.id : null;
    const cohortId = props.cohort ? props.cohort.id : null;
    const roleId = auth.project.permissions.role_id;
    const customerId = auth.customerId;
    const projectId = auth.project.id;
    const lockConfig = auth.env.lockConfig ? auth.env.lockConfig : false;
    const projectStatus = auth.project.status ? auth.project.status : 0;
    const room = auth.project.info.room;

    const {runAsync: getVisitSettingsRun, loading: getVisitSettingsLoading} = useFetch(getVisitSettings, {manual: true});


    const { runAsync: getVisitListRun } = useFetch(getVisitList, {
        manual: true,
    });
    const { runAsync: getConfigureListRun, loading } = useFetch(
        getConfigureList,
        { manual: true }
    );
    const { runAsync: deleteConfigureRun } = useFetch(deleteConfigure, {
        manual: true,
    });

    const { runAsync: getIsGroupRun, loading: getIsGroupLoading } = useFetch(
        getIsGroup,
        { manual: true }
    );

    const { runAsync: getIsLabelRun, loading: getIsLabelLoading } = useFetch(
        getIsLabel,
        { manual: true }
    );

    const { runAsync: getSetUpDrugConfigureDtpRuleRun, loading: getSetUpDrugConfigureDtpRuleLoading } = useFetch(
        getSetUpDrugConfigureDtpRule,
        { manual: true }
    );

    function handleRefresh() {
        getList();
        // 调用其他方法
        getJudgeLabel();
    }

    const getList = () => {
        //获取访视周期
        getVisitListRun({
            customerId,
            projectId,
            envId,
            cohortId: cohortId,
            roleId: auth.project.permissions.role_id,
        }).then((result: any) => {
            if (result.data.infos != null) {
                const options = result.data.infos.map((it: any) => ({
                    label: it.name,
                    option: it.group,
                    value: it.id,
                }));
                setVisitCycles(options);
                let vcs = options
                getVisitSettingsRun({
                    customerId,
                    projectId,
                    envId,
                    cohortId: cohortId,
                    roleId: roleId,
                }).then((result: any) => {
                    if(result.data.isOpen){
                      let out = {
                        label: g.lang === "en"?result.data.nameEn:result.data.nameZh,
                        option:[],
                        value:result.data.id,
                      }
                      vcs.push(out);
                    }
                });
                setVcList(vcs);
            }
            
            getConfigureListRun({
                customerId,
                projectId,
                envId,
                cohortId: cohortId,
                roleId: auth.project.permissions.role_id,
            }).then((result: any) => {
                setId(result.data.id);
                setData(result.data.configures);
            });
        });
        getSetUpDrugConfigureDtpRuleRun({
            customerId,
            projectId,
            envId,
            cohortId: cohortId,
            roleId: auth.project.permissions.role_id,
        }).then((result: any) => {
            setDtpRules(result.data.type);
            if(result.data.drugNameList !== null && result.data.drugNameList !== undefined && result.data.drugNameList.length > 0){
            const list = (result.data.drugNameList).map((it: any) => {
                return { value: it, label: it };
            });
            setDrugNameList(list);
            }
        });
    };


    const getJudgeGroup = () => {
        getIsGroupRun({
          customerId,
          projectId,
          envId,
          cohortId: cohortId,
          category: "2",
          roleId: auth.project.permissions.role_id,
        }).then((result: any) => {
          var visits = [] 
          if(result.data !== null && result.data.length > 0){
            notification.open({
                message: (
                  <div
                    style={{
                        fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                      fontSize: "14px",
                    }}
                  >
                    <CloseCircleTwoTone
                      twoToneColor="#FF0000"
                      style={{ paddingRight: "8px" }}
                    />
                    {formatMessage({ id: result.data })}
                  </div>
                ),
                // duration: 5,
                placement: "top",
                style: {
                  width: "720px",
                  height: "55px",
                  background: "#fff2f1",
                  borderStyle: "solid",
                  border: "1px",
                  borderColor: "#41CC82",
                  borderRadius: "4px",
                },
                // closeIcon: null, // 隐藏关闭按钮，用户必须手动关闭
                duration: null, // 设置为null，让通知框不自动关闭
              });
          }
        });
    };


    const getJudgeLabel = () => {
        getIsLabelRun({
          customerId,
          projectId,
          envId,
          cohortId: cohortId,
          roleId: auth.project.permissions.role_id,
        }).then((result: any) => {
          var visits = [] 
          if(result.data !== null && result.data.length > 0){
            notification.open({
                message: (
                  <div
                    style={{
                        fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                      fontSize: "14px",
                    }}
                  >
                    <CloseCircleTwoTone
                      twoToneColor="#FF0000"
                      style={{ paddingRight: "8px" }}
                    />
                    {formatMessage({ id: result.data })}
                  </div>
                ),
                // duration: 5,
                placement: "top",
                style: {
                  width: "720px",
                  height: "55px",
                  background: "#fff2f1",
                  borderStyle: "solid",
                  border: "1px",
                  borderColor: "#41CC82",
                  borderRadius: "4px",
                },
                // closeIcon: null, // 隐藏关闭按钮，用户必须手动关闭
                duration: null, // 设置为null，让通知框不自动关闭
              });
          }
        });
    };

    const add = () => {
        // @ts-ignore
        drug_configure_add.current.show(id, visitCycles);
    };

    const setting = () => {
        // @ts-ignore
        drug_configure_setting.current.show(dtpRules, drugNameList);
    };

    const edit = (configure: any) => {
        let data: any = JSON.parse(JSON.stringify(configure));
        // @ts-ignore
        drug_configure_add.current.show(id, visitCycles, data);
    };

    const remove = (configureId: any, record: any) => {
        let name:any[] = []
        record?.values.map((item:any) => {
            name.push(item.drugName)
        })
        CustomConfirmModal({
            title: formatMessage({ id: 'common.tips' }),
            content: formatMessage({ id: 'drug.configure.delete' }),
            okText: formatMessage({ id: 'common.ok' }),
            cancelText: formatMessage({ id: 'common.cancel' }),
            onOk: () => deleteConfigureRun({ id: id, configureId: configureId, envId: envId, name: name.join(",") }).then(
                (data: any) => {
                    message.success(data.msg)
                    getList()
                }
            )
        });
    };

    const renderDrugName = (it:any,record:any) => {
        let number :any = ""
        if (record.openSetting !== 3) {
            number = it.customDispensingNumber
        }
        return it.drugName + "/" + number + "/" + it.drugSpec + " "
    }

    function renderVisitName(value: any) {
        // @ts-ignore
        return vcList.find((it: any) => it.value === value) ? vcList.find((it: any) => it.value === value).label  : "";
    }
    React.useEffect(() => {
        getList();
        getJudgeGroup();
        getJudgeLabel();
    }, [g.lang]);

    return (
        <React.Fragment>
            <Spin spinning={loading}>
                <Row 
                    gutter={8} 
                    justify="space-between"
                    style={{ marginTop: 6, marginBottom: 16,}}
                >
                    <Col></Col>
                    <Col>
                        {   
                            permissionsCohort(
                                auth.project.permissions,
                                "operation.build.medicine.configuration.setting.list",
                                props.cohort?props.cohort.status:0
                            ) &&
                            !lockConfig &&
                            projectStatus !== 2 && (
                                <AuthButton
                                    onClick={() => {
                                        setting();
                                    }}
                                    style={{ marginRight: 8 }}
                                >
                                    {formatMessage({ id: "common.setting", allowComponent: true })}
                                </AuthButton>
                            )
                        }
                        {
                            permissionsCohort(
                                auth.project.permissions,
                                "operation.build.medicine.configuration.add",
                                props.cohort?props.cohort.status:0
                            ) &&
                            !lockConfig &&
                            projectStatus !== 2 && (
                                <AuthButton
                                    type="primary"
                                    onClick={() => {
                                        add();
                                    }}
                                    style={{marginRight: 0}}
                                >
                                    {formatMessage({ id: "common.add", allowComponent: true })}
                                </AuthButton>
                            )
                        }
                    </Col>
                </Row>
                <Table
                    dataSource={data}
                    scroll={{ x: "max-content", y: "calc(100vh - 270px)"  }}
                    pagination={false}
                    rowKey={(record: any) => record.id}
                >
                    <Table.Column
                        title={<FormattedMessage id={"common.serial"} />}
                        dataIndex="#"
                        key="#"
                        width={56}
                        render={(text, record, index) => index + 1}
                    />
                    <Table.Column
                        title={<FormattedMessage id="drug.configure.group" />}
                        key="parName"
                        dataIndex="parName"
                        align="left"
                        ellipsis
                        render={(v) => (v ? v : "-")}
                    />
                    {data &&
                    data.findIndex((it: any) => it.subName !== "") !== -1 ? (
                        <Table.Column
                            title={
                                <FormattedMessage id="drug.configure.subGroup" />
                            }
                            key="subName"
                            dataIndex="subName"
                            align="left"
                            ellipsis
                            render={(v) => (v ? v : "-")}
                        />
                    ) : null}
                    <Table.Column
                        title={
                            <FormattedMessage id="drug.configure.drugName" />
                        }
                        key="values"
                        dataIndex="values"
                        align="left"
                        render={(value, record: any, index) =>
                            value.map((it: any) => {
                                return renderDrugName(it, record);
                            })
                        }
                    />
                    <Table.Column
                        title={
                            <FormattedMessage id="drug.configure.visitName" />
                        }
                        // width={130}
                        width="auto"
                        // className="custom-column"
                        key="visitCycles"
                        dataIndex="visitCycles"
                        align="left"
                        ellipsis
                        render={(value) => {
                            const result = value
                                ?.map((it: any) => renderVisitName(it))
                                .join(" ")
                                .trim();
                            return !value ? "-" : !result ? "-" : result;
                        }}
                    />
                    <Table.Column 
                        width={144} 
                        title={<FormattedMessage id="drug.configure.drugLabel.config" />}
                        key="label"
                        dataIndex="label"
                        align="left"
                        ellipsis
                        render={
                            (value, record :any , index) => {
                                let labels: any[] = []
                                record?.values.forEach((item:any) => {
                                    if (item.label && (item.label !== undefined || item.label !== "")){
                                        labels.push(item.label)
                                    }
                                })
                                if (value !== "") {
                                    labels.push(value)
                                }
                                if (labels.length > 0) {
                                    return labels.join(",")
                                }else {
                                    return "-"
                                }

                            }
                        }
                    />
                    {!room && (
                        <Table.Column
                        width={144}
                        title={<FormattedMessage id="drug.configure.roomNumber"/>}
                        key="roomNumbers"
                        dataIndex="roomNumbers"
                        align="left"
                        ellipsis
                        render={(text, record: any, index) => {
                            return (
                                record.roomNumbers && record.roomNumbers.length > 0 ?
                                    record.roomNumbers.join(",")
                                    :
                                    '-'
                            );
                        }}
                    />)}
                    {
                        lockConfig || auth.project.status === 2 ? null :
                            <Table.Column
                                title={<FormattedMessage id="common.operation" />}
                                width={intl.locale === 'zh' ? 108 : 112}
                                fixed={"right"}
                                render={
                                    (value, record: any, index) => {
                                        const btns: React.ReactNode[] = []; // 显式指定 btns 的类型为 React.ReactNode[]
                                        if (permissionsCohort(auth.project.permissions, "operation.build.medicine.configuration.edit",props.cohort?.status)) {
                                            btns.push(<AuthButton
                                                style={{ padding: 0, marginRight: '12px' }}
                                                size="small"
                                                type="link"
                                                onClick={() => edit(record)}>
                                                <FormattedMessage id="common.edit" />
                                            </AuthButton>)

                                        }
                                        if (permissionsCohort(auth.project.permissions, "operation.build.medicine.configuration.delete",props.cohort?.status) && !record.isCopyEditDelete) {
                                            btns.push(<AuthButton
                                                style={{ padding: 0 }}
                                                size="small"
                                                type="link"
                                                onClick={() => remove(record.id, record)}>
                                                <FormattedMessage id="common.delete" />
                                            </AuthButton>)
                                        }
                                        return btns
                                        // return InsertDivider(btns)
                                    }
                                }
                            />
                    }
                </Table>
            </Spin>
            <DrugConfigureAdd bind={drug_configure_add} refresh={handleRefresh} cohort={props.cohort}/>
            <DrugConfigureSetting bind={drug_configure_setting} refresh={getList} cohort={props.cohort}/>
        </React.Fragment>
    );
};
