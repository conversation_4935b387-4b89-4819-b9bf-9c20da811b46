import React, { useState } from 'react';
import { useIntl } from "react-intl";
import {
    Checkbox,
    Form,
    Input,
    message,
    Modal,
    Row,
    InputNumber,
    Typography,
    Col,
    Button,
    Badge,
    notification,
} from "antd";
import { combineRow } from "../../../../utils/merge_cell_bath";
import { fillTableCellEmptyPlaceholder } from "../../../../components/table";
import { useFetch } from "../../../../hooks/request";
import { useAuth } from "../../../../context/auth";
import { updateBatch, getBatchGroupStatus, getBatchSelectList } from "../../../../api/medicine";
import { useSafeState } from "ahooks";
import { batchManagementTypes, batchStatus } from "../../../../data/data.jsx";
import { useGlobal } from "../../../../context/global";
import styled from "@emotion/styled";
import { CustomDateTimePicker } from "../../../../components/CustomDateTimePicker";
import { permissions } from "../../../../tools/permission";
import _ from 'lodash';
import { Title } from "../../../../components/title";
import { usePage, PageContextProvider } from "../../../../context/page";
import { medicineStatusData } from "../../../../tools/medicine_status";
import { medicineStatusColors } from "../../../../data/data";
import { MedicineDataView } from "./medicine-data";
import { useDrug } from "./context";
import {CheckCircleFilled} from "@ant-design/icons";
import {getDepotBatchGroup} from "../../../../api/project_storehouse";


export const BatchManagementEdit = (props: any) => {
    const page = usePage();
    const g = useGlobal();
    const auth = useAuth();
    const intl = useIntl();
    const { formatMessage } = intl;
    const { Link } = Typography;
    const ud = useDrug();

    const [showUpdateMessage, setShowUpdateMessage] = useSafeState(false);
    const [visible, setVisible] = useSafeState(false);
    const [submitting, setSubmitting] = useSafeState(false);
    const [updateCountMax, setUpdateCountMax] = useSafeState(0);
    const [updateCountDisable, setUpdateCountDisable] = useSafeState(true);
    const [canUpdate, setCanUpdate] = useSafeState(true);
    const [record, setRecord] = useSafeState<any>({});
    const [statusCount, setStatusCount] = useSafeState<any>({});

    const [isOther, setIsOther] = useSafeState(false); //是否未编号药物
    const [isExistsPackageNumber, setIsExistsPackageNumber] = useSafeState(false); //是否存在包装


    const [upperNumber, setUpperNumber] = useState("");
    const [lowerNumber, setLowerNumber] = useState("");
    const [upperSequence, setUpperSequence] = useState("");
    const [lowerSequence, setLowerSequence] = useState("");


    const [medicineTableData, setMedicineTableData] = useSafeState<any>([]);

    const [medicineListOpen, setMedicineListOpen] = useState(false);
    const [statusOptions, setStatusOptions] = useSafeState<any>([]);
    const [selectedRowKeys, setSelectedRowKeys] = useSafeState<React.Key[]>([]);

    const [selectedNumber, setSelectedNumber] = useSafeState(0);
    const [selectedPackageNumber, setSelectedPackageNumber] = useSafeState(0);
    const [selectedPackageTotal, setSelectedPackageTotal] = useSafeState(0);

    const [selectedData, setSelectedData] = useSafeState<any>([]);
    const [selectedTotal, setSelectedTotal] = useSafeState(0);

    const [form] = Form.useForm();
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId;
    const projectStatus = auth.project.status ? auth.project.status : 0;

    const [expirationDateValue, setExpirationDateValue] = useState(null);

    const { runAsync: updateBatchRun } = useFetch(updateBatch, {
        manual: true,
    });

    const { runAsync: getBatchSelectListRun } = useFetch(getBatchSelectList, {
        manual: true,
    });
    const { runAsync: getBatchGroupStatusRun } = useFetch(getBatchGroupStatus, {
        manual: true,
    });

    function renderStatus(value: any, shortCode: any) {
        let code_rule = 1; // 自动编码
        if (shortCode === "") {
            code_rule = 0; // 手动上传
        }
        // @ts-ignore
        return (
            <Badge
                color={medicineStatusColors[value]}
                text={
                    medicineStatusData(
                        code_rule,
                        auth.project.info.research_attribute
                    ).find((it: any) => it.value === value)?.label ?? "-"
                }
            />
        );
    }


    const getSelectList = (key: any) => {
        setSelectedTotal(0);
        setSelectedData([]);
        page.setTotal(0);
        page.setData([]);
        var findBatchs = [record];
        let batch = null;
        findBatchs.forEach((item: any) => {
            if (item.batchNumber === null || item.batchNumber === undefined || item.batchNumber === "-") {
                item.batchNumber = "";
            }

            if (item.expirationDate === null || item.expirationDate === undefined || item.expirationDate === "-") {
                item.expirationDate = "";
            }
        });
        if (findBatchs[0].batchNumber !== null && findBatchs[0].batchNumber !== undefined && findBatchs[0].batchNumber !== "-") {
            batch = findBatchs[0].batchNumber
        };
        let expirationDate = null;
        if (findBatchs[0].expirationDate !== null && findBatchs[0].expirationDate !== undefined && findBatchs[0].expirationDate !== "-") {
            expirationDate = findBatchs[0].expirationDate
        };
        let un = "";
        if (upperNumber !== null && upperNumber !== undefined) {
            un = upperNumber;
        };
        let ln = "";
        if (lowerNumber !== null && lowerNumber !== undefined) {
            ln = lowerNumber;
        };
        let us = "";
        if (upperSequence !== null && upperSequence !== undefined) {
            us = upperSequence;
        };
        let ls = "";
        if (lowerSequence !== null && lowerSequence !== undefined) {
            ls = lowerSequence;
        };
        getBatchSelectListRun({
            customerId,
            projectId,
            envId,
            status: form.getFieldValue("status"),
            findBatchs,
            batch: batch,
            expirationDate: expirationDate,
            upperNumber: un,
            lowerNumber: ln,
            upperSequence: us,
            lowerSequence: ls,
        }).then((res: any) => {
            let data = res.data;
            let existsPackage = false;
            if (data !== null && data.length > 0) {
                for (let obj of data) {
                    if (obj.packageNumber !== null && obj.packageNumber !== "" && obj.packageNumber !== undefined && obj.packageNumber !== "-") {
                        existsPackage = true;
                        break;
                    }
                }
                data.sort((a: any, b: any) => {
                    let nameA = a.packageNumber.toUpperCase(); // 不区分大小写排序
                    let nameB = b.packageNumber.toUpperCase(); // 不区分大小写排序

                    if (nameA < nameB) {
                        return -1;
                    }
                    if (nameA > nameB) {
                        return 1;
                    }
                    return 0;
                });
                setIsExistsPackageNumber(existsPackage);
                // const tableData = useCacheTable(data);
                var tableData = data;
                if (existsPackage) {
                    tableData = combineRow(
                        res.data,
                        "packageNumber",
                        "packageNumber", false
                    );
                }
                let informationData = fillTableCellEmptyPlaceholder(tableData ? tableData : [])
                setMedicineTableData(informationData);

                setSelectedNumber(informationData.length);
                setSelectedPackageTotal(informationData.length);
                if (!existsPackage) {
                    setSelectedTotal(informationData.length);
                    page.setTotal(informationData.length);
                } else {
                    let da = informationData;
                    // 提取 packageNumber 不为空的值
                    const nonEmptyPNValues = da
                        .filter((item: any) => item.packageNumber !== null && item.packageNumber !== undefined && item.packageNumber !== "" && item.packageNumber !== "-") // 过滤出 packageNumber 不为空的对象
                        .map((item: any) => item.packageNumber); // 提取 packageNumber 值
                    // 使用 Set 去重
                    const uniqueNonEmptyPNValues = [...new Set(nonEmptyPNValues)];
                    let pn = uniqueNonEmptyPNValues ? uniqueNonEmptyPNValues.length : 0;
                    const emptyIdValues = da
                        .filter((item: any) => item.packageNumber == null || item.packageNumber === undefined || item.packageNumber === "" || item.packageNumber === "-") // 过滤出 packageNumber 为空的对象
                        .map((item: any) => item.id); // 提取 id 值
                    // 使用 Set 去重
                    const uniqueNonEmptyIdValues = [...new Set(emptyIdValues)];
                    let id = uniqueNonEmptyIdValues ? uniqueNonEmptyIdValues.length : 0;
                    setSelectedTotal(pn + id);
                    setSelectedPackageNumber(pn + id);
                    page.setTotal(pn + id);
                }
                page.setData(informationData);
                setSelectedData(informationData);
                if (key === 1) {
                    if(ud.selectedRowKeys && ud.selectedRowKeys.length >= 0){
                        setSelectedRowKeys(ud.selectedRowKeys);
                        ud.setOldSelectedRowKeys(ud.selectedRowKeys);
                        if (!existsPackage) {
                            setSelectedTotal(ud.selectedRowKeys.length);
                        } else {
                            // 使用 filter 方法筛选出符合条件的元素
                            const da = informationData.filter((item) => ud.selectedRowKeys.includes(item.id));
                            // 提取 packageNumber 不为空的值
                            const nonEmptyPNValues = da
                                .filter((item: any) => item.packageNumber !== null && item.packageNumber !== undefined && item.packageNumber !== "" && item.packageNumber !== "-") // 过滤出 packageNumber 不为空的对象
                                .map((item: any) => item.packageNumber); // 提取 packageNumber 值
                            // 使用 Set 去重
                            const uniqueNonEmptyPNValues = [...new Set(nonEmptyPNValues)];
                            let pn = uniqueNonEmptyPNValues ? uniqueNonEmptyPNValues.length : 0;
                            const emptyIdValues = da
                                .filter((item: any) => item.packageNumber == null || item.packageNumber === undefined || item.packageNumber === "" || item.packageNumber === "-") // 过滤出 packageNumber 为空的对象
                                .map((item: any) => item.id); // 提取 id 值
                            // 使用 Set 去重
                            const uniqueNonEmptyIdValues = [...new Set(emptyIdValues)];
                            let id = uniqueNonEmptyIdValues ? uniqueNonEmptyIdValues.length : 0;
                            setSelectedPackageNumber(pn + id);
                        }
                    }else {
                        // 使用map方法提取id属性
                        const idArray = (informationData).map((item: any) => item.id);
                        setSelectedRowKeys(idArray);
                        ud.setSelectedRowKeys(idArray);
                        ud.setOldSelectedRowKeys(idArray);
                    }
                    setMedicineListOpen(true);
                }
            } else {
                setSelectedTotal(0);
                setSelectedData([]);
                page.setTotal(0);
                page.setData([]);
                setMedicineTableData([]);
            }
        });
    };

    const show = (record: any) => {
        if (record) {
            setRecord(record);
            setVisible(true);
            setIsOther(record.isOther);
        }
        let ubs = _.cloneDeep(record);
        if (ubs.batchNumber === null || ubs.batchNumber === undefined || ubs.batchNumber === "-") {
            ubs.batchNumber = "";
        }

        if (ubs.expirationDate === null || ubs.expirationDate === undefined || ubs.expirationDate === "-") {
            ubs.expirationDate = "";
        }
        //按状态分组查询出数据
        getBatchGroupStatusRun({
            customerId: auth.customerId,
            envId: auth.env.id,
            roleId: auth.project.permissions.role_id,
            batchNumber: ubs.batchNumber,
            expirationDate: ubs.expirationDate,
            name: record.name,
            positionId: record.positionId,
            type: record.type,
            count: record.count,
        }).then((result: any) => {
            let data = result.data;
            const statusCountMap: Map<number, number> = new Map();
            let key = [1, 2, 3, 4, 6, 7, 11, 14, 20];
            key.forEach((item) => {
                if (data[item] !== undefined && data[item] !== null) {
                    statusCountMap.set(item, data[item]);
                }
            });
            checkStatus(record, statusCountMap);
            setStatusCount(statusCountMap);
        });
    };

    const hide = () => {
        ud.setSelectedRowKeys(null);
        ud.setOldSelectedRowKeys(null);
        setSelectedData([]);
        setSelectedTotal(0);
        setSelectedPackageTotal(0);
        setSelectedPackageNumber(0);
        setSelectedNumber(0);
        setSelectedRowKeys([]);
        setMedicineTableData([]);
        setIsExistsPackageNumber(false);
        setExpirationDateValue(null);
        setVisible(false);
        setSubmitting(false);
        setUpdateCountDisable(true);
        setUpdateCountMax(0);
        setCanUpdate(true);
        setIsOther(false);
        setShowUpdateMessage(false);
        form.resetFields();
        props.refresh();
    };
    const { runAsync: getDepotBatchGroupRun, loading:getDepotBatchGroupLoading } = useFetch(getDepotBatchGroup, { manual: true });

    const showBatchTip = () => {
        getDepotBatchGroupRun({envId:envId}).then(
            (res:any) => {
                if (res.data.info?.length > 0) {
                    notification.open({
                        message: (
                            <div
                                style={{
                                    fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                    fontSize: "14px",
                                }}
                            >
                                <CheckCircleFilled
                                    color="#2FC25B"
                                    style={{ color: "#2FC25B", paddingRight: "8px" }}
                                />
                                <span>{formatMessage({ id: "visit.cycle.management.is.group.visit.batch.save" })}</span>

                            </div>
                        ),
                        // duration: 5,
                        placement: "top",
                        style: {
                            width: "720px",
                            // height: gb.lang === "zh"?"55px":"75px",
                            background: "#E2F7EC",
                            borderStyle: "solid",
                            border: "1px",
                            borderColor: "#41CC82",
                            borderRadius: "4px",
                        },
                        // closeIcon: null, // 隐藏关闭按钮，用户必须手动关闭
                        duration: null, // 设置为null，让通知框不自动关闭
                    });
                }else{
                    message.success(formatMessage({ id: "common.success" }));

                }
            }
        )


    }


    const save = () => {
        let updateBatchs = _.cloneDeep([record]);
        let ids = ud.selectedRowKeys;
        form.validateFields()
            .then((values) => {
                // let updateExpirationDate = values.updateExpirationDate
                //     ? moment(values.updateExpirationDate).format("YYYY-MM-DD")
                //     : "";
                updateBatchs.forEach((item: any) => {
                    if (item.batchNumber === null || item.batchNumber === undefined || item.batchNumber === "-") {
                        item.batchNumber = "";
                    }

                    if (item.expirationDate === null || item.expirationDate === undefined || item.expirationDate === "-") {
                        item.expirationDate = "";
                    }
                    if(item.name === "-"){
                        if(ud.selectedRowKeys){
                            item.count = ud.selectedRowKeys.length;
                            values.updateCount = ud.selectedRowKeys.length;
                        }
                    }
                });
                
                let updateExpirationDate = (values.updateExpirationDate !== null && values.updateExpirationDate !== undefined && values.updateExpirationDate !== "-") ? values.updateExpirationDate : "";
                
                updateBatchRun({
                    customerId,
                    projectId,
                    envId,
                    updateBatchs,
                    ...values,
                    updateExpirationDate: updateExpirationDate,
                    multUpdate: false,
                    idList: ids,
                }).then(() => {
                    showBatchTip()
                    hide();
                });
            })
            .catch(() => { });
    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang === "zh" ? 7 : 8 },
        },
        wrapperCol: {
            xs: { span: 24 },
            sm: { span: g.lang === "zh" ? 17 : 16 },
        },
    };

    const medicineListClose = () => {
        ud.setSelectedRowKeys(ud.oldSelectedRowKeys);
        setMedicineTableData([]);
        setMedicineListOpen(false);
    };


    const medicineListClick = () => {
        setUpperNumber("");
        setLowerNumber("");
        setUpperSequence("");
        setLowerSequence("");
        getSelectList(1);
    };

    React.useEffect(() => {
        getSelectList(0);
    }, [upperNumber, lowerNumber, upperSequence, lowerSequence]);

    function renderType(type: any) {
        return batchManagementTypes.find((it: any) => it.key === type)?.value;
    }

    const selectedStatus = () => {
        setSelectedRowKeys([]);
        var count = 0;
        let statuss = form.getFieldValue("status");
        statuss.forEach((e: any) => {
            if (
                statusCount.get(e) !== null &&
                statusCount.get(e) !== undefined
            ) {
                count = count + statusCount.get(e);
            }
        });
        setUpdateCountMax(count);
        ud.setSelectedRowKeys(null);
        ud.setOldSelectedRowKeys(null);
        form.setFieldsValue({ updateCount: count });
        if (record.name !== "-" && statuss.length <= 1) {
            setUpdateCountDisable(false);
            if (statuss.length === 1 && (statuss[0] === 20 || statuss[0] === 4)) { //已隔离、锁定
                setUpdateCountDisable(true);
            }
        } else {
            setUpdateCountDisable(true);
        }
        if (count > 0) {
            setCanUpdate(false);
        } else {
            setCanUpdate(true);
        }
    };

    const selectedInitialStatus = (statusCount: any) => {
        setSelectedRowKeys([]);
        var count = 0;
        let statuss = form.getFieldValue("status");
        statuss.forEach((e: any) => {
            if (
                statusCount.get(e) !== null &&
                statusCount.get(e) !== undefined
            ) {
                count = count + statusCount.get(e);
            }
        });
        setUpdateCountMax(count);
        ud.setSelectedRowKeys(null);
        ud.setOldSelectedRowKeys(null);
        form.setFieldsValue({ updateCount: count });
        if (record.name !== "-" && statuss.length <= 1) {
            setUpdateCountDisable(false);
            if (statuss.length === 1 && (statuss[0] === 20 || statuss[0] === 4)) { //已隔离、锁定
                setUpdateCountDisable(true);
            }
        } else {
            setUpdateCountDisable(true);
        }
        if (count > 0) {
            setCanUpdate(false);
        } else {
            setCanUpdate(true);
        }
    };

    const checkStatus = (record: any, statusCount: any) => {
        var showOptions: any[] = [];
        var options = batchStatus;
        if (record.type === 3) { //订单
            var medOrderStatus = 0;
            if (record.status === 6) {
                medOrderStatus = 11;
            } else if (record.status === 1) {
                medOrderStatus = 2;
            } else if (record.status === 2) {
                medOrderStatus = 3;
            }
            options.forEach((option: any) => {
                if (option.value === medOrderStatus) {
                    option.disabled = false;
                    showOptions.push(option)
                }
            });
            // if (statusCount.size > 0) {
            //     options.forEach((option: any) => {
            //         if (
            //             option.value === 2 ||
            //             option.value === 3 ||
            //             option.value === 11
            //         ) {
            //             if (
            //                 statusCount.get(option.value) !== null &&
            //                 statusCount.get(option.value) !== undefined
            //             ) {
            //                 option.disabled = false;
            //             } else {
            //                 option.disabled = true;
            //             }
            //             showOptions.push(option)
            //         }
            //     });
            // }
        } else {
            if (statusCount.size > 0) {
                options.forEach((option: any) => {
                    if (
                        option.value !== 2 &&
                        option.value !== 3 &&
                        option.value !== 11
                    ) {
                        if (
                            statusCount.get(option.value) !== null &&
                            statusCount.get(option.value) !== undefined
                        ) {
                            option.disabled = false;
                        } else {
                            option.disabled = true;
                        }
                        showOptions.push(option)
                    }
                });
            }
        }
        form.setFieldValue("status", showOptions?.filter((it: any) => !it.disabled).map((it: any) => it.value));
        selectedInitialStatus(statusCount);
        setStatusOptions(showOptions);
    };


    const checkUpdateCount = (e: any) => {
        if (record.isPackage && record.orderType !== 5 && record.orderType !== 6) {
            if (e % record.packageNumber > 0) {
                setShowUpdateMessage(true)
            } else {
                setShowUpdateMessage(false)
            }
        }
    }

    return (
        <React.Fragment>
            <Modal
                title={formatMessage({ id: "common.edit" })}
                visible={visible}
                onCancel={hide}
                maskClosable={false}
                centered
                destroyOnClose
                className="custom-medium-modal-new-batch"
                okButtonProps={{ loading: submitting, disabled: canUpdate }}
                onOk={save}
                okText={formatMessage({ id: "common.ok" })}
            >
                <Form form={form} layout="horizontal" {...formItemLayout}>
                    <Form.Item
                        label={formatMessage({
                            id: "drug.list.batch",
                        })}
                        name="batchNumber"
                    >
                        <Row>{record.batchNumber}</Row>
                    </Form.Item>
                    <Form.Item
                        label={formatMessage({ id: "drug.list.expireDate" })}
                        name="expirationDate"
                    >
                        <Row>{record.expirationDate}</Row>
                    </Form.Item>
                    <Form.Item
                        label={formatMessage({
                            id: "drug.configure.drugName",
                        })}
                        name="name"
                    >
                        <Row>{record.name}</Row>
                    </Form.Item>
                    <Form.Item
                        label={formatMessage({
                            id: "projects.statistics.sku.place",
                        })}
                        name="position"
                    >
                        <Row>{record.position}</Row>
                    </Form.Item>
                    <Form.Item
                        label={formatMessage({
                            id: "drug.other.singleCount",
                        })}
                        name="count"
                    >
                        <Row>{record.count}</Row>
                    </Form.Item>
                    <Form.Item
                        label={formatMessage({
                            id: "common.type",
                        })}
                        name="type"
                    >
                        <Row>{renderType(record.type)}</Row>
                    </Form.Item>
                    {
                        record.type != 3?
                        <Form.Item
                            label={formatMessage({
                                id: "drug.batch.management.status",
                            })}
                            rules={[{ required: true }]}
                            name="status"
                            // initialValue={record.type===3?(statusOptions?.filter((it: any) => !it.disabled).map((it: any) => it.value)):[]}
                        >
                            {/* {console.log("orderStatusOptions==" + JSON.stringify(record.type===3?(statusOptions?.filter((it: any) => !it.disabled).map((it: any) => it.value)):[]))} */}
                            <CustomCheckboxGroup
                                lang={g.lang}
                                options={statusOptions}
                                // defaultValue={record.type===3?(statusOptions?.filter((it: any) => !it.disabled).map((it: any) => it.value)):[]}
                                onChange={selectedStatus}
                            />
                        </Form.Item>:
                        <Form.Item
                            label={formatMessage({
                                id: "drug.batch.management.status",
                            })}
                            name="status"
                        >
                            {medicineStatusData(auth.codeRule,auth.project.info.research_attribute).find((it: any) => it.value === (record.status===6?11:(record.status===1?2:(record.status===2&&3))))?.label ?? "-"}
                        </Form.Item>
                    }

                    {
                        isOther ?
                            <Form.Item
                                //  style={{ marginTop: -16 }}
                                label={formatMessage({
                                    id: "common.update.count",
                                })}
                                name="updateCount"
                                validateStatus={showUpdateMessage ? "error" : ""}
                                help={showUpdateMessage ? formatMessage({
                                    id: "drug.batch.management.updateError",
                                }) : ""}
                            // rules={[{ required: true }]}
                            >
                                <InputNumber
                                    placeholder={formatMessage({
                                        id: "placeholder.input.common",
                                    })}
                                    precision={0}
                                    min={0}
                                    step={1}
                                    disabled={updateCountDisable}
                                    max={updateCountMax}
                                    controls
                                    className="full-width"
                                    onChange={(e: any) =>
                                        checkUpdateCount(e)
                                    }
                                />
                            </Form.Item> :
                            <Form.Item
                                //  style={{ marginTop: -16 }}
                                label={formatMessage({
                                    id: "common.update.count",
                                })}
                                name="updateCount"
                                initialValue={updateCountMax + "/" + updateCountMax}
                            // rules={[{ required: true }]}
                            >
                                <>
                                    <Modal
                                        forceRender={true}
                                        className="custom-medium-modal-batch"
                                        style={{ width: "1200px" }}
                                        title={
                                            <span>
                                                <span
                                                    style={{
                                                        fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                                        fontSize: "16px",
                                                        fontWeight: 500,
                                                        lineHeight: "24px",
                                                        textAlign: "left",
                                                    }}
                                                >
                                                    {formatMessage({ id: "common.select" })}
                                                </span>
                                            </span>
                                        }
                                        visible={medicineListOpen}
                                        onCancel={medicineListClose}
                                        destroyOnClose={true}
                                        centered
                                        maskClosable={false}
                                        footer={
                                            <Row justify="end">
                                                <Col style={{ marginRight: "16px" }}>
                                                    <Button onClick={medicineListClose}>
                                                        {formatMessage({ id: "common.cancel" })}
                                                    </Button>
                                                </Col>
                                                <Col>
                                                    <Button
                                                        type={"primary"}
                                                        className="full-width"
                                                        onClick={() => {
                                                            setUpperNumber("");
                                                            setLowerNumber("");
                                                            setUpperSequence("");
                                                            setLowerSequence("");
                                                            setMedicineListOpen(false);
                                                        }}
                                                    >
                                                        {formatMessage({ id: "common.ok" })}
                                                    </Button>
                                                </Col>
                                            </Row>
                                        }
                                    >
                                        {/* <Table rowSelection={rowSelection} columns={columns} dataSource={data} /> */}

                                        <div>
                                            <div>
                                                <Title name={formatMessage({ id: "drug.medicine" })}></Title>
                                                <Row>
                                                    <div style={{ textAlign: "left", display: "inline-block", marginTop: "16px", marginBottom: "16px" }}>
                                                        <Col style={{ textAlign: "left" }}>
                                                            {
                                                                formatMessage({
                                                                    id: "drug.list.drugNumber",
                                                                }) + "："
                                                            }
                                                            <Input
                                                                placeholder={formatMessage({
                                                                    id: "common.required.prefix",
                                                                })}
                                                                onChange={(e) => {
                                                                    setUpperNumber(e.target.value);
                                                                }}
                                                                value={upperNumber}
                                                                style={{ width: 100, marginRight: 12 }}
                                                                allowClear
                                                            />
                                                            ～
                                                            <Input
                                                                placeholder={formatMessage({
                                                                    id: "common.required.prefix",
                                                                })}
                                                                onChange={(e) => {
                                                                    setLowerNumber(e.target.value);
                                                                }}
                                                                value={lowerNumber}
                                                                style={{ width: 100, marginLeft: 12 }}
                                                                allowClear
                                                            />
                                                            <span style={{ marginLeft: "12px" }}>
                                                                {formatMessage({
                                                                    id: "drug.list.serialNumber",
                                                                }) + "："}
                                                            </span>
                                                            <Input
                                                                placeholder={formatMessage({
                                                                    id: "common.required.prefix",
                                                                })}
                                                                onChange={(e) => {
                                                                    setUpperSequence(e.target.value);
                                                                }}
                                                                value={upperSequence}
                                                                style={{ width: 100, marginRight: 12 }}
                                                                allowClear
                                                            />
                                                            ～
                                                            <Input
                                                                placeholder={formatMessage({
                                                                    id: "common.required.prefix",
                                                                })}
                                                                onChange={(e) => {
                                                                    setLowerSequence(e.target.value);
                                                                }}
                                                                value={lowerSequence}
                                                                style={{ width: 100, marginLeft: 12 }}
                                                                allowClear
                                                            />
                                                            {
                                                                ((upperNumber !== null && upperNumber !== undefined && upperNumber !== "") ||
                                                                    (lowerNumber !== null && lowerNumber !== undefined && lowerNumber !== "") ||
                                                                    (upperSequence !== null && upperSequence !== undefined && upperSequence !== "") ||
                                                                    (lowerSequence !== null && lowerSequence !== undefined && lowerSequence !== "")) &&
                                                                <Link
                                                                    className="mar-rgt-5"
                                                                    style={{ marginLeft: "8px" }}
                                                                    onClick={(e: any) => {
                                                                        setUpperNumber("");
                                                                        setLowerNumber("");
                                                                        setUpperSequence("");
                                                                        setLowerSequence("");
                                                                    }}
                                                                >
                                                                    {formatMessage({ id: "common.pagination.empty" })}
                                                                </Link>
                                                            }
                                                        </Col>
                                                    </div>
                                                </Row>
                                                <PageContextProvider>
                                                    <MedicineDataView
                                                        data={medicineTableData}
                                                        selectedRowKeys={selectedRowKeys}
                                                        selectedNumber={selectedNumber}
                                                        selectedPackageNumber={selectedPackageNumber}
                                                        selectedPackageTotal={selectedPackageTotal}
                                                        isExistsPackageNumber={isExistsPackageNumber}
                                                        selectedTotal={selectedTotal}
                                                        medicineListOpen={medicineListOpen}
                                                    />
                                                </PageContextProvider>
                                            </div>
                                        </div>
                                    </Modal>

                                    <span>
                                        {
                                            updateCountMax ?
                                                (ud.selectedRowKeys ?
                                                    <span>
                                                        {ud.selectedRowKeys.length}/{updateCountMax}
                                                    </span> :
                                                    <span>{updateCountMax}/{updateCountMax}</span>
                                                ) : "-"
                                        }
                                    </span>
                                    {
                                        permissions(
                                            auth.project.permissions,
                                            "operation.build.medicine.batch.edit"
                                        ) && projectStatus !== 2 && <Link
                                            className="mar-rgt-5"
                                            style={{ marginLeft: "8px" }}
                                            onClick={(e: any) => {
                                                medicineListClick();
                                            }}
                                        >
                                            {formatMessage({ id: "common.select" })}
                                        </Link>
                                    }

                                </>
                            </Form.Item>
                    }

                    <Form.Item
                        label={formatMessage({
                            id: "drug.batch.management.update",
                        })}
                        rules={[{ required: true }]}
                        name="updateBatch"
                    >
                        <Input
                            placeholder={formatMessage({
                                id: "placeholder.input.common",
                            })}
                            allowClear
                            className="full-width"
                        />
                    </Form.Item>
                    <Form.Item
                        label={formatMessage({
                            id: "drug.batch.management.updateExpireDate",
                        })}
                        rules={[{ required: true }]}
                        name="updateExpirationDate"
                    >
                        {/* <DatePickers
                            placeholder={formatMessage({
                                id: "placeholder.select.common",
                            })}
                            className="full-width"
                        ></DatePickers> */}
                        <CustomDateTimePicker value={expirationDateValue} onChange={setExpirationDateValue} ph={'placeholder.select.common'} disabledTime={null}></CustomDateTimePicker>
                    </Form.Item>
                </Form>
            </Modal>
        </React.Fragment>
    );
};

interface LangCheckboxGroupProps
    extends React.ComponentProps<typeof Checkbox.Group> {
    lang: string;
}

const LangCheckboxGroup: React.FC<LangCheckboxGroupProps> = ({
    lang,
    ...restProps
}) => <Checkbox.Group {...restProps}/>;

const CustomCheckboxGroup = styled(LangCheckboxGroup)`
    padding: 4px;
    .ant-checkbox-group-item {
        margin-bottom: 24px;
    }
    ${(props) =>
        props.lang === "zh" &&
        `
            .ant-checkbox-group-item:nth-last-child(-n+4) {
                margin-bottom: 0 !important;
            }
        `}
    ${(props) =>
        props.lang !== "zh" &&
        `
            .ant-checkbox-group-item:nth-last-child(-n+3) {
                margin-bottom: 0 !important;
            }
        `}
`;
