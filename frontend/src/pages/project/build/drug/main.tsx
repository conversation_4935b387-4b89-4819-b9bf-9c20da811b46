import React, {useEffect} from "react";
import { Tabs } from "antd";
import { VisitIndex } from "./visit-index";
import { DrugIndex } from "./drug-configure-index";
import { MedicineList } from "./medicine-list";
import { BatchManagement } from "./batch-management";
import { MedicineOther } from "./medicine-other";
import { FormattedMessage } from "react-intl";
import { permissions } from "../../../../tools/permission";
import { BarcodeIndex } from "./barcode-index";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import { getBarcodeRule } from "../../../../api/barcode";
import { useSafeState } from "ahooks";
import { usePage } from "../../../../context/page";
import {useDrug} from "./context";
import {getProjectAttributeList} from "../../../../api/randomization";
import {BarcodeLabelIndex} from "./barcode_label";

export const Main = (props: any) => {
  const auth = useAuth();
  const page = usePage();
  const drug = useDrug()
  const cohorts = auth.env?.cohorts;
  const projectType = auth.project.info.type;
  const [codeRule0, setCodeRule0] = useSafeState<any>(null);
  const [codeRule1, setCodeRule1] = useSafeState<any>(null);
  const [codeCohorts, setCodeCohorts] = useSafeState<any>([]);
  const [dispensingCohorts, setDispensingCohorts] = useSafeState<any>([]); // 开启发药的cohorts
  const [isHideTabs, setIsHideTabs] = useSafeState<any>(false); // 再随机或者群组项目是否隐藏相关 tabs
  const envId = auth.env ? auth.env.id : null;
  const customerId = auth.customerId;
  const projectId = auth.project.id;

  const { runAsync: getBarcodeRun } = useFetch(getBarcodeRule, {
    manual: true,
  });

  const { runAsync: runGetProjectAttributeList } = useFetch( getProjectAttributeList,{ manual: true });

  useEffect(() => {
    if (!!props.tabKey) drug.setTabKey(props.tabKey)
  }, [props.tabKey])

  const tabChange = (newActiveKey: any) => {
    page.setCurrentPage(1);
    page.setPageSize(20);
    drug.setTabKey(newActiveKey); // 更新状态以反映新的活动标签页
  };

  React.useEffect(() => {
    getBarcodeRun({
      customerId: customerId,
      projectId: projectId,
      envId: envId,
    }).then((response: any) => {
      if (response) {
        let data = response.data;
        setCodeRule0(data.codeRule0);
        setCodeRule1(data.codeRule1);
        var cohortIds = data.cohortIds;
        if (cohortIds != null && cohortIds.length > 0) {
          var codeCohorts: any = [];
          cohorts.forEach((e: any) => {
            if (cohortIds.includes(e.id)) {
              codeCohorts.push(e);
            }
          });
          setCodeCohorts(codeCohorts);
        }
      }
    });
  }, []);

  React.useEffect(() => {
    runGetProjectAttributeList({ envId: envId })
      .then((response: any) => {
      if (response) {
        let data = response.data;
        if (data != null && data.length > 0) {
          let retCohorts: any = [];
          cohorts.forEach((cohort: any) => {
            let matchedData = data.find((item: any) => item.cohortId === cohort.id);
            if (matchedData && matchedData.info.dispensing) {
              retCohorts.push(cohort);
            }
          });
          setDispensingCohorts(retCohorts);
          if (projectType !== 1 && retCohorts.length === 0) {
            setIsHideTabs(true);
            drug.setTabKey("6");
          }
        }
      }
    });
  }, []);

  return (
    <Tabs
      destroyInactiveTabPane={true}
      size="small"
      // defaultActiveKey="1" 如果再随机或群组项目无发药的群组或阶段，默认设为6
      activeKey={drug.tabKey}
      tabPosition="top"
      onChange={tabChange}
    >
      {permissions(
        auth.project.permissions,
        "operation.build.medicine.visit.list"
      ) && !isHideTabs ? (
        <Tabs.TabPane
          tab={<FormattedMessage id="menu.projects.project.build.drug.visit" />}
          key="1"
        >
          {projectType === 1 ? (
            <VisitIndex />
          ) : (
            <Tabs
              destroyInactiveTabPane={true}
              size="small"
              defaultActiveKey="1"
              tabPosition="top"
              className="block-tab"
              items={dispensingCohorts.map((cohort: any, cohort_index: number) => {
                const id = cohort.id;
                return {
                  label: cohort.type === 1?cohort.name + " - " + cohort.re_random_name:cohort.name,
                  key: id,
                  children: <VisitIndex cohort={cohort} />,
                };
              })}
            ></Tabs>
          )}
        </Tabs.TabPane>
      ) : null}

      {permissions(
        auth.project.permissions,
        "operation.build.medicine.configuration.list"
      ) && !isHideTabs ? (
        <Tabs.TabPane tab={<FormattedMessage id="menu.projects.project.build.drug.config" />} key="2">
          {projectType === 1 ? (
            <DrugIndex />
          ) : (
            <Tabs
              destroyInactiveTabPane={true}
              size="small"
              className="block-tab"
              defaultActiveKey="1"
              tabPosition="top"
              items={dispensingCohorts.map((cohort: any, cohort_index: number) => {
                const id = cohort.id;
                return {
                  label: cohort.type === 1?cohort.name + " - " + cohort.re_random_name:cohort.name,
                  key: id,
                  children: <DrugIndex cohort={cohort} />,
                };
              })}
            ></Tabs>
          )}
        </Tabs.TabPane>
      ) : null}

      {codeRule1 &&
      permissions(
        auth.project.permissions,
        "operation.build.medicine.barcode.view"
      ) ? (
        <Tabs.TabPane tab={<FormattedMessage id="menu.projects.project.build.drug.barcode" />} key="6">
          {projectType === 1 ? (
            <BarcodeIndex />
          ) : (
            <Tabs
              destroyInactiveTabPane={true}
              size="small"
              defaultActiveKey="1"
              tabPosition="top"
              className="block-tab"
              items={codeCohorts.map((cohort: any, cohort_index: number) => {
                const id = cohort.id;
                return {
                  label: cohort.type === 1?cohort.name + " - " + cohort.re_random_name:cohort.name,
                  key: id,
                  children: <BarcodeIndex cohort={cohort} />,
                };
              })}
            ></Tabs>
          )}
        </Tabs.TabPane>
      ) : null}

      {permissions(
        auth.project.permissions,
        "operation.build.medicine.upload.list"
      ) && !isHideTabs ? (
        <Tabs.TabPane tab={<FormattedMessage id="menu.projects.project.build.drug.list" />} key="3">
          <MedicineList codeRule0={codeRule0} codeRule1={codeRule1} />
        </Tabs.TabPane>
      ) : null}

      {permissions(
        auth.project.permissions,
        "operation.build.medicine.otherm.list"
      ) && !isHideTabs ? (
        <Tabs.TabPane tab={<FormattedMessage id="menu.projects.project.build.drug.no_number" />} key="5">
          <MedicineOther />
        </Tabs.TabPane>
      ) : null}

      {permissions(
        auth.project.permissions,
        "operation.build.medicine.batch.list"
      ) && !isHideTabs ? (
        <Tabs.TabPane
          tab={<FormattedMessage id="menu.projects.project.build.drug.batch" />}
          key="4"
        >
          <BatchManagement />
        </Tabs.TabPane>
      ) : null}

      {permissions(
          auth.project.permissions,
          "operation.build.medicine.barcode_label.view"
      ) && !isHideTabs && (
          <Tabs.TabPane tab={<FormattedMessage id="menu.projects.project.build.drug.barcode_label" />} key="7">
            <BarcodeLabelIndex />
          </Tabs.TabPane>
      )}
    </Tabs>
  );
};
