import React from "react";
import {useIntl} from "react-intl";
import {Modal, Table, Tabs,} from "antd";
import {useAuth} from "../../../../context/auth";
import {useGlobal} from "../../../../context/global";
import {groupsAtom, overviewKeyAtom, overviewVisibleAtom, selectDataAtom, titleAtom} from "./ctx";
import {useAtom} from "jotai/index";

export const Overview = (props) => {

    const g = useGlobal()
    const intl = useIntl();
    const { formatMessage } = intl;

    const auth = useAuth()
    const [overviewVisible,setOverviewVisible] = useAtom(overviewVisibleAtom)
    const [title,] = useAtom(titleAtom)
    const [selectData,] = useAtom(selectDataAtom)
    const [groups,] = useAtom(groupsAtom)
    const [overviewKey,setOverviewKey] = useAtom(overviewKeyAtom)
    const hide = () => {
        setOverviewVisible(false);
    };
    const avgSDItem = () => {
        let group = []
        for (let i = 0; i < groups.length; i++) {
            let name = groups[i];
            group.push({
                    title: name + "(" + formatMessage({id: "simulate_random.detail.meanStandard"}) + ")",
                    dataIndex: name,
                    key: name,
                render: (text, record, index) =>record.avgSDs?.find((v)=>v.group === name)?.avg +"+/-"+record.avgSDs?.find((v)=>v.group === name)?.sd});
    }
        const projectColumn = [
            {
                title: intl.formatMessage({ id: "simulate_random.project" }), dataIndex: "name", key: "name", width:150,ellipsis: true
            },
            ...group
        ]
        const siteColumn = [
            {
                title: intl.formatMessage({ id: "common.serial" }), dataIndex: "#", key: "#", width:70,render: (text, record, index) => (index + 1)
            },
            {
                title: intl.formatMessage({ id: "common.site" }), dataIndex: "name", key: "name", width:150,ellipsis: true
            },
            ...group
        ]
        const factorColumn = [
            {
                title: intl.formatMessage({ id: "common.serial" }), dataIndex: "#", key: "#", width:70,render: (text, record, index) => (index + 1)
            },
            {
                title: intl.formatMessage({ id: "randomization.config.factors" }), dataIndex: "name", key: "name", width:150,ellipsis: true
            },
            ...group
        ]
        const countryColumn = [
            {
                title: intl.formatMessage({ id: "common.serial" }), dataIndex: "#", key: "#", width:70,render: (text, record, index) => (index + 1)
            },
            {
                title: intl.formatMessage({ id: "projects.attributes.country" }), dataIndex: "name", key: "name", width:150,ellipsis: true
            },
            ...group
        ]
        const regionColumn = [
            {
                title: intl.formatMessage({ id: "common.serial" }), dataIndex: "#", key: "#", width:70,render: (text, record, index) => (index + 1)
            },
            {
                title: intl.formatMessage({ id: "projects.attributes.regionLayered" }), dataIndex: "name", key: "name", width:150,ellipsis: true
            },
            ...group
        ]
        const combinationFactorColumn = [
            {
                title: intl.formatMessage({ id: "common.serial" }), dataIndex: "#", key: "#", width:70,render: (text, record, index) => (index + 1)
            },
            {
                title: intl.formatMessage({ id: "simulate_random.combination.factor" }), dataIndex: "name", key: "name", width:150,ellipsis: true,
                render: (text, record, index) => (
                   record.labels.map((v,i)=><p style={{marginBottom: -5}}>{v}</p>)
                )
            },
            ...group
        ]

        let item = [
            {
                key: '1',
                label: formatMessage({id: 'simulate_random.project.overview'}),
                children: <>
                    <Table
                        className="mar-top-10"
                        scroll={{ x: "max-content" }}
                        dataSource={[selectData?.simulationRandomResult.avgSDOverview.projectOverView]}
                        pagination={false}
                        style={{ overflowX: "auto" }}
                        rowKey={(record) => (record.id)}
                        columns={projectColumn}
                    />
                </>,
            },
            {
                key: '2',
                label: formatMessage({id: 'simulate_random.site.overview'}),
                children: <>
                    <Table
                        className="mar-top-10"
                        scroll={{ x: "max-content" }}
                        dataSource={selectData?.simulationRandomResult?.avgSDOverview.siteOverView}
                        pagination={false}
                        style={{ overflowX: "auto" }}
                        rowKey={(record) => (record.id)}
                        columns={siteColumn}
                    />
                </>,
            },
            {
                key: '3',
                label: formatMessage({id: 'simulate_random.layered.overview'}),
                children: <>
                    <Table
                        className="mar-top-10"
                        scroll={{ x: "max-content" }}
                        dataSource={selectData?.simulationRandomResult?.avgSDOverview.factorOverView}
                        pagination={false}
                        style={{ overflowX: "auto" }}
                        rowKey={(record) => (record.id)}
                        columns={factorColumn}
                    />
                </>,
            },
        ]
        if (selectData?.countryQuantity>0){
            item.push(
                {
                    key: '4',
                    label: formatMessage({id: 'simulate_random.country.overview'}),
                    children: <>
                        <Table
                            className="mar-top-10"
                            scroll={{x: "max-content"}}
                            dataSource={selectData?.simulationRandomResult?.avgSDOverview.countryOverView}
                            pagination={false}
                            style={{overflowX: "auto"}}
                            rowKey={(record) => (record.id)}
                            columns={countryColumn}
                        />
                    </>,
                }
            )
        }
        if (selectData?.regionQuantity>0){
            item.push(
                {
                    key: '5',
                    label: formatMessage({id: 'simulate_random.region.overview'}),
                    children: <>
                        <Table
                            className="mar-top-10"
                            scroll={{ x: "max-content" }}
                            dataSource={selectData?.simulationRandomResult?.avgSDOverview.regionOverView}
                            pagination={false}
                            style={{ overflowX: "auto" }}
                            rowKey={(record) => (record.id)}
                            columns={regionColumn}
                        />
                    </>,
                }
            )
        }
        item.push(
            {
                key: '6',
                label: formatMessage({id: 'simulate_random.combination.factor.overview'}),
                children: <>
                    <Table
                        // loading={loading}
                        className="mar-top-10"
                        scroll={{ x: "max-content" }}
                        dataSource={selectData?.simulationRandomResult?.avgSDOverview.combinationFactorOverView}
                        pagination={false}
                        style={{ overflowX: "auto" }}
                        rowKey={(record) => (record.id)}
                        columns={combinationFactorColumn}
                    />
                </>,
            }
        )
        return item
    }

    const minItem = () => {
        let group = []
        for (let i = 0; i < groups.length; i++) {
            let name = groups[i];
            group.push({ title: name+"("+formatMessage({id:"simulate_random.subject.count.min"})+")", dataIndex: name, key: name,
                render: (text, record, index) =>record.mins?.find((v)=>v.group === name)?record.mins.find((v)=>v.group === name).min:0});
        }
        const projectColumn = [
            {
                title: intl.formatMessage({ id: "simulate_random.project" }), dataIndex: "name", key: "name", width:150,ellipsis: true
            },
            ...group
        ]
        const siteColumn = [
            {
                title: intl.formatMessage({ id: "common.serial" }), dataIndex: "#", key: "#", width:70,render: (text, record, index) => (index + 1)
            },
            {
                title: intl.formatMessage({ id: "common.site" }), dataIndex: "name", key: "name", width:150,ellipsis: true
            },
            ...group
        ]
        const factorColumn = [
            {
                title: intl.formatMessage({ id: "common.serial" }), dataIndex: "#", key: "#", width:70,render: (text, record, index) => (index + 1)
            },
            {
                title: intl.formatMessage({ id: "randomization.config.factors" }), dataIndex: "name", key: "name", width:150,ellipsis: true
            },
            ...group
        ]
        const countryColumn = [
            {
                title: intl.formatMessage({ id: "common.serial" }), dataIndex: "#", key: "#", width:70,render: (text, record, index) => (index + 1)
            },
            {
                title: intl.formatMessage({ id: "projects.attributes.country" }), dataIndex: "name", key: "name", width:150,ellipsis: true
            },
            ...group
        ]
        const regionColumn = [
            {
                title: intl.formatMessage({ id: "common.serial" }), dataIndex: "#", key: "#", width:70,render: (text, record, index) => (index + 1)
            },
            {
                title: intl.formatMessage({ id: "projects.attributes.regionLayered" }), dataIndex: "name", key: "name", width:150,ellipsis: true
            },
            ...group
        ]
        const combinationFactorColumn = [
            {
                title: intl.formatMessage({ id: "common.serial" }), dataIndex: "#", key: "#", width:70,render: (text, record, index) => (index + 1)
            },
            {
                title: intl.formatMessage({ id: "simulate_random.combination.factor" }), dataIndex: "name", key: "name", width:150,ellipsis: true,
                render: (text, record, index) => (
                    record.labels.map((v,i)=><p style={{marginBottom: -5}}>{v}</p>)
                )
            },
            ...group
        ]

        let item = [
            {
                key: '1',
                label: formatMessage({id: 'simulate_random.project.overview'}),
                children: <>
                    <Table
                        className="mar-top-10"
                        scroll={{ x: "max-content" }}
                        dataSource={[selectData?.simulationRandomResult?.minOverview.projectOverView]}
                        pagination={false}
                        style={{ overflowX: "auto" }}
                        rowKey={(record) => (record.id)}
                        columns={projectColumn}
                    />
                </>,
            },
            {
                key: '2',
                label: formatMessage({id: 'simulate_random.site.overview'}),
                children: <>
                    <Table
                        className="mar-top-10"
                        scroll={{ x: "max-content" }}
                        dataSource={selectData?.simulationRandomResult?.minOverview.siteOverView}
                        pagination={false}
                        style={{ overflowX: "auto" }}
                        rowKey={(record) => (record.id)}
                        columns={siteColumn}
                    />
                </>,
            },
            {
                key: '3',
                label: formatMessage({id: 'simulate_random.layered.overview'}),
                children: <>
                    <Table
                        className="mar-top-10"
                        scroll={{ x: "max-content" }}
                        dataSource={selectData?.simulationRandomResult?.minOverview.factorOverView}
                        pagination={false}
                        style={{ overflowX: "auto" }}
                        rowKey={(record) => (record.id)}
                        columns={factorColumn}
                    />
                </>,
            },
        ]
        if (selectData?.countryQuantity>0){
            item.push(
                {
                    key: '4',
                    label: formatMessage({id: 'simulate_random.country.overview'}),
                    children: <>
                        <Table
                            className="mar-top-10"
                            scroll={{ x: "max-content" }}
                            dataSource={selectData?.simulationRandomResult?.minOverview.countryOverView}
                            pagination={false}
                            style={{ overflowX: "auto" }}
                            rowKey={(record) => (record.id)}
                            columns={countryColumn}
                        />
                    </>,
                }
            )
        }
        if (selectData?.regionQuantity>0){
            item.push(
                {
                    key: '5',
                    label: formatMessage({id: 'simulate_random.region.overview'}),
                    children: <>
                        <Table
                            className="mar-top-10"
                            scroll={{ x: "max-content" }}
                            dataSource={selectData?.simulationRandomResult?.minOverview.regionOverView}
                            pagination={false}
                            style={{ overflowX: "auto" }}
                            rowKey={(record) => (record.id)}
                            columns={regionColumn}
                        />
                    </>,
                }
            )
        }
        item.push(
            {
                key: '6',
                label: formatMessage({id: 'simulate_random.combination.factor.overview'}),
                children: <>
                    <Table
                        // loading={loading}
                        className="mar-top-10"
                        scroll={{ x: "max-content" }}
                        dataSource={selectData?.simulationRandomResult?.minOverview.combinationFactorOverView}
                        pagination={false}
                        style={{ overflowX: "auto" }}
                        rowKey={(record) => (record.id)}
                        columns={combinationFactorColumn}
                    />
                </>,
            }
        )
        return item
    }

    const unbalancedRunCountOverviewItem = () => {
        const projectColumn = [
            {
                title: intl.formatMessage({ id: "simulate_random.project" }), dataIndex: "name", key: "name", width:150,ellipsis: true
            },
            {
                title: intl.formatMessage({ id: "simulate_random.unbalanced.run.count" }), dataIndex: "count", key: "count", width:150,ellipsis: true
            },
        ]
        const siteColumn = [
            {
                title: intl.formatMessage({ id: "common.serial" }), dataIndex: "#", key: "#", width:70,render: (text, record, index) => (index + 1)
            },
            {
                title: intl.formatMessage({ id: "common.site" }), dataIndex: "name", key: "name", width:150,ellipsis: true
            },
            {
                title: intl.formatMessage({ id: "simulate_random.unbalanced.run.count" }), dataIndex: "count", key: "count", width:150,ellipsis: true
            },
        ]
        const factorColumn = [
            {
                title: intl.formatMessage({ id: "common.serial" }), dataIndex: "#", key: "#", width:70,render: (text, record, index) => (index + 1)
            },
            {
                title: intl.formatMessage({ id: "randomization.config.factors" }), dataIndex: "name", key: "name", width:150,ellipsis: true
            },
            {
                title: intl.formatMessage({ id: "simulate_random.unbalanced.run.count" }), dataIndex: "count", key: "count", width:150,ellipsis: true
            },
        ]
        const countryColumn = [
            {
                title: intl.formatMessage({ id: "common.serial" }), dataIndex: "#", key: "#", width:70,render: (text, record, index) => (index + 1)
            },
            {
                title: intl.formatMessage({ id: "projects.attributes.country" }), dataIndex: "name", key: "name", width:150,ellipsis: true
            },
            {
                title: intl.formatMessage({ id: "simulate_random.unbalanced.run.count" }), dataIndex: "count", key: "count", width:150,ellipsis: true
            },
        ]
        const regionColumn = [
            {
                title: intl.formatMessage({ id: "common.serial" }), dataIndex: "#", key: "#", width:70,render: (text, record, index) => (index + 1)
            },
            {
                title: intl.formatMessage({ id: "projects.attributes.regionLayered" }), dataIndex: "name", key: "name", width:150,ellipsis: true
            },
            {
                title: intl.formatMessage({ id: "simulate_random.unbalanced.run.count" }), dataIndex: "count", key: "count", width:150,ellipsis: true
            },
        ]
        const combinationFactorColumn = [
            {
                title: intl.formatMessage({ id: "common.serial" }), dataIndex: "#", key: "#", width:70,render: (text, record, index) => (index + 1)
            },
            {
                title: intl.formatMessage({ id: "simulate_random.combination.factor" }), dataIndex: "name", key: "name", width:150,ellipsis: true,
                render: (text, record, index) => (
                    record.labels.map((v,i)=><p style={{marginBottom: -5}}>{v}</p>)
                )
            },
            {
                title: intl.formatMessage({ id: "simulate_random.unbalanced.run.count" }), dataIndex: "count", key: "count", width:150,ellipsis: true
            },
        ]

        let item = [
            {
                key: '1',
                label: formatMessage({id: 'simulate_random.project.overview'}),
                children: <>
                    <Table
                        className="mar-top-10"
                        scroll={{ x: "max-content" }}
                        dataSource={[selectData?.simulationRandomResult?.unbalancedRunCountOverview.projectOverView]}
                        pagination={false}
                        style={{ overflowX: "auto" }}
                        rowKey={(record) => (record.id)}
                        columns={projectColumn}
                    />
                </>,
            },
            {
                key: '2',
                label: formatMessage({id: 'simulate_random.site.overview'}),
                children: <>
                    <Table
                        className="mar-top-10"
                        scroll={{ x: "max-content" }}
                        dataSource={selectData?.simulationRandomResult?.unbalancedRunCountOverview.siteOverView}
                        pagination={false}
                        style={{ overflowX: "auto" }}
                        rowKey={(record) => (record.id)}
                        columns={siteColumn}
                    />
                </>,
            },
            {
                key: '3',
                label: formatMessage({id: 'simulate_random.layered.overview'}),
                children: <>
                    <Table
                        className="mar-top-10"
                        scroll={{ x: "max-content" }}
                        dataSource={selectData?.simulationRandomResult?.unbalancedRunCountOverview.factorOverView}
                        pagination={false}
                        style={{ overflowX: "auto" }}
                        rowKey={(record) => (record.id)}
                        columns={factorColumn}
                    />
                </>,
            },
        ]
        if (selectData?.countryQuantity>0){
            item.push(
                {
                    key: '4',
                    label: formatMessage({id: 'simulate_random.country.overview'}),
                    children: <>
                        <Table
                            className="mar-top-10"
                            scroll={{ x: "max-content" }}
                            dataSource={selectData?.simulationRandomResult?.unbalancedRunCountOverview.countryOverView}
                            pagination={false}
                            style={{ overflowX: "auto" }}
                            rowKey={(record) => (record.id)}
                            columns={countryColumn}
                        />
                    </>,
                }
            )
        }
        if (selectData?.regionQuantity>0){
            item.push(
                {
                    key: '5',
                    label: formatMessage({id: 'simulate_random.region.overview'}),
                    children: <>
                        <Table
                            className="mar-top-10"
                            scroll={{ x: "max-content" }}
                            dataSource={selectData?.simulationRandomResult?.unbalancedRunCountOverview.regionOverView}
                            pagination={false}
                            style={{ overflowX: "auto" }}
                            rowKey={(record) => (record.id)}
                            columns={regionColumn}
                        />
                    </>,
                }
            )
        }
        item.push(
            {
                key: '6',
                label: formatMessage({id: 'simulate_random.combination.factor.overview'}),
                children: <>
                    <Table
                        // loading={loading}
                        className="mar-top-10"
                        scroll={{ x: "max-content" }}
                        dataSource={selectData?.simulationRandomResult?.unbalancedRunCountOverview.combinationFactorOverView}
                        pagination={false}
                        style={{ overflowX: "auto" }}
                        rowKey={(record) => (record.id)}
                        columns={combinationFactorColumn}
                    />
                </>,
            }
        )
        return item
    }

    return (
        <>
            <Modal
                title={title}
                open={overviewVisible}
                onCancel={hide}
                maskClosable={false}
                centered
                destroyOnClose
                className='custom-large-modal'
                footer={null}
            >
                {
                    overviewKey === 1?
                        <Tabs
                            destroyInactiveTabPane={true}
                            size="small"
                            defaultActiveKey="1"
                            tabPosition="top"
                            items={avgSDItem()}
                        >
                        </Tabs>
                        :null
                }
                {
                    overviewKey === 2?
                        <Tabs
                            destroyInactiveTabPane={true}
                            size="small"
                            defaultActiveKey="1"
                            tabPosition="top"
                            items={minItem()}
                        >
                        </Tabs>
                        :null
                }
                {
                    overviewKey === 3?
                        <Tabs
                            destroyInactiveTabPane={true}
                            size="small"
                            defaultActiveKey="1"
                            tabPosition="top"
                            items={unbalancedRunCountOverviewItem()}
                        >
                        </Tabs>
                        :null
                }

            </Modal>
        </>
    )
};