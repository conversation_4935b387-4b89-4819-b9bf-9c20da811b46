import React, { ReactNode } from "react";
import { useSafeState } from "ahooks";

export const CodeRuleContext = React.createContext<{
    timeZone: number;
    setTimeZone: (data: number) => void;
} | null>(null);

export const CodeRuleProvider = ({ children }: { children: ReactNode }) => {
    const [timeZone, setTimeZone] = useSafeState<number>(8);

    return (
        <CodeRuleContext.Provider
            value={{
                timeZone,
                setTimeZone,
            }}
        >
            {children}
        </CodeRuleContext.Provider>
    );
};

export const useCodeRule = () => {
    const context = React.useContext(CodeRuleContext);
    if (!context) {
        throw new Error("useNotice must be used in NoticeContextProvider");
    }
    return context;
};
