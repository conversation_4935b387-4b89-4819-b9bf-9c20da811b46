import React, {useEffect} from "react";
import {Badge, Button, Col, message, Row, Table} from "antd";
import {SupplyPlanAdd} from "./supply-plan-add"
import {SupplyPlanMedicine} from "./supply-plan-medicine"
import {permissions} from '../../../../tools/permission'
import {useAuth} from "../../../../context/auth";
import {PaginationView} from "../../../../components/pagination";
import {usePage} from "../../../../context/page";
import {useFetch} from "../../../../hooks/request";
import {getSupplyPlanList, removeSupplyPlan} from "../../../../api/supply_plan";
import {CustomConfirmModal} from "components/modal";
import {InsertDivider} from "components/divider";
import {fillTableCellEmptyPlaceholder} from "../../../../components/table";
import {useSafeState} from "ahooks";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {AuthButton} from "../../../common/auth-wrap";


export const Plan = (props: any) => {
    const intl = useTranslation();
    const { formatMessage } = intl;


    const auth = useAuth();
    const page = usePage();

    const supply_ref = React.useRef()
    const medicine_show = React.useRef();

    const { runAsync, loading } = useFetch(getSupplyPlanList, { manual: true })
    const { runAsync: removeSupplyPlanRun } = useFetch(removeSupplyPlan, { manual: true })
    const [oldData,setOldData] = useSafeState([])

    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId;
    const projectStatus = auth.project.status ? auth.project.status : 0


    const showMedicine = (item: any) => {
        // @ts-ignore
        medicine_show.current.show(item);
    };

    const add = () => {
        // @ts-ignore
        supply_ref.current.show();
    };

    const update = (item: any) => {
        let updateData = oldData.find((i:any)=>i.id ===item.id)
        // @ts-ignore
        supply_ref.current.show(updateData);
    };
    const list = () => {
        const params = {
            "projectId": projectId,
            "customerId": customerId,
            "envId": envId,
        }
        runAsync({...params, "start": (page.currentPage - 1) * page.pageSize, "limit": page.pageSize}).then(
            (result: any) => {
                setOldData(JSON.parse(JSON.stringify(result.data.items)))
                let listData = fillTableCellEmptyPlaceholder(result.data.items)
                page.setData(listData)
                page.setTotal(result.data.total)
            }
        )
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
    useEffect(list, [page.pageSize, page.currentPage]);


    const deleteSupplyPlan = (id: any) => {
        CustomConfirmModal({
            title: formatMessage({ id: 'common.confirm.delete' }),
            okText:formatMessage({ id: 'common.ok' }),
            cancelText:formatMessage({ id: 'common.cancel' }),
            onOk: () => {
                removeSupplyPlanRun({ id }).then(
                    () => {
                        message.success(formatMessage({ id: 'message.save.success' }))
                        list()
                    }
                )
            }
        });
    };

    const renderOperationBtns = (item: any) => {
        const btns: any[] = [];
        if (permissions(auth.project.permissions, "operation.build.supply-plan.edit") && projectStatus !== 2) {
            btns.push(<AuthButton style={{padding:0}} size="small" type="link" onClick={() => { update(item) }}>
                <FormattedMessage id="common.edit" />
            </AuthButton>)
        }
        if (item.status === 0 && permissions(auth.project.permissions, "operation.build.supply-plan.delete") && projectStatus !== 2) {
            btns.push(<AuthButton style={{padding:0}}  size="small" type="link" onClick={() => { deleteSupplyPlan(item.id) }}>
                <FormattedMessage id="common.delete" />
            </AuthButton>)
        }
        return InsertDivider(btns)
    }


    // 展开表
    return (
        <React.Fragment>
            <Row >
                <Col>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                        <svg className="iconfont" width={16} height={16}>
                            <use xlinkHref="#icon-quanbugongyingjihua"/>
                        </svg>
                        <span style={{ color: "#1D2129", fontWeight: 600, paddingLeft: "8px" }}>{formatMessage({ id: 'projects.supplyPlan.all', allowComponent: true})}</span>
                    </div>
                </Col>
            </Row>
            {
                permissions(auth.project.permissions, "operation.build.supply-plan.add")
                && projectStatus !== 2 &&
                <Row justify="end">
                    <AuthButton type="primary" onClick={add}><FormattedMessage id="common.add" /></AuthButton>
                </Row>
            }
            {
                permissions(auth.project.permissions, "operation.build.supply-plan.view") ?
                    <Table
                        loading={loading}
                        rowKey={(record: any) => (record.id)}
                        className="mar-top-10"
                        dataSource={page.data}
                        pagination={false}
                    >
                        <Table.Column
                            title={intl.formatMessage({ id: "common.serial", allowComponent: true })}
                            dataIndex="#"
                            key="#"
                            width={70}
                            render={(value, record, index) => ((page.currentPage - 1) * page.pageSize + index + 1)} />
                        <Table.Column title={formatMessage({ id: 'projects.supplyPlan.name', allowComponent: true })} dataIndex="name" key="name" ellipsis
                            render={
                                (value, record) => (
                                    permissions(auth.project.permissions, "operation.build.supply-plan.medicine.view") ?
                                        <AuthButton size="small" type="link" onClick={() => { showMedicine(record) }}>{value}</AuthButton>
                                        :
                                        value
                                )
                            }
                        />
                        <Table.Column title={<FormattedMessage id="common.status"/>} dataIndex="status"
                                      width={80}
                                      key="status" ellipsis
                                      render={(value, record, index) => {
                                          if (value === 0) {
                                              return <div >
                                                  <Badge color={"#F96964"} style={{marginRight: "8px"}}/>
                                                  {formatMessage({id: 'common.invalid', allowComponent: true})}
                                              </div>
                                          } else if (value === 1) {
                                              return <div> <Badge color={"#41CC82"} style={{marginRight: "8px"}}/>{formatMessage({id: 'common.effective', allowComponent: true})}</div>
                                          }
                                      }}
                        />
                        <Table.Column title={<FormattedMessage id={'projects.supplyPlan.description'} />} dataIndex="description" key="description" ellipsis />
                        <Table.Column
                            title={<FormattedMessage id="common.operation" />}
                            width={200}
                            render={(value, record, index) => renderOperationBtns(record)}
                        />

                    </Table>
                    :
                    null
            }
            <PaginationView />
            <SupplyPlanMedicine bind={medicine_show} />
            <SupplyPlanAdd bind={supply_ref} refresh={list} />
        </React.Fragment>
    )
};
