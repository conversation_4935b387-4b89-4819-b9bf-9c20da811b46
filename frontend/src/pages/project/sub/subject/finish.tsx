import React from "react";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {Button, Col, Form, Input, Modal, Row} from "antd";
import {useFetch} from "../../../../hooks/request";
import {useSafeState} from "ahooks";
import {updateStatus} from "../../../../api/subject";
import {useAuth} from "../../../../context/auth";
import {useGlobal} from "../../../../context/global";
import {CustomConfirmModal} from "../../../../components/modal";

export const Finish = (props:any) => {
    const g = useGlobal()
    const auth = useAuth();
    const intl = useTranslation();
    const {formatMessage} = intl;

    const [visible, setVisible] = useSafeState<any>(false);
    const [record, setRecord] = useSafeState<any>({});
    const [form] = Form.useForm();

    const show = (record:any, type: any) => {
        setVisible(true);
        setRecord(record)
    };

    const hide = () => {
        setVisible(false);
        form.resetFields();
    };

    const {runAsync:updateStatusRun, loading:updateStatusLoading} = useFetch(updateStatus, {manual: true})


    // 受试者退出
    const save = () => {
        CustomConfirmModal({
            title: formatMessage({id:"subject.confirm.finish"}),
            cancelText: formatMessage({id: 'common.cancel'}),
            okText: formatMessage({id: 'common.ok'}),
            onOk: () => {
                form.validateFields()
                    .then(values => {
                        updateStatusRun({
                            "id": record.id,
                            sign:"4",
                            ...values,
                        }).then(
                            () => {
                                props.refresh();
                                hide();
                            }
                        )
                    })
                    .catch(() => { })
            }
        })


    };

    React.useImperativeHandle(props.bind, () => ({show}));

    const formItemLayout = {
        labelCol: { style: {   width: g.lang === "en"? "210px": "150px"} },
    };

    return (
        <>
            <Modal
                className="custom-little-modal"
                title={<FormattedMessage id="subject.finish.title" />}
                open={visible}
                centered
                maskClosable={false}
                
                onCancel={hide}
                destroyOnClose={true}
                footer={
                    <Row justify="end">
                        <Col style={{marginRight:"16px"}}>
                            <Button onClick={hide}>
                                <FormattedMessage id="common.cancel" />
                            </Button>
                        </Col>
                        <Col>
                            <Button onClick={ save } type="primary" loading={updateStatusLoading}>
                                <FormattedMessage id="common.ok" />
                            </Button>
                        </Col>
                    </Row>
                }
            >
                <Form form={form} {...formItemLayout}>
                    {auth.project.info.type === 2 && (
                        <Col>
                            <Form.Item
                                label={formatMessage({
                                    id: "projects.second",
                                })}
                            >
                                {record.cohort?.type === 1?record.cohortName + " - " + record.reRandomName:record.cohortName}
                            </Form.Item>
                        </Col>
                    )}
                    {auth.project.info.type === 3 && (
                        <Col>
                            <Form.Item
                                label={formatMessage({
                                    id: "common.stage",
                                })}
                            >
                                {record.cohortName}
                            </Form.Item>
                        </Col>
                    )}
                    <Form.Item 
                        label={
                            record.attribute
                            ?((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn === "")?(formatMessage({ id: "subject.number" })):((record.attribute.info.subjectReplaceText !== "" && record.attribute.info.subjectReplaceTextEn === "")?record.attribute.info.subjectReplaceText:((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn !== "")?(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:formatMessage({ id: "subject.number" })):(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:record.attribute.info.subjectReplaceText))))
                            :formatMessage({id:"subject.number"}) 
                        }
                    >
                        {record.shortname}
                    </Form.Item>
                    {
                        record.group!== "" && record.attribute && record.attribute.info.random?
                            <Form.Item label={ formatMessage({id:"projects.randomization.randomNumber"}) }>
                                {record.randomNumber}
                            </Form.Item>
                            :null
                    }
                    <Form.Item label={formatMessage({id: 'subject.finish.remark'})} name="finishRemark" className="mar-ver-5">
                        <Input.TextArea placeholder={formatMessage({id:"common.required.prefix"})} allowClear className="full-width" showCount maxLength={500}/>
                    </Form.Item>
                </Form>
            </Modal>
        </>
    )
};