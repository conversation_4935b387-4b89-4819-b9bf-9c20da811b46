import React, {ReactNode, useCallback, useEffect, useMemo, useRef, useState,} from "react";
import {
    Badge,
    Col,
    ConfigProvider,
    Dropdown,
    Empty,
    Form,
    Input,
    Menu,
    message,
    Modal,
    notification,
    Pagination,
    Result,
    Row,
    Select,
    Space,
    Spin,
    Table,
    TableProps,
    Typography
} from 'antd';
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {
    CheckCircleOutlined,
    CloseCircleFilled,
    DownOutlined,
    ExclamationCircleOutlined,
    InfoCircleFilled,
    SearchOutlined,
    UpOutlined,
} from "@ant-design/icons";
import {HistoryList} from "../../../common/history-list.jsx";
import {permissions, permissionsCohort} from "../../../../tools/permission.js";
import {useSafeState, useUpdateEffect} from "ahooks";
import {useAuth} from "../../../../context/auth.js";
import {atRandomSubjectStatus, nilObjectId, subjectStatus} from "../../../../data/data.jsx";
import {useFetch} from "../../../../hooks/request.js";
import {
    deleteSubject,
    downloadDispensingList,
    downloadRandomData,
    downloadRoomRecord,
    downloadUnblindingData,
    getInvalidSubject,
    getNewSubjectPage,
    getSubjectList,
    subjectEdcVerification,
    subjectRandom,
    userSites,
} from "../../../../api/subject.js";
import {RegisterAdd} from "./add.js";
import {Unblinding} from "./unblinding.js";
import {SignOut} from "./sign_out.js";
import {Transport} from "./transport.js";
import {SwitchCohort} from "./switch_cohort.js";
import {Replace} from "./replace.js";
import {Dispensing} from "./dispensing.js";
import {useSubject} from "./context.js";
import {Screen} from "./screen";
import {Finish} from "./finish";
import styled from "@emotion/styled";
import dayjs from "dayjs";
import {CustomConfirmModal} from "../../../../components/modal";
import {Title} from "../../../../components/title.js";
import {useGlobal} from "../../../../context/global.js";
import {InsertDivider} from "../../../../components/divider";
import {EdcVerificationTip} from "./edc_verification_tip";
import {inRandomIsolation} from "../../../../utils/in_random_isolation";
import {useAtom} from "jotai/index";
import {listDataAtom} from "./ctx";
import {pushScenarioFilter} from "../../../../utils/irt_push_edc_util";
import EmptyImg from "../../../../images/empty.png";
// 引入 Decimal.js
import {Decimal} from 'decimal.js';
import {
    AuthButton,
    authButtonFilter,
    AuthDropdown,
    clickFilter,
    previewFilter,
    previewShowFilter,
} from "../../../common/auth-wrap";

export const Subject = () => {
    const auth = useAuth();
    const intl = useTranslation();
    const ctx = useSubject();
    const { formatMessage } = intl;
    const g = useGlobal();
    const { Text } = Typography;

    const [currentPage, setCurrentPage] = useSafeState<any>(1);
    const [pageSize, setPageSize] = useSafeState<any>(20);
    const [total, setTotal] = useSafeState<any>(0);
    const [fieldsListData, setFieldsListData] = useSafeState<any>([]);
    const [reRandomFieldsListData, setReRandomFieldsListData] = useSafeState<any>([]);
    const [blind, setBlind] = useSafeState<any>(false);
    const [isRandomNumber, setRandomNumber] = useSafeState<any>(false);
    const [isRandomSequenceNumber, setRandomSequenceNumber] = useSafeState<any>(false);
    const subject_pt: any = React.useRef();
    const unblinding_pt: any = React.useRef();
    const sign_out_pt: any = React.useRef();
    const transport_site: any = React.useRef();
    const switch_cohort: any = React.useRef();
    const replace_pt: any = React.useRef();
    const edc_verification_tip_pt: any = React.useRef();
    const [data, setData] = useSafeState<any>([]);
    const [haveJustDispensing, setHaveJustDispensing] = useSafeState<any>(false);
    const [haveJustRandom, setHaveJustRandom] = useSafeState<any>(false);
    const [haveCohortReRandom, setHaveCohortReRandom] = useSafeState<any>(false);
    const [, setListData] = useAtom(listDataAtom);
    const [subjectNo, setSubjectNo] = useSafeState<any>("");
    const [subjectRecord, setSubjectRecord] = useSafeState<any>(null);
    const [subjectRecordShow, setSubjectRecordShow] = useSafeState<any>(false);
    const [invalidSubjectShow, setInvalidSubjectShow] = useSafeState<any>(false);
    const [invalidData, setInvalidData] = useSafeState<any>([]);

    const projectId = auth.project.id;
    const edcSupplier = auth.project.info.edc_supplier;
    const pushScenario = auth.project.info.push_scenario !== undefined ? auth.project.info.push_scenario.random_push : false;
    const customerId = auth.customerId;
    const envId = auth.env.id;
    const [confirmCohortName, setConfirmCohortName] = useSafeState<any>("");
    const projectNumber = auth.project.info.number ? auth.project.info.number : "";
    const projectStatus = auth.project.status ? auth.project.status : 0;
    const roleId = auth.project.permissions.role_id
    const connectEdc = auth.project.info.connect_edc;
    const projectType = auth.project.info.type;
    const pushMode = auth.project.info.push_mode;
    const cohorts = auth.env?.cohorts ? auth.env.cohorts : null;
    const researchAttribute = auth.project.info.research_attribute
        ? auth.project.info.research_attribute
        : 0;
    const dispensing_pt: any = React.useRef();
    const screen_pt: any = React.useRef();
    const finish_pt: any = React.useRef();
    const history_ref: any = React.useRef();
    const [downloadDropdownMenuVisible, setDownloadDropdownMenuVisible] =
        useSafeState<boolean>(false);
    const [loadingData, setLoadingData] = useSafeState(null)
    const [sites, setSites] = useSafeState([])
    const [paramSites, setParamSites] = useSafeState<any>([])
    const [paramSitesOp, setParamSitesOp] = useSafeState<any>(1)
    const [siteMode, setSiteMode] = useSafeState<any>({})
    const [siteOpen, setSiteOpen] = useSafeState(false)
    const [search, setSearch] = useSafeState(0)
    const [cohortId, setCohortId] = useSafeState<any>(null)
    const [cohortStatus, setCohortStatus] = useSafeState<any>(0)
    const [sortOrder, setSortOrder] = useState<any>({});
    const [sortField, setSortField] = useState<any>("");
    const [sortRule, setSortRule] = useState<any>(0);
    const [filterArr, setFilterArr] = useState<any>([]);
    const [newAddSubjectNumber, setNewAddSubjectNumber] = useSafeState<string>("")
    const newRowRef = useRef<HTMLTableRowElement | null>(null); // 存储新添加行的 DOM
    const [shortnameField, setShortnameField] = useSafeState<any>(null)

    const { runAsync: getSubjectListRun, loading: getSubjectListLoading } =
        useFetch(getSubjectList, {
            manual: true,

            onError: (err: any) => {

                err.json().then((data: any) => {
                    message.error(data.msg)
                    setData([])
                    setListData(null)
                    setTotal(0)
                }
                )
            },
        });

    const { runAsync: getNewSubjectPageRun, loading: getNewSubjectPageLoading } = useFetch(
        getNewSubjectPage,
        { manual: true }
    );

    const { runAsync: getInvalidSubjectRun, loading: getInvalidSubjectLoading } = useFetch(
        getInvalidSubject,
        { manual: true }
    );

    const { runAsync: userSitesRun, loading: userSitesLoading } = useFetch(
        userSites,
        { manual: true }
    );
    const { runAsync: subjectRandomRun, loading: subjectRandomLoading } =
        useFetch(subjectRandom, {
            manual: true,
            onError: (err: any) => {
                err.json().then((data: any) =>
                    notification.open({
                        message: (
                            <div
                                style={{
                                    fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                    fontSize: "14px",
                                }}
                            >
                                <CloseCircleFilled
                                    style={{
                                        color: "#F96964",
                                        paddingRight: "8px",
                                    }}
                                />
                                {formatMessage({ id: "subject.random.fail" })}
                            </div>
                        ),
                        description: (
                            <div
                                style={{
                                    paddingLeft: "20px",
                                    color: "#646566",
                                }}
                            >
                                {data.msg}
                            </div>
                        ),
                        duration: 5,
                        placement: "top",
                        style: {
                            width: "720px",
                            // height: "88px",
                            background: "#FEF0EF",
                            borderRadius: "4px",
                        },
                    })
                );
            },
        });
    const {
        runAsync: edcSubjectVerificationRun,
        loading: edcSubjectVerificationRunLoading,
    } = useFetch(subjectEdcVerification, { manual: true });

    const { runAsync: deleteSubjectRun } = useFetch(deleteSubject, {
        manual: true,
    });
    const {
        runAsync: downloadUnblindingDataRun,
        loading: downloadUnblindingDataLoading,
    } = useFetch(downloadUnblindingData, { manual: true });
    const {
        runAsync: downloadDispensingListRun,
        loading: downloadDispensingListLoading,
    } = useFetch(downloadDispensingList, { manual: true });
    const {
        runAsync: downloadRandomDataRun,
        loading: downloadRandomDataLoading,
    } = useFetch(downloadRandomData, { manual: true });
    const {
        runAsync: downloadRoomRecordRun,
        loading: downloadRoomRecordLoading,
    } = useFetch(downloadRoomRecord, { manual: true });
    React.useEffect(() => {
        // 查询中心
        userSitesRun({
            projectId: projectId,
            customerId: customerId,
            envId: envId,
            roleId: auth.project.permissions.role_id,
        }).then((result: any) => {
            setSites(result.data);
        });


    }, [projectId, envId, cohortId, g.lang]);

    const showHistory = (record: any) => {
        let chId = record.cohortId;
        if (projectType === 3 && !inRandomIsolation(auth.project.info.number) || (projectType === 2 && record.cohort.type === 1)){      // 在随机
            chId = record.cohort.id;
        }
        history_ref.current.show("history.subject", record.id, null, record.timeZone, chId, record.tz);
    };

    // const { runAsync: runGetProject, loading } = useFetch(() => getProject({ id: projectId }), {
    //     refreshDeps: [projectId],
    //     onSuccess: (result:any) => {
    //         console.log("kk==" + projectId);
    //         setEdcSupplier(result.data.info.edcSupplier)
    //     }
    // });

    // const {
    //     runAsync: runGetProject,
    //     loading: loading,
    // } = useFetch(getProject, { manual: true });

    function escapeRegexSpecialChars(str: any) {
        // 正则表达式特殊字符
        var specialChars = /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g;

        // 使用replace方法和函数作为第二个参数来转义每个匹配项
        return str.replace(specialChars, '\\$&');
    }

    const list: any = (cohortIdReturn: any, projectSiteIdReturn: any, newSubjectNumber: any) => {
        if (cohortIdReturn) {
            setCohortStatus(auth.env?.cohorts?.find((it: any) => it.id === cohortIdReturn).status)
            if (projectType === 2 || inRandomIsolation(auth.project.info.number)) {
                setCohortId(cohortIdReturn)
            }
        }

        let key = subjectNo;
        if (subjectNo !== null && subjectNo !== undefined) {
            key = subjectNo.trim();
        }

        let siteIdsParam = projectSiteIdReturn ? [projectSiteIdReturn] : paramSites;
        let cohortIdParam = cohortIdReturn ? cohortIdReturn : cohortId;

        let reSetPage
        if (projectSiteIdReturn) {
            setParamSites([projectSiteIdReturn])
            setParamSitesOp([projectSiteIdReturn])
            setSiteMode({})
            reSetPage = 1
            // 如果添加成功，查找在当前页面查询条件下，新增的受试者所处的页码
            if (newSubjectNumber != "") {
                setNewAddSubjectNumber(newSubjectNumber)
                getNewSubjectPageRun({
                    customerId,
                    envId,
                    projectId,
                    siteIds: projectSiteIdReturn ? [projectSiteIdReturn] : paramSites,
                    roleId: auth.project.permissions.role_id,
                    cohortId: cohortIdReturn ? cohortIdReturn : cohortId,
                    limit: pageSize,
                    keyword: key.trim(),
                    sortField: sortField,
                    sortCollation: sortRule,
                    status: filterArr,
                    newSubjectNumber: newSubjectNumber,
                }).then((result: any) => {
                    reSetPage = result.data;  // 接口返回的页码
                    if (reSetPage != currentPage) {
                        setCurrentPage(result.data);
                    } else {
                        ctx.setRefresh(ctx.refresh + 1);
                    }
                    fetchSubjectList(siteIdsParam, cohortIdParam, reSetPage)
                }).catch((err: any) => {
                    reSetPage = 1;  // 如果出错，默认第一页
                    setCurrentPage(1)
                    fetchSubjectList(siteIdsParam, cohortIdParam, 1)
                });
            }
        } else {
            fetchSubjectList(siteIdsParam, cohortIdParam, currentPage)
        }
    };

    // 获取受试者列表的函数
    const fetchSubjectList = (siteIds: any, cohortId: any, page: number) => {
        let key = subjectNo?.trim() || '';
        getSubjectListRun({
            customerId,
            envId,
            projectId,
            siteIds: siteIds,
            roleId: auth.project.permissions.role_id,
            cohortId: cohortId,
            start: (page - 1) * pageSize,
            limit: pageSize,
            keyword: key,
            sortField: sortField,
            sortCollation: sortRule,
            status: filterArr,
        })
            .then((result: any) => {
                let data = result.data;
                setHaveJustDispensing(data.haveJustDispensing);
                setHaveJustRandom(data.haveJustRandom);
                setHaveCohortReRandom(data.haveCohortReRandom)
                if (projectType === 1 || (projectType === 2 && !data.haveCohortReRandom) || inRandomIsolation(auth.project.info.number)) {
                    setFieldsListData(data.fields);
                } else if (projectType === 3 || (projectType === 2 && data.haveCohortReRandom)) {
                    setReRandomFieldsListData(data.reRandomForm);
                    if (data.fields === null || data.fields.length === 0) {
                        if (data.reRandomForm !== null && data.reRandomForm.length > 0) {
                            setFieldsListData(data.reRandomForm[0].fields);
                        }
                    } else {
                        setFieldsListData(data.fields);
                    }
                }
                setTotal(data.total);
                data = setDataFormat(data);
                setData(data.items);
                setListData(data);
                setBlind(data.blind);
                setRandomNumber(data.isRandomNumber);
                setRandomSequenceNumber(data.isRandomSequenceNumber);
                if (ctx.selectRecord && ctx.dispensingVisible) {
                    ctx.setSelectRecord(data.items?.find((it: any) => it.shortname === ctx.selectRecord.shortname));
                }
                if (newAddSubjectNumber !== "") {
                    // 页码渲染之后三秒，移除设置的新受试者所在行的背景色显示
                    setTimeout(() => {
                        setNewAddSubjectNumber(""); // 清空状态
                    }, 3000);
                }
            });
    };

    // 获取所有无效的受试者
    const showInvalidSubjectList = () => {
        setInvalidSubjectShow(true)
        getInvalidSubjectRun({
            projectId: projectId,
            envId: envId,
            roleId: roleId,
        }).then((result: any) => {
            let invalidData = result.data;
            invalidData = setDataFormat(invalidData);
            setInvalidData(invalidData.items);
        })
    }

    const hideInvalidSubjectList = () => {
        setInvalidSubjectShow(false);
    };

    // 当数据变化后，如果有新增的受试者，滚动到该行
    useEffect(() => {
        if (newRowRef.current) {
            newRowRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' }); // 平滑滚动到目标行
        }
    }, [data]);

    useEffect(() => {
        if (fieldsListData.length > 0) {
            let ret = fieldsListData?.filter((it: any) => it.name === "shortname")
            if (ret.length !== 0) {
                setShortnameField(ret[0])
            }
        }
    }, [fieldsListData]);

    useEffect(() => {
        // 重置页码
        if (currentPage !== 1) {
            setCurrentPage(1);
        } else {
            ctx.setRefresh(ctx.refresh + 1);
        }
    }, [search]);
    // 中心onChange事件

    const setDataFormat = (data: any) => {
        if (data.items === null) return [];
        let invalidDisplays = data.fields.filter((v: any) => v.invalidDisplay !== "");
        data.items.forEach((item: any, index: any) => {
            if ((projectType === 1 || (projectType === 2 && !data.haveCohortReRandom) || inRandomIsolation(auth.project.info.number)) && item.info != null) {
                // 基本研究 cohort
                item.info.forEach((field: any) => {
                    if (field.name === "shortname") {
                        data.items[index]["shortname"] = field.value;
                    }
                    let formField = item.form.fields.find((it: any) => it.name === field.name);
                    if ((formField != undefined || formField != null) && field.value != null) {
                        let label = formField.label
                        for (let i = 0; i < invalidDisplays.length; i++) {
                            if (invalidDisplays[i].name === field.name) {
                                label = formField.label + field.name;
                            }
                        }
                        if (formField.isCalc === false) {
                            data.items[index][label] = field.value;
                            data.items[index][formField.name] = field.value;
                            let type = formField ? formField.type : null;
                            if (type === "select" || type === "radio") {
                                if (formField.options.find((its: any) => its.value === field.value) !== undefined) {
                                    data.items[index][label] = formField.options.find((its: any) => its.value === field.value).label;
                                    data.items[index][formField.name] = formField.options.find((its: any) => its.value === field.value).label;
                                }
                            }
                            if (type === "checkbox") {
                                let opt = formField.options;
                                let opts = "";
                                if (field.value != null && Array.isArray(field.value)) {
                                    field.value.forEach((item: any) => {
                                        opts = opts + opt.find((it: any) => it.value === item).label + " ";
                                    });
                                }
                                data.items[index][label] = opts;
                                data.items[index][formField.name] = opts;
                            }
                            if (type === "switch") {
                                if (field.value === null) {

                                } else if (!field.value) {
                                    data.items[index][label] = "no";
                                    data.items[index][formField.name] = "no";
                                } else {
                                    data.items[index][formField.label] = "yes";
                                    data.items[index][formField.name] = "yes";
                                }
                            }
                            if (type === "datePicker") {
                                if (field.value != null && field.value) {
                                    let format = formField.dateFormat;
                                    if (format === null || !format) {
                                        format = "YYYY-MM-DD";
                                    }
                                    data.items[index][label] = dayjs(
                                        field.value
                                    ).format(format);
                                    data.items[index][formField.name] = dayjs(
                                        field.value
                                    ).format(format);
                                }
                            }

                            if (type === "timePicker") {
                                if (field.value != null && field.value) {
                                    let format = formField.timeFormat;
                                    if (format === null || !format) {
                                        format = "YYYY-MM-DD HH:mm:ss";
                                    }
                                    data.items[index][label] = dayjs(
                                        field.value
                                    ).format(format);
                                    data.items[index][formField.name] = dayjs(
                                        field.value
                                    ).format(format);
                                }
                            }
                        } else {
                            data.items[index][formField.label] = field.value;
                            if (formField.options.find((its: any) => its.value === field.value) !== undefined) {
                                data.items[index][label] = formField.options.find((its: any) => its.value === field.value).label;
                                data.items[index][formField.name] = formField.options.find((its: any) => its.value === field.value).label;
                            }
                        }

                    }
                });
            } else if (projectType === 3||(projectType === 2 && data.haveCohortReRandom)) {
                item.reRandomInfo.forEach((field: any) => {
                    data.items[index][field.cohortId + field.name] = field.value;
                    let reRandomFields = data.reRandomForm.find((it: any) => it.cohortId === field.cohortId)
                    let fields;
                    if (reRandomFields != null && reRandomFields.fields.length > 0) {
                        fields = reRandomFields.fields.find(
                            (it: any) => it.name === field.name
                        );
                    }
                    let type = fields ? fields.type : null;
                    if (fields !== null && fields !== undefined && field.value != null) {
                        if (fields.isCalc === true) {
                            type = "select"
                        }
                        if (type === "select" || type === "radio") {
                            if (
                                fields
                                    .options.find((its: any) => its.value === field.value) !== undefined
                            ) {
                                data.items[index][field.cohortId + field.name] = fields
                                    .options.find((its: any) => its.value === field.value).label;
                            }
                        }
                        if (type === "checkbox") {
                            let opt = reRandomFields.find(
                                (it: any) => it.name === field.name
                            ).options;
                            let opts = "";
                            if (field.value != null && Array.isArray(field.value)) {
                                field.value.forEach((item: any) => {
                                    opts =
                                        opts +
                                        opt.find((it: any) => it.value === item)
                                            .label +
                                        " ";
                                });
                            }
                            data.items[index][field.cohortId + field.name] = opts;
                        }
                        if (type === "switch") {
                            if (field.value === null) {
                            } else if (!field.value) {
                                data.items[index][field.cohortId + field.name] = "no";
                            } else {
                                data.items[index][field.cohortId + field.name] = "yes";
                            }
                        }
                        if (type === "datePicker") {
                            if (field.value != null && field.value) {
                                let format = fields.dateFormat;
                                if (format === null || !format) {
                                    format = "YYYY-MM-DD";
                                }
                                data.items[index][field.cohortId + field.name] = dayjs(
                                    field.value
                                ).format(format);
                            }
                        }
                        if (type === "timePicker") {
                            if (field.value != null && field.value) {
                                let format = fields.timeFormat;
                                if (format === null || !format) {
                                    format = "YYYY-MM-DD HH:mm:ss";
                                }
                                data.items[index][field.cohortId + field.name] = dayjs(
                                    field.value
                                ).format(format);
                            }
                        }
                    }

                });
                // 发药详情-受试者信息
                item.info.forEach((field: any) => {
                    if (field.name === "shortname") {
                        data.items[index]["shortname"] = field.value;
                    }
                    let formField = item.form.fields.find((it: any) => it.name === field.name);
                    if (formField != undefined || formField != null) {
                        if (formField.isCalc === false) {
                            data.items[index][formField.label] = field.value;
                            data.items[index][formField.name] = field.value;
                            let type = formField ? formField.type : null;
                            if (type === "select" || type === "radio") {
                                if (formField.options.find((its: any) => its.value === field.value) !== undefined) {
                                    data.items[index][formField.label] = formField.options.find((its: any) => its.value === field.value).label;
                                    data.items[index][formField.name] = formField.options.find((its: any) => its.value === field.value).label;
                                }
                            }
                            if (type === "checkbox") {
                                let opt = formField.options;
                                let opts = "";
                                if (field.value != null && Array.isArray(field.value)) {
                                    field.value.forEach((item: any) => {
                                        opts = opts + opt.find((it: any) => it.value === item).label + " ";
                                    });
                                }
                                data.items[index][formField.label] = opts;
                                data.items[index][formField.name] = opts;
                            }
                            if (type === "switch") {
                                if (field.value === null) {

                                } else if (!field.value) {
                                    data.items[index][formField.label] = "no";
                                    data.items[index][formField.name] = "no";
                                } else {
                                    data.items[index][formField.label] = "yes";
                                    data.items[index][formField.name] = "yes";
                                }
                            }
                            if (type === "datePicker") {
                                if (field.value != null && field.value) {
                                    let format = formField.dateFormat;
                                    if (format === null || !format) {
                                        format = "YYYY-MM-DD";
                                    }
                                    data.items[index][formField.label] = dayjs(field.value).format(format);
                                    data.items[index][formField.name] = dayjs(field.value).format(format);
                                }
                            }

                            if (type === "timePicker") {
                                if (field.value != null && field.value) {
                                    let format = formField.timeFormat;
                                    if (format === null || !format) {
                                        format = "YYYY-MM-DD HH:mm:ss";
                                    }
                                    data.items[index][formField.label] = dayjs(field.value).format(format);
                                    data.items[index][formField.name] = dayjs(field.value).format(format);
                                }
                            }
                        } else {
                            data.items[index][formField.label] = field.value;
                            if (formField.options.find((its: any) => its.value === field.value) !== undefined) {
                                data.items[index][formField.label] = formField.options.find((its: any) => its.value === field.value).label;
                                data.items[index][formField.name] = formField.options.find((its: any) => its.value === field.value).label;
                            }
                        }

                    }
                });
            }
        });
        return data;
    };

    const hide_confirm = () => {
        setSubjectRecord(null);
        setSubjectRecordShow(false);
        setConfirmCohortName("")
    };

    const edc_random_verification = () => {
        let dataP = { ...subjectRecord };
        dataP?.info.map((sr: any) => {
            dataP[sr.name] = sr.value;
        });
        edcSubjectVerificationRun(dataP).then((resp: any) => {
            if (!resp.data.linkStatus) {                 // 请求接口响应异常
                notification.open({
                    message: (
                        <div
                            style={{
                                fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                fontSize: "14px",
                            }}
                        >
                            <CheckCircleOutlined
                                style={{
                                    color: "#00BB00",
                                    paddingRight: "8px",
                                }}
                            />
                            {formatMessage({ id: "common.success" })}
                        </div>
                    ),
                    description: (
                        <div style={{ paddingLeft: "20px", color: "#646566" }}>
                            {formatMessage({
                                id: "subject.edc.interface.error",
                            })}
                        </div>
                    ),
                    duration: 5,
                    placement: "top",
                    style: {
                        width: "720px",
                        background: "#F0FFF0",
                        borderRadius: "4px",
                    },
                });
                random_save();
            } else if (!resp.data.subjectIsExist) {       // EDC不存在当前受试者
                Modal.confirm({
                    centered: true,
                    title: formatMessage({ id: "common.please.confirm" }),
                    icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                    content: formatMessage({ id: "subject.edc.create.subject" }),
                    onOk: () => {
                        if (auth.project.info.push_scenario.random_block_push || auth.project.info.push_scenario.form_random_block_push || (projectType !== 1 && auth.project.info.push_scenario.cohort_random_block_push)) {
                            let labelList = [];
                            if (auth.project.info.push_scenario.random_block_push) {
                                labelList.push(formatMessage({ id: "projects.subject.stratification" }));
                            }
                            if (auth.project.info.push_scenario.form_random_block_push) {
                                labelList.push(formatMessage({ id: "projects.subject.form" }));
                            }
                            if (projectType !== 1 && auth.project.info.push_scenario.cohort_random_block_push) {
                                if (projectType === 2) {
                                    labelList.push(formatMessage({ id: "projects.subject.cohortName" }));
                                } else if (projectType === 3) {
                                    labelList.push(formatMessage({ id: "projects.subject.stageName" }));
                                }

                            }

                            let labelStr = labelList.join("/");
                            message.error(formatMessage({ id: "subject.edc.inconsistent.information1" }, { label: labelStr }));
                        } else {
                            random_save();
                        }
                    },
                    cancelText: formatMessage({ id: "subject.edc.return.modification" }),
                    okText: formatMessage({ id: "subject.edc.continue.submitting" }),
                });
            } else if (resp.data.siteStatus == 1) {     // 1中心匹配
                edcRandomLogic(resp);
            } else if (resp.data.siteStatus == 2) {     // 2中心不匹配
                CustomConfirmModal({
                    icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                    title: formatMessage({ id: "common.tips" }),
                    content: formatMessage({ id: "subject.edc.site.inconsistent" }),
                    okText: formatMessage({ id: "subject.edc.continue.submitting" }),
                    cancelText: formatMessage({ id: "common.cancel" }),
                    onOk: () =>
                        edcRandomLogic(resp)
                });
            } else {                                    // 3未获取到中心编号或者名称
                CustomConfirmModal({
                    icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                    title: formatMessage({ id: "common.tips" }),
                    content: formatMessage({ id: "subject.edc.site.empty" }),
                    okText: formatMessage({ id: "subject.edc.continue.submitting" }),
                    cancelText: formatMessage({ id: "common.cancel" }),
                    onOk: () =>
                        edcRandomLogic(resp)
                });
            }
        });
    };

    // 随机逻辑
    const edcRandomLogic = (resp: any) => {
        if (resp.data.edcSubjectStatus === 2) {          // 筛选失败
            notification.open({
                message: (
                    <div
                        style={{
                            fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                            fontSize: "14px",
                        }}
                    >
                        <CloseCircleFilled
                            style={{
                                color: "#F96964",
                                paddingRight: "8px",
                            }}
                        />
                        {formatMessage({ id: "subject.random.fail" })}
                    </div>
                ),
                description: (
                    <div style={{ paddingLeft: "20px", color: "#646566" }}>
                        {formatMessage({
                            id: "subject.edc.random.failure.filter.failed",
                        })}
                    </div>
                ),
                duration: 5,
                placement: "top",
                style: {
                    width: "720px",
                    background: "#FEF0EF",
                    borderRadius: "4px",
                },
            });
        } else if (resp.data.edcSubjectStatus === 5) {    // 已退出
            notification.open({
                message: (
                    <div
                        style={{
                            fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                            fontSize: "14px",
                        }}
                    >
                        <CloseCircleFilled
                            style={{
                                color: "#F96964",
                                paddingRight: "8px",
                            }}
                        />
                        {formatMessage({ id: "subject.random.fail" })}
                    </div>
                ),
                description: (
                    <div style={{ paddingLeft: "20px", color: "#646566" }}>
                        {formatMessage({
                            id: "subject.edc.random.failure.exit",
                        })}
                    </div>
                ),
                duration: 5,
                placement: "top",
                style: {
                    width: "720px",
                    background: "#FEF0EF",
                    borderRadius: "4px",
                },
            });
        } else if (resp.data.edcSubjectStatus === 6) {    // 完成研究
            notification.open({
                message: (
                    <div
                        style={{
                            fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                            fontSize: "14px",
                        }}
                    >
                        <CloseCircleFilled
                            style={{
                                color: "#F96964",
                                paddingRight: "8px",
                            }}
                        />
                        {formatMessage({ id: "subject.random.fail" })}
                    </div>
                ),
                description: (
                    <div style={{ paddingLeft: "20px", color: "#646566" }}>
                        {formatMessage({
                            id: "subject.edc.random.failure.complete.the.study",
                        })}
                    </div>
                ),
                duration: 5,
                placement: "top",
                style: {
                    width: "720px",
                    background: "#FEF0EF",
                    borderRadius: "4px",
                },
            });
        } else if (resp.data.edcSubjectStatus === 8) {    // 筛选中不可随机
            notification.open({
                message: (
                    <div
                        style={{
                            fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                            fontSize: "14px",
                        }}
                    >
                        <CloseCircleFilled
                            style={{
                                color: "#F96964",
                                paddingRight: "8px",
                            }}
                        />
                        {formatMessage({ id: "subject.random.fail" })}
                    </div>
                ),
                description: (
                    <div style={{ paddingLeft: "20px", color: "#646566" }}>
                        {formatMessage({
                            id: "subject.edc.random.failure.screen.failed",
                        })}
                    </div>
                ),
                duration: 5,
                placement: "top",
                style: {
                    width: "720px",
                    background: "#FEF0EF",
                    borderRadius: "4px",
                },
            });
        } else {
            // EDC如果配置了分层、表单、受试者号、cohort
            if (resp.data.edcOpenStratification || resp.data.edcOpenForm || resp.data.edcOpenSubjectNo || resp.data.edcOpenCohort) {
                edcTip(resp.data, "random");
            } else {
                random_save();
            }
        }
    };

    const random_save = () => {
        subjectRandomRun({ id: subjectRecord.id, roleId: roleId }).then((resp: any) => {
            message.success(formatMessage({ id: "subject.random.success" }));
            setSubjectRecord(null);
            setSubjectRecordShow(false);
            setConfirmCohortName("")
            list(null, null);
            // if(cohortId != null){
            //     RefreshcohortStatus()
            // }
        });
    };

    // 受试者随机
    const subject_random = (record: any) => {
        // 在随机第二阶段要额外进行提示
        if (
            (
                (projectType === 3 && !inRandomIsolation(auth.project.info.number))||
                (projectType === 2 && record.cohort.type === 1)
            ) &&
            record.cohort.lastId != null &&
            record.cohort.lastId !== nilObjectId
        ) {
            let lastName = ""; // 上阶段名称
            auth.project.envs.forEach((it: any) => {
                it.cohorts.forEach((cohort: any) => {
                    if (cohort.id === record.cohort.lastId) {
                        lastName = cohort.name;
                    }
                });
            });
            CustomConfirmModal({
                title: formatMessage({ id: "common.tips" }),
                content:
                    formatMessage({ id: "subject.random.section1" }) +
                    "“" +
                    lastName +
                    "”" +
                    formatMessage({ id: "subject.random.section2" }) +
                    "“" +
                    record.cohortName +
                    "”" +
                    formatMessage({ id: "subject.random.section3" }) +
                    "?",
                okText: formatMessage({ id: "common.ok" }),
                onOk: () => {
                    let ccn =  record.cohortName
                    if(projectType === 2 && record.cohort.type === 1){
                        ccn = record.cohortName + " - " + record.reRandomName
                    }
                    setConfirmCohortName(ccn)
                    setSubjectRecord(record);
                    setSubjectRecordShow(true);
                },
            });
        } else {
            let ccn =  record.cohortName
            if(projectType === 2 && record.cohort.type === 1){
                ccn = record.cohortName + " - " + record.reRandomName
            }
            setConfirmCohortName(ccn)
            setSubjectRecord(record);
            setSubjectRecordShow(true);
        }
    };

    // 受试者删除
    const delete_subject = (record: any) => {
        let id = record.id;
        CustomConfirmModal({
            title: formatMessage({ id: "common.confirm.delete" }),
            content: formatMessage({
                id: "common.confirm.delete.unable.recover",
            }),
            okText: formatMessage({ id: "common.ok" }) + " ",
            cancelText: formatMessage({ id: "common.cancel" }) + " ",
            onOk: () =>
                deleteSubjectRun({ id }).then(() => {
                    list(null, null);
                }),
        });
    };

    const handleChange: TableProps<any>['onChange'] = (pagination: any, filters: any, sorter: any) => {
        setCurrentPage(1);
        setTotal(0)
        setData([])
        setListData(null)
        let filterList: any = []
        if (filters["status"]) {
            filterList = filters["status"]
            if (filterList.includes(4)) {
                filterList.push(5);
            }
        }
        setFilterArr(filters["status"])

    };

    // useEffect(() => {
    //     let key = subjectNo;
    //     if (subjectNo !== null && subjectNo !== undefined) {
    //         key = subjectNo.trim();
    //     }
    //     getSubjectListRun({
    //         customerId,
    //         envId,
    //         projectId,
    //         siteIds: paramSites,
    //         roleId: auth.project.permissions.role_id,
    //         cohortId: cohortId,
    //         start: (currentPage - 1) * pageSize,
    //         limit: pageSize,
    //         keyword: key.trim(),
    //         sortField: sortField,
    //         sortCollation: sortRule,
    //         status: filterArr,
    //     }).then((result: any) => {
    //
    //         let data = result.data;
    //         setHaveJustDispensing(data.haveJustDispensing)
    //         setHaveJustRandom(data.haveJustRandom)
    //         setHaveCohortReRandom(data.haveCohortReRandom)
    //         if (projectType === 1 || (projectType === 2 && !data.haveCohortReRandom) || inRandomIsolation(auth.project.info.number)) {
    //             setFieldsListData(data.fields);
    //         } else if (projectType === 3||(projectType === 2 && data.haveCohortReRandom)) {
    //             setReRandomFieldsListData(data.reRandomForm);
    //             if (data.fields === null || data.fields.length === 0) {
    //                 if (data.reRandomForm !== null && data.reRandomForm.length > 0) {
    //                     setFieldsListData(data.reRandomForm[0].fields);
    //                 }
    //             } else {
    //                 setFieldsListData(data.fields);
    //             }
    //         }
    //         setTotal(data.total);
    //         data = setDataFormat(data);
    //         setData(data.items);
    //         setListData(data);
    //         setBlind(data.blind)
    //         setRandomNumber(data.isRandomNumber)
    //         setRandomSequenceNumber(data.isRandomSequenceNumber)
    //     });
    //     },[])

    const handleSortClick = (field: string) => {
        setTotal(0)
        setData([])
        setListData(null)
        let sortCollation = 0;
        if (sortField.length !== 0 && sortField !== field) {
            setSortOrder("ascend");
            sortCollation = 1;
            setSortRule(1);
        } else {
            if (Object.keys(sortOrder).length === 0) {
                setSortOrder("ascend");
                sortCollation = 1;
                setSortRule(1);
            } else if (sortOrder === "ascend") {
                setSortOrder("descend");
                sortCollation = -1;
                setSortRule(-1);
            } else if (sortOrder === "descend") {
                setSortOrder({});
                sortCollation = 0;
                setSortRule(0);
            }
        }
        setSortField(field);
        // let key = escapeRegexSpecialChars(subjectNo);
        let key = subjectNo;
        if (subjectNo !== null && subjectNo !== undefined) {
            key = subjectNo.trim();
        }
    };

    // 受试者登记
    const add = () => {
        let choId = cohortId;
        // 在随机
        if (projectType === 3 && !inRandomIsolation(auth.project.info.number)) {
            choId = cohorts[0].id;
        }else if (projectType === 2 && data.haveCohortReRandom){
            cohorts.find((i:any) => {
                if (i.id === cohortId && i.type === 1) {
                    let firstCohort = cohorts.find((j:any) => j.name === i.name && j.lastId === nilObjectId);
                    choId = firstCohort ? firstCohort.id : null;
                }
            })
        }
        subject_pt.current.show(
            null,
            1,
            sites,
            paramSites,
            auth.env.cohorts,
            choId
        );
    };

    // 受试者修改
    const update = (record: any) => {
        let choId = record.cohort.id;
        subject_pt.current.show(
            record,
            2,
            null,
            null,
            auth.env.cohorts,
            choId
        );
    };

    // 填写表单的随机 fixme 再随机需要走这里
    const randomForm = (record: any) => {
        let choId = record.cohort.id;
        subject_pt.current.show(
            record,
            3,
            null,
            null,
            auth.env.cohorts,
            choId
        );
    };

    // 受试者揭盲(pv|紧急)
    const unblinding = (record: any, type: any) => {
        ctx.setUnblindingDetailsSign(type);
        if (
            type === "2" &&
            auth.project.info.unblinding_control &&
            auth.project.info.unblinding_control === 1 &&
            auth.project.info.unblinding_type &&
            auth.project.info.unblinding_type === 1
        ) {
            // 紧急揭盲
            ctx.setCurrentRecord(record);
            ctx.setUnblindingApprovalVisible(true);
        } else if (
            type === "3" &&
            auth.project.info.unblinding_control &&
            auth.project.info.unblinding_control === 1 &&
            auth.project.info.pv_unblinding_type &&
            auth.project.info.pv_unblinding_type === 1
        ) {
            // pv揭盲
            ctx.setCurrentRecord(record);
            ctx.setPvUnblindingApprovalVisible(true);
        } else {
            unblinding_pt.current.show(record, type, record.form.fields);
        }
    };

    // 受试者退出
    const signOut = (record: any, type: any) => {
        sign_out_pt.current.show(record, type);
    };

    // 受试者转运
    const transportSite = (record: any) => {
        transport_site.current.show(record, sites);
    }

    // 受试者切换群组
    const switchCohort = (record: any) => {
        switch_cohort.current.show(record);
    }


    const edcTip = (record: any, type: string) => {
        edc_verification_tip_pt.current.show(record, type);
    };

    // 受试者替换
    const replace = (record: any) => {
        replace_pt.current.show(record, record.form.fields[0].label, record.form.fields, record.factorSign);
    };

    // 查看揭盲信息
    const unblindingDetails = (record: any, type: any) => {
        ctx.setUnblindingDetailsSign(type);
        ctx.setCurrentRecord(record);
        if (type === "2") {   // 紧急揭盲
            ctx.setUnblindingApprovalVisible(true);
        } else if (type === "3") {  // pv揭盲
            ctx.setPvUnblindingApprovalVisible(true);
        }
    };

    // 发药
    const dispensing = (record: any) => {
        ctx.setSelectRecord({ ...record })
        ctx.setDispensingVisible(true)
    };
    //筛选
    const screen = (record: any) => {
        screen_pt.current.show(record);
    };
    //完成研究
    const finish = (record: any) => {
        finish_pt.current.show(record);
    };

    // 揭盲数据下载
    function downloadUnblinding_data() {
        downloadUnblindingDataRun({
            customerId,
            projectId,
            envId,
            cohortId: cohortId,
            roleId: auth.project.permissions.role_id,
        })
            .then()
            .catch(() => {
                message.error(
                    intl.formatMessage({ id: "common.download.fail" })
                );
            });
    }

    // 随机数据下载
    function download_random_data() {
        downloadRandomDataRun({
            customerId,
            projectId,
            envId,
            cohortId: cohortId,
            roleId: auth.project.permissions.role_id,
        })
            .then()
            .catch(() => {
                message.error(
                    intl.formatMessage({ id: "common.download.fail" })
                );
            });
    }

    useUpdateEffect(list, [currentPage, pageSize, ctx.refresh, sortField, sortRule, sortOrder, filterArr]);

    function subjectStatusItem(value: any, record: any) {
        let obj = subjectStatus.find((it: any) => it.value === value);
        // 待随机后面需要跟一个阶段名称
        let showSecondStageName = "";
        if ((projectType === 3 && auth.env.cohorts[1]?.id === record.cohort.id && !inRandomIsolation(auth.project.info.number)) ||
            (projectType === 2  && record.cohort.type === 1 && record.cohort.lastId !== nilObjectId)) {
            obj = atRandomSubjectStatus.find((it: any) => it.value === value);
            if (value === 1 || value === 7) {
                showSecondStageName = projectType === 3?record.cohortName:record.cohort.reRandomName;
            }
        }
        if ((value === 1 || value === 7) && (record.attribute.info.dispensing && !record.attribute.info.random) && record.isDispensing) {
            obj = subjectStatus.find((it: any) => it.value === 11)
            showSecondStageName = ""; // 非待随机，重置为空
        }
        let textValue = (
            <>
                {obj?.label}
                {showSecondStageName != "" && <>({showSecondStageName})</>}
            </>
        );

        return <Badge color={obj?.color} text={textValue} />;
    }

    const downloadRoom = () => {
        downloadRoomRecordRun({
            customerId,
            projectId,
            envId,
            cohortId,
            roleId: auth.project.permissions.role_id,
        })
            .then()
            .catch(() => {
                message.error(
                    intl.formatMessage({ id: "common.download.fail" })
                );
            });
    };

    const selectRoomDownloadDispensing = () => {
        downloadDispensingListRun({
            customerId,
            projectId,
            envId,
            cohortId,
            roleId: auth.project.permissions.role_id,
        })
            .then()
            .catch(() => {
                message.error(
                    intl.formatMessage({ id: "common.download.fail" })
                );
            });
    };


    const getValue = (value: any, record: any, index: any, name: any) => {
        if (!value) {
            if (projectType === 3) {
                return <>-</>;
            } else {
                return <></>;
            }
        }
        if (record.form !== undefined && record.form !== null) {
            if (record.form.fields !== undefined && record.form.fields !== null && record.form.fields.length > 0) {
                for (let i = 0; i < record.form.fields.length; i++) {
                    if (record.form.fields[i].type === "inputNumber" && record.form.fields[i].formatType === "decimalLength" &&
                        (record.form.fields[i].length !== undefined && record.form.fields[i].length !== null) &&
                        (value !== undefined && value !== null && typeof value === 'number') && name === record.form.fields[i].name) {
                        var lengthString = record.form.fields[i].length.toString();
                        if (lengthString.indexOf(".") !== -1) {
                            const digits = getFractionDigits(record.form.fields[i].length);
                            value = value.toFixed(digits);
                        }
                    }
                }
            }
        }
        return <>{value}</>;
    };

    function getFractionDigits(f: any) {
        // 分离整数和小数部分
        // const fractionalPart = f - Math.trunc(f);
        const fractionalPart = getFractionalPart(f);

        // 将小数部分转换为字符串形式，以便于处理
        // 使用 toFixed(12) 来确保有足够的精度，然后去掉前面的 "0."
        let fractionStr = fractionalPart.toFixed(12).split('.')[1];

        // 移除末尾不必要的零
        fractionStr = fractionStr.replace(/0+$/, '');

        // 如果结果为空（即原数没有小数部分或小数部分全是0），则返回0
        if (fractionStr === '') {
            return 0;
        }

        // 返回小数部分作为整数
        return parseInt(fractionStr, 10);
    }

    function getFractionalPart(f: any) {
        // 使用 Decimal 来处理数字，避免精度丢失
        const number = new Decimal(f);
        const integerPart = number.trunc(); // 获取整数部分
        const fractionalPart = number.minus(integerPart); // 从小数中减去整数部分得到小数部分

        return fractionalPart.toNumber(); // 如果需要，将结果转换回普通 JavaScript 数字类型
    }


    const onSearch = (e: any) => {
        setSubjectNo(e.target.value);
        setCurrentPage(1);
        ctx.setRefresh(ctx.refresh + 1);
    };
    const formItemLayout = {
        labelCol: { style: { width: g.lang === "en" ? "180px" : "180px" } },
    };
    // 下载相关的按钮
    const _displayBtns = useMemo(() => {
        let btns = {
            randomDownload: false,
            dispensingDownload: false,
            unblindingDownload: false,
            roomDownload: false,
            // 便于判断是否显示下载菜单
            isAllFalse: true,
        };
        let suffix = researchAttribute === 0 ? "" : "-dtp";
        if (
            permissions(
                auth.project.permissions,
                `operation.subject${suffix}.download-random`
            ) &&
            auth.project.info.research_attribute === 1
        ) {
            btns.randomDownload = true;
        }
        if (
            permissions(
                auth.project.permissions,
                `operation.subject${suffix}.medicine.export`
            ) &&
            auth.project.info.research_attribute === 1 &&
            auth.attribute &&
            auth.attribute.info.dispensing
        ) {
            btns.dispensingDownload = true;
        }
        if (
            permissions(
                auth.project.permissions,
                `operation.subject${suffix}.download`
            ) &&
            auth.project.info.research_attribute === 1
        ) {
            btns.unblindingDownload = true;
        }
        btns.isAllFalse =
            !btns.randomDownload &&
            !btns.dispensingDownload &&
            !btns.unblindingDownload
        return btns;
    }, [auth.project, auth.attribute]);
    const closeDownloadMenu = useCallback(() => {
        setDownloadDropdownMenuVisible(false);
    }, []);
    const isDownloading =
        downloadRoomRecordLoading ||
        downloadUnblindingDataLoading ||
        downloadDispensingListLoading ||
        downloadRandomDataLoading;

    const downloadBtns = !_displayBtns.isAllFalse && (
        <>
            <Dropdown
                open={downloadDropdownMenuVisible}
                trigger={["click"]}
                onOpenChange={(v) => {
                    // 正在下载中，不受理事件
                    if (isDownloading) {
                        setDownloadDropdownMenuVisible(false);
                        return;
                    }
                    setDownloadDropdownMenuVisible(v);
                }}
                overlay={
                    <Menu>
                        {_displayBtns.randomDownload && (
                            <Menu.Item
                                onClick={() => {
                                    closeDownloadMenu();
                                    download_random_data();
                                }}
                            >
                                <FormattedMessage id="subject.random.download" />
                            </Menu.Item>
                        )}
                        {_displayBtns.dispensingDownload && (
                            <Menu.Item
                                onClick={() => {
                                    closeDownloadMenu();
                                    selectRoomDownloadDispensing();
                                }}
                            >
                                <FormattedMessage id="subject.dispensing.download" />
                            </Menu.Item>
                        )}
                        {_displayBtns.unblindingDownload && (
                            <Menu.Item
                                onClick={() => {
                                    closeDownloadMenu();
                                    downloadUnblinding_data();
                                }}
                            >
                                <FormattedMessage id="subject.unblinding.download" />
                            </Menu.Item>
                        )}
                        {/*{_displayBtns.roomDownload && (*/}
                        {/*    <Menu.Item*/}
                        {/*        onClick={() => {*/}
                        {/*            closeDownloadMenu();*/}
                        {/*            downloadRoom();*/}
                        {/*        }}*/}
                        {/*    >*/}
                        {/*        <FormattedMessage id="subject.dispensing.room.download" />*/}
                        {/*    </Menu.Item>*/}
                        {/*)}*/}
                    </Menu>
                }
            >
                <span>
                    <AuthButton show type="primary" loading={isDownloading}>
                        {formatMessage({ id: "common.download" })}{" "}
                        {downloadDropdownMenuVisible ? (
                            <UpOutlined />
                        ) : (
                            <DownOutlined />
                        )}
                    </AuthButton>
                </span>
            </Dropdown>
        </>
    );

    // 操作相关的按钮
    const _operationPermission = useMemo(() => {
        const op = {
            // 发药
            dispensing: false,
            // 随机
            random: false,
            // 轨迹
            trail: false,

            // 修改
            update: false,
            // 删除
            delete: false,
            // 替换
            replace: false,
            // 退出
            secede: false,
            //随机前停用
            secedeRegistered: false,
            //筛选
            screen: false,
            //完成研究
            finish: false,
            //转运
            transport: false,
        };
        let suffix = researchAttribute === 0 ? "" : "-dtp";
        permissions(
            auth.project.permissions,
            `operation.subject${suffix}.medicine.view-dispensing`
        ) && (op.dispensing = true);
        permissions(
            auth.project.permissions,
            `operation.subject${suffix}.random`
        ) && (op.random = true);
        permissions(
            auth.project.permissions,
            `operation.subject${suffix}.trail`
        ) && (op.trail = true);

        permissions(
            auth.project.permissions,
            `operation.subject${suffix}.update`
        ) && (op.update = true);
        permissions(
            auth.project.permissions,
            `operation.subject${suffix}.delete`
        ) && (op.delete = true);
        permissions(
            auth.project.permissions,
            `operation.subject${suffix}.replace`
        ) && (op.replace = true);
        permissions(
            auth.project.permissions,
            `operation.subject${suffix}.secede`
        ) && (op.secede = true);
        permissions(
            auth.project.permissions,
            `operation.subject${suffix}.secede-registered`
        ) && (op.secedeRegistered = true);
        permissions(
            auth.project.permissions,
            `operation.subject${suffix}.screen`
        ) && (op.screen = true);
        permissions(
            auth.project.permissions,
            `operation.subject${suffix}.finish`
        ) && (op.finish = true);
        permissions(
            auth.project.permissions,
            `operation.subject${suffix}.medicine.transport`
        ) && (op.transport = true);
        return op;
    }, [auth.project]);
    const renderOperations = (record: any) => {
        const blindCanDispensing = !(record.attribute.info.blindingRestrictions && record.status === 6)
        const pvCanDispensing = !(record.attribute.info.pvUnBlindingRestrictions && record.pvUnblindingStatus === 1);
        const allBtns: { [key: string]: any }[] = []
        const reRandomCohorts = cohorts.filter((item:any) =>  item.name === record.cohort?.name)
        //筛选
        allBtns.push({
            key: 'screen',
            label: "subject.screen",
            onClick: () => screen(record),
            show: (permissionsCohort(auth.project.permissions, "operation.subject.screen", record.cohortStatus) || permissionsCohort(auth.project.permissions, "operation.subject-dtp.screen", record.cohortStatus))
                && record.attribute.info.isScreen && record.status === 1 &&
                (projectType == 1 ||
                    inRandomIsolation(auth.project.info.number) ||
                    (projectType == 3 && cohorts[0].id == record.cohort.id) ||
                    (projectType === 2 && record.cohort.type === 0) ||
                    (projectType === 2 && record.cohort.type === 1 && reRandomCohorts[0]?.id === record.cohort.id)
                )
        })

        // 发药，随机，轨迹是主操作，有的话显示在外头
        // 中心交付模式 显示发药  DTP模式  显示发药申请
        const label = researchAttribute === 0 ? "subject.dispensing.dispensing" : "subject.dispensing.apply"
        allBtns.push({
            key: 'dispensing',
            label: label,
            onClick: () => dispensing(record),
            show: (permissionsCohort(auth.project.permissions, "operation.subject.medicine.view-dispensing", record.cohortStatus) || permissionsCohort(auth.project.permissions, "operation.subject-dtp.medicine.view-dispensing", record.cohortStatus)) &&
                record.dispensingSign
        })

        // 随机
        const randomShow =  (permissionsCohort(auth.project.permissions, "operation.subject.random", record.cohortStatus) || permissionsCohort(auth.project.permissions, "operation.subject-dtp.random", record.cohortStatus))
            && projectStatus === 0 && record.attribute.info.random &&
            (record.attribute.info.isScreen ? record.status === 7 : (record.status === 1 || record.status === 2)) &&
            record.sign
        // factorSign 为true 表示登记时不填，那么随机的时候就要填写
        // factorSign 为false 表示登记时填写了，那么随机的时候就无需填写
        const factorSign = (projectType === 3 && cohorts[0].id !== record.cohortId && !inRandomIsolation(auth.project.info.number)) ||
            (projectType === 2 && record.cohort.type === 1 && reRandomCohorts[0]?.id !== record.cohortId) ||
            record.factorSign
        if (factorSign) {
            allBtns.push({
                key: 'random',
                label: "subject.random",
                onClick: () => randomForm(record),
                show: randomShow
            })
        } else {
            allBtns.push({
                key: 'random',
                label: "subject.random",
                show: randomShow,
                normalProps: {
                    disabled: record.id != loadingData && subjectRandomLoading,
                    loading: record.id == loadingData && subjectRandomLoading,
                    onClick: () => {
                        setLoadingData(record.id)
                        subject_random(record);
                    }
                },
                moreProps: {
                    onClick: () => subject_random(record)
                }
            })
        }
        // 轨迹
        allBtns.push({key: 'history', label: "common.history", onClick: () => showHistory(record), show: _operationPermission.trail})

        // 受试者修改
        const updateScreen = record.attribute.info.isScreen
        const updateScreenTime = record.attribute.info.isScreen &&
            ([3, 5, 6, 7, 8, 9].includes(record?.status) || (record?.status === 4 && record?.isScreen !== null))
        const updateStop = (record?.signOutPeople !== nilObjectId && (record?.status === 4 || record?.status === 6 || record?.status === 9)) || record?.status === 5 || (record?.status === 4 && record?.pvUnblindingStatus)
        const haveForm = record?.form.fields.filter((it: any) => it.name !== "shortname" && it.stratification === false && it.modifiable === true).length > 0
        // 修改
        // 随机后不可修改
        allBtns.push({
            key: 'update',
            label: "subject.update",
            onClick: () => update(record),
            show: (permissionsCohort(auth.project.permissions, "operation.subject.update", record.cohortStatus) || permissionsCohort(auth.project.permissions, "operation.subject-dtp.update", record.cohortStatus))
                && projectStatus !== 2 && blindCanDispensing && pvCanDispensing &&
                ((connectEdc === 1 && pushMode === 2) || connectEdc !== 1) &&
                (updateScreen || updateScreenTime || updateStop || haveForm || (record?.group === ""))
        })

        // 删除
        // 随机后不可删除
        allBtns.push({
            key: 'delete',
            label: "common.delete",
            onClick: () => delete_subject(record),
            show: ((permissionsCohort(auth.project.permissions, "operation.subject.delete", record.cohortStatus) || permissionsCohort(auth.project.permissions, "operation.subject-dtp.delete", record.cohortStatus)) &&
                    !record.hasDispensing && // 有发药轨迹代表发过药 隐藏删除按钮
                    projectStatus !== 2 &&
                    record.status === 1 &&
                (projectType === 1 ||
                    (projectType === 2 && !data.haveCohortReRandom) ||
                    inRandomIsolation(auth.project.info.number) ||
                    (projectType == 3 && cohorts[0].id === record.cohort.id) ||
                    (projectType === 2 && data.haveCohortReRandom && record.cohort.type === 1 && reRandomCohorts[0]?.id === record.cohortId)
                )
            )
        })
        // 替换
        allBtns.push({
            key: 'replace',
            label: "subject.replace",
            onClick: () => replace(record),
            show:  (permissionsCohort(auth.project.permissions, "operation.subject.replace", record.cohortStatus) || permissionsCohort(auth.project.permissions, "operation.subject-dtp.replace", record.cohortStatus))
                && projectStatus !== 2 && record.status === 3 && record.replaceSign
        })
        // 停用
        allBtns.push({
            key: 'signOut',
            label: "subject.exited",
            onClick: () => signOut(record, "1"),
            show: (permissionsCohort(auth.project.permissions, "operation.subject.secede", record.cohortStatus) || permissionsCohort(auth.project.permissions, "operation.subject-dtp.secede", record.cohortStatus)) &&
                blindCanDispensing &&
                projectStatus !== 2 &&
                (record.status === 3 || record.status === 6)
        })

        // 随机前退出
        allBtns.push({
            key: 'signOut',
            label: "subject.exited",
            onClick: () => signOut(record, "1"),
            show:  (permissionsCohort(auth.project.permissions, "operation.subject.secede-registered", record.cohortStatus) || permissionsCohort(auth.project.permissions, "operation.subject-dtp.secede-registered", record.cohortStatus)) &&
                projectStatus !== 2 && (record.status === 1 || record.status === 7)
        })
        // 转运
        allBtns.push({
            key: 'transportSite',
            label: "subject.transport",
            onClick: () => transportSite(record),
            show:  (permissionsCohort(auth.project.permissions, "operation.subject.medicine.transport", record.cohortStatus) || permissionsCohort(auth.project.permissions, "operation.subject-dtp.medicine.transport", record.cohortStatus)) &&
                projectStatus !== 2 && record.status !== 4
        })
        // 切换群组
        allBtns.push({
            key: 'switchCohort',
            label: "subject.switch.cohort",
            onClick: () => switchCohort(record),
            show:  (permissionsCohort(auth.project.permissions, "operation.subject.switch.cohort", record.cohortStatus)) &&
                projectStatus !== 2 && projectType === 2 && record.cohort.type === 0 && (record.group === "" || record.group === null || record.group === undefined) && record.status !== 4 && !record.hasDispensing
        })
        //完成研究
        allBtns.push({
            key: 'finish',
            label: "subject.status.finish",
            onClick: () => finish(record),
            show:  (permissionsCohort(auth.project.permissions, "operation.subject.finish", record.cohortStatus) || permissionsCohort(auth.project.permissions, "operation.subject-dtp.finish", record.cohortStatus)) &&
                (
                    (blindCanDispensing && (record.status === 3 || record.status === 6))
                    || (record.isDispensing
                        && record.attribute.info.dispensing && !record.attribute.info.random
                        && (record.status === 1 || record.status === 7)
                    )
                )
        })
        // 主操作之间分隔符分割，次操作(如果有)放在dropdown中
        // TODO 尝试性重构受试者操作
        const allButtons = authButtonFilter(allBtns)
        const btns: ReactNode[] = allButtons.slice(0, 3).map((it: any) => {
            const otherProps = it.onClick ? {onClick: it.onClick} : it.normalProps
            return <AuthButton
                style={{ padding: 0 }}
                type="link"
                className="mouse"
                {...otherProps}
            >
                <FormattedMessage id={it.label} />
            </AuthButton>
        })
        const moreItems = allButtons.slice(3).map((it: any) => {
            const otherProps = it.onClick ? {onClick: it.onClick} : it.moreProps
            return {
                key: it.key,
                label: <FormattedMessage id={it.label} />,
                ...otherProps
            }
        })
        return (
            <div style={{ display: "flex", alignItems: "center" }}>
                {InsertDivider(btns)}
                <AuthDropdown show={moreItems.length > 0} menu={{items:moreItems}}>
                    <span style={{ marginLeft: 12 }} className="dropdown-button">
                        <i className="iconfont icon-gengduo-changgui" />
                    </span>
                </AuthDropdown>
            </div>
        );
    };
    const getScroll = (data: any) => {
        if (data?.length > 0) {
            return { y: "calc(100vh - 260px)", x: "max-content" }
        } else {
            return { x: "max-content" }
        }
    }

    const rowClassName = (record: any) => {
        return record.shortname === newAddSubjectNumber ? 'new-subject-highlight-row' : '';
    };


    const getSubjectValue = (value: any, record: any, index: any, name: any) => {
        if (!value) {
            if (projectType === 3) {
                return <>-</>;
            } else {
                return <></>;
            }
        }
        if (record.form !== undefined && record.form !== null) {
            if (record.form.fields !== undefined && record.form.fields !== null && record.form.fields.length > 0) {
                for (let i = 0; i < record.form.fields.length; i++) {
                    if (record.form.fields[i].type === "inputNumber" && record.form.fields[i].formatType === "decimalLength" &&
                        (record.form.fields[i].length !== undefined && record.form.fields[i].length !== null) &&
                        (value !== undefined && value !== null && typeof value === 'number') && name === record.form.fields[i].name) {
                        var lengthString = record.form.fields[i].length.toString();
                        if (lengthString.indexOf(".") !== -1) {
                            const digits = getFractionDigits(record.form.fields[i].length);
                            value = value.toFixed(digits);
                        }
                    }
                }
            }
        }
        return <>{value}</>;
    };

    return (
        <React.Fragment>
            <Row>
                <Col>
                    <div style={{ display: "flex", alignItems: "center" }}>
                        <svg className="iconfont" width={16} height={16}>
                            <use xlinkHref="#icon-quanbushoushizhe"></use>
                        </svg>
                        <span
                            style={{
                                color: "#1D2129",
                                fontWeight: 600,
                                paddingLeft: "8px",
                            }}
                        >
                            <FormattedMessage id={"subject.random.all"} />
                        </span>
                    </div>
                </Col>
            </Row>
            {(researchAttribute === 0
                ? permissions(
                    auth.project.permissions,
                    "operation.subject.view-list"
                )
                : permissions(
                    auth.project.permissions,
                    "operation.subject-dtp.view-list"
                )) && (
                    <Row justify="space-between" style={{ marginTop: 16 }}>
                        <Col>
                            <Input.Group compact>
                                {(projectType === 2 || inRandomIsolation(auth.project.info.number)) ?
                                    <>
                                        <Form.Item label={<span style={{ color: '#1D2129' }}>
                                            {
                                                projectType === 2 ?
                                                    <FormattedMessage id="projects.second" /> :
                                                    <FormattedMessage id="projects.third" />
                                            }
                                        </span>}>

                                            <Select
                                                value={cohortId}
                                                style={{ width: 220, marginRight: 12 }}
                                                onChange={(value: any) => {
                                                    setCohortId(value)
                                                    setCohortStatus(value !== null ? auth.env?.cohorts?.find((it: any) => it.id === value).status : 0)
                                                    setSearch(search + 1)
                                                }}
                                            >
                                                {projectType === 2 || inRandomIsolation(auth.project.info.number) ? <Select.Option value={null}>{formatMessage({ id: "common.all" })}</Select.Option> : null}
                                                {
                                                    inRandomIsolation(auth.project.info.number) ?
                                                        auth.env.cohorts?.map((item: any) => (
                                                            <Select.Option key={item.id} value={item.id}>
                                                                {item.name}
                                                            </Select.Option>
                                                        ))
                                                        :
                                                        auth.env.cohorts?.filter((it: any) => it.last_id === nilObjectId).map((item: any) => (
                                                            <Select.Option key={item.id} value={item.id}>
                                                                {item.name}
                                                            </Select.Option>
                                                        ))
                                                }
                                            </Select>

                                        </Form.Item>
                                    </> : null}

                                <Form.Item label={<span style={{ color: '#1D2129' }}><FormattedMessage id="common.site" /></span>}>
                                    <Select
                                        value={paramSitesOp}
                                        style={{ width: 300 }}
                                        {...siteMode}
                                        maxTagCount={1}
                                        maxTagTextLength={12}
                                        open={siteOpen}
                                        onBlur={() => {
                                            setSiteOpen(false)
                                            if (paramSitesOp !== null) {
                                                setSearch(search + 1)
                                            }
                                        }

                                        }
                                        showArrow={true}
                                        showSearch={false}
                                        onDropdownVisibleChange={(visible: boolean) => setSiteOpen(visible)}
                                        onChange={(value: any) => {
                                            if (value === 1 || (Array.isArray(value) && (value.find((i: any) => i === 1) || value.length === 0))) {
                                                if (siteMode.mode != null) {
                                                    setSiteMode({})
                                                    setSiteOpen(true)
                                                }
                                                setParamSites([])
                                                setParamSitesOp(1)
                                                setSearch(search + 1)
                                                setSiteOpen(false)
                                            } else {
                                                setSiteOpen(true)
                                                if (siteMode.mode !== "multiple") {
                                                    setSiteMode({ mode: "multiple", })
                                                    setSearch(search + 1)
                                                    setSiteOpen(true)
                                                }
                                                if (!Array.isArray(value)) {
                                                    let siteIds = [value];
                                                    setParamSites(siteIds)
                                                    setParamSitesOp(siteIds)
                                                } else {
                                                    setParamSites(value)
                                                    setParamSitesOp(value)
                                                }
                                            }
                                        }}
                                    >
                                        <Select.Option value={1}>{formatMessage({ id: "common.all" })}</Select.Option>
                                        {
                                            sites?.map((item: any) => {
                                                return <Select.Option value={item.id}>{item.number + "-" + item.name}</Select.Option>
                                            })
                                        }
                                    </Select>
                                </Form.Item>

                                <Input
                                    placeholder={formatMessage({ id: 'placeholder.input.common' })}
                                    onChange={onSearch}
                                    style={{ width: 220, marginLeft: 12 }}
                                    suffix={
                                        <SearchOutlined
                                            style={{ color: "#BFBFBF" }}
                                        />
                                    }
                                />
                            </Input.Group>
                        </Col>
                        <Col>
                            <AuthButton
                                show={permissionsCohort(auth.project.permissions, "operation.subject.invalid-list", cohortStatus)}
                                onClick={showInvalidSubjectList} style={{ marginRight: 12 }}
                            >
                                <FormattedMessage id="subject.invalid.list" />
                            </AuthButton>
                            {
                                projectStatus === 0 &&
                                    ((connectEdc === 1 && pushMode === 2) ||
                                        connectEdc !== 1) &&
                                    (researchAttribute === 0
                                        ? permissionsCohort(
                                            auth.project.permissions,
                                            "operation.subject.registered", cohortStatus
                                        )
                                        : permissionsCohort(
                                            auth.project.permissions,
                                            "operation.subject-dtp.registered", cohortStatus
                                        )) ? (
                                    (projectNumber === "ANG601-1001" && cohortStatus === 5 && auth.attribute?.info.random === false && auth.attribute?.info.dispensing === true) ? null :
                                        <AuthButton show onClick={add} style={{ marginRight: 12 }}>
                                            <FormattedMessage id="subject.register" />
                                        </AuthButton>
                                ) : null}
                            {/*下载按钮先屏蔽*/}
                            {downloadBtns}
                        </Col>
                    </Row>
                )}
            {
                (researchAttribute === 0
                    ? permissions(
                        auth.project.permissions,
                        "operation.subject.view-list"
                    )
                    : permissions(
                        auth.project.permissions,
                        "operation.subject-dtp.view-list"
                    )) ? (
                    <Spin
                        spinning={
                            userSitesLoading ||
                            getSubjectListLoading
                        }
                    >
                        <Table
                            style={{ paddingBottom: 8 }}
                            className="mar-top-10"
                            dataSource={data}
                            scroll={getScroll(data)}
                            pagination={false}
                            rowKey={(record) => record.id}
                            onChange={handleChange}
                            rowClassName={rowClassName}
                        >
                            <Table.Column
                                title={<FormattedMessage id={"common.serial"} />}
                                dataIndex="#"
                                key="#"
                                width={50}
                                className="table-column-padding-left-16-1"
                                fixed="left"
                                render={(value, record, index) =>
                                    (currentPage - 1) * pageSize + index + 1
                                }
                            />

                            {fieldsListData.length > 0 ?
                                fieldsListData?.filter((it: any) => it.name === "shortname")?.map((field: any) =>
                                    <Table.Column
                                        fixed="left"
                                        title={(field.labelEn === "" && field.label === "") ? (
                                            <FormattedMessage id={"subject.number"} />) :
                                            ((field.labelEn !== "" && field.label === "") ?
                                                field.labelEn : ((field.labelEn === "" && field.label !== "") ?
                                                    field.label : (g.lang === "en" ? field.labelEn : field.label)))}
                                        width={200}
                                        className="table-column-padding-left-32"
                                        dataIndex={field.name}
                                        key={field.name}
                                        ellipsis
                                        render={(value, record: any, index) => {
                                            return permissionsCohort(auth.project.permissions, "operation.subject.medicine.view-dispensing", record.cohortStatus) ?
                                                <>
                                                    <AuthButton
                                                        show
                                                        previewProps={{allowClick: true, keepProto: true}}
                                                        ref={value === newAddSubjectNumber ? newRowRef : null}
                                                        style={{ padding: 0 }}
                                                        type="link"
                                                        className="mouse"
                                                        onClick={() => dispensing(record)}
                                                    >
                                                        {
                                                            value
                                                        }
                                                    </AuthButton>
                                                </>
                                                : <span ref={value === newAddSubjectNumber ? newRowRef : null}>
                                                    {value}
                                                </span>

                                        }}
                                        sorter={
                                            field.name === "shortname"
                                                ? sortOrder
                                                : undefined
                                        }
                                        onHeaderCell={() => ({
                                            onClick: () =>
                                                field.name === "shortname"
                                                    ? handleSortClick(
                                                        "shortname"
                                                    )
                                                    : undefined,
                                        })}
                                    />
                                )
                                : null
                            }


                            {isRandomNumber ? (
                                <Table.Column
                                    title={<FormattedMessage id={"projects.randomization.randomNumber"}/>}
                                    className="table-column-padding-left-32"
                                    dataIndex="randomNumber"
                                    width={intl.locale === "zh" ? 120 : 200}
                                    key="randomNumber"
                                    ellipsis
                                    sorter={previewFilter(sortOrder)}
                                    onHeaderCell={() => ({
                                        onClick: clickFilter(() => handleSortClick("random_number")),
                                    })}
                                />
                            ) : null}
                            {isRandomSequenceNumber ? (
                                <Table.Column
                                    title={
                                        <FormattedMessage
                                            id={
                                                "projects.randomization.randomSequenceNumber"
                                            }
                                        />
                                    }
                                    className="table-column-padding-left-32"
                                    dataIndex="randomSequenceNumber"
                                    width={g.lang === "zh" ? 120 : 200}
                                    key="randomSequenceNumber"
                                    ellipsis
                                    sorter={previewFilter(sortOrder)}
                                    onHeaderCell={() => ({
                                        onClick: clickFilter(() => handleSortClick("random_sequence_number")),
                                    })}
                                />
                            ) : null}

                            <Table.Column
                                title={<FormattedMessage id={"common.site"} />}
                                dataIndex="siteName"
                                key="siteName"
                                width={200}
                                className="table-column-padding-left-16-1"
                                render={(value, record: any, index) => record.siteNumber + "-" + value
                                }
                            />
                            {
                                projectType === 2 || inRandomIsolation(auth.project.info.number) ?
                                    <Table.Column
                                        title={<FormattedMessage id={"projects.second"} />}
                                        dataIndex="cohortName"
                                        key="cohortName"
                                        width={200}
                                        className="table-column-padding-left-32"
                                    />
                                    : null
                            }
                            {
                                (projectType === 2 || inRandomIsolation(auth.project.info.number)) && haveCohortReRandom ?
                                    <Table.Column
                                        title={<FormattedMessage id={"common.stage"} />}
                                        dataIndex="reRandomName"
                                        key="reRandomName"
                                        width={200}
                                        className="table-column-padding-left-32"
                                    />
                                    : null
                            }
                            {((projectType === 1 || (projectType === 2 && !haveCohortReRandom) || inRandomIsolation(auth.project.info.number)) && fieldsListData.length > 0)
                                ? fieldsListData?.filter((it: any) => it.name !== "shortname")?.map((field: any) =>
                                    <Table.Column
                                        title={(field.name === "shortname" && field.labelEn === "" && field.label === "") ? (
                                            <FormattedMessage id={"subject.number"} />) :
                                            ((field.name === "shortname" && field.labelEn !== "" && field.label === "") ?
                                                field.labelEn : ((field.name === "shortname" && field.labelEn === "" && field.label !== "") ?
                                                    field.label : ((field.name === "shortname" && g.lang === "en") ? field.labelEn : field.label)))}
                                        width={200}
                                        className="table-column-padding-left-32"
                                        dataIndex={field.name === "shortname" ? field.name : (field.invalidDisplay === "" ? field.label : field.label + field.name)}
                                        key={field.name === "shortname" ? field.name : (field.invalidDisplay === "" ? field.label : field.label + field.name)}
                                        ellipsis
                                        // render={(value: any, record: any, index: any) => {
                                        //     if (!value) {
                                        //         return <></>;
                                        //     }
                                        //     return <>{value}</>;
                                        // }}
                                        render={(value: any, record: any, index: any) => {
                                            return getValue(value, record, index, field.name);
                                        }}
                                        // sorter={field.name === "shortname" ? (a: any, b: any) => a[field.name] - b[field.name] : undefined}
                                        sorter={
                                            field.name === "shortname"
                                                ? sortOrder
                                                : undefined
                                        }
                                        onHeaderCell={() => ({
                                            onClick: () =>
                                                field.name === "shortname"
                                                    ? handleSortClick(
                                                        "shortname"
                                                    )
                                                    : undefined,
                                        })}
                                    />
                                )
                                : null
                            }
                            {((projectType === 3 ||(projectType === 2 && haveCohortReRandom)) && !inRandomIsolation(auth.project.info.number) && reRandomFieldsListData != null && reRandomFieldsListData.length > 0)
                                ? reRandomFieldsListData?.map((reRandomFields: any) =>
                                    reRandomFields.fields?.map((field: any) =>
                                        field.name !== "shortname" ?
                                            <Table.Column
                                                title={(field.name === "shortname" && field.labelEn === "" && field.label === "") ?
                                                    (<FormattedMessage id={"subject.number"} />) :
                                                    ((field.name === "shortname" && field.labelEn !== "" && field.label === "") ?
                                                        field.labelEn :
                                                        ((field.name === "shortname" && field.labelEn === "" && field.label !== "") ?
                                                            field.label :
                                                            ((field.name === "shortname" && g.lang === "en") ?
                                                                field.labelEn :reRandomFields.reRandomName === ""?
                                                                    reRandomFields.cohortName + "-" + field.label :
                                                                    reRandomFields.reRandomName + "-" + field.label)))}
                                                width={200}
                                                className="table-column-padding-left-32"
                                                dataIndex={reRandomFields.cohortId + field.name}
                                                key={reRandomFields.cohortId + field.name}
                                                ellipsis
                                                // render={(value) => {
                                                //     if (!value) {
                                                //         return <>-</>;
                                                //     }
                                                //     return <>{value}</>;
                                                // }}
                                                render={(value: any, record: any, index: any) => {
                                                    return getValue(value, record, index, field.name);
                                                }}
                                                sorter={
                                                    field.name === "shortname"
                                                        ? sortOrder
                                                        : undefined
                                                }
                                                onHeaderCell={() => ({
                                                    onClick: () =>
                                                        field.name === "shortname"
                                                            ? handleSortClick(
                                                                "shortname"
                                                            )
                                                            : undefined,
                                                })}
                                            />
                                            :
                                            null
                                    )
                                )
                                : null
                            }
                            {!blind ||
                                (researchAttribute === 0
                                    ? permissions(
                                        auth.project.permissions,
                                        "operation.subject.view-list"
                                    )
                                    : permissions(
                                        auth.project.permissions,
                                        "operation.subject-dtp.view-list"
                                    )) ? (
                                <Table.Column
                                    title={
                                        <FormattedMessage
                                            id={"drug.configure.group"}
                                        />
                                    }
                                    className="table-column-padding-left-32"
                                    dataIndex="group"
                                    key="group"
                                    width={120}
                                    ellipsis
                                />
                            ) : null}
                            {data &&
                                data.findIndex((it: any) => it.subGroupName !== "") !==
                                -1 &&
                                (!blind ||
                                    (researchAttribute === 0
                                        ? permissions(
                                            auth.project.permissions,
                                            "operation.subject.view-list"
                                        )
                                        : permissions(
                                            auth.project.permissions,
                                            "operation.subject-dtp.view-list"
                                        ))) ? (
                                <Table.Column
                                    title={
                                        <FormattedMessage
                                            id={"drug.configure.subGroup"}
                                        />
                                    }
                                    className="table-column-padding-left-32"
                                    dataIndex="subGroupName"
                                    key="subGroupName"
                                    width={120}
                                    ellipsis
                                />
                            ) : null}
                            <Table.Column
                                title={<FormattedMessage id="common.status" />}
                                dataIndex="status"
                                key="status"
                                width={155}
                                className="table-column-padding-left-32"
                                // width={g.lang === "en" ? 120 : 80}
                                ellipsis
                                render={(value, record, index) =>
                                    subjectStatusItem(value, record)
                                }
                                filters={previewShowFilter((projectType === 3 ||(projectType === 2 && haveCohortReRandom))  && !inRandomIsolation(auth.project.info.number) ?
                                    [
                                        {
                                            text: formatMessage({ id: 'subject.status.registered' }),
                                            value: 1,
                                        },
                                        {
                                            text: formatMessage({ id: 'subject.status.random' }),
                                            value: 3,
                                        },
                                        {
                                            text: formatMessage({ id: 'subject.status.exited' }),
                                            value: 4,
                                        },
                                        {
                                            text: formatMessage({ id: 'subject.status.blinded.urgent' }),
                                            value: 6,
                                        },
                                        {
                                            text: formatMessage({ id: 'subject.status.blinded.pv' }),
                                            value: 12,
                                        },
                                        {
                                            text: formatMessage({ id: 'subject.status.screen.success' }),
                                            value: 7,
                                        },
                                        {
                                            text: formatMessage({ id: 'subject.status.screen.fail' }),
                                            value: 8,
                                        },
                                        {
                                            text: formatMessage({ id: 'subject.status.finish' }),
                                            value: 9,
                                        },
                                        {
                                            text: formatMessage({ id: 'subject.status.to.be.random' }) + (auth.project.info.type === 3 && cohorts[1] ? "(" + cohorts[1]?.name + ")" : ""),
                                            value: 10,
                                        },


                                    ] :
                                    (
                                        haveJustDispensing && haveJustRandom ?
                                            [
                                                {
                                                    text: formatMessage({ id: 'subject.status.registered' }),
                                                    value: 1,
                                                },
                                                {
                                                    text: formatMessage({ id: 'subject.status.random' }),
                                                    value: 3,
                                                },
                                                {
                                                    text: formatMessage({ id: 'subject.status.exited' }),
                                                    value: 4,
                                                },
                                                {
                                                    text: formatMessage({ id: 'subject.status.blinded.urgent' }),
                                                    value: 6,
                                                },
                                                {
                                                    text: formatMessage({ id: 'subject.status.blinded.pv' }),
                                                    value: 12,
                                                },
                                                {
                                                    text: formatMessage({ id: 'subject.status.screen.success' }),
                                                    value: 7,
                                                },
                                                {
                                                    text: formatMessage({ id: 'subject.status.screen.fail' }),
                                                    value: 8,
                                                },
                                                {
                                                    text: formatMessage({ id: 'subject.status.finish' }),
                                                    value: 9,
                                                },
                                                {
                                                    text: formatMessage({ id: 'subject.status.join' }),
                                                    value: 11,
                                                },
                                            ] :
                                            (haveJustDispensing ?
                                                    [
                                                        {
                                                            text: formatMessage({ id: 'subject.status.registered' }),
                                                            value: 1,
                                                        },
                                                        {
                                                            text: formatMessage({ id: 'subject.status.exited' }),
                                                            value: 4,
                                                        },
                                                        {
                                                            text: formatMessage({ id: 'subject.status.screen.success' }),
                                                            value: 7,
                                                        },
                                                        {
                                                            text: formatMessage({ id: 'subject.status.screen.fail' }),
                                                            value: 8,
                                                        },
                                                        {
                                                            text: formatMessage({ id: 'subject.status.finish' }),
                                                            value: 9,
                                                        },
                                                        {
                                                            text: formatMessage({ id: 'subject.status.join' }),
                                                            value: 11,
                                                        },
                                                    ] :
                                                    [
                                                        {
                                                            text: formatMessage({ id: 'subject.status.registered' }),
                                                            value: 1,
                                                        },
                                                        {
                                                            text: formatMessage({ id: 'subject.status.random' }),
                                                            value: 3,
                                                        },
                                                        {
                                                            text: formatMessage({ id: 'subject.status.exited' }),
                                                            value: 4,
                                                        },
                                                        {
                                                            text: formatMessage({ id: 'subject.status.blinded.urgent' }),
                                                            value: 6,
                                                        },
                                                        {
                                                            text: formatMessage({ id: 'subject.status.blinded.pv' }),
                                                            value: 12,
                                                        },
                                                        {
                                                            text: formatMessage({ id: 'subject.status.screen.success' }),
                                                            value: 7,
                                                        },
                                                        {
                                                            text: formatMessage({ id: 'subject.status.screen.fail' }),
                                                            value: 8,
                                                        },
                                                        {
                                                            text: formatMessage({ id: 'subject.status.finish' }),
                                                            value: 9,
                                                        },
                                                    ]
                                            )
                                    ), undefined)

                                }
                                filterSearch={true}
                                filterMultiple={true}
                            />


                            {blind &&
                                (researchAttribute === 0
                                    ? permissions(
                                        auth.project.permissions,
                                        "operation.subject.unblinding"
                                    )
                                    : permissions(
                                        auth.project.permissions,
                                        "operation.subject-dtp.unblinding"
                                    )) ? (
                                <Table.Column
                                    title={
                                        <FormattedMessage id="subject.unblinding.urgent" />
                                    }
                                    align="left"
                                    width={g.lang === "zh" ? 120 : 200}
                                    // width={150}
                                    className="table-column-padding-left-32"
                                    render={(value, record: any, index) => {
                                        let btns: any = [];
                                        let blind = true;
                                        if ((projectType === 3 ||(projectType === 2 && haveCohortReRandom)) && !inRandomIsolation(auth.project.info.number)) {
                                            blind = record.attribute.info.blind
                                        }
                                        if (
                                            record.urgentUnblindingStatus === 0 &&
                                            (record.status === 3 || record.status === 4 || record.status === 9) && blind
                                        ) {
                                            if (record.group !== "") {
                                                if (
                                                    auth.project.info.unblinding_control === 1 && auth.project.info.unblinding_type === 1 ?
                                                        (researchAttribute === 0 ?
                                                            (
                                                                !permissionsCohort(auth.project.permissions, "operation.subject.unblinding-application") &&
                                                                !permissionsCohort(auth.project.permissions, "operation.subject.unblinding-approval")) :
                                                            (
                                                                !permissionsCohort(auth.project.permissions, "operation.subject-dtp.unblinding-application") &&
                                                                !permissionsCohort(auth.project.permissions, "operation.subject-dtp.unblinding-approval"))) :
                                                        (researchAttribute === 0 ?
                                                            !permissionsCohort(auth.project.permissions, "operation.subject.unblinding", cohortStatus)
                                                            :
                                                            !permissionsCohort(auth.project.permissions, "operation.subject-dtp.unblinding", cohortStatus))
                                                ) {
                                                    btns.push(
                                                        <FormattedMessage id="subject.Unblinded" />
                                                    );
                                                } else {
                                                    btns.push(
                                                        <AuthButton
                                                            show
                                                            type="link"
                                                            size="small"
                                                            style={{
                                                                paddingLeft: 0,
                                                            }}
                                                            onClick={() => {
                                                                unblinding(
                                                                    record,
                                                                    "2"
                                                                );
                                                            }}
                                                        >
                                                            <FormattedMessage id="subject.brokenBlinded" />
                                                        </AuthButton>
                                                    );
                                                }
                                            }
                                        }
                                        if (record.urgentUnblindingStatus === 1 && blind) {
                                            btns.push(
                                                <AuthButton
                                                    show
                                                    type="link"
                                                    size="small"
                                                    style={{ paddingLeft: 0 }}
                                                    onClick={() => {
                                                        unblindingDetails(
                                                            record,
                                                            "2"
                                                        );
                                                    }}
                                                >
                                                    <FormattedMessage id="subject.already.unblinding" />
                                                </AuthButton>
                                            );
                                        }
                                        if (btns.length === 0) {
                                            return <>-</>;
                                        }
                                        return (
                                            <div>
                                                {btns.map((item: any, index: any) => (
                                                    <div key={index}>{item}</div>
                                                ))}
                                            </div>
                                        );
                                    }}
                                />
                            ) : null}

                            {blind &&
                                (researchAttribute === 0 && permissions(
                                    auth.project.permissions,
                                    "operation.subject.unblinding-pv-view"
                                )) ? (
                                <Table.Column
                                    title={
                                        <FormattedMessage id="subject.unblinding.pv" />
                                    }
                                    align="left"
                                    width={g.lang === "zh" ? 100 : 160}
                                    className="table-column-padding-left-32"
                                    render={(value, record: any, index) => {
                                        let btns: any = [];
                                        let blind = true;
                                        if ((projectType === 3 ||(projectType === 2 && haveCohortReRandom)) && !inRandomIsolation(auth.project.info.number)) {
                                            blind = record.attribute.info.blind
                                        }
                                        if (
                                            (record.status === 3 ||
                                                record.status === 4 ||
                                                record.status === 6 ||
                                                record.status === 9) &&
                                            record.pvUnblindingStatus === 0 && blind
                                        ) {
                                            if (record.group !== "") {
                                                if (auth.project.info.unblinding_control === 1 && auth.project.info.pv_unblinding_type === 1 ?
                                                    !permissionsCohort(auth.project.permissions, "operation.subject.unblinding-pv-application") &&
                                                    !permissionsCohort(auth.project.permissions, "operation.subject.unblinding-pv-approval")
                                                    : !permissionsCohort(auth.project.permissions, "operation.subject.unblinding-pv-view")) {
                                                    btns.push(
                                                        <FormattedMessage id="subject.Unblinded" />
                                                    );
                                                } else {
                                                    btns.push(
                                                        <AuthButton
                                                            show
                                                            type="link"
                                                            size="small"
                                                            style={{
                                                                paddingLeft: 0,
                                                            }}
                                                            onClick={() => {
                                                                unblinding(
                                                                    record,
                                                                    "3"
                                                                );
                                                            }}
                                                        >
                                                            <FormattedMessage id="subject.brokenBlinded" />
                                                        </AuthButton>
                                                    );
                                                }
                                            }
                                        }
                                        if (
                                            (record.status === 3 ||
                                                record.status === 4 ||
                                                record.status === 6 ||
                                                record.status === 9) &&
                                            record.pvUnblindingStatus === 1 && blind
                                        ) {
                                            if (record.group !== "") {
                                                btns.push(
                                                    <AuthButton
                                                        show
                                                        type="link"
                                                        size="small"
                                                        style={{ paddingLeft: 0 }}
                                                        onClick={() => {
                                                            unblindingDetails(
                                                                record,
                                                                "3"
                                                            );
                                                        }}
                                                    >
                                                        <FormattedMessage id="subject.already.unblinding" />
                                                    </AuthButton>
                                                );
                                            }
                                        }
                                        if (btns.length === 0) {
                                            return <>-</>;
                                        }
                                        return (
                                            <div>
                                                {btns.map((item: any, index: any) => (
                                                    <div key={index}>{item}</div>
                                                ))}
                                            </div>
                                        );
                                    }}
                                />
                            ) : null}

                            <Table.Column
                                title={<FormattedMessage id="common.operation" />}
                                // width={g.lang == "zh" ? 210 : 250}
                                width={222}
                                className="table-column-padding-left-16-2"
                                fixed="right"
                                render={(value, record: any, index) =>
                                    renderOperations(record)
                                }
                            />
                        </Table>
                    </Spin>
                ) : (
                    <Result
                        icon={<ExclamationCircleOutlined />}
                        title={
                            <Typography.Text type="secondary">
                                {formatMessage({
                                    id: "projects.subject.selectSite",
                                })}
                            </Typography.Text>
                        }
                    />
                )}
            {
                total > 20 && (
                    <Pagination
                        hideOnSinglePage={false}
                        className="text-right"
                        current={currentPage}
                        pageSize={pageSize}
                        pageSizeOptions={["10", "20", "50", "100"]}
                        total={total}
                        showSizeChanger
                        showTotal={(total, range) =>
                            `${range[0]} - ${range[1]} / ${total}`
                        }
                        onChange={(page, pageSize) => {
                            setCurrentPage(page);
                        }}
                        onShowSizeChange={(current, size) => {
                            setCurrentPage(1);
                            setPageSize(size);
                        }}
                    />
                )}
            {/*<Modal title={formatMessage({id: 'subject.room.download'})} visible={isVisible} onOk={downloadDispensing}*/}
            {/*       onCancel={cancel} centered>*/}
            {/*    <Form form={dispensing_form}>*/}
            {/*        <Form.Item initialValue={1} label={formatMessage({id: 'subject.with.room'})} name="room"*/}
            {/*                   className="mar-ver-5" rules={[{required: true}]}>*/}
            {/*            <Radio.Group>*/}
            {/*                <Radio value={1}><FormattedMessage id="common.yes"/></Radio>*/}
            {/*                <Radio value={2}><FormattedMessage id="common.no"/></Radio>*/}
            {/*            </Radio.Group>*/}
            {/*        </Form.Item>*/}
            {/*    </Form>*/}
            {/*</Modal>*/}
            <HistoryList
                bind={history_ref}
                permission={
                    researchAttribute === 0
                        ? permissions(
                            auth.project.permissions,
                            "operation.subject.print"
                        )
                        : permissions(
                            auth.project.permissions,
                            "operation.subject-dtp.print"
                        )
                }
            />
            <RegisterAdd bind={subject_pt} refresh={list} />
            <Unblinding bind={unblinding_pt} refresh={list} />
            <SignOut bind={sign_out_pt} refresh={list} />
            <Transport bind={transport_site} refresh={list} />
            <SwitchCohort bind={switch_cohort} refresh={list} />
            <Replace bind={replace_pt} refresh={list} />
            <Dispensing bind={dispensing_pt} refresh={list} />
            <Screen bind={screen_pt} refresh={list} />
            <Finish bind={finish_pt} refresh={list} />
            <EdcVerificationTip
                bind={edc_verification_tip_pt}
                refresh={list}
                random_save={random_save}
            />
            {/*TODO 后续确认是否删除该功能*/}
            {/*<CohortStatusModify />*/}
            <Modal
                className="custom-small-modal"
                title={formatMessage({ id: "notice.subject.random" })}
                // closable={false}
                onCancel={hide_confirm}
                maskClosable={false}
                open={subjectRecordShow}
                centered
                destroyOnClose={true}
                footer={
                    <Row gutter={8} justify="space-between">
                        <Col span={15} style={{ textAlign: "left" }}>
                            {" "}
                        </Col>
                        <Col>
                            <AuthButton show onClick={hide_confirm}>
                                {formatMessage({ id: "common.cancel" })}
                            </AuthButton>
                            {pushScenarioFilter(connectEdc, pushMode, edcSupplier, pushScenario) ? (
                                <AuthButton
                                    show
                                    previewProps={{disabledClick: true}}
                                    loading={edcSubjectVerificationRunLoading || subjectRandomLoading}
                                    onClick={edc_random_verification}
                                    type={"primary"}
                                >
                                    {formatMessage({ id: "common.ok" })}
                                </AuthButton>
                            ) : (
                                <AuthButton
                                    show
                                    previewProps={{disabledClick: true}}
                                    loading={subjectRandomLoading}
                                    onClick={random_save}
                                    type={"primary"}
                                >
                                    {formatMessage({ id: "common.ok" })}
                                </AuthButton>
                            )}
                        </Col>
                    </Row>
                }
            >
                <TitleContent style={{ height: intl.locale === "zh" ? "" : "54px" }}>
                    <Col
                        style={{
                            fontSize: "14px",
                            fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                            marginTop: "3px",
                            marginLeft: "8px",
                            fontWeight: "400",
                        }}
                    >
                        <InfoCircleFilled style={{ color: "#FFAE00" }} />{" "}
                        <FormattedMessage id={"subject.confirm.random"} />
                    </Col>
                </TitleContent>

                <StyleFrom
                    layout={"vertical"}
                // labelCol={{ span: 4, style: { whiteSpace: "pre-line" } }}
                >
                    <div style={{ marginTop: "24px", marginBottom: "12px" }}>
                        <Title
                            name={formatMessage({ id: "subject.random.info" })}
                        />
                    </div>
                    {confirmCohortName !== "" &&
                        projectType === 2 && (
                            <Form.Item
                                label={formatMessage({
                                    id: "projects.second",
                                })}
                                style={{ marginBottom: "16px" }}
                            >
                                {confirmCohortName}
                            </Form.Item>
                        )}
                    {
                        confirmCohortName !== "" &&
                        projectType === 3 && (
                            <Form.Item
                                label={formatMessage({
                                    id: "common.stage",
                                })}
                                style={{ marginBottom: "16px" }}
                            >
                                {confirmCohortName}
                            </Form.Item>
                        )}
                    {subjectRecord && ((projectType === 1 || (projectType === 2 && subjectRecord?.cohort?.type === 0)|| inRandomIsolation(auth.project.info.number)) && fieldsListData)
                        ? subjectRecord.form?.fields.map((it: any, index: any) => (
                            <Form.Item
                                key={index}
                                label={(it.name === "shortname" && it.labelEn === "" && it.label === "") ? (<FormattedMessage id={"subject.number"} />) : ((it.name === "shortname" && it.labelEn !== "" && it.label === "") ? (g.lang === "en" ? it.labelEn : formatMessage({ id: "subject.number" })) : ((it.name === "shortname" && it.labelEn === "" && it.label !== "") ? it.label : ((it.name === "shortname" && g.lang === "en") ? it.labelEn : it.label)))}
                                style={{ marginBottom: "16px" }}
                            >
                                {getSubjectValue(subjectRecord[it.name], subjectRecord, index, it.name)}
                            </Form.Item>
                        ))
                        : null}
                    {subjectRecord && ((projectType === 3 ||(projectType === 2 && subjectRecord?.cohort?.type === 1)) && !inRandomIsolation(auth.project.info.number))
                        ?
                        subjectRecord.form?.fields.map((it: any, index: any) => (
                            <>
                                {
                                    subjectRecord[it.cohortId + it.name] === "" ||
                                        subjectRecord[it.cohortId + it.name] === null ||
                                        subjectRecord[it.cohortId + it.name] === undefined ?
                                        null
                                        :
                                        <Form.Item
                                            key={index}
                                            label={(it.name === "shortname" && it.labelEn === "" && it.label === "") ? (<FormattedMessage id={"subject.number"} />) : ((it.name === "shortname" && it.labelEn !== "" && it.label === "") ? (g.lang === "en" ? it.labelEn : formatMessage({ id: "subject.number" })) : ((it.name === "shortname" && it.labelEn === "" && it.label !== "") ? it.label : ((it.name === "shortname" && g.lang === "en") ? it.labelEn : (projectType === 3 ?subjectRecord?.cohortName + "-" + it.label:subjectRecord?.reRandomName + "-" + it.label))))}
                                            style={{ marginBottom: "16px" }}
                                        >
                                            {getSubjectValue(subjectRecord[it.cohortId + it.name], subjectRecord, index, it.name)}
                                        </Form.Item>
                                }
                            </>

                        ))
                        : null}
                </StyleFrom>
            </Modal>
            <Modal
                title={<FormattedMessage id={"subject.invalid.list"} />}
                open={invalidSubjectShow}
                zIndex={999}
                centered
                onCancel={hideInvalidSubjectList}
                maskClosable={false}
                destroyOnClose={true}
                className="custom-small-modal"
                footer={
                    <Row gutter={8} justify="space-between">
                        <Col span={15} style={{ textAlign: "left" }}>
                            {" "}
                        </Col>
                        <Col>
                            <AuthButton show onClick={hideInvalidSubjectList}>
                                {formatMessage({ id: "common.close" })}
                            </AuthButton>
                        </Col>
                    </Row>
                }
            >
                <ConfigProvider
                    renderEmpty={
                        () => {
                            return <Empty
                                image={<img alt={""} src={EmptyImg} style={{ width: 200, height: 142 }}></img>}
                            />
                        }
                    }
                >
                    <Spin spinning={getInvalidSubjectLoading}>
                        <Table
                            loading={getInvalidSubjectLoading}
                            dataSource={invalidData}
                            rowKey={(record) => (record.id)}
                            pagination={false}
                        >
                            <Table.Column title={<FormattedMessage id="common.serial" />} dataIndex="#" key="#" width={64}
                                render={(text, record, index) => ((currentPage - 1) * pageSize + index + 1)} />
                            <Table.Column
                                title={(shortnameField?.labelEn === "" && shortnameField?.label === "") ? (
                                    <FormattedMessage id={"subject.number"} />) :
                                    ((shortnameField?.labelEn !== "" && shortnameField?.label === "") ?
                                        shortnameField?.labelEn : ((shortnameField?.labelEn === "" && shortnameField?.label !== "") ?
                                            shortnameField?.label : (g.lang === "en" ? shortnameField?.labelEn : shortnameField?.label)))}
                                key="shortname" dataIndex="shortname"
                                render={(text: string, record: any, index) => {
                                    return (
                                        <Text delete style={{ color: "#ADB2BA" }}>{record.info[0].value}</Text>
                                    )
                                }} />
                            <Table.Column
                                title={<FormattedMessage id="common.operation" />}
                                key="time"
                                dataIndex="time"
                                align="left"
                                width={120}
                                ellipsis
                                render={(value, record: any, index) => {
                                    return (
                                        <>
                                            <Space size={"small"}>
                                                <AuthButton
                                                    show
                                                    style={{ padding: 0 }}
                                                    type="link"
                                                    size="small"
                                                    onClick={() => {
                                                        dispensing(record);
                                                    }}
                                                >
                                                    <FormattedMessage id="common.view" />
                                                </AuthButton>
                                                <AuthButton
                                                    show
                                                    style={{ padding: 0 }}
                                                    type="link"
                                                    size="small"
                                                    onClick={() => {
                                                        showHistory(record);
                                                    }}
                                                >
                                                    <FormattedMessage id="common.history" />
                                                </AuthButton>
                                            </Space>

                                        </>
                                    )
                                }}
                            />
                        </Table>
                    </Spin>
                </ConfigProvider>
            </Modal>

        </React.Fragment>
    );
}


const StyleFrom = styled(Form)`
    .ant-form-item-label {
        padding: 0;
    }
    .ant-form-item-label label {
        height: auto;
        color: #4e5969;
        opacity: 0.8;
    }
    .ant-form-item-control-input {
        min-height: 0;
    }
`;

const TitleContent = styled.div`
    height: 32px;
    width: 552px;
    left: 444px;
    top: 322px;
    border-radius: 2px;
    background: rgba(255, 174, 0, 0.06);
    border: 0.5px solid rgba(255, 174, 0, 0.5);
    border-radius: 2px;
`;

// const MyTable = styled(Table)`
//     .ant-table-cell-ellipsis:hover {
//         white-space: nowrap;
//     }
// `;
