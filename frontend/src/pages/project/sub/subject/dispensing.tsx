import React, {useEffect} from 'react';
// import moment from "moment";
import moment from 'moment-timezone';
import {
    <PERSON><PERSON>,
    <PERSON>,
    Divider,
    Drawer,
    Form,
    InputNumber,
    message,
    Modal,
    Popover,
    Row,
    Spin,
    Switch,
    Table,
    Tooltip,
    Typography,
} from "antd";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {permissions, permissionsCohort} from "../../../../tools/permission";
import {HistoryList} from "../../../common/history-list";
import {useAuth} from "../../../../context/auth";
import {useSafeState} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {
    dispensingResume,
    getDispensing,
    getSubjectStatusAndRoom,
    retrievalDrug,
    StartFollowUpVisits,
} from "../../../../api/dispensing";
import {DispensingRoom} from "./dispensing_room";
import {DispensingAdd} from "./dispensing_add";
import {DispensingReason} from "./dispensing_reason";
import {DispensingAddDTP} from "./dispensing_add_dtp";
import {DispensingReissueDTP} from "./dispensing_reissue_dtp";
import {CustomConfirmModal} from "../../../../components/modal";
import {Title} from "../../../../components/title";
import styled from "@emotion/styled";
import {getVisitList} from "../../../../api/visit";
import {InsertDivider} from "../../../../components/divider";
import {DispensingRegister} from "./dispensing_register";
import {DispensingInvalid} from "./dispensing_invalid";
import {useGlobal} from "../../../../context/global";
import {ExclamationCircleOutlined} from "@ant-design/icons";
import {combineRow} from "../../../../utils/merge_cell";
import {useSubject} from "./context";
import {inRandomIsolation} from "../../../../utils/in_random_isolation";
import {atRandomSubjectStatus, nilObjectId, subjectStatus} from "../../../../data/data";
import {useAtom} from "jotai";
import {
    currentDispensingAtom,
    currentDispensingIDAtom,
    currentMedicineAtom,
    doseSelectAtom,
    editVisibleAtom,
    listDataAtom,
    showUnBlindAtom,
    showUnBlindIDAtom,
    visitOpenAtom
} from "./ctx";
import {Edit} from "./edit";
import TextArea from "antd/es/input/TextArea";
import _ from "lodash";
import subjectURL from "../../../../images/subject.png";
// 引入 Decimal.js
import {Decimal} from 'decimal.js';
import {DispensingUnblindingApproval} from "./dispensing_unblinding";
import {
    AuthButton,
    AuthDropdown,
    btnFilter,
    clickFilter,
    previewShowFilter,
} from "../../../common/auth-wrap";

export const Dispensing = (props: any) => {
    const auth = useAuth();
    const intl = useTranslation();
    const { formatMessage } = intl;
    const g = useGlobal();
    const subjectCtx = useSubject()
    const [visitOpen, setVisitOpen] = useAtom(visitOpenAtom);

    const projectStatus = auth.project.status ? auth.project.status : 0;
    const [timeZone, setTimeZone] = useSafeState<any>(null);
    const { Link } = Typography;
    const [record, setRecord] = useSafeState<any>({});
    const [canOutVisit, setCanOutVisit] = useSafeState<any>(false); // 控制访视外、
    const [canReissue, setCanReissue] = useSafeState<any>(false); // 补发按钮
    const [visitCycleInfoId, setVisitCycleInfoId] = useSafeState<any>(null);
    const [visitOrder, setVisitOrder] = useSafeState<any>([]); // DTP访视 访视内是否存在订单状态为7（申请态）的数据
    const projectId = auth.project.id;
    const customerId = auth.customerId;
    const envId = auth.env.id;
    const researchAttribute = auth.project.info.research_attribute
        ? auth.project.info.research_attribute
        : 0;
    const projectType = auth.project.info.type;
    const cohorts = auth.env?.cohorts ? auth.env.cohorts : null;
    const reRandomCohorts = projectType === 3 && !inRandomIsolation(auth.project.info.number) ?cohorts: cohorts.filter((item: any) => item.name === subjectCtx.selectRecord?.cohort?.name)

    const dispensing_pt: any = React.useRef();
    const register_pt: any = React.useRef();
    const invalid_pt: any = React.useRef();
    const dispensing_add_dtp_pt: any = React.useRef();
    const dispensing_reissue_dtp_pt: any = React.useRef();
    const reason_pt: any = React.useRef();
    const history_ref: any = React.useRef();
    const [room, setRoom] = useSafeState<any>(false);
    const [isModalVisible, setIsModalVisible] = useSafeState<any>(false);
    const [visitConfig, setVisitConfig] = useSafeState<any>([]);
    const [screenDispensing, setScreenDispensing] = useSafeState<any>(true);
    const [settingOpen, setSettingOpen] = useSafeState<any>(false);
    const [retrievalData, setRetrievalData] = useSafeState<any>([]);
    const [otherSelecteds, setOtherSelecteds] = useSafeState<any>([]);
    const [otherSelectedIds, setOtherSelectedIds] = useSafeState<any>([]);
    const [oldSettingChecked, setOldSettingChecked] = useSafeState<boolean>(false);
    const [settingChecked, setSettingChecked] = useSafeState<boolean>(false);
    const [send, setSend] = useSafeState<any>(true);
    const [disabledCheckedCountBl, setDisabledCheckedCountBl] = useSafeState<boolean>(false);
    const [settingDisabled, setSettingDisabled] = useSafeState<boolean>(false);
    const [shortname, setShortname] = useSafeState<string>("");
    const [cohortId, setCohortId] = useSafeState<string>("");
    const [atRandomSign, setAtRandomSign] = useSafeState<boolean>(true);
    // 判断再随机项目 最后一个访视是否显示访视外
    const [atRandomOutVisit, setAtRandomOutVisit] = useSafeState<boolean>(true);
    const [isFirstDispensing, setIsFirstDispensing] = useSafeState<any>(false);

    const blindCanDispensing =
        !(record.attribute?.info.blindingRestrictions && record.status === 6) &&
        record.status !== 5 &&
        record.status !== 4 &&
        record.status !== 9
    const pvCanDispensing =
        record.attribute?.info.pvUnBlindingRestrictions &&
        record.pvUnblindingStatus === 1;
    const {
        runAsync: getSubjectStatusAndRoomRun,
        loading: getSubjectStatusAndRoomLoading,
    } = useFetch(getSubjectStatusAndRoom, { manual: true });

    const { runAsync: retrievalDrugRun, loading: retrievalDrugLoading } =
        useFetch(retrievalDrug, { manual: true });

    const { runAsync: getDispensingRun, loading: getDispensingLoading } =
        useFetch(getDispensing, { manual: true });

    const { runAsync: getVisitListRun } = useFetch(getVisitList, {
        manual: true,
    });

    const { runAsync: dispensingResumeRun, loading: dispensingResumeLoading } = useFetch(dispensingResume, { manual: true });
    const {
        runAsync: startFollowUpVisits,
        loading: startFollowUpVisitsLoading,
    } = useFetch(StartFollowUpVisits, { manual: true });

    const [dispensing, setDispensing] = useSafeState<any>([]); //访视记录历史
    const [replaceFrom] = Form.useForm();
    const hide = () => {
        setVisitOrder([]);
        setRecord({});
        subjectCtx.setDispensingVisible(false)
        setCanOutVisit(false);
        setCanReissue(false);
        setVisitOpen("");
        setDispensing([]);
        setRoom(false);
        setVisitConfig([]);
        setRetrievalOpen(false);
        setScreenDispensing(true);
        setCohortId("");
        setAtRandomSign(true);
        setOldSettingChecked(false);
        setSettingChecked(false);
        // setAtRandom({});
        subjectCtx.setSelectRecord(null)

        setDose(true)
        props.refresh();
    };
    const [, setDose ] = useAtom(doseSelectAtom);

    const retrievalClose = () => {
        setRetrievalOpen(false)
        retrievalForm.setFieldsValue({
            ...retrievalForm.getFieldsValue,
            remark: null,
        });
    };


    const getList = (record: any) => {
        let cohortId = record.cohortId;
        if ((projectType == 3 && !inRandomIsolation(auth.project.info.number))||(projectType === 2 && subjectCtx.selectRecord?.cohort?.type === 1)) {
            cohortId = subjectCtx.selectRecord?.cohort?.id;
        }
        setCohortId(cohortId);
        if ((projectType == 3 && !inRandomIsolation(auth.project.info.number))||(projectType === 2 && subjectCtx.selectRecord?.cohort?.type === 1)) {
            record.info?.map((it: any) => {
                if (it.name === "shortname") {
                    setShortname(it.value);
                }
            })
        }

        // 实时获取受试者状态 房间号信息
        subjectCtx.setDispensingVisible(true)
        setTimeZone(record.timeZone);
        //如果开启了筛选，登记以及筛选失败时不能发药
        if (record.attribute.info.isScreen && (record.status === 1 || record.status === 8)) {
            setScreenDispensing(false)
        }
        getVisitListRun({
            customerId,
            projectId,
            envId,
            cohortId: cohortId,
        }).then((result: any) => {
            setVisitConfig(result.data?.infos);
            list(record, result.data?.infos, cohortId, record.status);
        });
    }

    useEffect(() => {
        if (subjectCtx.selectRecord && subjectCtx.dispensingVisible && ((projectType === 3 && !inRandomIsolation(auth.project.info.number)) || (projectType === 2 && subjectCtx.selectRecord?.cohort?.type === 1))) {
            getList(subjectCtx.selectRecord)
        }
    }, [subjectCtx.selectRecord])

    useEffect(() => {
        if (subjectCtx.selectRecord && subjectCtx.dispensingVisible && (projectType === 1 || (projectType === 2 && subjectCtx.selectRecord?.cohort?.type === 0) || (projectType === 3 &&inRandomIsolation(auth.project.info.number)))) {
            getList(subjectCtx.selectRecord)
        }
    }, [subjectCtx.dispensingVisible, subjectCtx.dispensingVisible])

    function formatTimezoneOffset(offset: any) {
        const negative = offset < 0;
        offset = Math.abs(offset);
        const hours = Math.floor(offset);
        const minutes = Math.round((offset - hours) * 60); // 四舍五入处理 .5

        const sign = negative ? "-" : "";
        const hh = String(hours).padStart(2, '0');
        const mm = String(minutes).padStart(2, '0');

        return `${sign}${hh}:${mm}`;
    }

    const formatTimezone = (timestamp: any, timezone: any) => {
        const tzMoment = moment.tz(timestamp * 1000, timezone);

        // 获取时区偏移分钟数（如 -330 表示 UTC-05:30）
        const offsetMinutes = tzMoment.utcOffset(); // 单位是分钟
        const totalHours = Math.abs(offsetMinutes);
        const hours = Math.floor(totalHours / 60);
        const minutes = totalHours % 60;

        // 构造 UTC±HH:mm 格式
        const sign = offsetMinutes < 0 ? "-" : "+";
        const formattedOffset = `${sign}${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;

        // 格式化时间
        const formattedTime = tzMoment.format('YYYY-MM-DD HH:mm:ss');

        // 拼接结果
        return `${formattedTime}(UTC${formattedOffset})`;
    };

    // 点击开关
    const settingSwitchOnclick = (checked: boolean) => {
        setSettingChecked(checked);
        if (oldSettingChecked !== checked) {
            setSend(false);
        } else {
            setSend(true);
        }
    };

    // 设置按钮点击事件
    const settingOnclick = () => {
        setSettingOpen(true);
    };

    // 在随机设置模态框确定按钮事件
    const settingOk = () => {
        let subjectData = {
            shortname: shortname,
            settingChecked: settingChecked,
            currentStage: record.cohort?.type === 0 ? cohorts[0].name: reRandomCohorts[0]?.re_random_name,
            nextStage: record.cohort?.type === 0 ? cohorts[1].name: reRandomCohorts[1]?.re_random_name,
            projectId: projectId,
            envId: envId,
            cohortName: reRandomCohorts[0]?.name

        };
        if (settingChecked) {
            // 如果开启，必须是第一阶段才可以执行
            if (cohortId === reRandomCohorts[0]?.id) {
                startFollowUpVisits(subjectData).then((resp: any) => {
                    if (resp.code === 0) {
                        message.success(resp.msg);
                        setSettingOpen(false);
                        if (settingChecked) {
                            list(subjectCtx.selectRecord, null, null, null);
                            props.refresh();
                        } else {
                            hide();
                        }
                    }
                });
            }
        } else {
            // 如果关闭，必须进入第二阶段才可以执行
            if (cohortId === reRandomCohorts[1]?.id) {
                startFollowUpVisits(subjectData).then((resp: any) => {
                    if (resp.code === 0) {
                        message.success(resp.msg);
                        setSettingOpen(false);
                        if (settingChecked) {
                            list(subjectCtx.selectRecord, null, null, null);
                            props.refresh();
                        } else {
                            hide();
                        }
                    }
                });
            }
        }
    };

    // 在随机设置模态框取消按钮事件
    const settingCanCel = () => {
        setSettingOpen(false);
        setSend(true);
        setSettingChecked(disabledCheckedCountBl);
    };

    const showHistory = (id: any, data: any) => {
        let cohortId = record.cohortId;
        if ((projectType == 3 && !inRandomIsolation(auth.project.info.number))||(projectType === 2 && subjectCtx.selectRecord?.cohort?.type === 1)) {
            cohortId = data.cohortId;
        }
        history_ref.current.show("history.dispensing", id, null, timeZone, cohortId, record.tz);
    };

    // 判断是不是首次发药
    const is_first_dispensing = (infos: any[]) => {
        // 检查infos数组中的每个元素的status属性
        return infos.every(info => {
            // 如果status不存在或者不等于2或3，返回true
            return info.status === undefined || info.status !== 2 && info.status !== 3;
        });
    };

    // 获取发药记录
    const get_dispensing = (subjectId: any, visitConfig: any, record: any, chId: any, subjectStatus: any) => {
        getDispensingRun({
            id: subjectId,
            roleId: auth.project.permissions.role_id,
        }).then((result: any) => {
            let data: any = null;
            if (result.data.info){
                data = [...result.data.info]
                let flag = is_first_dispensing(data);
                setIsFirstDispensing(flag);
            }
            setCanOutVisit(result.data?.canOutVisit)
            setCanReissue(result.data?.canReissue)
             setVisitOpen(result.data?.visitOpen)
            setVisitCycleInfoId(result.data?.visitCycleInfoId)
            if (data) {
                let atRandomIndex: any = -1
                if ((projectType == 3 && !inRandomIsolation(auth.project.info.number))||(projectType === 2 && subjectCtx.selectRecord?.cohort?.type === 1)) {
                    let disabledCheckedCount = 0;
                    let cId = chId ? chId : cohortId;
                    let count = 0;
                    let startVisit = 0;
                    let dispensingTrailCount = 0;
                    let nextStageSign = false;
                    if (((projectType == 3 && !inRandomIsolation(auth.project.info.number))?cohortId !== cohorts[0]?.id: cohortId !== reRandomCohorts[0]?.id) && subjectStatus !== 1 && subjectStatus !== 7 && subjectStatus !== 8) {
                        nextStageSign = true;
                    }

                    atRandomIndex = data.findIndex((it: any) => it?.cohortId == subjectCtx.selectRecord?.cohortId && it.visitInfo.random === true)

                    data.forEach((value: any, index: any) => {
                        if (value.cohortId == cId && value.status == 2 || value.status == 3) {
                            count++;
                        }
                        if (reRandomCohorts[0]?.id != value.cohortId) {
                            disabledCheckedCount++;
                        }

                        if (cohorts.length >= 2) {
                            // 统计第一阶段是不是通过开启后续阶段访视关闭的
                            if (value.cohortId === reRandomCohorts[0]?.id && value.startVisit == 1) {
                                startVisit++;
                            }
                            // 统计第二阶段是否有操作发药的记录
                            if (value.cohortId === reRandomCohorts[1]?.id && value.dispensingTrailCount > 0) {
                                dispensingTrailCount++;
                            }
                        }
                    });
                    if (count === 0) {
                        setAtRandomSign(false);
                    } else {
                        setAtRandomSign(true);
                    }

                    // 如果是发药项目
                    if (record.attribute.info.dispensing) {
                        if (disabledCheckedCount === 0) {
                            if (subjectStatus !== 1 && subjectStatus !== 7 && subjectStatus !== 8) {
                                setSettingChecked(false);
                                setOldSettingChecked(false);
                                setDisabledCheckedCountBl(false);
                                setSettingDisabled(false);
                            } else {
                                setSettingChecked(true);
                                setSettingChecked(false);           // 默认不选
                                setOldSettingChecked(false);
                                setDisabledCheckedCountBl(false);   // 默认不选
                                setSettingDisabled(true);
                            }
                        } else {
                            setSettingChecked(true);
                            setOldSettingChecked(true);
                            setDisabledCheckedCountBl(true);
                            if (nextStageSign) {
                                setSettingDisabled(true);
                            } else {
                                if (startVisit > 0 && dispensingTrailCount == 0) {
                                    setSettingDisabled(false);
                                } else {
                                    setSettingDisabled(true);
                                }
                            }
                        }
                    } else {
                        if (cId !== reRandomCohorts[0]?.id) {
                            setSettingDisabled(true);
                            setSettingChecked(true);
                            setOldSettingChecked(true);
                            setDisabledCheckedCountBl(true);
                        } else {
                            setSettingDisabled(false);
                            setSettingChecked(false);
                            setOldSettingChecked(false);
                            setDisabledCheckedCountBl(false);
                        }
                    }
                }
                data.forEach((value: any, index: any) => {

                    // 判断访视内申请态的订单  区分DTP 通用判断
                    if (researchAttribute === 1) {
                        if (value.medicineOrder && !value.visitSign) {
                            if (
                                value.medicineOrder.find(
                                    (it: any) =>
                                        it.status !== 7 && it.status !== 0
                                )
                            ) {
                                visitOrder.push(
                                    value.visitInfo.visitCycleInfoId
                                );
                                setVisitOrder(visitOrder);
                            }
                        }
                    }

                    // 未编号相同名称合并
                    let otherDispensingMedicinesCount: any[] = [];
                    value.otherDispensingMedicines?.map((it: any) => {
                        let i = otherDispensingMedicinesCount.findIndex(
                            (item: any) => it.name === item.name
                        );
                        if (i !== -1) {
                            otherDispensingMedicinesCount[i].count =
                                it.count +
                                otherDispensingMedicinesCount[i].count;
                        } else {
                            otherDispensingMedicinesCount.push({
                                name: it.name,
                                count: it.count,
                            });
                        }
                    });
                    data[index].otherDispensingMedicinesCount =
                        otherDispensingMedicinesCount;

                    // 登记未编号相同名称合并
                    let realOtherDispensingMedicinesCount: any[] = [];
                    value.realOtherDispensingMedicines?.map((it: any) => {
                        let i = realOtherDispensingMedicinesCount.findIndex(
                            (item: any) => it.name === item.name
                        );
                        if (i !== -1) {
                            realOtherDispensingMedicinesCount[i].count =
                                it.count +
                                realOtherDispensingMedicinesCount[i].count;
                        } else {
                            realOtherDispensingMedicinesCount.push({
                                name: it.name,
                                count: it.count,
                            });
                        }
                    });
                    data[index].realOtherDispensingMedicinesCount =
                        realOtherDispensingMedicinesCount;
                });
                if ((projectType == 3 && !inRandomIsolation(auth.project.info.number))||(projectType === 2 && subjectCtx.selectRecord?.cohort?.type === 1)) {
                    let mySet = new Set();
                    let lastFinish: boolean = true

                    data.forEach((it: any, number: any) => {
                        mySet.add(it.cohortId)
                        if (it.status === 1) {
                            lastFinish = false
                        }
                        if (it.cohortId != reRandomCohorts[0]?.id) {
                            if ((atRandomIndex === -1 || (atRandomIndex !== -1 && number < atRandomIndex))) {
                                data[number].visitInfo.random = false       // 这个代码会导致二阶段 虽然是随机后的发药，在待随机状态下也展示 发药 不参加访视的按钮
                            } else {
                                data[number].visitInfo.random = true
                            }
                        }
                    })
                    // DTP访视 第一阶段最后一个发药完成、未接收订单 发药数据未进入到第二阶段   不显示访视外、补发的情况
                    if (mySet.size === 1 && lastFinish) {
                        setAtRandomOutVisit(false)
                    }
                }
                setDispensing(data);
            }
        });
    };

    const add_dispensing = (id: any, item: any, dispensingItem: any) => {
        if (researchAttribute === 0) {
            dispensing_pt.current.show(
                record,
                1,
                id,
                item,
                visitConfig,
                dispensingItem?.reissue === 1,
                dispensingItem?.visitSign,
                dispensingItem?.period?.outSizeWindow,
                isFirstDispensing,
            );
        } else {
            dispensing_add_dtp_pt.current.show(
                record,
                1,
                id,
                item,
                visitOrder,
                dispensing
            );
        }
    };

    const reissue_dispensing = (item: any) => {
        if (researchAttribute === 0) {
            dispensing_pt.current.show(
                record,
                2,
                null,
                item,
                visitConfig,
                true
            );
        } else {
            dispensing_reissue_dtp_pt.current.show(
                record,
                2,
                null,
                item,
                visitOrder,
                dispensing
            );
        }
    };

    const [retrievalOpen, setRetrievalOpen] = useSafeState<any>(false);
    const [retrievalForm] = Form.useForm();

    const selectAll = (record: any, e: any) => {
        const checkd: any = e
        let ids: any[] = []
        if (checkd) {
            record.canRetrieval?.map((item: any) => {
                ids.push(item.medicineId)
            })
            record.otherCanRetrieval?.map((item: any) => {
                ids.push(item.id)
            })
            replaceFrom.setFieldsValue({ ...replaceFrom.getFieldsValue, number: ids })
        } else {
            replaceFrom.setFieldsValue({ ...replaceFrom.getFieldsValue, number: ids })
            changeRetrieval(record)
        }
    }

    const changeRetrieval = (record: any) => {
        let canRetrievalLength: any = record.canRetrieval?.length ? record.canRetrieval?.length : 0
        let otherCanRetrievalLength: any = record.otherCanRetrieval?.length ? record.otherCanRetrieval?.length : 0
        let selectLen: any = replaceFrom.getFieldsValue().number?.length
        if (selectLen) {
            if (selectLen !== (canRetrievalLength + otherCanRetrievalLength)) {
            } else {
            }
        } else {

        }
    }

    const otherRowSelection = {
        type: "checkbox" as "checkbox",
        onChange: (selectedRowKeys: any, selectedRows: any) => {
            setOtherSelecteds(selectedRows);
            setOtherSelectedIds(selectedRowKeys);
        },
        selectedRowKeys: otherSelectedIds,
        selectedRows: otherSelecteds,
    };

    const otherHandleChange = (value: any, item: any) => {
        retrievalData.forEach((it: any) => {
            if (item.id === it.id) {
                it.retrieval_count = value
            }
        });
        otherSelecteds.forEach((it: any) => {
            if (item.id === it.id) {
                it.retrieval_count = value
            }
        });
    };
    // 合并数据源
    const mergedData = (record: any) => {
        var data: any = [];
        var ids: any = [];
        // record.canRetrieval?.map((item: any, index: string | number) => ({
        //     ...item,
        //     ...record.otherCanRetrieval[index],
        // }));
        if (record.canRetrieval) {
            for (let i = 0; i < record.canRetrieval.length; i++) {
                data.push(record.canRetrieval[i])
                ids.push(record.canRetrieval[i].medicineId)
            }
        }
        if (record.otherCanRetrieval) {
            for (let i = 0; i < record.otherCanRetrieval.length; i++) {
                data.push(record.otherCanRetrieval[i])
                ids.push(record.otherCanRetrieval[i].id)
            }
        }
        data.forEach((it: any) => {
            if (it.id) {
                it.retrieval_count = it.count
            }
        });
        setRetrievalData(data);
        setOtherSelecteds(data);
        setOtherSelectedIds(ids);
    };


    // 取回研究产品
    const retrieval = (record: any) => {
        if (
            record.canRetrieval?.length > 0 ||
            record.otherCanRetrieval?.length > 0
        ) {
            return (
                <>
                    <Modal
                        forceRender={true}
                        className="custom-medium-modal"
                        title={formatMessage({ id: 'subject.dispensing.retrieval', allowComponent: true })}
                        open={retrievalOpen}
                        onCancel={retrievalClose}
                        destroyOnClose={true}
                        centered={true}
                        maskClosable={false}
                        footer={
                            <Row justify="end">
                                <Col style={{ marginRight: "16px" }}>
                                    <Button onClick={retrievalClose}>
                                        <FormattedMessage id="common.cancel" />
                                    </Button>
                                </Col>
                                <Col>
                                    <Button
                                        type={"primary"}
                                        className="full-width"
                                        onClick={() => {
                                            retrieval_drug(record.id);
                                        }}
                                    >
                                        <FormattedMessage id="common.ok" />
                                    </Button>
                                </Col>
                            </Row>
                        }
                    >

                        <Table
                            className="mar-top-5"
                            dataSource={retrievalData ? retrievalData : []}
                            pagination={false}
                            rowKey={(re) => re.medicineId ? re.medicineId : re.id}
                            rowSelection={otherRowSelection}
                        >
                            <Table.Column
                                title={
                                    <FormattedMessage id="shipment.medicine" />
                                }
                                dataIndex={"id" ? "id" : "medicineId"}
                                key={"id" ? "id" : "medicineId"}
                                ellipsis
                                width={180}
                                render={(value, it: any, index) => {
                                    return it.medicineId ? (
                                        it.number
                                    ) : (
                                        // 显示文本
                                        it.name +
                                        "[" +
                                        it.batch +
                                        "][" +
                                        it.expireDate +
                                        "]"
                                    );
                                }}
                            />
                            <Table.Column
                                width={50}
                                title={
                                    <FormattedMessage id="drug.freeze.count" />
                                }
                                dataIndex={"count"}
                                key="count"
                                ellipsis
                                render={(value, it: any, index) => {
                                    return it.medicineId ? (
                                        1
                                    ) : (
                                        <InputNumber
                                            className="full-width"
                                            placeholder={formatMessage({
                                                id: "placeholder.input.common",
                                            })}
                                            precision={0}
                                            min={1}
                                            step={1}
                                            max={it.count}
                                            defaultValue={
                                                it.count
                                            }
                                            onChange={(value) => { otherHandleChange(value, it) }}
                                        />
                                    );
                                }}
                            />
                        </Table>
                        <Form form={retrievalForm}>
                            <Form.Item style={{ marginBottom: "0px", marginTop: 12 }} name="remark"
                                label={formatMessage({ id: 'subject.unblinding.remark', allowComponent: true })}>
                                <TextArea placeholder={formatMessage({ id: 'common.required.prefix' })} allowClear
                                    className="full-width" autoSize={{ minRows: 2, maxRows: 6 }} maxLength={500} />
                            </Form.Item>
                        </Form>

                    </Modal>
                    <Link
                        className="mar-rgt-5"
                        onClick={clickFilter(() => {
                            setRetrievalOpen(true)
                            selectAll(record, true)
                            mergedData(record)
                        })}
                    >
                        {formatMessage({ id: "subject.dispensing.retrieval", allowComponent: true })}
                    </Link>

                </>

            );
        } else {
            return;
        }
    };

    const retrieval_drug = (id: any) => {
        if (otherSelecteds.length === 0) {
            message.warning(formatMessage({ id: "common.select.list.tips" }))
            return
        } else {
            CustomConfirmModal({
                title: formatMessage({ id: 'subject.dispensing.confirmRetrieval' }),
                content: formatMessage({ id: 'subject.dispensing.retrievalMessage' }),
                okText: formatMessage({ id: 'common.ok' }),
                cancelText: formatMessage({ id: 'common.cancel' }),
                centered: true,
                onOk: () => {
                    retrievalConfirm(id)
                }
            })
        }
    };

    const retrievalConfirm = (retrievalId: any) => {
        let record: any = dispensing.find((it: any) => it.id === retrievalId);
        let number: any[] = [];
        let other_number_count: any[] = [];

        otherSelecteds.forEach((value: any) => {
            if (
                record.otherDispensingMedicines?.find(
                    (it: any) => it.id === value.id
                )
            ) {
                let other = { id: value.id, batch: value.batch, retrievalCount: value.retrieval_count };
                other_number_count.push(other);
            } else {
                number.push(value.medicineId);
            }
        })
        retrievalDrugRun({ id: retrievalId }, { number, other_number_count, remark: retrievalForm.getFieldsValue()?.remark })
            .then((response: any) => {
                message.success(response.msg);
                replaceFrom.resetFields();
                setIsModalVisible(false);
                list(subjectCtx.selectRecord, null, null, null);
                retrievalClose()

            })
            .catch(() => {
                replaceFrom.resetFields();
            });
    };

    // 替换研究产品编号
    const replace = (value: any) => {
        reason_pt.current.show(
            1,
            record.id,
            value.id,
            value,
            record,
            visitConfig
        );
    };

    const resume = (data: any) => {
        CustomConfirmModal({
            title: formatMessage({ id: "subject.dispensing.resumeTip" }),
            okText: formatMessage({ id: "common.ok" }),
            onOk: () =>
                dispensingResumeRun({ dispensingId: data.id, roleId: auth.project.permissions.role_id }).then(
                    (res: any) => {
                        message.success(res.msg);
                        list(subjectCtx.selectRecord, null, null, null);
                    }
                )
        });


    };

    const list = (showRecord: any, visits: any, cohortId: any, subjectStatus: any) => {
        // 刷新受试者状态， 再更新发药列表
        let records = showRecord ? showRecord : record;
        let visit = visits ? visits : visitConfig;
        getSubjectStatusAndRoomRun({
            id: records.id,
            role_id: auth.project.permissions.role_id,
        }).then((result: any) => {
            let res: any = result.data;
            records.status = res.status;
            records.roomNumber = res.roomNumber;
            setRecord(records);
            if (records.roomNumber !== "") {
                setRoom(true);
            }
            get_dispensing(records.id, visit, records, cohortId, subjectStatus);
        });
    };

    const room_pt: any = React.useRef();

    // 修改为进入页面实时请求
    const showDispensingRoom = (id: any) => {
        room_pt.current.show(id);
    };

    // 无效（不参加）访视
    const invalid_dispensing = (id: any) => {
        invalid_pt.current.show(id);
    };

    // 登记实际使用药物
    const real_dispensing = (data: any) => {
        register_pt.current.show(data);
    };

    const renderVisit = (value: any, record: any) => {
        let name: any = value.name;
        !record.visitSign ? (name = value.name)
            : record.reissue === 1
                ? (name = <span>{value.name}({formatMessage({ id: "subject.dispensing.reissue", allowComponent: true })})</span>)
                : (name = value.name + "(" + record.outVisitStr + ")");
        if (record.medicineOrder.find((it: any) => it.status === 1 || it.status === 2 || it.status === 3 || it.status === 6)) {
            name = (
                <span>
                    {name}
                    <svg className="iconfont" width={12} height={12}>
                        <use xlinkHref="#icon-DTP"></use>
                    </svg>
                </span>
            );
        }

        if (record?.status === 3) {
            name = <Row>{name} <PeriodDivCancel style={{ marginTop: 6, marginLeft: 6, paddingLeft: 6, paddingRight: 6 }}>{formatMessage({ id: 'subject.dispensing.no.join', allowComponent: true })}</PeriodDivCancel></Row>
        } else if (record?.period?.outSize) {
            name = <Row>{name} <PeriodDiv style={{ marginTop: 6, marginLeft: 6, paddingLeft: 6, paddingRight: 6 }}>{formatMessage({ id: 'subject.dispensing.outsize', allowComponent: true })}</PeriodDiv></Row>
        }
        return <>{name}</>;
    };

    const renderPeriod = (record: any) => {
        if (record.period && record?.period?.minPeriod !== "") {
            return (
                <>
                    <Row>{record?.period?.minPeriod}~</Row>
                    <Row>{record?.period?.maxPeriod}</Row>
                </>
            );
        } else {
            return "-";
        }
    };

    const renderOpt = (value: any, data: any, index: any) => {
        if (!data.visitInfo.dispensing || !record.attribute?.info.dispensing) {
            return <>-</>
        }
        let subjectDisabled = record.status === 10;
        let buts: any = [];
        let register: any =
            data.dispensingMedicines?.length > 0 ||
            data.otherDispensingMedicines?.length > 0;
        //未发药
        let unDispensing = data.status === 1;
        // 发药
        let atRandom = true;
        let dispensingButSign = false;
        if ((projectType == 3 && !inRandomIsolation(auth.project.info.number))||(projectType === 2 && subjectCtx.selectRecord?.cohort?.type === 1)) {
            if (data.cohortId != cohortId) {
                atRandom = false
            }
            if (!pvCanDispensing &&
                projectStatus !== 2 &&
                data.canDispensing &&
                (
                    permissionsCohort(
                        auth.project.permissions,
                        "operation.subject.medicine.dispensing", record.cohortStatus
                    )
                    || permissionsCohort(
                        auth.project.permissions,
                        "operation.subject-dtp.medicine.dispensing", record.cohortStatus
                    )) &&
                atRandom
            ) {
                dispensingButSign = true
            }
        } else {
            if (!pvCanDispensing &&
                projectStatus !== 2 &&
                data.canDispensing &&
                (
                    permissionsCohort(
                        auth.project.permissions,
                        "operation.subject.medicine.dispensing", record.cohortStatus
                    )
                    || permissionsCohort(
                        auth.project.permissions,
                        "operation.subject-dtp.medicine.dispensing", record.cohortStatus
                    )) &&
                atRandom) {
                dispensingButSign = true
            }
        }
        // dispensing
        const dispensingShow = dispensingButSign && (!subjectDisabled && screenDispensing && blindCanDispensing && (!unDispensing || record.status !== 8))
        buts.push({
            key: "dispensing",
            label: "subject.dispensing.dispensing",
            onClick: () => add_dispensing(data.id, data.visitInfo.visitCycleInfoId, data),
            show: dispensingShow && researchAttribute === 0,
            btn:  <AuthButton
                style={{ padding: 0 }}
                size="small"
                type="link"
                onClick={() => {add_dispensing(data.id, data.visitInfo.visitCycleInfoId, data);}}
            >
                {formatMessage({id: "subject.dispensing.dispensing", allowComponent: true})}
            </AuthButton>
        });
        buts.push({
            key: "apply",
            label: "subject.apply",
            onClick: () => add_dispensing(data.id, data.visitInfo.visitCycleInfoId, data),
            show: dispensingShow && researchAttribute !== 0,
            btn:  <AuthButton
                style={{ padding: 0 }}
                size="small"
                type="link"
                onClick={() => add_dispensing(data.id, data.visitInfo.visitCycleInfoId, data)}
            >
                {formatMessage({id: "subject.apply", allowComponent: true})}
            </AuthButton>
        });
        // 替换
        const replaceShow = !subjectDisabled && (!pvCanDispensing && (
            permissionsCohort(auth.project.permissions, "operation.subject.medicine.replace", record.cohortStatus) ||
                permissionsCohort(auth.project.permissions, "operation.subject-dtp.medicine.replace", record.cohortStatus)))
            && (((record.attribute.info.dispensing && record.hasDispensing) || screenDispensing) && blindCanDispensing && (
                !unDispensing || record.status !== 8)) && data.canReplace && atRandom
        buts.push({
            key: "replace",
            label: "operation.subject.medicine.replace",
            onClick: () => replace(data),
            show: replaceShow,
            btn: <AuthButton
                style={{ padding: 0 }}
                size="small"
                type="link"
                onClick={() => replace(data)}
            >
                {formatMessage({id: "operation.subject.medicine.replace", allowComponent: true})}
            </AuthButton>
        });

        // 恢复访视
        const resumeShow = !subjectDisabled && !pvCanDispensing &&
            permissionsCohort(auth.project.permissions, "operation.subject.medicine.resume", record.cohortStatus) &&
            data.canResume && (screenDispensing && blindCanDispensing && (!unDispensing || record.status !== 8)) && atRandom
        buts.push({
            key: "resume",
            label: "operation.subject.medicine.resume",
            onClick: () => resume(data),
            show: resumeShow,
            btn: <AuthButton
                style={{ padding: 0 }}
                size="small"
                type="link"
                onClick={() => resume(data)}
            >
                {formatMessage({id: "operation.subject.medicine.resume", allowComponent: true})}
            </AuthButton>
        });

        // 取回
        const unDispensingShow = !pvCanDispensing && (((record.attribute.info.dispensing && record.hasDispensing) || screenDispensing)
                && blindCanDispensing && (!unDispensing || record.status !== 8)) && researchAttribute === 0
            && projectStatus !== 2 && data.retrieval && atRandom
        buts.push({
            key: "retrieval",
            label: "subject.dispensing.retrieval",
            onClick: () => {
                setRetrievalOpen(true)
                selectAll(record, true)
                mergedData(record)
            },
            show: unDispensingShow && !subjectDisabled &&
                permissionsCohort(auth.project.permissions, "operation.subject.medicine.retrieval", record.cohortStatus),
            btn: retrieval(data)
        });
        // 不参加访视
        const dispensingInvalidShow = !subjectDisabled && !pvCanDispensing && projectStatus !== 2 &&
            (researchAttribute === 0 ? permissionsCohort(auth.project.permissions, "operation.subject.medicine.invalid", record.cohortStatus)
                    : permissionsCohort(auth.project.permissions, "operation.subject-dtp.medicine.invalid", record.cohortStatus)
            ) && (screenDispensing && blindCanDispensing && (!unDispensing || record.status !== 8)) && data.canDispensing
        buts.push({
            key: "invalid",
            label: "subject.dispensing.invalid",
            onClick: () => invalid_dispensing(data.id),
            show: dispensingInvalidShow,
            btn:  <AuthButton
                style={{ padding: 0 }}
                type={"link"}
                size={"small"}
                onClick={() => invalid_dispensing(data.id)}
            >
                {formatMessage({ id: "subject.dispensing.invalid", allowComponent: true })}
            </AuthButton>
        });

        // 登记
        const registerShow = !subjectDisabled && (!pvCanDispensing && researchAttribute === 0 ? permissionsCohort(auth.project.permissions, "operation.subject.medicine.register", record.cohortStatus)
                : permissionsCohort(auth.project.permissions, "operation.subject-dtp.medicine.register", record.cohortStatus)) &&
            data.status === 2 && (((record.attribute.info.dispensing && record.hasDispensing) || screenDispensing)
                && blindCanDispensing && (!unDispensing || record.status !== 8)) && data.canRegister
        buts.push({
            key: "register",
            label: "subject.dispensing.number.register",
            onClick: () => real_dispensing(data),
            show: registerShow,
            btn: <AuthButton
                style={{ padding: 0 }}
                type={"link"}
                size="small"
                onClick={() => real_dispensing(data)}
            >
                {formatMessage({id: "subject.dispensing.number.register", allowComponent: true})}
            </AuthButton>
        })
        // 房间号
        const roomNumberShow = !subjectDisabled && (researchAttribute === 0 ? permissions(auth.project.permissions, "operation.subject.medicine.room") &&
            data.dispensingTime > parseInt(String(new Date().getTime() / 1000)) - 24 * 60 * 60
            : permissions(auth.project.permissions, "operation.subject-dtp.medicine.room")) && room && data.status === 2
        if (roomNumberShow) {
            buts.push({
                key: "roomNumber",
                label: "drug.configure.roomNumber",
                onClick: () => showDispensingRoom(data.id),
                show: roomNumberShow,
                btn: <AuthButton
                    style={{ padding: 0 }}
                    size="small"
                    type="link"
                    onClick={() => showDispensingRoom(data.id)}
                >
                    {formatMessage({ id: "drug.configure.roomNumber", allowComponent: true })}
                </AuthButton>
            });
        }

        // 轨迹
        const trailShow = researchAttribute === 0 ? permissions(auth.project.permissions, "operation.subject.medicine.trail")
            : permissions(auth.project.permissions, "operation.subject-dtp.medicine.trail")
        buts.push({
            key: "history",
            label: "common.history",
            onClick: () => showHistory(data.id, data),
            show: trailShow,
            btn: <AuthButton
                style={{ padding: 0 }}
                size="small"
                type="link"
                onClick={() => showHistory(data.id, data)}
            >
                {formatMessage({ id: "common.history", allowComponent: true })}
            </AuthButton>
        });
        const {btns, moreItems} = btnFilter(buts)
        return (
            <div style={{ display: "flex", alignItems: "center" }}>
                {InsertDivider(btns.map(it => it.btn))}
                <AuthDropdown
                    show={moreItems.length > 0}
                    menu={{items:moreItems.map(it => ({
                        key: it.key,
                        label: <FormattedMessage id={it.label} />,
                        onClick: it.onClick,
                    }))}}
                >
                    <span style={{ marginLeft: 12 }} className="dropdown-button">
                        <i className="iconfont icon-gengduo-changgui" />
                    </span>
                </AuthDropdown>
            </div>
        );
    };


    const handleCombineData = (record: any) => {
        record.otherDispensingMedicines?.map((item: any, index: any) => {
            record.otherDispensingMedicines[index].batchNumber = item.batch
            record.otherDispensingMedicines[index].expirationDate = item.expireDate
        })
        let cancelData: any = record?.cancelMedicinesHistory?.filter((item: any) => item.type === 9)
        let tableData: any[] = []
        if (record?.dispensingMedicines) {
            tableData = [...record?.dispensingMedicines, ...cancelData ? cancelData : [], ...record?.otherDispensingMedicines]

        }
        tableData?.sort(function (a: any, b: any) {
            return a.packageNumber - b.packageNumber
        })
        tableData?.map((item: any, index: any) => {
            if (item.packageNumber && item?.packageNumber !== "") {
                tableData[index].packageDrug = true
            }
        })
        return combineRow(tableData, "packageNumber", "packageNumber", false)
    }


    const handleRealOtherCombineData = (record: any) => {
        let rodm: any[] = []
        if (record?.realOtherDispensingMedicines !== null && record?.realOtherDispensingMedicines !== undefined){
            record.realOtherDispensingMedicines?.map((item: any, index: any) => {
                record.realOtherDispensingMedicines[index].batchNumber = item.batch
                record.realOtherDispensingMedicines[index].expirationDate = item.expireDate
            })
            rodm = record?.realOtherDispensingMedicines
        }
        let cancelData: any = []
        let tableData: any[] = []
        if (record?.realDispensingMedicines) {
            tableData = [...record?.realDispensingMedicines, ...cancelData ? cancelData : [], ...rodm]
        } else {
            tableData = [...[], ...cancelData ? cancelData : [], ...rodm]
        }
        tableData?.sort(function (a: any, b: any) {
            return a.packageNumber - b.packageNumber
        })
        tableData?.map((item: any, index: any) => {
            if (item.packageNumber && item?.packageNumber !== "") {
                tableData[index].packageDrug = true
            }
        })
        return combineRow(tableData, "packageNumber", "packageNumber", false)
    }


    const SubjectStatusItem = () => {
        let obj: any = subjectStatus.find((it: any) => it.value === record.status);
        // 待随机后面需要跟一个阶段名称
        let showSecondStageName = "";
        if (record?.attribute?.info?.random){
            if ((projectType == 3 && auth.env.cohorts[1]?.id === subjectCtx.selectRecord?.cohort?.id && !inRandomIsolation(auth.project.info.number))|| (projectType === 2 && subjectCtx.selectRecord?.cohort?.type === 1 && subjectCtx.selectRecord?.cohort?.lastId !== nilObjectId)) {
                obj = atRandomSubjectStatus.find((it: any) => it.value === record.status);
                if (record.status === 1 ||record.status === 7) {
                    showSecondStageName = projectType === 3 && !inRandomIsolation(auth.project.info.number)?record.cohortName:record.cohort.reRandomName;
                }
            }
        }else{
            let isDispensing :any = dispensing.find((it:any) => it.status === 2)
            if ((record.status === 1 ||record.status === 7) && (record.attribute.info.dispensing && !record.attribute.info.random) && isDispensing) {
                obj = subjectStatus.find((it: any) => it.value === 11)
                showSecondStageName = ""; // 非待随机，重置为空
            }
        }


        return <StatusDiv
            textColor={obj?.color}
            background={obj?.background}
        >{obj?.label}{showSecondStageName != "" && <>({showSecondStageName})</>}</StatusDiv>;
    }

    const [, setEditVisible] = useAtom(editVisibleAtom)
    const [finalFields, setFinalFields] = useSafeState<any>([])
    const [listData, ] = useAtom<any>(listDataAtom);
    const getDatasource = () => {
        let final: any = []
        if ((projectType === 3 && !inRandomIsolation(auth.project.info.number))||(projectType === 2 && listData?.haveCohortReRandom)){
            let stageOneCohortId = projectType === 3 && !inRandomIsolation(auth.project.info.number)?auth.env.cohorts[0]?.id:auth.env.cohorts.filter((it: any) => it.name === subjectCtx.selectRecord?.cohortName)[0]?.id;
            let forms = projectType === 3 && !inRandomIsolation(auth.project.info.number)?listData?.reRandomForm:listData?.reRandomForm?.filter((it: any) => it.cohortName === subjectCtx.selectRecord?.cohortName)
            console.log(forms)
            for (let i = 0; i < forms?.length; i++) {
                //还没到第二阶段就隐藏二阶段的表单分层字段
                if (subjectCtx.selectRecord?.cohortId === stageOneCohortId && forms[i].cohortId !== stageOneCohortId){
                    continue
                }
                for (let j = 0; j < forms[i].fields.length; j++) {
                    let field = forms[i].fields[j];
                    const f = _.cloneDeep(field);
                    if (field.name !== "shortname" &&
                        subjectCtx.selectRecord?.reRandomInfo?.find((d: any) =>d.cohortId === forms[i].cohortId && d.name === field.name)){
                        f.cohortId = forms[i].cohortId
                        f.cohortName = forms[i].cohortName
                        f.reRandomName = forms[i].reRandomName
                        let reRandomJoinTime = subjectCtx.selectRecord?.reRandomJoinTime.find((i: any) => i.cohortId === f.cohortId);
                        f.joinTime = reRandomJoinTime?.joinTime
                        final.push(f)
                    }
                    //TODO 二阶段还没数据要不要显示？
                    // if (forms[i].fields.length<=1){
                    //     let cohortId = forms[i].cohortId
                    //     let reRandomJoinTime = subjectCtx.selectRecord?.reRandomJoinTime?.find((i: any) => i.cohortId === cohortId);
                    //     final.push({
                    //         cohortId : cohortId,
                    //         cohortName : forms[i].cohortName,
                    //         reRandomName : forms[i].reRandomName,
                    //         joinTime : reRandomJoinTime?.joinTime
                    //     })
                    // }
                }
            }
        }else {
            let filter: any = []
            filter = subjectCtx.selectRecord?.form.fields.filter((it: any) => it.name !== "shortname" &&
                ( subjectCtx.selectRecord?.info?.find((i: any) => i.name === it.name) || subjectCtx.selectRecord?.actualInfo?.find((i: any) => i.name === it.name))
            )
            for (let i = 0; i < filter?.length; i++) {
                final.push(filter[i])
            }
        }
        setFinalFields(final)
    }

    const getValue = (value: any, it: any, index: any) => {
        if ((projectType === 3 && !inRandomIsolation(auth.project.info.number)) || (projectType === 2 && subjectCtx.selectRecord?.cohort?.type === 1)){
            if(subjectCtx.selectRecord?.reRandomInfo.find((i: any) => i.name === it.name && i.cohortId === it.cohortId)?.label){
                value = subjectCtx.selectRecord?.reRandomInfo.find((i: any) => i.name === it.name && i.cohortId === it.cohortId)?.label;
                if(subjectCtx.selectRecord.form !== undefined && subjectCtx.selectRecord.form !== null){
                    if(subjectCtx.selectRecord.form.fields !== undefined && subjectCtx.selectRecord.form.fields !== null && subjectCtx.selectRecord.form.fields.length > 0){
                        for (let i = 0; i < subjectCtx.selectRecord.form.fields.length; i++) {
                            if(subjectCtx.selectRecord.form.fields[i].type === "inputNumber" && subjectCtx.selectRecord.form.fields[i].formatType === "decimalLength" &&
                                (subjectCtx.selectRecord.form.fields[i].length !== undefined && subjectCtx.selectRecord.form.fields[i].length !== null) &&
                                (value !== undefined && value !== null && value !== "" && typeof value === 'number') && it.name === subjectCtx.selectRecord.form.fields[i].name){
                                    let lengthString = subjectCtx.selectRecord.form.fields[i].length.toString();
                                    if (lengthString.indexOf(".") !== -1) {
                                        const digits = getFractionDigits(subjectCtx.selectRecord.form.fields[i].length)
                                        value = Number(value).toFixed(digits)
                                    }
                            }
                        }
                    }
                }
                return value;
            } else {
                return "";
            }
        } else {
            if(subjectCtx.selectRecord?.info.find((i: any) => i.name === it.name)?.label){
                value = subjectCtx.selectRecord?.info.find((i: any) => i.name === it.name)?.label;
                if(subjectCtx.selectRecord.form !== undefined && subjectCtx.selectRecord.form !== null){
                    if(subjectCtx.selectRecord.form.fields !== undefined && subjectCtx.selectRecord.form.fields !== null && subjectCtx.selectRecord.form.fields.length > 0){
                        for (let i = 0; i < subjectCtx.selectRecord.form.fields.length; i++) {
                            if(subjectCtx.selectRecord.form.fields[i].type === "inputNumber" && subjectCtx.selectRecord.form.fields[i].formatType === "decimalLength" &&
                                (subjectCtx.selectRecord.form.fields[i].length !== undefined && subjectCtx.selectRecord.form.fields[i].length !== null) &&
                                (value !== undefined && value !== null && value !== ""  && typeof value === 'number') && it.name === subjectCtx.selectRecord.form.fields[i].name){
                                    let lengthString = subjectCtx.selectRecord.form.fields[i].length.toString();
                                    if (lengthString.indexOf(".") !== -1) {
                                        const digits = getFractionDigits(subjectCtx.selectRecord.form.fields[i].length)
                                        value = Number(value).toFixed(digits)
                                    }
                            }
                        }
                    }
                }
                return value;
            }else {
                return "";
            }
        }
    };

    function getFractionDigits(f: any) {
        // 分离整数和小数部分
        // const fractionalPart = f - Math.trunc(f);
        const fractionalPart = getFractionalPart(f);
    
        // 将小数部分转换为字符串形式，以便于处理
        // 使用 toFixed(12) 来确保有足够的精度，然后去掉前面的 "0."
        let fractionStr = fractionalPart.toFixed(12).split('.')[1];
    
        // 移除末尾不必要的零
        fractionStr = fractionStr.replace(/0+$/, '');
    
        // 如果结果为空（即原数没有小数部分或小数部分全是0），则返回0
        if (fractionStr === '') {
            return 0;
        }
    
        // 返回小数部分作为整数
        return parseInt(fractionStr, 10);
    }

    function getFractionalPart(f: any) {
        // 使用 Decimal 来处理数字，避免精度丢失
        const number = new Decimal(f);
        const integerPart = number.trunc(); // 获取整数部分
        const fractionalPart = number.minus(integerPart); // 从小数中减去整数部分得到小数部分
    
        return fractionalPart.toNumber(); // 如果需要，将结果转换回普通 JavaScript 数字类型
    }

    useEffect(() => {
        setFinalFields([])
        getDatasource()
    }, [subjectCtx.selectRecord]);

    return (
        <>
            <CustomerDrawer
                // className="drawer-width-percent"
                width={previewShowFilter("100%", "90%")}
                title={
                    researchAttribute === 1
                        ? formatMessage({
                            id: "subject.dispensing.apply.subjectInfo.apply", allowComponent: true
                        })
                        : formatMessage({ id: "subject.dispensing.subject.detail", allowComponent: true })
                }
                open={subjectCtx.dispensingVisible}
                onClose={hide}
                maskClosable={false}
                footer={
                    canOutVisit &&
                        !pvCanDispensing &&
                        (screenDispensing && blindCanDispensing && record.status !== 8) &&
                        projectStatus !== 2
                        && atRandomSign
                        &&
                        atRandomOutVisit
                        && record.attribute.info?.dispensing
                        ? (
                            <Row justify="space-between">
                                <Col></Col>
                                {(
                                    <Col>
                                        {(
                                            (permissionsCohort(
                                                auth.project.permissions,
                                                "operation.subject.medicine.out-visit-dispensing", record.cohortStatus
                                            )
                                            || permissionsCohort(
                                                auth.project.permissions,
                                                "operation.subject-dtp.medicine.out-visit-dispensing", record.cohortStatus
                                            ))
                                            // && visitOrder.length !== 0
                                            && canOutVisit
                                        ) && (
                                                <AuthButton
                                                    className="mar-all-10"
                                                    onClick={() => {
                                                        add_dispensing(
                                                            null,
                                                            visitCycleInfoId,
                                                            null
                                                        );
                                                    }}
                                                >
                                                    {researchAttribute === 0
                                                        ?

                                                        visitOpen === ""?
                                                            formatMessage({id: "subject.dispensing.visitSignDispensing", allowComponent: true})
                                                            :
                                                            visitOpen
                                                        : formatMessage({
                                                            id: "subject.dispensing.visitSignDispensing.apply", allowComponent: true
                                                        })}
                                                </AuthButton>
                                            )}
                                        {(

                                            (permissionsCohort(
                                                auth.project.permissions,
                                                "operation.subject.medicine.reissue", record.cohortStatus
                                            )
                                            || permissionsCohort(
                                                auth.project.permissions,
                                                "operation.subject-dtp.medicine.reissue", record.cohortStatus
                                            ))
                                            // && visitOrder.length !== 0
                                            && canReissue
                                        ) && (
                                                <AuthButton
                                                    type="primary"
                                                    className="mar-all-10"
                                                    onClick={() => {
                                                        reissue_dispensing(
                                                            visitCycleInfoId
                                                        );
                                                    }}
                                                >
                                                    {researchAttribute === 0
                                                        ? formatMessage({
                                                            id: "subject.dispensing.reissue", allowComponent: true
                                                        })
                                                        : formatMessage({
                                                            id: "subject.dispensing.reissue.apply", allowComponent: true
                                                        })}
                                                </AuthButton>
                                            )}
                                    </Col>
                                )}
                            </Row>
                        ) : null
                }
            >
                <Spin
                    spinning={getDispensingLoading}
                >
                    <div style={{ margin: "24px", marginTop: 12 }}>

                        <Row style={{ marginBottom: 20, marginTop: 12 }}>
                            <Col style={{ width: 40, height: 40 }}>
                                <img style={{ width: 40, height: 40 }} src={subjectURL} />
                            </Col>
                            <div style={{ display: 'flex',
                                alignItems: 'center',
                                marginLeft:12,
                                fontWeight: 500,
                                fontSize:20
                            }}>
                                {
                                    ((projectType === 3 && !inRandomIsolation(auth.project.info.number))||(projectType === 2 && subjectCtx.selectRecord?.cohort?.type === 1) ) && record.reRandomInfo != null && record.reRandomInfo.length > 0 ?
                                        record.reRandomInfo[0].value
                                        :
                                        record.shortname
                                }
                            </div>

                            <div style={{display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                            }}>
                                <SubjectStatusItem />
                            </div>
                        </Row>
                        <Row>
                            <Title
                                name={formatMessage({
                                    id: "subject.dispensing.subject", allowComponent: true
                                })}

                            ></Title>
                            {
                                permissionsCohort(
                                    auth.project.permissions,
                                    "operation.subject.medicine.joinTime",
                                    props.record?.cohortStatus
                                ) && record.status !== 10 ?
                                    <Tooltip
                                        title={formatMessage({ id: "common.edit" })}

                                    >
                                        <AuthButton previewProps={{hideOnPreview: true}} size={"small"} type={"link"}
                                                onClick={() => {
                                                    setEditVisible(true)
                                                }}

                                        >
                                            <div className={"download-hover"}>
                                                <svg  width={16} height={16} style={{ paddingTop: 2 }} >
                                                    <use xlinkHref="#icon-bianji"></use>
                                                </svg>
                                            </div>
                                        </AuthButton>
                                    </Tooltip>
                                    : null
                            }
                        </Row>
                        <Form
                            layout="inline"
                            style={{ marginTop: 16, marginBottom:16 }}
                        >
                            {((projectType === 2 && subjectCtx.selectRecord?.cohort?.type === 0)||(projectType === 3 &&inRandomIsolation(auth.project.info.number))) && (
                                <>
                                    <Form.Item
                                        label={<span style={{ color: "#677283" }}>{formatMessage({
                                            id: "projects.second", allowComponent: true
                                        })}</span>}
                                        style={{ marginRight: 6 }}
                                        
                                    >
                                        {record.cohortName}
                                    </Form.Item>
                                    <StyleDivider style={{marginTop: 4,  color: "#677283" }}>
                                        <Divider type={"vertical"} />

                                    </StyleDivider></>
                            )}
                            {(projectType === 3 && !inRandomIsolation(auth.project.info.number)) || (projectType === 2 && subjectCtx.selectRecord?.cohort?.type === 1) &&
                                <>
                                    <Form.Item
                                        label={<span style={{ color: "#677283" }}>{formatMessage({
                                            id: "projects.current.stage", allowComponent: true
                                        })}</span>}
                                        style={{ marginRight: 6 }}

                                    >
                                        {projectType === 3 && !inRandomIsolation(auth.project.info.number)?record.cohortName :record.reRandomName}
                                    </Form.Item>
                                    <StyleDivider style={{ marginTop: 4, color: "#677283" }}>
                                        <Divider type={"vertical"} />

                                    </StyleDivider>
                                </>
                            }
                            <Form.Item
                                label={<span style={{ color: "#677283" }}>{formatMessage({
                                    id: "common.site", allowComponent: true
                                })}</span>}
                                style={{ marginRight: 6 }}
                            >
                                {record.siteNumber}
                                -
                                {record.siteName}
                            </Form.Item>

                            {
                                (projectType === 3 && !inRandomIsolation(auth.project.info.number))||(projectType === 2 && subjectCtx.selectRecord?.cohort?.type === 1)?null:
                                    <>
                                    <StyleDivider style={{ marginTop: 4, color: "#677283" }}>
                                        <Divider type={"vertical"} />

                                    </StyleDivider>
                                    <Form.Item >
                                        <span style={{ color: "#677283" }}>{formatMessage({ id: "check.select.time", allowComponent: true })}</span>：<span>{subjectCtx.selectRecord?.joinTime && subjectCtx.selectRecord.joinTime !== ""?subjectCtx.selectRecord.joinTime:"-"}</span>
                                    </Form.Item>
                                    </>
                            }

                        </Form>

                        <Table bordered={true} pagination={false} dataSource={finalFields} >
                            {
                                (projectType === 3 && !inRandomIsolation(auth.project.info.number))||(projectType === 2 && subjectCtx.selectRecord?.cohort?.type === 1)?
                                    <Table.Column
                                        title={<FormattedMessage id="common.stage" />}
                                        dataIndex="#"
                                        key="#"
                                        width={500}
                                        render={(text, it: any, index) => projectType === 3&& !inRandomIsolation(auth.project.info.number)?it.cohortName:it.reRandomName}
                                        onCell={(_, index: any) => {
                                            let forms = projectType === 3 && !inRandomIsolation(auth.project.info.number)?listData?.reRandomForm:listData?.reRandomForm?.filter((it: any) => it.cohortName === subjectCtx.selectRecord?.cohortName)
                                            let nextStage = 0
                                            if(forms?.length >= 1) {
                                                for (let i = 0; i < forms[0]?.fields.length; i++) {
                                                    let field = forms[0].fields[i];
                                                    if (field.name !== "shortname") {
                                                        nextStage = nextStage + 1
                                                    }
                                                }
                                            }
                                            let secondStage = 0
                                            if(forms?.length > 1){
                                                for (let i = 0; i < forms[1].fields.length; i++) {
                                                    let field = forms[1].fields[i];
                                                    if (field.name !== "shortname"){
                                                       secondStage = secondStage + 1
                                                    }
                                                }
                                            }
                                            if (index === 0) {
                                                return { rowSpan: nextStage };
                                            }else if (index > 0 && index < nextStage){
                                                return { rowSpan: 0 };
                                            } else if (index === nextStage){
                                                return { rowSpan: secondStage };
                                            }else if (index > nextStage){
                                                return { rowSpan: 0 };
                                            }
                                            return {}
                                        }}
                                    />:null
                            }
                            {
                                (projectType === 3 && !inRandomIsolation(auth.project.info.number))||(projectType === 2 && subjectCtx.selectRecord?.cohort?.type === 1)?
                                    <Table.Column
                                        title={<FormattedMessage id="check.select.time" />}
                                        dataIndex="#"
                                        key="#"
                                        width={500}
                                        render={(text, it: any, index) => it.joinTime}
                                        onCell={(_, index: any) => {
                                            let forms = projectType === 3 && !inRandomIsolation(auth.project.info.number)?listData?.reRandomForm:listData?.reRandomForm?.filter((it: any) => it.cohortName === subjectCtx.selectRecord?.cohortName)
                                            let nextStage = 0
                                            if(forms?.length >= 1) {
                                                for (let i = 0; i < forms[0]?.fields.length; i++) {
                                                    let field = forms[0].fields[i];
                                                    if (field.name !== "shortname") {
                                                        nextStage = nextStage + 1
                                                    }
                                                }
                                            }
                                            let secondStage = 0
                                            if(forms?.length > 1){
                                                for (let i = 0; i < forms[1].fields.length; i++) {
                                                    let field = forms[1].fields[i];
                                                    if (field.name !== "shortname"){
                                                        secondStage = secondStage + 1
                                                    }
                                                }
                                            }
                                            if (index === 0) {
                                                return { rowSpan: nextStage };
                                            }else if (index > 0 && index < nextStage){
                                                return { rowSpan: 0 };
                                            } else if (index === nextStage){
                                                return { rowSpan: secondStage };
                                            }else if (index > nextStage){
                                                return { rowSpan: 0 };
                                            }
                                            return {}
                                        }}
                                    />:null
                            }
                            <Table.Column
                                title={<FormattedMessage id="subject.dispensing.form.factor.title" />}
                                dataIndex="#"
                                key="#"
                                width={500}
                                render={(text, it: any, index) => it.label}
                            />
                            <Table.Column
                                title={<FormattedMessage id="subject.dispensing.factor.title" />}
                                dataIndex="#"
                                key="#"
                                width={500}
                                render={(value: any, record: any, index: any) => {
                                    return getValue(value, record, index);
                                }}
                            />
                            {
                                subjectCtx.selectRecord?.attribute.info.random === true ?
                                <Table.Column
                                    title={<FormattedMessage id="subject.dispensing.actual.factor.title" />}
                                    dataIndex="#"
                                    key="#"
                                    width={500}
                                    render={(text, it: any, index) => {
                                        if ((projectType === 3 && !inRandomIsolation(auth.project.info.number)) || (projectType === 2 && subjectCtx.selectRecord?.cohort?.type === 1)){
                                            return subjectCtx.selectRecord?.reRandomActualInfo?.find((i: any) => i.name === it.name && i.cohortId === it.cohortId)?.label ?
                                                subjectCtx.selectRecord?.reRandomActualInfo?.find((i: any) => i.name === it.name && i.cohortId === it.cohortId)?.label : ""
                                        }
                                        return subjectCtx.selectRecord?.actualInfo?.find((i: any) => i.name === it.name)?.label ?
                                            subjectCtx.selectRecord?.actualInfo?.find((i: any) => i.name === it.name)?.label : ""
                                    }}
                                />:null
                            }

                        </Table>
                    </div>


                    {/*发放记录*/}
                    {
                        (subjectCtx.selectRecord?.attribute.info.dispensing === true || subjectCtx.selectRecord?.hasDispensing)?
                            <div style={{paddingTop: "8px", paddingLeft: "24px"}}>
                                <Row justify={"space-between"}>
                                    <Col>
                                        <Title
                                            name={formatMessage({
                                                id: "subject.dispensing.record", allowComponent: true
                                            })}
                                        ></Title>
                                    </Col>
                                    <Col>
                                        {
                                            ((projectType === 3 && !inRandomIsolation(auth.project.info.number)) || (projectType === 2 && subjectCtx.selectRecord?.cohort?.type === 1))&&
                                             permissionsCohort(auth.project.permissions, "operation.subject.medicine.setUp", record.cohortStatus) &&
                                             record.status !== 4 && record.status !== 5 && record.status !== 9 && record.status !== 10 ?
                                                <Button size={"small"} type="link" style={{paddingRight: 24}}
                                                        onClick={settingOnclick}><FormattedMessage id="common.setting"/></Button>
                                                :
                                                null

                                        }
                                    </Col>


                                </Row>
                                <Table
                                    style={{paddingTop: "16px", paddingRight: "24px"}}
                                    size="small"
                                    dataSource={dispensing}
                                    pagination={false}
                                    rowKey={(record) => record.id}
                                >
                                    <Table.Column
                                        title={
                                            <span style={{marginLeft: 16}}>
                                        <FormattedMessage id="common.serial"/>
                                    </span>
                                        }
                                        dataIndex="#"
                                        key="#"
                                        width={80}
                                        render={(text, record, index) => (
                                            <span style={{marginLeft: 16}}>
                                        {index + 1}
                                    </span>
                                )}
                            />
                            {
                                ((projectType === 3 && !inRandomIsolation(auth.project.info.number)) || (projectType === 2 && subjectCtx.selectRecord?.cohort?.type === 1)) ?
                                    <Table.Column
                                        title={formatMessage({
                                            id: "common.stage", allowComponent: true
                                        })}
                                        key="cohortName"
                                        dataIndex="cohortName"
                                        width={200}
                                    />
                                    :
                                    null
                            }
                            <Table.Column
                                title={formatMessage({
                                    id: "visit.cycle.visitName", allowComponent: true
                                })}
                                key="visitInfo"
                                dataIndex="visitInfo"
                                width={300}
                                render={(value, record: any, index) =>
                                    renderVisit(value, record)
                                }
                            />
                            <Table.Column title={formatMessage({ id: 'subject.dispensing.plan.time', allowComponent: true })} key="period"
                                dataIndex="period" width={200}
                                render={(value, record: any, index) => (
                                    renderPeriod(record)
                                )
                                }
                            />
                            <Table.Column
                                title={formatMessage({
                                    id: "common.operation.time", allowComponent: true
                                })}
                                width={220}
                                key="DispensingTime"
                                dataIndex="dispensingTime"
                                render={(value, record: any, index) =>
                                    // (value === null || value === 0) || record.status !== 2 ? '' : moment.unix(value).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss')
                                    value === null ||
                                        value === 0 ||
                                        record.status !== 2
                                        ? "-"
                                        : (
                                            record.tz === "" ?
                                                moment
                                                    .unix(value)
                                                    .utc()
                                                    .add(timeZone, "hour")
                                                    .format("YYYY-MM-DD HH:mm:ss") +
                                                (timeZone >= 0 ? "(UTC+" : "(UTC") +
                                                formatTimezoneOffset(timeZone) +
                                                ")" :
                                                formatTimezone(value, record.tz)
                                        )
                                }
                            />
                            <Table.Column
                                title={formatMessage({id: "shipment.medicine", allowComponent: true})}
                                width={250}
                                key="dispensingMedicines"
                                dataIndex="dispensingMedicines"
                                render={(value, info: any, index) => (

                                    <Col style={{width:250}}>
                                        <span>
                                            {value && value.map((it: any) => (
                                                <span className="mar-rgt-10" key={it.number}>
                                                {it.name}(<strong>{it.number}</strong>)
                                            </span>
                                            ))}
                                            {info.cancelMedicinesHistory?.map((it: any) => it.type === 9 && (
                                                <span className="mar-rgt-10" style={{color: "#4E5969"}} key={it.number}>
                                                {it.name}(<strong>{it.number}</strong>)
                                            </span>))}
                                            {info.otherDispensingMedicinesCount !== null &&
                                                info.otherDispensingMedicinesCount?.map((it: any) => (
                                                    <CustomerTooltip
                                                        it={it}
                                                        record={info.otherDispensingMedicines}
                                                    />
                                                ))
                                            }
                                            {(!info.otherDispensingMedicines ||
                                                (info.otherDispensingMedicines &&
                                                    info.otherDispensingMedicines
                                                        .length === 0)) &&
                                            (!value || (value && value.length === 0)) &&
                                            !info.cancelMedicinesHistory?.find((it: any) => it.type === 9) ?
                                                "-" :
                                                <CombineRowTableData data={handleCombineData(info)} subjectRecord={record} dispensing={info} />
                                            }
                                        </span>

                                    </Col>
                                )}
                            />
                            <Table.Column
                                title={formatMessage({id: "subject.dispensing.realDispensing", allowComponent: true})}
                                width={200}
                                key="realDispensingMedicines"
                                dataIndex="realDispensingMedicines"
                                render={(value, info: any, index) => (
                                    <Col style={{width:200}}>
                                        {value?.map((it: any) => (
                                            <span className="mar-rgt-10">{it.name}(<strong>{it.number}</strong>)</span>
                                        ))}

                                        {info.realOtherDispensingMedicinesCount !==
                                            null && info.realOtherDispensingMedicinesCount?.map(
                                            (it: any) => (
                                                <CustomerTooltip
                                                    key={it.name}
                                                    it={it}
                                                    record={
                                                        info.realOtherDispensingMedicines
                                                    }
                                                />
                                            )
                                        )}
                                        {(
                                            (!value || value.length === 0) &&
                                            (!info.realOtherDispensingMedicines ||info.realOtherDispensingMedicines?.length === 0)
                                        )?
                                            "-":
                                            <CombineRowTableData data={handleRealOtherCombineData(info)} subjectRecord={record} dispensing={info}/>
                                        }
                                    </Col>
                                )}
                            />
                                <Table.Column
                                    title={formatMessage({id: "common.operation", allowComponent: true})}
                                    fixed="right"
                                    width={previewShowFilter(undefined, 300)}
                                    render={(value, data: any, index) =>
                                        renderOpt(value, data, index)
                                    }
                                />
                            </Table>
                            </div>
                            :  <Col style={{ textAlign: 'right' }}>
                                {
                                    ((projectType === 3 && !inRandomIsolation(auth.project.info.number)) || (projectType === 2 && subjectCtx.selectRecord?.cohort?.type === 1 && subjectCtx.selectRecord?.cohort?.lastId === nilObjectId)) && permissionsCohort(auth.project.permissions, "operation.subject.medicine.setUp", record.cohortStatus) && record.status !== 4 && record.status !== 5 && record.status !== 9 ?
                                        <Button size={"small"} type="link" style={{paddingRight: 24}}
                                                onClick={settingOnclick}><FormattedMessage id="common.setting"/></Button>
                                        :
                                        null

                                }
                            </Col>
                    }
                </Spin>
                <Modal
                    className="custom-small-modal"
                    open={isModalVisible}
                    onOk={retrievalConfirm}
                    okText={formatMessage({id: "common.ok"})}
                    onCancel={() => {
                        retrievalClose()
                        setIsModalVisible(false);
                        replaceFrom.resetFields();
                    }}
                    confirmLoading={retrievalDrugLoading}
                    destroyOnClose={true}
                >
                    <p>
                        {formatMessage({
                            id: "subject.dispensing.confirmRetrieval",
                        })}
                    </p>
                    <>

                        <p>
                            {formatMessage({
                                id: "subject.dispensing.retrievalMessage",
                            })}
                        </p>
                    </>
                </Modal>

                {/*设置*/}
                <Modal
                    title={formatMessage({id: "common.setting"})}
                    open={settingOpen}
                    // closable={true}
                    onCancel={settingCanCel}
                    maskClosable={false}
                    centered={true}
                    footer={
                        !settingDisabled?
                        <>
                            <Col>
                                <Button onClick={settingCanCel}>{formatMessage({id: 'common.cancel'})}</Button>
                                <Button onClick={settingOk} type={"primary"}
                                        disabled={settingDisabled}>{formatMessage({id: 'common.ok'})}</Button>
                            </Col>
                        </>:null
                    }
                >
                    {formatMessage({
                        id: "subject.dispensing.open.visit",
                    })}：
                    <span style={{paddingLeft: 0}}>
                        <Switch size='small' checked={settingChecked} disabled={settingDisabled}
                                onClick={settingSwitchOnclick}/>
                    </span>
                    <div style={{paddingLeft: 125, paddingTop: 10}}>
                        {
                            cohorts.length === 0 || !settingChecked?
                                null
                                :
                                <>
                                {projectType === 3&& !inRandomIsolation(auth.project.info.number) ?

                                    (cohorts.length >= 2 ?
                                            <span>
                                                {formatMessage({id: "subject.dispensing.open.visit.tip1"}, {
                                                    "atThisStage": reRandomCohorts[0]?.name,
                                                    "nextStage": reRandomCohorts[1]?.name
                                                })}
                                            </span>
                                            :
                                            <span>{formatMessage({id: "subject.dispensing.open.visit.tip2"}, {"atThisStage": reRandomCohorts[0]?.name})}</span>)
                                    :

                                    (reRandomCohorts >= 2 ?
                                            <span>
                                                {formatMessage({id: "subject.dispensing.open.visit.tip1"}, {
                                                    "atThisStage": reRandomCohorts[0]?.re_random_name,
                                                    "nextStage": reRandomCohorts[1]?.re_random_name
                                                })}
                                            </span>
                                            :
                                            <span>{formatMessage({id: "subject.dispensing.open.visit.tip2"}, {"atThisStage": reRandomCohorts[0]?.re_random_name})}</span>)

                                }
                                </>
                        }
                    </div>
                </Modal>

                <DispensingRegister bind={register_pt} list={list}/>
                <DispensingInvalid bind={invalid_pt} refresh={list} subjectRefresh={props.refresh}/>
                <DispensingAdd
                    bind={dispensing_pt}
                    subjectRefresh={props.refresh}
                    refresh={list}
                />
                <DispensingReason bind={reason_pt} refresh={list}/>
                <HistoryList
                    bind={history_ref}
                    permission={
                        researchAttribute === 0
                            ? permissions(
                                auth.project.permissions,
                                "operation.subject.medicine.trail.print"
                            )
                            : permissions(
                                auth.project.permissions,
                                "operation.subject-dtp.medicine.print"
                            )
                    }
                />
                <DispensingRoom bind={room_pt} refresh={list}/>
                <DispensingAddDTP
                    bind={dispensing_add_dtp_pt}
                    refresh={list}
                    subjectRefresh={props.refresh}
                />
                <DispensingReissueDTP
                    bind={dispensing_reissue_dtp_pt}
                    refresh={list}
                />
                <DispensingUnblindingApproval refresh={list}/>
            </CustomerDrawer>
            <Edit refresh={props.refresh}/>
        </>
    );
};

const CustomerDrawer = styled(Drawer)`
    .ant-drawer-header {
        background: #f9fafb;
        border-bottom: 0;
    }

    .ant-drawer-title {
        line-height: 24px;
    }
`;

interface CombineRowTableDataInterface {
    data: any,
    subjectRecord :any
    dispensing :any
}

export const CombineRowTableData = (props: CombineRowTableDataInterface) => {
    const {data,subjectRecord, dispensing} = props
    const dispensingId = dispensing.id
    const isPage = data.find((it: any) => it.packageNumber && it.packageNumber != "")
    const isLabel = data.find((it: any) => it.label && it.label != "")
    const doseInfo = data.find((it: any) => it.doseInfo && it.doseInfo != "")
    const unblind = data.find((it: any) => it.unBlind !==0)
    const auth = useAuth()
    const subjectCtx = useSubject()
    const intl = useTranslation();
    const {formatMessage} = intl;

    const [, setShowUnBlind ] = useAtom(showUnBlindAtom);
    const [, setShowUnBlindID ] = useAtom(showUnBlindIDAtom);
    const [, setCurrentDispensingId ] = useAtom(currentDispensingIDAtom);
    const [, setCurrentDispensing ] = useAtom(currentDispensingAtom);
    const [, setCurrentMedicine ] = useAtom(currentMedicineAtom);
    const [open, setOpen] = useSafeState<boolean>(false)


    const unBlindShow = (info:any) => {
        setCurrentDispensingId(dispensingId)
        setCurrentDispensing(dispensing)
        setCurrentMedicine(info)
        subjectCtx.setCurrentRecord(subjectRecord)
        setShowUnBlind(true)
        setShowUnBlindID(info.medicineId)
        setOpen(false)
    }

    const showButton = permissions(auth.project.permissions, "operation.subject.unblinding-ip-log") || permissions(auth.project.permissions, "operation.subject.unblinding-ip-view")

    const content = () => {
        return <StyledTable size={"small"}
                      dataSource={data}
                      pagination={false}
                      rowKey={(record:any) => (record.id)}
                      scroll={{y: 'calc(100vh - 140px)',}}
        >
            {
                isPage &&
                <Table.Column
                    title={<FormattedMessage id="drug.medicine.packlist"/>}
                    dataIndex={"packageNumber"}
                    key="packageNumber"
                    ellipsis
                    align="left"
                    render={(value, record, index) => {
                        let newValue = ""
                        if (value !== undefined && value !== "") {
                            newValue = value
                        } else {
                            newValue = "-"
                        }
                        let obj = {
                            children: newValue,
                            props: {rowSpan: data[index].packageNumberRowSpan}
                        }
                        return obj
                    }}
                />
            }
            <Table.Column
                title={<FormattedMessage id={"drug.configure.drugName"} />}
                dataIndex="name"
                key="name"
                ellipsis
            />
            <Table.Column
                title={<FormattedMessage id={"drug.list.drugNumber"} />}
                dataIndex="number"
                key="number"
                ellipsis
                render={(value: any) => {
                    if (value) {
                        return value
                    } else {
                        return "-"
                    }
                }}

            />
            <Table.Column
                title={<FormattedMessage id={"drug.list.batch"} />}
                dataIndex="batchNumber"
                key="batchNumber"
                ellipsis
            />
            <Table.Column
                title={<FormattedMessage id={"drug.list.expireDate"} />}
                dataIndex="expirationDate"
                key="expirationDate"
                ellipsis
                width={120}
            />
            <Table.Column
                title={<FormattedMessage id={"drug.other.count"} />}
                dataIndex="count"
                key="count"
                ellipsis
                render={(value: any) => {
                    if (!value) {
                        return 1
                    } else {
                        return value
                    }
                }}
            />
            {
                isLabel &&
                <Table.Column
                    title={<FormattedMessage id={"report.attributes.dispensing.label"} />}
                    dataIndex="label"
                    key="label"
                    ellipsis
                    render={(value: any) => {
                        if (value || value !== "") {
                            return value
                        } else {
                            return "-"
                        }
                    }}
                />
            }
            {
                doseInfo &&
                <Table.Column
                    title={<FormattedMessage id={"report.attributes.dispensing.dose"} />}
                    dataIndex="doseInfo"
                    key="doseInfo"
                    ellipsis
                    render={(value: any) => {
                        if (value?.doseLevelList) {
                            return value?.doseLevelList?.name
                        } else {
                            return "-"
                        }
                    }}
                />
            }
            {
                unblind &&
                <Table.Column
                    title={formatMessage({
                        id: "common.operation", allowComponent: true
                    })}
                    dataIndex="unBlind"
                    key="unBlind"
                    ellipsis
                    render={(value: any,info:any, ) => {
                        if (value === 0 || !showButton) {
                            return "-"
                        }
                        if (value === 1 ) {
                            return <AuthButton
                                size="small"
                                style={{ padding: 0 , fontSize: "12px"}}
                                type={"link"} onClick={() => {
                                unBlindShow(info)
                            }}>
                                <FormattedMessage id={"subject.brokenBlinded"} />
                            </AuthButton>
                        }

                        if (value === 2) {
                            if (showButton) {
                                return <AuthButton
                                    size="small"
                                    style={{ padding: 0 , fontSize: "12px"}}

                                    type={"link"} onClick={() => {
                                    unBlindShow(info)
                                }}>
                                    <FormattedMessage id={"subject.already.unblinding"} />
                                </AuthButton>
                            }else{
                                return "-"
                            }

                        }
                        return "-"
                    }

                    }
                />
            }


        </StyledTable>
    }

    return <Popover
        placement="top"
        content={content}
        overlayStyle={{width: 900}}
        open={open}
        onOpenChange={setOpen}
    >
        <ExclamationCircleOutlined style={{color: "#165DFF"}}/>
    </Popover>
}

export const CustomerTooltip = (prop: any) => {
    const {Link} = Typography;

    return (
        <span className="mar-rgt-10">
            {prop.it.name}(<span>{prop.it.count}</span>)
        </span>
    );
};

const StatusDiv: any = styled.div`
    height: 22px;
    border-radius: 2px;
    font-size: 12px;
    font-weight: 400;
    text-align: center;
    padding-left: 6px;
    padding-left: 6px;
    align-content: center;
    padding-right: 6px;
    margin-left: 12px;
    background: ${(props: any) => props.background || "rgba(255, 174, 0, 0.1)"};
    color: ${(props: any) => props.textColor || '#ffae00'};
`;

const PeriodDiv = styled.div`
    height: 20px;
    min-width: 34px;
    border-radius: 2px;
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: 400;
    line-height: 17px;
    letter-spacing: 0px;
    text-align: center;
    background: rgba(255, 174, 0, 0.1);
    color: #ffae00;
`;

const PeriodDivCancel = styled.div`
  height: 20px;
  border-radius: 2px;
  font-family: PingFang SC;
  font-size: 12px;
  font-weight: 400;
  line-height: 17px;
  letter-spacing: 0px;
  text-align: center;
  background: rgba(103, 114, 131, 0.1);
  color: #677283;
`;

const StyleDivider = styled.div`
  .ant-divider-vertical {
    border-left: 1px solid #C8C9CC; !important;
    height: 14px;
  }`

const StyledTable = styled(Table)`
  font-size: 12px;
  
  .ant-table-thead > tr > th {
    font-size: 12px !important;;
  }
  
  .ant-table-tbody > tr > td {
    font-size: 12px;
  }
`;