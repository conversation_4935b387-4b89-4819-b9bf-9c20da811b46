import React from "react";
import {<PERSON><PERSON>, Col, Form, Input, message, Modal, notification, Row} from "antd";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {useAuth} from "../../../../context/auth";
import {useSafeState, useSize} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {
    addDispensingVisit,
    dispensingLabelMedicine,
    DoseForm,
    getDispensingConfirmTable,
    getFormula,
    getFormulaForm,
    getReissueMedicineName,
    reissueDispensing,
} from "../../../../api/dispensing";
import {DispensingConfirm} from "./dispensing_confirm";
import {useSubject} from "./context";
import {useGlobal} from "../../../../context/global";
// @ts-ignore
import {ModeLogistics} from "./mode_logistics";
import {CheckCircleOutlined, CloseCircleFilled, InfoCircleFilled} from "@ant-design/icons";
import {FormulaCalculation} from "./formula_calculation";
import dayjs from "dayjs";
import styled from "@emotion/styled";
import {Dose} from "./dose";
import {DrugLabel} from "./drug_label";
import {DrugOpen} from "./drug_open";
import {getProject} from "../../../../api/projects";
import {subjectEdcVerification} from "../../../../api/subject";
import {CustomConfirmModal} from "../../../../components/modal";
import {inRandomIsolation} from "../../../../utils/in_random_isolation";
import {pushScenarioFilter} from "../../../../utils/irt_push_edc_util";
import {useAtom} from "jotai";
import {doseSelectAtom, visitOpenAtom} from "./ctx";

export const DispensingAdd = (props: any) => {
    const auth = useAuth();
    const ctx = useSubject();
    const intl = useTranslation();
    const subject = useSubject();
    const subjectCtx = useSubject();
    const {formatMessage} = intl;

    const [type, setType] = useSafeState<any>(null);
    const [dtp, setDtp] = useSafeState<boolean>(false);
    const [dtpType, setDtpType] = useSafeState<any>([]);
    const [data, setData] = useSafeState<any>([]);
    const [visible, setVisible] = useSafeState<any>(false);
    const [record, setRecord] = useSafeState<any>({});
    const [outSize, setOutSize] = useSafeState<any>(false);
    const [dispensingId, setDispensingId] = useSafeState<any>(null);
    const [isReissue, setIsReissue] = useSafeState<any>(false);
    const [isFirstDispensing, setIsFirstDispensing] = useSafeState<any>(false);
    const projectId = auth.project.id;
    const customerId = auth.customerId;
    const envId = auth.env.id;
    const codeRule = auth.codeRule;
    const connectEdc = auth.project.info.connect_edc;
    const pushMode = auth.project.info.push_mode;
    const projectType = auth.project.info.type;
    const pushScenario = auth.project.info.push_scenario !== undefined? auth.project.info.push_scenario.dispensing_push: false;

    const [visitName, setVisitName] = useSafeState<any>("");
    const [drugOption, setDrugOption] = useSafeState<any>([]);
    const [drugNameOption, setDrugNameOption] = useSafeState<any>([]);
    const [form] = Form.useForm();
    const {TextArea} = Input;
    const [sendType, setSendType] = useSafeState<number>(0);
    const [otherLogistics, setOtherLogistics] = useSafeState<boolean>(false);
    const [visits, setVisits] = useSafeState<any>("");
    const [openDrug, setOpenDrug] = useSafeState<any>([]);
    const [visitIdState, setVisitIdState] = useSafeState<any>(null);
    const [visitSign, setVisitSign] = useSafeState<any>(null);

    const [subjectId, setSubjectId] = useSafeState<any>(null);
    const [deleteDose, setDeleteDose] = useSafeState<any>("");
    const {
        runAsync: edcSubjectVerificationRun,
        loading: edcSubjectVerificationRunLoading,
    } = useFetch(subjectEdcVerification, { manual: true });
    const [edcSupplier, setEdcSupplier] = useSafeState(0);
    const { runAsync: runGetProject, loading } = useFetch(() => getProject({ id: projectId }), {
        refreshDeps: [projectId],
        onSuccess: (result:any) => {
            setEdcSupplier(result.data.info.edcSupplier)
        }
    });

    const dispensing_confirm_pt: any = React.useRef();

    const {runAsync: getLabelMedicineRun, loading: getLabelMedicineLoading} = useFetch(
        dispensingLabelMedicine,
        {manual: true}
    );
    const {runAsync: getReissueMedicine, loading: getReissueMedicineLoading} =
        useFetch(getReissueMedicineName, {manual: true});
    const {runAsync: getFormulaRun, loading: getFormulaLoading} = useFetch(
        getFormula,
        {manual: true}
    );

    const {runAsync: getFormulaFormRun, loading: getFormulaFormLoading} = useFetch(
        getFormulaForm,
        {manual: true}
    );

    const {runAsync: getDispensingConfirmTableRun, loading: getDispensingConfirmTableLoading} = useFetch(
        getDispensingConfirmTable,
        {manual: true}
    );

    const {
        runAsync: addDispensingVisitRun,
        loading: addDispensingVisitLoading,
    } = useFetch(addDispensingVisit, {manual: true});
    const {
        runAsync: reissueDispensingRun,
        loading: reissueDispensingLoading,
    } = useFetch(reissueDispensing, {manual: true});
    const [visitOpen, ] = useAtom(visitOpenAtom);

    const hide = () => {
        setVisitIdState(null);
        setOpenDrug([]);
        setVisible(false);
        form.resetFields();
        setDrugOption([]);
        subjectCtx.setDrugOption([])
        setVisitName("");
        setDrugNameOption([]);
        subjectCtx.setDrugNameOption([])
        setSendType(0);
        setDtp(false);
        setVisits("");
        setOtherLogistics(false);
        subjectCtx.setConfigData({});
        subjectCtx.setRefreshMedicine(0);
        setDtpType([])
        subject.setFormulaForm([])
        subject.setFormulaRes([])
        subjectCtx.setDrugSelect({})
        subjectCtx.setSelectOption({})
        setDrugLabelFrom({})
        setDrugNameFrom({})
        subjectCtx.setFormulaNameRes({})
        subjectCtx.setFormulaLabelRes({})
        subjectCtx.setUpdateAdd(0)
        setDoseForm(undefined)
        ctx.setFieldSelect(undefined)
        setOutSize(false)
        ctx.setInheritValue(false)
        ctx.setIsPage(false)
        ctx.setIsLevel(false)
        ctx.setIsSelectDose(false)
        setDeleteDose("")
        setDose(true)

    };

    const show = (
        record: any,
        type: any,
        id: any,
        visitId: any,
        visits: any,
        isReissue: any,
        visitSign: any,
        outSize: any,
        isFirstDispensing: any,
    ) => {
        let visitInfo :any = visits.find((it: any) => it.id === visitId);
        setVisits(visitInfo?.name);
        setVisitIdState(visitId);
        setVisitSign(visitSign);
        setDispensingId(null);
        setIsReissue(isReissue);
        setIsFirstDispensing(isFirstDispensing);
        if (id) {
            setDispensingId(id);
        }
        setOutSize(outSize)
        setRecord(record);
        if (type === 2 || isReissue) {
            getReissueMedicine({
                subject_id: record.id,
                visit_id: visitId,
                role_id: auth.project.permissions.role_id,
            }).then((res: any) => {
                if (res.data) {
                    setDeleteDose(res.data.delete_dose)
                    form.setFieldsValue({visit: visitId});
                    setVisitName(res.data.visit_name);
                    form.setFieldsValue({...form.getFieldsValue, "openSetting": [{labels:[{}]},]})
                    if (res.data.medicine_name) {
                        setDrugNameOption(res.data.medicine_name);
                        subjectCtx.setDrugNameOption(res.data.medicine_name)
                        if (res.data.medicine_name?.length === 1 ) {
                            subject.setSelectOption({
                                // 00 代表  父索引 +子索引
                                [0+""+0]: res.data.medicine_name[0]
                            });
                            drugNameFrom[0] = res.data.medicine_name[0].customerCalculation
                            setDrugNameFrom(drugNameFrom)
                            let openSettingInfo: any = {"labels":[{name: res.data.medicine_name[0].key}]}
                            if (res.data.medicine_name[0].custom_dispensing_number?.length === 1) {
                                openSettingInfo = {"labels":[{name: res.data.medicine_name[0].key, count: res.data.medicine_name[0].custom_dispensing_number[0].value}]}
                            }
                            form.setFieldsValue({...form.getFieldsValue, "openSetting": [openSettingInfo,]})
                        }
                    }
                }
            });
        } else {
            let cohortId = record.cohortId;
            if (projectType == 3 && !inRandomIsolation(auth.project.info.number)){
                cohortId = record.cohort.id;
            }
            getFormulaFormRun({customerId, envId, cohortId:cohortId, subjectId:record.id, visitId}).then(
                (res:any) => {
                    subject.setFormulaForm(res.data)
                }
            )
            getDispensingLabelMedicine(record.id, visitId, id)
        }


        setDtp(visitInfo?.dtp);
        setType(type);
        setVisible(true);
        setDtpType(visitInfo?.DTPType);
        runGetProject().then();
        setSubjectId(record.id);
        get_dose_form(record, visitId, id, visitSign)

    };
    const get_formula = (
        record: any,
        visit_id: any,
        dispensingId: any,
        visitSign: any,
        dose: any,
    ) => {
        let cohortId = record.cohortId;
        if (projectType == 3 && !inRandomIsolation(auth.project.info.number)){
            cohortId = record.cohort.id;
        }
        getFormulaRun({
            env_id: envId,
            cohort_id: cohortId,
            subject_id: record.id,
            visit_id,
            role_id: auth.project.permissions.role_id,
            dose: dose,
        }).then((value: any) => {
            subjectCtx.setFormulaData(value.data);
            let medicine: any = [];
            subjectCtx.setFormulaMedicine(value.data?.medicine_name);
            value.data?.medicine_name?.map((value: any, index: any) => {
                let info :any = {value: index, name: value.name}
                if (value?.dtp?.length === 1) {
                    info.dtp = value?.dtp[0]
                }
                medicine.push(info);
            });
            if (form) {
                form?.setFieldsValue({
                    ...form.getFieldsValue(),
                    medicine: medicine,
                    weight: value.data?.weight_value,
                    height: value.data?.height_value,
                    age: value.data?.age_value ? dayjs(value.data?.age_value) : undefined,
                });
            }
            if (!dispensingId || visitSign) {
                subjectCtx.setRefreshMedicine(subjectCtx.refreshMedicine + 1);
            }
        });
    };

    // 获取访视周期选项
    const getDispensingLabelMedicine = (subjectId:any, visitId:any, id:any)   => {
        getLabelMedicineRun({subjectId,visitId,id}).then(
            (res:any) => {
                form.setFieldsValue({visit: visitId});
                setVisitName(res.data?.visitName);
                setDrugLabel(res.data?.label, res.data?.medicine)
            }
        )
    }

    const {runAsync: DoseFormRun, loading: DoseFormLoading} = useFetch(
        DoseForm,
        {manual: true}
    );
    const [doseForm, setDoseForm] = useSafeState<any>(undefined);
    const [dose, setDose ] = useAtom(doseSelectAtom);

    const get_dose_form = (record:any, visitId:any, id :any,visitSign:any) => {
        DoseFormRun({subjectId:record.id, visitId}).then(
            (res:any) => {
                setDoseForm(res.data?.field)
                if(res.data?.inheritValue){
                    ctx.setInheritValue(true)
                    setDose(false)
                    ctx.setUpdateAdd(ctx.updateAdd+1)
                    form.setFieldsValue({...form.getFieldsValue,[res.data.field.variable]:res.data.inheritValue})
                    ctx.setFieldSelect(res.data.inheritValue)
                }
                if (res.data?.isPage){
                    ctx.setIsPage(res.data.isPage)
                }
                if (res.data?.isLevel){
                    ctx.setIsLevel(res.data?.isLevel)
                }
                // 剂量 请求按公式
                get_formula(record, visitId, id, visitSign, res.data?.inheritValue);
            }
        )

    }

    //EDC对接项目需要校验中心
    const save_confirm = () => {
        if(pushScenarioFilter(connectEdc, pushMode, edcSupplier, pushScenario)){
            let subjectData = {
                id: subjectId,
            };
            edcSubjectVerificationRun(subjectData).then((resp: any) => {
                if (!resp.data.linkStatus) {      // 请求接口响应异常
                    notification.open({
                        message: (
                            <div
                                style={{
                                    fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                    fontSize: "14px",
                                }}
                            >
                                <CheckCircleOutlined
                                    style={{
                                        color: "#00BB00",
                                        paddingRight: "8px",
                                    }}
                                />
                                {formatMessage({ id: "common.success" })}
                            </div>
                        ),
                        description: (
                            <div style={{ paddingLeft: "20px", color: "#646566" }}>
                                {formatMessage({
                                    id: "subject.edc.interface.error",
                                })}
                            </div>
                        ),
                        duration: 5,
                        placement: "top",
                        style: {
                            width: "720px",
                            background: "#F0FFF0",
                            borderRadius: "4px",
                        },
                    });
                    confirm_dispensing();
                }else{
                    // 1中心匹配 2中心不匹配 3 未获取到中心编号或者名称
                    if (resp.data.siteStatus == 1) {
                        if(!record.attribute.info.random && record.attribute.info.dispensing && isFirstDispensing){
                            edcRandomLogic(resp);
                        }else{
                            confirm_dispensing();
                        }
                    }else if (resp.data.siteStatus == 2){
                        CustomConfirmModal({
                            icon: <InfoCircleFilled style={{ color: "#FFAE00"}} />,
                            title: formatMessage({ id: "common.tips" }),
                            content: formatMessage({ id: "subject.edc.site.inconsistent" }),
                            okText: formatMessage({ id: "subject.edc.continue.dispense" }),
                            cancelText: formatMessage({ id: "common.cancel" }),
                            onOk: () =>
                            {
                                if(!record.attribute.info.random && record.attribute.info.dispensing && isFirstDispensing){
                                    edcRandomLogic(resp);
                                }else{
                                    confirm_dispensing();
                                }
                            }
                        });
                    }else{
                        CustomConfirmModal({
                            icon: <InfoCircleFilled style={{ color: "#FFAE00"}} />,
                            title: formatMessage({ id: "common.tips" }),
                            content: formatMessage({ id: "subject.edc.site.empty" }),
                            okText: formatMessage({ id: "subject.edc.continue.dispense" }),
                            cancelText: formatMessage({ id: "common.cancel" }),
                            onOk: () =>
                            {
                                if(!record.attribute.info.random && record.attribute.info.dispensing && isFirstDispensing){
                                    edcRandomLogic(resp);
                                }else{
                                    confirm_dispensing();
                                }
                            }
                        });
                    }
                }
            });
        }else{
            confirm_dispensing();
        }
    };


    // 发药逻辑
    const edcRandomLogic = (resp: any)=>{
        if(resp.data.edcSubjectStatus === 2){  // EDC筛选失败
            notification.open({
                message: (
                    <div
                        style={{
                            fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                            fontSize: "14px",
                        }}
                    >
                        <CloseCircleFilled
                            style={{
                                color: "#F96964",
                                paddingRight: "8px",
                            }}
                        />
                        {formatMessage({ id: "common.operation.error" })}
                    </div>
                ),
                description: (
                    <div style={{ paddingLeft: "20px", color: "#646566" }}>
                        {formatMessage({
                            id: "subject.edc.dispensing.failure.filter.failed",
                        })}
                    </div>
                ),
                duration: 5,
                placement: "top",
                style: {
                    width: "720px",
                    background: "#FEF0EF",
                    borderRadius: "4px",
                },
            });
        }else if(resp.data.edcSubjectStatus === 5){   // EDC已退出
            notification.open({
                message: (
                    <div
                        style={{
                            fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                            fontSize: "14px",
                        }}
                    >
                        <CloseCircleFilled
                            style={{
                                color: "#F96964",
                                paddingRight: "8px",
                            }}
                        />
                        {formatMessage({ id: "common.operation.error" })}
                    </div>
                ),
                description: (
                    <div style={{ paddingLeft: "20px", color: "#646566" }}>
                        {formatMessage({
                            id: "subject.edc.dispensing.failure.exit",
                        })}
                    </div>
                ),
                duration: 5,
                placement: "top",
                style: {
                    width: "720px",
                    background: "#FEF0EF",
                    borderRadius: "4px",
                },
            });
        }else if(resp.data.edcSubjectStatus === 6){     // EDC完成研究
            notification.open({
                message: (
                    <div
                        style={{
                            fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                            fontSize: "14px",
                        }}
                    >
                        <CloseCircleFilled
                            style={{
                                color: "#F96964",
                                paddingRight: "8px",
                            }}
                        />
                        {formatMessage({ id: "common.operation.error" })}
                    </div>
                ),
                description: (
                    <div style={{ paddingLeft: "20px", color: "#646566" }}>
                        {formatMessage({
                            id: "subject.edc.dispensing.failure.complete.the.study",
                        })}
                    </div>
                ),
                duration: 5,
                placement: "top",
                style: {
                    width: "720px",
                    background: "#FEF0EF",
                    borderRadius: "4px",
                },
            });
        }else{
            confirm_dispensing();
        }
    };

    const getFormulaArray = () => {
        let useFormula :any =[]
        if (Array.isArray(subjectCtx?.configData)){
            subjectCtx.configData?.map(
                (item:any) => {
                    if (item.useFormula){
                        useFormula.push(item.useFormula)
                    }
                }
            )
        }
        Object.keys(subjectCtx?.formulaLabelRes).forEach(
            (item:any) => {
                if (subjectCtx.formulaLabelRes[item][0]?.useFormula){
                    useFormula.push(subjectCtx.formulaLabelRes[item][0]?.useFormula)

                }
            }
        )
        Object.keys(subjectCtx.formulaNameRes).forEach(
            (item:any) => {
                if (subjectCtx.formulaNameRes[item][0]?.useFormula){
                    useFormula.push(subjectCtx.formulaNameRes[item][0]?.useFormula)
                }
            }
        )
        return useFormula
    }

    const confirm_dispensing = () => {
        form.validateFields()
            .then((value) => {
                let formula_medicine: any = [];
                value.medicine?.map((item: any, index: any) => {
                    formula_medicine.push({
                        info: subjectCtx.formulaMedicine[index],
                        count: item.number,
                        weight: subjectCtx.configData[index].actualWeight,
                        useFormula:subjectCtx.configData[index]?.useFormula,
                        formulaCount :subjectCtx.configData[index].number,
                        dtp:item?.dtp,

                    });
                });

                let labels: any = []
                let customForm :any = {}
                value.drugLabel?.forEach(
                    (item:any, index:any) => {
                        if (item.form){
                            Object.keys(item.form).forEach(
                                (key:any)=>{
                                    customForm[key] = item.form[key]
                                }
                            )
                        }
                        item.labels?.map((label:any)=>{
                            labels.push({
                                id: drugOption[label.label]?.id,
                                label: drugOption[label.label]?.name,
                                count:label.count,
                                level:label.level,
                                dtp:label.dtp,
                                useFormula:label.useFormula,
                                formulaCount:label.formulaCount,
                            })
                        })
                    }
                )
                value.openSetting?.forEach(
                    (item:any) => {
                        if (item.form){
                            Object.keys(item.form).forEach(
                                (key:any)=>{
                                    customForm[key] = item.form[key]
                                }
                            )
                        }
                    }
                )
                let useFormulas :any = getFormulaArray()
                let cohortId = record.cohortId;
                if (projectType == 3 && !inRandomIsolation(auth.project.info.number)){
                    cohortId = record.cohort.id;
                }
                const data :any = {
                    dispensing_id: dispensingId,
                    customer_id: customerId,
                    project_id: projectId,
                    env_id: envId,
                    cohort_id: cohortId,
                    subject_id: record.id,
                    visit_id: value.visit,
                    // visit_label_id: drugOption[value.drugLabel]?.id,
                    // label: drugOption[value.drugLabel].name,
                    visit_labels: labels,
                    reason: value.reason,
                    remark: value.remark,
                    app: codeRule,
                    role_id: auth.project.permissions.role_id,
                    send_type: value.send_type,
                    logistics: value.logistics,
                    other: value.other,
                    number: value.number,
                    open_setting: openDrug,
                    formula_medicine: formula_medicine, // 包括每个药物计算的实际体重
                    formula_weight: value.weight, // 体重
                    formula_height: value.height, // 身高
                    formula_age: value.age, //年龄
                    form: customForm, //自定义表单
                    useFormulas: useFormulas,     //使用计算的体重
                    out_size: outSize
                };
                if (value[doseForm?.variable]){
                    data.dose_info = {key:doseForm.variable, value: value[doseForm?.variable]} //剂量信息
                }
                addDispensingVisitRun(data).then((response: any) => {
                    form.resetFields();
                    hide();
                    if (
                        codeRule === 1 &&
                        response.data.dispensing_info.dispensingMedicines
                            .length > 0
                    ) {
                        notification.open({
                            duration: 3,
                            message: formatMessage({
                                id: "workTask.system.add.success",
                            }),
                        });
                    } else {
                        message.success(response.msg);
                    }
                    props.refresh();
                    props.subjectRefresh();
                    ctx.setDispensingConfirmVisible(false);
                    // 订单回显
                    if (response.data.order_info?.length > 0) {
                        ctx.setShowTitle(
                            formatMessage({
                                id: "subject.dispensing.dispensing",
                            })
                        );
                        ctx.setShowData(response.data.order_info);
                        ctx.setVisible(true);
                    }
                });
            })
            .catch(() => {
        });
    };

    const save = () => {
        // 发药
        if (type === 1) {
            dispensing_confirm();
        }
        // 补发
        if (type === 2) {
            if (deleteDose !== "") {
                return message.error(deleteDose)
            }
            form.validateFields().then((value) => {
                let openSetting: any[] = [];
                value.openSetting?.map((it: any) =>
                    it.labels?.map((label:any)=> {
                        openSetting.push({
                            ...drugNameOption[label.name],
                            count: label.count,
                            dtp: label?.dtp,
                        })
                    })
                );

                value = {...value, openSetting: openSetting};
                setOpenDrug(openSetting);
                dispensing_confirm_pt.current.show(
                    formatMessage({id: "common.tips"}),
                    record,
                    visitName,
                    openSetting,
                    2,
                    value
                );
            });
        }
    };

    //EDC对接项目需要校验中心
    const reissue_confirm = () => {
        if(pushScenarioFilter(connectEdc, pushMode, edcSupplier, pushScenario)){
            let subjectData = {
                id: subjectId,
            };
            edcSubjectVerificationRun(subjectData).then((resp: any) => {
                if (!resp.data.linkStatus) {      // 请求接口响应异常
                    notification.open({
                        message: (
                            <div
                                style={{
                                    fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                    fontSize: "14px",
                                }}
                            >
                                <CheckCircleOutlined
                                    style={{
                                        color: "#00BB00",
                                        paddingRight: "8px",
                                    }}
                                />
                                {formatMessage({ id: "common.success" })}
                            </div>
                        ),
                        description: (
                            <div style={{ paddingLeft: "20px", color: "#646566" }}>
                                {formatMessage({
                                    id: "subject.edc.interface.error",
                                })}
                            </div>
                        ),
                        duration: 5,
                        placement: "top",
                        style: {
                            width: "720px",
                            background: "#F0FFF0",
                            borderRadius: "4px",
                        },
                    });
                    reissue_confirm_method();
                }else{
                    // 1中心匹配 2中心不匹配 3 未获取到中心编号或者名称
                    if(resp.data.siteStatus == 1){
                        reissue_confirm_method();
                    }else if(resp.data.siteStatus == 2){
                        CustomConfirmModal({
                            icon: <InfoCircleFilled style={{ color: "#FFAE00"}} />,
                            title: formatMessage({ id: "common.tips" }),
                            content: formatMessage({ id: "subject.edc.site.inconsistent" }),
                            okText: formatMessage({ id: "subject.edc.continue.dispense" }),
                            cancelText: formatMessage({ id: "common.cancel" }),
                            onOk: () =>
                                reissue_confirm_method()
                        });
                    }else{
                        CustomConfirmModal({
                            icon: <InfoCircleFilled style={{ color: "#FFAE00"}} />,
                            title: formatMessage({ id: "common.tips" }),
                            content: formatMessage({ id: "subject.edc.site.empty" }),
                            okText: formatMessage({ id: "subject.edc.continue.dispense" }),
                            cancelText: formatMessage({ id: "common.cancel" }),
                            onOk: () =>
                                reissue_confirm_method()
                        });
                    }
                }
            });
        }else{
            reissue_confirm_method();
        }
    };

    const reissue_confirm_method = () => {
        form.validateFields().then((value) => {
            const data = {
                customer_id: customerId,
                project_id: projectId,
                env_id: envId,
                cohort_id: record.cohortId,
                subject_id: record.id,
                visit_id: value.visit,
                medicine_info: openDrug,
                app: codeRule,
                roleId: auth.project.permissions.role_id,
                remark: value.remark,
                reason: value.reason,
                send_type: value.send_type,
                logistics: value.logistics,
                other: value.other,
                number: value.number,
            };
            reissueDispensingRun(data).then((response: any) => {
                form.resetFields();
                hide();
                if (codeRule === 1 && response.data.app) {
                    notification.open({
                        duration: 3,
                        message: formatMessage({
                            id: "workTask.system.add.success",
                        }),
                    });
                } else {
                    message.success(response.msg);
                }
                props.refresh();
                if (response.data.resOrderInfo?.length > 0) {
                    ctx.setShowTitle(
                        formatMessage({id: "subject.dispensing.reissue"})
                    );
                    ctx.setShowData(response.data?.resOrderInfo);
                    ctx.setVisible(true);
                }
            });
        });
    };

    // 设置研究产品标签选项
    const [drugLabelFrom, setDrugLabelFrom] = useSafeState<any>({});
    const [drugNameFrom, setDrugNameFrom] = useSafeState<any>({});



    const setDrugLabel: any = (option: any, optionsList :any, doseAdjustment:boolean) => {
        form.setFieldsValue({drugLabel: null, name: null});
        setDrugOption(option);
        subjectCtx.setDrugOption(option)
        // 7549 优先自动选择勾选了【发放展示全部】的标签
        if (option.filter((it:any) => it.defaultShow).length > 0 && !doseAdjustment) {
            let selectIndex :number = 0
            const defaultShowLabel = option.reduce((res:any, item:any, index:any) => {
                if (!item.defaultShow) return res
                res.drugSelect[`${selectIndex}0`] = item
                selectIndex++
                res.drugLabelFrom = res.drugLabelFrom.concat(item.customerCalculation)
                const dtp = item.dtp && item.dtp.length === 1 ? item.dtp[0] : null
                res.drugLabel = res.drugLabel.concat({labels:[{label:index, dtp: dtp}]})
                return res
            }, {drugSelect: {}, drugLabelFrom: [], drugLabel: []})
            subjectCtx.setDrugSelect({...defaultShowLabel.drugSelect})
            setDrugLabelFrom(defaultShowLabel.drugLabelFrom)
            form.setFieldsValue({...form.getFieldsValue, "drugLabel": defaultShowLabel.drugLabel})
        } else if (option.length === 1 && !doseAdjustment) {
            // if option只有一条 默认选择
            subjectCtx.setDrugSelect({[0+""+0]:option[0]})
            drugLabelFrom[0] = option[0].customerCalculation
            form.setFieldsValue({...form.getFieldsValue, "drugLabel": [{labels:[{label:0}]},]})
            setDrugLabelFrom(drugLabelFrom)
            if (option[0]?.dtp && option[0]?.dtp?.length === 1){
                form.setFieldsValue({...form.getFieldsValue, "drugLabel": [{labels:[{label:0, dtp:option[0]?.dtp[0]}]},]})
            }
        } else {
            form.setFieldsValue({...form.getFieldsValue, "drugLabel": [{labels:[{}]},]})
        }
        subjectCtx.setLabelAdd(hasOtherIDCheck(option))
        subjectCtx.setNameAdd(hasOtherIDCheck(optionsList))
        // 7549 优先自动选择勾选了【发放展示全部】的标签
        if (optionsList.filter((it:any) => it.defaultShow).length > 0 && !doseAdjustment) {
            let selectIndex :number = 0

            const defaultShowLabel = optionsList.reduce((res:any, item:any, index:any) => {
                if (!item.defaultShow) return res
                res.selectOption[`${selectIndex}0`] = item
                selectIndex++
                res.drugNameFrom = res.drugNameFrom.concat(item.customerCalculation)
                const dtp = item.dtp && item.dtp.length === 1 ? item.dtp[0] : null
                const count = item.custom_dispensing_number?.length == 1 ? item.custom_dispensing_number[0].value : null
                res.openSetting = res.openSetting.concat({
                    labels:[{
                        name: item.key,
                        count: count,
                        dtp: dtp
                    }]
                })
                return res
            }, {selectOption: {}, drugNameFrom: [], openSetting: []})
            subjectCtx.setSelectOption(defaultShowLabel.selectOption)
            setDrugNameFrom(defaultShowLabel.drugNameFrom)
            form.setFieldsValue({...form.getFieldsValue, "openSetting": defaultShowLabel.openSetting})
        } else if (optionsList?.length === 1 && !doseAdjustment) {
            subject.setSelectOption({
                // 00 代表  父索引 +子索引
                [0+""+0]: optionsList[0]
            });
            drugNameFrom[0] = optionsList[0].customerCalculation
            setDrugNameFrom(drugNameFrom)
            let openSettingInfo: any = {"labels":[{name: optionsList[0].key}]}
            if (optionsList[0].custom_dispensing_number?.length === 1) {
                openSettingInfo["labels"][0]["count"] = optionsList[0].custom_dispensing_number[0].value
            }
            if (optionsList[0]?.dtp?.length === 1) {
                openSettingInfo["labels"][0]["dtp"] = optionsList[0]?.dtp[0]
            }
            form.setFieldsValue({...form.getFieldsValue, "openSetting": [openSettingInfo,]})
        } else {
            form.setFieldsValue({...form.getFieldsValue, "openSetting": [{"labels":[{}]},]})

        }
        setDrugNameOption(optionsList);
        subjectCtx.setDrugNameOption(optionsList);

    };
    React.useImperativeHandle(props.bind, () => ({show}));


    const hasOtherIDCheck = (option:any[]) => {
        const seenIds = new Set();
        for (let i = 0; i < option.length; i++) {
            if (!seenIds.has(option[i].id)){
                seenIds.add(option[i].id)
            }
        }
        return seenIds.size > 1

    }
    // 打印房间号页面


    // 计算公式中 无法匹配
    const formulaMatch = ()=>{
        let outSize :boolean = false
        if (Array.isArray(subjectCtx.configData)){
            subjectCtx.configData?.map(
                (item:any) => {
                    if (item.out_size){
                        outSize = true
                    }
                }
            )
        }

        Object.keys(subjectCtx.formulaLabelRes).forEach(
            (item:any) => {
                if (subjectCtx.formulaLabelRes[item][0]?.name?.find((it:any)=> it.outSize=== true)){
                    outSize = true
                }
            }
        )
        Object.keys(subjectCtx.formulaNameRes).forEach(
            (item:any) => {
                if (subjectCtx.formulaNameRes[item][0]?.name?.find((it:any)=> it.outSize=== true)){
                    outSize = true
                }
            }
        )
        return outSize
    }

    const dispensing_confirm = () => {
        if (formulaMatch()){
            return message.error(formatMessage({id:"subject.dispensing.drug.formula.error"}))
        }

        form.validateFields().then((value) => {
            let openSetting: any[] = [];
            // 开放药物
            value.openSetting?.map((it: any) =>
                it.labels?.map((label:any)=> {
                    openSetting.push({
                        ...drugNameOption[label.name],
                        count: label.count,
                        level:label.level,
                        dtp:label?.dtp,
                        useFormula: label?.useFormula,
                        formulaCount: label?.formulaCount,
                    })
                })
            );
            let drugSetting: any[] = []
            // 公式计算药
            value.medicine?.map((it: any, index: number) =>
                drugSetting.push({
                    name: it.name,
                    count: it.number,
                    spec: subjectCtx.configData[index]?.spec,
                    is_other: subjectCtx.configData[index]?.is_other,
                })
            );
            setOpenDrug(openSetting);

            if (value.drugLabel?.length > 0) {
                let label :any = []
                value.drugLabel?.forEach(
                    (it:any) => {
                        it?.labels?.forEach(
                            (item:any) => {
                                label.push({count:item.count, name : drugOption[item.label].name, id:drugOption[item.label].id, level:item.level, spec :drugOption[item.label]?.values?.spec})
                            }
                        )
                    }
                )
                getDispensingConfirmTableRun({
                    envId,
                    cohortId:record.cohortId,
                    roleId: auth.project.permissions.role_id,
                    subjectId: record.id,
                    dispensingId: dispensingId,
                    visitId: visitIdState,
                    info:label,
                    levelOption:value[doseForm?.variable]
                }).then(
                    (res:any)=> {
                        value = {...value, openSetting: [...res.data,...openSetting, ...drugSetting]};
                        dispensing_confirm_pt.current.show(
                            formatMessage({id: "common.tips"}),
                            record,
                            visitName,
                            null,
                            1,
                            value,
                            dispensingId,
                        );
                    }
                )
            }else{

                value = {...value, openSetting: [...openSetting, ...drugSetting]};
                dispensing_confirm_pt.current.show(
                    formatMessage({id: "common.tips"}),
                    record,
                    visitName,
                    null,
                    1,
                    value,
                    dispensingId,
                );
            }


        });
    };


    const g = useGlobal();
    const subjectWidthRef: any = React.useRef();

    const size: any = useSize(subjectWidthRef);

    const formItemLayout = () => {
        let width =
            g.lang === "en"
                ? dispensingId != null || type !== 1
                    ? 140
                    : 128
                : dispensingId != null || type !== 1
                    ? 118
                    : 128;
        if (width < size?.width + 10) {
            width = size?.width + 10;
        }
        subjectCtx.setLabelWidth(width)
        return {
            labelCol: {style: {width: width, }},
        };
    };
    //
    // useEffect(()=>{
    //     if (subjectCtx.updateAdd !== 0){
    //         updateOption(subjectCtx, drugOption, drugNameOption, setDrugOption, setDrugNameOption, form)
    //     }
    // }, [subjectCtx.updateAdd])

    return (
        <Modal
            centered
            className="custom-medium-modal"
            title={
                type === 1
                    ? formatMessage({
                        id: dispensingId
                            ? "subject.dispensing.dispensing"
                            :
                            visitOpen !== ""?
                                visitOpen
                                :
                                "subject.dispensing.visitSignDispensing",
                    })
                    : formatMessage({id: "subject.dispensing.reissue"})
            }
            visible={visible}
            onCancel={hide}
            maskClosable={false}
            footer={
                <Row justify="space-between">
                    <Col></Col>
                    <Col>
                        <Button onClick={hide}>
                            <FormattedMessage id="common.cancel"/>
                        </Button>
                        <Button
                            disabled={
                                !(
                                    (drugOption && drugOption.length > 0) ||
                                    (drugNameOption &&
                                        drugNameOption.length > 0) ||
                                    subjectCtx.setFormulaData
                                )
                            }
                            className="mar-lft-10"
                            onClick={save}
                            type="primary"
                            loading={
                                addDispensingVisitLoading ||
                                reissueDispensingLoading ||
                                edcSubjectVerificationRunLoading
                            }
                        >
                            <FormattedMessage id="common.ok"/>
                        </Button>
                    </Col>
                </Row>
            }
        >
            <CustomForm labelWrap form={form} {...formItemLayout()}>
                {auth.project.info.type === 2 && (
                    <Col>
                        <Form.Item
                            label={formatMessage({
                                id: "projects.second",
                            })}
                        >
                            {record.cohort?.type === 1?record.cohortName + " - " + record.reRandomName:record.cohortName}
                        </Form.Item>
                    </Col>
                )}
                {auth.project.info.type === 3 && (
                    <Col>
                        <Form.Item
                            label={formatMessage({
                                id: "common.stage",
                            })}
                        >
                            {record.cohortName}
                        </Form.Item>
                    </Col>
                )}
                <Form.Item
                    label={
                        <span ref={subjectWidthRef}>
                            {record.attribute
                                ? ((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn === "")?(formatMessage({ id: "subject.number" })):((record.attribute.info.subjectReplaceText !== "" && record.attribute.info.subjectReplaceTextEn === "")?record.attribute.info.subjectReplaceText:((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn !== "")?(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:formatMessage({ id: "subject.number" })):(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:record.attribute.info.subjectReplaceText))))
                                : formatMessage({ id: "subject.number" })}
                        </span>
                    }
                    style={{marginBottom: "22px", marginTop: "6px"}}
                >
                    <Row>
                        {
                            projectType === 3 && !inRandomIsolation(auth.project.info.number)?
                                <>
                                    {
                                        record.info != null && record.info.length > 0 ?
                                            record.info[0].value
                                            :
                                            null
                                    }
                                </>
                                :
                                <>
                                    {record.shortname}
                                </>
                        }
                    </Row>
                </Form.Item>
                {!record.randomNumber ||
                record.randomNumber === "" ||
                record.status === 1 ||
                record.status === 2 ||
                !record.attribute.info.random ? null : (
                    <Form.Item
                        style={{marginBottom: "22px"}}
                        label={formatMessage({
                            id: "projects.randomization.randomNumber",
                        })}
                    >
                        <Row>{record.randomNumber}</Row>
                    </Form.Item>
                )}

                {type === 1 && !isReissue ?
                    <>
                        <Form.Item
                            style={{marginBottom: "22px"}}
                            name="visit"
                            label={formatMessage({id: "visit.cycle.name"})}
                        >
                            <Row>{visits}</Row>
                        </Form.Item>

                        {
                            doseForm !== undefined &&
                            <Dose
                                subjectId={record.id}
                                visitId={visitIdState}
                                field={doseForm}
                                form={form}
                                drugLabel={subjectCtx.drugOption}
                                openSetting={subjectCtx.drugNameOption}
                                drugLabelFrom={drugLabelFrom}
                                setDrugLabelFrom={setDrugLabelFrom}
                                drugNameFrom={drugNameFrom}
                                setDrugNameFrom={setDrugNameFrom}
                                setDrugOption={setDrugOption}
                                setDrugNameOption={setDrugNameOption}
                                getFormula={get_formula}
                                record={record}
                                visitSign={visitSign}
                            />
                        }

                        {
                            // !getFormulaLoading && (doseForm === undefined || ctx.isSelectDose) &&
                            (subjectCtx.formulaData?.height ||
                            subjectCtx.formulaData?.weight ||
                            subjectCtx.formulaData?.age) &&
                        <Form.Item label={formatMessage({id:"subject.dispensing.formula.dispensing"})}>
                            <FormulaCalculation
                                form={form}
                                record={record}
                                visit_id={visitIdState}
                            />
                        </Form.Item>
                        }

                        {drugOption && drugOption.length > 0 && (
                            <DrugLabel
                                drugNameOption={subjectCtx.drugNameOption}
                                drugOption={subjectCtx.drugOption}
                                form={form}
                                drugLabelFrom={drugLabelFrom}
                                setDrugLabelFrom={setDrugLabelFrom}
                                record={record}
                                visit_id={visitIdState}
                                drugNameFrom={drugNameFrom}
                                setDrugOption={setDrugOption}
                            />
                        )}

                        {
                            drugNameOption && drugNameOption.length > 0 &&
                            <DrugOpen form={form} drugNameOption={subjectCtx.drugNameOption} drugOption={subjectCtx.drugOption} drugFormFrom={drugLabelFrom}
                                        record={record} visit_id={visitIdState}  drugNameFrom={drugNameFrom} setDrugNameFrom={setDrugNameFrom}
                            />
                        }
                        {
                            record?.attribute?.info?.dtpRule === 2 && dtp && <ModeLogistics dtpType={dtpType} otherLogistics={otherLogistics}
                                                  setOtherLogistics={setOtherLogistics} type={sendType}
                                                  setType={setSendType} form={form}/>
                        }
                        {
                            !dispensingId ?
                                <Form.Item style={{marginBottom: "16px"}} rules={[{required: true}]} name="reason"
                                           label={

                                               visitOpen != ""?
                                                   visitOpen + formatMessage({id: "common.reason"})
                                                   :
                                                   formatMessage({id: 'subject.dispensing.visitSignDispensingReason'})}>
                                    <TextArea placeholder={formatMessage({id: 'common.required.prefix'})} allowClear
                                              className="full-width" autoSize={{minRows: 2, maxRows: 6}}/>
                                </Form.Item>
                                :
                                null
                        }
                    </>
                    :
                    <>
                        <Form.Item name="visit" label={formatMessage({id: 'visit.cycle.name'})}>
                            <Row
                                className="mar-lft-10">{visitName}</Row>
                        </Form.Item>

                        {drugNameOption && drugNameOption.length > 0 && (
                            <DrugOpen
                                form={form}
                                drugNameOption={drugNameOption}
                                drugFormFrom={drugLabelFrom}
                                type={2}
                                drugOption={drugOption}
                                record={record} visit_id={visitIdState}  drugNameFrom={drugNameFrom} setDrugNameFrom={setDrugNameFrom}
                            />
                        )}

                        {
                            record?.attribute?.info?.dtpRule === 2 && dtp && <ModeLogistics dtpType={dtpType} otherLogistics={otherLogistics}
                                                  setOtherLogistics={setOtherLogistics} type={sendType}
                                                  setType={setSendType} form={form}/>
                        }
                        <Form.Item name="reason"
                                   label={formatMessage({id: 'subject.dispensing.reissue.reason'})
                        } rules={[{required: true}]}>
                            <TextArea placeholder={formatMessage({id: 'common.required.prefix'})} allowClear
                                      className="full-width" autoSize={{minRows: 2, maxRows: 6}} maxLength={500}/>
                        </Form.Item>
                    </>
                }
                <Form.Item style={{marginBottom: "0px"}} name="remark"
                           label={formatMessage({id: 'subject.unblinding.remark'})}>
                    <TextArea placeholder={formatMessage({id: 'common.required.prefix'})} allowClear
                              className="full-width" autoSize={{minRows: 2, maxRows: 6}} maxLength={500}/>
                </Form.Item>
                {
                    outSize &&
                    <Row style={{marginLeft:subjectCtx.labelWidth, marginTop:12,
                        borderRadius: '2px 2px 2px 2px',
                        backgroundColor:"#fff7e6",
                        padding: '8px',
                        height: "auto",
                        alignItems: 'center',
                        opacity: "0.1px",
                        color:"#1D2129"
                    }}>
                        {
                            <Col>
                                <svg className="iconfont" width={16} height={16} style={{marginBottom:"-4px"}}>
                                    <use xlinkHref="#icon-jinggao"></use>
                                </svg>
                                <span style={{marginLeft:8,fontWeight: 600 }}>{formatMessage({id:"subject.dispensing.outsize.reason"})}</span>
                            </Col>
                        }
                    </Row>
                }
            </CustomForm>

            <DispensingConfirm
                bind={dispensing_confirm_pt}
                save={type === 1 ? save_confirm : reissue_confirm}
            />
        </Modal>
    );
};



// 设置研究产品标签选值
const CustomForm = styled(Form)`
  .ant-form-item-label label {
    text-align: right;
  }
`;