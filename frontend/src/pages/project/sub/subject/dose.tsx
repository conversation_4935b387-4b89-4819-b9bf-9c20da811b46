import {Form, message, Radio, Row, Select, Space} from "antd"
import {useEffect} from "react";
import {useSubject} from "./context";
import {useFetch} from "../../../../hooks/request";
import {DoseInfo} from "../../../../api/dispensing";
import {useSafeState} from "ahooks";
import {updateInitForm, updateOption} from "./get_formula_medicine";
import {useTranslation} from "../../../common/multilingual/component";
import {useAtom} from "jotai/index";
import {doseSelectAtom} from "./ctx";


interface DoseInterface {
    subjectId: any
    visitId: any
    field: any
    form: any
    drugLabel: any
    openSetting: any
    drugLabelFrom:any
    setDrugLabelFrom:any
    drugNameFrom:any
    setDrugNameFrom:any
    setDrugOption:any
    setDrugNameOption:any
    getFormula:any
    record:any
    visitSign:any
}

export const Dose = (props: DoseInterface) => {
    const {record,visitSign, subjectId, visitId, field, form,drugLabel, openSetting, drugLabelFrom, setDrugLabelFrom, drugNameFrom, setDrugNameFrom, setDrugOption, setDrugNameOption, getFormula} = props
    const intl = useTranslation();
    const {formatMessage} = intl;
    const subjectCtx = useSubject();

    const [levelTip, setLevelTip] = useSafeState<any>(undefined);

    const afterDoseInfoResError: any = (e: any, params: any) => {
        const contentType = e.headers.get("content-type");
        if (contentType && contentType.indexOf("application/json") > -1) {
            e.json().then((result: any) => {
                form.setFieldsValue({...form.getFieldsValue, "drugLabel": [{labels:[{}]},], "openSetting": [{labels:[{}]},]})
                setLevelTip(undefined)
                message.error(result.msg)
            });
        }
    };
    const {runAsync: DoseInfoRun, loading: DoseInfoLoading} = useFetch(
        DoseInfo,
        {manual: true, onError: afterDoseInfoResError}
    );

    const selectDose = (v: any) => {
        let value: any = v
        subjectCtx.setIsSelectDose(true)
        getFormula(record, visitId, visitId, visitSign, value)
        DoseInfoRun({subjectId, visitId, value,}).then(
            (res: any) => {
                setLevelTip(res.data.levelTip)
                let labels :any = []
                let labelsMap :any = {}
                res.data.label?.forEach(
                   (item:any, index:number) => {
                        subjectCtx.drugOption?.forEach(
                            (label:any, childIndex:any)=> {
                                if (item.id === label.id && item.label === label.name){
                                    let info :any = {label:childIndex,count: item.value?item.value:null, level : res.data.levelTip?.current}
                                    if (label.dtp?.length === 1 ) {
                                        info["dtp"] = label.dtp[0]
                                    }
                                    if (labelsMap[item.id]) {
                                        labelsMap[item.id].push(info)
                                    } else {
                                        labelsMap[item.id] = [{...info}]
                                    }

                                    // labelsMap[item.id] = index
                                    // subjectCtx.setDrugSelect({[0+""+index]:label})
                                    // labels.push({label:childIndex, level : res.data.levelTip?.current})
                                }
                            }
                        )
                    }
                )
                Object.keys(labelsMap)?.forEach(
                    (key: any) => {
                        labels.push({labels:labelsMap[key]})
                    }
                )
                if (labels.length > 0 ){
                    setDrugOption(subjectCtx.drugOption)
                    form.setFieldsValue({...form.getFieldsValue, "drugLabel": labels})
                }else{
                    setDrugOption([])
                    form.setFieldsValue({...form.getFieldsValue, "drugLabel": [{labels:[{}]},]})
                }
                let names :any = []
                let namesMap :any = {}
                res.data.name?.forEach(
                    (item:any, index:number) => {
                        subjectCtx.drugNameOption?.forEach(
                            (label:any, childIndex:number)=> {
                                if (item.id === label.id && item.label === label.name){
                                    let info :any = {name:childIndex, count: item.value?item.value:null, level : res.data.levelTip?.current}
                                    if (label.dtp?.length === 1 ) {
                                        info["dtp"] = label.dtp[0]
                                    }
                                    if (namesMap[item.id]) {
                                        namesMap[item.id].push(info)
                                    } else {
                                        namesMap[item.id] = [{...info}]
                                    }
                                }
                            }
                        )
                    }
                )
                Object.keys(namesMap)?.forEach(
                    (key: any) => {
                        names.push({labels:namesMap[key]})
                    }
                )
                if (names.length > 0 ){
                    setDrugNameOption(subjectCtx.drugNameOption)

                    form.setFieldsValue({...form.getFieldsValue, "openSetting": names})
                }else{
                    setDrugNameOption([])
                    form.setFieldsValue({...form.getFieldsValue, "openSetting": [{labels:[{}]},]})

                }
                updateInitForm(subjectCtx, form, drugLabelFrom,setDrugLabelFrom, drugNameFrom, setDrugNameFrom, setDrugOption, setDrugNameOption)
            }
        )
    }
    useEffect(() => {
        if (subjectCtx.fieldSelect && (drugLabel.length !== 0 || openSetting.length !== 0)){
            selectDose(subjectCtx.fieldSelect)
        }
        },[subjectCtx.fieldSelect, drugLabel, openSetting]
    )
    const [, setDose ] = useAtom(doseSelectAtom);
    return <>


            {
                field.type === "radio" &&
                <Form.Item
                    rules={[{required: field?.required}]}
                    label={field.label}
                    name={field.variable }
                >
                <Radio.Group
                    onChange={(v: any) => selectDose(v.target.value)}
                    value={subjectCtx.fieldSelect}
                    disabled={subjectCtx.inheritValue}
                >
                    <Space direction="vertical">
                        {
                            field.options?.map((item: any) =>
                                <div
                                    onDoubleClick={() => {
                                        subjectCtx.setIsSelectDose(false)
                                        setDrugOption(subjectCtx.drugOption)
                                        setDrugNameOption(subjectCtx.drugNameOption)
                                        setDose(true)
                                        form.setFieldsValue({...form.getFieldsValue,[field.variable]:null, "openSetting": [{labels:[{}]}],"drugLabel": [{labels:[{}]},]})
                                        updateInitForm(subjectCtx, form, drugLabelFrom,setDrugLabelFrom, drugNameFrom, setDrugNameFrom, setDrugOption, setDrugNameOption)
                                        getFormula(record, visitId, visitId, visitSign, null)
                                    }}
                                >
                                    <Radio onClick={() => {
                                        setDose(false)
                                    }} value={item.value} disabled={subjectCtx.inheritValue || item.disable}>{item.label}</Radio>
                                </div>
                            )
                        }
                    </Space>

                </Radio.Group>
                </Form.Item>
            }
            {
                field.type === "select" &&
                <Form.Item
                    rules={[{required: field?.required}]}
                    label={field.label}
                    name={field.variable}
                >
                <Select
                    placeholder={formatMessage({id:"placeholder.select.common"})}
                    onChange={(v: any) => selectDose(v)}
                    className="full-width"
                    disabled={subjectCtx.inheritValue}
                >
                    {
                        field.options?.map((item:any) =>
                            <Select.Option key={item.value} value={item.value} disabled={item.disable}>
                                {item.label}
                            </Select.Option>
                        )
                    }
                </Select>
                </Form.Item>
            }
        {
            levelTip &&
            levelTip?.last !== "" &&
            <Row style={{marginLeft: subjectCtx.labelWidth,marginBottom: 16, background: "#165DFF1A", height: 32}}>
                <svg className="iconfont" width={16} height={16}
                     style={{marginLeft: 8, marginTop: 8, marginRight: 4, color: "#165DFF"}}>
                    <use xlinkHref="#icon-xinxitishi1"></use>
                </svg>
                <div style={{marginTop: 4}}>
                    {formatMessage({id:"subject.dispensing.dose.tip",},{...levelTip})}

                </div>
            </Row>
        }


    </>
}