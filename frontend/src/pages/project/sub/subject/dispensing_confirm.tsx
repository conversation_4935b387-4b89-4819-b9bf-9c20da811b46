import React from 'react';
import {<PERSON><PERSON>, Col, Form, Modal, Row, Table} from "antd";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {useAuth} from "../../../../context/auth";
import {useSubject} from "./context";
import styled from "@emotion/styled";
import {InfoCircleFilled} from "@ant-design/icons";
import {useSafeState, useSize} from "ahooks";
import {useGlobal} from "../../../../context/global";
// import {logistics} from "../../../data/data";
import {Title} from "../../../../components/title";
import {useFetch} from "../../../../hooks/request";
import {getCompanyCode, getLogisticsCompanyCode} from "../../../../api/order";
import {inRandomIsolation} from "../../../../utils/in_random_isolation";
import {DispensingConfirmCable} from "./dispensing_confirm_table";
import {useAtom} from "jotai";
import {visitOpenAtom} from "./ctx";


export const DispensingConfirm = (props:any) => {

    const auth = useAuth();
    const ctx = useSubject();
    const intl = useTranslation();
    const {formatMessage} = intl;
    const projectType = auth.project.info.type;

    const [subjectRecord, setSubjectRecord] = useSafeState<any>({});
    const [visit, setVisit] = useSafeState<any>(null);
    const [labelName, setLabelName] = useSafeState<any>(null);
    const [drugName, setDrugName] = useSafeState<any>(null);
    const [title, setTitle] = useSafeState<any>(false);
    const [visible, setVisible] = useSafeState<any>(false);
    const [type, setType] = useSafeState<any>(false);
    const [remark, setRemark] = useSafeState<any>(false);
    const [sendType, setSendType] = useSafeState<number>(-1);
    const [sendTypeStr, setSendTypeStr] = useSafeState<any>("");
    const [logisticsNumber, setLogisticsNumber] = useSafeState<any>("");
    const [number, setNumber] = useSafeState<any>("");
    const [value, setValue] = useSafeState<any>({});
    const [dispensingId, setDispensingId] = useSafeState<any>(null);

    const [logistics, setLogistics] = useSafeState<any[]>([]);

    const show = (title:any, subjectRecord:any, visitName:any, replaceNumber:any, type : any, value: any, dispensingId :any) => {
        setValue(value)
        setTitle(title)
        setLabelName(replaceNumber)
        setSubjectRecord(subjectRecord)
        setVisit(visitName)
        setType(type)
        setRemark(value.remark)
        setDispensingId(dispensingId)
        // setSendType(0)
        if (value.send_type || value.send_type === 0) {
            switch (value.send_type) {
                case 0:
                    setSendType(0)
                    setSendTypeStr(formatMessage({id:"logistics.send.site"}))
                    break;
                case 1:
                    setSendType(1)
                    setSendTypeStr(formatMessage({id:"logistics.send.site.subject"}))
                    break;
                case 2:
                    setSendType(2)
                    setSendTypeStr(formatMessage({id:"logistics.send.depot.subject"}))
                    break;
            }
            setLogisticsNumber(value.logistics)
            setNumber(value.number)

        }
        ctx.setDispensingConfirmVisible(true)
        setVisible(true)

        run_getCompanyCode({ code: value?.logistics }).then(
            (res: any) => {
                setLogistics(
                    res.data?.map((item: any) => ({
                        label: item.name,
                        value: item.code,
                    })) || []
                );
            }
        );

    }
    const hide = () => {
        setValue({})
        setVisible(false)
        setType(0)
        setTitle(null)
        setSendType(-1)
        setLogisticsNumber("")
        setLabelName(null)
        setSubjectRecord({})
        setDispensingId(null)
        ctx.setDispensingConfirmVisible(false)
    }

    React.useImperativeHandle(props.bind, () => ({show}));
    const save = () => {
        props.save()
        hide()
    }

    const handleSearch = (newValue: string) => {
        run_getLogisticsCompanyCode({ name: newValue }).then(
            (res: any) => {
                setLogistics(
                    res.data?.map((item: any) => ({
                        label: item.name,
                        value: item.code,
                    })) || []
                );
                // if(res.data.length === 1 && res.data[0].code === "qita"){
                //     setOtherLogistics(true)
                // }else{
                //     setOtherLogistics(false)
                // }
            }
        );
    };
    const [visitOpen, ] = useAtom(visitOpenAtom);

    const handleChange = (newValue: string) => {
        // if (newValue === "qita"){
        //     setOtherLogistics(true)
        // }else{
        //     setOtherLogistics(false)
        // }
        setValue(newValue);
    };
    const {runAsync: run_getLogisticsCompanyCode} = useFetch(getLogisticsCompanyCode, {manual: true})
    const {runAsync: run_getCompanyCode} = useFetch(getCompanyCode, {manual: true})
    const g = useGlobal()
    const subjectWidthRef :any = React.useRef();
    const size :any= useSize(subjectWidthRef)
    const formItemLayout = () => {
        let width = g.lang === "en"? 250: 110
        width = size?.width+ 10 < width ? width : size?.width+ 10
        return {
            labelCol: { style: {   width: width} },
        };
    }
    return (
        <Modal
            className="custom-small-modal"
            title={title}
            visible={ctx.dispensingConfirmVisible && visible}
            onCancel={hide}
            centered
            forceRender={true}
            destroyOnClose={true}
            footer={
                <>
                    <Col>
                        <Button onClick={hide}>{formatMessage({ id: 'common.cancel' })}</Button>
                        <Button loading={false} onClick={save} type={"primary"}>{formatMessage({ id: 'subject.confirm.dispensing.button' })}</Button>
                    </Col>
                </>
            }
        >
            <TitleContent style={{height:g.lang === "en"? "60px": "" , marginBottom:16}} >
                <Col style={{fontSize:"14px", fontFamily: 'PingFang SC', marginTop:"6px", marginLeft:"6px", fontWeight: "400"}}>
                <InfoCircleFilled style={{color:"#FFAE00"}}/> {
                        type === 3?
                            formatMessage({ id: 'subject.dispensing.confirm.replace' })
                            :
                            formatMessage({ id: 'subject.dispensing.confirm' })
                    }</Col>
            </TitleContent>
            <Form labelWrap {...formItemLayout()} className="mar-lft-10">
                <Title name={formatMessage({id: 'subject.dispensing.info'})}/>
                <Col>
                        <Form.Item label={<span ref={subjectWidthRef}>{subjectRecord.attribute?((subjectRecord.attribute.info.subjectReplaceText === "" && subjectRecord.attribute.info.subjectReplaceTextEn === "")?(formatMessage({ id: "subject.number" })):((subjectRecord.attribute.info.subjectReplaceText !== "" && subjectRecord.attribute.info.subjectReplaceTextEn === "")?subjectRecord.attribute.info.subjectReplaceText:((subjectRecord.attribute.info.subjectReplaceText === "" && subjectRecord.attribute.info.subjectReplaceTextEn !== "")?(g.lang === "en"?subjectRecord.attribute.info.subjectReplaceTextEn:formatMessage({ id: "subject.number" })):(g.lang === "en"?subjectRecord.attribute.info.subjectReplaceTextEn:subjectRecord.attribute.info.subjectReplaceText)))):formatMessage({id:"subject.number"}) }</span>} style={{ marginBottom: "12px" }}>
                            {
                                projectType === 3 && !inRandomIsolation(auth.project.info.number)?
                                    <>
                                        {
                                            subjectRecord.info != null && subjectRecord.info.length > 0 ?
                                                subjectRecord.info[0].value
                                                :
                                                null
                                        }
                                    </>
                                    :
                                    <>
                                        {subjectRecord.shortname}
                                    </>
                            }

                        </Form.Item>
                    {
                            !subjectRecord.randomNumber || subjectRecord.randomNumber ===  "" || subjectRecord.status === 1 || subjectRecord.status === 2 || !subjectRecord.attribute.info.random?
                            null
                            :
                            <Form.Item label={formatMessage({ id: 'projects.randomization.randomNumber' })} style={{ marginBottom: "12px" }}>
                                {subjectRecord.randomNumber}
                            </Form.Item>
                    }

                    </Col>

                    <Form.Item label={formatMessage({ id: 'visit.cycle.name' })} style={{ marginBottom: "12px" }}>
                        {visit}
                    </Form.Item>
                {
                    type === 3?
                        <Form.Item label={formatMessage({ id: 'subject.dispensing.replace.info' })} style={{ marginBottom: "12px" }}>
                            {/* {labelName} */}
                            {labelName.split(',').map((item: any, index: any) => (
                                <div key={index}>{item}</div>
                            ))}
                        </Form.Item>
                        :
                        null
                }
                {
                    sendType > -1 && <>
                        <Form.Item label={formatMessage({ id: 'logistics.dispensing.method' })} style={{ marginBottom: "12px" }}>
                            {sendTypeStr}
                        </Form.Item>
                        {
                            sendType !== 0 &&
                            <Form.Item label={formatMessage({ id: 'logistics.info' })} style={{ marginBottom: "12px" }}>
                                {logisticsNumber && logisticsNumber !== 0? logistics.find((it:any) => it.value ===logisticsNumber)?.label : "-"}{number && number!== "" && "/"+number}
                                {/* <Select
                                    showSearch
                                    value={value}
                                    placeholder={props.placeholder}
                                    style={props.style}
                                    defaultActiveFirstOption={false}
                                    showArrow={false}
                                    filterOption={false}
                                    onSearch={handleSearch}
                                    onChange={handleChange}
                                    notFoundContent={null}
                                    // options={(logistics || []).map((d) => ({
                                    //     value: d.value,
                                    //     label: d.code,
                                    // }))}
                                >
                                    {(logistics || []).map(v => <Select.Option key={v.value} value={v.value}>{v.label}</Select.Option>)}
                                </Select> */}
                                {/* {logisticsNumber && logisticsNumber !== 0? logistics.find((it:any) => it.value ===logisticsNumber)?.label : "-"}{number && number!== "" && "/"+number} */}
                            </Form.Item>
                        }
                    </>
                }
                {
                    (type === 3 || type == 2 || (type == 1 && dispensingId === null)) &&
                        <Form.Item
                            label={
                                type === 3 && formatMessage({id: 'subject.dispensing.replace.reason'})
                                ||
                                type == 2 && formatMessage({id: 'subject.dispensing.reissue.reason'})
                                ||
                                ((type == 1 && dispensingId === null) &&
                                visitOpen !== ""?
                                    visitOpen +  formatMessage({id: 'common.reason'})
                                    :
                                    formatMessage({id: 'subject.dispensing.visitSignDispensingReason'})
                                )
                                }
                        >
                            {value.reason}
                        </Form.Item>


                }
                <Form.Item
                    label={formatMessage({id:'common.remark'})}
                    style={{marginBottom: "0px"}}>
                    {remark ? remark : "-"}
                </Form.Item>
                {
                    value.openSetting && value.openSetting.length > 0 &&
                        <DispensingConfirmCable data={value.openSetting} types={type}/>
                }
            </Form>

        </Modal>

    )

}

const StyleFrom = styled(Form)`
  .ant-form-item-label {
    line-height: 16px;
    //label {            
    //  white-space: pre-line; // 控制换行
    //}
  }`

const TitleContent = styled.div`
      height: 32px;
      width: 552px;
      left: 444px;
      top: 322px;
      border-radius: 2px;
      background: rgba(255, 174, 0, 0.06);
      border: 0.5px solid rgba(255, 174, 0, 0.5);
      border-radius: 2px;

`