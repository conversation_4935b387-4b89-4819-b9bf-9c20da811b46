import {
  <PERSON><PERSON>,
  Col,
  List,
  message,
  Row,
  Tooltip,
  <PERSON>po<PERSON>,
  Badge,
} from "antd";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import React, { useEffect, useState } from "react";
import { useFetch } from "../../../../hooks/request";
import { all } from "../../../../api/project_dynamics";
import { useAuth } from "../../../../context/auth";
import { useNavigate } from "react-router-dom";
import { permissions } from "../../../../tools/permission";
import { Title } from "../../../../components/title";
import { useGlobal } from "../../../../context/global";
import { useSafeState } from "ahooks";
import { useVisit } from "./visit_context";
import { NoticeVisit } from "./notice_visit";
import styled from "@emotion/styled";
import {AuthButton} from "../../../common/auth-wrap";

const { Text } = Typography;

export const ProjectSiteVisitMatter = () => {
  const context = useVisit();
  const g = useGlobal();
  const intl = useTranslation();
  const { formatMessage } = intl;
  const auth = useAuth();
  const [listData, setListData] = useSafeState<any>([]);
  const [dateString, setDateString] = useSafeState<any>(null);
  const { runAsync: run_all, loading: allLoading } = useFetch(all, {
    manual: true,
  });
  const notice_view: any = React.useRef();
  const navigate = useNavigate();
  const [current, setCurrent] = useState(1);

  const getListData = () => {
    var list = context.calendarList;
    if (list.length > 0) {
      list.sort((a: any, b: any) => a.status - b.status);
    }
    setListData(list);
  };

  const getDateString = () => {
    if (intl.locale === "zh") {
      const date = new Date(context.calendarListDate);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const formattedDate = `${year}年${month}月${day}日`;
      setDateString(formattedDate);
    } else if (intl.locale === "en") {
      const date = new Date(context.calendarListDate);
      const year = date.getFullYear();
      const month = date.getMonth();
      const day = String(date.getDate()).padStart(2, "0");
      const months = [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
      ];
      const formattedDate = `${months[month]} ${day}, ${year}`;
      setDateString(formattedDate);
    }
  };

  // 通知-查看
  const notice = (data: any) => {
    if (
      data.status != 3 &&
      data.status != 5 &&
      permissions(
        auth.project.permissions,
        "operation.project.subject.visit.cycle.send.notice"
      )
    ) {
      notice_view.current.show(data, 1);
    } else {
      notice_view.current.show(data, 2);
    }
  };

  const jump = (item: any) => {
    let split = item.content.split("<a>" + item.highlight + "</a>");
    let jumperFunc = () => {
      message.error(formatMessage({ id: "no.permission" }));
    };
    switch (item.typeTran) {
      case "project_dynamics_type_enter_site":
        if (
          permissions(
            auth.project.permissions,
            "operation.build.settings.user.view"
          )
        ) {
          jumperFunc = () => {
            navigate("/project/settings/user");
          };
        }
        break;
      case "project_dynamics_type_bind_storehouse":
        if (
          permissions(
            auth.project.permissions,
            "operation.build.settings.user.view"
          )
        ) {
          jumperFunc = () => {
            navigate("/project/settings/user");
          };
        }
        break;
      case "project_dynamics_type_role_assignment":
        if (
          permissions(
            auth.project.permissions,
            "operation.build.settings.user.view"
          )
        ) {
          jumperFunc = () => {
            navigate("/project/settings/user");
          };
        }
        break;
      case "project_dynamics_type_overtime":
        if (
          permissions(
            auth.project.permissions,
            "operation.supply.shipment.list"
          )
        ) {
          jumperFunc = () => {
            navigate("/project/supply/shipment");
          };
        }
        break;
      case "project_dynamics_type_overtime_recovery":
        if (
          permissions(
            auth.project.permissions,
            "operation.supply.recovery.list"
          )
        ) {
          jumperFunc = () => {
            navigate("/project/supply/drug-recovery");
          };
        }
        break;
      case "project_dynamics_type_emergency_unblinding":
        if (
          permissions(auth.project.permissions, "operation.subject.view-list")
        ) {
          jumperFunc = () => {
            navigate("/project/sub/subject");
          };
        }
        break;
      case "project_dynamics_type_alert_storehouse":
        if (
          permissions(
            auth.project.permissions,
            "operation.supply.storehouse.medicine.summary"
          )
        ) {
          jumperFunc = () => {
            navigate("/project/supply/storehouse");
          };
        }
        break;
    }
    let btn = (
      <Button
        style={{ border: "0px", padding: "0px" }}
        type={"link"}
        onClick={jumperFunc}
      >
        {item.highlight}
      </Button>
    );
    if (item.typeTran === "project_dynamics_type_forecast") {
      btn = (
        <Tooltip
          title={
            <div>
              {item?.tooltip.map((value: any) => (
                <Row>
                  【{g.lang === "en" ? value.name_en : value.name}
                  】当前库存最晚可用时间：{value.date}
                </Row>
              ))}
            </div>
          }
        >
          <Button style={{ border: "0px", padding: "0px" }} type={"link"}>
            {item.highlight}
          </Button>
        </Tooltip>
      );
    }
    return (
      <p style={{ marginBottom: "0px" }}>
        <Typography.Text>{split[0]}</Typography.Text>
        {btn}
        <Typography.Text>{split[1]}</Typography.Text>
      </p>
    );
  };

  useEffect(() => {
    getListData();
    // run_all({
    //     envId: envId,
    //     cohortId: cohortId,
    //     // start: page.currentPage,
    //     // limit: 10
    // }).then((result: any) => {
    //     // page.setTotal(result.data.total)
    //     // page.setData(result.data.data)
    // })
  }, [current, getListData]);

  useEffect(() => {
    getDateString();
    setCurrent(1);
  }, [context.calendarListDate, g.lang]);

  return (
    <div
      style={{
        borderLeft: "1px solid #E0E1E2",
        height: "100%",
        padding: "16px 0 16px 16px",
      }}
    >
      <Title name={<FormattedMessage id={"subject.visit.cycle.item"} />} />
      <Row style={{ paddingTop: "16px" }} align="middle">
        <svg className="iconfont" width={18} height={12}>
          <use xlinkHref="#icon-fangshishixiangicon"></use>
        </svg>
        <span style={{ marginLeft: "8px" }}>{dateString}</span>
      </Row>
      <MyList
        loading={allLoading}
        dataSource={listData}
        style={{ maxHeight: 820, paddingRight: 16 }}
        pagination={
          listData.length > 5
            ? {
                size: "small",
                total: listData.length,
                pageSize: 5,
                current,
                onChange: setCurrent,
              }
            : undefined
        }
        renderItem={(item: any) => (
          <List.Item style={{ display: "block", padding: "12px 0" }}>
            <Row justify="space-between">
              <Row>
                <Col>
                  <span
                    style={{
                      color: "#1D2129",
                      fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                      fontSize: "14px",
                      fontStyle: "normal",
                      fontWeight: 500,
                      lineHeight: "normal",
                    }}
                  >
                    {item.subjectNumber}
                  </span>
                  {/* {jump(item)}
                                    <Typography.Text
                                        type="secondary"
                                        style={{ color: "#ADB2BA" }}
                                    >{moment.unix(item.time).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss')}</Typography.Text> */}
                </Col>
                <Col>
                  <span
                    style={{
                      color: "#ADB2BA",
                      fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                      fontSize: "14px",
                      fontStyle: "normal",
                      fontWeight: 200,
                      lineHeight: "normal",
                      marginLeft: "8px",
                    }}
                  >
                    |
                  </span>
                </Col>
                <Col
                  style={{
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                    width:
                      intl.locale === "zh"
                        ? "120px"
                        : item.hyperwindow === "subject.visit.item.on.schedule"
                        ? "70px"
                        : "90px",
                  }}
                >
                  <Tooltip
                    trigger={["hover", "click"]}
                    // overlayInnerStyle={{ fontSize: 12, background: "#646566" }}
                    overlayStyle={{
                      whiteSpace: "pre-wrap",
                      maxWidth: "300px",
                      width: "auto",
                      fontSize: 12,
                    }}
                    placement="top"
                    title={<span>{item.visitName}</span>}
                  >
                    <span
                      style={{
                        color: "#1D2129",
                        fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                        fontSize: "14px",
                        fontStyle: "normal",
                        fontWeight: 500,
                        lineHeight: "normal",
                        marginLeft: "8px",
                      }}
                    >
                      {item.visitName}
                    </span>
                  </Tooltip>
                </Col>
              </Row>
              <Col
                style={{
                  textAlign: "center",
                  alignItems: "center",
                }}
              >
                <div
                  style={{
                    borderRadius: "2px",
                    // opacity: 0.1,
                    background: item.overdueBackgroundColor,
                    width:
                      intl.locale === "zh"
                        ? "36px"
                        : item.hyperwindow === "subject.visit.item.on.schedule"
                        ? "100px"
                        : "70px",
                    height: "24px",
                    flexShrink: 0,
                    // letterSpacing: "0px",
                  }}
                >
                  <span
                    style={{
                      color: item.hyperwindowColor,
                        fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                      fontSize: "12px",
                      fontStyle: "normal",
                      fontWeight: 400,
                      lineHeight: "normal",
                    }}
                  >
                    {item.hyperwindow !== "" ? <FormattedMessage id={item.hyperwindow} /> : ""}
                  </span>
                </div>
              </Col>
            </Row>
            <Row>
              <Text
                style={{ width: "100%", color: "#677283" }}
                ellipsis={{ tooltip: item.siteName }}
              >
                <FormattedMessage id={"subject.visit.cycle.item.site"} />
                {item.siteName}
              </Text>
            </Row>
            <Row>
              <Col>
                <span
                  style={{
                    color: "#677283",
                      fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                    fontSize: "14px",
                    fontStyle: "normal",
                    fontWeight: 400,
                    lineHeight: "normal",
                  }}
                >
                  <FormattedMessage id={"subject.visit.cycle.item.plan"} />
                  {item.plan}
                </span>
              </Col>
            </Row>
            <Row>
              <Col>
                <span
                  style={{
                    color: "#677283",
                      fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                    fontSize: "14px",
                    fontStyle: "normal",
                    fontWeight: 400,
                    lineHeight: "normal",
                  }}
                >
                  <FormattedMessage id={"subject.visit.cycle.item.actual"} />
                  {item.actual}
                </span>
              </Col>
            </Row>
            <Row
              style={{
                marginTop: "16px",
              }}
              justify="space-between"
              align="middle"
            >
              <Col>
                <span
                  style={{
                    color: "#1D2129",
                      fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                    fontSize: "12px",
                    fontStyle: "normal",
                    fontWeight: 400,
                    lineHeight: "normal",
                  }}
                >
                  <Badge
                    color={item.statusColor}
                    style={{ marginRight: "8px" }}
                  />
                    <FormattedMessage id={item.statusName} />
                </span>
              </Col>
              <Col>
                {permissions(
                  auth.project.permissions,
                  "operation.project.subject.visit.cycle.notice.view"
                ) && (
                  <MyButton size={"small"} onClick={() => notice(item)}>
                    {item.status === 3 || item.status === 4 || item.status === 5
                      ? formatMessage({
                          id: "common.view", allowComponent: true
                        })
                      : formatMessage({
                          id: "calendar.button.site.visit.matter.notice", allowComponent: true
                        })}
                  </MyButton>
                )}
              </Col>
            </Row>
          </List.Item>
        )}
      />
      <NoticeVisit bind={notice_view} />
    </div>
  );
};

const MyList = styled(List)`
  .ant-list-pagination {
    margin-top: 12px;

    .ant-pagination-item {
      border: none;

      &.ant-pagination-item-active {
        border: none;
        background: none;

        > a {
          color: #165dff;
        }
      }
    }
  }
`;

const MyButton = styled(AuthButton)`
  border-color: #165dff;

  span {
    color: #165dff;
  }
`;
