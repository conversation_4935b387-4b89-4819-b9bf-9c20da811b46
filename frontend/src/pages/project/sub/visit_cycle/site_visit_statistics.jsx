import { Col, Row, Select, Spin, Tooltip, Statistic } from "antd";
import React from "react";
import { useFetch } from "hooks/request";
import { useAuth } from "../../../../context/auth";
import { useSafeState } from "ahooks";
import { userSites } from "../../../../api/project_site";
import { useVisit } from "./visit_context";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import { Title } from "../../../../components/title";
import { subjectVisitSummary } from "../../../../api/subject_visit";

export const ProjectSiteVisitStatistics = () => {
  const context = useVisit();
  const intl = useTranslation();
  const { formatMessage } = intl;
  const auth = useAuth();
  const envId = auth.env ? auth.env.id : null;
  const projectId = auth.project.id;
  const customerId = auth.customerId;

  const [site, setSite] = useSafeState("");
  const [sites, setSites] = useSafeState([]);
  const [subjectVisitSummaryData, setSubjectVisitSummaryData] = useSafeState(
    {}
  );

  const { runAsync: run_userSites } = useFetch(userSites, { manual: true });
  const {
    runAsync: getSubjectVisitListRun,
    loading: getSubjectVisitListLoading,
  } = useFetch(subjectVisitSummary, { manual: true });

  React.useEffect(() => {
    run_userSites({
      projectId: projectId,
      customerId: customerId,
      envId: envId,
      roleId: auth.project.permissions.role_id,
    }).then((result) => {
      const response = result.data;
      let list = [];
      list.push({
        id: "",
        name: <FormattedMessage id={"common.all"} />,
        number: "",
      });
      if (response) {
        response.forEach((item) => {
          list.push({ id: item.id, name: item.name, number: item.number });
        });
      }
      if ((site === undefined || site === null) && list[0] != null) {
        setSite("");
        context.setSiteId("");
        // getListData("");
      }
      setSites(list);
    });
    // if (site !== undefined && site !== null) {
    //   getListData(site);
    // }
    setSubjectVisitSummaryData(context.subjectVisitSummaryData);
  }, [site]);

  // const getListData = (siteId) => {
  //   const currentDate = new Date();
  //   const currentYear = currentDate.getFullYear();
  //   const currentMonth = currentDate.getMonth() + 1; // 月份从0开始，所以需要加1
  //   const currentDay = currentDate.getDate();
  //   var month = currentMonth + "";
  //   if (currentMonth < 10) {
  //     month = "0" + currentMonth;
  //   }
  //   getSubjectVisitListRun({
  //     envId: envId,
  //     siteId: siteId,
  //     roleId: auth.project.permissions.role_id,
  //     types: `${currentYear}-${month}`,
  //   }).then((result) => {
  //     let data = result.data.subjectVisitSummary;
  //     setSubjectVisitSummaryData(data);
  //   });
  // };

  return (
    <>
      <Spin spinning={getSubjectVisitListLoading}>
        <Row
          style={{ padding: "12px 16px", borderBottom: "1px solid #E0E1E2" }}
        >
          <Col>
            <span><FormattedMessage id={"common.site"} />：</span>
            <Select
              style={{ width: "242px" }}
              dropdownMatchSelectWidth={false}
              dropdownStyle={{ maxWidth: 500 }}
              onChange={(value) => {
                setSite(value);
                context.setSiteId(value);
              }}
              value={site}
              showSearch
              allowClear
              // 如果发现下拉菜单跟随页面滚动，或者需要在其他弹层中触发 Select，请尝试使用 getPopupContainer={triggerNode => triggerNode.parentElement} 将下拉弹层渲染节点固定在触发器的父元素中。
              getPopupContainer={(triggerNode) => triggerNode.parentElement}
              filterOption={(input, option) => {
                  const childrenText = option.props.children;
                  if (typeof childrenText === 'string') {
                      return childrenText.trim().toLowerCase().indexOf(input.trim().toLowerCase()) >= 0;
                  }
                  return false;
              }}
            >
              {sites.map((it) => (
                <Select.Option key={it.id} value={it.id}>
                  {it.number ? `${it.number}-${it.name}` : it.name}
                </Select.Option>
              ))}
            </Select>
          </Col>
        </Row>
        <div style={{ padding: "12px 16px" }}>
          <Row align="middle">
            <Title
              name={<FormattedMessage id={"projects.storehouse.statistics.summary"} />}
            />
            <Tooltip
              trigger={["hover", "click"]}
              // overlayInnerStyle={{fontSize: 12, background: "#646566"}}
              overlayStyle={{
                whiteSpace: "pre-wrap",
                maxWidth: "600px",
                width: "auto",
                fontSize: 12,
              }}
              placement="top"
              title={
                <div>
                  <Row>
                    <FormattedMessage id={"project.visit.cycle.calendar.summary.outsize.completed"} />
                  </Row>
                  <Row>
                    <FormattedMessage id={"project.visit.cycle.calendar.summary.outsize.undone"} />
                  </Row>
                  <Row>
                    <FormattedMessage id={"project.visit.cycle.calendar.summary.in.progress"} />
                  </Row>
                  <Row>
                    <FormattedMessage id={"project.visit.cycle.calendar.summary.completed.on.schedule"} />
                  </Row>
                  <Row>
                    <FormattedMessage id={"project.visit.cycle.calendar.summary.has.not.started"} />
                  </Row>
                </div>
              }
            >
              <svg
                className="iconfont mouse"
                width={12}
                height={12}
                style={{ marginLeft: 4 }}
              >
                <use xlinkHref="#icon-xinxitishi" />
              </svg>
            </Tooltip>
          </Row>
          <Row gutter={24}>
            <Col span={8}>
              <Statistic
                prefix={
                  <svg className="iconfont" width={32} height={32}>
                    <use xlinkHref="#icon-chaochuang"></use>
                  </svg>
                }
                formatter={() => (
                  <div style={{ marginLeft: 16 }}>
                    <span
                      style={{
                        color: "#333",
                        fontFamily: "DIN Alternate",
                        fontSize: "24px",
                        fontWeight: 600,
                      }}
                    >
                      {(subjectVisitSummaryData.outSizeNotCompleted ? subjectVisitSummaryData.outSizeNotCompleted : 0) +
                        (subjectVisitSummaryData.outSizeCompleted ? subjectVisitSummaryData.outSizeCompleted : 0)}
                    </span>
                    <div
                      style={{
                        color: "#666",
                          fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                        fontSize: "14px",
                        fontStyle: "normal",
                        fontWeight: 400,
                        lineHeight: "normal",
                      }}
                    >
                      <FormattedMessage id={"subject.visit.outsize"} />(
                      <span
                        style={{
                          color: "#999",
                            fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                          fontSize: "12px",
                          fontStyle: "normal",
                          fontWeight: 500,
                          lineHeight: "normal",
                          letterSpacing: "0.228px",
                        }}
                      >
                        <FormattedMessage id={"subject.visit.outsize.undone"} />
                      </span>
                      <span
                        style={{
                          color: "#F96964",
                            fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                          fontSize: "12px",
                          fontStyle: "normal",
                          fontWeight: 500,
                          lineHeight: "normal",
                          letterSpacing: "0.228px",
                        }}
                      >
                        {subjectVisitSummaryData.outSizeNotCompleted
                          ? subjectVisitSummaryData.outSizeNotCompleted
                          : 0}
                      </span>
                      <span style={{ padding: "0 4px" }}>/</span>
                      <span
                        style={{
                          color: "#999",
                            fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                          fontSize: "12px",
                          fontStyle: "normal",
                          fontWeight: 500,
                          lineHeight: "normal",
                          letterSpacing: "0.228px",
                        }}
                      >
                        <FormattedMessage id={"subject.visit.outsize.completed"} />
                      </span>
                      <span
                        style={{
                          color: "#333",
                          fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                          fontSize: "12px",
                          fontStyle: "normal",
                          fontWeight: 500,
                          lineHeight: "normal",
                          letterSpacing: "0.228px",
                        }}
                      >
                        {subjectVisitSummaryData.outSizeCompleted
                          ? subjectVisitSummaryData.outSizeCompleted
                          : 0}
                      </span>
                      )
                    </div>
                  </div>
                )}
              />
            </Col>
            <Col span={5}>
              <Statistic
                prefix={
                  <svg className="iconfont" width={32} height={32}>
                    <use xlinkHref="#icon-jinhangzhong1"></use>
                  </svg>
                }
                formatter={() => (
                  <div style={{ marginLeft: 16 }}>
                    <span
                      style={{
                        color: "#333",
                        fontFamily: "DIN Alternate",
                        fontSize: "24px",
                        fontWeight: 600,
                      }}
                    >
                      {subjectVisitSummaryData.inProgress
                        ? subjectVisitSummaryData.inProgress
                        : 0}
                    </span>
                    <div
                      style={{
                        color: "#666",
                        fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                        fontSize: "14px",
                      }}
                    >
                        <FormattedMessage id={"projects.status.progress"} />
                    </div>
                  </div>
                )}
              />
            </Col>
            <Col span={6}>
              <Statistic
                prefix={
                  <svg className="iconfont" width={32} height={32}>
                    <use xlinkHref="#icon-anqiyiwancheng"></use>
                  </svg>
                }
                formatter={() => (
                  <div style={{ marginLeft: 16 }}>
                    <span
                      style={{
                        color: "#333",
                        fontFamily: "DIN Alternate",
                        fontSize: "24px",
                        fontWeight: 600,
                      }}
                    >
                      {subjectVisitSummaryData.completedOnSchedule
                        ? subjectVisitSummaryData.completedOnSchedule
                        : 0}
                    </span>
                    <div
                      style={{
                        color: "#666",
                        fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                        fontSize: "14px",
                        fontWeight: 400,
                      }}
                    >
                        <FormattedMessage id={"subject.visit.completed.on.schedule"} />
                    </div>
                  </div>
                )}
              />
            </Col>
            <Col span={5}>
              <Statistic
                prefix={
                  <svg className="iconfont" width={32} height={32}>
                    <use xlinkHref="#icon-weikaishi1"></use>
                  </svg>
                }
                formatter={() => (
                  <div style={{ marginLeft: 16 }}>
                    <span
                      style={{
                        color: "#333",
                        fontFamily: "DIN Alternate",
                        fontSize: "24px",
                        fontWeight: 600,
                      }}
                    >
                      {subjectVisitSummaryData.prepare
                        ? subjectVisitSummaryData.prepare
                        : 0}
                    </span>
                    <div
                      style={{
                        color: "#666",
                        fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                        fontSize: "14px",
                      }}
                    >
                      <FormattedMessage id={"project.task.status.notStarted"} />
                    </div>
                  </div>
                )}
              />
            </Col>
          </Row>
        </div>
      </Spin>
    </>
  );
};
