import React, {ReactNode} from "react";
import {useSafeState} from "ahooks";

export const VisitCycleContext = React.createContext<
    {
        siteId: any;
        setSiteId: (siteId: any) => void;
        types: any;
        setTypes: (types: any) => void;
        calendarList: any;
        setCalendarList: (calendarList: any) => void;
        calendarListDate: any;
        setCalendarListDate: (calendarListDate: any) => void;
        noticeTemplate: any;
        setNoticeTemplate: (noticeTemplate: any) => void;
        noticeMode: any;
        setNoticeMode: (noticeMode: any) => void;
        noticeRole: any;
        setNoticeRole: (noticeRole: any) => void;
        noticeUsers: any;
        setNoticeUsers: (noticeUsers: any) => void;
        noticeUsersAll: any;
        setNoticeUsersAll: (noticeUsersAll: any) => void;
        phoneList: any;
        setPhoneList: (phoneList: any) => void;
        noticeVisible :any;
        setNoticeVisible :any;
        roleData: any;
        setRoleData: (roleData: any) => void;
        dispensingId: any;
        setDispensingId: (dispensingId: any) => void;
    }
    |
    null>(null);

export const VisitCycleProvider = ({children}: { children: ReactNode }) => {

    const [siteId, setSiteId] = useSafeState<any>([]);
    const [noticeVisible, setNoticeVisible] = useSafeState<any>(false);

    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // 月份从0开始，所以需要加1
    const currentDay = currentDate.getDate();
    var month = currentMonth + ""
    if(currentMonth < 10){
        month = "0" + currentMonth
    }
    var day = currentDay + ""
    if(currentDay < 10){
        day = "0" + currentDay
    }

    const [types, setTypes] = useSafeState<any>(`${currentYear}-${month}-${day}`);
    const [calendarList, setCalendarList] = useSafeState<any>([]);
    const [calendarListDate, setCalendarListDate] = useSafeState<any>(null);
    const [noticeTemplate, setNoticeTemplate] = useSafeState<any>("a");
    const [noticeMode, setNoticeMode] = useSafeState<any>(1);
    const [noticeRole, setNoticeRole] = useSafeState<any>([]);
    const [noticeUsers, setNoticeUsers] = useSafeState<any>(new Map());
    const [noticeUsersAll, setNoticeUsersAll] = useSafeState<any>(new Map());
    const [phoneList, setPhoneList] = useSafeState<any>([]);
    
    const [roleData, setRoleData] = useSafeState<any>([]);
    const [dispensingId, setDispensingId] = useSafeState<any>("");

    let componentRef: any = React.useRef();
    return (
        <VisitCycleContext.Provider
            value={
                {
                    siteId, 
                    setSiteId,
                    types,
                    setTypes,
                    calendarList,
                    setCalendarList,
                    calendarListDate,
                    setCalendarListDate,
                    noticeTemplate,
                    setNoticeTemplate,
                    noticeMode,
                    setNoticeMode,
                    noticeRole,
                    setNoticeRole,
                    noticeUsers,
                    setNoticeUsers,
                    noticeUsersAll,
                    setNoticeUsersAll,
                    phoneList,
                    setPhoneList,
                    noticeVisible, setNoticeVisible,
                    roleData,
                    setRoleData,
                    dispensingId,
                    setDispensingId,
                }
            }
        >
            {children}
        </VisitCycleContext.Provider>
    )
};

export const visitContext = () => {
    const context = React.useContext(VisitCycleContext);
    if (!context) {
        throw new Error("visitContext must be used in VisitCycleProvider");
    }
    return context;
};

