import Tags from "@yaireo/tagify/dist/react.tagify";
import "@yaireo/tagify/dist/tagify.css";
import {useTranslation} from "../../../common/multilingual/component";
import { usePhones } from "./hooks";

export const EditReceivers = () => {
    const intl = useTranslation();

    const ctx = usePhones();

    return (
        <Tags
            // style={{ width: "100%" }}
            className="custom-tagify"
            settings={{
                pattern: /^(?:(?:\+|00)86)?1[3-9]\d{9}$/,
                // pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                delimiters: ",|;| ",
            }}
            value={ctx.phoneList}
            onChange={(e: any) => ctx.setPhoneList(e.detail.tagify.getCleanValue().map((it: any) => it.value))}
            placeholder={intl.formatMessage({ id: "projects.notice.visit.notification.required.prefix" })}
        />
    );
};
