import {<PERSON><PERSON>, Card, Checkbox, Col, Form, Menu, Modal, Row, Select, Space, Tabs, Tree, Radio, RadioChangeEvent, Input, message, Table, Typography, } from "antd";
import {useVisit} from "./visit_context";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import React, {ReactNode, useEffect, useRef, useState} from "react";
import {permissions} from "../../../../tools/permission";
import {useAuth} from "../../../../context/auth";
import styled from "@emotion/styled";
import moment from "moment";
import { useSafeState } from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {MinusCircleFilled, PlusOutlined, SearchOutlined} from "@ant-design/icons";
import {GetRoleList, GetRoleUserList, pushNotice, noticeHistory} from "../../../../api/subject_visit";
import { template } from "lodash";
import {RoleUser} from "./role_user";
import {useForm} from "antd/es/form/Form.js";
import {useGlobal} from "../../../../context/global";

export const NoticeVisit = (props: any) => {
    const g = useGlobal();
    const auth = useAuth()
    const envId = auth.env ? auth.env.id : null;
    const projectId = auth.project? auth.project.id : null;
    const customerId = auth.customerId;
    const [noticeVisible, setNoticeVisible] = useSafeState(false);
    const [titleType, setTitleType] = useSafeState(1);
    const [noticeTabVisible, setNoticeTabVisible] = useSafeState(false);
    const intl = useTranslation();
    const { formatMessage } = intl;
    const context = useVisit();
    const [activeKey, setActiveKey] = useState<any>("1");
    const [dispensingId, setDispensingId] = useState<any>("");
    const [periodMin, setPeriodMin] = useState<any>("");
    const [historyData, setHistoryData] = useState<any>([]);
    const [form] = useForm()

    const { runAsync: getRoleListRun, loading: getRoleListLoading } = useFetch(GetRoleList, { manual: true });
    const {runAsync: postPushNoticeRun, loading: postPushNoticeLoading} = useFetch(pushNotice, {manual: true});
    const {runAsync: getNoticeHistoryRun, loading: getNoticeHistoryLoading } = useFetch(noticeHistory, { manual: true });


    const getListData = (value: any) => {
        getNoticeHistoryRun({
            envId:envId,
            dispensingId: value,
        }).then((result: any) => {
            setHistoryData(result.data);
        });
        context.setDispensingId(value);
        getRoleListRun({
            customerId: customerId,
            projectId:  projectId,
        }).then((result: any) => {
            let data = result.data;
            context.setRoleData(data);
        });
    };

    const onChange = (newActiveKey: string) => {
        setActiveKey(newActiveKey);
    };

    const save = () => {
        form.validateFields().then((value:any) => {
            // console.log("data==" + JSON.stringify(context.noticeUsers))
            // console.log("data==" + JSON.stringify(Object.fromEntries(context.noticeUsers.entries())))
            let rolesUser :any= []
            // Object.keys(context.noticeUsers).map((item:any)=>
            //     rolesUser.push({"roleId": item, "users":context.noticeUsers[item]})
            // )
            var userList: any[] = []
            context.noticeUsers.forEach((value: any, key: any) => {
                rolesUser.push({"roleId": key, "users": value})
                if(value !== null && value.length > 0){
                    for (let i = 0; i < value.length; i++) {
                        userList.push(value[i]);
                    }
                }
            });
            if(context.noticeMode === 1 && userList.length === 0){
                message.error(formatMessage({id: "projects.notice.visit.notification.required.prefix.recipient"}))
                return
            }
            if(context.noticeMode === 2 && userList.length === 0 && context.phoneList.length === 0){
                message.error(formatMessage({id: "projects.notice.visit.notification.required.prefix.recipient"}))
                return
            }
            const postValue = {
                dispensingId: dispensingId,
                userIds: rolesUser,
                phoneUser: context.phoneList,
                type: context.noticeMode,
                template: context.noticeTemplate,
                periodMin: periodMin,
            };
            postPushNoticeRun({...postValue}).then(
                (resp:any) => {
                    message.success(resp.msg)
                    context.setNoticeTemplate("a");
                    context.setNoticeMode(1);
                    context.setNoticeRole([]);
                    context.setNoticeUsers(new Map());
                    context.setPhoneList([]);
            
                    setNoticeVisible(false);
                }
            ).catch(
                () => {}
            )
        })
    }

    const hide = () => {
        context.setNoticeTemplate("a");
        context.setNoticeMode(1);
        context.setNoticeRole([]);
        context.setNoticeUsers(new Map());
        context.setPhoneList([]);
        setDispensingId("");
        setPeriodMin("");
        setHistoryData([]);
        setNoticeVisible(false);
        setTitleType(1);
    }

    const handleCancel = () => {
        context.setNoticeTemplate("a");
        context.setNoticeMode(1);
        context.setNoticeRole([]);
        context.setNoticeUsers(new Map());
        context.setPhoneList([]);
        // console.log("2==" + JSON.stringify(Object.fromEntries(context.noticeUsers.entries())))
        setDispensingId("");
        setPeriodMin("");
        setHistoryData([]);
        setNoticeVisible(false);
    };

    const show = (data: any, type: any) => {
        context.setNoticeTemplate("a");
        context.setNoticeMode(1);
        context.setNoticeRole([]);
        context.setNoticeUsers(new Map());
        context.setPhoneList([]);
        setNoticeVisible(true);
        setDispensingId(data.dispensingId)
        setPeriodMin(data.minPeriod);
        getListData(data.dispensingId);
        setTitleType(type);
        if(type === 1){
            setNoticeTabVisible(true)
        }else if(type === 2){
            setNoticeTabVisible(false)
        }
    };

    function renderContent(value: any) {
        return (
            value.template === "a"
                ?
                <Typography.Text>{value.day + formatMessage({id: "projects.notice.rules.template.a.content"})}</Typography.Text>
                :
                (value.template === "b"?(<Typography.Text>{formatMessage({id: "projects.notice.rules.template.b.content"})}</Typography.Text>):
                (value.template === "c"?(<Typography.Text>{formatMessage({id: "projects.notice.rules.template.c.content"})}</Typography.Text>):
                (<Typography.Text>{formatMessage({id: "projects.notice.rules.template.d.content"})}</Typography.Text>)))
        );
    }

    React.useImperativeHandle(props.bind, () => ({ show }));

    function formatTimezoneOffset(offset: any) {
        const negative = offset < 0;
        offset = Math.abs(offset);
        const hours = Math.floor(offset);
        const minutes = Math.round((offset - hours) * 60); // 四舍五入处理 .5

        const sign = negative ? "-" : "";
        const hh = String(hours).padStart(2, '0');
        const mm = String(minutes).padStart(2, '0');

        return `${sign}${hh}:${mm}`;
    }

    return <Modal
        // open={visitCtx.noticeVisible}
        title={titleType===2?formatMessage({id: 'common.view'}):formatMessage({id: 'calendar.button.site.visit.matter.notice'})}
        className={"custom-medium-modal"}
        open={noticeVisible}
        centered={true}
        closable={true}
        onCancel={handleCancel}
        maskClosable={false}
        footer={
            activeKey === "1" && titleType===1 ?
            <div>
                {
                    permissions(auth.project.permissions, "operation.project.subject.visit.cycle.send.notice") &&
                    <Row>
                        <Col span={24} style={{textAlign: 'right'}}>
                            <Space direction='horizontal'>
                                <Button onClick={() => { hide(); }} >
                                    {formatMessage({ id: "common.cancel" })}
                                </Button>
                                {
                                    (permissions(auth.project.permissions, "operation.supply.shipment.confirm") ||  permissions(auth.project.permissions, "operation.supply.drug.order.confirm")) &&

                                    <Button onClick={save} type="primary" loading={false}>
                                        <FormattedMessage id="common.ok"/>
                                    </Button>
                                }
                            </Space>
                        </Col>
                    </Row>
                }
            </div>:null
        }
    >
        <Tabs
            size="small"
            defaultActiveKey={"1"}
            onChange={onChange}
        >
            {
                noticeTabVisible &&
                <Tabs.TabPane
                    tab={<FormattedMessage id="calendar.button.site.visit.matter.notice.send" />}
                    destroyInactiveTabPane={true}
                    key="1"
                >
                    <NoticeVisitTab />
                </Tabs.TabPane>
            }
            <Tabs.TabPane
                tab={<FormattedMessage id="calendar.button.site.visit.matter.notice.history" />}
                destroyInactiveTabPane={true}
                key="2"
            >
            <Table
                className="mar-top-10"
                size="small"
                // loading={getProjectHomeListLoading || focusOnProjectsRunLoading}
                dataSource={historyData}
                pagination={false}
                scroll={{ y: 'calc(100vh - 230px)'}}
                // scroll={{y: 'calc(100vh - 230px)'}}
                rowKey={(record: any) => (record.id)}
                // onRow={(record, index: any) => {    // 鼠标移入行
                //     return {
                //         onMouseEnter: (event) => {  // 鼠标移入行
                //             let newData = JSON.parse(JSON.stringify(ctx.data));
                //             newData[index].sign = "1";
                //             ctx.setData(newData);
                //         },
                //         onMouseLeave: (event) => {  // 鼠标移出行
                //             let newData = JSON.parse(JSON.stringify(ctx.data));
                //             newData[index].sign = "0";
                //             ctx.setData(newData);
                //         },
                //     };
                // }}
            >
                <Table.Column
                    title={<FormattedMessage id="calendar.button.site.visit.matter.notice.history.content" />}
                    dataIndex={"content"}
                    key="content"
                    width={200}
                    ellipsis
                    render={(value, record, index) => (
                            renderContent(value)
                        )
                    }
                />
                <Table.Column
                    title={<FormattedMessage id="calendar.button.site.visit.matter.notice.history.time" />}
                    dataIndex={"pushTime"}
                    key="pushTime"
                    ellipsis
                    render={
                        (value: any, record: any, index: any) => (
                            (value === null || value === 0) ? '' : (moment.unix(value).utc().add(record.timeZone, "hour").format('YYYY-MM-DD HH:mm:ss') + (record.timeZone >= 0 ? "(UTC+" : "(UTC") + formatTimezoneOffset(record.timeZone) + ")")
                        )
                    } 
                />
                <Table.Column
                    title={<FormattedMessage id="calendar.button.site.visit.matter.notice.history.people" />}
                    dataIndex={"pushPeople"}
                    key="pushPeople"
                    ellipsis
                />
                <Table.Column
                    title={<FormattedMessage id="calendar.button.site.visit.matter.notice.history.way" />}
                    dataIndex={"pushType"}
                    key="pushType"
                    ellipsis
                    render={(value, record, index) => {
                        return value === 1 ? (
                            <FormattedMessage id="calendar.button.site.visit.matter.app.notice" />
                        ):<FormattedMessage id="calendar.button.site.visit.matter.text.message" />
                    }}
                />
                <Table.Column
                    title={<FormattedMessage id="calendar.button.site.visit.matter.notice.history.object" />}
                    dataIndex={"object"}
                    key="object"
                    width={g.lang==="zh"?100:150}
                    ellipsis
                    // render={(value, record, index) => (renderStatus(value))}
                />
            </Table>
            </Tabs.TabPane>
        </Tabs>

    </Modal>

}

const NoticeVisitTab = ({ } ) => {
    const context = useVisit();
    const intl = useTranslation();
    const { formatMessage } = intl;
    const [value, setValue] = useState(context.noticeMode);
    const [form] = useForm()
    const g = useGlobal();


    const auth = useAuth()
    const envId = auth.env ? auth.env.id : null;
    const projectId = auth.project? auth.project.id : null;
    const customerId = auth.customerId;


    const onChangeMode = (e: RadioChangeEvent) => {
    //   console.log('radio checked', e.target.value);
      context.setNoticeMode(e.target.value);
    };

    const onChangeTemplate = (value: string) => {
        // console.log(`selected ${value}`);
        context.setNoticeTemplate(value);
    };
    
    const onSearchTemplate = (value: string) => {
        // console.log('search:', value);
    };



    return <>
        <Form form={form} labelCol={{ span: g.lang === 'en' ? 3 : 3 }}>
            <Form.Item
                label={formatMessage({id: "common.template"})}
            >
                 <Select
                    value={context.noticeTemplate}
                    onChange={onChangeTemplate}
                    onSearch={onSearchTemplate}
                 >
                     <Select.Option  value={"a"}>
                         {
                             <Row><span style={{color:"#666666", marginRight:2}}>{formatMessage({id: "projects.notice.rules.template.a"})}:</span>{formatMessage({id: "projects.notice.rules.template.a.bracket"})}{formatMessage({id: "projects.notice.rules.template.a.content"})} </Row>
                         }
                     </Select.Option>
                     <Select.Option value={"b"}>
                         {
                             <Row><span style={{color:"#666666", marginRight:2}}>{formatMessage({id: "projects.notice.rules.template.b"})}:</span>{formatMessage({id: "projects.notice.rules.template.b.content"})}</Row>
                         }
                     </Select.Option>
                     <Select.Option value={"c"}>
                         {
                             <Row><span style={{color:"#666666", marginRight:2}}>{formatMessage({id: "projects.notice.rules.template.c"})}:</span>{formatMessage({id: "projects.notice.rules.template.c.content"})}</Row>
                         }
                     </Select.Option>
                     <Select.Option value={"d"}>
                         {
                             <Row><span style={{color:"#666666", marginRight:2}}>{formatMessage({id: "projects.notice.rules.template.d"})}:</span>{formatMessage({id: "projects.notice.rules.template.d.content"})}</Row>
                         }
                     </Select.Option>
                 </Select>
            </Form.Item>
            <Form.Item
                label={formatMessage({id: "project.statistics.push.mode"})}
            >
                <Radio.Group onChange={onChangeMode} value={context.noticeMode}>
                    <Radio value={1}>{formatMessage({id: "calendar.button.site.visit.matter.app.notice"})}</Radio>
                    <Radio value={2}>{formatMessage({id: "calendar.button.site.visit.matter.text.message"})}</Radio>
                </Radio.Group>
            </Form.Item>
            <Form.Item
                rules={[{required:true}]}
                label={formatMessage({id: "calendar.button.site.visit.matter.recipient"})}
            >
                <RoleUser context={context} customerId={customerId} projectId={projectId} envId={envId} type={2} projectType={0}/>
            </Form.Item>
        </Form>
    </>
}
