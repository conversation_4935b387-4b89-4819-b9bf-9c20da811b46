import React from "react";
import {<PERSON><PERSON>, <PERSON>, Col, DatePicker, Form, Input, Pagination, Row, Select, Table, Typography} from "antd";
import {FormattedMessage, useIntl} from "react-intl";
import {InfoCircleTwoTone, SearchOutlined} from "@ant-design/icons";
import moment from "moment";
import {useSafeState} from "ahooks";
import Footer from "../../main/layout/footer";
import {useFetch} from "../../../hooks/request";
import {getVisitList} from "../../../api/visit";
import {getConfigureList} from "../../../api/drug";
import {getDispensing, getProjectSite} from "../../../api/check";
import {useAuth} from "../../../context/auth";


export const Dispensing = () => {
    const {Text} = Typography;
    const { Search } = Input;
    const RangePicker:any= DatePicker.RangePicker
    const auth = useAuth();
    const intl = useIntl();
    const {formatMessage} = intl;
    const [form] = Form.useForm();
    const [data, setData] = useSafeState<any>([]);
    const [visitCycles, setVisitCycles] = useSafeState<any>([]);
    const [envId, setEnvId] = useSafeState<any>(auth.env ? auth.env.id : null);
    const [cohortId, setCohortId] = useSafeState<any>(auth.cohort ? auth.cohort.id : null);
    const [customerId, setCustomerId] = useSafeState<any>(auth.customerId);
    const [projectId, setProjectId] = useSafeState<any>(auth.project.id);
    const [cohortOption, setCohortOption] = useSafeState<any>([]);
    const [siteOption, setSiteOption] = useSafeState<any>([]);
    const [project, setProject] = useSafeState<any>([]);
    const [dispensingData, setDispensingData] = useSafeState<any>([]);

    const [currentPage, setCurrentPage] = useSafeState<any>(1);
    const [pageSize, setPageSize] = useSafeState<any>(20);
    const [total, setTotal] = useSafeState<any>(0);
    const [totalErr, setTotalErr] = useSafeState<any>(0);

    const {runAsync: getVisitListRun, loading:getVisitListLoading} = useFetch(getVisitList, {manual: true})
    const {runAsync:getConfigureListRun, loading:getConfigureListLoading} = useFetch(getConfigureList, {manual: true})
    const {runAsync:getDispensingRun, loading:getDispensingLoading} = useFetch(getDispensing, {manual: true})
    const {runAsync:getProjectSiteRun, loading:getProjectSiteLoading} = useFetch(getProjectSite, {manual: true})

    const search = () => {

        form.validateFields().then(
            value => {
                let request = {...form.getFieldsValue()}
                if (form.getFieldsValue().time) {
                    // @ts-ignore
                    const startAt = parseInt(String(moment(form.getFieldsValue().time[0]).startOf("day").format("x") / 1000))
                    // @ts-ignore
                    const endAt = parseInt(String(moment(form.getFieldsValue().time[1]).endOf("day").format("x") / 1000));
                    // form.getFieldsValue().time[0] = startAt
                    // form.getFieldsValue().time[1] = endAt
                    request["time"] = [startAt, endAt]
                    request["start"] = (currentPage - 1) * pageSize
                    request["limit"] = pageSize
                }
                getDispensingRun(request).then(
                    (result :any) => {
                        let data = result.data
                        setTotal(data.total)
                        setTotalErr(data.errorCount)
                        setDispensingData(data.item)
                    },
                )
            }
        )
    }




    const getList = () => {
        //获取访视周期
        getVisitListRun({customerId, projectId, envId, cohortId: cohortId}).then(
            (result :any) => {
                let data = result.data
                if (data.infos != null) {
                    const options = data.infos.map((it:any) => ({
                        label: it.name,
                        value: it.id
                    }));
                    setVisitCycles(options);
                }
                //研究产品配置列表查询
                getConfigureListRun({customerId, envId, cohortId: cohortId}).then(
                    (result :any) => {
                        let data = result.data
                        setData(data.configures);
                    }
                )
            }
        )
    };

    const getProject = () => {
        getProjectSiteRun().then(
            (result :any) => {
                let data = result.data
                if (data){
                    setProject(data)
                    if (data.findIndex((value:any) => envId === value.env_id) !== -1){
                        const cohorts = data.find((value:any) => envId === value.env_id).cohorts
                        const site = data.find((value:any) => envId === value.env_id).site
                        setCohortOption(cohorts)
                        setSiteOption(site)
                        form.setFieldsValue(
                            {
                                env: envId,
                                cohort: cohortId,
                                site: [],
                                time: [moment().subtract(10,"days"),moment()]
                            })
                        search()
                    }
                }
            }
        )
    }
    React.useEffect(getProject, []);
    React.useEffect(getList, [envId,cohortId]);
    React.useEffect(search, [currentPage,pageSize]);


    const projectChange = (e:any) => {
        setDispensingData([])

        form.setFieldsValue({cohort: null, site: []})
        if (e) {
            const project_id = project.find((value:any) => e === value.env_id).project_id
            const customer_id = project.find((value:any) => e === value.env_id).customer_id
            const env_id = project.find((value:any) => e === value.env_id).env_id
            const cohorts = project.find((value:any) => e === value.env_id).cohorts
            const site = project.find((value:any) => e === value.env_id).site
            setCohortOption(cohorts)
            setSiteOption(site)
            setProjectId(project_id)
            setCustomerId(customer_id)
            setEnvId(env_id)
            setData([])
            setTotalErr(0)

            if (cohorts.length === 0){
                setCohortId(null)
                form.setFieldsValue({site: []})
            }else{
                setCohortId(cohorts[0].id)
                form.setFieldsValue({site: [], cohort:cohorts[0].id})
            }
        } else {
            setCohortOption([])
            setSiteOption([])
            setProjectId(null)
            setCustomerId(null)
            setEnvId(null)
            setData(null)
            setCohortId(null)
            setTotalErr(0)
        }

    }

    const cohortChange = (e:any) => {
        setCohortId(e)
        setDispensingData([])
    }

    function renderVisitName(value:any) {
        return visitCycles.find((it:any) => (it.value === value)) ? visitCycles.find((it:any) => (it.value === value)).label : "";
    }


    return (
        <React.Fragment>
            <Form form={form}>
                <Row gutter={8} justify="start">
                    <Col className="mar-all-10">
                        <Form.Item name="env" label={formatMessage({id: 'check.select.project'})} rules={[{ required: true }]}>
                            <Select allowClear style={{width: 200}} onChange={(e) => {
                                projectChange(e)
                            }}>
                                {
                                    project.map((value:any) =>
                                        <Select.Option title={value.number+"-"+value.name} key={value.env_id}
                                                       value={value.env_id}>{value.number}-{value.name}</Select.Option>
                                    )
                                }
                            </Select>
                        </Form.Item>
                    </Col>
                    {
                        cohortOption.length > 0 ?
                            <Col className="mar-all-10">
                                <Form.Item name="cohort" label={formatMessage({id: 'check.cohort'})} rules={[{ required: true }]}>
                                    <Select allowClear style={{width: 200}} onChange={(e) => {cohortChange(e)}}>
                                        {
                                            cohortOption.map((value:any) =>
                                                <Select.Option title={value.name} key={value.id}
                                                               value={value.id}>{value.name}</Select.Option>
                                            )
                                        }
                                    </Select>
                                </Form.Item>
                            </Col>
                            :
                            null
                    }

                    <Col className="mar-all-10">
                        <Form.Item name="site" label={formatMessage({id: 'check.select.site'})} >
                            <Select placeholder={formatMessage({id: 'common.sites'})} mode="multiple" allowClear style={{width: 200}} maxTagCount='responsive'>
                                {
                                    siteOption.map((value:any) =>
                                        <Select.Option title={value.number +"-"+value.name} key={value._id}
                                                       value={value._id}>{value.number}-{value.name}</Select.Option>
                                    )
                                }
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col className="mar-all-10">
                        <Form.Item name="time" label={formatMessage({id: 'check.select.time'})} rules={[{ required: true }]}>
                            <RangePicker
                                placeholder={[formatMessage({id: 'check.select.unlimited'}), formatMessage({id: 'check.select.unlimited'})]}/>
                        </Form.Item>
                    </Col>
                    <Col className="mar-all-10">
                        <Button onClick={search}><SearchOutlined style={{ color: "#BFBFBF" }} /> {formatMessage({id: 'check.search'})}</Button>
                    </Col>
                </Row>
                <Card
                    title={<div><InfoCircleTwoTone/> {formatMessage({id: 'drug.configure'})}</div>}
                    headStyle={{backgroundColor: "#EDECECEC"}}
                    bodyStyle={{backgroundColor: "#EDECECEC"}}
                >
                    <Table
                        size="small"
                        loading={getConfigureListLoading || getVisitListLoading}
                        dataSource={data}
                        pagination={false}
                        rowKey={(record) => (record.id)}
                    >
                        <Table.Column title={<FormattedMessage id="common.serial"/>} dataIndex="#" key="#" width={35}
                                      render={(text, record, index) => (index + 1)}/>
                        <Table.Column width="10%" title={<FormattedMessage id="drug.configure.group"/>} key="group"
                                      dataIndex="group" align="left" ellipsis/>
                        <Table.Column
                            width="40%"
                            title={<FormattedMessage id="drug.configure.drugName"/>}
                            key="values"
                            dataIndex="values"
                            align="left"
                            ellipsis
                            render={
                                (value, record, index) => (
                                    value.map((it:any) => {
                                        return it.drugName + "/" + it.dispensingNumber + "/" + it.drugSpec + " "
                                    })
                                )
                            }
                        />
                        <Table.Column
                            title={<FormattedMessage id="drug.configure.visitName"/>}
                            width="40%"
                            key="visitCycles"
                            dataIndex="visitCycles"
                            align="left"
                            ellipsis
                            render={
                                (value, record, index) => (
                                    value === null ? "" : value.map((it:any) => {
                                        return renderVisitName(it) + " "
                                    })
                                )
                            }
                        />
                        <Table.Column width="10%" title={<FormattedMessage id="drug.configure.drugLabel"/>} key="label"
                                      dataIndex="label" align="left" ellipsis/>
                        <Table.Column
                            width="10%"
                            title={<FormattedMessage id="drug.configure.roomNumber"/>}
                            key="roomNumbers"
                            dataIndex="roomNumbers"
                            align="left"
                            ellipsis
                            render={(text, record:any, index) => {
                                return (
                                    record.roomNumbers ?
                                        record.roomNumbers.join(",")
                                        :
                                        null
                                );
                            }}
                        />
                    </Table>

                </Card>
                <Row className="mar-top-10">
                    <Col span={20}>{formatMessage({ id: 'check.dispensing.error' })}：<Text style={{color:"#e70c0c"}}>{totalErr}</Text></Col>
                    <Col span={4}>
                        <Form.Item name="subject">
                            <Search onSearch={search} placeholder={formatMessage({ id: 'check.subject.search' })} />
                        </Form.Item>
                    </Col>
                </Row>
                <Row className="mar-all-10">
                    <Table
                        size="small"
                        loading={getDispensingLoading}
                        dataSource={dispensingData}
                        pagination={false}
                        rowKey={(record) => ((currentPage-1)*pageSize+record.id)}
                    >
                        <Table.Column title={<FormattedMessage id="common.serial"/>} dataIndex="#" key="#" width={35}
                                      render={(text, record, index) => (index + 1)}/>
                        <Table.Column width="30%" title={formatMessage({ id: 'subject.number' })} key="subject"
                                      dataIndex="subject" align="left"
                                      render={
                                          (value, record:any, index) => (
                                              record.check ?
                                                  value
                                                  :
                                                  <div style={{color:"#e70c0c"}}>{value}</div>
                                          )
                                      }
                        />
                        <Table.Column width="40%" title={formatMessage({ id: 'common.site' })} key="site" dataIndex="site" align="left" ellipsis/>
                        <Table.Column width="40%" title={formatMessage({ id: 'check.random.group' })} key="random_group" dataIndex="random_group" align="left" ellipsis/>
                        <Table.Column width="40%" title={formatMessage({ id: "visit.cycle.visitName" })} key="visit" dataIndex="visit" align="left" ellipsis
                                      render={
                                          (value, record:any, index) => (
                                              record.type > 0 ?
                                                  record.type ===2 ?(value + "(" + formatMessage({id: 'subject.dispensing.reissue'}) + ")"):(value + "(" + formatMessage({id: 'subject.dispensing.visitSign'}) + ")")
                                                  :
                                                  value

                                          )
                                      }
                        />
                        <Table.Column width="40%" title={formatMessage({ id: "check.dispensing.medicine" })} key="medicine" dataIndex="medicine" align="left"
                                      render={
                                          (value, record, index) => (
                                              value && value.map((item:any) =>
                                                  <Row>{item}</Row>
                                              )
                                          )
                                      }

                        />
                        <Table.Column width="40%" title={formatMessage({ id: "check.dispensing.group" })} key="medicine_group" dataIndex="medicine_group" align="left" ellipsis/>
                        <Table.Column width="40%" title={formatMessage({ id: "check.dispensing.medicine.blind" })} key="medicine_true" dataIndex="medicine_true" align="left"
                                      render={
                                          (value, record, index) => (
                                              value && value.map((item:any) =>
                                                  <Row>{item}</Row>
                                              )
                                          )
                                      }
                        />
                        <Table.Column width="40%" title={formatMessage({ id: "check.dispensing.time" })} key="dispensing_time" dataIndex="dispensing_time" align="left" ellipsis
                                      render={
                                          (value, record:any, index) => (
                                              value +"("+record.utc+")"
                                          )
                                      }
                        />
                    </Table>
                </Row>
                <Footer>
                    <Pagination
                        hideOnSinglePage={false}
                        className="text-right"
                        current={currentPage}
                        pageSize={pageSize}
                        pageSizeOptions={['10', '20', '50', '100']}
                        total={total}
                        showSizeChanger
                        showTotal={(total, range) => (`${range[0]} - ${range[1]} / ${total}`)}
                        onChange={(page, pageSize) => {
                            setCurrentPage(page);
                        }}
                        onShowSizeChange={(current, size) => {
                            setCurrentPage(1);
                            setPageSize(size);
                        }}
                    />
                </Footer>

            </Form>


        </React.Fragment>
    )
};


// const mapStateToProps = (state) => {
//     return {
//         permissions: state.common.project.permissions,
//         project: state.common.project,
//     }
// };
//
// const mapDispatchToProps = (dispatch) => {
//     return {
//         getVisitList(customerId, projectId, envId, param, success, fail) {
//             const action = getVisitList(customerId, projectId, envId, param, success, fail);
//             dispatch(action);
//         },
//         getList(customerId, envId, param, success, fail) {
//             const action = getConfigureList(customerId, envId, param, success, fail);
//             dispatch(action);
//         },
//         getProjectSite(success, fail, err) {
//             const action = getProjectSite(success, fail, err);
//             dispatch(action);
//         },
//         getDispensing(data, success, fail, err) {
//             const action = getDispensing(data, success, fail, err);
//             dispatch(action);
//         }
//
//     }
// };
