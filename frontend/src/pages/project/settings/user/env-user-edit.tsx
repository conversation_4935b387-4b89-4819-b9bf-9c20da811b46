import React from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {
    Button,
    Checkbox,
    Col,
    Form,
    Input,
    message as antd_message,
    Modal,
    Popover,
    Row,
    Spin,
    Tag,
    Tooltip,
    Select,
    Radio,
    Space,
} from "antd";

import {useFetch} from "../../../../hooks/request";
import {
    addProjectEnvironmentUser,
    setEnvUserUnblindingCode,
    setProjectEnvironmentUserRoles,
    getProjectEnvBlind
} from "../../../../api/projects";
import {listUserRoles} from "../../../../api/user";
import {useAuth} from "../../../../context/auth";
import {useSafeState} from "ahooks";
import {permissions} from "../../../../tools/permission";
import {EditOutlined} from "@ant-design/icons";
import {UnblindingCodeTag} from "./unblinding_code_tag";
import {useGlobal} from "../../../../context/global";
import {CustomConfirmModal} from "../../../../components/modal";


export const EnvUserEdit = (props:any) => {


    const auth = useAuth();

    const customerId = auth.customerId;
    const projectStatus = auth.project.status ? auth.project.status : 0

    const intl = useIntl();
    const {formatMessage} = intl;

    const [visible, setVisible] = useSafeState<any>(false);
    const [roles, setRoles] = useSafeState<any>([]);
    const [data, setData] = useSafeState<any>(null); // 从env-users传递过来的用户信息
    const [form] = Form.useForm();
    const [codeClicked,setCodeClicked] = useSafeState(false);
    const [unbind,setUnbind] = useSafeState(true);
    const [blind,setBlind] = useSafeState(false);
    const researchAttribute = auth.project.info.research_attribute === 1 ? 2 : 1;
    const {runAsync:getProjectEnvBlindRun} = useFetch(getProjectEnvBlind, {manual: true})
    const {runAsync:setProjectEnvironmentUserRolesRun, loading:setProjectEnvironmentUserRolesLoading} = useFetch(setProjectEnvironmentUserRoles, {manual: true})
    const {runAsync:setEnvUserUnblindingCodeRun, loading:setEnvUserUnblindingCodeLoading} = useFetch(setEnvUserUnblindingCode, {manual: true})
    const {runAsync: addProjectEnvironmentUserRun, loading:addProjectEnvironmentUserLoading} = useFetch(addProjectEnvironmentUser, {manual: true})
    const {runAsync: listUserRolesRun, loading:listUserRolesLoading} = useFetch(listUserRoles, {manual: true})


    const show = (data:any) => {
        if (data) {
             //判断项目是否盲态
            getProjectEnvBlindRun(
                {projectId: auth.project.id, envId:auth.env.id},
            ).then(
                (resp:any) => {
                    setBlind(resp.data)
                }
            )
            setUnbind(data.unbind)
            data.availableCount = data.unblindingCode.availableCode?data.unblindingCode.availableCode.length:0
            data.usedCount = data.unblindingCode.usedCode?data.unblindingCode.usedCode.length:0
            setData(data);
            form.setFieldsValue({ email: data.email, roles: data.roles, availableCount:data.availableCount});
            listUserRolesRun({projectId:auth.project.id,userId:data.userId}).then(
                (result:any) => {
                    setRoles(result.data);
                }
            )
        }else{
            setUnbind(false)
        }
        setVisible(true);
    };

    const hide = () => {
        setRoles([]);
        setData(null);
        form.resetFields();
        setVisible(false);
        setUnbind(true);
        props.refresh()
    };

    function statusItem(it:any) {
        let color: string;
        if (it.scope === "study") {
            color = "red"
        } else if (it.scope === "site") {
            color = "orange"
        } else {
            color = "green"
        }
        return <Tooltip placement="right" title={it.scope}>{it.name}</Tooltip>;
    }
    const handleClickChange = (visible:any) => {
        setCodeClicked(visible);
    };

    const onSaveUnblindCode = () =>{
        const availableCount = form.getFieldValue("availableCount");
        form.validateFields(["availableCount"]).then(() => {
            setEnvUserUnblindingCodeRun(
                {projectId: auth.project.id, envId: auth.env.id, userId: data.userId},
                {unblindingAvailableCount: parseInt(availableCount)},
            ).then((resp: any) => {
                    antd_message.success(resp.msg)
                    data.unblindingCode = resp.data.unblindingCode
                    data.availableCount = data.unblindingCode ? data.unblindingCode.availableCode.length : 0
                    setData(data)
                    setCodeClicked(false)
                }
            ).catch(() => setCodeClicked(false))

        })
    }

    const numberValidator = {
        validator: (rule:any, value:any, callback:any) => {
            const reg = /^[0-9]\d*$/;
            if (value && !reg.test(value)) {
                return Promise.reject(formatMessage({id: 'validator.msg.number'}));
            }
            // @ts-ignore
            if (value && (0 > parseInt(value) ||  parseInt(value) > 999)) {
                return Promise.reject(formatMessage({id: 'validator.msg.number'}));
            }
            return Promise.resolve();
        }
    };

    // 提交数据
    const saveForm = (values: any) => {
        if (data) {
            // 将无效角色的push到数组，保留原来已经勾选的数据
            data.roles.forEach((item:any) => {
                if (roles.findIndex((it:any)=>(item === it.id && it.status === 2)) !== -1){
                    values.roles.push(item)
                }
            })
            setProjectEnvironmentUserRolesRun(
                {projectId: auth.project.id, envId:auth.env.id, userId:data.userId},
                {roles: values.roles},
            ).then(
                (resp:any) => {
                    antd_message.success(formatMessage({id: 'common.authorization.success'}))
                    // antd_message.success(resp.msg)
                    hide()
                }
            )
        } else {
            addProjectEnvironmentUserRun({projectId: auth.project.id, envId:auth.env.id},
                {
                    customerId: customerId,
                    projectId: auth.project.id,
                    envId: auth.env.id,
                    emailLanguage: values.emailLanguage,
                    email: values.email,
                },).then(
                (resp:any) => {
                    antd_message.success(formatMessage({id: 'common.authorization.success'}))
                    // antd_message.success(resp.msg)
                    hide()
                }
            )
        }
    };

    const save = () => {
        form.validateFields().then(
            values => {
                if (data) {
                    let roleName = "";
                    values.roles.forEach(
                        (v: any) => {
                            roles.forEach(
                                (r: any) => {
                                    if (v === r.id && r.type === 1) {
                                        roleName += r.name + "  "
                                    }
                                }
                            )
                        }
                    );
                    if (blind && roleName.length >0){
                        CustomConfirmModal({
                            title: formatMessage({id: 'env.user.edit.section1'}) + "(" + roleName + ")?",
                            okText: formatMessage({id: 'common.ok'}),
                            onOk: () => {
                                saveForm(values);
                            }
                        });
                    }else{
                        saveForm(values);
                    }
                } else {
                    saveForm(values);
                }
            }
        )
    }
    React.useImperativeHandle(props.bind, () => ({ show }));

    const language = [
        {label:formatMessage({id:"common.email.language.zh"}), value:"zh"},
        {label:formatMessage({id:"common.email.language.en"}), value:"en"},
    ]

    const g = useGlobal()
    const formItemLayout = {
        labelCol: { style: {   width: g.lang === "en"? "120px": "88px"} },
    };

    return (
        <>
            <Modal
                className={data? "custom-large-modal":"custom-small-modal"}
                title={data?<FormattedMessage id="modal.title.user.role" />:<FormattedMessage id="common.add" />}
                open={visible}
                onCancel={hide}
                centered
                maskClosable={false}
                destroyOnClose={true}
                footer={
                    <Row justify="space-between">
                        <Col>

                        </Col>
                        {
                            (projectStatus !== 2 && !unbind && permissions(auth.project.permissions, data?"operation.build.settings.user.role":"operation.build.settings.user.add"))?
                            <Col>
                                <Button onClick={hide}>
                                    <FormattedMessage id="common.cancel" />
                                </Button>
                                <Button type="primary" loading={setProjectEnvironmentUserRolesLoading || addProjectEnvironmentUserLoading} onClick={save}>
                                    <FormattedMessage id="common.ok" />
                                </Button>
                            </Col>
                            :null
                        }

                    </Row>
                }
            >
                <Spin spinning={listUserRolesLoading}>
                    <Form form={form} {...formItemLayout}>
                        {
                            data?null:
                            <Form.Item
                                label={formatMessage({ id: 'common.email.language' })}
                                name="emailLanguage"
                                style={{marginBottom:data?24:24}}
                                rules={[{ required: true, message: formatMessage({ id: 'placeholder.select.common.email.language' })}]}
                                // initialValue={"zh"}
                            >
                                {/* <Select 
                                    className="full-width" 
                                    options={language}
                                >
                                </Select> */}
                                <Radio.Group 
                                    className="full-width"
                                    disabled={props.disabled}
                                >
                                    <Space >
                                        <Col
                                            style={{ marginRight: 24 }}
                                        >
                                            <Radio value={"zh"}>
                                                {
                                                    intl.formatMessage({id: "common.email.language.zh"})
                                                }
                                            </Radio>
                                        </Col>
                                        <Col
                                            style={{ marginRight: 24 }}
                                        >
                                            <Radio value={"en"}>
                                                {
                                                    intl.formatMessage({id: "common.email.language.en"})
                                                }
                                            </Radio>
                                        </Col>
                                    </Space>

                                </Radio.Group>
                            </Form.Item>
                        }
                        <Form.Item
                            label={formatMessage({ id: 'common.email' })}
                            name="email"
                            style={{marginBottom:data?24:0}}
                            rules={[{ required: !data, type: 'email' }]}
                        >
                            {
                                data ?
                                    <span className="ant-form-text">{data.email}</span>:

                                    <Input allowClear placeholder={formatMessage({id: "placeholder.input.common"})}/>
                            }

                        </Form.Item>
                        {
                            data && roles
                                ?
                                <Form.Item
                                    label={formatMessage({ id: "common.role" })}
                                    name="roles"
                                >
                                    <Checkbox.Group
                                        disabled={projectStatus === 2 || unbind || !permissions(auth.project.permissions, "operation.build.settings.user.role")}>
                                        <Row
                                            gutter={24}>{roles.filter((it: any) => it.status === 1 && it.template === researchAttribute && it.name !== "Customer-Admin" && it.name !== "Sys-Admin")
                                            .map((it: any) =>
                                                <Col span={12} key={it.id}
                                                     style={{ height: 32, display: "flex", width: "420px" }}>
                                                    <Checkbox disabled={it.name === "Project-Admin"}
                                                              value={it.id}>{statusItem(it)}</Checkbox>
                                                </Col>
                                            )}
                                        </Row>
                                    </Checkbox.Group>
                                </Form.Item>
                                :
                                null
                        }
                        {
                            (data && auth.project.info.unblinding_code === 1) ?
                                <>
                                    <Row gutter={24}>
                                        <Col style={{height:"32px", width:"80px"}} span={24}>
                                            <Form.Item
                                                label={formatMessage({ id: "project.setting.checkbox.unblinded-code" })}
                                            >
                                        <span className="ant-form-text">
                                            <FormattedMessage id={"form.label.unblinding.code.available"} />
                                            <span style={{ color: "#165DFF" }}>{data.availableCount}</span>
                                            <FormattedMessage id={"form.label.unblinding.code.indivual"} /></span>
                                                {permissions(auth.project.permissions, "operation.subject.unblinding-application") && permissions(auth.project.permissions, "operation.build.settings.user.role") && !unbind ?
                                                    <Popover
                                                        visible={codeClicked}
                                                        trigger="click"
                                                        onVisibleChange={handleClickChange}
                                                        content={
                                                            <>
                                                                <Row style={{ padding: 4 }}>
                                                                    <Col>
                                                                        <Form.Item
                                                                            name="availableCount"
                                                                            label={formatMessage({ id: "form.label.unblinding.code.available" })}
                                                                            rules={[numberValidator]}
                                                                        >
                                                                            <Input style={{ width: "88px" }}
                                                                                size={"small"} />
                                                                            {/*<InputNumber controls={false} size={'small'}/>*/}

                                                                        </Form.Item>
                                                                    </Col>
                                                                    <Col>
                                                                        <Form.Item>
                                                                            <Button size={"small"} style={{
                                                                                marginLeft: "8px",
                                                                                marginRight: "8px"
                                                                            }}
                                                                                    onClick={() => setCodeClicked(false)}>
                                                                                <FormattedMessage id="common.cancel" />
                                                                            </Button>
                                                                        </Form.Item>
                                                                    </Col>
                                                                    <Col>
                                                                        <Form.Item>
                                                                            <Button type="primary" size={"small"}
                                                                                    loading={setEnvUserUnblindingCodeLoading}
                                                                                    onClick={onSaveUnblindCode}>
                                                                                <FormattedMessage id="common.ok" />
                                                                            </Button>
                                                                        </Form.Item>
                                                                    </Col>
                                                                </Row>
                                                            </>
                                                        }>
                                                        <EditOutlined style={{ color: "#2B74EA" }} />
                                                    </Popover> : null}
                                            </Form.Item>
                                        </Col>
                                    </Row>
                                    <Spin spinning={setEnvUserUnblindingCodeLoading}>
                                        <Row justify="start" style={{marginLeft:"64px",marginTop:"14px"}} >
                                            {data.availableCount > 0 ?
                                                data.unblindingCode.availableCode.map((code: string) =>
                                                    <>
                                                        <Col style={{paddingBottom: "12px", marginRight:"16px"}}>
                                                            <UnblindingCodeTag code={code}/>
                                                        </Col>
                                                    </>
                                                )
                                                : null}

                                        </Row>
                                    </Spin>
                                    <Row gutter={24} style={{marginTop:"14px"}} >
                                        <Col span={22} offset={2}>
                                          <span className="ant-form-text"><FormattedMessage
                                              id={'form.label.unblinding.code.used'}/><span style={{color:"#165DFF"}}>{data.usedCount}</span><FormattedMessage
                                              id={'form.label.unblinding.code.indivual'}/></span>
                                        </Col>
                                    </Row>
                                    <Row justify="start" style={{marginLeft:"64px",marginTop:"14px"}} >
                                        {data.usedCount>0 ?
                                            data.unblindingCode.usedCode.map((code:string)=>
                                                <>
                                                    <Col style={{paddingBottom: "12px", marginRight:"16px"}}>
                                                        <Tag
                                                            style={{
                                                                backgroundColor: "#F4F4F5",
                                                                color: "#ADB2BA",
                                                                border: "none",
                                                                height: "28px",
                                                                width: "98px",
                                                                left: "0px",
                                                                top: "0px",
                                                                borderRadius: "2px",
                                                                textAlign: "center",
                                                                lineHeight: "30px",
                                                                fontSize: "14px"
                                                            }}>
                                                            {code}
                                                        </Tag>
                                                    </Col>
                                                </>
                                            )
                                            :null}
                                    </Row>
                                </>
                                : null
                        }
                    </Form>
                </Spin>
            </Modal>
        </>
    )
};
