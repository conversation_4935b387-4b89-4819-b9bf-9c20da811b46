import React from "react";
import {<PERSON><PERSON>, <PERSON><PERSON>, Col, Input, message, Row, Space, Switch, Table, Tooltip} from "antd";
import {UserDepot} from "./depot";
import {
    batchUnbindProjectEnvironmentUser,
    downloadProjectEnvironmentUserData,
    projectEnvironmentUsersSearchList,
    reauthorizationProjectEnvironmentUser,
    resendInviteEmail,
    switchAppAccount,
    unbindProjectEnvironmentUser
} from "../../../../api/projects";
import {EnvUserEdit} from "./env-user-edit";
import {EnvUserBatchAdd} from "./env_user_batch_add";
import {EnvUserInviteAgain} from "./env-user-edit-inviteAgain";
import moment from "moment";
import {useAuth} from "../../../../context/auth";
import {useProjectUser} from "./context";
import {useFetch} from "../../../../hooks/request";
import {UserSite} from "./site";
import Footer from "pages/main/layout/footer";
import {PaginationView} from "components/pagination";
import {usePage} from "context/page";
import {useSafeState} from "ahooks";
import {permissions} from "../../../../tools/permission";
import {CustomConfirmModal} from "components/modal";
import {useGlobal} from "../../../../context/global";
import {OperationList} from "../../../common/operation-list";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {AuthButton, AuthSpan, AuthSwitch} from "../../../common/auth-wrap";

export const User = () => {

    const ctx = useProjectUser();
    const auth = useAuth();
    const g = useGlobal()
    const user_site = React.useRef<any>();
    const user_depot = React.useRef<any>();
    const env_user_edit = React.useRef<any>();
    const env_user_batchAdd = React.useRef<any>();
    const env_user_inviteAgain = React.useRef<any>();
    const searchInputRef = React.useRef<any>();
    const history_ref: any = React.useRef();
    // const [doSearch, setDoSearch] = useSafeState(0);
    const [timeZone, setTimeZone] = useSafeState<any>(null);
    const intl = useTranslation();
    const {formatMessage} = intl;
    const page = usePage();
    const envId = auth.env ? auth.env.id : null;
    const [selectedRowKeys, setSelectedRowKeys] = useSafeState<React.Key[]>([]);
    const {runAsync, loading} = useFetch(projectEnvironmentUsersSearchList, {manual: true, debounceWait: 300})
    const {
        runAsync: downloadRun,
        loading: downloadLoading
    } = useFetch(downloadProjectEnvironmentUserData, {manual: true})
    const {runAsync: appUpdateRun,loading:appUpdateLoading} = useFetch(switchAppAccount, {manual: true})
    const {runAsync: resendInviteEmailRun} = useFetch(resendInviteEmail, {manual: true})
    const {runAsync: unbindProjectEnvUserRun,loading:unbindProjectEnvUserLoading} = useFetch(unbindProjectEnvironmentUser, {manual: true})
    const {runAsync: batchUnbindProjectEnvUserRun,loading:batchUnbindProjectEnvUserLoading} = useFetch(batchUnbindProjectEnvironmentUser, {manual: true})
    const {runAsync: reauthorizationProjectEnvironmentUserRun,loading:reauthorizationProjectEnvironmentUserLoading} = useFetch(reauthorizationProjectEnvironmentUser, {manual: true})

    const rowSelection = {
        type: "checkbox",
        onChange: (selectedRowIds: React.SetStateAction<React.Key[]>) => {
            setSelectedRowKeys(selectedRowIds);
        },
        selectedRowKeys: selectedRowKeys,
        getCheckboxProps: (record: any) => ({
            disabled: record.unbind
        }),
        preserveSelectedRowKeys: true,
    };

    const resetSelected = () => {
        setSelectedRowKeys([]);
    }

    const refreshList = () => {
        getList();
        resetSelected();
    };

    const getList = () => {
        if (auth.project.info.timeZoneStr !== undefined && auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "") {
            ctx.setTimeZone(Number(auth.project.info.timeZoneStr));
            setTimeZone(Number(auth.project.info.timeZoneStr));
        } else {
            ctx.setTimeZone(8);
            setTimeZone(8);

        }
        if (permissions(auth.project.permissions, "operation.build.settings.user.view")) {
            runAsync({
                projectId: auth.project.id,
                envId: auth.env.id,
                email: ctx.email.trim(),
                start: (page.currentPage - 1) * page.pageSize,
                limit: page.pageSize
            }).then(
                (result: any) => {
                    page.setData(result?.data?.list ? result.data.list : []);
                    page.setTotal(result?.data.total)
                }
            )
        }
    };

    const download = () => {
        downloadRun({projectId: auth.project.id, envId: auth.env.id}).then(
        ).catch(
            () => {
                message.error(formatMessage({id: 'common.download.fail'})).then(() => {
                });
            }
        )
    };

    const add = () => {
        env_user_batchAdd.current.show();
        // env_user_edit.current.show();
    };
    //
    const edit = (item: any) => {
        env_user_edit.current.show({
            email: item.info.email,
            userId: item.id,
            roles: item.roles.map((it: any) => it._id),
            unblindingCode: item.unblindingCode,
            unbind:item.unbind
        });
    };

    const unbind = (userId: any) => {
        CustomConfirmModal({
            title: formatMessage({id: 'common.confirm.unbinding'}),
            content: formatMessage({id: 'tip.user.unbind'}),
            okText: formatMessage({id: 'common.ok'}) + " ",
            okButtonProps: {loading: unbindProjectEnvUserLoading},
            cancelText: formatMessage({id: 'common.cancel'}) + " ",
            onOk: () => {
                unbindProjectEnvUserRun(
                    {projectId: auth.project.id, envId: auth.env.id, userId: userId}).then(
                    (resp: any) => {
                        message.success(resp.msg)
                        // let curPage = Math.ceil((page.total - 1) / page.pageSize)
                        // curPage = curPage > 1 ? curPage : 1
                        // page.setCurrentPage(curPage)
                        // setDoSearch(doSearch + 1)
                        ctx.setDoSearch(ctx.doSearch + 1)
                    }
                )
            }
        });
    }

    const batchUnbind = () => {
        if (selectedRowKeys.length === 0) {
            message.warn(intl.formatMessage({ id: "common.select.list.tips" }));
            return;
        }

        CustomConfirmModal({
            title: formatMessage({id: 'common.confirm.unbinding'}),
            content: formatMessage({id: 'tip.user.unbind'}),
            okText: formatMessage({id: 'common.ok'}) + " ",
            okButtonProps: {loading: batchUnbindProjectEnvUserLoading},
            cancelText: formatMessage({id: 'common.cancel'}) + " ",
            onOk: () => {
                batchUnbindProjectEnvUserRun(
                    {projectId: auth.project.id, envId: auth.env.id},
                    {ids: selectedRowKeys}).then(
                    (resp: any) => {
                        message.success(resp.msg)
                        // let curPage = Math.ceil((page.total - 1) / page.pageSize)
                        // curPage = curPage > 1 ? curPage : 1
                        // page.setCurrentPage(curPage)
                        // setDoSearch(doSearch + 1)
                        ctx.setDoSearch(ctx.doSearch + 1)
                        setSelectedRowKeys([])
                    }
                )
            }
        });
    }

    const reauthorization = (userId: any) => {
        env_user_inviteAgain.current.show({
            customerId: auth.customerId, 
            projectId: auth.project.id, 
            envId: auth.env.id, 
            userId: userId,
            key:1
        });
        // reauthorizationProjectEnvironmentUserRun(
        //     {customerId: auth.customerId, projectId: auth.project.id, envId: auth.env.id, userId: userId}).then(
        //     (resp: any) => {
        //         message.success(resp.msg)
        //         let curPage = Math.ceil((page.total - 1) / page.pageSize)
        //         curPage = curPage > 1 ? curPage : 1
        //         page.setCurrentPage(curPage)
        //         setDoSearch(doSearch + 1)
        //     })
    }

    const inviteAgain = (item: any) => {
        env_user_inviteAgain.current.show({
            customerId: auth.customerId, 
            userId: item.id,
            envId:auth.env.id,
            unbind:item.unbind,
            key:2
        });
        // resendInviteEmailRun({customerId: auth.customerId, userId: item.id,envId:auth.env.id}).then(
        //     (result: any) => {
        //         message.success(result.msg).then(() => {
        //         });
        //     }
        // )
    }

    // const resendEmail = (item: any) => {
    //     CustomConfirmModal({
    //         title: formatMessage({id: 'common.tips'}),
    //         content: formatMessage({id: 'user.resend.invite.email.info'}),
    //         okText:formatMessage({ id: 'common.ok' }),
    //         onOk: () => {
    //             resendInviteEmailRun({customerId: auth.customerId, userId: item.id}).then(
    //                 (result: any) => {
    //                     message.success(result.msg).then(() => {
    //                     });
    //                 }
    //             ).catch(
    //                 (result: any) => {
    //                     if (result) {
    //                         message.error(result.msg).then(() => {
    //                         });
    //                     }
    //                 }
    //             )
    //         }
    //     });
    // }

    function renderStatus(record: any) {
        if (record.unbind === true) {
            return <div><Badge color={"#C0C4CC"} style={{marginRight: "8px"}}/>{formatMessage({id: 'common.invalid', allowComponent: true})}</div>
        }else if(record.info.status === 0) {
            return <div><Badge color={"#FFAE00"} style={{marginRight: "8px"}}/>{formatMessage({id: 'user.status.open', allowComponent: true})}</div>
        }else {
            return <div><Badge color={"#41CC82"} style={{marginRight: "8px"}}/>{formatMessage({id: 'common.effective', allowComponent: true})}</div>
        }
    }

    function getSwitchAppAccount(userId: any, c: any) {
        appUpdateRun({envId, userId, checked: c}).then(
            (resp: any) => {
                message.success(resp.msg)
                getList()
            }
        ).catch(
            () => {
                getList()
            }
        )
    }

    const showHistory = (record: any) => {
        history_ref.current.show("operation_log.module.user,operation_log.module.projectUser,operation_log.module.projectUser_role,operation_log.module.projectUser_site,operation_log.module.projectUser_depot", envId,record._id, null, timeZone);
    };


    const operationBut = (record:any) => {
        let buts :any[] = []
        if ((permissions(auth.project.permissions, "operation.build.settings.user.unbind") && !record.unbind)){
            buts.push(<AuthButton style={{marginLeft: "-8px"}} size="small" type="link"
                            onClick={() => unbind(record.id)}><FormattedMessage
                id="common.unbinding"/></AuthButton>)
        }
        if (permissions(auth.project.permissions, "operation.build.settings.user.reauthorization") && record.info.status !== 0 &&
            record.unbind && !record.closeCustomer.includes(auth.customerId)) {
            buts.push(<AuthButton style={{marginLeft: "-8px"}} size="small"
                              type="link"
                              onClick={() => reauthorization(record.id)}><FormattedMessage
                id="user.settings.reauthorization"/></AuthButton>) //再次授权
        }
        if (permissions(auth.project.permissions, "operation.build.settings.users.invite-again") && record.info.status=== 0){
            buts.push(<AuthButton style={{marginLeft: "-8px"}} size="small" type="link" onClick={() => inviteAgain(record)}>
                <FormattedMessage id="user.settings.invite-again"/></AuthButton>) //再次邀请
        }
        if (permissions(auth.project.permissions, "operation.build.settings.user.history")) {
            buts.push(<AuthButton style={{marginLeft: "-8px"}} size="small" type="link" onClick={() => {
                showHistory(record)
            }}>{formatMessage({id: 'common.history', allowComponent: true})}</AuthButton>)
        }
        if (buts.length === 0 ){
            return <span>-</span>
        }
        return buts
        // return InsertDivider(buts)
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
    React.useEffect(getList, [ ctx.doSearch, page.currentPage, page.pageSize]);

    const formatTimezone = (timestamp: any, timezone: any) => {
        const tzMoment = moment.tz(timestamp * 1000, timezone);

        // 获取时区偏移分钟数（如 -330 表示 UTC-05:30）
        const offsetMinutes = tzMoment.utcOffset(); // 单位是分钟
        const totalHours = Math.abs(offsetMinutes);
        const hours = Math.floor(totalHours / 60);
        const minutes = totalHours % 60;

        // 构造 UTC±HH:mm 格式
        const sign = offsetMinutes < 0 ? "-" : "+";
        const formattedOffset = `${sign}${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;

        // 格式化时间
        const formattedTime = tzMoment.format('YYYY-MM-DD HH:mm:ss');

        // 拼接结果
        return `${formattedTime}`;
    };
    
    return (
        <>
            <Row >
                <Col>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                        <svg className="iconfont" width={16} height={16}>
                            <use xlinkHref="#icon-quanburenyuan"/>
                        </svg>
                        <span style={{ color: "#1D2129", fontWeight: 600, paddingLeft: "8px" }}>{formatMessage({ id: 'projects.users.all', allowComponent: true})}</span>
                    </div>
                </Col>
            </Row>
            {
                permissions(auth.project.permissions, "operation.build.settings.user.view") &&
                <>
                    <Row className='mar-top-15'>
                        <div  style={{textAlign:"left",display:"inline-block"}}>
                            <Input.Search
                                className="mar-rgt-5"
                                placeholder={formatMessage({id: 'common.email'}) as string}
                                ref={searchInputRef}
                                value={ctx.email}
                                onChange={e => {
                                    ctx.setEmail(e.target.value)
                                }}
                                allowClear
                                onSearch={() => {
                                    page.setCurrentPage(1)
                                    searchInputRef?.current?.blur()
                                    // setDoSearch(doSearch + 1);
                                    ctx.setDoSearch(ctx.doSearch + 1)
                                }}
                            />
                        </div>
                        <div className="ver-center" style={{ marginLeft: "auto" }}>
                            {/* <Input.Search
                                className="mar-rgt-5"
                                placeholder={formatMessage({id: 'common.email'})}
                                ref={searchInputRef}
                                value={ctx.email}
                                onChange={e => {
                                    ctx.setEmail(e.target.value)
                                }}
                                allowClear
                                onSearch={() => {
                                    page.setCurrentPage(1)
                                    searchInputRef?.current?.blur()
                                    setDoSearch(doSearch + 1);
                                }}
                            /> */}
                            <Space size={12}>
                                <AuthButton
                                    show={permissions(auth.project.permissions, "operation.build.settings.user.download")&& auth.project.info.research_attribute===1}
                                    className="mar-rgt-5" loading={downloadLoading}
                                    onClick={download}
                                >
                                    <FormattedMessage id="common.download.data"/>
                                </AuthButton>
                                <AuthButton
                                    show={permissions(auth.project.permissions, "operation.build.settings.user.unbind.batch")}
                                    onClick={batchUnbind}
                                >
                                    <FormattedMessage id="common.unbinding.batch"/>
                                </AuthButton>
                                <AuthButton
                                    type={"primary"}
                                    onClick={add}
                                    show={permissions(auth.project.permissions, "operation.build.settings.user.add")}
                                >
                                    <FormattedMessage id="common.add"/>
                                </AuthButton>
                            </Space>
                        </div>
                    </Row>
                        <Table
                            className="mar-top-10"
                            scroll={{ x: "100%" ,y: 'calc(100vh - 270px)'}}
                            dataSource={page.data}
                            loading={loading}
                            pagination={false}
                            rowKey={(record) => (record.id)}
                            rowSelection={permissions(auth.project.permissions, "operation.build.settings.user.unbind.batch")? rowSelection:null}
                        >
                            <Table.Column
                                title={<FormattedMessage id="common.serial"/>}
                                key="#"
                                width={60}
                                render={(value, record, index) => ((page.currentPage-1)*page.pageSize+index + 1)}
                            />
                            <Table.Column 
                                title={<FormattedMessage id="user.name"/>} 
                                dataIndex={["info", "name"]}
                                key="name"
                                width={90}
                                ellipsis
                                render={
                                    (value, record: any) => (
                                        <span>
                                            {(value !== undefined && value !== null && value !== "") ? value :"-"}
                                        </span>
                                    )
                                }
                            />
                            <Table.Column title={<FormattedMessage id="common.email"/>} dataIndex={["info", "email"]}
                                          key="email" width={220} ellipsis/>
                            <Table.Column
                                title={
                                    <>
                                        <FormattedMessage id="common.status"/>
                                        <Tooltip
                                            trigger={["hover", "click"]}
                                            overlayInnerStyle={{width: 310, fontSize: 12, background: "#646566"}}
                                            placement="top"
                                            title={
                                                <>
                                                    <Row>
                                                        <FormattedMessage id="project.user.status.Valid"/>
                                                    </Row>
                                                    <Row>
                                                        <FormattedMessage id="project.user.status.Inactivated"/>
                                                    </Row>
                                                    <Row>
                                                        <FormattedMessage id="project.user.status.Invalid"/>
                                                    </Row>
                                                </>
                                            }>
                                            <svg className="iconfont mouse" width={12} height={12}
                                                style={{marginLeft: 4}}>
                                                <use xlinkHref="#icon-xinxitishi"/>
                                            </svg>
                                        </Tooltip>
                                    </>
                                }
                                dataIndex={["info", "status"]}
                                key="status"
                                ellipsis
                                width={100}
                                render={(value,record:any) => (renderStatus(record))}
                            />
                            <Table.Column title={<FormattedMessage id="common.role"/>}
                                          dataIndex="roles"
                                          key="roles"
                                          align="center"
                                          ellipsis
                                          width={70}
                                          render={
                                              (value, record: any) => (
                                                  <AuthSpan style={{ color: "#165DFF" }}
                                                        className="mouse"
                                                        onClick={() => edit(record)}
                                                  >
                                                      {record.roles ? record.roles.filter((it:any)=> it.status === 1).length : 0}
                                                  </AuthSpan>
                                              )
                                          }
                            />
                            <Table.Column
                                title={<FormattedMessage id="common.site"/>}
                                dataIndex="sites"
                                key="sites"
                                align="center"
                                width={70}
                                render={
                                    (value, record: any) => (
                                            <AuthSpan style={{color: "#165DFF"}}
                                                  className="mouse"
                                                  onClick={() => user_site.current.show(record)}
                                            >
                                            {record.sites ? record.sites.length : 0}
                                            </AuthSpan>
                                    )
                                }
                            />
                            <Table.Column
                                title={<FormattedMessage id="common.depot"/>}
                                dataIndex="depots"
                                key="depots"
                                align="center"
                                width={80}
                                render={
                                    (value, record: any) => (
                                        <AuthSpan style={{ color: "#165DFF" }}
                                              className="mouse"
                                             onClick={() => user_depot.current.show(record)}
                                        >
                                            {record.depots ? record.depots.length : 0}
                                        </AuthSpan>
                                    )
                                }
                            />
                            <Table.Column
                                title={<FormattedMessage id="common.created.at"/>}
                                key="createAt"
                                dataIndex="createAt"
                                width={160}
                                render={

                                    (value, record: any) => (
                                            record.settings.tz === null ?
                                                (record.createAt === undefined || record.createAt === null || record.createAt === 0 || record.createAt === "") ? '-' : (moment.unix(record.createAt).utc().add(record.settings.timeZone, "hour").format('YYYY-MM-DD HH:mm:ss')):
                                                (record.createAt === undefined || record.createAt === null || record.createAt === 0 || record.createAt === "") ? '-' : formatTimezone(record.createAt, record.settings.tz)
                                        // (record.createAt === undefined || record.createAt === null || record.createAt === 0 || record.createAt === "") ? '-' : moment.unix(record.createAt).utc().add(ctx.timeZone, "hour").format('YYYY-MM-DD HH:mm:ss')
                                    )
                                }
                            />
                            <Table.Column
                                title={<FormattedMessage id="user.app.account"/>}
                                key={"app"}
                                dataIndex={ "app"}
                                width={g.lang === "zh"?90:120}
                                fixed="right"
                                render={
                                    (value, record: any) => (
                                        <AuthSwitch
                                            size={"small"} checked={value}
                                            disabled={auth.project.status === 2 || !permissions(auth.project.permissions, "operation.build.settings.user.app") || record.unbind}
                                            loading={appUpdateLoading}
                                            onChange={(c: any) => getSwitchAppAccount(record.id, c)}
                                        />
                                    )
                                }
                            />
                            {auth.project.status === 2 ? null :
                                <Table.Column
                                    title={<FormattedMessage id="common.operation"/>}
                                    align="left"
                                    fixed="right"
                                    width={g.lang === "zh"?180:220}
                                    render={
                                        (value, record: any) => operationBut(record)
                                    }
                                />
                            }
                        </Table>
                    <Footer>
                        <PaginationView mode="SELECTABLE" selectedNumber={selectedRowKeys?.length} clearDisplay={true} refresh={refreshList} />
                    </Footer>
                    <UserSite bind={user_site} refresh={getList}/>
                    <UserDepot bind={user_depot} refresh={getList}/>
                    <EnvUserEdit bind={env_user_edit} refresh={getList}/>
                    <EnvUserBatchAdd bind={env_user_batchAdd} refresh={getList}/>
                    <EnvUserInviteAgain bind={env_user_inviteAgain} refresh={getList}/>
                    <OperationList bind={history_ref} permission={permissions(auth.project.permissions, "operation.build.settings.user.print")}/>
                </>
            }
        </>
    );
};