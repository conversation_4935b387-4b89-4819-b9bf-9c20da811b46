import { Checkbox, Col, Form, InputNumber, Row, Radio, Space, Layout, Tooltip } from "antd";
import React, { useState,  } from "react";
import { useNotice } from "./context";
import { Title as CustomTitle } from "../../../../components/title";
import {
    dispensingContentEnum,
    dispensingStateEnum,
    eamilBodyContentEnum,
    orderSceneEnum,
    eamilProjectBodyContentEnum,
    mailOrderContentEnum,
    orderIsolationEnum,
    randomContentEnum,
    medicineAlarmEnum, subjectUpdateStateEnum, subjectScreenStateEnum,
} from "../../../../data/data";
import { EditReceivers } from "./edit-receivers";
import {EmailUpload} from "./EmailUpload";
import {useGlobal} from "../../../../context/global";
import {downloadNoticeTemplate} from "../../../../api/notice";
import { useFetch } from "hooks/request";
import { EditExclusiveReceiversImport } from "./edit-exclusive-receivers-import";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";

export const RoleList = (props: {
    keys: string;
    name: any;
    refresh: any;
    disabled: boolean;
}) => {
    const ctx = useNotice();
    const intl = useTranslation();
    const { formatMessage } = intl

    const changeAlarmState = (v: any) => {
        if (v?.find((it: any) => it === "order.forecast_title")) {
            ctx.setShowForecast(true)
        } else {
            ctx.setShowForecast(false)
        }
    }

    const g = useGlobal();
    const emailUpload = React.useRef<any>(null);
    
    const { runAsync: downloadNoticeTemplateRun } = useFetch(downloadNoticeTemplate, { manual: true });


    const numberValidator = {
        validator: (rule: any, value: any, callback: any) => {
            const reg = /^(\d+|(\d+~\d+))(,(\d+|(\d+~\d+)))*$/;
            if (value < 1) {
                return Promise.reject(
                    formatMessage({ id: "input.error.common" })
                );
            }
            return Promise.resolve();
        },
    };
    return (
        <React.Fragment>
            <Form form={ctx.form} name={props.name} style={{marginRight: 24 }}>
                <>
                {(props.keys === "notice.basic.settings") && (
                            <>
                                <CustomTitle
                                    name={intl.formatMessage({
                                        id: "common.email.language", allowComponent: true
                                    })}
                                ></CustomTitle>
                                <Form.Item 
                                    name={"automatic"} 
                                    label={formatMessage({ id: 'common.task.automatic', allowComponent:  true })}
                                    style={{marginTop: 20}}
                                    rules={[{required: !props.disabled?true:false, message: intl.formatMessage({ id: 'form.required' })}]}
                                >
                                    <Checkbox.Group
                                        className="full-width"
                                        disabled={props.disabled}
                                    >
                                        <Space >
                                            <Col
                                                style={{ marginRight: 24 }}
                                            >
                                                <Checkbox value={1}>
                                                    {
                                                        intl.formatMessage({id: "common.email.language.zh"})
                                                    }
                                                </Checkbox>
                                            </Col>
                                            <Col
                                                style={{ marginRight: 24 }}
                                            >
                                                <Checkbox value={2}>
                                                    {
                                                        intl.formatMessage({id: "common.email.language.en"})
                                                    }
                                                </Checkbox>
                                            </Col>
                                        </Space>

                                    </Checkbox.Group>
                                </Form.Item>
                                <Form.Item 
                                    name={"manual"} 
                                    label={formatMessage({ id: 'common.task.manual', allowComponent: true })}
                                    style={{marginTop: 20}}
                                    rules={[{required: !props.disabled?true:false, message: formatMessage({ id: 'form.required' })}]}
                                >
                                    <Checkbox.Group 
                                        className="full-width" 
                                        disabled={props.disabled}
                                    >
                                        <Space >
                                            <Col
                                                style={{ marginRight: 24 }}
                                            >
                                                <Checkbox value={1}>
                                                    {
                                                        intl.formatMessage({id: "common.email.language.zh"})
                                                    }
                                                </Checkbox>
                                            </Col>
                                            <Col
                                                style={{ marginRight: 24 }}
                                            >
                                                <Checkbox value={2}>
                                                    {
                                                        intl.formatMessage({id: "common.email.language.en"})
                                                    }
                                                </Checkbox>
                                            </Col>
                                        </Space>

                                    </Checkbox.Group>
                                </Form.Item>
                            </>
                        )}
                    {(props.keys === "notice.subject.random" ||
                        props.keys === "notice.subject.signOut" ||
                        props.keys === "notice.subject.replace") && (
                            <>
                                <CustomTitle
                                    name={intl.formatMessage({
                                        id: "notice.config.content",
                                    })}
                                ></CustomTitle>
                                <Form.Item name={props.name + "Content"} >
                                    <Checkbox.Group className="full-width">
                                        {
                                            <Row>
                                                {randomContentEnum.map((it) => (
                                                    <Col
                                                        key={it.value}
                                                        style={{ marginRight: 24 }}
                                                    >
                                                        <Checkbox
                                                            disabled={
                                                                props.disabled
                                                            }
                                                            value={it.value}
                                                        >
                                                            {it.label}
                                                        </Checkbox>
                                                    </Col>
                                                ))}
                                            </Row>
                                        }
                                    </Checkbox.Group>
                                </Form.Item>
                            </>
                        )}
                    {(props.keys === "notice.subject.add" ||
                        props.keys === "notice.subject.screen" ||
                        props.keys === "notice.subject.unblinding" ||
                        props.keys === "notice.medicine.un_provide_date" ||
                        props.keys === "notice.medicine.alarm") && (
                            <>
                                <CustomTitle
                                    name={intl.formatMessage({
                                        id: "notice.config.content",
                                    })}
                                ></CustomTitle>
                                <Form.Item name={props.name + "Content"}>
                                    <Checkbox.Group className="full-width">
                                        {
                                            <Row>
                                                {eamilBodyContentEnum.map((it) => (
                                                    <Col
                                                        key={it.value}
                                                        style={{ marginRight: 24 }}
                                                    >
                                                        <Checkbox
                                                            disabled={
                                                                props.disabled
                                                            }
                                                            value={it.value}
                                                        >
                                                            {it.label}
                                                        </Checkbox>
                                                    </Col>
                                                ))}
                                            </Row>
                                        }
                                    </Checkbox.Group>
                                </Form.Item>
                            </>
                        )}
                    {(
                        props.keys === "notice.medicine.order"
                    ) && (
                            <>
                                <CustomTitle
                                    name={intl.formatMessage({
                                        id: "notice.config.content",
                                    })}
                                ></CustomTitle>
                                <Form.Item name={props.name + "Content"}>
                                    <Checkbox.Group className="full-width">
                                        {
                                            <Row>
                                                {mailOrderContentEnum.map(
                                                    (it) => (
                                                        <Col
                                                            key={it.value}
                                                            style={{
                                                                marginRight: 24,
                                                            }}
                                                        >
                                                            <Checkbox
                                                                disabled={
                                                                    props.disabled
                                                                }
                                                                value={it.value}
                                                            >
                                                                {it.label}
                                                            </Checkbox>
                                                        </Col>
                                                    )
                                                )}
                                            </Row>
                                        }
                                    </Checkbox.Group>
                                </Form.Item>
                            </>
                        )}
                    {(props.keys === "notice.subject.alarm" ||
                        props.keys === "notice.medicine.isolation" ||
                        //props.keys === "notice.medicine.order" ||
                        props.keys === "notice.medicine.reminder" ||
                        props.keys === "notice.storehouse.alarm" ||
                        props.keys === "notice.order.timeout" ||
                        props.keys === "notice.subject.alert.threshold" ||
                        props.keys === "notice.subject.medicine.alarm" ||
                        props.keys === "notice.subject.medicine.capping"
                    ) && (
                            <>
                                <CustomTitle
                                    name={intl.formatMessage({
                                        id: "notice.config.content",
                                    })}
                                ></CustomTitle>
                                <Form.Item name={props.name + "Content"}>
                                    <Checkbox.Group className="full-width">
                                        {
                                            <Row>
                                                {eamilProjectBodyContentEnum.map(
                                                    (it) => (
                                                        <Col
                                                            key={it.value}
                                                            style={{
                                                                marginRight: 24,
                                                            }}
                                                        >
                                                            <Checkbox
                                                                disabled={
                                                                    props.disabled
                                                                }
                                                                value={it.value}
                                                            >
                                                                {it.label}
                                                            </Checkbox>
                                                        </Col>
                                                    )
                                                )}
                                            </Row>
                                        }
                                    </Checkbox.Group>
                                </Form.Item>
                            </>
                        )}

                    {props.keys === "notice.subject.update" && (
                        <>
                            <CustomTitle
                                name={intl.formatMessage({
                                    id: "notice.config.content",
                                })}
                            ></CustomTitle>
                            <Form.Item name={props.name + "Content"}>
                                <Checkbox.Group className="full-width">
                                    {
                                        <Row>
                                            {dispensingContentEnum.map((it) => (
                                                <Col
                                                    key={it.value}
                                                    style={{ marginRight: 24 }}
                                                >
                                                    <Checkbox
                                                        disabled={
                                                            props.disabled
                                                        }
                                                        value={it.value}
                                                    >
                                                        {it.label}
                                                    </Checkbox>
                                                </Col>
                                            ))}
                                        </Row>
                                    }
                                </Checkbox.Group>
                            </Form.Item>
                            <CustomTitle
                                name={intl.formatMessage({
                                    id: "notice.config.state",
                                })}
                            ></CustomTitle>
                            <Form.Item name={"state"}>
                                <Checkbox.Group className="full-width">
                                    {
                                        <Row gutter={24}>
                                            {subjectUpdateStateEnum.map((it) => (
                                                <Col key={it.value}>
                                                    <Checkbox
                                                        disabled={
                                                            props.disabled
                                                        }
                                                        value={it.value}
                                                    >
                                                        {it.label}
                                                    </Checkbox>
                                                </Col>
                                            ))}
                                        </Row>
                                    }
                                </Checkbox.Group>
                            </Form.Item>
                        </>
                    )}

                    {props.keys === "notice.subject.screen" && (
                        <>
                            <CustomTitle
                                name={intl.formatMessage({
                                    id: "notice.config.state",
                                })}
                            ></CustomTitle>
                            <Form.Item name={"state"}>
                                <Checkbox.Group className="full-width">
                                    {
                                        <Row gutter={24}>
                                            {subjectScreenStateEnum.map((it) => (
                                                <Col key={it.value}>
                                                    <Checkbox
                                                        disabled={
                                                            props.disabled
                                                        }
                                                        value={it.value}
                                                    >
                                                        {it.label}
                                                    </Checkbox>
                                                </Col>
                                            ))}
                                        </Row>
                                    }
                                </Checkbox.Group>
                            </Form.Item>
                        </>
                    )}

                    {props.keys === "notice.medicine.alarm" && (
                        <>
                            <CustomTitle
                                name={intl.formatMessage({
                                    id: "notice.config.state",
                                })}
                            ></CustomTitle>
                            <div style={{ display: "flex" }}>
                                <Form.Item name={"state"}>
                                    <Checkbox.Group className="full-width" onChange={
                                        (v) => {
                                            changeAlarmState(v)
                                        }
                                    }>
                                        <Row gutter={24}>
                                            {medicineAlarmEnum.map((it) => (
                                                <Col key={it.value}>
                                                    <Checkbox
                                                        disabled={
                                                            props.disabled
                                                        }
                                                        value={it.value}
                                                    >
                                                        {it.label}
                                                    </Checkbox>
                                                </Col>
                                            ))}
                                        </Row>
                                    </Checkbox.Group>
                                </Form.Item>
                                {
                                    ctx.showForecast &&
                                    <Form.Item
                                        style={{ marginLeft: 12 }}
                                        label={intl.formatMessage({
                                            id: "notice.alarm.scene.forecast.time",
                                        })}
                                        name="forecastTime"
                                        rules={[numberValidator]}
                                    >
                                        <InputNumber
                                            style={{ width: 120 }}
                                            placeholder={formatMessage({ id: "common.required.prefix" }) as string}
                                            precision={0}
                                            min={0}
                                            step={1}
                                            disabled={props.disabled}
                                        />
                                    </Form.Item>
                                }

                            </div>
                        </>
                    )}

                    {props.keys === "notice.subject.dispensing" && (
                        <>
                            <CustomTitle
                                name={intl.formatMessage({
                                    id: "notice.config.content",
                                })}
                            ></CustomTitle>
                            <Form.Item name={props.name + "Content"}>
                                <Checkbox.Group className="full-width">
                                    {
                                        <Row>
                                            {dispensingContentEnum.map((it) => (
                                                <Col
                                                    key={it.value}
                                                    style={{ marginRight: 24 }}
                                                >
                                                    <Checkbox
                                                        disabled={
                                                            props.disabled
                                                        }
                                                        value={it.value}
                                                    >
                                                        {it.label}
                                                    </Checkbox>
                                                </Col>
                                            ))}
                                        </Row>
                                    }
                                </Checkbox.Group>
                            </Form.Item>
                            <CustomTitle
                                name={intl.formatMessage({
                                    id: "notice.config.state",
                                })}
                            ></CustomTitle>
                            <Form.Item name={"state"}>
                                <Checkbox.Group className="full-width">
                                    {
                                        <Row gutter={24}>
                                            {dispensingStateEnum.map((it) => (
                                                <Col key={it.value}>
                                                    <Checkbox
                                                        disabled={
                                                            props.disabled
                                                        }
                                                        value={it.value}
                                                    >
                                                        {it.label}
                                                    </Checkbox>
                                                </Col>
                                            ))}
                                        </Row>
                                    }
                                </Checkbox.Group>
                            </Form.Item>
                        </>
                    )}
                    {props.keys === "notice.medicine.isolation" && (
                        <>
                            <CustomTitle
                                name={intl.formatMessage({
                                    id: "notice.config.state",
                                })}
                            ></CustomTitle>
                            <Form.Item name={"state"}>
                                <Checkbox.Group className="full-width">
                                    {
                                        <Row gutter={24}>
                                            {orderIsolationEnum.map((it) => (
                                                <Col key={it.value}>
                                                    <Checkbox
                                                        disabled={
                                                            props.disabled
                                                        }
                                                        value={it.value}
                                                    >
                                                        {it.label}
                                                    </Checkbox>
                                                </Col>
                                            ))}
                                        </Row>
                                    }
                                </Checkbox.Group>
                            </Form.Item>
                        </>
                    )}
                    {props.keys === "notice.medicine.order" && (
                        <>
                            <CustomTitle
                                name={intl.formatMessage({
                                    id: "notice.config.state",
                                })}
                            ></CustomTitle>
                            <Form.Item name={"state"}>
                                <Checkbox.Group className="full-width">
                                    <Row gutter={24}>
                                        {orderSceneEnum.map((it) => (
                                            <Col
                                                style={{ marginBottom: 12 }}
                                                key={it.value}
                                            >
                                                <Checkbox
                                                    disabled={props.disabled}
                                                    value={it.value}
                                                >
                                                    {it.label}
                                                </Checkbox>
                                            </Col>
                                        ))}
                                    </Row>
                                </Checkbox.Group>
                            </Form.Item>
                        </>
                    )}
                    {props.name === "rolesTimeout" ? (
                        <>
                            <CustomTitle
                                name={intl.formatMessage({
                                    id: "notice.config.state",
                                })}
                            ></CustomTitle>
                            <Row>
                                <Col>
                                    <Form.Item
                                        className="mar-ver-5"
                                        label={intl.formatMessage({
                                            id: "order.timeout.days",
                                        })}
                                        name="timeoutDays"
                                    >
                                        <InputNumber
                                            style={{ width: 120 }}
                                            precision={0}
                                            min={0}
                                            step={1}
                                            disabled={props.disabled}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col style={{ marginLeft: 24 }}>
                                    <Form.Item
                                        className="mar-ver-5"
                                        label={intl.formatMessage({
                                            id: "order.send.days",
                                        })}
                                        name="sendDays"
                                    >
                                        <InputNumber
                                            style={{ width: 120 }}
                                            precision={0}
                                            min={0}
                                            step={1}
                                            disabled={props.disabled}
                                        />
                                    </Form.Item>
                                </Col>
                            </Row>
                        </>
                    ) : null}
                    {
                        props.keys !== "notice.basic.settings" &&
                        <div>
                            <CustomTitle
                                name={intl.formatMessage({ id: "common.role" })}
                            ></CustomTitle>
                            <Row>
                                <Col>
                                    <Form.Item
                                        className="mar-ver-5"
                                        // label={intl.formatMessage({ id: 'common.role' })}
                                        name={props.name}
                                    >
                                        <Checkbox.Group className="full-width">
                                            <Row gutter={[0, 20]}>
                                                {ctx.roles.map((it) => (
                                                    <Col
                                                        xs={24}
                                                        sm={12}
                                                        md={8}
                                                        lg={8}
                                                        key={it.id}
                                                    >
                                                        <Checkbox
                                                            disabled={props.disabled}
                                                            value={it.id}
                                                        >
                                                            {it.name}
                                                        </Checkbox>
                                                    </Col>
                                                ))}
                                            </Row>
                                        </Checkbox.Group>
                                    </Form.Item>
                                </Col>
                            </Row>
                        </div>
                    }
                    {
                        props.keys !== "notice.basic.settings" &&
                        <div style={{marginTop: 20}}>
                            <CustomTitle
                                name={intl.formatMessage({ id: "notice.exclude_recipient_list.email" })}
                            ></CustomTitle>
                            <Row style={{marginTop:8,  marginRight: 24 }} >
                                <Col span={24} >
                                    <Form.Item
                                        className="mar-ver-5"
                                        // label={intl.formatMessage({ id: 'common.email' })}
                                        label={
                                            <>
                                                <FormattedMessage id="common.email"/>
                                                <Tooltip
                                                    trigger={["hover", "click"]}
                                                    overlayInnerStyle={{fontSize: 12, background: "#646566"}}
                                                    placement="top"
                                                    title={
                                                        <>
                                                            <Row>
                                                                <FormattedMessage id="notice.exclude_recipient_list.email.tip3"/>
                                                            </Row>
                                                        </>
                                                    }>
                                                    <svg className="iconfont mouse" width={12} height={12}
                                                        style={{marginLeft: 4}}>
                                                        <use xlinkHref="#icon-xinxitishi"/>
                                                    </svg>
                                                </Tooltip>
                                            </>
                                        }
                                        name="excludeRecipientList"
                                    >
                                        {
                                            ((ctx.emailList !== undefined && ctx.emailList !== null && ctx.emailList.length > 0) || !props.disabled)?
                                            <EditReceivers />:"-"
                                        }
                                    </Form.Item>
                                </Col>
                                {
                                    !props.disabled?
                                    <Col style={{marginTop: 12, marginLeft: 58}}>
                                        <EditExclusiveReceiversImport />
                                        {/* <EmailUpload
                                            bind={emailUpload}
                                            tips={
                                                <span style={{fontSize: 12, color: '#ADB2BA', marginLeft: 12}} onClick={e => e.stopPropagation()}>
                                                    <span>{formatMessage({id: 'notice.exclude_recipient_list.email.batch.tip'})}{g.lang === 'zh' ? ', ' : ' '}</span><a onClick={() => downloadNoticeTemplateRun().then()}>{formatMessage({ id: 'common.download.template' })}</a>{g.lang === 'zh' ? '。' : '.'}
                                                </span>
                                            }
                                            accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                                            width={485}
                                        /> */}
                                    </Col>:null
                                }
                            </Row>
                        </div>
                    }
                </>
            </Form>
        </React.Fragment>
    );
};
