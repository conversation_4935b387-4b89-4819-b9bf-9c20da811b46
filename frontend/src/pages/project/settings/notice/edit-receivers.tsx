import "@yaireo/tagify/dist/tagify.css";
import { useIntl } from "react-intl";
import { useEmails } from "../notice/hooks";
// import Tags from "@yaireo/tagify/dist/react.tagify";
import { InputTag, Tag } from "@arco-design/web-react";
import { message } from "antd";
import '@arco-design/web-react/dist/css/arco.css'; // 引入样式

export const EditReceivers = () => {
    const intl = useIntl();

    const ctx = useEmails();

    const options = ['arcoblue', 'orange', 'lime', '<EMAIL>'];

    // const tagRender = (props: any) => {
    //     const { label, closable, onClose, value } = props;
    
    //     // 假设 value 是一个对象，并且包含 type 属性
    //     const isType2 = value.type === '<EMAIL>';
    
    //     return (
    //         <span 
    //             style={{ 
    //                 // backgroundColor: '#e0f7fa', 
    //                 // padding: '5px 10px', 
    //                 // borderRadius: '3px', 
    //                 // margin: '2px', 
    //                 textDecoration: isType2 ? 'line-through' : 'none' // 根据 type 条件添加中划线
    //             }}
    //         >
    //             {label}
    //             {closable && (
    //                 <span 
    //                     onClick={onClose} 
    //                     style={{ marginLeft: '5px', cursor: 'pointer' }}
    //                 >
    //                     &times; {/* 关闭图标 */}
    //                 </span>
    //             )}
    //         </span>
    //     );
    // };

    function tagRender(props: any) {
        const { label, value, closable, onClose } = props;
        const isLine = ctx.unbindEmailList?.indexOf(label) !== -1; // true
        return (
          <Tag
            // color={options.indexOf(value) > -1 ? value : 'gray'}
            closable={closable}
            onClose={onClose}
            style={{ backgroundColor: ctx.disabled?"#f5f6f7":"#f5f5f5", color: "#2E323A", border: "1px solid #DDDEDF", marginLeft: "4px" }}
          >
            <span style={{
                textDecoration: isLine?'line-through':'none',
            }}>
                {label}
            </span>
          </Tag>
        );
    }

    return (
        <InputTag
            allowClear
            style={{backgroundColor: ctx.disabled?"#ffffff":"#f5f5f5", border: "1px solid #E3E4E6"}}
            placeholder={intl.formatMessage({ id: "user.batch.add.email.required" })}
            className="w-full !bg-white !border !border-gray-500/20 !hover:border-blue-600"
            value={ctx.emailList}
            saveOnBlur
            disabled={ctx.disabled?false:true}
            // onChange={(v: any) => ctx.setEmailList(v)}
            onChange={(v: any) => {
                const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                // 过滤出符合邮箱规则的邮箱
                const validEmails = v.filter((email: any) => emailRegex.test(email.trim()));
                const trimmedArr = validEmails.map((item: any) => item.trim());
                const uniqueArray = [...new Set(trimmedArr)];
                ctx.setEmailList(uniqueArray);
            }}
            renderTag={tagRender}
            // validate={(v: any) => {
            //     if (v) {
            //         const regex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            //         const ok = regex.test(v);
            //         if (!ok) {
            //             message.error(intl.formatMessage({ id: "notice.exclude_recipient_list.email.err" }));
            //             // v = null;
            //         }
            //         return ok;
            //     }
            //     return false;
            // }}
        />
        // <Tags
        //     // style={{ width: "100%" }}
        //     className="custom-tagify"
        //     settings={{
        //         pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        //         // pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        //         delimiters: ",|;| ",
        //     }}
        //     value={ctx.emailList}
        //     onChange={(e: any) => ctx.setEmailList(e.detail.tagify.getCleanValue().map((it: any) => it.value))}
        //     placeholder={intl.formatMessage({ id: "user.batch.add.email.required" })}
        // />
    );
};
