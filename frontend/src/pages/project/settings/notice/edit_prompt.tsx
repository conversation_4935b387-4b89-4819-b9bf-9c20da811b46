import React from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {
    Button,
    Checkbox,
    Col,
    Form,
    Input,
    Table,
    message as antd_message,
    Modal,
    Popover,
    Row,
    Spin,
    Tag,
    Tooltip,
    Select,
    Radio,
    Space,
    message, 
} from "antd";
import {useFetch} from "../../../../hooks/request";
import {useSafeState} from "ahooks";
import {useGlobal} from "../../../../context/global";
import {useNotice} from "./context";
import {setNoticeConfig} from "../../../../api/notice";
import {useAuth} from "../../../../context/auth";

export const EditPrompt = (props :any) => {

    const auth = useAuth();
    const ctx = useNotice();
    const intl = useIntl();
    const {formatMessage} = intl;
    const g = useGlobal();
    

    const [visible, setVisible] = useSafeState<any>(false);
    const [customerId, setCustomerId] = useSafeState<any>(null);
    const [projectId, setProjectId] = useSafeState<any>(null);
    const [envId, setEnvId] = useSafeState<any>(null);
    const [key, setKey] = useSafeState<any>(null);
    const [roles, setRoles] = useSafeState<any>([]);
    const [automatic, setAutomatic] = useSafeState<any>(null);
    const [manual, setManual] = useSafeState<any>(null);
    const [timeoutDays, setTimeoutDays] = useSafeState<any>(null);
    const [forecastTime, setForecastTime] = useSafeState<any>(null);
    const [sendDays, setSendDays] = useSafeState<any>(null);
    const [state, setState] = useSafeState<any>([]);
    const [fieldsConfig, setFieldsConfig] = useSafeState<any>([]);
    const [excludeRecipientList, setExcludeRecipientList] = useSafeState<any>([]);
    const [data, setData] = useSafeState<any>([]);

    const show = (
        customerId: any,
        projectId: any,
        envId: any,
        key: any,
        roles: any,
        automatic: any,
        manual: any,
        timeoutDays: any,
        forecastTime: any,
        sendDays: any,
        state: any,
        fieldsConfig: any,
        excludeRecipientList: any,
        data: any
    ) => {
        setVisible(true);
        setCustomerId(customerId);
        setProjectId(projectId);
        setEnvId(envId);
        setKey(key);
        setRoles(roles);
        setAutomatic(automatic);
        setManual(manual);
        setTimeoutDays(timeoutDays);
        setForecastTime(forecastTime);
        setSendDays(sendDays);
        setState(state);
        setFieldsConfig(fieldsConfig);
        setExcludeRecipientList(excludeRecipientList);
        setData(data);
    };

    const hide = () => {
        setVisible(false);
    };

    const refresh = () => {
        setVisible(false);
        props.refresh();
    };

    // const { run, loading: saveLoading } = useFetch(setNoticeConfig, {
    //     manual: true,
    //     onSuccess: (result: any, params) => {
    //         message.success(result.msg);
    //         refresh();
    //     },
    //     onError: (error) => {
    //         message.error(error.message);
    //     },
    // });

    const {runAsync:run, loading:saveLoading} = useFetch(setNoticeConfig, {manual: true});


    const save = () => {
        // 使用filter方法过滤出不存在于数组B中的元素
        const availableEmail = excludeRecipientList.filter((item: any) => !data.includes(item));
        run({
            customerId: customerId,
            projectId: projectId,
            envId: envId,
            key: key,
            roles: roles,
            automatic: automatic,
            manual: manual,
            timeoutDays: timeoutDays,
            forecastTime: forecastTime,
            sendDays: sendDays,
            state: state,
            fieldsConfig: fieldsConfig,
            excludeRecipientList: availableEmail,
            unbindEmailList:[],
        }).then(
            (result:any) => {
                message.success(result.msg);
                refresh();
            }
        )

    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    const language = [
        {label:formatMessage({id:"common.email.language.zh"}), value:"zh"},
        {label:formatMessage({id:"common.email.language.en"}), value:"en"},
    ]

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 5: 8 },
        },

    }

    return (
        <React.Fragment>
            <Modal
                width={500}
                title={<FormattedMessage id={"common.tips"} />}
                visible={visible}
                onCancel={hide}
                // cancelText={formatMessage({id: 'common.back.revise'})}
                maskClosable={false}
                centered
                destroyOnClose
                className='custom-small-modal'
                // okText={emailList.length !== data.length?formatMessage({id: 'common.ignore.continue'}):""}
                // okButtonProps={{loading: addBatchUserLoading}}
                // onOk={save}
                footer={
                    <Row style={{ paddingLeft: 24 }}>
                        <Col span={24} style={{ textAlign: 'right' }}>
                        <Button onClick={hide}>{formatMessage({ id: "common.back.revise" })}</Button>
                        {
                            excludeRecipientList.length !== data.length?
                            <Button 
                                type={"primary"} 
                                onClick={save}
                                loading={saveLoading}
                            >
                                {formatMessage({ id: "common.ignore.continue" })}
                            </Button>:null
                        }
                      </Col>
                    </Row>
                }
            >
                <Row style={{ backgroundColor: "#165DFF1A", height: (excludeRecipientList.length !== data.length && g.lang === "en")?54:32, marginBottom: 16, paddingLeft: 12, paddingBottom: 12 }}>
                    <Col style={{ width: 24 }}>
                        <svg className="iconfont" width={20} height={20} style={{ marginRight: 12, marginTop: 8 }} fill={"#165DFF"}>
                            <use xlinkHref="#icon-xinxitishi1" ></use>
                        </svg>
                    </Col>
                    <Col style={{ width: "calc(100% - 50px)", marginTop: 6 }}>
                        {
                            excludeRecipientList.length === data.length?
                            formatMessage({ id: "notice.exclude_recipient_list.email.tip1" }):
                            formatMessage({ id: "notice.exclude_recipient_list.email.tip2" })
                        }
                    </Col>
                </Row>
                <Table
                    //style={{ marginTop: 16 }}
                    dataSource={data}
                    pagination={false}
                    scroll={{
                        y:
                            data.length < 20
                                ? "calc(100vh - 222px)"
                                : "calc(100vh - 262px)",
                    }}
                    rowKey={(record: any) => record.id}
                >
                    <Table.Column
                        title={
                            <FormattedMessage id="common.serial" />
                        }
                        dataIndex="#"
                        key="#"
                        width={60}
                        render={(text, record, index) =>
                            index + 1
                        }
                    />
                    <Table.Column
                        title={
                            <FormattedMessage id="common.email" />
                        }
                        // dataIndex="email"
                        // key="email"
                        ellipsis
                    />
                </Table>
            </Modal>
        </React.Fragment>
    )
};