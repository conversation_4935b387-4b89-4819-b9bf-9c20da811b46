import React, {useEffect, useMemo} from "react";
import {BackTop, Layout, message} from "antd";
import { Sider } from "./sider";
import { Header } from "./header";
import { Content as Body } from "pages/main/layout/content";
import { LayoutContext } from "./context";
import styled from "@emotion/styled";
import { useMount, useSafeState } from "ahooks";
import { useFetch } from "../../../hooks/request";
import { useAuth } from "../../../context/auth";
import { Outlet } from "react-router-dom";
import { getAuth } from "../../../api/check";
import { CheckRoleLearn } from "./check_role_learn";
import { ProjectContextProvider } from "../context";
import {getLocales} from "../../common/multilingual/util";
import {IntlProvider} from "react-intl";
import {getList, getTranslateMap} from "../../../api/multi_language";
import _ from "lodash";
import {getDefaultLocale} from "../../../data/data";
import {useGlobal} from "../../../context/global";

export const Index = () => {
    const g = useGlobal()
    const auth = useAuth()
    const [collapsed, setCollapsed] = useSafeState(false);
    const { runAsync: run_get_auth } = useFetch(getAuth, { manual: true });
    const list = () => {
        run_get_auth({}).then(
            (response) => {
                let result = response.data
                if (result != null) {
                    auth.setSystemCheck(result.auth)
                }
            }
        )
    }
    useMount(() => list())

    const toggle = () => {
        setCollapsed(!collapsed);
    };

    const [langOptions, setLangOptions] = useSafeState([])
    const [customMessage, setCustomMessage] = useSafeState({})

    const { run: getCustomerLanguage } = useFetch(getList,{
        manual: true,
        onSuccess: (resp) => {
            const options = _.uniqBy(resp.data.filter((it) => !!it.status), 'code').map((it) => ({
                label: it.language,
                value: it.code,
                projectId: it.projectId,
                languageId: it.id,
            }))
            g.setProjectLanguageOptions(options)
            setLangOptions(options)
        },
        onError: () => {
            g.setProjectLanguageOptions([])
            setLangOptions([])
        }
    })
    const { run: getTranslate } = useFetch(getTranslateMap, {
        manual: true,
        onSuccess: (resp) => {
            setCustomMessage(resp.data)
        },
        onError: () => {
            setCustomMessage({})
            message.error("语言切换失败")
        }
    })

    useEffect(() => {
        getCustomerLanguage({customerId: auth.customerId, projectId: auth.project?.id})
    }, [])

    useEffect(() => {
        const customLang = langOptions.find(it => it.value === g.lang)
        if (!!customLang?.languageId) {
            // 获取翻译
            getTranslate( { customerId:  auth.customerId, projectId: auth.project?.id }, {
                languageId: customLang?.languageId,
            })
        } else {
            setCustomMessage({})
        }
    }, [g.lang])

    const defaultLocale = useMemo(() => {
        return getDefaultLocale(g.lang)
    }, [g.lang])

    return (
        <LayoutContext.Provider value={{ collapsed, setCollapsed, toggle }}>
            <IntlProvider messages={getLocales(customMessage, defaultLocale)} locale={defaultLocale} defaultLocale="en">
                <ProjectContextProvider>
                    <StyledLayout>
                        <Layout>
                            <Sider />
                            <Layout>
                                <Header />
                                <StyledContent id="content" className="right">
                                    <BackTop
                                        style={{ position: 'fixed', right: '25px', bottom: '60px' }}
                                        visibilityHeight={200}
                                        target={() => document.getElementById('content')}
                                    />
                                    <Body>
                                        <Outlet />
                                    </Body>
                                </StyledContent>
                            </Layout>
                        </Layout>
                    </StyledLayout>
                    <CheckRoleLearn />
                </ProjectContextProvider>
            </IntlProvider>
        </LayoutContext.Provider>
    );

};

const StyledLayout = styled.div`
    width: 100%;
    height: 100vh;
    overflow: hidden;
`

const StyledContent = styled.div`
    height: calc(100vh - 50px);
    overflow-y: auto;
`