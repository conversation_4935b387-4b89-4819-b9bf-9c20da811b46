import React from "react"
import {<PERSON>, <PERSON>, Modal, Row, Tree} from "antd";
import {useIntl} from "react-intl";
import {QuestionCircleFilled} from "@ant-design/icons";
import useSafeState from "ahooks/lib/useSafeState";
import {MenuPermission} from "../../../types/menu";
import {permissionsDisable} from "../../../data/data";

export const ProjectRoleAuthority = (props:any) => {
    const intl = useIntl();
    const {formatMessage} = intl;
    const [visible, setVisible] = useSafeState<any>(false);
    const [record, setRecord] = useSafeState<any>(null);
    const [menu, setMenu] = useSafeState<any>(null);
    const [tree, setTree] = useSafeState<any>([]);
    const [permissionsSet, setPermissionsSet] = useSafeState<any>(null);
    const [disableData, setDisableData] = useSafeState<any>(null);
    const [researchAttribute, setResearchAttribute] = useSafeState<any>(null);
    const [permissions, setPermissions] = useSafeState<any[]>([]);
    const [expandedPermission, setExpandedPermission] = useSafeState<any[]>([]);
    const [authoritys, setAuthoritys] = useSafeState<any>([]);
    const [halfCheckedKeys, setHalfCheckedKeys] = useSafeState<string[]>([]);
    const show = (record: any, menu: any, rolesPool: any, projectData: any) => {
        setVisible(true);
        setRecord(record);
        setMenu(menu);
        setResearchAttribute(projectData.info.researchAttribute);
        let tree = getChildren(menu, record, rolesPool, projectData.info.researchAttribute);
        setTree([...tree]);
        setPermissionsSet(new Set(record.permissions));

        let role = rolesPool.find((it: any) => it.name === record.name)
        if (!role) {
            setDisableData([]);
            return
        };
        let disableData = permissionsDisable.find((it: any) => it.type === role.type);
        if (disableData) {
            setDisableData(disableData.disables);
        } else {
            setDisableData([]);
        }

        permissionTree(menu, "menu.report.randomizeReport");
    };

    const hide = () => {
        setVisible(false);
        setRecord(null);
    };

    function getChildren(n: any, currentRole: any, rolesPool: any, researchAttribute: any) {
        let arr: any[] = [];
        n.forEach(
            (value: MenuPermission) => {
                let menuItem: any = {}
                let role = rolesPool.find((it: any) => it.name === currentRole.name)
                if (value.researchAttribute.includes(researchAttribute)) {
                    if (!role ||
                        value.system === 0 ||
                        (value.system === 2 && [1, 2, 3, 6].includes(role?.type)) ||
                        (value.system === 1 && [6].includes(role?.type))) {
                        if (value.text) {
                            menuItem["title"] = formatMessage({id: value.text})
                            menuItem["key"] = value.text
                            menuItem["selectable"] = !!value.permissions
                        }
                        if (value.children) {
                            menuItem["children"] = getChildren(value.children, currentRole, rolesPool, researchAttribute)
                        }
                        arr.push(menuItem)
                    }
                }
            }
        );
        return arr
    }

    const onSelect = (data: any) => {
        let e: any = data[0];
        permissionTree(menu, e);
    };

    const handlePermission = (permission: any) => {
        let permissions: any[] = []
        let expandedPermission: any[] = []
        let authorityList: string[] = [];

        permission.forEach(
            (value: any) => {
                let item: any = {}
                item["title"] = formatMessage({id: value})
                item["key"] = value
                item["disabled"] = true
                permissions.push(item)
                if (permissionsSet.has(value)) {
                    authorityList.push(formatMessage({id: value}));
                    expandedPermission.push(value)
                }
            }
        )
        setAuthoritys(authorityList);

        // 计算父节点状态
        const total = permission.length;
        const checkedCount = expandedPermission.length;
        let newHalfCheckedKeys: string[] = [];

        // 当所有子节点都被选中时，将父节点加入全选数组
        if (checkedCount === total && total > 0) {
            expandedPermission.push("all");
        }
        // 当部分子节点被选中时，将父节点加入半选数组
        else if (checkedCount > 0 && checkedCount < total) {
            newHalfCheckedKeys = ["all"];
        }

        setHalfCheckedKeys(newHalfCheckedKeys);
        setExpandedPermission(expandedPermission);

        let tree: any = [{
            "title": formatMessage({id: "common.all"}),
            "key": "all",
            "disabled": true,
            "children": permissions,
        }]

        return tree
    };

    const permissionTree = (n: any[], e: any) => {
        let template = researchAttribute ===0?1:2
        n.forEach(
            (value: any) => {
                // 通用 树
                if (template ===1 && value.researchAttribute.findIndex((item: number) => item === 0) !== -1) {
                    if (value.text === e) {
                        let permissions = handlePermission(value.permissions)
                        setPermissions(permissions)
                        return
                    } else {
                        if (value.children) {
                            permissionTree(value.children, e)
                        }
                    }
                }
                // DTP 树
                if (template === 2 && value.researchAttribute.findIndex((item: number) => item === 1) !== -1) {
                    if (value.text === e) {
                        let permissions = handlePermission(value.permissions)
                        setPermissions(permissions)
                        return
                    } else {
                        if (value.children) {
                            permissionTree(value.children, e)
                        }
                    }
                }
            }
        )
        return
    };

    React.useImperativeHandle(props.bind, () => ({show}));
    return (

        record ?
            <Modal
                destroyOnClose={true}
                width={800}
                title={formatMessage({ id: 'common.permission' })}
                visible={visible}
                onCancel={hide}
                footer={null}
                centered
                maskClosable={false}
            >
                <>
                    <Row justify="center" gutter={24} style={{height: "100%"}}>
                        <Col span={12} style={{paddingRight:"0px"}}>
                            <Card title={formatMessage({id: "common.menu"})}
                                  style={{width: "374px", height: "400px",overflowY: "auto"}}
                            >
                                {
                                    tree.length>0 &&
                                    <Tree
                                        defaultSelectedKeys={['menu.report.randomizeReport']}
                                        blockNode
                                        onSelect={onSelect}
                                        treeData={tree}
                                        virtual={false}
                                        defaultExpandAll
                                    />
                                }
                            </Card>
                        </Col>
                        <Col span={12} style={{paddingLeft:"0px"}}>
                            <Card title={formatMessage({id: "role.setting.operation"})}
                                  style={{width: "374px", height: "400px",overflowY: "auto"}}>
                                {/*<ul style={{paddingLeft: 10}}>*/}
                                    {/*<li>*/}
                                        {/*aaa*/}
                                    {/*</li>*/}
                                    {/*<li>*/}
                                        {/*bbb*/}
                                    {/*</li>*/}
                                {/*</ul>*/}
                                <Tree
                                    checkedKeys={{
                                        checked: expandedPermission, // 包含全选时的父节点
                                        halfChecked: halfCheckedKeys // 包含半选时的父节点
                                    }}
                                    checkable={true}
                                    defaultExpandedKeys={["all"]}
                                    treeData={permissions}
                                    virtual={false}
                                    checkStrictly={true} // 保持父子状态独立
                                />
                            </Card>
                        </Col>
                    </Row>
                </>
            </Modal>
        :
            null
    )
}