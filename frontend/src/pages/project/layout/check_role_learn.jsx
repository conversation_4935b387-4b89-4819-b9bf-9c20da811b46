import React from 'react';

import {message} from "antd";
import ReactModal from "react-modal";
import {useFetch} from "../../../hooks/request";
import {getLearn} from "../../../api/user";
import {useIntl} from "react-intl";
import {useNavigate} from "react-router-dom";
import {useGlobal} from "../../../context/global";
import {useSafeState} from "ahooks";
import {useAuth} from "../../../context/auth";
import {useProject} from "../context";

import WelcomeImageEN from 'images/welcome_en.png'
import WelcomeImageZH from 'images/welcome_zh.png'
import WelcomeImageHomeEN from 'images/welcome_home_en.png'
import WelcomeImageHomeZH from 'images/welcome_home_zh.png'
import learn_project_optional_zh from 'images/learn_project_optional_zh.png'
import learn_project_optional_en from 'images/learn_project_optional_en.png'


export const CheckRoleLearn = () => {
    const auth = useAuth()
    const home = useProject();

    const intl = useIntl();
    const g = useGlobal()
    const {formatMessage} = intl;

    const [check, setCheck] = useSafeState(false);

    const navigate = useNavigate();

    const {runAsync: learnRunAsync} = useFetch(getLearn, {manual: true})

    const list = () => {
        //所有项目都未开启系统课程和项目课程学习
        if (sessionStorage.getItem("projectMark") == null || sessionStorage.getItem("systemMark") == null) {
            let envs = auth.project.info.need_learning_env?  auth.project.info.need_learning_env:[]
            const envsLearn = envs.find((value) => value === auth.env.name)
            home.setMustLearn(auth.project.info?.need_learning === 1)
            if (auth.project.info.connect_learning === 1 && envsLearn && sessionStorage.getItem("projectLearn") !== "0") {
                learnRunAsync({
                    "project_id": auth.project ? auth.project.id : "",
                    "env_id": auth.env ? auth.env.id : ""
                }).then(
                    (result) => {
                        sessionStorage.setItem("projectLearn", "0");
                        // 筛选返回结果里面是否有未完成的项目课程
                        sessionStorage.setItem("learn_url", result.data.url);
                        sessionStorage.setItem("learn_token", result.data.token);
                        if (result.data.courses > 0) {
                            result.data.rows.forEach((kc) => {
                                if (kc.project != null && kc.project.length > 0) {
                                    home.setLearnToken(result.data.token);
                                    home.setLearnVisible(true);
                                }
                            });
                        }
                    }
                )
            } else {
                learnRunAsync({
                    "project_id": auth.project ? auth.project.id : "",
                    "env_id": auth.env ? auth.env.id : ""
                }).then(
                    (result) => {
                        // 筛选返回结果里面是否有未完成的项目课程
                        sessionStorage.setItem("learn_url", result.data.url);
                        sessionStorage.setItem("learn_token", result.data.token);
                    }
                )
            }
        }
    }
    React.useEffect(list, []);

    const handleOK = () => {
        // 请求 learn
        sessionStorage.removeItem("completeLearning");
        if (check){
            learnRunAsync({"project_id":auth.project.id, "env_id":auth.env.id}).then(
                (result) => {
                    if (result.data.courses === 0) {
                        home.setLearnVisible(false)
                    }else{
                        if (home.learnToken !== ""){
                            window.open(sessionStorage.getItem("learn_url")+"?t="+ home.learnToken+"&lang="+g.lang)
                            return;
                        }
                        message.error(formatMessage({ id: "user.customer.unLearn" }));
                        window.open(sessionStorage.getItem("learn_url")+"?t="+sessionStorage.getItem("learn_token")+"&lang="+g.lang)
                    }
                }
            )
        }else{
            setCheck(true)
            if (home.learnToken !== ""){
                window.open(sessionStorage.getItem("learn_url")+"?t="+ home.learnToken+"&lang="+g.lang)
                return;
            }
            window.open(sessionStorage.getItem("learn_url")+"?t="+sessionStorage.getItem("learn_token")+"&lang="+g.lang)
        }

    }

    const handleCancel = () => {
        home.setLearnVisible(false)
        if (home.mustLearn){
            home.setMustLearn(true)
            home.setLearnToken("")
            auth.setProject(null)
            auth.setEnv(null)
            auth.setCohort(null)
            auth.setAttribute(null)
            navigate("/");
        }
        sessionStorage.setItem("completeLearning", "1");
        // @ts-ignore
        window.location = "";
    }



    return (
        <React.Fragment>
            <ReactModal
                ariaHideApp={false}
                isOpen={home.learnVisible}
                // onRequestClose={() => g.setWelcome(false)}
                shouldCloseOnEsc={false}
                shouldCloseOnOverlayClick={false}
                   style={
                       {
                           overlay: {
                               width: "100%",
                               height: "100%",
                               background: "rgba(0, 0, 0, 0.7)",
                               padding: 0,
                               zIndex: 1000,
                               backdropFilter: "blur(10px)",
                           },
                           content: {
                               top: '50%',
                               left: '50%',
                               right: 'auto',
                               bottom: 'auto',
                               marginRight: '-50%',
                               transform: 'translate(-50%, -50%)',
                               padding: 0,
                               border: "none",
                               background: "rgba(0, 0, 0, 0)",
                           }
                       }
                   }
            >
                <div style={{
                    width: "743px",
                    height: "404px",
                    backgroundSize:"cover",
                    backgroundImage: `url(${
                        g.lang === "zh" ?
                            (!auth.project ?
                                WelcomeImageZH
                                :
                                home.mustLearn?WelcomeImageHomeZH: learn_project_optional_zh
                            )
                            :
                            (!auth.project ?
                                WelcomeImageEN
                                :
                                home.mustLearn? WelcomeImageHomeEN: learn_project_optional_en

                    )}

                            )`,
                }}>
                    <div onClick={handleOK} style={{ position: "absolute", left: "35px", bottom: "122px", width: "225px", height: "38px", cursor: "pointer" }}></div>
                    <div onClick={handleCancel} style={{ position: "absolute", left: "35px", bottom: "67px", width: "225px", height: "38px", cursor: "pointer" }}></div>
                </div>

                {/*<div className="mar-top-15" align="center">*/}
                {/*    <p>{formatMessage({ id: "user.customer.welcome" })}</p>*/}
                {/*    <p>{formatMessage({ id: "user.customer.invite" })}</p>*/}
                {/*</div>*/}


            </ReactModal>
        </React.Fragment>
    );
}