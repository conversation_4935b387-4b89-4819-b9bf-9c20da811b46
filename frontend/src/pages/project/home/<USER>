import { <PERSON><PERSON>, Card, Carousel, Col, Layout, Row, Spin } from "antd";
import { FormattedMessage } from "../../common/multilingual/component";
import React, { useEffect } from "react";
import { useFetch } from "../../../hooks/request";
import { useAuth } from "../../../context/auth";
import { useSafeState } from "ahooks";
import styled from "@emotion/styled";
import dayjs from "dayjs";
import { getProjectOverview } from "../../../api/projects";
import { useGlobal } from "../../../context/global";

// import projectURL from './../../../../src/images/project.png';
import projectURL from "images/project.png";
import siteURL from "images/site.png";
import randomURL from "images/random.png";
import zhedieLeft from "images/zhedieLeft.png";
import zhedieRight from "images/zhedieRight.png";

import inProgressURL from "images/in_progress.png";
import completedURL from "images/completed.png";
import closedURL from "images/closed.png";
import pausedURL from "images/paused.png";
import terminatedURL from "images/terminated.png";
import { LeftOutlined, RightOutlined } from "@ant-design/icons";

const { Header, Footer, Sider, Content } = Layout;

export const ProjectOverview = () => {
  const g = useGlobal();
  const auth = useAuth();
  const customerId = auth.customerId;
  const envId = auth.env ? auth.env.id : null;
  const projectId = auth.project.id;
  const roleId = auth.project.permissions.role_id;

  const [projectStatus, setProjectStatus] = useSafeState(0);
  const [siteCount, setSiteCount] = useSafeState(0);
  const [actualCount, setActualCount] = useSafeState(0);
  const [planCount, setPlanCount] = useSafeState(0);
  const [startTime, setStartTime] = useSafeState(0);
  const [endTime, setEndTime] = useSafeState(0);

  const [projectTimeZone, setProjectTimeZone] = useSafeState(0);

  const [startTimeStr, setStartTimeStr] = useSafeState(null);
  const [endTimeStr, setEndTimeStr] = useSafeState(null);

  const [startYear, setStartYear] = useSafeState<any>("-");
  const [startMonth, setStartMonth] = useSafeState<any>("-");
  const [startDay, setStartDay] = useSafeState<any>("-");

  const [endYear, setEndYear] = useSafeState<any>("-");
  const [endMonth, setEndMonth] = useSafeState<any>("-");
  const [endDay, setEndDay] = useSafeState<any>("-");

  const [projectStatusName, setProjectStatusName] = useSafeState<any>("");
  const [projectStatusUrl, setProjectStatusUrl] = useSafeState("");
  const [cohorts, setCohorts] = useSafeState<any>("");
  const [currentCohortsPage, setCurrentCohortsPage] = useSafeState<any>(1);

  const { runAsync: run_getProjectOverview, loading: overviewLoading } =
    useFetch(getProjectOverview, { manual: true });

  const is_same_day = function (a: any, b: any) {
    return (
      new Date(a * 1000).toDateString() === new Date(b * 1000).toDateString()
    );
  };
  const Status = styled.div`
    background-image: url(${projectStatusUrl});
    background-size: 100% 20px;
    background-repeat: no-repeat;
    color: #ffffff;
    font-family: "PingFang SC";
    position: absolute;
    z-index: 2;
    top: -2px;
    left: 4px;
    padding: 0 18px;
    line-height: 20px;
    font-size: 12px;
  `;

  const overviewData = () => {
    //订单列表查询
    run_getProjectOverview({
      customerId: customerId,
      projectId: projectId,
      envId: envId,
      roleId: roleId,
      // cohortId: cohortId,
    }).then((result: any) => {
      const data = result.data;
      if (data != null) {
        setSiteCount(data.siteCount);
        setActualCount(data.actualCases);
        setPlanCount(data.plannedCases);
        setStartTime(data.startTime);
        setEndTime(data.endTime);
        setStartTimeStr(data.startTimeStr);
        setEndTimeStr(data.endTimeStr);
        setProjectStatus(data.projectStatus);
        setProjectTimeZone(data.projectTimeZone);
        const datas: any = handleCohortsCases(data.cohorts_cases);
        setCohorts(datas);
        var nowTimeZone = getLocalTimeByTimeZone(data.projectTimeZone);
        // console.log("1==" + new Date().getTime() / 1000);
        // console.log("2==" + nowTimeZone.getTime() / 1000);
        const currentTime = Math.round(nowTimeZone.getTime() / 1000);
        if (data.startTime != null && currentTime - data.startTime >= 0) {
          var runTime = currentTime - data.startTime;
          var year = Math.floor(runTime / 86400 / 365);
          setStartYear(year);
          runTime = runTime % (86400 * 365);
          var month = Math.floor(runTime / 86400 / 30);
          setStartMonth(month);
          runTime = runTime % (86400 * 30);
          var day = Math.floor(runTime / 86400);
          if (is_same_day(data.startTime + 86400 * day, currentTime)) {
            day = day;
          } else {
            day = day + 1;
          }
          setStartDay(day);
        }

        if (endTime != null && data.endTime - currentTime >= 0) {
          var runTime = data.endTime - currentTime;
          var year = Math.floor(runTime / 86400 / 365);
          setEndYear(year);
          runTime = runTime % (86400 * 365);
          var month = Math.floor(runTime / 86400 / 30);
          setEndMonth(month);
          runTime = runTime % (86400 * 30);
          var day = Math.floor(runTime / 86400);
          if (is_same_day(currentTime + 86400 * day, data.endTime)) {
            day = day;
          } else {
            day = day + 1;
          }
          setEndDay(day);
        }

        if (data.projectStatus != null) {
          if (data.projectStatus === 0) {
            setProjectStatusName(
              <FormattedMessage id="projects.status.progress" dark />
            );
            setProjectStatusUrl(inProgressURL);
          } else if (data.projectStatus === 1) {
            setProjectStatusName(
              <FormattedMessage id="projects.status.finish" dark />
            );
            setProjectStatusUrl(completedURL);
          } else if (data.projectStatus === 2) {
            setProjectStatusName(
              <FormattedMessage id="projects.status.close" dark />
            );
            setProjectStatusUrl(closedURL);
          } else if (data.projectStatus === 3) {
            setProjectStatusName(
              <FormattedMessage id="projects.status.pause" dark />
            );
            setProjectStatusUrl(pausedURL);
          } else if (data.projectStatus === 4) {
            setProjectStatusName(
              <FormattedMessage id="projects.status.terminate" dark />
            );
            setProjectStatusUrl(terminatedURL);
          }
        }
      }
    });
  };
  const carouselRef = React.useRef<any>(null);

  const prev = () => {
    setCurrentCohortsPage(currentCohortsPage - 1);

    carouselRef.current.prev();
  };

  const next = () => {
    setCurrentCohortsPage(currentCohortsPage + 1);
    carouselRef.current.next();
  };

  useEffect(overviewData, [customerId, projectId, envId]);


  function getLocalTimeByTimeZone(timeZone: any) {
    // 根据传入的时区计算偏移量
    var timeZoneOffset = timeZone * 60; // 将时区转换为分钟

    const now = new Date();
    const localOffset = now.getTimezoneOffset(); // 获取本地时区偏移量（分钟）
    const utcTime = now.getTime() + (timeZoneOffset * 60 * 1000); // 根据传入的UTC时区偏移量计算UTC时间戳
    const localTime = utcTime + (localOffset * 60 * 1000); // 计算本地时间戳
    return new Date(localTime);
  }

  const handleCohortsCases = (cohort: any[]) => {
    const item: any[] = [null, ...cohort];
    const result = [];
    const chunkSize = 3;
    for (let i = 0; i < item.length; i += chunkSize) {
      result.push(item.slice(i, i + chunkSize));
    }
    return result;
  };

  const CohortsItem = (props: any): any => {
    const { index } = props;
    let item: any[] = [];
    cohorts[index].forEach((value: any) => {
      if (value === null) {
        //第一个中心
        item.push(
          <Col>
            <Row align="middle" style={{ width: 118 }}>
              <Col style={{ marginRight: "8px", width: 30, height: 30 }}>
                <img style={{ width: 30, height: 30 }} src={siteURL} />
              </Col>
              <Col>
                <Row style={{ fontSize: 20, fontWeight: 600 }}>{siteCount}</Row>
                <Row style={{ color: "#A8A8A8", width: 80, height: 45 }}>
                  <FormattedMessage id="project.overview.site.number" />
                </Row>
              </Col>
            </Row>
          </Col>
        );
      } else {
        item.push(
          <Col>
            <div style={{ width: 118 }}>
              <Row align="middle">
                <Col
                  style={{
                    marginRight: "8px",
                    width: 30,
                    height: 30,
                  }}
                >
                  <img style={{ width: 30, height: 30 }} src={randomURL} />
                </Col>
                <Col style={{ color: "#A8A8A8", width: 80 }}>
                  <Row align="middle">
                    <span
                      style={{
                        color: "#000000",
                        fontSize: 20,
                        fontWeight: 600,
                      }}
                    >
                      {value.actualCases != null && value.actualCases !== 0
                        ? value.actualCases
                        : "-"}
                    </span>
                    <span style={{ color: "#677283" }}>
                      /
                      {value.plannedCases != null && value.plannedCases !== 0
                        ? value.plannedCases
                        : "-"}
                    </span>
                  </Row>
                  {/* // 在渲染CohortName时添加title属性 */}
                  <CohortName title={value.cohort_name ? value.cohort_name : ""}>
                    {value.cohort_name ? (
                      value.cohort_name
                    ) : (
                      <FormattedMessage id="project.overview.random.number" />
                    )}
                  </CohortName>
                </Col>
              </Row>
            </div>
          </Col>
        );
      }
    });

    if (cohorts.length > index + 1) {
      item.push(
        <Row align={"middle"} style={{ paddingLeft: 16 }}>
          <img style={{ zIndex: 1, width: 30, height: 30 }} src={randomURL} />
        </Row>
      );
    }
    return (
      <div
        style={{
          width: 400,
          alignItems: "center",
          display: "flex",
        }}
      >
        <Row>{item}</Row>{" "}
      </div>
    );
  };

  return (
    <div style={{ position: "relative" }}>
      <Status>{projectStatusName}</Status>
      <Card bodyStyle={{ padding: "24px 16px", paddingRight: 0 }}>
        <Spin spinning={overviewLoading}>
          <div style={{display: "flex", alignItems: "center", justifyContent: "space-between"}}>
            <div style={{ flex: 1, display: "flex", alignItems: "center" }}>
              <Col><img style={{ width: 40, marginLeft: 0 }} src={projectURL} /></Col>
              <Col style={{ marginLeft: 5 }}>
                <Row>
                  <WeightCol>
                    <WeightCol>
                      <FormattedMessage id={'project.overview.progress.template'} values={{
                        startYear: <span> {startYear} </span>,
                        startMonth: <span> {startMonth} </span>,
                        startDay: <span> {startDay} </span>,
                        endYear: <span className={'red'}> {endYear} </span>,
                        endMonth: <span className={'red'}> {endMonth} </span>,
                        endDay: <span className={'red'}> {endDay} </span>,
                      }}/>
                    </WeightCol>
                  </WeightCol>
                </Row>
                <Row>
                  <Col style={{ color: "#A8A8A8" }}>
                    <FormattedMessage id="project.overview.time" />:{" "}
                    {startTime != null
                      ? dayjs.unix(startTime).format("YYYY.MM.DD")
                      : null}{" "}
                    -{" "}
                    {endTime != null
                      ? dayjs.unix(endTime).format("YYYY.MM.DD")
                      : null}
                  </Col>
                </Row>
              </Col>
            </div>

            {auth.project.info.type !== 1 && (
              <div style={{ width: 400, position: "relative" }}>
                {currentCohortsPage !== 1 && (
                  <img
                    style={{
                      zIndex: 5,
                      borderRadius: 36,
                      boxShadow: "0px 0px 12px 0px #2F313640",
                      top: "50%",
                      transform: "translate(-50%, -50%)",
                      position: "absolute",
                      left: -16,
                      width: 20,
                      height: 20,
                    }}
                    src={zhedieLeft}
                    onClick={prev}
                  />
                )}
                <CustomerCarousel>
                  <Carousel style={{ width: 400 }} ref={carouselRef}>
                    {cohorts &&
                      cohorts?.map((value: any, index: any) => (
                        <CohortsItem index={index} />
                      ))}
                  </Carousel>
                </CustomerCarousel>

                {currentCohortsPage !== cohorts?.length && (
                  <Butt>
                    <img
                      style={{
                        borderRadius: 36,
                        boxShadow: "0px 0px 12px 0px #2F313640",
                        top: 42,
                        width: 20,
                        height: 20,
                      }}
                      src={zhedieRight}
                      onClick={next}
                    />
                  </Butt>
                )}
              </div>
            )}
            {auth.project.info.type === 1 && (
              <div style={{ width: 260, position: "relative" }}>
                {cohorts &&
                  cohorts?.map((value: any, index: any) => (
                    <CohortsItem index={index} />
                  ))}
              </div>
            )}
          </div>
        </Spin>
      </Card>
    </div>
  );
};

const Divider = styled.div`
  height: 40px;
  width: 0.5px;
  background: #e3e4e6;
  margin-right: 16px;
`;

const Butt = styled.div`
  font-family: "PingFang SC";
  position: absolute;
  z-index: 5;
  top: 50%;
  transform: translate(-50%, -50%);
  right: -20px;
  line-height: 20px;
  font-size: 12px;
  box-shadow: 0px 0px 12px 0px #2f313640;
  border-radius: 36px;
`;

const CohortName = styled.div`
  width: 60px; /* 固定宽度 */
  height: 45px;
  white-space: nowrap; /* 确保文本不换行 */
  overflow: hidden;
  text-overflow: ellipsis; /* 省略显示 */
`;

const CustomerCarousel = styled.div`
  .ant-carousel .slick-dots {
    visibility: hidden !important;
  }
`;

const WeightCol = styled(Col)`
  span {
    font-size: 20px;
    font-weight: 600;
  }
  .red {
    color: red;
    font-size: 20px;
    font-weight: 500;
  }
`