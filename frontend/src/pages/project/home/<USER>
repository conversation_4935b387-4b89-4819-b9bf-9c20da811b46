import React from "react";
import { FormattedMessage, useTranslation } from "../../common/multilingual/component";
import {
    <PERSON>ton,
    Col,
    Divider,
    Form,
    Input,
    message,
    Modal,
    Radio,
    Row,
    Steps,
    Tooltip
} from "antd";
import { useSafeState } from "ahooks";
import { useAuth } from "../../../context/auth";
import { useFetch } from "../../../hooks/request";
import { useGlobal } from "../../../context/global";
import styled from "@emotion/styled";
import {
    approvalProcessStatus,
    orderAddTaskStatus,
} from "../../../data/data";
import moment from "moment";
import { updateApprovalTask } from "../../../api/approval_process";
import ReactToPrint from "react-to-print";
import {permissions, permissionsCohort} from "../../../tools/permission";
import { TitleIcon } from "../../../components/title";
import { useSubject } from "./context";
import {UnblindingApproval} from "../../../api/dispensing";


export const UnblindingApprovalProcessDetail = (props: any) => {
    const intl = useTranslation();
    const home = useSubject();
    const g = useGlobal();
    const { formatMessage } = intl;
    const auth = useAuth();
    const [visible, setVisible] = useSafeState(false);
    const [title, setTitle] = useSafeState("");
    const [id, setId] = useSafeState(null);
    const [submitting, setSubmitting] = useSafeState(false);
    const [record, setRecord] = useSafeState<any>(null);
    const [unblinding, setUnblinding] = useSafeState<any | undefined>(undefined);
    const [attributeInfo, setAttributeInfo] = useSafeState<any | undefined>(undefined);
    const [type, setType] = useSafeState(1);
    const [status, setStatus] = useSafeState(0);
    const [approvalStatus, setApprovalStatus] = useSafeState(0);
    const [reason, setReason] = useSafeState("");
    const researchAttribute = auth.project.info.research_attribute ? auth.project.info.research_attribute : 0;
    const [estimatedCompletionTime, setEstimatedCompletionTime] =
        useSafeState(null);
    const [approvalTime, setApprovalTime] = useSafeState(null);

    const [form] = Form.useForm();
    const timeZone =
        (auth.project.info.timeZoneStr !== undefined && auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "")
            ? Number(auth.project.info.timeZoneStr)
            : 8;
    const projectNumber = auth.project.info.number;
    const envId = auth.env ? auth.env.id : null;
    const envName = auth.env ? auth.env.name : "";
    const [approvalUserOne, setApprovalUserOne] = useSafeState("")
    const [approvalUserTwo, setApprovalUserTwo] = useSafeState("")



    const show = (
        item: any
    ) => {
        setRecord(item);
        setType(item.type);
        setId(item.id);
        //获取供应计划
        setStatus(item.status);
        setEstimatedCompletionTime(item.estimatedCompletionTime);
        setApprovalTime(item.approvalTime);
        setReason(item.reason);
        setApprovalStatus(item.approvalStatus);
        if (item.type === 2) {
            setTitle(formatMessage({ id: 'menu.projects.project.subject.urgent-unblinding.unblinding' }))
        }
        if (item.type === 3) {
            setTitle(formatMessage({ id: 'menu.projects.project.subject.urgent-unblinding.unblinding-pv' }))
        }
        if (item.type === 4) {
            setTitle(formatMessage({ id: 'menu.projects.project.subject.urgent-unblinding.unblinding-ip' }))
        }
        setUnblinding(item.unblindingData);
        setAttributeInfo(item.attributeInfo);
        approvedUsers(item.unblindingData);
        setVisible(true);
    };

    // 组装可审批用户
    const approvedUsers = (item: any) => {
        // 展示可揭盲的用户信息
        if (item?.approvalUser != null && item?.approvalUser.length > 0) {
            let uau: any = [];
            for (let i = 0; i < item?.approvalUser.length; i++) {
                let namePhone = item?.approvalUser[i].approvalName + "/" + item?.approvalUser[i].approvalPhone;
                uau.push(namePhone);
                if (i === 2) {
                    break
                }
            }
            if (item?.approvalUser.length > 3) {
                uau.push("...");
            }
            setApprovalUserOne(
                item?.approvalUser.map((ccu: any) =>
                    <Row >
                        <span className={'.ant-steps-step-description'}>
                            {ccu.approvalName}/{ccu.approvalPhone}
                        </span>
                    </Row>
                )
            )
            setApprovalUserTwo(
                uau?.map((ccu: any) =>
                    <Row>
                        <span className={'.ant-steps-step-description'}>
                            {ccu}
                        </span>
                    </Row>
                )
            )
        }

    }


    const hide = () => {
        setVisible(false);
        form.resetFields();
        setSubmitting(false);
    };

    const { runAsync: run_updateApprovalTask } = useFetch(updateApprovalTask, {
        manual: true,
    });
    const save = () => {
        form.validateFields()
            .then((values) => {
                setSubmitting(true);
                run_updateApprovalTask({
                    id: id,
                    ...values,
                    roleId: auth.project.permissions.role_id,
                }).then(
                    (result: any) => {
                        home.setTaskUpdate(home.taskUpdate + 1);
                        message.success(result.msg);
                        props.refresh();
                        hide();
                    },
                    (data) => {
                        hide();
                    }
                );
            })
            .catch((errors) => {
                setSubmitting(false);
            });
    };

    const changeApprovalStatus = (status: any) => {
        setApprovalStatus(status);
        approvedUsers(unblinding);
    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    return (
        <React.Fragment>
            <Modal
                className="custom-large-modal"
                title={
                    title +
                    ` - ${projectNumber}[${envName}]`
                }
                centered={true}
                open={visible}
                onCancel={hide}
                bodyStyle={{ paddingTop: "0px", overflowX: "hidden" }}
                width={1000}
                footer={
                    status === 0 &&
                    ((type === 2 && (permissionsCohort(auth.project.permissions, "operation.subject.unblinding-approval",record?.cohort.status) || permissionsCohort(auth.project.permissions, "operation.subject-dtp.unblinding-approval",record?.cohort.status)))
                        || (type === 3 && permissionsCohort(auth.project.permissions, "operation.subject.unblinding-pv-approval",record?.cohort.status))
                        || (type === 4 && permissionsCohort(auth.project.permissions, "operation.subject.unblinding-ip-approval",record?.cohort.status))
                    )
                    && (
                        <Row justify="end">
                            <Col>
                                <Button onClick={hide}>
                                    <FormattedMessage id="common.cancel" />
                                </Button>
                                <Button
                                    onClick={save}
                                    type="primary"
                                    loading={submitting}
                                >
                                    <FormattedMessage id="common.ok" />
                                </Button>
                            </Col>
                        </Row>
                    )
                }
            >
                <Row style={{ margin: "16px 0" }}>
                    <Col span={8}>
                        <Form.Item
                            label={
                                <div
                                    style={{
                                        display: "flex",
                                        alignItems: "center",
                                    }}
                                >
                                    <svg
                                        className="iconfont"
                                        width={18}
                                        height={18}
                                        style={{ marginRight: "5px" }}
                                    >
                                        <use xlinkHref="#icon-renwuzhuangtai"></use>
                                    </svg>
                                    <FormattedMessage id="project.task.status" />
                                </div>
                            }
                            className="mar-ver-5"
                        >
                            {status === 0 && (
                                <div
                                    style={{
                                        background: "#f6f6f8",
                                        color: "#ADB2BA",
                                        fontStyle: "normal",
                                        width:
                                            g.lang === "en" ? "96px" : "65px",
                                        fontWeight: 400,
                                    }}
                                >
                                    <span
                                        style={{
                                            marginLeft: "4px",
                                            marginRight: "4px",
                                        }}
                                    >
                                        <svg
                                            className="iconfont"
                                            width={12}
                                            height={12}
                                        >
                                            <use xlinkHref="#icon-weikaishi"></use>
                                        </svg>
                                    </span>
                                    {
                                        orderAddTaskStatus.find(
                                            (it) => it.value === status
                                        )?.label
                                    }
                                </div>
                            )}
                            {status === 1 && (
                                <div
                                    style={{
                                        background: "#e3f8ec",
                                        color: "#41CC82",
                                        fontStyle: "normal",
                                        width:
                                            g.lang === "en" ? "96px" : "65px",
                                        fontWeight: 400,
                                    }}
                                >
                                    <span
                                        style={{
                                            marginLeft: "4px",
                                            marginRight: "4px",
                                        }}
                                    >
                                        <svg
                                            className="iconfont"
                                            width={12}
                                            height={12}
                                        >
                                            <use xlinkHref="#icon-yiwancheng1"></use>
                                        </svg>
                                    </span>
                                    {
                                        orderAddTaskStatus.find(
                                            (it) => it.value === status
                                        )?.label
                                    }
                                </div>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item
                            label={
                                <div
                                    style={{
                                        display: "flex",
                                        alignItems: "center",
                                    }}
                                >
                                    <svg
                                        className="iconfont"
                                        width={18}
                                        height={18}
                                        style={{ marginRight: "5px" }}
                                    >
                                        <use xlinkHref="#icon-yuqiwancheng"></use>
                                    </svg>
                                    <FormattedMessage id="project.task.estimatedCompletionTime" />
                                </div>
                            }
                            className="mar-ver-5"
                        >
                            {estimatedCompletionTime === null ||
                                estimatedCompletionTime === 0
                                ? ""
                                : moment
                                    .unix(estimatedCompletionTime)
                                    .utc()
                                    .add(timeZone, "hour")
                                    .format("YYYY-MM-DD")}
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item
                            label={
                                <div
                                    style={{
                                        display: "flex",
                                        alignItems: "center",
                                    }}
                                >
                                    <svg
                                        className="iconfont"
                                        width={18}
                                        height={18}
                                        style={{ marginRight: "5px" }}
                                    >
                                        <use xlinkHref="#icon-shijiwancheng"></use>
                                    </svg>
                                    <FormattedMessage id="project.task.approvalTime" />
                                </div>
                            }
                            className="mar-ver-5"
                        >
                            {approvalTime === null || approvalTime === 0
                                ? "-"
                                : moment
                                    .unix(approvalTime)
                                    .utc()
                                    .add(timeZone, "hour")
                                    .format("YYYY-MM-DD HH:mm:ss")}
                        </Form.Item>
                    </Col>
                </Row>
                <Divider
                    style={{
                        margin: "0px 0px 8px -24px",
                        borderWidth: "4px",
                        width: "1000px",
                        background: "#F9FAFB !important",
                    }}
                ></Divider>

                <div ref={el => (home.componentRef = el)}>
                    {type !== 1 &&
                        <>
                            <StepsContainer>
                                <div style={{ display: "flex", alignItems: "center", marginBottom: 32 }}>
                                    <TitleIcon />
                                    <span style={{ color: "#4E5969" }}>{intl.formatMessage({ id: "subject.unblinding.approval.number" })}:</span>
                                    <span style={{ marginLeft: 8, color: "#1D2129" }}>{unblinding.number}</span>
                                </div>
                                <Steps progressDot current={unblinding.status === 0 ? 0 : 1}>
                                    <Steps.Step
                                        title={formatMessage({ id: 'subject.urgentUnblindingApproval.pending' })}
                                        description={
                                            <>
                                                <Row>
                                                    {unblinding.applicationByEmail}
                                                </Row>
                                                <Row>
                                                    {moment.unix(unblinding.applicationTime).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss')}
                                                </Row>
                                            </>
                                        } />
                                    {approvalStatus === 0 ?
                                        <Steps.Step title={formatMessage({ id: 'medicine.status.toBeApproved' })} description={
                                            <div >
                                                <Tooltip
                                                    placement="top"
                                                    title={
                                                        <>
                                                            {approvalUserOne}
                                                        </>
                                                    }
                                                >
                                                    <>
                                                        {approvalUserTwo}
                                                    </>
                                                </Tooltip>
                                            </div>
                                        } /> :
                                        <Steps.Step title={
                                            unblinding.status === 0 ?
                                                <>
                                                    <FormattedMessage id={"common.toBeApproved"} />
                                                </> :
                                                unblinding.status === 1 ?
                                                    <FormattedMessage id={"subject.urgentUnblindingApproval.agree"} /> :
                                                    unblinding.status === 2 ? <FormattedMessage id={"subject.urgentUnblindingApproval.reject"} /> :
                                                        null
                                        }
                                            description={
                                                unblinding.status === 0 ?
                                                    <>
                                                        <Tooltip
                                                            placement="top"
                                                            title={
                                                                <div style={{ marginLeft: -30 }}>
                                                                    {

                                                                        unblinding?.approvalUser != null && unblinding?.approvalUser.length > 0 ?
                                                                            unblinding?.approvalUser?.map((ccu: any) =>
                                                                                <Row>
                                                                                    <span className={'.ant-steps-step-description'}>
                                                                                        {ccu.approvalName}/{ccu.approvalPhone}
                                                                                    </span>
                                                                                </Row>
                                                                            )
                                                                            :
                                                                            null

                                                                    }
                                                                </div>
                                                            }
                                                        >
                                                            <div style={{ marginLeft: -30 }}>
                                                                {
                                                                    unblinding?.approvalUser != null && unblinding?.approvalUser.length > 0 ?
                                                                        unblinding?.approvalUser?.map((ccu: any, index: number) =>
                                                                            index < 3 ?
                                                                                <Row>
                                                                                    <span className={'.ant-steps-step-description'}>
                                                                                        {ccu.approvalName}/{ccu.approvalPhone}
                                                                                    </span>
                                                                                </Row>
                                                                                :
                                                                                index === 3 ?
                                                                                    <Row>
                                                                                        <span className={'.ant-steps-step-description'}>
                                                                                            ...
                                                                                        </span>
                                                                                    </Row>
                                                                                    :
                                                                                    null
                                                                        )
                                                                        :
                                                                        null
                                                                }
                                                            </div>
                                                        </Tooltip>
                                                    </>
                                                    :
                                                    <div style={{ color: "#4E5969", marginLeft: -20 }}>
                                                        <span className={'.ant-steps-step-description'}>
                                                            {unblinding.approvalByEmail}
                                                        </span>
                                                        <Row>
                                                            <span className={'.ant-steps-step-description'}>
                                                                {unblinding.approvalTime !== 0 && moment.unix(unblinding.approvalTime).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss')}
                                                            </span>
                                                        </Row>
                                                    </div>
                                            }
                                        />

                                    }


                                </Steps>
                            </StepsContainer>

                            <Form form={form} labelCol={{ span: g.lang === 'en' ? 6 : 3 }}>
                                <Form.Item label={attributeInfo ?
                                    ((attributeInfo.subjectReplaceText === "" &&
                                        attributeInfo.subjectReplaceTextEn === "") ?
                                        (formatMessage({ id: "subject.number" })) :
                                        ((attributeInfo.subjectReplaceText !== "" &&
                                            attributeInfo.subjectReplaceTextEn === "") ?
                                            attributeInfo.subjectReplaceText :
                                            ((attributeInfo.subjectReplaceText === ""
                                                && attributeInfo.subjectReplaceTextEn !== "") ?
                                                (g.lang === "en" ? attributeInfo.subjectReplaceTextEn
                                                    : formatMessage({ id: "subject.number" })) :
                                                (g.lang === "en" ? attributeInfo.subjectReplaceTextEn :
                                                    attributeInfo.subjectReplaceText)))) :
                                    formatMessage({ id: "subject.number" })}>
                                    <span>{unblinding.subjectNumber}</span>
                                </Form.Item>
                                <Form.Item
                                    label={formatMessage({ id: 'projects.randomization.randomNumber' })}>
                                    <span>{unblinding.subjectRandomNumber}</span>
                                </Form.Item>
                                {
                                    type === 4 &&
                                    <Form.Item
                                        label={formatMessage({ id: 'drug.list.drugNumber' })}>
                                        <span>{unblinding?.medicineNumber}</span>
                                    </Form.Item>
                                }


                                <Form.Item
                                    label={formatMessage({ id: 'subject.unblinding.reason' })}>
                                    <span>{unblinding.reasonStr}</span>
                                    {unblinding.remark !== "" ?
                                        <span>{' '}{unblinding.remark}</span>
                                        : null}
                                </Form.Item>
                                {status !== 0 ? (
                                    <>
                                        <div>
                                            <Form.Item
                                                label={formatMessage({
                                                    id: "shipment.approval.confirm.title",
                                                })}
                                                className="mar-ver-5"
                                            >
                                                {approvalStatus === 1 ||
                                                    approvalStatus === 2
                                                    ? approvalProcessStatus.find(
                                                        (it) =>
                                                            it.value === approvalStatus
                                                    )?.label
                                                    : ""}
                                            </Form.Item>
                                            {approvalStatus === 2 ? (
                                                <Form.Item
                                                    label={formatMessage({
                                                        id: "common.reason",
                                                    })}
                                                    className="mar-ver-5"
                                                >
                                                    {reason}
                                                </Form.Item>
                                            ) : null}
                                        </div>
                                    </>
                                ) : null}
                                {status === 0 ? (
                                    <>
                                        <div>
                                            {((type === 2 && (researchAttribute === 0 ?
                                                permissionsCohort(auth.project.permissions, "operation.subject.unblinding-approval",record?.cohort.status) :
                                                permissionsCohort(auth.project.permissions, "operation.subject-dtp.unblinding-approval",record?.cohort.status))) ||
                                                (type === 3 && permissionsCohort(auth.project.permissions, "operation.subject.unblinding-pv-approval",record?.cohort.status))
                                                ||
                                                (type === 4 && permissionsCohort(auth.project.permissions, "operation.subject.unblinding-ip-approval",record?.cohort.status))
                                            ) ? (
                                                <>

                                                    <Form.Item
                                                        label={formatMessage({
                                                            id: "shipment.approval.confirm.title",
                                                        })}
                                                        name="approvalStatus"
                                                        rules={[{ required: true }]}
                                                        className="mar-ver-5"
                                                    >
                                                        <Radio.Group
                                                            onChange={(e) =>
                                                                changeApprovalStatus(
                                                                    e.target.value
                                                                )
                                                            }
                                                        >
                                                            <Radio value={1}>
                                                                <FormattedMessage id="project.task.approvalStatus.pass" />
                                                            </Radio>
                                                            <Radio value={2}>
                                                                <FormattedMessage id="project.task.approvalStatus.reject" />
                                                            </Radio>
                                                        </Radio.Group>
                                                    </Form.Item>
                                                    {approvalStatus === 2 ? (
                                                        <Form.Item
                                                            label={formatMessage({id: "common.reason"})}
                                                            name="reason"
                                                            rules={[{ required: true }]}
                                                            className="full-width"
                                                        >
                                                            <Input.TextArea
                                                                allowClear
                                                                autoSize={{
                                                                    minRows: 2,
                                                                    maxRows: 10,
                                                                }}
                                                            />
                                                        </Form.Item>
                                                    ) : null}

                                                </>
                                            ) : (
                                                <>

                                                    <Form.Item
                                                        label={formatMessage({
                                                            id: "shipment.approval.confirm.title",
                                                        })}
                                                        name="approvalStatus"
                                                        className="mar-ver-5"
                                                    >
                                                        <Radio.Group
                                                            onChange={(e) =>
                                                                changeApprovalStatus(
                                                                    e.target.value
                                                                )
                                                            }
                                                            disabled
                                                        >
                                                            <Radio value={1}>
                                                                <FormattedMessage id="project.task.approvalStatus.pass" />
                                                            </Radio>
                                                            <Radio value={2}>
                                                                <FormattedMessage id="project.task.approvalStatus.reject" />
                                                            </Radio>
                                                        </Radio.Group>
                                                    </Form.Item>

                                                </>
                                            )}
                                        </div>
                                    </>
                                ) : null}
                            </Form>


                        </>
                    }
                </div>

                {   (status === 1 &&
                    ((type === 2 && permissions(auth.project.permissions, "operation.subject.unblinding-print")) ||
                    (type === 3 && permissions(auth.project.permissions, "operation.subject.unblinding-pv-print")) ||
                    (type === 4 && permissions(auth.project.permissions, "operation.subject.unblinding-ip-print"))))
                        ?
                        <div style={{ paddingLeft: "94%", paddingBottom: 8 }}>
                            <ReactToPrint
                                trigger={() => {
                                    return <Button>{<FormattedMessage id={"common.print"} />}</Button>;
                                }}
                                content={() => home.componentRef}
                            />
                        </div>
                        :
                        null
                }
            </Modal>
        </React.Fragment>
    );
};






const StepsContainer = styled.div`
    margin-bottom: 18px;
    padding: 16px 12px;
    border-radius: 2px;
    background: rgba(227,228,230,0.2);
    .ant-steps-horizontal {
      .ant-steps-item:last-child {
        .ant-steps-item-title {
          width: 140px;
        }
        .ant-steps-item-content {
          display: flex;
          flex-direction: column;
          width: 100%;
        }
      }
    }
    
    .ant-steps-label-vertical .ant-steps-item-content .ant-steps-item-description {
        text-align: left !important;
        white-space: nowrap;
        // width: 200px !important;
    }
    .ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-tail::after {
        background-color: #C8C9CC;
    }
`