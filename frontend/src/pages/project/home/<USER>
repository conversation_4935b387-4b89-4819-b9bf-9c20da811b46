import { Card, Col, message, Radio, Row, Spin, Tooltip } from "antd";
import { FormattedMessage, useTranslation } from "../../common/multilingual/component";
import React, {useEffect, useMemo, useRef, useState} from "react";
import { useAuth } from "../../../context/auth";
import { useSafeState } from "ahooks";
import ReactEcharts from "echarts-for-react";
import { Title } from "components/title";
import { useFetch } from "../../../hooks/request";
import {
  getProjectAttribute,
  getProjectCohortRandom,
  getRandomStatisticsBar,
  getRandomStatisticsPie,
} from "../../../api/randomization";
import { useGlobal } from "../../../context/global";
import { permissions } from "../../../tools/permission";
import { exportReport } from "../../../api/report";
import { CohortSelect } from "./cohort_select";
import {AuthWrap} from "../../common/auth-wrap";

export const ProjectRandomStatistics = () => {
  const g = useGlobal();
  const intl = useTranslation();
  const { formatMessage } = intl;
  const auth = useAuth();
  const projectId = auth.project ? auth.project.id : null;
  const envId = auth.env ? auth.env.id : null;
  const [pieOption, setPieOption] = useSafeState<any>({});
  const [barOption, setBarOption] = useSafeState<any>({});
  const [pieType, setPieType] = useSafeState(0);
  const [barType, setBarType] = useSafeState(0);
  const pieRef = useRef<any>();
  const barRef = useRef<any>();
  const [endValue, setEndValue] = useState<number>(0);

  const [attribute, setAttribute] = useState<any>(null);
  const cohortIds = auth.env?.cohorts ? auth.env?.cohorts : null;
  const [cohortId, setCohortId] = useState<any>(null);
  const {
    runAsync: getRandomStatisticsPieRun,
    loading: getRandomStatisticsPieLoading,
  } = useFetch(getRandomStatisticsPie, { manual: true });
  const {
    runAsync: getRandomStatisticsBarRun,
    loading: getRandomStatisticsBarLoading,
  } = useFetch(getRandomStatisticsBar, { manual: true });
  const { runAsync: exportReportRun, loading: exportReportLoading } = useFetch(
    exportReport,
    { manual: true }
  );

  useEffect(() => {
    if (attribute) {
      setPieType(attribute.info.countryLayered ? 0 : 1);
    }
  }, [attribute]);

  const [cohorts, setCohorts] = useSafeState<any>([]);

  const {runAsync} = useFetch(getProjectAttribute, {manual: true});
  const customerId = auth.customerId;
  const {
    runAsync: getProjectCohortRandomRun,
    loading: getProjectCohortRandomLoading,
  } = useFetch(getProjectCohortRandom, { manual: true });

  useEffect(() => {
    getProjectCohortRandomRun({"projectId":auth.project.id, "envId":auth.env.id}).then(
        (data:any) => {
          let cohorts = data.data
          for (let i = 0; i < cohorts.length; i++) {
            let c = cohorts[i]
            if (c.type === 1){
              cohorts[i].re_random_name = cohorts[i].reRandomName
            }
          }
          setCohorts(cohorts)
        }
    )
    if (cohortIds.length === 0) {
      runAsync({projectId: projectId, env: envId, cohort: cohortId, customer: customerId}).then(
          (result: any) => {
            setAttribute(result.data)
          }
      )
    }
  },[])

  useEffect(() => {
    if (attribute && (cohortIds.length === 0 || cohortId)) {
      getRandomStatisticsPieRun({
        envId: envId,
        cohortId: cohortId,
        type: pieType,
        roleId: auth.project.permissions.role_id,
      }).then((resp: any) => {
        let array = resp.data.array;
        let total = resp.data.total;
        setPieOption({
          color: [
            "#165DFF",
            "#9FDB1D",
            "#F7BA1E",
            "#14C9C9",
            "#3491FA",
            "#00B42A",
            "#FADC19",
            "#D91AD9",
            "#FF7A00",
            "#722ED1",
            "#F5319D",
            "#F53F3F",
          ],
          title: {
            text: formatMessage({ id: "random.statistics.total" }),
            subtext: total === 0 ? "-" : total,
            textStyle: {
              color: "#677283",
              fontWeight: 400,
              fontSize: 12,
            },
            subtextStyle: {
              color: "#1D2129",
              fontWeight: 500,
              fontSize: 20,
            },
            left: "center",
            bottom: 120,
          },
          tooltip: {
            trigger: "item",
            confine: true,
          },
          series: [
            {
              type: "pie",
              data: array,
              radius: ["40%", "65%"],
              bottom: 10,
              legendHoverLink: true,
              label: {
                formatter: "{b}: {c}",
              },
            },
          ],
          legend: {
            itemWidth: 6,
            itemHeight: 6,
            icon: "circle",
            bottom: -5,
            show: true,
            type: "scroll",
          },
        });
        pieRef.current.resize();
      });
    }
  }, [attribute, g.lang, pieType]);
  useEffect(() => {
    if (cohortIds.length === 0 || (cohortIds && cohortId)) {
      getRandomStatisticsBarRun({
        projectId: projectId,
        envId: envId,
        cohortId: cohortId,
        type: barType,
        roleId: auth.project.permissions.role_id,
      }).then((resp: any) => {
        let current = resp.data.current;
        let cumulative = resp.data.cumulative;
        let currentName = current.map((c: any) => c.name);
        let currentValue = current.map((c: any) => c.value);
        let cumulativeValue = cumulative.map((c: any) => c.value);
        // setEndValue(Math.floor(barRef.current.ele.clientWidth / 150));

        setBarOption({
          color: ["#165DFF"],
          grid: {
            bottom: currentName.length > 0 ? (barType === 0 ? 60 : 70) : 30,
          },
          dataZoom: [
            {
              type: "slider",
              height: 7,
              bottom: 25,
              start: 0,
              endValue,
              show: currentName.length > endValue + 1,
              textStyle: false,
              brushSelect: false,
              handleStyle: {
                opacity: 0,
              },
              dataBackground: {
                lineStyle: {
                  opacity: 0,
                },
                areaStyle: {
                  opacity: 0,
                },
              },
              fillerColor: "#E3E4E6",
            },
          ],
          xAxis: {
            type: "category",
            data: currentName,
            axisLabel: {
              interval: 0,
              overflow: "break",
              width: 75,
            },
          },
          yAxis: {
            type: currentValue.length > 0 ? "value" : "category",
            data: [0, 5, 10, 15, 20, 25],
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
            },
            axisLine: {
              show: false,
            },
          },
          tooltip: {
            trigger: "axis",
          },
          series: [
            {
              name: formatMessage({
                id:
                  barType === 0
                    ? "random.statistics.month"
                    : "random.statistics.week",
              }),
              type: "bar",
              data: currentValue,
              barMinWidth: 30,
              barMaxWidth: 50,
            },
            {
              name: formatMessage({ id: "random.statistics.cumulative" }),
              type: "line",
              data: cumulativeValue,
              itemStyle: {
                color: "#4BE2AC",
              },
              showSymbol: false,
            },
          ],
          legend: {
            bottom: -5,
            show: true,
          },
        });
        barRef.current.resize();
      });
    }
  }, [attribute, g.lang, endValue, barType]);
  const handleResize = () => {
    pieRef.current.resize();
    barRef.current.resize();
    setEndValue(Math.floor(barRef.current.ele.clientWidth / 150));
  };
  React.useEffect(() => {
    // 监听
    window.addEventListener("resize", handleResize);
    if (barRef.current) {
      setEndValue(Math.floor(barRef.current.ele.clientWidth / 150));
    }
    // 销毁
    return () => window.removeEventListener("resize", handleResize);
  });
  const downloadData = () => {
    const params = {
      type: 1,
      projectId: auth.project.id,
      envId: envId,
      roleId: auth.project.permissions.role_id,
      cohortIds: cohortId ? [cohortId] : [],
    };
    exportReportRun(params).then(() => {
      message.success(formatMessage({ id: "report.download.success" }));
    });
  };

  //
  // const getAttribute = () => {
  //   if (cohortIds.length === 0 || (cohortIds && cohortId)) {
  //     runAsync({projectId: projectId, env: envId, cohort: cohortId, customer: customerId}).then(
  //         (result: any) => {
  //           setAttribute(result.data)
  //         }
  //     )
  //   }
  // };
  // useEffect(getAttribute, [cohortId])

  return (
    <>
      <Card
        headStyle={{ padding: "0 16px" }}
        bodyStyle={{ padding: "16px" }}
        title={
          <Row justify={"space-between"} align="middle">
            <Row align="middle">
              <Title name={<FormattedMessage id={"project.random.statistics"} />}/>
              <Tooltip
                trigger={["hover", "click"]}
                overlayInnerStyle={{
                  width: 210,
                  fontSize: 12,
                  background: "#646566",
                }}
                placement="top"
                title={<FormattedMessage id="project.random.statistics.info" />}
              >
                <svg
                  className="iconfont mouse"
                  width={12}
                  height={12}
                  style={{ marginLeft: 4 }}
                >
                  <use xlinkHref="#icon-xinxitishi" />
                </svg>
              </Tooltip>
            </Row>
            <AuthWrap
                show={permissions(auth.project.permissions, "operation.project.random.download")}
                previewProps={{hideOnPreview: true}}
            >
                <Spin spinning={exportReportLoading}>
                  <div onClick={downloadData}>
                    <svg className="iconfont download-hover">
                      <use xlinkHref="#icon-daochu" fill="#000" />
                    </svg>
                  </div>
                </Spin>
            </AuthWrap>
          </Row>
        }
      >
        {
          cohorts.length > 0  &&
            <CohortSelect
                cohortId={cohortId}
                setCohortId={setCohortId}
                attribute={attribute}
                setAttribute={setAttribute}
                cohortIds={cohorts}
            />
        }

        <Row gutter={24}>
          <Col span={12}>
            <Spin spinning={getRandomStatisticsPieLoading}>
              {attribute &&
              (attribute.info.countryLayered ||
                attribute.info.regionLayered) ? (
                <Row>
                  <Radio.Group
                    value={pieType}
                    onChange={(e: any) => setPieType(e.target.value)}
                    className="block-radio"
                  >
                    {attribute && attribute.info.countryLayered ? (
                      <Radio.Button value={0}>
                        {formatMessage({ id: "random.statistics.country" })}
                      </Radio.Button>
                    ) : null}
                    {attribute && attribute.info.regionLayered ? (
                      <Radio.Button value={2}>
                        {formatMessage({ id: "random.statistics.region" })}
                      </Radio.Button>
                    ) : null}
                    <Radio.Button value={1}>
                      {formatMessage({ id: "random.statistics.factor" })}
                    </Radio.Button>
                  </Radio.Group>
                </Row>
              ) : null}
              <Row>
                <ReactEcharts
                  ref={pieRef}
                  notMerge={true}
                  style={{ flexGrow: 1 }}
                  option={pieOption}
                />
              </Row>
            </Spin>
          </Col>
          <Col span={12}>
            <Spin spinning={getRandomStatisticsBarLoading}>
              <Row>
                <Radio.Group
                  value={barType}
                  onChange={(e: any) => setBarType(e.target.value)}
                  className="block-radio"
                >
                  <Radio.Button value={0}>
                    {formatMessage({ id: "random.statistics.month", allowComponent: true })}
                  </Radio.Button>
                  <Radio.Button value={1}>
                    {formatMessage({ id: "random.statistics.week", allowComponent: true  })}
                  </Radio.Button>
                </Radio.Group>
              </Row>
              <Row>
                <ReactEcharts
                  ref={barRef}
                  notMerge={true}
                  style={{ flexGrow: 1 }}
                  option={barOption}
                />
              </Row>
            </Spin>
          </Col>
        </Row>
      </Card>
    </>
  );
};
