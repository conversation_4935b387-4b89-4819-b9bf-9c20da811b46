import {<PERSON><PERSON>, Card, Col, Input, message, Row, Select, Spin, Table, Tooltip} from "antd";
import { FormattedMessage, useTranslation } from "../../common/multilingual/component";
import React from "react";
import {useFetch} from "hooks/request";
import {useAuth} from "../../../context/auth";
import {useSafeState} from "ahooks";
import {Title} from "components/title";
import styled from "@emotion/styled";
import {userStoreHouses} from "../../../api/project_storehouse";
import {allMedicineStorehouseSTAT} from "../../../api/medicine";
import {exportReport} from "../../../api/report";
import {permissions} from "../../../tools/permission";
import { getProjectAttributeConnect } from "../../../api/randomization";
import {AuthWrap} from "../../common/auth-wrap";


export const ProjectDepotIpStatistics = () => {
    const intl = useTranslation();
    const { formatMessage } = intl;
    const auth = useAuth()
    const envId = auth.env ? auth.env.id : null;
    const projectId = auth.project.id;
    const customerId = auth.customerId;


    const [storehouse, setStorehouse] = useSafeState(undefined);
    const [storehouses, setStorehouses] = useSafeState([]);
    const [summaryList, setSummaryList] = useSafeState([]);
    const [ali, setAli] = useSafeState(0);
    
    
    const commonRender = text => (text || text === 0) ? text : '-'
    const { runAsync: getProjectAttributeConnectRun, loading: getProjectAttributeConnectLoading } = useFetch(getProjectAttributeConnect, { manual: true })


    const { runAsync: run_userStoreHouses } = useFetch(userStoreHouses, { manual: true })
    const { runAsync: run_allMedicineStorehouseSTAT, loading } = useFetch(allMedicineStorehouseSTAT, { manual: true })
    React.useEffect(() => {
        getProjectAttributeConnectRun({ projectId: projectId, envId: envId}).then((res) => {
            if(res.data){
                if(res.data.info.connectAli){
                    setAli(1)
                }
            }
        });
        setSummaryList([]);
        run_userStoreHouses({
            customerId: customerId,
            projectId: projectId,
            envId: envId,
            roleId: auth.project.permissions.role_id
        }).then((response) => {
            let list = [];
            if (response) {
                let data = response.data
                data?.forEach(item => {
                    list.push({ id: item.id, name: item.name })
                });
            }
            setStorehouses(list);
        });

        run_allMedicineStorehouseSTAT({
            customerId: customerId,
            projectId: projectId,
            envId: envId,
            storehouseId: storehouse && storehouse !== "all" ? storehouse.split("__")[0] : null,
            roleId: auth.project.permissions.role_id,
        }).then((result) => {
            let response = result.data
            setSummaryList(response.items)
        })
    }, [storehouse]);
    const {runAsync: exportReportRun, loading: exportReportLoading} = useFetch(exportReport, {manual: true})
    const downloadData = () => {
        const params = {
            type:4,
            projectId: projectId,
            envId: envId,
            roleId: auth.project.permissions.role_id,
        };
        exportReportRun(params).then(() => {
            message.success(formatMessage({ id: "report.download.success" }));
        });
    };

    return (
        <>
            <Card
                headStyle={{ padding: "0 16px" }}
                bodyStyle={{ padding: "16px" }}
                title={
                    <Row style={{ display: 'flex', alignItems: 'center' }}>
                        <Title name={<FormattedMessage id={'project.depot.ip.statistics'} />}/>
                        <Tooltip
                            trigger={["hover", "click"]}
                            overlayInnerStyle={{ fontSize: 12, background: "#646566" }}
                            placement="top"
                            title={<FormattedMessage id="project.depot.ip.statistics.info" dark />}>
                            <svg className="iconfont mouse" width={12} height={12} style={{ marginLeft: 4}} >
                                <use xlinkHref="#icon-xinxitishi"/>
                            </svg>
                        </Tooltip>
                    </Row>
                }
            >
                {/*<Spin spinning={false}>*/}
                <Row justify="space-between" align="middle">
                    <Col>
                        {formatMessage({ id: "common.depot" })}：
                        <Select
                            style={{ width: "200px" }}
                            dropdownMatchSelectWidth={false}
                            dropdownStyle={{ maxWidth: 500 }}
                            onChange={(value)=>{setStorehouse(value)}}
                            value={storehouse}
                            showSearch
                            allowClear
                            defaultValue={["all"]}
                            optionFilterProp="children"
                            filterOption={(input, option) => {
                                const childrenText = option.props.children;
                                if (typeof childrenText === 'string') {
                                    return childrenText.trim().toLowerCase().indexOf(input.trim().toLowerCase()) >= 0;
                                }
                                return false;
                            }}
                            getPopupContainer={(triggerNode) => triggerNode.parentElement}
                        >
                            <Select.Option value="all" >
                                {<FormattedMessage id="common.all" />}
                            </Select.Option>
                            {
                                storehouses.map(
                                    it => (
                                        <Select.Option key={it.id} value={it.id + "__" + it.name}>
                                            {it.name}
                                        </Select.Option>
                                    )
                                )
                            }
                        </Select>
                    </Col>
                    <AuthWrap
                        show={permissions(auth.project.permissions, "operation.project.depot.IPStatistics.download")}
                        previewProps={{hideOnPreview: true}}
                    >
                        <Spin spinning={exportReportLoading}>
                            <svg className="iconfont download-hover" onClick={downloadData}>
                                <use xlinkHref="#icon-daochu"/>
                            </svg>
                        </Spin>
                    </AuthWrap>
                </Row>
                <Col span={24}>
                    <Table
                        className="mar-top-15"
                        loading={loading}
                        dataSource={summaryList}
                        size="small"
                        pagination={false}
                        scroll={{ x: 1300 }}
                        rowKey={(record) => (record.id)}
                        style={{ width: "calc(100vw - 526px)" }}
                    >
                        {/* <Table.Column title={<FormattedMessage id="common.depot" />}
                            dataIndex="storehouseName" fixed="left"
                            key="storehouseName"   render={commonRender} /> */}
                        <Table.Column
                            title={<FormattedMessage id="shipment.medicine" />}
                            dataIndex="name" key="name"  render={commonRender}
                        />
                        {/* <Table.Column
                            title={<FormattedMessage id="drug.list.batch" />}
                            dataIndex="batchNumber" render={commonRender} 
                            key="batchNumber"    />
                        <Table.Column title={<FormattedMessage id="projects.statistics.sku.expirationDate" />}
                            dataIndex="expirationDate"  render={commonRender} 
                            key="expirationDate"  /> */}
                        <Table.Column title={<FormattedMessage id="drug.configure.spec" />}
                                      dataIndex="spec" key="spec"
                        />
                        <Table.Column title={<FormattedMessage id="medicine.status.available" />}
                            dataIndex="count" key="count"  render={commonRender} 
                        />
                        <Table.Column title={<FormattedMessage id="medicine.status.InOrder" />}
                            dataIndex="to_be_confirm_count" key="to_be_confirm_count"  render={commonRender} 
                        />
                        <Table.Column title={<FormattedMessage id="medicine.status.delivered" />}
                            dataIndex="to_be_send_count" key="to_be_send_count"  render={commonRender} 
                        />
                        <Table.Column title={<FormattedMessage id="medicine.status.transit" />}
                            dataIndex="in_transit_count" key="in_transit_count"   render={commonRender} 
                        />
                        <Table.Column title={<FormattedMessage id="medicine.status.quarantine" />}
                            dataIndex="quarantined_count" key="quarantined_count"   render={commonRender} 
                        />
                        <Table.Column title={<FormattedMessage id="medicine.status.used" />}
                            dataIndex="used_count" key="used_count"   render={commonRender} 
                        />
                        <Table.Column title={<FormattedMessage id="medicine.status.lose" />}
                            dataIndex="lost_count" key="lost_count"  render={commonRender} 
                        />

                        {
                            ali === 1?
                            <Table.Column title={<FormattedMessage id="medicine.status.receive" />}
                                dataIndex="receive_count" key="receive_count"  render={commonRender} 
                            />:null
                        }
                        {
                            ali === 1?
                            <Table.Column title={<FormattedMessage id="medicine.status.return" />}
                                dataIndex="return_count" key="return_count"  render={commonRender} 
                            />:null
                        }
                        {
                            ali === 1?
                            <Table.Column title={<FormattedMessage id="medicine.status.destroy" />}
                                dataIndex="destroy_count" key="destroy_count"  render={commonRender} 
                            />:null
                        }

                        <Table.Column title={<FormattedMessage id="medicine.status.expired" />}
                            dataIndex="expired_count" key="expired_count"   render={commonRender} 
                        />
                        <Table.Column title={<FormattedMessage id="medicine.status.fzn" />}
                            dataIndex="frozen_count" key="frozen_count"  render={commonRender} 
                        />
                        <Table.Column title={<FormattedMessage id="medicine.status.locked" />}
                            dataIndex="locked_count" key="locked_count"   render={commonRender} 
                        />            
                    </Table>
                </Col>
                {/*</Spin>*/}
            </Card>
        </>
    )
};

const CustomSearch = styled(Input.Search)`
    .ant-input-affix-wrapper {
        height: 32px !important;
    }
`

const CustomButton = styled(Button)`
    margin-right: 7px !important;
`