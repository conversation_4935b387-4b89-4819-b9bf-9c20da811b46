import React from "react";
import {HomeProvider} from "./context";
import {ProjectDynamics} from "./project_dynamics";
import {PageContextProvider} from "../../../context/page";
import {ProjectAnalysis} from "./analysis";
import {ProjectDepotIpStatistics} from "./depot_Ip_statistics";
import {ProjectSiteIpStatistics} from "./site_Ip_statistics";
import {ProjectApprovalTask} from "./project_approval_task";
import {ProjectRandomStatistics} from "./random_statistics";
import {ProjectSubjectStatistics} from "./subject_statistics";
import {isBindRole} from "../../../api/roles";
import {useFetch} from "../../../hooks/request";
import {useAuth} from "../../../context/auth";
import {ProjectOverview} from "./overview";
import {ProjectTimeZone} from "./project_time_zone";
import {useSafeState} from "ahooks";
import {permissions} from "../../../tools/permission";

export const Home = () => {
    const auth = useAuth()
    const customerId = auth.customerId;
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const roleId = auth.project.permissions.role_id;
    const roleScope = auth.project.permissions.scope ? auth.project.permissions.scope : "";

    const [isBlind, setIsBlind] = useSafeState(true);
    const {runAsync: run_isBlindRole} = useFetch(isBindRole, {manual: true})
    const isBindRoleBool = () => {
        run_isBlindRole({
            customerId: customerId,
            envId: envId,
            // cohortId: cohortId,
            projectId: projectId,
            roleId: roleId,
        }).then(
            (result: any) => {
                const data = result.data
                setIsBlind(data)
            }
        )
    };
    React.useEffect(
        () => {
            isBindRoleBool();
        }, [roleId]
    )

    return (
        <HomeProvider>
            <div style={{ display: "flex" }}>
                <div style={{ flex: 1, marginRight: "8px" }}>
                    {
                        <ProjectTimeZone/>
                    }
                    {
                        permissions(auth.project.permissions, "operation.project.status.view") && 
                        <div className="mar-top-8">
                            <ProjectOverview/>
                        </div>
                    }
                    {
                        (permissions(auth.project.permissions, "operation.project.random.view")) && auth.isRandomDispensing?.random?
                        <div className="mar-top-8">
                            <ProjectRandomStatistics/>
                        </div>:null
                    }
                    {
                        permissions(auth.project.permissions, "operation.project.subject.view") &&
                        <div className="mar-top-8">
                            <ProjectSubjectStatistics/>
                        </div>
                    }
                    {
                    // !isBlind &&
                        <>
                            {(roleScope !== "" && roleScope !== "site"&&permissions(auth.project.permissions, "operation.project.depot.IPStatistics.view") )&&
                                <div className="mar-top-8">
                                    <ProjectDepotIpStatistics/>
                                </div>
                            }
                            {(roleScope !== "" && roleScope !== "depot" &&permissions(auth.project.permissions, "operation.project.site.IPStatistics.view") )&&
                                <div className="mar-top-8">
                                    <ProjectSiteIpStatistics/>
                                </div>
                            }
                        </>
                    }
                    {
                        permissions(auth.project.permissions, "operation.project.analysis.view") &&
                        <div className="mar-top-8">
                            <ProjectAnalysis/>
                        </div>
                    }
                </div>
                <div style={{ width: "280px" }}>
                    {
                        permissions(auth.project.permissions, "operation.project.task.view") &&
                        <PageContextProvider>
                            <ProjectApprovalTask/>
                        </PageContextProvider>
                    }

                    {
                        permissions(auth.project.permissions, "operation.project.dynamics.view") &&
                        <div className="mar-top-8">
                            <PageContextProvider>
                                <ProjectDynamics/>
                            </PageContextProvider>
                        </div>
                    }
                </div>
            </div>
        </HomeProvider>
        // <>
        //     <div style={{ height: '100%', background: '#FFF', margin: '8px 8px 0 8px' }}>
        //         <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '95%' }}>
        //             <div style={{ textAlign: 'center' }}>
        //                 <div>
        //                     <img style={{ width: '128px' }} src={Logo} alt=""/>
        //                 </div>
        //                 <div style={{ marginTop: '40px', color: '#2D332F', fontSize: '33px', fontWeight: '600' }}>
        //                     Welcome To Clinflash IRT!
        //                 </div>
        //                 <div style={{ marginTop: '20px', color: '#2D332F', fontSize: '16px', fontWeight: '600' }}>
        //                     "Striving for Excellence With Customers"
        //                 </div>
        //             </div>
        //             <div>
        //                 <img style={{ width: '480px' }} src={Welcome} alt=""/>
        //             </div>
        //         </div>
        //         <div style={{ display: 'flex', justifyContent: 'center', color: '#999999', fontSize: '12px' }}>
        //             ©2020-2021 Jiaxing Clinflash Computer Technology Co., Ltd.
        //         </div>
        //     </div>
        // </>
    )
};
