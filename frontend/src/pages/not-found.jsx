import React from "react";
import {But<PERSON>, Result} from "antd";
import {useNavigate} from "react-router-dom";

export const NotFound = () => {

    const navigate = useNavigate();

    return (
        <Result
            status="warning"
            title="404"
            subTitle="Sorry, the page you visited does not exist."
            extra={<Button onClick={() => navigate("/")}>Back Home</Button>}
        />
    );
}