import React from 'react';
import { Tag } from 'antd';

/**
 * 从文本中提取所有占位符
 * @param text 包含占位符的文本
 * @returns 占位符数组
 */
export const extractPlaceholders = (text: string): string[] => {
    if (!text) return []
    const regex = /\{[^}]+\}/g;
    return text.match(regex) || [];
}


/**
 * 将文本中的占位符高亮显示
 * @param text 包含占位符的文本
 * @returns 带有高亮占位符的React节点
 */
export const highlightPlaceholders = (text: string): React.ReactNode => {
    const placeholders = extractPlaceholders(text)
    if (placeholders.length === 0) return text

    let result: React.ReactNode[] = []
    let lastIndex = 0
    placeholders.forEach((placeholder, index) => {
        const placeholderIndex = text.indexOf(placeholder, lastIndex)
        // 添加占位符前的文本
        if (placeholderIndex > lastIndex) {
            result.push(text.substring(lastIndex, placeholderIndex))
        }
        // 添加高亮的占位符
        result.push(
            <Tag key={index} color="blue" style={{marginRight: 0}}>
                {placeholder}
            </Tag>
        )
        lastIndex = placeholderIndex + placeholder.length
    })
    // 添加最后剩余的文本
    if (lastIndex < text.length) {
        result.push(text.substring(lastIndex))
    }

    return <>{result}</>
}

