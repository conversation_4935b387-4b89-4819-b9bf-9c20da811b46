import React, {useState, useRef, useEffect} from 'react'
import {Button, Select, Spin, message, Card, Space, Row, Col, Statistic, Tag, Divider, Alert, Typography, Tooltip} from "antd";
import {FileSearchOutlined, FilterOutlined, ReloadOutlined, PlusOutlined, WarningOutlined, CheckCircleOutlined, DiffOutlined, InfoCircleOutlined, SettingOutlined} from '@ant-design/icons';
import {useFetch} from "../../../../../hooks/request";
import {flatModules, get, getAllKeys, post, splitModule} from "../util";
import {message as zhmessage} from "../../../../../locales/zh";
import {message as enmessage} from "../../../../../locales/en";
import {LocalesTable} from "./table";
import {allFields, allFlatModules, allModules, editFields} from "../../locales";
import {unuseKeys} from "../special-field/unuse";
import {machineKeys} from "../special-field/machine";
import {backendKeys} from "../special-field/backend";
import {menuModule} from "./menu-permissions";
import PathDiffModal from "./path-diff-modal";
import _ from "lodash";
import {useAtom} from "jotai";
import {localesDataAtom} from "./context";
import {reportModule} from "./report";
import {localesProject} from "../../locales/module";
import {AddFieldModal} from "./add-field-modal";

const { Title, Text } = Typography;

// 直接扫描文件中的id
export const LocalesScanFile = (props) => {

    const [data, setData] = useState([])
    const [showData, setShowData] = useState([])
    const [options, setOptions] = useState([])
    const [localesData, setLocalesData] = useAtom(localesDataAtom)
    const [unManageFields, setUnManageFields] = useState([])
    const addFieldModalRef = useRef(null)
    const pathDiffModalRef = useRef(null)

    // 新增状态管理
    const [currentFilter, setCurrentFilter] = useState(null)
    const [statistics, setStatistics] = useState({
        total: 0,
        uniqueKeys: 0,
        duplicateKeys: 0,
        filtered: 0,
        unManaged: 0,
        translated: 0
    })

    // 表格筛选状态
    const [tableSearch, setTableSearch] = useState({
        path: [], type: '', value: '', excludeOperator: false, excludeMenu: false,
        excludeUnuseKey: false, excludeMachineKey: false, duplicateRemoval: false,
        excludeBackendKey: false, unScan: false
    })

    // 筛选后的数据
    const [filteredData, setFilteredData] = useState([])



    const { run: getFields, loading } = useFetch(() => get('read-file'), {
        manual: true,
        onSuccess: (result) => {
            const module = [...result, menuModule, reportModule]
            for (let i = 0; i < module.length; i++) {
                if (module[i].key === 'project_setting') {
                    module[i] = splitModule(module[i], localesProject[1])
                }
            }

            console.log(module)

            const allKeys = getAllKeys(module)
            module.push({
                key: "un_scan",
                name: "没扫描到的字段",
                children: Object.keys(zhmessage).filter(it => !allKeys.includes(it)).map(it => ({
                    key: it
                }))
            })

            const m = flatModules(module)

            // 对初始数据应用相邻排序
            const sortedFields = sortDataWithDuplicatesAdjacent(m.fields);

            setData(sortedFields)
            setShowData(sortedFields)
            setOptions(m.options)
            setLocalesData({...localesData, scanFile: sortedFields})

            // 自动检测未管理字段
            const mangeKeys = allFields.map(it => it.key)
            const unManageData = sortedFields.filter(it => !mangeKeys.includes(it.key))
            setUnManageFields(unManageData)

            // 初始化筛选数据
            setFilteredData(sortedFields);

            // 计算统计信息
            updateStatistics(sortedFields, sortedFields)

            // 显示检测结果
            if (unManageData.length > 0) {
                message.info(`检测到 ${unManageData.length} 个未管理的字段，可以点击"批量添加字段"按钮进行管理`);
            }
        },
        onError: (e) => {
            console.log(e)
        }
    })

    // 更新统计信息
    const updateStatistics = (allData, filteredData) => {
        // 按key分组统计
        const keyGroups = _.groupBy(allData, 'key');
        const uniqueKeys = Object.keys(keyGroups);
        const duplicateKeys = uniqueKeys.filter(key => keyGroups[key].length > 1);

        // 翻译统计（按唯一key计算）
        const translated = uniqueKeys.filter(key =>
            zhmessage[key] && enmessage[key] && zhmessage[key] !== enmessage[key]
        ).length;

        setStatistics({
            total: allData.length,
            uniqueKeys: uniqueKeys.length,
            duplicateKeys: duplicateKeys.length,
            filtered: filteredData.length,
            unManaged: unManageFields.length,
            translated: translated
        });
    };

    // 文本匹配函数
    const like = (text1, text2) => {
        if (!text1 || !text2) return false
        return text1?.toUpperCase().indexOf(text2?.toUpperCase()) !== -1
    }

    // 将重复字段排序到相邻位置
    const sortDataWithDuplicatesAdjacent = (data) => {
        // 统计每个key的出现次数
        const keyCounts = _.countBy(data, 'key');

        // 分离重复字段和非重复字段
        const duplicateFields = [];
        const singleFields = [];

        data.forEach(item => {
            if (keyCounts[item.key] > 1) {
                duplicateFields.push(item);
            } else {
                singleFields.push(item);
            }
        });

        // 对重复字段按key分组并排序
        const groupedDuplicates = _.groupBy(duplicateFields, 'key');
        const sortedDuplicateGroups = Object.keys(groupedDuplicates)
            .sort()
            .map(key => {
                // 每组内按路径排序
                return groupedDuplicates[key].sort((a, b) => {
                    const pathA = a.path ? a.path.join('/') : '';
                    const pathB = b.path ? b.path.join('/') : '';
                    return pathA.localeCompare(pathB);
                });
            });

        // 展平重复字段组
        const sortedDuplicates = sortedDuplicateGroups.flat();

        // 对单个字段按key排序
        const sortedSingles = singleFields.sort((a, b) => a.key.localeCompare(b.key));

        // 重复字段在前，单个字段在后
        return [...sortedDuplicates, ...sortedSingles];
    };

    // 应用表格筛选逻辑
    const applyTableFilter = (sourceData, searchParams) => {
        const {path, type, value, excludeOperator, excludeMenu,
            excludeUnuseKey, excludeMachineKey, duplicateRemoval, excludeBackendKey, unScan} = searchParams

        let filtered = sourceData
            .filter(it => !path || path.length === 0 || it.path.slice(0, path.length).toString() === path.toString())
            .filter(it => !type || it.type === type)
            .filter(it => !value || like(it.key, value) || like(zhmessage[it.key], value) || like(enmessage[it.key], value))
            .filter(it => !excludeOperator || !it.key.startsWith('operation'))
            .filter(it => !excludeMenu || !it.key.startsWith('menu'))
            .filter(it => !excludeUnuseKey || !unuseKeys.includes(it.key))
            .filter(it => !excludeMachineKey || !machineKeys.includes(it.key))
            .filter(it => !excludeBackendKey || !backendKeys.includes(it.key))
            .filter(it => !unScan || it.path[0] !== 'un_scan')

        if (duplicateRemoval) {
            filtered = _.uniqBy(filtered, 'key');
        }

        // 将重复字段排序到相邻位置
        return sortDataWithDuplicatesAdjacent(filtered);
    }

    // 更新筛选数据
    const updateFilteredData = () => {
        const filtered = applyTableFilter(showData, tableSearch);
        setFilteredData(filtered);
        updateStatistics(data, filtered);
    };

    // 监听数据和筛选条件变化
    useEffect(() => {
        if (showData.length > 0) {
            const filtered = applyTableFilter(showData, tableSearch);
            setFilteredData(filtered);
            updateStatistics(data, filtered);
        }
    }, [showData, tableSearch]);

    // 强制重新渲染以更新筛选数量
    const [, forceUpdate] = useState({});
    useEffect(() => {
        if (data.length > 0) {
            forceUpdate({});
        }
    }, [data, allFields]);

    // 通用筛选函数
    const applyFilter = (filterFn, filterName) => {
        const filtered = data.filter(filterFn);
        setShowData(filtered);
        setCurrentFilter(filterName);
        updateStatistics(data, filtered);
    };

    const unTranslation = () => {
        applyFilter(
            it => !zhmessage[it.key] || !enmessage[it.key] || (zhmessage[it.key] === enmessage[it.key]),
            '未完全翻译字段'
        );
    }

    const unuseButUse = () => {
        applyFilter(
            it => it.path.toString() !== 'un_scan' && unuseKeys.includes(it.key),
            '应该废弃但页面中使用到的字段'
        );
    }

    const unMange = () => {
        const mangeKeys = allFields.map(it => it.key)
        const unManageData = data.filter(it => !mangeKeys.includes(it.key))
        // 只更新未管理字段数据，不改变显示
        setUnManageFields(unManageData)
        // 更新统计信息
        updateStatistics(data, showData)
        setShowData(unManageData)

        // 显示提示信息
        if (unManageData.length > 0) {
            message.success(`发现 ${unManageData.length} 个未管理的字段，可以点击"批量添加"按钮进行管理`);
        } else {
            message.info('所有字段都已被管理');
        }
    }

    // 已经废弃的字段
    const unuseField = () => {
        applyFilter(
            it => unuseKeys.includes(it.key),
            '已经废弃的字段'
        );
    }

    const unuseButInMange = () => {
        const allKeys = allFields.map(it => it.key)
        applyFilter(
            it => unuseKeys.includes(it.key) && allKeys.includes(it.key),
            '应该废弃但还是纳入管理的字段'
        );
    }

    const noScanButInMange = () => {
        const allKeys = allFields.map(it => it.key)
        const unScanKeys = data.filter(it => it.path[0] === 'un_scan').map(it => it.key)
        applyFilter(
            it => allKeys.includes(it.key) && unScanKeys.includes(it.key),
            '没扫描到但是纳入管理的字段'
        );
    }


    const repeatField = () => {
        const counts = _.countBy(data, 'key')
        const repeatNames = Object.keys(counts).filter(key => counts[key] > 1)

        // 按key字母顺序排序，确保相同key的记录聚集在一起
        const sortedRepeatNames = repeatNames.sort();

        // 按排序后的key顺序，将相同key的记录放在一起
        const groupedData = sortedRepeatNames.reduce((res, key) => {
            const keyRecords = data.filter(it => it.key === key);
            // 在每个key组内，按路径排序，让展示更有序
            const sortedKeyRecords = keyRecords.sort((a, b) => {
                const pathA = a.path ? a.path.join('/') : '';
                const pathB = b.path ? b.path.join('/') : '';
                return pathA.localeCompare(pathB);
            });

            res = res.concat(sortedKeyRecords);
            return res;
        }, []);

        // 直接设置分组后的数据
        setShowData(groupedData);
        setCurrentFilter('重复字段');
        updateStatistics(data, groupedData);
    }

    // 显示全部数据
    const showAll = () => {
        const sortedData = sortDataWithDuplicatesAdjacent(data);
        setShowData(sortedData)
        setCurrentFilter(null)
        updateStatistics(data, sortedData)
    }

    const reportField = () => {
        const reportData = data.filter(it => it.key.startsWith('report.'));
        setShowData(reportData);
        setCurrentFilter('报表字段');
        updateStatistics(data, reportData);
    }

    const pathNotSame = () => {
        const pathMismatchData = data.filter(it =>
            !!editFields.find(f => f.key === it.key && f.path?.join('/') !== it.path?.join('/'))
        );
        setShowData(pathMismatchData);
        setCurrentFilter('路径不匹配字段');
        updateStatistics(data, pathMismatchData);
    }

    const notLocales = () => {
        const keys = Object.keys(zhmessage);
        const notConfiguredData = data.filter(it => !keys.includes(it.key));
        setShowData(notConfiguredData);
        setCurrentFilter('翻译缺失字段');
        updateStatistics(data, notConfiguredData);
    }

    const translationIsSame = () => {
        const sameData = _.uniqBy(data, 'key');
        const counts = _.countBy(sameData, 'value');
        const repeatNames = Object.keys(counts).filter(key => counts[key] > 1);
        const translationSameData = repeatNames.reduce((res, item) => {
            res = res.concat(sameData.filter(it => it.value === item));
            return res;
        }, []);
        setShowData(translationSameData);
        setCurrentFilter('翻译内容完全一致字段');
        updateStatistics(data, translationSameData);
    }


    // 中文一致但是英文不一致的字段
    const partSame = () => {
        const messageList = data.map(it => ({
            ...it,
            cn: zhmessage[it.key],
            en: enmessage[it.key]
        }));
        const d = _.uniqWith(messageList, (one, two) => one.cn === two.cn && one.en === two.en);
        const counts = _.countBy(d, 'cn');
        const repeatNames = Object.keys(counts).filter(key => counts[key] > 1);
        const partSameData = repeatNames.reduce((res, item) => {
            res = res.concat(d.filter(it => it.cn === item));
            return res;
        }, []);
        setShowData(partSameData);
        setCurrentFilter('中文一致英文不一致字段');
        updateStatistics(data, partSameData);
    }


    // 打开添加字段弹窗
    const openAddFieldModal = () => {
        if (unManageFields.length === 0) {
            message.warning('请先过滤出未管理的字段');
            return;
        }
        addFieldModalRef.current?.open();
    };

    // 打开路径差异弹窗
    const openPathDiffModal = () => {
        if (pathDiffModalRef.current) {
            pathDiffModalRef.current.open(options, allModules);
        }
    };

    // 处理字段添加成功后的回调
    const handleAddFieldSuccess = () => {
        addFieldModalRef.current?.close();
        // 清空未管理字段列表，因为已经添加完成
        setUnManageFields([]);
        // 更新统计信息
        updateStatistics(data, showData);
        // 显示成功提示
        message.success('字段添加完成，所有字段已被管理！');
    };

    // 计算各种筛选条件的数量
    const getFilterCounts = () => {
        if (data.length === 0) return {};

        const mangeKeys = allFields.map(it => it.key);
        const keyGroups = _.groupBy(data, 'key');
        const uniqueKeys = Object.keys(keyGroups);
        const duplicateKeys = uniqueKeys.filter(key => keyGroups[key].length > 1);

        return {
            // 字段内容维度
            unTranslated: uniqueKeys.filter(key =>
                !zhmessage[key] || !enmessage[key] || (zhmessage[key] === enmessage[key])
            ).length,
            translationSame: (() => {
                const sameData = _.uniqBy(data, 'key');
                const counts = _.countBy(sameData, 'value');
                return Object.keys(counts).filter(key => counts[key] > 1).reduce((sum, key) => sum + counts[key], 0);
            })(),
            cnSameEnDiff: (() => {
                const messageList = data.map(it => ({
                    ...it,
                    cn: zhmessage[it.key],
                    en: enmessage[it.key]
                }));
                const d = _.uniqWith(messageList, (one, two) => one.cn === two.cn && one.en === two.en);
                const counts = _.countBy(d, 'cn');
                return Object.keys(counts).filter(key => counts[key] > 1).reduce((sum, key) => sum + counts[key], 0);
            })(),
            duplicateKeys: duplicateKeys.length,
            missingTranslation: data.filter(it => !Object.keys(zhmessage).includes(it.key)).length,

            // 模块分布维度
            crossModule: duplicateKeys.length, // 跨模块使用的字段数量
            singleModule: uniqueKeys.length - duplicateKeys.length, // 单模块使用的字段数量
            reportFields: data.filter(it => it.key.startsWith('report.')).length,

            // 管理状态维度
            unManaged: data.filter(it => !mangeKeys.includes(it.key)).length,
            unusedButManaged: data.filter(it => unuseKeys.includes(it.key) && mangeKeys.includes(it.key)).length,
            managedButNotScanned: (() => {
                const unScanKeys = data.filter(it => it.path[0] === 'un_scan').map(it => it.key);
                return data.filter(it => mangeKeys.includes(it.key) && unScanKeys.includes(it.key)).length;
            })(),
            pathMismatch: data.filter(it =>
                !!editFields.find(f => f.key === it.key && f.path?.join('/') !== it.path?.join('/'))
            ).length,
            unusedButUsed: data.filter(it => it.path.toString() !== 'un_scan' && unuseKeys.includes(it.key)).length,
            unusedFields: data.filter(it => unuseKeys.includes(it.key)).length,

            // 问题字段检测 - 使用与getProblemFields完全相同的逻辑
            problemFields: (() => {
                const problemFields = [];
                const scanKeyGroups = _.groupBy(data, 'key');
                const managedKeyGroups = _.groupBy(allFields, 'key');

                // 检查路径是否在管理配置中存在
                const isPathInAllFieldsCount = (path) => {
                    const pathString = Array.isArray(path) ? path.join('/') : path;
                    return allFields.some(field => field.path?.join('/') === pathString);
                };

                Object.keys(scanKeyGroups).forEach(key => {
                    const scanItems = scanKeyGroups[key];
                    const managedFields = managedKeyGroups[key] || [];

                    if (managedFields.length > 0) {
                        // 获取有效的扫描路径（排除un_scan）
                        const validScanItems = scanItems.filter(item => {
                            const itemPath = item.path?.join('/') || '';
                            return itemPath && itemPath !== 'un_scan';
                        });

                        if (validScanItems.length === 0) {
                            return; // 没有有效的扫描路径，跳过
                        }

                        // 获取唯一的扫描路径和管理路径
                        const scanPaths = validScanItems.map(item => item.path?.join('/') || '');
                        const uniqueScanPaths = [...new Set(scanPaths)];
                        const managedPaths = managedFields.map(field => field.path?.join('/') || '');
                        const uniqueManagedPaths = [...new Set(managedPaths)];

                        // 情况1: 管理配置不完整（欠管理）
                        const unmanagedPaths = uniqueScanPaths.filter(scanPath => !uniqueManagedPaths.includes(scanPath));
                        if (unmanagedPaths.length > 0) {
                            const problemCount = validScanItems.filter(item => {
                                const itemScanPath = item.path?.join('/') || '';
                                // 只统计扫描路径在 allFields 中有对应管理配置的字段实例
                                return unmanagedPaths.includes(itemScanPath) && isPathInAllFieldsCount(itemScanPath);
                            }).length;
                            problemFields.push(...Array(problemCount).fill(null));
                        }

                        // 情况2: 管理配置过度（超管理）
                        const overManagedPaths = uniqueManagedPaths.filter(managedPath => !uniqueScanPaths.includes(managedPath));
                        if (overManagedPaths.length > 0) {
                            // 每个多余的管理路径算作一个问题（保持现有逻辑）
                            problemFields.push(...Array(overManagedPaths.length).fill(null));
                        }

                        // 情况3: 单路径字段管理错误
                        if (uniqueScanPaths.length === 1 && uniqueManagedPaths.length === 1) {
                            const singleScanPath = uniqueScanPaths[0];
                            const singleManagedPath = uniqueManagedPaths[0];

                            // 只统计扫描路径在 allFields 中有对应管理配置的字段实例
                            if (singleScanPath !== singleManagedPath && isPathInAllFieldsCount(singleScanPath)) {
                                problemFields.push(...Array(validScanItems.length).fill(null));
                            }
                        }
                    }
                });

                return problemFields.length;
            })()
        };
    };

    const filterCounts = getFilterCounts();

    // 检查路径是否在管理配置中存在
    const isPathInAllFields = (path) => {
        const pathString = Array.isArray(path) ? path.join('/') : path;
        return allFields.some(field => field.path?.join('/') === pathString);
    };

    // 获取问题字段详细信息
    const getProblemFields = () => {
        const problemFields = [];

        // 按key分组扫描数据
        const scanKeyGroups = _.groupBy(data, 'key');

        // 按key分组管理配置数据
        const managedKeyGroups = _.groupBy(allFields, 'key');

        // 处理扫描到的字段
        Object.keys(scanKeyGroups).forEach(key => {
            const scanItems = scanKeyGroups[key];
            const managedFields = managedKeyGroups[key] || [];

            if (managedFields.length > 0) {
                // 获取有效的扫描路径（排除un_scan）
                const validScanItems = scanItems.filter(item => {
                    const itemPath = item.path?.join('/') || '';
                    return itemPath && itemPath !== 'un_scan';
                });

                if (validScanItems.length === 0) {
                    return; // 没有有效的扫描路径，跳过
                }

                // 获取唯一的扫描路径和管理路径
                const scanPaths = validScanItems.map(item => item.path?.join('/') || '');
                const uniqueScanPaths = [...new Set(scanPaths)];
                const managedPaths = managedFields.map(field => field.path?.join('/') || '');
                const uniqueManagedPaths = [...new Set(managedPaths)];

                // 情况1: 管理配置不完整（欠管理）- 扫描路径多于管理路径
                const unmanagedPaths = uniqueScanPaths.filter(scanPath => !uniqueManagedPaths.includes(scanPath));
                if (unmanagedPaths.length > 0) {
                    validScanItems.forEach(item => {
                        const itemScanPath = item.path?.join('/') || '';
                        // 只显示扫描路径在 allFields 中有对应管理配置的字段实例
                        if (unmanagedPaths.includes(itemScanPath) && isPathInAllFields(itemScanPath)) {
                            problemFields.push({
                                ...item,
                                problemType: 'cross_path_incomplete',
                                problemDescription: `管理配置不完整（欠管理）`,
                                problemDetail: `字段在 ${uniqueScanPaths.length} 个路径中使用，但仅在 ${uniqueManagedPaths.length} 个路径中被管理，缺少对路径 "${itemScanPath}" 的管理配置`,
                                scanPath: itemScanPath,
                                managedPaths: uniqueManagedPaths,
                                allScanPaths: uniqueScanPaths,
                                severity: 'warning', // 橙色警告
                                isRealField: true // 标识这是真实的扫描字段
                            });
                        }
                    });
                }

                // 情况2: 管理配置过度（超管理）- 管理路径多于扫描路径
                const overManagedPaths = uniqueManagedPaths.filter(managedPath => !uniqueScanPaths.includes(managedPath));
                if (overManagedPaths.length > 0) {
                    overManagedPaths.forEach(overManagedPath => {
                        // 创建虚拟的问题字段记录来表示多出的管理路径
                        const managedField = managedFields.find(field => field.path?.join('/') === overManagedPath);
                        problemFields.push({
                            key: key,
                            path: managedField?.path || [],
                            value: `[管理配置] ${zhmessage[key] || key}`,
                            problemType: 'over_managed',
                            problemDescription: `管理配置过度（超管理）`,
                            problemDetail: `字段在 ${uniqueScanPaths.length} 个路径中使用，但却在 ${uniqueManagedPaths.length} 个路径中被管理，路径 "${overManagedPath}" 存在多余的管理配置`,
                            scanPath: overManagedPath, // 这里scanPath实际是多余的管理路径
                            managedPaths: uniqueManagedPaths,
                            allScanPaths: uniqueScanPaths,
                            severity: 'error', // 红色错误
                            isRealField: false, // 标识这是虚拟的管理配置字段
                            isOverManaged: true // 特殊标识超管理问题
                        });
                    });
                }

                // 情况3: 单路径字段管理错误（路径完全不匹配）
                if (uniqueScanPaths.length === 1 && uniqueManagedPaths.length === 1) {
                    const singleScanPath = uniqueScanPaths[0];
                    const singleManagedPath = uniqueManagedPaths[0];

                    // 只显示扫描路径在 allFields 中有对应管理配置的字段实例
                    if (singleScanPath !== singleManagedPath && isPathInAllFields(singleScanPath)) {
                        validScanItems.forEach(item => {
                            problemFields.push({
                                ...item,
                                problemType: 'single_path_mismatch',
                                problemDescription: `单路径字段管理错误`,
                                problemDetail: `字段的实际使用路径与管理配置路径不一致`,
                                scanPath: singleScanPath,
                                managedPaths: uniqueManagedPaths,
                                severity: 'error', // 红色错误
                                isRealField: true
                            });
                        });
                    }
                }
            }
        });

        // 最终过滤：对于真实字段，确保扫描路径与管理路径不同；对于虚拟字段，直接包含
        return problemFields.filter(field =>
            !field.isRealField || // 虚拟字段（超管理）直接包含
            !field.managedPaths?.includes(field.scanPath) // 真实字段需要路径不匹配
        );
    };

    // 问题字段筛选函数
    const showProblemFields = () => {
        const problemFields = getProblemFields();
        setShowData(problemFields);
        setCurrentFilter('问题字段检测');
        updateStatistics(data, problemFields);

        if (problemFields.length > 0) {
            const overManagedCount = problemFields.filter(f => f.isOverManaged).length;
            const underManagedCount = problemFields.filter(f => f.problemType === 'cross_path_incomplete').length;
            const mismatchCount = problemFields.filter(f => f.problemType === 'single_path_mismatch').length;

            let messageText = `检测到 ${problemFields.length} 个路径配置问题字段`;

            const details = [];
            if (overManagedCount > 0) details.push(`${overManagedCount} 个超管理`);
            if (underManagedCount > 0) details.push(`${underManagedCount} 个欠管理`);
            if (mismatchCount > 0) details.push(`${mismatchCount} 个路径错误`);

            if (details.length > 0) {
                messageText += `（${details.join('，')}）`;
            }

            message.warning(messageText);
        } else {
            message.success('未检测到路径配置问题，所有字段路径配置正确');
        }
    };

    // 分组筛选选项
    const filterOptions = [
        {
            label: '字段内容维度',
            options: [
                {
                    label: `未完全翻译字段 (${filterCounts.unTranslated || 0})`,
                    value: 'unTranslation',
                    onClick: unTranslation,
                    tooltip: '中英文翻译完全一致或缺失翻译的字段'
                },
                {
                    label: `翻译内容完全一致字段 (${filterCounts.translationSame || 0})`,
                    value: 'translationSame',
                    onClick: translationIsSame,
                    tooltip: '多个不同key但翻译内容完全相同的字段'
                },
                {
                    label: `中文一致英文不一致字段 (${filterCounts.cnSameEnDiff || 0})`,
                    value: 'partSame',
                    onClick: partSame,
                    tooltip: '中文翻译相同但英文翻译不同的字段'
                },
                {
                    label: `重复Key字段 (${filterCounts.duplicateKeys || 0})`,
                    value: 'repeatField',
                    onClick: repeatField,
                    tooltip: '存在重复key的字段，按key分组显示'
                },
                {
                    label: `翻译缺失字段 (${filterCounts.missingTranslation || 0})`,
                    value: 'notConfigured',
                    onClick: notLocales,
                    tooltip: '扫描到但没有配置翻译的字段'
                }
            ]
        },
        {
            label: '模块分布维度',
            options: [
                {
                    label: `跨模块使用字段 (${filterCounts.crossModule || 0})`,
                    value: 'crossModule',
                    onClick: repeatField,
                    tooltip: '在多个模块中都有使用的字段'
                },
                {
                    label: `单模块使用字段 (${filterCounts.singleModule || 0})`,
                    value: 'singleModule',
                    onClick: () => {
                        const keyGroups = _.groupBy(data, 'key');
                        const singleKeys = Object.keys(keyGroups).filter(key => keyGroups[key].length === 1);
                        const singleData = data.filter(it => singleKeys.includes(it.key));
                        setShowData(singleData);
                        setCurrentFilter('单模块使用字段');
                        updateStatistics(data, singleData);
                    },
                    tooltip: '仅在单个模块中使用的字段'
                },
                {
                    label: `报表字段 (${filterCounts.reportFields || 0})`,
                    value: 'reportField',
                    onClick: reportField,
                    tooltip: '报表相关的多语言字段'
                }
            ]
        },
        {
            label: '管理状态维度',
            options: [
                {
                    label: `未纳入管理字段 (${filterCounts.unManaged || 0})`,
                    value: 'unMange',
                    onClick: unMange,
                    tooltip: '扫描到但未纳入多语言管理的字段'
                },
                {
                    label: `已废弃但仍管理字段 (${filterCounts.unusedButManaged || 0})`,
                    value: 'unuseButInMange',
                    onClick: unuseButInMange,
                    tooltip: '应该废弃但还在管理列表中的字段'
                },
                {
                    label: `已管理但未扫描字段 (${filterCounts.managedButNotScanned || 0})`,
                    value: 'noScanButInMange',
                    onClick: noScanButInMange,
                    tooltip: '在管理列表中但页面代码中未扫描到的字段'
                },
                {
                    label: `路径不匹配字段 (${filterCounts.pathMismatch || 0})`,
                    value: 'pathNotSame',
                    onClick: pathNotSame,
                    tooltip: '扫描路径与系统预定义路径不一致的字段'
                },
                {
                    label: `废弃但仍使用字段 (${filterCounts.unusedButUsed || 0})`,
                    value: 'unuseButUse',
                    onClick: unuseButUse,
                    tooltip: '标记为废弃但页面中仍在使用的字段'
                },
                {
                    label: `已废弃字段 (${filterCounts.unusedFields || 0})`,
                    value: 'unuseField',
                    onClick: unuseField,
                    tooltip: '所有标记为废弃的字段'
                },
                {
                    label: `问题字段检测 (${filterCounts.problemFields || 0})`,
                    value: 'problemFields',
                    onClick: showProblemFields,
                    tooltip: '检测扫描路径与管理配置路径不一致的问题字段，包括跨路径管理不完整和单路径管理错误'
                }
            ]
        }
    ];

    return (
        <div style={{
            height: 'calc(100vh - 24px)',
            display: 'flex',
            flexDirection: 'column',
            backgroundColor: '#f5f5f5',
            overflow: 'hidden'
        }}>
            <Spin spinning={loading} style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                {/* 固定头部区域 */}
                <div style={{
                    padding: '8px 16px 0 16px',
                    flexShrink: 0
                }}>
                    {/* 页面标题 - 单行布局 */}
                    <div style={{
                        marginBottom: '8px',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '12px'
                    }}>
                        <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
                            <FileSearchOutlined style={{ marginRight: '8px' }} />
                            多语言字段管理
                        </Title>
                        <Text type="secondary" style={{ fontSize: '12px', color: '#999' }}>
                            扫描、筛选和管理项目中的多语言字段
                        </Text>
                    </div>

                    {/* 紧凑统计面板 */}
                    {data.length > 0 && (
                        <Card size="small" style={{ marginBottom: '8px' }}>
                            <Row gutter={[8, 0]}>
                                <Col span={4}>
                                    <Statistic
                                        title="总字段数"
                                        value={statistics.total}
                                        prefix={<FileSearchOutlined style={{ color: '#1890ff' }} />}
                                        valueStyle={{ color: '#1890ff', fontSize: '14px' }}
                                        style={{ textAlign: 'center' }}
                                    />
                                </Col>
                                <Col span={4}>
                                    <Statistic
                                        title="唯一Key数"
                                        value={statistics.uniqueKeys}
                                        prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                                        valueStyle={{ color: '#52c41a', fontSize: '14px' }}
                                        style={{ textAlign: 'center' }}
                                    />
                                </Col>
                                <Col span={4}>
                                    <Statistic
                                        title="重复Key数"
                                        value={statistics.duplicateKeys}
                                        prefix={<WarningOutlined style={{ color: '#ff4d4f' }} />}
                                        valueStyle={{ color: '#ff4d4f', fontSize: '14px' }}
                                        style={{ textAlign: 'center' }}
                                    />
                                </Col>
                                <Col span={4}>
                                    <Statistic
                                        title="当前显示"
                                        value={statistics.filtered}
                                        prefix={<FilterOutlined style={{ color: '#722ed1' }} />}
                                        valueStyle={{ color: '#722ed1', fontSize: '14px' }}
                                        style={{ textAlign: 'center' }}
                                    />
                                </Col>
                                <Col span={4}>
                                    <Statistic
                                        title="未管理字段"
                                        value={statistics.unManaged}
                                        prefix={<WarningOutlined style={{ color: '#faad14' }} />}
                                        valueStyle={{ color: '#faad14', fontSize: '14px' }}
                                        style={{ textAlign: 'center' }}
                                    />
                                </Col>
                                <Col span={4}>
                                    <Statistic
                                        title="已翻译字段"
                                        value={statistics.translated}
                                        prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                                        valueStyle={{ color: '#52c41a', fontSize: '14px' }}
                                        style={{ textAlign: 'center' }}
                                    />
                                </Col>
                            </Row>
                        </Card>
                    )}

                    {/* 紧凑操作区域 */}
                    <Card size="small" style={{ marginBottom: '8px' }}>
                        <Row gutter={[16, 8]} align="middle">
                            <Col span={16}>
                                <Space wrap size="small">
                                    <Button
                                        type="primary"
                                        icon={<FileSearchOutlined />}
                                        onClick={getFields}
                                        disabled={data.length > 0}
                                        size="small"
                                    >
                                        读取字段
                                    </Button>

                                    <Select
                                        allowClear
                                        placeholder="选择过滤类型"
                                        style={{ width: '200px' }}
                                        size="small"
                                        value={currentFilter ? filterOptions.flatMap(group => group.options).find(f => f.label.includes(currentFilter))?.value : undefined}
                                        onChange={(value) => {
                                            if (!value) {
                                                showAll();
                                                return;
                                            }
                                            const option = filterOptions.flatMap(group => group.options).find(opt => opt.value === value);
                                            if (option) {
                                                option.onClick();
                                            }
                                        }}
                                        dropdownRender={(menu) => (
                                            <div>
                                                {menu}
                                                <Divider style={{ margin: '8px 0' }} />
                                                <div style={{
                                                    padding: '8px 12px',
                                                    fontSize: '12px',
                                                    color: '#666',
                                                    backgroundColor: '#f5f5f5',
                                                    borderRadius: '4px',
                                                    margin: '0 8px 8px 8px'
                                                }}>
                                                    <InfoCircleOutlined style={{ marginRight: '4px', color: '#1890ff' }} />
                                                    支持多维度筛选，数字表示匹配的字段数量
                                                </div>
                                            </div>
                                        )}
                                    >
                                        {filterOptions.map(group => (
                                            <Select.OptGroup
                                                key={group.label}
                                                label={
                                                    <span style={{
                                                        fontWeight: 'bold',
                                                        color: '#1890ff',
                                                        fontSize: '13px'
                                                    }}>
                                                        {group.label}
                                                    </span>
                                                }
                                            >
                                                {group.options.map(option => (
                                                    <Select.Option
                                                        key={option.value}
                                                        value={option.value}
                                                        title={option.tooltip}
                                                        style={{ paddingLeft: '24px' }}
                                                    >
                                                        <Tooltip title={option.tooltip} placement="right">
                                                            <span style={{ fontSize: '13px' }}>
                                                                {option.label}
                                                            </span>
                                                        </Tooltip>
                                                    </Select.Option>
                                                ))}
                                            </Select.OptGroup>
                                        ))}
                                    </Select>

                                    <Button
                                        icon={<FilterOutlined />}
                                        onClick={() => {
                                            const uniqueData = _.uniqBy(data, 'key');
                                            setShowData(uniqueData);
                                            setCurrentFilter('去重显示');
                                            updateStatistics(data, uniqueData);
                                        }}
                                        disabled={data.length === 0}
                                        size="small"
                                    >
                                        去重
                                    </Button>

                                    <Button
                                        icon={<WarningOutlined />}
                                        onClick={repeatField}
                                        disabled={data.length === 0 || statistics.duplicateKeys === 0}
                                        size="small"
                                        danger={statistics.duplicateKeys > 0}
                                    >
                                        重复字段 ({statistics.duplicateKeys})
                                    </Button>

                                    {filterCounts.unManaged > 0 && (
                                        <Button
                                            icon={<PlusOutlined />}
                                            onClick={unMange}
                                            size="small"
                                            type="dashed"
                                            style={{ color: '#fa8c16', borderColor: '#fa8c16' }}
                                        >
                                            未管理 ({filterCounts.unManaged})
                                        </Button>
                                    )}

                                    {filterCounts.unTranslated > 0 && (
                                        <Button
                                            icon={<WarningOutlined />}
                                            onClick={unTranslation}
                                            size="small"
                                            type="dashed"
                                            style={{ color: '#ff4d4f', borderColor: '#ff4d4f' }}
                                        >
                                            未翻译 ({filterCounts.unTranslated})
                                        </Button>
                                    )}

                                    {filterCounts.problemFields > 0 && (
                                        <>
                                            <Button
                                                icon={<WarningOutlined />}
                                                onClick={showProblemFields}
                                                size="small"
                                                type="primary"
                                                danger
                                                style={{
                                                    backgroundColor: '#ff4d4f',
                                                    borderColor: '#ff4d4f',
                                                    fontWeight: 'bold'
                                                }}
                                            >
                                                问题字段检测 ({filterCounts.problemFields})
                                            </Button>
                                            <Button
                                                icon={<SettingOutlined />}
                                                onClick={() => {
                                                    const problemFields = getProblemFields();
                                                    if (problemFields.length === 0) {
                                                        message.info('当前没有检测到问题字段');
                                                        return;
                                                    }
                                                    // 打开批量处理模态框
                                                    addFieldModalRef.current?.open('problem', problemFields);
                                                }}
                                                size="small"
                                                type="default"
                                                style={{
                                                    borderColor: '#fa8c16',
                                                    color: '#fa8c16',
                                                    fontWeight: 'bold'
                                                }}
                                            >
                                                批量处理 ({filterCounts.problemFields})
                                            </Button>
                                        </>
                                    )}

                                    <Button
                                        icon={<ReloadOutlined />}
                                        onClick={showAll}
                                        disabled={data.length === 0}
                                        size="small"
                                    >
                                        重置
                                    </Button>

                                    <Button
                                        icon={<DiffOutlined />}
                                        onClick={openPathDiffModal}
                                        disabled={data.length === 0}
                                        size="small"
                                        style={{
                                            color: '#fa8c16',
                                            borderColor: '#fa8c16'
                                        }}
                                    >
                                        差异路径
                                    </Button>
                                </Space>
                            </Col>

                            <Col span={8} style={{ textAlign: 'right' }}>
                                <Space size="small">
                                    {currentFilter && (
                                        <Tag color="blue" style={{ margin: 0, fontSize: '11px' }}>
                                            {currentFilter}
                                        </Tag>
                                    )}
                                    {unManageFields.length > 0 && (
                                        <Button
                                            type="primary"
                                            icon={<PlusOutlined />}
                                            onClick={openAddFieldModal}
                                            size="small"
                                        >
                                            批量添加字段 ({unManageFields.length})
                                        </Button>
                                    )}
                                    {data.length > 0 && unManageFields.length === 0 && (
                                        <Tag color="green" style={{ fontSize: '11px' }}>
                                            所有字段已管理
                                        </Tag>
                                    )}
                                </Space>
                            </Col>
                        </Row>
                    </Card>
                </div>

                {/* 可滚动的表格区域 */}
                <div style={{
                    flex: 1,
                    padding: '0 16px 8px 16px',
                    overflow: 'hidden',
                    display: 'flex',
                    flexDirection: 'column'
                }}>
                    <div style={{
                        flex: 1,
                        backgroundColor: '#fff',
                        borderRadius: '6px',
                        border: '1px solid #f0f0f0',
                        overflow: 'hidden',
                        display: 'flex',
                        flexDirection: 'column'
                    }}>
                        <LocalesTable
                            dataSource={showData}
                            modules={options}
                            filteredData={filteredData}
                            search={tableSearch}
                            onSearchChange={setTableSearch}
                            allFields={allFields}
                            allModules={allFlatModules}
                        />
                    </div>
                </div>

                <AddFieldModal
                    ref={addFieldModalRef}
                    onSuccess={handleAddFieldSuccess}
                    dataSource={unManageFields}
                    problemFields={getProblemFields()}
                    modules={allModules}
                    flatModules={allFlatModules}
                    options={options}
                />

                <PathDiffModal
                    ref={pathDiffModalRef}
                />
            </Spin>
        </div>
    )

}