import React, {useMemo, useState, useEffect} from "react";
import {Button, Cascader, Checkbox, Input, Select, Table, Space, Card, Row, Col, Tag, Divider, Tooltip} from "antd";
import {SearchOutlined, DownloadOutlined, FilterOutlined, ClearOutlined} from '@ant-design/icons';
import {createUUid} from "../../../../../utils/uuid";
import {fieldType} from "../util";
import {message as zhmessage} from "../../../../../locales/zh";
import {message as enmessage} from "../../../../../locales/en";
import ExcelJS from "exceljs";
import {saveAs} from "file-saver";

export const LocalesTable = (props) => {

    // 使用父组件传递的筛选状态
    const search = props.search || {
        path: [], type: '', value: '', excludeOperator: false, excludeMenu: false,
        excludeUnuseKey: false, excludeMachineKey: false, duplicateRemoval: false,
        excludeBackendKey: false, unScan: false
    }

    const setSearch = (newSearch) => {
        if (props.onSearchChange) {
            props.onSearchChange(newSearch);
        }
    }

    const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
    const [tableHeight, setTableHeight] = useState(400)
    const [pageSize, setPageSize] = useState(20)
    const [currentPage, setCurrentPage] = useState(1)

    // 简化的高度计算 - 使用 flex 布局后不需要复杂计算
    useEffect(() => {
        const updateHeight = () => {
            // 基础高度计算，主要用于确定合适的显示行数
            const filterHeight = showAdvancedFilters ? 140 : 80;
            const baseHeight = 300; // 基础最小高度
            setTableHeight(baseHeight + filterHeight);
        };

        updateHeight();
        window.addEventListener('resize', updateHeight);

        return () => {
            window.removeEventListener('resize', updateHeight);
        };
    }, [showAdvancedFilters])

    const columns = [
        {
            title: "#",
            dataIndex: "#",
            width: 60,
            fixed: 'left',
            render: (text, record, index) => (
                <span style={{ color: '#999', fontSize: '12px' }}>
                    {index + 1}
                </span>
            )
        },
        {
            title: 'Key',
            dataIndex: 'key',
            key: 'key',
            width: 250,
            fixed: 'left',
            render: (text, record, index) => {
                // 检查该字段是否在allFields中（同时匹配key和path）
                const isInAllFields = checkFieldInAllFields(record.key, record.path, props.allFields);

                // 检查是否为重复字段
                const allKeys = showData.map(item => item.key);
                const keyCount = allKeys.filter(key => key === record.key).length;
                const isFirstInGroup = allKeys.indexOf(record.key) === index;
                const isDuplicate = keyCount > 1;

                // 检查是否为问题字段
                const isProblemField = record.problemType;
                const isWarning = isProblemField && record.severity === 'warning';
                const isError = isProblemField && record.severity === 'error';
                const isOverManaged = record.isOverManaged;
                const isVirtualField = !record.isRealField && isProblemField;

                // 根据问题类型设置颜色
                let problemColor = '#333';
                let backgroundColor = 'transparent';
                let borderColor = 'none';

                if (isError) {
                    problemColor = '#ff4d4f';
                    backgroundColor = isOverManaged ? '#fff1f0' : '#fff2f0';
                    borderColor = isOverManaged ? '1px dashed #ff4d4f' : '1px solid #ffccc7';
                } else if (isWarning) {
                    problemColor = '#fa8c16';
                    backgroundColor = '#fff7e6';
                    borderColor = '1px solid #ffd591';
                } else if (isInAllFields) {
                    problemColor = '#1890ff';
                }

                return (
                    <div style={{
                        fontFamily: 'Monaco, Consolas, monospace',
                        fontSize: '12px',
                        wordBreak: 'break-all',
                        color: problemColor,
                        fontWeight: isInAllFields || isProblemField ? 'bold' : 'normal',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px',
                        backgroundColor: backgroundColor,
                        padding: isProblemField ? '2px 4px' : '0',
                        borderRadius: isProblemField ? '3px' : '0',
                        border: borderColor,
                        fontStyle: isVirtualField ? 'italic' : 'normal'
                    }}>
                        {isProblemField && (
                            <Tooltip title={
                                <div>
                                    <div><strong>问题类型:</strong> {record.problemDescription}</div>
                                    <div><strong>详细说明:</strong> {record.problemDetail}</div>
                                    {record.isOverManaged ? (
                                        <>
                                            <div><strong>多余管理路径:</strong> {record.scanPath}</div>
                                            <div><strong>实际扫描路径:</strong> {record.allScanPaths?.join(', ') || '无'}</div>
                                            <div><strong>所有管理路径:</strong> {record.managedPaths?.join(', ')}</div>
                                        </>
                                    ) : (
                                        <>
                                            <div><strong>扫描路径:</strong> {record.scanPath}</div>
                                            <div><strong>管理路径:</strong> {record.managedPaths?.join(', ')}</div>
                                            {record.allScanPaths && (
                                                <div><strong>所有扫描路径:</strong> {record.allScanPaths.join(', ')}</div>
                                            )}
                                        </>
                                    )}
                                    <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                                        {isOverManaged ? '🔴 需要移除多余的管理配置' :
                                         isError ? '🔴 需要修正管理配置路径' :
                                         '🟡 需要完善管理配置覆盖'}
                                    </div>
                                </div>
                            }>
                                <span style={{
                                    color: isError ? '#ff4d4f' : '#fa8c16',
                                    fontSize: '10px',
                                    fontWeight: 'bold',
                                    minWidth: '16px',
                                    textAlign: 'center'
                                }}>
                                    {isOverManaged ? '🗑️' : isError ? '❌' : '⚠️'}
                                </span>
                            </Tooltip>
                        )}
                        {isDuplicate && !isProblemField && (
                            <span style={{
                                color: '#faad14',
                                fontSize: '10px',
                                fontWeight: 'bold',
                                minWidth: '20px',
                                textAlign: 'right'
                            }}>
                                {isFirstInGroup ? `${keyCount}×` : '↳'}
                            </span>
                        )}
                        <span style={{ flex: 1 }}>
                            {text}
                        </span>
                    </div>
                )
            }
        },
        {
            title: '中文翻译',
            dataIndex: 'zh',
            key: 'zh',
            width: 180,
            render: (text, record) => {
                const translation = zhmessage[record.key];
                return (
                    <div style={{
                        color: translation ? '#333' : '#999',
                        fontSize: '13px'
                    }}>
                        {translation || '暂无翻译'}
                    </div>
                );
            }
        },
        {
            title: '英文翻译',
            dataIndex: 'en',
            key: 'en',
            width: 180,
            render: (text, record) => {
                const translation = enmessage[record.key];
                return (
                    <div style={{
                        color: translation ? '#333' : '#999',
                        fontSize: '13px'
                    }}>
                        {translation || 'No translation'}
                    </div>
                );
            }
        },
        {
            title: '字段类型',
            dataIndex: 'type',
            key: 'type',
            width: 120,
            render: (text) => {
                const typeInfo = fieldType.find(it => it.value === text);
                return typeInfo ? (
                    <Tag color="blue" style={{ fontSize: '12px' }}>
                        {typeInfo.label}
                    </Tag>
                ) : (
                    <Tag color="default" style={{ fontSize: '12px' }}>
                        未知
                    </Tag>
                );
            }
        },
        {
            title: '文件路径',
            dataIndex: 'path',
            key: 'path',
            width: 300,
            render: (text, record) => {
                // 检查字段是否在allFields中（同时匹配key和path）
                const isInAllFields = checkFieldInAllFields(record.key, record.path, props.allFields);

                // 检查是否为问题字段
                const isProblemField = record.problemType;
                const isWarning = isProblemField && record.severity === 'warning';
                const isError = isProblemField && record.severity === 'error';
                const isOverManaged = record.isOverManaged;
                const isVirtualField = !record.isRealField && isProblemField;

                // 获取路径的中文名称
                const chineseName = getPathChineseName(text, props.modules);
                const englishPath = text?.join(' / ') || '-';

                // 根据问题类型设置路径颜色和样式
                let pathColor = '#666';
                let backgroundColor = 'transparent';
                let borderColor = 'none';

                if (isError) {
                    pathColor = '#ff4d4f';
                    backgroundColor = isOverManaged ? '#fff1f0' : '#fff2f0';
                    borderColor = isOverManaged ? '1px dashed #ff4d4f' : '1px solid #ffccc7';
                } else if (isWarning) {
                    pathColor = '#fa8c16';
                    backgroundColor = '#fff7e6';
                    borderColor = '1px solid #ffd591';
                } else if (isInAllFields) {
                    pathColor = '#1890ff';
                }

                return (
                    <div style={{
                        fontSize: '12px',
                        color: pathColor,
                        fontWeight: isInAllFields || isProblemField ? 'bold' : 'normal',
                        wordBreak: 'break-all',
                        display: 'flex',
                        alignItems: 'flex-start',
                        gap: '4px',
                        lineHeight: '1.4',
                        backgroundColor: backgroundColor,
                        padding: isProblemField ? '4px 6px' : '0',
                        borderRadius: isProblemField ? '4px' : '0',
                        border: borderColor,
                        fontStyle: isVirtualField ? 'italic' : 'normal'
                    }}>
                        {isProblemField && (
                            <Tooltip title={
                                <div>
                                    <div><strong>问题类型:</strong> {record.problemDescription}</div>
                                    <div><strong>详细说明:</strong> {record.problemDetail}</div>
                                    {record.isOverManaged ? (
                                        <>
                                            <div><strong>多余管理路径:</strong> {record.scanPath}</div>
                                            <div><strong>实际扫描路径:</strong> {record.allScanPaths?.join(', ') || '无'}</div>
                                            <div><strong>所有管理路径:</strong> {record.managedPaths?.join(', ')}</div>
                                            <div style={{ marginTop: '4px', color: '#ff4d4f' }}>
                                                ⚠️ 此路径在管理配置中存在但实际扫描中不存在
                                            </div>
                                        </>
                                    ) : (
                                        <>
                                            <div><strong>当前扫描路径:</strong> {record.scanPath}</div>
                                            <div><strong>管理配置路径:</strong> {record.managedPaths?.join(', ')}</div>
                                            {record.allScanPaths && (
                                                <div><strong>所有扫描路径:</strong> {record.allScanPaths.join(', ')}</div>
                                            )}
                                        </>
                                    )}
                                    <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                                        {isOverManaged ? '🗑️ 需要移除多余的管理配置' :
                                         isError ? '🔴 需要修正管理配置路径' :
                                         '🟡 需要完善管理配置覆盖'}
                                    </div>
                                </div>
                            }>
                                <span style={{
                                    color: isError ? '#ff4d4f' : '#fa8c16',
                                    fontSize: '10px',
                                    fontWeight: 'bold',
                                    marginTop: '2px'
                                }}>
                                    {isOverManaged ? '🗑️' : isError ? '❌' : '⚠️'}
                                </span>
                            </Tooltip>
                        )}
                        {isInAllFields && !isProblemField && (
                            <span style={{
                                color: '#1890ff',
                                fontSize: '10px',
                                fontWeight: 'bold',
                                marginTop: '2px'
                            }}>
                                ●
                            </span>
                        )}
                        <div style={{ flex: 1 }}>
                            {/* 中文路径名称 */}
                            {chineseName && (
                                <div style={{
                                    fontSize: '13px',
                                    fontWeight: '500',
                                    color: isInAllFields ? '#1890ff' : '#333',
                                    marginBottom: '2px'
                                }}>
                                    {chineseName}
                                </div>
                            )}
                            {/* 英文路径 */}
                            <div style={{
                                fontFamily: 'Monaco, Consolas, monospace',
                                fontSize: '11px',
                                color: isInAllFields ? '#40a9ff' : '#999',
                                opacity: chineseName ? 0.8 : 1
                            }}>
                                {englishPath}
                            </div>
                        </div>
                        {isInAllFields && (
                            <span style={{
                                fontSize: '10px',
                                color: '#1890ff',
                                backgroundColor: '#e6f7ff',
                                padding: '1px 4px',
                                borderRadius: '2px',
                                border: '1px solid #91d5ff',
                                whiteSpace: 'nowrap',
                                marginTop: '1px'
                            }}>
                                已管理
                            </span>
                        )}
                    </div>
                )
            }
        },
    ]

    const like = (text1, text2) => {
        if (!text1 || !text2) return false
        return text1?.toUpperCase().indexOf(text2?.toUpperCase()) !== -1
    }

    // 检查字段是否在 allFields 中存在（同时匹配key和path）
    const checkFieldInAllFields = (key, path, allFields) => {
        if (!key || !allFields || !Array.isArray(allFields)) return false;

        // 查找同时匹配key和path的字段
        return allFields.some(field => {
            // 检查key是否匹配
            if (field.key !== key) return false;

            // 检查path是否匹配
            if (!field.path || !path) {
                return !field.path && !path; // 都为空时匹配
            }

            // 比较路径数组
            if (field.path.length !== path.length) return false;

            return field.path.every((pathItem, index) => pathItem === path[index]);
        });
    }

    // 获取路径的中文显示名称
    const getPathChineseName = (path, modules) => {
        if (!path || !modules || path.length === 0) return null;

        // 递归查找路径对应的中文名称
        const findChineseName = (moduleList, targetPath, currentDepth = 0) => {
            if (!moduleList || currentDepth >= targetPath.length) return null;

            for (const module of moduleList) {
                if (module.value === targetPath[currentDepth]) {
                    // 如果是最后一层，返回当前模块的label
                    if (currentDepth === targetPath.length - 1) {
                        return module.label || module.value;
                    }
                    // 如果还有子层级，继续递归查找
                    if (module.children && currentDepth < targetPath.length - 1) {
                        const childResult = findChineseName(module.children, targetPath, currentDepth + 1);
                        if (childResult) {
                            return `${module.label || module.value} / ${childResult}`;
                        }
                    }
                }
            }
            return null;
        };

        return findChineseName(modules, path);
    }

    // 直接使用父组件传递的已筛选数据
    const showData = props.filteredData || props.dataSource;

    // 为Cascader选项添加颜色标识和优化显示
    const enhanceModulesWithColor = (modules, allModules) => {
        if (!modules || !allModules) return modules;

        const enhanceModule = (module, currentPath = []) => {
            const fullPath = [...currentPath, module.value];

            // 检查是否有任何字段使用了这个路径
            const isManaged = allModules.some(m => m.value === module.value)

            const enhancedModule = {
                ...module,
                label: (
                    <div style={{
                        color: isManaged ? '#1890ff' : '#333',
                        fontWeight: isManaged ? 'bold' : 'normal',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px'
                    }}>
                        {isManaged && (
                            <span style={{
                                color: '#1890ff',
                                fontSize: '10px'
                            }}>●</span>
                        )}
                        <div>
                            <div style={{ fontSize: '13px' }}>
                                {module.label || module.value}
                            </div>
                            {module.label && module.label !== module.value && (
                                <div style={{
                                    fontSize: '11px',
                                    color: '#999',
                                    fontFamily: 'Monaco, Consolas, monospace'
                                }}>
                                    {module.value}
                                </div>
                            )}
                        </div>
                    </div>
                )
            };

            if (module.children) {
                enhancedModule.children = module.children.map(child =>
                    enhanceModule(child, fullPath)
                );
            }

            return enhancedModule;
        };

        return modules.map(module => enhanceModule(module));
    };

    // 增强后的模块选项
    const enhancedModules = enhanceModulesWithColor(props.modules, props.allModules);

    const print = () => {
        const data = showData.reduce((res, item) => {
            res[item.key] = item.value
            return res
        }, {})
        console.log(data)
    }

    const printOrigin = () => {
        const data = showData.map(it => {
            const d = {key: it.key}
            // const d = {key: it.key, cn: zhmessage[it.key], en: enmessage[it.key]}
            if (it.type) d.type = it.type
            return d
        })
        console.log(data)
        // download(data)
    }

    const download = async (data) => {
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet();
        worksheet.addRow(['key', "中文翻译", "英文翻译"])
        data.forEach((item) => {
            worksheet.addRow([item.key, item.cn, item.en])
        })
        // 生成 Excel 文件
        const buffer = await workbook.xlsx.writeBuffer();
        const blob = new Blob([buffer], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
        saveAs(blob, '翻译.xlsx')
    }


    return (
        <div style={{
            height: '100%',
            display: 'flex',
            flexDirection: 'column'
        }}>
            {/* 固定的筛选区域 */}
            <div style={{ flexShrink: 0, padding: '8px 12px' }}>
                <Card size="small">
                    <Row gutter={[12, 8]} align="middle">
                        <Col span={5}>
                            <Cascader
                                options={enhancedModules}
                                onChange={value => setSearch({...search, path: value})}
                                style={{ width: '100%' }}
                                changeOnSelect
                                expandTrigger="hover"
                                placeholder="路径筛选"
                                allowClear
                                size="small"
                            />
                        </Col>
                        <Col span={4}>
                            <Select
                                style={{ width: '100%' }}
                                options={fieldType}
                                placeholder="类型筛选"
                                allowClear
                                onChange={value => setSearch({...search, type: value})}
                                size="small"
                            />
                        </Col>
                        <Col span={5}>
                            <Input
                                style={{ width: '100%' }}
                                placeholder="搜索 Key 或翻译"
                                prefix={<SearchOutlined />}
                                allowClear
                                onChange={e => setSearch({...search, value: e.target.value})}
                                size="small"
                            />
                        </Col>
                        <Col span={10}>
                            <Space size="small">
                                <Button
                                    icon={<FilterOutlined />}
                                    onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                                    size="small"
                                    type={showAdvancedFilters ? 'primary' : 'default'}
                                >
                                    高级筛选
                                </Button>
                                <Button
                                    icon={<DownloadOutlined />}
                                    onClick={print}
                                    size="small"
                                >
                                    导出
                                </Button>
                                <Button
                                    icon={<ClearOutlined />}
                                    onClick={() => {
                                        setSearch({
                                            path: [], type: '', value: '', excludeOperator: false, excludeMenu: false,
                                            excludeUnuseKey: false, excludeMachineKey: false, duplicateRemoval: false,
                                            excludeBackendKey: false, unScan: false
                                        });
                                        setShowAdvancedFilters(false);
                                    }}
                                    size="small"
                                >
                                    重置
                                </Button>
                            </Space>
                        </Col>
                    </Row>

                    {/* 可折叠的高级筛选选项 */}
                    {showAdvancedFilters && (
                        <div style={{
                            backgroundColor: '#fafafa',
                            padding: '8px',
                            borderRadius: '4px',
                            border: '1px dashed #d9d9d9',
                            marginTop: '8px'
                        }}>
                            <Row gutter={[12, 6]}>
                                <Col span={6}>
                                    <Checkbox
                                        onChange={e => setSearch({...search, duplicateRemoval: e.target.checked})}
                                        checked={search.duplicateRemoval}
                                    >
                                        <span style={{ fontSize: '11px' }}>去重显示</span>
                                    </Checkbox>
                                </Col>
                                <Col span={6}>
                                    <Checkbox
                                        onChange={e => setSearch({...search, excludeOperator: e.target.checked})}
                                        checked={search.excludeOperator}
                                    >
                                        <span style={{ fontSize: '11px' }}>排除 operation</span>
                                    </Checkbox>
                                </Col>
                                <Col span={6}>
                                    <Checkbox
                                        onChange={e => setSearch({...search, excludeMenu: e.target.checked})}
                                        checked={search.excludeMenu}
                                    >
                                        <span style={{ fontSize: '11px' }}>排除 menu</span>
                                    </Checkbox>
                                </Col>
                                <Col span={6}>
                                    <Checkbox
                                        onChange={e => setSearch({...search, unScan: e.target.checked})}
                                        checked={search.unScan}
                                    >
                                        <span style={{ fontSize: '11px' }}>排除未扫描</span>
                                    </Checkbox>
                                </Col>
                            </Row>
                            <Row gutter={[12, 6]} style={{ marginTop: '6px' }}>
                                <Col span={6}>
                                    <Checkbox
                                        onChange={e => setSearch({...search, excludeUnuseKey: e.target.checked})}
                                        checked={search.excludeUnuseKey}
                                    >
                                        <span style={{ fontSize: '11px' }}>排除废弃 key</span>
                                    </Checkbox>
                                </Col>
                                <Col span={6}>
                                    <Checkbox
                                        onChange={e => setSearch({...search, excludeMachineKey: e.target.checked})}
                                        checked={search.excludeMachineKey}
                                    >
                                        <span style={{ fontSize: '11px' }}>排除机翻 key</span>
                                    </Checkbox>
                                </Col>
                                <Col span={6}>
                                    <Checkbox
                                        onChange={e => setSearch({...search, excludeBackendKey: e.target.checked})}
                                        checked={search.excludeBackendKey}
                                    >
                                        <span style={{ fontSize: '11px' }}>排除后端字段</span>
                                    </Checkbox>
                                </Col>
                                <Col span={6}>
                                    {/* 预留位置 */}
                                </Col>
                            </Row>
                        </div>
                    )}
                </Card>
            </div>

            {/* 可滚动的表格区域 */}
            <div style={{
                flex: 1,
                overflow: 'hidden',
                padding: '0 12px 8px 12px',
                display: 'flex',
                flexDirection: 'column'
            }}>
                <div style={{
                    flex: 1,
                    overflow: 'hidden',
                    display: 'flex',
                    flexDirection: 'column'
                }}>
                    <Table
                        rowKey={() => createUUid()}
                        columns={columns}
                        dataSource={showData}
                        scroll={{
                            // x: 1200,
                            y: showAdvancedFilters ? 'calc(100vh - 520px)' : 'calc(100vh - 420px)' // 根据高级筛选状态动态调整，为分页留出空间
                        }}
                        pagination={{
                            current: currentPage,
                            pageSize: pageSize,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: (total, range) => (
                                <span style={{ fontSize: '12px' }}>
                                    第 {range[0]}-{range[1]} 条，共 <strong>{total}</strong> 条
                                </span>
                            ),
                            pageSizeOptions: ['10', '20', '30', '50', '100', '200', '500', '1000'],
                            size: 'small',
                            onChange: (page, size) => {
                                setCurrentPage(page);
                            },
                            onShowSizeChange: (current, size) => {
                                setPageSize(size);
                                setCurrentPage(1); // 切换页面大小时重置到第一页
                            }
                        }}
                        size="small"
                        rowClassName={(record, index) => {
                            const isInAllFields = checkFieldInAllFields(record.key, record.path, props.allFields);
                            let className = index % 2 === 0 ? 'even-row' : 'odd-row';

                            if (isInAllFields) {
                                className += ' managed-field';
                            }

                            // 检查是否为重复字段组的第一个
                            const allKeys = showData.map(item => item.key);
                            const keyCount = allKeys.filter(key => key === record.key).length;
                            const isFirstInGroup = allKeys.indexOf(record.key) === index;

                            if (keyCount > 1) {
                                className += ' duplicate-field';
                                if (isFirstInGroup) {
                                    className += ' first-duplicate';
                                }
                            }

                            return className;
                        }}
                        locale={{
                            emptyText: (
                                <div style={{ padding: '40px 0', color: '#999', textAlign: 'center' }}>
                                    <div style={{ fontSize: '14px', marginBottom: '8px', color: '#bfbfbf' }}>
                                        📋 暂无数据
                                    </div>
                                    <div style={{ fontSize: '12px', color: '#d9d9d9' }}>
                                        请先点击"读取字段"按钮扫描项目中的字段
                                    </div>
                                </div>
                            )
                        }}
                    />
                </div>
            </div>

            <style jsx>{`
                .even-row {
                    background-color: #fafafa;
                }
                .odd-row {
                    background-color: #ffffff;
                }
                .even-row:hover,
                .odd-row:hover {
                    background-color: #e6f7ff !important;
                }

                /* 已管理字段样式 */
                .managed-field {
                    border-left: 3px solid #1890ff;
                    background-color: #e6f7ff !important;
                }

                .managed-field:hover {
                    background-color: #bae7ff !important;
                }

                /* 重复字段样式 */
                .duplicate-field {
                    background-color: #fffbe6 !important;
                }

                .first-duplicate {
                    border-top: 1px solid #faad14;
                }

                .duplicate-field:hover {
                    background-color: #fff7e6 !important;
                }
            `}</style>
        </div>
    )


}