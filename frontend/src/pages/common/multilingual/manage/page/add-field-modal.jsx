import React, {useState, useEffect, useMemo, useImperativeHandle, forwardRef} from 'react'
import {Modal, Table, Select, Cascader, Button, message, Input, Space, Card, Tag, Divider, Row, Col, Checkbox} from "antd";
import {SearchOutlined, FilterOutlined, ClearOutlined, SettingOutlined} from '@ant-design/icons';
import {fieldType} from "../util";
import {message as zhmessage} from "../../../../../locales/zh";
import {message as enmessage} from "../../../../../locales/en";
import {unuseKeys} from "../special-field/unuse";
import {machineKeys} from "../special-field/machine";
import {backendKeys} from "../special-field/backend";
import {post} from "../util";
import _ from "lodash";

/**
 * 字段添加弹窗组件
 * 用于将未管理的字段添加到相应的 locales 文件中
 */
export const AddFieldModal = forwardRef((props, ref) => {
    const {onSuccess, dataSource = [], flatModules, options = [], problemFields = [], mode = 'add'} = props;
    const modules = props.modules.map(it => {
        if (it.value === 'report') {
            it.children = [
                {value: 'report_page', label: '报表页面', labelEn: 'Report Page'},
                {value: 'report_field', label: '报表字段', labelEn: 'Report Field'}
            ]
        }
        return it
    })

    const [visible, setVisible] = useState(false);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);
    const [loading, setLoading] = useState(false);

    // 问题字段处理模式相关状态
    const [processingMode, setProcessingMode] = useState(mode); // 'add' | 'problem'
    const [selectedProblemType, setSelectedProblemType] = useState('all'); // 'all' | 'cross_path_incomplete' | 'over_managed' | 'single_path_mismatch'

    // 筛选和搜索状态
    const [searchText, setSearchText] = useState('');
    const [filterType, setFilterType] = useState(null);
    const [filterPath, setFilterPath] = useState(null);
    const [showFilters, setShowFilters] = useState(false);
    const [showPathMismatch, setShowPathMismatch] = useState(false);
    const [showPathMatch, setShowPathMatch] = useState(false);

    // 高级筛选状态
    const [showAdvancedFilters, setShowAdvancedFilters] = useState(true);
    const [advancedFilters, setAdvancedFilters] = useState({
        excludeOperator: true,
        excludeMenu: true,
        excludeUnuseKey: true,
        excludeMachineKey: true,
        excludeBackendKey: true,
        excludeUnScan: true,
        duplicateRemoval: false,
        onlyUntranslated: false
    });

    // 分页状态
    const [pageSize, setPageSize] = useState(100);
    const [currentPage, setCurrentPage] = useState(1);
    
    // 暴露给父组件的方法
    useImperativeHandle(ref, () => ({
        open: (openMode = 'add', problemData = []) => {
            setVisible(true);
            setSelectedRowKeys([]);
            setSelectedRows([]);
            setProcessingMode(openMode);

            // 如果是问题字段处理模式，设置默认筛选
            if (openMode === 'problem') {
                setSelectedProblemType('all');
                setShowAdvancedFilters(false);
            } else {
                // 重置筛选状态
                setSearchText('');
                setFilterType(null);
                setFilterPath(null);
                setShowFilters(false);
                setShowPathMismatch(false);
                setShowPathMatch(false);
                setShowAdvancedFilters(true);
                setAdvancedFilters({
                    excludeOperator: true,
                    excludeMenu: true,
                    excludeUnuseKey: true,
                    excludeMachineKey: true,
                    excludeBackendKey: true,
                    excludeUnScan: true,
                    duplicateRemoval: false,
                    onlyUntranslated: false
                });
            }

            // 重置分页状态
            setCurrentPage(1);
            setPageSize(100);
        },
        close: () => {
            setVisible(false);
        }
    }), []);

    // 关闭弹窗
    const handleCancel = () => {
        setVisible(false);
        setSelectedRowKeys([]);
        setSelectedRows([]);
    };

    // 文本匹配函数
    const like = (text1, text2) => {
        if (!text1 || !text2) return false;
        return text1?.toUpperCase().indexOf(text2?.toUpperCase()) !== -1;
    };




    // 自动匹配字段类型
    const getAutoType = (key) => {
        if (key.startsWith('menu.')) return 'menu';
        if (key.includes('placeholder')) return 'placeholder';
        if (key.includes('button') || key.includes('btn')) return 'button';
        if (key.includes('tip') || key.includes('tooltip')) return 'tips';
        if (key.includes('warn') || key.includes('error')) return 'warn';
        if (key.includes('option')) return 'option';
        return 'label';
    };

    // 自动匹配路径
    const getAutoPath = (key, path) => {
        // 如果已有路径且不是 un_scan，直接使用
        if (path && path.length > 0 && path[0] !== 'un_scan') {
            return path;
        }

        // 根据 key 的前缀自动匹配到具体的文件路径
        // 匹配规则：根据 key 的特征匹配到 locales 目录中对应的文件

        // 菜单相关
        if (key.startsWith('menu.')) {
            if (key.includes('home')) return ['main', 'main_home'];
            if (key.includes('project')) return ['main', 'main_project'];
            if (key.includes('multiLanguage')) return ['main', 'main_multiLanguage'];
            if (key.includes('report')) return ['main', 'main_report'];
            if (key.includes('setting')) return ['main', 'main_settings'];
            return ['main', 'main_layout'];
        }

        // 通用字段
        if (key.startsWith('common.')) {
            return ['main', 'main_multiLanguage'];
        }

        // 项目相关
        if (key.startsWith('project.') || key.startsWith('projects.')) {
            // 项目设置相关
            if (key.includes('timezone') || key.includes('orderCheck') || key.includes('timing')) {
                return ['project_setting', 'business_functions'];
            }
            if (key.includes('recycling') || key.includes('isolation')) {
                return ['project_setting', 'business_functions'];
            }
            // 其他项目字段默认到项目信息
            return ['project_setting', 'base_info'];
        }

        // 受试者相关
        if (key.startsWith('subject.')) {
            if (key.includes('visit')) return ['project_detail', 'subject_visit'];
            return ['project_detail', 'subject_subjects'];
        }

        // 供应相关
        if (key.startsWith('supply.')) {
            if (key.includes('freeze')) return ['project_detail', 'supply_freeze'];
            if (key.includes('shipment')) return ['project_detail', 'supply_shipment'];
            if (key.includes('site')) return ['project_detail', 'supply_site'];
            if (key.includes('depot')) return ['project_detail', 'supply_depot'];
            return ['project_detail', 'supply_site'];
        }

        // 报表相关
        if (key.startsWith('report.')) {
            return ['report', 'report_field'];
        }

        // 多语言相关
        if (key.startsWith('multiLanguage.')) {
            return ['main', 'main_multiLanguage'];
        }

        // 用户相关
        if (key.startsWith('user.')) {
            return ['main', 'main_layout'];
        }

        // 占位符相关
        if (key.startsWith('placeholder.')) {
            return ['main', 'main_multiLanguage'];
        }

        // 消息相关
        if (key.startsWith('message.')) {
            return ['main', 'main_multiLanguage'];
        }

        // 默认路径
        return ['main', 'main_multiLanguage'];
    };

    // 处理数据，添加自动匹配的类型和路径
    const [processedData, setProcessedData] = useState([]);

    useEffect(() => {
        try {
            let sourceData = [];

            // 根据处理模式选择数据源
            if (processingMode === 'problem') {
                sourceData = problemFields || [];
            } else {
                sourceData = dataSource || [];
            }

            if (!Array.isArray(sourceData)) {
                console.warn('sourceData 不是数组:', sourceData);
                setProcessedData([]);
                return;
            }

            const newData = sourceData.map((item, index) => {
                if (!item || typeof item !== 'object') {
                    console.warn('无效的数据项:', item);
                    return null;
                }

                // 为问题字段添加特殊处理
                if (processingMode === 'problem') {
                    return {
                        ...item,
                        id: index,
                        autoType: getAutoType(item.key || ''),
                        autoPath: item.path || getAutoPath(item.key || '', item.path),
                        selectedType: getAutoType(item.key || ''),
                        selectedPath: item.path || getAutoPath(item.key || '', item.path),
                        // 问题字段特有属性
                        problemType: item.problemType,
                        problemDescription: item.problemDescription,
                        problemDetail: item.problemDetail,
                        severity: item.severity,
                        isRealField: item.isRealField,
                        isOverManaged: item.isOverManaged
                    };
                } else {
                    return {
                        ...item,
                        id: index,
                        autoType: getAutoType(item.key || ''),
                        autoPath: getAutoPath(item.key || '', item.path),
                        selectedType: getAutoType(item.key || ''),
                        selectedPath: getAutoPath(item.key || '', item.path)
                    };
                }
            }).filter(Boolean); // 过滤掉 null 值

            setProcessedData(newData);
        } catch (error) {
            console.error('处理数据时出错:', error);
            setProcessedData([]);
        }
    }, [dataSource, problemFields, processingMode]);

    // 筛选后的数据
    const filteredData = useMemo(() => {
        let filtered = [...processedData];

        // 问题字段类型筛选
        if (processingMode === 'problem' && selectedProblemType !== 'all') {
            filtered = filtered.filter(item => item.problemType === selectedProblemType);
        }

        // 搜索筛选
        if (searchText) {
            filtered = filtered.filter(item =>
                like(item.key, searchText) ||
                like(zhmessage[item.key], searchText) ||
                like(enmessage[item.key], searchText) ||
                (processingMode === 'problem' && like(item.problemDescription, searchText))
            );
        }

        // 类型筛选
        if (filterType) {
            filtered = filtered.filter(item => item.selectedType === filterType);
        }

        // 路径筛选
        if (filterPath && filterPath.length > 0) {
            filtered = filtered.filter(item => {
                const itemPath = item.selectedPath || [];
                return filterPath.every((pathItem, index) => itemPath[index] === pathItem);
            });
        }

        // 高级筛选
        const {excludeOperator, excludeMenu, excludeUnuseKey, excludeMachineKey,
               excludeBackendKey, excludeUnScan, duplicateRemoval, onlyUntranslated} = advancedFilters;

        if (excludeOperator) {
            filtered = filtered.filter(item => !item.key?.startsWith('operation'));
        }

        if (excludeMenu) {
            filtered = filtered.filter(item => !item.key?.startsWith('menu'));
        }

        if (excludeUnuseKey) {
            filtered = filtered.filter(item => !unuseKeys.includes(item.key));
        }

        if (excludeMachineKey) {
            filtered = filtered.filter(item => !machineKeys.includes(item.key));
        }

        if (excludeBackendKey) {
            filtered = filtered.filter(item => !backendKeys.includes(item.key));
        }

        if (excludeUnScan) {
            filtered = filtered.filter(item => !(item.path && item.path[0] === 'un_scan'));
        }

        if (onlyUntranslated) {
            filtered = filtered.filter(item =>
                !zhmessage[item.key] || !enmessage[item.key] ||
                zhmessage[item.key] === enmessage[item.key]
            );
        }

        // 路径不匹配筛选
        if (showPathMismatch) {
            filtered = filtered.filter(item => {
                const selectedPath = item.selectedPath || [];
                return !selectedPath.every(s => !!flatModules.find(f => f.value === s))
            });
        }

        // 路径匹配筛选
        if (showPathMatch) {
            filtered = filtered.filter(item => {
                const selectedPath = item.selectedPath || [];
                return selectedPath.every(s => !!flatModules.find(f => f.value === s))
            });
        }

        // 去重处理
        if (duplicateRemoval) {
            filtered = _.uniqBy(filtered, 'key');
        }

        return filtered;
    }, [processedData, searchText, filterType, filterPath, advancedFilters, showPathMismatch, showPathMatch]);

    // 获取路径的中文显示名称
    const getPathChineseName = (path, modules) => {
        if (!path || !modules || path.length === 0) return null;

        // 递归查找路径对应的中文名称
        const findChineseName = (moduleList, targetPath, currentDepth = 0) => {
            if (!moduleList || currentDepth >= targetPath.length) return null;

            for (const module of moduleList) {
                if (module.value === targetPath[currentDepth]) {
                    // 如果是最后一层，返回当前模块的label
                    if (currentDepth === targetPath.length - 1) {
                        return module.label || module.value;
                    }
                    // 如果还有子层级，继续递归查找
                    if (module.children && currentDepth < targetPath.length - 1) {
                        const childResult = findChineseName(module.children, targetPath, currentDepth + 1);
                        if (childResult) {
                            return `${module.label || module.value} / ${childResult}`;
                        }
                    }
                }
            }
            return null;
        };

        return findChineseName(modules, path);
    };

    // 清除所有筛选
    const clearAllFilters = () => {
        setSearchText('');
        setFilterType(null);
        setFilterPath(null);
        setShowPathMismatch(false);
        setShowPathMatch(false);
        setAdvancedFilters({
            excludeOperator: true,
            excludeMenu: true,
            excludeUnuseKey: true,
            excludeMachineKey: true,
            excludeBackendKey: true,
            excludeUnScan: true,
            duplicateRemoval: false,
            onlyUntranslated: false
        });
        // 重置分页到第一页
        setCurrentPage(1);
    };

    // 批量设置类型
    const batchSetType = (type) => {
        const newData = processedData.map(item => {
            if (selectedRowKeys.includes(item.id)) {
                return { ...item, selectedType: type };
            }
            return item;
        });
        setProcessedData(newData);

        // 更新选中的行数据
        const newSelectedRows = selectedRows.map(row => ({ ...row, selectedType: type }));
        setSelectedRows(newSelectedRows);
    };

    // 批量设置路径
    const batchSetPath = (path) => {
        const newData = processedData.map(item => {
            if (selectedRowKeys.includes(item.id)) {
                return { ...item, selectedPath: path };
            }
            return item;
        });
        setProcessedData(newData);

        // 更新选中的行数据
        const newSelectedRows = selectedRows.map(row => ({ ...row, selectedPath: path }));
        setSelectedRows(newSelectedRows);
    };

    // 更新字段类型
    const updateFieldType = (id, type) => {
        const newData = processedData.map(item =>
            item.id === id ? {...item, selectedType: type} : item
        );
        setProcessedData(newData);

        // 更新选中的行数据
        const newSelectedRows = selectedRows.map(row =>
            row.id === id ? {...row, selectedType: type} : row
        );
        setSelectedRows(newSelectedRows);
    };

    // 更新字段路径
    const updateFieldPath = (id, path) => {
        const newData = processedData.map(item =>
            item.id === id ? {...item, selectedPath: path} : item
        );
        setProcessedData(newData);

        // 更新选中的行数据
        const newSelectedRows = selectedRows.map(row =>
            row.id === id ? {...row, selectedPath: path} : row
        );
        setSelectedRows(newSelectedRows);
    };

    // 表格列定义
    const columns = [
        {
            title: 'Key',
            dataIndex: 'key',
            key: 'key',
            width: 220,
            fixed: 'left',
            render: (text, record) => (
                <div style={{
                    fontFamily: 'Monaco, Consolas, monospace',
                    fontSize: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px'
                }}>
                    {processingMode === 'problem' && record.problemType && (
                        <span style={{
                            fontSize: '10px',
                            color: record.severity === 'error' ? '#ff4d4f' : '#fa8c16'
                        }}>
                            {record.isOverManaged ? '🗑️' : record.severity === 'error' ? '❌' : '⚠️'}
                        </span>
                    )}
                    {text}
                </div>
            )
        },
        ...(processingMode === 'problem' ? [{
            title: '问题类型',
            dataIndex: 'problemType',
            key: 'problemType',
            width: 150,
            render: (text, record) => {
                const typeMap = {
                    'cross_path_incomplete': { text: '欠管理', color: '#fa8c16' },
                    'over_managed': { text: '超管理', color: '#ff4d4f' },
                    'single_path_mismatch': { text: '路径错误', color: '#ff4d4f' }
                };
                const typeInfo = typeMap[text] || { text: '未知', color: '#666' };
                return (
                    <Tag color={typeInfo.color} style={{ fontSize: '11px' }}>
                        {typeInfo.text}
                    </Tag>
                );
            }
        }] : []),
        {
            title: '中文翻译',
            dataIndex: 'cn',
            key: 'cn',
            width: 160,
            render: (text, record) => {
                const translation = zhmessage[record.key];
                return (
                    <div style={{
                        color: translation ? '#333' : '#999',
                        fontSize: '13px'
                    }}>
                        {translation || '暂无翻译'}
                    </div>
                );
            }
        },
        {
            title: '英文翻译',
            dataIndex: 'en',
            key: 'en',
            width: 160,
            render: (text, record) => {
                const translation = enmessage[record.key];
                return (
                    <div style={{
                        color: translation ? '#333' : '#999',
                        fontSize: '13px'
                    }}>
                        {translation || 'No translation'}
                    </div>
                );
            }
        },
        {
            title: '字段类型',
            dataIndex: 'selectedType',
            key: 'selectedType',
            width: 140,
            render: (text, record) => (
                <Select
                    value={record.selectedType}
                    style={{ width: '100%' }}
                    onChange={(value) => updateFieldType(record.id, value)}
                    options={fieldType}
                    size="small"
                />
            )
        },
        {
            title: '目标路径',
            dataIndex: 'selectedPath',
            key: 'selectedPath',
            width: 220,
            render: (text, record) => (
                <Cascader
                    value={record.selectedPath}
                    style={{ width: '100%' }}
                    options={modules}
                    onChange={(value) => updateFieldPath(record.id, value)}
                    placeholder="选择路径"
                    showSearch
                    size="small"
                />
            )
        },
        {
            title: '原始路径',
            dataIndex: 'path',
            key: 'originalPath',
            width: 220,
            render: (text, record) => {
                // 获取路径的中文名称
                const chineseName = getPathChineseName(text, modules);
                const englishPath = text?.join(' / ') || '-';

                return (
                    <div style={{
                        fontSize: '12px',
                        color: '#666',
                        wordBreak: 'break-all',
                        lineHeight: '1.4'
                    }}>
                        {/* 中文路径名称 */}
                        {chineseName && (
                            <div style={{
                                fontSize: '13px',
                                fontWeight: '500',
                                color: '#333',
                                marginBottom: '2px'
                            }}>
                                {chineseName}
                            </div>
                        )}
                        {/* 英文路径 */}
                        <div style={{
                            fontFamily: 'Monaco, Consolas, monospace',
                            fontSize: '11px',
                            color: '#999',
                            opacity: chineseName ? 0.8 : 1
                        }}>
                            {englishPath}
                        </div>
                    </div>
                )
            }
        }
    ];

    // 行选择配置
    const rowSelection = {
        selectedRowKeys,
        onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRowKeys(selectedRowKeys);
            setSelectedRows(selectedRows);
        },
        onSelectAll: (selected, selectedRows, changeRows) => {
            if (selected) {
                // 选择当前筛选结果中的所有行
                const currentPageKeys = filteredData.map(item => item.id);
                const newSelectedKeys = [...new Set([...selectedRowKeys, ...currentPageKeys])];
                const newSelectedRows = processedData.filter(item => newSelectedKeys.includes(item.id));
                setSelectedRowKeys(newSelectedKeys);
                setSelectedRows(newSelectedRows);
            } else {
                // 取消选择当前筛选结果中的所有行
                const currentPageKeys = filteredData.map(item => item.id);
                const newSelectedKeys = selectedRowKeys.filter(key => !currentPageKeys.includes(key));
                const newSelectedRows = selectedRows.filter(row => !currentPageKeys.includes(row.id));
                setSelectedRowKeys(newSelectedKeys);
                setSelectedRows(newSelectedRows);
            }
        }
    };

    // 处理普通字段添加
    const handleAddFields = async () => {
        const fieldsData = selectedRows.map(field => ({
            key: field.key,
            type: field.selectedType,
            path: field.selectedPath
        }));

        const requestData = {
            fields: fieldsData
        };

        await post('add-file', requestData);
    };

    // 处理问题字段
    const handleProblemFields = async () => {
        // 按问题类型分组处理
        const groupedFields = _.groupBy(selectedRows, 'problemType');

        for (const [problemType, fields] of Object.entries(groupedFields)) {
            switch (problemType) {
                case 'cross_path_incomplete':
                    // 欠管理：添加缺失的管理配置
                    await handleUnderManagedFields(fields);
                    break;
                case 'over_managed':
                    // 超管理：移除多余的管理配置
                    await handleOverManagedFields(fields);
                    break;
                case 'single_path_mismatch':
                    // 路径错误：修正路径配置
                    await handlePathMismatchFields(fields);
                    break;
                default:
                    console.warn('未知的问题类型:', problemType);
            }
        }
    };

    // 处理欠管理字段
    const handleUnderManagedFields = async (fields) => {
        const fieldsData = fields.map(field => ({
            key: field.key,
            type: field.selectedType,
            path: field.selectedPath
        }));

        await post('add-file', { fields: fieldsData });
    };

    // 处理超管理字段
    const handleOverManagedFields = async (fields) => {
        const fieldsData = fields.map(field => ({
            key: field.key,
            path: field.path
        }));

        await post('remove-file', { fields: fieldsData });
    };

    // 处理路径错误字段
    const handlePathMismatchFields = async (fields) => {
        // 先移除错误的配置，再添加正确的配置
        for (const field of fields) {
            // 移除错误配置
            await post('remove-file', {
                fields: [{ key: field.key, path: field.managedPaths[0] }]
            });

            // 添加正确配置
            await post('add-file', {
                fields: [{
                    key: field.key,
                    type: field.selectedType,
                    path: field.selectedPath
                }]
            });
        }
    };

    // 提交保存
    const handleSave = async () => {
        if (selectedRows.length === 0) {
            const actionText = processingMode === 'problem' ? '处理' : '添加';
            message.warning(`请选择要${actionText}的字段`);
            return;
        }

        setLoading(true);

        try {
            if (processingMode === 'problem') {
                // 问题字段处理逻辑
                await handleProblemFields();
            } else {
                // 普通字段添加逻辑
                await handleAddFields();
            }

            setLoading(false);
            const actionText = processingMode === 'problem' ? '处理' : '添加';
            message.success(`成功${actionText} ${selectedRows.length} 个字段`);

            if (onSuccess) {
                onSuccess();
            } else {
                handleCancel();
            }

        } catch (error) {
            setLoading(false);
            const actionText = processingMode === 'problem' ? '处理' : '保存';
            message.error(`${actionText}失败，请重试`);
        }
    };

    return (
        <Modal
            title={
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <span>
                        {processingMode === 'problem' ? '批量处理问题字段' : '添加字段到多语言管理'}
                    </span>
                    <Space>
                        <Tag color="blue">已选择 {selectedRows.length} 个</Tag>
                        <Tag color="green">共 {filteredData.length} 个</Tag>
                        {processingMode === 'problem' && (
                            <Tag color="orange">问题字段</Tag>
                        )}
                    </Space>
                </div>
            }
            open={visible}
            onCancel={handleCancel}
            width={1400}
            style={{ top: 20 }}
            footer={[
                <Button key="cancel" onClick={handleCancel}>
                    取消
                </Button>,
                <Button
                    key="save"
                    type="primary"
                    loading={loading}
                    onClick={handleSave}
                    disabled={selectedRows.length === 0}
                >
                    {processingMode === 'problem' ? '批量处理' : '保存'} ({selectedRows.length})
                </Button>
            ]}
        >
            {/* 说明卡片 */}
            <Card
                size="small"
                style={{
                    marginBottom: 16,
                    backgroundColor: '#f6f8fa',
                    border: '1px solid #e1e4e8'
                }}
            >
                <div style={{ fontSize: '13px', color: '#666', lineHeight: '1.5' }}>
                    <strong style={{ color: '#0366d6' }}>
                        {processingMode === 'problem' ? '🔧 问题字段批量处理：' : '💡 使用说明：'}
                    </strong>
                    <br />
                    {processingMode === 'problem' ? (
                        <>
                            • <span style={{ color: '#fa8c16' }}>⚠️ 欠管理</span>：为缺失的路径添加管理配置
                            <br />
                            • <span style={{ color: '#ff4d4f' }}>🗑️ 超管理</span>：移除多余的管理配置
                            <br />
                            • <span style={{ color: '#ff4d4f' }}>❌ 路径错误</span>：修正错误的路径配置
                            <br />
                            • 选择要处理的问题字段，系统将自动执行相应的修复操作
                        </>
                    ) : (
                        <>
                            • 系统会自动匹配字段类型和路径，您可以手动调整
                            <br />
                            • 使用搜索和筛选功能快速定位目标字段
                            <br />
                            • 支持批量设置类型和路径，提高操作效率
                            <br />
                            • 选择要添加的字段，点击保存后将添加到相应的 locales 文件中
                        </>
                    )}
                </div>
            </Card>

            {/* 筛选和搜索区域 */}
            <Card
                size="small"
                style={{
                    marginBottom: 16,
                    border: '1px solid #d9d9d9'
                }}
                title={
                    <div style={{ fontSize: '14px', fontWeight: 500 }}>
                        <SettingOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                        筛选和搜索
                        {(searchText || filterType || filterPath || showPathMismatch || showPathMatch || Object.values(advancedFilters).some(v => v)) && (
                            <Tag color="orange" style={{ marginLeft: 8, fontSize: '12px' }}>
                                已筛选
                            </Tag>
                        )}
                    </div>
                }
            >
                <Row gutter={[16, 12]}>
                    {processingMode === 'problem' && (
                        <Col span={6}>
                            <Select
                                placeholder="问题类型筛选"
                                value={selectedProblemType}
                                onChange={(value) => {
                                    setSelectedProblemType(value);
                                    setCurrentPage(1);
                                }}
                                style={{ width: '100%' }}
                                size="small"
                            >
                                <Select.Option value="all">全部问题类型</Select.Option>
                                <Select.Option value="cross_path_incomplete">
                                    <Tag color="#fa8c16" size="small">欠管理</Tag>
                                </Select.Option>
                                <Select.Option value="over_managed">
                                    <Tag color="#ff4d4f" size="small">超管理</Tag>
                                </Select.Option>
                                <Select.Option value="single_path_mismatch">
                                    <Tag color="#ff4d4f" size="small">路径错误</Tag>
                                </Select.Option>
                            </Select>
                        </Col>
                    )}
                    <Col span={processingMode === 'problem' ? 10 : 10}>
                        <Input
                            placeholder={processingMode === 'problem' ? "搜索字段 Key、翻译内容或问题描述..." : "搜索字段 Key 或翻译内容..."}
                            prefix={<SearchOutlined style={{ color: '#bfbfbf' }} />}
                            value={searchText}
                            onChange={(e) => {
                                setSearchText(e.target.value);
                                setCurrentPage(1); // 搜索时重置到第一页
                            }}
                            allowClear
                        />
                    </Col>
                    <Col span={5}>
                        <Select
                            placeholder="筛选类型"
                            value={filterType}
                            onChange={(value) => {
                                setFilterType(value);
                                setCurrentPage(1); // 筛选时重置到第一页
                            }}
                            allowClear
                            style={{ width: '100%' }}
                            options={[
                                { label: '全部类型', value: null },
                                ...fieldType
                            ]}
                        />
                    </Col>
                    <Col span={9}>
                        <Cascader
                            placeholder="筛选路径"
                            value={filterPath}
                            onChange={(value) => {
                                setFilterPath(value);
                                setCurrentPage(1); // 筛选时重置到第一页
                            }}
                            options={options}
                            allowClear
                            style={{ width: '100%' }}
                            showSearch
                        />
                    </Col>
                </Row>
                <Row gutter={[8, 8]} style={{ marginTop: '12px' }}>
                    <Col span={24}>
                        <Space size="small" wrap>
                            <Button
                                icon={<FilterOutlined />}
                                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                                type={showAdvancedFilters ? 'primary' : 'default'}
                                size="small"
                            >
                                高级筛选
                            </Button>
                            <Button
                                onClick={() => {
                                    setShowPathMismatch(!showPathMismatch);
                                    if (!showPathMismatch) {
                                        setShowPathMatch(false); // 互斥：开启不匹配时关闭匹配
                                    }
                                    setCurrentPage(1); // 筛选时重置到第一页
                                }}
                                type={showPathMismatch ? 'primary' : 'default'}
                                size="small"
                                style={{
                                    color: showPathMismatch ? '#fff' : '#fa8c16',
                                    borderColor: '#fa8c16',
                                    backgroundColor: showPathMismatch ? '#fa8c16' : 'transparent'
                                }}
                            >
                                路径不匹配
                            </Button>
                            <Button
                                onClick={() => {
                                    setShowPathMatch(!showPathMatch);
                                    if (!showPathMatch) {
                                        setShowPathMismatch(false); // 互斥：开启匹配时关闭不匹配
                                    }
                                    setCurrentPage(1); // 筛选时重置到第一页
                                }}
                                type={showPathMatch ? 'primary' : 'default'}
                                size="small"
                                style={{
                                    color: showPathMatch ? '#fff' : '#52c41a',
                                    borderColor: '#52c41a',
                                    backgroundColor: showPathMatch ? '#52c41a' : 'transparent'
                                }}
                            >
                                路径匹配
                            </Button>
                            <Button
                                icon={<SettingOutlined />}
                                onClick={() => setShowFilters(!showFilters)}
                                type={showFilters ? 'primary' : 'default'}
                                size="small"
                            >
                                批量操作
                            </Button>
                            <Button
                                icon={<ClearOutlined />}
                                onClick={clearAllFilters}
                                disabled={!searchText && !filterType && !filterPath && !showPathMismatch && !showPathMatch && !Object.values(advancedFilters).some(v => v)}
                                size="small"
                            >
                                清除筛选
                            </Button>
                        </Space>
                    </Col>
                </Row>

                {/* 高级筛选选项 */}
                {showAdvancedFilters && (
                    <>
                        <Divider style={{ margin: '12px 0' }}>
                            <span style={{ fontSize: '12px', color: '#999' }}>高级筛选选项</span>
                        </Divider>
                        <div style={{
                            backgroundColor: '#fafafa',
                            padding: '16px',
                            borderRadius: '6px',
                            border: '1px dashed #d9d9d9'
                        }}>
                            <Row gutter={[16, 12]}>
                                <Col span={6}>
                                    <Checkbox
                                        checked={advancedFilters.excludeOperator}
                                        onChange={(e) => setAdvancedFilters({
                                            ...advancedFilters,
                                            excludeOperator: e.target.checked
                                        })}
                                    >
                                        <span style={{ fontSize: '13px' }}>排除 operation</span>
                                    </Checkbox>
                                </Col>
                                <Col span={6}>
                                    <Checkbox
                                        checked={advancedFilters.excludeMenu}
                                        onChange={(e) => setAdvancedFilters({
                                            ...advancedFilters,
                                            excludeMenu: e.target.checked
                                        })}
                                    >
                                        <span style={{ fontSize: '13px' }}>排除 menu</span>
                                    </Checkbox>
                                </Col>
                                <Col span={6}>
                                    <Checkbox
                                        checked={advancedFilters.excludeUnuseKey}
                                        onChange={(e) => setAdvancedFilters({
                                            ...advancedFilters,
                                            excludeUnuseKey: e.target.checked
                                        })}
                                    >
                                        <span style={{ fontSize: '13px' }}>排除废弃 key</span>
                                    </Checkbox>
                                </Col>
                                <Col span={6}>
                                    <Checkbox
                                        checked={advancedFilters.excludeMachineKey}
                                        onChange={(e) => setAdvancedFilters({
                                            ...advancedFilters,
                                            excludeMachineKey: e.target.checked
                                        })}
                                    >
                                        <span style={{ fontSize: '13px' }}>排除机翻 key</span>
                                    </Checkbox>
                                </Col>
                            </Row>
                            <Row gutter={[16, 12]} style={{ marginTop: '12px' }}>
                                <Col span={6}>
                                    <Checkbox
                                        checked={advancedFilters.excludeBackendKey}
                                        onChange={(e) => setAdvancedFilters({
                                            ...advancedFilters,
                                            excludeBackendKey: e.target.checked
                                        })}
                                    >
                                        <span style={{ fontSize: '13px' }}>排除后端字段</span>
                                    </Checkbox>
                                </Col>
                                <Col span={6}>
                                    <Checkbox
                                        checked={advancedFilters.excludeUnScan}
                                        onChange={(e) => setAdvancedFilters({
                                            ...advancedFilters,
                                            excludeUnScan: e.target.checked
                                        })}
                                    >
                                        <span style={{ fontSize: '13px' }}>排除未扫描字段</span>
                                    </Checkbox>
                                </Col>
                                <Col span={6}>
                                    <Checkbox
                                        checked={advancedFilters.duplicateRemoval}
                                        onChange={(e) => setAdvancedFilters({
                                            ...advancedFilters,
                                            duplicateRemoval: e.target.checked
                                        })}
                                    >
                                        <span style={{ fontSize: '13px' }}>去重显示</span>
                                    </Checkbox>
                                </Col>
                                <Col span={6}>
                                    <Checkbox
                                        checked={advancedFilters.onlyUntranslated}
                                        onChange={(e) => setAdvancedFilters({
                                            ...advancedFilters,
                                            onlyUntranslated: e.target.checked
                                        })}
                                    >
                                        <span style={{ fontSize: '13px' }}>仅未翻译</span>
                                    </Checkbox>
                                </Col>
                            </Row>
                        </div>
                    </>
                )}

                {/* 批量操作区域 */}
                {showFilters && (
                    <>
                        <Divider style={{ margin: '12px 0' }}>
                            <span style={{ fontSize: '12px', color: '#999' }}>批量操作</span>
                        </Divider>
                        <div style={{
                            backgroundColor: '#fafafa',
                            padding: '12px',
                            borderRadius: '6px',
                            border: '1px dashed #d9d9d9'
                        }}>
                            <Row gutter={[16, 8]} align="middle">
                                <Col span={12}>
                                    <Space align="center">
                                        <span style={{
                                            fontSize: '13px',
                                            color: '#666',
                                            minWidth: '80px',
                                            display: 'inline-block'
                                        }}>
                                            批量设置类型：
                                        </span>
                                        <Select
                                            placeholder="选择类型"
                                            style={{ width: 140 }}
                                            onChange={batchSetType}
                                            disabled={selectedRows.length === 0}
                                            options={fieldType}
                                            size="small"
                                        />
                                        {selectedRows.length === 0 && (
                                            <span style={{ fontSize: '12px', color: '#999' }}>
                                                请先选择字段
                                            </span>
                                        )}
                                    </Space>
                                </Col>
                                <Col span={12}>
                                    <Space align="center">
                                        <span style={{
                                            fontSize: '13px',
                                            color: '#666',
                                            minWidth: '80px',
                                            display: 'inline-block'
                                        }}>
                                            批量设置路径：
                                        </span>
                                        <Cascader
                                            placeholder="选择路径"
                                            style={{ width: 220 }}
                                            onChange={batchSetPath}
                                            disabled={selectedRows.length === 0}
                                            options={modules}
                                            showSearch
                                            size="small"
                                        />
                                    </Space>
                                </Col>
                            </Row>
                        </div>
                    </>
                )}
            </Card>

            <div style={{
                border: '1px solid #f0f0f0',
                borderRadius: '6px',
                overflow: 'hidden'
            }}>
                <Table
                    rowSelection={rowSelection}
                    columns={columns}
                    dataSource={filteredData}
                    rowKey="id"
                    scroll={{ x: 1360, y: 450 }}
                    pagination={{
                        current: currentPage,
                        pageSize: pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total, range) => (
                            <span style={{ fontSize: '13px' }}>
                                第 {range[0]}-{range[1]} 条，共 <strong>{total}</strong> 条
                                {selectedRows.length > 0 && (
                                    <span style={{ marginLeft: 8, color: '#1890ff' }}>
                                        (已选择 {selectedRows.length} 条)
                                    </span>
                                )}
                            </span>
                        ),
                        pageSizeOptions: ['10', '20', '30', '50', '100', '200', '500', '1000'],
                        size: 'small',
                        onChange: (page, size) => {
                            setCurrentPage(page);
                        },
                        onShowSizeChange: (current, size) => {
                            setPageSize(size);
                            setCurrentPage(1); // 切换页面大小时重置到第一页
                        }
                    }}
                    size="small"
                    style={{
                        backgroundColor: '#fff'
                    }}
                    rowClassName={(record, index) => {
                        if (selectedRowKeys.includes(record.id)) {
                            return 'selected-row';
                        }
                        return index % 2 === 0 ? 'even-row' : 'odd-row';
                    }}
                />
            </div>

            <style jsx>{`
                .selected-row {
                    background-color: #e6f7ff !important;
                }
                .even-row {
                    background-color: #fafafa;
                }
                .odd-row {
                    background-color: #ffffff;
                }
                .selected-row:hover {
                    background-color: #bae7ff !important;
                }
            `}</style>
        </Modal>
    );
});

// 设置组件显示名称，便于调试
AddFieldModal.displayName = 'AddFieldModal';
