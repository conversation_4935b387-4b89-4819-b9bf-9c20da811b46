import {base_info} from "./base_info";
import {env} from "./env";
import {business_functions} from "./business_functions";
import {external_docking} from "./external_docking";
import {custom_process} from "./custom_process";
import {permissions} from "./permissions";
import {notice} from "./notice";
import {menu} from "../menu/menu";

export const project_info =  {
    key: "project_setting",
    name: "menu.projects.main.setting",
    children: [
        {
            key: "base_info",
            name: "menu.projects.project.basic.information",
            children: base_info,
        },
        {
            key: "env",
            name: "menu.projects.project.basic.environment",
            children: env,
        },
        {
            key: "business_functions",
            name: "menu.projects.project.business.functions",
            children: business_functions,
        },
        {
            key: "external_docking",
            name: "menu.projects.project.external.docking",
            children: external_docking,
        },
        {
            key: "custom_process",
            name: "menu.projects.project.custom.process",
            children: custom_process,
        },
        {
            key: "permissions",
            name: "menu.projects.project.permissions",
            children: permissions,
        },
        {
            key: "notice",
            name: "menu.projects.notice.permissions",
            children: notice,
        },
        {
            key: "menu",
            name: "common.menu",
            children: menu
        },
    ]
}
