export const build_treatment_barcode = [
    {
        key: "barcode.add"
    },
    {
        key: "common.ok"
    },
    {
        key: "storehouse.name"
    },
    {
        key: "placeholder.select.common",
        type: "placeholder"
    },
    {
        key: "drug.list.expireDate"
    },
    {
        key: "drug.list.batch"
    },
    {
        key: "placeholder.input.common",
        type: "placeholder"
    },
    {
        key: "barcode.count"
    },
    {
        key: "shortCode.prefix"
    },
    {
        key: "barcode.rule"
    },
    {
        key: "projects.randomization.blockRule.order"
    },
    {
        key: "projects.randomization.blockRule.reverse"
    },
    {
        key: "barcode.package"
    },
    {
        key: "barcode.isPackage.tip"
    },
    {
        key: "barcode.package.rule"
    },
    {
        key: "common.serial"
    },
    {
        key: "barcode.available-count"
    },
    {
        key: "package.count"
    },
    {
        key: "common.created.at"
    },
    {
        key: "common.operation"
    },
    {
        key: "common.export"
    },
    {
        key: "barcode.correlationID"
    }
];