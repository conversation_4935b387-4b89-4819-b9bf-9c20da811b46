export const workbench = [
    {
        key: "user.customer.unLearn",
        type: "warn"
    },
    {
        key: "project.noPermission"
    },
    {
        key: "common.select.role"
    },
    {
        key: "project.number.name",
        type: "placeholder"
    },
    {
        key: "project.focus.on"
    },
    {
        key: "projects.no.system.learning",
        type: "warn"
    },
    {
        key: "projects.learning",
        type: "warn"
    },
    {
        key: "projects.no.learning",
        type: "warn"
    },
    {
        key: "common.role.assigned",
        type: "warn"
    },
    {
        key: "project.focus.on.ok.success"
    },
    {
        key: "project.focus.on.cancel.success"
    },
    {
        key: "projects.type"
    },
    {
        key: "projects.number"
    },
    {
        key: "project.focus.on.cancel"
    },
    {
        key: "project.focus.on.ok"
    },
    {
        key: "projects.name"
    },
    {
        key: "projects.sponsor"
    },
    {
        key: "projects.customer"
    },
    {
        key: "projects.status"
    },
    {
        key: "projects.envs"
    },
    {
        key: "projects.types.first",
        type: "option"
    },
    {
        key: "projects.types.second",
        type: "option"
    },
    {
        key: "projects.types.third",
        type: "option"
    },
    {
        key: "projects.status.progress",
        type: "option"
    },
    {
        key: "projects.status.finish",
        type: "option"
    },
    {
        key: "projects.status.close",
        type: "option"
    },
    {
        key: "projects.status.pause",
        type: "option"
    },
    {
        key: "projects.status.terminate",
        type: "option"
    }
]