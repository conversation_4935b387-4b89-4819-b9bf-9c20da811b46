import {project} from "./project";
import {project_info} from "./project_info";
import {report} from "./report";
import {main} from "./main";
import {message as zhmessage} from "../../../../locales/zh";
import {message as enmessage} from "../../../../locales/en";

const enrichModule = (modules) => {
    return modules.map(it => {
        if ("children" in it) {
            return {
                key: it.key,
                title: zhmessage[it.name] || it.name,
                titleEn: enmessage[it.name] || it.name,
                children: enrichModule(it.children)
            }
        }
        return {...it, label: {cn: zhmessage[it.key], en: enmessage[it.key]}}
    })
}

const projects = [project, project_info, report]
const all = [...projects, main]

export const localesProject = enrichModule(projects)
export const localesAll = enrichModule(all)