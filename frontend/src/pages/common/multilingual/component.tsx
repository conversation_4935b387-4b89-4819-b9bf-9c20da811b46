import React, {useRef} from "react"
import {FormattedMessage as DefaultFormattedMessage, useIntl} from "react-intl"
import {TranslateModal} from "./translate-modal"
import "./component.less"

interface PreviewProps {
    disabledPreview?: boolean,
    allowComponent?: boolean,
    dark?: boolean,
    previewStyle?: any,
}

type messageDescriptor = PreviewProps & {
    id: string,
    defaultMessage?: string,
}

type localeProps = PreviewProps & {
    children: any,
    filedKey: string,
    [key: string]: any,
}

type messageProps = PreviewProps & {
    id: string,
    [key: string]: any,
}


const isPreviewMode = () => {
    return !!sessionStorage.getItem('project_preview')
}

const CustomLocale = ({children, filedKey, ...previewProps}: localeProps) => {
    if (isPreviewMode()) {
        const translateRef = useRef<any>()
        const spanRef = useRef<HTMLSpanElement>(null)

        // 检查元素是否在 Select 组件内部
        const handleClick = (event: React.MouseEvent) => {
            // 检查当前元素或其父元素是否在 .ant-select-selection-item 内部
            const isInSelectItem = (element: HTMLElement | null): boolean => {
                if (!element) return false
                if (element.classList && element.classList.contains('ant-select-selection-item')) return true
                if (element.parentElement) return isInSelectItem(element.parentElement)
                return false
            }

            // 如果不在 Select 组件内部，才执行点击事件
            if (!isInSelectItem(event.currentTarget as HTMLElement)) {
                event.stopPropagation()
                console.log('点击了：', filedKey)
                translateRef?.current?.show(filedKey)
            }
        }

        return <div style={{display: 'inline-block', ...(previewProps.previewStyle || {})}}>
            <span
                ref={spanRef}
                className={`custom-locale-preview ${children.props?.className || ''}`}
                style={{
                    ...(children.props?.style || {}),
                    color: previewProps.dark ? 'rgba(0, 0, 0, 0.85)' : 'inherit'
                }}
                onClick={handleClick}
            >
                {children}
            </span>
            <TranslateModal bind={translateRef} centered={true} />
        </div>
    }
    return children
}

export const FormattedMessage = ({id, disabledPreview, ...props}: messageProps) => {
    if (disabledPreview) return <DefaultFormattedMessage id={id} {...props} />
    return <CustomLocale filedKey={id} {...props}>
        <DefaultFormattedMessage id={id} {...props} />
    </CustomLocale>
}


export const useTranslation = () => {
    const intl = useIntl()

    const formatMessage = (descriptor: messageDescriptor, values?: Record<string, any>): string | any => {
        const {id, defaultMessage, allowComponent} = descriptor
        if (isPreviewMode() && allowComponent) {
            return <FormattedMessage {...descriptor} values={values}  />
        }
        return intl.formatMessage({id, defaultMessage: defaultMessage || id}, values)
    }

    const formatErrorMessage = (errMsg: string, errorData: any) => {
        // TODO
        return errMsg
    }

    return {
        locale: intl.locale,
        formatMessage,
        formatErrorMessage,
        formatDate: intl.formatDate,
        formatNumber: intl.formatNumber,
    }
}