import React, {useState, useCallback, useEffect} from "react"
import {Input, Button} from "antd"
import {ArrowLeftOutlined, ArrowRightOutlined, CloseCircleTwoTone} from "@ant-design/icons";
import {extractPlaceholders} from "./translate-utils";

// 翻译元素类型
type TranslationElement = {
    id: string;
    type: 'text' | 'placeholder';
    content: string;
    editable: boolean;
}

type TranslationInput = {
    element: TranslationElement;
    index: number;
    onChange?: (index: number, content: string) => void;
    onMove?: (fromIndex: number, toIndex: number) => void;
    onRemove?: (index: number) => void;
    totalElements: number;
}

// 可移动位置的占位符组件（不可删除）
const MovablePlaceholder = ({ element }: TranslationInput) => {
    return (
        <div style={{
            display: 'inline-flex',
            alignItems: 'center',
            margin: '2px',
            backgroundColor: '#e6f7ff',
            border: '1px solid #91d5ff',
            borderRadius: '3px',
            padding: '4px 3px',
            fontSize: '12px',
        }}>
            <span style={{color: '#1890ff', fontWeight: '500', cursor: 'default', userSelect: 'none'}}>
                {element.content}
            </span>
        </div>
    );
};

// 可编辑的文本组件（可移除）
const EditableText = ({ element, index, onChange, onMove, onRemove, totalElements }: TranslationInput) => {
    const [tempValue, setTempValue] = useState(element.content)
    const [isHovered, setIsHovered] = useState(false)

    const handleSave = (value: string) => {
        setTempValue(value)
        onChange && onChange(index, value)
    }

    return (
        <div
            style={{position: 'relative'}}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            <div style={{ display: 'inline-block', margin: '2px', position: 'relative' }}>
                <Input.TextArea
                    value={tempValue}
                    onChange={(e) => handleSave(e.target.value)}
                    autoSize={{minRows: 1, maxRows: 5}}
                    style={{
                        width: Math.max(60, tempValue.length * 7 + 16),
                        maxWidth: '278px',
                        fontSize: 12,
                        transition: 'border-radius 0.2s ease'
                    }}
                />
            </div>

            {/* 左移动按钮 - 悬停时显示 */}
            {isHovered && (<Button
                type="text" size="small" icon={<ArrowLeftOutlined />}
                style={{
                    position: 'absolute', top: '2px', left: '0px',
                    height: 'calc(100% - 4px)', minHeight: '28px', width: '18px',
                    backgroundColor: 'rgba(24, 144, 255, 0.1)', zIndex: 10,
                    opacity: index === 0 ? 0.3 : 1,
                    cursor: index === 0 ? 'not-allowed' : 'pointer',
                    transition: 'all 0.2s ease'
                }}
                onClick={() => index > 0 && onMove && onMove(index, index - 1)}
                disabled={index === 0}
                title="向左移动"
            />)}

            {/* 右移动按钮 - 悬停时显示 */}
            {isHovered && (<Button
                type="text" size="small" icon={<ArrowRightOutlined />}
                style={{
                    position: 'absolute', top: '2px', right: '0px',
                    height: 'calc(100% - 4px)', minHeight: '28px', width: '18px',
                    backgroundColor: 'rgba(24, 144, 255, 0.1)', zIndex: 10,
                    opacity: index === totalElements - 1 ? 0.3 : 1,
                    cursor: index === totalElements - 1 ? 'not-allowed' : 'pointer',
                    transition: 'all 0.2s ease'
                }}
                onClick={() => index < totalElements - 1 && onMove && onMove(index, index + 1)}
                disabled={index === totalElements - 1}
                title="向右移动"
            />)}

            {/* 删除按钮 - 悬停时显示 */}
            {isHovered && (<span
                style={{position: 'absolute', top: '-8px', right: '-8px', cursor: 'pointer', zIndex: 11}}
                onClick={() => onRemove && onRemove(index)}
                title="删除文本框"
            >
                <CloseCircleTwoTone style={{fontSize: '14px', color: '#ff4d4f'}}/>
            </span>)}
        </div>
    )
}

// 根据原文结构创建默认的翻译元素布局
const createDefaultElements = (originalText: string, translationText?: string): TranslationElement[] => {
    const placeholders = extractPlaceholders(originalText)
    // 没有占位符，直接返回文本元素
    if (placeholders.length === 0) {
        return [{id: 'text-0', type: 'text', content: translationText || '', editable: true}]
    }
    // 按原文结构解析现有翻译
    const defaultText = !!translationText ? translationText : originalText
    const res = placeholders.reduce((res: any, placeholder: string, currentIndex: number) => {
        const lastIndex = res.lastIndex
        const index = defaultText.indexOf(placeholder, lastIndex)
        if (index === -1) return res
        // 添加占位符前文本
        if (index > lastIndex) res.elements.push({type: 'text', content: defaultText.substring(lastIndex, index)})
        // 添加占位符
        res.elements.push({type: 'placeholder', content: placeholder})
        res.lastIndex = index + placeholder.length
        // 添加最后剩余的文本
        if ((currentIndex + 1) === placeholders.length && res.lastIndex < defaultText.length) {
            res.elements.push({type: 'text', content: defaultText.substring(res.lastIndex)})
        }
        return res
    }, {elements: [], lastIndex: 0})

    return res.elements.map((it: any, index: number) => ({
        id: `text-${index}`,
        type: it.type,
        content: it.content,
        editable: it.type === 'text'
    }))
}


export const TranslateInput = (props: any) => {
    // 使用结构化的翻译元素数组替代简单的字符串
    const [translationElements, setTranslationElements] = useState<TranslationElement[]>([])
    const [isComponentHovered, setIsComponentHovered] = useState(false)

    useEffect(() => {
        if (translationElements.length > 0) return
        // 根据原文结构创建默认布局
        const elements = createDefaultElements(props.originalText, props.translationText)
        setTranslationElements(elements)
    }, [props.originalText, props.translationText])


    const handleChange = (newElements: TranslationElement[]) => {
        props.onChange && props.onChange(newElements.map(el => el.content).join(''))
        setTranslationElements(newElements)
    }

    // 处理翻译元素的操作函数
    const handleElementChange = (index: number, content: string) => {
        const newElements = [...translationElements]
        newElements[index].content = content
        handleChange(newElements)
    }

    const handleElementMove = (fromIndex: number, toIndex: number) => {
        const newElements = [...translationElements]
        const [movedElement] = newElements.splice(fromIndex, 1);
        newElements.splice(toIndex, 0, movedElement)
        handleChange(newElements)
    }

    const handleElementRemove = useCallback((index: number) => {
        const newElements = [...translationElements]
        // 只允许删除文本元素，不允许删除占位符
        if (newElements[index]?.type === 'text') newElements.splice(index, 1)
        handleChange(newElements)
    }, []);


    const addTextElement = (position?: number) => {
        const newElements = [...translationElements]
        const insertIndex = position !== undefined ? position : newElements.length
        newElements.splice(insertIndex, 0, {
            id: `text-${Date.now()}`,
            type: 'text',
            content: '',
            editable: true
        })
        handleChange(newElements)
    }

    return <div>
        {/* 结构化翻译编辑器 */}
        <div
            style={{
                display: 'flex', flexWrap: 'wrap', alignItems: 'center', border: '1px solid #d9d9d9',
                borderRadius: '4px', padding: '2px', minHeight: '60px', backgroundColor: '#fff', position: 'relative'
            }}
            onMouseEnter={() => setIsComponentHovered(true)}
            onMouseLeave={() => setIsComponentHovered(false)}
        >
            {translationElements.map((element, index) => (
                element.type === 'placeholder' ? (
                    <MovablePlaceholder
                        key={element.id}
                        element={element}
                        index={index}
                        onMove={handleElementMove}
                        totalElements={translationElements.length}
                    />
                ) : (
                    <EditableText
                        key={element.id}
                        element={element}
                        index={index}
                        onChange={handleElementChange}
                        onMove={handleElementMove}
                        onRemove={handleElementRemove}
                        totalElements={translationElements.length}
                    />
                )
            ))}

            {/* 悬停预览信息 */}
            {isComponentHovered && translationElements.length > 0 && (
                <div style={{
                    position: 'absolute',
                    bottom: '100%',
                    left: '0',
                    right: '0',
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    color: '#fff',
                    padding: '8px 12px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    zIndex: 1000,
                    wordBreak: 'break-word',
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
                    border: '1px solid rgba(255, 255, 255, 0.1)'
                }}>
                    <div style={{ marginBottom: '4px', color: '#91d5ff', fontSize: '11px' }}>
                        翻译预览：
                    </div>
                    <div>
                        {translationElements.map(el => el.content).join('')}
                    </div>
                </div>
            )}
        </div>
        {/* 操作区域 */}
        <div style={{marginTop: '6px', borderTop: '1px dashed #e8e8e8', paddingTop: '4px'}}>
            <div style={{ marginBottom: '4px' }}>
                <Button
                    type="dashed"
                    size="small"
                    onClick={() => addTextElement()}
                    style={{ fontSize: '11px', height: '20px' }}
                >
                    ➕ 添加文本框
                </Button>
            </div>
        </div>
    </div>
}