import fs from "fs";
import path from "path";
import _ from 'lodash';
import {getFilePathAndVariableName, modifyExistingFile, createNewFile} from "./common-utils.js";
import {scanConfigStructure} from "./config-structure-scanner.js";

/**
 * 将字段添加到相应的 locales 文件中
 * @param {string} fileName - 文件名（基于路径生成）
 * @param {Array} fields - 字段数组，每个字段包含 key, type, path
 */
export const addFile = (fileName, fields) => {
    if (!fields || !Array.isArray(fields) || fields.length === 0) {
        return Promise.reject(new Error('字段数据不能为空'));
    }

    const fileMapping = scanConfigStructure();

    // 按实际文件路径分组字段（处理多个 children 映射到同一文件的情况）
    const groupedFields = _.groupBy(fields, (field) => {
        const fieldPath = field.path || [];
        const { filePath } = getFilePathAndVariableName(fieldPath, fileMapping);
        return filePath; // 按实际文件路径分组
    });

    // 为每个文件处理保存
    const savePromises = Object.entries(groupedFields).map(([filePath, fieldsGroup]) => {
        const firstField = fieldsGroup[0];
        const fieldPath = firstField.path || [];

        // 构建变量名
        const { variableName } = getFilePathAndVariableName(fieldPath, fileMapping);

        // 确保目录存在
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }

        // 检查文件是否存在并处理
        if (fs.existsSync(filePath)) {
            return modifyExistingFile(filePath, variableName, fieldsGroup, 'add');
        } else {
            // 文件不存在，创建新文件
            return createNewFile(filePath, variableName, fieldsGroup);
        }
    });

    return Promise.all(savePromises).then(results => {
        const totalFields = fields.length;
        const fileCount = results.length;
        return `成功添加 ${totalFields} 个字段到 ${fileCount} 个文件中`;
    });
};
