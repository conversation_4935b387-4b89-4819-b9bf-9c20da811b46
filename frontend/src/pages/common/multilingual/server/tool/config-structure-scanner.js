import fs from "fs";
import path from "path";
import { fileURLToPath } from 'url';
import babelParser from '@babel/parser';
import babel from '@babel/traverse';

// 获取当前文件的目录路径（ES 模块中的 __dirname 替代）
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * 多语言配置文件结构扫描器
 * 用于扫描和解析 locales 目录下的所有配置文件，生成 key 到文件路径的映射关系
 */
class ConfigStructureScanner {
    constructor() {
        // 相对于 server/tool 目录的 locales 目录路径
        this.localesDir = path.resolve(__dirname, '../../locales');
        this.keyToPathsMap = new Map();
        this.importCache = new Map();
        this.processedFiles = new Set();
    }

    /**
     * 扫描所有配置文件并生成映射关系
     * @returns {Object} key 到文件路径数组的映射
     */
    scanAllConfigs() {
        this.keyToPathsMap.clear();
        this.importCache.clear();
        this.processedFiles.clear();

        try {
            // 递归扫描 locales 目录下的所有 .js 文件
            this.scanDirectory(this.localesDir);
            
            // 转换 Map 为普通对象并返回
            const result = {};
            for (const [key, paths] of this.keyToPathsMap) {
                result[key] = [...new Set(paths)]; // 去重
            }
            
            return result;
        } catch (error) {
            console.error('扫描配置文件时出错:', error);
            return {};
        }
    }

    /**
     * 递归扫描目录
     * @param {string} dirPath 目录路径
     */
    scanDirectory(dirPath) {
        try {
            const items = fs.readdirSync(dirPath, { withFileTypes: true });
            
            for (const item of items) {
                const fullPath = path.join(dirPath, item.name);
                
                if (item.isDirectory()) {
                    // 递归扫描子目录
                    this.scanDirectory(fullPath);
                } else if (item.name.endsWith('.js') && !item.name.includes('.test.')) {
                    // 处理 JavaScript 文件
                    this.processFile(fullPath);
                }
            }
        } catch (error) {
            console.error(`扫描目录 ${dirPath} 时出错:`, error);
        }
    }

    /**
     * 处理单个文件
     * @param {string} filePath 文件路径
     */
    processFile(filePath) {
        // 避免重复处理同一个文件
        if (this.processedFiles.has(filePath)) {
            return;
        }
        this.processedFiles.add(filePath);

        try {
            const content = fs.readFileSync(filePath, 'utf8');
            const relativePath = this.getRelativePath(filePath);
            
            // 解析文件内容
            const ast = babelParser.parse(content, {
                sourceType: 'module',
                plugins: ['typescript'],
            });

            // 解析 import 语句
            const imports = this.parseImports(ast, filePath);
            this.importCache.set(filePath, imports);

            // 解析导出的配置
            this.parseExports(ast, filePath, relativePath);

        } catch (error) {
            console.error(`处理文件 ${filePath} 时出错:`, error);
        }
    }

    /**
     * 解析 import 语句
     * @param {Object} ast AST 对象
     * @param {string} filePath 当前文件路径
     * @returns {Map} import 映射
     */
    parseImports(ast, filePath) {
        const imports = new Map();
        const self = this; // 保存 this 上下文

        babel.default(ast, {
            ImportDeclaration(path) {
                const importPath = path.node.source.value;
                const specifiers = path.node.specifiers;

                // 解析相对路径
                const resolvedPath = self.resolveImportPath(filePath, importPath);

                for (const spec of specifiers) {
                    if (spec.type === 'ImportSpecifier') {
                        // 命名导入: import { name } from './path'
                        imports.set(spec.local.name, {
                            path: resolvedPath,
                            exportName: spec.imported.name
                        });
                    } else if (spec.type === 'ImportDefaultSpecifier') {
                        // 默认导入: import name from './path'
                        imports.set(spec.local.name, {
                            path: resolvedPath,
                            exportName: 'default'
                        });
                    }
                }
            }
        });

        return imports;
    }

    /**
     * 解析导出的配置
     * @param {Object} ast AST 对象
     * @param {string} filePath 当前文件路径
     * @param {string} relativePath 相对路径
     */
    parseExports(ast, filePath, relativePath) {
        const self = this; // 保存 this 上下文

        babel.default(ast, {
            ExportNamedDeclaration: (path) => {
                if (path.node.declaration) {
                    self.handleExportDeclaration(path.node.declaration, filePath, relativePath);
                }
            },
            ExportDefaultDeclaration: (path) => {
                self.handleExportValue(path.node.declaration, filePath, relativePath);
            }
        });
    }

    /**
     * 处理导出声明
     * @param {Object} declaration 声明节点
     * @param {string} filePath 文件路径
     * @param {string} relativePath 相对路径
     */
    handleExportDeclaration(declaration, filePath, relativePath) {
        if (declaration.type === 'VariableDeclaration') {
            for (const declarator of declaration.declarations) {
                this.handleExportValue(declarator.init, filePath, relativePath);
            }
        }
    }

    /**
     * 处理导出值
     * @param {Object} node AST 节点
     * @param {string} filePath 文件路径
     * @param {string} relativePath 相对路径
     */
    handleExportValue(node, filePath, relativePath) {
        if (!node) return;

        switch (node.type) {
            case 'ObjectExpression':
                this.handleObjectExpression(node, filePath, relativePath);
                break;
            case 'ArrayExpression':
                this.handleArrayExpression(node, filePath, relativePath);
                break;
            case 'Identifier':
                this.handleIdentifierReference(node, filePath, relativePath);
                break;
            case 'CallExpression':
                this.handleCallExpression(node, filePath, relativePath);
                break;
        }
    }

    /**
     * 获取相对于 locales 目录的路径
     * @param {string} filePath 文件绝对路径
     * @returns {string} 相对路径
     */
    getRelativePath(filePath) {
        return path.relative(this.localesDir, filePath).replace(/\\/g, '/');
    }

    /**
     * 解析 import 路径
     * @param {string} currentFile 当前文件路径
     * @param {string} importPath import 路径
     * @returns {string} 解析后的绝对路径
     */
    resolveImportPath(currentFile, importPath) {
        // 忽略第三方库导入
        if (!importPath.startsWith('.')) {
            return null;
        }

        const currentDir = path.dirname(currentFile);
        let resolvedPath = path.resolve(currentDir, importPath);
        
        // 如果没有扩展名，尝试添加 .js
        if (!path.extname(resolvedPath)) {
            const jsPath = resolvedPath + '.js';
            if (fs.existsSync(jsPath)) {
                resolvedPath = jsPath;
            }
        }
        
        return resolvedPath;
    }

    /**
     * 处理对象表达式
     * @param {Object} node 对象节点
     * @param {string} filePath 文件路径
     * @param {string} relativePath 相对路径
     */
    handleObjectExpression(node, filePath, relativePath) {
        // 查找 key 属性
        const keyProperty = node.properties.find(prop =>
            prop.key && prop.key.name === 'key'
        );

        // 查找 name 属性
        const nameProperty = node.properties.find(prop =>
            prop.key && prop.key.name === 'name'
        );

        // 只处理同时包含 key 和 name 属性的对象
        if (keyProperty && keyProperty.value && keyProperty.value.type === 'StringLiteral' &&
            nameProperty && nameProperty.value && nameProperty.value.type === 'StringLiteral') {

            const key = keyProperty.value.value;

            // 查找 children 属性
            const childrenProperty = node.properties.find(prop =>
                prop.key && prop.key.name === 'children'
            );

            if (childrenProperty && childrenProperty.value) {
                if (childrenProperty.value.type === 'Identifier') {
                    // children 是外部引用，映射到引用的实际文件路径
                    const referencedPaths = this.resolveReference(childrenProperty.value.name, filePath);
                    for (const refPath of referencedPaths) {
                        this.addKeyToPath(key, refPath);
                    }
                } else if (childrenProperty.value.type === 'ArrayExpression') {
                    // children 是数组，检查是否为叶子节点
                    if (this.isLeafNode(childrenProperty.value, filePath)) {
                        // 普通数组且没有下级 children，映射到当前文件路径
                        this.addKeyToPath(key, relativePath);
                    } else {
                        // 数组中还有嵌套结构，递归处理
                        this.handleExportValue(childrenProperty.value, filePath, relativePath);
                    }
                } else if (childrenProperty.value.type === 'CallExpression') {
                    // children 是函数调用（如 lodash 合并），处理动态合并
                    const referencedPaths = this.handleCallExpression(childrenProperty.value, filePath, relativePath);
                    for (const refPath of referencedPaths) {
                        this.addKeyToPath(key, refPath);
                    }
                } else {
                    // 其他类型的 children，递归处理
                    this.handleExportValue(childrenProperty.value, filePath, relativePath);
                }
            }
        }

        // 递归处理对象的其他属性
        for (const property of node.properties) {
            if (property.value && property.key && property.key.name !== 'key' && property.key.name !== 'name') {
                this.handleExportValue(property.value, filePath, relativePath);
            }
        }
    }

    /**
     * 处理数组表达式
     * @param {Object} node 数组节点
     * @param {string} filePath 文件路径
     * @param {string} relativePath 相对路径
     */
    handleArrayExpression(node, filePath, relativePath) {
        for (const element of node.elements) {
            if (element) {
                // 检查数组元素是否是叶子节点
                if (element.type === 'ObjectExpression') {
                    const keyProperty = element.properties.find(prop =>
                        prop.key && prop.key.name === 'key'
                    );

                    const nameProperty = element.properties.find(prop =>
                        prop.key && prop.key.name === 'name'
                    );

                    // 只处理同时包含 key 和 name 属性的对象
                    if (keyProperty && keyProperty.value && keyProperty.value.type === 'StringLiteral' &&
                        nameProperty && nameProperty.value && nameProperty.value.type === 'StringLiteral') {

                        const key = keyProperty.value.value;

                        // 检查是否有 children 属性
                        const hasChildren = element.properties.some(prop =>
                            prop.key && prop.key.name === 'children'
                        );

                        if (!hasChildren) {
                            // 这是一个叶子节点，同时包含 key 和 name
                            this.addKeyToPath(key, relativePath);
                        }
                    }
                }

                this.handleExportValue(element, filePath, relativePath);
            }
        }
    }

    /**
     * 处理标识符引用
     * @param {Object} node 标识符节点
     * @param {string} filePath 文件路径
     * @param {string} relativePath 相对路径
     */
    handleIdentifierReference(node, filePath, relativePath) {
        const referencedPaths = this.resolveReference(node.name, filePath);
        // 对于引用，我们不在当前文件添加映射，而是处理引用的文件
        for (const refPath of referencedPaths) {
            if (fs.existsSync(path.resolve(this.localesDir, refPath))) {
                this.processFile(path.resolve(this.localesDir, refPath));
            }
        }
    }

    /**
     * 处理函数调用表达式（如 lodash 的 uniqBy）
     * @param {Object} node 调用表达式节点
     * @param {string} filePath 文件路径
     * @param {string} relativePath 相对路径
     * @returns {Array} 解析到的引用文件路径数组
     */
    handleCallExpression(node, filePath, relativePath) {
        const referencedPaths = [];

        // 处理 _.uniqBy([...array1, ...array2], 'key') 这种情况
        if (node.callee && node.callee.type === 'MemberExpression' &&
            node.callee.property && node.callee.property.name === 'uniqBy') {

            const firstArg = node.arguments[0];
            if (firstArg && firstArg.type === 'ArrayExpression') {
                // 处理展开的数组元素
                for (const element of firstArg.elements) {
                    if (element && element.type === 'SpreadElement') {
                        // 处理 ...array 展开语法
                        if (element.argument.type === 'Identifier') {
                            const refPaths = this.resolveReference(element.argument.name, filePath);
                            referencedPaths.push(...refPaths);
                        }
                    } else if (element && element.type === 'Identifier') {
                        // 处理直接的标识符引用
                        const refPaths = this.resolveReference(element.name, filePath);
                        referencedPaths.push(...refPaths);
                    }
                }
            }
        } else {
            // 处理其他类型的函数调用
            for (const arg of node.arguments) {
                if (arg.type === 'Identifier') {
                    const refPaths = this.resolveReference(arg.name, filePath);
                    referencedPaths.push(...refPaths);
                }
            }
        }

        return referencedPaths;
    }

    /**
     * 检查节点是否是叶子节点
     * @param {Object} node AST 节点
     * @param {string} filePath 文件路径
     * @returns {boolean} 是否是叶子节点
     */
    isLeafNode(node, filePath) {
        if (node.type === 'ArrayExpression') {
            // 检查数组中的元素是否都不包含 children 属性
            return node.elements.every(element => {
                if (element && element.type === 'ObjectExpression') {
                    return !element.properties.some(prop =>
                        prop.key && prop.key.name === 'children'
                    );
                }
                return true;
            });
        }

        if (node.type === 'Identifier') {
            // 对于引用，需要解析引用的内容来判断
            const referencedContent = this.getReferencedContent(node.name, filePath);
            if (referencedContent) {
                return this.isLeafNode(referencedContent, filePath);
            }
        }

        return false;
    }

    /**
     * 解析引用
     * @param {string} variableName 变量名
     * @param {string} filePath 当前文件路径
     * @returns {Array} 引用的文件路径数组
     */
    resolveReference(variableName, filePath) {
        const imports = this.importCache.get(filePath);
        if (!imports || !imports.has(variableName)) {
            return [];
        }

        const importInfo = imports.get(variableName);
        if (!importInfo.path) {
            return [];
        }

        const referencedPath = this.getRelativePath(importInfo.path);
        return [referencedPath];
    }

    /**
     * 获取引用的内容
     * @param {string} variableName 变量名
     * @param {string} filePath 当前文件路径
     * @returns {Object|null} 引用的 AST 节点
     */
    getReferencedContent(variableName, filePath) {
        const imports = this.importCache.get(filePath);
        if (!imports || !imports.has(variableName)) {
            return null;
        }

        const importInfo = imports.get(variableName);
        if (!importInfo.path || !fs.existsSync(importInfo.path)) {
            return null;
        }

        try {
            const content = fs.readFileSync(importInfo.path, 'utf8');
            const ast = babelParser.parse(content, {
                sourceType: 'module',
                plugins: ['typescript'],
            });

            let referencedNode = null;

            babel.default(ast, {
                ExportNamedDeclaration: (path) => {
                    if (path.node.declaration && path.node.declaration.type === 'VariableDeclaration') {
                        for (const declarator of path.node.declaration.declarations) {
                            if (declarator.id.name === importInfo.exportName) {
                                referencedNode = declarator.init;
                                break;
                            }
                        }
                    }
                },
                ExportDefaultDeclaration: (path) => {
                    if (importInfo.exportName === 'default') {
                        referencedNode = path.node.declaration;
                    }
                }
            });

            return referencedNode;
        } catch (error) {
            console.error(`获取引用内容时出错 ${importInfo.path}:`, error);
            return null;
        }
    }

    /**
     * 添加 key 到路径映射
     * @param {string} key 配置 key
     * @param {string} relativePath 相对路径
     */
    addKeyToPath(key, relativePath) {
        if (!this.keyToPathsMap.has(key)) {
            this.keyToPathsMap.set(key, []);
        }
        this.keyToPathsMap.get(key).push(relativePath);
    }

    /**
     * 获取扫描统计信息
     * @returns {Object} 统计信息
     */
    getStatistics() {
        const stats = {
            totalKeys: this.keyToPathsMap.size,
            totalFiles: this.processedFiles.size,
            keysWithMultiplePaths: 0,
            pathDistribution: {}
        };

        for (const [key, paths] of this.keyToPathsMap) {
            if (paths.length > 1) {
                stats.keysWithMultiplePaths++;
            }

            for (const path of paths) {
                const dir = path.split('/')[0];
                stats.pathDistribution[dir] = (stats.pathDistribution[dir] || 0) + 1;
            }
        }

        return stats;
    }

    /**
     * 根据路径前缀过滤结果
     * @param {string} pathPrefix 路径前缀
     * @returns {Object} 过滤后的映射
     */
    filterByPathPrefix(pathPrefix) {
        const filtered = {};

        for (const [key, paths] of this.keyToPathsMap) {
            const matchingPaths = paths.filter(path => path.startsWith(pathPrefix));
            if (matchingPaths.length > 0) {
                filtered[key] = matchingPaths;
            }
        }

        return filtered;
    }

    /**
     * 查找重复的 key
     * @returns {Object} 重复 key 的映射
     */
    findDuplicateKeys() {
        const duplicates = {};

        for (const [key, paths] of this.keyToPathsMap) {
            if (paths.length > 1) {
                duplicates[key] = [...new Set(paths)]; // 去重
            }
        }

        return duplicates;
    }
}

// 导出扫描器实例和便捷方法
const scanner = new ConfigStructureScanner();

/**
 * 扫描所有多语言配置文件并返回 key 到路径的映射
 * @returns {Object} key 到文件路径数组的映射
 */
export const scanConfigStructure = () => {
    return scanner.scanAllConfigs();
};

/**
 * 获取扫描统计信息
 * @returns {Object} 统计信息
 */
export const getConfigStatistics = () => {
    scanner.scanAllConfigs(); // 确保已扫描
    return scanner.getStatistics();
};

/**
 * 根据路径前缀过滤配置
 * @param {string} pathPrefix 路径前缀（如 'main', 'project', 'report'）
 * @returns {Object} 过滤后的映射
 */
export const filterConfigByPath = (pathPrefix) => {
    scanner.scanAllConfigs(); // 确保已扫描
    return scanner.filterByPathPrefix(pathPrefix);
};

/**
 * 查找重复的配置 key
 * @returns {Object} 重复 key 的映射
 */
export const findDuplicateConfigKeys = () => {
    scanner.scanAllConfigs(); // 确保已扫描
    return scanner.findDuplicateKeys();
};

/**
 * 根据 key 查找对应的文件路径
 * @param {string} key 配置 key
 * @returns {Array} 文件路径数组
 */
export const findPathsByKey = (key) => {
    const mapping = scanner.scanAllConfigs();
    return mapping[key] || [];
};

/**
 * 验证配置结构的完整性
 * @returns {Object} 验证结果
 */
export const validateConfigStructure = () => {
    const mapping = scanner.scanAllConfigs();
    const stats = scanner.getStatistics();
    const duplicates = scanner.findDuplicateKeys();

    return {
        isValid: Object.keys(duplicates).length === 0,
        totalKeys: stats.totalKeys,
        totalFiles: stats.totalFiles,
        duplicateKeys: duplicates,
        pathDistribution: stats.pathDistribution,
        warnings: Object.keys(duplicates).length > 0 ?
            [`发现 ${Object.keys(duplicates).length} 个重复的配置 key`] : [],
        suggestions: stats.keysWithMultiplePaths > 0 ?
            [`建议检查 ${stats.keysWithMultiplePaths} 个存在多路径映射的 key`] : []
    };
};

/**
 * 生成配置结构报告
 * @returns {Object} 详细报告
 */
export const generateConfigReport = () => {
    const mapping = scanner.scanAllConfigs();
    const stats = scanner.getStatistics();
    const duplicates = scanner.findDuplicateKeys();

    // 按模块分组统计
    const moduleStats = {};
    for (const [key, paths] of Object.entries(mapping)) {
        for (const path of paths) {
            const module = path.split('/')[0];
            if (!moduleStats[module]) {
                moduleStats[module] = { keys: 0, files: new Set() };
            }
            moduleStats[module].keys++;
            moduleStats[module].files.add(path);
        }
    }

    // 转换 Set 为数组长度
    for (const module of Object.keys(moduleStats)) {
        moduleStats[module].files = moduleStats[module].files.size;
    }

    return {
        summary: {
            totalKeys: stats.totalKeys,
            totalFiles: stats.totalFiles,
            duplicateKeys: Object.keys(duplicates).length,
            modules: Object.keys(moduleStats).length
        },
        moduleBreakdown: moduleStats,
        duplicateAnalysis: duplicates,
        keyToPathMapping: mapping,
        generatedAt: new Date().toISOString()
    };
};

export default ConfigStructureScanner;
