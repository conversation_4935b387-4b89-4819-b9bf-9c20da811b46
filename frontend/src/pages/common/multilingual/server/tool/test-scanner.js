import {
    scanConfigStructure,
    getConfigStatistics,
    filterConfigByPath,
    findDuplicateConfigKeys,
    findPathsByKey,
    validateConfigStructure,
    generateConfigReport
} from './config-structure-scanner.js';

/**
 * 测试配置结构扫描器的功能
 */
function testConfigScanner() {
    console.log('=== 多语言配置文件结构扫描器测试 ===\n');

    try {
        // 1. 基本扫描测试
        console.log('1. 执行基本扫描...');
        const mapping = scanConfigStructure();
        console.log(`扫描完成，发现 ${Object.keys(mapping).length} 个配置 key`);
        
        // 显示前10个映射示例
        const sampleKeys = Object.keys(mapping).slice(0, 10);
        console.log('\n前10个配置 key 示例:');
        sampleKeys.forEach(key => {
            console.log(`  ${key} -> [${mapping[key].join(', ')}]`);
        });

        // 2. 统计信息测试
        console.log('\n2. 获取统计信息...');
        const stats = getConfigStatistics();
        console.log('统计信息:', JSON.stringify(stats, null, 2));

        // 3. 路径过滤测试
        console.log('\n3. 测试路径过滤...');
        const mainConfigs = filterConfigByPath('main');
        console.log(`main 模块配置数量: ${Object.keys(mainConfigs).length}`);
        
        const reportConfigs = filterConfigByPath('report');
        console.log(`report 模块配置数量: ${Object.keys(reportConfigs).length}`);

        // 4. 重复 key 检测
        console.log('\n4. 检测重复配置 key...');
        const duplicates = findDuplicateConfigKeys();
        console.log(`发现 ${Object.keys(duplicates).length} 个重复的配置 key`);
        
        if (Object.keys(duplicates).length > 0) {
            console.log('重复 key 详情:');
            Object.entries(duplicates).slice(0, 5).forEach(([key, paths]) => {
                console.log(`  ${key} -> [${paths.join(', ')}]`);
            });
        }

        // 5. 按 key 查找路径
        console.log('\n5. 测试按 key 查找路径...');
        const testKeys = ['report', 'main', 'project'];
        testKeys.forEach(key => {
            const paths = findPathsByKey(key);
            console.log(`  ${key} -> [${paths.join(', ')}]`);
        });

        // 6. 配置结构验证
        console.log('\n6. 验证配置结构...');
        const validation = validateConfigStructure();
        console.log('验证结果:', JSON.stringify(validation, null, 2));

        // 7. 生成详细报告
        console.log('\n7. 生成配置结构报告...');
        const report = generateConfigReport();
        console.log('报告摘要:', JSON.stringify(report.summary, null, 2));
        console.log('模块分布:', JSON.stringify(report.moduleBreakdown, null, 2));

        console.log('\n=== 测试完成 ===');
        
        return {
            success: true,
            mapping,
            stats,
            validation,
            report
        };

    } catch (error) {
        console.error('测试过程中出现错误:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * 测试特定功能
 */
function testSpecificFeatures() {
    console.log('\n=== 特定功能测试 ===\n');

    try {
        // 测试复杂引用解析
        console.log('1. 测试复杂引用解析...');
        const mapping = scanConfigStructure();
        
        // 查找使用了 lodash 合并的配置
        const reportKey = findPathsByKey('report');
        console.log(`report key 映射到的路径: [${reportKey.join(', ')}]`);

        // 测试叶子节点识别
        console.log('\n2. 测试叶子节点识别...');
        const leafKeys = Object.entries(mapping).filter(([key, paths]) => {
            // 查找只映射到单个文件且文件名包含具体字段的 key
            return paths.length === 1 && paths[0].includes('field');
        });
        
        console.log(`发现 ${leafKeys.length} 个叶子节点配置`);
        leafKeys.slice(0, 5).forEach(([key, paths]) => {
            console.log(`  ${key} -> ${paths[0]}`);
        });

        // 测试动态合并处理
        console.log('\n3. 测试动态合并处理...');
        const mergedKeys = Object.entries(mapping).filter(([key, paths]) => {
            return paths.length > 1;
        });
        
        console.log(`发现 ${mergedKeys.length} 个使用动态合并的配置`);
        mergedKeys.slice(0, 3).forEach(([key, paths]) => {
            console.log(`  ${key} -> [${paths.join(', ')}]`);
        });

        // 测试 key 和 name 过滤逻辑
        console.log('\n4. 测试 key 和 name 过滤逻辑...');
        console.log('注意：扫描器现在只收集同时包含 key 和 name 属性的配置对象');
        console.log('只有 key 而没有 name 的对象将被跳过');

        // 显示一些示例来验证过滤逻辑
        const sampleEntries = Object.entries(mapping).slice(0, 5);
        console.log('示例配置（应该都同时包含 key 和 name）:');
        sampleEntries.forEach(([key, paths]) => {
            console.log(`  ${key} -> [${paths.join(', ')}]`);
        });

        console.log('\n=== 特定功能测试完成 ===');

    } catch (error) {
        console.error('特定功能测试中出现错误:', error);
    }
}

// 如果直接运行此文件，执行测试
const result = testConfigScanner();
testConfigScanner()
testSpecificFeatures();
if (result.success) {
    console.log('\n✅ 所有测试通过');
} else {
    console.log('\n❌ 测试失败:', result.error);
    process.exit(1);
}

export { testConfigScanner, testSpecificFeatures };
