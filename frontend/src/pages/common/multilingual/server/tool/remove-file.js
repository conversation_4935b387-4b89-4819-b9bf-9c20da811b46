import fs from "fs";
import _ from 'lodash';
import {getFilePathAndVariableName, modifyExistingFile} from './common-utils.js';
import { scanConfigStructure } from './config-structure-scanner.js';

/**
 * 从相应的 locales 文件中移除字段
 * @param {string} fileName - 文件名（基于路径生成）
 * @param {Array} fields - 字段数组，每个字段包含 key, path
 */
export const removeFile = (fileName, fields) => {
    if (!fields || !Array.isArray(fields) || fields.length === 0) {
        return Promise.reject(new Error('字段数据不能为空'));
    }

    // 获取文件映射
    const fileMapping = scanConfigStructure();

    // 按实际文件路径分组字段（处理多个 children 映射到同一文件的情况）
    const groupedFields = _.groupBy(fields, (field) => {
        const fieldPath = field.path || [];
        const { filePath } = getFilePathAndVariableName(fieldPath, fileMapping);
        return filePath; // 按实际文件路径分组
    });

    // 为每个文件处理移除
    const removePromises = Object.entries(groupedFields).map(([filePath, fieldsGroup]) => {
        const firstField = fieldsGroup[0];
        const fieldPath = firstField.path || [];

        // 构建变量名
        const { variableName } = getFilePathAndVariableName(fieldPath, fileMapping);

        // 检查文件是否存在
        if (fs.existsSync(filePath)) {
            return modifyExistingFile(filePath, variableName, fieldsGroup, 'remove');
        } else {
            return Promise.reject(new Error(`文件 ${filePath} 不存在，无法移除字段`));
        }
    });

    return Promise.all(removePromises).then(results => {
        const totalFields = fields.length;
        const fileCount = results.length;
        return `成功从 ${fileCount} 个文件中移除 ${totalFields} 个字段`;
    });
};
