import fs from "fs";
import babelParser from '@babel/parser';
import babel from '@babel/traverse';
import generate from '@babel/generator';
import t from '@babel/types';

/**
 * 根据路径数组确定文件路径和变量名
 * 规则：选择路径的最后一层与 locales 中文件夹内的 key 做对应
 */
export const getFilePathAndVariableName = (fieldPath, fileMapping) => {
    // 使用相对路径，从 server/tool 目录到 locales 目录
    const localesDir = '../locales';

    if (!fieldPath || fieldPath.length === 0) {
        return {
            filePath: `${localesDir}/main/multi_language.js`,
            variableName: 'multi_language'
        };
    }

    // 获取路径的最后一层作为目标 key
    const targetKey = fieldPath[fieldPath.length - 1];

    // 动态扫描 locales 目录，查找包含目标 key 的文件
    const result = fileMapping[targetKey] || Object.values(fileMapping).flat().filter(it => it.includes(targetKey))
    if (result && result.length > 0) {
        return {
            filePath: `${localesDir}/${result[0]}`,
            variableName: targetKey
        }
    }

    // 如果没有找到，使用默认路径
    return {
        filePath: `${localesDir}/main/multi_language.js`,
        variableName: 'multi_language'
    };
};


/**
 * 格式化代码，确保符合原始文件格式
 */
export const formatCode = (code) => {
    // 使用正则表达式重新格式化代码
    // 匹配 export const variableName = [...]
    const exportMatch = code.match(/export const (\w+) = \[(.*)\];/s);

    if (!exportMatch) {
        return code; // 如果格式不匹配，返回原代码
    }

    const variableName = exportMatch[1];
    const arrayContent = exportMatch[2];

    // 解析数组中的对象
    const objects = parseObjects(arrayContent);

    // 生成格式化的代码
    let result = `export const ${variableName} = [\n`;

    objects.forEach((obj, index) => {
        result += '    {\n';
        result += `        key: "${obj.key}"`;

        if (obj.type) {
            result += ',\n';
            result += `        type: "${obj.type}"`;
        }

        result += '\n    }';

        if (index < objects.length - 1) {
            result += ',';
        }

        result += '\n';
    });

    result += '];';

    return result;
};

/**
 * 解析数组中的对象
 */
const parseObjects = (arrayContent) => {
    const objects = [];

    // 使用正则表达式匹配对象，支持单引号、双引号和不带引号的属性名和值
    const objectRegex = /\{\s*["']?key["']?\s*:\s*["']?([^"',}]+)["']?(?:\s*,\s*["']?type["']?\s*:\s*["']?([^"',}]+)["']?)?\s*\}/g;
    let match;

    while ((match = objectRegex.exec(arrayContent)) !== null) {
        const obj = {
            key: match[1]
        };

        if (match[2]) {
            obj.type = match[2];
        }

        objects.push(obj);
    }

    return objects;
};

/**
 * 通用文件操作函数 - 支持添加和移除字段
 * @param {string} filePath - 文件路径
 * @param {string} variableName - 变量名
 * @param {Array} fields - 字段数组
 * @param {string} operation - 操作类型：'add' 或 'remove'
 * @returns {Promise} 操作结果
 */
export const modifyExistingFile = (filePath, variableName, fields, operation = 'add') => {
    return new Promise((resolve, reject) => {
        fs.readFile(filePath, 'utf8', (err, data) => {
            if (err) {
                reject(err);
                return;
            }

            try {
                // 解析现有文件
                const ast = babelParser.parse(data, {
                    sourceType: 'module',
                    plugins: ['typescript'],
                });

                let foundVariable = false;
                let targetExportDeclaration = null;

                // 遍历 AST，找到对应的变量声明
                babel.default(ast, {
                    enter(path) {
                        if (
                            path.node.type === 'ExportNamedDeclaration' &&
                            path.node.declaration &&
                            path.node.declaration.type === 'VariableDeclaration'
                        ) {
                            const declaration = path.node.declaration.declarations[0];
                            if (declaration.id.name === variableName) {
                                foundVariable = true;
                                targetExportDeclaration = declaration;
                            }
                        }
                    }
                });

                if (!foundVariable) {
                    reject(new Error(`在文件 ${filePath} 中未找到变量 ${variableName}`));
                    return;
                }

                let operationResult;
                if (operation === 'add') {
                    operationResult = handleAddOperation(targetExportDeclaration, variableName, fields);
                } else if (operation === 'remove') {
                    operationResult = handleRemoveOperation(targetExportDeclaration, variableName, fields);
                } else {
                    reject(new Error(`不支持的操作类型: ${operation}`));
                    return;
                }

                // 生成新代码
                const { code: newCode } = generate.default(ast, {
                    compact: false,
                    retainLines: false,
                    concise: false,
                    minified: false,
                    jsescOption: {
                        quotes: 'double'
                    }
                });

                // 格式化代码，确保每个字段占一行
                const formattedCode = formatCode(newCode);

                // 写入文件
                fs.writeFile(filePath, formattedCode, 'utf8', (writeErr) => {
                    if (writeErr) {
                        reject(writeErr);
                    } else {
                        const action = operation === 'add' ? '添加' : '移除';
                        resolve(`成功${action} ${operationResult.count} 个字段到文件 ${filePath}`);
                    }
                });
            } catch (parseError) {
                reject(parseError);
            }
        });
    });
};

/**
 * 处理添加操作
 * @param {Object} declaration - 目标变量声明
 * @param {string} variableName - 变量名
 * @param {Array} fields - 要添加的字段数组
 * @returns {Object} 操作结果
 */
const handleAddOperation = (declaration, variableName, fields) => {
    // 检查数组内容类型
    const arrayElements = declaration.init.elements;
    const hasNameAndChildren = arrayElements.some(element => {
        if (element.type === 'ObjectExpression') {
            const hasName = element.properties.some(prop =>
                prop.key && (prop.key.name === 'name' || prop.key.value === 'name')
            );
            const hasChildren = element.properties.some(prop =>
                prop.key && (prop.key.name === 'children' || prop.key.value === 'children')
            );
            return hasName && hasChildren;
        }
        return false;
    });

    if (!hasNameAndChildren) {
        // 情况1: 只包含简单字段对象，直接插入
        return handleSimpleFieldInsertion(declaration, fields);
    } else {
        // 情况2: 包含name和children字段，需要找到匹配的对象并插入到其children中
        return handleComplexFieldInsertion(declaration, variableName, fields);
    }
};

/**
 * 处理移除操作
 * @param {Object} declaration - 目标变量声明
 * @param {string} variableName - 变量名
 * @param {Array} fields - 要移除的字段数组
 * @returns {Object} 操作结果
 */
const handleRemoveOperation = (declaration, variableName, fields) => {
    // 创建要移除的字段key集合
    const fieldsToRemove = new Set(fields.map(field => field.key));
    let removedCount = 0;

    // 检查数组内容类型
    const arrayElements = declaration.init.elements;
    const hasNameAndChildren = arrayElements.some(element => {
        if (element.type === 'ObjectExpression') {
            const hasName = element.properties.some(prop =>
                prop.key && (prop.key.name === 'name' || prop.key.value === 'name')
            );
            const hasChildren = element.properties.some(prop =>
                prop.key && (prop.key.name === 'children' || prop.key.value === 'children')
            );
            return hasName && hasChildren;
        }
        return false;
    });

    if (!hasNameAndChildren) {
        // 情况1: 简单字段数组，直接移除
        const originalLength = declaration.init.elements.length;
        declaration.init.elements = declaration.init.elements.filter(element => {
            if (element.type === 'ObjectExpression') {
                const keyProperty = element.properties.find(prop =>
                    prop.key && (prop.key.name === 'key' || prop.key.value === 'key')
                );
                if (keyProperty && keyProperty.value) {
                    const fieldKey = keyProperty.value.value;
                    return !fieldsToRemove.has(fieldKey);
                }
            }
            return true;
        });
        removedCount = originalLength - declaration.init.elements.length;
    } else {
        // 情况2: 复杂字段结构，从匹配对象的children中移除
        removedCount = handleComplexFieldRemoval(declaration, variableName, fieldsToRemove, fields);
    }

    return { count: removedCount };
};

/**
 * 处理简单字段插入（情况1：文件只包含简单字段对象）
 * @param {Object} declaration - 目标变量声明
 * @param {Array} fields - 要插入的字段数组
 * @returns {Object} 操作结果
 */
const handleSimpleFieldInsertion = (declaration, fields) => {
    fields.forEach(field => {
        const properties = [
            t.objectProperty(t.identifier("key"), t.stringLiteral(field.key))
        ];

        if (field.type) {
            properties.push(
                t.objectProperty(t.identifier("type"), t.stringLiteral(field.type))
            );
        }

        const objectExpression = t.objectExpression(properties);
        declaration.init.elements.push(objectExpression);
    });

    return { count: fields.length };
};

/**
 * 处理复杂字段插入（情况2：文件包含name和children字段）
 * @param {Object} declaration - 目标变量声明
 * @param {string} variableName - 变量名
 * @param {Array} fields - 要插入的字段数组
 * @returns {Object} 操作结果
 */
const handleComplexFieldInsertion = (declaration, variableName, fields) => {
    // 查找key与variableName相等且包含children的对象
    // 如果字段有path信息，使用path的最后一层作为目标key
    let targetKey = variableName;
    if (fields.length > 0 && fields[0].path && fields[0].path.length > 1) {
        targetKey = fields[0].path[fields[0].path.length - 1];
    }

    let targetObject = null;

    declaration.init.elements.forEach(element => {
        if (element.type === 'ObjectExpression') {
            const keyProperty = element.properties.find(prop =>
                prop.key && (prop.key.name === 'key' || prop.key.value === 'key')
            );

            const childrenProperty = element.properties.find(prop =>
                prop.key && (prop.key.name === 'children' || prop.key.value === 'children')
            );

            if (keyProperty && childrenProperty &&
                keyProperty.value.value === targetKey) {
                targetObject = element;
            }
        }
    });

    if (targetObject) {
        // 找到目标对象，插入到其children数组中
        const childrenProperty = targetObject.properties.find(prop =>
            prop.key && (prop.key.name === 'children' || prop.key.value === 'children')
        );

        if (childrenProperty && childrenProperty.value.type === 'ArrayExpression') {
            fields.forEach(field => {
                const properties = [
                    t.objectProperty(t.identifier("key"), t.stringLiteral(field.key))
                ];

                if (field.type) {
                    properties.push(
                        t.objectProperty(t.identifier("type"), t.stringLiteral(field.type))
                    );
                }

                const objectExpression = t.objectExpression(properties);
                childrenProperty.value.elements.push(objectExpression);
            });
        }
    } else {
        // 如果没有找到匹配的对象，创建一个新的对象
        const childrenArray = t.arrayExpression(
            fields.map(field => {
                const properties = [
                    t.objectProperty(t.identifier("key"), t.stringLiteral(field.key))
                ];

                if (field.type) {
                    properties.push(
                        t.objectProperty(t.identifier("type"), t.stringLiteral(field.type))
                    );
                }

                return t.objectExpression(properties);
            })
        );

        const newObject = t.objectExpression([
            t.objectProperty(t.identifier("key"), t.stringLiteral(variableName)),
            t.objectProperty(t.identifier("name"), t.stringLiteral(`menu.${variableName.replace('_', '.')}`)),
            t.objectProperty(t.identifier("children"), childrenArray)
        ]);

        declaration.init.elements.push(newObject);
    }

    return { count: fields.length };
};

/**
 * 处理复杂字段移除
 * @param {Object} declaration - 目标变量声明
 * @param {string} variableName - 变量名
 * @param {Set} fieldsToRemove - 要移除的字段key集合
 * @param {Array} fields - 原始字段数组（用于获取path信息）
 * @returns {number} 移除的字段数量
 */
const handleComplexFieldRemoval = (declaration, variableName, fieldsToRemove, fields = []) => {
    let removedCount = 0;

    // 查找key与variableName相等且包含children的对象
    // 如果字段有path信息，使用path的最后一层作为目标key
    let targetKey = variableName;
    if (fields.length > 0 && fields[0].path && fields[0].path.length > 1) {
        targetKey = fields[0].path[fields[0].path.length - 1];
    }

    let targetObject = null;

    declaration.init.elements.forEach(element => {
        if (element.type === 'ObjectExpression') {
            const keyProperty = element.properties.find(prop =>
                prop.key && (prop.key.name === 'key' || prop.key.value === 'key')
            );

            const childrenProperty = element.properties.find(prop =>
                prop.key && (prop.key.name === 'children' || prop.key.value === 'children')
            );

            if (keyProperty && childrenProperty &&
                keyProperty.value.value === targetKey) {
                targetObject = element;
            }
        }
    });

    if (targetObject) {
        // 找到目标对象，从其children数组中移除字段
        const childrenProperty = targetObject.properties.find(prop =>
            prop.key && (prop.key.name === 'children' || prop.key.value === 'children')
        );

        if (childrenProperty && childrenProperty.value.type === 'ArrayExpression') {
            const originalLength = childrenProperty.value.elements.length;
            childrenProperty.value.elements = childrenProperty.value.elements.filter(element => {
                if (element.type === 'ObjectExpression') {
                    const keyProperty = element.properties.find(prop =>
                        prop.key && (prop.key.name === 'key' || prop.key.value === 'key')
                    );
                    if (keyProperty && keyProperty.value) {
                        const fieldKey = keyProperty.value.value;
                        return !fieldsToRemove.has(fieldKey);
                    }
                }
                return true;
            });
            removedCount = originalLength - childrenProperty.value.elements.length;

            // 如果children数组为空，移除整个对象
            if (childrenProperty.value.elements.length === 0) {
                const objectIndex = declaration.init.elements.indexOf(targetObject);
                if (objectIndex > -1) {
                    declaration.init.elements.splice(objectIndex, 1);
                }
            }
        }
    }

    return removedCount;
};

/**
 * 创建新文件
 * @param {string} filePath - 文件路径
 * @param {string} variableName - 变量名
 * @param {Array} fields - 字段数组
 * @returns {Promise} 操作结果
 */
export const createNewFile = (filePath, variableName, fields) => {
    return new Promise((resolve, reject) => {
        try {
            // 创建新的数组内容
            const fieldObjects = fields.map(field => {
                const properties = [
                    `        "key": "${field.key}"`
                ];

                if (field.type) {
                    properties.push(`        "type": "${field.type}"`);
                }

                return `    {\n${properties.join(',\n')}\n    }`;
            });

            const fileContent = `export const ${variableName} = [\n${fieldObjects.join(',\n')}\n];\n`;

            // 写入文件
            fs.writeFile(filePath, fileContent, 'utf8', (writeErr) => {
                if (writeErr) {
                    reject(writeErr);
                } else {
                    resolve(`成功创建新文件 ${filePath} 并添加 ${fields.length} 个字段`);
                }
            });
        } catch (error) {
            reject(error);
        }
    });
};