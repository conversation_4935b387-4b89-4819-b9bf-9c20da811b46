import express from 'express';
import path from 'path';
import cors from 'cors'
import bodyParser from 'body-parser'
import {addKey} from "./tool/add-key.js";
import {readMessageId} from "./tool/read-message-id.js";
import {addFile} from "./tool/add-file.js";
import {removeFile} from "./tool/remove-file.js";
const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
const __dirname = path.resolve();
app.use(express.static(path.join(__dirname, './static')));
app.use(cors()); // 允许所有来源的跨域请求
app.use(bodyParser.json()); // 用于解析JSON格式的请求体
app.use(bodyParser.urlencoded({ extended: true })); // 用于解析URL编码的请求体

const port = 8001;

app.get('/add-key', (req, res) => {
    const file = ''
    const declarationName = ''
    const field = {key: '', type: ''}
    addKey(file, declarationName, field).then((resp => res.status(200).send(resp)))
})

app.post('/add-file', (req, res) => {
    console.log('接收到添加字段请求:', req.body)
    const { fileName, fields } = req.body;

    if (!fields || !Array.isArray(fields) || fields.length === 0) {
        return res.status(400).send({ error: '字段数据不能为空' });
    }

    addFile(fileName, fields)
        .then(resp => {
            console.log('添加字段成功:', resp);
            res.status(200).send({ success: true, message: resp });
        })
        .catch(err => {
            console.error('添加字段失败:', err);
            res.status(500).send({ error: err.message || '添加字段失败' });
        });
})

app.get('/read-file', (req, res) => {
    readMessageId().then((resp => res.status(200).send(resp)))
})

app.post('/remove-file', (req, res) => {
    console.log('接收到移除字段请求:', req.body)
    const { fileName, fields } = req.body;

    if (!fields || !Array.isArray(fields) || fields.length === 0) {
        return res.status(400).send({ error: '字段数据不能为空' });
    }

    removeFile(fileName, fields)
        .then(resp => {
            console.log('移除字段成功:', resp);
            res.status(200).send({ success: true, message: resp });
        })
        .catch(err => {
            console.error('移除字段失败:', err);
            res.status(500).send({ error: err.message || '移除字段失败' });
        });
})

app.listen(port, () => {
    console.log(`Server running at http://localhost:${port}/`);
    console.log(`Mange page running at http://localhost:3000/locales-manage`);
});
export default app;