import { ReactNode } from "react";
import { Layout } from "antd";
import styled from "@emotion/styled";
import { useLocation } from "react-router-dom";

export const Content = ({ children }: { children: ReactNode }) => {
  let location = useLocation();
  return location.pathname === "/message-center" ||
    location.pathname.startsWith("/message-single/") ? (
    <MessageContent>
      <BodyContainer>
        <Body>{children}</Body>
      </BodyContainer>
    </MessageContent>
  ) : (
    <LayoutContent>
      <BodyContainer>
        {location.pathname === "/project/home" ||
        location.pathname === "/project/settings/notice" ? (
          location.pathname === "/project/home" ? (
            <HomeBody>{children}</HomeBody>
          ) : (
            <NoticeBody>{children}</NoticeBody>
          )
        ) : (
          <Body
            style={{
              padding: [
                "/project/build/attribute",
                "/project/sub/visit-cycle",
              ].includes(location.pathname)
                ? 0
                : "16px 24px",
            }}
          >
            {children}
          </Body>
        )}
      </BodyContainer>
    </LayoutContent>
  );
};

const LayoutContent = styled(Layout.Content)`
  flex: 1;
  width: 100%;
  // height    : calc( 100vh - 58px );
  padding: 8px 8px 0px 8px;
  background: #f0f0f0;
`;
const MessageContent = styled(Layout.Content)`
  flex: 1;
  width: 1000px;
  height: calc(100vh - 58px);
  padding: 24px 0px 0px 0px;
  background: #f0f2f5;
  margin: 0 auto;
`;

const BodyContainer = styled.div`
  min-height: calc(100vh - 58px);
  background: #ffffff;
  // overflow: auto;
`;

const HomeBody = styled.div`
  min-height: inherit;
  background-color: #f0f2f5;
`;
const Body = styled.div`
  //   padding: 16px 24px;
  // height: inherit;
`;

const NoticeBody = styled.div`
  padding: 0px;
  flex: 1;
  display: flex;
`;
