import {Col, Layout, Row} from "antd";
import {Navigation} from "./navigation-hor.jsx";
import styled from "@emotion/styled";
import {Person} from "pages/common/person";
import React from "react";
// import logo from 'images/logo24.svg';
// @ts-ignore
import logo from "../../../images/logo24.svg";
import {Message} from "./message.jsx";
import {Learning} from "./learning.jsx";

export const Header = () => {
    return (
        <LayoutHeader>
            <Row justify="space-between">
                <Row>
                    <Col>
                        <div style={{ display: "flex", alignItems: "center" }}>
                            <img alt={""} style={{ width: 24, marginLeft: 6, marginRight: 8 }} src={logo} />
                            <span style={{ marginTop: -2 }} className="brand-title">
                                Clinflash IRT
                            </span>
                            <Divider />
                        </div>
                    </Col>
                    <Col style={{ width: "650px" }}>
                        <Navigation />
                    </Col>
                </Row>
                <Row align="middle">
                    {/*<Col className="ver-center" style={{ margin: "0px 12px" }}>*/}
                    {/*<Language />*/}
                    {/*</Col>*/}
                    <Message />
                    <Learning />
                    <Person />
                </Row>
            </Row>
        </LayoutHeader>
    );
};

const Square = styled.div`
    background: #3562ec;
    border-radius: 4px;
    width: 28px;
    height: 28px;
    margin: 11px 8px 11px 0px;
`;

const Divider = styled.div`
  background-color: #F2F3F5;
  height: 24px;
  margin-left: 16px;
  width: 1px;
`;

const LayoutHeader = styled(Layout.Header)`
    padding: 0 10px !important;
    height: 50px !important;
    line-height: 50px !important;
    background: white !important;
    border-bottom: #f0f0f0 solid 1px;
`;
