import React, {ReactNode} from "react";
import {useSafeState} from "ahooks";

export const HomeContext = React.createContext<
    {
        rolesVisible: boolean;
        setRolesVisible: (data: boolean) => void;
        roles: any[];
        setRoles: (data: any[]) => void;
        learnVisible: boolean;
        setLearnVisible: (data: boolean) => void;
        mustLearn: boolean;
        setMustLearn: (data: boolean) => void;
        learnToken: any;
        setLearnToken: (data: any) => void;
        open: boolean;
        setOpen: (data: boolean) => void;
        keyword: string;
        setKeyword: (data: string) => void;
        doSearch: number;
        setDoSearch: (data: number) => void;
        focusOn: boolean;
        setFocusOn: (data: boolean) => void;
    }
    |
    null
    >(null);

export const HomeContextProvider = ({ children }: { children: ReactNode }) => {
    const [rolesVisible, setRolesVisible] = useSafeState<boolean>(false);
    const [learnVisible, setLearnVisible] = useSafeState<boolean>(false);
    const [mustLearn, setMustLearn] = useSafeState<boolean>(true);
    const [roles, setRoles] = useSafeState<any[]>([]);
    const [learnToken,setLearnToken] = useSafeState<any>("");
    const [open, setOpen] = useSafeState<boolean>(true);
    const [keyword, setKeyword] = useSafeState<string>("");
    const [doSearch,setDoSearch] = useSafeState<number>(0);
    const [focusOn, setFocusOn] = useSafeState<boolean>(false);
    return (
        <HomeContext.Provider
            value={
                {
                    rolesVisible, setRolesVisible,
                    roles, setRoles,
                    learnVisible, setLearnVisible,
                    learnToken,setLearnToken,
                    mustLearn,setMustLearn,
                    open, setOpen,
                    keyword,setKeyword,
                    doSearch,setDoSearch,
                    focusOn, setFocusOn,
                }
            }
        >
            {children}
        </HomeContext.Provider>
    )
};

export const useHome = () => {
    const context = React.useContext(HomeContext);
    if (!context) {
        throw new Error("useHome must be used in HomeContextProvider");
    }
    return context;
};

