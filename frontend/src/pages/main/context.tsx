import React, {ReactNode} from "react";
import {Menu} from "types/menu";
import {menu_main} from "data/menu";
import {useSafeState} from "ahooks";

export const MainContext = React.createContext<{
    menu: Menu[];
    setMenu: (v: Menu[]) => void;
    env: any;
    setEnv: (v: any) => void;
    cohort: any;
    setCohort: (v: any) => void;
    project: any;
    setProject: (v: any) => void;
    attr: any;
    setAttr: (v: any) => void;
} | null>(null);

export const MainContextProvider = ({ children }: { children: ReactNode }) => {
    const [menu, setMenu] = useSafeState<Menu[]>(menu_main);
    const [project, setProject] = useSafeState<any>();
    const [env, setEnv] = useSafeState<any>();
    const [cohort, setCohort] = useSafeState<any>();
    const [attr, setAttr] = useSafeState<any>();

    return (
        <MainContext.Provider
            value={{
                menu,
                setMenu,
                env,
                setEnv,
                cohort,
                setCohort,
                project,
                setProject,
                attr,
                setAttr,
            }}
        >
            {children}
        </MainContext.Provider>
    );
};

export const useMain = () => {
    const context = React.useContext(MainContext);
    if (!context) {
        throw new Error("useMain must be used in MainContextProvider");
    }
    return context;
};
