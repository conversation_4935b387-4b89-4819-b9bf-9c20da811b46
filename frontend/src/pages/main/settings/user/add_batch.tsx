import React, { useEffect } from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {Form, Input, message, Modal, Select, Radio, Space, Col, Row, Table, Switch, notification, } from "antd";
import {useFetch} from "../../../../hooks/request";
import {addBatchUserVerify, addBatchUser} from "../../../../api/user";
import {useSafeState} from "ahooks";
import {useGlobal} from "../../../../context/global";
import {list} from "../../../../api/roles";
import {setUsersRoles as setUsersRolesList} from "../../../../api/user";
import { EditReceivers } from "./edit-receivers";
import {useUser} from "./context";
import { UserAddPrompt } from "./add_prompt";
import { UserAddTip } from "./add_tip";

export const UserAdd = (props :any) => {

    const intl = useIntl();
    const {formatMessage} = intl;
    const g = useGlobal();
    const ctx = useUser();

    const [visible, setVisible] = useSafeState(false);
    const [customerId, setCustomerId] = useSafeState(null);

    const user_prompt: any = React.useRef();
    const user_tip: any = React.useRef();

    const [form] = Form.useForm();

    const show = (customerId:any) => {
        setVisible(true);
        setCustomerId(customerId);
    };

    const hide = () => {
        setVisible(false);
        form.resetFields();
        ctx.setEmailList([]);
        props.refresh();
    };

    const {runAsync:addBatchUserVerifyRun, loading:addBatchUserVerifyLoading} = useFetch(addBatchUserVerify, {manual: true});
    const {runAsync:addBatchUserRun, loading:addBatchUserLoading} = useFetch(addBatchUser, {manual: true});

    useEffect(()=>{
        form.setFieldValue("emailList",  ctx.emailList);
    },[ctx.emailList]);

    const save = () => {
        const emailList = ctx.emailList;
        form.validateFields()
            .then(values => {
                const emailLanguage = values.emailLanguage;
                addBatchUserVerifyRun({customerId},{ emailLanguage, emailList}).then(
                    (resp:any) => {
                        let data = resp.data
                        if (data !== undefined && data !== null && data.length > 0) {
                            user_prompt.current.show(customerId, emailLanguage, emailList, data);
                        } else {
                            addBatchUserRun({customerId},{ emailLanguage, emailList}).then(
                                (result:any) => {
                                    let data = result.data
                                    if (data !== undefined && data !== null && data.length > 0) {
                                        user_tip.current.show(data);
                                        hide();
                                    }
                                }
                            )
                        }
                    }
                )
            })
            .catch(error => {
            })

    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    const language = [
        {label:formatMessage({id:"common.email.language.zh"}), value:"zh"},
        {label:formatMessage({id:"common.email.language.en"}), value:"en"},
    ]

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 5: 5 },
        },
    }

    return (
        <React.Fragment>
            <Modal
                title={<FormattedMessage id={"common.add"} />}
                visible={visible}
                onCancel={hide}
                
                maskClosable={false}
                centered
                destroyOnClose
                className='custom-small-modal'
                okText={formatMessage({id: 'common.ok'})}
                okButtonProps={{loading: addBatchUserVerifyLoading || addBatchUserLoading}}
                onOk={save}
            >
                <Form form={form} {...formItemLayout}>
                    <Row gutter={16}>
                        <Col span={24}>
                            <Form.Item
                                className="mar-ver-5" name="emailLanguage"
                                label={formatMessage({ id: 'common.email.language' })}
                                rules={[{ required: true, message: formatMessage({ id: 'placeholder.select.common.email.language' })}]}
                                style={{marginBottom:24}}
                                // initialValue={"zh"}
                            >
                                {/* <Select 
                                    className="full-width" 
                                    options={language}
                                >
                                </Select> */}
                                <Radio.Group 
                                    className="full-width"
                                    disabled={props.disabled}
                                >
                                    <Space >
                                        <Col
                                            style={{ marginRight: 24 }}
                                        >
                                            <Radio value={"zh"}>
                                                {
                                                    intl.formatMessage({id: "common.email.language.zh"})
                                                }
                                            </Radio>
                                        </Col>
                                        <Col
                                            style={{ marginRight: 24 }}
                                        >
                                            <Radio value={"en"}>
                                                {
                                                    intl.formatMessage({id: "common.email.language.en"})
                                                }
                                            </Radio>
                                        </Col>
                                    </Space>

                                </Radio.Group>
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={16} style={{marginTop: 16}}>
                        <Col span={24}>
                            <Form.Item
                                className="mar-ver-5" 
                                name="emailList"
                                label={formatMessage({ id: 'common.email' })}
                                rules={[{ required: true }]}
                            >
                                {/* <Input allowClear /> */}
                                <EditReceivers form={form}/>
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
            <UserAddPrompt bind={user_prompt} refresh={hide}/>
            <UserAddTip bind={user_tip} refresh={hide}/>
        </React.Fragment>
    )
};