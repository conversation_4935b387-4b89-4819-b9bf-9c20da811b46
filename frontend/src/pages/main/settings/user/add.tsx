import React from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {Form, Input, message, Modal, Select, Radio, Space, Col} from "antd";
import {useFetch} from "../../../../hooks/request";
import {addUser, updateUser} from "../../../../api/user";
import {useSafeState} from "ahooks";
import {useGlobal} from "../../../../context/global";

export const UserAdd = (props :any) => {

    const intl = useIntl();
    const {formatMessage} = intl;
    const g = useGlobal()

    const [visible, setVisible] = useSafeState(false);
    const [customerId, setCustomerId] = useSafeState(null);
    const [id, setId] = useSafeState(null);

    const [form] = Form.useForm();

    const show = (customerId:any) => {
        setVisible(true);
        setCustomerId(customerId);
    };

    const hide = () => {
        setVisible(false);
        form.resetFields();
        setId(null);
    };

    const {runAsync:updateUserRun, loading:updateUserLoading} = useFetch(updateUser, {manual: true});
    const {runAsync:addUserRun, loading:addUserLoading} = useFetch(addUser, {manual: true});


    const save = () => {
        form.validateFields()
            .then(values => {
                const emailLanguage = form.getFieldValue("emailLanguage");
                const email = form.getFieldValue("email");
                if (id) {
                    updateUserRun({id},{ email, customerId}).then(
                        (result:any) => {
                            // message.success(result.msg).then(() => {});
                            message.success(formatMessage({id: 'common.authorization.success'})).then(() => {});
                            props.refresh();
                            hide();
                        }
                    )
                } else {
                    addUserRun({customerId},{ emailLanguage, email}).then(
                        (result:any) => {
                            // message.success(result.msg).then(() => {});
                            message.success(formatMessage({id: 'common.authorization.success'})).then(() => {});
                            props.refresh();
                            hide();
                        }
                    )
                }
            })
            .catch(error => {
            })

    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    const language = [
        {label:formatMessage({id:"common.email.language.zh"}), value:"zh"},
        {label:formatMessage({id:"common.email.language.en"}), value:"en"},
    ]

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 5: 8 },
        },

    }

    return (
        <React.Fragment>
            <Modal
                width={500}
                title={<FormattedMessage id={!id ? "common.add" : "common.edit"} />}
                visible={visible}
                onCancel={hide}
                
                maskClosable={false}
                centered
                destroyOnClose
                className='custom-small-modal'
                okText={formatMessage({id: 'common.ok'})}
                okButtonProps={{loading: addUserLoading || updateUserLoading}}
                onOk={save}
            >
                <Form form={form} {...formItemLayout}>
                    <Form.Item
                        className="mar-ver-5" name="emailLanguage"
                        label={formatMessage({ id: 'common.email.language' })}
                        rules={[{ required: true, message: formatMessage({ id: 'placeholder.select.common.email.language' })}]}
                        style={{marginBottom:24}}
                        // initialValue={"zh"}
                    >
                        {/* <Select 
                            className="full-width" 
                            options={language}
                        >
                        </Select> */}
                        <Radio.Group 
                            className="full-width"
                            disabled={props.disabled}
                        >
                            <Space >
                                <Col
                                    style={{ marginRight: 24 }}
                                >
                                    <Radio value={"zh"}>
                                        {
                                            intl.formatMessage({id: "common.email.language.zh"})
                                        }
                                    </Radio>
                                </Col>
                                <Col
                                    style={{ marginRight: 24 }}
                                >
                                    <Radio value={"en"}>
                                        {
                                            intl.formatMessage({id: "common.email.language.en"})
                                        }
                                    </Radio>
                                </Col>
                            </Space>

                        </Radio.Group>
                    </Form.Item>
                    <Form.Item
                        className="mar-ver-5" name="email"
                        label={formatMessage({ id: 'common.email' })}
                        rules={[{ required: true, type: "email" }]}
                    >
                        <Input allowClear />
                    </Form.Item>
                    {/*<Form.Item*/}
                    {/*    className="mar-ver-5" name="description"*/}
                    {/*    label={formatMessage({ id: 'common.description' })}*/}
                    {/*>*/}
                    {/*    <Input.TextArea allowClear autoSize={{ minRows: 3, maxRows: 10 }} />*/}
                    {/*</Form.Item>*/}
                </Form>
            </Modal>
        </React.Fragment>
    )
};