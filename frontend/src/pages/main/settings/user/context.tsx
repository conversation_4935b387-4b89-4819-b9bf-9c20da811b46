import React, {ReactNode} from "react";
import {useSafeState} from "ahooks";

export const UserContext = React.createContext<{
        keyword: string;
        setKeyword: (data: string) => void;
        emailList: any;
        setEmailList: (emailList: any) => void;
    }
    |
    null>(null);

export const  UserProvider = ({children}: { children: ReactNode }) => {

    const [keyword, setKeyword] = useSafeState<any>("");
    const [emailList, setEmailList] = useSafeState<any>([]);


    return (
        < UserContext.Provider
            value={
                {
                    keyword,setKeyword,
                    emailList,setEmailList,
                }
            }
        >
            {children}
        </ UserContext.Provider>
    )
};

export const useUser = () => {
    const context = React.useContext( UserContext);
    if (!context) {
        throw new Error("useNotice must be used in NoticeContextProvider");
    }
    return context;
};

