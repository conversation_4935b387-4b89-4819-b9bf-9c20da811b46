import React from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {<PERSON><PERSON>, Col, Drawer, Form, Input, message, Row} from "antd";
import {useSafeState} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {addStorehouses, updateStorehouses} from "../../../../api/storehouses";
import _ from "lodash";

export const StorehouseAdd = (props:any) => {

    const intl = useIntl();
    const {formatMessage} = intl;

    const [visible, setVisible] = useSafeState<boolean>(false);
    const [id, setId] = useSafeState<any>(null);
    const [send, setSend] = useSafeState<any>(true);
    const [oldData, setOldData] = useSafeState(null);

    const {runAsync: addStorehousesRun, loading:addStorehousesLoading} = useFetch(addStorehouses, {manual: true})
    const {runAsync: updateStorehousesRun, loading:updateStorehousesLoading} = useFetch(updateStorehouses, {manual: true})


    const [form] = Form.useForm();

    const show =(data:any) => {
        setVisible(true);
        if (data) {
            setId(data.id);
            setOldData(data);
            form.setFieldsValue({...data});
        }
    };


    const formChange = () => {
        if (id) {
            const a = _.cloneDeep(oldData);
            // console.log("1===" + JSON.stringify(a)); 
            let b = _.cloneDeep(form.getFieldsValue());
            // console.log("2===" + JSON.stringify(b)); 
            if (!compareObjects(a, b)) {
                setSend(false);
            } else {
                setSend(true);
            }

        }
    };

    //比较两个JavaScript对象是否相同
    function compareObjects(obj1: any, obj2: any) {
        for (let key in obj1) {
            if (obj2.hasOwnProperty(key) && obj2[key] !== undefined) {
                if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
                    if (!arraysAreEqual(obj1[key], obj2[key])) {
                        return false;
                    }
                } else if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
                    if (!compareObjects(obj1[key], obj2[key])) {
                        return false;
                    }
                } else {
                    if (obj1[key] !== obj2[key]) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    //比较两个数组是否相同
    function arraysAreEqual(arr1: any, arr2: any) {
        // 检查数组长度是否相同
        if (arr1.length !== arr2.length) {
            return false;
        }

        const a = _.cloneDeep(arr1);
        const b = _.cloneDeep(arr2);
        // 将数组转换为字符串并比较
        const str1 = JSON.stringify(a.sort());
        const str2 = JSON.stringify(b.sort());

        return str1 === str2;
    }


    const hide = () => {
        setVisible(false);
        form.resetFields();
        setId(null);
        setSend(true);
        setOldData(null);
    };

    const save = () => {
        form.validateFields()
            .then(values => {
                if (id) {
                    updateStorehousesRun({id},values).then(
                        (resp:any) => {
                            message.success(resp.msg)
                            props.refresh();
                            hide();
                        }
                    )
                } else {
                    addStorehousesRun(values).then(
                        (resp:any) => {
                            message.success(resp.msg)
                            props.refresh();
                            hide();
                        }
                    )
                }
            })
            .catch(() => { })

    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    return (
        <React.Fragment>
            <Drawer
                width={500}
                title={<FormattedMessage id={!id ? "common.add" : "common.edit"} />}
                visible={visible}
                onClose={hide}
                
                maskClosable={false}
                footer={
                    <Row justify="end">
                        <Col>
                            <Button onClick={save} disabled={id?send:false} type="primary" loading={addStorehousesLoading || updateStorehousesLoading}>
                                <FormattedMessage id="common.save" />
                            </Button>
                        </Col>
                    </Row>
                }
            >
                <Form form={form} onValuesChange={formChange} layout="vertical">
                    <Form.Item
                        className="mar-ver-5"
                        label={formatMessage({ id: 'common.number' })}
                        name="number" rules={[{ required: true }]}
                    >
                        <Input allowClear />
                    </Form.Item>
                    <Form.Item
                        className="mar-ver-5"
                        label={formatMessage({ id: 'common.name' })}
                        name="name" rules={[{ required: true }]}
                    >
                        <Input allowClear />
                    </Form.Item>
                </Form>
            </Drawer>
        </React.Fragment>
    )
};