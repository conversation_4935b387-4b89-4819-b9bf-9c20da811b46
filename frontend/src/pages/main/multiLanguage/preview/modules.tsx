import React from "react"
import {Home as ProjectHome} from "../../../project/home"
import {Index as Subject} from "../../../project/sub/subject"
import {VisitCycle as ProjectVisitCycle} from "../../../project/sub/visit_cycle"
import {Shipment} from "../../../project/supply/shipment";
import {Storehouse} from "../../../project/supply/storehouse_statistics";
import {SupplySite} from "../../../project/supply/site_pharmacy";
import {DrugFreeze} from "../../../project/supply/drug_freeze";
import {DrugRecovery} from "../../../project/supply/drug_recovery";
import {Index as Plan} from "../../../project/build/plan";
import {Index as Drug} from "../../../project/build/drug";
import {StorehouseManage} from "../../../project/build/storehouse";
import {Index as Randomization} from "../../../project/build/randomization";
import {Index as Attribute} from "../../../project/build/attribute";
import {ProjectSite} from "../../../project/build/site";
import {HistoryList} from "../../../project/build/history_info";
import {PushList} from "../../../project/build/push";
import {SimulateRandom} from "../../../project/build/simulate_random";
import {Index as Notice} from "../../../project/settings/notice";
import {Index as ProjectUser} from "../../../project/settings/user";

const modules: any[] = [
    {
        key: 'home',
        component: <ProjectHome />
    },
    {
        key: "subject",
        children: [
            {
                key: "subject_subjects",
                component: <Subject />
            },
            {
                key: "subject_visit",
                component: <ProjectVisitCycle />
            }
        ]
    },
    {
        key: "supply",
        children: [
            {
                key: "supply_depot",
                component: <Storehouse />
            },
            {
                key: "supply_site",
                component: <SupplySite />
            },
            {
                key: "supply_shipment",
                component: <Shipment />
            },
            {
                key: "supply_shipment_return",
                component: <DrugRecovery />
            },
            {
                key: "supply_freeze",
                component: <DrugFreeze />
            },
        ]
    },
    {
        key: "build",
        children: [
            {
                key: "build_storehouse",
                component: <StorehouseManage />
            },
            {
                key: "build_site",
                component: <ProjectSite />
            },
            {
                key: "build_attribute",
                component: <Attribute />
            },
            {
                key: "build_push_statistics",
                component: <PushList />
            },
            {
                key: "build_random_simulate",
                component: <SimulateRandom />
            },
            {
                key: "build_randomization",
                component: <Randomization />,
                children: [
                    {
                        key: "build_randomization_design",
                        component: <Randomization />
                    },
                    {
                        key: "build_randomization_form",
                        component: <Randomization />
                    }
                ]
            },
            {
                key: "build_treatment",
                component: <Drug />,
                children: [
                    {
                        key: "build_treatment_visit_management",
                        component: <Drug />
                    },
                    {
                        key: "build_treatment_design",
                        component: <Drug />
                    },
                    {
                        key: "build_treatment_barcode",
                        component: <Drug />
                    },
                    {
                        key: "build_treatment_ip_list",
                        component:  <Drug />
                    },
                    {
                        key: "build_treatment_ip_no_number",
                        component: <Drug />
                    },
                    {
                        key: "build_treatment_drug_batch",
                        component: <Drug />
                    },
                ]
            },
            {
                key: "build_supply_plan",
                component: <Plan />
            },
            {
                key: "build_history",
                component: <HistoryList />
            },
        ]
    },
    {
        key: "setting",
        children: [
            {
                key: "setting_notice",
                component: <Notice />
            },
            {
                key: "setting_user",
                component: <ProjectUser />
            },
        ]
    },
]

const getPreviewModules = (modules: any[], parentKeys: string[]) => {
    return modules.reduce((res, item) => {
        const path = [...parentKeys, item.key]
        if (!!item.component) res.push({path: path.join('/'), component: item.component})
        res = res.concat(getPreviewModules(item.children || [], path))
        return res
    }, [])
}

const allowPreviewModules = (getPreviewModules(modules, ['project_detail']))
const allowPreviewPath = allowPreviewModules.map((it: { path: string }) => it.path)

export const allowPreview = (pagePath: string[]): boolean => {
    if (pagePath.length === 0) return false
    const path = pagePath.join('/')
    return !!allowPreviewPath.find((it: string) => it === path || path.includes(it))
}

export const previewComponent = (pagePath: string[]): null | React.Component => {
    if (pagePath.length === 0) return null
    const path = pagePath.join('/')
    return allowPreviewModules.find((it: { path: string }) => it.path === path || path.includes(it.path))?.component
}
