import {useSafeState} from "ahooks";
import React, {useImperativeHandle, useMemo} from "react";
import {Drawer} from "antd";
import {useAuth} from "../../../../context/auth";
import {mockProject} from "./mock";
import {useAtom} from "jotai";
import {localesPreviewAtom} from "./context";
import {ProjectContextProvider} from "../../../project/context";
import {getLocales} from "../../../common/multilingual/util";
import {IntlProvider} from "react-intl";
import {customLocalesAtom} from "../../../common/multilingual/context";
import {previewModule} from "./modules"
import {getDefaultLocale} from "../../../../data/data";

export const LocalesPreview = (props: any) => {

    const [_, setPreviewMode] = useAtom(localesPreviewAtom)
    const [previewMessages] = useAtom(customLocales<PERSON>tom)

    const [visible, setVisible] = useSafeState<boolean>(false)
    const [modulePath, setModulePath] = useSafeState<string[]>([])

    useImperativeHandle(props.bind, () => ({ show }))

    const auth = useAuth()

    const show = (path: string[]) => {
        setPreviewMode(true)
        sessionStorage.setItem("project_preview", "true")
        auth.setProject(mockProject)
        auth.setEnv(mockProject.envs[0])
        auth.setIsRandomDispensing({random: true, dispensing: true})
        setModulePath(path)
        setVisible(true)
    }

    const hide = () => {
        setPreviewMode(false)
        sessionStorage.removeItem("project_preview")
        auth.setEnv(null)
        auth.setProject({})
        setVisible(false)
    }

    const PreviewComponent = useMemo(() => {
        const module = previewModule(modulePath)
        const Component = module?.component
        return Component ? <Component {...(module.props || {})} /> : <>无预览页面</>
    }, [modulePath])

    const language = useMemo(() => {
        return getDefaultLocale(props.languageCode)
    }, [props.languageCode])

    return <>
        <Drawer
            open={visible}
            onClose={hide}
            title={"预览"}
            closable
            width={'90%'}
            maskClosable={false}
            zIndex={2}
            push={false}
        >
            <IntlProvider messages={getLocales(previewMessages, language)} locale={language} defaultLocale="en">
                {visible && <div>
                    <ProjectContextProvider>
                        {PreviewComponent}
                    </ProjectContextProvider>
                </div>}
            </IntlProvider>


        </Drawer>
    </>
}