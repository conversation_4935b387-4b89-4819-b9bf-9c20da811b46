export const mockProject = {
    "administrators": [
        "622eb5f0825a5d11f3e126d5",
        "610b49c1e3ea63e00e3bdb3c",
        "6090e4318d9d12afad9d4ebd"
    ],
    "create_time": [],
    "customerId": "6090e4318d9d12afad9d4ebc",
    "customerName": "cjc",
    "envs": [
        {
            "cohorts": [],
            "id": "64dc2d65de9209a4e22d3ceb",
            "lockconfig": false,
            "name": "DEV",
            "status": 2
        },
    ],
    "id": "64dc2d36de9209a4e22d3cb9",
    "info": {
        "ali_project_id": "",
        "bound": 0,
        "connect_ali": 0,
        "connect_edc": 2,
        "connect_learning": 1,
        "de_isolation_approval": 1,
        "description": "",
        "edc_supplier": 1,
        "edc_url": "",
        "end_time": 0,
        "ip_unblinding_process": 1,
        "ip_unblinding_sms": 0,
        "ip_unblinding_type": 1,
        "name": "公式计算1",
        "need_learning": 2,
        "need_learning_env": [
            "PROD"
        ],
        "need_system_courses": 0,
        "number": "0816",
        "order_approval_control": 1,
        "order_approval_process": 1,
        "order_approval_sms": 0,
        "order_check": 1,
        "order_check_day": [
            1,
            2,
            3,
            4,
            5,
            6,
            7
        ],
        "order_check_time": "08:00",
        "order_confirmation": 1,
        "phone": "",
        "planned_cases": null,
        "push_mode": 1,
        "push_rules": 0,
        "push_scenario": {
            "cohort_random_block_push": false,
            "dispensing_push": false,
            "form_random_block_push": false,
            "random_block_push": false,
            "random_push": false,
            "register_push": false,
            "screen_push": false,
            "update_random_after_push": false,
            "update_random_front_push": false
        },
        "pv_unblinding_process": 1,
        "pv_unblinding_sms": 0,
        "pv_unblinding_type": 1,
        "research_attribute": 0,
        "room": true,
        "sponsor": "xxx",
        "start_time": 0,
        "status_reason": "",
        "synchronization_mode": 1,
        "system_courses": 1,
        "timeZone": 8,
        "timeZoneStr": "8",
        "type": 1,
        "unblinding_code": 0,
        "unblinding_control": 1,
        "unblinding_process": 1,
        "unblinding_sms": 0,
        "unblinding_type": 1,
        "visit_Randomization": {
            "randomizations": null,
            "visits": null
        }
    },
    "meta": {
        "created_at": 1692151094,
        "created_by": "000000000000000000000000",
        "updated_at": 0,
        "updated_by": "000000000000000000000000"
    },
    "sign": "0",
    "status": 0,
    "permissions": {
        "_id": "64dc2d36de9209a4e22d3cbf",
        "role_id": "64dc2d36de9209a4e22d3cbf",
        "role": "IP Officer",
        "customer_id": "000000000000000000000000",
        "project_id": "000000000000000000000000",
        "scope": "study",
        "template": 1,
        "status": 1,
        "permissions": [],
        "page": [],
        "cohortDisablePermission": []
    }
}