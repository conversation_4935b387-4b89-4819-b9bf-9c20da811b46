import { Button, Dropdown, Input, message, Tooltip } from "antd";
import React, { useEffect, useRef } from "react";
import { useSafeState } from "ahooks";
import { useIntl } from "react-intl";
import styled from "@emotion/styled";
import { DownOutlined } from "@ant-design/icons";
import ExcelJS from "exceljs";
import { saveAs } from "file-saver";
import { extractPlaceholders } from "../../../common/multilingual/translate-utils";
import { TranslateModal } from "../../../common/multilingual/translate-modal";

export const TranslateEdit = (props: any) => {
    const [value, setValue] = useSafeState<string>("");
    const [isTemplate, setIsTemplate] = useSafeState<boolean>(false)
    const { formatMessage } = useIntl();
    const translateRef = useRef<any>()

    useEffect(() => {
        setValue(props.value);
        setIsTemplate(extractPlaceholders(props.originalValue).length > 0)
    }, [props.value, props.originalValue]);

    const onSave = () => {
        props.onSave && props.onSave(value);
    };

    const onActive = (e: any) => {
        e.stopPropagation()
        props.onActive && props.onActive()
        if (isTemplate) {
            translateRef?.current?.show(props.filedKey)
        }
    }

    const isEdit = () => {
        return props.active && !isTemplate
    }

    return (
        <div style={{width: '100%', display: 'flex', alignItems: 'center'}}>
            {!isEdit() ? <span style={{ display: 'inline-block', maxWidth: '100%', overflow: 'hidden', textOverflow: 'ellipsis'}}>{value}</span> : (<Input.Group compact>
                <Input
                    placeholder={formatMessage({ id: "placeholder.input.common" })}
                    value={value}
                    onChange={(e) => setValue(e.target.value)}
                    suffix={
                        <Button size="small" type="primary" onClick={onSave} loading={props.loading}>
                            {formatMessage({ id: "common.save" })}
                        </Button>
                    }
                />
            </Input.Group>)}
            {props.allowEdit && !isEdit() && (
                <Tooltip title={formatMessage({ id: "common.edit" })}>
                    <StyleSpan><i className="iconfont icon-bianji" onClick={onActive} /></StyleSpan>
                </Tooltip>
            )}
            {isTemplate && <TranslateModal bind={translateRef} centered={true} height={300} />}
        </div>
    );
};

export const TranslateTemplateDownload = (props: any) => {

    const { formatMessage } = useIntl();

    const downloadExcel = async (data: any) => {
        try {
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet();

            // 添加表头（第一行）
            worksheet.addRow([
                formatMessage({ id: "common.type" }), "key",
                formatMessage({ id: "common.name" }), props.languageName,
            ]);

            // 添加数据行
            data.forEach((item: any) => {
                const typeStr = props.typeOptionList.find((it: any) => it.value === item.type)?.label || "";
                const translation = props.translateMessages[item.key] || "";
                worksheet.addRow([typeStr, item.key, item.label, translation,]);
            });

            // 锁定需要保护的区域（第一行和前三列）
            worksheet.eachRow((row, rowNumber) => {
                row.eachCell((cell, colNumber) => {
                    if (rowNumber === 1 || colNumber <= 3) {
                        // 保护标题行和前三列
                        cell.protection = { locked: true };
                    } else {
                        cell.protection = { locked: false }; // 第四列可编辑
                    }
                });
            });

            // 启用工作表保护（随机密码）
            worksheet.protect(Math.random().toString(36).slice(2, 10), {
                selectLockedCells: true, // 允许选中锁定单元格
                formatColumns: true, // 允许调整列宽
                formatCells: false, // 禁止格式化
                insertRows: false, // 禁止插入行
                insertColumns: false, // 禁止插入列
                deleteColumns: false, // 禁止删除列
            });

            // 生成 Excel 文件
            const buffer = await workbook.xlsx.writeBuffer();
            const blob = new Blob([buffer], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            });
            const timeString = formatDateTime(props.timeZone); // 默认使用本机时区
            const fileName = `${props.projectNumber}_TranslationTemplate_${timeString}.xlsx`;

            saveAs(blob, fileName);
            props.onComplete && props.onComplete()
            message.success(formatMessage({ id: "common.export.success" }));
        } catch (error: any) {
            message.error("生成 Excel 失败: " + error.message);
        }
    };

    const downloadSelected = () => {
        if (props.selectedData.length === 0) {
            message.warn(formatMessage({ id: "common.select.list.tips" }));
            return;
        }
        downloadExcel(props.selectedData);
    }

    const downloadAll = () => {
        downloadExcel(props.allData);
    }

    const items = [
        {
            key: '1',
            label: formatMessage({ id: "multiLanguage.translation.downloadTemplate.downloadCheckbox" }),
            onClick: downloadSelected
        },
        {
            key: '2',
            label: formatMessage({ id: "multiLanguage.translation.downloadTemplate.downloadAll" }),
            onClick: downloadAll
        }
    ]

    return (
        <Dropdown menu={{ items: items }}>
            <Button>
                {formatMessage({ id: "common.download.template" })} <DownOutlined />
            </Button>
        </Dropdown>
    );
}

const formatDateTime = (timezone: string) => {
    return new Date()
        .toLocaleString("en-CA", {
            timeZone: timezone,
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
            second: "2-digit",
            hour12: false,
        })
        .replace(/[^\d]/g, ""); // 移除非数字字符
};

const StyleSpan = styled.span`
  margin-left: 8px;
  .iconfont {
    cursor: pointer;
    color: #999999;
  }
  .iconfont:hover {
    color: #165DFF;
  }
`
