import { useSafeState } from "ahooks";
import { LocalesPreview } from "../preview";
import React, { useEffect, useImperativeHandle, useRef } from "react";
import { Button, Col, message, Row, Spin, Table } from "antd";
import { allowPreview } from "../preview/modules";
import { useIntl } from "react-intl";
import { useFetch } from "../../../../hooks/request";
import { getTranslateMap, updateTranslate } from "../../../../api/multi_language";
import { useAtom } from "jotai";
import { customLanguageAtom, customLocalesAtom } from "../../../common/multilingual/context";
import { getLocalesData, languageCountryCode } from "../../../common/multilingual/util";
import _ from "lodash";
import { TranslateEdit, TranslateTemplateDownload } from "./component";
import { LanguageLibraryOption } from "./data";
import { PaginationView } from "../../../../components/pagination";
import { usePage } from "../../../../context/page";
import { useGlobal } from "../../../../context/global";
import { VirtualTable } from "../../../../components/virtual-tabel";

export const SystemTable = (props: any) => {

    const pageCtx = usePage()
    const g = useGlobal()
    const {formatMessage} = useIntl()
    // 翻译字段
    const [data, setData] = useSafeState<any[]>([])
    const [showData, setShowData] = useSafeState<any[]>([])
    const [translateMessages, setTranslateMessages] = useAtom(customLocalesAtom);
    const [languageInfo] = useAtom(customLanguageAtom)
    // 选中的字段
    const [selectedRowKeys, setSelectedRowKeys] = useSafeState<any[]>([]);
    // 系统库字段选项
    const [typeOptionList, setTypeOptionList] = useSafeState<any[]>([]);
    const [flatPath, setFlatPath] = useSafeState<any[]>([])
    const [flatType, setFlatType] = useSafeState<any[]>([])
    const [activeKey, setActiveKey] = useSafeState<string>('')
    // 组件引用
    const previewRef = useRef<any>();

    useImperativeHandle(props.bind, () => ({ refresh: getAllTranslate}))


    // 更新翻译
    const { runAsync: updateTranslateLoadingRun, loading: updateTranslateLoading } = useFetch(updateTranslate, {
        manual: true,
    });
    // 获得翻译
    const { runAsync: getTranslate, loading: getTranslateListLoading } = useFetch(getTranslateMap, { manual: true });

    const isDefaultLanguage = () => ["zh", "en"].includes(languageInfo.languageCode)

    const previewClick = () => {
        if (!allowPreview(props.pagePath)) return;
        previewRef?.current?.show(props.pagePath);
    };

    const getAllTranslate = () => {
        const params = { customerId: languageInfo.customerId, projectId: languageInfo.projectId }
        getTranslate(params, {languageId: languageInfo.id}).then((resp: any) => {
            setTranslateMessages(resp.data)
        })
    }

    useEffect(() => {
        if (!languageInfo.id) return
        const {data, types, flatModules, flatTypes} = getLocalesData(g.lang)
        setActiveKey('')
        setData(data)
        setTypeOptionList(types)
        setFlatPath(flatModules)
        setFlatType(flatTypes)
        // 获得全部翻译
        getAllTranslate()
    }, [languageInfo])

    const getRecordKey = (record: any) => {
        return record.key + record.path.toString()
    }

    // 获取选中的行数据
    const getSelectedRowsData = () => {
        return _.uniqBy(data.filter(record => selectedRowKeys.includes(getRecordKey(record))), 'key')
    }

    const rowSelection: any = {
        selectedRowKeys,
        type: "checkbox",
        onChange: (newSelectedRowKeys: any[]) => setSelectedRowKeys(newSelectedRowKeys),
        preserveSelectedRowKeys: true,
    };

    //单个翻译编辑
    const onTranslate = (record: any, value: string)=>{
        const languageLibrary = LanguageLibraryOption.find(it => it.value === 1)!!
        const path = flatPath.filter(p => record.path.includes(p.value))
        const type = flatType.find(it => it.value === record.type)
        updateTranslateLoadingRun({ customerId:  languageInfo.customerId, projectId: languageInfo.projectId },
            {
                id: record.id,
                customerId: languageInfo.customerId,
                projectId: languageInfo.projectId,
                envId: props.envId,
                cohortId: props.cohortId,
                languageId: languageInfo.id,
                languageLibrary: 1,
                pagePath: path,
                type: record.type,
                key: record.key,
                name: value,
                languageValue: languageInfo.languageName,
                languageLibraryValueZh: languageLibrary.labelCn,
                languageLibraryValueEn: languageLibrary.labelEn,
                pagePathValueZh: path.map(it => it.label).join(' / '),
                pagePathValueEn: path.map(it => it.labelEn).join(' / '),
                typeValueZh: type.label,
                typeValueEn: type.labelEn,
                nameValueZh: record.defaultLabel.cn,
                nameValueEn: record.defaultLabel.en,
            }
        ).then(() => {
            setTranslateMessages({...translateMessages, [record.key]: value})
            message.success(formatMessage({ id: 'message.save.success' }))
        });
    }

    const renderLanguage = (record: any) => {
        if (isDefaultLanguage()) return record.defaultLabel[languageCountryCode[languageInfo.languageCode].languageCode];
        return  <TranslateEdit
            value={translateMessages[record.key]}
            originalValue={record.label}
            onSave={(value: string) => onTranslate(record, value)}
            loading={updateTranslateLoading}
            active={activeKey === record.key}
            onActive={() => setActiveKey(record.key)}
            filedKey={record.key}
            allowEdit={props.permissionList.includes("operation.projects.project.multiLanguage.details.edit")}
        />
    }


    useEffect(() => {
        const { name, translationStatus, type } = props.search
        const showData = data
            .filter((it) => !name || it.label?.includes(name))
            .filter((it) => !props.pagePath || props.pagePath.length === 0 ||
                it.path.slice(0, props.pagePath.length).toString() === props.pagePath.toString(),
            ).filter((it) => (translationStatus ?? "") === "" || !!translateMessages[it.key] === translationStatus)
            .filter((it) => !type || it.type === type)
        updateShowData(_.uniqBy(showData, "key"))
    }, [props.search, data, pageCtx.pageSize, pageCtx.currentPage])

    const updateShowData = (data: any[]) => {
        const start = (pageCtx.currentPage - 1) * pageCtx.pageSize;
        const end = pageCtx.currentPage * pageCtx.pageSize;
        const showData = data.slice(start, end)
        setShowData(showData)
        pageCtx.setTotal(data.length)
    }



    return <>
        {!isDefaultLanguage() && (
            <Row justify="end" style={{ marginTop: 20, marginBottom: 20 }}>
                <Col className="mar-rgt-12">
                    {props.permissionList.includes("operation.projects.project.multiLanguage.details.preview") ? (
                        <Button
                            onClick={() => previewClick()}
                            style={{ marginLeft: 8 }}
                            disabled={!allowPreview(props.pagePath)}
                        >
                            {formatMessage({ id: "form.preview" })}
                        </Button>
                    ) : null}
                    {props.permissionList.includes(
                        "operation.projects.project.multiLanguage.details.downloadTemplate",
                    ) ? (
                        <span style={{ marginLeft: 8 }}>
                            <TranslateTemplateDownload
                                projectNumber={languageInfo.projectNumber}
                                selectedData={getSelectedRowsData()}
                                allData={data}
                                timeZone={props.timeZone}
                                typeOptionList={typeOptionList}
                                translateMessages={translateMessages}
                                languageName={languageInfo.languageName}
                                onComplete={() => setSelectedRowKeys([])}
                            />
                        </span>
                    ) : null}
                </Col>
            </Row>
        )}

        <Spin spinning={getTranslateListLoading}>
            <VirtualTable
                className="mar-top-10"
                size="small"
                pagination={false}
                rowKey={(record) => getRecordKey(record)}
                dataSource={showData}
                rowSelection={!isDefaultLanguage() ? rowSelection : null}
                height={280}
            >
                <Table.Column
                    title={formatMessage({ id: "common.serial" })}
                    dataIndex="#"
                    key="#"
                    width={70}
                    render={(text, record, index) => (pageCtx.currentPage - 1) * pageCtx.pageSize + index + 1}
                />
                <Table.Column
                    width={200}
                    title={formatMessage({ id: "common.type" })}
                    dataIndex={"type"}
                    key="type"
                    ellipsis
                    render={(text) => typeOptionList.find((it) => it.value === text)?.label}
                />
                <Table.Column width={200} title={"key"} dataIndex={"key"} key="key" ellipsis />
                <Table.Column
                    width={200}
                    title={formatMessage({ id: "common.name" })}
                    dataIndex={"label"}
                    key="label"
                    ellipsis
                />
                <Table.Column
                    width={150}
                    title={languageInfo.languageName}
                    dataIndex="compare"
                    key="compare"
                    ellipsis
                    render={(value: any, record: any) => renderLanguage(record)}
                />
            </VirtualTable>
            <PaginationView
                mode={"SELECTABLE"}
                selectedNumber={selectedRowKeys.length}
                clearDisplay={true}
                refresh={() => setSelectedRowKeys([])}
            />
        </Spin>

        <LocalesPreview bind={previewRef} languageCode={languageInfo.languageCode} />
    </>

}