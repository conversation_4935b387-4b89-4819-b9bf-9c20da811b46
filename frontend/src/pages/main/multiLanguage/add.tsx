import React, { useEffect } from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {Form, Input, message, Modal, Select, Radio, Space, Col, Row, Table, Switch, notification, Button, } from "antd";
import {useSafeState} from "ahooks";
import {useGlobal} from "../../../context/global";
import {useMultiLanguage} from "./context";
import {PlusOutlined, CloseCircleFilled, } from "@ant-design/icons";
import styled from "@emotion/styled";
import { useFetch } from "hooks/request";
import {addLanguage} from "../../../api/multi_language";
import { json } from "stream/consumers";

export const Add = (props :any) => {

    const intl = useIntl();
    const {formatMessage} = intl;
    const g = useGlobal();
    const ctx = useMultiLanguage();

    const [visible, setVisible] = useSafeState<any>(false);

    const [customerId, setCustomerId] = useSafeState<any>(null);
    const [projectId, setProjectId] = useSafeState<any>(null);

    const [languageTotalOptions, setLanguageTotalOptions] = useSafeState<any>([]);
    const [languageOptions, setLanguageOptions] = useSafeState<any>([]);
    const [selectOpen, setSelectOpen] = useSafeState<any>(false);

    const [form] = Form.useForm();

    const { runAsync: addLanguageRun, loading: addLanguageLoading } = useFetch(addLanguage,{ manual: true });

    const show = (customerId: any, projectId: any, initOptions: any, languageNameList: any) => {
        setVisible(true);
        // 使用 filter 和 includes 去掉 arrayA 中存在于 arrayB 的元素
        const differenceArray = initOptions.filter((item: any) => !languageNameList.includes(item.name));
        let arr = differenceArray.map((item: any)=> {
            return {label:item.name,value:item.code};
        });
        setLanguageTotalOptions(initOptions);
        setLanguageOptions(arr);
        setCustomerId(customerId);
        setProjectId(projectId);
    };

    const hide = () => {
        setVisible(false);
        setCustomerId(null);
        setProjectId(null);
        form.resetFields();
        props.refresh();
    };
    useEffect(()=>{

    },[]);

    const save = () => {
        form.validateFields()
            .then(values => {
                let name = languageTotalOptions?.find((item: any) => item.code === values.code).name;
                addLanguageRun({ customerId:  customerId, projectId: projectId },
                    {
                        customerId: customerId,
                        projectId: projectId,
                        code: values.code,
                        language: name,
                        status: 1,
                        sharedSystemLibrary: values.sharedSystemLibrary,

                    }).then(
                        (resp:any) => {
                            message.success(resp.msg)
                            props.refresh();
                            hide();
                        }
                    )
            }).catch(error => {
            })
    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 5: 7 },
        },

    }

    const CustomEnvAdder = (ceapr: any) => {
        const [form] = Form.useForm()
        const onAdd = () => {
            form.validateFields().then((values) => {
                const v = values.env
                setLanguageOptions(languageOptions.concat({label: v, value: v}))
                ceapr.form.setFieldsValue({env: v})
                ceapr.setOpen(false)
            })
        }

        const validator = {
            validator: (_: any, value: any) => {
                if (value
                    && value.trim().length > 0
                    && languageOptions.findIndex((item: any) => item.value === value) === -1
                ) {
                    return Promise.resolve()
                } else if (!value || value.trim().length === 0) {
                    return Promise.reject(formatMessage({id: 'common.required.prefix'}) + formatMessage({id: 'common.language'}))
                } else {
                    return Promise.reject(formatMessage({id: 'multiLanguage.duplicate.name'}))
                }
            }
        }

        return (
            <Form style={{margin: '0 10px'}} form={form}>
                <Form.Item name='env' rules={[validator]}>
                    <Input
                        placeholder={formatMessage({id: 'placeholder.input.common'})}
                        addonAfter={
                            <Button type='link' size='small' onClick={onAdd}>
                                <PlusOutlined /> <FormattedMessage id={'common.addTo'} />
                            </Button>
                        }
                        onBlur={e => {
                            if (!e.target.value || e.target.value.trim().length === 0) {
                                form.setFields([{name: 'env'}])
                            }
                        }}
                    />
                </Form.Item>
            </Form>
        )
    };

    const onChangeSelect = (visible: any) => {
        setSelectOpen(visible);
    };

    return (
        <React.Fragment>
            <Modal
                title={<FormattedMessage id={"common.addTo"} />}
                visible={visible}
                onCancel={hide}
                
                maskClosable={false}
                centered
                destroyOnClose
                className='custom-small-modal'
                okText={formatMessage({id: 'common.ok'})}
                okButtonProps={{loading: addLanguageLoading}}
                onOk={save}
            >
                <Form form={form} {...formItemLayout}>
                    <Form.Item
                        label={formatMessage({id: 'common.language'})}
                        name='code'
                        rules={[{required: true}]}
                    >
                        <Select
                            className="full-width"
                            open={selectOpen}
                            onDropdownVisibleChange={onChangeSelect}
                            allowClear
                            placeholder={formatMessage({id: 'placeholder.select.common'})}
                            // dropdownRender={menu => (
                            //     <>
                            //         {languageOptions && languageOptions.length > 0 ? menu : null}
                            //         <SelectInput><CustomEnvAdder form={form} setOpen={setSelectOpen} /></SelectInput>
                            //     </>
                            // )}
                        >
                            {
                                languageOptions.map((it: any) =>
                                    <Select.Option
                                        key={it.value}
                                        value={it.value}
                                    >
                                        {it.label}
                                    </Select.Option>
                                )
                            }
                        </Select>
                    </Form.Item>
                    <Form.Item
                        className="mar-ver-5" name="sharedSystemLibrary"
                        label={formatMessage({ id: 'multiLanguage.shared.system.library' })}
                        style={{marginBottom:24}}
                    >
                        <Radio.Group 
                            className="full-width"
                        >
                            <Space >
                                <Col
                                    style={{ marginRight: 24 }}
                                >
                                    <Radio value={1}>
                                        {
                                            intl.formatMessage({id: "common.yes"})
                                        }
                                    </Radio>
                                </Col>
                                <Col
                                    style={{ marginRight: 24 }}
                                >
                                    <Radio value={0}>
                                        {
                                            intl.formatMessage({id: "common.no"})
                                        }
                                    </Radio>
                                </Col>
                            </Space>

                        </Radio.Group>
                    </Form.Item>
                </Form>
            </Modal>
        </React.Fragment>
    )
};

const SelectInput = styled.div`
    margin: 6px 0 0 0;
    .ant-input-group-addon {
        background: unset !important;
        border: unset !important;
    }
`