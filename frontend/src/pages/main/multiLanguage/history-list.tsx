import React, { useEffect, useRef } from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {Form, Input, message, Modal, Select, Radio, Space, Col, Row, Table, Switch, notification, But<PERSON>, Spin, Pagination, } from "antd";
import {useSafeState} from "ahooks";
import {useGlobal} from "../../../context/global";
import styled from "@emotion/styled";
import { useFetch } from "../../../hooks/request";
import {getHistoryList} from "../../../api/multi_language";
import { usePage } from "../../../context/page";

export const HistoryList = (props :any) => {

    const page = usePage();
    const intl = useIntl();
    const {formatMessage} = intl;
    const g = useGlobal();

    const [visible, setVisible] = useSafeState<any>(false);
    const [currentPage, setCurrentPage] = useSafeState<any>(1);
    const [pageSize, setPageSize] = useSafeState<any>(10);
    const [total, setTotal] = useSafeState<any>(0);
    const [data, setData] = useSafeState<any>([]);

    const [form] = Form.useForm();
    const [projectId, setProjectId] = useSafeState<any>(null);

    const { runAsync, loading: getHistoryListLoading } = useFetch(getHistoryList, { manual: true })
        
    const show = (customerId: any, projectId: any, permissionList: any) => {
        setVisible(true);
        // if(permissionList.includes("operation.projects.project.multiLanguage.trail.print")){
        //     setPrint(true);
        // }
        setProjectId(projectId);
        getList(projectId, projectId, currentPage);
    };

    const getList = (id: any, projectId: any, page: any) => {
        runAsync({
            oid: id,
            projectId: projectId,
            start: (page - 1) * pageSize,
            limit: pageSize,
            module: "operation_log.module.project_multi_language,operation_log.module.project_multi_language_translate,operation_log.module.project_multi_language_batch_upload",
        }).then((result: any) => {
            let data = result.data
            setTotal(data.total);
            setData(data.items);
            // page.setData(fillTableCellEmptyPlaceholder(data.items));
        });

    }

    const hide = () => {
        setVisible(false);
        setCurrentPage(1);
        setPageSize(10);
        setTotal(0);
        setData([]);
        form.resetFields();
        props.refresh();
    };
    useEffect(()=>{
        if(projectId !== undefined && projectId !== null && projectId !== ""){
            getList(projectId, projectId, currentPage);
        }
    },[currentPage, pageSize]);

    React.useImperativeHandle(props.bind, () => ({ show }));

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 5: 5 },
        },

    }

    return (
        <React.Fragment>
            <Modal
                title={<FormattedMessage id={"common.history"} />}
                visible={visible}
                onCancel={hide}
                
                maskClosable={false}
                centered
                destroyOnClose
                className='custom-medium-modal-multiLanguage'
                okText={formatMessage({id: 'common.ok'})}
                onOk={hide}
            >
                <Spin spinning={getHistoryListLoading}>
                    <Table
                        loading={getHistoryListLoading}
                        className="mar-top-10"
                        dataSource={data}
                        // scroll={{ x: 'max-content', y: 'calc(100vh - 230px)' }}
                        pagination={false}
                        rowKey={(record: any) => (record.id)}
                    >
                        <Table.Column title={<FormattedMessage id="common.serial" />} dataIndex="#" key="#" width={100} ellipsis
                            render={(text, record, index) => ((currentPage - 1) * pageSize + index + 1)} />
                        <Table.Column 
                            title={<FormattedMessage id="common.operator" />} 
                            key="user" 
                            dataIndex="user"
                            width={300} 
                            align="left" 
                            ellipsis
                            render={
                                (value, record: any, index) => (
                                    record.user_name
                                )
                            } />
                        <Table.Column
                            title={<FormattedMessage id="common.operation.time" />}
                            key="time_str"
                            dataIndex="time_str"
                            align="left"
                            width={300}
                            ellipsis
                        />
                        <Table.Column 
                            title={<FormattedMessage id="common.operation.type" />} 
                            key="operation_type"
                            dataIndex="operation_type" 
                            align="left" 
                            ellipsis 
                            width={150}
                        />
                        <Table.Column 
                            title={<FormattedMessage id="common.operation.content" />} 
                            key="fields"
                            dataIndex="fields" align="left" ellipsis
                            render={
                                (value, record, index) => (
                                    value && value.map((item: any) =>
                                        <Row key={item}
                                             style={{ wordBreak: 'break-word' }}
                                        >
                                            {item}
                                        </Row>
                                    )
                                )
                            }
                        />
                    </Table>
                    {/* <PaginationView /> */}
                    {
                        total > 20 && (
                            <Pagination
                                hideOnSinglePage={false}
                                className="text-right"
                                current={currentPage}
                                pageSize={pageSize}
                                pageSizeOptions={["10", "20", "50", "100"]}
                                total={total}
                                showSizeChanger
                                showTotal={(total, range) =>
                                    `${range[0]} - ${range[1]} / ${total}`
                                }
                                onChange={(page, pageSize) => {
                                    setCurrentPage(page);
                                }}
                                onShowSizeChange={(current, size) => {
                                    setCurrentPage(1);
                                    setPageSize(size);
                                }}
                            />
                        )}
                </Spin>
            </Modal>
        </React.Fragment>
    )
};

const SelectInput = styled.div`
    margin: 6px 0 0 0;
    .ant-input-group-addon {
        background: unset !important;
        border: unset !important;
    }
`