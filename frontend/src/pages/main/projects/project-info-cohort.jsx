import React from 'react';
import {FormattedMessage, useIntl} from 'react-intl'
import {Button, Col, Form, Input, message, Modal, Row, Select, Space} from 'antd'
import {useSafeState} from 'ahooks';
import {useFetch} from '../../../hooks/request'
import {addCohort, updateCohort} from '../../../api/projects'
import {useGlobal} from '../../../context/global'
import {AlertThresholdType, nilObjectId} from "../../../data/data";
import {getSubjectRealityCapacity} from '../../../api/subject';
import {useAuth} from "../../../context/auth";
import {MinusCircleFilled, PlusOutlined} from "@ant-design/icons";
import {TipInputNumber} from 'components/TipInput';
import _ from "lodash";

const ProjectInfoCohort = (props) => {

    const auth = useAuth();
    const g = useGlobal()
    const {formatMessage} = useIntl()
    const customerId = auth.customerId;

    const [title, setTitle] = useSafeState('')
    const [isAdd, setIsAdd] = useSafeState(false)
    const [visible, setVisible] = useSafeState(false)
    const [projectId, setProjectId] = useSafeState(null)
    const [envId, setEnvId] = useSafeState(null)
    const [type, setType] = useSafeState(null)
    const [cohortType, setCohortType] = useSafeState(null)
    const [cohortStages, setCohortStages] = useSafeState(null)
    const [connectEdc, setConnectEdc] = useSafeState(null)
    const [pushMode, setPushMode] = useSafeState(0)
    const [cohorts, setCohorts] = useSafeState([])
    const [id, setId] = useSafeState(null)
    const [status, setStatus] = useSafeState(null)
    const [newStatus, setNewStatus] = useSafeState(null)
    const [oldCapacity, setOldCapacity] = useSafeState(-1);
    const [newCapacity, setNewCapacity] = useSafeState(-1);
    const [envName, setEnvName] = useSafeState(null)
    const [realityCapacity, setRealityCapacity] = useSafeState(0)
    const [unUsedOptions, setUnUsedOptions] = useSafeState(AlertThresholdType)
    const [optionCount,setOptionCount] = useSafeState(0)
    const [form] = Form.useForm()
    const {runAsync: runUpdateCohort} = useFetch(updateCohort, {manual: true})
    const {runAsync: runAddCohort} = useFetch(addCohort, {manual: true})

    const [send, setSend] = useSafeState(true);
    const [oldData, setOldData] = useSafeState(null);
    const [envLocked, setEnvLocked] = useSafeState(false);

    const { runAsync: getSubjectRealityCapacityRun, loading: getSubjectRealityCapacitLoading } = useFetch(getSubjectRealityCapacity, { manual: true });
    const show = (locked, pushMode, projectId, envId, envName, type, connectEdc, cs, data) => {
        setProjectId(projectId)
        setEnvId(envId)
        setType(type)
        setConnectEdc(connectEdc)
        setPushMode(pushMode)
        setEnvLocked(locked)
        if(envName === "PROD"){
            setEnvName(envName)
        }
        if (cs != null && cs.length > 0) {
            if (type === 3 || type === 2) {
                setCohorts(cs.map(it => ({label: it.name, value: it.id, reRandomName: it.reRandomName})))
                setCohortStages(cs.map(it => ({label: it.name, value: it.id, reRandomName: it.reRandomName})))
            }
        }

        setVisible(true)
        if (data) {
            if (data.alertThresholds != null  && data.alertThresholds.length > 0){
                let unUse = AlertThresholdType.filter((it)=>{
                    return !data.alertThresholds.find((i) => i.type === it.id)
                })
                setUnUsedOptions(unUse)
                setOptionCount(data.alertThresholds.length)
            }
            if (type === 2){
                setCohortStages((cs.filter((v)=>v.name === data.name && v.reRandomName !== "" && v.reRandomName !== data.reRandomName).map(v=>({label:v.reRandomName, value:v.id, reRandomName: v.reRandomName}))))
            }
            setOldCapacity(data.alertThresholds?.find((v)=>v.type === 3)?.capacity)
            setNewCapacity(data.alertThresholds?.find((v)=>v.type === 3)?.capacity)
            setId(data.id)
            setTitle(formatMessage({id: 'common.edit'}))
            setIsAdd(false)
            setCohortType(data.type)
            form.setFieldsValue({...data});
            setOldData({...data});
            if (data.lastId === nilObjectId) {
                form.setFieldsValue({
                    lastId: null,
                    alertThresholds:data.alertThresholds
                })
            }
            setStatus(data.status)
            setNewStatus(data.status)

            getSubjectRealityCapacityRun({
                customerId,
                envId,
                projectId,
                cohortId: data.id,
            }).then((result) => {
                let data = result.data;
                setRealityCapacity(data)
            });
        } else {
            setTitle(formatMessage({id: 'common.addTo'}))
            setIsAdd(true)
            form.setFieldsValue({status: 1})   // 默认草稿
        }
    }

    const hide = () => {
        setSend(true);
        setUnUsedOptions(AlertThresholdType)
        setOptionCount(0)
        setVisible(false)
        setProjectId(null)
        setEnvId(null)
        setType(null)
        setCohorts([])
        setCohortStages([])
        setId(null)
        setStatus(null)
        setNewStatus(null)
        setCohortType(null)
        form.resetFields()
    }
    

    const formChange = () => {
        setNewStatus(form.getFieldValue().status);
        if (title === formatMessage({id: 'common.edit'})) {
            const a = _.cloneDeep(oldData);
            if(a.alertThresholds === null || a.alertThresholds === undefined){
                a.alertThresholds = [];
            }
            let b = _.cloneDeep(form.getFieldsValue());
            if (!compareObjects(a, b)) {
                setSend(false);
            } else {
                setSend(true);
            }
  
        }
    }

    //比较两个JavaScript对象是否相同
    function compareObjects(obj1, obj2) {
        for (let key in obj1) {
            if (obj2.hasOwnProperty(key) && obj2[key] !== undefined) {
                if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
                    if (!arraysAreEqual(obj1[key], obj2[key])) {
                        return false;
                    }
                } else if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
                    if (!compareObjects(obj1[key], obj2[key])) {
                        return false;
                    }
                } else {
                    if (obj1[key] !== obj2[key]) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    //比较两个数组是否相同
    function arraysAreEqual(arr1, arr2) {
        // 检查数组长度是否相同
        if (arr1.length !== arr2.length) {
            return false;
        }
        const a = _.cloneDeep(arr1);
        const b = _.cloneDeep(arr2);

        let str1Type = a.map(({ type, ...rest }) => type !== null ? { type, ...rest } : rest);
        let str1Capacity = str1Type.map(({ capacity, ...rest }) => capacity !== null ? { capacity, ...rest } : rest);
        let str1Tthresholds = str1Capacity.map(({ thresholds, ...rest }) => thresholds !== null ? { thresholds, ...rest } : rest);

        let str2Type = b.map(({ type, ...rest }) => type !== null ? { type, ...rest } : rest);
        let str2Capacity = str2Type.map(({ capacity, ...rest }) => capacity !== null ? { capacity, ...rest } : rest);
        let str2Tthresholds = str2Capacity.map(({ thresholds, ...rest }) => thresholds !== null ? { thresholds, ...rest } : rest);


        // 将数组转换为字符串并比较
        const str1 = JSON.stringify(str1Tthresholds.sort());
        const str2 = JSON.stringify(str2Tthresholds.sort());

        return str1 === str2;
    }

    const handleChangeType = (e) => {
        let useType = form.getFieldValue("alertThresholds").map((v)=> v.type)
        let unUse = AlertThresholdType.filter((v) => !useType.includes(v.id))
        setUnUsedOptions(unUse.filter((value) => value.id !== e))
        if (e === 3){
            let capacity = form.getFieldValue("alertThresholds").find((v)=>v.type === 3).capacity;
            if (capacity != null){
                setNewCapacity(capacity);
            }
        }
    };
    const handleChangeCapacity = (value) => {
        let i = form.getFieldValue("alertThresholds").findIndex((v)=>v.type === 3);
        if (i !== -1){
            setNewCapacity(form.getFieldValue("alertThresholds")[i].capacity);
        }
    };
    const  addClick = (add, fields)=>{
        add();
        setOptionCount(optionCount+1)
    }
    const save = React.useCallback(
        () => {
            form.validateFields()
                .then(values => {
                    values.name = values.name?.trim();
                    values.reRandomName = values.reRandomName?.trim();
                    values.factor = values.factor?.trim();
                    if (id) {
                        runUpdateCohort({id: projectId, envID: envId, cohortID: id}, {...values}).then((resp) => {
                            message.success(resp.msg)
                            props.refresh()
                            hide()
                            })
                    } else {
                        runAddCohort({id: projectId, envID: envId}, {...values}).then((resp) => {
                            message.success(resp.msg)
                            props.refresh()
                            hide()
                        })
                    }

                })
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [projectId, envId, id]
    )

    React.useImperativeHandle(props.bind, () => ({show}))

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 4: 6 },
        },
        wrapperCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 20: 19 },
        },
    }

    return (
        <Modal
            centered
            width={600}
            title={title}
            open={visible}
            onClose={hide}
            maskClosable={false}
            onOk={save}
            onCancel={hide}
            okText={formatMessage({id: 'common.ok'})}
            okButtonProps={{ disabled: title === formatMessage({id: 'common.edit'}) ? send : false }}
        >
            <Form form={form} layout='horizontal'
                  onValuesChange={formChange}
                  {...formItemLayout}>
                {
                    !envLocked && type === 2 ?
                        <Form.Item
                            label={formatMessage({id: 'projects.envs.cohort.type'})}
                            name='type'
                            rules={[{required: true}]}
                        >
                            <Select
                                className="full-width"
                                allowClear
                                placeholder={formatMessage({id: 'placeholder.select.common'})}
                                onChange={(e) => {setCohortType(e)}}
                            >
                                <Select.Option value={0}><FormattedMessage id="projects.envs.cohort.type.cohort" /></Select.Option>
                                <Select.Option value={1}><FormattedMessage id="projects.envs.cohort.type.reRandom" /></Select.Option>
                            </Select>
                        </Form.Item>
                        :null
                }
                <Form.Item label={formatMessage({id: 'common.name'})} name='name' rules={[{required: !envLocked || isAdd}]}
                           onChange={(e) => {
                               if (type === 2 && cohortType === 1){
                                   let reRandomName = form.getFieldsValue().reRandomName
                                   setCohortStages(cohorts.filter((v)=>v.label === e.target.value && v.reRandomName !== "" && v.reRandomName !== reRandomName).map(v=>({label:v.reRandomName, value:v.value})))
                               }
                           }}>
                    {
                        envLocked && !isAdd?
                            form.getFieldValue("name")
                            :
                            <Input allowClear placeholder={formatMessage({id: 'common.required.prefix'})}/>
                    }

                </Form.Item>
                {
                    !envLocked && type === 2 && cohortType === 1 ?
                        <Form.Item label={formatMessage({id: 'projects.subject.stageName'})} name='reRandomName'
                                    rules={[{required: true}]}>
                            <Input allowClear placeholder={formatMessage({id: 'common.required.prefix'})}/>
                        </Form.Item>
                        : null
                }
                {
                    !envLocked && connectEdc === 1 && pushMode === 1?
                        <Form.Item label={formatMessage({id: 'projects.envs.cohorts.factor'})} name='factor'
                                    rules={[{required: true}]}>
                            <Input placeholder={formatMessage({id: 'common.required.prefix'})} allowClear/>
                        </Form.Item>
                        : null
                }
                {
                    !envLocked && type === 3 || (type === 2 && cohortType === 1)?
                        <Form.Item label={formatMessage({id: 'projects.envs.cohorts.stage'})} name='lastId'>
                            <Select placeholder={formatMessage({id: 'placeholder.select.common'})} options={cohortStages}/>
                        </Form.Item>
                        : null
                }
                {
                    status === 1 ?
                        <Form.Item label={formatMessage({id: 'common.status'})} name='status'
                                    rules={[{required: true}]}>
                            <Select>
                                <Select.Option value={1}><FormattedMessage
                                    id='projects.envs.cohorts.status1'/></Select.Option>
                                <Select.Option value={2}><FormattedMessage
                                    id='projects.envs.cohorts.status2'/></Select.Option>
                            </Select>
                        </Form.Item> : null
                }
                {
                    status === 2 ?
                        <Form.Item label={formatMessage({id: 'common.status'})} name='status'
                                    rules={[{required: true}]}>
                            <Select>
                                <Select.Option value={2}><FormattedMessage
                                    id='projects.envs.cohorts.status2'/></Select.Option>
                                <Select.Option value={5}><FormattedMessage
                                    id='projects.envs.cohorts.status5'/></Select.Option>
                                <Select.Option value={3}><FormattedMessage
                                    id='projects.envs.cohorts.status3'/></Select.Option>
                                <Select.Option value={4}><FormattedMessage
                                    id='projects.envs.cohorts.status4'/></Select.Option>
                            </Select>
                        </Form.Item> : null
                }

                {
                    status === 3 && oldCapacity === newCapacity && oldCapacity !== -1 ?
                        <Form.Item label={formatMessage({id: 'common.status'})} name='status'
                                    rules={[{required: true}]}>
                            <Select>
                                <Select.Option value={3}><FormattedMessage
                                    id='projects.envs.cohorts.status3'/></Select.Option>
                                <Select.Option value={4}><FormattedMessage
                                    id='projects.envs.cohorts.status4'/></Select.Option>
                            </Select>
                        </Form.Item> : null
                }

                {
                    status === 3 && oldCapacity !== newCapacity && oldCapacity !== -1 && envName === null?
                        <Form.Item label={formatMessage({id: 'common.status'})} name='status'
                                    rules={[{required: true}]}>
                            <Select>
                                <Select.Option value={1}><FormattedMessage
                                    id='projects.envs.cohorts.status1'/></Select.Option>
                                <Select.Option value={2}><FormattedMessage
                                    id='projects.envs.cohorts.status2'/></Select.Option>
                                <Select.Option value={3}><FormattedMessage
                                    id='projects.envs.cohorts.status3'/></Select.Option>
                                <Select.Option value={4}><FormattedMessage
                                    id='projects.envs.cohorts.status4'/></Select.Option>
                            </Select>
                        </Form.Item> : null
                }

                {
                    status === 3 && oldCapacity !== newCapacity && oldCapacity !== -1 && envName != null?
                        <Form.Item label={formatMessage({id: 'common.status'})} name='status'
                                    rules={[{required: true}]}>
                            <Select>
                                <Select.Option value={2}><FormattedMessage
                                    id='projects.envs.cohorts.status2'/></Select.Option>
                                <Select.Option value={3}><FormattedMessage
                                    id='projects.envs.cohorts.status3'/></Select.Option>
                                <Select.Option value={4}><FormattedMessage
                                    id='projects.envs.cohorts.status4'/></Select.Option>
                            </Select>
                        </Form.Item> : null
                }

                {
                    status === 4 ?
                        <Form.Item label={formatMessage({id: 'common.status'})} name='status'
                                    rules={[{required: true}]}>
                            <Select>
                                <Select.Option value={4}><FormattedMessage
                                    id='projects.envs.cohorts.status4'/></Select.Option>
                            </Select>
                        </Form.Item> : null
                }
                {
                    status === 5 ?
                        <Form.Item label={formatMessage({id: 'common.status'})} name='status'
                                    rules={[{required: true}]}>
                            <Select>
                                {
                                    realityCapacity < newCapacity ?
                                        <Select.Option value={2}>
                                            <FormattedMessage id='projects.envs.cohorts.status2'/>
                                        </Select.Option>:null
                                }
                                <Select.Option value={5}><FormattedMessage
                                    id='projects.envs.cohorts.status5'/></Select.Option>
                                <Select.Option value={3}><FormattedMessage
                                    id='projects.envs.cohorts.status3'/></Select.Option>
                                <Select.Option value={4}><FormattedMessage
                                    id='projects.envs.cohorts.status4'/></Select.Option>
                            </Select>
                            
                        </Form.Item> : null
                }
                {
                    id === null ?
                        <Form.Item label={formatMessage({id: 'common.status'})} name='status'
                                    rules={[{required: true}]}>
                            <Select>
                                <Select.Option value={1}><FormattedMessage
                                    id='projects.envs.cohorts.status1'/></Select.Option>
                            </Select>
                        </Form.Item> : null
                }
                {!envLocked && status !== newStatus && id != null ?
                    <Form.Item label={formatMessage({id: 'common.password'})} name='password'
                                rules={[{required: true}]}>
                        <Input.Password allowClear className='full-width'/>
                    </Form.Item> : null
                }
                <Form.Item
                    label={formatMessage({ id: "projects.envs.cohorts.reminder.thresholds.toplimit" })}
                    name="alertThresholds"
                >
                    <Form.List name="alertThresholds">
                        {(fields, { add, remove }) => (
                            <>
                                {fields.map((field, index) => (
                                    <Col key={field.key}>
                                        <Row>
                                            <Space
                                                key={field.key}
                                                style={{ display: "flex" }}
                                            >
                                                <Form.Item
                                                    rules={[{required: true, message: formatMessage({ id: 'placeholder.select.common' })}]}
                                                    {...field}
                                                    name={[field.name, "type"]}>
                                                    <Select style={{width: 180}} onChange={(e) => handleChangeType(e)}
                                                            placeholder={formatMessage({id: 'common.status'})}
                                                    >
                                                        {
                                                            AlertThresholdType.map((value) =>
                                                                <Select.Option disabled={!unUsedOptions.find((v) => v.id === value.id)} value={value.id}>{value.name}</Select.Option>
                                                            )
                                                        }
                                                    </Select>
                                                </Form.Item>
                                                <Form.Item
                                                    rules={[{required: true,message: (formatMessage({ id: 'placeholder.input.common' }))}]}
                                                    {...field}
                                                    name={[field.name, "capacity"]}
                                                >
                                                    <TipInputNumber
                                                        onChange={(e)=>handleChangeCapacity(e)} style={{width: g.lang === "en"?70:100}} placeholder={formatMessage({id: 'projects.envs.cohorts.capacity.value'})} precision={0} min={0}
                                                        step={1}
                                                    />
                                                </Form.Item>
                                                <Form.Item
                                                    {...field}
                                                    name={[field.name, "thresholds"]}
                                                >
                                                    <TipInputNumber
                                                        style={{width: g.lang === "en"?114:134}} placeholder={formatMessage({id: 'projects.envs.cohorts.reminder.thresholds'})} addonAfter={<span>%</span>} allowClear precision={0} min={0}
                                                        max={100}
                                                    />
                                                </Form.Item>

                                                <MinusCircleFilled className="iconfont" width={16} height={16}
                                                     onClick={() => {
                                                         remove(field.name)
                                                         let useType = form.getFieldValue("alertThresholds").map((v)=> v.type)
                                                         let unUse = AlertThresholdType.filter((v) => !useType.includes(v.id))
                                                         setUnUsedOptions(unUse)
                                                         setOptionCount(optionCount-1)
                                                     }}
                                                     style={{marginLeft: 6, marginBottom:16,  cursor: "pointer", color: "#fe5b5a"}}/>
                                            </Space>
                                        </Row>
                                    </Col>
                                ))}
                                {/* {
                                    console.log("optionCount==" + optionCount)
                                } */}
                                {(optionCount < AlertThresholdType.length) ? <Form.Item style={{ marginBottom: 0 }}>
                                    <Button
                                        type="dashed"
                                        onClick={() => addClick(add, fields)}
                                        block
                                        icon={<PlusOutlined />}
                                    >
                                        <FormattedMessage id={"common.addTo"} />
                                    </Button>
                                </Form.Item>:null
                                }

                            </>
                        )}
                    </Form.List>
                </Form.Item>
            </Form>
        </Modal>
    )
}

export default ProjectInfoCohort
