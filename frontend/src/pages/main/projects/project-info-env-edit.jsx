import React, {useImperativeHandle} from 'react'
import {FormattedMessage, useIntl} from 'react-intl'
import {Button, Col, Form, Input, message, Modal, Row, Select, Space} from 'antd'
import {useSafeState} from 'ahooks'
import {useFetch} from '../../../hooks/request'
import {editEnv} from "../../../api/projects";
import {useGlobal} from "../../../context/global";
import {MinusCircleFilled, PlusOutlined} from "@ant-design/icons";
import {AlertThresholdType} from "../../../data/data";
import {TipInputNumber} from 'components/TipInput';
import {getSubjectRealityCapacity} from "../../../api/subject";
import {useAuth} from "../../../context/auth";
import _ from "lodash";

const ProjectInfoEnvEdit = (props) => {
    const {formatMessage} = useIntl()
    const g = useGlobal()
    const auth = useAuth()
    const [form] = Form.useForm()
    const [visible, setVisible] = useSafeState(false)
    const [envId, setEnvId] = useSafeState(null)
    const [projectId, setProjectId] = useSafeState(null)
    const [envLocked, setEnvLocked] = useSafeState(false)
    useImperativeHandle(props.bind, ()  => ({show}))
    const {runAsync: runEditEnv, loading: submitting} = useFetch(editEnv, {manual: true})
    const [unUsedOptions, setUnUsedOptions] = useSafeState([])
    const [optionCount,setOptionCount] = useSafeState(0)
    const [status, setStatus] = useSafeState(null)
    const [oldCapacity, setOldCapacity] = useSafeState(-1);
    const [newCapacity, setNewCapacity] = useSafeState(-1);
    const [realityCapacity, setRealityCapacity] = useSafeState(0)
    const customerId = auth.customerId;
    const [newStatus, setNewStatus] = useSafeState(null)

    const [send, setSend] = useSafeState(true);
    const [oldData, setOldData] = useSafeState(null);

    const { runAsync: getSubjectRealityCapacityRun, loading: getSubjectRealityCapacitLoading } = useFetch(getSubjectRealityCapacity, { manual: true });
    const save = () => {
        form.validateFields().then(values => {
            values.envName = values.envName.trim();
            runEditEnv({envId:envId,projectId:projectId,...values}).then(() => {
                message.success(formatMessage({id: 'common.success'}))
                props.refresh()
                setVisible(false)
            })
        })
    }

    const show = (project,env) => {
        setEnvId(env.id)
        setProjectId(project.id)
        setStatus(env.status)
        setNewStatus(env.status)
        setEnvLocked(env.lockConfig)
        if (env.alertThresholds != null && env.alertThresholds.length > 0){
            let unUse = AlertThresholdType.filter((it)=>{
                return !env.alertThresholds.find((i) => i.type === it.id)
            })
            setOldCapacity(env.alertThresholds?.find((v)=>v.type === 3)?.capacity)
            setNewCapacity(env.alertThresholds?.find((v)=>v.type === 3)?.capacity)
            setUnUsedOptions(unUse)
            setOptionCount(env.alertThresholds.length)
        }else {
            setUnUsedOptions(AlertThresholdType)
            setOptionCount(0)
        }
        form.setFieldsValue({
            envId:env.id,
            projectId:project.id,
            envName: env.name,
            alertThresholds:env.alertThresholds,
            status:env.status,
        })
        setOldData({
            envId:env.id,
            projectId:project.id,
            envName: env.name,
            alertThresholds:env.alertThresholds,
            status:env.status,
        });
        getSubjectRealityCapacityRun({
            customerId,
            envId:env.id,
            projectId:project.id,
            cohortId: null,
        }).then((result) => {
            let data = result.data;
            setRealityCapacity(data);
        });
        setVisible(true)
    }
    const formItemLayout = {
        labelCol: { style: { width: g.lang === "en" ? "180px" : "80px"} },
    };
    const  addClick = (add, fields)=>{
        add();
        setOptionCount(optionCount+1)
    }
    const handleChangeCapacity = (value) => {
        let i = form.getFieldValue("alertThresholds").findIndex((v)=>v.type === 3);
        if (i !== -1){
            setNewCapacity(form.getFieldValue("alertThresholds")[i].capacity);
        }
    };

    const formChange = () => {
        setNewStatus(form.getFieldValue().status);
        const a = _.cloneDeep(oldData);
        if(a.alertThresholds === null || a.alertThresholds === undefined){
            a.alertThresholds = [];
        }
        // console.log("1===" + JSON.stringify(a)); 
        let b = _.cloneDeep(form.getFieldsValue());
        // console.log("2===" + JSON.stringify(b)); 
        if (!compareObjects(a, b)) {
            setSend(false);
        } else {
            setSend(true);
        }
    }

    //比较两个JavaScript对象是否相同
    function compareObjects(obj1, obj2) {
        for (let key in obj1) {
            if (obj2.hasOwnProperty(key) && obj2[key] !== undefined) {
                if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
                    if (!arraysAreEqual(obj1[key], obj2[key])) {
                        return false;
                    }
                } else if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
                    if (!compareObjects(obj1[key], obj2[key])) {
                        return false;
                    }
                } else {
                    if (obj1[key] !== obj2[key]) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    //比较两个数组是否相同
    function arraysAreEqual(arr1, arr2) {
        // 检查数组长度是否相同
        if (arr1.length !== arr2.length) {
            return false;
        }

        const a = _.cloneDeep(arr1);
        const b = _.cloneDeep(arr2);

        let str1Type = a.map(({ type, ...rest }) => type !== null ? { type, ...rest } : rest);
        let str1Capacity = str1Type.map(({ capacity, ...rest }) => capacity !== null ? { capacity, ...rest } : rest);
        let str1Tthresholds = str1Capacity.map(({ thresholds, ...rest }) => thresholds !== null ? { thresholds, ...rest } : rest);

        let str2Type = b.map(({ type, ...rest }) => type !== null ? { type, ...rest } : rest);
        let str2Capacity = str2Type.map(({ capacity, ...rest }) => capacity !== null ? { capacity, ...rest } : rest);
        let str2Tthresholds = str2Capacity.map(({ thresholds, ...rest }) => thresholds !== null ? { thresholds, ...rest } : rest);


        // 将数组转换为字符串并比较
        const str1 = JSON.stringify(str1Tthresholds.sort());
        const str2 = JSON.stringify(str2Tthresholds.sort());


        console.log(str1 === str2)

        return str1 === str2;
    }


    return (
        <Modal
            centered
            width={600}
            destroyOnClose={true}
            maskClosable={false}
            title={formatMessage({id: 'projects.envs.edit'})}
            open={visible}
            onOk={() => save()}
            onCancel={() => {
                setSend(true);
                setVisible(false);
            }}
            confirmLoading={submitting}
            okText={formatMessage({id: 'common.ok'})}
            okButtonProps={{ disabled: send}}
        >
            <Form form={form} onValuesChange={formChange} layout="horizontal" {...formItemLayout}>
                <Form.Item label={formatMessage({id: 'projects.envs.name'})}
                           name='envName'
                           rules={[{required: !envLocked}]}
                >
                    {envLocked ?
                        form.getFieldValue("envName")
                        :
                        <Input placeholder={formatMessage({id: 'placeholder.input.common'})} allowClear showCount/>
                    }
                </Form.Item>
                {
                    props.project?.info.type === 1 ?
                <Form.Item
                    label={formatMessage({ id: "projects.envs.cohorts.reminder.thresholds.toplimit" })}
                    name="alertThresholds"
                >
                    <Form.List name="alertThresholds">
                        {(fields, { add, remove }) => (
                            <>
                                {fields.map((field, index) => (
                                    <Col key={field.key}>
                                        <Row>
                                            <Space
                                                key={field.key}
                                                style={{ display: "flex" }}
                                            >
                                                <Form.Item
                                                    rules={[{required: true, message: formatMessage({ id: 'placeholder.select.common' })}]}
                                                    {...field}
                                                    name={[field.name, "type"]}>
                                                    <Select style={{width: 150}} onChange={(e) => {
                                                        let useType = form.getFieldValue("alertThresholds").map((v)=> v.type)
                                                        let unUse = AlertThresholdType.filter((v) => !useType.includes(v.id))
                                                        setUnUsedOptions(unUse.filter((value) => value.id !== e))
                                                        if (e === 3){
                                                            let capacity = form.getFieldValue("alertThresholds").find((v)=>v.type === 3).capacity;
                                                            if (capacity != null){
                                                                setNewCapacity(capacity);
                                                            }
                                                        }
                                                    }}
                                                            placeholder={formatMessage({id: 'placeholder.select.common'})}
                                                    >
                                                        {
                                                            AlertThresholdType.map((value) =>
                                                                <Select.Option disabled={!unUsedOptions.find((v) => v.id === value.id)} value={value.id}>{value.name}</Select.Option>
                                                            )
                                                        }
                                                    </Select>
                                                </Form.Item>
                                                <Form.Item
                                                    rules={[{required: true,message: (formatMessage({ id: 'placeholder.input.common' })+formatMessage({id: 'projects.envs.cohorts.capacity.value'}))}]}
                                                    {...field}
                                                    name={[field.name, "capacity"]}
                                                >
                                                    <TipInputNumber
                                                        onChange={(e)=>handleChangeCapacity(e)} style={{width: g.lang === "en"?70:150}} placeholder={formatMessage({id: 'projects.envs.cohorts.capacity.value'})} precision={0} min={0}
                                                        step={1}
                                                    />
                                                </Form.Item>
                                                <Form.Item
                                                    {...field}
                                                    name={[field.name, "thresholds"]}
                                                >
                                                    <TipInputNumber
                                                        style={{width: g.lang === "en"?94:114}} placeholder={formatMessage({id: 'projects.envs.cohorts.reminder.thresholds'})} addonAfter={<span>%</span>} allowClear precision={0} min={0}
                                                        max={100}
                                                    />
                                                </Form.Item>
                                                <MinusCircleFilled
                                                    className="iconfont" width={16} height={16}
                                                    style={{marginLeft: 6, marginBottom:16,  cursor: "pointer", color: "#fe5b5a"}}
                                                    onClick={() => {
                                                        remove(field.name)
                                                        let useType = form.getFieldValue("alertThresholds").map((v)=>v.type)
                                                        let unUse = AlertThresholdType.filter((v) => !useType.includes(v.id))
                                                        setUnUsedOptions(unUse)
                                                        setOptionCount(optionCount-1)
                                                    }}
                                                />
                                            </Space>
                                        </Row>
                                    </Col>
                                ))}
                                {optionCount< AlertThresholdType.length ?
                                    <>
                                    <Form.Item style={{ marginBottom: 0 }}>
                                    <Button
                                        type="dashed"
                                        onClick={() => addClick(add, fields)}
                                        block
                                        icon={<PlusOutlined />}
                                    >
                                        <FormattedMessage id={"common.addTo"} />
                                    </Button>
                                </Form.Item>
                                    </>
                                        :null
                                }

                            </>
                        )}
                    </Form.List>
                </Form.Item>
                        :null}
                {
                    props.project?.info?.type === 1?
                        <>
                            {
                                status === 1 ?
                                    <Form.Item label={formatMessage({id: 'common.status'})} name='status'
                                               rules={[{required: true}]}>
                                        <Select>
                                            <Select.Option value={1}><FormattedMessage
                                                id='projects.envs.cohorts.status1'/></Select.Option>
                                            <Select.Option value={2}><FormattedMessage
                                                id='projects.envs.cohorts.status2'/></Select.Option>
                                        </Select>
                                    </Form.Item> : null
                            }
                            {
                                status === 2 ?
                                    <Form.Item label={formatMessage({id: 'common.status'})} name='status'
                                               rules={[{required: true}]}>
                                        <Select>
                                            <Select.Option value={2}><FormattedMessage
                                                id='projects.envs.cohorts.status2'/></Select.Option>
                                            <Select.Option value={5}><FormattedMessage
                                                id='projects.envs.cohorts.status5'/></Select.Option>
                                            <Select.Option value={3}><FormattedMessage
                                                id='projects.envs.cohorts.status3'/></Select.Option>
                                            <Select.Option value={4}><FormattedMessage
                                                id='projects.envs.cohorts.status4'/></Select.Option>
                                        </Select>
                                    </Form.Item> : null
                            }

                            {
                                status === 3 && oldCapacity === newCapacity && oldCapacity !== -1 ?
                                    <Form.Item label={formatMessage({id: 'common.status'})} name='status'
                                               rules={[{required: true}]}>
                                        <Select>
                                            <Select.Option value={3}><FormattedMessage
                                                id='projects.envs.cohorts.status3'/></Select.Option>
                                            <Select.Option value={4}><FormattedMessage
                                                id='projects.envs.cohorts.status4'/></Select.Option>
                                        </Select>
                                    </Form.Item> : null
                            }

                            {
                                status === 3 && oldCapacity !== newCapacity && oldCapacity !== -1 && envName === null?
                                    <Form.Item label={formatMessage({id: 'common.status'})} name='status'
                                               rules={[{required: true}]}>
                                        <Select>
                                            <Select.Option value={1}><FormattedMessage
                                                id='projects.envs.cohorts.status1'/></Select.Option>
                                            <Select.Option value={2}><FormattedMessage
                                                id='projects.envs.cohorts.status2'/></Select.Option>
                                            <Select.Option value={3}><FormattedMessage
                                                id='projects.envs.cohorts.status3'/></Select.Option>
                                            <Select.Option value={4}><FormattedMessage
                                                id='projects.envs.cohorts.status4'/></Select.Option>
                                        </Select>
                                    </Form.Item> : null
                            }

                            {
                                status === 3 && oldCapacity !== newCapacity && oldCapacity !== -1 && envName != null?
                                    <Form.Item label={formatMessage({id: 'common.status'})} name='status'
                                               rules={[{required: true}]}>
                                        <Select>
                                            <Select.Option value={2}><FormattedMessage
                                                id='projects.envs.cohorts.status2'/></Select.Option>
                                            <Select.Option value={3}><FormattedMessage
                                                id='projects.envs.cohorts.status3'/></Select.Option>
                                            <Select.Option value={4}><FormattedMessage
                                                id='projects.envs.cohorts.status4'/></Select.Option>
                                        </Select>
                                    </Form.Item> : null
                            }

                            {
                                status === 4 ?
                                    <Form.Item label={formatMessage({id: 'common.status'})} name='status'
                                               rules={[{required: true}]}>
                                        <Select>
                                            <Select.Option value={4}><FormattedMessage
                                                id='projects.envs.cohorts.status4'/></Select.Option>
                                        </Select>
                                    </Form.Item> : null
                            }
                            {
                                status === 5 ?
                                    <Form.Item label={formatMessage({id: 'common.status'})} name='status'
                                               rules={[{required: true}]}>
                                        <Select>
                                            {
                                                realityCapacity < newCapacity ?
                                                    <Select.Option value={2}>
                                                        <FormattedMessage id='projects.envs.cohorts.status2'/>
                                                    </Select.Option>:null
                                            }
                                            <Select.Option value={5}><FormattedMessage
                                                id='projects.envs.cohorts.status5'/></Select.Option>
                                            <Select.Option value={3}><FormattedMessage
                                                id='projects.envs.cohorts.status3'/></Select.Option>
                                            <Select.Option value={4}><FormattedMessage
                                                id='projects.envs.cohorts.status4'/></Select.Option>
                                        </Select>

                                    </Form.Item> : null
                            }
                        </>
                        :null
                }
                {status !== newStatus  ?
                    <Form.Item label={formatMessage({id: 'common.password'})} name='password'
                               rules={[{required: true}]}>
                        <Input.Password allowClear className='full-width'/>
                    </Form.Item> : null
                }
            </Form>
        </Modal>
    )
}
export default ProjectInfoEnvEdit
