import {MinusCircleFilled, PlusOutlined} from '@ant-design/icons'
import {addEnv} from '../../../api/projects'
import {useSafeState} from 'ahooks'
import {useFetch} from '../../../hooks/request'
import {FormattedMessage, useIntl} from 'react-intl'
import {Button, Col, Form, Input, message, Modal, Row, Select, Space} from 'antd'
import React, {useImperativeHandle} from 'react'
import styled from '@emotion/styled'
import {AlertThresholdType} from "../../../data/data";
import {useGlobal} from "../../../context/global";
import {TipInputNumber} from 'components/TipInput';

const ProjectInfoEnvAdd = props => {
    const {formatMessage} = useIntl()
    const g = useGlobal()
    const [form] = Form.useForm()
    const [visible, setVisible] = useSafeState(false)

    const initOptions = [
        {label: 'PROD', value: 'PROD'},
        {label: 'DEV', value: 'DEV'},
        {label: 'UAT', value: 'UAT'},
        {label: 'TEST', value: 'TEST'}
    ]
    const [unUsedOptions, setUnUsedOptions] = useSafeState([])
    const [optionCount,setOptionCount] = useSafeState(0)
    useImperativeHandle(props.bind, () => ({
        show: () => show()
    }))

    const {runAsync: runAddEnv, loading: submitting} = useFetch(addEnv, {manual: true})
    const onAddEnv = values => {
        let env = values.env
        if (env && env.trim() !== '') {
            if (props.project.envs.map(it => it.name).findIndex(it => it === env) !== -1) {
                message.error(formatMessage({id: 'projects.envs.duplicate.name'})).then()
            } else {
                runAddEnv({projectId: props.project.id, ...values}).then((resp) => {
                        message.success(resp.msg)
                        props.refresh()
                    }
                )
            }
        } else {
            message.warn(formatMessage({id: 'projects.envs.empty'})).then()
        }
    }

    const [envOptions, setEnvOptions] = useSafeState(initOptions.filter(it => props.project?.envs.findIndex(item => item.name === it.value) === -1))

    const show = () => {
        form.resetFields()
        setVisible(true)
        setEnvOptions(initOptions.filter(it => props.project?.envs.findIndex(item => item.name === it.value) === -1))
        setUnUsedOptions(AlertThresholdType)
        setOptionCount(0)
    }

    const CustomEnvAdder = ceapr => {
        const [form] = Form.useForm()
        const onAdd = () => {
            form.validateFields().then((values) => {
                const v = values.env
                setEnvOptions(envOptions.concat({label: v, value: v}))
                ceapr.form.setFieldsValue({env: v})
                ceapr.setOpen(false)
            })
        }

        const validator = {
            validator: (_, value) => {
                if (value 
                    && value.trim().length > 0
                    && props.project?.envs.findIndex(item => item.name === value) === -1
                    && envOptions.findIndex(item => item.value === value) === -1
                ) {
                    return Promise.resolve()
                } else if (!value || value.trim().length === 0) {
                    return Promise.reject(formatMessage({id: 'common.required.prefix'}) + formatMessage({id: 'projects.envs.name'}))
                } else {
                    return Promise.reject(formatMessage({id: 'projects.envs.duplicate.name'}))
                }
            }
        }
        
        return (
            <Form style={{margin: '0 10px'}} form={form}>
                <Form.Item name='env' rules={[validator]}>
                    <Input 
                        placeholder={formatMessage({id: 'placeholder.input.common'})}
                        addonAfter={
                            <Button type='link' size='small' onClick={onAdd}>
                                <PlusOutlined /> <FormattedMessage id={'common.addTo'} />
                            </Button>
                        }
                        onBlur={e => {
                            if (!e.target.value || e.target.value.trim().length === 0) {
                                form.setFields([{name: 'env', errors: null}])
                            }
                        }}
                    />
                </Form.Item>
            </Form>
        )
    }
    const  addClick = (add, fields)=>{
        add();
        setOptionCount(optionCount+1)
    }
    const formItemLayout = {
        labelCol: { style: { width: g.lang === "en" ? "180px" : "80px"} },
    };
    const [selectOpen, setSelectOpen] = useSafeState(false)
    return (
        <Modal
            centered
            width={600}
            destroyOnClose={true}
            maskClosable={false}
            title={formatMessage({id: 'projects.envs.add'})}
            visible={visible}
            onOk={() => {
                form.validateFields().then(values => {
                    form.setFieldsValue({env: null})
                    onAddEnv(values)
                    setVisible(false)
                })
            }}
            onCancel={() => {
                form.setFieldsValue({env: null})
                setVisible(false)
            }}
            confirmLoading={submitting}
            okText={formatMessage({id: 'common.ok'})}
        >
            <Form form={form} layout='horizontal'  {...formItemLayout}>
                <Form.Item
                    label={formatMessage({id: 'projects.envs.name'})}
                    name='env'
                    rules={[{required: true}]}
                >
                    <Select
                        className="full-width"
                        open={selectOpen}
                        onDropdownVisibleChange={(visible) => setSelectOpen(visible)}
                        allowClear
                        placeholder={formatMessage({id: 'placeholder.select.common'})}
                        dropdownRender={menu => (
                            <>
                                {envOptions && envOptions.length > 0 ? menu : null}
                                {/* <Divider style={{ margin: '8px 0' }} /> */}
                                {/* <div className={styles.customEnvAdder}><CustomEnvAdder form={form} setOpen={setSelectOpen} /></div> */}
                                <SelectInput><CustomEnvAdder form={form} setOpen={setSelectOpen} /></SelectInput>
                            </>
                        )}
                    >
                        {
                            envOptions.map(it => 
                                <Select.Option 
                                    key={it.value} 
                                    value={it.value}
                                >
                                    {it.label}
                                </Select.Option>
                            )
                        }
                    </Select> 
                </Form.Item>
                {
                    props.project?.info.type === 1 ?
                        <Form.Item
                            label={formatMessage({ id: "projects.envs.cohorts.reminder.thresholds.toplimit" })}
                            name="alertThresholds"
                        >
                            <Form.List name="alertThresholds">
                                {(fields, { add, remove }) => (
                                    <>
                                        {fields.map((field, index) => (
                                            <Col key={field.key}>
                                                <Row>
                                                    <Space
                                                        key={field.key}
                                                        style={{ display: "flex" }}
                                                    >
                                                        <Form.Item
                                                            rules={[{required: true, message: formatMessage({ id: 'placeholder.select.common' })}]}
                                                            {...field}
                                                            name={[field.name, "type"]}>
                                                            <Select style={{width: 150}} onChange={(e) => {
                                                                let useType = form.getFieldValue("alertThresholds").map((v)=> v.type)
                                                                let unUse = AlertThresholdType.filter((v) => !useType.includes(v.id))
                                                                setUnUsedOptions(unUse.filter((value) => value.id !== e))
                                                            }}
                                                                    placeholder={formatMessage({id: 'placeholder.select.common'})}
                                                            >
                                                                {
                                                                    AlertThresholdType.map((value) =>
                                                                        <Select.Option disabled={!unUsedOptions.find((v) => v.id === value.id)} value={value.id}>{value.name}</Select.Option>
                                                                    )
                                                                }
                                                            </Select>
                                                        </Form.Item>
                                                        <Form.Item
                                                            rules={[{required: true,message: (formatMessage({ id: 'placeholder.input.common' })+formatMessage({id: 'projects.envs.cohorts.capacity.value'}))}]}
                                                            {...field}
                                                            name={[field.name, "capacity"]}
                                                        >
                                                            <TipInputNumber
                                                                style={{width: g.lang === "en"?70:150}} placeholder={formatMessage({id: 'projects.envs.cohorts.capacity.value'})} precision={0} min={0}
                                                                step={1}
                                                            />
                                                        </Form.Item>
                                                        <Form.Item
                                                            {...field}
                                                            name={[field.name, "thresholds"]}
                                                        >
                                                            <TipInputNumber
                                                                style={{width: g.lang === "en"?94:114}} placeholder={formatMessage({id: 'projects.envs.cohorts.reminder.thresholds'})} addonAfter={<span>%</span>} allowClear precision={0} min={0}
                                                                max={100}
                                                            />
                                                        </Form.Item>
                                                        <MinusCircleFilled
                                                            className="iconfont" width={16} height={16}
                                                            style={{marginLeft: 6, marginBottom:16,  cursor: "pointer", color: "#fe5b5a"}}
                                                            onClick={() => {
                                                                remove(field.name)
                                                                let useType = form.getFieldValue("alertThresholds").map((v)=>v.type)
                                                                let unUse = AlertThresholdType.filter((v) => !useType.includes(v.id));
                                                                setUnUsedOptions(unUse)
                                                                setOptionCount(optionCount-1)
                                                            }}
                                                        />
                                                    </Space>
                                                </Row>
                                            </Col>
                                        ))}
                                        {optionCount<AlertThresholdType.length? <Form.Item style={{ marginBottom: 0 }}>
                                            <Button
                                                type="dashed"
                                                onClick={() => addClick(add, fields)}
                                                block
                                                icon={<PlusOutlined />}
                                            >
                                                <FormattedMessage id={"common.addTo"} />
                                            </Button>
                                        </Form.Item>:null
                                        }

                                    </>
                                )}
                            </Form.List>
                        </Form.Item>
                        :null
                }

                {
                    props.project?.info?.type === 1?
                        <>
                            <Form.Item label={formatMessage({id: 'common.status'})} name='status'
                                       rules={[{required: true}]}>
                                <Select>
                                    <Select.Option value={1}><FormattedMessage
                                        id='projects.envs.cohorts.status1'/></Select.Option>
                                </Select>
                            </Form.Item>
                        </>
                        :null
                }
            </Form>
        </Modal>
    )
}

export default ProjectInfoEnvAdd

const SelectInput = styled.div`
    margin: 6px 0 0 0;
    .ant-input-group-addon {
        background: unset !important;
        border: unset !important;
    }
`
