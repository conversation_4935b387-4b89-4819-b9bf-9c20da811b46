import {But<PERSON>, Col, Input, Row, Table} from "antd";
import React, {useEffect} from "react";
import {useMount, useSafeState} from "ahooks";
import {useIntl} from "react-intl";
import {useProjectRole} from "pages/main/projects/project-role-context";
import {useAuth} from "context/auth";
import {useFetch} from "hooks/request";
import {all, permissionExcel} from "api/menu";
import {list} from "api/projects_roles";
import {Result} from "types/result";
import {MenuPermission} from "types/menu";
import {permissionsDisable} from "../../../data/data";
import {projectSettingPermission} from "tools/permission";
import {Title as CustomTitle} from "components/title";
import {InsertDivider} from "../../../components/divider";
import {useGlobal} from "../../../context/global";
import {SearchOutlined} from "@ant-design/icons";


export const ProjectRoleMain = (props:any) => {
    const g = useGlobal();
    const ctx = useProjectRole();
    const auth = useAuth();
    const intl = useIntl();
    const { formatMessage } = intl;
    const [keyword, setKeyword] = useSafeState<any>("");
    const { runAsync: allRun, loading: allLoading } = useFetch(all, { manual: true });
    const {
        runAsync: permissionExcelRun,
        loading: permissionExcelLoading
    } = useFetch(permissionExcel, { manual: true });
    const [disable, setDisable] = useSafeState<boolean>(false);

    useEffect(() => {
        setDisable(props.project.status !== 0);
    }, [props.project]);

    const { runAsync, loading: listLoading } = useFetch(list, { manual: true });
    const getList = () => {
        runAsync({ projectId: props.project.id, "keywords": keyword.trim(), all: "1" }).then(
            (result: any) => {
                const res = result as Result;
                ctx.setRoles(res.data);
            }
        );
    };
    useEffect(getList, [keyword,ctx.refresh])
    useMount(() => {
        allRun().then((result: any) => {
            ctx.setMenu(result.data)
        })
    })
    const onSearch = (e: any) => {
        setKeyword(e.target.value);
    }

    function getChildren(n: any, currentRole: any) {
        let arr: any[] = []
        let researchAttribute = props.project.info.researchAttribute
        n.forEach(
            (value: MenuPermission) => {
                let menuItem: any = {}
                let role = ctx.rolesPool.find((it: any) => it.name === currentRole.name)
                if (value.researchAttribute.includes(researchAttribute)) {
                    if (!role ||
                        value.system === 0 ||
                        (value.system === 2 && [1, 2, 3, 6].includes(role?.type)) ||
                        (value.system === 1 && [6].includes(role?.type))) {
                        if (value.text) {
                            menuItem["title"] = formatMessage({id: value.text})
                            menuItem["key"] = value.text
                            menuItem["selectable"] = !!value.permissions
                        }
                        if (value.children) {
                            menuItem["children"] = getChildren(value.children, currentRole)
                        }
                        arr.push(menuItem)
                    }
                }
            }
        );
        return arr
    }

    const roleConfig = (record: any) => {
        ctx.setRole(record);
        let tree = getChildren(ctx.menu, record);
        ctx.setTree([...tree]);
        ctx.setPermissionsSet(new Set(record.permissions));
        ctx.setOldData(record.permissions);
        let role = ctx.rolesPool.find((it: any) => it.name === record.name);
        if (!role) {
            ctx.setDisableData([]);
            ctx.setRoleConfigVisible(true);
            return
        }
        let disableData = permissionsDisable.find((it: any) => it.type === role.type);
        if (disableData) {
            let arr = disableData.disables;
            if(record.scope === "site"){
                arr.push("operation.build.site.add");
            }
            ctx.setDisableData(arr);
        } else {
            let arr :any[] = [];
            if(record.scope === "site"){
                arr.push("operation.build.site.add")
            }
            ctx.setDisableData(arr);
        }
        ctx.setRoleConfigVisible(true);
    }

    const edit = (record: any) => {
        ctx.setRole(record)
        ctx.setEditVisible(true)
    }
    const add = () => {
        ctx.setEditVisible(true)
    }
    const exportExcel = () =>{
        permissionExcelRun({projectId: props.project.id}).then()
    }
    const operationBtn = (record:any) =>{
        let btns :any[] = []
            if (projectSettingPermission(props.project, auth, "operation.projects.main.setting.permission.edit")){
                btns.push(<Button style={{ borderLeft: 0, paddingLeft: 0 }} size="small" type="link" onClick={() => {
                    edit(record);
                }} disabled={disable}>{formatMessage({ id: "common.edit" })}</Button>)
            }
            if (projectSettingPermission(props.project, auth, "operation.projects.main.setting.permission.setting")){
                btns.push(<Button size="small" type="link" onClick={() => {
                    roleConfig(record);
                }} disabled={disable}>{formatMessage({ id: "common.role.setting" })}</Button>)
            }
        return InsertDivider(btns)
    }

    return (
        <>
            {
                <>
                    <Row>
                        <CustomTitle name={intl.formatMessage({id: "common.all.role"})}/>
                    </Row>
                    <Row justify="space-between" style={{ margin: "12px 0" }}>
                        <Input
                            placeholder={formatMessage({id: "role.name.enter"})}
                            onChange={onSearch}
                            style={{width: 200}}
                            suffix={<SearchOutlined style={{ color: "#BFBFBF" }} />}
                        />
                        <Col>
                            {
                                projectSettingPermission(props.project, auth, "operation.projects.main.setting.permission.add") &&
                                <Button
                                        onClick={add}
                                        disabled={disable}>{formatMessage({ id: "common.addTo" })}</Button>
                            }
                            {
                                (projectSettingPermission(props.project, auth, "operation.projects.main.setting.permission.export") &&
                                    props.project.info.researchAttribute === 1)?
                                <Button type="primary" loading={permissionExcelLoading}
                                        onClick={exportExcel} style={{marginLeft: 12}}>{formatMessage({id: "common.export"})}</Button>:null
                            }
                        </Col>
                    </Row>
                    <Table
                        loading={listLoading || allLoading}
                        size="small"
                        dataSource={ctx.roles}
                        rowKey={(record: any) => record.name}
                        pagination={false}
                    >
                        <Table.Column title={formatMessage({id: "common.serial"})} dataIndex="#" key="#" width={60}
                                        render={(text, record, index) => (index + 1)}/>
                        <Table.Column
                            title={formatMessage({id: "roles.name"})}
                            key="name"
                            dataIndex="name"
                            ellipsis
                        />
                        <Table.Column
                            title={formatMessage({id: "common.classification"})}
                            key="scope"
                            dataIndex="scope"
                            width={100}
                            ellipsis
                        />
                        <Table.Column
                            title={formatMessage({id: "common.template"})}
                            key="template"
                            dataIndex="template"
                            ellipsis
                            width={100}
                            render={(template, record, index) => {
                                return (
                                    template === 1 ?
                                        formatMessage({id: "common.common"})
                                        :
                                        "DTP"
                                );
                            }}
                        />
                        <Table.Column
                            title={formatMessage({id: "common.description"})}
                            key="description"
                            dataIndex="description"
                            ellipsis
                            render={(description, record, index) => {
                                return (
                                    description === '' ?
                                        "-"
                                        :
                                        description
                                );
                            }}
                        />
                        <Table.Column
                            title={formatMessage({id: "common.operation"})}
                            align={'left'}
                            key="operation"
                            dataIndex="operation"
                            width={g.lang === "zh"? 200:250}
                            ellipsis
                            render={
                                (value, record, index) => (operationBtn(record))
                            }
                        />
                    </Table>
                </>
            }
        </>
    )
}
