import {Button, Checkbox, Form, Input, message, Modal, notification, Select,} from 'antd'
import {useSafeState} from 'ahooks'
import {copyEnv, getCopyIsProd, getCopyIsProdData} from '../../../api/projects'
import {useAuth} from '../../../context/auth'
import {useFetch} from '../../../hooks/request'
import React, {useState} from 'react'
import {FormattedMessage, useIntl} from 'react-intl'
import {useGlobal} from '../../../context/global'
import {CloseCircleFilled, PlusOutlined,} from "@ant-design/icons";
import styled from "@emotion/styled";
import {CustomConfirmModal} from "../../../components/modal";


const ProjectInfoEnvCopy = props => {
    const {formatMessage} = useIntl()
    const auth = useAuth()
    const g = useGlobal()
    const [form] = Form.useForm()
    const [visible, setVisible] = useSafeState(false)
    const [env, setEnv] = useSafeState(null)
    const [selectCohorts, setSelectCohorts] = useSafeState([]);
    const projectType = props.project && props.project.info.type;
    const [envOptions, setEnvOptions] = useSafeState([]);
    const [selectOpen, setSelectOpen] = useSafeState(false);
    const [tickContain, setTickContain] = useState(true);

    const [isData, setIsData] = useState(false);
    const [isProdData, setIsProdData] = useState(false);

    const show = (projectId, envId,env) => {
        let initOptions = props.project?.envs
            .filter(it=>env.name !== it.name)
            .map(item=> {
            return {label:item.name,value:item.name}
        });
        setEnvOptions(initOptions);
        form.resetFields();
        setEnv({ projectId, envId,env });
        prodIsData(projectId, envId,env);
        setVisible(true);
        
    }
    // useImperativeHandle(props.bind, () => ({
    //     show: (projectId, envId,env) => {
    //         let initOptions = props.project?.envs
    //             .filter(it=>env.name !== it.name)
    //             .map(item=> {
    //             return {label:item.name,value:item.name}
    //         });
    //         setEnvOptions(initOptions)
    //         form.resetFields()
    //         setEnv({ projectId, envId,env })
    //         setVisible(true)
    //     }
    // }))

    const {runAsync: runCopyEnv, loading: submitting} = useFetch(
        copyEnv, 
        { 
            manual: true,
            onError: (err) => {
                form.resetFields();
                err.json().then((data) =>
                    notification.open({
                        message: (
                            <div
                                style={{
                                    fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                    fontSize: "14px",
                                }}
                            >
                                <CloseCircleFilled
                                    style={{
                                        color: "#F96964",
                                        paddingRight: "8px",
                                    }}
                                />
                                {data.msg}
                            </div>
                        ),
                        // description: (
                        //     <div
                        //         style={{
                        //             paddingLeft: "20px",
                        //             color: "#646566",
                        //         }}
                        //     >
                        //         {data.msg}
                        //     </div>
                        // ),
                        duration: 5,
                        placement: "top",
                        style: {
                            width: "720px",
                            // height: "88px",
                            background: "#FEF0EF",
                            borderRadius: "4px",
                        },
                    })
                );
            },
          }

    )

    const { runAsync: getCopyIsProdRun, loading: getCopyIsProdLoading } = useFetch(getCopyIsProd, { manual: true });
    const { runAsync: getCopyIsProdDataRun, loading: getCopyIsProdDataLoading } = useFetch(getCopyIsProdData, { manual: true });

    const prodIsData = (projectId, envId,env) => {
        if(props.project?.envs?.find(item => item.id === envId)?.name !== "PROD" && form?.getFieldValue("envName") === "PROD"){
            getCopyIsProdRun({
                customerId: auth.customerId,
                projectId: projectId,
                envId: envId,
                envName: "PROD",
            }).then((result) => {
                setIsProdData(result.data);
            });
        }
    }

    const prodData = (projectId, envId,env) => {
        if(props.project?.envs?.find(item => item.id === envId)?.name === "PROD"){
            getCopyIsProdDataRun({
                customerId: auth.customerId,
                projectId: projectId,
                envId: envId,
            }).then((result) => {
                setIsData(result.data);
            });
        }
    }

    const onChangeIsProjectConfig = (e) => {
        setTickContain(e.target.checked);
        prodIsData(env.projectId, env.envId, env);
        prodData(env.projectId, env.envId, env);
    };

    const onChangeSelect = (visible) => {
        setSelectOpen(visible)
        prodIsData(env.projectId, env.envId, env);
        prodData(env.projectId, env.envId, env);
    };

    const hide = () => {
        setIsData(false);
        setIsProdData(false);
        setTickContain(true);
        setVisible(false);
        form.resetFields();
        
    }

    React.useImperativeHandle(props.bind, () => ({show}))

    const validator = {
        validator: (_, value) => {
            if (value 
                && value.trim().length > 0
                && props.project?.envs?.findIndex(item => item.name === value) === -1
            ) {
                return Promise.resolve()
            } else if (!value || value.trim().length === 0) {
                return Promise.reject(formatMessage({id: 'common.required.prefix'}) + formatMessage({id: 'projects.envs.new'}))
            } else {
                if(projectType === 1 && props.project?.envs?.find(item => item.id === env?.envId)?.name !== "PROD" && form?.getFieldValue("envName") !== "PROD"){
                    return Promise.reject(formatMessage({id: 'projects.envs.duplicate.name'}))
                } else {
                    return Promise.resolve()
                }
            }
        }
    }

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 4: 7 },
        },
        wrapperCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 20: 17 },
        },
    }
    const CustomEnvAdder = ceapr => {
        const [form] = Form.useForm()
        const onAdd = () => {
            form.validateFields().then((values) => {
                const v = values.env
                setEnvOptions(envOptions.concat({label: v, value: v}))
                ceapr.form.setFieldsValue({env: v})
                ceapr.setOpen(false)
            })
        }

        const validator = {
            validator: (_, value) => {
                if (value
                    && value.trim().length > 0
                    && props.project?.envs.findIndex(item => item.name === value) === -1
                    && envOptions.findIndex(item => item.value === value) === -1
                ) {
                    return Promise.resolve()
                } else if (!value || value.trim().length === 0) {
                    return Promise.reject(formatMessage({id: 'common.required.prefix'}) + formatMessage({id: 'projects.envs.name'}))
                } else {
                    return Promise.reject(formatMessage({id: 'projects.envs.duplicate.name'}))
                }
            }
        }

        return (
            <Form style={{margin: '0 10px'}} form={form}>
                <Form.Item name='env' rules={[validator]}>
                    <Input
                        placeholder={formatMessage({id: 'placeholder.input.common'})}
                        addonAfter={
                            <Button type='link' size='small' onClick={onAdd}>
                                <PlusOutlined /> <FormattedMessage id={'common.addTo'} />
                            </Button>
                        }
                        onBlur={e => {
                            if (!e.target.value || e.target.value.trim().length === 0) {
                                form.setFields([{name: 'env', errors: null}])
                            }
                        }}
                    />
                </Form.Item>
            </Form>
        )
    }
    return (
        <Modal
            centered
            width={600}
            destroyOnClose={true}
            maskClosable={false}
            
            title={formatMessage({id: 'common.copy'})}
            open={visible}
            onCancel={hide}
            confirmLoading={submitting}
            onOk={() => {
                form.validateFields().then(values => {
                    CustomConfirmModal({
                        title: formatMessage({id: 'common.confirm.copy'}),
                        okText: formatMessage({id: 'common.ok'}),
                        cancelText: formatMessage({id: 'common.cancel'}),
                        // okButtonProps: {submitting},
                        content: tickContain && isData && props.project?.envs?.find(item => item.id === env?.envId)?.name === "PROD"?formatMessage({id: 'env.copy.prod.isCopy.prompt.language.tips'}):"",
                        onOk: () =>
                            runCopyEnv({
                                customerId: auth.customerId,
                                projectId: env.projectId,
                                envId: env.envId,
                                ...values
                            }).then(data => {
                                message.success(data.msg);
                                props.refresh()
                                setVisible(false)
                                hide();              
                            })
                    })
                })
            }}
            // okButtonProps={{loading: submitting}}
            okText={formatMessage({id: 'common.ok'})}
        >
            <Form form={form} layout='horizontal' {...formItemLayout}>
                <Form.Item
                    label={formatMessage({id: 'projects.envs.current'})}
                >
                    <span>{props.project?.envs?.find(item => item.id === env?.envId)?.name}</span>
                </Form.Item>
                {
                    projectType !== 1?<Form.Item
                        name='cohortIds'
                        label={formatMessage({id: 'common.name'})}
                        required={true}
                    >
                        <Select className="full-width"
                                allowClear showSearch mode="multiple" value={selectCohorts}
                                filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                                onChange={setSelectCohorts}
                                showArrow={true}
                                placeholder={<span style={{color: "#1D2129"}}><FormattedMessage id="common.all"/></span>}
                        >
                            {
                                env?.env?.cohorts.map(
                                    it => (
                                        <Select.Option key={it.id} value={it.id}>
                                            {it.type === 0?it.name:it.name + " - " + it.reRandomName}
                                        </Select.Option>
                                    )
                                )
                            }
                        </Select>
                    </Form.Item> :null
                }
                <Form.Item
                    label={formatMessage({id: 'projects.envs.name'})}
                    name='envName'
                    rules={[{required: true}]}
                >
                    <Select
                        className="full-width"
                        open={selectOpen}
                        onDropdownVisibleChange={onChangeSelect}
                        allowClear
                        placeholder={formatMessage({id: 'placeholder.select.common'})}
                        dropdownRender={menu => (
                            <>
                                {envOptions && envOptions.length > 0 ? menu : null}
                                {/* <Divider style={{ margin: '8px 0' }} /> */}
                                {/* <div className={styles.customEnvAdder}><CustomEnvAdder form={form} setOpen={setSelectOpen} /></div> */}
                                <SelectInput><CustomEnvAdder form={form} setOpen={setSelectOpen} /></SelectInput>
                            </>
                        )}
                    >
                        {
                            envOptions.map(it =>
                                <Select.Option
                                    key={it.value}
                                    value={it.value}
                                >
                                    {it.label}
                                </Select.Option>
                            )
                        }
                    </Select>
                </Form.Item>
                <div>
                    <div style={{marginLeft: g.lang === "en"?114:92, width: "458px", height:"32px", backgroundColor:"#F8F9FA"}}>
                        <Form.Item initialValue={true} valuePropName='checked' style={{marginLeft: "8px"}} name="isContain">
                            <Checkbox value={tickContain} onChange={onChangeIsProjectConfig}>
                                {formatMessage({ id: "project.setting.cohort.copy.contain" })}
                            </Checkbox>
                        </Form.Item>
                    </div>
                    {
                        tickContain && props.project?.envs?.find(item => item.id === env?.envId)?.name !== "PROD" && form?.getFieldValue("envName") === "PROD" && !isProdData?
                        <div style={{marginTop: "20px", marginLeft: g.lang === "en"?114:92, width: "458px", height:"32px", backgroundColor:"#e8efff", display: "flex", alignItems: "center" }}>
                            <div style={{ paddingLeft: '8px' }}>
                                <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2725" width="16" height="16" style={{ verticalAlign:"middle",}}><path d="M512 83.2a428.8 428.8 0 1 1 0 857.6 428.8 428.8 0 0 1 0-857.6z m44.8 345.6h-89.6v256h-44.8v64h172.8v-64h-44.8l6.4-256z m-89.6 0h-38.4v64h44.8v-64zM512 256c-38.4 0-64 25.6-64 64s25.6 64 64 64 64-25.6 64-64-25.6-64-64-64z" fill="#2B74EA" p-id="2726"></path></svg>
                                <span style={{ paddingLeft: '8px' }}><FormattedMessage id="env.copy.prod.isCopy.prompt.language"/></span>
                            </div>
                        </div>:null
                    }
                </div>
            </Form>
        </Modal>
    )

}

export default ProjectInfoEnvCopy
const SelectInput = styled.div`
    margin: 6px 0 0 0;
    .ant-input-group-addon {
        background: unset !important;
        border: unset !important;
    }
`