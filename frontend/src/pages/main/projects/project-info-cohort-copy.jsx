import React, {useState} from 'react'
import {FormattedMessage, useIntl} from 'react-intl'
import {Button, Checkbox, Col, Form, Input, message, Modal, Row, Select, Space} from 'antd'
import {useSafeState} from 'ahooks'
import {useFetch} from '../../../hooks/request'
import {copyCohort} from '../../../api/projects'
import {useGlobal} from 'context/global'
import {AlertThresholdType, nilObjectId} from "../../../data/data";
import {CustomConfirmModal} from "../../../components/modal";
import {MinusCircleFilled, PlusOutlined} from "@ant-design/icons";
import {getProjectAttribute} from "../../../api/randomization";
import {TipInputNumber} from 'components/TipInput';

const ProjectInfoCohortCopy = (props) => {

    const g = useGlobal()
    const {formatMessage} = useIntl()

    const [title, setTitle] = useSafeState('')
    const [visible, setVisible] = useSafeState(false)
    const [projectId, setProjectId] = useSafeState(null)
    const [envId, setEnvId] = useSafeState(null)
    const [type, setType] = useSafeState(null)
    const [connectEdc, setConnectEdc] = useSafeState(null)
    const [pushMode, setPushMode] = useSafeState(0)
    const [cohorts, setCohorts] = useSafeState([])
    const [cohortName, setCohortName] = useSafeState(null)
    const [id, setId] = useSafeState(null)
    const [status, setStatus] = useSafeState(null)
    const [newStatus, setNewStatus] = useSafeState(null)
    const [tickContain, setTickContain] = useState(true);
    const [unUsedOptions, setUnUsedOptions] = useSafeState(AlertThresholdType)
    const [optionCount,setOptionCount] = useSafeState(0)
    const [form] = Form.useForm()
    const {runAsync: runCopyCohort,loading:copyCohortLoading} = useFetch(copyCohort, {manual: true})
    const show = (projectId, envId, type, connectEdc, cohorts, data, pushMode) => {
        setProjectId(projectId)
        setEnvId(envId)
        setCohortName(data.name)
        setType(type)
        setConnectEdc(connectEdc)
        setPushMode(pushMode)
        if (type === 3) {
            if (cohorts != null && cohorts.length > 0) {
                setCohorts(cohorts.map(it => ({label: it.name, value: it.id})))
            }
        }

        setVisible(true)
        if (data) {
            if (data.alertThresholds != null  && data.alertThresholds.length > 0){
                let unUse = AlertThresholdType.filter((it)=>{
                    return !data.alertThresholds.find((i) => i.type === it.id)
                })
                setUnUsedOptions(unUse)
                setOptionCount(data.alertThresholds.length)
            }
            setId(data.id)
            setTitle(formatMessage({id: 'common.copy'}))
            form.setFieldsValue({...data})
            if (data.lastId === nilObjectId) {
                form.setFieldsValue({lastId: null})
            }
            setStatus(data.status)
            setNewStatus(data.status)

        } else {
            setTitle(formatMessage({id: 'common.addTo'}))
            form.setFieldsValue({status: 1})   // 默认草稿
        }
    }
    const onChangeIsProjectConfig = (e) => {
        setTickContain(e.target.checked);
    };

    const hide = () => {
        setUnUsedOptions(AlertThresholdType)
        setOptionCount(0)
        setVisible(false)
        setProjectId(null)
        setEnvId(null)
        setType(null)
        setCohorts([])
        setId(null)
        setStatus(null)
        setNewStatus(null)
        form.resetFields()
        setTickContain(true);
    }

    const formChange = () => {
        setNewStatus(form.getFieldValue().status)
    }

    const save = () => {
        form.validateFields().then(values => {
                CustomConfirmModal({
                    title: formatMessage({id: 'tip.copy.title'}),
                    okText: formatMessage({id: 'common.ok'}),
                    cancelText: formatMessage({id: 'common.cancel'}),
                    okButtonProps: {loading: copyCohortLoading},
                    onOk: () => {
                        values.isContain = tickContain;
                        values.name = values.newName;
                        if (type !== 3) {
                            values.lastId = null;
                        }
                        runCopyCohort({
                            id: projectId,
                            envID: envId,
                            cohortID: id,
                            isContain: tickContain
                        }, {...values}).then((resp) => {
                            message.success(resp.msg)
                            props.refresh()
                            hide()
                        })
                    }
                })
            }
        )
    }

    React.useImperativeHandle(props.bind, () => ({show}))

    const handleChangeType = (e) => {
        let useType = form.getFieldValue("alertThresholds").map((v)=> v.type)
        let unUse = AlertThresholdType.filter((v) => !useType.includes(v.id))
        setUnUsedOptions(unUse.filter((value) => value.id !== e))
        if (e === 3){
            let capacity = form.getFieldValue("alertThresholds").find((v)=>v.type === 3).capacity;
            if (capacity != null){
                setNewCapacity(capacity);
            }
        }
    };

    const handleChangeCapacity = (value) => {
        let i = form.getFieldValue("alertThresholds").findIndex((v)=>v.type === 3);
        if (i !== -1){
            setNewCapacity(form.getFieldValue("alertThresholds")[i].capacity);
        }
    };

    const  addClick = (add, fields)=>{
        add();
        setOptionCount(optionCount+1)
    }

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 4: 6 },
        },
        wrapperCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 20: 19 },
        },
    }

    return (
        <Modal
            centered
            width={600}
            title={title}
            open={visible}
            onClose={hide}
            destroyOnClose={true}
            maskClosable={false}
            onOk={save}
            onCancel={hide}
            okText={formatMessage({id: 'common.ok'})}
        >
            <Form form={form} layout='horizontal' onValuesChange={formChange} {...formItemLayout}>
                <Form.Item label={formatMessage({id: 'common.name.current'})} name='name'>
                    {/* <Input allowClear disabled bordered={false}/> */}
                    <span>{cohortName}</span>
                </Form.Item>
                <Form.Item label={formatMessage({id: 'common.name.new'})} name='newName' rules={[{required: true}]}>
                    <Input allowClear placeholder={formatMessage({id: 'common.required.prefix'})}/>
                </Form.Item>
                {
                    connectEdc === 1 && pushMode === 1?
                        <Form.Item label={formatMessage({id: 'projects.envs.cohorts.factor'})} name='factor'
                                    rules={[{required: true}]}>
                            <Input placeholder={formatMessage({id: 'common.required.prefix'})} allowClear/>
                        </Form.Item>
                        : null
                }

                {
                    type === 3 ?
                        <Form.Item label={formatMessage({id: 'projects.envs.cohorts.stage'})} name='lastId'>
                            <Select placeholder={formatMessage({id: 'placeholder.select.common'})} options={cohorts} />
                        </Form.Item>
                        : null
                }
                {
                    status === 1 ?
                        <Form.Item label={formatMessage({id: 'common.status'})} name='status'
                                    rules={[{required: true}]}>
                            <Select>
                                <Select.Option value={1}><FormattedMessage
                                    id='projects.envs.cohorts.status1'/></Select.Option>
                                <Select.Option value={2}><FormattedMessage
                                    id='projects.envs.cohorts.status2'/></Select.Option>
                                <Select.Option value={5}><FormattedMessage
                                    id='projects.envs.cohorts.status5'/></Select.Option>
                                <Select.Option value={3}><FormattedMessage
                                    id='projects.envs.cohorts.status3'/></Select.Option>
                                <Select.Option value={4}><FormattedMessage
                                    id='projects.envs.cohorts.status4'/></Select.Option>
                            </Select>
                        </Form.Item> : null
                }
                {
                    status === 2 ?
                        <Form.Item label={formatMessage({id: 'common.status'})} name='status'
                                    rules={[{required: true}]}>
                            <Select>
                                <Select.Option value={1}><FormattedMessage
                                    id='projects.envs.cohorts.status1'/></Select.Option>
                                <Select.Option value={2}><FormattedMessage
                                    id='projects.envs.cohorts.status2'/></Select.Option>
                                <Select.Option value={5}><FormattedMessage
                                    id='projects.envs.cohorts.status5'/></Select.Option>
                                <Select.Option value={3}><FormattedMessage
                                    id='projects.envs.cohorts.status3'/></Select.Option>
                                <Select.Option value={4}><FormattedMessage
                                    id='projects.envs.cohorts.status4'/></Select.Option>
                            </Select>
                        </Form.Item> : null
                }
                {
                    status === 3 ?
                        <Form.Item label={formatMessage({id: 'common.status'})} name='status'
                                    rules={[{required: true}]}>
                            <Select>
                                <Select.Option value={1}><FormattedMessage
                                    id='projects.envs.cohorts.status1'/></Select.Option>
                                <Select.Option value={2}><FormattedMessage
                                    id='projects.envs.cohorts.status2'/></Select.Option>
                                <Select.Option value={5}><FormattedMessage
                                    id='projects.envs.cohorts.status5'/></Select.Option>
                                <Select.Option value={3}><FormattedMessage
                                    id='projects.envs.cohorts.status3'/></Select.Option>
                                <Select.Option value={4}><FormattedMessage
                                    id='projects.envs.cohorts.status4'/></Select.Option>
                            </Select>
                        </Form.Item> : null
                }
                {
                    status === 4 ?
                        <Form.Item label={formatMessage({id: 'common.status'})} name='status'
                                    rules={[{required: true}]}>
                            <Select>
                                <Select.Option value={1}><FormattedMessage
                                    id='projects.envs.cohorts.status1'/></Select.Option>
                                <Select.Option value={2}><FormattedMessage
                                    id='projects.envs.cohorts.status2'/></Select.Option>
                                <Select.Option value={5}><FormattedMessage
                                    id='projects.envs.cohorts.status5'/></Select.Option>
                                <Select.Option value={3}><FormattedMessage
                                    id='projects.envs.cohorts.status3'/></Select.Option>
                                <Select.Option value={4}><FormattedMessage
                                    id='projects.envs.cohorts.status4'/></Select.Option>
                            </Select>
                        </Form.Item> : null
                }
                {
                    status === 5 ?
                        <Form.Item label={formatMessage({id: 'common.status'})} name='status'
                                    rules={[{required: true}]}>
                            <Select>
                                <Select.Option value={1}><FormattedMessage
                                    id='projects.envs.cohorts.status1'/></Select.Option>
                                <Select.Option value={2}><FormattedMessage
                                    id='projects.envs.cohorts.status2'/></Select.Option>
                                <Select.Option value={5}><FormattedMessage
                                    id='projects.envs.cohorts.status5'/></Select.Option>
                                <Select.Option value={3}><FormattedMessage
                                    id='projects.envs.cohorts.status3'/></Select.Option>
                                <Select.Option value={4}><FormattedMessage
                                    id='projects.envs.cohorts.status4'/></Select.Option>
                            </Select>
                        </Form.Item> : null
                }
                {
                    id === null ?
                        <Form.Item label={formatMessage({id: 'common.status'})} name='status'
                                    rules={[{required: true}]}>
                            <Select>
                                <Select.Option value={1}><FormattedMessage
                                    id='projects.envs.cohorts.status1'/></Select.Option>
                            </Select>
                        </Form.Item> : null
                }
                {status !== newStatus && id != null ?
                    <Form.Item label={formatMessage({id: 'common.password'})} name='password'
                                rules={[{required: true}]}>
                        <Input.Password allowClear className='full-width'/>
                    </Form.Item> : null
                }
                <Form.Item
                    label={formatMessage({ id: "projects.envs.cohorts.reminder.thresholds.toplimit" })}
                    name="alertThresholds"
                >
                    <Form.List name="alertThresholds">
                        {(fields, { add, remove }) => (
                            <>
                                {fields.map((field, index) => (
                                    <Col key={field.key}>
                                        <Row>
                                            <Space
                                                key={field.key}
                                                style={{ display: "flex" }}
                                            >
                                                <Form.Item
                                                    rules={[{required: true, message: formatMessage({ id: 'placeholder.select.common' })}]}
                                                    {...field}
                                                    name={[field.name, "type"]}>
                                                    <Select style={{width: g.lang === "en"?180:150}} onChange={(e) => handleChangeType(e)}
                                                            placeholder={formatMessage({id: 'common.status'})}
                                                    >
                                                        {
                                                            AlertThresholdType.map((value) =>
                                                                <Select.Option disabled={!unUsedOptions.find((v) => v.id === value.id)} value={value.id}>{value.name}</Select.Option>
                                                            )
                                                        }
                                                    </Select>
                                                </Form.Item>
                                                <Form.Item
                                                    rules={[{required: true,message: (formatMessage({ id: 'placeholder.input.common' }))}]}
                                                    {...field}
                                                    name={[field.name, "capacity"]}
                                                >
                                                    <TipInputNumber
                                                        onChange={(e)=>handleChangeCapacity(e)} style={{width: g.lang === "en"?70:150}} placeholder={formatMessage({id: 'projects.envs.cohorts.capacity.value'})} precision={0} min={0}
                                                        step={1}
                                                    />
                                                    {/* <InputNumber onChange={(e)=>handleChangeCapacity(e)} style={{width: g.lang === "en"?70:150}} placeholder={formatMessage({id: 'placeholder.input.common'})} precision={0} min={0}
                                                                 step={1}/> */}
                                                </Form.Item>
                                                <Form.Item
                                                    {...field}
                                                    name={[field.name, "thresholds"]}
                                                >
                                                    <TipInputNumber
                                                        style={{width: g.lang === "en"?94:114}} placeholder={formatMessage({id: 'projects.envs.cohorts.reminder.thresholds'})} addonAfter={<span>%</span>} allowClear precision={0} min={0}
                                                        max={100}
                                                    />
                                                    {/* <InputNumber style={{width: g.lang === "en"?94:114}} placeholder={formatMessage({id: 'projects.envs.cohorts.reminder.thresholds'})} addonAfter={<span>%</span>} allowClear precision={0} min={0}
                                                                 max={100}/> */}
                                                </Form.Item>
                                                <MinusCircleFilled
                                                    className="iconfont" width={16} height={16}
                                                    onClick={() => {
                                                        remove(field.name)
                                                        let useType = form.getFieldValue("alertThresholds").map((v)=> v.type)
                                                        let unUse = AlertThresholdType.filter((v) => !useType.includes(v.id))
                                                        setUnUsedOptions(unUse)
                                                        setOptionCount(optionCount-1)
                                                    }}
                                                    style={{marginLeft: 6, marginBottom:16,  cursor: "pointer", color: "#fe5b5a"}}/>
                                            </Space>
                                        </Row>
                                    </Col>
                                ))}
                                {(optionCount < AlertThresholdType.length)? <Form.Item style={{ marginBottom: 0 }}>
                                    <Button
                                        type="dashed"
                                        onClick={() => addClick(add, fields)}
                                        block
                                        icon={<PlusOutlined />}
                                    >
                                        <FormattedMessage id={"common.addTo"} />
                                    </Button>
                                </Form.Item>:null
                                }

                            </>
                        )}
                    </Form.List>
                </Form.Item>
                <div style={{marginLeft: g.lang === "en"?114:92, width: g.lang === "en"?"430px":"460px",height:"32px", backgroundColor:"#F8F9FA"}}>
                    <Form.Item style={{marginLeft: "8px"}} name="isContain">
                        <Checkbox checked={tickContain} onChange={onChangeIsProjectConfig}>
                            {formatMessage({ id: "project.setting.cohort.copy.contain" })}
                        </Checkbox>
                    </Form.Item>
                </div>
            </Form>
        </Modal>
    )
}


export default ProjectInfoCohortCopy
