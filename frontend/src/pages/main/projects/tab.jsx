import React, { useEffect, useRef, useState } from "react";
import {
  <PERSON><PERSON>,
  Checkbox,
  Col,
  DatePicker,
  Divider,
  Drawer,
  Form,
  Input,
  InputNumber,
  Layout,
  message,
  Modal,
  Popover,
  Radio,
  Row,
  Select,
  Space,
  Switch,
  Table,
  Tabs, TimePicker,
  Tooltip, Typography,
} from "antd";
import { FormattedMessage, useIntl } from "react-intl";
import { useAuth } from "../../../context/auth";
import { useFetch } from "../../../hooks/request";
import { updateProject, updateProjectType } from "../../../api/projects";
import { useMount, useSafeState } from "ahooks";
import {
  EdcSupplier,
  projectStatus,
  projectTypes,
  projectUtc, WeekDay, WeekDayIntl,
} from "../../../data/data";
import {
  CalendarOutlined,
  CloseOutlined,
  ExclamationCircleFilled,
  InfoCircleFilled,
  PlusOutlined,
  QuestionCircleFilled,
  SearchOutlined,
} from "@ant-design/icons";
import { useGetState } from "ahooks";
import {
  permissions,
  projectSettingButtonPermission,
  projectSettingPermission,
} from "../../../tools/permission";
import styled from "@emotion/styled";
import { useGlobal } from "../../../context/global";
import { CustomConfirmModal } from "components/modal";
import { Title as CustomTitle } from "components/title";
import ProjectInfo from "./project-info";
import { ProjectRole } from "./project_role";
import { Md5 } from "ts-md5";
import CaretDownBlue from "../../../images/caretdownblue.svg";
import CaretDownGreen from "../../../images/caretdowngreen.svg";
import CaretDownYellow from "../../../images/caretdownyellow.svg";
import CaretDownRed from "../../../images/caretdownred.svg";
import dayjs, {Dayjs} from "dayjs";
import { Title } from "../../../components/title";
import moment from "moment";
import { useWatch } from "antd/lib/form/Form";
import { ProjectNotice } from "./project-notice";
import { useProjects } from "./context";
import { UnblindingControlView } from "./unblinding";
import _ from "lodash";
import { timezones } from "../../../data/timezone";
// import { Timezone } from "../../../types/timezone";
import { getTimezoneOffset, getTimezonesWithOffsets } from "../../../utils/timezones";
import { SelectUI } from "../../../components/SelectUi";

export const Tab = (props) => {
  const { Text } = Typography;
  const searchInput = useRef();
  const g = useGlobal();
  const intl = useIntl();
  const projectCtx = useProjects();
  const { formatMessage } = intl;
  const auth = useAuth();
  const { TabPane } = Tabs;
  const [userData, setUserData, getUserData] = useGetState([]);
  // 缓存用户数据，用于还原撤销修改前的状态
  const [userDataTmp, setUserDataTmp] = useSafeState([]);
  const [edit, setEdit] = useSafeState(false);
  const [modify, setModify] = useSafeState(false);
  const [modifyType, setModifyType] = useSafeState(null);
  const [edcPush, setEdcPush] = useSafeState(false);
  // const [unblindingMethod, setUnblindingMethod] = useSafeState(false);
  // const [unblindingType, setUnblindingType] = useSafeState(false);
  // const [pvUnblindingType, setPvUnblindingType] = useSafeState(false);
  const [unblindingControl, setUnblindingControl] = useSafeState(0);
  const [orderApprovalControl, setOrderApprovalControl] = useSafeState(0);
  const [isModalVisible, setIsModalVisible] = useSafeState(false);
  const [learningShow, setLearningShow] = useSafeState(false);
  const [systemCoursesShow, setSystemCoursesShow] = useSafeState(false);
  const [systemCoursesValue, setSystemCoursesValue] = useSafeState(false);
  const [needSystemCoursesValue, setNeedSystemCoursesValue] = useSafeState(false);
  const [registerPushValue, setRegisterPushValue] = useSafeState(false);
  const [updateRandomFrontPushValue, setUpdateRandomFrontPushValue] = useSafeState(false);
  const [updateRandomAfterPushValue, setUpdateRandomAfterPushValue] = useSafeState(false);
  const [dispensingPushValue, setDispensingPushValue] = useSafeState(false);
  const [screenPushValue, setScreenPushValue] = useSafeState(false);
  const [randomPushValue, setRandomPushValue] = useSafeState(false);
  const [randomBlockPushValue, setRandomBlockPushValue] = useSafeState(false); // 分层不一致是否阻断随机
  const [blockSwitch, setBlockSwitch] = useSafeState(false); // 阻断的开关
  const [formRandomBlockPushValue, setFormRandomBlockPushValue] = useSafeState(false); // 表单不一致是否阻断随机
  const [cohortRandomBlockPushValue, setCohortRandomBlockPushValue] = useSafeState(false); // cohort不一致是否阻断随机
  const [needLearningValue, setNeedLearningValue] = useSafeState(false);
  const [raceabilityCodeShow, setTraceabilityCodeShow] = useSafeState(false);
  const [form] = Form.useForm();
  const [projectChangeStatus, setProjectChangeStatus] = useSafeState([]);
  const [bfihv, setBfihv] = useSafeState(""); // bussiness function initial hash value
  const [bfChanged, setBfChanged] = useSafeState(false); // bussiness function changed
  const [dfihv, setDfihv] = useSafeState(""); // docking function initial hash value
  const [dfChanged, setDfChanged] = useSafeState(false); // docking function changed
  const [statusHover, setStatusHover] = useSafeState(false);
  const [statusModalVisible, setStatusModalVisible] = useSafeState(false);
  const [status, setStatus] = useSafeState();
  const cycleTime = useWatch(["info", "cycleTime"], form);
  const [start, setStart] = useState(false);
  const [iconColor, setIconColor] = useSafeState(0);
  const [oldConnectEdc, setOldConnectEdc] = useSafeState(0);
  const [docking, setDocking] = useState(false);
  const [orderCheck, setOrderCheck] = useState(0)
  let [projectData, setProjectData] = useSafeState({
    id: "",
    status: 0,
    customerName: "",
    administrators: [],
    info: {
      type: 1,
      name: "",
      number: "",
      phone: "",
      sponsor: "",
      connectEdc: 2,
      synchronizationMode: 1,
      pushRules: 2,
      pushScenario: {
        registerPush: false,
        updateRandomFrontPush: false,
        updateRandomAfterPush: false,
        randomPush: false,
        randomBlockPush: false,
        formRandomBlockPush: false,
        cohortRandomBlockPush: false,
        dispensingPush: false,
        screenPush: false,
      },
      description: "",
      timeZone: 0,
      timeZoneStr: "0",
      systemCourses: 2,
      needSystemCourses: 2,
      connectLearning: 2,
      needLearning: 2,
      needLearningEnv: ["PROD"],
      orderCheck: 0,
      orderCheckDay: WeekDay.map(it => it.value),
      orderCheckTime: "08:00",
      orderConfirmation: 0,
      deIsolationApproval: 0,
      researchAttribute: 0,
      unblindingControl: 0,
      unblindingType: 0,
      pvUnblindingType: 0,
      ipUnblindingType: 0,
      ipUnblindingSms: 0,
      ipUnblindingProcess: 0,
      unblindingSms: 0,
      unblindingProcess: 0,
      unblindingCode: 0,
      pvUnblindingSms: 0,
      pvUnblindingProcess: 0,
      orderApprovalControl: 0,
      orderApprovalSms: 0,
      orderApprovalProcess: 0,
      cycleTime: [],
      // plannedCases:null,
      edcSupplier: null,
      visitRandomization: [],
    },
  });
  const [tabsChangeKey, setTabsChangeKey] = useSafeState("1");
  const { runAsync: updateProjectRunAsync, loading: updateProjectLoading } =
      useFetch(updateProject, {
        manual: true,
      });
  const [activeKey, setActiveKey] = useSafeState("1");
  const [disable, setDisable] = useSafeState(false);
  const {
    runAsync: updateProjectTypeRunAsync,
    loading: updateProjectTypeLoading,
  } = useFetch(updateProjectType, {
    manual: true,
  });
  const [edcSupplier, setEdcSupplier] = useSafeState(0);
  const [oldData, setOldData] = useSafeState(null);

  const [send, setSend] = useSafeState(true);

  const [tzOptions, setTzOptions] = useSafeState([]);
  const [tzs, setTzs] = useSafeState([]);

  useMount(() => {
      setTzOptions(timezones.map((it) => ({ ...it, offset: getTimezoneOffset(it.location) })));
      setTzs(getTimezonesWithOffsets());
  });

  const formChange = () => {
    if (activeKey === "1") {
      const a = _.cloneDeep(oldData);
      // console.log("1===" + JSON.stringify(a));
      let b = _.cloneDeep(form.getFieldsValue());
      // console.log("2===" + JSON.stringify(b));
      if (!compareObjects(a, b)) {
        setSend(false);
      } else {
        setSend(true);
      }

    }
  };

  //比较两个JavaScript对象是否相同
  function compareObjects(obj1, obj2) {
    for (let key in obj1) {
      if (obj2.hasOwnProperty(key) && obj2[key] !== undefined) {
        if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
          if (!arraysAreEqual(obj1[key], obj2[key])) {
            return false;
          }
        } else if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
          if (!compareObjects(obj1[key], obj2[key])) {
            return false;
          }
        } else {
          if (obj1[key] !== obj2[key]) {
            return false;
          }
        }
      }
    }
    return true;
  }

  //比较两个数组是否相同
  function arraysAreEqual(arr1, arr2) {
    // 检查数组长度是否相同
    if (arr1.length !== arr2.length) {
      return false;
    }

    const a = _.cloneDeep(arr1);
    const b = _.cloneDeep(arr2);
    // 将数组转换为字符串并比较
    const str1 = JSON.stringify(a.sort());
    const str2 = JSON.stringify(b.sort());

    return str1 === str2;
  }


  const { confirm } = Modal;

  const onModifyChange = (value) => {
    setModifyType(value);
  };

  useEffect(() => {
    setDisable(projectData.status !== 0);
  }, [projectData, unblindingControl]);

  const onTabsChange = (key) => {
    // console.log(key);
    setTabsChangeKey(key)
  };

  const handleChange = (value) => {
    setEdcSupplier(value);
    if (value === 2) {
      const fieldsValue = form.getFieldsValue();
      form.setFieldsValue({
        ...fieldsValue,
        info: {
          ...fieldsValue.info,
          pushMode: 2
        }
      });
    } else {
      const fieldsValue = form.getFieldsValue();
      form.setFieldsValue({
        ...fieldsValue,
        info: {
          ...fieldsValue.info,
          pushMode: 1
        }
      });
    }
  };

  const deleteRow = (record, index, i) => {
    // 删除行数据
    // 更新data状态变量
    deleteVisitConfigs(index, i);
  };

  const addMedicine = (index) => {
    // 调用 updateVisitConfigs 函数来更新 visitRandomization 中的 visitConfigs 数组
    addVisitConfigs(index);
  };

  const addVisitConfigs = (index) => {
    const updatedVisits = [...projectData.info.visitRandomization.visits];
    // updatedVisits[index].visitConfigs = newVisitConfigs;
    if (updatedVisits[index].visitConfigs === null) {
      updatedVisits[index].visitConfigs = [];
    }
    updatedVisits[index].visitConfigs.push({
      visitCode: null,
      folderOid: null,
      formOid: null,
      ipNumberOid: null,
      dispenseTimeOid: null,
    });
    const updatedProjectData = {
      ...projectData,
      info: {
        ...projectData.info,
        visitRandomization: {
          ...projectData.info.visitRandomization,
          visits: updatedVisits,
        },
      },
    };
    setProjectData(updatedProjectData);
  };

  const deleteVisitConfigs = (index, i) => {
    const updatedVisits = [
      ...form.getFieldsValue().info.visitRandomization.visits,
    ];

    for (let j = 0; j < updatedVisits.length; j++) {
      if (updatedVisits[j] === null || updatedVisits[j] === undefined) {
        updatedVisits[j] = projectData.info.visitRandomization.visits[j];
      } else {
        updatedVisits[j].name = projectData.info.visitRandomization.visits[j].name
      }
    }
    updatedVisits[index].visitConfigs.splice(i, 1);
    const updatedProjectData = {
      ...projectData,
      info: {
        ...projectData.info,
        visitRandomization: {
          ...projectData.info.visitRandomization,
          visits: updatedVisits,
        },
      },
    };
    updatedProjectData.info.connectEdc = 1;
    updatedProjectData.info.pushMode = 2;
    updatedProjectData.info.visitRandomization.randomizations = [
      ...form.getFieldsValue().info.visitRandomization.randomizations,
    ];
    updatedProjectData.info.edcUrl = form.getFieldValue([
      "info",
      "edcUrl",
    ])
    setProjectData(updatedProjectData);
    setEdcSupplier(2);
    updatedProjectData.info.edcSupplier = 2;
    form.setFieldsValue({ ...updatedProjectData });
  };

  const EditableCell = ({
                          editing,
                          dataIndex,
                          title,
                          inputType,
                          record,
                          index,
                          children,
                          ...restProps
                        }) => {
    const inputNode = inputType === "number" ? <InputNumber /> : <Input />;
    return (
        <td {...restProps}>
          {editing ? (
              <Form.Item
                  name={dataIndex}
                  style={{
                    margin: 0,
                  }}
                  rules={[
                    {
                      required: true,
                      message: `Please Input ${title}!`,
                    },
                  ]}
              >
                {inputNode}
              </Form.Item>
          ) : (
              children
          )}
        </td>
    );
  };

  // const deleteVisitConfigs = (index, i) => {
  //     const updatedVisits = [...form.getFieldsValue().info.visitRandomization.visits];
  //     updatedVisits[index].visitConfigs.splice(i, 1);
  //     const updatedProjectData = {
  //       ...projectData,
  //       info: {
  //         ...projectData.info,
  //         visitRandomization: {
  //           ...projectData.info.visitRandomization,
  //           visits: updatedVisits,
  //         },
  //       },
  //     };
  //     updatedProjectData.info.connectEdc = 1;
  //     updatedProjectData.info.pushMode = 2;
  //     updatedProjectData.info.visitRandomization.randomizations = [...form.getFieldsValue().info.visitRandomization.randomizations];
  //     setProjectData(updatedProjectData);
  //     setEdcSupplier(2);
  //     updatedProjectData.info.edcSupplier = 2;
  //     form.setFieldsValue({ ...updatedProjectData });
  // };

  const show = (project, users) => {
    setBfChanged(false);
    setDfChanged(false);
    setDocking(false);
    form.resetFields();
    setEdit(false);
    setModify(false);
    project.info.cycleTime = [
      project.info.startTime ? dayjs.unix(project.info.startTime) : null,
      project.info.endTime ? dayjs.unix(project.info.endTime) : null,
    ];
    // 订单核查时间
    setOrderCheck(project.info.orderCheck)
    if (!!project.info.orderCheckTime) {
      project.info.orderCheckTime = dayjs(project.info.orderCheckTime, "HH:mm")
    }
    setOldConnectEdc(project.info.connectEdc);
    setProjectData({ ...project });
    if (project.info.edcSupplier === 0) {
      setEdcSupplier(project.info.edcSupplier);
      project.info.edcSupplier = 1;
      project.info.pushMode = 1;
      project.info.synchronizationMode = 1;

    } else {
      setEdcSupplier(project.info.edcSupplier);
    }
    setUnblindingControl(project.info.unblindingControl);
    setOrderApprovalControl(project.info.orderApprovalControl);
    // if (
    //   project.info.unblindingSms === 1 ||
    //   project.info.unblindingProcess === 1
    // ) {
    //   setUnblindingMethod(true);
    // }

    if (project.info.needLearning === 0) {
      project.info.needLearning = 2;
    }
    if (
        !project.info.needLearningEnv ||
        project.info.needLearningEnv.length === 0
    ) {
      project.info.needLearningEnv = ["PROD"];
    }
    if (project.info.connectLearning === 1) {
      setLearningShow(true);
    } else {
      setLearningShow(false);
    }
    if (project.info.systemCourses === 1) {
      setSystemCoursesShow(true);
      setSystemCoursesValue(true);
    } else {
      setSystemCoursesShow(false);
      setSystemCoursesValue(false);
    }

    projectCtx.setProjectType(project.info.type);
    setDispensingPushValue(project.info.pushScenario.dispensingPush);
    setScreenPushValue(project.info.pushScenario.screenPush);
    setRandomBlockPushValue(project.info.pushScenario.randomBlockPush);
    setFormRandomBlockPushValue(project.info.pushScenario.formRandomBlockPush);
    setCohortRandomBlockPushValue(project.info.pushScenario.cohortRandomBlockPush);
    setRandomPushValue(project.info.pushScenario.randomPush);
    setRegisterPushValue(project.info.pushScenario.registerPush);
    setUpdateRandomAfterPushValue(project.info.pushScenario.updateRandomAfterPush);
    setUpdateRandomFrontPushValue(project.info.pushScenario.updateRandomFrontPush);

    setBlockSwitch(project.info.pushScenario.randomBlockPush || project.info.pushScenario.formRandomBlockPush || project.info.pushScenario.cohortRandomBlockPush)

    if (project.info.needSystemCourses === 1) {
      setNeedSystemCoursesValue(true);
    } else {
      setNeedSystemCoursesValue(false);
    }
    if (project.info.needLearning === 1) {
      setNeedLearningValue(true);
    } else {
      setNeedLearningValue(false);
    }
    if (project.info.connectAli === 1) {
      setTraceabilityCodeShow(true);
    } else {
      setTraceabilityCodeShow(false);
    }
    let admins = users
        .map((u) => {
          u.admin = project.administrators.findIndex((a) => a === u.id) > -1;
          return u;
        })
        .filter((u) => !u.deleted && u.status !== 2);
    setUserData(admins);
    setUserDataTmp(JSON.parse(JSON.stringify(admins)));
    // edc推送如果没有选择推送方式要给默认值
    if (project.info.connectEdc === 1) {
      if (project.info.pushMode === 0) {
        setEdcPush(false);
        project.info.pushMode = 1;
        project.info.synchronizationMode = 1;
        setDfChanged(true);
      } else {
        setEdcPush(true);
      }
    }
    form.setFieldsValue(project);
    // 计算业务功能初始hash值
    const info = project.info;
    const str =
        "" +
        info?.timeZoneStr +
        info?.orderCheck +
        !!info?.orderConfirmation +
        !!info?.deIsolationApproval +
        getOrderCheckHashStr(info) +
        admins?.map((u) => u.info.email + !!u.admin);
    setBfihv(Md5.hashStr(str));
    const dstr =
        "" +
        info?.synchronizationMode +
        info?.connectLearning +
        info?.needLearning +
        info?.needLearningEnv;
    setDfihv(Md5.hashStr(dstr));

    setIsModalVisible(true);
    if (project.status === 0) {
      setProjectChangeStatus([
        { value: 0, label: <FormattedMessage id="projects.status.progress" /> },
        { value: 1, label: <FormattedMessage id="projects.status.finish" /> },
        { value: 2, label: <FormattedMessage id="projects.status.close" /> },
      ]);
    } else if (project.status === 1) {
      setProjectChangeStatus([
        { value: 1, label: <FormattedMessage id="projects.status.finish" /> },
        { value: 2, label: <FormattedMessage id="projects.status.close" /> },
      ]);
    } else if (project.status === 2) {
      setProjectChangeStatus([
        { value: 2, label: <FormattedMessage id="projects.status.close" /> },
      ]);
    }
    setOldData(project);
  };
  const onEdit = () => {
    setEdit(true);
  };

  const onModify = () => {
    setModify(true);
  };

  const onUpdateStatusClose = () => {
    setStatusModalVisible(false);
  };

  const onUpdateStatusOk = (reason) => {
    projectData.status = status;
    if (reason) {
      projectData.info.statusReason = reason;
    }

    let obj = tzs.find(it => it.name === projectData.info.tz);
    const offsetHour = obj && obj.offset !== undefined ? obj.offset / 60 : 0;
    projectData.info.timeZoneStr = String(offsetHour);

    const updateData = {
      ...projectData,
      info: {
        ...projectData.info,
        orderCheckTime: getOrderCheckTime(projectData.info.orderCheckTime),
        startTime: projectData.info.cycleTime
            ? dayjs(projectData.info.cycleTime[0])
                .hour(0)
                .minute(0)
                .second(0)
                .unix()
            : null,
        endTime: projectData.info.cycleTime
            ? dayjs(projectData.info.cycleTime[1])
                .hour(0)
                .minute(0)
                .second(0)
                .unix()
            : null,
      }
    }
    updateProjectRunAsync({ id: projectData.id }, { ...updateData }).then(
        () => {
          setProjectData({ ...projectData });
          message.success(formatMessage({ id: "message.save.success" })).then();
          props.refresh();
        }
    );
    setStatusModalVisible(false);
  };

  const UpdateStatusModal = ({ open, status, close, ok }) => {
    let tips, content, placeholder;
    switch (status) {
      case 0:
        tips = formatMessage({ id: "projects.edit.status.progress.tips" });
        content = formatMessage({
          id: "projects.edit.status.progress.content",
        });
        placeholder = formatMessage({
          id: "projects.edit.status.progress.reason",
        });
        break;
      case 1:
        tips = formatMessage({ id: "projects.edit.status.finish.tips" });
        content = formatMessage({ id: "projects.edit.status.finish.content" });
        break;
      case 2:
        tips = formatMessage({ id: "projects.edit.status.close.tips" });
        content = formatMessage({ id: "projects.edit.status.close.content" });
        break;
      case 3:
        tips = formatMessage({ id: "projects.edit.status.pause.tips" });
        content = formatMessage({ id: "projects.edit.status.pause.content" });
        placeholder = formatMessage({
          id: "projects.edit.status.pause.reason",
        });
        break;
      case 4:
        tips = formatMessage({ id: "projects.edit.status.terminate.tips" });
        content = formatMessage({
          id: "projects.edit.status.terminate.content",
        });
        placeholder = formatMessage({
          id: "projects.edit.status.terminate.reason",
        });
    }

    const [form] = Form.useForm();

    return (
        <Modal
            centered
            visible={open}
            closable={false}
            okText={formatMessage({ id: "common.confirm-change" })}
            cancelText={formatMessage({ id: "common.cancel" })}
            onOk={() => {
              if (status !== 1 || status !== 2) {
                form.validateFields().then((values) => {
                  ok(values.reason);
                });
              } else {
                ok();
              }
            }}
            onCancel={close}
            destroyOnClose
            width={status === 1 || status === 2 ? 320 : 480}
        >
          <>
            <Row wrap={false}>
              <Col>
                <ExclamationCircleFilled
                    style={{
                      color: "#FFAE00",
                      height: 18,
                      width: 18,
                      marginRight: 10,
                    }}
                />
              </Col>
              <Col flex={"auto"}>
              <span
                  style={{ fontSize: 14, fontWeight: 500, lineHeight: "14px" }}
              >
                {tips}
              </span>
              </Col>
            </Row>
            <Row>
            <span
                style={{
                  marginLeft: 28,
                  marginTop: 10,
                  fontSize: 12,
                  color: "#4E5969",
                }}
            >
              {content}
            </span>
            </Row>
            {status === 1 || status === 2 ? null : (
                <div style={{ position: "relative", marginTop: 16 }}>
                  <Form form={form}>
                    <Form.Item
                        name={"reason"}
                        rules={[{ required: true, message: "" }]}
                    >
                      <Input.TextArea
                          rows={7}
                          showCount
                          maxLength={1000}
                          placeholder={placeholder}
                      ></Input.TextArea>
                    </Form.Item>
                  </Form>
                </div>
            )}
          </>
        </Modal>
    );
  };

  const setLearningShowFun = (e) => {
    if (e) {
      setLearningShow(true);
    } else {
      setLearningShow(false);
    }
  };

  const onSystemCoursesChange = (e) => {
    const tmpInfo = form.getFieldValue("info");
    if (e) {
      setSystemCoursesShow(true);
      setSystemCoursesValue(true);
      tmpInfo.systemCourses = 1;
    } else {
      setSystemCoursesShow(false);
      setSystemCoursesValue(false);
      tmpInfo.systemCourses = 2;
    }
    form.setFieldsValue({ ...projectData, info: tmpInfo });
  };

  const setTraceabilityCodeShowFun = (e) => {
    if (e) {
      setTraceabilityCodeShow(true);
    } else {
      setTraceabilityCodeShow(false);
    }
  };

  // const onUnblindControl = (value) => {
  //   if (value) {
  //     setUnblindingControl(1);
  //   } else {
  //     setUnblindingControl(0);
  //     setUnblindingMethod(false);
  //     setUnblindingType(false);
  //     setPvUnblindingType(false);

  //     const tmpInfo = form.getFieldValue("info");
  //     projectData.info.unblindingType = 0;
  //     projectData.info.unblindingSms = 0;
  //     projectData.info.unblindingCode = 0;
  //     projectData.info.unblindingProcess = 0;
  //     projectData.info.pvUnblindingType = 0;
  //     projectData.info.pvUnblindingSms = 0;
  //     projectData.info.pvUnblindingProcess = 0;
  //     tmpInfo.unblindingType = 0;
  //     tmpInfo.unblindingSms = 0;
  //     tmpInfo.unblindingCode = 0;
  //     tmpInfo.unblindingProcess = 0;
  //     tmpInfo.pvUnblindingType = 0;
  //     tmpInfo.pvUnblindingSms = 0;
  //     tmpInfo.pvUnblindingProcess = 0;
  //     form.setFieldsValue({ ...projectData, info: tmpInfo });
  //     handleOk();
  //   }
  // };

  const onOrderApprovalControl = (value) => {
    if (value) {
      setOrderApprovalControl(1);
    } else {
      setOrderApprovalControl(0);
      const tmpInfo = form.getFieldValue("info");
      projectData.info.orderApprovalSms = 0;
      projectData.info.orderApprovalProcess = 0;
      tmpInfo.orderApprovalSms = 0;
      tmpInfo.orderApprovalProcess = 0;
      form.setFieldsValue({ ...projectData, info: tmpInfo });
      // handleOk();
    }
  };
  const phoneNumberValidator = {
    validator: (rule, value, callback) => {
      if (value && value !== "") {
        const reg = /^[-,0-9 ]+$/;
        if (!reg.test(value)) {
          return Promise.reject(formatMessage({ id: "input.error.common" }));
        }
      }
      return Promise.resolve();
    },
  };

  // const unblindCountrolValidator = {
  //   validator: (rule, value, callback) => {
  //     let info = form.getFieldValue("info");
  //     if (info.unblindingCode === 0 && !unblindingMethod) {
  //       return Promise.reject(formatMessage({ id: "validator.msg.required" }));
  //     }
  //     return Promise.resolve();
  //   },
  // };

  // const unblindTypeValidator = {
  //   validator: (rule, value, callback) => {
  //     if (!pvUnblindingType && !unblindingType) {
  //       return Promise.reject(formatMessage({ id: "validator.msg.required" }));
  //     }
  //     return Promise.resolve();
  //   },
  // };

  // const unblindApprovalValidator = {
  //   validator: (rule, value, callback) => {
  //     let info = form.getFieldValue("info");
  //     if (info.unblindingSms === 0 && info.unblindingProcess === 0) {
  //       return Promise.reject(formatMessage({ id: "validator.msg.required" }));
  //     }
  //     return Promise.resolve();
  //   },
  // };

  // const pvUnblindApprovalValidator = {
  //   validator: (rule, value, callback) => {
  //     let info = form.getFieldValue("info");
  //     if (info.pvUnblindingSms === 0 && info.pvUnblindingProcess === 0) {
  //       return Promise.reject(formatMessage({ id: "validator.msg.required" }));
  //     }
  //     return Promise.resolve();
  //   },
  // };

  const getHashValueOnAdminChange = (data) => {
    const info = form.getFieldsValue()?.info;
    const userStr = "" + data?.map((u) => u.info.email + !!u.admin);
    const str =
        "" +
        info?.timeZoneStr +
        info?.orderCheck +
        !!info?.orderConfirmation +
        !!info?.deIsolationApproval +
        getOrderCheckHashStr(info) +
        userStr;
    return Md5.hashStr(str);
  };

  const setBfChangedOnAdminChange = (data) => {
    const hash = getHashValueOnAdminChange(data);
    if (hash !== bfihv) {
      setBfChanged(true);
    } else {
      setBfChanged(false);
    }
  };

  const addAdmin = (uid) => {
    const ud = getUserData();
    const a = ud.findIndex((it) => it.id === uid);
    const _data = [...ud];
    _data[a].admin = true;
    setUserData(_data);

    setBfChangedOnAdminChange(_data);
  };

  const removeAdmin = (uid) => {
    const ud = getUserData();
    const a = ud.findIndex((it) => it.id === uid);
    const _data = [...ud];
    _data[a].admin = false;
    setUserData(_data);

    setBfChangedOnAdminChange(_data);
  };

  const handleOkModify = () => {
    if (modifyType === null) {
      message.warn(
          formatMessage({ id: "placeholder.select.common.project.type" })
      );
      return;
    }
    let values = form.getFieldsValue(true);
    updateProjectTypeRunAsync({
      id: values.id,
      modifyType: modifyType,
    }).then(() => {
      values.info.type = modifyType;
      setModify(false);
      props.refresh();
      const pd = { ...projectData, ...values };
      setProjectData(pd);
      message.success(formatMessage({ id: "message.save.success" })).then();
    });
  };

  // 这里只修改 info.onConnectEdc 字段的值，其余字段值不能影响
  const onConnectEdc = (e) => {
    setDocking(true);
    let tmpInfo = {};
    if(form.getFieldValue("info") !== undefined && form.getFieldValue("info") !== null){
      tmpInfo = form.getFieldValue("info");
    }
    // form.setFieldValue(["info", "pushMode"], "1")
    // form.setFieldValue(["info", "synchronizationMode"], "1")
    projectData.info.connectEdc = 1;
    projectData.info.pushMode = 1;
    projectData.info.synchronizationMode = 2;
    projectData.info.edcSupplier = 1;
    projectData.info.pushRules = 2;
    tmpInfo.connectEdc = 1;
    tmpInfo.pushMode = 1;
    tmpInfo.synchronizationMode = 2;
    tmpInfo.edcSupplier = 1;
    tmpInfo.pushRules = 2;
    setProjectData(projectData);
    form.setFieldsValue({ ...projectData, info: tmpInfo });
  };

  // 这里只修改 info.onConnectEdc 字段的值，其余字段值不能影响
  const onClinflashEdc = (tmpInfo) => {
    if(docking){
      CustomConfirmModal({
        title: formatMessage({ id: "model.note.title.edc" }),
        content: formatMessage({ id: "model.note.content.edc" }),
        okText: formatMessage({ id: "common.ok" }) + " ",
        cancelText: formatMessage({ id: "common.cancel" }) + " ",
        onOk: () => {
          dockingSave();
        },
        onCancel: () => {
          // handleCancelEdit();
        },
      });
    }else{
      dockingSave();
    }
  };

  // 外部对接保存的方法
  const dockingSave = () => {
    const admins = getUserData();
    const adminIds = getUserData()
        .filter((u) => u.admin)
        .map((u) => u.id);
    form.setFieldsValue({ administrators: adminIds });
    let values = form.getFieldsValue(true);

    let obj = tzs.find(it => it.name === values.info.tz);
    const offsetHour = obj && obj.offset !== undefined ? obj.offset / 60 : 0;
    values.info.timeZoneStr = String(offsetHour);

    updateProjectRunAsync(
        { id: values.id },
        {
          ...values,
          info: {
            ...values.info,
            orderCheckTime: getOrderCheckTime(values.info.orderCheckTime),
            startTime: values.info.cycleTime
                ? dayjs(values.info.cycleTime[0])
                    .hour(0)
                    .minute(0)
                    .second(0)
                    .unix()
                : null,
            endTime: values.info.cycleTime
                ? dayjs(values.info.cycleTime[1])
                    .hour(0)
                    .minute(0)
                    .second(0)
                    .unix()
                : null,
          },
        }
    ).then(() => {
      if (activeKey === "1") setEdit(false);
      if (activeKey === "2") setEdit(false)
      if (activeKey === "3") {
        setEdcPush(true);
        setEdit(false);
        setOldConnectEdc(projectData.info.connectEdc);
      }
      const pd = { ...projectData, ...values };
      setProjectData(pd);
      setUserDataTmp(JSON.parse(JSON.stringify(getUserData())));
      if(!docking){
        message.success(formatMessage({ id: "message.save.success" })).then();
      }

      props.refresh();

      // 重新计算业务功能初始hash值, 重置业务功能修改状态
      const info = pd.info;
      const str =
          "" +
          info?.timeZoneStr +
          info?.orderCheck +
          !!info?.orderConfirmation +
          !!info?.deIsolationApproval +
          getOrderCheckHashStr(info) +
          admins?.map((u) => u.info.email + !!u.admin);
      setBfihv(Md5.hashStr(str));
      setBfChanged(false);
      const dstr =
          "" +
          info?.synchronizationMode +
          info?.connectLearning +
          info?.needLearning +
          info?.needLearningEnv;
      setDfihv(Md5.hashStr(dstr));
      setDfChanged(false);
      if(docking){
        message.success(formatMessage({ id: "model.note.start.success" })).then();
      }
    });
  };

  const getOrderCheckHashStr = (info) => {
    if (info?.orderCheck !== 1) return ""
    const time  = info?.orderCheckTime ? dayjs(info?.orderCheckTime, "HH:mm").format("HH:mm") : ""
    const days = (info?.orderCheckDay || []).sort().join("")
    return time + days
  }

  const onPushModeChange = () => {
    const tmpInfo = form.getFieldValue("info");
    if(tmpInfo.pushMode == 2 && tmpInfo.pushRules == 0){
      tmpInfo.pushRules = 1;
      form.setFieldsValue({ info: tmpInfo });
    }
  };

  const getOrderCheckTime = (orderCheckTime) => {
    if (!orderCheckTime) return ""
    const format = "HH:mm"
    if (dayjs(orderCheckTime, format).isValid()) {
      return dayjs(orderCheckTime, format).format(format)
    }
    if (moment(orderCheckTime, format).isValid()) {
      return moment(orderCheckTime, format).format(format)
    }
    return ""
  }

  const handleOk = () => {
    form.validateFields().then((v) => {
      const admins = getUserData();
      const adminIds = getUserData()
          .filter((u) => u.admin)
          .map((u) => u.id);
      form.setFieldsValue({ administrators: adminIds });
      let values = form.getFieldsValue(true);
      values.info.edcUrl = values.info.edcUrl.trim();

      const tmpInfo = form.getFieldValue("info");
      if(tmpInfo.connectEdc === 1 &&
          tmpInfo.edcSupplier === 1 &&
          tmpInfo.pushMode === 2 &&
          !tmpInfo.pushScenario.registerPush &&
          !tmpInfo.pushScenario.updateRandomFrontPush &&
          !tmpInfo.pushScenario.updateRandomAfterPush &&
          !tmpInfo.pushScenario.randomPush &&
          !tmpInfo.pushScenario.randomBlockPush &&
          !tmpInfo.pushScenario.dispensingPush &&
          !tmpInfo.pushScenario.screenPush){
        return message.error(formatMessage({ id: "projects.push.scene.select" }));
      }

      if(tmpInfo.connectEdc === 1 &&
          tmpInfo.edcSupplier === 1 &&
          tmpInfo.pushMode === 2 &&
          blockSwitch &&
          !tmpInfo.pushScenario.randomBlockPush &&
          !tmpInfo.pushScenario.formRandomBlockPush &&
          !tmpInfo.pushScenario.cohortRandomBlockPush){
        return message.error(formatMessage({ id: "projects.push.scene.block.select" }));
      }

      if (activeKey === "3" && tmpInfo.edcSupplier === 1 && tmpInfo.connectEdc === 1 && tabsChangeKey === "1") {
        onClinflashEdc(tmpInfo);
      } else {
        if (activeKey === "5" && tmpInfo.unblindingControl === 1) {
          console.log(tmpInfo)
          if (!tmpInfo.unblindingType && !tmpInfo.pvUnblindingType && !tmpInfo.ipUnblindingType) {
            return message.error(formatMessage({ id: "project.setting.unblind.error1" }));
          }
          if (
              !!tmpInfo.unblindingType &&
              !tmpInfo.unblindingCode &&
              !tmpInfo.unblindingProcess &&
              !tmpInfo.unblindingSms
          ) {
            return message.error(formatMessage({ id: "project.setting.unblind.error2" }));
          }
          if (
              !!tmpInfo.pvUnblindingType &&
              !tmpInfo.pvUnblindingProcess &&
              !tmpInfo.pvUnblindingSms
          ) {
            return message.error(formatMessage({ id: "project.setting.unblind.error3" }));
          }
        }

        let obj = tzs.find(it => it.name === values.info.tz);
        const offsetHour = obj && obj.offset !== undefined ? obj.offset / 60 : 0;
        values.info.timeZoneStr = String(offsetHour);

        updateProjectRunAsync(
            { id: values.id },
            {
              ...values,
              info: {
                ...values.info,
                orderCheckTime: getOrderCheckTime(values.info.orderCheckTime),
                startTime: values.info.cycleTime
                    ? dayjs(values.info.cycleTime[0])
                        .hour(0)
                        .minute(0)
                        .second(0)
                        .unix()
                    : null,
                endTime: values.info.cycleTime
                    ? dayjs(values.info.cycleTime[1])
                        .hour(0)
                        .minute(0)
                        .second(0)
                        .unix()
                    : null,
              },
            }
        ).then(() => {
          if (activeKey === "1") setEdit(false);
          if (activeKey === "2") setEdit(false)
          if (activeKey === "3") {
            setEdcPush(true);
            setEdit(false);
            setOldConnectEdc(projectData.info.connectEdc);
          }
          const pd = { ...projectData, ...values };
          setProjectData(pd);
          setUserDataTmp(JSON.parse(JSON.stringify(getUserData())));
          message.success(formatMessage({ id: "message.save.success" })).then();
          props.refresh();

          // 重新计算业务功能初始hash值, 重置业务功能修改状态
          const info = pd.info;
          const str =
              "" +
              info?.timeZoneStr +
              info?.orderCheck +
              !!info?.orderConfirmation +
              !!info?.deIsolationApproval +
              getOrderCheckHashStr(info) +
              admins?.map((u) => u.info.email + !!u.admin);
          setBfihv(Md5.hashStr(str));
          setBfChanged(false);
          const dstr =
              "" +
              info?.synchronizationMode +
              info?.connectLearning +
              info?.needLearning +
              info?.needLearningEnv;
          setDfihv(Md5.hashStr(dstr));
          setDfChanged(false);
        });
      }

    });
  };

  // 取消修改项目类型
  const handleCancelModify = () => {
    setModify(false);
  };

  // 取消修改,只有基本信息tab取消会退出编辑态
  const handleCancelEdit = () => {
    if (activeKey === "1"){
      setSend(true);
      setEdit(false);
    }
    if (activeKey === "2") {
      setBfChanged(false);
      setEdit(false)
    }
    setUserData(JSON.parse(JSON.stringify(userDataTmp)));
    form.setFieldsValue({ ...projectData });
    if (activeKey === "3") {
      if (projectData.info.connectLearning === 1) {
        setLearningShow(true);
      } else {
        setLearningShow(false);
      }
      if (projectData.info.connectAli === 1) {
        setTraceabilityCodeShow(true);
      } else {
        setTraceabilityCodeShow(false);
      }
      if (projectData.info.systemCourses === 1) {
        setSystemCoursesShow(true);
        setSystemCoursesValue(true);
      } else {
        setSystemCoursesShow(false);
        setSystemCoursesValue(false);
      }
      if (projectData.info.needSystemCourses === 1) {
        setNeedSystemCoursesValue(true);
      } else {
        setNeedSystemCoursesValue(false);
      }

      if (projectData.info.needLearning === 1) {
        setNeedLearningValue(true);
      } else {
        setNeedLearningValue(false);
      }

      setRegisterPushValue(projectData.info.pushScenario.registerPush);
      setUpdateRandomFrontPushValue(projectData.info.pushScenario.updateRandomFrontPush);
      setUpdateRandomAfterPushValue(projectData.info.pushScenario.updateRandomAfterPush);
      setDispensingPushValue(projectData.info.pushScenario.dispensingPush);
      setScreenPushValue(projectData.info.pushScenario.screenPush);
      setRandomPushValue(projectData.info.pushScenario.randomPush);
      setRandomBlockPushValue(projectData.info.pushScenario.randomBlockPush);
      setFormRandomBlockPushValue(projectData.info.pushScenario.formRandomBlockPush);
      setCohortRandomBlockPushValue(projectData.info.pushScenario.cohortRandomBlockPush);

      setBlockSwitch(projectData.info.pushScenario.randomBlockPush || projectData.info.pushScenario.formRandomBlockPush || projectData.info.pushScenario.cohortRandomBlockPush)

      setDfChanged(false);
      setEdit(false);

      // console.log("kk==" + JSON.stringify(form.getFieldValue("info")));
      // console.log("kk==" + JSON.stringify(oldConnectEdc))
      if(oldConnectEdc === 1){
        const tmpInfo = form.getFieldValue("info");
        form.setFieldsValue({ ...projectData, tmpInfo });
      }else {
        // const tmpInfo = form.getFieldValue("info");
        projectData.info.connectEdc = 2;
        form.setFieldsValue({ ...projectData });
        // console.log("kk==" + JSON.stringify(tmpInfo));
        // console.log("kk==" + JSON.stringify(form.getFieldValue("info")));
      }
    }
  };

  const handleTabChange = (key) => {
    if (key === "7") {
      projectCtx.setShowNotice(true);
      projectCtx.setNoticeSave(0);

      projectCtx.setRoleEmail([]);
      projectCtx.setRoleData([]);
      projectCtx.setSelectMenu("");
      projectCtx.setSelectUser([]);
      projectCtx.setAllSelectUser({});
    } else {
      projectCtx.setShowNotice(false);
    }

    if (edit && key !== "3" && activeKey === "3") {
      confirm({
        title: formatMessage({ id: "common.tips" }),
        icon: <ExclamationCircleFilled />,
        content: formatMessage({ id: "common.tips.save" }),
        okText: formatMessage({ id: "common.ok" }),
        cancelText: formatMessage({ id: "common.cancel" }),
        centered: true,
        onOk() {
          switchTab(key);
        },
        onCancel() {
          setActiveKey(activeKey);
        },
      });
    } else {
      switchTab(key);
    }
  };

  // 切换Tab
  const switchTab = (key) => {
    handleCancelEdit();  // 切换tab 后，原本的修改取消保存回退为初始状态
    setEdit(false);
    // 切换到业务功能，外部对接的tab页,默认开启编辑态，取消编辑不退出编辑态，只还原表单
    if (key !== "1") {
      if (
          key === "2" &&
          permissions(
              auth.permissions,
              "operation.projects.main.setting.function.edit"
          )
      ) {
        // setEdit(true);
      } else if (
          key === "3" &&
          permissions(
              auth.permissions,
              "operation.projects.main.setting.docking.edit"
          )
      ) {
        // setEdit(true);
      }
          // else if (
          //   key === "5" &&
          //   permissions(
          //     auth.permissions,
          //     "operation.projects.main.setting.custom.edit"
          //   )
          // ) {
          //   setEdit(true);
          //   if (projectData.info.unblindingType === 1) {
          //     setUnblindingType(true);
          //   }
          //   if (projectData.info.pvUnblindingType === 1) {
          //     setPvUnblindingType(true);
          //   }
          //   if (projectData.info.unblindingControl === 1) {
          //     if (
          //       projectData.info.unblindingSms === 1 ||
          //       projectData.info.unblindingProcess === 1
          //     ) {
          //       setUnblindingMethod(true);
          //     }
          //   }
      // }
      else if (key === "4") {
        setEdit(true);
      }
    }
    setActiveKey(key);
    setUserData(JSON.parse(JSON.stringify(userDataTmp)));
    setUnblindingControl(projectData.info.unblindingControl);
    form.setFieldsValue({ ...projectData });
    setModify(false);
  };

  const hide = () => {
    setStart(false);
    setIconColor(0);
    setModifyType(null);
    form.setFieldValue("modifyType", modifyType);
  };

  const handleOpenChange = (newOpen) => {
    if (newOpen != null) {
      if (newOpen === true) {
        setIconColor(1);
      } else {
        setIconColor(0);
        setModifyType(null);
      }
    }
    setStart(newOpen);
    setModifyType(null);
    form.setFieldValue("modifyType", modifyType);
  };

  const handleMouseEnter = () => {
    setIconColor(1);
  };

  const handleMouseLeave = () => {
    setIconColor(0);
  };

  const handleClose = () => {
    form.resetFields();
    setActiveKey("1");
    setIsModalVisible(false);
    setEdcPush(false);
    props.refresh();
  };

  React.useImperativeHandle(props.bind, () => ({ show }));

  // const onApprovalChange = (e) => {
  //   setUnblindingMethod(e.target.checked);
  //   const tmpInfo = form.getFieldValue("info");
  //   tmpInfo.unblindingSms = 0;
  //   tmpInfo.unblindingProcess = 0;
  //   form.setFieldsValue({ ...projectData, info: tmpInfo });
  // };

  const onNeedSystemCoursesChange = (e) => {
    const tmpInfo = form.getFieldValue("info");
    if (e.target.checked) {
      setNeedSystemCoursesValue(true);
      tmpInfo.needSystemCourses = 1;
    } else {
      setNeedSystemCoursesValue(false);
      tmpInfo.needSystemCourses = 2;
    }
    form.setFieldsValue({ ...projectData, info: tmpInfo });
  };

  const onRegisterPushChange = (e) => {
    const tmpInfo = form.getFieldValue("info");
    setRegisterPushValue(e.target.checked);
    tmpInfo.pushScenario.registerPush = e.target.checked;
    form.setFieldsValue({ ...projectData, info: tmpInfo });
  };

  const onUpdateRandomFrontPushChange = (e) => {
    const tmpInfo = form.getFieldValue("info");
    setUpdateRandomFrontPushValue(e.target.checked);
    tmpInfo.pushScenario.updateRandomFrontPush = e.target.checked;
    form.setFieldsValue({ ...projectData, info: tmpInfo });
  };

  const onUpdateRandomAfterPushChange = (e) => {
    const tmpInfo = form.getFieldValue("info");
    setUpdateRandomAfterPushValue(e.target.checked);
    tmpInfo.pushScenario.updateRandomAfterPush = e.target.checked;
    form.setFieldsValue({ ...projectData, info: tmpInfo });
  };

  const onRandomPushChange = (e) => {
    const tmpInfo = form.getFieldValue("info");
    setRandomPushValue(e.target.checked);
    tmpInfo.pushScenario.randomPush = e.target.checked;
    // 随机取消选中 就把分层校验不一致，进行随机阻断的复选框取消掉
    if(!e.target.checked){
      setBlockSwitch(false);
      setRandomBlockPushValue(false);
      setFormRandomBlockPushValue(false);
      setCohortRandomBlockPushValue(false);
      tmpInfo.pushScenario.randomBlockPush = false;
      tmpInfo.pushScenario.formRandomBlockPush = false;
      tmpInfo.pushScenario.cohortRandomBlockPush = false;
    }
    form.setFieldsValue({ ...projectData, info: tmpInfo });
  };

  const onRandomBlockPushChange = (e) => {
    const tmpInfo = form.getFieldValue("info");
    setRandomBlockPushValue(e.target.checked);
    tmpInfo.pushScenario.randomBlockPush = e.target.checked;
    form.setFieldsValue({ ...projectData, info: tmpInfo });
  };

  const onBlockOptionChange = (value) => {
    const tmpInfo = form.getFieldValue("info");
    console.log(`selected ${value}`);
    const block1 = value.includes(1);
    const block2 = value.includes(2);
    const block3 = value.includes(3);

    setRandomBlockPushValue(block1);
    tmpInfo.pushScenario.randomBlockPush = block1;

    setFormRandomBlockPushValue(block2);
    tmpInfo.pushScenario.formRandomBlockPush = block2;

    setCohortRandomBlockPushValue(block3);
    tmpInfo.pushScenario.cohortRandomBlockPush = block3;

    form.setFieldsValue({ ...projectData, info: tmpInfo });
  };

  const onDispensingPushChange = (e) => {
    const tmpInfo = form.getFieldValue("info");
    setDispensingPushValue(e.target.checked);
    tmpInfo.pushScenario.dispensingPush = e.target.checked;
    form.setFieldsValue({ ...projectData, info: tmpInfo });
  };

  const onScreenPushChange = (e) => {
    const tmpInfo = form.getFieldValue("info");
    setScreenPushValue(e.target.checked);
    tmpInfo.pushScenario.screenPush = e.target.checked;
    form.setFieldsValue({ ...projectData, info: tmpInfo });
  };

  const onStratificationBlockChange = (e) => {
    const tmpInfo = form.getFieldValue("info");
    setRandomBlockPushValue(e.target.checked);
    tmpInfo.pushScenario.randomBlockPush = e.target.checked;
    form.setFieldsValue({ ...projectData, info: tmpInfo });
  };

  const onFormBlockChange = (e) => {
    const tmpInfo = form.getFieldValue("info");
    setFormRandomBlockPushValue(e.target.checked);
    tmpInfo.pushScenario.formRandomBlockPush = e.target.checked;
    form.setFieldsValue({ ...projectData, info: tmpInfo });
  };

  const onCohortBlockChange = (e) => {
    const tmpInfo = form.getFieldValue("info");
    setCohortRandomBlockPushValue(e.target.checked);
    tmpInfo.pushScenario.cohortRandomBlockPush = e.target.checked;
    form.setFieldsValue({ ...projectData, info: tmpInfo });
  };

  const handleBlockSwitchClick = (checked) => {
    setBlockSwitch(checked);
    if (!checked) {
      const tmpInfo = form.getFieldValue("info");
      setRandomBlockPushValue(false);
      setFormRandomBlockPushValue(false);
      setCohortRandomBlockPushValue(false);

      tmpInfo.pushScenario.randomBlockPush = false;
      tmpInfo.pushScenario.formRandomBlockPush = false;
      tmpInfo.pushScenario.cohortRandomBlockPush = false;

      form.setFieldsValue({ ...projectData, info: tmpInfo });
    }
  }

  const onNeedLearningChange = (e) => {
    const tmpInfo = form.getFieldValue("info");
    if (e.target.checked) {
      setNeedLearningValue(true);
      tmpInfo.needLearning = 1;
    } else {
      setNeedLearningValue(false);
      tmpInfo.needLearning = 2;
    }
    form.setFieldsValue({ ...projectData, info: tmpInfo });
  };

  const formItemLayout = {
    labelCol: {
      style: { width: g.lang === "en" ? "240px" : "150px", textAlign: "right" },
    },
  };

  const dockingLayout = {
    labelCol: {
      style: { width: g.lang === "en" ? "136px" : "112px", textAlign: "right" },
    },
  };

  const reasonTooltip = () => {
    if (!projectData.info.statusReason) return null;
    return (
        <Tooltip
            title={projectData.info.statusReason}
            placement="topLeft"
            arrowPointAtCenter
        >
          <InfoCircleFilled style={{ color: "#999999" }} />
        </Tooltip>
    );
  };

  // 业务功能table筛选
  const getColumnSearchProps = (dataIndex) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm }) => (
        <div style={{ padding: 8 }}>
          <Input.Search
              ref={searchInput}
              placeholder={formatMessage({
                id:
                    dataIndex === "email"
                        ? "placeholder.input.email"
                        : "placeholder.input.name",
              })}
              value={selectedKeys[0]}
              onChange={(e) =>
                  setSelectedKeys(e.target.value ? [e.target.value] : [])
              }
              onSearch={confirm}
              onPressEnter={confirm}
              enterButton
          />
        </div>
    ),
    filterIcon: (filtered) => (
        <SearchOutlined style={{ color: filtered ? "#1890ff" : "#677283" }} />
    ),
    onFilter: (value, record) =>
        record.info[dataIndex]
            .toString()
            .toLowerCase()
            .includes(value.toLowerCase()),
    onFilterDropdownOpenChange: (visible) => {
      if (visible) {
        setTimeout(() => searchInput.current?.select(), 100);
      }
    },
  });

  const handleChangeDate = (val, tag) => {
    const [start, end] = cycleTime;
    if (!!start && tag === "end" && moment(val) < moment(start)) {
      message.error(formatMessage({ id: "projects.endDateTooltip" }));
      form.setFieldValue(["info", "cycleTime", 1], null);
    } else if (!!end && tag === "start" && moment(val) > moment(end)) {
      message.error(formatMessage({ id: "projects.startDateTooltip" }));
      form.setFieldValue(["info", "cycleTime", 0], null);
    }
  };

  const getWeekText = (days) => {
    return WeekDayIntl
        .filter(it => days.includes(it.value))
        .map(it => formatMessage({id: it.label}))
  }
  const orderCheckDayShow = () => {
    const days = form.getFieldValue(["info", "orderCheckDay"]) || []
    const week = days.length === WeekDayIntl.length ? formatMessage({id: 'week.all'}) : getWeekText(days).join("、")
    const orderCheckTime = form.getFieldValue(["info", "orderCheckTime"])
    const time =  getOrderCheckTime(orderCheckTime)
    return `${week}，${time}`
  }

  const getOptions = () => {
    const baseOptions = [
      {
        value: 1,
        label: formatMessage({ id: "projects.subject.stratification" }),
      },
      {
        value: 2,
        label: formatMessage({ id: "projects.subject.form" }),
      },
    ];

    if (projectCtx.projectType === 2 || projectCtx.projectType === 3) {
      baseOptions.push({
        value: 3,
        label: projectCtx.projectType === 3
            ? formatMessage({ id: "projects.subject.stageName" })
            : formatMessage({ id: "projects.subject.cohortName" }),
      });
    }

    return baseOptions;
  };

  return (
      <>
        {/*项目设置*/}
        <StyledModal
            destroyOnClose={true}
            title={<FormattedMessage id="menu.projects.main.setting" />}
            visible={isModalVisible}
            onOk={handleOk}
            // bodyStyle={{
            //     maxHeight:
            //         edit &&
            //         (activeKey === "1" ||
            //             (activeKey !== "4" && activeKey !== "2" && activeKey !== "5") ||
            //             (activeKey === "2" && bfChanged) ||
            //             (activeKey === "3" && dfChanged) ||
            //             (activeKey === "5" && (unblindingControl || orderApprovalControl)))
            //             ? "639px"
            //             : "700px",
            // }}
            bodyStyle={{
              display: "flex",
              paddingRight: 0,
              overflow: "hidden",
              // maxHeight: "544px",
            }}
            // className="custom-large-modal"
            width={1200}
            centered
            maskClosable={false}
            okText={formatMessage({ id: "common.save" })}
            onCancel={handleClose}
            footer={null}
        >
          <Form
              form={form}
              {...formItemLayout}
              onValuesChange={(changed, values) => {
                const info = values?.info;
                const userData = getUserData();
                const userStr = "" + userData?.map((u) => u.info.email + !!u.admin);
                if (!!changed?.info?.orderCheck) setOrderCheck(changed?.info?.orderCheck)
                const str =
                    "" +
                    info?.timeZoneStr +
                    info?.orderCheck +
                    !!info?.orderConfirmation +
                    !!info?.deIsolationApproval +
                    getOrderCheckHashStr({
                      ...info,
                      orderCheckDay: form.getFieldValue(["info", "orderCheckDay"]),
                      orderCheckTime: form.getFieldValue(["info", "orderCheckTime"]),
                    }) +
                    userStr;
                const md5 = Md5.hashStr(str);
                if (md5 !== bfihv) {
                  setBfChanged(true);
                } else {
                  setBfChanged(false);
                }
                const dstr =
                    "" +
                    info?.synchronizationMode +
                    info?.connectLearning +
                    info?.needLearning +
                    info?.needLearningEnv;
                const dmd5 = Md5.hashStr(dstr);
                if (dmd5 !== dfihv) {
                  setDfChanged(true);
                } else {
                  setDfChanged(false);
                }
                formChange();
              }}
              style={{ flex: 1, width: "100%" }}
          >
            <Container
                lang={g.lang}
                edit={edit || ["3", "6"].includes(activeKey)}
            >
              <Tabs
                  tabBarStyle={{ width: "200px", overflow: "auto" }}
                  tabPosition="left"
                  activeKey={activeKey}
                  onChange={handleTabChange}
                  className="setting-tab"
                  destroyInactiveTabPane={true}
              >
                {projectSettingPermission(
                    projectData,
                    auth,
                    "operation.projects.main.setting.base.view"
                ) && (
                    <TabPane
                        tab={
                          <>
                            <i
                                className="iconfont icon-jibenxinxi"
                                style={{ marginRight: 8 }}
                            />
                            {formatMessage({ id: "menu.projects.main.setting.base" })}
                          </>
                        }
                        key="1"
                    >
                      <Layout.Header
                          style={{ height: "24px", background: "#FFFFFF" }}
                      />
                      <CustomTitle
                          name={intl.formatMessage({ id: "projects.attribute" })}
                      />
                      <Row style={{ marginTop: "10px" }}>
                        <Col className="gutter-row">
                          <Form.Item
                              label={<FormattedMessage id={"projects.type"} />}
                          >
                            {
                              projectTypes.find(
                                  (it) => it.key === projectData.info.type
                              )?.value
                            }
                            {projectData.info.type === 1 &&
                            projectData.status !== 2 &&
                            projectData.status !== 4 &&
                            projectSettingButtonPermission(
                                projectData,
                                auth,
                                "operation.projects.main.setting.base.modify"
                            ) ? (
                                <Popover
                                    placement="bottom"
                                    open={start}
                                    title={
                                      <div className="popover-title-type">
                                        {formatMessage({ id: "common.edit" })}
                                        <Button
                                            onClick={hide}
                                            type="text"
                                            icon={
                                              <CloseOutlined
                                                  style={{ color: "#666666" }}
                                              />
                                            }
                                            style={{
                                              float: "right",
                                              marginRight: -4,
                                              width: "15px",
                                              height: "15px",
                                            }}
                                        />
                                      </div>
                                    }
                                    content={
                                      <div style={{ width: "240px", height: "116px" }}>
                                        <Form layout={"vertical"} form={form}>
                                          <Form.Item
                                              // name={["info", "type"]}
                                              name={"modifyType"}
                                              rules={[
                                                {
                                                  required: true,
                                                  // message: formatMessage({ id: "placeholder.select.common.project.type" }),
                                                },
                                              ]}
                                              label={
                                                <span
                                                    style={{
                                                      fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                                      fontSize: "14px",
                                                      fontWeight: 400,
                                                      lineHeight: "20px",
                                                      letterSpacing: "0px",
                                                      textAlign: "left",
                                                      color: "#677283",
                                                    }}
                                                >
                                          {formatMessage({ id: "projects.type" })}
                                        </span>
                                              }
                                          >
                                            <Select
                                                showSearch
                                                placeholder={
                                                  <FormattedMessage
                                                      id={"placeholder.select.common"}
                                                  />
                                                }
                                                optionFilterProp="children"
                                                onChange={onModifyChange}
                                                // onSearch={onSearch}
                                                style={{ width: "240px" }}
                                                filterOption={(input, option) =>
                                                    (option?.label ?? "")
                                                        .toLowerCase()
                                                        .includes(input.toLowerCase())
                                                }
                                                options={[
                                                  {
                                                    value: 2,
                                                    label: formatMessage({
                                                      id: "projects.types.second",
                                                    }),
                                                  },
                                                  {
                                                    value: 3,
                                                    label: formatMessage({
                                                      id: "projects.types.third",
                                                    }),
                                                  },
                                                ]}
                                                value={
                                                    form.getFieldValue("modifyType") || null
                                                } // 根据表单字段值设置选项
                                            />
                                          </Form.Item>
                                        </Form>
                                        <Button
                                            loading={updateProjectTypeLoading}
                                            onClick={handleOkModify}
                                            type={"primary"}
                                            className="full-width"
                                        >
                                          {formatMessage({ id: "common.ok" })}
                                        </Button>
                                      </div>
                                    }
                                    style={{
                                      height: "180px",
                                      width: "260px !important",
                                      left: "496px",
                                      top: "155px",
                                      borderRadius: "2px",
                                      marginTop: "16px",
                                      marginLeft: "12px",
                                    }}
                                    trigger="click"
                                    onOpenChange={handleOpenChange}
                                >
                                  <Tooltip
                                      title={formatMessage({ id: "common.edit" })}
                                  >
                                    <i
                                        // onClick={()=>drug.setVisitTypeView(true)}
                                        style={{
                                          marginLeft: 8,
                                          cursor: "pointer",
                                          color:
                                              iconColor === 0 ? "#999999" : "#165DFF",
                                        }}
                                        className="iconfont icon-bianji"
                                        onMouseEnter={handleMouseEnter}
                                        onMouseLeave={handleMouseLeave}
                                    ></i>
                                  </Tooltip>
                                </Popover>
                            ) : null}
                          </Form.Item>
                        </Col>
                      </Row>
                      {/* <Row>
                      <Col className="gutter-row">
                        <Form.Item
                          label={
                            <FormattedMessage
                              id={"projects.research.attribute"}
                            />
                          }
                        >
                          {
                            projectResearch.find(
                              (it) =>
                                it.key === projectData.info.researchAttribute
                            )?.value
                          }
                        </Form.Item>
                      </Col>
                    </Row> */}

                      <UpdateStatusModal
                          open={statusModalVisible}
                          status={status}
                          close={onUpdateStatusClose}
                          ok={onUpdateStatusOk}
                      ></UpdateStatusModal>
                      <Row style={{ marginBottom: 0 }}>
                        <Col className="gutter-row" style={{ marginBottom: 0 }}>
                          <StatusFormItem
                              checkedStatus={form.getFieldValue("status")}
                              lang={g.lang}
                              label={<FormattedMessage id={"common.status"} />}
                              name="status"
                              style={{ marginBottom: 0 }}
                              className="x-curr-page-status"
                          >
                            <Row>
                              {projectData.status === 0 &&
                                  permissions(
                                      auth.permissions,
                                      "operation.projects.main.setting.base.edit"
                                  ) && (
                                      <Space>
                                        <CustomSelect
                                            lang={g.lang}
                                            hoverStatus={statusHover}
                                            style={{ paddingLeft: "0px" }}
                                            onChange={(v) => {
                                              setStatus(v);
                                              setStatusModalVisible(true);
                                            }}
                                            value={projectData.status}
                                            showArrow={false}
                                            bordered={false}
                                            onMouseEnter={() => {
                                              setStatusHover(true);
                                            }}
                                            onMouseLeave={() => setStatusHover(false)}
                                            getPopupContainer={() =>
                                                document.getElementsByClassName(
                                                    "x-curr-page-status"
                                                )[0]
                                            }
                                        >
                                          <Select.Option value={0}>
                                            <div
                                                style={{
                                                  paddingLeft: "0px",
                                                  display: "flex",
                                                  alignItems: "center",
                                                  justifyContent: "space-between",
                                                }}
                                            >
                                              <div
                                                  className="x-option x-option-green"
                                                  style={{
                                                    background: "#ecfaf2",
                                                    color: "#41cc82",
                                                    fontStyle: "normal",
                                                    fontWeight: 400,
                                                    alignItems: "center",
                                                    display: "flex",
                                                  }}
                                              >
                                                <svg
                                                    style={{
                                                      width: 16,
                                                      height: 16,
                                                      marginLeft: 8,
                                                      marginRight: 8,
                                                    }}
                                                    viewBox="0 0 12 12"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                >
                                                  <circle
                                                      cx="6"
                                                      cy="6"
                                                      r="5.5"
                                                      stroke="#41CC82"
                                                  />
                                                  <path
                                                      d="M3 6.5H9L7.5 5"
                                                      stroke="#41CC82"
                                                      strokeLinecap="round"
                                                      strokeLinejoin="round"
                                                  />
                                                </svg>

                                                <img
                                                    src={CaretDownGreen}
                                                    style={{
                                                      width: 14,
                                                      height: 14,
                                                      marginLeft: 8,
                                                      marginRight: 10,
                                                    }}
                                                />
                                                <FormattedMessage id="projects.status.progress" />
                                              </div>
                                              <svg
                                                  className="x-check"
                                                  style={{ width: 16, height: 16 }}
                                                  viewBox="0 0 15 11"
                                                  fill="none"
                                                  xmlns="http://www.w3.org/2000/svg"
                                              >
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M4.79629 8.54882L13.3329 0L14.5123 1.17767L4.79713 10.9067L0 6.10955L1.17851 4.93104L4.79629 8.54882Z"
                                                    fill="#165DFF"
                                                />
                                              </svg>
                                            </div>
                                          </Select.Option>
                                          <Select.Option value={1}>
                                            <div
                                                style={{
                                                  paddingLeft: "0px",
                                                  display: "flex",
                                                  alignItems: "center",
                                                  justifyContent: "space-between",
                                                }}
                                            >
                                              <div
                                                  className="x-option"
                                                  style={{
                                                    background: "#e8efff",
                                                    color: "#165DFF",
                                                    fontStyle: "normal",
                                                    fontWeight: 400,
                                                    alignItems: "center",
                                                    display: "flex",
                                                  }}
                                              >
                                                <svg
                                                    style={{
                                                      width: 16,
                                                      height: 16,
                                                      marginLeft: 8,
                                                      marginRight: 8,
                                                    }}
                                                    viewBox="0 0 12 12"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                >
                                                  <circle
                                                      cx="6"
                                                      cy="6"
                                                      r="5.5"
                                                      stroke="#165DFF"
                                                  />
                                                  <path
                                                      fill-rule="evenodd"
                                                      clip-rule="evenodd"
                                                      d="M8.32786 3.75023L5.07116 7.00669L3.67172 5.60735C3.44288 5.37851 3.07182 5.37851 2.84297 5.60735C2.61419 5.83618 2.61419 6.20714 2.84297 6.43597L4.6565 8.2493L4.65679 8.24964C4.77124 8.36409 4.92118 8.4213 5.07116 8.4213C5.22114 8.4213 5.37108 8.36409 5.48553 8.24964L9.15653 4.57892C9.38537 4.35008 9.38537 3.97906 9.15653 3.75023C8.92769 3.52141 8.5567 3.52141 8.32786 3.75023Z"
                                                      fill="#165DFF"
                                                  />
                                                </svg>

                                                <img
                                                    src={CaretDownBlue}
                                                    style={{
                                                      width: 14,
                                                      height: 14,
                                                      marginLeft: 8,
                                                      marginRight: 10,
                                                    }}
                                                />
                                                <FormattedMessage id="projects.status.finish" />
                                              </div>
                                            </div>
                                          </Select.Option>
                                          <Select.Option value={3}>
                                            <div
                                                style={{
                                                  paddingLeft: "0px",
                                                  display: "flex",
                                                  alignItems: "center",
                                                  justifyContent: "space-between",
                                                }}
                                            >
                                              <div
                                                  className="x-option x-option-yellow"
                                                  style={{
                                                    background: "#fff7e5",
                                                    color: "#FFAE00",
                                                    fontStyle: "normal",
                                                    fontWeight: 400,
                                                    alignItems: "center",
                                                    display: "flex",
                                                  }}
                                              >
                                                <svg
                                                    style={{
                                                      width: 18,
                                                      height: 18,
                                                      marginLeft: 8,
                                                      marginRight: 8,
                                                      color: "#FFAE00",
                                                    }}
                                                    viewBox="0 0 12 12"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                >
                                                  <circle
                                                      cx="6"
                                                      cy="6"
                                                      r="5.5"
                                                      stroke="#FFAE00"
                                                  />
                                                  <rect
                                                      x="4"
                                                      y="4"
                                                      width="4"
                                                      height="4"
                                                      rx="1"
                                                      fill="#FFAE00"
                                                  />
                                                </svg>
                                                <img
                                                    src={CaretDownBlue}
                                                    style={{
                                                      width: 14,
                                                      height: 14,
                                                      marginLeft: 8,
                                                      marginRight: 10,
                                                    }}
                                                />
                                                <FormattedMessage id="projects.status.pause" />
                                              </div>
                                            </div>
                                          </Select.Option>
                                          <Select.Option value={4}>
                                            <div
                                                style={{
                                                  paddingLeft: "0px",
                                                  display: "flex",
                                                  alignItems: "center",
                                                  justifyContent: "space-between",
                                                }}
                                            >
                                              <div
                                                  className="x-option x-option-red"
                                                  style={{
                                                    fontStyle: "normal",
                                                    fontWeight: 400,
                                                    alignItems: "center",
                                                    display: "flex",
                                                    color: "#F96964",
                                                    backgroundColor: "#fef0ef",
                                                  }}
                                              >
                                                <svg
                                                    style={{
                                                      width: 16,
                                                      height: 16,
                                                      marginLeft: 8,
                                                      marginRight: 8,
                                                      color: "#F96964",
                                                    }}
                                                    viewBox="0 0 12 12"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                >
                                                  <circle
                                                      cx="6"
                                                      cy="6"
                                                      r="5.5"
                                                      stroke="#F96964"
                                                  />
                                                  <path
                                                      d="M1.93408 2.39111L2.64119 1.68401L10.0658 9.10863L9.3587 9.81573L1.93408 2.39111Z"
                                                      fill="#F96964"
                                                  />
                                                </svg>
                                                <img
                                                    src={CaretDownBlue}
                                                    style={{
                                                      width: 14,
                                                      height: 14,
                                                      marginLeft: 8,
                                                      marginRight: 10,
                                                    }}
                                                />
                                                <FormattedMessage id="projects.status.terminate" />
                                              </div>
                                            </div>
                                          </Select.Option>
                                        </CustomSelect>
                                        {reasonTooltip()}
                                      </Space>
                                  )}
                              {projectData.status === 1 &&
                                  permissions(
                                      auth.permissions,
                                      "operation.projects.main.setting.base.edit"
                                  ) && (
                                      <CustomSelect
                                          lang={g.lang}
                                          hoverStatus={statusHover}
                                          style={{ paddingLeft: "0px" }}
                                          onChange={(v) => {
                                            setStatus(v);
                                            setStatusModalVisible(true);
                                          }}
                                          value={projectData.status}
                                          showArrow={false}
                                          bordered={false}
                                          onMouseEnter={() => {
                                            setStatusHover(true);
                                          }}
                                          onMouseLeave={() => setStatusHover(false)}
                                          getPopupContainer={() =>
                                              document.getElementsByClassName(
                                                  "x-curr-page-status"
                                              )[0]
                                          }
                                      >
                                        <Select.Option value={1}>
                                          <div
                                              style={{
                                                paddingLeft: "0px",
                                                display: "flex",
                                                alignItems: "center",
                                                justifyContent: "space-between",
                                              }}
                                          >
                                            <div
                                                className="x-option"
                                                style={{
                                                  background: "#e8efff",
                                                  color: "#165DFF",
                                                  fontStyle: "normal",
                                                  fontWeight: 400,
                                                  alignItems: "center",
                                                  display: "flex",
                                                }}
                                            >
                                              <svg
                                                  style={{
                                                    width: 16,
                                                    height: 16,
                                                    marginLeft: 8,
                                                    marginRight: 8,
                                                  }}
                                                  viewBox="0 0 12 12"
                                                  fill="none"
                                                  xmlns="http://www.w3.org/2000/svg"
                                              >
                                                <circle
                                                    cx="6"
                                                    cy="6"
                                                    r="5.5"
                                                    stroke="#165DFF"
                                                />
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M8.32786 3.75023L5.07116 7.00669L3.67172 5.60735C3.44288 5.37851 3.07182 5.37851 2.84297 5.60735C2.61419 5.83618 2.61419 6.20714 2.84297 6.43597L4.6565 8.2493L4.65679 8.24964C4.77124 8.36409 4.92118 8.4213 5.07116 8.4213C5.22114 8.4213 5.37108 8.36409 5.48553 8.24964L9.15653 4.57892C9.38537 4.35008 9.38537 3.97906 9.15653 3.75023C8.92769 3.52141 8.5567 3.52141 8.32786 3.75023Z"
                                                    fill="#165DFF"
                                                />
                                              </svg>
                                              <img
                                                  src={CaretDownBlue}
                                                  style={{
                                                    width: 14,
                                                    height: 14,
                                                    marginLeft: 8,
                                                    marginRight: 10,
                                                  }}
                                              />
                                              <FormattedMessage id="projects.status.finish" />
                                            </div>
                                            <svg
                                                className="x-check"
                                                style={{ width: 16, height: 16 }}
                                                viewBox="0 0 15 11"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg"
                                            >
                                              <path
                                                  fill-rule="evenodd"
                                                  clip-rule="evenodd"
                                                  d="M4.79629 8.54882L13.3329 0L14.5123 1.17767L4.79713 10.9067L0 6.10955L1.17851 4.93104L4.79629 8.54882Z"
                                                  fill="#165DFF"
                                              />
                                            </svg>
                                          </div>
                                        </Select.Option>
                                        <Select.Option value={2}>
                                          <div
                                              style={{
                                                paddingLeft: "0px",
                                                display: "flex",
                                                alignItems: "center",
                                                justifyContent: "space-between",
                                              }}
                                          >
                                            <div
                                                className="x-option x-option-red"
                                                lang={g.lang}
                                                style={{
                                                  background: "#e5e7eb",
                                                  color: "#4E5969",
                                                  fontStyle: "normal",
                                                  fontWeight: 400,
                                                  alignItems: "center",
                                                  display: "flex",
                                                }}
                                            >
                                              <svg
                                                  style={{
                                                    width: 16,
                                                    height: 16,
                                                    marginLeft: 8,
                                                    marginRight: 8,
                                                  }}
                                                  viewBox="0 0 12 12"
                                                  fill="none"
                                                  xmlns="http://www.w3.org/2000/svg"
                                              >
                                                <circle
                                                    cx="6"
                                                    cy="6"
                                                    r="5.5"
                                                    stroke="#4E5969"
                                                />
                                                <path
                                                    d="M8.24264 3.75776C8.04738 3.56249 7.7308 3.56249 7.53553 3.75776L6.12132 5.17197L4.70711 3.75776C4.51184 3.56249 4.19526 3.56249 4 3.75776C3.80474 3.95302 3.80474 4.2696 4 4.46486L5.41421 5.87908L4 7.29329C3.80474 7.48855 3.80474 7.80513 4 8.0004C4.19526 8.19566 4.51184 8.19566 4.70711 8.0004L6.12132 6.58618L7.53553 8.0004C7.7308 8.19566 8.04738 8.19566 8.24264 8.0004C8.4379 7.80513 8.4379 7.48855 8.24264 7.29329L6.82843 5.87908L8.24264 4.46486C8.4379 4.2696 8.4379 3.95302 8.24264 3.75776Z"
                                                    fill="#4E5969"
                                                />
                                              </svg>
                                              <FormattedMessage id="projects.status.close" />
                                            </div>
                                          </div>
                                        </Select.Option>
                                      </CustomSelect>
                                  )}
                              {(projectData.status === 2 ||
                                  !permissions(
                                      auth.permissions,
                                      "operation.projects.main.setting.base.edit"
                                  )) && (
                                  <React.Fragment>
                                    {projectData.status === 0 && (
                                        <Space>
                                          <div
                                              style={{
                                                paddingLeft: "0px",
                                                background: "#ecfaf2",
                                                color: "#41CC82",
                                                fontStyle: "normal",
                                                fontWeight: 400,
                                                height: 30,
                                                alignItems: "center",
                                                display: "flex",
                                                width: 84,
                                              }}
                                          >
                                            <svg
                                                style={{
                                                  width: 16,
                                                  height: 16,
                                                  marginLeft: 8,
                                                  marginRight: 8,
                                                }}
                                                viewBox="0 0 12 12"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg"
                                            >
                                              <circle
                                                  cx="6"
                                                  cy="6"
                                                  r="5.5"
                                                  stroke="#41CC82"
                                              />
                                              <path
                                                  d="M3 6.5H9L7.5 5"
                                                  stroke="#41CC82"
                                                  strokeLinecap="round"
                                                  strokeLinejoin="round"
                                              />
                                            </svg>

                                            {
                                              projectStatus.find(
                                                  (it) => it.value === projectData.status
                                              )?.label
                                            }
                                          </div>
                                          {reasonTooltip()}
                                        </Space>
                                    )}
                                    {projectData.status === 1 && (
                                        <div
                                            style={{
                                              paddingLeft: "0px",
                                              background: "#e8efff",
                                              color: "#165DFF",
                                              fontStyle: "normal",
                                              fontWeight: 400,
                                              height: 30,
                                              alignItems: "center",
                                              display: "flex",
                                              width: 84,
                                            }}
                                        >
                                          <svg
                                              style={{
                                                width: 16,
                                                height: 16,
                                                marginLeft: 8,
                                                marginRight: 8,
                                              }}
                                              viewBox="0 0 12 12"
                                              fill="none"
                                              xmlns="http://www.w3.org/2000/svg"
                                          >
                                            <circle
                                                cx="6"
                                                cy="6"
                                                r="5.5"
                                                stroke="#165DFF"
                                            />
                                            <path
                                                fill-rule="evenodd"
                                                clip-rule="evenodd"
                                                d="M8.32786 3.75023L5.07116 7.00669L3.67172 5.60735C3.44288 5.37851 3.07182 5.37851 2.84297 5.60735C2.61419 5.83618 2.61419 6.20714 2.84297 6.43597L4.6565 8.2493L4.65679 8.24964C4.77124 8.36409 4.92118 8.4213 5.07116 8.4213C5.22114 8.4213 5.37108 8.36409 5.48553 8.24964L9.15653 4.57892C9.38537 4.35008 9.38537 3.97906 9.15653 3.75023C8.92769 3.52141 8.5567 3.52141 8.32786 3.75023Z"
                                                fill="#165DFF"
                                            />
                                          </svg>
                                          <span style={{ lineHeight: "15px" }}>
                                        {
                                          projectStatus.find(
                                              (it) => it.value === projectData.status
                                          )?.label
                                        }
                                      </span>
                                        </div>
                                    )}
                                    {projectData.status === 2 && (
                                        <Space>
                                          <div
                                              style={{
                                                paddingLeft: "0px",
                                                background: "#e5e7eb",
                                                color: "#4E5969",
                                                fontStyle: "normal",
                                                fontWeight: 400,
                                                width: 84,
                                                height: 30,
                                                alignItems: "center",
                                                display: "flex",
                                              }}
                                          >
                                            <svg
                                                style={{
                                                  width: 16,
                                                  height: 16,
                                                  marginLeft: 8,
                                                  marginRight: 8,
                                                }}
                                                viewBox="0 0 12 12"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg"
                                            >
                                              <circle
                                                  cx="6"
                                                  cy="6"
                                                  r="5.5"
                                                  stroke="#4E5969"
                                              />
                                              <path
                                                  d="M8.24264 3.75776C8.04738 3.56249 7.7308 3.56249 7.53553 3.75776L6.12132 5.17197L4.70711 3.75776C4.51184 3.56249 4.19526 3.56249 4 3.75776C3.80474 3.95302 3.80474 4.2696 4 4.46486L5.41421 5.87908L4 7.29329C3.80474 7.48855 3.80474 7.80513 4 8.0004C4.19526 8.19566 4.51184 8.19566 4.70711 8.0004L6.12132 6.58618L7.53553 8.0004C7.7308 8.19566 8.04738 8.19566 8.24264 8.0004C8.4379 7.80513 8.4379 7.48855 8.24264 7.29329L6.82843 5.87908L8.24264 4.46486C8.4379 4.2696 8.4379 3.95302 8.24264 3.75776Z"
                                                  fill="#4E5969"
                                              />
                                            </svg>
                                            {
                                              projectStatus.find(
                                                  (it) => it.value === projectData.status
                                              )?.label
                                            }
                                          </div>
                                        </Space>
                                    )}
                                    {projectData.status === 3 && (
                                        <Space>
                                          <div
                                              style={{
                                                paddingLeft: "0px",
                                                background: "#fff7e5",
                                                color: "#FFAE00",
                                                fontStyle: "normal",
                                                fontWeight: 400,
                                                width: 84,
                                                height: 30,
                                                alignItems: "center",
                                                display: "flex",
                                              }}
                                          >
                                            <svg
                                                style={{
                                                  width: 18,
                                                  height: 18,
                                                  marginLeft: 8,
                                                  marginRight: 8,
                                                  color: "#FFAE00",
                                                }}
                                                viewBox="0 0 12 12"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg"
                                            >
                                              <circle
                                                  cx="6"
                                                  cy="6"
                                                  r="5.5"
                                                  stroke="#FFAE00"
                                              />
                                              <rect
                                                  x="4"
                                                  y="4"
                                                  width="4"
                                                  height="4"
                                                  rx="1"
                                                  fill="#FFAE00"
                                              />
                                            </svg>
                                            {
                                              projectStatus.find(
                                                  (it) => it.value === projectData.status
                                              )?.label
                                            }
                                          </div>
                                          {reasonTooltip()}
                                        </Space>
                                    )}
                                    {projectData.status === 4 && (
                                        <Space>
                                          <div
                                              style={{
                                                paddingLeft: "0px",
                                                color: "#F96964",
                                                backgroundColor: "#fef0ef",
                                                fontStyle: "normal",
                                                fontWeight: 400,
                                                width: 84,
                                                height: 30,
                                                alignItems: "center",
                                                display: "flex",
                                              }}
                                          >
                                            <svg
                                                style={{
                                                  width: 16,
                                                  height: 16,
                                                  marginLeft: 8,
                                                  marginRight: 8,
                                                  color: "#F96964",
                                                }}
                                                viewBox="0 0 12 12"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg"
                                            >
                                              <circle
                                                  cx="6"
                                                  cy="6"
                                                  r="5.5"
                                                  stroke="#F96964"
                                              />
                                              <path
                                                  d="M1.93408 2.39111L2.64119 1.68401L10.0658 9.10863L9.3587 9.81573L1.93408 2.39111Z"
                                                  fill="#F96964"
                                              />
                                            </svg>
                                            {
                                              projectStatus.find(
                                                  (it) => it.value === projectData.status
                                              )?.label
                                            }
                                          </div>
                                          {reasonTooltip()}
                                        </Space>
                                    )}
                                  </React.Fragment>
                              )}
                              {projectData.status === 3 &&
                                  permissions(
                                      auth.permissions,
                                      "operation.projects.main.setting.base.edit"
                                  ) && (
                                      <Space>
                                        <CustomSelect
                                            lang={g.lang}
                                            hoverStatus={statusHover}
                                            style={{ paddingLeft: "0px" }}
                                            onChange={(v) => {
                                              setStatus(v);
                                              setStatusModalVisible(true);
                                            }}
                                            value={projectData.status}
                                            showArrow={false}
                                            bordered={false}
                                            onMouseEnter={() => {
                                              setStatusHover(true);
                                            }}
                                            onMouseLeave={() => setStatusHover(false)}
                                            getPopupContainer={() =>
                                                document.getElementsByClassName(
                                                    "x-curr-page-status"
                                                )[0]
                                            }
                                        >
                                          <Select.Option value={0}>
                                            <div
                                                style={{
                                                  paddingLeft: "0px",
                                                  display: "flex",
                                                  alignItems: "center",
                                                  justifyContent: "space-between",
                                                }}
                                            >
                                              <div
                                                  className="x-option x-option-green"
                                                  style={{
                                                    background: "#ecfaf2",
                                                    color: "#41cc82",
                                                    fontStyle: "normal",
                                                    fontWeight: 400,
                                                    alignItems: "center",
                                                    display: "flex",
                                                  }}
                                              >
                                                <svg
                                                    style={{
                                                      width: 16,
                                                      height: 16,
                                                      marginLeft: 8,
                                                      marginRight: 8,
                                                    }}
                                                    viewBox="0 0 12 12"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                >
                                                  <circle
                                                      cx="6"
                                                      cy="6"
                                                      r="5.5"
                                                      stroke="#41CC82"
                                                  />
                                                  <path
                                                      d="M3 6.5H9L7.5 5"
                                                      stroke="#41CC82"
                                                      strokeLinecap="round"
                                                      strokeLinejoin="round"
                                                  />
                                                </svg>

                                                <img
                                                    src={CaretDownGreen}
                                                    style={{
                                                      width: 14,
                                                      height: 14,
                                                      marginLeft: 8,
                                                      marginRight: 10,
                                                    }}
                                                />
                                                <FormattedMessage id="projects.status.progress" />
                                              </div>
                                            </div>
                                          </Select.Option>
                                          <Select.Option value={3}>
                                            <div
                                                style={{
                                                  paddingLeft: "0px",
                                                  display: "flex",
                                                  alignItems: "center",
                                                  justifyContent: "space-between",
                                                }}
                                            >
                                              <div
                                                  className="x-option x-option-yellow"
                                                  style={{
                                                    background: "#fff7e5",
                                                    color: "#FFAE00",
                                                    fontStyle: "normal",
                                                    fontWeight: 400,
                                                    alignItems: "center",
                                                    display: "flex",
                                                  }}
                                              >
                                                <svg
                                                    style={{
                                                      width: 18,
                                                      height: 18,
                                                      marginLeft: 8,
                                                      marginRight: 8,
                                                      color: "#FFAE00",
                                                    }}
                                                    viewBox="0 0 12 12"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                >
                                                  <circle
                                                      cx="6"
                                                      cy="6"
                                                      r="5.5"
                                                      stroke="#FFAE00"
                                                  />
                                                  <rect
                                                      x="4"
                                                      y="4"
                                                      width="4"
                                                      height="4"
                                                      rx="1"
                                                      fill="#FFAE00"
                                                  />
                                                </svg>
                                                <img
                                                    src={CaretDownYellow}
                                                    style={{
                                                      width: 14,
                                                      height: 14,
                                                      marginLeft: 8,
                                                      marginRight: 10,
                                                    }}
                                                />
                                                <FormattedMessage id="projects.status.pause" />
                                              </div>
                                              <svg
                                                  className="x-check"
                                                  style={{ width: 16, height: 16 }}
                                                  viewBox="0 0 15 11"
                                                  fill="none"
                                                  xmlns="http://www.w3.org/2000/svg"
                                              >
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M4.79629 8.54882L13.3329 0L14.5123 1.17767L4.79713 10.9067L0 6.10955L1.17851 4.93104L4.79629 8.54882Z"
                                                    fill="#165DFF"
                                                />
                                              </svg>
                                            </div>
                                          </Select.Option>

                                          <Select.Option value={4}>
                                            <div
                                                style={{
                                                  paddingLeft: "0px",
                                                  display: "flex",
                                                  alignItems: "center",
                                                  justifyContent: "space-between",
                                                }}
                                            >
                                              <div
                                                  className="x-option x-option-red"
                                                  style={{
                                                    fontStyle: "normal",
                                                    fontWeight: 400,
                                                    alignItems: "center",
                                                    display: "flex",
                                                    color: "#F96964",
                                                    backgroundColor: "#fef0ef",
                                                  }}
                                              >
                                                <svg
                                                    style={{
                                                      width: 16,
                                                      height: 16,
                                                      marginLeft: 8,
                                                      marginRight: 8,
                                                      color: "#F96964",
                                                    }}
                                                    viewBox="0 0 12 12"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                >
                                                  <circle
                                                      cx="6"
                                                      cy="6"
                                                      r="5.5"
                                                      stroke="#F96964"
                                                  />
                                                  <path
                                                      d="M1.93408 2.39111L2.64119 1.68401L10.0658 9.10863L9.3587 9.81573L1.93408 2.39111Z"
                                                      fill="#F96964"
                                                  />
                                                </svg>
                                                <img
                                                    src={CaretDownBlue}
                                                    style={{
                                                      width: 14,
                                                      height: 14,
                                                      marginLeft: 8,
                                                      marginRight: 10,
                                                    }}
                                                />
                                                <FormattedMessage id="projects.status.terminate" />
                                              </div>
                                            </div>
                                          </Select.Option>
                                        </CustomSelect>
                                        {reasonTooltip()}
                                      </Space>
                                  )}
                              {projectData.status === 4 &&
                                  permissions(
                                      auth.permissions,
                                      "operation.projects.main.setting.base.edit"
                                  ) && (
                                      <Space>
                                        <CustomSelect
                                            lang={g.lang}
                                            hoverStatus={statusHover}
                                            style={{ paddingLeft: "0px" }}
                                            onChange={(v) => {
                                              setStatus(v);
                                              setStatusModalVisible(true);
                                            }}
                                            value={projectData.status}
                                            showArrow={false}
                                            bordered={false}
                                            onMouseEnter={() => {
                                              setStatusHover(true);
                                            }}
                                            onMouseLeave={() => setStatusHover(false)}
                                            getPopupContainer={() =>
                                                document.getElementsByClassName(
                                                    "x-curr-page-status"
                                                )[0]
                                            }
                                        >
                                          <Select.Option value={2}>
                                            <div
                                                style={{
                                                  paddingLeft: "0px",
                                                  display: "flex",
                                                  alignItems: "center",
                                                  justifyContent: "space-between",
                                                }}
                                            >
                                              <div
                                                  className="x-option x-option-red"
                                                  lang={g.lang}
                                                  style={{
                                                    background: "#e5e7eb",
                                                    color: "#4E5969",
                                                    fontStyle: "normal",
                                                    fontWeight: 400,
                                                    alignItems: "center",
                                                    display: "flex",
                                                  }}
                                              >
                                                <svg
                                                    style={{
                                                      width: 16,
                                                      height: 16,
                                                      marginLeft: 8,
                                                      marginRight: 8,
                                                    }}
                                                    viewBox="0 0 12 12"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                >
                                                  <circle
                                                      cx="6"
                                                      cy="6"
                                                      r="5.5"
                                                      stroke="#4E5969"
                                                  />
                                                  <path
                                                      d="M8.24264 3.75776C8.04738 3.56249 7.7308 3.56249 7.53553 3.75776L6.12132 5.17197L4.70711 3.75776C4.51184 3.56249 4.19526 3.56249 4 3.75776C3.80474 3.95302 3.80474 4.2696 4 4.46486L5.41421 5.87908L4 7.29329C3.80474 7.48855 3.80474 7.80513 4 8.0004C4.19526 8.19566 4.51184 8.19566 4.70711 8.0004L6.12132 6.58618L7.53553 8.0004C7.7308 8.19566 8.04738 8.19566 8.24264 8.0004C8.4379 7.80513 8.4379 7.48855 8.24264 7.29329L6.82843 5.87908L8.24264 4.46486C8.4379 4.2696 8.4379 3.95302 8.24264 3.75776Z"
                                                      fill="#4E5969"
                                                  />
                                                </svg>

                                                <FormattedMessage id="projects.status.close" />
                                              </div>
                                            </div>
                                          </Select.Option>
                                          <Select.Option value={4}>
                                            <div
                                                style={{
                                                  paddingLeft: "0px",
                                                  display: "flex",
                                                  alignItems: "center",
                                                  justifyContent: "space-between",
                                                }}
                                            >
                                              <div
                                                  className="x-option x-option-red"
                                                  style={{
                                                    fontStyle: "normal",
                                                    fontWeight: 400,
                                                    alignItems: "center",
                                                    display: "flex",
                                                    color: "#F96964",
                                                    backgroundColor: "#fef0ef",
                                                  }}
                                              >
                                                <svg
                                                    style={{
                                                      width: 16,
                                                      height: 16,
                                                      marginLeft: 8,
                                                      marginRight: 8,
                                                      color: "#F96964",
                                                    }}
                                                    viewBox="0 0 12 12"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                >
                                                  <circle
                                                      cx="6"
                                                      cy="6"
                                                      r="5.5"
                                                      stroke="#F96964"
                                                  />
                                                  <path
                                                      d="M1.93408 2.39111L2.64119 1.68401L10.0658 9.10863L9.3587 9.81573L1.93408 2.39111Z"
                                                      fill="#F96964"
                                                  />
                                                </svg>

                                                <img
                                                    src={CaretDownRed}
                                                    style={{
                                                      width: 14,
                                                      height: 14,
                                                      marginLeft: 8,
                                                      marginRight: 10,
                                                    }}
                                                />
                                                <FormattedMessage id="projects.status.terminate" />
                                              </div>
                                              <svg
                                                  className="x-check"
                                                  style={{ width: 16, height: 16 }}
                                                  viewBox="0 0 15 11"
                                                  fill="none"
                                                  xmlns="http://www.w3.org/2000/svg"
                                              >
                                                <path
                                                    fill-rule="evenodd"
                                                    clip-rule="evenodd"
                                                    d="M4.79629 8.54882L13.3329 0L14.5123 1.17767L4.79713 10.9067L0 6.10955L1.17851 4.93104L4.79629 8.54882Z"
                                                    fill="#165DFF"
                                                />
                                              </svg>
                                            </div>
                                          </Select.Option>
                                        </CustomSelect>
                                        {reasonTooltip()}
                                      </Space>
                                  )}
                            </Row>
                          </StatusFormItem>
                        </Col>
                      </Row>
                      {/* <Row>
                                        <Col className="gutter-row" >
                                            <Form.Item label={<FormattedMessage id={"common.status"}/>} >
                                                {projectStatus.find(it => (it.value === projectData.status))?.label}
                                                {(projectData.status !== 2 && permissions(auth.permissions, "operation.projects.main.setting.base.edit")) &&
                                                <Button type="link"
                                                        onClick={updateStatus}>{formatMessage({id: 'common.modify'})}</Button>}
                                            </Form.Item>
                                        </Col>
                                    </Row> */}
                      <Divider dashed />
                      <CustomTitle
                          name={intl.formatMessage({ id: "projects.brief" })}
                          extra={
                            !disable &&
                            projectSettingPermission(
                                projectData,
                                auth,
                                "operation.projects.main.setting.base.edit"
                            ) &&
                            !edit ? (
                                <Col className="text-right">
                                  <div
                                      className="mouse"
                                      style={{ color: "#165DFF" }}
                                      onClick={onEdit}
                                  >
                                    <i
                                        style={{ marginRight: 8 }}
                                        className="iconfont icon-bianji"
                                    />
                                    <span>
                                {intl.formatMessage({ id: "common.edit" })}
                              </span>
                                  </div>
                                </Col>
                            ) : null
                          }
                      />
                      <Row justify="start">
                        <Col
                            className="gutter-row"
                            span={16}
                            style={{ marginTop: 17 }}
                        >
                          <Form.Item
                              label={<FormattedMessage id={"projects.customer"} />}
                          >
                            {projectData.customerName}
                          </Form.Item>
                        </Col>
                        {/* <Col>
                                            {
                                                permissions(auth.permissions, "operation.projects.main.setting.base.edit") &&
                                                <Button onClick={onEdit} icon={<FormOutlined/>}/>
                                            }
                                        </Col>  */}
                      </Row>
                      <Col
                          className="gutter-row"
                          span={16}
                          style={{ marginBottom: 16 }}
                      >
                        <Form.Item
                            label={<FormattedMessage id={"projects.number"} />}
                        >
                          {projectData.info.number}
                        </Form.Item>
                      </Col>
                      <Col
                          className="gutter-row"
                          span={24}
                          style={{ marginBottom: 16 }}
                      >
                        {edit ? (
                            <Form.Item
                                label={formatMessage({ id: "projects.sponsor" })}
                                name={["info", "sponsor"]}
                                rules={[
                                  {
                                    required: true,
                                    message: formatMessage({
                                      id: "projects.sponsor.enter",
                                    }),
                                  },
                                ]}
                                className="mar-ver-5"
                            >
                              <Input
                                  placeholder={formatMessage({
                                    id: "placeholder.input.common",
                                  })}
                                  allowClear
                                  maxLength={100}
                                  showCount
                              />
                            </Form.Item>
                        ) : (
                            <Form.Item
                                label={formatMessage({ id: "projects.sponsor" })}
                            >
                              {projectData.info.sponsor !== ""
                                  ? projectData.info.sponsor
                                  : "-"}
                            </Form.Item>
                        )}
                      </Col>
                      <Col
                          className="gutter-row"
                          span={24}
                          style={{ marginBottom: 16 }}
                      >
                        {edit ? (
                            <Form.Item
                                label={formatMessage({ id: "projects.name" })}
                                name={["info", "name"]}
                                rules={[
                                  {
                                    required: true,
                                    message: formatMessage({
                                      id: "projects.name.enter",
                                    }),
                                  },
                                ]}
                                className="mar-ver-5"
                            >
                              <Input
                                  placeholder={formatMessage({
                                    id: "placeholder.input.common",
                                  })}
                                  allowClear
                                  maxLength={100}
                                  showCount
                              />
                            </Form.Item>
                        ) : (
                            <Form.Item label={formatMessage({ id: "projects.name" })}>
                              {projectData.info.name !== ""
                                  ? projectData.info.name
                                  : "-"}
                            </Form.Item>
                        )}
                      </Col>
                      <Col
                          className="gutter-row"
                          span={24}
                          style={{ marginBottom: 16 }}
                      >
                        <Form.Item
                            label={formatMessage({ id: "projects.startDate" })}
                            name={["info", "cycleTime", 0]}
                        >
                          {edit ? (
                              <DatePicker
                                  onChange={(date, dateString) =>
                                      handleChangeDate(dateString, "start")
                                  }
                                  style={{ width: "100%" }}
                                  suffixIcon={<CalendarOutlined />}
                              />
                          ) : (
                              <>
                                {projectData.info.cycleTime &&
                                projectData.info.cycleTime[0]
                                    ? projectData.info.cycleTime[0].format("YYYY-MM-DD")
                                    : "-"}
                              </>
                          )}
                        </Form.Item>
                        <Form.Item
                            label={formatMessage({ id: "projects.endDate" })}
                            name={["info", "cycleTime", 1]}
                        >
                          {edit ? (
                              <DatePicker
                                  onChange={(e) => handleChangeDate(e, "end")}
                                  style={{ width: "100%" }}
                                  suffixIcon={<CalendarOutlined />}
                              />
                          ) : (
                              <>
                                {projectData.info.cycleTime &&
                                projectData.info.cycleTime[1]
                                    ? projectData.info.cycleTime[1].format("YYYY-MM-DD")
                                    : "-"}
                              </>
                          )}
                        </Form.Item>
                      </Col>
                      {/*<Col className="gutter-row" span={24} style={{ marginBottom: 16 }}>*/}
                      {/*    {edit ? (*/}
                      {/*        <>*/}
                      {/*            <Form.Item*/}
                      {/*                label={formatMessage({ id: "projects.plannedCases" })}*/}
                      {/*                className="mar-ver-5"*/}
                      {/*                rules={[*/}
                      {/*                    {*/}
                      {/*                        type: "number",*/}
                      {/*                        min: 1,*/}
                      {/*                        message: formatMessage({*/}
                      {/*                            id: "projects.plannedCases.enter.min",*/}
                      {/*                        }),*/}
                      {/*                    },*/}
                      {/*                    {*/}
                      {/*                        required: activeKey === "1",*/}
                      {/*                        message: formatMessage({*/}
                      {/*                            id: "projects.plannedCases.enter",*/}
                      {/*                        }),*/}
                      {/*                    },*/}
                      {/*                ]}*/}
                      {/*                name={["info", "plannedCases"]}*/}
                      {/*            >*/}
                      {/*                <InputNumber*/}
                      {/*                    style={{ width: "100%" }}*/}
                      {/*                    placeholder={formatMessage({ id: "common.required.prefix" })}*/}
                      {/*                    precision={0}*/}
                      {/*                />*/}
                      {/*            </Form.Item>*/}
                      {/*        </>*/}
                      {/*    ) : (*/}
                      {/*        <Form.Item label={formatMessage({ id: "projects.plannedCases" })}>*/}
                      {/*            {projectData.info.plannedCases != null? projectData.info.plannedCases : "-"}*/}
                      {/*        </Form.Item>*/}
                      {/*    )}*/}
                      {/*</Col>*/}
                      <Col
                          className="gutter-row"
                          span={24}
                          style={{ marginBottom: 16 }}
                      >
                        {edit ? (
                            <Tooltip
                                placement="top"
                                title={formatMessage({
                                  id: "placeholder.input.contact",
                                })}
                            >
                              <Form.Item
                                  rules={[phoneNumberValidator]}
                                  label={formatMessage({
                                    id: "projects.contact.information",
                                  })}
                                  name={["info", "phone"]}
                                  className="mar-ver-5"
                              >
                                <Input
                                    placeholder={formatMessage({
                                      id: "placeholder.input.contact",
                                    })}
                                    allowClear
                                    maxLength={100}
                                    showCount
                                />
                              </Form.Item>
                            </Tooltip>
                        ) : (
                            <Form.Item
                                label={formatMessage({
                                  id: "projects.contact.information",
                                })}
                            >
                              {projectData.info.phone !== ""
                                  ? projectData.info.phone
                                  : "-"}
                            </Form.Item>
                        )}
                      </Col>
                      <Col
                          className="gutter-row"
                          span={24}
                          style={{ marginBottom: 16 }}
                      >
                        {edit ? (
                            <Form.Item
                                label={formatMessage({
                                  id: "projects.remark",
                                })}
                                name={["info", "description"]}
                                className="mar-ver-5"
                            >
                              <Input.TextArea
                                  placeholder={formatMessage({
                                    id: "placeholder.input.common",
                                  })}
                                  allowClear
                                  maxLength={500}
                                  showCount
                              />
                            </Form.Item>
                        ) : (
                            <Form.Item
                                label={formatMessage({
                                  id: "projects.remark",
                                })}
                            >
                              {projectData.info.description !== ""
                                  ? projectData.info.description
                                  : "-"}
                            </Form.Item>
                        )}
                      </Col>
                    </TabPane>
                )}
                {projectSettingPermission(
                    projectData,
                    auth,
                    "operation.projects.main.config.view"
                ) && (
                    <TabPane
                        tab={
                          <>
                            <i
                                className="iconfont icon-quanbuxiangmu"
                                style={{ marginRight: 8 }}
                            />
                            {formatMessage({ id: "menu.projects.main.env" })}
                          </>
                        }
                        key="4"
                    >
                      <Layout.Header
                          style={{ height: "24px", background: "#FFFFFF" }}
                      />
                      <ProjectInfo project={projectData} editable={edit} />
                    </TabPane>
                )}
                {projectSettingPermission(
                    projectData,
                    auth,
                    "operation.projects.main.setting.function.view"
                ) && (
                    <TabPane
                        tab={
                          <>
                            <i
                                className="iconfont icon-yewuduijie"
                                style={{ marginRight: 8 }}
                            />
                            {formatMessage({
                              id: "menu.projects.main.setting.function",
                            })}
                          </>
                        }
                        key="2"
                    >
                      <Layout.Header
                          style={{ height: "24px", background: "#FFFFFF" }}
                      />
                      <Row>
                        <Col className="gutter-row">
                          <Form.Item
                              // style={{width: 90}}
                              label={
                                <>
                                  {formatMessage({ id: "common.timezone" })}
                                  <Tooltip
                                      overlayInnerStyle={{ width: 600 }}
                                      placement="top"
                                      title={
                                        <FormattedMessage id="tool.tip.timezone" />
                                      }
                                  >
                                    <QuestionCircleFilled
                                        style={{ color: "#D0D0D0" }}
                                    />
                                  </Tooltip>
                                </>
                              }
                              name={["info", "tz"]}
                              className="mar-ver-5"
                          >
                            {/* {!edit ? projectUtc.find(it => it.value === form.getFieldValue(["info", "timeZone"]))?.label : <Select
                                disabled={disable || !edit}
                                options={projectUtc}
                                style={{ width: 400 }}
                            >
                            </Select>} */}
                            <SelectUI
                                  allowClear
                                  placeholder={intl.formatMessage({ id: "placeholder.select" })}
                                  showSearch
                                  filterOption={(input, option) =>
                                      `${option.label}`.toLowerCase().includes(input.toLowerCase())
                                  }
                                  style={{width: '430px'}}
                                  disabled={!edit?true:false}
                                  options={tzOptions.map((it) => ({
                                      label: `(${it.offset}) ${it[g.lang === "zh" ? "zh" : "en"]}`,
                                      value: it.location,
                                  }))}
                              />
                          </Form.Item>
                        </Col>
                      </Row>
                      <Row>
                        {projectData?.info?.researchAttribute !== 1 && (
                            <Col className="gutter-row" style={{ paddingTop: 0 }}>
                              <Form.Item
                                  // style={{width: 164}}
                                  label={formatMessage({ id: "projects.orderCheck" })}
                                  initialValue={1}
                                  name={["info", "orderCheck"]}
                                  className="mar-ver-5"
                              >
                                <Radio.Group
                                    disabled={disable || !edit}
                                    style={{ width: 480 }}
                                >
                                  <Radio value={1}>
                                    <FormattedMessage id="projects.timing" />
                                  </Radio>
                                  <Radio value={2}>
                                    <FormattedMessage id="projects.realTime" />
                                    <span style={{fontSize: '12px', color: '#434953'}}>
                                  {formatMessage({id: 'projects.manualRun'})}
                                </span>
                                  </Radio>
                                  <Radio value={3}>
                                    <FormattedMessage id="projects.notApplicable" />
                                  </Radio>
                                </Radio.Group>
                              </Form.Item>
                            </Col>
                        )}
                      </Row>
                      {projectData?.info?.researchAttribute !== 1 && (orderCheck === 1) ? (
                          <Row>
                            <Col className="gutter-row">
                              <Form.Item
                                  label={<FormattedMessage id={"projects.customTime"}/>}
                                  className="mar-ver-5"
                                  required={edit}
                              >
                                {!edit ? <span>{orderCheckDayShow()}</span> : <Space.Compact>
                                  <Form.Item
                                      name={["info", "orderCheckDay"]}
                                      noStyle
                                      rules={[{validator: (_, value) => {
                                          if (value.length === 0) {
                                            return Promise.reject(formatMessage({ id: "projects.orderCheckWeek.noEmpty" }))
                                          }
                                          return Promise.resolve()
                                        }}]}
                                  >
                                    <Select
                                        style={{width: '230px'}}
                                        options={WeekDay}
                                        mode={"multiple"}
                                        showArrow={true}
                                        showSearch={false}
                                        maxTagCount={2}
                                        placeholder={formatMessage({id: 'placeholder.select.common'})}
                                        disabled={disable || !edit}

                                        maxTagPlaceholder={(omittedValues) => (
                                            <Tooltip
                                                styles={{ root: { pointerEvents: 'none' } }}
                                                title={getWeekText(omittedValues.map(it => it.value)).join(", ")}
                                            >
                                              <span>{`+ ${omittedValues.length} ...`}</span>
                                            </Tooltip>
                                        )}
                                    />
                                  </Form.Item>
                                  <Form.Item name={["info", "orderCheckTime"]} noStyle>
                                    <TimePicker
                                        format="HH:mm"
                                        allowClear={false}
                                        style={{width: '170px'}}
                                        disabled={disable || !edit}
                                        popupClassName={'order-check-time-picker'}
                                        renderExtraFooter={() => (
                                            <div className={'order-check-time-header'}>
                                              <span>{formatMessage({id: 'project.overview.hour'})}</span>
                                              <Divider
                                                  type={'vertical'}
                                                  style={{margin: 0, borderLeft: '1px solid rgba(235, 235, 237, 1)', height: '28px'}}
                                              />
                                              <span>{formatMessage({id: 'time.minute'})}</span>
                                            </div>
                                        )}
                                    />
                                  </Form.Item>
                                </Space.Compact>}
                              </Form.Item>
                            </Col>
                          </Row>
                      ) : null}
                      <Row>
                        <Col className="gutter-row">
                          <Form.Item
                              //style={{width: 235}}
                              name={["info", "orderConfirmation"]}
                              className="mar-ver-5"
                              label={
                                <>
                                  <FormattedMessage
                                      id={"projects.recycling.confirmation"}
                                  />
                                  <Tooltip
                                      overlayInnerStyle={{ width: 500 }}
                                      placement="top"
                                      title={
                                        <FormattedMessage id="projects.recycling.confirmation.tip" />
                                      }
                                  >
                                    <QuestionCircleFilled
                                        style={{ marginLeft: "4px", color: "#D0D0D0" }}
                                    />
                                  </Tooltip>
                                </>
                              }
                          >
                            <ControlSwitch disabled={disable || !edit} />
                          </Form.Item>
                        </Col>
                      </Row>
                      <Row>
                        <Col className="gutter-row">
                          <Form.Item
                              // style={{width: 143}}
                              name={["info", "deIsolationApproval"]}
                              className="mar-ver-5"
                              label={
                                <>
                                  <FormattedMessage
                                      id={"project.de.isolation.approval"}
                                  />
                                  <Tooltip
                                      overlayInnerStyle={{ width: 500 }}
                                      placement="top"
                                      title={
                                        <FormattedMessage id="project.de.isolation.approval.tip" />
                                      }
                                  >
                                    <QuestionCircleFilled
                                        style={{ marginLeft: "4px", color: "#D0D0D0" }}
                                    />
                                  </Tooltip>
                                </>
                              }
                          >
                            <ControlSwitch disabled={disable || !edit} />
                          </Form.Item>
                        </Col>
                      </Row>
                      <CustomTitle
                          name={intl.formatMessage({ id: "common.administrator" })}
                      />
                      <Row>
                        <Col style={{ marginTop: 12 }}>
                          <Table
                              size="small"
                              style={{paddingBottom: 84}} // 64+20
                              dataSource={getUserData()}
                              pagination={false}
                              id="table"
                              rowKey={(record) => record.cloudId}
                              getPopupContainer={() =>
                                  document.getElementById("table")
                              }
                          >
                            <Table.Column
                                title={formatMessage({ id: "common.serial" })}
                                key="#"
                                width={50}
                                render={(v, r, i) => i + 1}
                            />
                            <Table.Column
                                title={<FormattedMessage id="common.email" />}
                                width={350}
                                dataIndex={["info", "email"]}
                                key="email"
                                ellipsis
                                {...getColumnSearchProps("email")}
                            />
                            <Table.Column
                                title={<FormattedMessage id="user.name" />}
                                width={120}
                                dataIndex={["info", "name"]}
                                key="name"
                                ellipsis
                                {...getColumnSearchProps("name")}
                                render={(v) => v || "-"}
                            />
                            <Table.Column
                                title={formatMessage({ id: "common.operation" })}
                                dataIndex="admin"
                                key="admin"
                                width={80}
                                align="center"
                                render={(v, r, i) => (
                                    <Switch
                                        size="small"
                                        disabled={
                                            disable ||
                                            !edit ||
                                            !projectSettingPermission(
                                                projectData,
                                                auth,
                                                "operation.projects.main.setting.function.admin"
                                            )
                                        }
                                        checked={v}
                                        onChange={(v) =>
                                            v ? addAdmin(r.id) : removeAdmin(r.id)
                                        }
                                    />
                                )}
                            />
                          </Table>
                        </Col>
                      </Row>
                    </TabPane>
                )}
                {projectSettingPermission(
                    projectData,
                    auth,
                    "operation.projects.main.setting.docking.view"
                ) && (
                    <TabPane
                        tab={
                          <>
                            <i
                                className="iconfont icon-waibuduijie"
                                style={{ marginRight: 8 }}
                            />
                            {formatMessage({
                              id: "menu.projects.main.setting.docking",
                            })}
                          </>
                        }
                        key="3"
                    >
                      <Layout.Header
                          style={{ height: "24px", background: "#FFFFFF" }}
                      />

                      <Tabs defaultActiveKey="1" scrollable size="small" onChange={onTabsChange}>
                        <TabPane tab="EDC" key="1">
                          {/* <CustomTitle name="EDC" /> */}
                          <Row gutter={12}>
                            <Col className="gutter-row" span={24}>
                              <Form.Item
                                  {...dockingLayout}
                                  label={
                                    <>
                                      <FormattedMessage id={"projects.connectEdc"} />
                                      <Tooltip
                                          overlayInnerStyle={{ width: 450 }}
                                          placement="top"
                                          title={<FormattedMessage id="tool.tip.edc" />}
                                      >
                                        <QuestionCircleFilled
                                            style={{
                                              color: "#C8C9CC",
                                              marginLeft: "2px",
                                            }}
                                        />
                                      </Tooltip>
                                    </>
                                  }
                                  name={["info", "connectEdc"]}
                                  className="mar-ver-8"
                              >
                                <CustomSwitch
                                    disabled={disable || edcPush || !edit}
                                    // disabled={
                                    //   disable ||
                                    //   projectData.info.researchAttribute === 1 ||
                                    //   projectData.info.connectEdc === 1 ||
                                    //   !edit
                                    // }
                                    openCallback={onConnectEdc}
                                />
                              </Form.Item>
                            </Col>
                            <Col className="gutter-row" span={24}>
                              <Form.Item
                                  dependencies={[["info", "connectEdc"]]}
                                  noStyle
                              >
                                {({ getFieldValue }) => {
                                  if (getFieldValue(["info", "connectEdc"]) === 1) {
                                    return (
                                        <span>
                                      {/* {edit ? ( */}
                                          <Form.Item
                                              {...dockingLayout}
                                              label={formatMessage({ id: "projects.connectEdc.supplier", })}
                                              name={["info", "edcSupplier"]}
                                              // name={["info", "0 if 'projectData.info.edcSupplier' == 0 else 1"]}
                                              defaultValue={1}
                                              // rules={[
                                              //   {
                                              //     // (disable || edcPush || !edit)?false:true
                                              //     required: ((disable || edcPush || !edit) && edit)?false:true,
                                              //     message: formatMessage({
                                              //       id: "common.select.supplier",
                                              //     }),
                                              //   },
                                              // ]}
                                              className="mar-ver-8"
                                          >
                                        {edit ? (
                                            <Select
                                                placeholder={
                                                  formatMessage({ id: "placeholder.select.common", })
                                                }
                                                disabled={disable || edcPush || !edit}
                                                // disabled={
                                                //   // disable ||
                                                //   // projectData.info.researchAttribute === 1 ||
                                                //   projectData.info.connectEdc === 1 &&
                                                //   edcSupplier !== 0
                                                //   // ||
                                                //   // !edit
                                                // }
                                                onChange={handleChange}
                                                // initialValue={1}
                                                defaultValue={1}
                                                // value={1} // 使用 value 属性设置默认值
                                                options={EdcSupplier}
                                                style={{ width: 400 }}
                                            ></Select>
                                        ) : (projectData.info.edcSupplier !== 0
                                            ? projectData.info.edcSupplier == 1
                                                ? "Clinflash  EDC"
                                                : "Medidata Rave  EDC"
                                            : "-")
                                        }

                                      </Form.Item>
                                    </span>
                                    );
                                  }
                                }}
                              </Form.Item>
                            </Col>
                            <Col className="gutter-row" span={24}>
                              <Form.Item
                                  dependencies={[["info", "connectEdc"]]}
                                  noStyle
                              >
                                {({ getFieldValue }) => {
                                  if (getFieldValue(["info", "connectEdc"]) === 1) {
                                    return (
                                        <Form.Item
                                            {...dockingLayout}
                                            label={
                                              <>
                                                <FormattedMessage
                                                    id={"project.statistics.mode"}
                                                />
                                                {edcSupplier === 1 ? (
                                                    <Tooltip
                                                        overlayInnerStyle={{ width: 550 }}
                                                        placement="top"
                                                        title={
                                                          <>
                                                            <Row>
                                                              <FormattedMessage id="project.statistics.mode.real.tp" />
                                                            </Row>
                                                            <Row>
                                                              <FormattedMessage id="project.statistics.mode.active.tp" />
                                                            </Row>
                                                          </>
                                                        }
                                                    >
                                                      <QuestionCircleFilled
                                                          style={{
                                                            color: "#C8C9CC",
                                                            marginLeft: "2px",
                                                          }}
                                                      />
                                                    </Tooltip>
                                                ) : (
                                                    <Tooltip
                                                        overlayInnerStyle={{ width: 550 }}
                                                        placement="top"
                                                        title={
                                                          <>
                                                            <Row>
                                                              <FormattedMessage id="project.statistics.mode.active.tp" />
                                                            </Row>
                                                          </>
                                                        }
                                                    >
                                                      <QuestionCircleFilled
                                                          style={{
                                                            color: "#C8C9CC",
                                                            marginLeft: "2px",
                                                          }}
                                                      />
                                                    </Tooltip>
                                                )}
                                              </>
                                            }
                                            name={["info", "pushMode"]}
                                            // rules={[
                                            //   {
                                            //     required: ((disable || edcPush || !edit) && edit)?false:true,
                                            //     message: formatMessage({
                                            //       id: "project.statistics.select.mode",
                                            //     }),
                                            //   },
                                            // ]}
                                            className="mar-ver-8"
                                            onChange={onPushModeChange}
                                        >
                                          {edcSupplier === 0 || edcSupplier === 1 ? (
                                              <Radio.Group
                                                  disabled={disable || edcPush || !edit}
                                              >
                                                <Radio value={1}>
                                                  <FormattedMessage id="project.statistics.real" />
                                                </Radio>
                                                <Radio value={2}>
                                                  <FormattedMessage id="project.statistics.active" />
                                                </Radio>
                                              </Radio.Group>
                                          ) : (
                                              <Radio.Group
                                                  disabled={disable || edcPush || !edit}
                                              >
                                                <Radio value={2}>
                                                  <FormattedMessage id="project.statistics.active" />
                                                </Radio>
                                              </Radio.Group>
                                          )}
                                        </Form.Item>
                                    );
                                  }
                                }}
                              </Form.Item>
                            </Col>
                            {((edcSupplier === 1 || edcSupplier === 0)) ? (
                                <Col className="gutter-row" span={24}>
                                  <Form.Item
                                      dependencies={[["info", "pushMode"], ["info", "connectEdc"]]}
                                      noStyle
                                  >
                                    {({ getFieldValue }) => {
                                      if (getFieldValue(["info", "connectEdc"]) === 1) {
                                        if (getFieldValue(["info", "pushMode"]) === 1) {
                                          return (
                                              <Form.Item
                                                  label={formatMessage({
                                                    id: "projects.synchronization.mode",
                                                  })}
                                                  name={["info", "synchronizationMode"]}
                                                  // rules={[{ required: true }]}
                                                  className="mar-ver-5"
                                              >
                                                <Radio.Group disabled={disable || !edit}>
                                                  <Radio value={1}>
                                                    <FormattedMessage id="projects.step.by.synchronization" />
                                                  </Radio>
                                                  <Radio value={2}>
                                                    <FormattedMessage id="projects.one.time.full.synchronization" />
                                                  </Radio>
                                                </Radio.Group>
                                              </Form.Item>
                                          );
                                        } else if (getFieldValue(["info", "pushMode"]) === 2) {
                                          return (
                                              <>
                                                <div className="mar-ver-8">
                                                  <Form.Item
                                                      {...dockingLayout}
                                                      label="URL"
                                                      name={["info", "edcUrl"]}
                                                      rules={[
                                                        {
                                                          required: !!edit,
                                                          message: formatMessage({
                                                            id: "project.statistics.url",
                                                          }),
                                                        },
                                                      ]}
                                                      // className="mar-ver-8"
                                                  >
                                                    {!edit ? (
                                                        projectData.info.edcUrl
                                                    ) : (
                                                        <Input
                                                            placeholder={formatMessage({
                                                              id: "project.statistics.url",
                                                            })}
                                                            disabled={!edit}
                                                            allowClear
                                                            maxLength={100}
                                                            showCount
                                                            style={{ width: 400 }}
                                                        />
                                                    )}
                                                  </Form.Item>
                                                </div>
                                                <div className="mar-ver-8">
                                                  <Form.Item
                                                      {...dockingLayout}
                                                      label={
                                                        <>
                                                          <FormattedMessage id="projects.push.rules" />
                                                          <Tooltip
                                                              overlayInnerStyle={{ width: 550 }}
                                                              placement="top"
                                                              title={
                                                                <>
                                                                  <Row>
                                                                    <FormattedMessage id="projects.subject.no.tip" />
                                                                  </Row>
                                                                  <Row>
                                                                    <FormattedMessage id="projects.subject.uid.tip" />
                                                                  </Row>
                                                                </>
                                                              }
                                                          >
                                                            <QuestionCircleFilled
                                                                style={{
                                                                  color: "#C8C9CC",
                                                                  marginLeft: "2px",
                                                                }}
                                                            />
                                                          </Tooltip>
                                                        </>
                                                      }
                                                      name={["info", "pushRules"]}
                                                      // className="mar-ver-8"
                                                  >
                                                    <Radio.Group disabled={disable || !edit}>
                                                      <Radio value={2}>
                                                        <FormattedMessage id="projects.subject.uid" />
                                                      </Radio>
                                                      <Radio value={1}>
                                                        <FormattedMessage id="project.statistics.subject.number" />
                                                      </Radio>
                                                    </Radio.Group>
                                                  </Form.Item>
                                                </div>
                                                <div className="mar-ver-8">
                                                  <Form.Item
                                                      {...dockingLayout}
                                                      label={formatMessage({ id: "projects.push.scene"})}
                                                      rules={[{ required: false }]}
                                                      name=""
                                                      // className={edit? "mar-ver-8 required-form-item" : "mar-ver-8"}
                                                      className={edit? "push-scene-checkbox required-form-item" : "push-scene-checkbox colored-disable-checked-checkbox"}
                                                  >
                                                    <>
                                                      <Row style={{height: 32}} justify="start" align="middle" gutter={[24, 12]}>
                                                        <Col style={{height: 32, display: "flex", alignItems: "center"}} >
                                                          <Checkbox
                                                              disabled={disable || !edit}
                                                              checked={registerPushValue}
                                                              onChange={(e) => onRegisterPushChange(e)}
                                                          >
                                                            <FormattedMessage id="subject.register" />
                                                          </Checkbox>
                                                        </Col>
                                                        <Col>
                                                          <Checkbox
                                                              disabled={disable || !edit}
                                                              checked={updateRandomFrontPushValue}
                                                              onChange={(e) => onUpdateRandomFrontPushChange(e)}
                                                          >
                                                            <FormattedMessage id="projects.subject.update" />
                                                            <FormattedMessage style={{fontSize: 12}} id="projects.subject.update.front" />
                                                          </Checkbox>
                                                        </Col>
                                                        <Col>
                                                          <Checkbox
                                                              disabled={disable || !edit}
                                                              checked={updateRandomAfterPushValue}
                                                              onChange={(e) => onUpdateRandomAfterPushChange(e)}
                                                          >
                                                            <FormattedMessage id="projects.subject.update" />
                                                            <FormattedMessage style={{fontSize: 12}}  id="projects.subject.update.after" />
                                                          </Checkbox>
                                                        </Col>

                                                        <Col>
                                                          <Checkbox
                                                              disabled={disable || !edit}
                                                              checked={screenPushValue}
                                                              onChange={(e) => onScreenPushChange(e)}
                                                          >
                                                            <FormattedMessage id="subject.screen" />
                                                          </Checkbox>
                                                        </Col>

                                                        <Col>
                                                          <Checkbox
                                                              disabled={disable || !edit}
                                                              checked={dispensingPushValue}
                                                              onChange={(e) => onDispensingPushChange(e)}
                                                          >
                                                            <FormattedMessage id="projects.attributes.dispensing.yes" />
                                                          </Checkbox>
                                                        </Col>
                                                      </Row>
                                                      <Row style={{height: 32, marginTop: 12}} justify="start" align="middle" gutter={[24, 12]}>
                                                        <Col style={{paddingRight: 0}}>
                                                          <Checkbox
                                                              disabled={disable || !edit}
                                                              checked={randomPushValue}
                                                              onChange={(e) => onRandomPushChange(e)}
                                                          >
                                                            <FormattedMessage id="subject.random" />
                                                          </Checkbox>
                                                        </Col>
                                                        <Divider type="vertical" className="edc-docking-setting-vertical-divider"/>
                                                        <Switch checked={blockSwitch}
                                                                size="small"
                                                                disabled={disable || !edit || !randomPushValue}
                                                                onClick={handleBlockSwitchClick}/>
                                                        <Text disabled={disable || !edit || !randomPushValue || !blockSwitch}
                                                          style={{marginLeft: 8}}>
                                                          {formatMessage({ id: "projects.subject.block.str-after" })}
                                                        </Text>
                                                        {
                                                          blockSwitch?
                                                              <>
                                                                <Col>
                                                                  <Checkbox
                                                                      disabled={disable || !edit || !randomPushValue || !blockSwitch}
                                                                      checked={randomBlockPushValue}
                                                                      onChange={(e) => onStratificationBlockChange(e)}
                                                                  >
                                                                    <FormattedMessage id="projects.subject.stratification" />
                                                                  </Checkbox>
                                                                </Col>
                                                                <Col>
                                                                  <Checkbox
                                                                      disabled={disable || !edit || !randomPushValue || !blockSwitch}
                                                                      checked={formRandomBlockPushValue}
                                                                      onChange={(e) => onFormBlockChange(e)}
                                                                  >
                                                                    <FormattedMessage id="projects.subject.form" />
                                                                  </Checkbox>
                                                                </Col>

                                                                {(projectCtx.projectType === 2 || projectCtx.projectType === 3) ?
                                                                    <Col>
                                                                      <Checkbox
                                                                          disabled={disable || !edit || !randomPushValue || !blockSwitch}
                                                                          checked={cohortRandomBlockPushValue}
                                                                          onChange={(e) => onCohortBlockChange(e)}
                                                                      >
                                                                        {
                                                                          projectCtx.projectType === 3?
                                                                              <FormattedMessage id="projects.subject.stageName" />
                                                                              :
                                                                              <FormattedMessage id="projects.subject.cohortName" />
                                                                        }
                                                                      </Checkbox>
                                                                    </Col>
                                                                    : null
                                                                }
                                                              </>
                                                              : null

                                                        }




                                                        {/*<Select*/}
                                                        {/*        style={{*/}
                                                        {/*            marginLeft: 8,  // 左外边距*/}
                                                        {/*            marginRight: 8,  // 右外边距*/}
                                                        {/*            width: 250,*/}
                                                        {/*        }}*/}
                                                        {/*        showArrow*/}
                                                        {/*        showSearch={false}*/}
                                                        {/*        className="custom-multi-select-with-slash"*/}
                                                        {/*        mode="multiple"*/}
                                                        {/*        placeholder={*/}
                                                        {/*            formatMessage({ id: "placeholder.select.common", })*/}
                                                        {/*        }*/}
                                                        {/*        disabled={disable || !edit || !randomPushValue}*/}
                                                        {/*        onChange={onBlockOptionChange}*/}
                                                        {/*        value={[*/}
                                                        {/*            ...(randomBlockPushValue ? [1] : []),*/}
                                                        {/*            ...(formRandomBlockPushValue ? [2] : []),*/}
                                                        {/*            ...(projectCtx.projectType !== 1 && cohortRandomBlockPushValue ? [3] : [])*/}
                                                        {/*        ]}*/}
                                                        {/*        options={getOptions()}*/}
                                                        {/*        tagRender={(props) => {*/}
                                                        {/*            // 获取当前所有选中值*/}
                                                        {/*            const selectedValues = [*/}
                                                        {/*                ...(randomBlockPushValue ? [1] : []),*/}
                                                        {/*                ...(formRandomBlockPushValue ? [2] : []),*/}
                                                        {/*                ...(projectCtx.projectType !== 1 && cohortRandomBlockPushValue ? [3] : [])*/}
                                                        {/*            ];*/}

                                                        {/*            // 判断是否是最后一个选中项*/}
                                                        {/*            const isLastSelected = props.value === selectedValues[selectedValues.length - 1];*/}

                                                        {/*            return (*/}
                                                        {/*                <span className="no-tag-style">*/}
                                                        {/*              {props.label}*/}
                                                        {/*                    {!isLastSelected && <span style={{ margin: '0' }}>/</span>}*/}
                                                        {/*            </span>*/}
                                                        {/*            );*/}
                                                        {/*        }}*/}
                                                        {/*    >*/}
                                                        {/*    </Select>*/}
                                                        {/*    <Text disabled={disable || !edit || !randomPushValue}>*/}
                                                        {/*        {formatMessage({ id: "projects.subject.block.str-after" })}*/}
                                                        {/*    </Text>*/}


                                                      </Row>
                                                    </>
                                                  </Form.Item>
                                                </div>
                                              </>
                                          );
                                        }
                                      }
                                    }}
                                  </Form.Item>
                                </Col>
                            ) : (
                                <Col className="gutter-row" span={24}>
                                  <Form.Item
                                      dependencies={[["info", "pushMode"], ["info", "connectEdc"]]}
                                      noStyle
                                  >
                                    {({ getFieldValue }) => {
                                      if (getFieldValue(["info", "connectEdc"]) !== 1)
                                        return null;
                                      if (getFieldValue(["info", "pushMode"]) === 2) {
                                        return (
                                            <>
                                              <Form.Item
                                                  label="URL"
                                                  name={["info", "edcUrl"]}
                                                  rules={[
                                                    {
                                                      required: !!edit,
                                                      message: formatMessage({
                                                        id: "project.statistics.url",
                                                      }),
                                                    },
                                                  ]}
                                                  className="mar-ver-5"
                                              >
                                                {!edit ? (
                                                    projectData.info.edcUrl
                                                ) : (
                                                    <Input
                                                        placeholder={formatMessage({
                                                          id: "project.statistics.url",
                                                        })}
                                                        disabled={!edit}
                                                        allowClear
                                                        maxLength={100}
                                                        showCount
                                                    />
                                                )}
                                              </Form.Item>
                                              <CustomTitle
                                                  name={formatMessage({
                                                    id: "projects.connectEdc.mapping.configuration",
                                                  })}
                                              />
                                              {projectData.info.type !== 1 ? (
                                                  // <Form form={visitForm} component={false}>
                                                  <Tabs
                                                      defaultActiveKey="1"
                                                      scrollable
                                                      className="block-tab"
                                                      size="small"
                                                      style={{ marginTop: 8 }}
                                                  >
                                                    {projectData.info.visitRandomization?.visits?.map(
                                                        (visit, index) => (
                                                            <TabPane
                                                                tab={visit?.name}
                                                                key={index + 1}
                                                            >
                                                              <MyTable
                                                                  key={Math.random()}
                                                                  size="small"
                                                                  dataSource={
                                                                    visit?.visitConfigs
                                                                  }
                                                                  // rowKey={(record,key) => (key)}
                                                                  pagination={false}
                                                                  className="custom-table"
                                                                  components={{
                                                                    body: {
                                                                      cell: EditableCell,
                                                                    },
                                                                  }}
                                                              >
                                                                <Table.Column
                                                                    width={240}
                                                                    title={() => (
                                                                        <div
                                                                            style={{
                                                                              position: "relative",
                                                                            }}
                                                                        >
                                                                          <div
                                                                              style={{
                                                                                position:
                                                                                    "absolute",
                                                                                right: "2px",
                                                                                bottom: "-10px",
                                                                              }}
                                                                          >
                                                                            {formatMessage({
                                                                              id: "project.external.edc.rave.mapping.edcOid",
                                                                            })}
                                                                          </div>
                                                                          <div
                                                                              style={{
                                                                                position:
                                                                                    "absolute",
                                                                                left: "-6px",
                                                                                top: "-6px",
                                                                              }}
                                                                          >
                                                                            {formatMessage({
                                                                              id: "project.external.edc.rave.mapping.visitCode",
                                                                            })}
                                                                          </div>
                                                                          <DividerLine />
                                                                        </div>
                                                                    )}
                                                                    dataIndex="visitCode"
                                                                    key={"visitCode"}
                                                                    ellipsis
                                                                    render={(
                                                                        value,
                                                                        record,
                                                                        i
                                                                    ) => {
                                                                      return edit ? (
                                                                          // 显示编辑框
                                                                          <Form.Item
                                                                              // name={"visitCode"}
                                                                              name={[
                                                                                "info",
                                                                                "visitRandomization",
                                                                                "visits",
                                                                                index,
                                                                                "visitConfigs",
                                                                                i,
                                                                                "visitCode",
                                                                              ]}
                                                                              initialValue={value}
                                                                              noStyle
                                                                              // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'project.external.edc.rave.mapping.visitCode'}) }]}
                                                                          >
                                                                            <Input
                                                                                placeholder={formatMessage(
                                                                                    {
                                                                                      id: "placeholder.input.common",
                                                                                    }
                                                                                )}
                                                                            />
                                                                          </Form.Item>
                                                                      ) : (
                                                                          // 显示文本
                                                                          value || "-"
                                                                      );
                                                                    }}
                                                                />
                                                                <Table.Column
                                                                    title={formatMessage({
                                                                      id: "project.external.edc.rave.mapping.folderOid",
                                                                    })}
                                                                    dataIndex="folderOid"
                                                                    key="folderOid"
                                                                    ellipsis
                                                                    render={(
                                                                        value,
                                                                        record,
                                                                        i
                                                                    ) => {
                                                                      return edit ? (
                                                                          // 显示编辑框
                                                                          <Form.Item
                                                                              // name={"folderOid" + i}
                                                                              name={[
                                                                                "info",
                                                                                "visitRandomization",
                                                                                "visits",
                                                                                index,
                                                                                "visitConfigs",
                                                                                i,
                                                                                "folderOid",
                                                                              ]}
                                                                              initialValue={value}
                                                                              noStyle
                                                                              // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'project.external.edc.rave.mapping.folderOid'}) }]}
                                                                          >
                                                                            <Input
                                                                                placeholder={formatMessage(
                                                                                    {
                                                                                      id: "placeholder.input.common",
                                                                                    }
                                                                                )}
                                                                            />
                                                                          </Form.Item>
                                                                      ) : (
                                                                          // 显示文本
                                                                          value || "-"
                                                                      );
                                                                    }}
                                                                />
                                                                <Table.Column
                                                                    title={formatMessage({
                                                                      id: "project.external.edc.rave.mapping.formOid",
                                                                    })}
                                                                    dataIndex="formOid"
                                                                    key="formOid"
                                                                    ellipsis
                                                                    render={(
                                                                        value,
                                                                        record,
                                                                        i
                                                                    ) => {
                                                                      return edit ? (
                                                                          // 显示编辑框
                                                                          <Form.Item
                                                                              // name={"formOid" + i}
                                                                              name={[
                                                                                "info",
                                                                                "visitRandomization",
                                                                                "visits",
                                                                                index,
                                                                                "visitConfigs",
                                                                                i,
                                                                                "formOid",
                                                                              ]}
                                                                              initialValue={value}
                                                                              noStyle
                                                                              // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'project.external.edc.rave.mapping.formOid'}) }]}
                                                                          >
                                                                            <Input
                                                                                placeholder={formatMessage(
                                                                                    {
                                                                                      id: "placeholder.input.common",
                                                                                    }
                                                                                )}
                                                                            />
                                                                          </Form.Item>
                                                                      ) : (
                                                                          // 显示文本
                                                                          value || "-"
                                                                      );
                                                                    }}
                                                                />
                                                                <Table.Column
                                                                    title={formatMessage({
                                                                      id: "project.external.edc.rave.mapping.ipNumberOid",
                                                                    })}
                                                                    dataIndex="ipNumberOid"
                                                                    key="ipNumberOid"
                                                                    ellipsis
                                                                    render={(
                                                                        value,
                                                                        record,
                                                                        i
                                                                    ) => {
                                                                      return edit ? (
                                                                          // 显示编辑框
                                                                          <Form.Item
                                                                              // name={"ipNumberOid" + i}
                                                                              name={[
                                                                                "info",
                                                                                "visitRandomization",
                                                                                "visits",
                                                                                index,
                                                                                "visitConfigs",
                                                                                i,
                                                                                "ipNumberOid",
                                                                              ]}
                                                                              initialValue={value}
                                                                              noStyle
                                                                              // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'project.external.edc.rave.mapping.ipNumberOid'}) }]}
                                                                          >
                                                                            <Input
                                                                                placeholder={formatMessage(
                                                                                    {
                                                                                      id: "placeholder.input.common",
                                                                                    }
                                                                                )}
                                                                            />
                                                                          </Form.Item>
                                                                      ) : (
                                                                          // 显示文本
                                                                          value || "-"
                                                                      );
                                                                    }}
                                                                />
                                                                <Table.Column
                                                                    title={formatMessage({
                                                                      id: "project.external.edc.rave.mapping.dispenseTimeOid",
                                                                    })}
                                                                    dataIndex="dispenseTimeOid"
                                                                    key="dispenseTimeOid"
                                                                    ellipsis
                                                                    render={(
                                                                        value,
                                                                        record,
                                                                        i
                                                                    ) => {
                                                                      return edit ? (
                                                                          // 显示编辑框
                                                                          <Form.Item
                                                                              // name={"dispenseTimeOid" + i}
                                                                              name={[
                                                                                "info",
                                                                                "visitRandomization",
                                                                                "visits",
                                                                                index,
                                                                                "visitConfigs",
                                                                                i,
                                                                                "dispenseTimeOid",
                                                                              ]}
                                                                              initialValue={value}
                                                                              noStyle
                                                                              // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'project.external.edc.rave.mapping.dispenseTimeOid'}) }]}
                                                                          >
                                                                            <Input
                                                                                placeholder={formatMessage(
                                                                                    {
                                                                                      id: "placeholder.input.common",
                                                                                    }
                                                                                )}
                                                                            />
                                                                          </Form.Item>
                                                                      ) : (
                                                                          // 显示文本
                                                                          value || "-"
                                                                      );
                                                                    }}
                                                                />
                                                                {edit && (
                                                                    <Table.Column
                                                                        fixed="right"
                                                                        title={
                                                                          <FormattedMessage id="common.operation" />
                                                                        }
                                                                        render={(
                                                                            item,
                                                                            record,
                                                                            i
                                                                        ) => (
                                                                            <Button
                                                                                type="link"
                                                                                onClick={() =>
                                                                                    deleteRow(
                                                                                        record,
                                                                                        index,
                                                                                        i
                                                                                    )
                                                                                }
                                                                            >
                                                                              {formatMessage({
                                                                                id: "common.delete",
                                                                              })}
                                                                            </Button>
                                                                        )}
                                                                    />
                                                                )}
                                                              </MyTable>
                                                              {edit && (
                                                                  <Button
                                                                      block
                                                                      type="dashed"
                                                                      style={{
                                                                        height: 36,
                                                                        marginBottom: 8,
                                                                      }}
                                                                      className="mar-top-10"
                                                                      icon={<PlusOutlined />}
                                                                      onClick={() =>
                                                                          addMedicine(index)
                                                                      }
                                                                  >
                                                      <span
                                                          style={{ fontSize: 12 }}
                                                      >
                                                        {intl.formatMessage({
                                                          id: "common.add",
                                                        })}
                                                      </span>
                                                                  </Button>
                                                              )}
                                                            </TabPane>
                                                        )
                                                    )}
                                                  </Tabs>
                                              ) : (
                                                  // </Form>
                                                  <>
                                                    {projectData.info.visitRandomization?.visits.map(
                                                        (visit, index) => (
                                                            <div style={{ marginTop: 24 }}>
                                                              <MyTable
                                                                  key={Math.random()}
                                                                  size="small"
                                                                  dataSource={
                                                                    visit.visitConfigs
                                                                  }
                                                                  // rowKey={(record,key) => (key)}
                                                                  pagination={false}
                                                                  className="custom-table"
                                                                  components={{
                                                                    body: {
                                                                      cell: EditableCell,
                                                                    },
                                                                  }}
                                                              >
                                                                <Table.Column
                                                                    width={240}
                                                                    title={() => (
                                                                        <div
                                                                            style={{
                                                                              position: "relative",
                                                                            }}
                                                                        >
                                                                          <div
                                                                              style={{
                                                                                position:
                                                                                    "absolute",
                                                                                right: "2px",
                                                                                bottom: "-10px",
                                                                              }}
                                                                          >
                                                                            {formatMessage({
                                                                              id: "project.external.edc.rave.mapping.edcOid",
                                                                            })}
                                                                          </div>
                                                                          <div
                                                                              style={{
                                                                                position:
                                                                                    "absolute",
                                                                                left: "-6px",
                                                                                top: "-6px",
                                                                              }}
                                                                          >
                                                                            {formatMessage({
                                                                              id: "project.external.edc.rave.mapping.visitCode",
                                                                            })}
                                                                          </div>
                                                                          <DividerLine />
                                                                        </div>
                                                                    )}
                                                                    dataIndex="visitCode"
                                                                    key={"visitCode"}
                                                                    ellipsis
                                                                    render={(
                                                                        value,
                                                                        record,
                                                                        i
                                                                    ) => {
                                                                      return edit ? (
                                                                          // 显示编辑框
                                                                          <Form.Item
                                                                              // name={"visitCode"}
                                                                              name={[
                                                                                "info",
                                                                                "visitRandomization",
                                                                                "visits",
                                                                                index,
                                                                                "visitConfigs",
                                                                                i,
                                                                                "visitCode",
                                                                              ]}
                                                                              initialValue={value}
                                                                              noStyle
                                                                              // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'project.external.edc.rave.mapping.visitCode'}) }]}
                                                                          >
                                                                            <Input
                                                                                placeholder={formatMessage(
                                                                                    {
                                                                                      id: "placeholder.input.common",
                                                                                    }
                                                                                )}
                                                                            />
                                                                          </Form.Item>
                                                                      ) : (
                                                                          // 显示文本
                                                                          value || "-"
                                                                      );
                                                                    }}
                                                                />
                                                                <Table.Column
                                                                    title={formatMessage({
                                                                      id: "project.external.edc.rave.mapping.folderOid",
                                                                    })}
                                                                    dataIndex="folderOid"
                                                                    key="folderOid"
                                                                    ellipsis
                                                                    render={(
                                                                        value,
                                                                        record,
                                                                        i
                                                                    ) => {
                                                                      return edit ? (
                                                                          // 显示编辑框
                                                                          <Form.Item
                                                                              // name={"folderOid" + i}
                                                                              name={[
                                                                                "info",
                                                                                "visitRandomization",
                                                                                "visits",
                                                                                index,
                                                                                "visitConfigs",
                                                                                i,
                                                                                "folderOid",
                                                                              ]}
                                                                              initialValue={value}
                                                                              noStyle
                                                                              // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'project.external.edc.rave.mapping.folderOid'}) }]}
                                                                          >
                                                                            <Input
                                                                                placeholder={formatMessage(
                                                                                    {
                                                                                      id: "placeholder.input.common",
                                                                                    }
                                                                                )}
                                                                            />
                                                                          </Form.Item>
                                                                      ) : (
                                                                          // 显示文本
                                                                          value || "-"
                                                                      );
                                                                    }}
                                                                />
                                                                <Table.Column
                                                                    title={formatMessage({
                                                                      id: "project.external.edc.rave.mapping.formOid",
                                                                    })}
                                                                    dataIndex="formOid"
                                                                    key="formOid"
                                                                    ellipsis
                                                                    render={(
                                                                        value,
                                                                        record,
                                                                        i
                                                                    ) => {
                                                                      return edit ? (
                                                                          // 显示编辑框
                                                                          <Form.Item
                                                                              // name={"formOid" + i}
                                                                              name={[
                                                                                "info",
                                                                                "visitRandomization",
                                                                                "visits",
                                                                                index,
                                                                                "visitConfigs",
                                                                                i,
                                                                                "formOid",
                                                                              ]}
                                                                              initialValue={value}
                                                                              noStyle
                                                                              // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'project.external.edc.rave.mapping.formOid'}) }]}
                                                                          >
                                                                            <Input
                                                                                placeholder={formatMessage(
                                                                                    {
                                                                                      id: "placeholder.input.common",
                                                                                    }
                                                                                )}
                                                                            />
                                                                          </Form.Item>
                                                                      ) : (
                                                                          // 显示文本
                                                                          value || "-"
                                                                      );
                                                                    }}
                                                                />
                                                                <Table.Column
                                                                    title={formatMessage({
                                                                      id: "project.external.edc.rave.mapping.ipNumberOid",
                                                                    })}
                                                                    dataIndex="ipNumberOid"
                                                                    key="ipNumberOid"
                                                                    ellipsis
                                                                    render={(
                                                                        value,
                                                                        record,
                                                                        i
                                                                    ) => {
                                                                      return edit ? (
                                                                          // 显示编辑框
                                                                          <Form.Item
                                                                              // name={"ipNumberOid" + i}
                                                                              name={[
                                                                                "info",
                                                                                "visitRandomization",
                                                                                "visits",
                                                                                index,
                                                                                "visitConfigs",
                                                                                i,
                                                                                "ipNumberOid",
                                                                              ]}
                                                                              initialValue={value}
                                                                              noStyle
                                                                              // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'project.external.edc.rave.mapping.ipNumberOid'}) }]}
                                                                          >
                                                                            <Input
                                                                                placeholder={formatMessage(
                                                                                    {
                                                                                      id: "placeholder.input.common",
                                                                                    }
                                                                                )}
                                                                            />
                                                                          </Form.Item>
                                                                      ) : (
                                                                          // 显示文本
                                                                          value || "-"
                                                                      );
                                                                    }}
                                                                />
                                                                <Table.Column
                                                                    title={formatMessage({
                                                                      id: "project.external.edc.rave.mapping.dispenseTimeOid",
                                                                    })}
                                                                    dataIndex="dispenseTimeOid"
                                                                    key="dispenseTimeOid"
                                                                    ellipsis
                                                                    render={(
                                                                        value,
                                                                        record,
                                                                        i
                                                                    ) => {
                                                                      return edit ? (
                                                                          // 显示编辑框
                                                                          <Form.Item
                                                                              // name={"dispenseTimeOid" + i}
                                                                              name={[
                                                                                "info",
                                                                                "visitRandomization",
                                                                                "visits",
                                                                                index,
                                                                                "visitConfigs",
                                                                                i,
                                                                                "dispenseTimeOid",
                                                                              ]}
                                                                              initialValue={value}
                                                                              noStyle
                                                                              // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'project.external.edc.rave.mapping.dispenseTimeOid'}) }]}
                                                                          >
                                                                            <Input
                                                                                placeholder={formatMessage(
                                                                                    {
                                                                                      id: "placeholder.input.common",
                                                                                    }
                                                                                )}
                                                                            />
                                                                          </Form.Item>
                                                                      ) : (
                                                                          // 显示文本
                                                                          value || "-"
                                                                      );
                                                                    }}
                                                                />
                                                                {edit && (
                                                                    <Table.Column
                                                                        align="center"
                                                                        fixed="right"
                                                                        title={
                                                                          <FormattedMessage id="common.operation" />
                                                                        }
                                                                        render={(
                                                                            item,
                                                                            record,
                                                                            i
                                                                        ) => (
                                                                            <Button
                                                                                type="link"
                                                                                onClick={() =>
                                                                                    deleteRow(
                                                                                        record,
                                                                                        index,
                                                                                        i
                                                                                    )
                                                                                }
                                                                            >
                                                                              {formatMessage({
                                                                                id: "common.delete",
                                                                              })}
                                                                            </Button>
                                                                        )}
                                                                    />
                                                                )}
                                                              </MyTable>
                                                              {edit && (
                                                                  <Button
                                                                      block
                                                                      type="dashed"
                                                                      style={{
                                                                        height: 36,
                                                                        marginBottom: 8,
                                                                      }}
                                                                      className="mar-top-10"
                                                                      icon={<PlusOutlined />}
                                                                      onClick={() =>
                                                                          addMedicine(index)
                                                                      }
                                                                  >
                                                      <span
                                                          style={{ fontSize: 12 }}
                                                      >
                                                        {intl.formatMessage({
                                                          id: "common.add",
                                                        })}
                                                      </span>
                                                                  </Button>
                                                              )}
                                                            </div>
                                                        )
                                                    )}
                                                  </>
                                              )}

                                              {projectData.info.type === 3 ? (
                                                  <Tabs
                                                      defaultActiveKey="1"
                                                      scrollable
                                                      size="small"
                                                      style={{ marginTop: 16 }}
                                                      className="block-tab"
                                                  >
                                                    {projectData.info.visitRandomization?.randomizations.map(
                                                        (randomization, index) => (
                                                            <TabPane
                                                                tab={randomization.name}
                                                                key={index + 1}
                                                            >
                                                              <MyTable
                                                                  style={{ marginTop: 16 }}
                                                                  key={Math.random()}
                                                                  size="small"
                                                                  dataSource={
                                                                    randomization.randomizationConfigs
                                                                  }
                                                                  // rowKey={(record,key) => (key)}
                                                                  pagination={false}
                                                                  className="custom-table"
                                                                  components={{
                                                                    body: {
                                                                      cell: EditableCell,
                                                                    },
                                                                  }}
                                                              >
                                                                <Table.Column
                                                                    width={43}
                                                                    title={() => (
                                                                        <div
                                                                            style={{
                                                                              position: "relative",
                                                                            }}
                                                                        >
                                                                          <div
                                                                              style={{
                                                                                position:
                                                                                    "absolute",
                                                                                left: "155px",
                                                                                top: "-26px",
                                                                              }}
                                                                          >
                                                                            {formatMessage({
                                                                              id: "project.external.edc.rave.mapping.edcOid",
                                                                            })}
                                                                          </div>
                                                                          <div
                                                                              style={{
                                                                                position:
                                                                                    "absolute",
                                                                                left: "-6px",
                                                                                top: "-6px",
                                                                              }}
                                                                          >
                                                                            {formatMessage({
                                                                              id: "project.external.edc.rave.mapping.randomizationField",
                                                                            })}
                                                                          </div>
                                                                          <DividerLine />
                                                                        </div>
                                                                    )}
                                                                    dataIndex="randomizationField"
                                                                    key={"randomizationField"}
                                                                    ellipsis
                                                                    render={(
                                                                        value,
                                                                        record,
                                                                        i
                                                                    ) => {
                                                                      return edit ? (
                                                                          // 显示编辑框
                                                                          <Form.Item
                                                                              // name={"randomizationField"}
                                                                              name={[
                                                                                "info",
                                                                                "visitRandomization",
                                                                                "randomizations",
                                                                                index,
                                                                                "randomizationConfigs",
                                                                                i,
                                                                                "randomizationField",
                                                                              ]}
                                                                              initialValue={formatMessage(
                                                                                  {
                                                                                    id:
                                                                                        "project.external.edc.rave.mapping" +
                                                                                        value,
                                                                                  }
                                                                              )}
                                                                              // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'project.external.edc.rave.mapping.randomizationField'}) }]}
                                                                          >
                                                                            {formatMessage({
                                                                              id:
                                                                                  "project.external.edc.rave.mapping." +
                                                                                  value,
                                                                            })}
                                                                          </Form.Item>
                                                                      ) : (
                                                                          // 显示文本
                                                                          <span>
                                                            {formatMessage({
                                                              id:
                                                                  "project.external.edc.rave.mapping." +
                                                                  value,
                                                            }) || "-"}
                                                          </span>
                                                                      );
                                                                    }}
                                                                />
                                                                <Table.Column
                                                                    width={30}
                                                                    title={formatMessage({
                                                                      id: "project.external.edc.rave.mapping.folderOid",
                                                                    })}
                                                                    dataIndex="folderOid"
                                                                    key="folderOid"
                                                                    ellipsis
                                                                    render={(
                                                                        value,
                                                                        record,
                                                                        i
                                                                    ) => {
                                                                      return edit ? (
                                                                          // 显示编辑框
                                                                          <Form.Item
                                                                              // name={"folderOid" + i}
                                                                              name={[
                                                                                "info",
                                                                                "visitRandomization",
                                                                                "randomizations",
                                                                                index,
                                                                                "randomizationConfigs",
                                                                                i,
                                                                                "folderOid",
                                                                              ]}
                                                                              initialValue={value}
                                                                              // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'project.external.edc.rave.mapping.folderOid'}) }]}
                                                                          >
                                                                            <Input
                                                                                placeholder={formatMessage(
                                                                                    {
                                                                                      id: "placeholder.input.common",
                                                                                    }
                                                                                )}
                                                                            />
                                                                          </Form.Item>
                                                                      ) : (
                                                                          // 显示文本
                                                                          value || "-"
                                                                      );
                                                                    }}
                                                                />
                                                                <Table.Column
                                                                    width={30}
                                                                    title={formatMessage({
                                                                      id: "project.external.edc.rave.mapping.formOid",
                                                                    })}
                                                                    dataIndex="formOid"
                                                                    key="formOid"
                                                                    ellipsis
                                                                    render={(
                                                                        value,
                                                                        record,
                                                                        i
                                                                    ) => {
                                                                      return edit ? (
                                                                          // 显示编辑框
                                                                          <Form.Item
                                                                              // name={"formOid" + i}
                                                                              name={[
                                                                                "info",
                                                                                "visitRandomization",
                                                                                "randomizations",
                                                                                index,
                                                                                "randomizationConfigs",
                                                                                i,
                                                                                "formOid",
                                                                              ]}
                                                                              initialValue={value}
                                                                              // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'project.external.edc.rave.mapping.formOid'}) }]}
                                                                          >
                                                                            <Input
                                                                                placeholder={formatMessage(
                                                                                    {
                                                                                      id: "placeholder.input.common",
                                                                                    }
                                                                                )}
                                                                            />
                                                                          </Form.Item>
                                                                      ) : (
                                                                          // 显示文本
                                                                          value || "-"
                                                                      );
                                                                    }}
                                                                />
                                                                <Table.Column
                                                                    width={30}
                                                                    title={formatMessage({
                                                                      id: "project.external.edc.rave.mapping.fieldOid",
                                                                    })}
                                                                    dataIndex="fieldOid"
                                                                    key="fieldOid"
                                                                    ellipsis
                                                                    render={(
                                                                        value,
                                                                        record,
                                                                        i
                                                                    ) => {
                                                                      return edit ? (
                                                                          // 显示编辑框
                                                                          <Form.Item
                                                                              // name={"fieldOid" + i}
                                                                              name={[
                                                                                "info",
                                                                                "visitRandomization",
                                                                                "randomizations",
                                                                                index,
                                                                                "randomizationConfigs",
                                                                                i,
                                                                                "fieldOid",
                                                                              ]}
                                                                              initialValue={value}
                                                                              // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'project.external.edc.rave.mapping.ipNumberOid'}) }]}
                                                                          >
                                                                            <Input
                                                                                placeholder={formatMessage(
                                                                                    {
                                                                                      id: "placeholder.input.common",
                                                                                    }
                                                                                )}
                                                                            />
                                                                          </Form.Item>
                                                                      ) : (
                                                                          // 显示文本
                                                                          value || "-"
                                                                      );
                                                                    }}
                                                                />
                                                              </MyTable>
                                                            </TabPane>
                                                        )
                                                    )}
                                                  </Tabs>
                                              ) : (
                                                  <>
                                                    {projectData.info.visitRandomization?.randomizations.map(
                                                        (randomization, index) => (
                                                            <MyTable
                                                                style={{ marginTop: 24 }}
                                                                key={Math.random()}
                                                                size="small"
                                                                dataSource={
                                                                  randomization.randomizationConfigs
                                                                }
                                                                // rowKey={(record,key) => (key)}
                                                                pagination={false}
                                                                className="custom-table"
                                                                components={{
                                                                  body: {
                                                                    cell: EditableCell,
                                                                  },
                                                                }}
                                                            >
                                                              <Table.Column
                                                                  width={240}
                                                                  title={() => (
                                                                      <div
                                                                          style={{
                                                                            position: "relative",
                                                                          }}
                                                                      >
                                                                        <div
                                                                            style={{
                                                                              position: "absolute",
                                                                              right: "2px",
                                                                              bottom: "-10px",
                                                                            }}
                                                                        >
                                                                          {formatMessage({
                                                                            id: "project.external.edc.rave.mapping.edcOid",
                                                                          })}
                                                                        </div>
                                                                        <div
                                                                            style={{
                                                                              position: "absolute",
                                                                              left: "-6px",
                                                                              top: "-6px",
                                                                            }}
                                                                        >
                                                                          {formatMessage({
                                                                            id: "project.external.edc.rave.mapping.randomizationField",
                                                                          })}
                                                                        </div>
                                                                        <DividerLine />
                                                                      </div>
                                                                  )}
                                                                  dataIndex="randomizationField"
                                                                  key={"randomizationField"}
                                                                  ellipsis
                                                                  render={(
                                                                      value,
                                                                      record,
                                                                      i
                                                                  ) => {
                                                                    return edit ? (
                                                                        // 显示编辑框
                                                                        <Form.Item
                                                                            // name={"randomizationField"}
                                                                            name={[
                                                                              "info",
                                                                              "visitRandomization",
                                                                              "randomizations",
                                                                              index,
                                                                              "randomizationConfigs",
                                                                              i,
                                                                              "randomizationField",
                                                                            ]}
                                                                            initialValue={value}
                                                                            noStyle
                                                                            // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'project.external.edc.rave.mapping.randomizationField'}) }]}
                                                                        >
                                                                          {formatMessage({
                                                                            id:
                                                                                "project.external.edc.rave.mapping." +
                                                                                value,
                                                                          })}
                                                                        </Form.Item>
                                                                    ) : (
                                                                        // 显示文本
                                                                        <span>
                                                          {formatMessage({
                                                            id:
                                                                "project.external.edc.rave.mapping." +
                                                                value,
                                                          }) || "-"}
                                                        </span>
                                                                    );
                                                                  }}
                                                              />
                                                              <Table.Column
                                                                  title={formatMessage({
                                                                    id: "project.external.edc.rave.mapping.folderOid",
                                                                  })}
                                                                  dataIndex="folderOid"
                                                                  key="folderOid"
                                                                  ellipsis
                                                                  render={(
                                                                      value,
                                                                      record,
                                                                      i
                                                                  ) => {
                                                                    return edit ? (
                                                                        // 显示编辑框
                                                                        <Form.Item
                                                                            // name={"folderOid" + i}
                                                                            name={[
                                                                              "info",
                                                                              "visitRandomization",
                                                                              "randomizations",
                                                                              index,
                                                                              "randomizationConfigs",
                                                                              i,
                                                                              "folderOid",
                                                                            ]}
                                                                            initialValue={value}
                                                                            noStyle
                                                                            // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'project.external.edc.rave.mapping.folderOid'}) }]}
                                                                        >
                                                                          <Input
                                                                              placeholder={formatMessage(
                                                                                  {
                                                                                    id: "placeholder.input.common",
                                                                                  }
                                                                              )}
                                                                          />
                                                                        </Form.Item>
                                                                    ) : (
                                                                        // 显示文本
                                                                        value || "-"
                                                                    );
                                                                  }}
                                                              />
                                                              <Table.Column
                                                                  title={formatMessage({
                                                                    id: "project.external.edc.rave.mapping.formOid",
                                                                  })}
                                                                  dataIndex="formOid"
                                                                  key="formOid"
                                                                  ellipsis
                                                                  render={(
                                                                      value,
                                                                      record,
                                                                      i
                                                                  ) => {
                                                                    return edit ? (
                                                                        // 显示编辑框
                                                                        <Form.Item
                                                                            // name={"formOid" + i}
                                                                            name={[
                                                                              "info",
                                                                              "visitRandomization",
                                                                              "randomizations",
                                                                              index,
                                                                              "randomizationConfigs",
                                                                              i,
                                                                              "formOid",
                                                                            ]}
                                                                            initialValue={value}
                                                                            noStyle
                                                                            // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'project.external.edc.rave.mapping.formOid'}) }]}
                                                                        >
                                                                          <Input
                                                                              placeholder={formatMessage(
                                                                                  {
                                                                                    id: "placeholder.input.common",
                                                                                  }
                                                                              )}
                                                                          />
                                                                        </Form.Item>
                                                                    ) : (
                                                                        // 显示文本
                                                                        value || "-"
                                                                    );
                                                                  }}
                                                              />
                                                              <Table.Column
                                                                  title={formatMessage({
                                                                    id: "project.external.edc.rave.mapping.fieldOid",
                                                                  })}
                                                                  dataIndex="fieldOid"
                                                                  key="fieldOid"
                                                                  ellipsis
                                                                  render={(
                                                                      value,
                                                                      record,
                                                                      i
                                                                  ) => {
                                                                    return edit ? (
                                                                        // 显示编辑框
                                                                        <Form.Item
                                                                            // name={"fieldOid" + i}
                                                                            name={[
                                                                              "info",
                                                                              "visitRandomization",
                                                                              "randomizations",
                                                                              index,
                                                                              "randomizationConfigs",
                                                                              i,
                                                                              "fieldOid",
                                                                            ]}
                                                                            initialValue={value}
                                                                            noStyle
                                                                            // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'project.external.edc.rave.mapping.ipNumberOid'}) }]}
                                                                        >
                                                                          <Input
                                                                              placeholder={formatMessage(
                                                                                  {
                                                                                    id: "placeholder.input.common",
                                                                                  }
                                                                              )}
                                                                          />
                                                                        </Form.Item>
                                                                    ) : (
                                                                        // 显示文本
                                                                        value || "-"
                                                                    );
                                                                  }}
                                                              />
                                                            </MyTable>
                                                        )
                                                    )}
                                                  </>
                                              )}
                                            </>
                                        );
                                      }
                                    }}
                                  </Form.Item>
                                </Col>
                            )}
                          </Row>
                        </TabPane>
                        <TabPane
                            tab={
                              <>
                                <span style={{ paddingLeft: 6 }}>eLearning</span>
                                <Tooltip
                                    overlayInnerStyle={{ width: 500 }}
                                    placement="top"
                                    title={
                                      <>
                                        <Row>
                                          <FormattedMessage id="tool.tip.elearning" />
                                        </Row>
                                        <Row>
                                          <FormattedMessage id="tool.tip.elearning.yes.tp" />
                                        </Row>
                                        <Row>
                                          <FormattedMessage id="tool.tip.elearning.no.tp" />
                                        </Row>
                                      </>
                                    }
                                >
                                  <QuestionCircleFilled
                                      style={{ marginLeft: "4px", color: "#D0D0D0" }}
                                  />
                                </Tooltip>
                              </>
                            }
                            key="2"
                        >
                          <div style={{ backgroundColor: "#F8F9FA", color: "#BEBEBE", height: 44 }}>
                            <Row gutter={16}>
                              <Col className="gutter-row" span={6}>
                                <Form.Item
                                    label={formatMessage({
                                      id: "projects.learning.system.courses",
                                    })}
                                    name={["info", "systemCourses"]}
                                    className="mar-ver-5"
                                >
                                  <Switch
                                      disabled={disable || !edit}
                                      size="small"
                                      checked={systemCoursesValue}
                                      onChange={onSystemCoursesChange}
                                  />
                                </Form.Item>
                              </Col>

                              {systemCoursesShow ? (
                                  <Col
                                      className="gutter-row"
                                      span={6}
                                      style={{
                                        paddingLeft: g.lang === "zh" ? 10 : 40,
                                        paddingTop: 10,
                                      }}
                                  >
                                    | &nbsp;&nbsp;&nbsp;
                                    <Checkbox
                                        disabled={disable || !edit}
                                        checked={needSystemCoursesValue}
                                        onChange={(e) => onNeedSystemCoursesChange(e)}
                                    >
                                      <FormattedMessage id="projects.learning.compulsory" />
                                    </Checkbox>
                                  </Col>
                              ) : null}
                            </Row>
                          </div>
                          <br />

                          <div style={{ backgroundColor: "#F8F9FA", color: "#BEBEBE", height: 44 }}
                          >
                            <Row gutter={16}>
                              <Col className="gutter-row" span={6}>
                                <Form.Item
                                    label={formatMessage({
                                      id: "projects.learning.courses",
                                    })}
                                    name={["info", "connectLearning"]}
                                    className="mar-ver-5"
                                >
                                  <CustomSwitch
                                      disabled={disable || !edit}
                                      type={1}
                                      setLearn={setLearningShowFun}
                                  />
                                </Form.Item>
                              </Col>
                              {learningShow ? (
                                  <>
                                    <span style={{ paddingLeft: g.lang === "zh" ? 10 : 40, paddingTop: 10 }}>|</span>
                                    <Col className="gutter-row" span={10}>
                                      <Form.Item
                                          label={formatMessage({ id: "projects.env" })}
                                          name={["info", "needLearningEnv"]}
                                          rules={[{ required: true }]}
                                          labelCol={{}}
                                          style={{ paddingLeft: 15, marginTop: 7 }}
                                      >
                                        <Select mode={"multiple"} showArrow>
                                          <Select.Option value="PROD">
                                            PROD
                                          </Select.Option>
                                          <Select.Option value="DEV">
                                            DEV
                                          </Select.Option>
                                          <Select.Option value="UAT">
                                            UAT
                                          </Select.Option>
                                          <Select.Option value="TEST">
                                            TEST
                                          </Select.Option>
                                          {projectData.envs.map(
                                              (value) =>
                                                  value.name !== "PROD" &&
                                                  value.name !== "DEV" &&
                                                  value.name !== "UAT" &&
                                                  value.name !== "TEST" && (
                                                      <Select.Option
                                                          key={value.id}
                                                          value={value.name}
                                                      >
                                                        {value.name}
                                                      </Select.Option>
                                                  )
                                          )}
                                        </Select>
                                      </Form.Item>
                                    </Col>
                                    <Col className="gutter-row" span={6}>
                                      <Checkbox
                                          style={{ paddingTop: 10 }}
                                          disabled={disable || !edit}
                                          checked={needLearningValue}
                                          onChange={(e) => onNeedLearningChange(e)}
                                      >
                                        <FormattedMessage id="projects.learning.compulsory" />
                                      </Checkbox>
                                    </Col>
                                  </>
                              ) : null}
                            </Row>
                          </div>
                        </TabPane>
                        {/* 添加更多的TabPane */}
                      </Tabs>
                    </TabPane>
                )}
                {projectSettingPermission(
                    projectData,
                    auth,
                    "operation.projects.main.setting.custom.view"
                ) && (
                    <TabPane
                        tab={
                          <>
                            <i
                                className="iconfont icon-zidingyiliucheng"
                                style={{ marginRight: 8 }}
                            />
                            {formatMessage({
                              id: "menu.projects.main.setting.custom",
                            })}
                          </>
                        }
                        key="5"
                    >
                      <Layout.Header
                          style={{ height: "24px", background: "#FFFFFF" }}
                      />
                      <Title
                          name={formatMessage({
                            id: "project.setting.switch.unblind.control",
                          })}
                      />
                      <UnblindingControlView project={projectData} form={form} disable={disable} unblindingControl={unblindingControl} />
                      {/* 研究中心订单申请  */}
                      <Title
                          name={formatMessage({
                            id: "project.setting.divider.approval.control",
                          })}
                      />
                      <Row justify="start" >
                        <Col className="gutter-row" span={24}>
                          <Row>
                            <Col style={{ marginBottom: "12px" }}>
                              <Form.Item
                                  label={
                                    <>
                                      <FormattedMessage
                                          id={"project.setting.switch.approval.control"}
                                      />
                                      <Tooltip
                                          overlayInnerStyle={{ width: 500 }}
                                          placement="top"
                                          title={
                                            <FormattedMessage id="tool.tip.approval.control" />
                                          }
                                      >
                                        <QuestionCircleFilled
                                            style={{ marginLeft: "4px", color: "#D0D0D0" }}
                                        />
                                      </Tooltip>
                                    </>
                                  }
                                  name={["info", "orderApprovalControl"]}
                                  className="mar-ver-5"
                              >
                                <ControlSwitch
                                    disabled={disable}
                                    onChange={onOrderApprovalControl}
                                />
                              </Form.Item>
                            </Col>
                            {
                                orderApprovalControl === 1 &&
                                <Col style={{
                                  width: g.lang === "zh" ? "70%" : "62%",
                                  marginLeft: 6, marginTop: 12,
                                  color: "#677283",
                                }}>
                                  <svg className="iconfont" width={16} height={16} style={{ marginBottom: "-3px" }}>
                                    <use xlinkHref="#icon-jinggao"></use>
                                  </svg>
                                  {formatMessage({ id: "project.setting.switch.approval.control.tip" })}
                                </Col>
                            }
                          </Row>
                        </Col>
                      </Row>
                      {orderApprovalControl === 1 ? (
                          <>
                            <Row style={{ marginBottom: 12 }}>
                              <Col className="gutter-row">
                                <Form.Item
                                    label={formatMessage({
                                      id: "project.setting.approval.method",
                                    })}
                                >
                                  <FormattedMessage
                                      id={"project.setting.checkbox.unblind.process"}
                                  />
                                </Form.Item>
                              </Col>
                            </Row>
                          </>
                      ) : null}
                    </TabPane>
                )}
                {projectSettingPermission(
                    projectData,
                    auth,
                    "operation.projects.main.setting.permission.view"
                ) && (
                    <TabPane
                        tab={
                          <>
                            <i
                                className="iconfont icon-xiangmuquanxian"
                                style={{ marginRight: 8 }}
                            />
                            {formatMessage({
                              id: "menu.projects.main.setting.permission",
                            })}
                          </>
                        }
                        key="6"
                    >
                      <Layout.Header
                          style={{ height: "24px", background: "#FFFFFF" }}
                      />
                      <ProjectRole project={projectData} editable={edit} />
                    </TabPane>
                )}
                {projectSettingPermission(
                    projectData,
                    auth,
                    "operation.projects.main.setting.notice.view"
                ) && (
                    <TabPane
                        tab={
                          <>
                            {/*<svg className="iconfont"  style={{marginRight: 6}} width={16} height={16}>*/}
                            {/*  <use xlinkHref="#icon-xiangmutongzhi"/>*/}
                            {/*</svg>*/}
                            <i
                                className="iconfont icon-xiangmutongzhi"
                                style={{ marginRight: 8 }}
                            />
                            {formatMessage({
                              id: "menu.projects.main.setting.notice",
                            })}
                          </>
                        }
                        key="7"
                    >
                      <Layout.Header
                          style={{ height: "24px", background: "#FFFFFF" }}
                      />
                      <ProjectNotice project={projectData} />
                    </TabPane>
                )}
              </Tabs>
            </Container>
          </Form>
          {/* footer */}
          {edit &&
          (activeKey === "1" ||
              (activeKey !== "4" && activeKey !== "2" && activeKey !== "5") ||
              (activeKey === "2") ||
              (activeKey === "3" && dfChanged)) ? (
              <Row
                  justify="end"
                  style={{
                    position: "absolute",
                    right: 0,
                    bottom: 0,
                    width: "calc(100% - 200px)",
                    padding: "16px 24px",
                    background: "#fff",
                    zIndex: 2,
                    borderTop: "1px solid #E3E4E6",
                  }}
              >
                {activeKey === "1" ||
                activeKey === "3" ? (
                    <Button className="mar-rgt-10" onClick={handleCancelEdit}>
                      <FormattedMessage id={"common.cancel"} />
                    </Button>
                ) : null}
                {activeKey !== "4" &&
                activeKey !== "2" &&
                activeKey !== "5" &&
                activeKey !== "3" ? (
                    <Button
                        loading={updateProjectLoading}
                        key="project-setting-ok"
                        onClick={handleOk}
                        type="primary"
                        // disabled={disable}
                        disabled={activeKey === "1" ?(send||disable):disable}
                    >
                      <FormattedMessage id={"common.save"} />
                    </Button>
                ) : null}
                {activeKey === "2" && <Button className="mar-rgt-10" onClick={handleCancelEdit}>
                  <FormattedMessage id={"common.cancel"} />
                </Button>}
                {activeKey === "2" ? (
                    <Button
                        loading={updateProjectLoading}
                        key="project-setting-ok"
                        onClick={handleOk}
                        type="primary"
                        disabled={disable || !bfChanged}
                    >
                      <FormattedMessage id={"common.save"} />
                    </Button>
                ) : null}
                {activeKey === "3" ? (
                    <Button
                        loading={updateProjectLoading}
                        key="project-setting-ok"
                        onClick={handleOk}
                        type="primary"
                        disabled={disable}
                    >
                      <FormattedMessage id={"common.save"} />
                    </Button>
                ) : null}
              </Row>
          ) : null}
          {
            activeKey === "2" && !edit && permissions(auth.permissions, "operation.projects.main.setting.function.view") ? <Row
                justify="end"
                style={{
                  position: "absolute",
                  right: 0,
                  bottom: 0,
                  width: "calc(100% - 200px)",
                  padding: "16px 24px",
                  background: "#fff",
                  zIndex: 2,
                  borderTop: "1px solid #E3E4E6",
                }}
            >
              <Button
                  key="project-setting-ok"
                  onClick={onEdit}
                  type="primary"
                  disabled={disable}
              >
                <FormattedMessage id={"common.edit"} />
              </Button>
            </Row> : null
          }
          {!edit &&
          permissions(
              auth.permissions,
              "operation.projects.main.setting.docking.edit"
          ) &&
          activeKey === "3" ? (
              <Row
                  justify="end"
                  style={{
                    position: "absolute",
                    right: 0,
                    bottom: 0,
                    width: "calc(100% - 200px)",
                    padding: "16px 24px",
                    background: "#fff",
                    zIndex: 2,
                    borderTop: "1px solid #E3E4E6",
                  }}
              >
                {activeKey === "3" ? (
                    <Button
                        key="project-setting-ok"
                        onClick={onEdit}
                        type="primary"
                        disabled={disable}
                    >
                      <FormattedMessage id={"common.edit"} />
                    </Button>
                ) : null}
              </Row>
          ) : null}
          {activeKey === "5" ? (
              <Row
                  justify="end"
                  style={{
                    position: "absolute",
                    right: 0,
                    bottom: 0,
                    width: "calc(100% - 200px)",
                    padding: "16px 24px",
                    background: "#fff",
                    zIndex: 2,
                    borderTop: "1px solid #E3E4E6",
                  }}
              >
                {activeKey === "5" ? (
                    <>
                      <Button
                          disabled={disable}
                          onClick={
                            handleOk
                          }
                          type="primary"
                          loading={projectCtx.submitLoading}
                      >
                        <FormattedMessage id={"common.save"} />
                      </Button>
                    </>
                ) : null}
              </Row>
          ) : null}
          {permissions(
              auth.permissions,
              "operation.projects.main.setting.docking.edit"
          ) && activeKey === "7" ? (
              <Row
                  justify="end"
                  style={{
                    position: "absolute",
                    right: 0,
                    bottom: 0,
                    width: "calc(100% - 200px)",
                    padding: "16px 24px",
                    background: "#fff",
                    zIndex: 2,
                    borderTop: "1px solid #E3E4E6",
                  }}
              >
                {activeKey === "7" ? (
                    <>
                      {/*<Button*/}
                      {/*  onClick={() => {*/}
                      {/*    projectCtx.setNoticeCancel(true);*/}
                      {/*  }}*/}
                      {/*  style={{ marginRight: 12 }}*/}
                      {/*>*/}
                      {/*  <FormattedMessage id={"common.cancel"} />*/}
                      {/*</Button>*/}
                      <Button
                          onClick={() => {
                            projectCtx.setNoticeSave(projectCtx.noticeSave + 1);
                          }}
                          type="primary"
                          loading={projectCtx.submitLoading}
                          // disabled={projectCtx.projectType !== 0 ? true : projectCtx.noticeSend?projectCtx.noticeSend:false}
                          disabled={projectCtx.projectType !== 0 ? true : false}
                      >
                        <FormattedMessage id={"common.save"} />
                      </Button>
                    </>
                ) : null}
              </Row>
          ) : null}
        </StyledModal>
      </>
  );
};

const CustomSwitch = (props) => {
  // console.log("1==" + JSON.stringify(props));
  const isOpen = props.value === 1;
  // const [isOpen, setIsOpen] = useSafeState(false);
  // useEffect(()=>{
  //   setIsOpen(props.value === 1)
  // },[props])
  return (
      <Switch
          disabled={props.disabled}
          size="small"
          checked={isOpen}
          onChange={(e) => {
            // console.log("2==" + JSON.stringify(props));
            const value = (props.value === 2 || props.value === 0 || props.value === undefined) ? 1 : 2;
            if (value === 1) {
              // console.log(3);
              props.openCallback && props.openCallback();
            }
            if (props.type === 1) {
              // learning
              props.setLearn(e);
            }
            props.onChange(value);
          }}
      />
  );
};



const ControlSwitch = (props) => {
  const isOpen = props.value === 1;
  return (
      <Switch
          disabled={props.disabled}
          size="small"
          checked={isOpen}
          onChange={() => {
            const value = props.value === 0 ? 1 : 0;
            props.onChange(value);
          }}
      />
  );
};

const StyledModal = styled(Modal)`
  height: calc(100vh - 120px);
  // height: 643px;

  .ant-modal-content {
    height: inherit;
  }
  .ant-modal-body {
    padding-left: 0px;
    padding-bottom: 0px;
    padding-top: 0px;
    height: inherit;
    max-height: unset;
  }
  .ant-modal-footer {
    margin-left: 199px;
    border-left: 1px solid #f0f0f0;
  }
`;


const Container = styled.div`
  .custom-error-msg:is(.ant-form-item-has-error) .ant-form-item-explain {
    position: relative;

    ${(props) =>
    props.lang === "zh" &&
    `
            @media (min-width: 453px) {
                top: 20px;
            }

            @media (min-width: 574px) {
                top: 52px;
            }

            @media (min-width: 582px) {
                top: 44px;
            }

            @media (min-width: 594px) {
                top: 44px;
            }

            @media (min-width: 613px) {
                top: 36px;
            }

            @media (min-width: 636px) {
                top: 36px;
            }

            @media (min-width: 665px) {
                top: 20px;
            }

            @media (min-width: 873px) {
                top: -4px;
            }
        `}

    ${(props) =>
    props.lang === "en" &&
    `

            @media (max-width: 575px) {
                top: 20px;
            }

            @media (min-width: 576px) {
                top: 164px;
            }

            @media (min-width: 772px) {
                top: 148px;
            }

            @media (min-width: 787px) {
                top: 108px;
            }

            @media (min-width: 801px) {
                top: 100px;
            }

            @media (min-width: 837px) {
                top: 76px;
            }

            @media (min-width: 883px) {
                top: 52px;
            }

            @media (min-width: 935px) {
                top: 52px;
            }

            @media (min-width: 942px) {
                top: 36px;
            }

            @media (min-width: 1066px) {
                top: 20px;
            }

            @media (min-width: 1418px) {
                top: -4px;
            }
        `}
  }
  .ant-tabs-left > .ant-tabs-content-holder,
  .ant-tabs-left > div > .ant-tabs-content-holder {
    height: calc(100vh - 220px);
    overflow: auto;
    padding-bottom: ${(props) => (!!props.edit ? "81px" : "0")};
    padding-right: 24px;
    width: 100%;
  }
`;


const CustomSelect = styled(Select)`
  .ant-select-selector {
    padding: 0 0 !important;
  }

  .ant-select-selection-item .x-check {
    display: none !important;
  }

  ${(props) =>
    props.hoverStatus &&
    `
        .ant-select-selection-item svg {
            display: none !important;
        }

        .ant-select-selection-item .anticon {
            display: none !important;
        }
    `}

  ${(props) =>
    !props.hoverStatus &&
    `
        .ant-select-selection-item img {
            display: none !important;
        }
    `}

    ${(props) =>
    props.lang === "en" &&
    `
        .ant-select-selection-item div {
            width: 110px !important;
        }
    `}

    ${(props) =>
    props.lang === "zh" &&
    `
        .ant-select-selection-item div {
            width: 84px !important;
        }
    `}
`;

const StatusFormItem = styled(Form.Item)`
  .ant-select-item-option-content .x-option {
    padding-right: 8px;
    border-radius: 2px;
  }

  .ant-select-item-option-content .x-option img {
    display: none !important;
  }

  .ant-select-item-option-content .x-option-blue {
    background-color: #e8efff !important;
  }

  .ant-select-item-option-content .x-option-yellow {
    background-color: #fff7e5;
    ${(props) =>
    props.lang === "en" &&
    `
            width: 108px !important;
        `}
  }

  .ant-select-item-option-content .x-option-red {
    ${(props) =>
    props.lang === "en" &&
    `
            width: 108px !important;
        `}
  }

  .ant-select-item-option-content div {
    line-height: 26px !important;
  }

  ${(props) =>
    props.lang === "zh" &&
    `
        .ant-select-dropdown {
            min-width: 128px !important;
            width: 128px !important;
            left: 151px !important;
        }
    `}
  ${(props) =>
    props.lang === "en" &&
    `
        .ant-select-dropdown {
            min-width: 160px !important;
            width: 160px !important;
            left: 230px !important;
        }
    `}
  .ant-select-dropdown {
    padding: 0 !important;
    border-radius: 2px;
  }

  .ant-select-item {
    padding: 7px 12px 7px 12px !important;
  }

  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    background: white !important;
  }

  .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
    background: #f5f5f5 !important;
  }

  ${(props) =>
    props.checkedStatus === 0 &&
    `
        .x-check-green {
            display: none !important;
        }
    `}

  ${(props) =>
    props.checkedStatus === 1 &&
    `
        .x-check-blue {
            display: none !important;
        }
    `}
`;

const DividerLine = styled.div`
  position: absolute;
  width: 0.5px;
  height: 245px;
  top: -122px;
  left: 104px;
  background: #e3e4e6;
  transform: rotate(-79deg);
`;

const MyTable = styled(Table)`
  .ant-table-thead tr th:first-child,
  .ant-table-tbody .ant-table-row td:first-child {
    border-right: 1px solid #e3e4e6;
  }
`;
