import React, {ReactNode} from "react";
import {useSafeState} from "ahooks";

export const ReportContext = React.createContext<{
        previewFieldsMany: any;
        setPreviewFieldsMany: (data: any) => void;
        showRandomNumber: any
        setShowRandomNumber: (data: any) => void;
        repeatedlySubject: any
        setRepeatedlySubject: (data: any) => void;
        selectCohort: any
        setSelectCohort: (data: any) => void;
        subject: any
        setSubject: (data: any) => void;
        attrs: any
        setAttrs: (data: any) => void;
        type: any
        setType: (data: any) => void;
        showRegisterGroup: any
        setShowRegisterGroup: (data: any) => void;
        outVisitStr: any
        setOutVisitStr: (data: any) => void;
    }
    |
    null>(null);

export const ReportProvider = ({children}: { children: ReactNode }) => {

    const [previewFieldsMany, setPreviewFieldsMany] = useSafeState<any[]>([]);
    const [showRandomNumber, setShowRandomNumber] = useSafeState<any>(false);
    const [repeatedlySubject, setRepeatedlySubject] = useSafeState<any>([]);
    const [selectCohort, setSelectCohort] = useSafeState<any>([]);
    const [subject, setSubject] = useSafeState<any[]>([]);
    const [attrs, setAttrs] = useSafeState<any[]>([]);
    const [type, setType] = useSafeState<number>();
    const [showRegisterGroup, setShowRegisterGroup] = useSafeState<any>();
    const [outVisitStr, setOutVisitStr] = useSafeState<any>();


    return (
        <ReportContext.Provider
            value={
                {
                    previewFieldsMany,
                    setPreviewFieldsMany,
                    showRandomNumber,
                    setShowRandomNumber,
                    repeatedlySubject, setRepeatedlySubject,
                    selectCohort, setSelectCohort,
                    subject, setSubject,
                    attrs, setAttrs,
                    type, setType,
                    showRegisterGroup, setShowRegisterGroup,
                    outVisitStr, setOutVisitStr,
                }
            }
        >
            {children}
        </ ReportContext.Provider>
    )
};

export const useReport = () => {
    const context = React.useContext(ReportContext);
    if (!context) {
        throw new Error("useNotice must be used in ReportContextProvider");
    }
    return context;
};

