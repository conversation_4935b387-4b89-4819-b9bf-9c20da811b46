import {Row, Select, Space, Table, Typography} from "antd";
import React, {useEffect, useRef} from "react";
import {Template} from "./template";
import {useSafeState} from "ahooks";
import {useIntl} from "react-intl";
import {useFetch} from "../../../hooks/request";
import {getReports} from "../../../api/report";
import {getProjectList} from "../../../api/projects";
import {DownloadHistory} from "./download_history";
import {Export} from "./export";
import {useMain} from "../context";
import {permissions} from "../../../tools/permission";
import {getUserProjectEnvironmentRoles} from "../../../api/user";
import {getProjectAttribute, getProjectAttributes,} from "../../../api/randomization";
import {getDrugPackageIsOpen} from "../../../api/drug";
import {useReport} from "./context";
import {useGlobal} from "../../../context/global";
import {getVisitSettings} from "../../../api/visit";
import {useAuth} from "context/auth";

const { Option } = Select;

export const Main = () => {
    const g = useGlobal();
    const custom: any = useRef();
    const history: any = useRef();
    const _export: any = useRef();
    const [reports, setReports] = useSafeState([]);
    const intl = useIntl();
    const { runAsync: runGetReports, loading: getReportsLoading } = useFetch(
        getReports,
        { manual: true }
    );
    const [isOpenPackage, setIsOpenPackage] = useSafeState<boolean>(false);
    const [report, setReport] = useSafeState<any>();
    const main = useMain();
    const auth = useAuth();
    const reportContext = useReport();
    const [projects, setProjects] = useSafeState<any[]>([]);
    const [selectProject, setSelectProject] = useSafeState<string>("");
    const [record, setRecord] = useSafeState<any>({});
    const [roles, setRoles] = useSafeState<any[]>([]);
    const { runAsync: roleRunAsync } = useFetch(
        getUserProjectEnvironmentRoles,
        {
            manual: true,
        }
    );

    const { runAsync: getDrugPackageIsOpenRun } = useFetch(
        getDrugPackageIsOpen,
        { manual: true }
    );
    const { runAsync: runGetProjectAttr } = useFetch(getProjectAttribute, {
        manual: true,
    });
    const { runAsync: runGetProjectAttributesAttr } = useFetch(
        getProjectAttributes,
        { manual: true }
    );

    const { formatMessage } = intl;
    const get_reports = () => {
        if (roles.length > 0)
            runGetReports({
                projectId: main.project.id,
                envId: main.env.id,
            }).then((res: any) => {
                const reports: any = [];
                res.data.forEach((it: any) => {
                    const filterRoles = roleFilter(roles, it.type);
                    const filterTemplateRoles = roleTemplateFilter(
                        roles,
                        it.type
                    );
                    if (
                        filterRoles.length > 0 ||
                        (it.customizable && filterTemplateRoles.length > 0)
                    ) {
                        reports.push(it);
                    }
                });
                setReports(reports);
            });
    };
    const { runAsync: runGetProjectList, loading: getProjectListLoading } =
        useFetch(getProjectList, { manual: true });
    useEffect(() => {
        runGetProjectList().then((res: any) => {
            const list: any = [];
            if (res.data) {
                res.data?.map((project: any) => {
                    project.envs.forEach((env: any) => {
                        list.push({
                            label: `${project.info.number}[${env.name}]`,
                            value: env.id,
                            project: project,
                            env: env,
                            cohorts: env.cohorts,
                        });
                    });
                });
                if (list.length) {
                    onSelectChange(list[0].value, {
                        option: {
                            project: list[0].project,
                            env: list[0].env,
                        },
                    });
                }
            }
            setProjects(list);
        });
    }, []);

    const {runAsync: getVisitSettingsRun, loading: getVisitSettingsLoading} = useFetch(getVisitSettings, {manual: true});


    useEffect(() => {
        if (main.project?.id && main.env?.id) {
            get_reports();
        }
    }, [roles]);

    useEffect(() => {
        if (main.project?.id && main.env?.id) {
            roleRunAsync({ projectId: main.project.id, envId: main.env.id,cohortId: main.cohort?.id }).then((res: any) => {
                if (res.data) {
                    setRoles(res.data);
                }
            });
            runGetProjectAttr({ projectId: main.project.id, env: main.env.id }).then((res: any) => {
                main.setAttr(res.data);
            });
            runGetProjectAttributesAttr({ env: main.env.id }).then(
                (res: any) => {
                    const showRandomNumber = res.data?.find(
                        (it: any) => it.info.isRandomNumber
                    );
                    reportContext.setShowRandomNumber(showRandomNumber);
                    const showRegisterGroup = res?.data?.find(
                        (it: any) => it.info.allowRegisterGroup
                    );
                    reportContext.setShowRegisterGroup(showRegisterGroup);
                }
            );
            getVisitSettingsRun({
                projectId:main.project.id,
                envId: main.env.id,
            }).then((result:any) => {
                if (result.data.isOpen){
                    reportContext.setOutVisitStr(g.lang === "zh"?result.data.nameZh:result.data.nameEn);
                }else{
                    reportContext.setOutVisitStr(formatMessage({ id: "project.statistics.out-visit-dispensing" }));
                }
            });


        }
    }, [main.env]);

    const onSelectChange = (value: any, option: any) => {
        const { project, env } = option.option;
        main.setProject(project);
        auth.setReportProjectId(project.id);
        main.setEnv(env);
        setSelectProject(value);
        // get_reports();
        getDrugPackageIsOpenRun({ envId: value }).then((res: any) => {
            setIsOpenPackage(res.data);
        });
    };

    const refresh = () => {
        get_reports();
    };

    const removeKey = (customFields: any, keys: string[]) => {
        keys.forEach((key: string) => {
            customFields = customFields.filter((it: any) => it.key !== key);
            customFields.forEach((item: any, index: number) => {
                if (item.children) {
                    customFields[index].children = item.children.filter(
                        (it: any) => it.key !== key
                    );
                }
            });
        });
        return customFields;
    };

    const selectRecord = (record: any, roles: any[]) => {
        let customFields = record.customFields;
        let fields = record.defaultFields;
        let type = main.project.info.type;
        if (record.multiDefaultFields) {
            fields = record.multiDefaultFields.find(
                (it: any) => it.type === type
            ).defaultFields;
            record.defaultFields = fields;
        }
        setRecord(record);

        if (type === 1) {
            customFields = removeKey(customFields, [
                "report.attributes.random.stage",
                "report.attributes.random.cohort",
            ]);
        }
        if (type === 2) {
            customFields.forEach((fields: any) => {
                if (fields.children && fields.children.length) {
                    fields.children = fields.children.filter(
                        (it: any) => it.key !== "report.attributes.random.stage"
                    );
                }
            });
        }
        if (type === 3) {
            customFields.forEach((fields: any) => {
                if (fields.children && fields.children.length) {
                    fields.children = fields.children.filter(
                        (it: any) =>
                            it.key !== "report.attributes.random.cohort"
                    );
                }
            });
        }
        //
        setReport({
            name: record.name,
            type: record.type,
            fields: fields,
            customFields: customFields,
        });
        custom.current.show({ roles });
    };

    const getOperateBtn = (record: any) => {
        const filterRoles = roleFilter(roles, record.type);
        const filterTemplateRoles = roleTemplateFilter(roles, record.type);
        return (
            <Row>
                <Space size={12}>
                    {filterRoles.length > 0 ? (
                        <Typography.Link
                            onClick={() => {
                                _export.current.show({
                                    roles: filterRoles,
                                    ...record,
                                    isOpenPackage: isOpenPackage,
                                });
                            }}
                        >
                            {formatMessage({ id: "common.download" })}
                        </Typography.Link>
                    ) : null}
                    {record.customizable && filterTemplateRoles.length > 0 ? (
                        <Typography.Link
                            onClick={() => selectRecord(record, filterRoles)}
                        >
                            {formatMessage({ id: "report.customTemplate" })}
                        </Typography.Link>
                    ) : null}
                    {filterRoles.length > 0 ? (
                        <Typography.Link
                            onClick={() => {
                                history.current.show(record.type, record.name);
                            }}
                        >
                            {formatMessage({ id: "report.history" })}
                        </Typography.Link>
                    ) : null}
                </Space>
            </Row>
        );
    };

    const roleFilter = (roles: any[], type: number) => {
        roles = roles.filter((role) => {
            if (type === 3) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.report.siteIPStatisticsExport.download"
                );
            } else if (type === 4) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.report.depotIPStatisticsExport.download"
                );
            } else if (type === 7) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.build.settings.user.download"
                );
            } else if (type === 9) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.projects.main.setting.permission.export"
                );
            } else if (type === 10) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.build.randomization.info.export"
                );
            } else if (type === 11) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.build.medicine.upload.downdata"
                );
            } else if (type === 16) {
                return (
                    permissions(
                        { permissions: role.permissions },
                        "operation.subject.download"
                    ) ||
                    permissions(
                        { permissions: role.permissions },
                        "operation.subject-dtp.download"
                    )
                );
            } else if (type === 19) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.supply.site.medicine.download"
                );
            } else if (type === 18) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.supply.storehouse.medicine.download"
                );
            } else if (type === 20) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.supply.shipment.download"
                );
            } else if (type === 21) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.supply.recovery.download"
                );
            } else if (type === 2) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.report.subjectStatisticsExport.download"
                );
            } else if (type === 12) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.build.randomization.list.export"
                );
            } else if (type === 15) {
                return (
                    permissions(
                        { permissions: role.permissions },
                        "operation.subject.medicine.export"
                    ) ||
                    permissions(
                        { permissions: role.permissions },
                        "operation.subject-dtp.medicine.export"
                    )
                );
            } else if (type === 1) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.report.randomizationStatisticsExport.download"
                );
            } else if (type === 5) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.report.userLoginHistory.download"
                );
            } else if (type === 6) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.report.userRoleAssignHistory.download"
                );
            } else if (type === 8) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.report.auditTrailExport.download"
                );
            } else if (type === 14) {
                return (
                    permissions(
                        { permissions: role.permissions },
                        "operation.subject.download-random"
                    ) ||
                    permissions(
                        { permissions: role.permissions },
                        "operation.subject-dtp.download-random"
                    )
                );
            } else if (type === 22) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.build.simulate-random.download"
                );
            } else if (type === 23) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.build.simulate-random.pdf.download"
                );
            } else if (type === 24) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.build.projectNotificationsConfigurationReport.download"
                );
            } else if (type === 25) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.source.ip.upload.history.downdata"
                );
            }
            else if (type === 26) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.report.forecastingPrediction.download"
                );
            }else if (type === 27) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.report.visitForecast.download"
                );
            }else if (type === 28) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.report.ip.unblinding.download"
                );
            }
        });
        return roles;
    };

    const roleTemplateFilter = (roles: any[], type: number) => {
        roles = roles.filter((role) => {
            if (type === 3) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.report.siteIPStatisticsExport.download.template"
                );
            } else if (type === 4) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.report.depotIPStatisticsExport.download.template"
                );
            }else if (type === 14) {
                return (
                    permissions(
                        { permissions: role.permissions },
                        "operation.subject.download-random.template"
                    ) ||
                    permissions(
                        { permissions: role.permissions },
                        "operation.subject-dtp.download-random.template"
                    )
                );
            } else if (type === 15) {
                return (
                    permissions(
                        { permissions: role.permissions },
                        "operation.subject.medicine.export.template"
                    ) ||
                    permissions(
                        { permissions: role.permissions },
                        "operation.subject-dtp.medicine.export.template"
                    )
                );
            } else if (type === 16) {
                return (
                    permissions(
                        { permissions: role.permissions },
                        "operation.subject.download.template"
                    ) ||
                    permissions(
                        { permissions: role.permissions },
                        "operation.subject-dtp.download.template"
                    )
                );
            } else if (type === 18) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.supply.storehouse.medicine.download.template"
                );
            } else if (type === 11) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.build.medicine.upload.downdata.template"
                );
            } else if (type === 19) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.supply.site.medicine.download.template"
                );
            } else if (type === 20) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.supply.shipment.download.template"
                );
            } else if (type === 21) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.supply.recovery.download.template"
                );
            } else if (type === 27) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.report.visitForecast.download.template"
                );
            } else if (type === 28) {
                return permissions(
                    { permissions: role.permissions },
                    "operation.report.visitForecast.download.template"
                );
            }
        });
        return roles;
    };

    return (
        <>
            <Row align={"middle"}>
                {formatMessage({ id: "report.projectEnv" })}：
                <Select
                    style={{ width: 300 }}
                    dropdownMatchSelectWidth={false}
                    dropdownStyle={{ maxWidth: 600 }}
                    onChange={onSelectChange}
                    value={selectProject}
                    showSearch
                    loading={getProjectListLoading}
                    filterOption={(input, option) =>
                        option.children.trim().toLowerCase().indexOf(input.trim().toLowerCase()) >= 0
                    }
                >
                    {projects.map((project: any, index) => {
                        return (
                            <Option
                                value={project.value}
                                id={project.value}
                                key={project.value}
                                option={project}
                            >
                                {project.label}
                            </Option>
                        );
                    })}
                </Select>
            </Row>
            <Table
                dataSource={reports}
                loading={getReportsLoading}
                rowKey={(record: any) => record.type}
                className="mar-top-16"
                pagination={false}
                scroll={{ y: "calc(100vh - 190px)" }}
            >
                <Table.Column
                    title={formatMessage({ id: "report" })}
                    dataIndex="name"
                    render={(value) => {
                        return formatMessage({ id: value });
                    }}
                    ellipsis
                />
                <Table.Column
                    title={formatMessage({ id: "report.latestDownloadTime" })}
                    dataIndex="latestDownloadTime"
                    render={(value) => value || "-"}
                    // width={220}
                    width={g.lang==="zh"?"20%":"25%"}
                />
                <Table.Column
                    title={formatMessage({ id: "common.operation" })}
                    dataIndex="#"
                    // width={g.lang==="zh"?250:300}
                    width={g.lang==="zh"?"20%":"25%"}
                    render={(_, record: any) => {
                        return getOperateBtn(record);
                    }}
                />
            </Table>
            <Export bind={_export} refresh={refresh} />
            <Template
                bind={custom}
                report={report}
                exportBind={_export}
                record={record}
                isOpenPackage={isOpenPackage}
            />
            <DownloadHistory bind={history} />
        </>
    );
};
