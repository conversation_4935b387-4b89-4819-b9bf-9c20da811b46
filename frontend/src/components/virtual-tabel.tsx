import React, {useEffect, useState} from "react"
import {useVT} from "virtualizedtableforantd4"
import {Table, TableProps} from "antd"
import {useDebounceFn} from "ahooks"

interface VirtualTableProps {
    height: number | (() => number)
}

export const VirtualTable: React.FC<TableProps<any> & VirtualTableProps> = (props) => {

    const [tableHeight, setTableHeight] = useState(200)
    const [dataSource, setDataSource] = useState<any[]>([])

    // 虚拟列表配置
    const [vt] = useVT(() => ({
        scroll: {y: tableHeight},
        overscanRowCount: 5,
        debug: false
    }), [tableHeight])

    useEffect(() => {
        setDataSource(props.dataSource?.slice(0) || [])
    }, [props.dataSource])

    const {run: handleResize} = useDebounceFn(() => {
        const height = typeof props.height === 'function' ? props.height() : props.height
        setTableHeight(height)
    }, {wait: 200})

    // 监听窗口大小变化，动态更新表格高度
    useEffect(() => {
        handleResize()
        window.addEventListener('resize', handleResize);
        return () => {
            window.removeEventListener('resize', handleResize);
        }
    }, [])


    return (
        <Table
            rowKey="key"
            size="small"
            {...props}
            dataSource={dataSource}
            scroll={{...(props.scroll || {}), y: tableHeight}}
            components={vt}
        />
    )
};