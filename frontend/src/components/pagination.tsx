import { Button, Pagination, Select } from "antd";
import { useMemo } from "react";
import { useIntl } from "react-intl";
import { usePage } from "../context/page";
import styled from "@emotion/styled";
import { useSafeState } from "ahooks";
import { InsertDivider } from "./divider";
import React from "react";

type MODE = "NORMAL" | "SELECTABLE";
type PaginationViewProp = {
  mode?: MODE;
  // 只有在SELECTABLE下起效
  selectedNumber?: number;
  // 页码器的大小
  pageSizeOptions?: number[];
  // 是否展示清空按钮
  clearDisplay?: boolean;
  // 是否展示清空按钮前面的分隔线
  clearDivider?: boolean;
  // 清空函数
  refresh?: any;
  // 数字字体样式
  numberStyle?: React.CSSProperties;
  // 总计
  amount?: number;
  // 显示页码器所需的最小数量
  minNumberRequired?: number;
};

export const PaginationView = (prop: PaginationViewProp) => {
  const ctx = usePage();
  const intl = useIntl();
  const [dropDownVisible, setDropDownVisible] = useSafeState(false);
  const pageOptions = useMemo(() => {
    let opts = [10, 20, 50, 100, 500, 1000, 5000];
    if (prop.pageSizeOptions) {
      opts = prop.pageSizeOptions;
    }
    return (
      <Select
        style={{ fontSize: 12 }}
        value={ctx.pageSize}
        suffixIcon={dropDownVisible ? <UpTriangleIcon /> : <DownTriangleIcon />}
        onDropdownVisibleChange={(v) => setDropDownVisible(v)}
        onSelect={(v: number) => {
          ctx.setCurrentPage(1);
          ctx.setPageSize(v);
        }}
      >
        {opts.map((item) => (
          <Select.Option style={{ fontSize: 12 }} key={item} value={item}>
            {item}
          </Select.Option>
        ))}
      </Select>
    );
  }, [ctx, dropDownVisible, setDropDownVisible]);
  // 每页多少条切换
  const pageSizeEl = useMemo(() => {
    return (
      <span style={{ color: "#666666", fontSize: 12 }}>
        {!prop.mode && (
          <span>
            {intl.formatMessage(
              { id: "common.pagination.tip.total" },
              { total: ctx.total }
            )}
          </span>
        )}
        <span>
          {intl.formatMessage({ id: "common.pagination.tip.pagesize-prefix" })}
          <span style={{ margin: "0px 4px" }}>{pageOptions}</span>
          <span>
            {intl.formatMessage({
              id: "common.pagination.tip.pagesize-suffix",
            })}
          </span>
        </span>
      </span>
    );
  }, [pageOptions, ctx.total, intl, prop.mode]);

  function empty() {
    prop.refresh();
  }

  // 已选中的条数
  const selectedMsgEl = useMemo(() => {
    if (prop.mode !== "SELECTABLE") return <></>;
    if (!prop.selectedNumber && !prop.amount) return <></>;
    const selectedNum = prop.selectedNumber ? prop.selectedNumber : 0;
    const amount = prop.amount ? prop.amount : 0;
    const content = [
      selectedNum > 0 ? (
        <>
          {intl.formatMessage({ id: "common.pagination.seleted" })}
          <span
            style={prop.numberStyle ? prop.numberStyle : { color: "#165DFF" }}
          >
            {" "}
            {selectedNum}{" "}
          </span>
          {intl.formatMessage({ id: "common.pagination.record" })}，
          {intl.formatMessage({ id: "common.pagination.all" })}
          <span
            style={prop.numberStyle ? prop.numberStyle : { color: "#165DFF" }}
          >
            {" "}
            {ctx.total}{" "}
          </span>{" "}
          {intl.formatMessage({ id: "common.pagination.record" })}&nbsp;
        </>
      ) : null,

      // 是否展示总计
      amount > 0 ? (
        <>
          {intl.formatMessage({ id: "simulate_random.site.detail.total" })}
          <span
            style={prop.numberStyle ? prop.numberStyle : { color: "#165DFF" }}
          >
            {" "}
            {amount}{" "}
          </span>{" "}
          {intl.formatMessage({ id: "common.pagination.record" })}&nbsp;
        </>
      ) : null,

      prop.clearDisplay ? (
        <Button type={"link"} onClick={empty} style={{ color: "#165DFF" }}>
          {intl.formatMessage({ id: "common.pagination.empty" })}
        </Button>
      ) : null,
    ];
    return (
      <div style={{ color: "#666666", fontSize: 12 }}>
        {prop.clearDivider ? InsertDivider(content) : content}
      </div>
    );
  }, [prop.mode, prop.selectedNumber, ctx.total]);
  return (
    <div style={{ marginTop: 12, display: "flex", alignItems: "center" }}>
      {selectedMsgEl}
      {ctx.total >
        (prop.minNumberRequired || prop.minNumberRequired === 0
          ? prop.minNumberRequired
          : 10) && (
        <div
          style={{ display: "flex", alignItems: "center", marginLeft: "auto" }}
        >
          <span style={{ marginRight: 4 }}>{pageSizeEl}</span>
          <StyledPagination
            hideOnSinglePage={false}
            showSizeChanger={false}
            showQuickJumper={true}
            className="text-right"
            current={ctx.currentPage}
            pageSize={ctx.pageSize}
            total={ctx.total}
            onChange={(page, pageSize) => {
              ctx.setCurrentPage(page);
            }}
          />
        </div>
      )}
    </div>
  );
};

const StyledPagination = styled(Pagination)`
  .ant-pagination-options {
    margin-left: 4px;
  }
  .ant-pagination-options > .ant-pagination-options-quick-jumper {
    font-size: 12px;
    color: #666666;
  }
`;

const DownTriangleIcon = styled.div`
  width: 0;
  height: 0;
  border-top: 6px solid #323233;
  border-right: 6px solid transparent;
  border-left: 6px solid transparent;
`;

const UpTriangleIcon = styled.div`
  width: 0;
  height: 0;
  border-bottom: 6px solid #323233;
  border-right: 6px solid transparent;
  border-left: 6px solid transparent;
`;
