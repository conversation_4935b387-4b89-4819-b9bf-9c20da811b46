import {Modal, ModalFuncProps} from "antd";
import {InfoCircleFilled,} from "@ant-design/icons";
import {clickFilter} from "../pages/common/auth-wrap";

const preCls = "custom-tips-modal"
export const CustomConfirmModal = (props: ModalFuncProps) => {
    Modal.confirm({
        ...props,
        onOk: clickFilter(props.onOk),
        centered: true,
        icon: (<InfoCircleFilled style={{ color: "#2B74EA" }} />),
        className: props.content ? `${preCls}-with-content` : `${preCls}-without-content`,
        okText: modalBtn(props.okText),
        cancelText: modalBtn(props.cancelText),
    })
}

export const CustomWarningModal = (props: ModalFuncProps) => {
    Modal.warning({
        ...props,
        centered: true,
        icon: (<InfoCircleFilled style={{ color: "#FFAE00" }} />),
        className: props.content ? `${preCls}-with-content` : `${preCls}-without-content`,
        okText: modalBtn(props.okText),
        cancelText: modalBtn(props.cancelText),
    })
}

export const CustomConfirmWarningModal = (props: ModalFuncProps) => {
    Modal.confirm({
        ...props,
        centered: true,
        icon: (<InfoCircleFilled style={{ color: "#FFAE00" }} />),
        className: props.content ? `${preCls}-with-content` : `${preCls}-without-content`,
        okText: modalBtn(props.okText),
        cancelText: modalBtn(props.cancelText),
    })
}

export const CustomInfoModal = (props: ModalFuncProps) => {
    Modal.info({
        ...props,
        centered: true,
        icon: (<InfoCircleFilled style={{ color: "#2B74EA" }} />),
        className: props.content ? `${preCls}-with-content` : `${preCls}-without-content`,
        okText: modalBtn(props.okText),
        cancelText: modalBtn(props.cancelText),
    })
}

const modalBtn = (text: any) => {
    return isTwoChineseChars(text) ? `${text} ` : text
}

// 是否是直接使用formatMessage包裹的文本
const isTwoChineseChars = (text: any) => {
    if (!text) return false
    if (typeof text !== 'string') return false
    return text.length === 2
}