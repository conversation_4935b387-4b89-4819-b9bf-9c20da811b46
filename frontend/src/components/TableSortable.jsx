import {
    closestCenter,
    defaultDropAnimation,
    DndContext,
    DragOverlay,
    PointerSensor,
    useSensor,
    useSensors,
} from "@dnd-kit/core";
import {restrictToFirstScrollableAncestor, restrictToVerticalAxis,} from "@dnd-kit/modifiers";
import {SortableContext, verticalListSortingStrategy,} from "@dnd-kit/sortable";
import {useState} from "react";
import {createPortal} from "react-dom";
import {TableSortableOverlay} from "./TableSortableOverlay";

export const TableSortable = ({
  children,
  columns,
  dataSource,
  rowKey,
  onDragEnd,
  onDragStart,
}) => {
  const sensors = useSensors(
    useSensor(PointerSensor, { activationConstraint: { distance: 16 } })
  );

  const [activeId, setActiveId] = useState();

  return (
    <DndContext
      collisionDetection={closestCenter}
      sensors={sensors}
      onDragEnd={(event) => {
        setActiveId();
        if (onDragEnd) {
          onDragEnd(event);
        }
      }}
      onDragStart={(event) => {
        setActiveId(event.active.id);
        if (onDragStart) {
          onDragStart(event);
        }
      }}
    >
      <SortableContext
        items={dataSource.map((data) => data[rowKey])}
        strategy={verticalListSortingStrategy}
      >
        {children}
      </SortableContext>
      {createPortal(
        <DragOverlay
          modifiers={[
            restrictToVerticalAxis,
            restrictToFirstScrollableAncestor,
          ]}
          dropAnimation={{ ...defaultDropAnimation, dragSourceOpacity: 1.0 }}
        >
          {activeId && (
            <TableSortableOverlay
              columns={columns}
              dataSource={dataSource.filter(
                (data) => data[rowKey] === activeId
              )}
              rowKey={rowKey}
            />
          )}
        </DragOverlay>,
        document.body
      )}
    </DndContext>
  );
};
