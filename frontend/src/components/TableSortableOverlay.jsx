import styled from "@emotion/styled";
import { Table } from "antd";
import React from "react";

export const TableSortableOverlay = React.forwardRef((props, ref) => {
  return (
    <OverlayTable
      columns={props.columns}
      dataSource={props.dataSource}
      pagination={false}
      ref={ref}
      rowKey={(record) => record[props.rowKey]}
      showHeader={false}
      style={{ cursor: "move" }}
    />
  );
});

const OverlayTable = styled(Table)`
  .ant-table {
    background: #e9f1fc;
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.16);

    &-tbody {
      > tr {
        > td {
          border-bottom: none;

          &.ant-table-cell-row-hover {
            background: transparent;
          }
        }
      }
    }
  }
`;
