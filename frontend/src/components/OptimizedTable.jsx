import React, { memo, useMemo, useCallback, useRef, useState, useEffect } from 'react';
import { Table, Checkbox } from 'antd';

/**
 * 高性能优化的 Table 组件
 * 专门针对大数据量和频繁选择操作进行优化
 */
const OptimizedTable = memo(({
    dataSource = [],
    columns = [],
    rowSelection: externalRowSelection,
    selectableStatusCheck,
    onSelectionChange,
    ...tableProps
}) => {
    const [selectedKeys, setSelectedKeys] = useState(new Set());
    const [selectedRows, setSelectedRows] = useState([]);
    const selectionTimeoutRef = useRef(null);
    const lastSelectionRef = useRef({ keys: new Set(), rows: [] });

    // 优化：预计算可选择的行
    const selectableRowsMap = useMemo(() => {
        const map = new Map();
        dataSource.forEach(row => {
            const isSelectable = selectableStatusCheck ? selectableStatusCheck(row) : true;
            map.set(row.id, isSelectable);
        });
        return map;
    }, [dataSource, selectableStatusCheck]);

    // 优化：批量处理选择变更
    const handleSelectionChange = useCallback((selectedRowKeys, selectedRowsData) => {
        const newSelectedKeys = new Set(selectedRowKeys);
        
        // 立即更新 UI 状态
        setSelectedKeys(newSelectedKeys);
        
        // 防抖处理业务逻辑
        if (selectionTimeoutRef.current) {
            clearTimeout(selectionTimeoutRef.current);
        }
        
        selectionTimeoutRef.current = setTimeout(() => {
            // 只有当选择真正发生变化时才更新
            const lastKeys = lastSelectionRef.current.keys;
            const hasChanged = newSelectedKeys.size !== lastKeys.size || 
                              [...newSelectedKeys].some(key => !lastKeys.has(key));
            
            if (hasChanged) {
                setSelectedRows(selectedRowsData);
                lastSelectionRef.current = { 
                    keys: new Set(selectedRowKeys), 
                    rows: [...selectedRowsData] 
                };
                
                // 调用外部回调
                if (onSelectionChange) {
                    onSelectionChange(selectedRowKeys, selectedRowsData);
                }
            }
        }, 50); // 50ms 防抖，平衡响应性和性能
    }, [onSelectionChange]);

    // 优化：缓存 getCheckboxProps 函数
    const getCheckboxProps = useCallback((record) => {
        const isSelectable = selectableRowsMap.get(record.id);
        return {
            disabled: !isSelectable,
            // 添加额外的性能优化属性
            'data-row-id': record.id,
        };
    }, [selectableRowsMap]);

    // 优化：全选/反选处理
    const handleSelectAll = useCallback((selected, selectedRowsData, changeRows) => {
        if (selected) {
            // 全选：只选择可选择的行
            const selectableRows = dataSource.filter(row => selectableRowsMap.get(row.id));
            const selectableKeys = selectableRows.map(row => row.id);
            handleSelectionChange(selectableKeys, selectableRows);
        } else {
            // 反选：清空选择
            handleSelectionChange([], []);
        }
    }, [dataSource, selectableRowsMap, handleSelectionChange]);

    // 优化：构建 rowSelection 对象
    const optimizedRowSelection = useMemo(() => {
        if (!externalRowSelection && !onSelectionChange) {
            return undefined;
        }

        return {
            type: 'checkbox',
            selectedRowKeys: [...selectedKeys],
            onChange: handleSelectionChange,
            onSelectAll: handleSelectAll,
            getCheckboxProps,
            preserveSelectedRowKeys: true,
            // 性能优化配置
            columnWidth: 32,
            checkStrictly: false,
            // 自定义渲染选择列以提升性能
            renderCell: (checked, record, index, originNode) => {
                const isSelectable = selectableRowsMap.get(record.id);
                if (!isSelectable) {
                    return <Checkbox disabled />;
                }
                return originNode;
            },
            ...externalRowSelection,
        };
    }, [
        externalRowSelection,
        onSelectionChange,
        selectedKeys,
        handleSelectionChange,
        handleSelectAll,
        getCheckboxProps,
        selectableRowsMap
    ]);

    // 清理定时器
    useEffect(() => {
        return () => {
            if (selectionTimeoutRef.current) {
                clearTimeout(selectionTimeoutRef.current);
            }
        };
    }, []);

    // 当数据源变化时，清理无效的选择
    useEffect(() => {
        const dataIds = new Set(dataSource.map(item => item.id));
        const validSelectedKeys = [...selectedKeys].filter(key => dataIds.has(key));
        
        if (validSelectedKeys.length !== selectedKeys.size) {
            setSelectedKeys(new Set(validSelectedKeys));
            const validSelectedRows = selectedRows.filter(row => dataIds.has(row.id));
            setSelectedRows(validSelectedRows);
        }
    }, [dataSource, selectedKeys, selectedRows]);

    return (
        <Table
            {...tableProps}
            dataSource={dataSource}
            columns={columns}
            rowSelection={optimizedRowSelection}
            rowKey="id"
            // 性能优化配置
            virtual={dataSource.length > 100}
            scroll={{
                y: 'calc(100vh - 280px)',
                x: dataSource.length > 0 ? '100%' : false,
                ...tableProps.scroll
            }}
            showSorterTooltip={false}
            // 启用行级别的优化
            components={{
                body: {
                    row: memo(({ children, ...props }) => <tr {...props}>{children}</tr>),
                    cell: memo(({ children, ...props }) => <td {...props}>{children}</td>),
                },
            }}
        />
    );
});

OptimizedTable.displayName = 'OptimizedTable';

export default OptimizedTable;

/**
 * 使用示例：
 * 
 * <OptimizedTable
 *   dataSource={summaryList}
 *   columns={tableColumns}
 *   selectableStatusCheck={(record) => [1, 6, 14, 7].includes(record.status)}
 *   onSelectionChange={(selectedKeys, selectedRows) => {
 *     setSelecteds(selectedRows);
 *     setSelectedIds(selectedKeys);
 *   }}
 *   scroll={{ y: 'calc(100vh - 280px)' }}
 *   size="small"
 *   pagination={false}
 * />
 */

/**
 * 高级用法 - 自定义选择逻辑：
 * 
 * const customRowSelection = {
 *   type: 'checkbox',
 *   // 自定义选择逻辑
 *   onSelect: (record, selected, selectedRows, nativeEvent) => {
 *     // 处理单行选择
 *   },
 *   onSelectAll: (selected, selectedRows, changeRows) => {
 *     // 处理全选
 *   },
 *   // 自定义选择框渲染
 *   columnTitle: (
 *     <Checkbox
 *       indeterminate={indeterminate}
 *       onChange={onSelectAllChange}
 *       checked={checkAll}
 *     >
 *       全选
 *     </Checkbox>
 *   ),
 * };
 * 
 * <OptimizedTable
 *   rowSelection={customRowSelection}
 *   // ... 其他属性
 * />
 */
