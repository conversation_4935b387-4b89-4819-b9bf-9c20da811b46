package web

import (
	"clinflash-irt/models"
	"clinflash-irt/service"
	"clinflash-irt/tools"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

// SimulateRandomController struct
type SimulateRandomController struct {
	s service.SimulateRandomService
}

// Add 新增模拟随机
func (c *SimulateRandomController) Add(ctx *gin.Context) {
	var err error
	var info models.SimulateRandomInfo
	err = ctx.ShouldBindBodyWith(&info, binding.JSON)
	err = c.s.AddSimulateRandom(ctx, info)
	tools.Response(ctx, err)
}

// Update 修改模拟随机
func (c *SimulateRandomController) Update(ctx *gin.Context) {
	var err error
	id := ctx.Query("id")
	var info models.SimulateRandomInfo
	err = ctx.ShouldBindBodyWith(&info, binding.JSON)
	err = c.s.UpdateSimulateRandom(ctx, id, info)
	tools.Response(ctx, err)
}

// Run 运行
func (c *SimulateRandomController) Run(ctx *gin.Context) {
	id := ctx.Query("id")
	err := c.s.RunSimulateRandom(ctx, id)
	tools.Response(ctx, err)
}

// List 模拟随机列表
func (c *SimulateRandomController) List(ctx *gin.Context) {
	list, err := c.s.List(ctx)
	tools.Response(ctx, err, list)
}

// GetDetail 模拟随机模拟详情
func (c *SimulateRandomController) GetDetail(ctx *gin.Context) {
	id := ctx.Query("id")
	detail, err := c.s.GetDetail(ctx, id)
	tools.Response(ctx, err, detail)
}

// DownloadList 数据下载
func (c *SimulateRandomController) DownloadList(ctx *gin.Context) {
	id := ctx.Query("id")
	err := c.s.DownloadList(ctx, id)
	if err != nil {
		tools.Response(ctx, err)
	}
}

func (c *SimulateRandomController) GetFactor(ctx *gin.Context) {
	data, err := c.s.GetFactor(ctx)
	tools.Response(ctx, err, data)
}
