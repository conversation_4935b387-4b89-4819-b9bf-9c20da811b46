package web

import (
	"clinflash-irt/models"
	"clinflash-irt/service"
	"clinflash-irt/tools"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ProjectStorehouseController struct
type ProjectStorehouseController struct {
	s service.ProjectStorehouseService
}

// List 获取项目仓库
// Method:	Get
func (c *ProjectStorehouseController) List(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	data, err := c.s.List(ctx, customerID, projectID, envID)
	tools.Response(ctx, err, data)
}

// UserStoreHouses 用户关联仓库
// Method:	Get
func (c *ProjectStorehouseController) UserStoreHouses(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	roleID := ctx.Query("roleId")
	data, err := c.s.UserStoreHouses(ctx, customerID, projectID, envID, roleID)
	tools.Response(ctx, err, data)
}

// Save 新增/修改项目仓库
// Method:	Patch
func (c *ProjectStorehouseController) Save(ctx *gin.Context) {
	var data models.ProjectStorehouseParameter
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	err := c.s.Save(ctx, data)
	tools.Response(ctx, err)
}

// Delete 删除项目仓库
// Method:	Delete
func (c *ProjectStorehouseController) Delete(ctx *gin.Context) {
	var data map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	id := data["id"].(string)
	var cohortId = primitive.NilObjectID
	if data["cohortId"] != nil {
		cohortId, _ = primitive.ObjectIDFromHex(data["cohortId"].(string))
	}
	err := c.s.Delete(ctx, id, cohortId)
	tools.Response(ctx, err)
}

// ValidityReminder 项目仓库有效期提醒设置
// Method:	Patch
func (c *ProjectStorehouseController) ValidityReminder(ctx *gin.Context) {
	var data models.ProjectDepotSettingParameter
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	err := c.s.ValidityReminder(ctx, data)
	tools.Response(ctx, err)
}

// GetValidityReminder 获取项目仓库有效期提醒配置
// Method:	Get
func (c *ProjectStorehouseController) GetValidityReminder(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	cohortID := ctx.Query("cohortId")
	data, err := c.s.GetValidityReminder(ctx, customerID, projectID, envID, cohortID)
	tools.Response(ctx, err, data)
}

// EditDrugAlertValue 编辑研究产品警戒值
// Method:	POST
func (c *ProjectStorehouseController) EditDrugAlertValue(ctx *gin.Context) {
	var data map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	id := data["id"].(string)
	alert := models.Alert{
		MedicineName: data["medicineName"].(string),
		Value:        int(data["value"].(float64)),
	}
	var cohortId = primitive.NilObjectID
	if data["cohortId"] != nil {
		cohortId, _ = primitive.ObjectIDFromHex(data["cohortId"].(string))
	}
	err := c.s.EditDrugAlertValue(ctx, id, alert, cohortId)
	tools.Response(ctx, err)
}

// QueryOne 查询仓库研究产品信息
// Method:	GET
func (c *ProjectStorehouseController) QueryOne(ctx *gin.Context) {
	id := ctx.Query("id")
	data, err := c.s.QueryOne(ctx, id)
	tools.Response(ctx, err, data)
}

// DeleteDrug 删除仓库研究产品信息
// Method:	DELETE
func (c *ProjectStorehouseController) DeleteDrug(ctx *gin.Context) {
	var data map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	id := data["id"].(string)
	drugName := data["drugName"].(string)
	var cohortId = primitive.NilObjectID
	if data["cohortId"] != nil {
		cohortId, _ = primitive.ObjectIDFromHex(data["cohortId"].(string))
	}
	err := c.s.DeleteDrug(ctx, id, drugName, cohortId)
	tools.Response(ctx, err)
}

func (c *ProjectStorehouseController) SaveMedicineInfo(ctx *gin.Context) {
	err := c.s.SaveMedicineInfo(ctx)
	tools.Response(ctx, err)
}

func (c *ProjectStorehouseController) ListMedicine(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	roleID := ctx.Query("roleId")
	//attributeId := ctx.Query("attributeId")
	data, err := c.s.ListMedicine(ctx, customerID, projectID, envID, roleID)
	tools.Response(ctx, err, data)
}

func (c *ProjectStorehouseController) UpdateStoreBatchGroup(ctx *gin.Context) {
	var data models.DepotBatchGroup
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	err := c.s.UpdateStoreBatchGroup(ctx, data)
	tools.Response(ctx, err)
}

func (c *ProjectStorehouseController) GetStoreBatchGroup(ctx *gin.Context) {
	envID := ctx.Query("envId")
	data, err := c.s.GetStoreBatchGroup(ctx, envID)
	tools.Response(ctx, err, data)
}
