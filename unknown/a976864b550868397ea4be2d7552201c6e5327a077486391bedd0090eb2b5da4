package web

import (
	"clinflash-irt/models"
	"clinflash-irt/service"
	"clinflash-irt/tools"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

// StorehouseController ..
type StorehouseController struct {
	s service.StorehouseService
}

// Get 分页查询仓库列表
func (c *StorehouseController) Get(ctx *gin.Context) {
	keyword := ctx.Query("keyword")
	start, _ := strconv.Atoi(ctx.DefaultQuery("start", "0"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	data, err := c.s.List(ctx, map[string]string{"keyword": keyword}, start, limit)
	tools.Response(ctx, err, data)
}

// GetAll 查询仓库列表所有
func (c *StorehouseController) GetAll(ctx *gin.Context) {
	keyword, _ := ctx.GetQuery("keyword")
	data, err := c.s.ListAll(ctx, map[string]string{"keyword": keyword})
	tools.Response(ctx, err, data)
}

// Update 更新仓库
func (c *StorehouseController) Update(ctx *gin.Context) {
	var err error
	id := ctx.Query("id")
	var storehouse models.Storehouse
	err = ctx.ShouldBindBodyWith(&storehouse, binding.JSON)
	err = c.s.Update(ctx, id, storehouse)
	tools.Response(ctx, err)
}

// Delete 删除仓库
func (c *StorehouseController) Delete(ctx *gin.Context) {
	id := ctx.Query("id")
	err := c.s.Delete(ctx, id)
	tools.Response(ctx, err)
}

// Add 新增仓库
func (c *StorehouseController) Add(ctx *gin.Context) {
	var err error
	var storehouse models.Storehouse
	err = ctx.ShouldBindBodyWith(&storehouse, binding.JSON)
	err = c.s.Add(ctx, storehouse)
	tools.Response(ctx, err)
}
