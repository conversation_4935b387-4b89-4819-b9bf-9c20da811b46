package web

import (
	"clinflash-irt/models"
	"clinflash-irt/service"
	"clinflash-irt/tools"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"strconv"
)

// ProjectRoleController struct
type ProjectRoleController struct {
	s service.ProjectRoleService
}

// Get 查询配置的所有角色
func (c *ProjectRoleController) Get(ctx *gin.Context) {
	search := ctx.Query("keywords")
	projectId := ctx.Query("projectId")
	all := ctx.DefaultQuery("all", "") // 角色编辑主页面显示全部
	data, err := c.s.List(ctx, projectId, search, all)
	tools.Response(ctx, err, data)
}

// ListPool 查询的所有角色池
func (c *ProjectRoleController) ListPool(ctx *gin.Context) {
	templateStr := ctx.Query("template")
	template, _ := strconv.Atoi(templateStr)
	data, err := c.s.ListPool(ctx, template)
	tools.Response(ctx, err, data)
}

// Post 新增角色
func (c *ProjectRoleController) Post(ctx *gin.Context) {
	var err error
	var role models.ProjectRolePermission
	err = ctx.ShouldBindBodyWith(&role, binding.JSON)
	err = c.s.Add(ctx, role)
	tools.Response(ctx, err)
}

// Update 更新角色
func (c *ProjectRoleController) Update(ctx *gin.Context) {
	var err error
	id := ctx.Query("id")
	var role models.ProjectRolePermission
	err = ctx.ShouldBindBodyWith(&role, binding.JSON)
	err = c.s.Update(ctx, id, role)
	tools.Response(ctx, err)
}

// Delete 删除角色
func (c *ProjectRoleController) Delete(ctx *gin.Context) {
	var err error
	id := ctx.Query("id")
	err = c.s.Delete(ctx, id)
	tools.Response(ctx, err)
}

// GetBy 获取角色信息
func (c *ProjectRoleController) GetBy(ctx *gin.Context) {
	id := ctx.Query("id")
	Role, err := c.s.Get(ctx, id)
	tools.Response(ctx, err, Role)
}

func (c *ProjectRoleController) HasBindUser(ctx *gin.Context) {
	id := ctx.Query("id")
	data, err := c.s.HasBindUser(ctx, id)
	tools.Response(ctx, err, data)
}

// GetBy 获取角色信息
func (c *ProjectRoleController) Permission(ctx *gin.Context) {
	projectId := ctx.Query("projectId")
	Role, err := c.s.Permission(ctx, projectId)
	tools.Response(ctx, err, Role)
}
