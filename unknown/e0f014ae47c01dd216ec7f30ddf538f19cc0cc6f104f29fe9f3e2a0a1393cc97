import {get, patch, post} from "utils/http";


export const list = (params: object = {}) => get(`/roles`, { params })
export const listPool = (params: object = {}) => get(`/roles/pool`, { params })
export const add = (data: object = {}) => post(`/roles`, { data })
export const update = (params: object = {},data: object = {}) => patch(`/roles`, { params,data })
export const isBindRole = (params: object = {}) => get(`/roles/is-bind-role`, { params })
export const roleIsBind = (params: object = {}) => get(`/roles/roleIsBind`, { params })