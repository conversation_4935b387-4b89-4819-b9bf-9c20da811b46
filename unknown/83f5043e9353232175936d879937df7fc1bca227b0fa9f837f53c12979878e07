package tools

import (
	"clinflash-irt/models"
	"context"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"io/ioutil"
	"strconv"
	"strings"
	"time"
)

type HttpLog struct {
	ID         primitive.ObjectID `bson:"_id"`
	RequestId  string
	Url        string
	HttpMethod string
	UserEmail  string
	ReqBody    string
	StackTrace string
	Time       string
	CostTime   int64              //请求花费的毫秒数
	ExpireAt   primitive.DateTime `json:"expireAt" bson:"expire_at"`
}

type ErrorLog struct {
	ID         primitive.ObjectID `bson:"_id"`
	Method     string
	StackTrace string
	Time       string
	ExpireAt   primitive.DateTime `json:"expireAt" bson:"expire_at"`
}

func RequestId() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		uid, _ := uuid.NewRandom()
		id := uid.String()
		ctx.Set("Request-Id", id)
		ctx.Next()
	}
}

func Logger() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		nowBefore := time.Now()
		ctx.Set("Request-Time", nowBefore.UnixMilli())
		reqBody := ""
		if ctx.ContentType() == "application/json" {
			var body []byte
			body, _ = ioutil.ReadAll(ctx.Request.Body)
			ctx.Set(gin.BodyBytesKey, body)
			reqBody = string(body)
			reqBody = strings.ReplaceAll(reqBody, " ", "")
			reqBody = strings.ReplaceAll(reqBody, "\n", "")
		}
		u, ok := ctx.Get("user")
		email := ""
		if ok {
			user := u.(models.User)
			email = user.Email
		}
		hours, _ := time.ParseDuration("720h")
		requestId, _ := ctx.Get("Request-Id")
		requestLog := HttpLog{
			ID:         primitive.NewObjectID(),
			RequestId:  requestId.(string),
			Url:        ctx.Request.RequestURI,
			HttpMethod: ctx.Request.Method,
			UserEmail:  email,
			ReqBody:    reqBody,
			StackTrace: "",
			Time:       nowBefore.UTC().Add(time.Hour * time.Duration(8)).Format("2006-01-02 15:04:05.999"),
			ExpireAt:   primitive.NewDateTimeFromTime(time.Now().Add(hours)),
		}
		go func() {
			_, _ = Database.Collection("error_log").InsertOne(nil, requestLog)

			fields := logrus.Fields{"ID": requestLog.ID.Hex(), "RequestId": requestLog.RequestId, "Url": requestLog.Url, "HttpMethod": requestLog.HttpMethod,
				"UserEmail": requestLog.UserEmail, "ReqBody": requestLog.ReqBody, "StackTrace": requestLog.StackTrace, "Time": requestLog.Time,
				"ExpireAt": requestLog.ExpireAt.Time().GoString()}
			logrus.WithFields(fields).Info(fmt.Sprintf("%s %s", ctx.Request.Method, ctx.Request.URL.Path))

		}()

		ctx.Next()
		err, exists := ctx.Get("Response-Error")
		_, isServerError := err.(*ServerError)
		if exists && err != nil && !isServerError {
			nowAfter := time.Now()
			hours, _ := time.ParseDuration("720h")
			requestId, _ := ctx.Get("Request-Id")
			requestTime, _ := ctx.Get("Request-Time")
			requestT := requestTime.(int64)
			responseLog := HttpLog{
				ID:         primitive.NewObjectID(),
				RequestId:  requestId.(string),
				StackTrace: fmt.Sprintf("%+v", err),
				Time:       nowAfter.UTC().Add(time.Hour * time.Duration(8)).Format("2006-01-02 15:04:05.999"),
				CostTime:   nowAfter.UnixMilli() - requestT,
				ExpireAt:   primitive.NewDateTimeFromTime(time.Now().Add(hours)),
			}
			go func() {
				_, _ = Database.Collection("error_log").InsertOne(nil, responseLog)

				fields := logrus.Fields{"ID": responseLog.ID.Hex(), "RequestId": responseLog.RequestId, "StackTrace": responseLog.StackTrace, "Time": responseLog.Time,
					"CostTime": strconv.FormatInt(responseLog.CostTime, 10), "ExpireAt": responseLog.ExpireAt.Time().GoString()}
				logrus.WithFields(fields).Info(fmt.Sprintf("%s %s", ctx.Request.Method, ctx.Request.URL.Path))

			}()
		}

	}
}

func SaveErrorLog(methodName string, err error) {
	go func() {
		hours, _ := time.ParseDuration("720h")
		errorLog := ErrorLog{
			ID:         primitive.NewObjectID(),
			Method:     methodName,
			StackTrace: fmt.Sprintf("%+v", err),
			Time:       time.Now().UTC().Add(time.Hour * time.Duration(8)).Format("2006-01-02 15:04:05.999"),
			ExpireAt:   primitive.NewDateTimeFromTime(time.Now().Add(hours)),
		}
		_, _ = Database.Collection("error_log").InsertOne(nil, errorLog)

		fields := logrus.Fields{"ID": errorLog.ID.Hex(), "Method": errorLog.Method, "StackTrace": errorLog.StackTrace, "Time": errorLog.Time,
			"ExpireAt": errorLog.ExpireAt.Time().GoString()}
		logrus.WithFields(fields).Info()
	}()
}

func SavePushEdcErrorLog(ctx *gin.Context, err error) {
	go func() {
		if err != nil {
			nowAfter := time.Now()
			hours, _ := time.ParseDuration("720h")
			requestId, _ := ctx.Get("Request-Id")
			requestTime, _ := ctx.Get("Request-Time")
			requestT := requestTime.(int64)
			responseLog := HttpLog{
				ID:         primitive.NewObjectID(),
				RequestId:  requestId.(string),
				StackTrace: fmt.Sprintf("%+v", err),
				Time:       nowAfter.UTC().Add(time.Hour * time.Duration(8)).Format("2006-01-02 15:04:05.999"),
				CostTime:   nowAfter.UnixMilli() - requestT,
				ExpireAt:   primitive.NewDateTimeFromTime(time.Now().Add(hours)),
			}
			_, _ = Database.Collection("error_log").InsertOne(nil, responseLog)

			fields := logrus.Fields{"ID": responseLog.ID.Hex(), "RequestId": responseLog.RequestId, "StackTrace": responseLog.StackTrace, "Time": responseLog.Time,
				"CostTime": strconv.FormatInt(responseLog.CostTime, 10), "ExpireAt": responseLog.ExpireAt.Time().GoString()}
			logrus.WithFields(fields).Info(fmt.Sprintf("%s %s", ctx.Request.Method, ctx.Request.URL.Path))
		}
	}()
}

// 准备日志数据的公共方法
func PrepareLogData(ctx *gin.Context) map[string]interface{} {
	data := make(map[string]interface{})

	// 提取请求信息
	if requestId, exists := ctx.Get("Request-Id"); exists {
		data["request_id"] = requestId.(string)
	}

	if requestTime, exists := ctx.Get("Request-Time"); exists {
		data["request_time"] = requestTime.(int64)
	} else {
		data["request_time"] = time.Now().UnixMilli()
	}

	// 其他通用信息
	data["method"] = ctx.Request.Method
	data["path"] = ctx.Request.URL.Path
	data["client_ip"] = ctx.ClientIP()
	data["user_agent"] = ctx.Request.UserAgent()

	return data
}

func SavePushEdcErrorLogNew(err error, logData map[string]interface{}) {
	go func(data map[string]interface{}) {
		if err != nil {
			nowAfter := time.Now()
			hours, _ := time.ParseDuration("720h")

			// 使用传入的日志数据而不是从 ctx 获取
			requestId := data["request_id"].(string)
			requestTime := data["request_time"].(int64)
			method := data["method"].(string)
			path := data["path"].(string)

			responseLog := HttpLog{
				ID:         primitive.NewObjectID(),
				RequestId:  requestId,
				StackTrace: fmt.Sprintf("%+v", err),
				Time:       nowAfter.UTC().Add(time.Hour * time.Duration(8)).Format("2006-01-02 15:04:05.999"),
				CostTime:   nowAfter.UnixMilli() - requestTime,
				ExpireAt:   primitive.NewDateTimeFromTime(time.Now().Add(hours)),
			}

			// 使用独立上下文
			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()
			_, _ = Database.Collection("error_log").InsertOne(ctx, responseLog)

			fields := logrus.Fields{
				"ID":         responseLog.ID.Hex(),
				"RequestId":  responseLog.RequestId,
				"StackTrace": responseLog.StackTrace,
				"Time":       responseLog.Time,
				"CostTime":   strconv.FormatInt(responseLog.CostTime, 10),
				"ExpireAt":   responseLog.ExpireAt.Time().GoString(),
			}
			logrus.WithFields(fields).Info(fmt.Sprintf("%s %s", method, path))
		}
	}(logData) // 传入日志数据的副本
}
