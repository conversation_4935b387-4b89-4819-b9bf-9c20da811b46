package models

import "go.mongodb.org/mongo-driver/bson/primitive"

// SupplyPlan ..
type SupplyPlan struct {
	ID             primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID     primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID      primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID  primitive.ObjectID `json:"envId" bson:"env_id"`
	SupplyPlanInfo SupplyPlanInfo     `json:"info" bson:"info"`
}

// SupplyPlanInfo ..
type SupplyPlanInfo struct {
	Name         string               `json:"name"`
	AllSite      bool                 `json:"allSite" bson:"all_site"`
	SiteIds      []primitive.ObjectID `json:"siteIds" bson:"site_ids"`
	Status       int                  `json:"status" bson:"status"` //0 无效 1有效
	StorehouseID primitive.ObjectID   `json:"storehouseId" bson:"storehouse_id"`
	Description  string               `json:"description" bson:"description"`
	PlanControl  bool                 `json:"planControl" bson:"plan_control"`
	SiteWarning  []int                `json:"siteWarning" bson:"site_warning"`
	AutoSupply   []int                `json:"autoSupply" bson:"auto_supply"`
}
type SupplyPlanSiteOperationLog struct {
	AllSiteTranKey string
	SiteNames      string
}

// SupplyPlanMedicine ..
type SupplyPlanMedicine struct {
	ID            primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID    primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID     primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID primitive.ObjectID `json:"envId" bson:"env_id"`
	SupplyPlanID  primitive.ObjectID `json:"supplyPlanId" bson:"supply_plan_id"`
	Medicine      MedicinePlanInfo   `json:"info" bson:"info"`
}

// MedicinePlanInfo ..
type MedicinePlanInfo struct {
	MedicineName       string `json:"medicineName" bson:"medicine_name"`              //研究产品名称
	IsBlind            bool   `json:"isBlind"`                                        //研究产品是否是盲态药物
	Warning            int    `json:"warning"`                                        // 研究产品警戒值
	Buffer             int    `json:"buffer"`                                         //最大缓冲
	ForecastMin        *int   `json:"forecastMin" bson:"forecast_min"`                //预测最小窗口期
	ForecastMax        *int   `json:"forecastMax" bson:"forecast_max"`                //预测最大窗口期
	SecondSupply       int    `json:"secondSupply" bson:"second_supply"`              //再供应量
	InitSupply         int    `json:"initSupply" bson:"init_supply"`                  //初始发药剂量
	UnDistributionDate int    `json:"unDistributionDate" bson:"un_distribution_date"` //不配送天数
	NotCountedDate     int    `json:"notCountedDate" bson:"not_counted_date"`         // 不计入天数
	UnProvideDate      int    `json:"unProvideDate" bson:"un_provide_date"`           //不发放天数
	ValidityReminder   int    `json:"validityReminder" bson:"validity_reminder"`      //有效期提醒
	AutoSupply         bool   `json:"autoSupply" bson:"auto_supply"`                  //是否自动配药
	AutoSupplySize     []int8 `json:"autoSupplySize" bson:"auto_supply_size"`         //自动配药量 1、最大缓冲 2、再供应量 3、最低预测
	SupplyMode         int8   `json:"supplyMode" bson:"supply_mode"`                  // 补充方式 1、全研究产品补充 2、单研究产品补充 3、全研究产品补充+1一个随机号研究产品 4 单研究产品补充+1一个随机号研究产品
	DispensingAlarm    int    `json:"dispensingAlarm" bson:"dispensing_alarm"`        // 发放警戒值
}

type SupplyPlanMedicineCheck struct {
	Index          int    `json:"index" bson:"index"`
	MedicineName   string `json:"medicineName" bson:"medicine_name"`      //研究产品名称
	AutoSupplySize string `json:"autoSupplySize" bson:"auto_supply_size"` //自动配药量 1、最大缓冲 2、再供应量 3、最低预测
	SupplyMode     string `json:"supplyMode" bson:"supply_mode"`          // 补充方式 1、全研究产品补充 2、单研究产品补充 3、全研究产品补充+1一个随机号研究产品 4 单研究产品补充+1一个随机号研究产品
}

type DrugPackageCount struct {
	Count              int
	UnDistributionDate string
	Other              bool
}
