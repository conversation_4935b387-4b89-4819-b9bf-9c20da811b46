<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clinflash</title>
    <style>
        /* 字体 */
        /* 微软雅黑 */
        @font-face {
            font-family: MicrosoftYaHei;
            font-weight: normal;
            src: url("./assets/fonts/Microsoft-YaHei.ttf") format('truetype');
        }

        /* 微软雅黑粗体 */
        @font-face {
            font-family: MicrosoftYaHei;
            font-weight: bold;
            src: url("./assets/fonts/Microsoft-YaHei-Bold.ttf") format('truetype');
        }

        /* 宋体 */
        @font-face {
            font-family: Song;
            font-weight: normal;
            src: url("./assets/fonts/SourceHanSerifCN-Regular.ttf") format('truetype');
        }

        /* 宋体加粗 */
        @font-face {
            font-family: Song;
            font-weight: bold;
            src: url("./assets/fonts/SourceHanSerifCN-Bold.ttf") format('truetype');
        }

        /* 等线 Light */
        @font-face {
            font-family: DengXianLight;
            font-weight: normal;
            src: url("./assets/fonts/DengXian-Light.ttf") format('truetype');
        }

        /* Arial */
        @font-face {
            font-family: Arial;
            font-weight: normal;
            src: url("./assets/fonts/Arial.ttf") format('truetype');
        }

        /* Calibri */
        @font-face {
            font-family: Calibri;
            font-weight: normal;
            src: url("./assets/fonts/Calibri-Regular.ttf") format('truetype');
        }


        /* TimesNewRoman */
        @font-face {
            font-family: TimesNewRoman;
            font-weight: normal;
            src: url("./assets/fonts/Times-New-Roman.ttf") format('truetype');
        }


        /* 清除默认的 HTML 和 body 的 margin，避免影响 WeasyPrint 的渲染 */
        html, body {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 设置统一的页面内边距 */
        @page {
            size: A4;
            margin-top: 1.3cm;
            margin-bottom: 2.75cm;
            margin-left: 1.41cm;
            margin-right: 1.8cm;
        }

        /* 在每页的页脚中显示当前页码和总页数 */
        @page {
            @top-center {
                content: '';
                background: #000; /* 设定细线颜色 */
                display: block;
                height: 0.05mm; /* 设置线条的高度 */
                opacity: 0.15; /* 设定透明度 */
                width: 115%; /* 设置宽度为页宽 */
                transform: translate(2mm, -2mm); /* 向右偏移 2mm，向上偏移 2mm */
            }
            @bottom-right {
                content: counter(page) " / " counter(pages); /* 在右下角显示页码 */
                font-size: 9pt;
                font-family: MicrosoftYaHei;
                height: 1cm;
                vertical-align: middle;
                transform: translateY(1.2cm); /* 向下偏移 */
            }
        }

        /* 页眉和页脚样式 */
        header, footer {
            position: fixed;
            left: 0; /* 左边距与正文一致 */
            right: 0; /* 右边距与正文一致 */
            text-align: center;
            margin: 0;
        }

        /* 页眉固定在页面顶部 */
        header {
            top: -1.2cm;
        }

        /* 设置页眉内容为水平排列 */
        .header-content {
            display: flex;
            justify-content: space-between; /* 左右两边对齐 */
            align-items: center; /* 垂直居中 */
        }

        /* 页眉页脚样式  */
        .header-text, .footer-text {
            font-family: Song, sans-serif;
            font-size: 10pt;
        }

        .header-text-en {
            font-family: TimesNewRoman, sans-serif;
            font-size: 10pt;
        }

        /* 右侧的 logo */
        .header-logo {
            width: 3.2cm;
            height: 1.2cm;
        }

        /* 页脚固定在页面底部 */
        footer {
            bottom: -2.2cm;
        }

        /* 设置页脚内容为水平排列 */
        .footer-content {
            display: flex;
            justify-content: space-between; /* 左右两边对齐 */
            align-items: center; /* 垂直居中 */
            font-size: 12px;
            height: 1cm;
        }

        /* 右侧的页码 */
        .footer-page-number {

        }


        /* 封面页的样式 */
        .cover-page {
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            page-break-after: always; /* 确保封面后自动分页 */
        }


        #toc ul {
            display: block;
            padding-left: 0;
            font-family: MicrosoftYaHei, sans-serif;
            font-weight: bold;
            font-size: 11pt;
        }

        #toc ul ul {
            display: block;
            padding-left: 2em; /* 二级目录缩进 */
        }

        #toc ul ul ul {
            display: block;
            padding-left: 2em; /* 三级目录缩进 */
            font-family: MicrosoftYaHei, sans-serif;
        }

        #toc li {
            display: block;
        }

        #toc a {
            color: inherit;
            text-decoration: none;
            display: block;
            line-height: 1.08; /* 行距 */
            margin-top: 0; /* 段前 0 行 */
            margin-bottom: 8pt; /* 段后 8 磅 */
        }

        #toc a::before {
            /*content: target-text(attr(href)) ' ' leader('.') ' ' target-counter(attr(href), page);*/
            content: target-text(attr(href)) ' ';
        }

        /* 分成两部分显示，来为 ... 和页码单独设置字体 */
        #toc a::after {
            content: leader('.') ' ' target-counter(attr(href), page);
            font-family: Calibri, sans-serif;
            font-size: 11pt;
        }

        #toc p {
            font-family: DengXianLight, sans-serif;
            font-size: 18pt; /* 三号字体对应16pt */
            color: #568fc3;
            margin: 0;
        }

        /* 设置页面内容 */
        .chapter {
            break-after: page; /* 模拟分页 */
            margin: 0;
        }

        .cover-content p {
            font-family: Song, sans-serif;
            font-weight: bold;
            /*font-size: 18pt; !* 小二号字体对应18pt *!*/
            font-size: 16pt; /* 三号字体对应18pt  模拟随机报告英文标题时封面有的不能放在一行*/
            line-height: 1.1; /* 行距 */
            margin-top: 0; /* 段前 0 行 */
            margin-bottom: 58.5pt; /* 段后 58.5 磅 */
        }

        /* 一级标题样式 */
        h1 {
            font-family: MicrosoftYaHei, sans-serif;
            font-weight: bold;
            font-size: 14pt; /* 四号字体对应14pt */
            line-height: 1.08; /* 行距 */
            margin-top: 0; /* 段前 0 行 */
            /*margin-bottom: 6.95pt; !* 段后 6.95 磅 *!*/
            margin-bottom: 24.2pt; /* 段后 24.2 磅 */
        }

        /* 二级标题样式 */
        h2 {
            font-family: MicrosoftYaHei, sans-serif;
            font-weight: bold;
            font-size: 12pt; /* 小四号字体对应12pt */
            line-height: 1.08; /* 行距 */
            margin-top: 0; /* 段前 0 行 */
            margin-bottom: 8pt; /* 段后 58.5 磅 */
        }

        /* 三级标题样式 */
        h3 {
            font-family: MicrosoftYaHei, sans-serif;
            font-size: 10.5pt; /* 五号字体对应10.5pt */
            line-height: 1.73; /* 行距 */
            margin-top: 13pt; /* 段前 0 行 */
            margin-bottom: 13pt; /* 段后 58.5 磅 */
        }

        .content {
            font-family: Song, sans-serif;
            font-size: 10.5pt; /* 五号字体对应10.5pt */
            /*hyphens: auto; !* 长单词中自动添加连字符，使折断更自然。 *!*/
            word-break: break-all;
        }

        /* 正文样式 */
        .content p {
            line-height: 1.64; /* 行距 */
            margin-top: 0; /* 段前 0 行 */
            margin-bottom: 6.95pt; /* 段后 6.95 磅 */
        }

        .small-title {
            line-height: 1.08; /* 行距 */
            margin-top: 0; /* 段前 0 行 */
            margin-bottom: 8pt; /* 段后 6.95 磅 */
        }

        .small-part {
            margin-bottom: 30px;
        }

        .small-title-bold {
            font-weight: bold;
            line-height: 1.08; /* 行距 */
            margin-top: 0; /* 段前 0 行 */
            margin-bottom: 8pt; /* 段后 6.95 磅 */
        }

        /* 首行缩进的段落 */
        .indentP {
            text-indent: 2em; /* 首行缩进，em单位是相对于当前字体尺寸的单位，1em等于当前字体尺寸 */
        }

        /*!* 正文英文样式 *!*/
        /*.content .english {*/
        /*    font-family: Arial, sans-serif; !* Arial，如果没有则使用微软雅黑，再没有则使用无衬线字体 *!*/
        /*    font-size: 10.5pt; !* 五号字体对应10.5pt *!*/
        /*}*/


        table {
            width: 100%;
            border-collapse: collapse; /* 去除单元格之间的间隙 */
            break-inside: avoid;
            margin-bottom: 30px;
        }

        th, td {
            border: 1px solid black; /* 设置边框 */
            padding: 5px; /* 设置单元格内边距 */
            text-align: left; /* 内容居中 */
        }

        /*th {*/
        /*    background-color: #f1f1f1; !* 表头背景色 *!*/
        /*}*/
    </style>
</head>
<body>

<!-- 页眉 -->
<header>
    <div class="header-content">
        <span class="header-text-en">Clinflash IRT&nbsp;&nbsp;<span class="header-text">{{.SimulateRandomReport}}</span></span>
        <img src="./assets/images/report_logo.png" alt="Logo" class="header-logo">
    </div>
</header>

<!-- 页脚 -->
<footer>
    <div class="footer-content">
        <div class="footer-text">{{.ProjectNumber}}({{.Env}})</div>
        <div class="footer-page-number"></div>
    </div>
</footer>


<!-- 封面 -->
<div class="cover-page">
    <div class="cover-content">
        <p>&nbsp;</p>
        <p>{{.Sponsor}}</p>
        <p>{{.ProjectNumber}}</p>
        <p>{{.SimulateRandomReport}}</p>
        <p>{{.Env}}</p>
        {{if ne .Cohort ""}}
        <p>{{.Cohort}}</p>
        {{end}}
        <p>{{.RunningTime}}{{.RunningDate}}</p>
        <p>{{.GenerationTime}}{{.GenerationDate}}</p>
        <p>{{.Generator}}{{.CreateBy}}</p>
    </div>
</div>

<!-- 目录 -->
<div class="chapter" id="toc">
    <p>{{.Directory}}</p>
    <ul>
        <li><a href="#chapter-1-h1"></a></li>
        <li><a href="#chapter-2-h1"></a></li>
        <li>
            <a href="#chapter-3-h1"></a>
            <ul>
                <li>
                    <a href="#chapter-3.1-h2"></a>
                </li>
                <li>
                    <a href="#chapter-3.2-h2"></a>
                    <ul>
                        <li><a href="#chapter-3.2.1-h3"></a></li>
                        <li><a href="#chapter-3.2.2-h3"></a></li>
                        <li><a href="#chapter-3.2.3-h3"></a></li>
                    </ul>
                </li>
                <li>
                    <a href="#chapter-3.3-h2"></a>
                </li>
            </ul>
        </li>
    </ul>
</div>

<!-- 章节 1 -->
<div class="chapter" id="chapter-1">
    <div class="content">
        <h1 id="chapter-1-h1">1 {{.Summary}}</h1>
        <p class="indentP">
            {{.SummaryDetails}}
        </p>
    </div>
</div>

<!-- 章节 2 -->
<div class="chapter" id="chapter-2">
    <div class="content">
        <h1 id="chapter-2-h1">2 {{.ConfigureParameterDetail.Title}}</h1>
        {{range .ConfigureParameterDetail.ConfigureParameterInfoList}}
        <table>
            <tr>
                <td>{{.RandomTypeLabel}}</td>
                <td colspan="3">{{.RandomType}}</td>
            </tr>
            <tr>
                <td>{{.RandomTableNameLabel}}</td>
                <td colspan="3">{{.RandomTableName}}</td>
            </tr>
            {{if eq .Type "2"}}
            <tr>
                <td>{{.BiasProbabilityLabel}}</td>
                <td colspan="3">{{.BiasProbability}}</td>
            </tr>
            {{end}}
            <tr>
                <td>{{.RandomIdNumberLabel}}</td>
                <td colspan="3">{{.RandomIdNumber}}</td>
            </tr>
            <!--组别配置-->
            <tr>
                <td rowspan="{{add (len .GroupConfigurationDetail.GroupDetailList) 1}}">
                    {{.GroupConfigurationDetail.GroupConfigurationLabel}}
                </td>
                <td>{{.GroupConfigurationDetail.GroupNameLabel}}</td>
                <td>{{.GroupConfigurationDetail.SubGroupNameLabel}}</td>
                <td>{{.GroupConfigurationDetail.GroupProportionLabel}}</td>
            </tr>
            {{if gt (len .GroupConfigurationDetail.GroupDetailList) 0}}
            {{range .GroupConfigurationDetail.GroupDetailList}}
            <tr>
                <td>{{ .GroupName }}</td>
                <td>{{ .SubGroupName }}</td>
                <td>{{ .GroupProportion }}</td>
            </tr>
            {{end}}
            {{else}}
            <tr>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
            </tr>
            {{end}}
            <!--分层因素-->
            {{if eq .Type "2"}}
            <tr>
                <td rowspan="{{add (len .FactorConfigurationDetail.FactorDetailList) 1}}">
                    {{.FactorConfigurationDetail.FactorConfigurationLabel}}
                </td>
                <td>{{.FactorConfigurationDetail.FactorNameLabel}}</td>
                <td>{{.FactorConfigurationDetail.OptionLabel}}</td>
                <td>{{.FactorConfigurationDetail.WeightRatioLabel}}</td>
            </tr>
            {{if gt (len .FactorConfigurationDetail.FactorDetailList) 0}}
            {{range .FactorConfigurationDetail.FactorDetailList}}
            <tr>
                <td>{{ .FactorName }}</td>
                <td>{{ .Option }}</td>
                <td>{{ .WeightRatio }}</td>
            </tr>
            {{end}}
            {{else}}
            <tr>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
            </tr>
            {{end}}


            {{else}}
            <tr>
                <td rowspan="{{add (len .FactorConfigurationDetail.FactorDetailList) 1}}">
                    {{.FactorConfigurationDetail.FactorConfigurationLabel}}
                </td>
                <td>{{.FactorConfigurationDetail.FactorNameLabel}}</td>
                <td colspan="2">{{.FactorConfigurationDetail.OptionLabel}}</td>
            </tr>
            {{if gt (len .FactorConfigurationDetail.FactorDetailList) 0}}
            {{range .FactorConfigurationDetail.FactorDetailList}}
            <tr>
                <td>{{ .FactorName }}</td>
                <td colspan="2">{{ .Option }}</td>
            </tr>
            {{end}}
            {{else}}
            <tr>
                <td>&nbsp;</td>
                <td colspan="2">&nbsp;</td>
            </tr>
            {{end}}

            {{end}}

            <tr>
                <td>{{.IsCountryLabel}}</td>
                <td colspan="3">{{.IsCountry}}</td>
            </tr>
            <tr>
                <td>{{.IsSiteLabel}}</td>
                <td colspan="3">{{.IsSite}}</td>
            </tr>
            <tr>
                <td>{{.IsRegionLabel}}</td>
                <td colspan="3">{{.IsRegion}}</td>
            </tr>

        </table>
        {{end}}
    </div>
</div>

<!-- 章节 3 -->
<div class="chapter content" id="chapter-3">
    <!--模拟随机参数-->
    <div class="chapter">
        <h1 id="chapter-3-h1">3 {{.SimulateRandomDetail.Title}}</h1>
        <h2 id="chapter-3.1-h2">3.1 {{.SimulateRandomDetail.SimulateRandomParameterDetail.Title}}</h2>
        <table>
            <colgroup>
                <col style="width: 50%;">
                <col style="width: 50%;">
            </colgroup>
            <tr>
                <td>
                    {{.SimulateRandomDetail.SimulateRandomParameterDetail.SimulateRandomNameLabel}}
                </td>
                <td>
                    {{.SimulateRandomDetail.SimulateRandomParameterDetail.SimulateRandomName}}
                </td>
            </tr>
            <tr>
                <td>
                    {{.SimulateRandomDetail.SimulateRandomParameterDetail.RandomListNameLabel}}
                </td>
                <td>
                    {{.SimulateRandomDetail.SimulateRandomParameterDetail.RandomListName}}
                </td>
            </tr>
            <tr>
                <td>
                    {{.SimulateRandomDetail.SimulateRandomParameterDetail.SiteNumberLabel}}
                </td>
                <td>
                    {{.SimulateRandomDetail.SimulateRandomParameterDetail.SiteNumber}}
                </td>
            </tr>
            <tr>
                <td>
                    {{.SimulateRandomDetail.SimulateRandomParameterDetail.RegionNumberLabel}}
                </td>
                <td>
                    {{.SimulateRandomDetail.SimulateRandomParameterDetail.RegionNumber}}
                </td>
            </tr>
            <tr>
                <td>
                    {{.SimulateRandomDetail.SimulateRandomParameterDetail.CountryNumberLabel}}
                </td>
                <td>
                    {{.SimulateRandomDetail.SimulateRandomParameterDetail.CountryNumber}}
                </td>
            </tr>
            <tr>
                <td>
                    {{.SimulateRandomDetail.SimulateRandomParameterDetail.RunNumberLabel}}
                </td>
                <td>
                    {{.SimulateRandomDetail.SimulateRandomParameterDetail.RunNumber}}
                </td>
            </tr>
            <tr>
                <td>
                    {{.SimulateRandomDetail.SimulateRandomParameterDetail.SubjectQuantityLabel}}
                </td>
                <td>
                    {{.SimulateRandomDetail.SimulateRandomParameterDetail.SubjectQuantity}}
                </td>
            </tr>
            {{if and .SimulateRandomDetail.SimulateRandomParameterDetail.FactorRatio (gt (len .SimulateRandomDetail.SimulateRandomParameterDetail.FactorRatio) 0)}}
            <tr>
                <td>{{.SimulateRandomDetail.SimulateRandomParameterDetail.FactorRatioLabel}}</td>
                <td>
                    {{range $index, $ratio := .SimulateRandomDetail.SimulateRandomParameterDetail.FactorRatio}}
                    {{if $index}}<br>{{end}}{{ $ratio }}
                    {{end}}
                </td>
            </tr>
            {{end}}
        </table>
    </div>
    <!--总览-->
    <div class="chapter">
        <h2 id="chapter-3.2-h2">3.2 {{.SimulateRandomDetail.OverviewDetail.Title}}</h2>
        <div>
            <h3 id="chapter-3.2.1-h3">3.2.1 {{.SimulateRandomDetail.OverviewDetail.AvgSDOverviewDetail.Title}}</h3>
            {{if gt (len .SimulateRandomDetail.OverviewDetail.Groups) 0}}
            {{- $avgSDTitle := .SimulateRandomDetail.OverviewDetail.AvgSDOverviewDetail.Title -}}
            <!--项目-->
            {{if gt (len .SimulateRandomDetail.OverviewDetail.AvgSDOverviewDetail.AvgSDOverview.ProjectOverView.AvgSDs)
            0}}
            <table>
                <tr>
                    <td>{{.SimulateRandomDetail.ProjectLabel}}</td>
                    {{range .SimulateRandomDetail.OverviewDetail.Groups}}
                    <td>{{.}}({{$avgSDTitle}})</td>
                    {{end}}
                </tr>
                <tr>
                    <td>
                        {{.SimulateRandomDetail.OverviewDetail.AvgSDOverviewDetail.AvgSDOverview.ProjectOverView.Name}}
                    </td>
                    {{range
                    .SimulateRandomDetail.OverviewDetail.AvgSDOverviewDetail.AvgSDOverview.ProjectOverView.AvgSDs}}
                    <td>{{.Avg}}+/-{{.SD}}</td>
                    {{end}}
                </tr>
            </table>
            {{end}}

            <!--中心-->
            {{if gt (len .SimulateRandomDetail.OverviewDetail.AvgSDOverviewDetail.AvgSDOverview.SiteOverView) 0}}
            <table>
                <tr>
                    <td>{{.SimulateRandomDetail.SerialLabel}}</td>
                    <td>{{.SimulateRandomDetail.SiteLabel}}</td>
                    {{range .SimulateRandomDetail.OverviewDetail.Groups}}
                    <td>{{.}}({{$avgSDTitle}})</td>
                    {{end}}
                </tr>
                {{range $index, $item :=
                .SimulateRandomDetail.OverviewDetail.AvgSDOverviewDetail.AvgSDOverview.SiteOverView}}
                <tr>
                    <td>{{add $index 1}}</td>
                    <td>{{$item.Name}}</td>
                    {{range $item.AvgSDs}}
                    <td>{{.Avg}}+/-{{.SD}}</td>
                    {{end}}
                </tr>
                {{end}}
            </table>
            {{end}}

            <!--区域-->
            {{if gt (len .SimulateRandomDetail.OverviewDetail.AvgSDOverviewDetail.AvgSDOverview.RegionOverView) 0}}
            <table>
                <tr>
                    <td>{{.SimulateRandomDetail.SerialLabel}}</td>
                    <td>{{.SimulateRandomDetail.RegionLabel}}</td>
                    {{range .SimulateRandomDetail.OverviewDetail.Groups}}
                    <td>{{.}}({{$avgSDTitle}})</td>
                    {{end}}
                </tr>
                {{range $index, $item :=
                .SimulateRandomDetail.OverviewDetail.AvgSDOverviewDetail.AvgSDOverview.RegionOverView}}
                <tr>
                    <td>{{add $index 1}}</td>
                    <td>{{$item.Name}}</td>
                    {{range $item.AvgSDs}}
                    <td>{{.Avg}}+/-{{.SD}}</td>
                    {{end}}
                </tr>
                {{end}}
            </table>
            {{end}}

            <!--国家-->
            {{if gt (len .SimulateRandomDetail.OverviewDetail.AvgSDOverviewDetail.AvgSDOverview.CountryOverView) 0}}
            <table>
                <tr>
                    <td>{{.SimulateRandomDetail.SerialLabel}}</td>
                    <td>{{.SimulateRandomDetail.CountryLabel}}</td>
                    {{range .SimulateRandomDetail.OverviewDetail.Groups}}
                    <td>{{.}}({{$avgSDTitle}})</td>
                    {{end}}
                </tr>
                {{range $index, $item :=
                .SimulateRandomDetail.OverviewDetail.AvgSDOverviewDetail.AvgSDOverview.CountryOverView}}
                <tr>
                    <td>{{add $index 1}}</td>
                    <td>{{$item.Name}}</td>
                    {{range $item.AvgSDs}}
                    <td>{{.Avg}}+/-{{.SD}}</td>
                    {{end}}
                </tr>
                {{end}}
            </table>
            {{end}}

            <!--分层-->
            {{if gt (len .SimulateRandomDetail.OverviewDetail.AvgSDOverviewDetail.AvgSDOverview.FactorOverView) 0}}
            <table>
                <tr>
                    <td>{{.SimulateRandomDetail.SerialLabel}}</td>
                    <td>{{.SimulateRandomDetail.FactorLabel}}</td>
                    {{range .SimulateRandomDetail.OverviewDetail.Groups}}
                    <td>{{.}}({{$avgSDTitle}})</td>
                    {{end}}
                </tr>
                {{range $index, $item :=
                .SimulateRandomDetail.OverviewDetail.AvgSDOverviewDetail.AvgSDOverview.FactorOverView}}
                <tr>
                    <td>{{add $index 1}}</td>
                    <td>{{$item.Name}}</td>
                    {{range $item.AvgSDs}}
                    <td>{{.Avg}}+/-{{.SD}}</td>
                    {{end}}
                </tr>
                {{end}}
            </table>
            {{end}}

            <!--组合分层-->
            {{if gt (len
            .SimulateRandomDetail.OverviewDetail.AvgSDOverviewDetail.AvgSDOverview.CombinationFactorOverView) 0}}
            <table>
                <tr>
                    <td>{{.SimulateRandomDetail.SerialLabel}}</td>
                    <td>{{.SimulateRandomDetail.CombinationFactorLabel}}</td>
                    {{range .SimulateRandomDetail.OverviewDetail.Groups}}
                    <td>{{.}}({{$avgSDTitle}})</td>
                    {{end}}
                </tr>
                {{range $index, $item :=
                .SimulateRandomDetail.OverviewDetail.AvgSDOverviewDetail.AvgSDOverview.CombinationFactorOverView}}
                <tr>
                    <td>{{add $index 1}}</td>
                    <td>{{joinLines $item.Labels}}</td>
                    {{range $item.AvgSDs}}
                    <td>{{.Avg}}+/-{{.SD}}</td>
                    {{end}}
                </tr>
                {{end}}
            </table>
            {{end}}


            {{end}}
        </div>

        <div>
            <h3 id="chapter-3.2.2-h3">3.2.2 {{.SimulateRandomDetail.OverviewDetail.MinOverviewDetail.Title}}</h3>
            {{if gt (len .SimulateRandomDetail.OverviewDetail.Groups) 0}}
            {{- $minTitle := .SimulateRandomDetail.SubjectCountMinLabel -}}
            <!--项目-->
            {{if gt (len .SimulateRandomDetail.OverviewDetail.MinOverviewDetail.MinOverview.ProjectOverView.Mins) 0}}
            <table>
                <tr>
                    <td>{{.SimulateRandomDetail.ProjectLabel}}</td>
                    {{range .SimulateRandomDetail.OverviewDetail.Groups}}
                    <td>{{.}}({{$minTitle}})</td>
                    {{end}}
                </tr>
                <tr>
                    <td>
                        {{.SimulateRandomDetail.OverviewDetail.MinOverviewDetail.MinOverview.ProjectOverView.Name}}
                    </td>
                    {{range .SimulateRandomDetail.OverviewDetail.MinOverviewDetail.MinOverview.ProjectOverView.Mins}}
                    <td>{{.Min}}</td>
                    {{end}}
                </tr>
            </table>
            {{end}}

            <!--中心-->
            {{if gt (len .SimulateRandomDetail.OverviewDetail.MinOverviewDetail.MinOverview.SiteOverView) 0}}
            <table>
                <tr>
                    <td>{{.SimulateRandomDetail.SerialLabel}}</td>
                    <td>{{.SimulateRandomDetail.SiteLabel}}</td>
                    {{range .SimulateRandomDetail.OverviewDetail.Groups}}
                    <td>{{.}}({{$minTitle}})</td>
                    {{end}}
                </tr>
                {{range $index, $item :=
                .SimulateRandomDetail.OverviewDetail.MinOverviewDetail.MinOverview.SiteOverView}}
                <tr>
                    <td>{{add $index 1}}</td>
                    <td>{{$item.Name}}</td>
                    {{range $item.Mins}}
                    <td>{{.Min}}</td>
                    {{end}}
                </tr>
                {{end}}
            </table>
            {{end}}

            <!--区域-->
            {{if gt (len .SimulateRandomDetail.OverviewDetail.MinOverviewDetail.MinOverview.RegionOverView) 0}}
            <table>
                <tr>
                    <td>{{.SimulateRandomDetail.SerialLabel}}</td>
                    <td>{{.SimulateRandomDetail.RegionLabel}}</td>
                    {{range .SimulateRandomDetail.OverviewDetail.Groups}}
                    <td>{{.}}({{$minTitle}})</td>
                    {{end}}
                </tr>
                {{range $index, $item :=
                .SimulateRandomDetail.OverviewDetail.MinOverviewDetail.MinOverview.RegionOverView}}
                <tr>
                    <td>{{add $index 1}}</td>
                    <td>{{$item.Name}}</td>
                    {{range $item.Mins}}
                    <td>{{.Min}}</td>
                    {{end}}
                </tr>
                {{end}}
            </table>
            {{end}}

            <!--国家-->
            {{if gt (len .SimulateRandomDetail.OverviewDetail.MinOverviewDetail.MinOverview.CountryOverView) 0}}
            <table>
                <tr>
                    <td>{{.SimulateRandomDetail.SerialLabel}}</td>
                    <td>{{.SimulateRandomDetail.CountryLabel}}</td>
                    {{range .SimulateRandomDetail.OverviewDetail.Groups}}
                    <td>{{.}}({{$minTitle}})</td>
                    {{end}}
                </tr>
                {{range $index, $item :=
                .SimulateRandomDetail.OverviewDetail.MinOverviewDetail.MinOverview.CountryOverView}}
                <tr>
                    <td>{{add $index 1}}</td>
                    <td>{{$item.Name}}</td>
                    {{range $item.Mins}}
                    <td>{{.Min}}</td>
                    {{end}}
                </tr>
                {{end}}
            </table>
            {{end}}

            <!--分层-->
            {{if gt (len .SimulateRandomDetail.OverviewDetail.MinOverviewDetail.MinOverview.FactorOverView) 0}}
            <table>
                <tr>
                    <td>{{.SimulateRandomDetail.SerialLabel}}</td>
                    <td>{{.SimulateRandomDetail.FactorLabel}}</td>
                    {{range .SimulateRandomDetail.OverviewDetail.Groups}}
                    <td>{{.}}({{$minTitle}})</td>
                    {{end}}
                </tr>
                {{range $index, $item :=
                .SimulateRandomDetail.OverviewDetail.MinOverviewDetail.MinOverview.FactorOverView}}
                <tr>
                    <td>{{add $index 1}}</td>
                    <td>{{$item.Name}}</td>
                    {{range $item.Mins}}
                    <td>{{.Min}}</td>
                    {{end}}
                </tr>
                {{end}}
            </table>
            {{end}}

            <!--组合分层-->
            {{if gt (len .SimulateRandomDetail.OverviewDetail.MinOverviewDetail.MinOverview.CombinationFactorOverView)
            0}}
            <table>
                <tr>
                    <td>{{.SimulateRandomDetail.SerialLabel}}</td>
                    <td>{{.SimulateRandomDetail.CombinationFactorLabel}}</td>
                    {{range .SimulateRandomDetail.OverviewDetail.Groups}}
                    <td>{{.}}({{$minTitle}})</td>
                    {{end}}
                </tr>
                {{range $index, $item :=
                .SimulateRandomDetail.OverviewDetail.MinOverviewDetail.MinOverview.CombinationFactorOverView}}
                <tr>
                    <td>{{add $index 1}}</td>
                    <td>{{joinLines $item.Labels}}</td>
                    {{range $item.Mins}}
                    <td>{{.Min}}</td>
                    {{end}}
                </tr>
                {{end}}
            </table>
            {{end}}


            {{end}}


        </div>

        <div>
            <h3 id="chapter-3.2.3-h3">3.2.3
                {{.SimulateRandomDetail.OverviewDetail.UnbalancedRunCountOverviewDetail.Title}}</h3>
            {{if gt (len .SimulateRandomDetail.OverviewDetail.Groups) 0}}
            {{- $minTitle := "受试者例数最小值" -}}
            <!--项目-->
            <table>
                <colgroup>
                    <col style="width: 50%;">
                    <col style="width: 50%;">
                </colgroup>
                <tr>
                    <td>{{.SimulateRandomDetail.ProjectLabel}}</td>
                    <td>{{.SimulateRandomDetail.OverviewDetail.UnbalancedRunCountOverviewDetail.Title}}</td>
                </tr>
                <tr>
                    <td>
                        {{.SimulateRandomDetail.OverviewDetail.UnbalancedRunCountOverviewDetail.UnbalancedRunCountOverview.ProjectOverView.Name}}
                    </td>
                    <td>
                        {{.SimulateRandomDetail.OverviewDetail.UnbalancedRunCountOverviewDetail.UnbalancedRunCountOverview.ProjectOverView.Count}}
                    </td>
                </tr>
            </table>

            <!--中心-->
            {{if gt (len .SimulateRandomDetail.OverviewDetail.UnbalancedRunCountOverviewDetail.UnbalancedRunCountOverview.SiteOverView) 0}}
            <table>
                <colgroup>
                    <col style="width: 33.33%;">
                    <col style="width: 33.33%;">
                    <col style="width: 33.33%;">
                </colgroup>
                <tr>
                    <td>{{.SimulateRandomDetail.SerialLabel}}</td>
                    <td>{{.SimulateRandomDetail.SiteLabel}}</td>
                    <td>{{.SimulateRandomDetail.OverviewDetail.UnbalancedRunCountOverviewDetail.Title}}</td>
                </tr>
                {{range $index, $item :=
                .SimulateRandomDetail.OverviewDetail.UnbalancedRunCountOverviewDetail.UnbalancedRunCountOverview.SiteOverView}}
                <tr>
                    <td>{{add $index 1}}</td>
                    <td>{{$item.Name}}</td>
                    <td>{{$item.Count}}</td>
                </tr>
                {{end}}
            </table>
            {{end}}

            <!--区域-->
            {{if gt (len .SimulateRandomDetail.OverviewDetail.UnbalancedRunCountOverviewDetail.UnbalancedRunCountOverview.RegionOverView) 0}}
            <table>
                <colgroup>
                    <col style="width: 33.33%;">
                    <col style="width: 33.33%;">
                    <col style="width: 33.33%;">
                </colgroup>
                <tr>
                    <td>{{.SimulateRandomDetail.SerialLabel}}</td>
                    <td>{{.SimulateRandomDetail.RegionLabel}}</td>
                    <td>{{.SimulateRandomDetail.OverviewDetail.UnbalancedRunCountOverviewDetail.Title}}</td>
                </tr>
                {{range $index, $item :=
                .SimulateRandomDetail.OverviewDetail.UnbalancedRunCountOverviewDetail.UnbalancedRunCountOverview.RegionOverView}}
                <tr>
                    <td>{{add $index 1}}</td>
                    <td>{{$item.Name}}</td>
                    <td>{{$item.Count}}</td>
                </tr>
                {{end}}
            </table>
            {{end}}

            <!--国家-->
            {{if gt (len .SimulateRandomDetail.OverviewDetail.UnbalancedRunCountOverviewDetail.UnbalancedRunCountOverview.CountryOverView) 0}}
            <table>
                <colgroup>
                    <col style="width: 33.33%;">
                    <col style="width: 33.33%;">
                    <col style="width: 33.33%;">
                </colgroup>
                <tr>
                    <td>{{.SimulateRandomDetail.SerialLabel}}</td>
                    <td>{{.SimulateRandomDetail.CountryLabel}}</td>
                    <td>{{.SimulateRandomDetail.OverviewDetail.UnbalancedRunCountOverviewDetail.Title}}</td>
                </tr>
                {{range $index, $item :=
                .SimulateRandomDetail.OverviewDetail.UnbalancedRunCountOverviewDetail.UnbalancedRunCountOverview.CountryOverView}}
                <tr>
                    <td>{{add $index 1}}</td>
                    <td>{{$item.Name}}</td>
                    <td>{{$item.Count}}</td>
                </tr>
                {{end}}
            </table>
            {{end}}

            <!--分层-->
            {{if gt (len .SimulateRandomDetail.OverviewDetail.UnbalancedRunCountOverviewDetail.UnbalancedRunCountOverview.FactorOverView) 0}}
            <table>
                <colgroup>
                    <col style="width: 33.33%;">
                    <col style="width: 33.33%;">
                    <col style="width: 33.33%;">
                </colgroup>
                <tr>
                    <td>{{.SimulateRandomDetail.SerialLabel}}</td>
                    <td>{{.SimulateRandomDetail.FactorLabel}}</td>
                    <td>{{.SimulateRandomDetail.OverviewDetail.UnbalancedRunCountOverviewDetail.Title}}</td>
                </tr>
                {{range $index, $item :=
                .SimulateRandomDetail.OverviewDetail.UnbalancedRunCountOverviewDetail.UnbalancedRunCountOverview.FactorOverView}}
                <tr>
                    <td>{{add $index 1}}</td>
                    <td>{{$item.Name}}</td>
                    <td>{{$item.Count}}</td>
                </tr>
                {{end}}
            </table>
            {{end}}

            <!--组合分层-->
            {{if gt (len .SimulateRandomDetail.OverviewDetail.UnbalancedRunCountOverviewDetail.UnbalancedRunCountOverview.CombinationFactorOverView)
            0}}
            <table>
                <colgroup>
                    <col style="width: 33.33%;">
                    <col style="width: 33.33%;">
                    <col style="width: 33.33%;">
                </colgroup>
                <tr>
                    <td>{{.SimulateRandomDetail.SerialLabel}}</td>
                    <td>{{.SimulateRandomDetail.CombinationFactorLabel}}</td>
                    <td>{{.SimulateRandomDetail.OverviewDetail.UnbalancedRunCountOverviewDetail.Title}}</td>
                </tr>
                {{range $index, $item :=
                .SimulateRandomDetail.OverviewDetail.UnbalancedRunCountOverviewDetail.UnbalancedRunCountOverview.CombinationFactorOverView}}
                <tr>
                    <td>{{add $index 1}}</td>
                    <td>{{joinLines $item.Labels}}</td>
                    <td>{{$item.Count}}</td>
                </tr>
                {{end}}
            </table>
            {{end}}


            {{end}}


        </div>
    </div>
    <!--详细结果-->
    <div class="chapter">
        <h2 id="chapter-3.3-h2">3.3 {{.SimulateRandomDetail.DetailedResults.Title}}</h2>
        {{- $projectLabel := .SimulateRandomDetail.ProjectLabel -}}
        {{- $serialLabel := .SimulateRandomDetail.SerialLabel -}}
        {{- $siteLabel := .SimulateRandomDetail.SiteLabel -}}
        {{- $regionLabel := .SimulateRandomDetail.RegionLabel -}}
        {{- $countryLabel := .SimulateRandomDetail.CountryLabel -}}
        {{- $factorLabel := .SimulateRandomDetail.FactorLabel -}}
        {{- $combinationFactorLabel := .SimulateRandomDetail.CombinationFactorLabel -}}
        {{- $totalLabel := .SimulateRandomDetail.TotalLabel -}}
        {{- $runCountLabel := .SimulateRandomDetail.RunCountLabel -}}
        {{- $peopleCountLabel := .SimulateRandomDetail.PeopleCountLabel -}}
        {{- $unbalancedLabel := .SimulateRandomDetail.UnbalancedLabel -}}


        {{range $index, $item := .SimulateRandomDetail.DetailedResults.UnbalancedDetails}}
        {{if gt (len $item.Groups) 0}}
        <div style="text-align: center; margin-bottom: 20px; font-weight: bold;">{{$runCountLabel}} {{add $index 1}}</div>

        <!--项目-->
        {{if gt (len $item.ProjectDetail.Unbalanceds) 0}}
        <table>
            <tr>
                <td>{{$projectLabel}}</td>
                {{range $item.Groups}}
                <td>{{.}}</td>
                {{end}}
                <td>{{$totalLabel}}</td>
            </tr>
            <tr>
                <td>{{$item.ProjectDetail.Name}}</td>
                {{range $item.ProjectDetail.Unbalanceds}}
                <td>{{$peopleCountLabel}}[{{.Count}}]/{{$unbalancedLabel}}[{{.Unbalanced}}]</td>
                {{end}}
                <td>{{$item.ProjectDetail.Total}}</td>
            </tr>
        </table>
        {{end}}

        <!--中心-->
        {{if gt (len $item.SiteDetail) 0}}
        <table>
            <tr>
                <td>{{$serialLabel}}</td>
                <td>{{$siteLabel}}</td>
                {{range $item.Groups}}
                <td>{{.}}</td>
                {{end}}
                <td>{{$totalLabel}}</td>
            </tr>
            {{range $i, $it := $item.SiteDetail}}
            <tr>
                <td>{{add $i 1}}</td>
                <td>{{$it.Name}}</td>
                {{range $it.Unbalanceds}}
                <td>{{$peopleCountLabel}}[{{.Count}}]/{{$unbalancedLabel}}[{{.Unbalanced}}]</td>
                {{end}}
                <td>{{$it.Total}}</td>
            </tr>
            {{end}}
        </table>
        {{end}}

        <!--区域-->
        {{if gt (len $item.RegionDetail) 0}}
        <table>
            <tr>
                <td>{{$serialLabel}}</td>
                <td>{{$regionLabel}}</td>
                {{range $item.Groups}}
                <td>{{.}}</td>
                {{end}}
                <td>{{$totalLabel}}</td>
            </tr>
            {{range $i, $it := $item.RegionDetail}}
            <tr>
                <td>{{add $i 1}}</td>
                <td>{{$it.Name}}</td>
                {{range $it.Unbalanceds}}
                <td>{{$peopleCountLabel}}[{{.Count}}]/{{$unbalancedLabel}}[{{.Unbalanced}}]</td>
                {{end}}
                <td>{{$it.Total}}</td>
            </tr>
            {{end}}
        </table>
        {{end}}

        <!--国家-->
        {{if gt (len $item.CountryDetail) 0}}
        <table>
            <tr>
                <td>{{$serialLabel}}</td>
                <td>{{$countryLabel}}</td>
                {{range $item.Groups}}
                <td>{{.}}</td>
                {{end}}
                <td>{{$totalLabel}}</td>
            </tr>
            {{range $i, $it := $item.CountryDetail}}
            <tr>
                <td>{{add $i 1}}</td>
                <td>{{$it.Name}}</td>
                {{range $it.Unbalanceds}}
                <td>{{$peopleCountLabel}}[{{.Count}}]/{{$unbalancedLabel}}[{{.Unbalanced}}]</td>
                {{end}}
                <td>{{$it.Total}}</td>
            </tr>
            {{end}}
        </table>
        {{end}}

        <!--分层-->
        {{if gt (len $item.FactorDetail) 0}}
        <table>
            <tr>
                <td>{{$serialLabel}}</td>
                <td>{{$factorLabel}}</td>
                {{range $item.Groups}}
                <td>{{.}}</td>
                {{end}}
                <td>{{$totalLabel}}</td>
            </tr>
            {{range $i, $it := $item.FactorDetail}}
            <tr>
                <td>{{add $i 1}}</td>
                <td>{{$it.Name}}</td>
                {{range $it.Unbalanceds}}
                <td>{{$peopleCountLabel}}[{{.Count}}]/{{$unbalancedLabel}}[{{.Unbalanced}}]</td>
                {{end}}
                <td>{{$it.Total}}</td>
            </tr>
            {{end}}
        </table>
        {{end}}

        <!--组合分层-->
        {{if gt (len $item.CombinationFactorDetail) 0}}
        <table>
            <tr>
                <td>{{$serialLabel}}</td>
                <td>{{$combinationFactorLabel}}</td>
                {{range $item.Groups}}
                <td>{{.}}</td>
                {{end}}
                <td>{{$totalLabel}}</td>
            </tr>
            {{range $i, $it := $item.CombinationFactorDetail}}
            <tr>
                <td>{{add $i 1}}</td>
                <td>{{joinLines $it.Labels}}</td>
                {{range $it.Unbalanceds}}
                <td>{{$peopleCountLabel}}[{{.Count}}]/{{$unbalancedLabel}}[{{.Unbalanced}}]</td>
                {{end}}
                <td>{{$it.Total}}</td>
            </tr>
            {{end}}
        </table>
        {{end}}


        {{end}}
        {{end}}

    </div>

</div>


</body>
</html>