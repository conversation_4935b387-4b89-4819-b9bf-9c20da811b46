import {del, get, patch, post} from "utils/http";

export const getSupplyPlanList = (params: object = {}) => get(`/supply-plan/`, {params})
export const removeSupplyPlan = (params: object = {}) => del(`/supply-plan/`, {params})
export const removeSupplyPlanMedicine = (params: object = {}) => del(`/supply-plan/medicine`, {params})
export const getSupplyPlanMedicineList = (params: object = {}) => get(`/supply-plan/medicine`, {params})
export const updateSupplyPlan = ( data: object = {}) => patch(`/supply-plan`, {data})
export const addSupplyPlan = (data: object = {}) => post(`/supply-plan/add`, {data})
export const getMedicineConfigures = (params: object = {}) => get(`/supply-plan/medicine-configures`, {params})
export const updateSupplyMedicinePlan = (params: object = {}, data: object = {}) => patch(`/supply-plan/medicine`, {params, data})
export const addSupplyMedicinePlan = (data: object = {}) => post(`/supply-plan/medicine/add`, {data})
export const getSupplyPlanDetail = (params: object = {}) => get(`/supply-plan/detail`, {params})
export const checkSite = (data: object = {}) => post(`/supply-plan/check-site`, {data})
export const getApplicableSiteSupplyPlan = (params: object = {}) => get(`/supply-plan/site`, {params})
export const getSupplyIdCount = (data: object = {}) => post(`/supply-plan/getSupplyIdCount`, {data})

export const batchSupplyMedicinePlan = (params: object = {}, data: object = {}) => post(`/supply-plan/medicine/batch`, {params, data})
