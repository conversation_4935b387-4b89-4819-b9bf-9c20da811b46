package service

import (
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
)

type CountryService struct{}

func (c CountryService) All(ctx *gin.Context) ([]models.Country, error) {
	var countries []models.Country
	cursor, err := tools.Database.Collection("country").Find(nil, bson.M{})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &countries)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return countries, nil
}
