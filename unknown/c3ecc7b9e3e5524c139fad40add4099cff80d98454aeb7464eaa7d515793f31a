import {del, get, patch, post} from "../utils/http";

export const getSubjectStatusAndRoom = (params: object = {}) => get(`/dispensing/subject/status-room`, {params})
export const realDispensing = (data: object = {}) => patch(`/dispensing/real`, {data})
export const invalidDispensing = (params: object = {}) => patch(`/dispensing/invalid`, {params})
export const retrievalDrug = (params: object = {},data: object = {}) => patch(`/dispensing/retrieval`, {params,data})
export const deleteDispensing = (params: object = {}, data: object = {}) => del(`/dispensing`, {params, data})
export const replaceDrug = (data: object = {}) => post(`/dispensing/replace-drug`, {data})
export const getDispensing = (params: object = {}) => get(`/dispensing/subject/role`, {params})
export const reissueDispensing = (data: object = {}) => post(`/dispensing/reissue`, {data})
export const addDispensingVisit = (data: object = {}) => post(`/dispensing/visit`, {data})
export const getVisit = (params: object = {}) => get(`/dispensing/visit`, {params})
export const getReissueMedicineName = (params: object = {}) => get(`/dispensing/reissue/medicine`, {params})
export const getDispensingRoomInfo = (params: object = {}) => get(`/dispensing/room/info`, {params})
export const postRecordRoomInfo = (params: object = {},data: object = {}) => post(`/dispensing/room/info`, {params, data})
export const updateDispensingVisitWithDTP = (data: object = {}) => patch(`/dispensing/visit/dtp`, {data})
export const addDispensingVisitWithDTP = (data: object = {}) => post(`/dispensing/visit/dtp`, {data})
export const reissueDispensingWithDTP = (data: object = {}) => post(`/dispensing/reissue/dtp`, {data})
export const getFormula = (params: object = {}) => get(`/dispensing/formula`, {params})
export const getFormulaMedicine = (data: object = {}) => post(`/dispensing/formula/medicine`, {data})

export const getFormulaForm = (params: object = {}) => get(`/randomization/formula/form`, {params})
export const getDispensingConfirmTable = (data: object = {}) => post(`/dispensing/confirm-table`, {data})
export const PatchAppDispenseTask = (params: object = {}) => patch(`/dispensing/subject/role/app/dispense-task`, {params})
export const dispensingResume = (params: object = {}) => get(`/dispensing/resume`, {params})
export const dispensingLabelMedicine = (params: object = {}) => get(`/dispensing/subject/visit/web`, {params})

export const DoseForm = (params: object = {}) => get(`/dispensing/dose`, {params})
export const DoseInfo = (data: object = {}) => post(`/dispensing/dose-res`, {data})
export const StartFollowUpVisits = (data: object = {}) => post(`/dispensing/start/follow/up/visits`, {data})

export const GetMedicineUnBlind = (params: object = {}) => get(`/dispensing/medicine/un-blind`, {params})
export const medicineUnblindingApplication = (data: object = {}) => post(`/dispensing/urgent-unblinding-application`, { data })
export const UnblindingApproval = (data: object = {}) => post(`/dispensing/urgent-unblinding-approval`, { data })