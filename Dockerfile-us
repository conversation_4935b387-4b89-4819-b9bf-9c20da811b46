# Step 1
############################
FROM harbor.clinflash.com/build/node:18-alpine
COPY ./frontend/package.json /frontend/package.json
WORKDIR /frontend/
RUN yarn config set registry https://registry.npmmirror.com --global
RUN yarn config set disturl https://npmmirror.com/dist --global
RUN yarn install
COPY ./frontend/ /frontend/
RUN yarn build

# Step 2
############################
FROM harbor.clinflash.com/build/golang:1.22-alpine
COPY ./backend/go.mod /backend/go.mod
COPY ./backend/go.sum /backend/go.sum
COPY ./backend/.netrc /root/.netrc
WORKDIR /backend/
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories
RUN apk add --no-cache git
RUN go env -w GOPROXY=https://goproxy.cn,https://gocenter.io,https://goproxy.io,direct
RUN go env -w GOPRIVATE=git.jxedc.com
RUN go get ./...
COPY ./backend/ /backend/
RUN GOARCH=amd64 CGO_ENABLED=0 GOOS=linux go build -tags timetzdata -o app

FROM harbor.clinflash.com/build/alpine:edge
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories
# 安装相关依赖
RUN apk add --no-cache py3-pip so:libgobject-2.0.so.0 so:libpango-1.0.so.0 so:libharfbuzz.so.0 so:libharfbuzz-subset.so.0 so:libfontconfig.so.1 so:libpangoft2-1.0.so.0 pango
# 创建虚拟环境并安装 WeasyPrint
RUN python3 -m venv venv && \
    venv/bin/pip install --upgrade pip && \
    venv/bin/pip install WeasyPrint
# 设置 PATH 环境变量，确保后续命令使用虚拟环境中的 Python 和 pip
ENV PATH="/venv/bin:$PATH"
COPY --from=0 /frontend/build/ ./build/
COPY --from=1 /backend/app ./
COPY --from=1 /backend/assets/fonts/ ./assets/fonts/
COPY --from=1 /backend/assets/images/ ./assets/images/
EXPOSE 8080
ENTRYPOINT ./app
